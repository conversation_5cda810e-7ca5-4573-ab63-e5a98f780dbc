local commit_branch = std.extVar('CI_COMMIT_BRANCH');
local deploy_env = std.extVar('DEPLOY_ENV');
local default_commit_branch = std.asciiLower(commit_branch);
local default_branch_name = "orch/" + default_commit_branch;
local netbox_docker_version = std.extVar('NETBOX_DOCKER_VERSION');
local netbox_version = std.extVar('NETBOX_VERSION');
local commit_hash = std.extVar('COMMIT_HASH');
local default_ecr_repo = "orchestrator_image";
local qa_aws_access_key_id = allConfigs["qa"]['meta']['aws_access_key_id'];
local qa_aws_secret_access_key = allConfigs["qa"]['meta']['aws_secret_access_key'];
local qa_aws_region = allConfigs["qa"]['meta']['regions'][0]; # common repo can not add active region value for qa env because of automation issue. so, find us-west-2 from regions.
local qa_aws_account_id =  allConfigs["qa"]['meta']['account_id'];

local env_type = allConfigs[deploy_env]['meta']['environment_type'];
local env_mode = allConfigs[deploy_env]['meta']['environment_mode'];
local deployment_stack = allConfigs[deploy_env]['meta']['deployment_stack'];

local validate_env_data_against_golden_template_var = std.extVar('VALIDATE_ENV_DATA_AGAINST_GOLDEN_TEMPLATE');

# applicable for prod env only
local dest_env_to_upload_image = deploy_env;
local dest_aws_access_key_id = allConfigs[dest_env_to_upload_image]['meta']['aws_access_key_id'];
local dest_aws_secret_access_key = allConfigs[dest_env_to_upload_image]['meta']['aws_secret_access_key'];
local dest_aws_account_id =  allConfigs[dest_env_to_upload_image]['meta']['account_id'];

local MR_ENV = if std.extVar('MR_ENV') == "" then "devX" else std.extVar('MR_ENV');
local DEFAULT_ENV = std.extVar('DEFAULT_ENV');
// Generate yaml files for all environments
local envs = if std.extVar('DEPLOY_ENV') != "" then std.split(std.extVar('DEPLOY_ENV'),',')
else if ((std.length(std.extVar('CI_MERGE_REQUEST_ID')) > 0) && (MR_ENV != 'devX')) then [MR_ENV]
else if std.extVar('CI_COMMIT_BRANCH') == 'dev/develop' then []
else [];


local generateBoilerPlate() =
    {
        "include": [
          {
                "project": "prisma-access/sase-cosmos-group/sase-controller-group/pa-infra-group/pa-infra-components-terraform",
                "ref":     "dev/develop",
                "file":    "cicd-templates/terraform-apply.yml"
          },
          {
            "project": "engineering-productivity/ci-templates",
            "ref":     "master",
            "file":    "output/docker/base.yaml",
          },
          {
                "project": "engineering-productivity/ci-templates",
                "ref": "master",
                "file": "output/ai-build-failure-triage/ai-build-failure-triage.yaml"
          },
          {
                "project": "prisma-access/sase-cosmos-group/sase-controller-group/pa-infra-group/pa-infra-components-terraform",
                "ref":     "dev/develop",
                "file":    "cicd-templates/verify-coverage.yml"
          },
          {
                "project": "prisma-access/sase-cosmos-group/sase-controller-group/pa-infra-group/pa-infra-components-terraform",
                "ref":     "dev/develop",
                "file":    "cicd-templates/merge-request.yml"
          },
        ]
    }
    +
    {
        "stages": [
            "pre_env",
            "prep_and_ut_coverage",
            "build_upload_ecr",
            "envVarSetup",
            "create_secrets",
            "pull_from_qa_upload_to_dest_env",
            "orch_deploy",
            "orch_deploy_verify",
            "terraform_plan_and_apply",
            "automation_test",
            "post_env"
        ]
    }
    +
    {
        "variables": {
            "SERVICE_NAME": "orchestrator",
            "SERVICE_ARTIFACTS_S3_BUCKET_US_WEST_2": "pan-components-us-west-2",
            "SERVICE_ARTIFACTS_S3_BUCKET_US_EAST_1": "pan-components-us-east-1",
            "SERVICE_TERRAFORM_STATE_S3_BUCKET_PREFIX": "pan-orchestrator-service-terraform-",
            "FEDRAMP_SERVICE_TERRAFORM_STATE_S3_BUCKET_PREFIX": "pan-orch-svc-terraform-",
        }
    };


local updateVersionInDB(region, aws_env, aws_access_key_id, aws_secret_access_key) = [
    "aws --profile default configure set aws_access_key_id " + "${" + aws_access_key_id + "}",
    "aws --profile default configure set aws_secret_access_key " + "${" + aws_secret_access_key + "}",
    if std.extVar('SERVICE_VERSION') == "" then
        "export IMAGE_TAG_TEMPLATE=`cat build_version.txt`-`cat build_number.txt`_${CI_COMMIT_SHORT_SHA}"
    else
        "export IMAGE_TAG_TEMPLATE=$(cat service_version.txt | grep SERVICE_VERSION | cut -d = -f2)",
    "pip3 install boto3",
    "export COMMON_LAYER_VER=`cat common_version.txt`",
    "python3 ./docker/scripts/update_service_version.py " + aws_env + " " + region
];

local post_checks() = [
    if std.extVar('SERVICE_VERSION') == "" then
        "export IMAGE_TAG_TEMPLATE=`cat build_version.txt`-`cat build_number.txt`_${CI_COMMIT_SHORT_SHA}"
    else
        "export IMAGE_TAG_TEMPLATE=$(cat service_version.txt | grep SERVICE_VERSION | cut -d = -f2)",
    "python3 ./docker/scripts/check_pods_status.py",
    "python3 ./docker/scripts/check_pods_image_version.py"
];

local terraform_plan_and_apply(region,branchName,aws_access_key_id,aws_secret_access_key,aws_env) =
{
    "stage": "terraform_plan_and_apply",
    "extends": [
        ".terraform-apply",
    ],
    "variables": {
        "AWS_REGION": region,
        "AWS_ENV": aws_env,
        "AWS_ACCESS_KEY_ID" : "${" + aws_access_key_id + "}",
        "AWS_SECRET_ACCESS_KEY": "${" + aws_secret_access_key + "}",
        "BRANCH_NAME": branchName,
        "SERVICE_TERRAFORM_STATE_S3_BUCKET_PREFIX": generateBoilerPlate()['variables']['SERVICE_TERRAFORM_STATE_S3_BUCKET_PREFIX'],
        "FEDRAMP_SERVICE_TERRAFORM_STATE_S3_BUCKET_PREFIX": generateBoilerPlate()['variables']['FEDRAMP_SERVICE_TERRAFORM_STATE_S3_BUCKET_PREFIX'],
    },
    "rules": [
      {
            "if": "((\"" + deploy_env + "\" != '') || ($CI_COMMIT_BRANCH == \"" + branchName + "\") || ($CI_MERGE_REQUEST_ID && (\"" + MR_ENV + "\" ==  \"" + aws_env + "\")))",
            #"if": "$CI_COMMIT_BRANCH == \""+branchName + "\"",
            "when": "on_success"
      }
    ],
};

local create_secrets(region,branchName, aws_access_key_id,aws_secret_access_key, aws_env,account_id) =
{
    "stage": "create_secrets",
    "image": "docker.art.code.pan.run/build-tools--image-aws-tools:0.0.1",
    "rules": [
        {
            "if": "((\"" + deploy_env + "\" != '') || ($CI_COMMIT_BRANCH == \"" + branchName + "\") || ($CI_MERGE_REQUEST_ID && (\"" + MR_ENV + "\" ==  \"" + aws_env + "\")))",
            "when": "on_success"
        }
    ],

    "script": [
      "aws --version",
      "pip install --upgrade awscli",
      "aws --version",
      "aws --profile default configure set aws_access_key_id " + "${" + aws_access_key_id + "}",
      "aws --profile default configure set aws_secret_access_key " + "${" + aws_secret_access_key + "}",
      "python3 secret_manager_py/Dependencies/get-pip.py",
      "pip install boto3",
      "pip install -U Jinja2",
      "git submodule add --force https://gitlab-ci-token:${CI_JOB_TOKEN}@code.pan.run/prisma-access/sase-cosmos-group/sase-controller-group/pa-infra-group/pa-infra-components-envs.git",
      "ls -lthr",
      "cp -R pa-infra-components-envs/k8s/helmcharts/saasinfra/* k8s/helmcharts/saasinfra",
      "ls -lthr k8s/helmcharts/saasinfra/",
      "python3 secret_manager_py/validate_upload_golden_templates.py " + aws_env + " " +region + " " + env_type + " " + env_mode + " " + validate_env_data_against_golden_template_var,
      "python3 secret_manager_py/secrets.py " +aws_env +" " +region + " " +env_type + " " +deployment_stack,
      "python3 secret_manager_py/eks_orch_secret.py " +aws_env +" " +region + " " + aws_access_key_id + " " + aws_secret_access_key,
      "rm -f /usr/local/bin/kubectl",
      "curl -LO https://dl.k8s.io/release/v1.20.0/bin/linux/amd64/kubectl",
      "chmod +x kubectl",
      "mkdir -p /usr/local/bin",
      "cp kubectl /usr/local/bin/",
      "ls /usr/local/bin/ | grep kubectl",
      "kubectl version --short --client",
      "aws secretsmanager list-secrets --profile=default --region=" + region  +" | grep Name"
    ],
    "allow_failure": false,
    "resource_group": aws_env + '-' + region
};

local verify_coverage() =
{
    "stage": "verify_coverage",
    "extends": [
        ".coverage-template"
    ],
    variables: {
    THRESHOLD: 0
  },
  "allow_failure": true //allow failure until CYR-55257 gets resolved
};


local prep_and_ut_coverage() =
{
    "stage": "prep_and_ut_coverage",
    "image": "docker-prisma-access-remote.art.code.pan.run/docker-prisma-access:1.0.28",
    "tags": [
        "gke",
        "large"
    ],
    "rules": [
        {
            "if": "($CI_COMMIT_BRANCH || $CI_MERGE_REQUEST_ID)",
            "when": "on_success"
        }
    ],

    "script": [
        "export SKIP_DEPLOYMENT=$(cat skip_deployment.txt)",
        "echo $SKIP_DEPLOYMENT",
        "if [ \"$SKIP_DEPLOYMENT\" = \"True\" ]; then exit; fi",
        "export PAN_BUILD_DIR=$(pwd)",
        "echo $PAN_BUILD_DIR",
        "echo $PAN_SW_VERSION",
        "apt update -y",
        "apt install docker.io -y",
        "apt install zip",
        "apt install tree",
        "apt install rpm2cpio",
        "apt install cpio",
        "apt install wget",
        "apt install -y awscli",
        "pip3 --cert /etc/ssl/certs/pan.pem install BeautifulSoup4",
        "export COMMON_LAYER_BRANCH=`awk -F'==' '/common-lib-layer-branch/ {print $2}' requirements-layer.txt`",
        "export COMMON_LAYER_BRANCH_TAG=$(echo $COMMON_LAYER_BRANCH | sed -E 's~/~_~g; s~-~_~g; s/([A-Z]+_[0-9]+).*/\\1/')",
        "export COMMON_LAYER_VER=`awk -F'==' '/common-lib-layer-ver/ {print $2}' requirements-layer.txt`",
        "rm -rf ${CI_PROJECT_DIR}/tf/scripts",
        "rm -rf ${CI_PROJECT_DIR}/pa-infra-components-terraform",
        "git submodule add --force https://gitlab-ci-token:${CI_JOB_TOKEN}@code.pan.run/prisma-access/sase-cosmos-group/sase-controller-group/pa-infra-group/pa-infra-components-terraform.git",
        "ls -ltrh pa-infra-components-terraform/scripts",
        "mkdir -p ${CI_PROJECT_DIR}/tf",
        "mv pa-infra-components-terraform/scripts ${CI_PROJECT_DIR}/tf/",
        "ls -ltrh ${CI_PROJECT_DIR}/tf/scripts",
        "if [ $COMMON_LAYER_VER = \"latest\" ]; then wget https://art.code.pan.run/artifactory/rpm-prismaaccess-remote/saas-infra/${COMMON_LAYER_BRANCH}/noarch/ --no-check-certificate; python3 ${CI_PROJECT_DIR}/tf/scripts/extract_latest_saas_infra_package.py; export COMMON_LAYER_VER=`cat /tmp/common_layer_ver`; fi;",
        "echo \"COMMON_LAYER_VER=$COMMON_LAYER_VER\" >> common_version.txt",
        "wget https://art.code.pan.run/artifactory/rpm-prismaaccess-remote/saas-infra/${COMMON_LAYER_BRANCH}/noarch/saas-infra-${COMMON_LAYER_VER}.noarch_${COMMON_LAYER_BRANCH_TAG}.rpm --no-check-certificate",
        "ls -ltrh",
        "export ORCH_BUILD_DIR=`pwd`",
        "echo $ORCH_BUILD_DIR",
        "rpm2cpio saas-infra-$COMMON_LAYER_VER.noarch_${COMMON_LAYER_BRANCH_TAG}.rpm | cpio -idmv",
        "ls -ltrh",
        "mkdir temp",
        "unzip $ORCH_BUILD_DIR/usr/local/saas-infra/common-$COMMON_LAYER_VER.noarch.zip -d $ORCH_BUILD_DIR/temp",
        "cp -rf $ORCH_BUILD_DIR/temp/common/shared $ORCH_BUILD_DIR/src/apps",
        "rm -rf $ORCH_BUILD_DIR/temp",
        "ls -lthr $ORCH_BUILD_DIR/src/apps/shared",
        "ls -lthr $ORCH_BUILD_DIR",
        "echo $CI_COMMIT_BRANCH",
        "echo commit_branch",
        "./docker/scripts/create_repo.sh " + "${" + qa_aws_access_key_id + "}" + " " + "${" + qa_aws_secret_access_key + "}" + "  " + default_branch_name,
        "./docker/scripts/s3bucket_file_creation.sh " + "${" + qa_aws_access_key_id + "}" + " " + "${" + qa_aws_secret_access_key + "}",
        "./docker/scripts/buildnum.sh " + "${" + qa_aws_access_key_id + "}" + " " + "${" + qa_aws_secret_access_key + "}",
        "cat .dockerbuildnum >> build_number.txt",
        "cat ./service_version/service_version.txt >> build_version.txt",
        "echo \"SERVICE_ZIP=orchestrator_service-$(cat build_version.txt)-$(cat build_number.txt)_${CI_COMMIT_SHORT_SHA}\" >> build.txt",
        "aws s3 cp .dockerbuildnum s3://${SERVICE_ARTIFACTS_S3_BUCKET_US_WEST_2}/artifacts/${SERVICE_NAME}/${CI_COMMIT_BRANCH:-$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME}/.dockerbuildnum",
        "pwd",
        "cd tests/orchestrator",
        "make unittest", #TODO changed ?
        "ls",
        "mv htmlcov ../../code_coverage",
        "mv .coverage ../../code_coverage",
        "cd ../../",
        "cd src/",
        "pwd",
        "export GOPATH=/go",
        "export PATH=$GOPATH/bin:/bin/go:$PATH",
        "mkdir -p '$GOPATH/src' '$GOPATH/bin' '$GOPATH/pkg' && chmod -R 777 '$GOPATH/'",
        "export GOPROXY=https://art.code.pan.run/artifactory/api/go/go-prisma-access",
        "export GOPRIVATE=go.panw.local",
        "export GONOPROXY=none",
        "export DOCKED=1",
        "make",
        "ls",
        "cd ..",
        "cd docker",
        "ls $PAN_BUILD_DIR/bin/",
        "ls",
        "make",
        "ls $PAN_BUILD_DIR/docker/bin/",
        "pwd",
        "ls"
    ],
      "artifacts": {
        "paths": [
          "docker",
          "$PAN_BUILD_DIR/src/apps/shared",
          "/builds/prisma-access/sase-cosmos-group/sase-controller-group/orchestrator/src/apps/shared",
          "build_number.txt",
          "build.txt",
          "build_version.txt",
          "common_version.txt",
          "code_coverage/"
        ],
        "expire_in": "1 day"
      },
      "allow_failure": false
};

local build_upload_ecr(branchName, build_version) =
{
  "stage": "build_upload_ecr",
  "rules": [
        {
            "if": "($CI_COMMIT_BRANCH || $CI_MERGE_REQUEST_ID)",
            "when": "on_success"

        }
    ],  
  "extends": [
    ".kaniko upload ecr",
  ],
  "variables": {
    "DOCKERFILE": "docker/Dockerfile.saasinfra",
    "IMAGE_NAME_TEMPLATE": default_branch_name,
    "IMAGE_SUFFIX": "",
    "IMAGE_TAG_TEMPLATE": "`cat build_version.txt`-`cat build_number.txt`_${CI_COMMIT_SHORT_SHA}",
    "AWS_ACCOUNT_ID": qa_aws_account_id,
    "AWS_REGION": qa_aws_region,
    "AWS_ACCESS_KEY_ID": "${" + qa_aws_access_key_id + "}",
    "AWS_SECRET_ACCESS_KEY": "${" + qa_aws_secret_access_key + "}",
    "KANIKO_EXTRA_ARGS": "--build-arg ARTIFACTORY_USERNAME=${ARTIFACTORY_API_USERNAME} --build-arg ARTIFACTORY_PASSWORD=${ARTIFACTORY_API_PASSWORD} --build-arg BUILD_VERSION=" + build_version + ".0rc" //This ensures we pick either release candidate(dev/develop) or release (release/<version>) builds 
  },
  "artifacts": {
     "paths": [
       "build_number.txt",
       "build_version.txt",
       "common_version.txt"
     ],
     "expire_in": "1 day"
   }
};

local set_image_tag_value() = [
    if std.extVar('SERVICE_VERSION') == "" then
        "export IMAGE_TAG_TEMPLATE=`cat build_version.txt`-`cat build_number.txt`_${CI_COMMIT_SHORT_SHA}"
    else
        "export IMAGE_TAG_TEMPLATE=$(cat service_version.txt | grep SERVICE_VERSION | cut -d = -f2)",
        
];

local download_image_from_ecr(region, branchName, aws_env) = [
    if aws_env == "qa" then
        "echo \"no need to download image as it is qa environment\""
    else
        "echo $aws_env",
        "export aws_region=\""+region + "\"",
        "export aws_env=\""+aws_env + "\"",
        "export AWS_ACCESS_KEY_ID=${" + dest_aws_access_key_id + "}",
        "export AWS_SECRET_ACCESS_KEY=${" + dest_aws_secret_access_key + "}",
        "export QA_AWS_ACCESS_KEY_ID=${" + qa_aws_access_key_id + "}",
        "export QA_AWS_SECRET_ACCESS_KEY=${" + qa_aws_secret_access_key + "}",
        "mkdir ~/.aws",
        "echo [profile ${aws_env}] >> ~/.aws/config",
        "echo 'region = us-west-2' >> ~/.aws/config",
        "echo 'output = json' >>  ~/.aws/config",
        "echo [profile qa] >> ~/.aws/config",
        "echo 'region = us-west-2' >> ~/.aws/config",
        "echo 'output = json' >>  ~/.aws/config",
        "echo [${aws_env}] >> ~/.aws/credentials",
        "echo aws_access_key_id = ${AWS_ACCESS_KEY_ID} >> ~/.aws/credentials",
        "echo aws_secret_access_key = ${AWS_SECRET_ACCESS_KEY} >>  ~/.aws/credentials",
        "echo [qa] >> ~/.aws/credentials",
        "echo aws_access_key_id = ${QA_AWS_ACCESS_KEY_ID} >> ~/.aws/credentials",
        "echo aws_secret_access_key = ${QA_AWS_SECRET_ACCESS_KEY} >>  ~/.aws/credentials",
        "cat ~/.aws/config",
        "cat ~/.aws/credentials",
        "./docker/scripts/download_image.sh " + default_branch_name + " " + aws_env + " " + dest_aws_account_id + " " + region
];

local pull_from_qa_upload_to_dest_env(region,branchName, aws_access_key_id,aws_secret_access_key, aws_env,account_id, aws_cluster_name) =
{
      "stage": "pull_from_qa_upload_to_dest_env",
      "tags": ["gce"],
      "image": "docker.art.code.pan.run/build-tools--image-alpine:3.ep3",
      "variables": {
       "DOCKER_HOST": "tcp://docker:2375",
       "DOCKER_DRIVER": "overlay2",
       "DOCKER_TLS_CERTDIR": ""
      },
      "services": [
        {
          "name": "docker.art.code.pan.run/build-tools--image-dind:latest",
          "alias": "docker"
        }
    ],
    "script": [
        "apk update && apk add --no-cache docker-cli",
        "apk add --no-cache python3 py3-pip",
        "pip3 install --break-system-packages --extra-index-url=https://art.code.pan.run/artifactory/api/pypi/pypi.org/simple/ --index-url=https://art.code.pan.run/artifactory/api/pypi/pypi/simple awscli",
        "aws --version",
        set_image_tag_value(),
        download_image_from_ecr(region, default_branch_name, dest_env_to_upload_image)
    ]
};

local orch_deploy(region,branchName, aws_access_key_id,aws_secret_access_key, aws_env,account_id, aws_cluster_name) =
{
    "stage": "orch_deploy",
    "image": "docker.art.code.pan.run/build-tools--image-aws-tools:0.0.1",
    "rules": [
        {
            "if": "((\"" + deploy_env + "\" != '') || ($CI_COMMIT_BRANCH == \"" + branchName + "\") || ($CI_MERGE_REQUEST_ID && (\"" + MR_ENV + "\" ==  \"" + aws_env + "\")))",
            "when": "on_success"
        }
    ],

    "script": [
      "export SKIP_DEPLOYMENT=$(cat skip_deployment.txt)",
      "echo $SKIP_DEPLOYMENT",
      "if [ \"$SKIP_DEPLOYMENT\" = \"True\" ]; then exit; fi",
      set_image_tag_value(),
      "echo $IMAGE_TAG_TEMPLATE",
      "export PAN_BUILD_DIR=$(pwd)",
      "echo $PAN_BUILD_DIR",
      "export DIR_PATH=$PAN_BUILD_DIR/k8s/helmcharts/saasinfra",
      "echo $DIR_PATH",
      "git submodule add --force https://gitlab-ci-token:${CI_JOB_TOKEN}@code.pan.run/prisma-access/sase-cosmos-group/sase-controller-group/pa-infra-group/pa-infra-components-envs.git",
      "ls -lthr",
      "cp -R pa-infra-components-envs/k8s/helmcharts/saasinfra/* k8s/helmcharts/saasinfra",
      "ls -lthr k8s/helmcharts/saasinfra/",
      "python3 ./docker/scripts/replace_file_values.py "+ default_branch_name  + " " + aws_env + " " + region + " " + deployment_stack,
      "aws --version",
      "pip install --upgrade awscli",
      "aws --version",
      "aws --profile default configure set aws_access_key_id " + "${" + aws_access_key_id + "}",
      "aws --profile default configure set aws_secret_access_key " + "${" + aws_secret_access_key + "}",
      "curl -LO https://get.helm.sh/helm-v3.8.2-linux-amd64.tar.gz",
      "tar -zxvf helm-v3.8.2-linux-amd64.tar.gz",
      "rm -f /usr/local/bin/helm",
      "ls | grep helm",
      "mv linux-amd64/helm /usr/local/bin/helm",
      "helm version",
      "rm -f ~/.kube/config",
      "aws eks update-kubeconfig --name "+  aws_cluster_name + " --region " + region +"",
      "pwd",
      "rm -f /usr/local/bin/kubectl",
      "curl -LO https://dl.k8s.io/release/v1.20.0/bin/linux/amd64/kubectl",
      "chmod +x kubectl",
      "mkdir -p /usr/local/bin",
      "cp kubectl /usr/local/bin/",
      "ls /usr/local/bin/ | grep kubectl",
      "kubectl version --short --client",
      "kubectl config view --minify",
      "helm repo add eks https://aws.github.io/eks-charts",
      "helm upgrade --install aws-load-balancer-controller eks/aws-load-balancer-controller -n kube-system --set clusterName="+aws_cluster_name +" --set serviceAccount.create=false --set serviceAccount.name=aws-load-balancer-controller --set enableServiceMutatorWebhook=false",
      "helm upgrade --install orchestrator k8s/helmcharts/saasinfra -f k8s/helmcharts/saasinfra/values_"+aws_env+".yaml -f k8s/helmcharts/saasinfra/values_image_cyr-"+aws_env+".yaml --namespace default --timeout 60s --debug"

    ],
    "artifacts": {
        "expire_in": "1 day",
        "paths": [
            "common_version.txt",
            "build_version.txt"
        ]
    },
    "allow_failure": false,
    "resource_group": aws_env + '-' + region
};

local orch_deploy_verify(region,branchName, aws_access_key_id,aws_secret_access_key, aws_env,account_id, aws_cluster_name) =
{
    "stage": "orch_deploy_verify",
    "image": "docker.art.code.pan.run/build-tools--image-aws-tools:0.0.1",
    "rules": [
        {
            "if": "((\"" + deploy_env + "\" != '') || ($CI_COMMIT_BRANCH == \"" + branchName + "\") || ($CI_MERGE_REQUEST_ID && (\"" + MR_ENV + "\" ==  \"" + aws_env + "\")))",
            "when": "on_success"
        }
    ],
    "script": [

      "aws --version",
      "pip install --upgrade awscli",
      "aws --version",
      "aws --profile default configure set aws_access_key_id " + "${" + aws_access_key_id + "}",
      "aws --profile default configure set aws_secret_access_key " + "${" + aws_secret_access_key + "}",
      "curl -LO https://get.helm.sh/helm-v3.8.2-linux-amd64.tar.gz",
      "tar -zxvf helm-v3.8.2-linux-amd64.tar.gz",
      "rm -f /usr/local/bin/helm",
      "ls | grep helm",
      "mv linux-amd64/helm /usr/local/bin/helm",
      "helm version",
      "rm -f ~/.kube/config",
      "aws eks update-kubeconfig --name "+ aws_cluster_name + " --region " + region +"",
      "rm -f /usr/local/bin/kubectl",
      "curl -LO https://dl.k8s.io/release/v1.20.0/bin/linux/amd64/kubectl",
      "chmod +x kubectl",
      "mkdir -p /usr/local/bin",
      "cp kubectl /usr/local/bin/",
      "ls /usr/local/bin/ | grep kubectl",
      "kubectl version --short --client",
      "kubectl get namespace",
      "kubectl get pod -n orchestrator",
      "kubectl config view --minify",
      "kubectl describe pods -n orchestrator --request-timeout=60s | grep -e Image: -e ^Name:",
      post_checks()
    ]+
    updateVersionInDB(region, aws_env, aws_access_key_id, aws_secret_access_key),
    "allow_failure": false,
    "resource_group": aws_env + '-' + region
};

local setUpEnvVars(branchName,service_version) =
{
    "stage": "envVarSetup",
    "image": "docker.art.code.pan.run/build-tools--image-aws-tools:0.0.1",
    "rules": [
        {
            "if": "(\"" + deploy_env + "\" != '')",
            "when": "on_success"
        }
    ],
    "script": [
        "export PAN_BUILD_DIR=$(pwd)",
        "echo $PAN_BUILD_DIR",
        "export SERVICE_VERSION=" + service_version,
        "echo \"SERVICE_VERSION=$SERVICE_VERSION\" >> service_version.txt",
        "echo \"SERVICE_ZIP=$SERVICE_VERSION\" >> build.txt",
        "export COMMON_LAYER_VER=`awk -F'==' '/common-lib-layer-ver/ {print $2}' requirements-layer.txt`",
        "echo \"COMMON_LAYER_VER=$COMMON_LAYER_VER\" >> common_version.txt",
        "echo \"COMMON_LAYER_ZIP=$COMMON_LAYER_VER\" >> build.txt",
    ],
    "artifacts": {
        "expire_in": "1 day",
        "paths": [
            "service_version.txt",
            "common_version.txt",
            "build.txt"
        ]
    },
    "allow_failure": false
};

local preEnvCheck(branchName, aws_access_key_id, aws_secret_access_key, aws_env) =
{
    "extends": [
        ".pre_env",
    ],
    "variables": {
        "BRANCH_NAME": branchName,
        "AWS_ENV": aws_env,
        "MR_ENV": MR_ENV,
        "DEFAULT_ENV": DEFAULT_ENV,
        "AWS_ACCESS_KEY_ID" : "${" + aws_access_key_id + "}",
        "AWS_SECRET_ACCESS_KEY": "${" + aws_secret_access_key + "}",
        "FORCE_DEPLOY_SAME_COMMIT_HASH" : std.extVar('FORCE_DEPLOY_SAME_COMMIT_HASH'),
    },
};

local postEnvStep(region, branchName, aws_access_key_id, aws_secret_access_key, aws_env) =
{
    "extends": [
        ".post_env",
    ],
    "variables": {
        "BRANCH_NAME": branchName,
        "AWS_ENV": aws_env,
        "MR_ENV": MR_ENV,
        "DEFAULT_ENV": DEFAULT_ENV,
        "FORCE_DEPLOY_SAME_COMMIT_HASH" : std.extVar('FORCE_DEPLOY_SAME_COMMIT_HASH'),
        "REGION": region,
        "AWS_ACCESS_KEY_ID" : "${" + aws_access_key_id + "}",
        "AWS_SECRET_ACCESS_KEY": "${" + aws_secret_access_key + "}",
    },
};

local automationtest(region,branchName, aws_access_key_id,aws_secret_access_key, aws_env) =
{
    "extends": [
        ".automation_test",
    ],
    "variables": {
        "BRANCH_NAME": branchName,
        "AWS_ENV": aws_env,
        "MR_ENV": MR_ENV,
        "DEFAULT_ENV": DEFAULT_ENV,
        "REGION": region,
        "AWS_ACCESS_KEY_ID" : "${" + aws_access_key_id + "}",
        "AWS_SECRET_ACCESS_KEY": "${" + aws_secret_access_key + "}",
        "PAN_TEST_COMMAND": "pan.test --testbed data/testbeds/pa_infra_component_test_bed.yaml --run-file data/features/orchestration/orchestration_runlist.yaml --noDB --console-log --logging-level DEBUG --reportportal --product-id prisma_access --component SASE_CICD --feature Orchestration --submitter mnadella --team sase-qa --run-type smoke --launchName ${CI_JOB_NAME} --html .",
        "USE_GCLOUD_AUTH": "true"
    },
};

local createSecretsSteps(branchName) =
{
  [envName + "_create_secrets_" + region]: create_secrets(region, allConfigs[envName]['meta']['branch'], allConfigs[envName]['meta']['aws_access_key_id'], allConfigs[envName]['meta']['aws_secret_access_key'], allConfigs[envName]['meta']['aws_env'], allConfigs[envName]['meta']['account_id'])
  for envName in envs
  for region in allConfigs[envName]['meta']['regions']
};

local prep_and_ut_coverageSteps(branchName) =
{
  [branchName + "_prep_and_ut_coverage"]: prep_and_ut_coverage()
};

local coverageStep(branchName) =
{
  [branchName + "_coverage"]: verify_coverage()
};

local buildUploadEcrSteps(branchName, build_version) =
{
  [branchName + "_build_upload_ecr_"]: build_upload_ecr(branchName, build_version)
};

local orchDeploySteps(branchName) =
{
  [envName + "_orch_deploy_" + region]: orch_deploy(region, allConfigs[envName]['meta']['branch'], allConfigs[envName]['meta']['aws_access_key_id'], allConfigs[envName]['meta']['aws_secret_access_key'], allConfigs[envName]['meta']['aws_env'], allConfigs[envName]['meta']['account_id'], allConfigs[envName]['meta']['eks_cluster_name'])
  for envName in envs
  for region in allConfigs[envName]['meta']['regions']
};

local orchVerificationSteps(branchName) =
{
  [envName + "_orch_deploy_verify_" + region]: orch_deploy_verify(region, allConfigs[envName]['meta']['branch'], allConfigs[envName]['meta']['aws_access_key_id'], allConfigs[envName]['meta']['aws_secret_access_key'], allConfigs[envName]['meta']['aws_env'], allConfigs[envName]['meta']['account_id'], allConfigs[envName]['meta']['eks_cluster_name'])
  for envName in envs
  for region in allConfigs[envName]['meta']['regions']
};

local testSteps(branchName) =
{
    [envName + "_automation_test_" + region]: automationtest(region, branchName, allConfigs[envName]['meta']['aws_access_key_id'], allConfigs[envName]['meta']['aws_secret_access_key'], allConfigs[envName]['meta']['aws_env'])
    for envName in envs
    for region in allConfigs[envName]['meta']['active_region']
};

local preEnv(branchName) =
{
    [envName + "_pre_env"]:preEnvCheck(branchName, allConfigs[envName]['meta']['aws_access_key_id'], allConfigs[envName]['meta']['aws_secret_access_key'], envName)
    for envName in envs
};

local postEnv(branchName) =
{
    [envName + "_post_env"]:postEnvStep(region, branchName, allConfigs[envName]['meta']['aws_access_key_id'], allConfigs[envName]['meta']['aws_secret_access_key'], envName)
    for envName in envs
    for region in allConfigs[envName]['meta']['active_region']
};

local deployTFSteps(branchName) =
{
    [envName + "_terraform_plan_and_apply_" + region]: terraform_plan_and_apply(region, branchName, allConfigs[envName]['meta']['aws_access_key_id'], allConfigs[envName]['meta']['aws_secret_access_key'], allConfigs[envName]['meta']['aws_env'])
    for envName in envs
    for region in allConfigs[envName]['meta']['regions']
};

local envSteps(branchName, service_version) =
{
    ["env_setup"]: setUpEnvVars(branchName, service_version)
};

local pullFromQAUploadDestEnv(branchName, service_version) =
{
    ["pull_from_qa_upload_to_dest_env_" + region]: pull_from_qa_upload_to_dest_env(region, allConfigs[envName]['meta']['branch'], allConfigs[envName]['meta']['aws_access_key_id'], allConfigs[envName]['meta']['aws_secret_access_key'], allConfigs[envName]['meta']['aws_env'], allConfigs[envName]['meta']['account_id'], allConfigs[envName]['meta']['eks_cluster_name'])
    for envName in envs
    for region in allConfigs[envName]['meta']['active_region']
};

local generateSteps(branchName) =
if std.extVar('CREATE_SECRETS_ONLY') == "True" then createSecretsSteps(branchName)
else if std.extVar('SERVICE_VERSION') == "" then local build_version = std.stripChars(importstr './service_version/service_version.txt', '\n'); preEnv(branchName) + prep_and_ut_coverageSteps(branchName) +  buildUploadEcrSteps(branchName, build_version) + createSecretsSteps(branchName) + orchDeploySteps(branchName) + orchVerificationSteps(branchName) + deployTFSteps(branchName) + testSteps(branchName) + postEnv(branchName)
else if ((env_type == 'dev' && env_mode == 'commercial') || (env_type == 'qa' && env_mode == 'commercial')) && std.extVar('SERVICE_VERSION') != "" then preEnv(branchName) + createSecretsSteps(branchName) + envSteps(branchName, std.extVar('SERVICE_VERSION')) + orchDeploySteps(branchName) + orchVerificationSteps(branchName) + deployTFSteps(branchName) + postEnv(branchName)
else preEnv(branchName) + createSecretsSteps(branchName) + envSteps(branchName, std.extVar('SERVICE_VERSION')) + pullFromQAUploadDestEnv(branchName,std.extVar('SERVICE_VERSION')) + orchDeploySteps(branchName) + orchVerificationSteps(branchName) + deployTFSteps(branchName) + postEnv(branchName);

generateBoilerPlate()
+
generateSteps(commit_branch)





/*
Stages :

#"pre_env" - This stage checks if we need to re deploy the same commit - redeploy same commit FORCE_DEPLOY_SAME_COMMIT_HASH = True
#"ut_coverage" - #Downloads common code from artifactory and runs python + go unit tests and stage the orch+common code directory for next stage to build/upload docker image to ecr
#"build_upload_ecr" - #Build/upload docker image using dockerfile
#"envVarSetup" - #setting up some env variable required for build/deployment
#"create_secrets" - #generate secrets and push to aws secrets manager
#"orch_deploy" - #Deploy orch pods to k8s cluster
#"orch_deploy_verify" - #Verify orch pods deployed successfully
#"automation_test"
#"post_env" - #approve or disapprove mr based on automation cases result..

From where to refer docker image:

Below env refers docker image from QA ECR
        k8s/helmcharts/saasinfra/values_image_cyr-auto.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev1.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev10.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev11.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev12.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev13.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev14.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev15.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev16.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev17.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev18.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev19.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev2.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev20.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev21.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev3.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev4.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev5.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev6.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev7.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev8.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-dev9.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-qa2.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-qa3.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-qa4.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-qa5.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-qa6.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-qa7.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-qa8.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-qa.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-test.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-staging.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-tme.yaml


Below env refers docker image from RESPECTIVE Env ECR
        k8s/helmcharts/saasinfra/values_image_cyr-lab.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-prod.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-prod2.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-prod3.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-prod4.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-prod5.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-prod6.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-prod7.yaml
        k8s/helmcharts/saasinfra/values_image_cyr-high-gov-dev.yaml


Below env expects image to be present in same env
    k8s/helmcharts/saasinfra/values_image_cyr-highdev.yaml


Note : For any environment, if we need to change the account for ECR, go to above specified file and update the repository name with the source account ID.
*/
