*~
Icon
.DS_Store
.DS_Store?
._*
ehthumbs.db
Thumbs.db
Desktop.ini

*.gz

logs
*.log
npm-debug.log*

pids
*.pid
*.seed

node_modules

.idea
.vscode

*.cli.xml
*.out.xml
*.sorted.xml
generated
.buildnum
pkg/.image_release
pkg/BUILD
pkg/BUILDROOT
pkg/RPMS
pkg/SOURCES
pkg/SRPMS
pkg/images
pkg/tmp
src/scripts/p4cl.txt
src/xsl/add_prune_on.xsl

.dockerbuildnum
src/cfg/xform/.bld/*
dbdiff.sql

__pycache__/
venv/
.venv/
cscope.files
cscope.out
.tags
p4cl.txt
bin/
*.pb.go
*_grpc.pb.go
*_pb2.py
*_pb2_grpc.py
*.sw?
coverity_config.xml
template-*
idir
*.pyc
.report
.coverage*
htmlcov*
tests/**/*.tmpl

src/apps/orchestrator/provision_service/devenv/
src/apps/orchestrator/upgrade_service/devenv/
src/apps/temp
src/apps/shared
src/apps/orchestrator/provision_service/pkg/
