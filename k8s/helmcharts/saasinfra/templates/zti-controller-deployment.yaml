apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: zti-controller
  name: zti-controller
  namespace: {{ .Values.namespace }}
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: zti-controller
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: zti-controller
      annotations:
        timestamp: {{ now | quote }}
    spec:
      volumes:
        - name: vault
          secret:
            secretName: orch-tls-certs
      containers:
        - command: ["/bin/sh", "-ec", "/usr/local/bin/init && /usr/local/bin/zti_controller"]
          image: "{{ .Values.saasinfra.image.repository }}:{{ .Values.saasinfra.image.tag }}"
          imagePullPolicy: Always
          name: zti-controller
          resources:
            requests:
              ephemeral-storage: "1Gi"
            limits:
              ephemeral-storage: "2Gi"
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          env:
            - name: AWS_ENV
              value: {{ .Values.saasinfra.aws_env }}
            - name: LOG_MODE
              value: {{ .Values.log_mode }}
            - name: ORCH_CLIENT_SSL_CERT
              value: /vault/secrets/tls.crt
            - name: ORCH_CLIENT_SSL_KEY
              value: /vault/secrets/tls.key
            - name: ORCH_CLIENT_SSL_CA
              value: /vault/secrets/ca.crt
          volumeMounts:
            - name: vault
              mountPath: /vault/secrets/
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: {{ .Values.saasinfra.service_account }}
      serviceAccountName: {{ .Values.saasinfra.service_account }}
      terminationGracePeriodSeconds: 30
