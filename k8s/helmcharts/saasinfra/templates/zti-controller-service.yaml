apiVersion: v1
kind: Service
metadata:
#  annotations:
#    service.beta.kubernetes.io/aws-load-balancer-type: nlb-ip
    #service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
#    service.beta.kubernetes.io/aws-load-balancer-name: "zti-controller-nlb"
#    service.beta.kubernetes.io/aws-load-balancer-scheme: internal
  name: zti-controller
  namespace: {{ .Values.namespace }}
spec:
  selector:
    app: zti-controller
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
