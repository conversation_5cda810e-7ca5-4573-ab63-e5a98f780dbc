include $(PAN_BUILD_DIR)/src/mk/version.mk
# XXX: What's a good way to do this? Our python 3rd party requirements are copied into
# /python2_dependencies. Unlike our statically compiled binaries, we need to have our
# python scripts and 3rd party modules copied into the docker container (and set the syspath
# accordingly) 
#include $(PAN_BUILD_DIR)/src/mk/docker.mk
REGISTRY ?= docker-saas-infra.af.paloaltonetworks.local
COMPONENT ?= saasinfra
PREFIX ?= $(shell git rev-parse --abbrev-ref HEAD)
POSTFIX ?= $(PAN_SW_VERSION)
GITREV ?= $(shell git rev-parse HEAD)
ENVIRONMENT ?= qa

IMAGETAG := $(shell echo $(REGISTRY)/$(PREFIX)/$(COMPONENT)-$(POSTFIX):$(GITREV) | tr A-Z a-z)
#ECR_IMAGETAG := $(shell echo $(ECR_REPO)/$(PREFIX):$(POSTFIX)-$(GITREV) | tr A-Z a-z)
ECR_IMAGETAG := $(shell echo $(ECR_REPO):$(POSTFIX)-$(GITREV) | tr A-Z a-z)



APPS_S := $(notdir $(shell ls $(PAN_BUILD_DIR)/bin/*-linux-amd64))
APPS_D := $(subst -linux-amd64,,$(APPS_S))
TEST ?= ravi



.PHONY: realall

ifeq ($(PUBLISH_TO_ECR),yes)
@echo "PUBLISH_TO_ECR : $(PUBLISH_TO_ECR)"
realall: deployment upload
else
realall: deployment
endif

.PHONY: deployment
deployment:
	#@echo "Building Docker image $(IMAGETAG)"
	#@echo "ECR Tag : $(ECR_IMAGETAG)"
	#@echo "APPS_S : $(APPS_S)"
	#@echo "APPS_D : $(APPS_D)"
	@-rm -rf $(PAN_BUILD_DIR)/docker/bin
	@-mkdir -p $(PAN_BUILD_DIR)/docker/bin
	# Only statically compiled applications are automatically loaded into the container.
	# Non compiled services need to make a change in Dockerfile.saasinfra to explicitly include the
	# source files.
	@for app in $(APPS_D); do cp $(PAN_BUILD_DIR)/bin/$${app}-linux-amd64 $(PAN_BUILD_DIR)/docker/bin/$${app}; done
	#@docker build --no-cache --force-rm=true -t $(IMAGETAG) .. -f Dockerfile.saasinfra
	#@if [ -z "$(ECR_REPO)" ]; then \
	#    echo "Skipping tagging the image for ECR publish"; \
	#else \
	#    echo "Tagging image for ECR publish"; \
	#    docker tag $(IMAGETAG) $(ECR_IMAGETAG); \
	#fi

.PHONY: local_build
local_build:
	@echo "ECR Tag : $(ECR_IMAGETAG)"
	@echo "IMAGETAG : $(IMAGETAG)"
	@-rm -rf $(PAN_BUILD_DIR)/docker/bin
	@-mkdir -p $(PAN_BUILD_DIR)/docker/bin
	# Only statically compiled applications are automatically loaded into the container.
	# Non compiled services need to make a change in Dockerfile.saasinfra to explicitly include the
	# source files.
	@for app in $(APPS_D); do cp $(PAN_BUILD_DIR)/bin/$${app}-linux-amd64 $(PAN_BUILD_DIR)/docker/bin/$${app}; done
	mkdir -p $(PAN_BUILD_DIR)/src/apps/temp
	chmod 777 $(PAN_BUILD_DIR)/src/apps/temp
	cp -r $(COMMON_LIB_PATH)/. $(PAN_BUILD_DIR)/src/apps/temp
	@docker build --no-cache --force-rm=true -t $(IMAGETAG) .. -f Dockerfile.saasinfra --build-arg COMMON_LIB_DIR=src/apps/temp
	rm -rf $(PAN_BUILD_DIR)/src/apps/temp
	echo "Tagging image for ECR publish"; \
	docker tag $(IMAGETAG) $(ECR_IMAGETAG); \

upload:
	@echo "Uncomment me to upload last tagged image to ECR"
	@./upload_to_dest_ecr.sh $(IMAGETAG) $(ECR_IMAGETAG) $(ENVIRONMENT) $(ECR_REPO)


#GCR_HOSTNAME  := $(shell echo "gcr.io")
#GCR_PROJECT_ID := $(shell echo "pa-sase-insights-dev-02")
#GCR_IMAGE := $(shell echo "sase-fabric")
#upload_to_gcr:
#	@echo "Upload last tagged image to Google Container Registry"
#	@./upload_to_gcr.sh $(IMAGETAG) $(GCR_HOSTNAME) $(GCR_PROJECT_ID) $(GCR_IMAGE)


.PHONY: help
help:
	@echo ""
	@echo "deployment           - generate deployment docker image"
	@echo ""
