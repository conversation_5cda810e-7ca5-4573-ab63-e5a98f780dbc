#!/bin/bash
# Script to upload the docker image to ECR
# The easiest way to run this is the `cd docker && PUBLISH_TO_ECR=yes ECR_REPO=saasinfra make`

aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin ************.dkr.ecr.us-west-2.amazonaws.com
BUILDNUM=$(cat $PAN_BUILD_DIR/.buildnum)
GIT_BRANCH=$(git branch | grep \* | cut -d ' ' -f2)
IMAGETAG=$1
ECR_IMAGETAG=$2

echo "IMAGETAG : " $IMAGETAG
echo "ECR_IMAGETAG : " $ECR_IMAGETAG

# qa2 ************
# dev12 ************
# Need to substitute the account ID for the corresponding CYR env
docker tag $IMAGETAG ************.dkr.ecr.us-west-2.amazonaws.com/$ECR_IMAGETAG-$BUILD<PERSON><PERSON>

docker push ************.dkr.ecr.us-west-2.amazonaws.com/$ECR_IMAGETAG-$BUILDNUM
