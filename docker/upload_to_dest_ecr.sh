IMAGETAG=$1
ECR_IMAGETAG=$2
ENV=$3
REPO_NAME=$4

echo "IMAGETAG : " $IMAGETAG
echo "ECR_IMAGETAG : " $ECR_IMAGETAG
echo "ENV : " $ENV
echo "REPO_NAME : " $REPO_NAME

BUILDNUM=$(cat $PAN_BUILD_DIR/.buildnum)

aws ecr describe-repositories --repository-names $REPO_NAME --profile $ENV --region us-west-2 --output text|| aws ecr create-repository --repository-name $REPO_NAME --profile $ENV --region us-west-2
echo "Creating permission policy..."
policy_document='{
      "Version": "2012-10-17",
      "Statement": [
        {
          "Sid": "AllowPull",
          "Effect": "Allow",
          "Principal": "*",
          "Action": [
            "ecr:BatchCheckLayerAvailability",
            "ecr:BatchGetImage",
            "ecr:DescribeImages",
            "ecr:DescribeRepositories",
            "ecr:GetDownloadUrlForLayer"
          ],
          "Condition": {
            "ForAnyValue:StringLike": {
              "aws:PrincipalOrgID": "o-vc84v8nvfy"
            }
          }
        }
      ]
    }'
aws ecr set-repository-policy --repository-name $REPO_NAME --policy-text "$policy_document" --region us-west-2
echo "Permission policy created successfully."


aws_password=`aws ecr get-login-password --region us-west-2 --profile $ENV`
echo "aws_password is : $aws_password"
target_account_id=`aws sts get-caller-identity --query Account --output text --profile $ENV --region us-west-2`
echo "target_account_id is : $target_account_id"
docker login --username AWS --password $aws_password $target_account_id.dkr.ecr.us-west-2.amazonaws.com


docker tag $IMAGETAG $target_account_id.dkr.ecr.us-west-2.amazonaws.com/$ECR_IMAGETAG-$BUILDNUM
docker push $target_account_id.dkr.ecr.us-west-2.amazonaws.com/$ECR_IMAGETAG-$BUILDNUM