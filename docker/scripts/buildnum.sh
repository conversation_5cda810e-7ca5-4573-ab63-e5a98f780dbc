#!/bin/bash
aws --profile default configure set aws_access_key_id $1
aws --profile default configure set aws_secret_access_key $2
pwd
if aws s3 cp s3://${SERVICE_ARTIFACTS_S3_BUCKET_US_WEST_2}/artifacts/${SERVICE_NAME}/${CI_COMMIT_BRANCH}/.dockerbuildnum . 2>/dev/null; then
  echo ".dockerbuildnum exists"
  cat .dockerbuildnum
  expr `cat .dockerbuildnum` + 1 > .dockerbuildnum
else
  echo ".dockerbuildnum doesn't exist"
  echo "1" > .dockerbuildnum
fi
exit 0