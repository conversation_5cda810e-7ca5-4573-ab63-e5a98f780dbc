import sys
import boto3
import json
import os
import time
awsEnv = sys.argv[1]
region = sys.argv[2]
serviceName = "orchestrator_component_service"
serviceZip = os.environ.get('IMAGE_TAG_TEMPLATE')
commonVersion = os.environ.get('COMMON_LAYER_VER')

print(awsEnv, region, serviceName, serviceZip)


def remove_prefix(text, prefix):
    if text.startswith(prefix):
        return text[len(prefix):]
    return text


def update_service_version(version_details=None):
    print(">>>>>inside update_orch_service_version")
    if region == 'us-west-2':
        client = boto3.client('lambda', region_name=region)
        for name,version in version_details.items():
            try:
                sql = "replace into orch_cfg (name,value) values ('%s','%s')" % (name, version)
                print(f"sql = {sql}")
                payload = {"sql": sql, "params": None}
                response = client.invoke(FunctionName='dbaccess', Payload=json.dumps(payload))
                response_payload = json.loads(response['Payload'].read())
                if response_payload['ok']:
                    print("Successfully updated the service version in orch_cfg table")
                else:
                    print("DB error when trying to update service version, response: " + response_payload)
            except Exception as ex:
                print("Exception occurred while updating service version " + str(ex))

def get_component_versions():
    component_version_details = dict()
    service_version = remove_prefix(serviceZip, serviceName + '-')
    commmon_version = remove_prefix(commonVersion, "COMMON_LAYER_VER=")
    component_version_details[serviceName + "_version"] = service_version
    component_version_details["version"] = service_version
    component_version_details[serviceName + "_common_layer_version"] = commmon_version
    print(f"component version data = {component_version_details}")
    return component_version_details


if __name__ == '__main__':
    comp_versions = get_component_versions()
    update_service_version(version_details=comp_versions)
