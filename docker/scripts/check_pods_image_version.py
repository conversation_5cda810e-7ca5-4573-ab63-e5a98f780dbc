# __author__ = '<PERSON>'
# Define the kubectl commands
import subprocess
import os
import sys
image_uploaded_on_ecr = os.environ.get('IMAGE_TAG_TEMPLATE')



def is_pod_in_set(pods_set, pod_name):
    for pod_prefix in pods_set:
        if pod_name.startswith(pod_prefix):
            return True
    return False

def extract_image_version(image_tag):
    version = image_tag.split(":")[-1]
    return version

get_pod_names_cmd = "kubectl get pods --no-headers=true -norchestrator | awk '{print $1}'" # to find list of pods in orchestrator namespace
get_image_cmd_template = "kubectl get pod {} -o=jsonpath='{{.spec.containers[*].image}}' -norchestrator" # to find image for a given pod

# Run the first kubectl command to get pod names
pod_names_process = subprocess.Popen(get_pod_names_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
pod_names_output, _ = pod_names_process.communicate()
pod_names_list = pod_names_output.decode().splitlines()
print(f"pod_names_list = {pod_names_list}") # pods name list

# Iterate over each pod name and run the second kubectl command to get container images
images = []
for pod_name in pod_names_list:
    get_image_cmd = get_image_cmd_template.format(pod_name)
    image_process = subprocess.Popen(get_image_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    image_output, _ = image_process.communicate()
    images.append(image_output.decode().strip())
print(f"images = {images}")

# Print or store the resulting images list
print("Container images for each pod:")
pods_set = {"avisar-server","inst-mgmt","notification","orchestration","provision-service","upgrade-service"}
misma_tch_map = {}
for pod_name, image in zip(pod_names_list, images):
    print(f"Pod: {pod_name}, Image: {image}")
    if is_pod_in_set(pods_set,pod_name):
        print(f"qualified Pod: {pod_name} uses Image: {image}")
        image_used_in_pod_deployment = extract_image_version(image)
        if image_used_in_pod_deployment != image_uploaded_on_ecr:
            misma_tch_map[pod_name] = image_used_in_pod_deployment
        else:
            print(f"pod {pod_name} ecr image = {image_uploaded_on_ecr} and image used to deployed pod = {image_used_in_pod_deployment} are same !!!")

if len(misma_tch_map) > 0:
    print("Pods with mismatched image versions:")
    for podname, image_used_in_pod_deployment in misma_tch_map.items():
        print(f"Mismatch found! Pod '{podname}' is using image version '{image_used_in_pod_deployment}' but the uploaded image on ECR is '{image_uploaded_on_ecr}'")
    sys.exit(1)