#!/bin/bash
aws --profile default configure set aws_access_key_id $1
aws --profile default configure set aws_secret_access_key $2
x=$1
echo "x is : $x"
y=$2
echo "y is : $y"
REPO_NAME=$3
echo "REPO_NAME is: $REPO_NAME"
# Check if the repository already exists us-west-2
aws ecr describe-repositories --repository-names "$REPO_NAME" --region us-west-2

# If the repository does not exist, create it
if [ $? -ne 0 ]; then
    aws ecr create-repository --repository-name "$REPO_NAME" --region us-west-2
else
    echo "Repository already exists."
fi
# Create a permission policy for the repository
echo "Creating permission policy..."
policy_document='{
      "Version": "2012-10-17",
      "Statement": [
        {
          "Sid": "AllowPull",
          "Effect": "Allow",
          "Principal": "*",
          "Action": [
            "ecr:BatchCheckLayerAvailability",
            "ecr:BatchGetImage",
            "ecr:DescribeImages",
            "ecr:DescribeRepositories",
            "ecr:GetDownloadUrlForLayer"
          ],
          "Condition": {
            "ForAnyValue:StringLike": {
              "aws:PrincipalOrgID": "o-vc84v8nvfy"
            }
          }
        }
      ]
    }'
aws ecr set-repository-policy --repository-name "$REPO_NAME" --policy-text "$policy_document" --region us-west-2
echo "Permission policy created successfully."