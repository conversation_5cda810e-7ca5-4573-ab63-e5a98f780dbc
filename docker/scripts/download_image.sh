#!/bin/sh
branch_name=$1
echo "branch_name is : $branch_name"
echo "IMAGE_TAG_TEMPLATE is : $IMAGE_TAG_TEMPLATE"
tag_value=$IMAGE_TAG_TEMPLATE
echo "tag_value is : $tag_value"
dest_aws_env=$2
echo "dest_aws_env is : $dest_aws_env"
dest_aws_region=$4
echo "dest_aws_region is : $dest_aws_region"
saas_infra_image="${branch_name}:${tag_value}"
echo "saas_infra_image is : $saas_infra_image"

aws_password=`aws ecr get-login-password --region us-west-2 --profile qa`
echo "aws_password is : $aws_password"
qa_account_id=`aws sts get-caller-identity --query Account --output text --profile qa --region us-west-2`
echo "qa_account_id is : $qa_account_id"
docker login --username AWS --password $aws_password  $qa_account_id.dkr.ecr.us-west-2.amazonaws.com
docker pull $qa_account_id.dkr.ecr.us-west-2.amazonaws.com/$saas_infra_image


reponame=`echo $saas_infra_image | awk -F':' '{print $1}'`
echo "reponame is : $reponame"

aws ecr describe-repositories --repository-names $reponame --profile $2 --region $4 --output text|| aws ecr create-repository --repository-name $reponame --profile $2 --region $4
echo "Creating permission policy..."
policy_document='{
      "Version": "2012-10-17",
      "Statement": [
        {
          "Sid": "AllowPull",
          "Effect": "Allow",
          "Principal": "*",
          "Action": [
            "ecr:BatchCheckLayerAvailability",
            "ecr:BatchGetImage",
            "ecr:DescribeImages",
            "ecr:DescribeRepositories",
            "ecr:GetDownloadUrlForLayer"
          ],
          "Condition": {
            "ForAnyValue:StringLike": {
              "aws:PrincipalOrgID": "o-vc84v8nvfy"
            }
          }
        }
      ]
    }'
aws ecr set-repository-policy --repository-name $reponame --policy-text "$policy_document" --region $4
echo "Permission policy created successfully."
aws_password=`aws ecr get-login-password --region $4 --profile $2`
echo "aws_password is : $aws_password"
target_account_id=`aws sts get-caller-identity --query Account --output text --profile $2 --region $4`
echo "target_account_id is : $target_account_id"
docker login --username AWS --password $aws_password $target_account_id.dkr.ecr.$dest_aws_region.amazonaws.com
docker tag $qa_account_id.dkr.ecr.us-west-2.amazonaws.com/$saas_infra_image  $target_account_id.dkr.ecr.$dest_aws_region.amazonaws.com/$saas_infra_image || exit 1
echo "Did exit work?"
docker push  $target_account_id.dkr.ecr.$dest_aws_region.amazonaws.com/$saas_infra_image
sleep 10
#clean up docker images on local
docker rmi $target_account_id.dkr.ecr.$dest_aws_region.amazonaws.com/$saas_infra_image
docker rmi $qa_account_id.dkr.ecr.us-west-2.amazonaws.com/$saas_infra_image