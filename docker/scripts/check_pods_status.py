import subprocess
import time
import sys


def check_pods(namespace="orchestrator", max_timeout=300, poll_interval=10):
    """
    Check the status of pods in the specified namespace.

    Args:
    - namespace: Namespace to check for pods (default: "orchestrator").
    - max_timeout: Maximum time to wait for pods to become ready (default: 180 seconds).
    - poll_interval: Interval between each check (default: 10 seconds).

    Returns:
    - True if all pods are running within the timeout, False otherwise.
    """
    start_time = time.time()
    while True:
        try:
            result = subprocess.run(["kubectl", "get", "pods", "-n", namespace], stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE)
            output = result.stdout.decode().split("\n")[1:]  # Remove header and split lines
            for line in output:
                if line.strip():  # Skip empty lines
                    parts = line.split()
                    pod_status = parts[2]
                    print(f"pod_status = {pod_status} , Pod {parts[0]} ")
                    if pod_status.lower() != "running":
                        print(f"Pod '{parts[0]}' is not running. Waiting for {poll_interval} sec")
                        break  # At least one pod is not running, so break the loop
                    else:
                        print(f"Pod '{parts[0]}' is running !!!!")
            else:
                # If all pods are running, exit the loop
                print("All pods are running.")
                return True
        except subprocess.CalledProcessError as e:
            print("Error:", e)
            sys.exit(1)

        elapsed_time = time.time() - start_time
        if elapsed_time >= max_timeout:
            print("Max Timeout reached. Exiting...")
            return False

        time.sleep(poll_interval)


def main():
    if not check_pods():
        print("Assertion failed: Timeout reached. Exiting...")
        sys.exit(1)


if __name__ == "__main__":
    main()