#!/bin/bash
aws --profile default configure set aws_access_key_id $1
aws --profile default configure set aws_secret_access_key $2
# Variables
S3_BUCKET=${SERVICE_ARTIFACTS_S3_BUCKET_US_WEST_2}
FOLDER_PATH="artifacts/${SERVICE_NAME}/${CI_COMMIT_BRANCH}"
FILE_NAME=".dockerbuildnum"

# Check if the S3 bucket exists
if aws s3api head-bucket --bucket "$S3_BUCKET" 2>/dev/null; then
  echo "Bucket exists: $S3_BUCKET"

  # Check if the S3 folder exists
  if aws s3 ls "s3://$S3_BUCKET/$FOLDER_PATH/" 2>/dev/null; then
    echo "Folder exists: $FOLDER_PATH"

    # Check if the file exists in the S3 folder
    if aws s3 ls "s3://$S3_BUCKET/$FOLDER_PATH/$FILE_NAME" 2>/dev/null; then
      echo "File already exists: $FILE_NAME"
    else
      echo "File does not exist. Uploading..."
      aws s3 cp "$FILE_NAME" "s3://$S3_BUCKET/$FOLDER_PATH/$FILE_NAME"
      echo "File uploaded: $FILE_NAME"
    fi
  else
    echo "Folder does not exist. Creating..."
    aws s3api put-object --bucket "$S3_BUCKET" --key "$FOLDER_PATH/"
    echo "Folder created: $FOLDER_PATH"

    # Upload the file to the newly created S3 folder
    aws s3 cp "$FILE_NAME" "s3://$S3_BUCKET/$FOLDER_PATH/$FILE_NAME"
    echo "File uploaded: $FILE_NAME"
  fi
else
  echo "Bucket does not exist: $S3_BUCKET"
fi