# __author__ = '<PERSON> Bhavsar'
import sys
import re
import traceback
import os

REPO_NAME = sys.argv[1]
PA_ENV = sys.argv[2]
REGION = sys.argv[3]
DEPLOYMENT_STACK = sys.argv[4]
DIRECTORY_PATH = os.environ.get('DIR_PATH')
FILE_PATH = f"{DIRECTORY_PATH}/values_image_cyr-{PA_ENV}.yaml"
TEMPLATES_FILE_PATHS = [f"{DIRECTORY_PATH}/templates/api-gw-integration.yaml",f"{DIRECTORY_PATH}/templates/orch-sa.yaml"]
IMAGE_TAG_VALUE = os.environ.get('IMAGE_TAG_TEMPLATE')

print("REPO_NAME:", REPO_NAME)
print("PA_ENV:", PA_ENV)
print("IMAGE_TAG_VALUE:", IMAGE_TAG_VALUE)
print("DEPLOYMENT_STACK:", DEPLOYMENT_STACK)
EXCEPTION_FOR_ROLE_NAME_ENV_LIST = ["gov-pre-prod", "fm-molokai","fh-maui","fh-molokai", "dev17-1", "dev2-1"]
OLD_ROLE_NAME = 'orchestrator'
NEW_ROLE_NAME = "orchestrator-"+REGION

def replace_string_in_file(file_path, old_string, new_string):
    # Read the contents of the file
    print(f'file_path = {file_path}')
    print(f'old_string = {old_string}')
    print(f'new_string = {new_string}')
    try:
        with open(file_path, 'r') as file:
            file_content = file.read()

        # Escape special characters in the new string
        escaped_new_string = re.escape(new_string)

        # Replace the old string with the new string
        modified_content = file_content.replace(old_string, new_string)

        # Write the modified content back to the file
        with open(file_path, 'w') as file:
            file.write(modified_content)
    except Exception as ex:
        print(f"ex = {str(ex.args)}")
        print(f"traceback = {str(traceback.format_exc())}")

def view_file(file_path):
    try:
        with open(file_path, 'r') as file:
            file_content = file.read()
            print(file_content)
    except Exception as ex:
        print(f"ex = {str(ex.args)}")
        print(f"traceback = {str(traceback.format_exc())}")


cyr_file_path = FILE_PATH
old_string = 'BRANCH_NAME'
new_string = REPO_NAME
old_tag_value = 'TAG_VALUE'
new_tag_value = IMAGE_TAG_VALUE

replace_string_in_file(cyr_file_path, old_string, new_string)
replace_string_in_file(cyr_file_path,old_tag_value,new_tag_value)
view_file(cyr_file_path)
for temp_file_path in TEMPLATES_FILE_PATHS:
    old_string = 'ROLE_NAME'
    new_string = OLD_ROLE_NAME
    if DEPLOYMENT_STACK == 'terragrunt':
        new_string = NEW_ROLE_NAME
    print(f"env = {PA_ENV} - for ServiceAccount file ROLE_NAME will be replaced - {new_string}")
    replace_string_in_file(temp_file_path,old_string,new_string)
    view_file(temp_file_path)