FROM docker-sbi.art.code.pan.run/secure-base-image-ubuntu:latest-python312-fips

ARG ARTIFACTORY_USERNAME
ARG ARTIFACTORY_PASSWORD
ARG BUILD_VERSION

ENV BUILD_VERSION=${BUILD_VERSION}

RUN apt-get update --fix-missing && apt install -y bash \
                   python3 \
                   python3-pip \
                   vim \
                   net-tools \
                   strace \
                   lsof

COPY src/apps/orchestrator/requirements.txt /usr/local
COPY src/apps/orchestrator/requirements-artifactory.txt /usr/local/requirements-artifactory.txt
RUN sed -i "s/\${BUILD_VERSION}/${BUILD_VERSION}/g" /usr/local/requirements-artifactory.txt
RUN pip3 install -Ur /usr/local/requirements.txt && \
    if [ -n "$ARTIFACTORY_USERNAME" ] && [ -n "$ARTIFACTORY_PASSWORD" ]; then \
        pip3 install --no-cache-dir --no-deps  --index-url https://${ARTIFACTORY_USERNAME}:${ARTIFACTORY_PASSWORD}@art.code.pan.run/artifactory/api/pypi/pypi-prisma-access-local/simple -Ur /usr/local/requirements-artifactory.txt; \
    fi
ENV TZ America/Los_Angeles

# Add our applications. Any newly built application will automatically be added here
ADD docker/bin /usr/local/bin
ARG COMMON_LIB_DIR=src/apps/shared
# Display COMMON_LIB_DIR
RUN echo "COMMON_LIB_DIR: ${COMMON_LIB_DIR}"

# Add our entire python source code into the image. Any newly created directory needs to
# be manually added here. TODO: Make it automatic. How do we quantify the exact directory for
# interpreted languages? What if the files in a new directory are using some libraries stored
# elsewhere?
#ADD src/apps/orchestrator/legacy orchestrator/
ADD src/apps/orchestrator/notification_service   						orch_aas/notification_service/
ADD src/apps/orchestrator/orchestration_service  						orch_aas/orchestration_service/
ADD src/apps/orchestrator/instance_mgmt_service  						orch_aas/instance_mgmt_service/
ADD src/apps/orchestrator/libs                   						orch_aas/libs/
ADD src/apps/orchestrator/global_cfg.py          						orch_aas/
ADD src/apps/orchestrator/pim_service/config.json						orch_aas/pim_service/
ADD $COMMON_LIB_DIR                              						shared/
ADD src/apps/orchestrator/utils                  						utils/
COPY src/apps/orchestrator/provision_service/resources/templates  		orch_aas/terraform_templates/

# All logs are written to /var/log/pan. TODO: Should everything be moved to stdout?
RUN mkdir -p /var/log/pan

# The inst_mgmt_service writes the generated cloud provider templates to /cft/...
RUN mkdir -p /cft

# We're not setting a `CMD` here. That's because this is a common container for all our services.
# Each services individual kubernetes deployment YAML will specify the startup command
ENV PYTHONPATH=$PYTHONPATH:/orch_aas/

# Adding an alias to avoid changing most script invocations""
RUN echo 'alias python=python3' >> ~/.bashrc

# Update permissions for the folders we operate in
RUN chown -R panuser /var/log
RUN chown -R panuser /var/log/pan
RUN chown -R panuser /orch_aas/
RUN chown -R panuser /shared/
RUN chown -R panuser /cft/
RUN chown -R panuser /utils/
# After actions that require root have completed, set the user to the predefined non-root user "panuser"
USER panuser
