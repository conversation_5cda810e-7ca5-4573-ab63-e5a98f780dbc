#!/bin/bash
# Script to upload the docker imaget to GCR
# The easiest way to run this is using make command. i.e. `make upload_to_gcr`

IMAGETAG=$1

GCR_HOSTNAME=$2
GCR_PROJECT_ID=$3
GCR_IMAGE=$4

# Authenticate
gcloud auth print-access-token | docker login -u oauth2accesstoken --password-stdin https://$GCR_HOSTNAME

# Tag the image
docker tag $IMAGETAG $GCR_HOSTNAME/$GCR_PROJECT_ID/$GCR_IMAGE

# Upload the image
docker push $GCR_HOSTNAME/$GCR_PROJECT_ID/$GCR_IMAGE
