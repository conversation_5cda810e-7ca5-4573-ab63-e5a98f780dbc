#
# Map to docker image for build
#
AF_REGISTRY ?= docker-saas-infra-base.af.paloaltonetworks.local

# This is the base image
DOCK_IMAGE              := $(AF_REGISTRY)/saas-infra-build:1.0.43

# This is a locally generate image. We can use it to locally test any container
# changes
#DOCK_IMAGE              ?= prisma_access_build:latest
#DOCK_IMAGE              ?= docker-sbi.art.code.pan.run/secure-base-image-ubuntu:latest-python312-fips

