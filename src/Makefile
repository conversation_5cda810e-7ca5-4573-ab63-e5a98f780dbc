#
# PAN Makefile - directory traversal
#
include $(PAN_BUILD_DIR)/src/mk/def.mk
PAN_PRODUCT := saas 
PLUGIN_BUILD_DIR := $(PAN_BUILD_DIR)/build

PAN_VERSION = $(PAN_SW_VERSION)-$(PAN_BUILD_NUM).pan_$(PAN_GIT_HASH)

export PAN_VERSION

#
# Subdirectories to recurse into
#
SUBDIRS += \
	apps \
    $(NULL)

realall: $(SUBDIRS)

$(SUBDIRS):
	$(MAKE) -C $@

coverity:
	@echo "Running Coverity Scan"
	$(MAKE) -C static_analysis
	

.PHONY: $(SUBDIRS)

clean:
