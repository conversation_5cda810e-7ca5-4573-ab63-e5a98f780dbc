// Package definitions has all common shared definitions for all packages to access
package definitions

const (
	// terraform_q.fifo is the topic to publish on to notify the generate_config state
	TopicInstanceChecker string = "terraform_q.fifo"
	// TopicReporting is the topic to publish on to collect metrics internally
	TopicReporting = "reporting.fifo"
)

const (
	// RunTypeVM signals that the Orchestrator is running in VM mode
	RunTypeVM string = "vm"
	// RunTypeKubernetes signals that the Orchestrator is running in Kubernetes mode
	RunTypeKubernetes = "kubernetes"
)
