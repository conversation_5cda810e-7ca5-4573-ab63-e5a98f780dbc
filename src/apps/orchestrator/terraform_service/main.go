/*
 *
 * Terraform Service
 * ------------------
 */
package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"orchestrator/libs/go/aws/kms"
	"orchestrator/libs/go/aws/region"
	"orchestrator/libs/go/aws/utils"
	"orchestrator/libs/go/cogs"
	"orchestrator/libs/go/config"
	"orchestrator/terraform_service/db"
	"orchestrator/terraform_service/definitions"
	"orchestrator/terraform_service/rest"
	"orchestrator/terraform_service/states/api"
	"orchestrator/terraform_service/states/check_status"
	"orchestrator/terraform_service/states/generate_config"
	"orchestrator/terraform_service/terraform"

	"go.panw.local/pangolin/clogger"
)

var (
	cfg                  *config.Config
	gLogger              *clogger.Clogger
	basePath             = flag.String("base-path", "/orch_aas/libs", "Base path to orchestrator")
	runType              = flag.String("run-type", "kubernetes", "Default to the kubernetes run type")
	logMode              = flag.String("log-mode", "stdout", "Log to stdout or file")
	logFile              = flag.String("log-file", "/var/log/pan/terraform_service.log", "Log file to log to")
	terraformTemplateDir = flag.String("template-dir", "/orch_aas/libs/cloud_providers/common/terraform_templates/", "Directory where terraform templates are")
	terraformStateDir    = flag.String("state-dir", "cft/terraform_state/", "Directory to store terraform state files")
)

type stateConfig struct {
	name         string
	concurrency  uint32
	pollInterval cogs.CronPeriod
}

func setupLogger(cfg *config.Config) {
	deployedEnv := cfg.TerraformService.DeployedEnv
	if deployedEnv == "dev" {
		gLogger = clogger.NewConsoleLogger()
	} else {
		gLogger = clogger.NewLogger(os.Stdout)
	}
	clogger.SetCallerFieldName("lambda_name")
	gLogger.AddContext(clogger.TimestampCtx)
	gLogger.AddContext(clogger.CallerCtx)
}

func setupFileLogger(cfg *config.Config, filePath string) {
	writer := &clogger.RotateWriter{
		FileName:     filePath,
		MaxSize:      50 * 1024 * 1024, // 50MB
		MaxRetained:  10,               // Keep 10 archived logs
		NeedCompress: true,             // Compress the archived logs
	}
	gLogger = clogger.NewLogger(writer)
	clogger.SetCallerFieldName("lambda_name")
	gLogger.AddContext(clogger.TimestampCtx)
	gLogger.AddContext(clogger.CallerCtx)
}

func init() {
	var err error
	flag.Parse()

	cfg, err = config.LoadConfig(fmt.Sprintf("%s/cfg.yaml", *basePath))
	if err != nil {
		log.Fatalf("Failed to read cfg.yaml : %v\n", err)
	}
	region.Set(cfg.Region)
	kms.CreateContext()
	utils.CreateContext()

	if *logMode != "stdout" {
		setupFileLogger(cfg, *logFile)
	} else {
		setupLogger(cfg)
	}
	if *runType == definitions.RunTypeVM {
		// utils.ReadFromS3ToFile(gLogger, cfg.KeysBucketName,
		// 	fmt.Sprintf("terraform/%s", cfg.TerraformService.TFTokenEncrypted),
		// 	fmt.Sprintf("%s/tf_token", *basePath),
		// 	true)
		// os.Setenv("TOKEN", fmt.Sprintf("%s/tf_token", *basePath))
		str, _ := cfg.GetTFToken()
		os.Setenv("TOKEN", fmt.Sprintf("%s", str))
	} else {
		str, _ := cfg.GetTFToken()
		os.Setenv("TOKEN", fmt.Sprintf("%s", str))
	}
	// Initialize the DB
	db.Initialize(cfg, gLogger)
	terraform.Initialize(gLogger, cfg, *terraformTemplateDir, *terraformStateDir)
	rest.Initialize()
	//go reporting.ServePrometheusMetrics()
}

func main() {
	gLogger.LogInfo("Starting the terraform_service...")
	utilsContext := utils.GetContext()
	stateCfg := make(map[string]*stateConfig)
	for _, state := range cfg.TerraformService.States {
		gLogger.LogDebug("Read state configuration : %+v", state)
		stateCfg[state.Name] = &stateConfig{
			name:        state.Name,
			concurrency: state.Concurrency,
			pollInterval: cogs.CronPeriod{
				Hours:   state.PollInterval.Hours,
				Minutes: state.PollInterval.Minutes,
				Seconds: state.PollInterval.Seconds,
			},
		}
		if stateCfg[state.Name].concurrency == 0 {
			stateCfg[state.Name].concurrency = 1
		}
	}

	m := cogs.NewMachine(
		cogs.WithLogger(gLogger),
		cogs.WithSQLConn(db.GetDBConn()),
		cogs.WithTFClient(terraform.GetTFClient()),
		cogs.WithRestAPI(rest.GetAPIRouter(), cfg.TerraformService.Port),
		cogs.WithState(
			cogs.NewState(
				"generate_config",
				"Generate terraform templates",
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithSQS("terraform_q.fifo")),
					cogs.WithHandler(generate_config.Handler)),
				cogs.WithCronJob(generate_config.HandleTerraformUpdates, stateCfg["generate_config"].pollInterval),
				cogs.WithMaxConcurrency(stateCfg["generate_config"].concurrency),
			),
		),
		cogs.WithState(
			cogs.NewState(
				"check_status",
				"Check run status in TFC/TFE",
				cogs.WithCronJob(check_status.CheckStatus, stateCfg["check_status"].pollInterval),
				cogs.WithMaxConcurrency(stateCfg["check_status"].concurrency),
			),
		),
		cogs.WithState(
			cogs.NewState(
				"api",
				"Rest API interface for terraform service",
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithRestCall(
					"GET", "/state/{tenant_name}/{region_name}/{cloud_provider}/get_output")),
					cogs.WithHandler(api.GetStateOutputHandler)),
			),
		),
		// cogs.WithState(
		// 	cogs.NewState(
		// 		"reporting",
		// 		"Publish status and metrics for Cosmos' consumption",
		// 		cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithSQS("reporting_q.fifo")),
		// 			cogs.WithHandler(reporting.Handler)),
		// 		cogs.WithCronJob(reporting.PeriodicPublisher, stateCfg["reporting"].pollInterval),
		// 		cogs.WithPublisher(cogs.WithPubSubPublisher(cfg.TerraformService.GCPProjectID)),
		//     ),
		// ),
	)
	curOrchState := utils.OrchStateInit
	prevOrchState := utils.OrchStateInit
	for {
		curOrchState = utilsContext.GetOrchCurrentState(gLogger)
		if prevOrchState != curOrchState {
			if curOrchState == utils.OrchStateActive {
				m.Start()
			} else {
				m.Stop()
				m.Wait()
			}
		}
		prevOrchState = curOrchState
		time.Sleep(5 * time.Second)
	}
}
