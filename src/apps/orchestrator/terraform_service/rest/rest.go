// Package rest is the common package for terraform access. It is simply a wrapper around a common
// TFClient object so that we can avoid passing the object around
package rest

import (
	"github.com/gorilla/mux"
)

var (
	apiRouter *mux.Router
)

// Initialize Terraform client
func Initialize() error {
	apiRouter = mux.NewRouter()
	return nil
}

// GetTFClient returns the terraform client object
func GetAPIRouter() *mux.Router {
	return apiRouter
}
