// Package db is the common package for database access. It is simply a wrapper around a common
// DbConn object so that we can avoid passing the object around
package db

import (
	"orchestrator/libs/go/config"
	"orchestrator/libs/go/dbaccess/sql"

	"go.panw.local/pangolin/clogger"
)

var (
	dbConn *sql.DbConn
)

// Initialize a DB connection
func Initialize(cfg *config.Config, gLogger *clogger.Clogger) error {
	var err error
	decryptedPassword, err := cfg.GetDBPassword()
	gLogger.LogInfo("GetDBPassword err: %s", err)
	if err != nil {
		return err
	}
	dbConn, err = sql.SetupClientConnSQL(sql.ClientConnInput{
		Host:       cfg.DBHost,
		User:       cfg.DBUser,
		Password:   decryptedPassword,
		Database:   cfg.DBName,
		MaxWorkers: 5,
	})
	gLogger.LogInfo("SetupClientConnSQL err: %s", err)
	return err
}

// GetDBConn returns the shared DB connection object
func GetDBConn() *sql.DbConn {
	return dbConn
}
