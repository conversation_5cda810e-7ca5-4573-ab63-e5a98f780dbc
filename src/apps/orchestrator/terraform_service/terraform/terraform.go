// Package terraform is the common package for terraform access. It is simply a wrapper around a common
// TFClient object so that we can avoid passing the object around
package terraform

import (
	"context"

	"github.com/google/uuid"
	"go.panw.local/pangolin/clogger"
	"orchestrator/libs/go/config"
	"orchestrator/libs/go/terraform"
	"orchestrator/libs/go/utils"
)

var (
	tfClient *terraform.Client
)

// Initialize Terraform client
func Initialize(logger *clogger.Clogger, cfg *config.Config,
	terraformTemplateDir string, terraformStateDir string) error {
	var err error
	tfToken, err := cfg.GetTFToken()
	if err != nil {
		return err
	}
	azrClientSecret, err := cfg.GetAzrSecret()
	if err != nil {
		return err
	}
	tfCfg := terraform.ClientConfig{
		Token:                  tfToken,
		ClientID:               uuid.New().String(),
		Organization:           cfg.TerraformService.TFOrg,
		AZRClientID:            cfg.TerraformService.AZRClientID,
		AZRClientSecret:        azrClientSecret,
		AZRTenantID:            cfg.TerraformService.AZRTenantID,
		AZRHostSubscriptionID:  cfg.AZRHostSubscriptionID,
		AZRImageSubscriptionID: cfg.AZRImageSubscriptionID,
		AZRImageGalleryName:    cfg.AZRImageGalleryName,
		AZRImageResourceGroup:  cfg.AZRImageResourceGroup,
		AZRMgmtResourceGroup:   cfg.AZRMgmtInfo.ResourceGroup,
		AZRMgmtVnetName:        cfg.AZRMgmtInfo.Vnet,
		AZRMgmtSubnetName:      cfg.AZRMgmtInfo.Subnet,
		AZRMgmtSyslogPlsID:     cfg.AZRMgmtInfo.SyslogPlsID,
		AZRMgmtProxySSHKeys:    cfg.AZRMgmtInfo.ProxySSHKeys,
		AZRBillingAccount:      cfg.AZRBillingAccount,
		AZREnrollmentAccount:   cfg.AZREnrollmentAccount,
		AZRMgmtGroup:           cfg.AZRMgmtGroup,
		Logger:                 utils.GetEventLogger(logger),
		TemplateDir:            terraformTemplateDir,
		StateDir:               terraformStateDir,
	}
	ctx := context.Background()
	tfClient, err = terraform.NewClient(ctx, tfCfg)
	tfCfg.Logger.LogInfo("Creating TF Client %+v, err %+v", tfClient, err)
	return err
}

// GetTFClient returns the terraform client object
func GetTFClient() *terraform.Client {
	return tfClient
}
