// Package generate_config checks the health and state of an SPN
// The first time we check the health of an SPN is always on an event. If while
// processing the event we see the state is not good, we defer the state check to
// our cron job. If the cron job tries to review the SPN's health 3(?) times and fails,
// we give up
package generate_config

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"orchestrator/libs/go/cogs"
	"orchestrator/libs/go/dbaccess/models/azr_vnet_table"
	"orchestrator/libs/go/dbaccess/models/commit_job_status_detail"
	"orchestrator/libs/go/dbaccess/models/cust_master"
	"orchestrator/libs/go/dbaccess/models/instance_master"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_azr_table"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_table"
	"orchestrator/libs/go/dbaccess/models/region_master"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/terraform"
	"orchestrator/libs/go/terraform/azr"
	"orchestrator/libs/go/utils"
	// "orchestrator/terraform_service/states/reporting"
	// "time"
)

const (
	SEQNO_MIN       = 1000
	SEQNO_MAX       = 200000000
	HUB_REGION_NAME = "us-west-2"
)

func azrCreateTenantSubscription(tfCtx *terraform.Context, custMasterEntry *cust_master.Row) {
	var err error
	eLog := tfCtx.Logger
	custName := custMasterEntry.Name.String()
	err = azr.GenerateTenantConfig(tfCtx, custMasterEntry)
	if err != nil {
		logMsg := fmt.Sprintf(`Failed to generate terraform config for the tenant..
                Name %s err %v`, custName, err)
		eLog.LogError(logMsg)
		return
	}
	_, err = tfCtx.TFClient.CreateWorkspace(nil, "", custName, "", "")
	if err != nil {
		return
	}
	// var runOutput *terraform.RunOutput
	_, err = tfCtx.TFClient.ScheduleRun(terraform.RunTypeApply, custName, false)
	if err != nil {
		tfCtx.TFClient.DeleteWorkspace(custName)
		return
	}
}

func azrCheckTenantStatus(dbCtx *sql.DbContext, tfCtx *terraform.Context, custMasterEntry *cust_master.Row, runID string) {
	currRunOutput, err := tfCtx.TFClient.ReadRunStatus(runID)
	eLog := tfCtx.Logger
	if err != nil {
		eLog.LogDebug("error reading run status: %+v", err)
		return
	}
	if terraform.IsEndStatus(currRunOutput.Run.Status) {
		subscriptionID, err := azr.GetAZRTenantOutput(tfCtx, custMasterEntry.Name.String())
		eLog.LogDebug("subscriptionID %s err %+v", subscriptionID, err)
		if err != nil {
			eLog.LogError("%+v", err)
			return
		}
		custMasterEntry.SubscriptionID = sql.String(subscriptionID)
		custMasterEntry.SaveSubscriptionID(dbCtx)
	}
}

/* Process Azure instance entries for a specific tenant and region: */
func processAZREntries(dbCtx *sql.DbContext, tfClient *terraform.Client, row *orch_instance_management_azr_table.Row) error {
	runType := terraform.RunTypeApply
	eLog := dbCtx.Logger
	eLog.LogInfo("Starting deployment for %+v", row)
	instMgmtEntry, err := orch_instance_management_table.GetRowByEIDX(dbCtx, row.InstMgmtID.Int64())
	if err != nil {
		logMsg := fmt.Sprintf(`Failed to get instance mgmt entry..
            InstMgmtID %d err %v`, row.InstMgmtID, err)
		eLog.LogError(logMsg)
		return err
	}
	eLog.SetLoggerTag(instMgmtEntry.DebugID.String())
	custID := instMgmtEntry.CustID.Int64()
	regionID := instMgmtEntry.Region.Int64()
	custMasterEntry, err := cust_master.GetRowByID(dbCtx, custID)
	if err != nil {
		logMsg := fmt.Sprintf(`Failed to get cust master entry..
            CustID %d err %v`, custID, err)
		eLog.LogError(logMsg)
		return err
	}
	tfCtx := &terraform.Context{
		TFClient: tfClient,
		Logger:   eLog,
	}
	regionMaster, err := region_master.GetRowByID(dbCtx, regionID)
	if err != nil {
		logMsg := fmt.Sprintf(`Failed to get region master entry..
            Region %d err %v`, regionID, err)
		eLog.LogError(logMsg)
		return err
	}
	eLog.LogInfo("Fetched instMgmtEntry %+v custMasterEntry %+v regionMaster %+v", instMgmtEntry, custMasterEntry, regionMaster)
	if custMasterEntry.SubscriptionID.String() == "" ||
		custMasterEntry.SubscriptionID.String() == "None" {
		eLog.LogInfo("Tenant Provisioning is not complete, creating subscription for %s",
			custMasterEntry.Name.String())
		_, runID, err := tfCtx.TFClient.ReadWorkspaceIDs(custMasterEntry.Name.String())
		if err != nil || runID == nil {
			logMsg := fmt.Sprintf(`Could not fetch tenant %s subscription info(err %+v), creating a new one`,
				custMasterEntry.Name.String(), err)
			eLog.LogInfo(logMsg)
			azrCreateTenantSubscription(tfCtx, custMasterEntry)
		} else {
			azrCheckTenantStatus(dbCtx, tfCtx, custMasterEntry, *runID)
		}
		return nil
	}
	instMasterEntries, err := instance_master.GetRowsByCustomerRegion(dbCtx, custID, regionID, 0)
	if len(instMasterEntries) == 0 && regionMaster.EdgeLocationRegionName.String() != HUB_REGION_NAME {
		runType = terraform.RunTypeDestroy
	}
	azrVnetTableEntry, err := azr_vnet_table.GetRowByRegionID(dbCtx, regionID)
	if err != nil {
		eLog.LogError("could not fetch azr vnet info from azr_vnet_table %+v", err)
		return err
	}
	isHubRegion := (regionMaster.EdgeLocationRegionName.String() == HUB_REGION_NAME)
	err = azr.GenerateConfig(tfCtx, instMgmtEntry.WorkspaceName.String(),
		custMasterEntry, regionMaster, instMasterEntries, azrVnetTableEntry,
		isHubRegion, HUB_REGION_NAME)
	if err != nil {
		logMsg := fmt.Sprintf(`Failed to generate terraform config..
            CustID %s Region %d err %v`, custID, regionID, err)
		eLog.LogError(logMsg)
		return err
	}
	_, err = tfClient.CreateWorkspace(nil, "", instMgmtEntry.WorkspaceName.String(), "", "")
	if err != nil {
		return err
	}
	var runOutput *terraform.RunOutput
	runOutput, err = tfClient.ScheduleRun(runType, instMgmtEntry.WorkspaceName.String(), row.RefreshOnly.Bool())
	if err != nil {
		return err
	}
	metaData, err := json.Marshal(runOutput)
	if err != nil {
		eLog.LogError("%v", err)
		return err
	}
	instMgmtAzrEntry, err := orch_instance_management_azr_table.GetRowByInstMgmtID(dbCtx, instMgmtEntry.EIDX.Int64())
	if err != nil {
		logMsg := fmt.Sprintf(`Failed to get instance mgmt entry for azure:
            workspace %s instMgmtID %d err %v, cannot continue`, instMgmtEntry.WorkspaceName.String(),
			instMgmtEntry.EIDX.Int64(), err)
		eLog.LogInfo(logMsg)
		return err
	}
	instMgmtAzrEntry.Metadata = sql.String(metaData)
	instMgmtAzrEntry.FinishedUpdate = instMgmtAzrEntry.TriggerUpdate
	instMgmtAzrEntry.StartedProcessing = instMgmtAzrEntry.FinishedUpdate
	instMgmtAzrEntry.CurrWorkspaceState = sql.String(runOutput.Run.Status)
	instMgmtAzrEntry.PrevWorkspaceState = sql.String("")
	dbCtx.Logger.LogDebug("Updated azure instance mgmt entry %+v with finished_update: %d, started processing %d",
		instMgmtAzrEntry, instMgmtAzrEntry.FinishedUpdate.Int64(), instMgmtAzrEntry.StartedProcessing.Int64())
	if err := instMgmtAzrEntry.SaveStage2(dbCtx); err != nil {
		dbCtx.Logger.LogError("Failed to update %+v into orch_instance_management_table : %v",
			instMgmtAzrEntry, err)
		return err
	}
	return nil
}

// Handler updates instance management tables to track config generation
func Handler(ctx *cogs.MessageContext) cogs.PublishMessage {
	var (
		err           error
		instMgmtEntry *orch_instance_management_table.Row
		emptyResp     cogs.PublishMessage
	)
	eLog := utils.GetEventLogger(ctx.Logger)
	msg := ctx.Payload

	// start := time.Now()
	// defer func() {
	//     duration := time.Since(start)
	//     reporting.StateDurationHistogram.WithLabelValues("generate_config.Handler").Observe(
	//         float64(duration.Milliseconds()))
	// }()
	dbCtx := &sql.DbContext{
		DbConn: ctx.DBConn,
		Logger: eLog,
	}

	var apiMsg Message
	if err = json.Unmarshal(msg, &apiMsg); err != nil {
		eLog.LogError("error : %+v", err)
		return emptyResp
	}

	eLog.SetLoggerTag(apiMsg.DebugID)
	eLog.LogDebug("Received : %+v", apiMsg)

	regionMasterEntry, err := region_master.GetRowByID(dbCtx, apiMsg.ComputeRegionID)
	if err != nil {
		logMsg := fmt.Sprintf(`Failed to get region master entry..
            Region %d err %v`, apiMsg.ComputeRegionID, err)
		eLog.LogError(logMsg)
		return emptyResp
	}
	workspaceName := fmt.Sprintf(`%s_%s_%s`, apiMsg.TenantName,
		regionMasterEntry.NativeComputeRegionName.String(), apiMsg.CloudProvider)
	eLog.LogInfo("workspace name %s", workspaceName)
	instMgmtEntry, err = orch_instance_management_table.GetRowByUniqueKeys(dbCtx, apiMsg.CustID, apiMsg.ComputeRegionID,
		apiMsg.CloudProvider, 0)
	if err != nil {
		logMsg := fmt.Sprintf(`Failed to get instance mgmt entry: creating new one..
            cust id %d workspace %s err %v`, apiMsg.CustID, workspaceName, err)
		eLog.LogInfo(logMsg)
		instMgmtEntry = &orch_instance_management_table.Row{
			CustID:        sql.Int64(apiMsg.CustID),
			Region:        sql.Int64(apiMsg.ComputeRegionID),
			WorkspaceName: sql.String(workspaceName),
			CloudProvider: sql.String(apiMsg.CloudProvider),
			// TODO: check with SRE on adding rastro support
			Debug:   sql.Int64(0),
			DebugID: sql.String(apiMsg.DebugID),
		}
		dbCtx.Logger.LogDebug("Saving %+v", instMgmtEntry)
		if err := instMgmtEntry.Save(dbCtx); err != nil {
			dbCtx.Logger.LogError("Failed to save %+v into orch_instance_management_table : %v",
				instMgmtEntry, err)
			return emptyResp
		}
		instMgmtEntry, err = orch_instance_management_table.GetRowByUniqueKeys(dbCtx, apiMsg.CustID, apiMsg.ComputeRegionID,
			apiMsg.CloudProvider, 0)
		if err != nil {
			logMsg := fmt.Sprintf(`Failed to get instance mgmt cust id %d workspace %s err %v`,
				apiMsg.CustID, workspaceName, err)
			eLog.LogInfo(logMsg)
			return emptyResp
		}
	}
	eLog.LogInfo("Found %+v row associated with cust id %d workspace %s", instMgmtEntry, apiMsg.CustID, workspaceName)
	instMgmtAzrEntry, err := orch_instance_management_azr_table.GetRowByInstMgmtID(dbCtx, instMgmtEntry.EIDX.Int64())
	if err != nil {
		logMsg := fmt.Sprintf(`Failed to get instance mgmt entry for azure: creating new one..
            workspace %s instMgmtID %d err %v`, workspaceName, instMgmtEntry.EIDX.Int64(), err)
		eLog.LogInfo(logMsg)
		instMgmtAzrEntry = &orch_instance_management_azr_table.Row{
			InstMgmtID:    instMgmtEntry.EIDX,
			TriggerUpdate: sql.Int64(rand.Intn(SEQNO_MAX-SEQNO_MIN+1) + SEQNO_MIN),
		}
		dbCtx.Logger.LogDebug("Saving %+v", instMgmtAzrEntry)
		if err := instMgmtAzrEntry.Save(dbCtx); err != nil {
			dbCtx.Logger.LogError("Failed to save %+v into orch_instance_management_azr_table : %v",
				instMgmtAzrEntry, err)
			return emptyResp
		}
	} else {
		instMgmtAzrEntry.TriggerUpdate += 10
		if err := instMgmtAzrEntry.SaveStage1(dbCtx); err != nil {
			dbCtx.Logger.LogError("Failed to update %+v into orch_instance_management_azr_table : %v",
				instMgmtAzrEntry, err)
			return emptyResp
		}
	}
	eLog.LogInfo("Found AZR %+v row associated with workspace %s", instMgmtAzrEntry, workspaceName)
	myJob, err := commit_job_status_detail.GetRowByPrimaryKeys(dbCtx, apiMsg.TenantID, apiMsg.JobInfo.PanoramaJobID, apiMsg.JobInfo.CustTopoID)
	if err != nil {
		logMsg := fmt.Sprintf(`Failed to get commit_job_status_detail entry for
            workspace %s instMgmtID %d custID %d JobID %d custTopoID %d err %v`,
			workspaceName, instMgmtEntry.EIDX, apiMsg.CustID,
			apiMsg.JobInfo.PanoramaJobID, apiMsg.JobInfo.CustTopoID, err)
		eLog.LogInfo(logMsg)
		return emptyResp
	}
	myJob.InstMgmtIdx = instMgmtEntry.EIDX
	myJob.InstMgmtSeqNr = instMgmtAzrEntry.TriggerUpdate
	if err := myJob.UpdateOnboardingStatus(dbCtx); err != nil {
		logMsg := fmt.Sprintf(`Failed to update commit_job_status_detail for entry %+v err %v`,
			myJob, err)
		eLog.LogInfo(logMsg)
		return emptyResp
	}
	return emptyResp
}

// HandleTerraformUpdates runs periodically to check for tenant/region updates that are pending,
// generates the state files and uploads them to terraform workspaces
func HandleTerraformUpdates(ctx *cogs.MessageContext, publishChan chan<- *cogs.PublishMessage) {
	/*
	   1> Get all the entries where trigger_update > finished_update.
	   2> For each of the entries call the Handler to generate config.
	*/
	// start := time.Now()
	// defer func() {
	//     duration := time.Since(start)
	//     reporting.StateDurationHistogram.WithLabelValues("generate_config.handleTerraformUpdates").Observe(
	//         float64(duration.Milliseconds()))
	// }()

	eLog := utils.GetEventLogger(ctx.Logger)
	dbCtx := &sql.DbContext{
		DbConn: ctx.DBConn,
		Logger: eLog,
	}

	rows, err := orch_instance_management_azr_table.GetUpdatePendingRows(dbCtx)
	if err != nil {
		eLog.LogInfo("No rows to update, return")
		return
	}
	for _, myRow := range rows {
		// Update event logger with the row's trace information
		eLog.LogInfo("Processing: MgmtInstID (%d), Metadata (%s)",
			myRow.InstMgmtID.Int64(), myRow.Metadata.String())
		err := processAZREntries(dbCtx, ctx.TFClient, myRow)
		if err != nil {
			eLog.LogError("Error while processing, will retry later: InstMgmtID (%d), Metadata (%s) Error %v",
				myRow.InstMgmtID.Int64(), myRow.Metadata.String(), err)
		}
	}
	return
}
