package generate_config

//Example:
// {
//     "tenant_id": 471627256,
//     "region_id": 217,
//     "cloud_provider": "azr",
//     "cust_id": 109,
//     "job_info": {
//         "job_id": 37,
//         "cust_topo_id": 3568
//     },
//     "trace_id": ""
// }

// Message is the message format that instance_checker expects
type Message struct {
	CustID           int64   `json:"cust_id"`
	TenantName       string  `json:"tenant_name"`
	TenantID         int64   `json:"tenant_id"`
	ComputeRegionID  int64   `json:"region_id"`
	NativeRegionName string  `json:"native_region_name"`
	CloudProvider    string  `json:"cloud_provider"`
	JobInfo          JobInfo `json:"job_info"`
	DebugID          string  `json:"debug_id"`
}

type JobInfo struct {
	PanoramaJobID int64 `json:"job_id"`
	CustTopoID    int64 `json:"cust_topo_id"`
}
