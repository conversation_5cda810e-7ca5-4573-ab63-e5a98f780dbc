// Package check_status periodically checks the runs on terraform cloud/enterprise and
// updates the run's current state into the DB
package check_status

import (
	"encoding/json"
	"go.panw.local/pangolin/clogger"
	//"fmt"
	// "math/rand"
	// "orchestrator/libs/go/dbaccess/models/commit_job_status_detail"
	// "orchestrator/libs/go/dbaccess/models/cust_master"
	// "orchestrator/libs/go/dbaccess/models/instance_master"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_azr_table"
	// "orchestrator/libs/go/dbaccess/models/orch_instance_management_table"
	// "orchestrator/libs/go/dbaccess/models/region_master"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/terraform"
	// "orchestrator/libs/go/terraform/azr"
	"orchestrator/libs/go/cogs"
	"orchestrator/libs/go/utils"
	//"orchestrator/terraform_service/states/reporting"
	// "time"
)

func updateAZREntries(dbCtx *sql.DbContext, tfClient *terraform.Client, instMgmtAzrEntry *orch_instance_management_azr_table.Row, eLog *clogger.EventLogger) error {
	var err error
	eLog.LogInfo("checking status for %+v", instMgmtAzrEntry)
	var prevRunOutput terraform.RunOutput
	if err = json.Unmarshal([]byte(instMgmtAzrEntry.Metadata.String()), &prevRunOutput); err != nil {
		eLog.LogDebug("error : %+v", err)
		return err
	}
	var currRunOutput *terraform.RunOutput
	currRunOutput, err = tfClient.ReadRunStatus(prevRunOutput.Run.ID)
	if err != nil {
		eLog.LogDebug("error reading run status: %+v", err)
		return err
	}
	if prevRunOutput.Run.Status != currRunOutput.Run.Status {
		currRunOutput.RunURL = prevRunOutput.RunURL
		metaData, err := json.Marshal(currRunOutput)
		if err != nil {
			eLog.LogError("%v", err)
			// Set refreshOnly to 1 for this entry, if it was not already set
			// This is to ensure that there is always an actual update scheduled after each refresh-only run.
			if instMgmtAzrEntry.RefreshOnly == false {
				// Set refreshOnly for this entry in the table
				instMgmtAzrEntry.RefreshOnly = true
				instMgmtAzrEntry.TriggerUpdate += 10
				if err := instMgmtAzrEntry.SaveStage1(dbCtx); err != nil {
					dbCtx.Logger.LogError("Failed to update %+v into orch_instance_management_azr_table : %v",
						instMgmtAzrEntry, err)
					return err
				}
				dbCtx.Logger.LogInfo("Updated azure instance mgmt entry %+v with refreshOnly: 1, cur status %s",
					instMgmtAzrEntry, currRunOutput.Run.Status)
			} else {
				eLog.LogInfo("Azure instance mgmt entry already has refreshOnly set; no action")
			}

			return err
		}
		// If refreshOnly is set to 1 for this entry, set the triggerUpdate and reset the refreshOnly flag.
		if instMgmtAzrEntry.RefreshOnly == true {
			instMgmtAzrEntry.RefreshOnly = false
			instMgmtAzrEntry.TriggerUpdate += 10
			if err := instMgmtAzrEntry.SaveStage1(dbCtx); err != nil {
				dbCtx.Logger.LogError("Failed to update %+v into orch_instance_management_azr_table : %v",
					instMgmtAzrEntry, err)
				return err
			}
			dbCtx.Logger.LogDebug("Updated azure instance mgmt entry %+v with refreshOnly: 0, cur status %s",
				instMgmtAzrEntry, currRunOutput.Run.Status)
		}

		instMgmtAzrEntry.Metadata = sql.String(metaData)
		instMgmtAzrEntry.CurrWorkspaceState = sql.String(currRunOutput.Run.Status)
		instMgmtAzrEntry.PrevWorkspaceState = sql.String(prevRunOutput.Run.Status)
		dbCtx.Logger.LogDebug("Updated azure instance mgmt entry %+v with metadata: %v, cur status %s",
			instMgmtAzrEntry, instMgmtAzrEntry.Metadata.String(), currRunOutput.Run.Status)
		if terraform.IsEndStatus(currRunOutput.Run.Status) {
			instMgmtAzrEntry.FinishedProcessing = instMgmtAzrEntry.StartedProcessing
			if err := instMgmtAzrEntry.SaveStage3(dbCtx); err != nil {
				dbCtx.Logger.LogError("Failed to update %+v into orch_instance_management_table : %v",
					instMgmtAzrEntry, err)
				return err
			}
		} else {
			if err := instMgmtAzrEntry.SaveStage2(dbCtx); err != nil {
				dbCtx.Logger.LogError("Failed to update %+v into orch_instance_management_table : %v",
					instMgmtAzrEntry, err)
				return err
			}
		}
	}
	return nil
}

// Check status runs periodically to check for terraform run updates,
// updates azr instance management table with start processing and finshed procoessing along with metadata
func CheckStatus(ctx *cogs.MessageContext, publishChan chan<- *cogs.PublishMessage) {
	/*
	   1> Get all the entries where started_processing > finished_processing.
	   2> For each of the entries call the Handler to check status and update state.
	*/
	// start := time.Now()
	// defer func() {
	//     duration := time.Since(start)
	//     reporting.StateDurationHistogram.WithLabelValues("generate_config.handleTerraformUpdates").Observe(
	//         float64(duration.Milliseconds()))
	// }()

	eLog := utils.GetEventLogger(ctx.Logger)
	dbCtx := &sql.DbContext{
		DbConn: ctx.DBConn,
		Logger: eLog,
	}
	rows, err := orch_instance_management_azr_table.GetStatusPendingRows(dbCtx)
	if err != nil {
		eLog.LogInfo("No rows to update, return")
		return
	}
	for _, myRow := range rows {
		// Update event logger with the row's trace information
		eLog.SetLoggerTag(string(myRow.TraceID))
		eLog.LogInfo("Processing: MgmtInstId (%d)", myRow.InstMgmtID.Int64())
		eLog.LogDebug("Metadata (%s)", myRow.Metadata.String())
		err := updateAZREntries(dbCtx, ctx.TFClient, myRow, eLog)
		if err != nil {
			eLog.LogError("Error while processing, will retry later: InstMgmtID (%d), Metadata (%s) Error %v",
				myRow.InstMgmtID.Int64(), myRow.Metadata.String(), err)
		}
	}
	return
}
