package reporting

import (
	"github.com/prometheus/client_golang/prometheus"
	model "github.com/prometheus/client_model/go"
)

var (
	// TotalAPIEventCount tracks the total number of PubSub messages received
	TotalAPIEventCount = prometheus.NewCounter(prometheus.CounterOpts{
		Namespace: "terraform_service",
		Subsystem: "generate_config",
		Name:      "total_pubsub_events",
		Help:      "Number of PubSub publishes from the vertical scale service",
	})
	// TerraformFailureCount tracks the number of event failures in generate config
	TerraformFailureCount = prometheus.NewCounter(prometheus.CounterOpts{
		Namespace: "terraform_service",
		Subsystem: "generate_config",
		Name:      "terraform_failures",
		Help:      "Number of Terraform failures",
	})
	// StateDurationHistogram tracks the time spent in each state
	// The histogram has the following buckets
	// 0-250ms
	// 250-500ms
	// 500-750ms
	// ...
	// 9750-10000ms
	// XXX: We are not tracking any durations greater than 10 seconds
	StateDurationHistogram = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Namespace: "terraform_service",
		Subsystem: "states",
		Name:      "state_duration",
		Help:      "Histogram of duration in Milliseconds for each state",
		Buckets:   prometheus.LinearBuckets(250, 250, 10),
	},
		[]string{"state"})

	metricsMap = map[string]prometheus.Collector{
		"total_api_event_count":    TotalAPIEventCount,
		"terraform_failure_count":  TerraformFailureCount,
		"state_duration_histogram": StateDurationHistogram,
	}
)

func getGauge(g prometheus.Gauge) float64 {
	mCh := make(chan prometheus.Metric, 1)
	g.Collect(mCh)
	m := model.Metric{}
	(<-mCh).Write(&m)
	return m.Gauge.GetValue()
}

func getCounter(c prometheus.Counter) float64 {
	mCh := make(chan prometheus.Metric, 1)
	c.Collect(mCh)
	m := model.Metric{}
	(<-mCh).Write(&m)
	return m.Counter.GetValue()
}

func getHistogram(h prometheus.Histogram) []*model.Bucket {
	mCh := make(chan prometheus.Metric, 1)
	h.Collect(mCh)
	m := model.Metric{}
	(<-mCh).Write(&m)
	return m.Histogram.GetBucket()
}

func getHistogramVec(hv *prometheus.HistogramVec, label string) ([]*model.Bucket, error) {
	metric, err := hv.MetricVec.GetMetricWithLabelValues(label)
	if err != nil {
		return nil, err
	}
	m := model.Metric{}
	metric.Write(&m)
	return m.Histogram.GetBucket(), nil
}
