package reporting

// Message defines the supported reporting format. This is the format that we use to report
// internally. This message will be massaged to the cosmosMessage before being published
type Message struct {
	UUID        string `json:"uuid"`                   // Rastro Tag
	Topic       string `json:"topic"`                  // Description of metric being published
	Status      string `json:"status"`                 // Success or Failure
	ErrorMsg    string `json:"error_msg,omitempty"`    // Brief Error on Failure
	DetailedMsg string `json:"detailed_msg,omitempty"` // Detailed Information
	// TBD
}

// EventSummary captures an overview of our processing of an event
type EventSummary struct {
	Status       string  `json:"status"`
	TFWorkspace  string  `json:"vmid"`
	CreateTime   string  `json:"create_time"`
	StartTime    float64 `json:"start_time"`
	FinishedTime float64 `json:"finished_time"`
}

type cosmosMessage struct {
	// TBD
}
