package reporting

import (
	"crypto/tls"
	"encoding/json"
	"net/http"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	model "github.com/prometheus/client_model/go"
	"github.com/prometheus/prom2json"
	"go.panw.local/pangolin/clogger"

	"orchestrator/libs/go/cogs"
)

func init() {
	for _, c := range metricsMap {
		prometheus.MustRegister(c)
	}
}

// ServePrometheusMetrics sets up a local server endpoint to serve all collected metrics.
// We use these to publish periodically to Cosmos. The endpoint also allows for SRE to
// query for the metrics for any of their dashboards
func ServePrometheusMetrics() error {
	http.Handle("/metrics", promhttp.Handler())
	if err := http.ListenAndServe(":2112", nil); err != nil {
		return err
	}
	return nil
}

// Handler is invoked for every reporting message or metrics published to this state
func Handler(ctx *cogs.MessageContext) ([]byte, string) {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		StateDurationHistogram.WithLabelValues("reporting.Handler").Observe(
			float64(duration.Milliseconds()))
	}()
	eLog := &clogger.EventLogger{Glogger: ctx.Logger}
	var msg EventSummary
	if err := json.Unmarshal(ctx.Payload, &msg); err != nil {
		eLog.LogError("Failed to unmarshal : %v", err)
		return nil, ""
	}
	eLog.LogDebug("Received message : %+v", msg)
	// TODO: Publish to PubSub?
	// Tenant specific? Region specific? Global topic?
	return nil, ""
}

func makeTransport() (*http.Transport, error) {
	// Start with the DefaultTransport for sane defaults.
	transport := http.DefaultTransport.(*http.Transport).Clone()
	// Conservatively disable HTTP keep-alives as this program will only
	// ever need a single HTTP request.
	transport.DisableKeepAlives = true
	// Timeout early if the server doesn't even return the headers.
	transport.ResponseHeaderTimeout = time.Minute
	tlsConfig := &tls.Config{InsecureSkipVerify: true}
	transport.TLSClientConfig = tlsConfig
	return transport, nil
}

// PeriodicPublisher periodically publishes all collected metrics on the relevant topics
func PeriodicPublisher(ctx *cogs.MessageContext, publishChan chan<- *cogs.PublishMessage) {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		StateDurationHistogram.WithLabelValues("reporting.PeriodicPublisher").Observe(
			float64(duration.Milliseconds()))
	}()
	eLog := &clogger.EventLogger{Glogger: ctx.Logger}
	eLog.LogDebug("Publishing collected metrics")
	transport, err := makeTransport()
	if err != nil {
		eLog.LogError("Failed to setup transport to fetch local prometheus metrics : %s", err.Error())
		return
	}
	mfChan := make(chan *model.MetricFamily, 1024) // Safe buffer for unique metrics count
	go func() {
		if err := prom2json.FetchMetricFamilies(
			"http://127.0.0.1:2112/metrics",
			mfChan,
			transport); err != nil {
			eLog.LogError("Failed to fetch prometheus metrics : %s", err.Error())
		}
	}()

	result := []*prom2json.Family{}
	for mf := range mfChan {
		result = append(result, prom2json.NewFamily(mf))
	}
	jsonBytes, err := json.Marshal(result)
	if err != nil {
		eLog.LogError("Failed to marshal metrics to json : %s", err.Error())
		return
	}
	eLog.LogDebug("Got metrics %s", string(jsonBytes))
	// TODO: publish to PubSub
}
