// Package api checks the health and state of an SPN
// The first time we check the health of an SPN is always on an event. If while
// processing the event we see the state is not good, we defer the state check to
// our cron job. If the cron job tries to review the SPN's health 3(?) times and fails,
// we give up
package api

import (
	// "encoding/json"
	"fmt"
	// "math/rand"
	// "orchestrator/libs/go/dbaccess/models/commit_job_status_detail"
	"orchestrator/libs/go/dbaccess/models/cust_master"
	// "orchestrator/libs/go/dbaccess/models/instance_master"
	// "orchestrator/libs/go/dbaccess/models/orch_instance_management_azr_table"
	// "orchestrator/libs/go/dbaccess/models/orch_instance_management_table"
	// "orchestrator/libs/go/dbaccess/models/region_master"
	"orchestrator/libs/go/cogs"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/rest/handler"
	"orchestrator/libs/go/terraform"
	"orchestrator/libs/go/terraform/azr"
	"orchestrator/libs/go/utils"

	"github.com/gorilla/mux"
	//"orchestrator/terraform_service/states/reporting"
	// "time"
)

// Handler updates instance management tables to track config generation
// /state/{tenant_id}/{region_id}/get_output
func GetStateOutputHandler(ctx *cogs.MessageContext) cogs.PublishMessage {
	eLog := utils.GetEventLogger(ctx.Logger)
	params := mux.Vars(ctx.Request)
	tenantName := params["tenant_name"]
	regionName := params["region_name"]
	cloudProvider := params["cloud_provider"]
	workspaceName := fmt.Sprintf(`%s_%s_%s`, tenantName, regionName, cloudProvider)
	emptyResp := cogs.PublishMessage{}
	eLog.LogDebug("Received request for workspace %s", workspaceName)
	dbCtx := &sql.DbContext{
		DbConn: ctx.DBConn,
		Logger: eLog,
	}
	custMasterEntry, err := cust_master.GetRowByName(dbCtx, tenantName)
	if err != nil {
		handler.WriteErrorResponse(err.Error(), ctx.ResponseWriter)
		return emptyResp
	}
	tfCtx := &terraform.Context{
		TFClient: ctx.TFClient,
		Logger:   eLog,
	}
	output, err := azr.GetAZRWorkspaceOutputs(tfCtx, custMasterEntry.SubscriptionID.String(),
		workspaceName)
	eLog.LogDebug("%+v %+v", string(output), err)
	if err != nil {
		handler.WriteErrorResponse(err.Error(), ctx.ResponseWriter)
		return emptyResp
	}
	handler.WriteJSONResponse(output, ctx.ResponseWriter)
	return emptyResp
}
