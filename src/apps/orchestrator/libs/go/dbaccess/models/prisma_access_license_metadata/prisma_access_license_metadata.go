package prisma_access_license_metadata

import (
	"orchestrator/libs/go/dbaccess/sql"
)

var tableName string = "fwaas_customer_provisioning.prisma_access_license_metadata"

type Row struct {
	LoggingServiceInstanceID       sql.Int64  `sql:"logging_service_instance_id"`
	PAInstanceID                   sql.String `sql:"pa_instance_id"`
	PrimaryPanorama                sql.String `sql:"primary_panorama"`
	SecondaryPanorama              sql.String `sql:"secondary_panorama"`
	PanoramaInstanceID             sql.String `sql:"panorama_instance_id"`
	FawkesInstanceID               sql.String `sql:"fawkes_instance_id"`
	ClientAppID                    sql.String `sql:"client_app_id"`
	TSGID                          sql.String `sql:"tsg_id"`
	IsDeleted                      sql.Bool   `sql:"is_deleted"`
	PrismaAccessProvisioningStatus sql.String `sql:"prisma_access_provisioning_status"`
	MigratedTenant                 sql.String `sql:"migrated_tenant"`
	ProductCapability              sql.String `sql:"product_capability"`
	FedrampStatus                  sql.String `sql:"fedramp_status"`
	ChinaStatus                    sql.String `sql:"china_status"`
	PAShard                        sql.String `sql:"pa_shard"`
	CreateTime                     sql.String `sql:"create_time"`
	UpdateTime                     sql.String `sql:"update_time"`
}

// GetByLoggingServiceInstanceID retrieves the row corresponding to the input logging_service_instance_id
func GetByLoggingServiceInstanceID(ctx *sql.DbContext, loggingServiceInstanceID int64) ([]*Row, error) {
	var rows []*Row
	qb := ctx.BuildQuery("SELECT * FROM " + tableName)
	qb.Where("logging_service_instance_id = ?", loggingServiceInstanceID)
	if err := qb.GetAll(&rows); err != nil {
		return nil, err
	}
	return rows, nil
}
