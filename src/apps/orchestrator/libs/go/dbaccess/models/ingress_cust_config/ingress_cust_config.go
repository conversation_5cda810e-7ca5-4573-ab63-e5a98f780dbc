package ingress_cust_config

import (
	"orchestrator/libs/go/dbaccess/sql"
	"strings"
)

var tableName string = "ingress_cust_config"

type Row struct {
	CustID           sql.Int64  `sql:"custid"`
	ComputeRegionID  sql.String `sql:"compute_region_id"`
	ChartSettings    *string    `sql:"chart_settings"`
	ProxySettings    *string    `sql:"proxy_settings"`
	CPUSettings      *string    `sql:"cpu_settings"`
	MemorySettings   *string    `sql:"memory_settings"`
	DeploymentLabels *string    `sql:"deployment_labels"`
	HPASettings      *string    `sql:"hpa_settings"`
	VPASettings      *string    `sql:"vpa_settings"`
	CreateTime       sql.String `sql:"create_time"`
	UpdateTime       sql.String `sql:"update_time"`
}

type ProxySettings struct {
	EnvoyVersion string `json:"envoy_version"`
}

func getColumnNames() string {
	var r Row
	columns := sql.GetMappedColumns(&r)
	return strings.Join(columns, ", ")
}

// getPlaceholders generates a string of placeholders for SQL queries
// based on the number of fields in Row.
func getPlaceholders() string {
	var r Row
	columns := sql.GetMappedColumns(&r)
	placeholders := make([]string, len(columns))
	for i := range placeholders {
		placeholders[i] = "?"
	}
	return strings.Join(placeholders, ", ")
}

// Save inserts the row in the table
func (r *Row) Save(ctx *sql.DbContext) error {
	qStr := "INSERT INTO " + tableName + " (" + getColumnNames() + ") VALUES (" + getPlaceholders() + ")"
	return ctx.Exec(qStr, sql.GetArgs(r)...)
}

// Save upserts the row in the table (insert or update if exists)
func (r *Row) Upsert(ctx *sql.DbContext) error {
	columns := getColumnNames()
	placeholders := getPlaceholders()

	// Create UPDATE clause for ON DUPLICATE KEY UPDATE
	var tempRow Row
	columnList := sql.GetMappedColumns(&tempRow)
	updateClauses := make([]string, 0, len(columnList))
	for _, column := range columnList {
		updateClauses = append(updateClauses, column+" = VALUES("+column+")")
	}
	updateClause := strings.Join(updateClauses, ", ")

	qStr := "INSERT INTO " + tableName + " (" + columns + ") VALUES (" + placeholders + ") ON DUPLICATE KEY UPDATE " + updateClause
	return ctx.Exec(qStr, sql.GetArgs(r)...)
}

// GetByID retrieves the row corresponds to the input primary key (ID)
func GetByCustID(ctx *sql.DbContext, id int64) (*Row, error) {
	row := &Row{}
	qb := ctx.BuildQuery("SELECT * FROM " + tableName)
	qb.Where("custid in (0, ?) ORDER BY custid DESC LIMIT 1", id)
	if err := qb.GetOne(row); err != nil {
		return nil, err
	}
	return row, nil
}

// GetByID retrieves the most specific row matching the customer ID and region ID.
// It falls back to a default ID (0) for either custid or region_id if a specific match is not found.
func GetByCustIDAndRegionID(ctx *sql.DbContext, id int64, regionID int64) (*Row, error) {
	row := &Row{}
	qb := ctx.BuildQuery("SELECT * FROM " + tableName)
	// The query now allows region_id to be a specific value or a default (0).
	// The ORDER BY clause prioritizes the most specific match first (specific custid and specific region_id).
	qb.Where("custid IN (0, ?) AND compute_region_id IN (0, ?) ORDER BY custid DESC, compute_region_id DESC LIMIT 1", id, regionID)
	if err := qb.GetOne(row); err != nil {
		return nil, err
	}
	return row, nil
}

// GetAllByID retrieves the row corresponds to the input primary key (ID)
func GetAllByID(ctx *sql.DbContext, id int64) ([]*Row, error) {
	var rows []*Row
	qb := ctx.BuildQuery("SELECT * FROM " + tableName)
	qb.Where("custid in (0, ?) ORDER BY custid DESC", id)
	if err := qb.GetAll(&rows); err != nil {
		return nil, err
	}
	return rows, nil
}
