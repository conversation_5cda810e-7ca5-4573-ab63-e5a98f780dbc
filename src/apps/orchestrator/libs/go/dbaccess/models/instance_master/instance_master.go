// Package instance_master defines a model for the `instance_master` table
package instance_master

import (
	"encoding/json"
	"fmt"
	"orchestrator/libs/go/dbaccess/sql"
	"strconv"
	"strings"
)

var tableName string = "instance_master"

// Row holds the contents of a single row in instance_master. Only keeping track
// of the fields we need for now.
// TODO: Add all columns if we need to
type Row struct {
	ID                   sql.Int64  `sql:"id"`
	VMID                 sql.String `sql:"vmid"`
	Name                 sql.String `sql:"name"`
	Alias                sql.String `sql:"alias"`
	NodeType             sql.Int64  `sql:"node_type"`
	AltNodeType          sql.Int64  `sql:"alt_node_type"`
	VpcID                sql.String `sql:"vpc_id"`
	CustID               sql.Int64  `sql:"custid"`
	AcctID               sql.Int64  `sql:"acct_id"`
	State                sql.Int64  `sql:"state"`
	VMStatus             sql.Int64  `sql:"vm_status"`
	Shutdown             sql.Int64  `sql:"shutdown"`
	MarkDelete           sql.Int64  `sql:"mark_delete"`
	ClusterID            sql.Int64  `sql:"clusterid"`
	HAPeer               sql.Int64  `sql:"ha_peer"`
	HAState              sql.Int64  `sql:"ha_state"`
	SpnName              sql.String `sql:"spn_name"`
	Mp1ID                sql.Int64  `sql:"mp1_id"`
	SlotNr               sql.Int64  `sql:"slot_nr"`
	MpDpClusterID        sql.Int64  `sql:"mpdp_clusterid"`
	ComputeRegionIdx     sql.Int64  `sql:"compute_region_idx"`
	ComputeRegionName    sql.String `sql:"compute_region_name"`
	CloudProvider        sql.String `sql:"cloud_provider"`
	Version              sql.String `sql:"version"`
	SaltProfile          sql.String `sql:"salt_profile"`
	EgressIpList         sql.String `sql:"egress_ip_list"`
	EgressIpv6ListSubnet sql.String `sql:"egress_ipv6_list_subnet"`
	UpgradeCreation      sql.Int64  `sql:"upgrade_creation"`
	InboundAccessIpList  sql.String `sql:"inbound_access_ip_list"`
	PublicIp             sql.String `sql:"public_ip"`
	IsPinnedInstance     sql.Int64  `sql:"is_pinned_instance"`
	IsDynamicInstance    sql.Int64  `sql:"is_dynamic_instance"`
	NativeMachineType    sql.String `sql:"native_machine_type"`
	IsInstanceBehindNlb  sql.Bool   `sql:"is_instance_behind_nlb"`
	DeleteDeployment     sql.Int64  `sql:"delete_deployment"`
	HasNatInstance       sql.Int64  `sql:"has_nat_instance"`
	InterfaceIpList      sql.String `sql:"interface_ip_list"`
	McwEnabled           sql.Bool   `sql:"mcw_enabled"`
	IsSaseFabricSpn      sql.Bool   `sql:"is_sase_fabric_spn"`
	NoPassiveInstance    sql.Bool   `sql:"no_passive_instance"`
	EgressIpListDP2      sql.String `sql:"egress_ip_list_dp2"`
	PriPubIPMappings     sql.String `sql:"pripub_ip_mappings"`
	EgressIPMappings     sql.String `sql:"egress_ip_mappings"`
	ManagementIP         sql.String `sql:"mgt_ip"`
}

// SaltProfile holds a JSON map of configuration primitives we use to spin up a compute resource
type SaltProfile struct {
	InstName                  string   `json:"InstanceName"`
	InstanceId                int64    `json:"InstanceId"`
	Zone                      string   `json:"Zone"`
	ZonesList                 []string `json:"ZonesList"`
	RegionName                string   `json:"RegionName"`
	UserData                  string   `json:"UserData"`
	ImageProject              string   `json:"ImageProject"`
	ImageName                 string   `json:"ImageName"`
	ImageId                   string   `json:"ImageId"`
	KeyPair                   string   `json:"key-pair"`
	SvcAcct                   string   `json:"svc_acct"`
	CleanIpTag                bool     `json:"clean_ip_tag"`
	InstType                  string   `json:"InstType"`
	PrimaryDpdkQcount         int64    `json:"PrimaryDpdkQcount"`
	SecondaryDpdkQcount       int64    `json:"SecondaryDpdkQcount"`
	MgmtSubnet                string   `json:"MgmtSubnet"`
	DPSubnet                  string   `json:"DPSubnet"`
	HASubnet                  string   `json:"HASubnet"`
	MgmtNetwork               string   `json:"MgmtNetwork"`
	DPNetwork                 string   `json:"DPNetwork"`
	HANetwork                 string   `json:"HANetwork"`
	MgmtInterfaceName         string   `json:"MgmtInterfaceName"`
	DPInterfaceName           string   `json:"DPInterfaceName"`
	HAInterfaceName           string   `json:"HAInterfaceName"`
	MgmtHasExternalIP         bool     `json:"MgmtHasExternalIP"`
	DPHasExternalIP           bool     `json:"DPHasExternalIP"`
	HAHasExternalIP           bool     `json:"HAHasExternalIP"`
	StaticIp                  string   `json:"static_ip"`
	SharedMgmtVPCProj         string   `json:"SharedMgmtVPCProj"`
	PrimaryCapacityType       string   `json:"PrimaryCapacityType"`
	SecondaryCapacityType     string   `json:"SecondaryCapacityType"`
	IsUsingSpInterconnect     bool     `json:"is_using_sp_interconnect"`
	ColoInterfaceSupport      bool     `json:"colo_interface_support"`
	ColoSubnet                string   `json:"COLOSubnet"`
	ColoHasExternalIp         bool     `json:"COLOHasExternalIP"`
	ColoInterfaceName         string   `json:"COLOInterfaceName"`
	ColoNetwork               string   `json:"COLONetwork"`
	HasExternalIPv6           bool     `json:"hasExternalIPv6"`
	IsServiceNicSupported     bool     `json:"is_service_nic_supported"`
	ServiceInterfaceName      string   `json:"serviceInterfaceName"`
	ServiceNetwork            string   `json:"serviceNetwork"`
	ServiceSubnet             string   `json:"serviceSubnet"`
	ServiceProjectOverride    string   `json:"serviceProjectOverride"`
	ServiceHasExternalIP      bool     `json:"serviceHasExternalIP"`
	IsDatapathOnSharedVpc     bool     `json:"is_datapath_on_shared_vpc"`
	DPProjectOverride         string   `json:"DPProjectOverride"`
	PerformIngressIPReduction int64    `json:"perform_ingress_ip_reduction"`
	ProxyProtocolEnabled      int64    `json:"proxy-protocol-enabled"`
	MinCpuPlatform            string   `json:"MinCpuPlatform"`
	PublicIpDp2               string   `json:"public_ip_dp2"`
	DP2Subnet                 string   `json:"DP2Subnet"`
	DP2Network                string   `json:"DP2Network"`
	DP2InterfaceName          string   `json:"DP2InterfaceName"`
	DP2HasExternalIP          bool     `json:"DP2HasExternalIP"`
	HasExternalIPv6Dp2        bool     `json:"hasExternalIPv6Dp2"`
	RegionId                  int64    `json:"RegionID"`
	CPUCount                  int64    `json:"CPUCount"`
	MemGB                     int64    `json:"MemGB"`
	BurstBaseline             string   `json:"BurstBaseline"`
	Is5gEnabled               bool     `json:"is_5g_enabled"`
	MasqueEnabled             bool     `json:"is_masque_enabled"`
	ColoILBName               string   `json:"colo_ilb_name"`
	IsInterconnectOnramp      bool     `json:"is_interconnect_onramp"`
	IntOnrampSubnet           string   `json:"intOnrampSubnet"`
	IntOnrampHasExternalIp    bool     `json:"intOnrampHasExternalIP"`
	IntOnrampInterfaceName    string   `json:"intOnrampInterfaceName"`
	IntOnrampNetwork          string   `json:"intOnrampNetwork"`
}

func getColumnNames() string {
	var r Row
	columns := sql.GetMappedColumns(&r)
	return strings.Join(columns, ", ")
}

// GetRowByID returns a single row.
func GetRowByID(ctx *sql.DbContext, id int64) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT %s FROM %s", getColumnNames(), tableName)).Where(
		"id=?", id).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

// GetRowByVMID returns a single row.
func GetRowByVMID(ctx *sql.DbContext, vmid string) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT %s FROM %s", getColumnNames(), tableName)).Where(
		"vmid=?", vmid).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

// GetInstancesForSlotWithMP returns all instances on a slot_nr with a given MP's VMID
func GetInstancesForSlotWithMP(ctx *sql.DbContext, vmid string, slotNr int8) ([]*Row, error) {
	var rows []*Row
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT %s FROM %s", getColumnNames(), tableName)).Where(
		"mpdp_clusterid=(SELECT mpdp_clusterid from instance_master where vmid=?)", vmid).Where(
		"slot_nr=?", slotNr).GetAll(&rows); err != nil {
		return nil, err
	}
	return rows, nil
}

// GetDPsForMP returns the DP instances for a given MP instance_id.
func GetDPsForMP(ctx *sql.DbContext, id int64) ([]*Row, error) {
	var rows []*Row
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where(
		"mp1_id=?", id).GetAll(&rows); err != nil {
		return nil, err
	}
	return rows, nil
}

// GetRowsByCustomerRegion returns the rows for a customer-region, and optionally a node type as well
func GetRowsByCustomerRegion(ctx *sql.DbContext, custID int64, computeRegionIndex int64, serviceTypeID int64) ([]*Row, error) {
	var rows []*Row
	if serviceTypeID != 0 {
		if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where(
			"custid=? and compute_region_idx=? and node_type=?", custID, computeRegionIndex, serviceTypeID).GetAll(&rows); err != nil {
			return nil, err
		}
	} else {
		if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where(
			`custid=? and compute_region_idx=? and (
			 (instance_master.node_type in (48, 51) and (
			   (instance_master.ha_peer is not NULL and instance_master.ha_peer != 0) or
			   instance_master.no_passive_instance = 1)) or
			 (instance_master.node_type in (49, 50, 156, 161, 191)))`,
			custID, computeRegionIndex).GetAll(&rows); err != nil {
			return nil, err
		}
	}
	return rows, nil
}

// GetAllInstancesForTenant returns all instances for given tenant_id.
func GetAllInstancesForTenant(ctx *sql.DbContext, tenantID int64) ([]*Row, error) {
	var rows []*Row
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where(
		"acct_id=?", tenantID).GetAll(&rows); err != nil {
		return nil, err
	}
	return rows, nil
}

// GetAllNonTransients returns all GPGW instances in a compute region for a tenant
// Don't count instances that are tracked for upgrade, are shutdown, or are unhealthy
func GetAllNonTransients(ctx *sql.DbContext, nodeType, custID, computeRegionIndex int64) ([]*Row, error) {
	var rows []*Row
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where(
		`node_type=? AND gw_capabilities<>2 AND mark_delete=0 AND custid=? AND
		 compute_region_idx=? AND upgrade_creation=0 AND shutdown=0 AND state!=-1`,
		nodeType, custID, computeRegionIndex).GetAll(&rows); err != nil {
		return nil, err
	}
	return rows, nil
}

// SetMarkDelete sets the mark_delete to the specified value
func (r *Row) SetMarkDelete(ctx *sql.DbContext, val int64) error {
	r.MarkDelete = sql.Int64(val)
	return ctx.Exec(fmt.Sprintf("UPDATE %s SET mark_delete=? WHERE id=?", tableName), val, r.ID.Int64())
}

// SetShutdown sets the VM shutdown state to the specified value
func (r *Row) SetShutdown(ctx *sql.DbContext, val int64) error {
	r.Shutdown = sql.Int64(val)
	return ctx.Exec(fmt.Sprintf("UPDATE %s SET shutdown=? WHERE id=?", tableName), val, r.ID.Int64())
}

func (r *Row) SetState(ctx *sql.DbContext, val int64) error {
	r.State = sql.Int64(val)
	return ctx.Exec(fmt.Sprintf("UPDATE %s SET state=? WHERE id=?", tableName), val, r.ID.Int64())
}

// Return one row where mark_delete=1 for the provided node_type.
// This will return an instance which was marked for deletion, but on which no user logouts were performed yet.
func GetRowMarkDeleteOne(ctx *sql.DbContext, nodeType, custID, computeRegionIndex int64) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where(
		`node_type=? AND gw_capabilities<>2 AND mark_delete=1 AND custid=? AND
		 compute_region_idx=? AND upgrade_creation=0`,
		nodeType, custID, computeRegionIndex).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

// Returns true if the customer has a NAT VM in the queried region
func CustRegionHasNAT(ctx *sql.DbContext, custID, regionID int64) bool {
	var rows []*Row
	if err := ctx.BuildQuery(`SELECT im.id, im.vmid FROM
                              instance_master im JOIN cust_topology ct
                              ON im.node_type=156 AND ct.is_deleted=0 AND
                              (ct.instance1_id=im.id OR ct.instance1_transient=im.id)`).Where(
		"im.custid=? AND im.compute_region_idx=?",
		custID, regionID).GetAll(&rows); err != nil {
		return false
	}
	if len(rows) == 0 {
		return false
	}
	return true
}

func GetNonTransientCountsByCustRegion(ctx *sql.DbContext) (map[string]int, error) {
	res, err := ctx.Query(`SELECT custid, compute_region_idx, node_type, count(*) FROM instance_master
						 WHERE node_type IN (49, 153) AND gw_capabilities<>2 AND mark_delete=0 AND
                         upgrade_creation=0 AND shutdown=0 AND state!=-1 
                         GROUP BY custid, compute_region_idx, node_type`)
	if err != nil {
		return nil, err
	}
	defer res.Close()
	ret := make(map[string]int)
	for res.Next() {
		var (
			custID, region, nodeType sql.Int64
			count                    int
		)
		if err := res.Scan(&custID, &region, &nodeType, &count); err != nil {
			return nil, err
		}
		if region.Int64() == 0 {
			continue
		}
		ret[fmt.Sprintf("%d-%d-%d", custID.Int64(), region.Int64(), nodeType.Int64())] = count
	}
	return ret, nil
}

func GetCurrentUnhealthyCount(ctx *sql.DbContext, custID, regionID, nodeType int64) (int, error) {
	res, err := ctx.Query("SELECT COUNT(*) FROM instance_master WHERE custid=? AND compute_region_idx=? AND node_type=? AND state=-1",
		custID, regionID, nodeType)
	if err != nil {
		return -1, err
	}
	var ret int
	defer res.Close()
	for res.Next() {
		if err := res.Scan(&ret); err != nil {
			return -1, err
		}
	}
	return ret, nil

}

func GetPinnedInstance(ctx *sql.DbContext, custID, regionID, nodeType int64) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where(
		`custid=? AND AND compute_region_idx=? AND node_type=? AND is_pinned_instance=1 
         AND is_dynamic_instance=0 and upgrade_creation=0`, custID, regionID, nodeType).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

func GetNATGWInfoPerTenantPerRegion(ctx *sql.DbContext, regionID, acctID, computeRegionID int64, isEdgeNAT bool, eipList, cloud string) (map[string]map[string][]string, error) {
	var res *sql.DbResult
	var err error
	eLog := ctx.Logger
	res, _ = ctx.Query("SELECT vmid, pripub_ip_mappings FROM instance_master WHERE acct_id=? AND compute_region_idx=? and node_type=156 and cloud_provider=?", acctID, computeRegionID, cloud)
	defer res.Close()
	ret := make(map[string]map[string][]string)
	for res.Next() {
		var (
			vmid, pripub_ip_mappings string
		)

		if err = res.Scan(&vmid, &pripub_ip_mappings); err != nil {
			eLog.LogError("GetNATGWInfoPerTenantPerRegion: Failed to scan existing IM entry data for vmid %s with mappings %v", vmid, pripub_ip_mappings)
			continue
		}

		eLog.LogInfo("GetNATGWInfoPerTenantPerRegion: Processing vmid %s with mappings %v for %d", vmid, pripub_ip_mappings, regionID)
		// Define a map to store the parsed JSON
		ipMappings := make(map[string][]string)

		// Unmarshal the JSON string into the map
		err = json.Unmarshal([]byte(pripub_ip_mappings), &ipMappings)
		if err != nil {
			return nil, err
		}

		if !isEdgeNAT {
			eipList, err := GetEIPListForVMID(ctx, vmid)
			eLog.LogInfo("GetNATGWInfoPerTenantPerRegion: Egress IP list is %s for vmid %s compute NAT", eipList, vmid)
			if err == nil {
				eLog.LogInfo("GetNATGWInfoPerTenantPerRegion: Egress IP list is %s for vmid %s for compute region NAT processing. Ignore this entry in IM as this is for the edge region NAT", eipList, vmid)
				eLog.LogInfo("GetNATGWInfoPerTenantPerRegion: Skip update for compute NAT gateway for region %d", regionID)
				continue
			} else {
				eLog.LogInfo("GetNATGWInfoPerTenantPerRegion: Egress IP list is %s for vmid %s for compute region NAT processing. Process this NAT entry", eipList, vmid)
			}
		} else {
			eipList, _ := GetEIPListForVMID(ctx, vmid)
			eLog.LogInfo("GetNATGWInfoPerTenantPerRegion: Egress IP list is %s for vmid %s edge NAT", eipList, vmid)
			ipList := make(map[string][]string)
			if err := json.Unmarshal([]byte(eipList), &ipList); err != nil {
				eLog.LogError("GetNATGWInfoPerTenantPerRegion: Error unmarshalling egress IP list: %v for edge region %d for egress_ip_list %s", err, regionID, eipList)
				continue
			}

			skipUpdate := false
			for region, _ := range ipList {
				natgwRegionID, _ := strconv.ParseInt(region, 10, 64)
				eLog.LogInfo("GetNATGWInfoPerTenantPerRegion: Found edge region %s in eip_list %s for VMID %s", region, ipList, vmid)
				if natgwRegionID != regionID {
					eLog.LogInfo("GetNATGWInfoPerTenantPerRegion: Existing NATGW region %d does not match eip list region %s", regionID, region)
					skipUpdate = true
				}
			}
			if skipUpdate {
				eLog.LogInfo("GetNATGWInfoPerTenantPerRegion: Skip update for edge NAT gateway IP mappings with eip list %v for region %d", ipList, regionID)
				continue
			} else {
				eLog.LogInfo("GetNATGWInfoPerTenantPerRegion: Process edge NAT gateway IP mappings with eip list %v for region %d", ipList, regionID)
			}
		}
		ret[vmid] = ipMappings
		eLog.LogInfo("GetNATGWInfoPerTenantPerRegion: Adding IP mappings %v for VMID %s Final ret %v", ipMappings, vmid, ret)
	}
	eLog.LogInfo("GetNATGWInfoPerTenantPerRegion: Final DB data %v", ret)
	return ret, nil
}

func GetEIPListForVMID(ctx *sql.DbContext, vmID string) (string, error) {
	eLog := ctx.Logger
	res, err := ctx.Query("SELECT egress_ip_list FROM instance_master WHERE vmid=?", vmID)
	if err != nil {
		eLog.LogError("GetEIPListForVMID: No entry found for VMID %s", vmID)
		return "", err
	}
	var ret string
	defer res.Close()
	for res.Next() {
		if err := res.Scan(&ret); err != nil {
			eLog.LogError("GetEIPListForVMID: Failed to scan egress_ip_list for VMID %s", vmID)
			return "", err
		}
	}
	eLog.LogInfo("GetEIPListForVMID: EIP list %v for vmid %s", ret, vmID)
	return ret, nil
}

// GetRowByNATOCID returns a single row. It queries for the row using the NAT gateway OCID
func GetRowByNATOCID(ctx *sql.DbContext, natOCID string) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where("vmid=? and node_type=156", natOCID).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

// GetRowsNATsForCloud returns all instance_master entries for NAT gateways of the specified cloud provider.
func GetRowsNATsForCloud(ctx *sql.DbContext, cloud string) ([]*Row, error) {
	var rows []*Row
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where("cloud_provider=? and node_type=156", cloud).GetAll(&rows); err != nil {
		return nil, err
	}
	return rows, nil
}

// GetRowByNATInstanceID returns a single row. It queries for the row using the NAT gateway IM ID
func GetRowByNATInstanceID(ctx *sql.DbContext, natInstanceID string) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where("id=? and node_type=156", natInstanceID).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

func GetPrivateIpByNodeTypeAndRegionImpl(ctx *sql.DbContext, nodeType int64,
	computeRegionIdx int64, custID int64) (map[string]string, error) {
	query := `SELECT id, name, pvt_ip FROM instance_master WHERE node_type = ? AND compute_region_idx = ? AND custid = ?`
	res, err := ctx.Query(query, nodeType, computeRegionIdx, custID)
	if err != nil {
		return nil, err
	}
	defer res.Close()
	ret := make(map[string]string)
	for res.Next() {
		var (
			id    int64
			name  []byte
			pvtIP []byte
		)

		if err := res.Scan(&id, &name, &pvtIP); err != nil {
			return nil, err
		}
		fmt.Println("id: %d, name: %s, pvtIP: %s", id, name, pvtIP)
		idStr := fmt.Sprintf("%d", id)
		nameStr := string(name)
		pvtIPStr := string(pvtIP)

		ret[idStr] = fmt.Sprintf("%s,%s", nameStr, pvtIPStr)
	}
	return ret, nil
}

func GetRowByNodeTypeAndRegion(ctx *sql.DbContext, custId, nodeType, computeRegionId int64) (*Row, error) {
	row := &Row{}
	qb := ctx.BuildQuery("SELECT public_ip FROM " + tableName)
	qb.Where("custid = ? AND compute_region_idx = ? AND node_type = ?", custId, computeRegionId, nodeType)
	if err := qb.GetOne(row); err != nil {
		return nil, err
	}
	return row, nil
}
