package ingress_master

import (
	"fmt"
	"orchestrator/libs/go/dbaccess/sql"
	"strings"
)

var tableName string = "ingress_master"

type Row struct {
	CustID                  sql.Int64  `sql:"custid"`
	ComputeRegionID         sql.String `sql:"compute_region_id"`
	VNI                     sql.Int64  `sql:"vni"`
	RegionalFQDN            sql.String `sql:"regional_fqdn"`
	ExternalRegionalIP      sql.String `sql:"external_regional_ip"`
	NativeComputeRegionName sql.String `sql:"native_compute_region_name"`
	CloudProvider           sql.String `sql:"cloud_provider"`
	ILBIP                   sql.String `sql:"ilb_ip"`
	ArgoAppURL              sql.String `sql:"argo_app_url"`
	UpgradeID               sql.String `sql:"upgrade_id"`
	TraceID                 sql.String `sql:"trace_id"`
	ProvisioningStatus      *string    `sql:"provisioning_status"`
	ValuesOverride          *string    `sql:"values_override"`
	OrchEntities            *string    `sql:"orch_entities"`
	DeploymentOutput        *string    `sql:"deployment_output"`
	ChartSettings           *string    `sql:"chart_settings"`
	ProxySettings           *string    `sql:"proxy_settings"`
	CPUSettings             *string    `sql:"cpu_settings"`
	MemorySettings          *string    `sql:"memory_settings"`
	DeploymentLabels        *string    `sql:"deployment_labels"`
	HPASettings             *string    `sql:"hpa_settings"`
	VPASettings             *string    `sql:"vpa_settings"`
	EdgeLocationMapping     *string    `sql:"edge_location_mapping"`
	CreateTime              sql.String `sql:"create_time"`
	UpdateTime              sql.String `sql:"update_time"`
}

type ProvisioningStatus struct {
	EnvoyDepStatus             string `json:"envoy_dep_status,omitempty"`
	ExternalLoadBalancerStatus string `json:"external_load_balancer_status,omitempty"`
	InternalLoadBalancerStatus string `json:"internal_load_balancer_status,omitempty"`
	KROExtIPStatus             string `json:"kro_ext_ip_status,omitempty"`
	Status                     string `json:"status,omitempty"`
}

func getColumnNames() string {
	var r Row
	columns := sql.GetMappedColumns(&r)
	return strings.Join(columns, ", ")
}

// getPlaceholders generates a string of placeholders for SQL queries
// based on the number of fields in Row.
func getPlaceholders() string {
	var r Row
	columns := sql.GetMappedColumns(&r)
	placeholders := make([]string, len(columns))
	for i := range placeholders {
		placeholders[i] = "?"
	}
	return strings.Join(placeholders, ", ")
}

// Save inserts the row in the table
func (r *Row) Save(ctx *sql.DbContext) error {
	qStr := "INSERT INTO " + tableName + " (" + getColumnNames() + ") VALUES (" + getPlaceholders() + ")"
	return ctx.Exec(qStr, sql.GetArgs(r)...)
}

func (r *Row) UpdateFQDN(ctx *sql.DbContext) error {
	qStr := fmt.Sprintf("UPDATE %s SET regional_fqdn = ?, external_regional_ip = ? WHERE custid = ?  AND compute_region_id = ?", tableName)
	return ctx.Exec(qStr, r.RegionalFQDN, r.ExternalRegionalIP, r.CustID, r.ComputeRegionID)
}

// Save upserts the row in the table (insert or update if exists)
func (r *Row) Upsert(ctx *sql.DbContext) error {
	columns := getColumnNames()
	placeholders := getPlaceholders()

	// Create UPDATE clause for ON DUPLICATE KEY UPDATE
	var tempRow Row
	columnList := sql.GetMappedColumns(&tempRow)
	updateClauses := make([]string, 0, len(columnList))
	for _, column := range columnList {
		updateClauses = append(updateClauses, column+" = VALUES("+column+")")
	}
	updateClause := strings.Join(updateClauses, ", ")

	qStr := "INSERT INTO " + tableName + " (" + columns + ") VALUES (" + placeholders + ") ON DUPLICATE KEY UPDATE " + updateClause
	return ctx.Exec(qStr, sql.GetArgs(r)...)
}

// GetByID retrieves the row corresponds to the input primary key (ID)
func GetByID(ctx *sql.DbContext, id int64) (*Row, error) {
	row := &Row{}
	qb := ctx.BuildQuery("SELECT * FROM " + tableName)
	qb.Where("custid in (0, ?) ORDER BY custid DESC LIMIT 1", id)
	if err := qb.GetOne(row); err != nil {
		return nil, err
	}
	return row, nil
}

func GetAllRows(ctx *sql.DbContext, id int64) ([]*Row, error) {
	var rows []*Row
	qb := ctx.BuildQuery("SELECT * FROM " + tableName)
	qb.Where("custid = ?", id)
	if err := qb.GetAll(&rows); err != nil {
		return nil, err
	}
	return rows, nil
}

func GetRowForRegionId(ctx *sql.DbContext, custId, regionId int64) (*Row, error) {
	row := &Row{}
	qb := ctx.BuildQuery("SELECT * FROM " + tableName)
	qb.Where("custid = ? AND compute_region_id = ?", custId, regionId)
	if err := qb.GetOne(row); err != nil {
		return nil, err
	}
	return row, nil
}

// Delete deletes the row using the primary key
func (r *Row) Delete(ctx *sql.DbContext) error {
	qStr := "DELETE FROM " + tableName + " WHERE custid = ? AND compute_region_id = ?"
	return ctx.Exec(qStr, r.CustID, r.ComputeRegionID)
}

func UpdateProvStatus(ctx *sql.DbContext, custId, computeRegionId int64, status *string) error {
	qStr := fmt.Sprintf("UPDATE %s SET provisioning_status = ? WHERE custid = ?  AND compute_region_id = ?", tableName)
	return ctx.Exec(qStr, status, custId, computeRegionId)
}

func UpdateIlbIP(ctx *sql.DbContext, custId, computeRegionId int64, ilbIP string) error {
	qStr := fmt.Sprintf("UPDATE %s SET ilb_ip = ? WHERE custid = ?  AND compute_region_id = ?", tableName)
	return ctx.Exec(qStr, ilbIP, custId, computeRegionId)
}
