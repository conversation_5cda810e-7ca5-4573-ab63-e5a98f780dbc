package gpaas_table

import (
	"orchestrator/libs/go/dbaccess/sql"
)

var tableName string = "gpaas_table"

type Row struct {
	ID               sql.Int64  `sql:"id"`
	CustID           sql.Int64  `sql:"custid"`
	Name             sql.String `sql:"name"`
	Hostname         sql.String `sql:"hostname"`
	SSLTlsSvcProfile sql.String `sql:"ssl_tls_svc_profile"`
	AuthProfile      sql.String `sql:"auth_profile"`
	IntHostName      sql.String `sql:"int_host_name"`
	IntHostIP        sql.String `sql:"int_host_ip"`
	PortalName       sql.String `sql:"portal_name"`
	GWName           sql.String `sql:"gw_name"`
	AlternateGWName  sql.String `sql:"alternate_gw_name"`
	PortalCert       sql.String `sql:"portal_cert"`
	GatewayCert      sql.String `sql:"gateway_cert"`
	GatewayCA        sql.String `sql:"gateway_ca"`
	GpaasDict        sql.String `sql:"gpaas_dict"`
	CreateTime       sql.String `sql:"create_time"`
	UpdateTime       sql.String `sql:"update_time"`
	GatewayCertType  sql.String `sql:"gateway_cert_type"`
	SelfSignedCert   sql.String `sql:"self_signed_cert"`
	Provider         sql.String `sql:"provider"`
	GatewayCert1     sql.String `sql:"gateway_cert_1"`
	GatewayCert2     sql.String `sql:"gateway_cert_2"`
}

// GetByRegionId retrieves the row corresponds to the input primary key (ID)
func GetByCustId(ctx *sql.DbContext, id int64) (*Row, error) {
	row := &Row{}
	qb := ctx.BuildQuery("SELECT * FROM " + tableName)
	qb.Where("custid=?", id)
	if err := qb.GetOne(row); err != nil {
		return nil, err
	}
	return row, nil
}
