package tenant_app_associations

import (
	"encoding/json"
	"errors"
	"fmt"
	"orchestrator/utils/cdl"

	"orchestrator/libs/go/dbaccess/sql"

	"go.panw.local/pangolin/clogger"
)

var tableName string = "tenant_app_associations"

// Row holds the contents of a single row from the instance_upgrade table
type Row struct {
	ClientTenantID      sql.String `sql:"client_tenant_id"`
	ClientAppID         sql.String `sql:"client_app_id"`
	TenantID            sql.Int64  `sql:"tenant_id"`
	PaTenantID          sql.Uint64 `sql:"pa_tenant_id"`
	TsgID               sql.String `sql:"tsg_id"`
	SupportAccountID    sql.Int64  `sql:"support_account_id"`
	Associations        sql.String `sql:"associations"`
	IsDeleted           sql.Int64  `sql:"is_deleted"`
	ProvisioningStatus  sql.String `sql:"provisioning_status"`
	SuperTenantID       sql.Int64  `sql:"super_tenant_id"`
	PaeMigrated         sql.Int64  `sql:"pae_migrated"`
	LicenseCapabilities sql.String `sql:"license_capabilities"`
	FedrampStatus       sql.String `sql:"fedramp_status"`
	/* Internal members */
	eLog *clogger.EventLogger `sql:"-"`
}

type TenantAppAssociation struct {
	TenantDetails struct {
		TenantID         string `json:"tenant_id"`
		SupportAccountID string `json:"support_account_id"`
		AppID            string `json:"app_id"`
	} `json:"tenant_details"`
	LoggingService struct {
		TenantID     string `json:"tenant_id"`
		Region       string `json:"region"`
		AppID        string `json:"app_id"`
		SerialNumber string `json:"serial_number"`
	} `json:"logging_service"`
}

// GetRowByTenantID returns the row from `tenant_app_associations` for the passed TenantID
func GetRowByTenantID(ctx *sql.DbContext, tenantID int64) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where(
		"tenant_id=(SELECT super_acct_id from cust_master where acct_id=?)", tenantID).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

// GetAllRows returns all the rows
func GetAllRows(ctx *sql.DbContext) ([]*Row, error) {
	var rows []*Row
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).GetAll(&rows); err != nil {
		return nil, err
	}
	return rows, nil
}

// Get the CDL region from the Database.
func GetCDLRegionByTenantID(ctx *sql.DbContext, tenantID int64) (int, error) {
	row, err := GetRowByTenantID(ctx, tenantID)
	if err != nil {
		ctx.Logger.LogInfo("Failed to get the customer entry for Tenant ID %v, error: %v", tenantID, err)
		return cdl.CDL_REGION_UNKNOWN_IDX, err
	}

	associationsJSON := row.Associations
	ctx.Logger.LogInfo("Found row with details %+v", row)
	var myTenantAppAssociation TenantAppAssociation
	if associationsJSON != "" {
		err := json.Unmarshal([]byte(associationsJSON), &myTenantAppAssociation)
		if err != nil {
			ctx.Logger.LogError("Json unmarshalling failed. associationsJSON: %v, error: %v",
				associationsJSON, err)
			return cdl.CDL_REGION_UNKNOWN_IDX, err
		} else {
			ctx.Logger.LogInfo("myTenantAppAssociation details: %+v", myTenantAppAssociation)
		}
	}

	region := string(myTenantAppAssociation.LoggingService.Region)
	idx := cdl.AppAssociationRegionStringLookupTable[region]
	if idx <= 0 {
		return cdl.CDL_REGION_UNKNOWN_IDX, errors.New(fmt.Sprintf("Lookup failed for region %s", region))
	}

	return idx, nil
}
