// Package cust_master defines a model for the `cust_master` table
package cust_master

import (
	"fmt"
	"orchestrator/libs/go/dbaccess/sql"
	"strings"
)

var tableName string = "cust_master"

// Row holds the contents of a single row in cust_master
type Row struct {
	ID                      sql.Int64  `sql:"id"`
	Name                    sql.String `sql:"name"`
	TenantServiceGroup      sql.String `sql:"tenant_service_group"`
	RNVertScale             sql.Int64  `sql:"rn_vertscale"`
	SCVertScale             sql.Int64  `sql:"sc_vertscale"`
	AcctID                  sql.Int64  `sql:"acct_id"`
	SuperAcctID             sql.Int64  `sql:"super_acct_id"`
	SupportAcctID           sql.Int64  `sql:"support_acct_id"`
	GroupSupportAcctID      sql.Int64  `sql:"group_support_acct_id"`
	SubscriptionID          sql.String `sql:"subscription_id"`
	ProjectID               sql.String `sql:"project_id"`
	HostProjectID           sql.String `sql:"host_project_id"`
	OrchProjectID           sql.String `sql:"orch_project_id"`
	AsymmetricHAMode        sql.Int64  `sql:"asymmetric_ha_mode"`
	Fwdrulesall             sql.String `sql:"fwdrulesall"`
	L3FwdrulesMU            sql.Bool   `sql:"l3fwdrules_mu"`
	L3FwdrulesRN            sql.Bool   `sql:"l3fwdrules_rn"`
	EnableGPAutoscale       sql.Int64  `sql:"enable_gp_autoscale"`
	EnableSWGAutoscale      sql.Int64  `sql:"enable_swg_autoscale"`
	UseAllowListMU          sql.Int64  `sql:"gp_use_allow_list"`
	UseAllowListSWG         sql.Int64  `sql:"swg_use_allow_list"`
	AllowListScaleOnlyMU    sql.Int64  `sql:"gp_use_allow_list_auto_scale_only"`
	MaxNumGW                sql.Int64  `sql:"max_num_gw"`
	MaxNumSWG               sql.Int64  `sql:"max_num_swg"`
	IsNgpaProtocolEnabled   sql.Bool   `sql:"is_ngpa_protocol_enabled"`
	CreateTime              sql.String `sql:"create_time"`
	URL                     sql.String `sql:"url"`
	IsCentralCacheSupported sql.Bool   `sql:"is_central_cache_supported"`
	IsNLBSupported          sql.Bool   `sql:"is_nlb_supported"`
	ExternalIPv6Support     sql.Int64  `sql:"external_ipv6_support"`
}

func getColumnNames() string {
	var r Row
	columns := sql.GetMappedColumns(&r)
	return strings.Join(columns, ", ")
}

// GetRowByID returns a single row. It queries for the row using the primary key
func GetRowByID(ctx *sql.DbContext, id int64) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT %s FROM %s", getColumnNames(), tableName)).Where(
		"id=?", id).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

// GetRowByName returns a single row. It queries for the row using the name
func GetRowByName(ctx *sql.DbContext, name string) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT %s FROM %s", getColumnNames(), tableName)).Where(
		"name=?", name).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

// GetRowByAcctID returns a single row. It queries for the row using the acct_id
func GetRowByAcctID(ctx *sql.DbContext, acctID int64) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where(
		"acct_id=?", acctID).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

// GetRowByProjectID returns a single row. It queries for the row using the project_id
func GetRowByProjectID(ctx *sql.DbContext, projectID string) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where(
		"project_id=?", projectID).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

func GetAllRows(ctx *sql.DbContext) ([]*Row, error) {
	var rows []*Row
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where(
		"is_deleted=0").GetAll(&rows); err != nil {
		return nil, err
	}
	return rows, nil
}
