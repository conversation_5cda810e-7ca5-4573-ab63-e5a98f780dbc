package mu_cnat_scaleout

import (
	"fmt"
	"orchestrator/libs/go/dbaccess/sql"
)

var tableName string = "mu_cnat_scaleout"

type Row struct {
	TenantID        sql.Int64  `sql:"tenant_id"`
	CustID          sql.Int64  `sql:"custid"`
	ProjectID       sql.String `sql:"project_id"`
	AlertID         sql.String `sql:"alert_id"`
	RegionID        sql.String `sql:"compute_region_id"`
	NATGWUID        sql.String `sql:"nat_gateway_uid"`
	NATGWInstanceID sql.String `sql:"nat_gateway_instance_id"`
	NATAlarmStatus  sql.String `sql:"nat_alarm_status"`
	EIPMappings     sql.String `sql:"last_policy_triggered"`
	NATGWRegionID   sql.String `sql:"nat_region_id"`
}

type CountStruct struct {
	Count sql.Int64 `sql:"count"`
}

func (r *Row) Save(ctx *sql.DbContext) error {
	qStr := fmt.Sprintf(`INSERT INTO %s (tenant_id, project_id, alert_id, compute_region_id, nat_gateway_uid, nat_gateway_instance_id, nat_alarm_status, nat_region_id, custid) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`, tableName)
	return ctx.Exec(qStr, r.TenantID, r.ProjectID, r.AlertID, r.RegionID, r.NATGWUID, r.NATGWInstanceID, r.NATAlarmStatus, r.NATGWRegionID, r.CustID)
}

func DeleteNATGWAlertEntryForTenant(ctx *sql.DbContext, tenantID int64, instanceID string) error {
	qStr := fmt.Sprintf(`DELETE FROM %s WHERE tenant_id=? AND nat_gateway_instance_id=?`, tableName)
	return ctx.Exec(qStr, tenantID, instanceID)
}

func GetNATInstanceIDFromTenantID(ctx *sql.DbContext, tenantID int64, instanceOCID string) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT nat_gateway_instance_id FROM %s", tableName)).Where("tenant_id=? AND nat_gateway_uid=?", tenantID, instanceOCID).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

func UpdateNATGWAlarmDataForInstanceID(ctx *sql.DbContext, alarmStatus, natOCID, alarmPayload string) error {
	qStr := fmt.Sprintf(`UPDATE %s set nat_alarm_status=?, nat_alarm_payload=? WHERE nat_gateway_uid=?`, tableName)
	return ctx.Exec(qStr, alarmStatus, alarmPayload, natOCID)
}

// GetRowByNATOCID returns a single row. It queries for the row using the NAT gateway OCID
func GetRowByNATOCID(ctx *sql.DbContext, natOCID string) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where("nat_gateway_uid=?", natOCID).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

func UpdateNATGWEIPMappingsForInstanceID(ctx *sql.DbContext, natInstanceID, eip_mappings string) error {
	qStr := fmt.Sprintf(`UPDATE %s set last_policy_triggered=? WHERE nat_gateway_instance_id=?`, tableName)
	return ctx.Exec(qStr, eip_mappings, natInstanceID)
}

// GetRowsNATs returns all mu_cnat_scaleout entries for NAT gateways
func GetRowsNATs(ctx *sql.DbContext) ([]*Row, error) {
	var rows []*Row
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).GetAll(&rows); err != nil {
		return nil, err
	}
	return rows, nil
}

// DeleteNATGWEntryForInstanceID deletes the NAT gateway entry for the given instance ID
func DeleteNATGWEntryForInstanceID(ctx *sql.DbContext, instanceID string) error {
	qStr := fmt.Sprintf(`DELETE FROM %s WHERE nat_gateway_instance_id=?`, tableName)
	return ctx.Exec(qStr, instanceID)
}
