// Package region_master defines a model for the region_master table
package region_master

import (
	"fmt"
	"orchestrator/libs/go/dbaccess/sql"
)

var tableName string = "region_master"

// Row holds the contents of a single row in region_master
type Row struct {
	EdgeLocationRegionId             sql.Int64  `sql:"edge_location_region_id"`
	EdgeLocationRegionName           sql.String `sql:"edge_location_region_name"`
	ComputeRegionId                  sql.String `sql:"compute_region_id"`
	ComputeRegionName                sql.String `sql:"compute_region_name"`
	NativeComputeRegionName          sql.String `sql:"native_compute_region_name"`
	CloudProvider                    sql.String `sql:"cloud_provider"`
	AvailabilityZone                 sql.String `sql:"availability_zone"`
	AggRegionCfgName                 sql.String `sql:"aggregate_region_cfg_name"`
	GpaasDisplayName                 sql.String `sql:"gpaas_display_name"`
	DistanceMappingCoordinatesLatest sql.String `sql:"distance_mapping_coordinates_latest"`
	Metadata                         sql.String `sql:"metadata"`
	FqdnLocationName                 sql.String `sql:"fqdn_location_name"`
}

// GetRowByID returns a single row. It queries for the row using the primary key
func GetRowByID(ctx *sql.DbContext, id int64) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where(
		"edge_location_region_id=?", id).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

// GetRowByNativeComputeRegion returns a single row.
// It queries for the row using the primary key and native region name
func GetRowByNativeRegionName(ctx *sql.DbContext, cloudProvider, nativeRegionName string) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where(
		"native_compute_region_name=? AND cloud_provider=? LIMIT 1",
		nativeRegionName, cloudProvider).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}

func GetRowBySameEdgeLocAndComputeRegion(ctx *sql.DbContext, computeRegionId int64) (*Row, error) {
	row := Row{}
	if err := ctx.BuildQuery(fmt.Sprintf("SELECT * FROM %s", tableName)).Where(
		"edge_location_region_id=compute_region_id AND compute_region_id=? LIMIT 1",
		computeRegionId).GetOne(&row); err != nil {
		return nil, err
	}
	return &row, nil
}
