package license

import (
	"fmt"
	"orchestrator/libs/go/dbaccess/models/tenant_app_associations"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/utils/cdl"
)

func GetCDLRegionByTenantID(ctx *sql.DbContext, tenantID int64) (int, error) {

	lokiUtils, err := NewLokiFeatureCapabilityUtil(ctx, tenantID)

	if err != nil {
		return cdl.CDL_REGION_UNKNOWN_IDX, fmt.Errorf("error creating loki feature capability utils: %w", err)
	}

	isLokiTenant := lokiUtils.IsLokiTenant()

	if isLokiTenant {
		return lokiUtils.GetCDLRegionID()
	}

	regionIdx, err := tenant_app_associations.GetCDLRegionByTenantID(ctx, tenantID)

	if err != nil {
		ctx.Logger.LogError("Failed to read region for tenant ID %d: %v", tenantID, err)
		return cdl.CDL_REGION_UNKNOWN_IDX, err
	}

	return regionIdx, nil

}
