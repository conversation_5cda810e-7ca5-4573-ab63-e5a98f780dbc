package license

import (
	"orchestrator/utils/cdl"
	"testing"

	"go.panw.local/pangolin/clogger"
)

// createMockUtil creates a mock LokiFeatureCapabilityUtil with test data
func createMockUtil(data []ProductCapabilityItem) *LokiFeatureCapabilityUtil {
	// Create a proper logger instance
	logger := &clogger.EventLogger{}
	logger.Glogger = clogger.NewConsoleLogger()
	logger.NewLoggerEvent() // Initialize the logger properly

	util := &LokiFeatureCapabilityUtil{
		lokiProductCapability: data,
		cdlTenantID:           12345,
		logger:                logger,
		dbCtx:                 nil,
	}
	return util
}

// createTestData creates sample test data for testing
func createTestData() []ProductCapabilityItem {
	quantity := 100
	return []ProductCapabilityItem{
		{
			ProductID:     "prisma_access",
			InstanceID:    "pa-instance-123",
			ComputeRegion: "us-west-2",
			Features: []Feature{
				{
					FeatureID: "defaults",
					Enabled:   true,
					Attributes: []Attribute{
						{Key: "management_mode", Value: "cloud_managed"},
						{Key: "default_service_connections", Value: true},
						{Key: "default_data_transfer_capacity", Value: "5TB"},
					},
				},
				{
					FeatureID: "pba",
					Enabled:   true,
				},
				{
					FeatureID: "fedramp_level_1",
					Enabled:   true,
				},
			},
			BaseFeatures: []Feature{
				{
					FeatureID: "mobile_users",
					Enabled:   true,
					Quantity:  &quantity,
				},
				{
					FeatureID: "remote_networks",
					Enabled:   true,
				},
				{
					FeatureID: "site_to_site",
					Enabled:   true,
				},
			},
		},
		{
			ProductID:     "sls",
			InstanceID:    "sls-instance-456",
			ComputeRegion: "americas",
			Features:      []Feature{},
			BaseFeatures:  []Feature{},
		},
		{
			ProductID:     "panorama",
			InstanceID:    "panorama-instance-789",
			ComputeRegion: "eu-west-1",
			Features: []Feature{
				{
					FeatureID: "base",
					Enabled:   true,
					Attributes: []Attribute{
						{Key: "panorama_version", Value: "10.1"},
						{Key: "device_group", Value: "test-dg"},
					},
				},
			},
			BaseFeatures: []Feature{},
		},
		{
			ProductID:     "cie",
			InstanceID:    "cie-instance-101",
			ComputeRegion: "ap-southeast-1",
			Features:      []Feature{},
			BaseFeatures:  []Feature{},
		},
	}
}

func TestIsLokiTenant(t *testing.T) {
	tests := []struct {
		name        string
		data        []ProductCapabilityItem
		expected    bool
		expectError bool
		nilUtil     bool
	}{
		{
			name:        "Valid loki tenant with data",
			data:        createTestData(),
			expected:    true,
			expectError: false,
		},
		{
			name:        "Empty data",
			data:        []ProductCapabilityItem{},
			expected:    false,
			expectError: false,
		},
		{
			name:        "Nil data",
			data:        nil,
			expected:    false,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var util *LokiFeatureCapabilityUtil
			if tt.nilUtil {
				util = nil
			} else {
				util = createMockUtil(tt.data)
			}

			result := util.IsLokiTenant()

			if result != tt.expected {
				t.Errorf("IsLokiTenant() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestGetProductInfoByProductID(t *testing.T) {
	util := createMockUtil(createTestData())

	tests := []struct {
		name        string
		productID   string
		expected    bool // whether result should be non-nil
		expectError bool
	}{
		{
			name:        "Valid product ID - prisma_access",
			productID:   "prisma_access",
			expected:    true,
			expectError: false,
		},
		{
			name:        "Valid product ID - sls",
			productID:   "sls",
			expected:    true,
			expectError: false,
		},
		{
			name:        "Empty product ID (should default to prisma_access)",
			productID:   "",
			expected:    true,
			expectError: true,
		},
		{
			name:        "Non-existent product ID",
			productID:   "non_existent",
			expected:    false,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := util.GetProductInfoByProductID(tt.productID)

			if tt.expectError {
				if err == nil {
					t.Errorf("GetProductInfoByProductID(%s) expected error but got none", tt.productID)
				}
				if result != nil {
					t.Errorf("GetProductInfoByProductID(%s) expected nil result on error", tt.productID)
				}
				return
			}

			if err != nil {
				t.Errorf("GetProductInfoByProductID(%s) unexpected error: %v", tt.productID, err)
				return
			}

			if (result != nil) != tt.expected {
				t.Errorf("GetProductInfoByProductID(%s) = %v, want non-nil: %v", tt.productID, result, tt.expected)
			}

			if tt.expected && result != nil {
				expectedID := tt.productID
				if expectedID == "" {
					expectedID = "prisma_access"
				}
				if result.ProductID != expectedID {
					t.Errorf("GetProductInfoByProductID(%s) returned product ID %s, want %s", tt.productID, result.ProductID, expectedID)
				}
			}
		})
	}
}

func TestGetInstanceIDByProductID(t *testing.T) {
	util := createMockUtil(createTestData())

	tests := []struct {
		name        string
		productID   string
		expected    string
		expectError bool
	}{
		{
			name:        "Valid product ID - prisma_access",
			productID:   "prisma_access",
			expected:    "pa-instance-123",
			expectError: false,
		},
		{
			name:        "Valid product ID - sls",
			productID:   "sls",
			expected:    "sls-instance-456",
			expectError: false,
		},
		{
			name:        "Empty product ID",
			productID:   "",
			expected:    "pa-instance-123",
			expectError: true,
		},
		{
			name:        "Non-existent product ID",
			productID:   "non_existent",
			expected:    "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := util.GetInstanceIDByProductID(tt.productID)

			if tt.expectError {
				if err == nil {
					t.Errorf("GetInstanceIDByProductID(%s) expected error but got none", tt.productID)
				}
				return
			}

			if err != nil {
				t.Errorf("GetInstanceIDByProductID(%s) unexpected error: %v", tt.productID, err)
				return
			}

			if result != tt.expected {
				t.Errorf("GetInstanceIDByProductID(%s) = %s, want %s", tt.productID, result, tt.expected)
			}
		})
	}
}

func TestGetCDLRegion(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.GetCDLRegion()
	if err != nil {
		t.Errorf("GetCDLRegion() unexpected error: %v", err)
		return
	}

	expected := "americas"
	if result != expected {
		t.Errorf("GetCDLRegion() = %s, want %s", result, expected)
	}

	// Test with no SLS product
	util = createMockUtil([]ProductCapabilityItem{})
	result, err = util.GetCDLRegion()
	if err == nil {
		t.Error("GetCDLRegion() with no SLS expected error but got none")
	}
}

func TestGetPrismaAccessTenantID(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.GetPrismaAccessTenantID()
	if err != nil {
		t.Errorf("GetPrismaAccessTenantID() unexpected error: %v", err)
		return
	}

	expected := "pa-instance-123"
	if result != expected {
		t.Errorf("GetPrismaAccessTenantID() = %s, want %s", result, expected)
	}
}

func TestGetManagementMode(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.GetManagementMode()
	if err != nil {
		t.Errorf("GetManagementMode() unexpected error: %v", err)
		return
	}

	expected := "cloud_managed"
	if result != expected {
		t.Errorf("GetManagementMode() = %s, want %s", result, expected)
	}

	// Test with no defaults feature
	data := []ProductCapabilityItem{
		{
			ProductID: "prisma_access",
			Features:  []Feature{},
		},
	}
	util = createMockUtil(data)
	result, err = util.GetManagementMode()
	if err == nil {
		t.Error("GetManagementMode() with no defaults expected error but got none")
	}
}

func TestIsChinaPresent(t *testing.T) {
	// Test with China feature
	dataWithChina := []ProductCapabilityItem{
		{
			ProductID: "prisma_access",
			Features: []Feature{
				{FeatureID: "china_level_1", Enabled: true},
			},
		},
	}
	util := createMockUtil(dataWithChina)
	result, err := util.IsChinaPresent()
	if err != nil {
		t.Errorf("IsChinaPresent() unexpected error: %v", err)
		return
	}
	if !result {
		t.Error("IsChinaPresent() = false, want true when china_level feature present")
	}

	// Test without China feature
	util = createMockUtil(createTestData())
	result, err = util.IsChinaPresent()
	if err != nil {
		t.Errorf("IsChinaPresent() unexpected error: %v", err)
		return
	}
	if result {
		t.Error("IsChinaPresent() = true, want false when no china_level feature")
	}
}

func TestIsFedRAMPPresent(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.IsFedRAMPPresent()
	if err != nil {
		t.Errorf("IsFedRAMPPresent() unexpected error: %v", err)
		return
	}
	if !result {
		t.Error("IsFedRAMPPresent() = false, want true when fedramp feature enabled")
	}

	// Test with disabled FedRAMP
	dataWithDisabledFedRAMP := []ProductCapabilityItem{
		{
			ProductID: "prisma_access",
			Features: []Feature{
				{FeatureID: "fedramp_level_1", Enabled: false},
			},
		},
	}
	util = createMockUtil(dataWithDisabledFedRAMP)
	result, err = util.IsFedRAMPPresent()
	if err != nil {
		t.Errorf("IsFedRAMPPresent() unexpected error: %v", err)
		return
	}
	if result {
		t.Error("IsFedRAMPPresent() = true, want false when fedramp feature disabled")
	}
}

func TestIsPrismaAccessEnabled(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.IsPrismaAccessEnabled()
	if err != nil {
		t.Errorf("IsPrismaAccessEnabled() unexpected error: %v", err)
		return
	}
	if !result {
		t.Error("IsPrismaAccessEnabled() = false, want true when prisma_access product exists")
	}

	// Test without Prisma Access
	util = createMockUtil([]ProductCapabilityItem{})
	result, err = util.IsPrismaAccessEnabled()
	if err != nil {
		t.Errorf("IsPrismaAccessEnabled() unexpected error: %v", err)
		return
	}
	if result {
		t.Error("IsPrismaAccessEnabled() = true, want false when no prisma_access product")
	}
}

func TestIsRNPresent(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.IsRNPresent()
	if err != nil {
		t.Errorf("IsRNPresent() unexpected error: %v", err)
		return
	}
	if !result {
		t.Error("IsRNPresent() = false, want true when remote_networks feature enabled")
	}

	// Test without RN features
	dataWithoutRN := []ProductCapabilityItem{
		{
			ProductID:    "prisma_access",
			BaseFeatures: []Feature{},
		},
	}
	util = createMockUtil(dataWithoutRN)
	result, err = util.IsRNPresent()
	if err != nil {
		t.Errorf("IsRNPresent() unexpected error: %v", err)
		return
	}
	if result {
		t.Error("IsRNPresent() = true, want false when no remote_networks features")
	}
}

func TestIsMUPresent(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.IsMUPresent()
	if err != nil {
		t.Errorf("IsMUPresent() unexpected error: %v", err)
		return
	}
	if !result {
		t.Error("IsMUPresent() = false, want true when mobile_users feature enabled")
	}

	// Test without MU features
	dataWithoutMU := []ProductCapabilityItem{
		{
			ProductID:    "prisma_access",
			BaseFeatures: []Feature{},
		},
	}
	util = createMockUtil(dataWithoutMU)
	result, err = util.IsMUPresent()
	if err != nil {
		t.Errorf("IsMUPresent() unexpected error: %v", err)
		return
	}
	if result {
		t.Error("IsMUPresent() = true, want false when no mobile_users features")
	}
}

func TestIsServiceConnectionPresent(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.IsServiceConnectionPresent()
	if err != nil {
		t.Errorf("IsServiceConnectionPresent() unexpected error: %v", err)
		return
	}
	if !result {
		t.Error("IsServiceConnectionPresent() = false, want true when default_service_connections attribute exists")
	}

	// Test without service connection
	dataWithoutSC := []ProductCapabilityItem{
		{
			ProductID: "prisma_access",
			Features: []Feature{
				{
					FeatureID:  "defaults",
					Enabled:    true,
					Attributes: []Attribute{},
				},
			},
		},
	}
	util = createMockUtil(dataWithoutSC)
	result, err = util.IsServiceConnectionPresent()
	if err != nil {
		t.Errorf("IsServiceConnectionPresent() unexpected error: %v", err)
		return
	}
	if result {
		t.Error("IsServiceConnectionPresent() = true, want false when no default_service_connections attribute")
	}
}

func TestGetPrismaAccessAppID(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.GetPrismaAccessAppID()
	if err != nil {
		t.Errorf("GetPrismaAccessAppID() unexpected error: %v", err)
		return
	}

	expected := "prisma_access_panorama"
	if result != expected {
		t.Errorf("GetPrismaAccessAppID() = %s, want %s when panorama product exists", result, expected)
	}

	// Test without Panorama
	dataWithoutPanorama := []ProductCapabilityItem{
		{ProductID: "prisma_access"},
	}
	util = createMockUtil(dataWithoutPanorama)
	result, err = util.GetPrismaAccessAppID()
	if err != nil {
		t.Errorf("GetPrismaAccessAppID() unexpected error: %v", err)
		return
	}

	expected = "prisma_access"
	if result != expected {
		t.Errorf("GetPrismaAccessAppID() = %s, want %s when no panorama product", result, expected)
	}
}

func TestGetDirectorySyncInstanceID(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.GetDirectorySyncInstanceID()
	if err != nil {
		t.Errorf("GetDirectorySyncInstanceID() unexpected error: %v", err)
		return
	}

	expected := "cie-instance-101"
	if result != expected {
		t.Errorf("GetDirectorySyncInstanceID() = %s, want %s", result, expected)
	}
}

func TestGetDirectorySyncRegion(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.GetDirectorySyncRegion()
	if err != nil {
		t.Errorf("GetDirectorySyncRegion() unexpected error: %v", err)
		return
	}

	expected := "ap-southeast-1"
	if result != expected {
		t.Errorf("GetDirectorySyncRegion() = %s, want %s", result, expected)
	}
}

func TestIsPBAEnabled(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.IsPBAEnabled()
	if err != nil {
		t.Errorf("IsPBAEnabled() unexpected error: %v", err)
		return
	}
	if !result {
		t.Error("IsPBAEnabled() = false, want true when pba feature enabled")
	}

	// Test with disabled PBA
	dataWithDisabledPBA := []ProductCapabilityItem{
		{
			ProductID: "prisma_access",
			Features: []Feature{
				{FeatureID: "pba", Enabled: false},
			},
		},
	}
	util = createMockUtil(dataWithDisabledPBA)
	result, err = util.IsPBAEnabled()
	if err != nil {
		t.Errorf("IsPBAEnabled() unexpected error: %v", err)
		return
	}
	if result {
		t.Error("IsPBAEnabled() = true, want false when pba feature disabled")
	}
}

func TestIsSitePresent(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.IsSitePresent()
	if err != nil {
		t.Errorf("IsSitePresent() unexpected error: %v", err)
		return
	}
	if !result {
		t.Error("IsSitePresent() = false, want true when site_ feature present")
	}

	// Test without site features
	dataWithoutSite := []ProductCapabilityItem{
		{
			ProductID:    "prisma_access",
			BaseFeatures: []Feature{},
		},
	}
	util = createMockUtil(dataWithoutSite)
	result, err = util.IsSitePresent()
	if err != nil {
		t.Errorf("IsSitePresent() unexpected error: %v", err)
		return
	}
	if result {
		t.Error("IsSitePresent() = true, want false when no site_ features")
	}
}

func TestGetPanoramaDetails(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.GetPanoramaDetails()
	if err != nil {
		t.Errorf("GetPanoramaDetails() unexpected error: %v", err)
		return
	}

	if len(result) == 0 {
		t.Error("GetPanoramaDetails() returned empty map, want panorama details")
	}

	if result["panorama_version"] != "10.1" {
		t.Errorf("GetPanoramaDetails() panorama_version = %v, want 10.1", result["panorama_version"])
	}

	if result["device_group"] != "test-dg" {
		t.Errorf("GetPanoramaDetails() device_group = %v, want test-dg", result["device_group"])
	}
}

func TestIsNextGenPA(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.IsNextGenPA()
	if err == nil {
		t.Error("IsNextGenPA() expected error for unimplemented function")
	}
	if result != false {
		t.Errorf("IsNextGenPA() = %v, want false", result)
	}
}

func TestIsNLBSupported(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.IsNLBSupported()
	if err == nil {
		t.Error("IsNLBSupported() expected error for unimplemented function")
	}
	if result != false {
		t.Errorf("IsNLBSupported() = %v, want false", result)
	}
}

func TestGetExcessDataTransferCap(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.GetExcessDataTransferCap()
	if err == nil {
		t.Error("GetExcessDataTransferCap() expected error, got nil")
	}
	if result != nil {
		t.Errorf("GetExcessDataTransferCap() = %v, want nil", result)
	}
}

func TestGetTotalDataTransferCap(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.GetTotalDataTransferCap()
	if err == nil {
		t.Error("GetTotalDataTransferCap() expected error, got nil")
	}
	if result != nil {
		t.Errorf("GetTotalDataTransferCap() = %v, want nil", result)
	}
}

func TestGetDefaultDataTransferCap(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.GetDefaultDataTransferCap()
	if err != nil {
		t.Errorf("GetDefaultDataTransferCap() unexpected error: %v", err)
		return
	}

	expected := "5TB"
	if result != expected {
		t.Errorf("GetDefaultDataTransferCap() = %v, want %s", result, expected)
	}

	// Test without default data transfer capacity
	dataWithoutDTC := []ProductCapabilityItem{
		{
			ProductID: "prisma_access",
			Features: []Feature{
				{
					FeatureID:  "defaults",
					Enabled:    true,
					Attributes: []Attribute{},
				},
			},
		},
	}
	util = createMockUtil(dataWithoutDTC)
	result, err = util.GetDefaultDataTransferCap()
	if err == nil {
		t.Error("GetDefaultDataTransferCap() expected error when no default_data_transfer_capacity")
	}
	if result != nil {
		t.Errorf("GetDefaultDataTransferCap() = %v, want nil when no default_data_transfer_capacity", result)
	}
}

func TestGetServicesInfo(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.GetServicesInfo()
	if err != nil {
		t.Errorf("GetServicesInfo() unexpected error: %v", err)
		return
	}

	expectedServices := []string{"mobile_users", "remote_networks", "service_connection"}
	if len(result) != len(expectedServices) {
		t.Errorf("GetServicesInfo() returned %d services, want %d", len(result), len(expectedServices))
	}

	serviceMap := make(map[string]bool)
	for _, service := range result {
		serviceMap[service] = true
	}

	for _, expected := range expectedServices {
		if !serviceMap[expected] {
			t.Errorf("GetServicesInfo() missing service %s", expected)
		}
	}
}

func TestGetBaseEditionType(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.GetBaseEditionType()
	if err == nil {
		t.Error("GetBaseEditionType() expected error, got nil")
	}
	if result != "" {
		t.Errorf("GetBaseEditionType() = %s, want empty string", result)
	}
}

func TestGetMobileUsersNumUsers(t *testing.T) {
	util := createMockUtil(createTestData())
	result, err := util.GetMobileUsersNumUsers()
	if err != nil {
		t.Errorf("GetMobileUsersNumUsers() unexpected error: %v", err)
		return
	}

	expected := 100
	if result != expected {
		t.Errorf("GetMobileUsersNumUsers() = %d, want %d", result, expected)
	}

	// Test with nil product capability
	util = createMockUtil(nil)
	util.lokiProductCapability = nil
	result, err = util.GetMobileUsersNumUsers()
	if err == nil {
		t.Error("GetMobileUsersNumUsers() expected error with nil capability")
	}
	if result != 0 {
		t.Errorf("GetMobileUsersNumUsers() with nil capability = %d, want 0", result)
	}

	// Test with no prisma_access product
	dataWithoutPA := []ProductCapabilityItem{
		{ProductID: "other_product"},
	}
	util = createMockUtil(dataWithoutPA)
	result, err = util.GetMobileUsersNumUsers()
	if err == nil {
		t.Error("GetMobileUsersNumUsers() expected error without prisma_access")
	}
	if result != 0 {
		t.Errorf("GetMobileUsersNumUsers() without prisma_access = %d, want 0", result)
	}

	// Test with no mobile users feature
	dataWithoutMU := []ProductCapabilityItem{
		{
			ProductID:    "prisma_access",
			BaseFeatures: []Feature{},
		},
	}
	util = createMockUtil(dataWithoutMU)
	result, err = util.GetMobileUsersNumUsers()
	if err == nil {
		t.Error("GetMobileUsersNumUsers() expected error without mobile_users feature")
	}
	if result != 0 {
		t.Errorf("GetMobileUsersNumUsers() without mobile_users feature = %d, want 0", result)
	}

	// Test with mobile users feature but no quantity
	dataWithoutQuantity := []ProductCapabilityItem{
		{
			ProductID: "prisma_access",
			BaseFeatures: []Feature{
				{
					FeatureID: "mobile_users",
					Enabled:   true,
					Quantity:  nil,
				},
			},
		},
	}
	util = createMockUtil(dataWithoutQuantity)
	result, err = util.GetMobileUsersNumUsers()
	if err == nil {
		t.Error("GetMobileUsersNumUsers() expected error without quantity")
	}
	if result != 0 {
		t.Errorf("GetMobileUsersNumUsers() without quantity = %d, want 0", result)
	}
}

func TestGetCDLRegionID(t *testing.T) {

	// Test with no SLS product
	util := createMockUtil([]ProductCapabilityItem{})
	result, err := util.GetCDLRegionID()
	if err == nil {
		t.Error("GetCDLRegionID() expected error with no SLS")
	}
	if result != cdl.CDL_REGION_UNKNOWN_IDX {
		t.Errorf("GetCDLRegionID() with no SLS = %d, want %d", result, cdl.CDL_REGION_UNKNOWN_IDX)
	}

	// Test with SLS product but unknown region
	dataWithUnknownRegion := []ProductCapabilityItem{
		{
			ProductID:     "sls",
			InstanceID:    "sls-instance-456",
			ComputeRegion: "unknown-region",
		},
	}
	util = createMockUtil(dataWithUnknownRegion)
	result, err = util.GetCDLRegionID()
	if err == nil {
		t.Error("GetCDLRegionID() expected error with unknown region")
	}
	if result != cdl.CDL_REGION_UNKNOWN_IDX {
		t.Errorf("GetCDLRegionID() with unknown region = %d, want %d", result, cdl.CDL_REGION_UNKNOWN_IDX)
	}

	// Test with different known regions
	tests := []struct {
		name           string
		region         string
		expectedRegion int
		expectError    bool
	}{
		{
			name:           "Europe region",
			region:         "europe",
			expectedRegion: cdl.CDL_REGION_EUROPE_IDX,
			expectError:    false,
		},
		{
			name:           "UK region",
			region:         "uk",
			expectedRegion: cdl.CDL_REGION_UK_IDX,
			expectError:    false,
		},
		{
			name:           "FedRAMP region",
			region:         "gov",
			expectedRegion: cdl.CDL_REGION_FEDRAMP_IDX,
			expectError:    false,
		},
		{
			name:           "Canada region",
			region:         "ca",
			expectedRegion: cdl.CDL_REGION_CANADA_IDX,
			expectError:    false,
		},
		{
			name:           "Japan region",
			region:         "jp",
			expectedRegion: cdl.CDL_REGION_JAPAN_IDX,
			expectError:    false,
		},
		{
			name:           "Australia region",
			region:         "au",
			expectedRegion: cdl.CDL_REGION_AUSTRALIA_IDX,
			expectError:    false,
		},
		{
			name:           "Germany region",
			region:         "de",
			expectedRegion: cdl.CDL_REGION_GERMANY_IDX,
			expectError:    false,
		},
		{
			name:           "India region",
			region:         "in",
			expectedRegion: cdl.CDL_REGION_INDIA_IDX,
			expectError:    false,
		},
		{
			name:           "Singapore region",
			region:         "sg",
			expectedRegion: cdl.CDL_REGION_SINGAPORE_IDX,
			expectError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dataWithSpecificRegion := []ProductCapabilityItem{
				{
					ProductID:     "sls",
					InstanceID:    "sls-instance-456",
					ComputeRegion: tt.region,
				},
			}
			util := createMockUtil(dataWithSpecificRegion)
			result, err := util.GetCDLRegionID()

			if tt.expectError {
				if err == nil {
					t.Errorf("GetCDLRegionID() for %s expected error but got none", tt.region)
				}
				return
			}

			if err != nil {
				t.Errorf("GetCDLRegionID() for %s unexpected error: %v", tt.region, err)
				return
			}

			if result != tt.expectedRegion {
				t.Errorf("GetCDLRegionID() for %s = %d, want %d", tt.region, result, tt.expectedRegion)
			}
		})
	}
}
