package license

import (
	"encoding/json"
	"fmt"
	"orchestrator/libs/go/dbaccess/models/prisma_access_license_metadata"
	"orchestrator/utils/cdl"
	"strings"

	"go.panw.local/pangolin/clogger"
	"orchestrator/libs/go/dbaccess/sql"
)

// ProductCapabilityItem represents a single product capability entry
type ProductCapabilityItem struct {
	ProductID     string    `json:"product_id"`
	InstanceID    string    `json:"instance_id"`
	ComputeRegion string    `json:"compute_region"`
	Features      []Feature `json:"features"`
	BaseFeatures  []Feature `json:"base_features"`
}

// Feature represents a feature within a product capability
type Feature struct {
	FeatureID  string      `json:"feature_id"`
	Enabled    bool        `json:"enabled"`
	Quantity   *int        `json:"quantity,omitempty"`
	Attributes []Attribute `json:"attributes,omitempty"`
}

// Attribute represents an attribute within a feature
type Attribute struct {
	Key   string      `json:"key"`
	Value interface{} `json:"value"`
}

// ProductCapabilityData represents the data structure from the database
type ProductCapabilityData struct {
	Data []ProductCapabilityItem `json:"data"`
}

// LokiFeatureCapabilityUtil contains helper functions to fetch product_capability
// and useful functions to get information from the data
type LokiFeatureCapabilityUtil struct {
	lokiProductCapability []ProductCapabilityItem
	cdlTenantID           int64
	logger                *clogger.EventLogger
	dbCtx                 *sql.DbContext
}

// NewLokiFeatureCapabilityUtil creates a new instance of LokiFeatureCapabilityUtil
func NewLokiFeatureCapabilityUtil(dbCtx *sql.DbContext, cdlTenantID int64) (*LokiFeatureCapabilityUtil, error) {
	if dbCtx == nil {
		return nil, fmt.Errorf("database context cannot be nil")
	}

	util := &LokiFeatureCapabilityUtil{
		cdlTenantID: cdlTenantID,
		logger:      dbCtx.Logger,
		dbCtx:       dbCtx,
	}

	if err := util.populateProductCapability(); err != nil {
		return nil, fmt.Errorf("failed to populate product capability: %w", err)
	}

	return util, nil
}

// populateProductCapability fetches product_capability from tenant_app_associations table
func (l *LokiFeatureCapabilityUtil) populateProductCapability() error {
	var capabilityData ProductCapabilityData

	pcsData, err := prisma_access_license_metadata.GetByLoggingServiceInstanceID(l.dbCtx, l.cdlTenantID)

	if err != nil {
		l.logger.LogError("Failed to retrieve product capability for tenant ID %d: %v", l.cdlTenantID, err)
		return fmt.Errorf("error fetching product capability: %w", err)
	}

	if len(pcsData) == 0 {
		l.logger.LogDebug("No PCS data found for tenant ID %d", l.cdlTenantID)
		return nil
	}

	productCapabilityJSON := []byte(pcsData[0].ProductCapability)
	if err := json.Unmarshal(productCapabilityJSON, &capabilityData); err != nil {
		l.logger.LogError("Failed to unmarshal product_capability JSON for tenant_id %d: %v", l.cdlTenantID, err)
		return fmt.Errorf("failed to unmarshal product_capability JSON for tenant_id %d: %w", l.cdlTenantID, err)
	}

	l.lokiProductCapability = capabilityData.Data
	l.logger.LogInfo("_populate_product_capability: successfully loaded capability data")

	return nil
}

// IsLokiTenant checks if this is a Loki tenant
func (l *LokiFeatureCapabilityUtil) IsLokiTenant() bool {
	isLokiTenant := l.lokiProductCapability != nil && len(l.lokiProductCapability) > 0

	l.logger.LogDebug("Tenant ID %d, is loki tenant: %t", l.cdlTenantID, isLokiTenant)

	return isLokiTenant
}

// GetProductInfoByProductID returns product info for a given product ID
func (l *LokiFeatureCapabilityUtil) GetProductInfoByProductID(productID string) (*ProductCapabilityItem, error) {
	if productID == "" {
		return nil, fmt.Errorf("productID is not provided")
	}

	if l.lokiProductCapability == nil {
		return nil, fmt.Errorf("product capability data is not loaded for tenant_id %d", l.cdlTenantID)
	}

	for _, item := range l.lokiProductCapability {
		if item.ProductID == productID {
			return &item, nil
		}
	}

	return nil, fmt.Errorf("product with ID '%s' not found for tenant_id %d", productID, l.cdlTenantID)
}

// GetInstanceIDByProductID returns instance ID for a given product ID
func (l *LokiFeatureCapabilityUtil) GetInstanceIDByProductID(productID string) (string, error) {
	if productID == "" {
		return "", fmt.Errorf("productID is not provided")
	}

	item, err := l.GetProductInfoByProductID(productID)
	if err != nil {
		return "", fmt.Errorf("failed to get product info for product_id '%s': %w", productID, err)
	}

	if item.InstanceID == "" {
		return "", fmt.Errorf("instance ID is empty for product_id '%s'", productID)
	}

	return item.InstanceID, nil
}

// GetCDLRegion returns the CDL region from SLS product info
func (l *LokiFeatureCapabilityUtil) GetCDLRegion() (string, error) {
	slsInfo, err := l.GetProductInfoByProductID("sls")
	if err != nil {
		return "", fmt.Errorf("failed to get SLS product info: %w", err)
	}

	if slsInfo.ComputeRegion == "" {
		return "", fmt.Errorf("compute region is empty for SLS product")
	}

	return slsInfo.ComputeRegion, nil
}

// GetCDLRegionID returns the CDL region ID from SLS product info
func (l *LokiFeatureCapabilityUtil) GetCDLRegionID() (int, error) {
	slsInfo, err := l.GetProductInfoByProductID("sls")
	if err != nil {
		return cdl.CDL_REGION_UNKNOWN_IDX, fmt.Errorf("failed to get SLS product info: %w", err)
	}

	if slsInfo.ComputeRegion == "" {
		return cdl.CDL_REGION_UNKNOWN_IDX, fmt.Errorf("compute region is empty for SLS product")
	}

	idx, exists := cdl.AppAssociationRegionStringLookupTable[slsInfo.ComputeRegion]
	if !exists {
		return cdl.CDL_REGION_UNKNOWN_IDX, fmt.Errorf("region '%s' not found in lookup table", slsInfo.ComputeRegion)
	}

	return idx, nil
}

// GetPrismaAccessTenantID returns the Prisma Access tenant ID
func (l *LokiFeatureCapabilityUtil) GetPrismaAccessTenantID() (string, error) {
	return l.GetInstanceIDByProductID("prisma_access")
}

// GetManagementMode returns the management mode from Prisma Access defaults
func (l *LokiFeatureCapabilityUtil) GetManagementMode() (string, error) {
	prismaAccessInfo, err := l.GetProductInfoByProductID("prisma_access")
	if err != nil {
		return "", fmt.Errorf("failed to get Prisma Access product info: %w", err)
	}

	for _, feature := range prismaAccessInfo.Features {
		if feature.FeatureID == "defaults" && feature.Enabled {
			for _, attribute := range feature.Attributes {
				if attribute.Key == "management_mode" {
					if mgmtMode, ok := attribute.Value.(string); ok {
						if mgmtMode == "" {
							return "", fmt.Errorf("management mode is empty")
						}
						return mgmtMode, nil
					}
					return "", fmt.Errorf("management mode value is not a string")
				}
			}
		}
	}

	return "", fmt.Errorf("management mode not found in defaults feature")
}

// IsChinaPresent checks if China level features are present
func (l *LokiFeatureCapabilityUtil) IsChinaPresent() (bool, error) {
	if l == nil {
		return false, fmt.Errorf("LokiFeatureCapabilityUtil is nil")
	}

	if l.lokiProductCapability == nil {
		return false, fmt.Errorf("product capability data is not loaded for tenant_id %d", l.cdlTenantID)
	}

	for _, item := range l.lokiProductCapability {
		if item.ProductID == "prisma_access" {
			for _, feature := range item.Features {
				if strings.HasPrefix(feature.FeatureID, "china_level") {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

// IsFedRAMPPresent checks if FedRAMP features are present and enabled
func (l *LokiFeatureCapabilityUtil) IsFedRAMPPresent() (bool, error) {
	if l.lokiProductCapability == nil {
		return false, fmt.Errorf("product capability data is not loaded for tenant_id %d", l.cdlTenantID)
	}

	for _, item := range l.lokiProductCapability {
		if item.ProductID == "prisma_access" {
			for _, feature := range item.Features {
				if strings.HasPrefix(feature.FeatureID, "fedramp_") && feature.Enabled {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

// IsPrismaAccessEnabled checks if Prisma Access is enabled
func (l *LokiFeatureCapabilityUtil) IsPrismaAccessEnabled() (bool, error) {
	_, err := l.GetProductInfoByProductID("prisma_access")
	if err != nil {
		// If the error is specifically that the product is not found, return false with no error
		if strings.Contains(err.Error(), "not found") {
			return false, nil
		}
		return false, fmt.Errorf("failed to check if Prisma Access is enabled: %w", err)
	}
	return true, nil
}

// IsRNPresent checks if Remote Networks features are present
func (l *LokiFeatureCapabilityUtil) IsRNPresent() (bool, error) {
	if l.lokiProductCapability == nil {
		return false, fmt.Errorf("product capability data is not loaded for tenant_id %d", l.cdlTenantID)
	}

	for _, item := range l.lokiProductCapability {
		if item.ProductID == "prisma_access" {
			for _, feature := range item.BaseFeatures {
				if (feature.FeatureID == "remote_networks" ||
					strings.HasPrefix(feature.FeatureID, "site_")) && feature.Enabled {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

// IsMUPresent checks if Mobile Users features are present
func (l *LokiFeatureCapabilityUtil) IsMUPresent() (bool, error) {
	if l.lokiProductCapability == nil {
		return false, fmt.Errorf("product capability data is not loaded for tenant_id %d", l.cdlTenantID)
	}

	for _, item := range l.lokiProductCapability {
		if item.ProductID == "prisma_access" {
			for _, feature := range item.BaseFeatures {
				if feature.FeatureID == "mobile_users" && feature.Enabled {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

// IsServiceConnectionPresent checks if service connection is present
func (l *LokiFeatureCapabilityUtil) IsServiceConnectionPresent() (bool, error) {
	prismaAccessInfo, err := l.GetProductInfoByProductID("prisma_access")
	if err != nil {
		return false, fmt.Errorf("failed to get Prisma Access product info: %w", err)
	}

	for _, feature := range prismaAccessInfo.Features {
		if feature.FeatureID == "defaults" && feature.Enabled {
			for _, attribute := range feature.Attributes {
				if attribute.Key == "default_service_connections" {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

// GetPrismaAccessAppID returns the appropriate app ID
func (l *LokiFeatureCapabilityUtil) GetPrismaAccessAppID() (string, error) {
	// Check if panorama exists (don't return error if not found, just check)
	panoramaInfo, err := l.GetProductInfoByProductID("panorama")
	if err == nil && panoramaInfo != nil {
		return "prisma_access_panorama", nil
	}

	return "prisma_access", nil
}

// GetDirectorySyncInstanceID returns the directory sync instance ID from CIE
func (l *LokiFeatureCapabilityUtil) GetDirectorySyncInstanceID() (string, error) {
	return l.GetInstanceIDByProductID("cie")
}

// GetDirectorySyncRegion returns the directory sync region from CIE
func (l *LokiFeatureCapabilityUtil) GetDirectorySyncRegion() (string, error) {
	cieInfo, err := l.GetProductInfoByProductID("cie")
	if err != nil {
		return "", fmt.Errorf("failed to get CIE product info: %w", err)
	}

	if cieInfo.ComputeRegion == "" {
		return "", fmt.Errorf("compute region is empty for CIE product")
	}

	return cieInfo.ComputeRegion, nil
}

// IsPBAEnabled checks if PBA is enabled
func (l *LokiFeatureCapabilityUtil) IsPBAEnabled() (bool, error) {
	prismaAccessInfo, err := l.GetProductInfoByProductID("prisma_access")
	if err != nil {
		return false, fmt.Errorf("failed to get Prisma Access product info: %w", err)
	}

	for _, feature := range prismaAccessInfo.Features {
		if feature.FeatureID == "pba" {
			return feature.Enabled, nil
		}
	}

	return false, nil
}

// IsSitePresent checks if site features are present
func (l *LokiFeatureCapabilityUtil) IsSitePresent() (bool, error) {
	if l == nil {
		return false, fmt.Errorf("LokiFeatureCapabilityUtil is nil")
	}

	if l.lokiProductCapability == nil {
		return false, fmt.Errorf("product capability data is not loaded for tenant_id %d", l.cdlTenantID)
	}

	for _, item := range l.lokiProductCapability {
		if item.ProductID == "prisma_access" {
			for _, feature := range item.BaseFeatures {
				if strings.HasPrefix(feature.FeatureID, "site_") {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

// GetPanoramaDetails returns panorama configuration details
func (l *LokiFeatureCapabilityUtil) GetPanoramaDetails() (map[string]interface{}, error) {
	panoramaDetails := make(map[string]interface{})
	panoramaEntry, err := l.GetProductInfoByProductID("panorama")
	if err != nil {
		return nil, fmt.Errorf("failed to get Panorama product info: %w", err)
	}

	for _, feature := range panoramaEntry.Features {
		if feature.FeatureID == "base" && feature.Enabled {
			for _, attribute := range feature.Attributes {
				panoramaDetails[attribute.Key] = attribute.Value
			}
		}
	}

	if len(panoramaDetails) == 0 {
		return nil, fmt.Errorf("no panorama base features found or enabled")
	}

	return panoramaDetails, nil
}

// IsNextGenPA checks if Next-Gen Prisma Access is enabled
func (l *LokiFeatureCapabilityUtil) IsNextGenPA() (bool, error) {
	// TODO: Implement logic to check if Next-Gen Prisma Access is enabled
	return false, fmt.Errorf("IsNextGenPA not implemented yet")
}

// IsNLBSupported checks if NLB is supported
func (l *LokiFeatureCapabilityUtil) IsNLBSupported() (bool, error) {
	// TODO: Implement logic to check if NLB is supported
	return false, fmt.Errorf("IsNLBSupported not implemented yet")
}

// GetExcessDataTransferCap returns excess data transfer capacity
func (l *LokiFeatureCapabilityUtil) GetExcessDataTransferCap() (interface{}, error) {
	// TODO: Add appropriate implementation for fetching excess data transfer cap
	return nil, fmt.Errorf("tenant is Loki enabled, Loki does not support excess data transfer cap info")
}

// GetTotalDataTransferCap returns total data transfer capacity
func (l *LokiFeatureCapabilityUtil) GetTotalDataTransferCap() (interface{}, error) {
	// TODO: default_data_transfer_capacity + excess_data_transfer
	return nil, fmt.Errorf("tenant is Loki enabled, Loki does not support total data transfer cap info")
}

// GetDefaultDataTransferCap returns default data transfer capacity
func (l *LokiFeatureCapabilityUtil) GetDefaultDataTransferCap() (interface{}, error) {
	prismaAccessInfo, err := l.GetProductInfoByProductID("prisma_access")
	if err != nil {
		return nil, fmt.Errorf("failed to get Prisma Access product info: %w", err)
	}

	for _, feature := range prismaAccessInfo.Features {
		if feature.FeatureID == "defaults" && feature.Enabled {
			for _, attribute := range feature.Attributes {
				if attribute.Key == "default_data_transfer_capacity" {
					if attribute.Value == nil {
						return nil, fmt.Errorf("default data transfer capacity is nil")
					}
					return attribute.Value, nil
				}
			}
		}
	}

	return nil, fmt.Errorf("default data transfer capacity not found in defaults feature")
}

// GetServicesInfo builds list of services enabled for this tenant
func (l *LokiFeatureCapabilityUtil) GetServicesInfo() ([]string, error) {
	if l == nil {
		return nil, fmt.Errorf("LokiFeatureCapabilityUtil is nil")
	}

	var services []string
	var errors []string

	muPresent, err := l.IsMUPresent()
	if err != nil {
		errors = append(errors, fmt.Sprintf("failed to check mobile users: %v", err))
	} else if muPresent {
		services = append(services, "mobile_users")
	}

	rnPresent, err := l.IsRNPresent()
	if err != nil {
		errors = append(errors, fmt.Sprintf("failed to check remote networks: %v", err))
	} else if rnPresent {
		services = append(services, "remote_networks")
	}

	scPresent, err := l.IsServiceConnectionPresent()
	if err != nil {
		errors = append(errors, fmt.Sprintf("failed to check service connection: %v", err))
	} else if scPresent {
		services = append(services, "service_connection")
	}

	if len(errors) > 0 {
		return services, fmt.Errorf("errors occurred while getting services info: %s", strings.Join(errors, "; "))
	}

	return services, nil
}

// GetBaseEditionType returns base edition type
func (l *LokiFeatureCapabilityUtil) GetBaseEditionType() (string, error) {
	// TODO: Add appropriate implementation for fetching base edition type
	return "", fmt.Errorf("tenant is Loki enabled, Loki does not support get base edition license_type")
}

// GetMobileUsersNumUsers returns the quantity of mobile users from the loki_product_capability
// Returns 0 if not found or if any error occurs
func (l *LokiFeatureCapabilityUtil) GetMobileUsersNumUsers() (int, error) {
	if l == nil {
		return 0, fmt.Errorf("LokiFeatureCapabilityUtil is nil")
	}

	if l.lokiProductCapability == nil {
		l.logger.LogError("Product capability is None for tenant_id %d", l.cdlTenantID)
		return 0, fmt.Errorf("product capability is nil for tenant_id %d", l.cdlTenantID)
	}

	prismaAccessInfo, err := l.GetProductInfoByProductID("prisma_access")
	if err != nil {
		l.logger.LogError("Prisma access product not found for tenant_id %d", l.cdlTenantID)
		return 0, fmt.Errorf("prisma access product not found for tenant_id %d: %w", l.cdlTenantID, err)
	}

	for _, feature := range prismaAccessInfo.BaseFeatures {
		if feature.FeatureID != "mobile_users" {
			continue
		}

		if feature.Quantity != nil {
			return *feature.Quantity, nil
		}

		l.logger.LogError("Mobile users feature found but quantity is missing for tenant_id %d", l.cdlTenantID)
		return 0, fmt.Errorf("mobile users feature found but quantity is missing for tenant_id %d", l.cdlTenantID)
	}

	l.logger.LogError("Mobile users feature not found in product capability for tenant_id %d", l.cdlTenantID)
	return 0, fmt.Errorf("mobile users feature not found in product capability for tenant_id %d", l.cdlTenantID)
}

// Example usage:
// func ClientCode(dbCtx *sql.DbContext, logger *clogger.EventLogger) error {
//     util, err := NewLokiFeatureCapabilityUtil(dbCtx, cdlTenantID)
//     if err != nil {
//         return fmt.Errorf("failed to create Loki utility: %w", err)
//     }
//
//     isLoki, err := util.IsLokiTenant()
//     if err != nil {
//         return fmt.Errorf("failed to check if Loki tenant: %w", err)
//     }
//
//     if isLoki {
//         mgmtMode, err := util.GetManagementMode()
//         if err != nil {
//             return fmt.Errorf("failed to get management mode: %w", err)
//         }
//         // Do work with mgmtMode
//     }
//     return nil
// }
