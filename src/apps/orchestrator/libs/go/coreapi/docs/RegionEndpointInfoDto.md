# RegionEndpointInfoDto

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AdemEndpoint** | **string** |  | [optional] [default to null]
**AwsEnvironmentId** | **string** |  | [optional] [default to null]
**EmcEndpoint** | **string** |  | [optional] [default to null]
**Endpoints** | **string** |  | [optional] [default to null]
**ImportedCreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDataflowProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDatasinkProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedUpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**InsightsEndpoint** | **string** |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**Oid** | **string** |  | [optional] [default to null]
**OkyoEndpoint** | **string** |  | [optional] [default to null]
**PaEndpoint** | **string** |  | [optional] [default to null]
**PacEndpoint** | **string** |  | [optional] [default to null]
**PascEndpoint** | **string** |  | [optional] [default to null]
**RegionDescription** | **string** |  | [optional] [default to null]
**RegionName** | **string** |  | [optional] [default to null]
**TokenEndpoint** | **string** |  | [optional] [default to null]
**TokenPublicKeyEndpoint** | **string** |  | [optional] [default to null]
**Version** | **int64** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

