# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateNetworkSecurityProcessingNode**](NetworkSecurityProcessingNodeApi.md#CreateNetworkSecurityProcessingNode) | **Post** /tenant/{tenant_id}/network-security-processing-node | Create NetworkSecurityProcessingNode instance
[**DeleteNetworkSecurityProcessingNodeByOid**](NetworkSecurityProcessingNodeApi.md#DeleteNetworkSecurityProcessingNodeByOid) | **Delete** /tenant/{tenant_id}/network-security-processing-node/{oid} | Delete NetworkSecurityProcessingNode instance by oid
[**FindNetworkSecurityProcessingNodeByOid**](NetworkSecurityProcessingNodeApi.md#FindNetworkSecurityProcessingNodeByOid) | **Get** /tenant/{tenant_id}/network-security-processing-node/{oid} | Find NetworkSecurityProcessingNode instance by oid
[**ListNetworkSecurityProcessingNode**](NetworkSecurityProcessingNodeApi.md#ListNetworkSecurityProcessingNode) | **Get** /tenant/{tenant_id}/network-security-processing-node | List NetworkSecurityProcessingNode instances of the tenant
[**ListTenantRelationship41**](NetworkSecurityProcessingNodeApi.md#ListTenantRelationship41) | **Get** /tenant/{tenant_id}/network-security-processing-node/{oid}/relationship | List relationships of NetworkSecurityProcessingNode instance
[**PatchNetworkSecurityProcessingNode**](NetworkSecurityProcessingNodeApi.md#PatchNetworkSecurityProcessingNode) | **Patch** /tenant/{tenant_id}/network-security-processing-node/{oid} | Patch NetworkSecurityProcessingNode instance by oid, if not found then attempt to create a new instance
[**UpdateNetworkSecurityProcessingNodeRelationship**](NetworkSecurityProcessingNodeApi.md#UpdateNetworkSecurityProcessingNodeRelationship) | **Patch** /tenant/{tenant_id}/network-security-processing-node/{oid}/relationship | Update relationships originated from NetworkSecurityProcessingNode instance

# **CreateNetworkSecurityProcessingNode**
> PostpatchResponseDtoCommon CreateNetworkSecurityProcessingNode(ctx, body, paRequestId, tenantId, optional)
Create NetworkSecurityProcessingNode instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**NetworkSecurityProcessingNodeDtoPost**](NetworkSecurityProcessingNodeDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***NetworkSecurityProcessingNodeApiCreateNetworkSecurityProcessingNodeOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a NetworkSecurityProcessingNodeApiCreateNetworkSecurityProcessingNodeOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteNetworkSecurityProcessingNodeByOid**
> DeleteResponseDtoCommon DeleteNetworkSecurityProcessingNodeByOid(ctx, paRequestId, tenantId, oid, optional)
Delete NetworkSecurityProcessingNode instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***NetworkSecurityProcessingNodeApiDeleteNetworkSecurityProcessingNodeByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a NetworkSecurityProcessingNodeApiDeleteNetworkSecurityProcessingNodeByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindNetworkSecurityProcessingNodeByOid**
> NetworkSecurityProcessingNodeDtoGet FindNetworkSecurityProcessingNodeByOid(ctx, paRequestId, tenantId, oid, optional)
Find NetworkSecurityProcessingNode instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***NetworkSecurityProcessingNodeApiFindNetworkSecurityProcessingNodeByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a NetworkSecurityProcessingNodeApiFindNetworkSecurityProcessingNodeByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**NetworkSecurityProcessingNodeDtoGet**](NetworkSecurityProcessingNodeDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListNetworkSecurityProcessingNode**
> NetworkSecurityProcessingNodeDtoList ListNetworkSecurityProcessingNode(ctx, paRequestId, tenantId, optional)
List NetworkSecurityProcessingNode instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***NetworkSecurityProcessingNodeApiListNetworkSecurityProcessingNodeOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a NetworkSecurityProcessingNodeApiListNetworkSecurityProcessingNodeOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**NetworkSecurityProcessingNodeDtoList**](NetworkSecurityProcessingNodeDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship41**
> RelgetResponseDtoCommon ListTenantRelationship41(ctx, paRequestId, tenantId, oid, optional)
List relationships of NetworkSecurityProcessingNode instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***NetworkSecurityProcessingNodeApiListTenantRelationship41Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a NetworkSecurityProcessingNodeApiListTenantRelationship41Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchNetworkSecurityProcessingNode**
> PostpatchResponseDtoCommon PatchNetworkSecurityProcessingNode(ctx, body, paRequestId, tenantId, oid, optional)
Patch NetworkSecurityProcessingNode instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**NetworkSecurityProcessingNodeDto**](NetworkSecurityProcessingNodeDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***NetworkSecurityProcessingNodeApiPatchNetworkSecurityProcessingNodeOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a NetworkSecurityProcessingNodeApiPatchNetworkSecurityProcessingNodeOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateNetworkSecurityProcessingNodeRelationship**
> RelpatchResponseDtoCommon UpdateNetworkSecurityProcessingNodeRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from NetworkSecurityProcessingNode instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***NetworkSecurityProcessingNodeApiUpdateNetworkSecurityProcessingNodeRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a NetworkSecurityProcessingNodeApiUpdateNetworkSecurityProcessingNodeRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

