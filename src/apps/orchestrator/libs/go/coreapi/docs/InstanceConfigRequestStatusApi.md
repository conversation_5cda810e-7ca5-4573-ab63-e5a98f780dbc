# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateInstanceConfigRequestStatus**](InstanceConfigRequestStatusApi.md#CreateInstanceConfigRequestStatus) | **Post** /tenant/{tenant_id}/instance-config-request-status | Create InstanceConfigRequestStatus instance
[**DeleteInstanceConfigRequestStatusByOid**](InstanceConfigRequestStatusApi.md#DeleteInstanceConfigRequestStatusByOid) | **Delete** /tenant/{tenant_id}/instance-config-request-status/{oid} | Delete InstanceConfigRequestStatus instance by oid
[**FindInstanceConfigRequestStatusByOid**](InstanceConfigRequestStatusApi.md#FindInstanceConfigRequestStatusByOid) | **Get** /tenant/{tenant_id}/instance-config-request-status/{oid} | Find InstanceConfigRequestStatus instance by oid
[**ListInstanceConfigRequestStatus**](InstanceConfigRequestStatusApi.md#ListInstanceConfigRequestStatus) | **Get** /tenant/{tenant_id}/instance-config-request-status | List InstanceConfigRequestStatus instances of the tenant
[**ListTenantRelationship31**](InstanceConfigRequestStatusApi.md#ListTenantRelationship31) | **Get** /tenant/{tenant_id}/instance-config-request-status/{oid}/relationship | List relationships of InstanceConfigRequestStatus instance
[**PatchInstanceConfigRequestStatus**](InstanceConfigRequestStatusApi.md#PatchInstanceConfigRequestStatus) | **Patch** /tenant/{tenant_id}/instance-config-request-status/{oid} | Patch InstanceConfigRequestStatus instance by oid, if not found then attempt to create a new instance
[**UpdateInstanceConfigRequestStatusRelationship**](InstanceConfigRequestStatusApi.md#UpdateInstanceConfigRequestStatusRelationship) | **Patch** /tenant/{tenant_id}/instance-config-request-status/{oid}/relationship | Update relationships originated from InstanceConfigRequestStatus instance

# **CreateInstanceConfigRequestStatus**
> PostpatchResponseDtoCommon CreateInstanceConfigRequestStatus(ctx, body, paRequestId, tenantId, optional)
Create InstanceConfigRequestStatus instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**InstanceConfigRequestStatusDtoPost**](InstanceConfigRequestStatusDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***InstanceConfigRequestStatusApiCreateInstanceConfigRequestStatusOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a InstanceConfigRequestStatusApiCreateInstanceConfigRequestStatusOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteInstanceConfigRequestStatusByOid**
> DeleteResponseDtoCommon DeleteInstanceConfigRequestStatusByOid(ctx, paRequestId, tenantId, oid, optional)
Delete InstanceConfigRequestStatus instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***InstanceConfigRequestStatusApiDeleteInstanceConfigRequestStatusByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a InstanceConfigRequestStatusApiDeleteInstanceConfigRequestStatusByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindInstanceConfigRequestStatusByOid**
> InstanceConfigRequestStatusDtoGet FindInstanceConfigRequestStatusByOid(ctx, paRequestId, tenantId, oid, optional)
Find InstanceConfigRequestStatus instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***InstanceConfigRequestStatusApiFindInstanceConfigRequestStatusByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a InstanceConfigRequestStatusApiFindInstanceConfigRequestStatusByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**InstanceConfigRequestStatusDtoGet**](InstanceConfigRequestStatusDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListInstanceConfigRequestStatus**
> InstanceConfigRequestStatusDtoList ListInstanceConfigRequestStatus(ctx, paRequestId, tenantId, optional)
List InstanceConfigRequestStatus instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***InstanceConfigRequestStatusApiListInstanceConfigRequestStatusOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a InstanceConfigRequestStatusApiListInstanceConfigRequestStatusOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**InstanceConfigRequestStatusDtoList**](InstanceConfigRequestStatusDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship31**
> RelgetResponseDtoCommon ListTenantRelationship31(ctx, paRequestId, tenantId, oid, optional)
List relationships of InstanceConfigRequestStatus instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***InstanceConfigRequestStatusApiListTenantRelationship31Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a InstanceConfigRequestStatusApiListTenantRelationship31Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchInstanceConfigRequestStatus**
> PostpatchResponseDtoCommon PatchInstanceConfigRequestStatus(ctx, body, paRequestId, tenantId, oid, optional)
Patch InstanceConfigRequestStatus instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**InstanceConfigRequestStatusDto**](InstanceConfigRequestStatusDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***InstanceConfigRequestStatusApiPatchInstanceConfigRequestStatusOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a InstanceConfigRequestStatusApiPatchInstanceConfigRequestStatusOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateInstanceConfigRequestStatusRelationship**
> RelpatchResponseDtoCommon UpdateInstanceConfigRequestStatusRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from InstanceConfigRequestStatus instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***InstanceConfigRequestStatusApiUpdateInstanceConfigRequestStatusRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a InstanceConfigRequestStatusApiUpdateInstanceConfigRequestStatusRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

