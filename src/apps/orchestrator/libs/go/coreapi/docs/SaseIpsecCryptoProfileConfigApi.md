# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateSaseIpsecCryptoProfileConfig**](SaseIpsecCryptoProfileConfigApi.md#CreateSaseIpsecCryptoProfileConfig) | **Post** /tenant/{tenant_id}/sase-ipsec-crypto-profile-config | Create SaseIpsecCryptoProfileConfig instance
[**DeleteSaseIpsecCryptoProfileConfigByOid**](SaseIpsecCryptoProfileConfigApi.md#DeleteSaseIpsecCryptoProfileConfigByOid) | **Delete** /tenant/{tenant_id}/sase-ipsec-crypto-profile-config/{oid} | Delete SaseIpsecCryptoProfileConfig instance by oid
[**FindSaseIpsecCryptoProfileConfigByOid**](SaseIpsecCryptoProfileConfigApi.md#FindSaseIpsecCryptoProfileConfigByOid) | **Get** /tenant/{tenant_id}/sase-ipsec-crypto-profile-config/{oid} | Find SaseIpsecCryptoProfileConfig instance by oid
[**ListSaseIpsecCryptoProfileConfig**](SaseIpsecCryptoProfileConfigApi.md#ListSaseIpsecCryptoProfileConfig) | **Get** /tenant/{tenant_id}/sase-ipsec-crypto-profile-config | List SaseIpsecCryptoProfileConfig instances of the tenant
[**ListTenantRelationship64**](SaseIpsecCryptoProfileConfigApi.md#ListTenantRelationship64) | **Get** /tenant/{tenant_id}/sase-ipsec-crypto-profile-config/{oid}/relationship | List relationships of SaseIpsecCryptoProfileConfig instance
[**PatchSaseIpsecCryptoProfileConfig**](SaseIpsecCryptoProfileConfigApi.md#PatchSaseIpsecCryptoProfileConfig) | **Patch** /tenant/{tenant_id}/sase-ipsec-crypto-profile-config/{oid} | Patch SaseIpsecCryptoProfileConfig instance by oid, if not found then attempt to create a new instance
[**UpdateSaseIpsecCryptoProfileConfigRelationship**](SaseIpsecCryptoProfileConfigApi.md#UpdateSaseIpsecCryptoProfileConfigRelationship) | **Patch** /tenant/{tenant_id}/sase-ipsec-crypto-profile-config/{oid}/relationship | Update relationships originated from SaseIpsecCryptoProfileConfig instance

# **CreateSaseIpsecCryptoProfileConfig**
> PostpatchResponseDtoCommon CreateSaseIpsecCryptoProfileConfig(ctx, body, paRequestId, tenantId, optional)
Create SaseIpsecCryptoProfileConfig instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseIpsecCryptoProfileConfigDtoPost**](SaseIpsecCryptoProfileConfigDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseIpsecCryptoProfileConfigApiCreateSaseIpsecCryptoProfileConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIpsecCryptoProfileConfigApiCreateSaseIpsecCryptoProfileConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteSaseIpsecCryptoProfileConfigByOid**
> DeleteResponseDtoCommon DeleteSaseIpsecCryptoProfileConfigByOid(ctx, paRequestId, tenantId, oid, optional)
Delete SaseIpsecCryptoProfileConfig instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIpsecCryptoProfileConfigApiDeleteSaseIpsecCryptoProfileConfigByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIpsecCryptoProfileConfigApiDeleteSaseIpsecCryptoProfileConfigByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindSaseIpsecCryptoProfileConfigByOid**
> SaseIpsecCryptoProfileConfigDtoGet FindSaseIpsecCryptoProfileConfigByOid(ctx, paRequestId, tenantId, oid, optional)
Find SaseIpsecCryptoProfileConfig instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIpsecCryptoProfileConfigApiFindSaseIpsecCryptoProfileConfigByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIpsecCryptoProfileConfigApiFindSaseIpsecCryptoProfileConfigByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**SaseIpsecCryptoProfileConfigDtoGet**](SaseIpsecCryptoProfileConfigDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListSaseIpsecCryptoProfileConfig**
> SaseIpsecCryptoProfileConfigDtoList ListSaseIpsecCryptoProfileConfig(ctx, paRequestId, tenantId, optional)
List SaseIpsecCryptoProfileConfig instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseIpsecCryptoProfileConfigApiListSaseIpsecCryptoProfileConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIpsecCryptoProfileConfigApiListSaseIpsecCryptoProfileConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**SaseIpsecCryptoProfileConfigDtoList**](SaseIpsecCryptoProfileConfigDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship64**
> RelgetResponseDtoCommon ListTenantRelationship64(ctx, paRequestId, tenantId, oid, optional)
List relationships of SaseIpsecCryptoProfileConfig instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIpsecCryptoProfileConfigApiListTenantRelationship64Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIpsecCryptoProfileConfigApiListTenantRelationship64Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchSaseIpsecCryptoProfileConfig**
> PostpatchResponseDtoCommon PatchSaseIpsecCryptoProfileConfig(ctx, body, paRequestId, tenantId, oid, optional)
Patch SaseIpsecCryptoProfileConfig instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseIpsecCryptoProfileConfigDto**](SaseIpsecCryptoProfileConfigDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIpsecCryptoProfileConfigApiPatchSaseIpsecCryptoProfileConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIpsecCryptoProfileConfigApiPatchSaseIpsecCryptoProfileConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateSaseIpsecCryptoProfileConfigRelationship**
> RelpatchResponseDtoCommon UpdateSaseIpsecCryptoProfileConfigRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from SaseIpsecCryptoProfileConfig instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIpsecCryptoProfileConfigApiUpdateSaseIpsecCryptoProfileConfigRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIpsecCryptoProfileConfigApiUpdateSaseIpsecCryptoProfileConfigRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

