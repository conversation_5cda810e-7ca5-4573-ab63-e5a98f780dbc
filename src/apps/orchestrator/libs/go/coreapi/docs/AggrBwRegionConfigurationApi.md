# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateAggrBwRegionConfiguration**](AggrBwRegionConfigurationApi.md#CreateAggrBwRegionConfiguration) | **Post** /tenant/{tenant_id}/aggr-bw-region-configuration | Create AggrBwRegionConfiguration instance
[**DeleteAggrBwRegionConfigurationByOid**](AggrBwRegionConfigurationApi.md#DeleteAggrBwRegionConfigurationByOid) | **Delete** /tenant/{tenant_id}/aggr-bw-region-configuration/{oid} | Delete AggrBwRegionConfiguration instance by oid
[**FindAggrBwRegionConfigurationByOid**](AggrBwRegionConfigurationApi.md#FindAggrBwRegionConfigurationByOid) | **Get** /tenant/{tenant_id}/aggr-bw-region-configuration/{oid} | Find AggrBwRegionConfiguration instance by oid
[**ListAggrBwRegionConfiguration**](AggrBwRegionConfigurationApi.md#ListAggrBwRegionConfiguration) | **Get** /tenant/{tenant_id}/aggr-bw-region-configuration | List AggrBwRegionConfiguration instances of the tenant
[**ListTenantRelationship1**](AggrBwRegionConfigurationApi.md#ListTenantRelationship1) | **Get** /tenant/{tenant_id}/aggr-bw-region-configuration/{oid}/relationship | List relationships of AggrBwRegionConfiguration instance
[**PatchAggrBwRegionConfiguration**](AggrBwRegionConfigurationApi.md#PatchAggrBwRegionConfiguration) | **Patch** /tenant/{tenant_id}/aggr-bw-region-configuration/{oid} | Patch AggrBwRegionConfiguration instance by oid, if not found then attempt to create a new instance

# **CreateAggrBwRegionConfiguration**
> PostpatchResponseDtoCommon CreateAggrBwRegionConfiguration(ctx, body, paRequestId, tenantId, optional)
Create AggrBwRegionConfiguration instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AggrBwRegionConfigurationDtoPost**](AggrBwRegionConfigurationDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***AggrBwRegionConfigurationApiCreateAggrBwRegionConfigurationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggrBwRegionConfigurationApiCreateAggrBwRegionConfigurationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteAggrBwRegionConfigurationByOid**
> DeleteResponseDtoCommon DeleteAggrBwRegionConfigurationByOid(ctx, paRequestId, tenantId, oid, optional)
Delete AggrBwRegionConfiguration instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***AggrBwRegionConfigurationApiDeleteAggrBwRegionConfigurationByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggrBwRegionConfigurationApiDeleteAggrBwRegionConfigurationByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindAggrBwRegionConfigurationByOid**
> AggrBwRegionConfigurationDtoGet FindAggrBwRegionConfigurationByOid(ctx, paRequestId, tenantId, oid, optional)
Find AggrBwRegionConfiguration instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***AggrBwRegionConfigurationApiFindAggrBwRegionConfigurationByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggrBwRegionConfigurationApiFindAggrBwRegionConfigurationByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**AggrBwRegionConfigurationDtoGet**](AggrBwRegionConfigurationDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListAggrBwRegionConfiguration**
> AggrBwRegionConfigurationDtoList ListAggrBwRegionConfiguration(ctx, paRequestId, tenantId, optional)
List AggrBwRegionConfiguration instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***AggrBwRegionConfigurationApiListAggrBwRegionConfigurationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggrBwRegionConfigurationApiListAggrBwRegionConfigurationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**AggrBwRegionConfigurationDtoList**](AggrBwRegionConfigurationDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship1**
> RelgetResponseDtoCommon ListTenantRelationship1(ctx, paRequestId, tenantId, oid, optional)
List relationships of AggrBwRegionConfiguration instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***AggrBwRegionConfigurationApiListTenantRelationship1Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggrBwRegionConfigurationApiListTenantRelationship1Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchAggrBwRegionConfiguration**
> PostpatchResponseDtoCommon PatchAggrBwRegionConfiguration(ctx, body, paRequestId, tenantId, oid, optional)
Patch AggrBwRegionConfiguration instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AggrBwRegionConfigurationDto**](AggrBwRegionConfigurationDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***AggrBwRegionConfigurationApiPatchAggrBwRegionConfigurationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggrBwRegionConfigurationApiPatchAggrBwRegionConfigurationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

