# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateUdaTenantEdgeLocation**](UdaTenantEdgeLocationApi.md#CreateUdaTenantEdgeLocation) | **Post** /tenant/{tenant_id}/uda-tenant-edge-location | Create UdaTenantEdgeLocation instance
[**DeleteUdaTenantEdgeLocationByOid**](UdaTenantEdgeLocationApi.md#DeleteUdaTenantEdgeLocationByOid) | **Delete** /tenant/{tenant_id}/uda-tenant-edge-location/{oid} | Delete UdaTenantEdgeLocation instance by oid
[**FindUdaTenantEdgeLocationByOid**](UdaTenantEdgeLocationApi.md#FindUdaTenantEdgeLocationByOid) | **Get** /tenant/{tenant_id}/uda-tenant-edge-location/{oid} | Find UdaTenantEdgeLocation instance by oid
[**ListTenantRelationship97**](UdaTenantEdgeLocationApi.md#ListTenantRelationship97) | **Get** /tenant/{tenant_id}/uda-tenant-edge-location/{oid}/relationship | List relationships of UdaTenantEdgeLocation instance
[**ListUdaTenantEdgeLocation**](UdaTenantEdgeLocationApi.md#ListUdaTenantEdgeLocation) | **Get** /tenant/{tenant_id}/uda-tenant-edge-location | List UdaTenantEdgeLocation instances of the tenant
[**PatchUdaTenantEdgeLocation**](UdaTenantEdgeLocationApi.md#PatchUdaTenantEdgeLocation) | **Patch** /tenant/{tenant_id}/uda-tenant-edge-location/{oid} | Patch UdaTenantEdgeLocation instance by oid, if not found then attempt to create a new instance

# **CreateUdaTenantEdgeLocation**
> PostpatchResponseDtoCommon CreateUdaTenantEdgeLocation(ctx, body, paRequestId, tenantId, optional)
Create UdaTenantEdgeLocation instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**UdaTenantEdgeLocationDtoPost**](UdaTenantEdgeLocationDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***UdaTenantEdgeLocationApiCreateUdaTenantEdgeLocationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaTenantEdgeLocationApiCreateUdaTenantEdgeLocationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteUdaTenantEdgeLocationByOid**
> DeleteResponseDtoCommon DeleteUdaTenantEdgeLocationByOid(ctx, paRequestId, tenantId, oid, optional)
Delete UdaTenantEdgeLocation instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***UdaTenantEdgeLocationApiDeleteUdaTenantEdgeLocationByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaTenantEdgeLocationApiDeleteUdaTenantEdgeLocationByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindUdaTenantEdgeLocationByOid**
> UdaTenantEdgeLocationDtoGet FindUdaTenantEdgeLocationByOid(ctx, paRequestId, tenantId, oid, optional)
Find UdaTenantEdgeLocation instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***UdaTenantEdgeLocationApiFindUdaTenantEdgeLocationByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaTenantEdgeLocationApiFindUdaTenantEdgeLocationByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**UdaTenantEdgeLocationDtoGet**](UdaTenantEdgeLocationDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship97**
> RelgetResponseDtoCommon ListTenantRelationship97(ctx, paRequestId, tenantId, oid, optional)
List relationships of UdaTenantEdgeLocation instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***UdaTenantEdgeLocationApiListTenantRelationship97Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaTenantEdgeLocationApiListTenantRelationship97Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListUdaTenantEdgeLocation**
> UdaTenantEdgeLocationDtoList ListUdaTenantEdgeLocation(ctx, paRequestId, tenantId, optional)
List UdaTenantEdgeLocation instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***UdaTenantEdgeLocationApiListUdaTenantEdgeLocationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaTenantEdgeLocationApiListUdaTenantEdgeLocationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**UdaTenantEdgeLocationDtoList**](UdaTenantEdgeLocationDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchUdaTenantEdgeLocation**
> PostpatchResponseDtoCommon PatchUdaTenantEdgeLocation(ctx, body, paRequestId, tenantId, oid, optional)
Patch UdaTenantEdgeLocation instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**UdaTenantEdgeLocationDto**](UdaTenantEdgeLocationDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***UdaTenantEdgeLocationApiPatchUdaTenantEdgeLocationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaTenantEdgeLocationApiPatchUdaTenantEdgeLocationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

