# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateSaseVionConfigServiceRoot**](SaseVionConfigServiceRootApi.md#CreateSaseVionConfigServiceRoot) | **Post** /tenant/{tenant_id}/sase-vion-config-service-root | Create SaseVionConfigServiceRoot instance
[**DeleteSaseVionConfigServiceRootByOid**](SaseVionConfigServiceRootApi.md#DeleteSaseVionConfigServiceRootByOid) | **Delete** /tenant/{tenant_id}/sase-vion-config-service-root/{oid} | Delete SaseVionConfigServiceRoot instance by oid
[**FindSaseVionConfigServiceRootByOid**](SaseVionConfigServiceRootApi.md#FindSaseVionConfigServiceRootByOid) | **Get** /tenant/{tenant_id}/sase-vion-config-service-root/{oid} | Find SaseVionConfigServiceRoot instance by oid
[**ListSaseVionConfigServiceRoot**](SaseVionConfigServiceRootApi.md#ListSaseVionConfigServiceRoot) | **Get** /tenant/{tenant_id}/sase-vion-config-service-root | List SaseVionConfigServiceRoot instances of the tenant
[**ListTenantRelationship67**](SaseVionConfigServiceRootApi.md#ListTenantRelationship67) | **Get** /tenant/{tenant_id}/sase-vion-config-service-root/{oid}/relationship | List relationships of SaseVionConfigServiceRoot instance
[**PatchSaseVionConfigServiceRoot**](SaseVionConfigServiceRootApi.md#PatchSaseVionConfigServiceRoot) | **Patch** /tenant/{tenant_id}/sase-vion-config-service-root/{oid} | Patch SaseVionConfigServiceRoot instance by oid, if not found then attempt to create a new instance
[**UpdateSaseVionConfigServiceRootRelationship**](SaseVionConfigServiceRootApi.md#UpdateSaseVionConfigServiceRootRelationship) | **Patch** /tenant/{tenant_id}/sase-vion-config-service-root/{oid}/relationship | Update relationships originated from SaseVionConfigServiceRoot instance

# **CreateSaseVionConfigServiceRoot**
> PostpatchResponseDtoCommon CreateSaseVionConfigServiceRoot(ctx, body, paRequestId, tenantId, optional)
Create SaseVionConfigServiceRoot instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseVionConfigServiceRootDtoPost**](SaseVionConfigServiceRootDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseVionConfigServiceRootApiCreateSaseVionConfigServiceRootOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseVionConfigServiceRootApiCreateSaseVionConfigServiceRootOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteSaseVionConfigServiceRootByOid**
> DeleteResponseDtoCommon DeleteSaseVionConfigServiceRootByOid(ctx, paRequestId, tenantId, oid, optional)
Delete SaseVionConfigServiceRoot instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseVionConfigServiceRootApiDeleteSaseVionConfigServiceRootByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseVionConfigServiceRootApiDeleteSaseVionConfigServiceRootByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindSaseVionConfigServiceRootByOid**
> SaseVionConfigServiceRootDtoGet FindSaseVionConfigServiceRootByOid(ctx, paRequestId, tenantId, oid, optional)
Find SaseVionConfigServiceRoot instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseVionConfigServiceRootApiFindSaseVionConfigServiceRootByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseVionConfigServiceRootApiFindSaseVionConfigServiceRootByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**SaseVionConfigServiceRootDtoGet**](SaseVionConfigServiceRootDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListSaseVionConfigServiceRoot**
> SaseVionConfigServiceRootDtoList ListSaseVionConfigServiceRoot(ctx, paRequestId, tenantId, optional)
List SaseVionConfigServiceRoot instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseVionConfigServiceRootApiListSaseVionConfigServiceRootOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseVionConfigServiceRootApiListSaseVionConfigServiceRootOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**SaseVionConfigServiceRootDtoList**](SaseVionConfigServiceRootDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship67**
> RelgetResponseDtoCommon ListTenantRelationship67(ctx, paRequestId, tenantId, oid, optional)
List relationships of SaseVionConfigServiceRoot instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseVionConfigServiceRootApiListTenantRelationship67Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseVionConfigServiceRootApiListTenantRelationship67Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchSaseVionConfigServiceRoot**
> PostpatchResponseDtoCommon PatchSaseVionConfigServiceRoot(ctx, body, paRequestId, tenantId, oid, optional)
Patch SaseVionConfigServiceRoot instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseVionConfigServiceRootDto**](SaseVionConfigServiceRootDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseVionConfigServiceRootApiPatchSaseVionConfigServiceRootOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseVionConfigServiceRootApiPatchSaseVionConfigServiceRootOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateSaseVionConfigServiceRootRelationship**
> RelpatchResponseDtoCommon UpdateSaseVionConfigServiceRootRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from SaseVionConfigServiceRoot instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseVionConfigServiceRootApiUpdateSaseVionConfigServiceRootRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseVionConfigServiceRootApiUpdateSaseVionConfigServiceRootRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

