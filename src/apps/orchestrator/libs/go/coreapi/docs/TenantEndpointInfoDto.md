# TenantEndpointInfoDto

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Associations** | **string** |  | [optional] [default to null]
**AwsEnvironmentId** | **string** |  | [optional] [default to null]
**CdlRegion** | **string** |  | [optional] [default to null]
**CdlTenantId** | **string** |  | [optional] [default to null]
**ClientAppId** | **string** |  | [optional] [default to null]
**ExtUrl** | **string** |  | [optional] [default to null]
**FedrampStatus** | **string** |  | [optional] [default to null]
**Gpfqdn** | **string** |  | [optional] [default to null]
**ImportedCreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDataflowProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDatasinkProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedUpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**IsDeleted** | **int64** |  | [optional] [default to null]
**LicenseCapabilities** | **string** |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**Oid** | **string** |  | [optional] [default to null]
**PaEnv** | **string** |  | [optional] [default to null]
**PaTenantId** | **string** |  | [optional] [default to null]
**PaeMigrated** | **int64** |  | [optional] [default to null]
**ProvisioningStatus** | **string** |  | [optional] [default to null]
**SubTenantId** | **string** |  | [optional] [default to null]
**TenantId** | **string** |  | [optional] [default to null]
**TenantServiceGroup** | **string** |  | [optional] [default to null]
**Url** | **string** |  | [optional] [default to null]
**Version** | **int64** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

