# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateSaseIkeCryptoProfileConfigRoot**](SaseIkeCryptoProfileConfigRootApi.md#CreateSaseIkeCryptoProfileConfigRoot) | **Post** /tenant/{tenant_id}/sase-ike-crypto-profile-config-root | Create SaseIkeCryptoProfileConfigRoot instance
[**DeleteSaseIkeCryptoProfileConfigRootByOid**](SaseIkeCryptoProfileConfigRootApi.md#DeleteSaseIkeCryptoProfileConfigRootByOid) | **Delete** /tenant/{tenant_id}/sase-ike-crypto-profile-config-root/{oid} | Delete SaseIkeCryptoProfileConfigRoot instance by oid
[**FindSaseIkeCryptoProfileConfigRootByOid**](SaseIkeCryptoProfileConfigRootApi.md#FindSaseIkeCryptoProfileConfigRootByOid) | **Get** /tenant/{tenant_id}/sase-ike-crypto-profile-config-root/{oid} | Find SaseIkeCryptoProfileConfigRoot instance by oid
[**ListSaseIkeCryptoProfileConfigRoot**](SaseIkeCryptoProfileConfigRootApi.md#ListSaseIkeCryptoProfileConfigRoot) | **Get** /tenant/{tenant_id}/sase-ike-crypto-profile-config-root | List SaseIkeCryptoProfileConfigRoot instances of the tenant
[**ListTenantRelationship63**](SaseIkeCryptoProfileConfigRootApi.md#ListTenantRelationship63) | **Get** /tenant/{tenant_id}/sase-ike-crypto-profile-config-root/{oid}/relationship | List relationships of SaseIkeCryptoProfileConfigRoot instance
[**PatchSaseIkeCryptoProfileConfigRoot**](SaseIkeCryptoProfileConfigRootApi.md#PatchSaseIkeCryptoProfileConfigRoot) | **Patch** /tenant/{tenant_id}/sase-ike-crypto-profile-config-root/{oid} | Patch SaseIkeCryptoProfileConfigRoot instance by oid, if not found then attempt to create a new instance
[**UpdateSaseIkeCryptoProfileConfigRootRelationship**](SaseIkeCryptoProfileConfigRootApi.md#UpdateSaseIkeCryptoProfileConfigRootRelationship) | **Patch** /tenant/{tenant_id}/sase-ike-crypto-profile-config-root/{oid}/relationship | Update relationships originated from SaseIkeCryptoProfileConfigRoot instance

# **CreateSaseIkeCryptoProfileConfigRoot**
> PostpatchResponseDtoCommon CreateSaseIkeCryptoProfileConfigRoot(ctx, body, paRequestId, tenantId, optional)
Create SaseIkeCryptoProfileConfigRoot instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseIkeCryptoProfileConfigRootDtoPost**](SaseIkeCryptoProfileConfigRootDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseIkeCryptoProfileConfigRootApiCreateSaseIkeCryptoProfileConfigRootOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIkeCryptoProfileConfigRootApiCreateSaseIkeCryptoProfileConfigRootOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteSaseIkeCryptoProfileConfigRootByOid**
> DeleteResponseDtoCommon DeleteSaseIkeCryptoProfileConfigRootByOid(ctx, paRequestId, tenantId, oid, optional)
Delete SaseIkeCryptoProfileConfigRoot instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIkeCryptoProfileConfigRootApiDeleteSaseIkeCryptoProfileConfigRootByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIkeCryptoProfileConfigRootApiDeleteSaseIkeCryptoProfileConfigRootByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindSaseIkeCryptoProfileConfigRootByOid**
> SaseIkeCryptoProfileConfigRootDtoGet FindSaseIkeCryptoProfileConfigRootByOid(ctx, paRequestId, tenantId, oid, optional)
Find SaseIkeCryptoProfileConfigRoot instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIkeCryptoProfileConfigRootApiFindSaseIkeCryptoProfileConfigRootByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIkeCryptoProfileConfigRootApiFindSaseIkeCryptoProfileConfigRootByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**SaseIkeCryptoProfileConfigRootDtoGet**](SaseIkeCryptoProfileConfigRootDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListSaseIkeCryptoProfileConfigRoot**
> SaseIkeCryptoProfileConfigRootDtoList ListSaseIkeCryptoProfileConfigRoot(ctx, paRequestId, tenantId, optional)
List SaseIkeCryptoProfileConfigRoot instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseIkeCryptoProfileConfigRootApiListSaseIkeCryptoProfileConfigRootOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIkeCryptoProfileConfigRootApiListSaseIkeCryptoProfileConfigRootOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**SaseIkeCryptoProfileConfigRootDtoList**](SaseIkeCryptoProfileConfigRootDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship63**
> RelgetResponseDtoCommon ListTenantRelationship63(ctx, paRequestId, tenantId, oid, optional)
List relationships of SaseIkeCryptoProfileConfigRoot instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIkeCryptoProfileConfigRootApiListTenantRelationship63Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIkeCryptoProfileConfigRootApiListTenantRelationship63Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchSaseIkeCryptoProfileConfigRoot**
> PostpatchResponseDtoCommon PatchSaseIkeCryptoProfileConfigRoot(ctx, body, paRequestId, tenantId, oid, optional)
Patch SaseIkeCryptoProfileConfigRoot instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseIkeCryptoProfileConfigRootDto**](SaseIkeCryptoProfileConfigRootDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIkeCryptoProfileConfigRootApiPatchSaseIkeCryptoProfileConfigRootOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIkeCryptoProfileConfigRootApiPatchSaseIkeCryptoProfileConfigRootOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateSaseIkeCryptoProfileConfigRootRelationship**
> RelpatchResponseDtoCommon UpdateSaseIkeCryptoProfileConfigRootRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from SaseIkeCryptoProfileConfigRoot instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIkeCryptoProfileConfigRootApiUpdateSaseIkeCryptoProfileConfigRootRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIkeCryptoProfileConfigRootApiUpdateSaseIkeCryptoProfileConfigRootRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

