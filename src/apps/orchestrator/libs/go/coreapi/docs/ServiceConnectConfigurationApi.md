# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateServiceConnectConfiguration**](ServiceConnectConfigurationApi.md#CreateServiceConnectConfiguration) | **Post** /tenant/{tenant_id}/service-connect-configuration | Create ServiceConnectConfiguration instance
[**DeleteServiceConnectConfigurationByOid**](ServiceConnectConfigurationApi.md#DeleteServiceConnectConfigurationByOid) | **Delete** /tenant/{tenant_id}/service-connect-configuration/{oid} | Delete ServiceConnectConfiguration instance by oid
[**FindServiceConnectConfigurationByOid**](ServiceConnectConfigurationApi.md#FindServiceConnectConfigurationByOid) | **Get** /tenant/{tenant_id}/service-connect-configuration/{oid} | Find ServiceConnectConfiguration instance by oid
[**ListServiceConnectConfiguration**](ServiceConnectConfigurationApi.md#ListServiceConnectConfiguration) | **Get** /tenant/{tenant_id}/service-connect-configuration | List ServiceConnectConfiguration instances of the tenant
[**ListTenantRelationship73**](ServiceConnectConfigurationApi.md#ListTenantRelationship73) | **Get** /tenant/{tenant_id}/service-connect-configuration/{oid}/relationship | List relationships of ServiceConnectConfiguration instance
[**PatchServiceConnectConfiguration**](ServiceConnectConfigurationApi.md#PatchServiceConnectConfiguration) | **Patch** /tenant/{tenant_id}/service-connect-configuration/{oid} | Patch ServiceConnectConfiguration instance by oid, if not found then attempt to create a new instance
[**UpdateServiceConnectConfigurationRelationship**](ServiceConnectConfigurationApi.md#UpdateServiceConnectConfigurationRelationship) | **Patch** /tenant/{tenant_id}/service-connect-configuration/{oid}/relationship | Update relationships originated from ServiceConnectConfiguration instance

# **CreateServiceConnectConfiguration**
> PostpatchResponseDtoCommon CreateServiceConnectConfiguration(ctx, body, paRequestId, tenantId, optional)
Create ServiceConnectConfiguration instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ServiceConnectConfigurationDtoPost**](ServiceConnectConfigurationDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***ServiceConnectConfigurationApiCreateServiceConnectConfigurationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a ServiceConnectConfigurationApiCreateServiceConnectConfigurationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteServiceConnectConfigurationByOid**
> DeleteResponseDtoCommon DeleteServiceConnectConfigurationByOid(ctx, paRequestId, tenantId, oid, optional)
Delete ServiceConnectConfiguration instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***ServiceConnectConfigurationApiDeleteServiceConnectConfigurationByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a ServiceConnectConfigurationApiDeleteServiceConnectConfigurationByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindServiceConnectConfigurationByOid**
> ServiceConnectConfigurationDtoGet FindServiceConnectConfigurationByOid(ctx, paRequestId, tenantId, oid, optional)
Find ServiceConnectConfiguration instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***ServiceConnectConfigurationApiFindServiceConnectConfigurationByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a ServiceConnectConfigurationApiFindServiceConnectConfigurationByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**ServiceConnectConfigurationDtoGet**](ServiceConnectConfigurationDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListServiceConnectConfiguration**
> ServiceConnectConfigurationDtoList ListServiceConnectConfiguration(ctx, paRequestId, tenantId, optional)
List ServiceConnectConfiguration instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***ServiceConnectConfigurationApiListServiceConnectConfigurationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a ServiceConnectConfigurationApiListServiceConnectConfigurationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**ServiceConnectConfigurationDtoList**](ServiceConnectConfigurationDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship73**
> RelgetResponseDtoCommon ListTenantRelationship73(ctx, paRequestId, tenantId, oid, optional)
List relationships of ServiceConnectConfiguration instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***ServiceConnectConfigurationApiListTenantRelationship73Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a ServiceConnectConfigurationApiListTenantRelationship73Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchServiceConnectConfiguration**
> PostpatchResponseDtoCommon PatchServiceConnectConfiguration(ctx, body, paRequestId, tenantId, oid, optional)
Patch ServiceConnectConfiguration instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ServiceConnectConfigurationDto**](ServiceConnectConfigurationDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***ServiceConnectConfigurationApiPatchServiceConnectConfigurationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a ServiceConnectConfigurationApiPatchServiceConnectConfigurationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateServiceConnectConfigurationRelationship**
> RelpatchResponseDtoCommon UpdateServiceConnectConfigurationRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from ServiceConnectConfiguration instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***ServiceConnectConfigurationApiUpdateServiceConnectConfigurationRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a ServiceConnectConfigurationApiUpdateServiceConnectConfigurationRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

