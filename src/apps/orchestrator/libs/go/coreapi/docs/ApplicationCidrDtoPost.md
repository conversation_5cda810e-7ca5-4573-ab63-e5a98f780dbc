# ApplicationCidrDtoPost

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AwsEnvironmentId** | **string** |  | [optional] [default to null]
**BlockId** | **int32** |  | [default to null]
**CdlTenantId** | **string** |  | [optional] [default to null]
**CgnxSecurityPrefixBindingId** | **string** |  | [optional] [default to null]
**CgnxSecurityPrefixId** | **string** |  | [optional] [default to null]
**CgnxSecurityRuleId** | **string** |  | [optional] [default to null]
**CgnxStaticRouteId** | **string** |  | [optional] [default to null]
**Cidr** | **string** |  | [default to null]
**CreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**DeletedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedCreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDataflowProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDatasinkProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedUpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**Oid** | **string** |  | [default to null]
**PaTenantId** | **string** |  | [optional] [default to null]
**PoolId** | **int32** |  | [default to null]
**SubBlockId** | **int32** |  | [default to null]
**SubTenantId** | **string** |  | [default to null]
**TenantId** | **string** |  | [default to null]
**TenantServiceGroup** | **string** |  | [default to null]
**TheaterId** | **int32** |  | [optional] [default to null]
**UpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**UsedBits** | **int32** |  | [optional] [default to null]
**User** | **string** |  | [default to null]
**Version** | **int64** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

