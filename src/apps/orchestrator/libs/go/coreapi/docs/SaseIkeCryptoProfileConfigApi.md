# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateSaseIkeCryptoProfileConfig**](SaseIkeCryptoProfileConfigApi.md#CreateSaseIkeCryptoProfileConfig) | **Post** /tenant/{tenant_id}/sase-ike-crypto-profile-config | Create SaseIkeCryptoProfileConfig instance
[**DeleteSaseIkeCryptoProfileConfigByOid**](SaseIkeCryptoProfileConfigApi.md#DeleteSaseIkeCryptoProfileConfigByOid) | **Delete** /tenant/{tenant_id}/sase-ike-crypto-profile-config/{oid} | Delete SaseIkeCryptoProfileConfig instance by oid
[**FindSaseIkeCryptoProfileConfigByOid**](SaseIkeCryptoProfileConfigApi.md#FindSaseIkeCryptoProfileConfigByOid) | **Get** /tenant/{tenant_id}/sase-ike-crypto-profile-config/{oid} | Find SaseIkeCryptoProfileConfig instance by oid
[**ListSaseIkeCryptoProfileConfig**](SaseIkeCryptoProfileConfigApi.md#ListSaseIkeCryptoProfileConfig) | **Get** /tenant/{tenant_id}/sase-ike-crypto-profile-config | List SaseIkeCryptoProfileConfig instances of the tenant
[**ListTenantRelationship62**](SaseIkeCryptoProfileConfigApi.md#ListTenantRelationship62) | **Get** /tenant/{tenant_id}/sase-ike-crypto-profile-config/{oid}/relationship | List relationships of SaseIkeCryptoProfileConfig instance
[**PatchSaseIkeCryptoProfileConfig**](SaseIkeCryptoProfileConfigApi.md#PatchSaseIkeCryptoProfileConfig) | **Patch** /tenant/{tenant_id}/sase-ike-crypto-profile-config/{oid} | Patch SaseIkeCryptoProfileConfig instance by oid, if not found then attempt to create a new instance
[**UpdateSaseIkeCryptoProfileConfigRelationship**](SaseIkeCryptoProfileConfigApi.md#UpdateSaseIkeCryptoProfileConfigRelationship) | **Patch** /tenant/{tenant_id}/sase-ike-crypto-profile-config/{oid}/relationship | Update relationships originated from SaseIkeCryptoProfileConfig instance

# **CreateSaseIkeCryptoProfileConfig**
> PostpatchResponseDtoCommon CreateSaseIkeCryptoProfileConfig(ctx, body, paRequestId, tenantId, optional)
Create SaseIkeCryptoProfileConfig instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseIkeCryptoProfileConfigDtoPost**](SaseIkeCryptoProfileConfigDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseIkeCryptoProfileConfigApiCreateSaseIkeCryptoProfileConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIkeCryptoProfileConfigApiCreateSaseIkeCryptoProfileConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteSaseIkeCryptoProfileConfigByOid**
> DeleteResponseDtoCommon DeleteSaseIkeCryptoProfileConfigByOid(ctx, paRequestId, tenantId, oid, optional)
Delete SaseIkeCryptoProfileConfig instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIkeCryptoProfileConfigApiDeleteSaseIkeCryptoProfileConfigByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIkeCryptoProfileConfigApiDeleteSaseIkeCryptoProfileConfigByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindSaseIkeCryptoProfileConfigByOid**
> SaseIkeCryptoProfileConfigDtoGet FindSaseIkeCryptoProfileConfigByOid(ctx, paRequestId, tenantId, oid, optional)
Find SaseIkeCryptoProfileConfig instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIkeCryptoProfileConfigApiFindSaseIkeCryptoProfileConfigByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIkeCryptoProfileConfigApiFindSaseIkeCryptoProfileConfigByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**SaseIkeCryptoProfileConfigDtoGet**](SaseIkeCryptoProfileConfigDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListSaseIkeCryptoProfileConfig**
> SaseIkeCryptoProfileConfigDtoList ListSaseIkeCryptoProfileConfig(ctx, paRequestId, tenantId, optional)
List SaseIkeCryptoProfileConfig instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseIkeCryptoProfileConfigApiListSaseIkeCryptoProfileConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIkeCryptoProfileConfigApiListSaseIkeCryptoProfileConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**SaseIkeCryptoProfileConfigDtoList**](SaseIkeCryptoProfileConfigDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship62**
> RelgetResponseDtoCommon ListTenantRelationship62(ctx, paRequestId, tenantId, oid, optional)
List relationships of SaseIkeCryptoProfileConfig instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIkeCryptoProfileConfigApiListTenantRelationship62Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIkeCryptoProfileConfigApiListTenantRelationship62Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchSaseIkeCryptoProfileConfig**
> PostpatchResponseDtoCommon PatchSaseIkeCryptoProfileConfig(ctx, body, paRequestId, tenantId, oid, optional)
Patch SaseIkeCryptoProfileConfig instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseIkeCryptoProfileConfigDto**](SaseIkeCryptoProfileConfigDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIkeCryptoProfileConfigApiPatchSaseIkeCryptoProfileConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIkeCryptoProfileConfigApiPatchSaseIkeCryptoProfileConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateSaseIkeCryptoProfileConfigRelationship**
> RelpatchResponseDtoCommon UpdateSaseIkeCryptoProfileConfigRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from SaseIkeCryptoProfileConfig instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseIkeCryptoProfileConfigApiUpdateSaseIkeCryptoProfileConfigRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseIkeCryptoProfileConfigApiUpdateSaseIkeCryptoProfileConfigRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

