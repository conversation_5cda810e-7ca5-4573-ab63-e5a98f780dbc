# OkyoDeviceStatusDtoPost

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AlertMessage** | **string** |  | [optional] [default to null]
**AssignedMbn1Updated** | **int64** |  | [optional] [default to null]
**AssignedMbn2Updated** | **int64** |  | [optional] [default to null]
**AwsEnvironmentId** | **string** |  | [optional] [default to null]
**CdlTenantId** | **string** |  | [optional] [default to null]
**ClientIpPool** | **string** |  | [optional] [default to null]
**ConnectionEstablishedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ConnectionFailureRetryCount** | **int64** |  | [optional] [default to null]
**CreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**DeletedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**DeregistrationFailureRetryCount** | **int64** |  | [optional] [default to null]
**DeregistrationTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**FailureEvent** | **string** |  | [optional] [default to null]
**FailureVersion** | **int64** |  | [optional] [default to null]
**ImportedCreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDataflowProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDatasinkProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedUpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**LastConnected** | **string** |  | [optional] [default to null]
**LastDisconnected** | **string** |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**Oid** | **string** |  | [default to null]
**PaTenantId** | **string** |  | [optional] [default to null]
**PreviousState** | **string** |  | [optional] [default to null]
**PreviousStateDetail** | **string** |  | [optional] [default to null]
**RegistrationFailureRetryCount** | **int64** |  | [optional] [default to null]
**RegistrationTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**State** | **string** |  | [optional] [default to null]
**StateDetail** | **string** |  | [optional] [default to null]
**SubTenantId** | **string** |  | [default to null]
**TenantId** | **string** |  | [default to null]
**TenantServiceGroup** | **string** |  | [default to null]
**UpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Version** | **int64** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

