# BgpSessionConfigDtoPost

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AwsEnvironmentId** | **string** |  | [optional] [default to null]
**CdlTenantId** | **string** |  | [optional] [default to null]
**CreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**DeletedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**DoNotExportRoutes** | **string** |  | [optional] [default to null]
**EcmpEnabled** | **bool** |  | [default to null]
**EcmpFastExternalFailover** | **string** |  | [optional] [default to null]
**EcmpPeeringType** | **string** |  | [optional] [default to null]
**ImportedCreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDataflowProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDatasinkProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedUpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**LocalIpAddress** | **string** |  | [default to null]
**LocalIpv6Address** | **string** |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**Oid** | **string** |  | [default to null]
**OriginateDefaultRoute** | **string** |  | [optional] [default to null]
**PaTenantId** | **string** |  | [optional] [default to null]
**PeerAs** | **int64** |  | [default to null]
**PeerIpAddress** | **string** |  | [default to null]
**PeerIpv6Address** | **string** |  | [optional] [default to null]
**SameAsPrimary** | **string** |  | [optional] [default to null]
**Secret** | **string** |  | [optional] [default to null]
**SubTenantId** | **string** |  | [default to null]
**SummarizeMobileUserRoutes** | **string** |  | [optional] [default to null]
**TenantId** | **string** |  | [default to null]
**TenantServiceGroup** | **string** |  | [default to null]
**UpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Version** | **int64** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

