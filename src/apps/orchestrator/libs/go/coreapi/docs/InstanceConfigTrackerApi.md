# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateInstanceConfigTracker**](InstanceConfigTrackerApi.md#CreateInstanceConfigTracker) | **Post** /tenant/{tenant_id}/instance-config-tracker | Create InstanceConfigTracker instance
[**DeleteInstanceConfigTrackerByOid**](InstanceConfigTrackerApi.md#DeleteInstanceConfigTrackerByOid) | **Delete** /tenant/{tenant_id}/instance-config-tracker/{oid} | Delete InstanceConfigTracker instance by oid
[**FindInstanceConfigTrackerByOid**](InstanceConfigTrackerApi.md#FindInstanceConfigTrackerByOid) | **Get** /tenant/{tenant_id}/instance-config-tracker/{oid} | Find InstanceConfigTracker instance by oid
[**ListInstanceConfigTracker**](InstanceConfigTrackerApi.md#ListInstanceConfigTracker) | **Get** /tenant/{tenant_id}/instance-config-tracker | List InstanceConfigTracker instances of the tenant
[**ListTenantRelationship32**](InstanceConfigTrackerApi.md#ListTenantRelationship32) | **Get** /tenant/{tenant_id}/instance-config-tracker/{oid}/relationship | List relationships of InstanceConfigTracker instance
[**PatchInstanceConfigTracker**](InstanceConfigTrackerApi.md#PatchInstanceConfigTracker) | **Patch** /tenant/{tenant_id}/instance-config-tracker/{oid} | Patch InstanceConfigTracker instance by oid, if not found then attempt to create a new instance
[**UpdateInstanceConfigTrackerRelationship**](InstanceConfigTrackerApi.md#UpdateInstanceConfigTrackerRelationship) | **Patch** /tenant/{tenant_id}/instance-config-tracker/{oid}/relationship | Update relationships originated from InstanceConfigTracker instance

# **CreateInstanceConfigTracker**
> PostpatchResponseDtoCommon CreateInstanceConfigTracker(ctx, body, paRequestId, tenantId, optional)
Create InstanceConfigTracker instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**InstanceConfigTrackerDtoPost**](InstanceConfigTrackerDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***InstanceConfigTrackerApiCreateInstanceConfigTrackerOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a InstanceConfigTrackerApiCreateInstanceConfigTrackerOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteInstanceConfigTrackerByOid**
> DeleteResponseDtoCommon DeleteInstanceConfigTrackerByOid(ctx, paRequestId, tenantId, oid, optional)
Delete InstanceConfigTracker instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***InstanceConfigTrackerApiDeleteInstanceConfigTrackerByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a InstanceConfigTrackerApiDeleteInstanceConfigTrackerByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindInstanceConfigTrackerByOid**
> InstanceConfigTrackerDtoGet FindInstanceConfigTrackerByOid(ctx, paRequestId, tenantId, oid, optional)
Find InstanceConfigTracker instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***InstanceConfigTrackerApiFindInstanceConfigTrackerByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a InstanceConfigTrackerApiFindInstanceConfigTrackerByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**InstanceConfigTrackerDtoGet**](InstanceConfigTrackerDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListInstanceConfigTracker**
> InstanceConfigTrackerDtoList ListInstanceConfigTracker(ctx, paRequestId, tenantId, optional)
List InstanceConfigTracker instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***InstanceConfigTrackerApiListInstanceConfigTrackerOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a InstanceConfigTrackerApiListInstanceConfigTrackerOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**InstanceConfigTrackerDtoList**](InstanceConfigTrackerDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship32**
> RelgetResponseDtoCommon ListTenantRelationship32(ctx, paRequestId, tenantId, oid, optional)
List relationships of InstanceConfigTracker instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***InstanceConfigTrackerApiListTenantRelationship32Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a InstanceConfigTrackerApiListTenantRelationship32Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchInstanceConfigTracker**
> PostpatchResponseDtoCommon PatchInstanceConfigTracker(ctx, body, paRequestId, tenantId, oid, optional)
Patch InstanceConfigTracker instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**InstanceConfigTrackerDto**](InstanceConfigTrackerDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***InstanceConfigTrackerApiPatchInstanceConfigTrackerOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a InstanceConfigTrackerApiPatchInstanceConfigTrackerOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateInstanceConfigTrackerRelationship**
> RelpatchResponseDtoCommon UpdateInstanceConfigTrackerRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from InstanceConfigTracker instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***InstanceConfigTrackerApiUpdateInstanceConfigTrackerRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a InstanceConfigTrackerApiUpdateInstanceConfigTrackerRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

