# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateRegionEndpointInfo**](RegionEndpointInfoApi.md#CreateRegionEndpointInfo) | **Post** /region-endpoint-info | Create RegionEndpointInfo instance
[**DeleteRegionEndpointInfoByOid**](RegionEndpointInfoApi.md#DeleteRegionEndpointInfoByOid) | **Delete** /region-endpoint-info/{oid} | Delete RegionEndpointInfo instance by oid
[**FindRegionEndpointInfoByOid**](RegionEndpointInfoApi.md#FindRegionEndpointInfoByOid) | **Get** /region-endpoint-info/{oid} | Find RegionEndpointInfo instance by oid
[**ListRegionEndpointInfo**](RegionEndpointInfoApi.md#ListRegionEndpointInfo) | **Get** /region-endpoint-info | List RegionEndpointInfo instances
[**ListTenantRelationship52**](RegionEndpointInfoApi.md#ListTenantRelationship52) | **Get** /region-endpoint-info/{oid}/relationship | List relationships of RegionEndpointInfo instance
[**PatchRegionEndpointInfo**](RegionEndpointInfoApi.md#PatchRegionEndpointInfo) | **Patch** /region-endpoint-info/{oid} | Patch RegionEndpointInfo instance by oid, if not found then attempt to create a new instance

# **CreateRegionEndpointInfo**
> PostpatchResponseDtoCommon CreateRegionEndpointInfo(ctx, body, paRequestId, optional)
Create RegionEndpointInfo instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RegionEndpointInfoDtoPost**](RegionEndpointInfoDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
 **optional** | ***RegionEndpointInfoApiCreateRegionEndpointInfoOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RegionEndpointInfoApiCreateRegionEndpointInfoOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.**| b3 header for trace context propagation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteRegionEndpointInfoByOid**
> DeleteResponseDtoCommon DeleteRegionEndpointInfoByOid(ctx, paRequestId, oid, optional)
Delete RegionEndpointInfo instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RegionEndpointInfoApiDeleteRegionEndpointInfoByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RegionEndpointInfoApiDeleteRegionEndpointInfoByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindRegionEndpointInfoByOid**
> RegionEndpointInfoDtoGet FindRegionEndpointInfoByOid(ctx, paRequestId, oid, optional)
Find RegionEndpointInfo instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RegionEndpointInfoApiFindRegionEndpointInfoByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RegionEndpointInfoApiFindRegionEndpointInfoByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 

### Return type

[**RegionEndpointInfoDtoGet**](RegionEndpointInfoDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListRegionEndpointInfo**
> RegionEndpointInfoDtoList ListRegionEndpointInfo(ctx, paRequestId, optional)
List RegionEndpointInfo instances

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
 **optional** | ***RegionEndpointInfoApiListRegionEndpointInfoOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RegionEndpointInfoApiListRegionEndpointInfoOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------

 **b3** | **optional.String**| b3 header for trace context propagation | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RegionEndpointInfoDtoList**](RegionEndpointInfoDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship52**
> RelgetResponseDtoCommon ListTenantRelationship52(ctx, paRequestId, oid, optional)
List relationships of RegionEndpointInfo instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RegionEndpointInfoApiListTenantRelationship52Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RegionEndpointInfoApiListTenantRelationship52Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchRegionEndpointInfo**
> PostpatchResponseDtoCommon PatchRegionEndpointInfo(ctx, body, paRequestId, oid, optional)
Patch RegionEndpointInfo instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RegionEndpointInfoDto**](RegionEndpointInfoDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RegionEndpointInfoApiPatchRegionEndpointInfoOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RegionEndpointInfoApiPatchRegionEndpointInfoOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

