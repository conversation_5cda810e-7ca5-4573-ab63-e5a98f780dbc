# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateAggrBwConfiguration**](AggrBwConfigurationApi.md#CreateAggrBwConfiguration) | **Post** /tenant/{tenant_id}/aggr-bw-configuration | Create AggrBwConfiguration instance
[**DeleteAggrBwConfigurationByOid**](AggrBwConfigurationApi.md#DeleteAggrBwConfigurationByOid) | **Delete** /tenant/{tenant_id}/aggr-bw-configuration/{oid} | Delete AggrBwConfiguration instance by oid
[**FindAggrBwConfigurationByOid**](AggrBwConfigurationApi.md#FindAggrBwConfigurationByOid) | **Get** /tenant/{tenant_id}/aggr-bw-configuration/{oid} | Find AggrBwConfiguration instance by oid
[**ListAggrBwConfiguration**](AggrBwConfigurationApi.md#ListAggrBwConfiguration) | **Get** /tenant/{tenant_id}/aggr-bw-configuration | List AggrBwConfiguration instances of the tenant
[**ListTenantRelationship**](AggrBwConfigurationApi.md#ListTenantRelationship) | **Get** /tenant/{tenant_id}/aggr-bw-configuration/{oid}/relationship | List relationships of AggrBwConfiguration instance
[**PatchAggrBwConfiguration**](AggrBwConfigurationApi.md#PatchAggrBwConfiguration) | **Patch** /tenant/{tenant_id}/aggr-bw-configuration/{oid} | Patch AggrBwConfiguration instance by oid, if not found then attempt to create a new instance
[**UpdateAggrBwConfigurationRelationship**](AggrBwConfigurationApi.md#UpdateAggrBwConfigurationRelationship) | **Patch** /tenant/{tenant_id}/aggr-bw-configuration/{oid}/relationship | Update relationships originated from AggrBwConfiguration instance

# **CreateAggrBwConfiguration**
> PostpatchResponseDtoCommon CreateAggrBwConfiguration(ctx, body, paRequestId, tenantId, optional)
Create AggrBwConfiguration instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AggrBwConfigurationDtoPost**](AggrBwConfigurationDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***AggrBwConfigurationApiCreateAggrBwConfigurationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggrBwConfigurationApiCreateAggrBwConfigurationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteAggrBwConfigurationByOid**
> DeleteResponseDtoCommon DeleteAggrBwConfigurationByOid(ctx, paRequestId, tenantId, oid, optional)
Delete AggrBwConfiguration instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***AggrBwConfigurationApiDeleteAggrBwConfigurationByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggrBwConfigurationApiDeleteAggrBwConfigurationByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindAggrBwConfigurationByOid**
> AggrBwConfigurationDtoGet FindAggrBwConfigurationByOid(ctx, paRequestId, tenantId, oid, optional)
Find AggrBwConfiguration instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***AggrBwConfigurationApiFindAggrBwConfigurationByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggrBwConfigurationApiFindAggrBwConfigurationByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**AggrBwConfigurationDtoGet**](AggrBwConfigurationDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListAggrBwConfiguration**
> AggrBwConfigurationDtoList ListAggrBwConfiguration(ctx, paRequestId, tenantId, optional)
List AggrBwConfiguration instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***AggrBwConfigurationApiListAggrBwConfigurationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggrBwConfigurationApiListAggrBwConfigurationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**AggrBwConfigurationDtoList**](AggrBwConfigurationDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship**
> RelgetResponseDtoCommon ListTenantRelationship(ctx, paRequestId, tenantId, oid, optional)
List relationships of AggrBwConfiguration instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***AggrBwConfigurationApiListTenantRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggrBwConfigurationApiListTenantRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchAggrBwConfiguration**
> PostpatchResponseDtoCommon PatchAggrBwConfiguration(ctx, body, paRequestId, tenantId, oid, optional)
Patch AggrBwConfiguration instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AggrBwConfigurationDto**](AggrBwConfigurationDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***AggrBwConfigurationApiPatchAggrBwConfigurationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggrBwConfigurationApiPatchAggrBwConfigurationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateAggrBwConfigurationRelationship**
> RelpatchResponseDtoCommon UpdateAggrBwConfigurationRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from AggrBwConfiguration instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***AggrBwConfigurationApiUpdateAggrBwConfigurationRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggrBwConfigurationApiUpdateAggrBwConfigurationRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

