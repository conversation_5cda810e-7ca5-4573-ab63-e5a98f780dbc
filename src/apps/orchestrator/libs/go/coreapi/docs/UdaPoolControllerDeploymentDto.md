# UdaPoolControllerDeploymentDto

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AwsEnvironmentId** | **string** |  | [optional] [default to null]
**CertInfo** | **string** |  | [optional] [default to null]
**CloudComputeRegionId** | **string** |  | [optional] [default to null]
**CloudComputeRegionName** | **string** |  | [optional] [default to null]
**CloudProvider** | **string** |  | [optional] [default to null]
**CreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**DeletedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**FirewallId** | **string** |  | [optional] [default to null]
**HealthCheckPort** | **int64** |  | [optional] [default to null]
**HelmChartInfo** | **string** |  | [optional] [default to null]
**ImageTag** | **string** |  | [optional] [default to null]
**ImportedCreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDataflowProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDatasinkProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedUpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**KubernetesClusterId** | **string** |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**Oid** | **string** |  | [optional] [default to null]
**PcServiceId** | **string** |  | [optional] [default to null]
**ProjectId** | **string** |  | [optional] [default to null]
**ReplicaInfo** | **int64** |  | [optional] [default to null]
**ServiceAccountInfo** | **string** |  | [optional] [default to null]
**ServiceName** | **string** |  | [optional] [default to null]
**ServiceNamespace** | **string** |  | [optional] [default to null]
**ServicePort** | **int64** |  | [optional] [default to null]
**SubnetId** | **string** |  | [optional] [default to null]
**TopicSubscribe** | **string** |  | [optional] [default to null]
**UpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Version** | **int64** |  | [optional] [default to null]
**VpcId** | **string** |  | [optional] [default to null]
**Zone** | **string** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

