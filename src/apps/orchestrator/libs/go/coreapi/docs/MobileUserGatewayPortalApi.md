# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateMobileUserGatewayPortal**](MobileUserGatewayPortalApi.md#CreateMobileUserGatewayPortal) | **Post** /tenant/{tenant_id}/mobile-user-gateway-portal | Create MobileUserGatewayPortal instance
[**DeleteMobileUserGatewayPortalByOid**](MobileUserGatewayPortalApi.md#DeleteMobileUserGatewayPortalByOid) | **Delete** /tenant/{tenant_id}/mobile-user-gateway-portal/{oid} | Delete MobileUserGatewayPortal instance by oid
[**FindMobileUserGatewayPortalByOid**](MobileUserGatewayPortalApi.md#FindMobileUserGatewayPortalByOid) | **Get** /tenant/{tenant_id}/mobile-user-gateway-portal/{oid} | Find MobileUserGatewayPortal instance by oid
[**ListMobileUserGatewayPortal**](MobileUserGatewayPortalApi.md#ListMobileUserGatewayPortal) | **Get** /tenant/{tenant_id}/mobile-user-gateway-portal | List MobileUserGatewayPortal instances of the tenant
[**ListTenantRelationship40**](MobileUserGatewayPortalApi.md#ListTenantRelationship40) | **Get** /tenant/{tenant_id}/mobile-user-gateway-portal/{oid}/relationship | List relationships of MobileUserGatewayPortal instance
[**PatchMobileUserGatewayPortal**](MobileUserGatewayPortalApi.md#PatchMobileUserGatewayPortal) | **Patch** /tenant/{tenant_id}/mobile-user-gateway-portal/{oid} | Patch MobileUserGatewayPortal instance by oid, if not found then attempt to create a new instance
[**UpdateMobileUserGatewayPortalRelationship**](MobileUserGatewayPortalApi.md#UpdateMobileUserGatewayPortalRelationship) | **Patch** /tenant/{tenant_id}/mobile-user-gateway-portal/{oid}/relationship | Update relationships originated from MobileUserGatewayPortal instance

# **CreateMobileUserGatewayPortal**
> PostpatchResponseDtoCommon CreateMobileUserGatewayPortal(ctx, body, paRequestId, tenantId, optional)
Create MobileUserGatewayPortal instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**MobileUserGatewayPortalDtoPost**](MobileUserGatewayPortalDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***MobileUserGatewayPortalApiCreateMobileUserGatewayPortalOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a MobileUserGatewayPortalApiCreateMobileUserGatewayPortalOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteMobileUserGatewayPortalByOid**
> DeleteResponseDtoCommon DeleteMobileUserGatewayPortalByOid(ctx, paRequestId, tenantId, oid, optional)
Delete MobileUserGatewayPortal instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***MobileUserGatewayPortalApiDeleteMobileUserGatewayPortalByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a MobileUserGatewayPortalApiDeleteMobileUserGatewayPortalByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindMobileUserGatewayPortalByOid**
> MobileUserGatewayPortalDtoGet FindMobileUserGatewayPortalByOid(ctx, paRequestId, tenantId, oid, optional)
Find MobileUserGatewayPortal instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***MobileUserGatewayPortalApiFindMobileUserGatewayPortalByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a MobileUserGatewayPortalApiFindMobileUserGatewayPortalByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**MobileUserGatewayPortalDtoGet**](MobileUserGatewayPortalDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListMobileUserGatewayPortal**
> MobileUserGatewayPortalDtoList ListMobileUserGatewayPortal(ctx, paRequestId, tenantId, optional)
List MobileUserGatewayPortal instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***MobileUserGatewayPortalApiListMobileUserGatewayPortalOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a MobileUserGatewayPortalApiListMobileUserGatewayPortalOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**MobileUserGatewayPortalDtoList**](MobileUserGatewayPortalDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship40**
> RelgetResponseDtoCommon ListTenantRelationship40(ctx, paRequestId, tenantId, oid, optional)
List relationships of MobileUserGatewayPortal instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***MobileUserGatewayPortalApiListTenantRelationship40Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a MobileUserGatewayPortalApiListTenantRelationship40Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchMobileUserGatewayPortal**
> PostpatchResponseDtoCommon PatchMobileUserGatewayPortal(ctx, body, paRequestId, tenantId, oid, optional)
Patch MobileUserGatewayPortal instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**MobileUserGatewayPortalDto**](MobileUserGatewayPortalDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***MobileUserGatewayPortalApiPatchMobileUserGatewayPortalOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a MobileUserGatewayPortalApiPatchMobileUserGatewayPortalOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateMobileUserGatewayPortalRelationship**
> RelpatchResponseDtoCommon UpdateMobileUserGatewayPortalRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from MobileUserGatewayPortal instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***MobileUserGatewayPortalApiUpdateMobileUserGatewayPortalRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a MobileUserGatewayPortalApiUpdateMobileUserGatewayPortalRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

