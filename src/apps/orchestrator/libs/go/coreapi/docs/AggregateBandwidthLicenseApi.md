# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateAggregateBandwidthLicense**](AggregateBandwidthLicenseApi.md#CreateAggregateBandwidthLicense) | **Post** /tenant/{tenant_id}/aggregate-bandwidth-license | Create AggregateBandwidthLicense instance
[**DeleteAggregateBandwidthLicenseByOid**](AggregateBandwidthLicenseApi.md#DeleteAggregateBandwidthLicenseByOid) | **Delete** /tenant/{tenant_id}/aggregate-bandwidth-license/{oid} | Delete AggregateBandwidthLicense instance by oid
[**FindAggregateBandwidthLicenseByOid**](AggregateBandwidthLicenseApi.md#FindAggregateBandwidthLicenseByOid) | **Get** /tenant/{tenant_id}/aggregate-bandwidth-license/{oid} | Find AggregateBandwidthLicense instance by oid
[**ListAggregateBandwidthLicense**](AggregateBandwidthLicenseApi.md#ListAggregateBandwidthLicense) | **Get** /tenant/{tenant_id}/aggregate-bandwidth-license | List AggregateBandwidthLicense instances of the tenant
[**ListTenantRelationship2**](AggregateBandwidthLicenseApi.md#ListTenantRelationship2) | **Get** /tenant/{tenant_id}/aggregate-bandwidth-license/{oid}/relationship | List relationships of AggregateBandwidthLicense instance
[**PatchAggregateBandwidthLicense**](AggregateBandwidthLicenseApi.md#PatchAggregateBandwidthLicense) | **Patch** /tenant/{tenant_id}/aggregate-bandwidth-license/{oid} | Patch AggregateBandwidthLicense instance by oid, if not found then attempt to create a new instance
[**UpdateAggregateBandwidthLicenseRelationship**](AggregateBandwidthLicenseApi.md#UpdateAggregateBandwidthLicenseRelationship) | **Patch** /tenant/{tenant_id}/aggregate-bandwidth-license/{oid}/relationship | Update relationships originated from AggregateBandwidthLicense instance

# **CreateAggregateBandwidthLicense**
> PostpatchResponseDtoCommon CreateAggregateBandwidthLicense(ctx, body, paRequestId, tenantId, optional)
Create AggregateBandwidthLicense instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AggregateBandwidthLicenseDtoPost**](AggregateBandwidthLicenseDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***AggregateBandwidthLicenseApiCreateAggregateBandwidthLicenseOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggregateBandwidthLicenseApiCreateAggregateBandwidthLicenseOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteAggregateBandwidthLicenseByOid**
> DeleteResponseDtoCommon DeleteAggregateBandwidthLicenseByOid(ctx, paRequestId, tenantId, oid, optional)
Delete AggregateBandwidthLicense instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***AggregateBandwidthLicenseApiDeleteAggregateBandwidthLicenseByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggregateBandwidthLicenseApiDeleteAggregateBandwidthLicenseByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindAggregateBandwidthLicenseByOid**
> AggregateBandwidthLicenseDtoGet FindAggregateBandwidthLicenseByOid(ctx, paRequestId, tenantId, oid, optional)
Find AggregateBandwidthLicense instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***AggregateBandwidthLicenseApiFindAggregateBandwidthLicenseByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggregateBandwidthLicenseApiFindAggregateBandwidthLicenseByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**AggregateBandwidthLicenseDtoGet**](AggregateBandwidthLicenseDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListAggregateBandwidthLicense**
> AggregateBandwidthLicenseDtoList ListAggregateBandwidthLicense(ctx, paRequestId, tenantId, optional)
List AggregateBandwidthLicense instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***AggregateBandwidthLicenseApiListAggregateBandwidthLicenseOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggregateBandwidthLicenseApiListAggregateBandwidthLicenseOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**AggregateBandwidthLicenseDtoList**](AggregateBandwidthLicenseDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship2**
> RelgetResponseDtoCommon ListTenantRelationship2(ctx, paRequestId, tenantId, oid, optional)
List relationships of AggregateBandwidthLicense instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***AggregateBandwidthLicenseApiListTenantRelationship2Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggregateBandwidthLicenseApiListTenantRelationship2Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchAggregateBandwidthLicense**
> PostpatchResponseDtoCommon PatchAggregateBandwidthLicense(ctx, body, paRequestId, tenantId, oid, optional)
Patch AggregateBandwidthLicense instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AggregateBandwidthLicenseDto**](AggregateBandwidthLicenseDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***AggregateBandwidthLicenseApiPatchAggregateBandwidthLicenseOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggregateBandwidthLicenseApiPatchAggregateBandwidthLicenseOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateAggregateBandwidthLicenseRelationship**
> RelpatchResponseDtoCommon UpdateAggregateBandwidthLicenseRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from AggregateBandwidthLicense instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***AggregateBandwidthLicenseApiUpdateAggregateBandwidthLicenseRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AggregateBandwidthLicenseApiUpdateAggregateBandwidthLicenseRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

