# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateSaseLocalInterfaceIpConfig**](SaseLocalInterfaceIpConfigApi.md#CreateSaseLocalInterfaceIpConfig) | **Post** /tenant/{tenant_id}/sase-local-interface-ip-config | Create SaseLocalInterfaceIpConfig instance
[**DeleteSaseLocalInterfaceIpConfigByOid**](SaseLocalInterfaceIpConfigApi.md#DeleteSaseLocalInterfaceIpConfigByOid) | **Delete** /tenant/{tenant_id}/sase-local-interface-ip-config/{oid} | Delete SaseLocalInterfaceIpConfig instance by oid
[**FindSaseLocalInterfaceIpConfigByOid**](SaseLocalInterfaceIpConfigApi.md#FindSaseLocalInterfaceIpConfigByOid) | **Get** /tenant/{tenant_id}/sase-local-interface-ip-config/{oid} | Find SaseLocalInterfaceIpConfig instance by oid
[**ListSaseLocalInterfaceIpConfig**](SaseLocalInterfaceIpConfigApi.md#ListSaseLocalInterfaceIpConfig) | **Get** /tenant/{tenant_id}/sase-local-interface-ip-config | List SaseLocalInterfaceIpConfig instances of the tenant
[**ListTenantRelationship65**](SaseLocalInterfaceIpConfigApi.md#ListTenantRelationship65) | **Get** /tenant/{tenant_id}/sase-local-interface-ip-config/{oid}/relationship | List relationships of SaseLocalInterfaceIpConfig instance
[**PatchSaseLocalInterfaceIpConfig**](SaseLocalInterfaceIpConfigApi.md#PatchSaseLocalInterfaceIpConfig) | **Patch** /tenant/{tenant_id}/sase-local-interface-ip-config/{oid} | Patch SaseLocalInterfaceIpConfig instance by oid, if not found then attempt to create a new instance
[**UpdateSaseLocalInterfaceIpConfigRelationship**](SaseLocalInterfaceIpConfigApi.md#UpdateSaseLocalInterfaceIpConfigRelationship) | **Patch** /tenant/{tenant_id}/sase-local-interface-ip-config/{oid}/relationship | Update relationships originated from SaseLocalInterfaceIpConfig instance

# **CreateSaseLocalInterfaceIpConfig**
> PostpatchResponseDtoCommon CreateSaseLocalInterfaceIpConfig(ctx, body, paRequestId, tenantId, optional)
Create SaseLocalInterfaceIpConfig instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseLocalInterfaceIpConfigDtoPost**](SaseLocalInterfaceIpConfigDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseLocalInterfaceIpConfigApiCreateSaseLocalInterfaceIpConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseLocalInterfaceIpConfigApiCreateSaseLocalInterfaceIpConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteSaseLocalInterfaceIpConfigByOid**
> DeleteResponseDtoCommon DeleteSaseLocalInterfaceIpConfigByOid(ctx, paRequestId, tenantId, oid, optional)
Delete SaseLocalInterfaceIpConfig instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseLocalInterfaceIpConfigApiDeleteSaseLocalInterfaceIpConfigByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseLocalInterfaceIpConfigApiDeleteSaseLocalInterfaceIpConfigByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindSaseLocalInterfaceIpConfigByOid**
> SaseLocalInterfaceIpConfigDtoGet FindSaseLocalInterfaceIpConfigByOid(ctx, paRequestId, tenantId, oid, optional)
Find SaseLocalInterfaceIpConfig instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseLocalInterfaceIpConfigApiFindSaseLocalInterfaceIpConfigByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseLocalInterfaceIpConfigApiFindSaseLocalInterfaceIpConfigByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**SaseLocalInterfaceIpConfigDtoGet**](SaseLocalInterfaceIpConfigDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListSaseLocalInterfaceIpConfig**
> SaseLocalInterfaceIpConfigDtoList ListSaseLocalInterfaceIpConfig(ctx, paRequestId, tenantId, optional)
List SaseLocalInterfaceIpConfig instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseLocalInterfaceIpConfigApiListSaseLocalInterfaceIpConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseLocalInterfaceIpConfigApiListSaseLocalInterfaceIpConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**SaseLocalInterfaceIpConfigDtoList**](SaseLocalInterfaceIpConfigDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship65**
> RelgetResponseDtoCommon ListTenantRelationship65(ctx, paRequestId, tenantId, oid, optional)
List relationships of SaseLocalInterfaceIpConfig instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseLocalInterfaceIpConfigApiListTenantRelationship65Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseLocalInterfaceIpConfigApiListTenantRelationship65Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchSaseLocalInterfaceIpConfig**
> PostpatchResponseDtoCommon PatchSaseLocalInterfaceIpConfig(ctx, body, paRequestId, tenantId, oid, optional)
Patch SaseLocalInterfaceIpConfig instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseLocalInterfaceIpConfigDto**](SaseLocalInterfaceIpConfigDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseLocalInterfaceIpConfigApiPatchSaseLocalInterfaceIpConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseLocalInterfaceIpConfigApiPatchSaseLocalInterfaceIpConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateSaseLocalInterfaceIpConfigRelationship**
> RelpatchResponseDtoCommon UpdateSaseLocalInterfaceIpConfigRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from SaseLocalInterfaceIpConfig instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseLocalInterfaceIpConfigApiUpdateSaseLocalInterfaceIpConfigRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseLocalInterfaceIpConfigApiUpdateSaseLocalInterfaceIpConfigRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

