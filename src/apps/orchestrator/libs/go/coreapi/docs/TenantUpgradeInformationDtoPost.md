# TenantUpgradeInformationDtoPost

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AwsEnvironmentId** | **string** |  | [optional] [default to null]
**BannerStage** | **string** |  | [optional] [default to null]
**BannerStartTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**BatchId** | **string** |  | [optional] [default to null]
**BuildNumber** | **int32** |  | [optional] [default to null]
**CdlTenantId** | **string** |  | [optional] [default to null]
**ChosenLocations** | **string** |  | [optional] [default to null]
**CompletedLocations** | **string** |  | [optional] [default to null]
**ComputeTimezone** | **string** |  | [optional] [default to null]
**CreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**DeletedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Dryrun** | **bool** |  | [optional] [default to null]
**Environment** | **string** |  | [optional] [default to null]
**ErrorInformation** | **string** |  | [optional] [default to null]
**ImportedCreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDataflowProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDatasinkProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedUpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**Oid** | **string** |  | [default to null]
**OnboardedLocations** | **string** |  | [optional] [default to null]
**PaTenantId** | **string** |  | [optional] [default to null]
**Phase** | **string** |  | [optional] [default to null]
**SoftwareVersion** | **string** |  | [optional] [default to null]
**Status** | **string** |  | [optional] [default to null]
**SubTenantId** | **string** |  | [default to null]
**TenantId** | **string** |  | [default to null]
**TenantServiceGroup** | **string** |  | [default to null]
**TimeWindow** | **string** |  | [optional] [default to null]
**Type_** | **string** |  | [optional] [default to null]
**UpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**UpgradeDate** | **string** |  | [optional] [default to null]
**UpgradeTimeUtc** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Userid** | **string** |  | [optional] [default to null]
**Version** | **int64** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

