# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateRegionUpgradeInformation**](RegionUpgradeInformationApi.md#CreateRegionUpgradeInformation) | **Post** /region-upgrade-information | Create RegionUpgradeInformation instance
[**DeleteRegionUpgradeInformationByOid**](RegionUpgradeInformationApi.md#DeleteRegionUpgradeInformationByOid) | **Delete** /region-upgrade-information/{oid} | Delete RegionUpgradeInformation instance by oid
[**FindRegionUpgradeInformationByOid**](RegionUpgradeInformationApi.md#FindRegionUpgradeInformationByOid) | **Get** /region-upgrade-information/{oid} | Find RegionUpgradeInformation instance by oid
[**ListRegionUpgradeInformation**](RegionUpgradeInformationApi.md#ListRegionUpgradeInformation) | **Get** /region-upgrade-information | List RegionUpgradeInformation instances
[**ListTenantRelationship53**](RegionUpgradeInformationApi.md#ListTenantRelationship53) | **Get** /region-upgrade-information/{oid}/relationship | List relationships of RegionUpgradeInformation instance
[**PatchRegionUpgradeInformation**](RegionUpgradeInformationApi.md#PatchRegionUpgradeInformation) | **Patch** /region-upgrade-information/{oid} | Patch RegionUpgradeInformation instance by oid, if not found then attempt to create a new instance
[**UpdateRegionUpgradeInformationRelationship**](RegionUpgradeInformationApi.md#UpdateRegionUpgradeInformationRelationship) | **Patch** /region-upgrade-information/{oid}/relationship | Update relationships originated from RegionUpgradeInformation instance

# **CreateRegionUpgradeInformation**
> PostpatchResponseDtoCommon CreateRegionUpgradeInformation(ctx, body, paRequestId, optional)
Create RegionUpgradeInformation instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RegionUpgradeInformationDtoPost**](RegionUpgradeInformationDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
 **optional** | ***RegionUpgradeInformationApiCreateRegionUpgradeInformationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RegionUpgradeInformationApiCreateRegionUpgradeInformationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.**| b3 header for trace context propagation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteRegionUpgradeInformationByOid**
> DeleteResponseDtoCommon DeleteRegionUpgradeInformationByOid(ctx, paRequestId, oid, optional)
Delete RegionUpgradeInformation instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RegionUpgradeInformationApiDeleteRegionUpgradeInformationByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RegionUpgradeInformationApiDeleteRegionUpgradeInformationByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindRegionUpgradeInformationByOid**
> RegionUpgradeInformationDtoGet FindRegionUpgradeInformationByOid(ctx, paRequestId, oid, optional)
Find RegionUpgradeInformation instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RegionUpgradeInformationApiFindRegionUpgradeInformationByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RegionUpgradeInformationApiFindRegionUpgradeInformationByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 

### Return type

[**RegionUpgradeInformationDtoGet**](RegionUpgradeInformationDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListRegionUpgradeInformation**
> RegionUpgradeInformationDtoList ListRegionUpgradeInformation(ctx, paRequestId, optional)
List RegionUpgradeInformation instances

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
 **optional** | ***RegionUpgradeInformationApiListRegionUpgradeInformationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RegionUpgradeInformationApiListRegionUpgradeInformationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------

 **b3** | **optional.String**| b3 header for trace context propagation | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RegionUpgradeInformationDtoList**](RegionUpgradeInformationDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship53**
> RelgetResponseDtoCommon ListTenantRelationship53(ctx, paRequestId, oid, optional)
List relationships of RegionUpgradeInformation instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RegionUpgradeInformationApiListTenantRelationship53Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RegionUpgradeInformationApiListTenantRelationship53Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchRegionUpgradeInformation**
> PostpatchResponseDtoCommon PatchRegionUpgradeInformation(ctx, body, paRequestId, oid, optional)
Patch RegionUpgradeInformation instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RegionUpgradeInformationDto**](RegionUpgradeInformationDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RegionUpgradeInformationApiPatchRegionUpgradeInformationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RegionUpgradeInformationApiPatchRegionUpgradeInformationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateRegionUpgradeInformationRelationship**
> RelpatchResponseDtoCommon UpdateRegionUpgradeInformationRelationship(ctx, body, paRequestId, oid, optional)
Update relationships originated from RegionUpgradeInformation instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RegionUpgradeInformationApiUpdateRegionUpgradeInformationRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RegionUpgradeInformationApiUpdateRegionUpgradeInformationRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

