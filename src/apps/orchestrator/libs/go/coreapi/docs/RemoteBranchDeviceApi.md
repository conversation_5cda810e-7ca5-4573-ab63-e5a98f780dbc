# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateRemoteBranchDevice**](RemoteBranchDeviceApi.md#CreateRemoteBranchDevice) | **Post** /tenant/{tenant_id}/remote-branch-device | Create RemoteBranchDevice instance
[**DeleteRemoteBranchDeviceByOid**](RemoteBranchDeviceApi.md#DeleteRemoteBranchDeviceByOid) | **Delete** /tenant/{tenant_id}/remote-branch-device/{oid} | Delete RemoteBranchDevice instance by oid
[**FindRemoteBranchDeviceByOid**](RemoteBranchDeviceApi.md#FindRemoteBranchDeviceByOid) | **Get** /tenant/{tenant_id}/remote-branch-device/{oid} | Find RemoteBranchDevice instance by oid
[**ListRemoteBranchDevice**](RemoteBranchDeviceApi.md#ListRemoteBranchDevice) | **Get** /tenant/{tenant_id}/remote-branch-device | List RemoteBranchDevice instances of the tenant
[**ListTenantRelationship55**](RemoteBranchDeviceApi.md#ListTenantRelationship55) | **Get** /tenant/{tenant_id}/remote-branch-device/{oid}/relationship | List relationships of RemoteBranchDevice instance
[**PatchRemoteBranchDevice**](RemoteBranchDeviceApi.md#PatchRemoteBranchDevice) | **Patch** /tenant/{tenant_id}/remote-branch-device/{oid} | Patch RemoteBranchDevice instance by oid, if not found then attempt to create a new instance
[**UpdateRemoteBranchDeviceRelationship**](RemoteBranchDeviceApi.md#UpdateRemoteBranchDeviceRelationship) | **Patch** /tenant/{tenant_id}/remote-branch-device/{oid}/relationship | Update relationships originated from RemoteBranchDevice instance

# **CreateRemoteBranchDevice**
> PostpatchResponseDtoCommon CreateRemoteBranchDevice(ctx, body, paRequestId, tenantId, optional)
Create RemoteBranchDevice instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RemoteBranchDeviceDtoPost**](RemoteBranchDeviceDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***RemoteBranchDeviceApiCreateRemoteBranchDeviceOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RemoteBranchDeviceApiCreateRemoteBranchDeviceOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteRemoteBranchDeviceByOid**
> DeleteResponseDtoCommon DeleteRemoteBranchDeviceByOid(ctx, paRequestId, tenantId, oid, optional)
Delete RemoteBranchDevice instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RemoteBranchDeviceApiDeleteRemoteBranchDeviceByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RemoteBranchDeviceApiDeleteRemoteBranchDeviceByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindRemoteBranchDeviceByOid**
> RemoteBranchDeviceDtoGet FindRemoteBranchDeviceByOid(ctx, paRequestId, tenantId, oid, optional)
Find RemoteBranchDevice instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RemoteBranchDeviceApiFindRemoteBranchDeviceByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RemoteBranchDeviceApiFindRemoteBranchDeviceByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RemoteBranchDeviceDtoGet**](RemoteBranchDeviceDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListRemoteBranchDevice**
> RemoteBranchDeviceDtoList ListRemoteBranchDevice(ctx, paRequestId, tenantId, optional)
List RemoteBranchDevice instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***RemoteBranchDeviceApiListRemoteBranchDeviceOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RemoteBranchDeviceApiListRemoteBranchDeviceOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RemoteBranchDeviceDtoList**](RemoteBranchDeviceDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship55**
> RelgetResponseDtoCommon ListTenantRelationship55(ctx, paRequestId, tenantId, oid, optional)
List relationships of RemoteBranchDevice instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RemoteBranchDeviceApiListTenantRelationship55Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RemoteBranchDeviceApiListTenantRelationship55Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchRemoteBranchDevice**
> PostpatchResponseDtoCommon PatchRemoteBranchDevice(ctx, body, paRequestId, tenantId, oid, optional)
Patch RemoteBranchDevice instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RemoteBranchDeviceDto**](RemoteBranchDeviceDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RemoteBranchDeviceApiPatchRemoteBranchDeviceOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RemoteBranchDeviceApiPatchRemoteBranchDeviceOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateRemoteBranchDeviceRelationship**
> RelpatchResponseDtoCommon UpdateRemoteBranchDeviceRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from RemoteBranchDevice instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RemoteBranchDeviceApiUpdateRemoteBranchDeviceRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RemoteBranchDeviceApiUpdateRemoteBranchDeviceRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

