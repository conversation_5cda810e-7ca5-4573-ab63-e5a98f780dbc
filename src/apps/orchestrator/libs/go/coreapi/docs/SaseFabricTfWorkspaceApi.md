# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateSaseFabricTfWorkspace**](SaseFabricTfWorkspaceApi.md#CreateSaseFabricTfWorkspace) | **Post** /tenant/{tenant_id}/sase-fabric-tf-workspace | Create SaseFabricTfWorkspace instance
[**DeleteSaseFabricTfWorkspaceByOid**](SaseFabricTfWorkspaceApi.md#DeleteSaseFabricTfWorkspaceByOid) | **Delete** /tenant/{tenant_id}/sase-fabric-tf-workspace/{oid} | Delete SaseFabricTfWorkspace instance by oid
[**FindSaseFabricTfWorkspaceByOid**](SaseFabricTfWorkspaceApi.md#FindSaseFabricTfWorkspaceByOid) | **Get** /tenant/{tenant_id}/sase-fabric-tf-workspace/{oid} | Find SaseFabricTfWorkspace instance by oid
[**ListSaseFabricTfWorkspace**](SaseFabricTfWorkspaceApi.md#ListSaseFabricTfWorkspace) | **Get** /tenant/{tenant_id}/sase-fabric-tf-workspace | List SaseFabricTfWorkspace instances of the tenant
[**ListTenantRelationship60**](SaseFabricTfWorkspaceApi.md#ListTenantRelationship60) | **Get** /tenant/{tenant_id}/sase-fabric-tf-workspace/{oid}/relationship | List relationships of SaseFabricTfWorkspace instance
[**PatchSaseFabricTfWorkspace**](SaseFabricTfWorkspaceApi.md#PatchSaseFabricTfWorkspace) | **Patch** /tenant/{tenant_id}/sase-fabric-tf-workspace/{oid} | Patch SaseFabricTfWorkspace instance by oid, if not found then attempt to create a new instance
[**UpdateSaseFabricTfWorkspaceRelationship**](SaseFabricTfWorkspaceApi.md#UpdateSaseFabricTfWorkspaceRelationship) | **Patch** /tenant/{tenant_id}/sase-fabric-tf-workspace/{oid}/relationship | Update relationships originated from SaseFabricTfWorkspace instance

# **CreateSaseFabricTfWorkspace**
> PostpatchResponseDtoCommon CreateSaseFabricTfWorkspace(ctx, body, paRequestId, tenantId, optional)
Create SaseFabricTfWorkspace instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseFabricTfWorkspaceDtoPost**](SaseFabricTfWorkspaceDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseFabricTfWorkspaceApiCreateSaseFabricTfWorkspaceOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseFabricTfWorkspaceApiCreateSaseFabricTfWorkspaceOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteSaseFabricTfWorkspaceByOid**
> DeleteResponseDtoCommon DeleteSaseFabricTfWorkspaceByOid(ctx, paRequestId, tenantId, oid, optional)
Delete SaseFabricTfWorkspace instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseFabricTfWorkspaceApiDeleteSaseFabricTfWorkspaceByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseFabricTfWorkspaceApiDeleteSaseFabricTfWorkspaceByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindSaseFabricTfWorkspaceByOid**
> SaseFabricTfWorkspaceDtoGet FindSaseFabricTfWorkspaceByOid(ctx, paRequestId, tenantId, oid, optional)
Find SaseFabricTfWorkspace instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseFabricTfWorkspaceApiFindSaseFabricTfWorkspaceByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseFabricTfWorkspaceApiFindSaseFabricTfWorkspaceByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**SaseFabricTfWorkspaceDtoGet**](SaseFabricTfWorkspaceDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListSaseFabricTfWorkspace**
> SaseFabricTfWorkspaceDtoList ListSaseFabricTfWorkspace(ctx, paRequestId, tenantId, optional)
List SaseFabricTfWorkspace instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseFabricTfWorkspaceApiListSaseFabricTfWorkspaceOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseFabricTfWorkspaceApiListSaseFabricTfWorkspaceOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**SaseFabricTfWorkspaceDtoList**](SaseFabricTfWorkspaceDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship60**
> RelgetResponseDtoCommon ListTenantRelationship60(ctx, paRequestId, tenantId, oid, optional)
List relationships of SaseFabricTfWorkspace instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseFabricTfWorkspaceApiListTenantRelationship60Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseFabricTfWorkspaceApiListTenantRelationship60Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchSaseFabricTfWorkspace**
> PostpatchResponseDtoCommon PatchSaseFabricTfWorkspace(ctx, body, paRequestId, tenantId, oid, optional)
Patch SaseFabricTfWorkspace instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseFabricTfWorkspaceDto**](SaseFabricTfWorkspaceDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseFabricTfWorkspaceApiPatchSaseFabricTfWorkspaceOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseFabricTfWorkspaceApiPatchSaseFabricTfWorkspaceOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateSaseFabricTfWorkspaceRelationship**
> RelpatchResponseDtoCommon UpdateSaseFabricTfWorkspaceRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from SaseFabricTfWorkspace instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseFabricTfWorkspaceApiUpdateSaseFabricTfWorkspaceRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseFabricTfWorkspaceApiUpdateSaseFabricTfWorkspaceRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

