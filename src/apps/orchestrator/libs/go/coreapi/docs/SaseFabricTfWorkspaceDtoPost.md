# SaseFabricTfWorkspaceDtoPost

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AwsEnvironmentId** | **string** |  | [optional] [default to null]
**CdlTenantId** | **string** |  | [optional] [default to null]
**CreatedAt** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**CreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**CurrentState** | **string** |  | [optional] [default to null]
**DeletedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedCreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDataflowProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDatasinkProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedUpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**LastQueriedAt** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**LastRetryHost** | **string** |  | [optional] [default to null]
**LastRetrySubmittedAt** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**LastRetryUpdatedAt** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**LastRunId** | **string** |  | [optional] [default to null]
**Metadata** | **string** |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**Oid** | **string** |  | [default to null]
**PaTenantId** | **string** |  | [optional] [default to null]
**RefreshStatus** | **bool** |  | [optional] [default to null]
**RegionId** | **string** |  | [optional] [default to null]
**RetryCount** | **int64** |  | [default to null]
**SaseAccessSharedVpcName** | **string** |  | [optional] [default to null]
**SaseAccessVni** | **int64** |  | [optional] [default to null]
**SaseFabricFeatureEnabled** | **bool** |  | [optional] [default to null]
**SaseFabricShardId** | **string** |  | [optional] [default to null]
**SdwanControllerUri** | **string** |  | [optional] [default to null]
**SdwanRegion** | **string** |  | [optional] [default to null]
**SdwanTenantId** | **string** |  | [optional] [default to null]
**SubTenantId** | **string** |  | [default to null]
**TenantId** | **string** |  | [default to null]
**TenantServiceGroup** | **string** |  | [default to null]
**UpdatedAt** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**UpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Version** | **int64** |  | [optional] [default to null]
**WorkspaceName** | **string** |  | [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

