# RemoteNetworkConfigurationDtoPost

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AwsEnvironmentId** | **string** |  | [optional] [default to null]
**BgpEnable** | **string** |  | [default to null]
**CdlTenantId** | **string** |  | [optional] [default to null]
**ConfigId** | **string** |  | [optional] [default to null]
**CreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**DeletedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**EcmpLoadBalancing** | **string** |  | [default to null]
**ImportedCreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDataflowProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDatasinkProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedUpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**IpsecTunnel** | **string** |  | [default to null]
**Name** | **string** |  | [optional] [default to null]
**Oid** | **string** |  | [default to null]
**PaTenantId** | **string** |  | [optional] [default to null]
**QosBranchShare** | **float64** |  | [optional] [default to null]
**QosEnabled** | **string** |  | [optional] [default to null]
**QosProfile** | **string** |  | [optional] [default to null]
**Region** | **string** |  | [optional] [default to null]
**SecondaryIpsecTunnel** | **string** |  | [optional] [default to null]
**SecondaryWanEnabled** | **string** |  | [default to null]
**SpnName** | **string** |  | [default to null]
**SubTenantId** | **string** |  | [default to null]
**Subnets** | **string** |  | [default to null]
**TenantId** | **string** |  | [default to null]
**TenantServiceGroup** | **string** |  | [default to null]
**UpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Version** | **int64** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

