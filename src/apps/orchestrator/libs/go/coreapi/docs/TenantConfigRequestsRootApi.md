# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateTenantConfigRequestsRoot**](TenantConfigRequestsRootApi.md#CreateTenantConfigRequestsRoot) | **Post** /tenant/{tenant_id}/tenant-config-requests-root | Create TenantConfigRequestsRoot instance
[**DeleteTenantConfigRequestsRootByOid**](TenantConfigRequestsRootApi.md#DeleteTenantConfigRequestsRootByOid) | **Delete** /tenant/{tenant_id}/tenant-config-requests-root/{oid} | Delete TenantConfigRequestsRoot instance by oid
[**FindTenantConfigRequestsRootByOid**](TenantConfigRequestsRootApi.md#FindTenantConfigRequestsRootByOid) | **Get** /tenant/{tenant_id}/tenant-config-requests-root/{oid} | Find TenantConfigRequestsRoot instance by oid
[**ListTenantConfigRequestsRoot**](TenantConfigRequestsRootApi.md#ListTenantConfigRequestsRoot) | **Get** /tenant/{tenant_id}/tenant-config-requests-root | List TenantConfigRequestsRoot instances of the tenant
[**ListTenantRelationship78**](TenantConfigRequestsRootApi.md#ListTenantRelationship78) | **Get** /tenant/{tenant_id}/tenant-config-requests-root/{oid}/relationship | List relationships of TenantConfigRequestsRoot instance
[**PatchTenantConfigRequestsRoot**](TenantConfigRequestsRootApi.md#PatchTenantConfigRequestsRoot) | **Patch** /tenant/{tenant_id}/tenant-config-requests-root/{oid} | Patch TenantConfigRequestsRoot instance by oid, if not found then attempt to create a new instance
[**UpdateTenantConfigRequestsRootRelationship**](TenantConfigRequestsRootApi.md#UpdateTenantConfigRequestsRootRelationship) | **Patch** /tenant/{tenant_id}/tenant-config-requests-root/{oid}/relationship | Update relationships originated from TenantConfigRequestsRoot instance

# **CreateTenantConfigRequestsRoot**
> PostpatchResponseDtoCommon CreateTenantConfigRequestsRoot(ctx, body, paRequestId, tenantId, optional)
Create TenantConfigRequestsRoot instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**TenantConfigRequestsRootDtoPost**](TenantConfigRequestsRootDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***TenantConfigRequestsRootApiCreateTenantConfigRequestsRootOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantConfigRequestsRootApiCreateTenantConfigRequestsRootOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteTenantConfigRequestsRootByOid**
> DeleteResponseDtoCommon DeleteTenantConfigRequestsRootByOid(ctx, paRequestId, tenantId, oid, optional)
Delete TenantConfigRequestsRoot instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantConfigRequestsRootApiDeleteTenantConfigRequestsRootByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantConfigRequestsRootApiDeleteTenantConfigRequestsRootByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindTenantConfigRequestsRootByOid**
> TenantConfigRequestsRootDtoGet FindTenantConfigRequestsRootByOid(ctx, paRequestId, tenantId, oid, optional)
Find TenantConfigRequestsRoot instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantConfigRequestsRootApiFindTenantConfigRequestsRootByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantConfigRequestsRootApiFindTenantConfigRequestsRootByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**TenantConfigRequestsRootDtoGet**](TenantConfigRequestsRootDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantConfigRequestsRoot**
> TenantConfigRequestsRootDtoList ListTenantConfigRequestsRoot(ctx, paRequestId, tenantId, optional)
List TenantConfigRequestsRoot instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***TenantConfigRequestsRootApiListTenantConfigRequestsRootOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantConfigRequestsRootApiListTenantConfigRequestsRootOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**TenantConfigRequestsRootDtoList**](TenantConfigRequestsRootDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship78**
> RelgetResponseDtoCommon ListTenantRelationship78(ctx, paRequestId, tenantId, oid, optional)
List relationships of TenantConfigRequestsRoot instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantConfigRequestsRootApiListTenantRelationship78Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantConfigRequestsRootApiListTenantRelationship78Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchTenantConfigRequestsRoot**
> PostpatchResponseDtoCommon PatchTenantConfigRequestsRoot(ctx, body, paRequestId, tenantId, oid, optional)
Patch TenantConfigRequestsRoot instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**TenantConfigRequestsRootDto**](TenantConfigRequestsRootDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantConfigRequestsRootApiPatchTenantConfigRequestsRootOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantConfigRequestsRootApiPatchTenantConfigRequestsRootOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateTenantConfigRequestsRootRelationship**
> RelpatchResponseDtoCommon UpdateTenantConfigRequestsRootRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from TenantConfigRequestsRoot instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantConfigRequestsRootApiUpdateTenantConfigRequestsRootRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantConfigRequestsRootApiUpdateTenantConfigRequestsRootRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

