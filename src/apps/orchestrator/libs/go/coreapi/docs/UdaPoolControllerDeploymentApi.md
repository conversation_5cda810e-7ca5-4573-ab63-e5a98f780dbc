# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateUdaPoolControllerDeployment**](UdaPoolControllerDeploymentApi.md#CreateUdaPoolControllerDeployment) | **Post** /uda-pool-controller-deployment | Create UdaPoolControllerDeployment instance
[**DeleteUdaPoolControllerDeploymentByOid**](UdaPoolControllerDeploymentApi.md#DeleteUdaPoolControllerDeploymentByOid) | **Delete** /uda-pool-controller-deployment/{oid} | Delete UdaPoolControllerDeployment instance by oid
[**FindUdaPoolControllerDeploymentByOid**](UdaPoolControllerDeploymentApi.md#FindUdaPoolControllerDeploymentByOid) | **Get** /uda-pool-controller-deployment/{oid} | Find UdaPoolControllerDeployment instance by oid
[**ListTenantRelationship94**](UdaPoolControllerDeploymentApi.md#ListTenantRelationship94) | **Get** /uda-pool-controller-deployment/{oid}/relationship | List relationships of UdaPoolControllerDeployment instance
[**ListUdaPoolControllerDeployment**](UdaPoolControllerDeploymentApi.md#ListUdaPoolControllerDeployment) | **Get** /uda-pool-controller-deployment | List UdaPoolControllerDeployment instances
[**PatchUdaPoolControllerDeployment**](UdaPoolControllerDeploymentApi.md#PatchUdaPoolControllerDeployment) | **Patch** /uda-pool-controller-deployment/{oid} | Patch UdaPoolControllerDeployment instance by oid, if not found then attempt to create a new instance

# **CreateUdaPoolControllerDeployment**
> PostpatchResponseDtoCommon CreateUdaPoolControllerDeployment(ctx, body, paRequestId, optional)
Create UdaPoolControllerDeployment instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**UdaPoolControllerDeploymentDtoPost**](UdaPoolControllerDeploymentDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
 **optional** | ***UdaPoolControllerDeploymentApiCreateUdaPoolControllerDeploymentOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaPoolControllerDeploymentApiCreateUdaPoolControllerDeploymentOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.**| b3 header for trace context propagation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteUdaPoolControllerDeploymentByOid**
> DeleteResponseDtoCommon DeleteUdaPoolControllerDeploymentByOid(ctx, paRequestId, oid, optional)
Delete UdaPoolControllerDeployment instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***UdaPoolControllerDeploymentApiDeleteUdaPoolControllerDeploymentByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaPoolControllerDeploymentApiDeleteUdaPoolControllerDeploymentByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindUdaPoolControllerDeploymentByOid**
> UdaPoolControllerDeploymentDtoGet FindUdaPoolControllerDeploymentByOid(ctx, paRequestId, oid, optional)
Find UdaPoolControllerDeployment instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***UdaPoolControllerDeploymentApiFindUdaPoolControllerDeploymentByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaPoolControllerDeploymentApiFindUdaPoolControllerDeploymentByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 

### Return type

[**UdaPoolControllerDeploymentDtoGet**](UdaPoolControllerDeploymentDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship94**
> RelgetResponseDtoCommon ListTenantRelationship94(ctx, paRequestId, oid, optional)
List relationships of UdaPoolControllerDeployment instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***UdaPoolControllerDeploymentApiListTenantRelationship94Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaPoolControllerDeploymentApiListTenantRelationship94Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListUdaPoolControllerDeployment**
> UdaPoolControllerDeploymentDtoList ListUdaPoolControllerDeployment(ctx, paRequestId, optional)
List UdaPoolControllerDeployment instances

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
 **optional** | ***UdaPoolControllerDeploymentApiListUdaPoolControllerDeploymentOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaPoolControllerDeploymentApiListUdaPoolControllerDeploymentOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------

 **b3** | **optional.String**| b3 header for trace context propagation | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**UdaPoolControllerDeploymentDtoList**](UdaPoolControllerDeploymentDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchUdaPoolControllerDeployment**
> PostpatchResponseDtoCommon PatchUdaPoolControllerDeployment(ctx, body, paRequestId, oid, optional)
Patch UdaPoolControllerDeployment instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**UdaPoolControllerDeploymentDto**](UdaPoolControllerDeploymentDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***UdaPoolControllerDeploymentApiPatchUdaPoolControllerDeploymentOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaPoolControllerDeploymentApiPatchUdaPoolControllerDeploymentOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

