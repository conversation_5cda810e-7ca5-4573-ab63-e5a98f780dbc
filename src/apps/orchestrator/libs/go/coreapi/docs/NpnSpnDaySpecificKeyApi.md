# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateNpnSpnDaySpecificKey**](NpnSpnDaySpecificKeyApi.md#CreateNpnSpnDaySpecificKey) | **Post** /tenant/{tenant_id}/npn-spn-day-specific-key | Create NpnSpnDaySpecificKey instance
[**DeleteNpnSpnDaySpecificKeyByOid**](NpnSpnDaySpecificKeyApi.md#DeleteNpnSpnDaySpecificKeyByOid) | **Delete** /tenant/{tenant_id}/npn-spn-day-specific-key/{oid} | Delete NpnSpnDaySpecificKey instance by oid
[**FindNpnSpnDaySpecificKeyByOid**](NpnSpnDaySpecificKeyApi.md#FindNpnSpnDaySpecificKeyByOid) | **Get** /tenant/{tenant_id}/npn-spn-day-specific-key/{oid} | Find NpnSpnDaySpecificKey instance by oid
[**ListNpnSpnDaySpecificKey**](NpnSpnDaySpecificKeyApi.md#ListNpnSpnDaySpecificKey) | **Get** /tenant/{tenant_id}/npn-spn-day-specific-key | List NpnSpnDaySpecificKey instances of the tenant
[**ListTenantRelationship43**](NpnSpnDaySpecificKeyApi.md#ListTenantRelationship43) | **Get** /tenant/{tenant_id}/npn-spn-day-specific-key/{oid}/relationship | List relationships of NpnSpnDaySpecificKey instance
[**PatchNpnSpnDaySpecificKey**](NpnSpnDaySpecificKeyApi.md#PatchNpnSpnDaySpecificKey) | **Patch** /tenant/{tenant_id}/npn-spn-day-specific-key/{oid} | Patch NpnSpnDaySpecificKey instance by oid, if not found then attempt to create a new instance

# **CreateNpnSpnDaySpecificKey**
> PostpatchResponseDtoCommon CreateNpnSpnDaySpecificKey(ctx, body, paRequestId, tenantId, optional)
Create NpnSpnDaySpecificKey instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**NpnSpnDaySpecificKeyDtoPost**](NpnSpnDaySpecificKeyDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***NpnSpnDaySpecificKeyApiCreateNpnSpnDaySpecificKeyOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a NpnSpnDaySpecificKeyApiCreateNpnSpnDaySpecificKeyOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteNpnSpnDaySpecificKeyByOid**
> DeleteResponseDtoCommon DeleteNpnSpnDaySpecificKeyByOid(ctx, paRequestId, tenantId, oid, optional)
Delete NpnSpnDaySpecificKey instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***NpnSpnDaySpecificKeyApiDeleteNpnSpnDaySpecificKeyByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a NpnSpnDaySpecificKeyApiDeleteNpnSpnDaySpecificKeyByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindNpnSpnDaySpecificKeyByOid**
> NpnSpnDaySpecificKeyDtoGet FindNpnSpnDaySpecificKeyByOid(ctx, paRequestId, tenantId, oid, optional)
Find NpnSpnDaySpecificKey instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***NpnSpnDaySpecificKeyApiFindNpnSpnDaySpecificKeyByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a NpnSpnDaySpecificKeyApiFindNpnSpnDaySpecificKeyByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**NpnSpnDaySpecificKeyDtoGet**](NpnSpnDaySpecificKeyDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListNpnSpnDaySpecificKey**
> NpnSpnDaySpecificKeyDtoList ListNpnSpnDaySpecificKey(ctx, paRequestId, tenantId, optional)
List NpnSpnDaySpecificKey instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***NpnSpnDaySpecificKeyApiListNpnSpnDaySpecificKeyOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a NpnSpnDaySpecificKeyApiListNpnSpnDaySpecificKeyOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**NpnSpnDaySpecificKeyDtoList**](NpnSpnDaySpecificKeyDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship43**
> RelgetResponseDtoCommon ListTenantRelationship43(ctx, paRequestId, tenantId, oid, optional)
List relationships of NpnSpnDaySpecificKey instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***NpnSpnDaySpecificKeyApiListTenantRelationship43Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a NpnSpnDaySpecificKeyApiListTenantRelationship43Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchNpnSpnDaySpecificKey**
> PostpatchResponseDtoCommon PatchNpnSpnDaySpecificKey(ctx, body, paRequestId, tenantId, oid, optional)
Patch NpnSpnDaySpecificKey instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**NpnSpnDaySpecificKeyDto**](NpnSpnDaySpecificKeyDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***NpnSpnDaySpecificKeyApiPatchNpnSpnDaySpecificKeyOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a NpnSpnDaySpecificKeyApiPatchNpnSpnDaySpecificKeyOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

