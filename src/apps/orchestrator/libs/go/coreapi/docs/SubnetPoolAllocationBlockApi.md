# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateSubnetPoolAllocationBlock**](SubnetPoolAllocationBlockApi.md#CreateSubnetPoolAllocationBlock) | **Post** /tenant/{tenant_id}/subnet-pool-allocation-block | Create SubnetPoolAllocationBlock instance
[**DeleteSubnetPoolAllocationBlockByOid**](SubnetPoolAllocationBlockApi.md#DeleteSubnetPoolAllocationBlockByOid) | **Delete** /tenant/{tenant_id}/subnet-pool-allocation-block/{oid} | Delete SubnetPoolAllocationBlock instance by oid
[**FindSubnetPoolAllocationBlockByOid**](SubnetPoolAllocationBlockApi.md#FindSubnetPoolAllocationBlockByOid) | **Get** /tenant/{tenant_id}/subnet-pool-allocation-block/{oid} | Find SubnetPoolAllocationBlock instance by oid
[**ListSubnetPoolAllocationBlock**](SubnetPoolAllocationBlockApi.md#ListSubnetPoolAllocationBlock) | **Get** /tenant/{tenant_id}/subnet-pool-allocation-block | List SubnetPoolAllocationBlock instances of the tenant
[**ListTenantRelationship76**](SubnetPoolAllocationBlockApi.md#ListTenantRelationship76) | **Get** /tenant/{tenant_id}/subnet-pool-allocation-block/{oid}/relationship | List relationships of SubnetPoolAllocationBlock instance
[**PatchSubnetPoolAllocationBlock**](SubnetPoolAllocationBlockApi.md#PatchSubnetPoolAllocationBlock) | **Patch** /tenant/{tenant_id}/subnet-pool-allocation-block/{oid} | Patch SubnetPoolAllocationBlock instance by oid, if not found then attempt to create a new instance
[**UpdateSubnetPoolAllocationBlockRelationship**](SubnetPoolAllocationBlockApi.md#UpdateSubnetPoolAllocationBlockRelationship) | **Patch** /tenant/{tenant_id}/subnet-pool-allocation-block/{oid}/relationship | Update relationships originated from SubnetPoolAllocationBlock instance

# **CreateSubnetPoolAllocationBlock**
> PostpatchResponseDtoCommon CreateSubnetPoolAllocationBlock(ctx, body, paRequestId, tenantId, optional)
Create SubnetPoolAllocationBlock instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SubnetPoolAllocationBlockDtoPost**](SubnetPoolAllocationBlockDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SubnetPoolAllocationBlockApiCreateSubnetPoolAllocationBlockOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SubnetPoolAllocationBlockApiCreateSubnetPoolAllocationBlockOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteSubnetPoolAllocationBlockByOid**
> DeleteResponseDtoCommon DeleteSubnetPoolAllocationBlockByOid(ctx, paRequestId, tenantId, oid, optional)
Delete SubnetPoolAllocationBlock instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SubnetPoolAllocationBlockApiDeleteSubnetPoolAllocationBlockByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SubnetPoolAllocationBlockApiDeleteSubnetPoolAllocationBlockByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindSubnetPoolAllocationBlockByOid**
> SubnetPoolAllocationBlockDtoGet FindSubnetPoolAllocationBlockByOid(ctx, paRequestId, tenantId, oid, optional)
Find SubnetPoolAllocationBlock instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SubnetPoolAllocationBlockApiFindSubnetPoolAllocationBlockByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SubnetPoolAllocationBlockApiFindSubnetPoolAllocationBlockByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**SubnetPoolAllocationBlockDtoGet**](SubnetPoolAllocationBlockDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListSubnetPoolAllocationBlock**
> SubnetPoolAllocationBlockDtoList ListSubnetPoolAllocationBlock(ctx, paRequestId, tenantId, optional)
List SubnetPoolAllocationBlock instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SubnetPoolAllocationBlockApiListSubnetPoolAllocationBlockOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SubnetPoolAllocationBlockApiListSubnetPoolAllocationBlockOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**SubnetPoolAllocationBlockDtoList**](SubnetPoolAllocationBlockDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship76**
> RelgetResponseDtoCommon ListTenantRelationship76(ctx, paRequestId, tenantId, oid, optional)
List relationships of SubnetPoolAllocationBlock instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SubnetPoolAllocationBlockApiListTenantRelationship76Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SubnetPoolAllocationBlockApiListTenantRelationship76Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchSubnetPoolAllocationBlock**
> PostpatchResponseDtoCommon PatchSubnetPoolAllocationBlock(ctx, body, paRequestId, tenantId, oid, optional)
Patch SubnetPoolAllocationBlock instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SubnetPoolAllocationBlockDto**](SubnetPoolAllocationBlockDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SubnetPoolAllocationBlockApiPatchSubnetPoolAllocationBlockOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SubnetPoolAllocationBlockApiPatchSubnetPoolAllocationBlockOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateSubnetPoolAllocationBlockRelationship**
> RelpatchResponseDtoCommon UpdateSubnetPoolAllocationBlockRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from SubnetPoolAllocationBlock instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SubnetPoolAllocationBlockApiUpdateSubnetPoolAllocationBlockRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SubnetPoolAllocationBlockApiUpdateSubnetPoolAllocationBlockRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

