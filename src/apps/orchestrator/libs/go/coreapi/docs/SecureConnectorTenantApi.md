# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateSecureConnectorTenant**](SecureConnectorTenantApi.md#CreateSecureConnectorTenant) | **Post** /secure-connector-tenant | Create SecureConnectorTenant instance
[**DeleteSecureConnectorTenantByOid**](SecureConnectorTenantApi.md#DeleteSecureConnectorTenantByOid) | **Delete** /secure-connector-tenant/{oid} | Delete SecureConnectorTenant instance by oid
[**FindSecureConnectorTenantByOid**](SecureConnectorTenantApi.md#FindSecureConnectorTenantByOid) | **Get** /secure-connector-tenant/{oid} | Find SecureConnectorTenant instance by oid
[**ListSecureConnectorTenant**](SecureConnectorTenantApi.md#ListSecureConnectorTenant) | **Get** /secure-connector-tenant | List SecureConnectorTenant instances
[**ListTenantRelationship70**](SecureConnectorTenantApi.md#ListTenantRelationship70) | **Get** /secure-connector-tenant/{oid}/relationship | List relationships of SecureConnectorTenant instance
[**PatchSecureConnectorTenant**](SecureConnectorTenantApi.md#PatchSecureConnectorTenant) | **Patch** /secure-connector-tenant/{oid} | Patch SecureConnectorTenant instance by oid, if not found then attempt to create a new instance

# **CreateSecureConnectorTenant**
> PostpatchResponseDtoCommon CreateSecureConnectorTenant(ctx, body, paRequestId, optional)
Create SecureConnectorTenant instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SecureConnectorTenantDtoPost**](SecureConnectorTenantDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
 **optional** | ***SecureConnectorTenantApiCreateSecureConnectorTenantOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SecureConnectorTenantApiCreateSecureConnectorTenantOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.**| b3 header for trace context propagation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteSecureConnectorTenantByOid**
> DeleteResponseDtoCommon DeleteSecureConnectorTenantByOid(ctx, paRequestId, oid, optional)
Delete SecureConnectorTenant instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SecureConnectorTenantApiDeleteSecureConnectorTenantByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SecureConnectorTenantApiDeleteSecureConnectorTenantByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindSecureConnectorTenantByOid**
> SecureConnectorTenantDtoGet FindSecureConnectorTenantByOid(ctx, paRequestId, oid, optional)
Find SecureConnectorTenant instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SecureConnectorTenantApiFindSecureConnectorTenantByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SecureConnectorTenantApiFindSecureConnectorTenantByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 

### Return type

[**SecureConnectorTenantDtoGet**](SecureConnectorTenantDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListSecureConnectorTenant**
> SecureConnectorTenantDtoList ListSecureConnectorTenant(ctx, paRequestId, optional)
List SecureConnectorTenant instances

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
 **optional** | ***SecureConnectorTenantApiListSecureConnectorTenantOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SecureConnectorTenantApiListSecureConnectorTenantOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------

 **b3** | **optional.String**| b3 header for trace context propagation | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**SecureConnectorTenantDtoList**](SecureConnectorTenantDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship70**
> RelgetResponseDtoCommon ListTenantRelationship70(ctx, paRequestId, oid, optional)
List relationships of SecureConnectorTenant instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SecureConnectorTenantApiListTenantRelationship70Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SecureConnectorTenantApiListTenantRelationship70Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchSecureConnectorTenant**
> PostpatchResponseDtoCommon PatchSecureConnectorTenant(ctx, body, paRequestId, oid, optional)
Patch SecureConnectorTenant instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SecureConnectorTenantDto**](SecureConnectorTenantDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SecureConnectorTenantApiPatchSecureConnectorTenantOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SecureConnectorTenantApiPatchSecureConnectorTenantOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

