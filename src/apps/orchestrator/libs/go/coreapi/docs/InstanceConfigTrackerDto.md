# InstanceConfigTrackerDto

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AwsEnvironmentId** | **string** |  | [optional] [default to null]
**CdlTenantId** | **string** |  | [optional] [default to null]
**CreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**CurrentConfigOid** | **string** |  | [optional] [default to null]
**DeletedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedCreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDataflowProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDatasinkProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedUpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**LastProcessedConfigStatus** | **string** |  | [optional] [default to null]
**LastTenantPushedConfigOid** | **string** |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**Oid** | **string** |  | [optional] [default to null]
**PaTenantId** | **string** |  | [optional] [default to null]
**ProcessedConfigOid** | **string** |  | [optional] [default to null]
**ProcessedPanJobId** | **string** |  | [optional] [default to null]
**SdbCfgFirstboot** | **string** |  | [optional] [default to null]
**SdbCfgMgmtsvrAutoCommitMerge** | **string** |  | [optional] [default to null]
**SdbCfgSaasSaasAgentForceRestart** | **string** |  | [optional] [default to null]
**SentConfigOid** | **string** |  | [optional] [default to null]
**SubTenantId** | **string** |  | [optional] [default to null]
**SuccessfulConfigOid** | **string** |  | [optional] [default to null]
**SuccessfulProcessedPanJobId** | **string** |  | [optional] [default to null]
**TenantId** | **string** |  | [optional] [default to null]
**TenantServiceGroup** | **string** |  | [optional] [default to null]
**UpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Version** | **int64** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

