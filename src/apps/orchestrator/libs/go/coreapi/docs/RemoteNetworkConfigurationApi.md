# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateRemoteNetworkConfiguration**](RemoteNetworkConfigurationApi.md#CreateRemoteNetworkConfiguration) | **Post** /tenant/{tenant_id}/remote-network-configuration | Create RemoteNetworkConfiguration instance
[**DeleteRemoteNetworkConfigurationByOid**](RemoteNetworkConfigurationApi.md#DeleteRemoteNetworkConfigurationByOid) | **Delete** /tenant/{tenant_id}/remote-network-configuration/{oid} | Delete RemoteNetworkConfiguration instance by oid
[**FindRemoteNetworkConfigurationByOid**](RemoteNetworkConfigurationApi.md#FindRemoteNetworkConfigurationByOid) | **Get** /tenant/{tenant_id}/remote-network-configuration/{oid} | Find RemoteNetworkConfiguration instance by oid
[**ListRemoteNetworkConfiguration**](RemoteNetworkConfigurationApi.md#ListRemoteNetworkConfiguration) | **Get** /tenant/{tenant_id}/remote-network-configuration | List RemoteNetworkConfiguration instances of the tenant
[**ListTenantRelationship57**](RemoteNetworkConfigurationApi.md#ListTenantRelationship57) | **Get** /tenant/{tenant_id}/remote-network-configuration/{oid}/relationship | List relationships of RemoteNetworkConfiguration instance
[**PatchRemoteNetworkConfiguration**](RemoteNetworkConfigurationApi.md#PatchRemoteNetworkConfiguration) | **Patch** /tenant/{tenant_id}/remote-network-configuration/{oid} | Patch RemoteNetworkConfiguration instance by oid, if not found then attempt to create a new instance
[**UpdateRemoteNetworkConfigurationRelationship**](RemoteNetworkConfigurationApi.md#UpdateRemoteNetworkConfigurationRelationship) | **Patch** /tenant/{tenant_id}/remote-network-configuration/{oid}/relationship | Update relationships originated from RemoteNetworkConfiguration instance

# **CreateRemoteNetworkConfiguration**
> PostpatchResponseDtoCommon CreateRemoteNetworkConfiguration(ctx, body, paRequestId, tenantId, optional)
Create RemoteNetworkConfiguration instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RemoteNetworkConfigurationDtoPost**](RemoteNetworkConfigurationDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***RemoteNetworkConfigurationApiCreateRemoteNetworkConfigurationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RemoteNetworkConfigurationApiCreateRemoteNetworkConfigurationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteRemoteNetworkConfigurationByOid**
> DeleteResponseDtoCommon DeleteRemoteNetworkConfigurationByOid(ctx, paRequestId, tenantId, oid, optional)
Delete RemoteNetworkConfiguration instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RemoteNetworkConfigurationApiDeleteRemoteNetworkConfigurationByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RemoteNetworkConfigurationApiDeleteRemoteNetworkConfigurationByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindRemoteNetworkConfigurationByOid**
> RemoteNetworkConfigurationDtoGet FindRemoteNetworkConfigurationByOid(ctx, paRequestId, tenantId, oid, optional)
Find RemoteNetworkConfiguration instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RemoteNetworkConfigurationApiFindRemoteNetworkConfigurationByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RemoteNetworkConfigurationApiFindRemoteNetworkConfigurationByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RemoteNetworkConfigurationDtoGet**](RemoteNetworkConfigurationDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListRemoteNetworkConfiguration**
> RemoteNetworkConfigurationDtoList ListRemoteNetworkConfiguration(ctx, paRequestId, tenantId, optional)
List RemoteNetworkConfiguration instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***RemoteNetworkConfigurationApiListRemoteNetworkConfigurationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RemoteNetworkConfigurationApiListRemoteNetworkConfigurationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RemoteNetworkConfigurationDtoList**](RemoteNetworkConfigurationDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship57**
> RelgetResponseDtoCommon ListTenantRelationship57(ctx, paRequestId, tenantId, oid, optional)
List relationships of RemoteNetworkConfiguration instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RemoteNetworkConfigurationApiListTenantRelationship57Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RemoteNetworkConfigurationApiListTenantRelationship57Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchRemoteNetworkConfiguration**
> PostpatchResponseDtoCommon PatchRemoteNetworkConfiguration(ctx, body, paRequestId, tenantId, oid, optional)
Patch RemoteNetworkConfiguration instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RemoteNetworkConfigurationDto**](RemoteNetworkConfigurationDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RemoteNetworkConfigurationApiPatchRemoteNetworkConfigurationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RemoteNetworkConfigurationApiPatchRemoteNetworkConfigurationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateRemoteNetworkConfigurationRelationship**
> RelpatchResponseDtoCommon UpdateRemoteNetworkConfigurationRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from RemoteNetworkConfiguration instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***RemoteNetworkConfigurationApiUpdateRemoteNetworkConfigurationRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RemoteNetworkConfigurationApiUpdateRemoteNetworkConfigurationRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

