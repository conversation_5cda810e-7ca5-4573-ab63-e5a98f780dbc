# TenantVirtualIonDtoPost

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AwsEnvironmentId** | **string** |  | [optional] [default to null]
**CdlTenantId** | **string** |  | [optional] [default to null]
**ClusterId** | **string** |  | [optional] [default to null]
**ClusterNodeType** | **string** |  | [optional] [default to null]
**CreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**DeletedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ElementClusterRole** | **string** |  | [optional] [default to null]
**ElementId** | **string** |  | [optional] [default to null]
**ImportedCreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDataflowProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDatasinkProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedUpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**LoopbackIpOrchResourceId** | **int64** |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**Oid** | **string** |  | [default to null]
**PaTenantId** | **string** |  | [optional] [default to null]
**SaseAccessSharedVpcName** | **string** |  | [optional] [default to null]
**SaseAccessVni** | **int64** |  | [optional] [default to null]
**SaseFabricFeatureEnabled** | **bool** |  | [optional] [default to null]
**SaseFabricShardId** | **string** |  | [optional] [default to null]
**SdwanControllerUri** | **string** |  | [optional] [default to null]
**SdwanRegion** | **string** |  | [optional] [default to null]
**SdwanTenantId** | **string** |  | [optional] [default to null]
**SiteId** | **string** |  | [optional] [default to null]
**SubTenantId** | **string** |  | [default to null]
**TenantId** | **string** |  | [default to null]
**TenantServiceGroup** | **string** |  | [default to null]
**TenantVirtualIonId** | **string** |  | [default to null]
**TenantVirtualIonLoopbackIp** | **string** |  | [optional] [default to null]
**UpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Version** | **int64** |  | [optional] [default to null]
**VirtualIonOid** | **string** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

