# InstanceConfigRequestStatusDto

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AwsEnvironmentId** | **string** |  | [optional] [default to null]
**CdlTenantId** | **string** |  | [optional] [default to null]
**ClusterId** | **string** |  | [optional] [default to null]
**ConfigRequestOid** | **string** |  | [optional] [default to null]
**CreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**DeletedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**FwJobId** | **string** |  | [optional] [default to null]
**FwJobResultDetails** | **string** |  | [optional] [default to null]
**FwJobStatus** | **string** |  | [optional] [default to null]
**ImportedCreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDataflowProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDatasinkProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedUpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**InstanceId** | **string** |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**NetworkMd5sum** | **string** |  | [optional] [default to null]
**Oid** | **string** |  | [optional] [default to null]
**PaTenantId** | **string** |  | [optional] [default to null]
**PanoramaId** | **string** |  | [optional] [default to null]
**PanoramaJobId** | **string** |  | [optional] [default to null]
**PeerStatus** | **string** |  | [optional] [default to null]
**PushType** | **string** |  | [optional] [default to null]
**RequestType** | **string** |  | [optional] [default to null]
**ServiceType** | **string** |  | [optional] [default to null]
**SubTenantId** | **string** |  | [optional] [default to null]
**TenantId** | **string** |  | [optional] [default to null]
**TenantServiceGroup** | **string** |  | [optional] [default to null]
**UpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ValidationError** | **string** |  | [optional] [default to null]
**Version** | **int64** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

