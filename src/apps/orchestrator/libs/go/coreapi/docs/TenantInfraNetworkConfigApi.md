# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateTenantInfraNetworkConfig**](TenantInfraNetworkConfigApi.md#CreateTenantInfraNetworkConfig) | **Post** /tenant/{tenant_id}/tenant-infra-network-config | Create TenantInfraNetworkConfig instance
[**DeleteTenantInfraNetworkConfigByOid**](TenantInfraNetworkConfigApi.md#DeleteTenantInfraNetworkConfigByOid) | **Delete** /tenant/{tenant_id}/tenant-infra-network-config/{oid} | Delete TenantInfraNetworkConfig instance by oid
[**FindTenantInfraNetworkConfigByOid**](TenantInfraNetworkConfigApi.md#FindTenantInfraNetworkConfigByOid) | **Get** /tenant/{tenant_id}/tenant-infra-network-config/{oid} | Find TenantInfraNetworkConfig instance by oid
[**ListTenantInfraNetworkConfig**](TenantInfraNetworkConfigApi.md#ListTenantInfraNetworkConfig) | **Get** /tenant/{tenant_id}/tenant-infra-network-config | List TenantInfraNetworkConfig instances of the tenant
[**ListTenantRelationship81**](TenantInfraNetworkConfigApi.md#ListTenantRelationship81) | **Get** /tenant/{tenant_id}/tenant-infra-network-config/{oid}/relationship | List relationships of TenantInfraNetworkConfig instance
[**PatchTenantInfraNetworkConfig**](TenantInfraNetworkConfigApi.md#PatchTenantInfraNetworkConfig) | **Patch** /tenant/{tenant_id}/tenant-infra-network-config/{oid} | Patch TenantInfraNetworkConfig instance by oid, if not found then attempt to create a new instance

# **CreateTenantInfraNetworkConfig**
> PostpatchResponseDtoCommon CreateTenantInfraNetworkConfig(ctx, body, paRequestId, tenantId, optional)
Create TenantInfraNetworkConfig instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**TenantInfraNetworkConfigDtoPost**](TenantInfraNetworkConfigDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***TenantInfraNetworkConfigApiCreateTenantInfraNetworkConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantInfraNetworkConfigApiCreateTenantInfraNetworkConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteTenantInfraNetworkConfigByOid**
> DeleteResponseDtoCommon DeleteTenantInfraNetworkConfigByOid(ctx, paRequestId, tenantId, oid, optional)
Delete TenantInfraNetworkConfig instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantInfraNetworkConfigApiDeleteTenantInfraNetworkConfigByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantInfraNetworkConfigApiDeleteTenantInfraNetworkConfigByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindTenantInfraNetworkConfigByOid**
> TenantInfraNetworkConfigDtoGet FindTenantInfraNetworkConfigByOid(ctx, paRequestId, tenantId, oid, optional)
Find TenantInfraNetworkConfig instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantInfraNetworkConfigApiFindTenantInfraNetworkConfigByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantInfraNetworkConfigApiFindTenantInfraNetworkConfigByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**TenantInfraNetworkConfigDtoGet**](TenantInfraNetworkConfigDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantInfraNetworkConfig**
> TenantInfraNetworkConfigDtoList ListTenantInfraNetworkConfig(ctx, paRequestId, tenantId, optional)
List TenantInfraNetworkConfig instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***TenantInfraNetworkConfigApiListTenantInfraNetworkConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantInfraNetworkConfigApiListTenantInfraNetworkConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**TenantInfraNetworkConfigDtoList**](TenantInfraNetworkConfigDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship81**
> RelgetResponseDtoCommon ListTenantRelationship81(ctx, paRequestId, tenantId, oid, optional)
List relationships of TenantInfraNetworkConfig instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantInfraNetworkConfigApiListTenantRelationship81Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantInfraNetworkConfigApiListTenantRelationship81Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchTenantInfraNetworkConfig**
> PostpatchResponseDtoCommon PatchTenantInfraNetworkConfig(ctx, body, paRequestId, tenantId, oid, optional)
Patch TenantInfraNetworkConfig instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**TenantInfraNetworkConfigDto**](TenantInfraNetworkConfigDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantInfraNetworkConfigApiPatchTenantInfraNetworkConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantInfraNetworkConfigApiPatchTenantInfraNetworkConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

