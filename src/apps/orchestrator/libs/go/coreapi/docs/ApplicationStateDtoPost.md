# ApplicationStateDtoPost

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ApplicationConfigOid** | **string** |  | [optional] [default to null]
**AwsEnvironmentId** | **string** |  | [optional] [default to null]
**CdlTenantId** | **string** |  | [optional] [default to null]
**CgnxAppDefId** | **string** |  | [optional] [default to null]
**CgnxDnatRule** | **string** |  | [optional] [default to null]
**CgnxElementId** | **string** |  | [optional] [default to null]
**CgnxGlobalPrefixId** | **string** |  | [optional] [default to null]
**CgnxNatPool** | **string** |  | [optional] [default to null]
**CgnxNatPrefix** | **string** |  | [optional] [default to null]
**CgnxSecurityPrefixBindingId** | **string** |  | [optional] [default to null]
**CgnxSecurityPrefixId** | **string** |  | [optional] [default to null]
**CgnxSecurityRuleId** | **string** |  | [optional] [default to null]
**CgnxSitePrefixId** | **string** |  | [optional] [default to null]
**CgnxTenantId** | **string** |  | [optional] [default to null]
**CreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**DeletedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Fqdn** | **string** |  | [optional] [default to null]
**GroupOid** | **string** |  | [optional] [default to null]
**Health** | **string** |  | [optional] [default to null]
**ImportedCreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDataflowProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDatasinkProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedUpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**Oid** | **string** |  | [default to null]
**PaConnectorOid** | **string** |  | [optional] [default to null]
**PaFabricAddress** | **string** |  | [optional] [default to null]
**PaTenantId** | **string** |  | [optional] [default to null]
**Ports** | **string** |  | [optional] [default to null]
**PrivateAddress** | **string** |  | [optional] [default to null]
**Protocol** | **string** |  | [optional] [default to null]
**StateBits** | **int32** |  | [optional] [default to null]
**SubTenantId** | **string** |  | [default to null]
**TenantId** | **string** |  | [default to null]
**TenantServiceGroup** | **string** |  | [default to null]
**UpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Version** | **int64** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

