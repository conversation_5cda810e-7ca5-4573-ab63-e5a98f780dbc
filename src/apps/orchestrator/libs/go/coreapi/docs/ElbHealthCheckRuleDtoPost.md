# ElbHealthCheckRuleDtoPost

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AwsEnvironmentId** | **string** |  | [optional] [default to null]
**CdlTenantId** | **string** |  | [optional] [default to null]
**CheckIntervalSec** | **int64** |  | [optional] [default to null]
**CreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**DeletedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**HealthyThreshold** | **int64** |  | [optional] [default to null]
**ImportedCreatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDataflowProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedDatasinkProcessedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**ImportedUpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**Oid** | **string** |  | [default to null]
**PaTenantId** | **string** |  | [optional] [default to null]
**Port** | **int64** |  | [optional] [default to null]
**PortName** | **string** |  | [optional] [default to null]
**ProxyHeader** | **string** |  | [optional] [default to null]
**Request** | **string** |  | [optional] [default to null]
**RequestPath** | **string** |  | [optional] [default to null]
**Response** | **string** |  | [optional] [default to null]
**SaseAccessSharedVpcName** | **string** |  | [optional] [default to null]
**SaseAccessVni** | **int64** |  | [optional] [default to null]
**SaseFabricFeatureEnabled** | **bool** |  | [optional] [default to null]
**SaseFabricShardId** | **string** |  | [optional] [default to null]
**SdwanControllerUri** | **string** |  | [optional] [default to null]
**SdwanRegion** | **string** |  | [optional] [default to null]
**SdwanTenantId** | **string** |  | [optional] [default to null]
**SubTenantId** | **string** |  | [default to null]
**TenantId** | **string** |  | [default to null]
**TenantServiceGroup** | **string** |  | [default to null]
**TimeoutSec** | **int64** |  | [optional] [default to null]
**Type_** | **string** |  | [optional] [default to null]
**UnhealthyThreshold** | **int64** |  | [optional] [default to null]
**UpdatedTime** | [**time.Time**](time.Time.md) |  | [optional] [default to null]
**Version** | **int64** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

