# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateTenantServicesConfigRequest**](TenantServicesConfigRequestApi.md#CreateTenantServicesConfigRequest) | **Post** /tenant/{tenant_id}/tenant-services-config-request | Create TenantServicesConfigRequest instance
[**DeleteTenantServicesConfigRequestByOid**](TenantServicesConfigRequestApi.md#DeleteTenantServicesConfigRequestByOid) | **Delete** /tenant/{tenant_id}/tenant-services-config-request/{oid} | Delete TenantServicesConfigRequest instance by oid
[**FindTenantServicesConfigRequestByOid**](TenantServicesConfigRequestApi.md#FindTenantServicesConfigRequestByOid) | **Get** /tenant/{tenant_id}/tenant-services-config-request/{oid} | Find TenantServicesConfigRequest instance by oid
[**ListTenantRelationship83**](TenantServicesConfigRequestApi.md#ListTenantRelationship83) | **Get** /tenant/{tenant_id}/tenant-services-config-request/{oid}/relationship | List relationships of TenantServicesConfigRequest instance
[**ListTenantServicesConfigRequest**](TenantServicesConfigRequestApi.md#ListTenantServicesConfigRequest) | **Get** /tenant/{tenant_id}/tenant-services-config-request | List TenantServicesConfigRequest instances of the tenant
[**PatchTenantServicesConfigRequest**](TenantServicesConfigRequestApi.md#PatchTenantServicesConfigRequest) | **Patch** /tenant/{tenant_id}/tenant-services-config-request/{oid} | Patch TenantServicesConfigRequest instance by oid, if not found then attempt to create a new instance
[**UpdateTenantServicesConfigRequestRelationship**](TenantServicesConfigRequestApi.md#UpdateTenantServicesConfigRequestRelationship) | **Patch** /tenant/{tenant_id}/tenant-services-config-request/{oid}/relationship | Update relationships originated from TenantServicesConfigRequest instance

# **CreateTenantServicesConfigRequest**
> PostpatchResponseDtoCommon CreateTenantServicesConfigRequest(ctx, body, paRequestId, tenantId, optional)
Create TenantServicesConfigRequest instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**TenantServicesConfigRequestDtoPost**](TenantServicesConfigRequestDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***TenantServicesConfigRequestApiCreateTenantServicesConfigRequestOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantServicesConfigRequestApiCreateTenantServicesConfigRequestOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteTenantServicesConfigRequestByOid**
> DeleteResponseDtoCommon DeleteTenantServicesConfigRequestByOid(ctx, paRequestId, tenantId, oid, optional)
Delete TenantServicesConfigRequest instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantServicesConfigRequestApiDeleteTenantServicesConfigRequestByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantServicesConfigRequestApiDeleteTenantServicesConfigRequestByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindTenantServicesConfigRequestByOid**
> TenantServicesConfigRequestDtoGet FindTenantServicesConfigRequestByOid(ctx, paRequestId, tenantId, oid, optional)
Find TenantServicesConfigRequest instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantServicesConfigRequestApiFindTenantServicesConfigRequestByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantServicesConfigRequestApiFindTenantServicesConfigRequestByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**TenantServicesConfigRequestDtoGet**](TenantServicesConfigRequestDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship83**
> RelgetResponseDtoCommon ListTenantRelationship83(ctx, paRequestId, tenantId, oid, optional)
List relationships of TenantServicesConfigRequest instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantServicesConfigRequestApiListTenantRelationship83Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantServicesConfigRequestApiListTenantRelationship83Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantServicesConfigRequest**
> TenantServicesConfigRequestDtoList ListTenantServicesConfigRequest(ctx, paRequestId, tenantId, optional)
List TenantServicesConfigRequest instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***TenantServicesConfigRequestApiListTenantServicesConfigRequestOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantServicesConfigRequestApiListTenantServicesConfigRequestOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**TenantServicesConfigRequestDtoList**](TenantServicesConfigRequestDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchTenantServicesConfigRequest**
> PostpatchResponseDtoCommon PatchTenantServicesConfigRequest(ctx, body, paRequestId, tenantId, oid, optional)
Patch TenantServicesConfigRequest instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**TenantServicesConfigRequestDto**](TenantServicesConfigRequestDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantServicesConfigRequestApiPatchTenantServicesConfigRequestOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantServicesConfigRequestApiPatchTenantServicesConfigRequestOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateTenantServicesConfigRequestRelationship**
> RelpatchResponseDtoCommon UpdateTenantServicesConfigRequestRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from TenantServicesConfigRequest instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantServicesConfigRequestApiUpdateTenantServicesConfigRequestRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantServicesConfigRequestApiUpdateTenantServicesConfigRequestRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

