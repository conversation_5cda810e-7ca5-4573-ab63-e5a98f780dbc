# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateSaseSpnVionTunnelConfig**](SaseSpnVionTunnelConfigApi.md#CreateSaseSpnVionTunnelConfig) | **Post** /tenant/{tenant_id}/sase-spn-vion-tunnel-config | Create SaseSpnVionTunnelConfig instance
[**DeleteSaseSpnVionTunnelConfigByOid**](SaseSpnVionTunnelConfigApi.md#DeleteSaseSpnVionTunnelConfigByOid) | **Delete** /tenant/{tenant_id}/sase-spn-vion-tunnel-config/{oid} | Delete SaseSpnVionTunnelConfig instance by oid
[**FindSaseSpnVionTunnelConfigByOid**](SaseSpnVionTunnelConfigApi.md#FindSaseSpnVionTunnelConfigByOid) | **Get** /tenant/{tenant_id}/sase-spn-vion-tunnel-config/{oid} | Find SaseSpnVionTunnelConfig instance by oid
[**ListSaseSpnVionTunnelConfig**](SaseSpnVionTunnelConfigApi.md#ListSaseSpnVionTunnelConfig) | **Get** /tenant/{tenant_id}/sase-spn-vion-tunnel-config | List SaseSpnVionTunnelConfig instances of the tenant
[**ListTenantRelationship66**](SaseSpnVionTunnelConfigApi.md#ListTenantRelationship66) | **Get** /tenant/{tenant_id}/sase-spn-vion-tunnel-config/{oid}/relationship | List relationships of SaseSpnVionTunnelConfig instance
[**PatchSaseSpnVionTunnelConfig**](SaseSpnVionTunnelConfigApi.md#PatchSaseSpnVionTunnelConfig) | **Patch** /tenant/{tenant_id}/sase-spn-vion-tunnel-config/{oid} | Patch SaseSpnVionTunnelConfig instance by oid, if not found then attempt to create a new instance
[**UpdateSaseSpnVionTunnelConfigRelationship**](SaseSpnVionTunnelConfigApi.md#UpdateSaseSpnVionTunnelConfigRelationship) | **Patch** /tenant/{tenant_id}/sase-spn-vion-tunnel-config/{oid}/relationship | Update relationships originated from SaseSpnVionTunnelConfig instance

# **CreateSaseSpnVionTunnelConfig**
> PostpatchResponseDtoCommon CreateSaseSpnVionTunnelConfig(ctx, body, paRequestId, tenantId, optional)
Create SaseSpnVionTunnelConfig instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseSpnVionTunnelConfigDtoPost**](SaseSpnVionTunnelConfigDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseSpnVionTunnelConfigApiCreateSaseSpnVionTunnelConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseSpnVionTunnelConfigApiCreateSaseSpnVionTunnelConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteSaseSpnVionTunnelConfigByOid**
> DeleteResponseDtoCommon DeleteSaseSpnVionTunnelConfigByOid(ctx, paRequestId, tenantId, oid, optional)
Delete SaseSpnVionTunnelConfig instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseSpnVionTunnelConfigApiDeleteSaseSpnVionTunnelConfigByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseSpnVionTunnelConfigApiDeleteSaseSpnVionTunnelConfigByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindSaseSpnVionTunnelConfigByOid**
> SaseSpnVionTunnelConfigDtoGet FindSaseSpnVionTunnelConfigByOid(ctx, paRequestId, tenantId, oid, optional)
Find SaseSpnVionTunnelConfig instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseSpnVionTunnelConfigApiFindSaseSpnVionTunnelConfigByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseSpnVionTunnelConfigApiFindSaseSpnVionTunnelConfigByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**SaseSpnVionTunnelConfigDtoGet**](SaseSpnVionTunnelConfigDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListSaseSpnVionTunnelConfig**
> SaseSpnVionTunnelConfigDtoList ListSaseSpnVionTunnelConfig(ctx, paRequestId, tenantId, optional)
List SaseSpnVionTunnelConfig instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseSpnVionTunnelConfigApiListSaseSpnVionTunnelConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseSpnVionTunnelConfigApiListSaseSpnVionTunnelConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**SaseSpnVionTunnelConfigDtoList**](SaseSpnVionTunnelConfigDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship66**
> RelgetResponseDtoCommon ListTenantRelationship66(ctx, paRequestId, tenantId, oid, optional)
List relationships of SaseSpnVionTunnelConfig instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseSpnVionTunnelConfigApiListTenantRelationship66Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseSpnVionTunnelConfigApiListTenantRelationship66Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchSaseSpnVionTunnelConfig**
> PostpatchResponseDtoCommon PatchSaseSpnVionTunnelConfig(ctx, body, paRequestId, tenantId, oid, optional)
Patch SaseSpnVionTunnelConfig instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseSpnVionTunnelConfigDto**](SaseSpnVionTunnelConfigDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseSpnVionTunnelConfigApiPatchSaseSpnVionTunnelConfigOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseSpnVionTunnelConfigApiPatchSaseSpnVionTunnelConfigOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateSaseSpnVionTunnelConfigRelationship**
> RelpatchResponseDtoCommon UpdateSaseSpnVionTunnelConfigRelationship(ctx, body, paRequestId, tenantId, oid, optional)
Update relationships originated from SaseSpnVionTunnelConfig instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseSpnVionTunnelConfigApiUpdateSaseSpnVionTunnelConfigRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseSpnVionTunnelConfigApiUpdateSaseSpnVionTunnelConfigRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

