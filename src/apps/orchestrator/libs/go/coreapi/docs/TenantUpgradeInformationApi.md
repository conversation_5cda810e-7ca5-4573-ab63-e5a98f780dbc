# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateTenantUpgradeInformation**](TenantUpgradeInformationApi.md#CreateTenantUpgradeInformation) | **Post** /tenant-upgrade-information | Create TenantUpgradeInformation instance
[**DeleteTenantUpgradeInformationByOid**](TenantUpgradeInformationApi.md#DeleteTenantUpgradeInformationByOid) | **Delete** /tenant-upgrade-information/{oid} | Delete TenantUpgradeInformation instance by oid
[**FindTenantUpgradeInformationByOid**](TenantUpgradeInformationApi.md#FindTenantUpgradeInformationByOid) | **Get** /tenant-upgrade-information/{oid} | Find TenantUpgradeInformation instance by oid
[**ListTenantRelationship84**](TenantUpgradeInformationApi.md#ListTenantRelationship84) | **Get** /tenant-upgrade-information/{oid}/relationship | List relationships of TenantUpgradeInformation instance
[**ListTenantUpgradeInformation**](TenantUpgradeInformationApi.md#ListTenantUpgradeInformation) | **Get** /tenant-upgrade-information | List TenantUpgradeInformation instances
[**PatchTenantUpgradeInformation**](TenantUpgradeInformationApi.md#PatchTenantUpgradeInformation) | **Patch** /tenant-upgrade-information/{oid} | Patch TenantUpgradeInformation instance by oid, if not found then attempt to create a new instance
[**UpdateTenantUpgradeInformationRelationship**](TenantUpgradeInformationApi.md#UpdateTenantUpgradeInformationRelationship) | **Patch** /tenant-upgrade-information/{oid}/relationship | Update relationships originated from TenantUpgradeInformation instance

# **CreateTenantUpgradeInformation**
> PostpatchResponseDtoCommon CreateTenantUpgradeInformation(ctx, body, paRequestId, optional)
Create TenantUpgradeInformation instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**TenantUpgradeInformationDtoPost**](TenantUpgradeInformationDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
 **optional** | ***TenantUpgradeInformationApiCreateTenantUpgradeInformationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantUpgradeInformationApiCreateTenantUpgradeInformationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.**| b3 header for trace context propagation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteTenantUpgradeInformationByOid**
> DeleteResponseDtoCommon DeleteTenantUpgradeInformationByOid(ctx, paRequestId, oid, optional)
Delete TenantUpgradeInformation instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantUpgradeInformationApiDeleteTenantUpgradeInformationByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantUpgradeInformationApiDeleteTenantUpgradeInformationByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindTenantUpgradeInformationByOid**
> TenantUpgradeInformationDtoGet FindTenantUpgradeInformationByOid(ctx, paRequestId, oid, optional)
Find TenantUpgradeInformation instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantUpgradeInformationApiFindTenantUpgradeInformationByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantUpgradeInformationApiFindTenantUpgradeInformationByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 

### Return type

[**TenantUpgradeInformationDtoGet**](TenantUpgradeInformationDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship84**
> RelgetResponseDtoCommon ListTenantRelationship84(ctx, paRequestId, oid, optional)
List relationships of TenantUpgradeInformation instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantUpgradeInformationApiListTenantRelationship84Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantUpgradeInformationApiListTenantRelationship84Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantUpgradeInformation**
> TenantUpgradeInformationDtoList ListTenantUpgradeInformation(ctx, paRequestId, optional)
List TenantUpgradeInformation instances

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
 **optional** | ***TenantUpgradeInformationApiListTenantUpgradeInformationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantUpgradeInformationApiListTenantUpgradeInformationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------

 **b3** | **optional.String**| b3 header for trace context propagation | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**TenantUpgradeInformationDtoList**](TenantUpgradeInformationDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchTenantUpgradeInformation**
> PostpatchResponseDtoCommon PatchTenantUpgradeInformation(ctx, body, paRequestId, oid, optional)
Patch TenantUpgradeInformation instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**TenantUpgradeInformationDto**](TenantUpgradeInformationDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantUpgradeInformationApiPatchTenantUpgradeInformationOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantUpgradeInformationApiPatchTenantUpgradeInformationOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateTenantUpgradeInformationRelationship**
> RelpatchResponseDtoCommon UpdateTenantUpgradeInformationRelationship(ctx, body, paRequestId, oid, optional)
Update relationships originated from TenantUpgradeInformation instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RelationshipsPatchDto**](RelationshipsPatchDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***TenantUpgradeInformationApiUpdateTenantUpgradeInformationRelationshipOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a TenantUpgradeInformationApiUpdateTenantUpgradeInformationRelationshipOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 

### Return type

[**RelpatchResponseDtoCommon**](RELPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

