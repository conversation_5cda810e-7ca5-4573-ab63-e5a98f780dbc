# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateUdaBHClusterDeployment**](UdaBhClusterDeploymentApi.md#CreateUdaBHClusterDeployment) | **Post** /uda-bh-cluster-deployment | Create UdaBHClusterDeployment instance
[**DeleteUdaBHClusterDeploymentByOid**](UdaBhClusterDeploymentApi.md#DeleteUdaBHClusterDeploymentByOid) | **Delete** /uda-bh-cluster-deployment/{oid} | Delete UdaBHClusterDeployment instance by oid
[**FindUdaBHClusterDeploymentByOid**](UdaBhClusterDeploymentApi.md#FindUdaBHClusterDeploymentByOid) | **Get** /uda-bh-cluster-deployment/{oid} | Find UdaBHClusterDeployment instance by oid
[**ListTenantRelationship91**](UdaBhClusterDeploymentApi.md#ListTenantRelationship91) | **Get** /uda-bh-cluster-deployment/{oid}/relationship | List relationships of UdaBHClusterDeployment instance
[**ListUdaBHClusterDeployment**](UdaBhClusterDeploymentApi.md#ListUdaBHClusterDeployment) | **Get** /uda-bh-cluster-deployment | List UdaBHClusterDeployment instances
[**PatchUdaBHClusterDeployment**](UdaBhClusterDeploymentApi.md#PatchUdaBHClusterDeployment) | **Patch** /uda-bh-cluster-deployment/{oid} | Patch UdaBHClusterDeployment instance by oid, if not found then attempt to create a new instance

# **CreateUdaBHClusterDeployment**
> PostpatchResponseDtoCommon CreateUdaBHClusterDeployment(ctx, body, paRequestId, optional)
Create UdaBHClusterDeployment instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**UdaBhClusterDeploymentDtoPost**](UdaBhClusterDeploymentDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
 **optional** | ***UdaBhClusterDeploymentApiCreateUdaBHClusterDeploymentOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaBhClusterDeploymentApiCreateUdaBHClusterDeploymentOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.**| b3 header for trace context propagation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteUdaBHClusterDeploymentByOid**
> DeleteResponseDtoCommon DeleteUdaBHClusterDeploymentByOid(ctx, paRequestId, oid, optional)
Delete UdaBHClusterDeployment instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***UdaBhClusterDeploymentApiDeleteUdaBHClusterDeploymentByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaBhClusterDeploymentApiDeleteUdaBHClusterDeploymentByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindUdaBHClusterDeploymentByOid**
> UdaBhClusterDeploymentDtoGet FindUdaBHClusterDeploymentByOid(ctx, paRequestId, oid, optional)
Find UdaBHClusterDeployment instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***UdaBhClusterDeploymentApiFindUdaBHClusterDeploymentByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaBhClusterDeploymentApiFindUdaBHClusterDeploymentByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 

### Return type

[**UdaBhClusterDeploymentDtoGet**](UdaBHClusterDeploymentDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship91**
> RelgetResponseDtoCommon ListTenantRelationship91(ctx, paRequestId, oid, optional)
List relationships of UdaBHClusterDeployment instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***UdaBhClusterDeploymentApiListTenantRelationship91Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaBhClusterDeploymentApiListTenantRelationship91Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListUdaBHClusterDeployment**
> UdaBhClusterDeploymentDtoList ListUdaBHClusterDeployment(ctx, paRequestId, optional)
List UdaBHClusterDeployment instances

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
 **optional** | ***UdaBhClusterDeploymentApiListUdaBHClusterDeploymentOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaBhClusterDeploymentApiListUdaBHClusterDeploymentOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------

 **b3** | **optional.String**| b3 header for trace context propagation | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**UdaBhClusterDeploymentDtoList**](UdaBHClusterDeploymentDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchUdaBHClusterDeployment**
> PostpatchResponseDtoCommon PatchUdaBHClusterDeployment(ctx, body, paRequestId, oid, optional)
Patch UdaBHClusterDeployment instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**UdaBhClusterDeploymentDto**](UdaBhClusterDeploymentDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **oid** | **string**| object ID | 
 **optional** | ***UdaBhClusterDeploymentApiPatchUdaBHClusterDeploymentOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UdaBhClusterDeploymentApiPatchUdaBHClusterDeploymentOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

