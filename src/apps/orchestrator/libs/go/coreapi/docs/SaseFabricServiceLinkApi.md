# {{classname}}

All URIs are relative to *http://localhost:8080/api/sase/v1.0/controller*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateSaseFabricServiceLink**](SaseFabricServiceLinkApi.md#CreateSaseFabricServiceLink) | **Post** /tenant/{tenant_id}/sase-fabric-service-link | Create SaseFabricServiceLink instance
[**DeleteSaseFabricServiceLinkByOid**](SaseFabricServiceLinkApi.md#DeleteSaseFabricServiceLinkByOid) | **Delete** /tenant/{tenant_id}/sase-fabric-service-link/{oid} | Delete SaseFabricServiceLink instance by oid
[**FindSaseFabricServiceLinkByOid**](SaseFabricServiceLinkApi.md#FindSaseFabricServiceLinkByOid) | **Get** /tenant/{tenant_id}/sase-fabric-service-link/{oid} | Find SaseFabricServiceLink instance by oid
[**ListSaseFabricServiceLink**](SaseFabricServiceLinkApi.md#ListSaseFabricServiceLink) | **Get** /tenant/{tenant_id}/sase-fabric-service-link | List SaseFabricServiceLink instances of the tenant
[**ListTenantRelationship59**](SaseFabricServiceLinkApi.md#ListTenantRelationship59) | **Get** /tenant/{tenant_id}/sase-fabric-service-link/{oid}/relationship | List relationships of SaseFabricServiceLink instance
[**PatchSaseFabricServiceLink**](SaseFabricServiceLinkApi.md#PatchSaseFabricServiceLink) | **Patch** /tenant/{tenant_id}/sase-fabric-service-link/{oid} | Patch SaseFabricServiceLink instance by oid, if not found then attempt to create a new instance

# **CreateSaseFabricServiceLink**
> PostpatchResponseDtoCommon CreateSaseFabricServiceLink(ctx, body, paRequestId, tenantId, optional)
Create SaseFabricServiceLink instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseFabricServiceLinkDtoPost**](SaseFabricServiceLinkDtoPost.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseFabricServiceLinkApiCreateSaseFabricServiceLinkOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseFabricServiceLinkApiCreateSaseFabricServiceLinkOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteSaseFabricServiceLinkByOid**
> DeleteResponseDtoCommon DeleteSaseFabricServiceLinkByOid(ctx, paRequestId, tenantId, oid, optional)
Delete SaseFabricServiceLink instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseFabricServiceLinkApiDeleteSaseFabricServiceLinkByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseFabricServiceLinkApiDeleteSaseFabricServiceLinkByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **ifMatch** | **optional.String**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **purge** | **optional.Bool**| Purge operation (hard deletion) | 
 **purgeBefore** | **optional.Time**| Purge operation (hard deletion) on instances archived before the input ISO-8601 instant format | 

### Return type

[**DeleteResponseDtoCommon**](DELETEResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindSaseFabricServiceLinkByOid**
> SaseFabricServiceLinkDtoGet FindSaseFabricServiceLinkByOid(ctx, paRequestId, tenantId, oid, optional)
Find SaseFabricServiceLink instance by oid

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseFabricServiceLinkApiFindSaseFabricServiceLinkByOidOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseFabricServiceLinkApiFindSaseFabricServiceLinkByOidOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 

### Return type

[**SaseFabricServiceLinkDtoGet**](SaseFabricServiceLinkDtoGET.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListSaseFabricServiceLink**
> SaseFabricServiceLinkDtoList ListSaseFabricServiceLink(ctx, paRequestId, tenantId, optional)
List SaseFabricServiceLink instances of the tenant

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
 **optional** | ***SaseFabricServiceLinkApiListSaseFabricServiceLinkOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseFabricServiceLinkApiListSaseFabricServiceLinkOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**SaseFabricServiceLinkDtoList**](SaseFabricServiceLinkDtoLIST.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTenantRelationship59**
> RelgetResponseDtoCommon ListTenantRelationship59(ctx, paRequestId, tenantId, oid, optional)
List relationships of SaseFabricServiceLink instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseFabricServiceLinkApiListTenantRelationship59Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseFabricServiceLinkApiListTenantRelationship59Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------



 **b3** | **optional.String**| b3 header for trace context propagation | 
 **prismaSubTenant** | **optional.String**| Prisma Access [Sub]Tenant ID | 
 **includeDeleted** | **optional.Bool**| Include archived (soft-deleted) instances | 

### Return type

[**RelgetResponseDtoCommon**](RELGETResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **PatchSaseFabricServiceLink**
> PostpatchResponseDtoCommon PatchSaseFabricServiceLink(ctx, body, paRequestId, tenantId, oid, optional)
Patch SaseFabricServiceLink instance by oid, if not found then attempt to create a new instance

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**SaseFabricServiceLinkDto**](SaseFabricServiceLinkDto.md)|  | 
  **paRequestId** | **string**| Prisma Access client request ID | 
  **tenantId** | **string**| CDL tenant ID | 
  **oid** | **string**| object ID | 
 **optional** | ***SaseFabricServiceLinkApiPatchSaseFabricServiceLinkOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SaseFabricServiceLinkApiPatchSaseFabricServiceLinkOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------




 **b3** | **optional.**| b3 header for trace context propagation | 
 **ifMatch** | **optional.**| Succeeds if the resource version is equal to the ETag (version) in this header | 
 **prismaSubTenant** | **optional.**| Prisma Access [Sub]Tenant ID | 
 **noImplicitCreate** | **optional.**| Disable implicit creation | 

### Return type

[**PostpatchResponseDtoCommon**](POSTPATCHResponseDtoCommon.md)

### Authorization

[baerer-scheme](../README.md#baerer-scheme)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

