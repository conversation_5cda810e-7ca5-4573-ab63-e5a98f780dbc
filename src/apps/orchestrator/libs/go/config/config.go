// Package config loads the running configuration from the YAML file.
// We currently only expect to this on start up, and don't yet support
// dynamically loading changes in.
// TODO: Support dynamically loading changes in as well
package config

import (
	"encoding/base64"
	"io/ioutil"
	"orchestrator/libs/go/aws/kms"
	"os"
	"sync"

	"go.panw.local/pangolin/clogger"

	"gopkg.in/yaml.v3"
)

// LogLevel defines our default logging level. It directly maps to the clogger.ClogLevel,
// but allows us to configure the level via strings instead of the intefer ClogLevel that
// is used under the hood.
type LogLevel string

const (
	// LogLevelDebug implies a log level of clogger.LogLevelDebug
	LogLevelDebug LogLevel = "DEBUG"
	// LogLevelInfo implies a log level of clogger.LogLevelInfo
	LogLevelInfo = "INFO"
	// LogLevelWarn implies a log level of clogger.LogLevelWarn
	LogLevelWarn = "WARNING"
	// LogLevelError implies a log level of clogger.LogLevelError
	LogLevelError = "ERROR"
	// LogLevelFatal implies a log level of clogger.LogLevelFatal
	LogLevelFatal = "FATAL"
)

var (
	LogLevelMap = map[LogLevel]clogger.ClogLevel{
		"DEBUG":   clogger.LogLevelDebug,
		"INFO":    clogger.LogLevelInfo,
		"WARNING": clogger.LogLevelWarn,
		"ERROR":   clogger.LogLevelError,
		"FATAL":   clogger.LogLevelFatal,
	}
)

// Config holds all (or at least a broad enough subset of our required configurations)
// configurations from the YAML file
// This assumes that the `cfg.py` file is dumped as a YAML in block mode. It might be
// more readable to have the YAML generated with clearer groupings (similar to how
// UpgradeService is defined here)
type Config struct {
	lock                            sync.RWMutex                    `yaml:"-"` // internal read-write lock
	cfgFilePath                     string                          `yaml:"-"` // internal config file path
	AcctID                          string                          `yaml:"acct_id"`
	AwsEnv                          string                          `yaml:"aws_env"`
	CftBucketName                   string                          `yaml:"cft-bucket-name"`
	DBHost                          string                          `yaml:"dbhost"`
	DBName                          string                          `yaml:"dbname"`
	DBUser                          string                          `yaml:"dbuser"`
	DBPasswordEncrypted             string                          `yaml:"dbpassword"`
	SimulateDB                      bool                            `yaml:"simulate-db"`
	dbPassword                      string                          `yaml:"-"` // unexported member holding the decrypted password
	KeysBucketName                  string                          `yaml:"keys-bucket-name"`
	GCPEnv                          string                          `yaml:"gcp_env"`
	GCPImgHost                      string                          `yaml:"gcp_img_host"`
	GCPOrchProjectID                string                          `yaml:"gcp_orch_project_id"`
	OciTenancyId                    string                          `yaml:"oci_tenancy_id"`
	OciPaTenantsCompartmentId       string                          `yaml:"oci_pa_tenants_compartment_id"`
	OpenstackOrchProjectID          string                          `yaml:"openstack_orch_project_id"`
	UpgradeService                  upgradeService                  `yaml:"upgrade_service"`
	AvisarService                   avisarService                   `yaml:"avisar_service"`
	TerraformService                tfService                       `yaml:"terraform_service"`
	SaseService                     saseService                     `yaml:"sase_service"`
	SaseSecurityOrchestratorService saseSecurityOrchestratorService `yaml:"sase_security_orchestrator_service"`
	TFWorkerService                 tfWorkerService                 `yaml:"tf_worker_service"`
	ProvisionService                ProvisionService                `yaml:"provision_service"`
	ZtiController                   ZtiController                   `yaml:"zti_controller"`
	AWSAccessKeyID                  string                          `yaml:"aws_access_key_id"`
	AWSSecretAccessKey              string                          `yaml:"aws_secret_access_key"`
	AZRHostSubscriptionID           string                          `yaml:"azr_host_subscription_id"`
	AZRImageSubscriptionID          string                          `yaml:"azr_image_subscription_id"`
	AZRImageGalleryName             string                          `yaml:"azr_image_gallery_name"`
	AZRImageResourceGroup           string                          `yaml:"azr_image_resource_group"`
	AZRBillingAccount               string                          `yaml:"azr_billing_account"`
	AZREnrollmentAccount            string                          `yaml:"azr_enrollment_account"`
	AZRMgmtGroup                    string                          `yaml:"azr_mgmt_group"`
	AZRMgmtInfo                     azrMgmtInfo                     `yaml:"azr_mgmt_info"`
	Region                          string                          `yaml:"region"`
	IsChinaEnv                      bool                            `yaml:"is_china_env"`
	AwsEnvType                      string                          `yaml:"aws_env_type"`
	AvisarForwardingEnabled         bool                            `yaml:"avisar_forwarding_enabled"`
	AvisarServiceEndpoint           string                          `yaml:"avisar_service_endpoint"`
	AwsPartition                    string                          `yaml:"aws_partition"`
}

type upgradeService struct {
	LoggingLevel                     LogLevel      `yaml:"log_level"`
	DeployedEnv                      string        `yaml:"deployed_env"`
	States                           []stateConfig `yaml:"states"`
	GCPProjectID                     string        `yaml:"gcp_project_id"`
	GAppCredentials                  string        `yaml:"google_application_credentials"`
	VertScalePubSubTopic             string        `yaml:"vertical_scale_pubsub_topic"`
	AutoscalePubSubTopic             string        `yaml:"autoscale_pubsub_topic"`
	EPCnatScalePubSubTopic           string        `yaml:"epcnatscale_pubsub_topic"`
	EPCnatScaleLatency               int           `yaml:"epcnatscale_latency"`
	LogoutWaitTimeMin                int           `yaml:"logout_wait_time_min"`
	CleanupWaitTimeMin               int           `yaml:"cleanup_wait_time_min"`
	CSPToPAAlertPubSubTopic          string        `yaml:"csp_to_pa_alert_pubsub_topic"`
	CSPToPAAlertPubSubTopicNumShards int           `yaml:"csp_to_pa_alert_topic_num_shards"`
}

type saseService struct {
	LoggingLevel                      LogLevel      `yaml:"log_level"`
	DeployedEnv                       string        `yaml:"deployed_env"`
	States                            []stateConfig `yaml:"states"`
	Port                              int           `yaml:"port"`
	InstProvisionPubSubTopic          string        `yaml:"inst_provision_pubsub_topic"`
	InstProvisionPubSubSubscription   string        `yaml:"inst_provision_pubsub_subscription"`
	ProvisionNotifyPubsubTopic        string        `yaml:"provision_notify_pubsub_topic"`
	ProvisionNotifyPubsubSubscription string        `yaml:"provision_notify_pubsub_subscription"`
	/* TODO CVS PENDING CLEANUP */
	TFSReqPubsubTopic         string `yaml:"tf_service_req_pubsub_topic"`
	TFSReqPubsubSubscription  string `yaml:"tf_service_req_pubsub_subscription"`
	TFSRespPubsubTopic        string `yaml:"tf_service_resp_pubsub_topic"`
	TFSRespPubsubSubscription string `yaml:"tf_service_resp_pubsub_subscription"`
	/* TODO CVS PENDING CLEANUP */
	BwAllocationPubSubTopic         string `yaml:"bw_allocation_pubsub_topic"`
	BwAllocationPubSubSubscription  string `yaml:"bw_allocation_pubsub_subscription"`
	SpnAllocDeferredTopic           string `yaml:"spn_alloc_deferred_pubsub_topic"`
	SpnAllocDeferredSubscription    string `yaml:"spn_alloc_deferred_pubsub_subscription"`
	RegionSerializarionTopic        string `yaml:"region_serialization_topic"`
	RegionSerializarionSubscription string `yaml:"region_serialization_sub"`
	RegionZoneMappingFile           string `yaml:"region_zone_mapping_file"`
	SpnNotificationChannelHost      string `yaml:"spn_notification_channel_host"`
	SpnNotificationChannelPort      int    `yaml:"spn_notification_channel_port"`
	RNNotifyPubsubTopic             string `yaml:"rn_notification_pubsub_topic"`
	RNNotifyPubsubSubscription      string `yaml:"rn_notification_pubsub_subscription"`
	RNRelnNotifyPubsubSubscription  string `yaml:"rn_reln_notification_pubsub_subscription"`
}

type saseSecurityOrchestratorService struct {
	LoggingLevel                    LogLevel      `yaml:"log_level"`
	DeployedEnv                     string        `yaml:"deployed_env"`
	States                          []stateConfig `yaml:"states"`
	Port                            int           `yaml:"port"`
	BwAllocationPubSubTopic         string        `yaml:"bw_allocation_pubsub_topic"`
	BwAllocationPubSubSubscription  string        `yaml:"bw_allocation_pubsub_subscription"`
	InstanceNotifTopic              string        `yaml:"instance_notif_topic"`
	InstanceNotifSub                string        `yaml:"instance_notif_sub"`
	CoreApiUpdatesTopic             string        `yaml:"core_api_updates_topic"`
	AggBwLicChangesSub              string        `yaml:"agg_bw_license_changes_sub"`
	RegionSerializarionTopic        string        `yaml:"region_serialization_topic"`
	RegionSerializarionSubscription string        `yaml:"region_serialization_sub"`
	RegionZoneMappingFile           string        `yaml:"region_zone_mapping_file"`
	/* TODO CVS PENDING CLEANUP */
	TFSReqPubsubTopic         string `yaml:"tf_service_req_pubsub_topic"`
	TFSReqPubsubSubscription  string `yaml:"tf_service_req_pubsub_subscription"`
	TFSRespPubsubTopic        string `yaml:"tf_service_resp_pubsub_topic"`
	TFSRespPubsubSubscription string `yaml:"tf_service_resp_pubsub_subscription"`
	/* TODO CVS PENDING CLEANUP */
}

type avisarService struct {
	// CredsFilePath string `yaml:"credsFilePath"`
	MarsEvents struct {
		ForwardingEnabled bool `yaml:"forwarding_enabled"`
		Americas          struct {
			MarsEventsTopicName string `yaml:"mars_events_topic_name"`
			GcpProjectID        string `yaml:"gcp_project_id"`
		} `yaml:"americas"`
		Europe struct {
			MarsEventsTopicName string `yaml:"mars_events_topic_name"`
			GcpProjectID        string `yaml:"gcp_project_id"`
		} `yaml:"europe"`
		Unitedkingdom struct {
			MarsEventsTopicName string `yaml:"mars_events_topic_name"`
			GcpProjectID        string `yaml:"gcp_project_id"`
		} `yaml:"unitedkingdom"`
		Fedramp struct {
			MarsEventsTopicName string `yaml:"mars_events_topic_name"`
			GcpProjectID        string `yaml:"gcp_project_id"`
		} `yaml:"fedramp"`
		Canada struct {
			MarsEventsTopicName string `yaml:"mars_events_topic_name"`
			GcpProjectID        string `yaml:"gcp_project_id"`
		} `yaml:"canada"`
		Japan struct {
			MarsEventsTopicName string `yaml:"mars_events_topic_name"`
			GcpProjectID        string `yaml:"gcp_project_id"`
		} `yaml:"japan"`
		Australia struct {
			MarsEventsTopicName string `yaml:"mars_events_topic_name"`
			GcpProjectID        string `yaml:"gcp_project_id"`
		} `yaml:"australia"`
		Germany struct {
			MarsEventsTopicName string `yaml:"mars_events_topic_name"`
			GcpProjectID        string `yaml:"gcp_project_id"`
		} `yaml:"germany"`
		India struct {
			MarsEventsTopicName string `yaml:"mars_events_topic_name"`
			GcpProjectID        string `yaml:"gcp_project_id"`
		} `yaml:"india"`
		Singapore struct {
			MarsEventsTopicName string `yaml:"mars_events_topic_name"`
			GcpProjectID        string `yaml:"gcp_project_id"`
		} `yaml:"singapore"`
	} `yaml:"mars_events"`
	AllEvents struct {
		ForwardingEnabled   bool   `yaml:"forwarding_enabled"`
		MarsEventsTopicName string `yaml:"mars_events_topic_name"`
		GcpProjectID        string `yaml:"gcp_project_id"`
	} `yaml:"all_events"`
	IotCertCleanup struct {
		ReceivingEnabled     bool   `yaml:"receiving_enabled"`
		ListenerSQSQueueName string `yaml:"listener_sqs_queue_name"`
		AwsAccountID         string `yaml:"aws_account_id"`
		CronPollFrequency    uint32 `yaml:"cron_poll_frequency"`
	} `yaml:"iot_cert_cleanup"`
	VerticalScaleEvent struct {
		ForwardingEnabled  bool   `yaml:"forwarding_enabled"`
		VertScaleTopicName string `yaml:"vertical_scale_topic_name"`
		GcpProjectID       string `yaml:"gcp_project_id"`
	} `yaml:"vertical_scale_event"`
	KeyRotation struct {
		ServiceEnabled bool `yaml:"service_enabled"`
	} `yaml:"key_rotation"`
	NGPAAutoMigration struct {
		ServiceEnabled bool `yaml:"service_enabled"`
	} `yaml:"ngpa_auto_migration"`
	EPKeyRotation struct {
		ServiceEnabled bool `yaml:"service_enabled"`
	} `yaml:"ep_key_rotation"`
	IPAMEvents struct {
		ForwardingEnabled bool   `yaml:"forwarding_enabled"`
		IPAMTopicName     string `yaml:"ipam_topic_name"`
		GcpProjectID      string `yaml:"gcp_project_id"`
	} `yaml:"ipam_events"`
}

type tfService struct {
	LoggingLevel             LogLevel      `yaml:"log_level"`
	DeployedEnv              string        `yaml:"deployed_env"`
	States                   []stateConfig `yaml:"states"`
	Port                     int           `yaml:"port"`
	AZRTenantID              string        `yaml:"azr_tenant_id"`
	AZRClientID              string        `yaml:"azr_client_id"`
	AZRClientSecretEncrypted string        `yaml:"azr_client_secret"`
	azrClientSecret          string        `yaml:"-"` // unexported member holding the decrypted password
	TfToken                  string        `yaml:"token"`
	TFOrg                    string        `yaml:"organisation"`
}

type tfWorkerService struct {
	LoggingLevel      LogLevel           `yaml:"log_level"`
	DeployedEnv       string             `yaml:"deployed_env"`
	Listener          listenerConfig     `yaml:"listener"`
	Notifier          notifierConfig     `yaml:"notifier"`
	Port              int                `yaml:"port"`
	TfToken           string             `yaml:"token"` // unexported member holding the decrypted password
	TFOrg             string             `yaml:"organisation"`
	TFServerUrl       string             `yaml:"server_url"`
	TFMaxWsMonitoring int                `yaml:"max_ws_monitored"`
	TemplateDir       string             `yaml:"template_dir"`
	StateDir          string             `yaml:"state_dir"`
	CoreApiBasePath   string             `yaml:"core_api_base_path"`
	GcpDeployConfig   gcpDeployConfig    `yaml:"gcp"`
	OspDeployConfig   ospDeployConfig    `yaml:"openstack"`
	OciDeployConfig   ociDeployConfig    `yaml:"oci"`
	WsMonitorConfig   wsMonitoringConfig `yaml:"ws_monitor_config"`
	NetOpsTenantId    string             `yaml:"netops_tenant_id"`
	StateBucketName   string             `yaml:"state_bucket_name"`
}

type provisionServiceApi struct {
	CACertPath     string `yaml:"ca_cert_path"`
	CAKeyPath      string `yaml:"ca_key_path"`
	TaskAttemptMax int    `yaml:"task_attempt_max"`
}

type ProvisionService struct {
	LoggingLevel          LogLevel            `yaml:"log_level"`
	DeployedEnv           string              `yaml:"deployed_env"`
	TfAddress             string              `yaml:"tf_server"`
	TfToken               string              `yaml:"tf_token"`
	TfOrg                 string              `yaml:"tf_org"`
	TfPrj                 string              `yaml:"tf_prj"`
	TfInsecureSkipVerify  bool                `yaml:"tf_insecure_skip_verify"`
	RunType               string              `yaml:"run_type"`
	GcpDeployConfig       gcpDeployConfig     `yaml:"gcp"`
	OspDeployConfig       ospDeployConfig     `yaml:"openstack"`
	OciDeployConfig       ociDeployConfig     `yaml:"oci"`
	DbSimulation          bool                `yaml:"db_simulation"`
	S3Upload              bool                `yaml:"s3_upload"`
	PostProcessingUrl     string              `yaml:"post_processing_url"`
	Api                   provisionServiceApi `yaml:"api"`
	GoogleProviderVersion string              `yaml:"google_provider_version"`
}

type ZtiController struct {
	LoggingLevel    LogLevel        `yaml:"log_level"`
	DeployedEnv     string          `yaml:"deployed_env"`
	Service         Service         `yaml:"service"`
	DbSimulation    bool            `yaml:"db_simulation"`
	ArgoServiceInfo ArgoServiceInfo `yaml:"argo_service_info"`
	Kro             Kro             `yaml:"kro"`
	DataplaneInfo   DataplaneInfo   `yaml:"dataplane_info"`
	R53Info         R53Info         `yaml:"r53_info"`
}

type R53Info struct {
	Route53acct   string `yaml:"route53_acct"`
	EntradaDomain string `yaml:"entrada_domain"`
}

type DataplaneInfo struct {
	GcpProjectID string `yaml:"gcp_project_id"`
}
type Kro struct {
	AddressType string `yaml:"address_type"`
	NameSuffix  string `yaml:"name_suffix"`
}
type ArgoServiceInfo struct {
	ArgoBaseUrl       string `yaml:"argo_base_url"`
	ArgoApiPath       string `yaml:"argo_api_path"`
	ArgoDeleteUrl     string `yaml:"argo_delete_url"`
	ArgoDeleteApiPath string `yaml:"argo_delete_api_path"`
}

type Service struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
}

type azrMgmtInfo struct {
	ResourceGroup string `yaml:"resource_group"`
	Vnet          string `yaml:"vnet"`
	Subnet        string `yaml:"subnet"`
	SyslogPlsID   string `yaml:"syslog_pls_resource_id"`
	ProxySSHKeys  string `yaml:"proxy_ssh_keys"`
}

type notifierConfig struct {
	Queue        pubSubName   `yaml:"queue"`
	Concurrency  uint32       `yaml:"concurrency"`
	PollInterval pollInterval `yaml:"poll_interval"`
}

type listenerConfig struct {
	Queue pubSubName `yaml:"queue"`
}

type stateConfig struct {
	Name         string       `yaml:"name"`
	Concurrency  uint32       `yaml:"concurrency"`
	PollInterval pollInterval `yaml:"poll_interval"`
}

type pubSubName struct {
	Topic        string `yaml:"topic"`
	Subscription string `yaml:"subscription"`
}

type pollInterval struct {
	Hours   uint32 `yaml:"hours"`
	Minutes uint32 `yaml:"minutes"`
	Seconds uint32 `yaml:"seconds"`
}

// Configuration to migrate from DM to Terraform
type gcpTerraformMigrationConfig struct {
	AutoMigration bool `yaml:"auto_migration"`
}

// Configuration to deploy virtual machines in GCP Project via Terraform
type gcpDeployConfig struct {
	DeploymentCredential             string                      `yaml:"deploy_credential"`
	DeploymentProjectName            string                      `yaml:"deploy_project_name"`
	NameFormats                      nameFormats                 `yaml:"name_formats"`
	MachineTypeTemplate              string                      `yaml:"machine_type_template"`
	RegionZoneMappingFile            string                      `yaml:"region_zone_mapping_file"`
	Overrides                        map[string]string           `yaml:"overrides"`
	TerraformVersion                 string                      `yaml:"terraform_version"`
	DefaultTerraformService          string                      `yaml:"default_terraform_service"`
	MigrateToDefaultTerraformService bool                        `yaml:"migrate_to_default_terraform_service"`
	TerraformMigrationConfig         gcpTerraformMigrationConfig `yaml:"terraform_migration_config"`
	NewProjectGcpProvisionType       string                      `yaml:"new_project_gcp_provision_type"`
	NewNetworkGcpProvisionType       string                      `yaml:"new_network_gcp_provision_type"`
	UsageQuerySupport                bool                        `yaml:"usage_query_support"`
}

type ociDeployConfig struct {
	DeploymentCredential string `yaml:"deploy_credential"`
	TerraformVersion     string `yaml:"terraform_version"`
}

// Configuration to deploy virtual machines in Openstack Project via Terraform
type ospDeployConfig struct {
	DeploymentCertificate string `yaml:"deploy_certificate"`
	DeploymentCredential  string `yaml:"deploy_credential"`
	DecryptCredential     string `yaml:"decrypt_credential"`
	AuthUrl               string `yaml:"auth_url"`
	ExecutionMode         string `yaml:"execution_mode"`
	AgentPoolId           string `yaml:"agent_pool_id"`
}

// Workspace Monitoring Configuration
type wsMonitoringConfig struct {
	RetryCount        int          `yaml:"retry_count"`
	RetryDelay        int          `yaml:"retry_delay"`
	RegionMessageIntv pollInterval `yaml:"region_message_poll_interval"`
}

// Naming formats for a virtual machine resources
type nameFormats struct {
	InfController interfaceNameFormats `yaml:"inf_controller"`
	InfManagement interfaceNameFormats `yaml:"inf_management"`
}

// Vpc and Subnet Formats
type interfaceNameFormats struct {
	Vpc     string `yaml:"vpc"`
	Subnet  string `yaml:"subnet"`
	Project string `yaml:"project"`
}

// LoadConfig reads the configuration from a YAML file specified at fPath and returns
// a struct holding the unmarshalled information
func LoadConfig(fPath string) (*Config, error) {
	cfg := Config{}
	cfg.lock.Lock()
	defer func() {
		cfg.lock.Unlock()
	}()
	yamlFile, err := ioutil.ReadFile(fPath)
	if err != nil {
		return nil, err
	}
	err = yaml.Unmarshal(yamlFile, &cfg)
	if err != nil {
		return nil, err
	}
	cfg.cfgFilePath = fPath
	return &cfg, nil
}

// ReloadConfig re-reads the YAML config. It does so by grabbing an internal write lock
func (c *Config) ReloadConfig() error {
	c.lock.Lock()
	defer func() {
		c.lock.Unlock()
	}()
	yamlFile, err := ioutil.ReadFile(c.cfgFilePath)
	if err != nil {
		return err
	}
	err = yaml.Unmarshal(yamlFile, c)
	if err != nil {
		return err
	}
	return nil
}

// GetDBPassword returns the decrypted DB password
func (c *Config) GetDBPassword() (string, error) {
	c.lock.RLock()
	defer func() {
		c.lock.RUnlock()
	}()
	if c.SimulateDB {
		// Simulating DB - No need to decrypt with KMS
		return c.DBPasswordEncrypted, nil
	}
	if c.dbPassword != "" {
		return c.dbPassword, nil
	}
	cipher, err := base64.StdEncoding.DecodeString(c.DBPasswordEncrypted)
	if err != nil {
		return "", err
	}
	kmsContext := kms.GetContext()
	decryptedPassword, err := kmsContext.Decrypt(string(cipher))
	if err != nil {
		return "", err
	}
	c.dbPassword = decryptedPassword
	return c.dbPassword, nil
}

// Are we running inside a VM?
func (c *Config) LocalMode() bool {
	c.lock.RLock()
	defer func() {
		c.lock.RUnlock()
	}()
	_, present := os.LookupEnv("TF_WORKER_LOCAL_MODE")
	if !present {
		return c.TerraformService.DeployedEnv == "dev"
	}
	return present
}

// GetTFToken returns the decrypted DB password
func (c *Config) GetTFToken() (string, error) {
	c.lock.RLock()
	defer func() {
		c.lock.RUnlock()
	}()
	if c.TerraformService.TfToken != "" {
		return c.TerraformService.TfToken, nil
	}
	cipher, err := base64.StdEncoding.DecodeString(c.TerraformService.TfToken)
	if err != nil {
		return "", err
	}
	kmsContext := kms.GetContext()
	decryptedToken, err := kmsContext.Decrypt(string(cipher))
	if err != nil {
		return "", err
	}
	c.TerraformService.TfToken = decryptedToken
	return c.TerraformService.TfToken, nil
}

// GetAzrSecret returns the decrypted Azure secret
func (c *Config) GetAzrSecret() (string, error) {
	c.lock.RLock()
	defer func() {
		c.lock.RUnlock()
	}()
	if c.TerraformService.azrClientSecret != "" {
		return c.TerraformService.azrClientSecret, nil
	}
	cipher, err := base64.StdEncoding.DecodeString(c.TerraformService.AZRClientSecretEncrypted)
	if err != nil {
		return "", err
	}
	kmsContext := kms.GetContext()
	decryptedToken, err := kmsContext.Decrypt(string(cipher))
	if err != nil {
		return "", err
	}
	c.TerraformService.azrClientSecret = decryptedToken
	return c.TerraformService.azrClientSecret, nil
}
