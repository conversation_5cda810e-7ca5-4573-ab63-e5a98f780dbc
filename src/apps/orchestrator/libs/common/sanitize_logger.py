import re
from scrubadub import Filth
import scrubadub

class SanitizeLogger:
    """
    A proxy class to the logger that will eventually sanitize log messages
    before passing them to the underlying logger.
    """

    def __init__(self, proxy_logger):
        """Initialize the SanitizeLogger with an underlying logger instance."""
        self.logger = proxy_logger
        self.scrubber = scrubadub.Scrubber()
        self.scrubber.add_detector(OtpDetector)

    def set_trace_id(self, uuid):
        """Set the trace ID for the logger."""
        return self.logger.set_trace_id(uuid)

    def set_default_trace_id(self, uuid):
        """Set the default trace ID for the logger."""
        return self.logger.set_default_trace_id(uuid)

    def reset_trace_id(self):
        """Reset the trace ID to the default value."""
        return self.logger.reset_trace_id()

    def info(self, msg, *args, **kwargs):
        sanitized_msg, sanitized_args, sanitized_kwargs = self.sanitize(msg, *args, **kwargs)
        return self.logger.info(sanitized_msg, *sanitized_args, **sanitized_kwargs)

    def error(self, msg, *args, **kwargs):
        sanitized_msg, sanitized_args, sanitized_kwargs = self.sanitize(msg, *args, **kwargs)
        return self.logger.error(sanitized_msg, *sanitized_args, **sanitized_kwargs)

    def debug(self, msg, *args, **kwargs):
        sanitized_msg, sanitized_args, sanitized_kwargs = self.sanitize(msg, *args, **kwargs)
        return self.logger.debug(sanitized_msg, *sanitized_args, **sanitized_kwargs)

    def warning(self, msg, *args, **kwargs):
        sanitized_msg, sanitized_args, sanitized_kwargs = self.sanitize(msg, *args, **kwargs)
        return self.logger.warning(sanitized_msg, *sanitized_args, **sanitized_kwargs)

    def critical(self, msg, *args, **kwargs):
        sanitized_msg, sanitized_args, sanitized_kwargs = self.sanitize(msg, *args, **kwargs)
        return self.logger.critical(sanitized_msg, *sanitized_args, **sanitized_kwargs)

    def exception(self, msg, *args, **kwargs):
        sanitized_msg, sanitized_args, sanitized_kwargs = self.sanitize(msg, *args, **kwargs)
        return self.logger.exception(sanitized_msg, *sanitized_args, **sanitized_kwargs)

    def setLevel(self, level):
        """Set the logging level."""
        return self.logger.setLevel(level)

    def sanitize(self, msg, *args, **kwargs):
        # Sanitize the message
        sanitized_msg = self.scrubber.clean(str(msg))

        # Sanitize all positional arguments
        sanitized_args = []
        for arg in args:
            sanitized_arg = self.scrubber.clean(str(arg))
            sanitized_args.append(sanitized_arg)

        # Sanitize all keyword arguments
        sanitized_kwargs = {}
        for key, value in kwargs.items():
            # Special handling for 'extra' parameter used by Python logging
            if key == 'extra' and isinstance(value, dict):
                # Sanitize the values within the extra dictionary while preserving structure
                sanitized_extra = {}
                for extra_key, extra_value in value.items():
                    sanitized_extra[extra_key] = self.scrubber.clean(str(extra_value))
                sanitized_kwargs[key] = sanitized_extra
            else:
                sanitized_value = self.scrubber.clean(str(value))
                sanitized_kwargs[key] = sanitized_value

        return sanitized_msg, tuple(sanitized_args), sanitized_kwargs


class OtpFilth(Filth):
    type = 'otp'

    def replace_with(self, replace_with='placeholder', **kwargs):
        """Define what to replace the OTP with."""
        return 'cert_fetch_otp={{OTP}}'

class OtpDetector(scrubadub.detectors.RegexDetector):
    name = 'otp'
    filth_cls = OtpFilth
    autoload = True
    regex = re.compile(
        r'cert_fetch_otp=[a-z0-9\-]+',
        re.IGNORECASE
    )