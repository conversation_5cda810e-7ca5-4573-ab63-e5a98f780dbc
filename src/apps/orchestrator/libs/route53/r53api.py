#!/usr/bin/python2.7 -t
import sys, os
import json
import boto3
import time
import sched
import random
import traceback
import re
from struct import *
import logging, logging.handlers
import copy
from botocore.exceptions import ClientError

from libs.common.shared.grpc.proto.avisarpb.avisar_pb2_client import METRIC_SEVERITY_CRITICAL, \
    ORCHESTRATION_INSTANCE_ROUTE53_CLEANUP_FAILED
from libs.common.utils import publish_avisar_event_with_tenant_region_nodetype_key
from libs.route53.r53api_test import mock
from libs.cfg import *
from libs.common.shared.utils import get_aws_partition
from libs.apis.api_utils import is_aws_fedramp_env_govcloud, get_route53_client_for_fedramp
from libs.common.shared.utils import get_r53_aws_partition
from libs.common.shared.r53_utils import publish_r53_alert_to_cosmos

MAX_R53_RECORDS = 300
gw_pattern = re.compile(r'((us|eu|ap|sa)-(east|west|south|northeast|southeast|central)-\d)-(.*)')
AWS_PARTITION = get_aws_partition()

class R53Exception(Exception):
    # define Error code
    DeleteHealthCheckError = 0
    DeleteFQDNError = 1
    UpdateFQDNError = 2
    def __init__(self, code, *args):
        self.code = code
        super(R53Exception, self).__init__(*args)


class PanR53Context(object):
    def __init__(self, gpcs_base, r53_account, logger, db_h, acct_id="0"):
        self.r53_zone_name = gpcs_base
        self.r53_account = r53_account
        self.logger = logger
        self.sts_client = boto3.client('sts')
        self.acct_id = acct_id
        self.dbh = db_h
        if is_aws_fedramp_env_govcloud(logger):
            self.account = cfg['acct_id']
        else:
            self.account = self.sts_client.get_caller_identity()["Arn"].split(':')[4]

        AWS_R53_PARTITION = get_r53_aws_partition()
        self.r53_role_arn = "arn:%s:iam::%s:role/Route53_Policy_Export" % (AWS_R53_PARTITION, self.r53_account)
        self.r53_client = self.r53_client_construct()
        self.r53_zone = self.r53_get_zone_id()
        self.region_mapping = {}
        self.__get_gp_region_mapping()

    def __get_gp_region_mapping(self):
        retry = 5
        res = None
        while retry > 0:
            retry -= 1
            sql = ("SELECT gpcs_region_mapping.region_id, types_master.name "
                   "FROM gpcs_region_mapping LEFT JOIN types_master "
                   "ON gpcs_region_mapping.gw_location=types_master.id")
            try:
                cursor = self.dbh.get_cursor(prepared=True)
                cursor.execute(sql)
                res = cursor.fetchall()
                break
            except Exception as ex:
                self.logger.error("Fail to get GP region mapping. Exception: %s" % str(ex))
            finally:
                self.dbh.cursorclose(cursor)
        if retry == 0:
            raise Exception("Fail to get GP region mapping")

        if res is None:
            raise Exception("Fail to get GP region mapping")

        if len(res) == 0:
            raise Exception("Fail to get GP region mapping")
        
        for row in res:
            region, location_name = row
            self.region_mapping[region] = str(location_name)

    def r53_client_construct(self):
        if self.account != self.r53_account:
            if is_aws_fedramp_env_govcloud(self.logger):
                r53_client = get_route53_client_for_fedramp(self.logger)
            else:
                assumedRole = self.sts_client.assume_role(
                                 RoleArn = self.r53_role_arn,
                                 RoleSessionName="AssumeRoleForRoute53")
                credentials = assumedRole['Credentials']
                r53_client = boto3.client('route53',
                                    aws_access_key_id = credentials['AccessKeyId'],
                                    aws_secret_access_key = credentials['SecretAccessKey'],
                                    aws_session_token = credentials['SessionToken'])
        else:
            r53_client = boto3.client('route53')
        return r53_client

    def r53_get_zone_id(self):
        dns_name = '%s.' % self.r53_zone_name
        retry = 10
        throttling = 0
        default_wait = 10

        while retry:
            retry -= 1
            try:
                response = self.r53_client.list_hosted_zones_by_name(
                    DNSName=dns_name
                )
                alert_log = {'dns_req': dns_name}
                publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", True, action='list_hosted_zones_by_name', dbh=self.dbh)
                break
            except ClientError as err:
                self.logger.error(f"boto3 ClientError: {err}")
                # boto3 exception
                code = err.response['Error'].get('Code', 'Unknown')
                msg = err.response['Error'].get('Message', 'Unknown Error')
                self.logger.error(msg)
                if code == 'Throttling':
                    # route 53 is busy
                    self.logger.exception('r53 api rate limited')
                    if throttling < 4:
                        throttling += 1
                    else:
                        alert_log = {'dns_req': dns_name, 'error_msg':'r53 api rate limited'}
                        publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='list_hosted_zones_by_name', dbh=self.dbh)

                time.sleep(default_wait * (1 << throttling))
                continue
            if retry == 0:
                self.logger.error("Failed to get ListHostedZonesByName")
                alert_log = {'dns_req': dns_name, 'error_msg':'Failed to get ListHostedZonesByNam'}
                publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='list_hosted_zones_by_name', dbh=self.dbh)
                raise Exception("Failed to get ListHostedZonesByName")

        if not response['HostedZones']:
            err_msg = "gpcs domain %s is not registered" % self.r53_zone_name
            self.logger.error(err_msg)
            alert_log = {'dns_req': dns_name, 'error_msg': err_msg}
            publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='list_hosted_zones_by_name', dbh=self.dbh)
            raise Exception(err_msg)

        for z in response['HostedZones']:
            if z['Name'] == dns_name:
                self.logger.info('zone id for domain %s is: %s' % (str(dns_name), str(z['Id'])))
                return z['Id']

        err_msg = "gpcs domain %s is not registered" % self.r53_zone_name
        self.logger.error(err_msg)
        alert_log = {'dns_req': dns_name, 'error_msg': err_msg}
        publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='list_hosted_zones_by_name', dbh=self.dbh)
        raise Exception(err_msg)

    @mock
    def __r53_delete_health_check(self, hc_id):
        return self.r53_client.delete_health_check(HealthCheckId=hc_id)

    def deleteHealthCheck(self, hc_id):
        self.logger.info('Deleting HealthCheck %s' % hc_id)
        retry = 10
        throttling = 0
        default_wait = 10

        while retry:
            retry -= 1
            if self.r53_client is None:
                self.r53_client = self.r53_client_construct()
            try:
                response = self.__r53_delete_health_check(hc_id)
                alert_log = {'dns_req': hc_id}
                publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", True, action='delete_health_check', dbh=self.dbh)
                break
            except ClientError as err:
                # boto3 exception
                code = err.response['Error'].get('Code', 'Unknown')
                msg = err.response['Error'].get('Message', 'Unknown Error')
                self.logger.error(msg)
                if code == 'Throttling':
                    # route 53 is busy
                    self.logger.exception('r53 api rate limited')
                    if throttling < 4:
                        throttling += 1
                    else:
                        alert_log = {'dns_req': hc_id, 'error_msg':'r53 api rate limited'}
                        publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='delete_health_check', dbh=self.dbh)
                elif ((code == 'ExpiredToken') or (code == 'InvalidClientTokenId')):
                    throttling = 0
                    # reset r53 context and retry
                    self.logger.info('resetting r53 context, retry')
                    self.r53_client = None
                    continue
                elif code == 'NoSuchHealthCheck':
                    self.logger.info('Heath Check %s does not exist' % hc_id)
                    alert_log = {'dns_req': hc_id, 'error_msg':'Health Check does not exist'}
                    publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='delete_health_check', dbh=self.dbh)
                    return
                else:
                    # other errors, failed
                    throttling = 0
                    alert_log = {'dns_req': hc_id, 'error_msg':'Failed to delete healthcheck'}
                    publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='delete_health_check', dbh=self.dbh)
                    self.logger.exception('fail to delete healthcheck for %s' % hc_id)

                time.sleep(default_wait * (1 << throttling))
                continue
            except Exception as e:
                throttling = 0
                self.logger.exception('fail to delete healthcheck for %s' % hc_id)
                alert_log = {'dns_req': hc_id, 'error_msg':'Failed to delete healthcheck'}
                publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='delete_health_check', dbh=self.dbh)
                time.sleep(default_wait)
                continue

        if retry == 0:
            msg = 'fail to delete healthcheck for %s' % hc_id
            alert_log = {'dns_req': hc_id, 'error_msg': msg}
            publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='delete_health_check', dbh=self.dbh)
            raise R53Exception(R53Exception.DeleteHealthCheckError, msg)

    @mock
    def __r53_list_resource_record_sets(self, node_fqdn, maxItems):
        return self.r53_client.list_resource_record_sets(
                                 HostedZoneId = self.r53_zone,
                                 StartRecordName = node_fqdn,
                                 MaxItems=str(maxItems))
    def __get_fqdn_record(self, node_fqdn, maxItems = MAX_R53_RECORDS):
        if not node_fqdn.endswith('.'):
            node_fqdn = node_fqdn + '.'

        retry = 10
        throttling = 0
        default_wait = 10

        response = {'ResourceRecordSets':[]}
        while retry:
            retry -= 1
            # check r53 context
            if self.r53_client is None:
                self.r53_client = self.r53_client_construct()
            try:
                response = self.__r53_list_resource_record_sets(node_fqdn, maxItems)
                alert_log = {'response': str(response), 'dns_req': str(node_fqdn), 'maxItems': str(maxItems)}
                publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", True, action='list_resource_record_sets', dbh=self.dbh)
                break
            except ClientError as err:
                # boto3 exception
                code = err.response['Error'].get('Code', 'Unknown')
                msg = err.response['Error'].get('Message', 'Unknown Error')
                self.logger.error(msg)
                if code == 'Throttling':
                    # route 53 is busy
                    self.logger.exception('r53 api rate limited')
                    if throttling < 4:
                        throttling += 1
                    else:
                        alert_log = {'response': str(response), 'dns_req': str(node_fqdn), 'maxItems': str(maxItems), 'error_msg': 'r53 api rate limited'}
                        publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='list_resource_record_sets', dbh=self.dbh)
                elif ((code == 'ExpiredToken') or (code == 'InvalidClientTokenId')):
                    # reset r53 context and retry
                    throttling = 0
                    self.logger.info('resetting r53 context, retry')
                    self.r53_client = None
                    continue
                else:
                    # other errors, failed
                    throttling = 0
                    error_msg = 'fail to get record sets for %s' % node_fqdn
                    self.logger.error(error_msg)
                    self.logger.exception(str(err))
                    alert_log = {'response': str(response), 'dns_req': str(node_fqdn), 'maxItems': str(maxItems), 'error_msg': error_msg}
                    publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='list_resource_record_sets', dbh=self.dbh)
                # sleep and retry
                time.sleep(default_wait * (1 << throttling))
                continue
            except Exception as e:
                error_msg = 'fail to get record sets for %s' % node_fqdn
                self.logger.error(error_msg)
                alert_log = {'response': str(response), 'dns_req': str(node_fqdn), 'maxItems': str(maxItems), 'error_msg': error_msg}
                publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='list_resource_record_sets', dbh=self.dbh)
                time.sleep(default_wait)
                continue
 
        if retry == 0:
            self.logger.error('Fail to get resource recodr set for %s' % node_fqdn)
            alert_log = {'response': str(response), 'dns_req': str(node_fqdn), 'maxItems': str(maxItems), 'error_msg': 'Fail to get resource record set'}
            publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='list_resource_record_sets', dbh=self.dbh)
            return []

        node_fqdns = [r for r in response['ResourceRecordSets'] if r['Name'].lower() == node_fqdn.lower()]
        return node_fqdns


    def __get_alias_fqdns(self, node_fqdn, region):
        gw_fqdn = 'gw.%s' % self.r53_zone_name
        if node_fqdn.endswith(gw_fqdn):
            # this is gateway
            result = gw_pattern.match(node_fqdn)
            if result is not None:
                base_tokens = result.group(4)
                #primary_gw = 'primary-' + base_tokens
                #secondary_gw = 'secondary-' + base_tokens
                region_prefix = self.region_mapping.get(region)
                if region_prefix is not None:
                    gw_fqdn = region_prefix + '-' + base_tokens
                    return [gw_fqdn]
                else:
                    return []
            else:
                return []
        else:
            # this is portal
            base_tokens = node_fqdn.split('.')
            portal = '.'.join(base_tokens[1:])
            return [portal]

    def __get_alias_rrs(self, node_fqdn, region):
        alias_fqdns = self.__get_alias_fqdns(node_fqdn, region)
        self.logger.info(f'All alias records: {alias_fqdns}')
        alias_rrs = []
        for f in alias_fqdns:
            self.logger.info(f'Checking alias FQDN: {f}')
            rrs = self.__get_fqdn_record(f)
            alias_rrs.extend(rrs)
        return alias_rrs

    @mock
    def __r53_change_resource_record_sets(self, dns_req):
        return self.r53_client.change_resource_record_sets(
                               HostedZoneId = self.r53_zone,
                               ChangeBatch = dns_req)
    def __deleteFQDN(self, fqdn_rrs):
        dns_req = {
            'Changes': [
            ]
        }
       
        if len(fqdn_rrs) == 0:
            self.logger.info('No FQDNs to be removed')
            return

        for r in fqdn_rrs:
            fqdn_req = {
                'Action': 'DELETE',
                'ResourceRecordSet': r
            }
            dns_req['Changes'].append(fqdn_req)

        self.logger.info('FQDN request: %s' % str(dns_req))

        retry = 10
        throttling = 0
        default_wait = 10

        while retry:
            retry -= 1
            if self.r53_client is None:
                self.r53_client = self.r53_client_construct()
            try:
                response = self.__r53_change_resource_record_sets(dns_req)
                self.logger.info('FQDN record %s is removed' % str(fqdn_rrs))
                alert_log = {'dns_req': dns_req}
                publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", True, action='change_resource_record_sets', dbh=self.dbh)
                break
            except ClientError as err:
                # boto3 exception
                code = err.response['Error'].get('Code', 'Unknown')
                msg = err.response['Error'].get('Message', 'Unknown Error')
                self.logger.error(msg)
                if code == 'Throttling':
                    # route 53 is busy
                    self.logger.exception('r53 api rate limited')
                    if throttling < 4:
                        throttling += 1
                    else:
                        alert_log = {'dns_req': dns_req, 'error_msg':'r53 api rate limited'}
                        publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='change_resource_record_sets', dbh=self.dbh)
                elif ((code == 'ExpiredToken') or (code == 'InvalidClientTokenId')):
                    # reset r53 context and retry
                    throttling = 0
                    self.logger.info('resetting r53 context, retry')
                    self.r53_client = None
                    continue
                elif code == 'InvalidChangeBatch':
                    # no fqdn found
                    self.logger.info('Invalid FQDN batch: %s', str(dns_req))
                    alert_log = {'dns_req': dns_req, 'error_msg':'Invalid FQDN batch'}
                    publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='change_resource_record_sets', dbh=self.dbh)
                    if self.dbh is not None:
                        publish_avisar_event_with_tenant_region_nodetype_key(self.dbh.avctx, self.logger,
                                                                             ORCHESTRATION_INSTANCE_ROUTE53_CLEANUP_FAILED,
                                                                             "Invalid FQDN batch",
                                                                             METRIC_SEVERITY_CRITICAL)
                    return
                else:
                    # other errors, failed
                    throttling = 0
                    self.logger.exception(str(err))
                    alert_log = {'dns_req': dns_req, 'error_msg':str(err)}
                    publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='change_resource_record_sets', dbh=self.dbh)
                # sleep and retry
                time.sleep(default_wait * (1 << throttling))
                continue
            except Exception as e:
                self.logger.error('Fail to remove FQDN record %s' % str(dns_req))
                self.logger.exception(str(e))
                time.sleep(default_wait)
                alert_log = {'dns_req': dns_req, 'error_msg':str(e)}
                publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='change_resource_record_sets', dbh=self.dbh)
                continue

        if retry == 0:
            msg = 'Fail to remove FQDN record %s' % str(dns_req)
            alert_log = {'dns_req': dns_req, 'error_msg': 'Fail to remove FQDN record'}
            publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='change_resource_record_sets', dbh=self.dbh)
            publish_avisar_event_with_tenant_region_nodetype_key(self.dbh.avctx, self.logger,
                                                                 ORCHESTRATION_INSTANCE_ROUTE53_CLEANUP_FAILED,
                                                                 "Fail to remove FQDN record",
                                                                 METRIC_SEVERITY_CRITICAL)
            raise R53Exception(R53Exception.DeleteFQDNError, msg)

    def deleteRNSCFQDN(self, node_fqdn):
        try:
            fqdn_rrs = self.__get_fqdn_record(node_fqdn)
            self.logger.info('Removing RN/SC Node FQDNs: %s' % str(fqdn_rrs))
            # delete all records with node FQDN
            self.__deleteFQDN(fqdn_rrs)
        except Exception as e:
            self.logger.exception("Failed to remove RN/SC Node FQDNs."
                                    "Exception: %s" % e)

    def deleteNodeFQDNPrivateRegion(self, node_fqdn, is_ipv6=False):
        # This function deletes the private region FQDN entry for  IPv4 if
        # SetIdentifier is a special keyword 'sase-private-region'
        fqdn_rrs = self.__get_fqdn_record(node_fqdn)
        self.logger.info('Node FQDNs: %s' % str(fqdn_rrs))
        rrs_to_deleted = []
        rr_identifier = "sase-private-region"

        for r in fqdn_rrs:
            if (rr_identifier in r['SetIdentifier']):
                # this is myself
                self.logger.info('Removing FQDN record %s' % str(r))
                rrs_to_deleted.append(r)
            else:
                self.logger.info(f'Ignoring FQDN record {r}')

        # delete all related FQDNs
        self.__deleteFQDN(rrs_to_deleted)

    def deleteNodeFQDN(self, node_fqdn, vmid, public_ip, region, is_nlb_ip, is_ipv6=False):
        # This function deletes the compute location FQDN entry for both IPv6 and IPv4 if
        # is_ipv6 is false since the lookup is done based on vmid in SetIdentifier
        # If is_ipv6 = True, we need to only disable for IPv6 stack and also delete the
        # Alias record created (Alias record is created only in the case of AWS)
        fqdn_rrs = self.__get_fqdn_record(node_fqdn)
        self.logger.info('Node FQDNs: %s' % str(fqdn_rrs))
        delete_ipv6_alias_record = False
        other_fqdn_rrs = []
        rrs_to_deleted = []
        for r in fqdn_rrs:
            if is_ipv6:
                if "ocid" in r.get('SetIdentifier', ''):
                    self.logger.info('deleteNodeFQDN : Attempting removing OCI FQDN record %s' % str(r))
                    # find the truncate IPv6 IP in SetIdentifier
                    if r['SetIdentifier'].split('-')[-1] in public_ip:
                        self.logger.info('deleteNodeFQDN: Removing OCI FQDN record %s' % str(r))
                        rrs_to_deleted.append(r) 
                    else:
                        self.logger.info(f'deleteNodeFQDN : OCI resource record details {r["SetIdentifier"]} does not match {public_ip}')
                        other_fqdn_rrs.append(r)
                        delete_ipv6_alias_record=True
                else:
                    if ((public_ip in r['SetIdentifier']) and (re.search(
                            r'[a-zA-Z0-9-]%s|%s[a-zA-Z0-9-]' % (public_ip, public_ip), r['SetIdentifier']) is not None)):
                        self.logger.info('Removing FQDN record %s' % str(r))
                        rrs_to_deleted.append(r)
                    else:
                        self.logger.info(f'resource record details {r["SetIdentifier"]} does not match {public_ip}')
                        other_fqdn_rrs.append(r)
                        delete_ipv6_alias_record=True
            else:
                if (((vmid in r['SetIdentifier']) and (re.search(
                        r'[a-zA-Z0-9]%s|%s[a-zA-Z0-9]' % (vmid, vmid), r['SetIdentifier']) is None))
                        or
                        (not is_nlb_ip and
                         ((public_ip in r['SetIdentifier']) and (re.search(
                            r'[a-zA-Z0-9]%s|%s[a-zA-Z0-9]' % (public_ip, public_ip), r['SetIdentifier']) is None)))):
                    # this is myself
                    self.logger.info('Removing FQDN record %s' % str(r))
                    rrs_to_deleted.append(r)
                else:
                    self.logger.info(f'Ignoring FQDN record {r}')
                    other_fqdn_rrs.append(r)
        
        if delete_ipv6_alias_record or len(other_fqdn_rrs) == 0:
            # no more fqdn_names exist, removing the alias targets
            fqdn_rrs = self.__get_alias_rrs(node_fqdn, region)
            self.logger.info('Node Alias FQDNs: %s' % str(fqdn_rrs))
            for r in fqdn_rrs:
                alias_target = r.get('AliasTarget', {'DNSName':''})['DNSName'].rstrip('.')
                if alias_target == node_fqdn:
                    self.logger.info(f"Got resource record {r}")
                    if delete_ipv6_alias_record:
                        if r.get('Type') == 'AAAA':
                            self.logger.info(f"Adding alias target {alias_target} for type {r.get('Type')} to "
                                             f"the delete record list")
                            rrs_to_deleted.append(r)
                        else:
                            self.logger.info(f"Skipping alias target {alias_target}")
                    else:
                        self.logger.info(f"Adding alias target {alias_target} to the delete record list")
                        rrs_to_deleted.append(r)

        # delete all related FQDNs
        self.logger.info(f"deleteNodeFQDN rrs_to_deleted {rrs_to_deleted}")
        self.__deleteFQDN(rrs_to_deleted)

    def BatchDeleteNodeFQDN(self, egress_loc_fqdn_ip_dict, is_nlb_ip, is_ipv6=False):
        rrs_to_deleted = []
        for loc, info in list(egress_loc_fqdn_ip_dict.items()):
            other_fqdn_rrs = []
            fqdn_rrs = self.__get_fqdn_record(info["fqdn"])
            self.logger.info('Node FQDNs: %s' % str(fqdn_rrs))
            for r in fqdn_rrs:
                if is_ipv6:
                    if ((info["ip"] in r['SetIdentifier']) and (re.search(
                            r'[a-zA-Z0-9-]%s|%s[a-zA-Z0-9-]' % (info["ip"], info["ip"]), r['SetIdentifier']) is not None)):
                        self.logger.info('Removing FQDN record %s' % str(r))
                        rrs_to_deleted.append(r)
                    else:
                        self.logger.info(f'resource record details {r["SetIdentifier"]} does not match {info["ip"]}')
                        other_fqdn_rrs.append(r)
                else:
                    if (((info["vmid"] in r['SetIdentifier']) and (re.search(
                            r'[a-zA-Z0-9]%s|%s[a-zA-Z0-9]' % (info["vmid"], info["vmid"]), r['SetIdentifier']) is None))
                            or
                            (not is_nlb_ip and
                             ((info["ip"] in r['SetIdentifier']) and (re.search(
                                r'[a-zA-Z0-9]%s|%s[a-zA-Z0-9]' % (info["ip"], info["ip"]), r['SetIdentifier']) is None)))):
                        # this is myself
                        self.logger.info('Removing FQDN record %s' % str(r))
                        rrs_to_deleted.append(r)
                    else:
                        self.logger.info(f'Ignoring FQDN record {r}')
                        other_fqdn_rrs.append(r)

            if len(other_fqdn_rrs) == 0:
            # no more fqdn_names exist, removing the alias targets
                fqdn_rrs = self.__get_alias_rrs(info["fqdn"], info["region"])
                self.logger.info('Node Alias FQDNs: %s' % str(fqdn_rrs))
                for r in fqdn_rrs:
                    alias_target = r.get('AliasTarget', {'DNSName':''})['DNSName'].rstrip('.')
                    if alias_target == info["fqdn"]:
                        self.logger.info('Removing Alias FQDN record %s' % str(r))
                        rrs_to_deleted.append(r)
  
        # delete all related FQDNs
        self.__deleteFQDN(rrs_to_deleted)


    def __change_tags_for_resource(self, hc_id, hc_name, lb_ip):
          return self.r53_client.change_tags_for_resource(
                       ResourceType = 'healthcheck',
                       ResourceId = hc_id,
                       AddTags = [{'Key': 'Name', 'Value': hc_name[:63]},
                                  {'Key': 'LoadBalancerIP', 'Value': lb_ip}]
                 )


    def __r53_create_health_check(self, call_reference, healthCheckConfig):
        return self.r53_client.create_health_check(
                       CallerReference = call_reference,
                       HealthCheckConfig = healthCheckConfig)


    def __createHealthCheck(self, hc_name, health_check_config, create_tag):
        retry = 5
        throttling = 0
        default_wait = 10
        callReference = hc_name[:48] + str(random.randint(1, 1024))
        health_check_id = None
        while retry:
            if self.r53_client is None:
                self.r53_client = self.r53_client_construct()
            try:
                response = self.__r53_create_health_check(callReference,
                                                          health_check_config)
                health_check_id = response['HealthCheck']['Id']
                self.logger.info('HealthCheck %s is created for callReference %s'
                                 % (health_check_id, callReference))
                alert_log = {'response': str(response), 'CallerReference': str(callReference),'HealthCheckConfig': str(health_check_config)}
                publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", True, action='create_health_check', dbh=self.dbh)
                if health_check_id and create_tag:
                    # create tag for resource
                    self.logger.info('Creating tags for healthcheck %s' % health_check_id)
                    response = self.__change_tags_for_resource(health_check_id,
                                                               hc_name,
                                                               health_check_config.get('IPAddress'))
                    self.logger.info('Tags for healthcheck %s are created' % health_check_id)
                    alert_log = {'response': str(response), 'ResourceType' : 'heathcheck','ResourceId': str(health_check_id)}
                    publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", True, action='change_tags_for_resource', dbh=self.dbh)
                break
            except ClientError as err:
                retry -= 1
                # boto3 exception
                self.logger.error('Failed to create HealthCheck for %s with callReference %s'
                                  % (hc_name, callReference))
                code = err.response['Error'].get('Code', 'Unknown')
                msg = err.response['Error'].get('Message', 'Unknown Error')
                self.logger.info('Error code: %s' % code)
                self.logger.error(msg)
                if code == 'Throttling':
                    # route 53 is busy
                    self.logger.exception('r53 api rate limited')
                    if throttling < 4:
                        throttling += 1
                    else:
                        alert_log = {'dns_req': str(health_check_id), 'error_msg':'r53 api rate limited'}
                        publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='create_health_check', dbh=self.dbh)
                elif code == 'HealthCheckAlreadyExists':
                    throttling = 0
                    callReference = hc_name[:48] + '-' + str(random.randint(1, 1024))
                else:
                    throttling = 0
                    # other errors, failed
                    self.logger.exception(str(err))
                    alert_log = {'dns_req': str(health_check_id), 'error_msg':str(err)}
                    publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='create_health_check', dbh=self.dbh)
                time.sleep(default_wait * (1 << throttling))
                continue
            except Exception as err:
                retry -= 1
                throttling = 0
                self.logger.exception('Failed to create healthcheck for %s', hc_name)
                alert_log = {'dns_req': str(health_check_id), 'error_msg':str(err)}
                publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='create_health_check', dbh=self.dbh)
                time.sleep(default_wait)
                continue

        if retry == 0:
            alert_log = {'dns_req': str(health_check_id), 'error_msg':"Failed to create healthcheck"}
            publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='create_health_check', dbh=self.dbh)
            raise Exception("Fail to create healthcheck for %s" % hc_name)

        return health_check_id


    def createHealthCheck(self, identifier, health_check_ip):
        healthCheckConfig = {
            'IPAddress':        health_check_ip,
            'Port':             443,
            'Type':             'TCP',
            'RequestInterval':  30,
        }

        name = "%s-orchestrator" % identifier
        self.logger.info("Create health check for endpoint %s, ip is %s" % (name, health_check_ip))
        # create healthcheck
        hc_id = self.__createHealthCheck(name, healthCheckConfig, True)
        self.logger.info("Returning health check id: %s" % hc_id)
        return hc_id


    def __updateFQDN(self, fqdn_rrs):
        # Update fqdn into AWS Route53
        if len(fqdn_rrs) == 0:
            self.logger.info("No FQDNs to be updated")
            return

        dns_req = {
            'Changes': [
                {
                    'Action': 'UPSERT',
                    'ResourceRecordSet': fqdn_rrs
                }
            ]
        }
        self.logger.info('FQDN request: %s' % str(dns_req))

        retry = 10
        throttling = 0
        default_wait = 10

        while retry:
            retry -= 1
            if self.r53_client is None:
                self.r53_client = self.r53_client_construct()
            try:
                response = self.__r53_change_resource_record_sets(dns_req)
                self.logger.info('FQDN record %s is updated' % str(fqdn_rrs))
                alert_log = {'dns_req': dns_req}
                publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", True, action='change_resource_record_sets', dbh=self.dbh)
                break
            except ClientError as err:
                # boto3 exception
                code = err.response['Error'].get('Code', 'Unknown')
                msg = err.response['Error'].get('Message', 'Unknown Error')
                self.logger.error(msg)
                if code == 'Throttling':
                    # route 53 is busy
                    self.logger.exception('r53 api rate limited')
                    if throttling < 4:
                        throttling += 1
                    else:
                        alert_log = {'dns_req': dns_req, 'error_msg':'r53 api rate limited'}
                        publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='change_resource_record_sets', dbh=self.dbh)
                elif ((code == 'ExpiredToken') or (code == 'InvalidClientTokenId')):
                    # reset r53 context and retry
                    throttling = 0
                    self.logger.info('resetting r53 context, retry')
                    self.r53_client = None
                    continue
                elif code == 'InvalidChangeBatch':
                    # no fqdn found
                    self.logger.info('Invalid FQDN batch: %s', str(dns_req))
                    alert_log = {'dns_req': dns_req, 'error_msg':'Invalid FQDN batch'}
                    publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='change_resource_record_sets', dbh=self.dbh)
                    return
                else:
                    # other errors, failed
                    throttling = 0
                    self.logger.exception(str(err))
                    alert_log = {'dns_req': dns_req, 'error_msg':str(err)}
                    publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='change_resource_record_sets', dbh=self.dbh)
                # sleep and retry
                time.sleep(default_wait * (1 << throttling))
                continue
            except Exception as e:
                self.logger.error('Fail to update FQDN record %s' % str(dns_req))
                self.logger.exception(str(e))
                time.sleep(default_wait)
                cosmos_logmsg = 'Fail to update FQDN record %s' % str(e)
                alert_log = {'dns_req': dns_req, 'error_msg': cosmos_logmsg}
                publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='change_resource_record_sets', dbh=self.dbh)
                continue

        if retry == 0:
            msg = 'Fail to update FQDN record %s' % str(dns_req)
            alert_log = {'dns_req': dns_req, 'error_msg': 'Fail to update FQDN record'}
            publish_r53_alert_to_cosmos(self.logger, str(alert_log), str(self.acct_id), str(self.acct_id), "orchestrator", False, action='change_resource_record_sets', dbh=self.dbh)
            raise R53Exception(R53Exception.UpdateFQDNError, msg)


    def __get_r53_fqdn_record_for_update(self, fqdn, address, health_check_id):
        ttl = 60
        weight = 200
        set_id = "%s-%s" % (fqdn[:4], address)
        rrs = {
          'Type': 'A',
          'Name': fqdn,
          'TTL':  ttl,
          'Weight': weight,
          'SetIdentifier': set_id,
          'ResourceRecords': [ {'Value': address} ],
        }
        if health_check_id:
            rrs['HealthCheckId'] = health_check_id

        return rrs


    def updateNodeFQDN(self, node_fqdn, public_ip, health_check_id):
        fqdn_rrs = self.__get_r53_fqdn_record_for_update(node_fqdn, public_ip,
                                                         health_check_id)
        self.logger.info('Node FQDNs: %s' % str(fqdn_rrs))

        # Update the FQDNs in AWS Route53
        self.__updateFQDN(fqdn_rrs)

