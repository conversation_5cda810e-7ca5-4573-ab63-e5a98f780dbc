#!/usr/bin/python
import sqlite3
import mysql.connector
import sys
import json, time, datetime
import traceback
import ipaddress
from base64 import b64decode
import boto3
import hashlib
import re

from googleapiclient.discovery import build

from libs.common.shared.grpc.proto.avisarpb.avisar_pb2_client import ORCHESTRATION_INSTANCE_CREATION_FAILED, \
    METRIC_SEVERITY_CRITICAL
from libs.model.orchjobs import OrchJobs
from libs.model.custmodel import CustomerModel
from libs.model.custEpaasConfigModel import CustEpaasConfigModel
from libs.model.vpcmodel import VpcModel
from libs.model.nodetypemodel import NodeTypeModel
from libs.model.instancemodel import InstanceModel
from libs.model.instancemodel import get_mapping_for_firewall_summary_table, is_ingress_ip_reduction_enabled
from libs.msg_defs.interface import *
from libs.cfg import *
import libs.route53.r53api as r53
from libs.common.utils import prepare_job_status_msg, is_dual_egress_nic_supported, \
    publish_avisar_event_with_tenant_region_nodetype_key
from libs.common.shared.utils import get_cdl_region_info, is_no_passive_instances_enabled, is_china_env, is_swg_aws_enabled
from libs.common.shared.sys_utils import *
from libs.apis.api_utils import is_env_fedramp_il5
from libs.cloud_providers.common.instance_param_mgmt import get_salt_profile_for_instance_entry
from libs.cloud_providers.common.instance_param_mgmt import get_salt_profile_name_for_instance_entry
from libs.cloud_providers.common.masque_helper import is_masque_feature_enabled
from libs.cloud_providers.common.swgproxy_bringup import swgproxy_bringup
from libs.apis.region_master_api import *
from libs.model.orchcfgmodel_v2 import OrchCfgModel_v2 as OrchCfgModel
from libs.common.otp import generate_otp
from libs.model.custSiteSpecificCfgModel import custSiteSpecificCfgModel
from libs.common.shared.base_domain import get_portal_base
from libs.common.shared.pba_utils import is_dhcp_pool_allocation_enabled, is_pba_enabled
from libs.common.shared.frr_utils import is_frr_enabled_from_ff
from packaging.version import  Version
from libs.model.aggbandwidthmgmtmodel import AggBandwidthMgmtModel
from libs.common.shared.db_lambda_triggers import config_commit_alert_notify as config_commit_al_notify
from libs.feature_flags.rnsc_fqdn_feature_flags_utils import saas_agent_version_supports_fqdn, plugin_version_supports_fqdn
from libs.common.utils import mu_sc_mapping_feature_flag_enabled
from libs.common.shared.gcp_utils import gcp_authenticate
from libs.common.shared.grpc.proto.avisarpb.avisar_pb2_client import ORCHESTRATION_INSTANCE_LOOPBACK_IP_FETCH_FAILED
from ngpa_extern.external_utils import is_ciam_enabled_from_ff
from orchestration_service.core.orchestrator_pvt_region_mgmt import setup_private_instance_mgmt_entry,\
    delete_private_instance_mgmt_entry, trigger_priv_inst_mgmt_lb_update

SUBNET = 'subnet'
CLEANPIPE = 152
MU_VERTICAL_SCALE_NODE_TYPES = {NODE_TYPE_GP_GATEWAY}
SHARED_VPC_PROD_PREFIX = 'prod'
SHARED_VPC_PRE_PROD_PREFIX = 'pre-prod'
SHARED_VPC_NON_PROD_PREFIX = 'non-prod'
SHARED_VPC_EMPTY_PREFIX = ""

class DbHandle(): 
    def __init__(self, logger, decrypt_password=False, avctx=None):
        self.logger = logger
        self.avctx = avctx
        self.dbpass = None
        if 'dbpassword_decrypted' not in cfg:
            decrypt_password=True
        if decrypt_password:
            ENCRYPTED = cfg['dbpassword']
            password = boto3.client('kms', cfg['region']).decrypt(
                CiphertextBlob=b64decode(ENCRYPTED))['Plaintext']
            self.dbpass = password.decode('utf-8')
        else:
            self.dbpass = cfg['dbpassword']
        try:
            self.conn = self.connect()
            self.error = ""
        except Exception as ex:
            self.conn = None
            self.logger.error("Failed to open db: %s, Exiting!" % (str(ex)))
            sys.exit(-1)

        # init route53 context
        r53_acct = cfg['route53acct'][1:]  # first character is removed
        self.r53_ctx = r53.PanR53Context(cfg['gp-gw-domain'], r53_acct, self.logger, self)
        self.r53_ctx_prisma = r53.PanR53Context(cfg['gp-gw-domain-prisma'], r53_acct, self.logger, self)

        self.capacity_type = ""
        self.cloud_machine_type = ""
        self.cpu_platform = ""
        self.market_type = ""
        self.dpdk_qcount = 0
        self.gcp_credentials = self.get_gcp_credentials(cfg)

    def get_gcp_credentials(self, cfg):
        return gcp_authenticate(cfg, self.logger)

    def connect(self):
        return mysql.connector.connect(user=cfg['dbuser'],
                                       password=self.dbpass,
                                       host=cfg['dbhost'], database=cfg['dbname'], autocommit=True)


    def cursorclose(self, cursor):
        try:
            cursor.fetchall()
        except Exception as ex:
            pass
        try:
            cursor.fetchwarnings()
        except Exception as ex:
            pass
        try:
            cursor.close()
        except Exception as ex:
            self.logger.error("Cursor close exception %s: " % str(ex))
            pass

    def get_cursor(self, prepared=False, buffered=False):
        try:
            self.conn.ping(reconnect=True, attempts=2)
        except Exception as ex:
            self.logger.error("DB ping raised exception %s: " % str(ex))

            for retry in range(1, 4):
                try:
                    self.conn = mysql.connector.connect(
                        user=cfg['dbuser'], password=cfg['dbpassword'],
                        host=cfg['dbhost'], database=cfg['dbname'],
                        autocommit=True)
                    self.error = ""
                    break
                except Exception as ex:
                    self.logger.error("Failed to open DB connection: %s" % (str(ex)))
                    if (retry >= 3):
                        self.logger.error("Fatal! Failed to open DB connection: %s" % (str(ex)))
                        self.conn = None
                        return
                    else:
                        timeout = ((3 ** retry) * 10)  # 30, 90, 270
                        self.logger.debug('Sleeping for %d seconds' % timeout)
                        time.sleep(timeout)

        cursor = self.conn.cursor(prepared=prepared, buffered=buffered)
        return cursor

    def exec_query(self, query):
        self.logger.debug(str(query))
        ret = []
        cursor = None
        try:
            cursor = self.get_cursor()
            cursor.execute(query)
        except Exception as ex:
            self.logger.error("exec_query failed %s" % (str(ex)))
            self.cursorclose(cursor)
            return DbResult(False, [])
        self.cursorclose(cursor)
        return DbResult(True, [])

    def get_id_from_type_name(self, idtype, name):
        params = (idtype, name)
        ok = False
        ret = []
        res = None
        self.logger.debug("sql = select id from types_master where type = %s and name = %s" % params)
        cursor = self.get_cursor()
        try:
            cursor.execute('select id from types_master where type = %s and name = %s', params)
            res = cursor.fetchone()
        except Exception as ex:
            self.logger.error("get_id_from_type_name failed %s" % (str(ex)))
        if res:
            ret.append(res[0])
            ok = True
        else:
            self.logger.error("get_id_from_type_name No record found")
        self.cursorclose(cursor)
        return DbResult(ok, ret)

    def is_gcp_cnat_supported(self, cloud_provider, custid, compute_region_idx):

        self.logger.info(f"is_gcp_cnat_supported: Checking if GCP CNAT is supported for {custid}, compute region {compute_region_idx}, cloud provider {cloud_provider}")
        gcp_cnat_supported = False
        if cloud_provider != PROVIDER_GCP and cloud_provider != PROVIDER_GCP_DB_ENUM_VALUE:
            return gcp_cnat_supported
        
        params = (custid, compute_region_idx)
        self.logger.info("sql = select is_cnat_supported from cust_nat_mgmt_table where custid = %s and compute_region_id = %s" % params)
        cursor = self.get_cursor()
        try:
            cursor.execute('select is_cnat_supported from cust_nat_mgmt_table where custid = %s and compute_region_id = %s', params)
            res = cursor.fetchone()
        except Exception as ex:
            self.logger.error(f"is_gcp_cnat_supported: failed to get is_cnat_supported for {custid}, compute region {compute_region_idx}, cloud provider {cloud_provider} with exception {ex}")
            self.cursorclose(cursor)
            return gcp_cnat_supported
        if res:
            gcp_cnat_supported = res[0]
        else:
            self.logger.error("is_gcp_cnat_supported: No record found")
        self.cursorclose(cursor)
        return gcp_cnat_supported

    def get_type_name_from_id(self, idval):
        params = (idval,)
        ok = False
        ret = []
        res = None
        self.logger.debug("sql = select type, name from types_master where id = %d" % params)
        cursor = self.get_cursor()
        try:
            cursor.execute('select type, name from types_master where id = %s', params)
            res = cursor.fetchone()
        except Exception as ex:
            self.logger.error("get_id_from_type_name failed %s" % (str(ex)))
        if res:
            type, name = res
            ret.append(name)
            ok = True
        else:
            self.logger.error("get_type_name_from_id: No record found")
        self.cursorclose(cursor)
        return DbResult(ok, ret)

    def get_datetime_from_timestamp(self, timestamp):
        sql = "select from_unixtime(%s) as time"
        params = (timestamp,)
        cursor = self.get_cursor()
        try:
            cursor.execute(sql, params)
            res = cursor.fetchone()
        except Exception as ex:
            self.logger.error("db_handle:get_datetime_from_timestamp(): Unable to "
                              "convert timestamp to datetime: %s" % (str(ex)))
            return None
        if not res:
            self.logger.error("db_handle:get_datetime_from_timestamp(): Invalid "
                              "input %s" % (str(timestamp)))
            return None
        time = res[0]
        return time

    ############### CUSTOMER GET APIs #########################################
    def get_cust_id_from_name(self, custname):
        params = (custname,)
        ok = False
        ret = []
        res = None
        cursor = self.get_cursor()
        try:
            cursor.execute('select id from cust_master where name = %s', params)
            res = cursor.fetchone()
        except Exception as ex:
            self.logger.error("get_cust_id_from_name failed %s" % (str(ex)))
        if res:
            id, = res
            self.logger.debug("get_cust_id_from_name: Found result %s" % str(id))
            ret.append(id)
            ok = True
        else:
            self.logger.error("get_cust_id_from_name: No record found")
        self.logger.info("get_cust_id_from_name: returning %s" % str(ret))
        self.cursorclose(cursor)
        return DbResult(ok, ret)

    def get_cust_name_from_id(self, custid):
        params = (custid,)
        ok = False
        ret = []
        res = None
        cursor = self.get_cursor()
        try:
            cursor.execute('select name from cust_master where id = %s', params)
            res = cursor.fetchone()
        except Exception as ex:
            self.logger.error("get_cust_name_from_id failed %s" % (str(ex)))
        if res:
            name, = res
            ret.append(name)
            ok = True
        else:
            self.logger.error("get_cust_name_from_id: No record found")
        self.cursorclose(cursor)
        return DbResult(ok, ret)

    def  get_cust_sw_version(self, custid):
        params = (custid,)
        self.logger.info("get_cust_sw_version: custid = %s" % (str(custid)))
        ok = False
        ret = []
        res = None
        cursor = self.get_cursor()
        try:
            cursor.execute('select version from cust_master where id = %s', params)
            res = cursor.fetchone()
        except Exception as ex:
            self.logger.error("get_cust_sw_version failed %s" % (str(ex)))
        if res:
            version, = res
            ret.append(version)
            ok = True
        else:
            self.logger.error("get_cust_sw_version: No record found")
        self.cursorclose(cursor)
        return DbResult(ok, ret)

    def get_cust_region(self, custid):
        params = (custid,)
        ok = False
        ret = []
        res = None
        cursor = self.get_cursor()
        try:
            cursor.execute('select primary_region from cust_master where id = %s', params)
            res = cursor.fetchone()
        except Exception as ex:
            self.logger.error("get_cust_region failed: %s" % (str(ex)))
        if res:
            primary_region, = res
            ret.append(primary_region)
            self.logger.debug("ret is %s %s" % (custid, str(ret)))
        else:
            self.logger.error("get_cust_region: No record found")
        self.cursorclose(cursor)
        return DbResult(ok, ret)

    def is_sase_fabric_enabled(self, custid):
        params = (custid,)
        ok = False
        ret = []
        res = None
        cursor = self.get_cursor()
        try:
            cursor.execute('select sase_fabric_support from cust_master where id = %s', params)
            res = cursor.fetchone()
        except Exception as ex:
            self.logger.error("is_sase_fabric_enabled failed: %s" % (str(ex)))
        if res:
            sase_fabric_support, = res
            self.logger.debug("sase_fabric_support is %s for custid %s" % (sase_fabric_support, custid))
            self.cursorclose(cursor)
            return sase_fabric_support == 1
        else:
            self.logger.error("is_sase_fabric_enabled: No record found")
        self.cursorclose(cursor)
        return False
    
    ###############   JOB GET APIS #########################################
    def get_job_all_active_jobs(self):
        retval = []
        ok = False
        ret = None
        res = self.get_id_from_type_name("jobstatus", "DONE")
        if not res.ok:
            self.logger.error("Unable to find id for status DONE")
            return DbResult(False, [])
        done_id = res.result[0]
        res = self.get_id_from_type_name("jobstatus", "FAILED")
        if not res.ok:
            self.logger.error("Unable to find id for status DONE")
            return DbResult(False, [])
        fail_id = res.result[0]
        params = (done_id, fail_id)
        cursor = self.get_cursor()
        try:
            cursor.execute(
                'select jobid,job_type,custid,startts,status,last_status_change_ts,status_msg from jobs_table where status !=  %s and status != %s',
                params)
            ret = cursor.fetchall()
        except Exception as ex:
            self.logger.error("get_job_all_active_jobs failed to select jobs: %s" % (str(ex)))
        if not ret:
            self.logger.error("get_job_all_active_jobs: No record found")
        else:
            for (jobid, job_type, custid, startts, status, last_status_change_ts, status_msg) in ret:
                job = OrchJobs(job_type, custid, status, status_msg, jobid)
                job.last_status_change_ts = last_status_change_ts
                job.status_msg = prepare_job_status_msg(status_msg)
                retval.append(job)
                job = None
                ok = True
        if (len(retval) < 1):
            self.logger.error("get_job_all_active_jobs: No record found")
            ok = False
        self.cursorclose(cursor)
        return DbResult(ok, retval)

    def get_job_by_id(self, jobid):
        ok = False
        ret = None
        res = []
        params = (jobid,)
        cursor = self.get_cursor()
        self.cursorclose(cursor)
        cursor = self.get_cursor()

        try:
            query = "select jobid,job_type,custid,startts,status,last_status_change_ts,status_msg from jobs_table where jobid = %s"
            cursor.execute(query, params)
            ret = cursor.fetchone()
        except Exception as ex:
            self.logger.error("get_job_by_id failed for jobid %s: %s" % (str(jobid), str(ex)))

        if not ret:
            self.logger.error("get_job_by_id no job found with jobid %s" % str(jobid))
        else:
            jobid, job_type, custid, startts, status, last_status_change_ts, status_msg = ret
            job = OrchJobs(job_type, custid, status, status_msg, jobid)
            job.startts = startts
            job.last_status_change_ts = last_status_change_ts
            res.append(job)
            ok = True
        self.cursorclose(cursor)
        return DbResult(ok, res)

    def populate_network_config_for_instance(self, network_cfg, instance_model):
        success = False
        try:
            if network_cfg.get("mgmt.alloc_type") == "static":
                self.logger.info(f"setting management address to {network_cfg['mgmt.ip_address']}")
                instance_model.set_param("mgt_ip", network_cfg["mgmt.ip_address"].split("/")[0])
            if instance_model.get_param("node_type") in [ NODE_TYPE_GP_GATEWAY, NODE_TYPE_SASE_PRIV_REGION_LB,
                                                          NODE_TYPE_REMOTE_NET ]:
                self.logger.info(f"setting the private ip to "
                                 f"{network_cfg['dataplane_outside.ip_address']}")
                instance_model.set_param("pvt_ip", network_cfg["dataplane_outside.ip_address"].split("/")[0])
                # If the node type is local inspection then we don't need to set the lb_details
                if instance_model.get_param("node_type") in [NODE_TYPE_REMOTE_NET]:
                    self.logger.info("Skip setting lb details for local inspection Node")
                else:
                    lb_details_vip_public_private = (f"{network_cfg.get('dataplane_outside.private.vip_address')}:"
                                                     f"{network_cfg.get('dataplane_outside.public.vip_address')}")
                    self.logger.info(f"setting the lb_details to "
                                     f"{network_cfg.get('dataplane_outside.private.vip_address')}")
                    instance_model.set_param("lb_details", lb_details_vip_public_private)
            success = True
        except Exception as E:
            self.logger.error(f"Failed with exception {E.args}, Traceback: {traceback.format_exc()}, "
                              f"locals : {locals()}")
        finally:
            return success

############################# VPC APIs ####################################
    def find_missing_num(self, numbers, end=255):
        """
            This function returns the missing number in a sorted list from range 0,255
            :param numbers: Sorted list of numbers
            :return: First missing number from start to end, or None if list has end-start elements
        """
        start = 0
        if not numbers:
            return start
        for i in range(start, end):
            if i and i >= len(numbers):
                return i
            if numbers[i] != i:
                return i
        return None

    def get_last_successfull_job_info_for_commit_validate(self, upgrade_instance_id):
        '''
        This function finds the last successfull job id of the instance to be validated
        If there is no successfull job of the parent then lets pickup the last job id of the instance to be validated

        :param upgrade_instance_id: int
        :return: panorama_job_id (int)
        '''

        try:
            self.logger.info(
                "get_last_successfull_job_info_for_commit_validate: called with parent instance_id %s" % (str(upgrade_instance_id)))

            # get the last successfull job
            sql = "select panorama_job_id from xxx_cfgserv_firewall_summary where firewall_id = %s and " \
                  "fw_job_status='Success' and panorama_job_id <> 0 order by id desc LIMIT 1"

            # get the cursor
            cursor = self.get_cursor(prepared=True)
            cursor.execute(sql, (upgrade_instance_id,))
            ret = cursor.fetchone()
            self.logger.info(
                "get_last_successfull_job_info_for_commit_validate: response of query for panorama job id %s, for successull job" % (
                    str(ret)))

            if ret and len(ret) > 0:  # last successul job id is present for the tenant
                (panorama_job_id) = ret[0]
                return panorama_job_id
            else:  # no successfull job present for the tenant, lets pick up the lastest job of the parent.
                sql = "select panorama_job_id from xxx_cfgserv_firewall_summary where firewall_id = %s " \
                      " and panorama_job_id <> 0 order by id desc LIMIT 1"
                cursor.execute(sql, (upgrade_instance_id,))
                ret = cursor.fetchone()
                self.logger.info(
                    "get_last_successfull_job_info_for_commit_validate: response of query for panorama job id : %s, for any job" % (
                        str(ret)))
                if ret and len(ret) > 0:  # there exists a job for the parent, not in success state though
                    (panorama_job_id) = ret[0]
                    return panorama_job_id
                else:  # there is no job available for the tenant, this ideally should not happen, but in this case we
                    # will fail the upgrade
                    return -1

        except Exception as ex:
            error = "Unable to get last successfull/last executed job id of the instance to be upgraded " \
                        "instance id %s :%s" % (
                            str(upgrade_instance_id), str(ex))
            self.logger.error(error)
            return -1

    def insert_job_in_commit_validate_firewall_summary(self, instance, custid, node_id, commit_validate_metadata):
        '''
            This function finds the last successfull job id of the instance to be validated
            and then inserts entry into xxx_cfgserv_firewall_upgrade_commit_validate_summary with
            job status 'Init' and push_type 'upgrade-commit-on-startup'
        '''
        firewall_id = instance.get_param("id")
        # get the cursor
        cursor = self.get_cursor(prepared=True)

        try:
            upgrade_vmid = json.loads(commit_validate_metadata)['vmid']
        except Exception as e:
            self.logger.error("Exception in getting commit validate metatdata: %s", str(e))
            upgrade_vmid = 0

        upgrade_firewall_id = 0
        upgrade_super_acct_id = 0
        upgrade_acct_id = 0
        upgrade_custid = 0

        sql = "SELECT id, acct_id, custid from instance_master where vmid=%s"
        params = (upgrade_vmid, )
        cursor.execute(sql, params)
        ret = cursor.fetchall()

        if not ret or len(ret) == 0:  # upgrade instance is not found
            self.logger.error(
                "insert_job_in_commit_validate_firewall_summary: unable to find the instance for vmid : %s" % (
                    upgrade_vmid))
            self.logger.error(self.error)
            raise Exception(self.error)
        else:  # pinned parent instance is found
            self.logger.info(
                "insert_job_in_commit_validate_firewall_summary: found the instance for vmid : %s, reponse of sql %s" % (
                    upgrade_vmid, ret))
            (upgrade_firewall_id, upgrade_acct_id, upgrade_custid) = ret[0]
            upgrade_super_acct_id = upgrade_acct_id

        # Create a customer object.
        customer = CustomerModel(custid=custid, dbh=self)
        if not customer.get_param("id"):
            self.error = ("insert_job_in_commit_validate_firewall_summary():Unable to find customer in "
                          "the Database with id %d" % (custid,))
            self.logger.error(self.error)
            raise Exception(self.error)
        super_acct_id = customer.get_param("super_acct_id")
        acct_id = customer.get_param("acct_id")


        panorama_job_id = self.get_last_successfull_job_info_for_commit_validate(upgrade_firewall_id)

        self.logger.info(
            "insert_job_in_commit_validate_firewall_summary: inserting jobid %s for new instance id %s in "
            "xxx_cfgserv_firewall_upgrade_commit_validate_summary table" % (
                panorama_job_id, firewall_id))

        # mapping from node type to service type for xxx_cfgserv_firewall_summary table
        mapping_for_commit_validate_firewall_summary_table = {48: 'FW', 49: 'GPGW', 50: 'GPPT', 51: 'SFW', 152: 'CPFW', 153: 'EPFW'}

        sql = "insert into xxx_cfgserv_firewall_upgrade_commit_validate_summary(super_tenant_id, tenant_id,firewall_id,panorama_job_id,fw_job_status," \
              "push_type, service_type, upgrade_super_tenant_id, upgrade_tenant_id, upgrade_firewall_id) values (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"

        params = (super_acct_id, acct_id, firewall_id, panorama_job_id, "Init", "upgrade-commit-on-startup",
                  mapping_for_commit_validate_firewall_summary_table[node_id], upgrade_super_acct_id, upgrade_acct_id, upgrade_firewall_id)
        cursor.execute(sql, params)
        # closing the cursor
        self.cursorclose(cursor)
        self.conn.close()

    def insert_job_in_firewall_summary(self, instance, is_dynamic, panorama_job_id, node_id):
        '''
        This function inserts the entry in firewall summary table for the new instance

        This entry is based on is_dynamic flag.
        :param instance: object of instancemodel
        :type instance: object
        :param is_dynamic: flag for whether its a dynamic node or not
        :type is_dynamic: bool
        :param panorama_job_id: panorama job id
        :type panorama_job_id: str
        :return:
        '''

        cursor = None
        try:
            panorama_id = None
            #lets get the customer info
            customer = CustomerModel(custid=instance.get_param("custid"), dbh=self)
            if not customer.get_param("id"):
                error = ("insert_job_in_firewall_summary():Unable to find customer in "
                              "the Database with id %d" % (instance.get_param("custid")))
                self.logger.error(error)
                raise Exception(error)

            self.logger.info("insert_job_in_firewall_summary called : instance id %s , is_dynamic %s, panorama_job_id %s"
                             %(instance.get_param("id"), is_dynamic, panorama_job_id))
            # get the cursor
            cursor = self.get_cursor(prepared=True)

            if is_dynamic: #autoscaled instance
                self.logger.info("insert_job_in_firewall_summary called : instance %s is autoscaled" % (instance.get_param("id")))

                #lets get the parent instance master entry, which has is_pinned_instance=1
                sql = "select id from instance_master where is_pinned_instance=1 and acct_id= %s and compute_region_idx = %s and node_type = %s"
                params = (instance.get_param("acct_id"), instance.get_param("compute_region_idx"), node_id)
                cursor.execute(sql, params)
                ret = cursor.fetchall()
                self.logger.info("insert_job_in_firewall_summary : response of sql %s, result %s" %(sql, str(ret)))

                if not ret or len(ret) ==0: #pinned instance is not found
                    self.logger.info("insert_job_in_firewall_summary: unable to find the pinned instance for current instance : %s" % (instance.get_param("id")))
                else:#pinned parent instance is found
                    self.logger.info(
                        "insert_job_in_firewall_summary: found the pinned instance for current instance : %s, reponse of sql %s" % (
                        instance.get_param("id"), ret))

                    parent_id = ret[0][0]

                    #now lets insert the entry in the xxx_firewall summary table
                    ret = instance.insert_last_successfull_job_info(self, parent_id, push_type = "commit-on-startup", customer=customer)

                    if ret == False:
                        self.logger.info("insert_job_in_firewall_summary: Unable to insert entry in xxx_cfgserv_firewall_summary for instance id %s " % instance.get_param("id"))

                # closing the cursor
                self.cursorclose(cursor)
                self.conn.close()
            elif instance.get_param("slot_nr") <= INSTANCE_ROLE_MP:
                # New onboardings. But, we only insert the job for Integrated FWs and MP instances
                self.logger.info("insert_job_in_firewall_summary called : instance %s is newly onboarded, panorama_job_id %s"
                                 % (instance.get_param("id"), panorama_job_id))

                #lets get the panorama id from the xxx_cfgserv_panorama_cfg_request using the tenant id and panorama job id
                sql_panorana_id = "select panorama_id from xxx_cfgserv_panorama_cfg_request where tenant_id=%s and panorama_job_id=%s order by id desc limit 1"
                params = (instance.get_param("acct_id"), panorama_job_id)
                cursor.execute(sql_panorana_id, params)
                ret = cursor.fetchall()
                self.logger.info("insert_job_in_firewall_summary : response of sql %s, result %s" % (sql_panorana_id, str(ret)))

                if not ret or len(ret) == 0:
                    self.logger.info("insert_job_in_firewall_summary: unable to retrieve panorama id for the "
                                     "instance id %s, acct_id %s, and panorama_job_id %s, "
                                     "result sql %s" %(instance.get_param("id"), instance.get_param("acct_id"), panorama_job_id, ret))
                else:
                    self.logger.info("insert_job_in_firewall_summary: found the panorama id for the "
                                     "instance id %s, acct_id %s, and panorama_job_id %s, "
                                     "result sql %s" %(instance.get_param("id"), instance.get_param("acct_id"), panorama_job_id, ret))
                    if isinstance(ret[0][0], bytearray):
                        panorama_id = ret[0][0].decode()
                    else:
                        panorama_id = ret[0][0]


                    # sql = "insert into xxx_cfgserv_firewall_summary (tenant_id, firewall_id, panorama_job_id,panorama_id, " \
                    #       "fw_job_status, push_type,super_tenant_id,service_type) " \
                    #       "values (%d, %d, %s, '%s' ,'%s', '%s', %d, '%s') " % (int(instance.acct_id),
                    #                                                             int(instance.id), panorama_job_id,
                    #                                                             str(panorama_id), 'Init',
                    #                                                             'commit-on-startup', int(customer.super_acct_id), mapping_for_firewall_summary_table[instance.node_type])

                    param_sql = "insert into xxx_cfgserv_firewall_summary (tenant_id, firewall_id, panorama_job_id,panorama_id, " \
                          "fw_job_status, push_type,super_tenant_id,service_type) values (%s, %s, %s, %s, %s, %s, %s, %s)"
                    params = (int(instance.get_param("acct_id")),
                              int(instance.get_param("id")),
                              int(panorama_job_id),
                              str(panorama_id), 'Init', 'commit-on-startup',
                              int(customer.get_param("super_acct_id")),
                              get_mapping_for_firewall_summary_table(instance.get_param("node_type"), instance.get_param("alt_node_type")))

                    self.logger.info("insert_job_in_firewall_summary: found panorama id, inserting row in xxx_cfgserv_firewall_summary "
                                        "with sql %s, params %s " % (param_sql,params))
                    #lets execute the query
                    cursor.execute(param_sql,params)

                #closing the cursor
                self.cursorclose(cursor)
                self.conn.close()
                try:
                    # Notify alert engine about initial commit for new onboarding
                    lambda_params = dict()
                    lambda_params['prefix'] = "xxx"
                    lambda_params['super_tenant_id'] = str(customer.get_param("super_acct_id"))
                    lambda_params['tenant_id'] = str(instance.get_param("acct_id"))
                    lambda_params['firewall_id'] = str(instance.get_param("id"))
                    lambda_params['panorama_job_id'] = str(panorama_job_id)
                    lambda_params['panorama_id'] = str(panorama_id)
                    lambda_params['fw_job_status'] = "Init"
                    lambda_params['service_type'] = get_mapping_for_firewall_summary_table(instance.get_param("node_type"), instance.get_param("alt_node_type"))
                    lambda_params['push_type'] = 'commit-on-startup'
                    # CYR-28762 fix the missing create_time for init entries in prisma_access_fw_config_commit_alert BQ table
                    lambda_params['create_time'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    lambda_params['update_time'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    self.logger.info("Sending the params to async_call_firewall_summary_notify_lambda")
                    config_commit_al_notify.async_call_firewall_summary_notify_lambda(lambda_params, dbh=self)
                except Exception as e:
                    self.logger.error("Exception: Failed to send new job message to alert engine for new onboarding. Err: %s" % str(e))

        except Exception as ex:
            error = "insert_job_in_firewall_summary: Unable to insert entry in xxx_cfgserv_firewall_summary " \
                    "for instance id %s: %s" % (str(instance.get_param("id")), str(ex))
            self.logger.info(error)
            # closing the cursor
            self.cursorclose(cursor)
            self.conn.close()

    def sase_pvt_region_mgmt_post_processing(self, active, passive, svc_node_type):
        success = False
        failed = False
        try:
            if not active:
                self.logger.info(f"active entry is null, hence not proceeding")
                success = True
                return

            # For sase private region we need to update the seq # in priv instance mgmt table!
            self.logger.info(f"Processing priv inst mgmt update for instance {active.get_param('id')} "
                             f"and name {active.get_param('name')}")
            success = setup_private_instance_mgmt_entry(self, active, svc_node_type)
            if success == False:
                self.logger.error(f"Failed to update priv instance mgmt entry for instance {active.get_param('id')}")
                failed = True
                return

            if passive:
                success = setup_private_instance_mgmt_entry(self, passive, svc_node_type)
                if success == False:
                    self.logger.error(f"Failed to update priv instance mgmt entry for instance {passive.get_param('id')}")
                    failed = True
                    return

            if active.get_param("node_type") in [ NODE_TYPE_GP_GATEWAY ]:
                self.logger.info("Trying get trigger pvt inst mgmt update for re-discovery!")
                success = trigger_priv_inst_mgmt_lb_update(self,
                                                           active.get_param("custid"),
                                                           active.get_param("compute_region_idx"),
                                                           svc_node_type)
                if success == False:
                    self.logger.error(
                        f"Failed to trigger_priv_inst_mgmt_lb_update for instance {passive.get_param('id')}")
                    failed = True
                    return

            success = True
        except Exception as E:
            self.logger.error(f"Failed with exception {E.args}, traceback: {traceback.format_exc()}, locals: {locals()}")
            if failed:
                if active:
                    delete_private_instance_mgmt_entry(self, active.get_param("id"))
                if passive:
                    delete_private_instance_mgmt_entry(self, passive.get_param("id"))
        finally:
            return success

    def get_new_instance_mp_dp_seperation(self, version, node_type, region,
                                          custnode, transient, custid = 0,
                                          is_clean_pipe = 0, panorama_job_id = None,
                                          has_nat_gateway = False,
                                          has_nlb = False):
        inst_ids_mp = []
        inst_ids_dp = []
        try:
            # Just using sha1 instead of sha256 to reduce len of char string.
            my_inter_instance_psk = hashlib.sha1().hexdigest()
            inst_ids_mp = self.get_new_instance(version, node_type, region,
                              custnode, transient, custid,
                              is_clean_pipe, panorama_job_id,
                              instance_role = INSTANCE_ROLE_MP,
                              slot_nr = 1,
                              inter_instance_psk = my_inter_instance_psk,
                              has_nat_gateway = has_nat_gateway,
                              has_nlb = has_nlb)

            if not inst_ids_mp:
                err_msg = ("Unable to get new MP instances for node type %s" % str(node_type))
                self.error = err_msg
                raise Exception(err_msg)

            inst_ids_dp = self.get_new_instance(version, node_type, region,
                              custnode, transient, custid,
                              is_clean_pipe, panorama_job_id,
                              instance_role = INSTANCE_ROLE_DP,
                              slot_nr = 2,
                              inter_instance_psk = my_inter_instance_psk,
                              has_nat_gateway = has_nat_gateway,
                              has_nlb = has_nlb)
            if not inst_ids_dp:
                self.error = self.error
                err_msg = ("Unable to get new DP instances for node type %s" % str(node_type))
                self.error = err_msg
                self.logger.error(err_msg)
                raise Exception(err_msg)

        except Exception as E:
            self.logger.error("get_new_instance_mp_dp_seperation: Failed with exception %s %s %s" %
                               str(E.args), str(traceback.format_exc()), str(locals()) )
        finally:
            return inst_ids_mp, inst_ids_dp

    def get_new_instance(self, version, node_type, region, custnode, transient, custid=0,
                         is_clean_pipe=0, panorama_job_id = None,
                         instance_role = INSTANCE_ROLE_LEGACY, slot_nr = 0,
                         inter_instance_psk = None, has_nat_gateway = False,
                         has_nlb = False, spn_name=""):

        machine_type = custnode.machine_type
        is_premium_zone = is_region_id_premium_zone(dbh=self, region_id=region, custid=custid)
        colo_sc = False
        ret = False
        virtual_ip = None

        if custnode.capacity_type is None:
            self.logger.error( "get_new_instance() custnode.capacity_type is None!")
        else:
            self.capacity_type = custnode.capacity_type

        if instance_role == INSTANCE_ROLE_DP:
            # Initialize DP specific size
            gpcs_instance_size = custnode.dp_gpcs_instance_size
            self.cloud_machine_type = custnode.dp_cloud_machine_type
        else:
            gpcs_instance_size = custnode.gpcs_instance_size
            self.cloud_machine_type = custnode.cloud_machine_type
        self.cpu_platform = custnode.cpu_platform


        self.market_type = custnode.market_type
        self.dpdk_qcount = custnode.dpdk_qcount

        # is_dynamic_node would be True for AutoScale/Upgrade.
        is_dynamic_node = custnode.is_dynamic_node
        # transport type = colo-connect is for SC today
        if custnode.transport_type is not None \
                and custnode.transport_type == 'colo-connect':
            colo_sc = True
            # get virtual ip for eth3
            customer = CustomerModel(custid=custid, dbh=self)
            if self.get_colo_100g_no_gre_ff(customer.get_param("acct_id")) is False:
                virtual_ip, ret = self.get_ip_alias_for_colo_intf(custid=custid,
                                                                  custnode=custnode,
                                                                  region=region)
                if ret == False:
                    virtual_ip = None
                    self.error = (
                        "get_new_instance: Unable to find virtual ip for colo sc"
                        " cust topology id %s" % str(custnode.id))
                    self.logger.error(
                        "get_new_instance: Unable to find virtual ip for colo sc"
                        " cust topology id %s" % str(custnode.id))
                    return None

        nodetypemodel = NodeTypeModel(node_type, machine_type, self)
        instances = []
        active = None
        passive = None
        self.logger.info(
                "get_new_instance(): is_clean_pipe: %s; "
                "is_pa_connector_managed: %s" % (str(is_clean_pipe),
                         str(custnode.is_pa_connector_managed)))
        if not nodetypemodel.node_id:
            self.error = (
                "get_new_instance: Unable to find nodetype id for node"
                " type %s" % str(node_type))
            self.logger.error(
                "get_new_instance: Unable to find nodetype id for node"
                " type %s" % str(node_type))
            return None

        if is_clean_pipe:
            nodetypemodel.num_loopback_ip =1
            nodetypemodel.ha_reqd = 0

        # use spn_name from cust_toplogy if available otherwise use provided spn_name
        if custnode.spn_name != None and custnode.spn_name != "":
            spn_name = custnode.spn_name

        # if custnode.is_commit_validate:
        #     nodetypemodel.ha_reqd = 0

        self.logger.info("get_new_instance(): gpcs_instance_size: %s, dp_gpcs_instance_size: %s, "
                                        "ha_mode: %s" % (custnode.gpcs_instance_size,
                                        custnode.dp_gpcs_instance_size, custnode.ha_mode))

        if node_type in [ NODE_TYPE_SASE_PRIV_REGION_LB_NAME,  NODE_TYPE_SASE_PRIV_REGION_AGENT_NAME ]:
            res = self.get_new_instance_non_panos_int(version,
                                                      node_type,
                                                      region,
                                                      machine_type,
                                                      gpcs_instance_size,
                                                      custid,
                                                      cust_topology_id=custnode.id,
                                                      spn_name=custnode.spn_name,
                                                      custnode=custnode)
        else:
            res = self.get_new_instance_int(version, node_type, region, machine_type,
                                            gpcs_instance_size, custid,
                                            is_dynamic_instance=is_dynamic_node,
                                            is_clean_pipe = is_clean_pipe,
                                            cust_topology_id = custnode.id, avail_domain=custnode.avail_domain,
                                            inbound_access=custnode.inbound_access,
                                            spn_name=spn_name,
                                            instance_role=instance_role,
                                            slot_nr=slot_nr,
                                            inter_instance_psk=inter_instance_psk,
                                            okyo_edge_site_id=custnode.okyo_edge_site_id,
                                            has_nat_gateway=has_nat_gateway,
                                            sase_fabric=custnode.sase_fabric,
                                            is_pa_connector_managed = custnode.is_pa_connector_managed,
                                            is_instance_behind_nlb = has_nlb,
                                            is_commit_validate = custnode.is_commit_validate,
                                            is_premium_zone=is_premium_zone,
                                            colo_sc=colo_sc,
                                            virtual_ip=virtual_ip,
                                            alt_node_type=custnode.alt_node_type,
                                            custnode=custnode)

        if not res.ok:
            err_msg = (f"Unable to get new instance for node type {str(node_type)}")
            self.error += err_msg
            self.logger.error(err_msg)
            self.update_status_msg_for_cust_topology_node(custnode.id, err_msg)
            return None
        active = res.result[0]

        if nodetypemodel.ha_reqd and node_type in [ NODE_TYPE_SASE_PRIV_REGION_LB_NAME,
                                                    NODE_TYPE_SASE_PRIV_REGION_AGENT_NAME ]:

            res = self.get_new_instance_non_panos_int(version,
                                                      node_type,
                                                      region,
                                                      machine_type,
                                                      gpcs_instance_size,
                                                      custid,
                                                      is_passive=True,
                                                      cust_topology_id = custnode.id,
                                                      spn_name=custnode.spn_name,
                                                      ha_peer_id=active.get_param("id"),
                                                      custnode=custnode)
            if not res.ok:
                err_msg = (f"Unable to get new passive instance for node "
                           f"type %s" % str(node_type))
                self.error += err_msg
                self.logger.error(err_msg)
                self.update_status_msg_for_cust_topology_node(custnode.id,
                                                              err_msg)
                if active:
                    active.delete(self, allocation_failed=True)
                return None

            passive = res.result[0]
            active.set_param("ha_peer", passive.get_param("id"))
            active.save()
            instances.append(active.get_param("id"))
            instances.append(passive.get_param("id"))

        elif nodetypemodel.ha_reqd and active.get_param("no_passive_instance") == False:
            res = self.get_new_instance_int(version, node_type, region, machine_type,
                                            gpcs_instance_size, custid, is_passive=True,
                                            cust_topology_id = custnode.id,
                                            ha_peer_id=active.get_param("id"), inbound_access=custnode.inbound_access,
                                            spn_name=spn_name, instance_role=instance_role,
                                            slot_nr=slot_nr, inter_instance_psk=inter_instance_psk,
                                            okyo_edge_site_id=custnode.okyo_edge_site_id,
                                            has_nat_gateway=has_nat_gateway,
                                            sase_fabric=custnode.sase_fabric,
                                            is_pa_connector_managed = custnode.is_pa_connector_managed,
                                            is_instance_behind_nlb = has_nlb,
                                            is_premium_zone=is_premium_zone,
                                            colo_sc=colo_sc,
                                            virtual_ip=virtual_ip,
                                            custnode=custnode, sc_map_id=active.get_param("sc_map_id"))
            if not res.ok:
                err_msg = (f"Unable to get new passive instance for node "
                           f"type %s" % str(node_type))
                self.error += err_msg
                self.logger.error(err_msg)
                self.update_status_msg_for_cust_topology_node(custnode.id,
                                                              err_msg)
                if active:
                    active.delete(self, allocation_failed=True)
                return None
            passive = res.result[0]
            active.set_param("ha_peer", passive.get_param("id"))
            active.save()
            instances.append(active.get_param("id"))
            instances.append(passive.get_param("id"))
        else:
            instances.append(active.get_param("id"))

        #now since instances are created lets insert the entry in xxx_cfgserv_firewall_summary
        #insert entry for active
        if active is not None:
            if custnode.is_commit_validate:
                self.insert_job_in_commit_validate_firewall_summary(active, custid, nodetypemodel.node_id, custnode.commit_validate_metadata)
            else:
                self.insert_job_in_firewall_summary(active, is_dynamic_node, panorama_job_id, nodetypemodel.node_id)
        #insert entry for passive
        if passive is not None:
            self.insert_job_in_firewall_summary(passive, is_dynamic_node, panorama_job_id, nodetypemodel.node_id)

        # If the instance role is DP instance.
        if instance_role == INSTANCE_ROLE_DP:
            return instances

        failed = 0
        sql = "call bind_new_instance(%s, %s, %s)"
        params = (custnode.id, active.get_param("id"), transient)
        cursor = self.get_cursor(prepared=True)
        ret = None
        try:
            self.logger.info("get_new_instance: sql = %s" % sql)
            self.logger.info("get_new_instance: params = %s" % str(params))
            cursor.execute(sql, params)
            ret = cursor.fetchone()
            self.cursorclose(cursor)
            self.conn.close()
        except Exception as ex:
            err_msg = (f"Exception Unable to bind new instance with "
                        f"node {str(custnode.name)}: {str(ex)}")
            self.error += err_msg
            self.logger.error(self.error)
            self.update_status_msg_for_cust_topology_node(custnode.id, err_msg)
            self.cursorclose(cursor)
            self.conn.close()
            failed = 1
        if not ret or ret[0] == 0:
            dberr = ""
            if ret and len(ret) > 1:
                dberrr = ret[1]
            err_msg = (f"Unable to bind new instance with "
                       f"node {str(custnode.name)}: {str(ret)}")
            self.error += err_msg
            self.logger.error(self.error)
            self.update_status_msg_for_cust_topology_node(custnode.id, err_msg)
            failed = 1

        if active and (active.get_param("cloud_provider") == PROVIDER_VMWARE_ESXI_DB_ENUM_VALUE):
            self.logger.info(f"Processing pvt inst mgmt check for cloud_provider {active.get_param('cloud_provider')}")
            if self.sase_pvt_region_mgmt_post_processing(active, passive, custnode.svc_node_type) == False:
                failed = True

        if failed:
            if active:
                if active.get_param("cloud_provider") == PROVIDER_VMWARE_ESXI_DB_ENUM_VALUE:
                    delete_private_instance_mgmt_entry(self, active.get_param("id"))
                active.unbind(custnode, self, None)
                active.delete(self, allocation_failed=True)
            if passive:
                if passive.get_param("cloud_provider") == PROVIDER_VMWARE_ESXI_DB_ENUM_VALUE:
                    delete_private_instance_mgmt_entry(self, passive.get_param("id"))
                passive.unbind(custnode, self, None)
                passive.delete(self, allocation_failed=True)
            instances = []

        if not failed:
            self.update_status_msg_for_cust_topology_node(custnode.id,
                                                          "Successfully created required instances")
        else:
            self.update_status_msg_for_cust_topology_node(custnode.id,
                                                          "Failed to create instances")
        return instances

    def get_vpc_id_for_instance(self, region_idx, region_name, is_passive, cloud_provider, custid=0, is_premium_zone=False):
        vpc_id = None
        try:
            if cloud_provider == PROVIDER_AWS:
                ha_flag = is_passive if not is_premium_zone else 0
                vpc = VpcModel(region_idx, self, ha_flag=ha_flag, custid=custid, is_premium_zone=is_premium_zone)
                if not vpc.vpcid:
                    self.error = ("!!! Fatal Unable to get vpc for region idx %s, region name %s"
                                  % (str(region_idx), str(region_name)))
                    self.logger.error(self.error)
                    raise Exception(self.error)
                vpc_id = vpc.vpcid
            elif cloud_provider == PROVIDER_GCP:
                vpc_id = "gcp-region-"+str(region_name)
            elif cloud_provider == PROVIDER_AZR:
                vpc_id = None
            elif cloud_provider == PROVIDER_OPENSTACK:
                #NOTE: The below vpc ID is for name sake. we will evaluate if we really need vpc for openstack.
                vpc_id = "openstack-region-"+str(region_name)
            elif cloud_provider == PROVIDER_OCI:
                vpc_id = "oci-region-"+str(region_name)
            else:
                raise Exception("Unknown cloud provider!!!")
        except Exception as E:
            self.logger.error("get_vpc_id_for_instance: Failed to get the VPC id for instance. Exception %s, locals %s"
                              % (str(E.args), str(locals())))
        finally:
            return vpc_id

    def get_vpc_sec_key_name(self, region_idx, region_name, is_passive, cloud_provider, custid=0, is_premium_zone=False):
        vpc_key_name = None
        try:
            if cloud_provider == PROVIDER_AWS:
                ha_flag = is_passive if not is_premium_zone else 0
                vpc = VpcModel(region_idx, self, ha_flag=ha_flag, custid=custid, is_premium_zone=is_premium_zone)
                if not vpc.vpcid:
                    self.error = ("!!! Fatal Unable to get vpc sec key for region idx %s, region name %s"
                                  % (str(region_idx), str(region_name)))
                    self.logger.error(self.error)
                    raise Exception(self.error)
                vpc_key_name = vpc.sec_keyname
            elif cloud_provider in (PROVIDER_GCP, PROVIDER_AZR, PROVIDER_OPENSTACK, PROVIDER_OCI):
                # No VPC key name for GCP, AZR or OPENSTACK.
                vpc_key_name = None
            else:
                raise Exception("Unknown cloud provider!!!")
        except Exception as E:
            self.logger.error("get_vpc_sec_key_name: Failed to get the VPC sec key for instance. Exception %s, "
                              "locals %s"
                              % (str(E.args), str(locals())))
        finally:
            return vpc_key_name


    def get_nodeTypeInstance(self, node_type, machine_type):
        nodeTypeInstance = None
        try:
            nodetypemodel = NodeTypeModel(node_type, machine_type, self)
            if not nodetypemodel.node_id:
                error = (
                    "get_new_instance: Unable to find nodetype id for node"
                    " type %s" % str(node_type))
                self.logger.error(error)
                raise Exception(error)
            nodeTypeInstance = nodetypemodel
        except Exception as E:
            self.logger.error("get_nodeTypeInstance: Failed to get the node type for instance %s. locals %s" %
                             (str(E.args), str(locals())))
            nodeTypeInstance = None
        finally:
            return nodeTypeInstance

    # TODO: Nilesh Flexible capacity layer.
    def get_capacity_for_instance(self, node_type, machine_type, region_idx):
        capacity = None
        try:
            nodeTypeInstance = self.get_nodeTypeInstance(node_type, machine_type)
            if nodeTypeInstance == None:
                raise Exception("Failed to get the get_nodeTypeInstance. Cannot continue.")
            capacity = nodeTypeInstance.max_capacity
        except Exception as E:
            self.logger.error("get_capacity_for_instance: Unable to get capacity for machine type %s. %s locals: %s" %
                             (str(machine_type), str(E.args), str(locals())))
        finally:
            return capacity

    def feature_500Mbps_enabled(self):

        # check whether 500Mbps support should be enabled
        support_full_500Mbps = False
        params = ('500Mbps_full_support',)
        self.logger.info("feature_500Mbps: checking DB for setting")
        res = None
        cursor = self.get_cursor()
        try:
            cursor.execute('select value from global_settings where setting = %s', params)
            res = cursor.fetchone()
        except Exception as ex:
            self.logger.error("feature_500Mbps failed %s" % (str(ex)))
        if res:
            feature_full_500Mbps, = res
            self.logger.info("feature_500Mbps : found setting, value : %s "
                                % (str(feature_full_500Mbps)))
            if feature_full_500Mbps == 'enabled':
               support_full_500Mbps = True
        else:
            self.logger.error("feature_500Mbps: No record found, disabling by default")
        self.cursorclose(cursor)
        return support_full_500Mbps

    def get_ip_alias_for_colo_intf(self, custid, custnode, region):
        virtual_ip = None
        colo_subnet = None
        region_subnet = None
        sc_name = None
        success = False
        try:
            customer = CustomerModel(custid=custid, dbh=self)
            self.logger.info("Retrieve colo subnet from custmaster")
            if customer.get_param("colo_connect_subnet") != None and customer.get_param("colo_connect_subnet") != "":
                colo_subnet = json.loads(customer.get_param("colo_connect_subnet"))
            if not colo_subnet:
                raise Exception("Colo Connect subnet is not set Cust Master")

            region_subnet = colo_subnet[str(region)]
            ip_network = ipaddress.ip_network(region_subnet["range"])
            list_host = list(ip_network.hosts())

            if custnode.name == None or custnode.name == "":
                raise Exception("cust topology entry %s does not have name" % str(custnode.id))

            sc_name = custnode.name
            sc_pair_no = sc_name.split(":")[1]
            if int(sc_pair_no) == 1:
                # first pair use third ip
                virtual_ip = str(list_host[2])
            else:
                virtual_ip = str(list_host[4])
            success = True
        except Exception as E:
            self.logger.error("get_ip_alias_for_colo_intf: Failed to get virtual ip for Colo SC %s %s"% (str(E.args), str(traceback.format_exc())))
        finally:
            return virtual_ip, success
    def get_updated_max_capacity_for_instance(self, custid, current_max_capacity, spn_name=None):

        customer_fw_version = None
        customer_fw_version_split = None
        fw_version_major_minor = None
        update_max_capacity = False
        new_max_capacity = current_max_capacity

        try:

            customer = CustomerModel(custid=custid, dbh=self)
            self.logger.info("Check if 500 Mbps feature is enabled")
            if not self.feature_500Mbps_enabled():
                self.logger.info("get_updated_max_capacity: 500 Mbps support not enabled")
                return False, new_max_capacity

            res = self.get_cust_sw_version(custid)
            if not res.ok:
                self.logger.error("get_updated_max_capacity: Unable to find cust_master version for id %s"
                                      % (str(custid)))
                return False, new_max_capacity

            customer_fw_version = res.result[0]
            self.logger.info("get_updated_max_capacity: customer_fw_version from cust_master is %s"
                             % (str(customer_fw_version)))
            customer_fw_version_split = str(customer_fw_version).split('-')
            if customer_fw_version_split[0] == 'PANOS':  # PANOS-9.0.6.saas
                fw_version_major_minor = str(customer_fw_version_split[1])
            elif customer_fw_version_split[0] == 'PA':  # PA-VM-Saas
                fw_version_major_minor = str(customer_fw_version_split[3])
            else:
                self.logger.error(
                    "get_updated_max_capacity : unrecognized prefix in cust_master version %s for custid %s"
                    % (str(customer_fw_version), str(custid)))
                return False, new_max_capacity

            if fw_version_major_minor != None:
                if Version(fw_version_major_minor) >= Version(MIN_FW_VERSION_500_MBPS):
                    self.logger.info("Max capacity from cust_master for instance updated to 500 Mbps")
                    update_max_capacity = True
                    new_max_capacity = FW_VERSION_MAX_CAPACITY_MAP[MIN_FW_VERSION_500_MBPS]
                else:
                    self.logger.info("Max capacity for instance to remain at current capacity %s"
                                     % (str(new_max_capacity)))
            if spn_name is not None:
                update_max_capacity = True
                new_max_capacity = AGG_BW_LATEST_MAX_CAPACITY

        except Exception as E:
            self.logger.error("get_updated_max_capacity_for_instance: Failed to update max_capacity")
        finally:
            return update_max_capacity, new_max_capacity

    def get_db_enum_for_cloud_provider(self, cloud_provider):
        ret = PROVIDER_UNKNOWN_DB_ENUM_VALUE
        try:
            if cloud_provider == PROVIDER_AWS:
                ret = PROVIDER_AWS_DB_ENUM_VALUE
            elif cloud_provider == PROVIDER_GCP:
                ret = PROVIDER_GCP_DB_ENUM_VALUE
            elif cloud_provider == PROVIDER_AZR:
                ret = PROVIDER_AZR_DB_ENUM_VALUE
            elif cloud_provider == PROVIDER_OPENSTACK:
                ret = PROVIDER_OPENSTACK_DB_ENUM_VALUE
            elif cloud_provider == PROVIDER_OCI:
                ret = PROVIDER_OCI_DB_ENUM_VALUE
            elif cloud_provider == PROVIDER_VMWARE_ESXI:
                ret = PROVIDER_VMWARE_ESXI_DB_ENUM_VALUE
            else:
                ret = PROVIDER_UNKNOWN_DB_ENUM_VALUE
        except Exception as E:
            self.logger.error("get_db_enum_for_cloud_provider: Failed to get the DB enum value for cloud_provider %s."
                              " Exception %s"
                              % (str(cloud_provider), str(E.args)))
        finally:
            return ret

    def is_ipv6_enabled_on_gcp_subnet(self, project_id, region, subnet_name):
        """
        Get details of a subnet and check if it has IPv6 enabled.

        Args:
            project_id (str): GCP project ID
            region (str): Region where the subnet is located
            subnet_name (str): Name of the subnet

        Returns:
            dict: Subnet details
        """
        ipv6_enabled = False
        try:
            if self.gcp_credentials == None:
                self.gcp_credentials = self.get_gcp_credentials(cfg)
            # Initialize the API client
            service = build('compute', 'v1', credentials=self.gcp_credentials)

            # Get the subnet details
            request = service.subnetworks().get(project=project_id, region=region, subnetwork=subnet_name)
            subnet = request.execute()
            print(subnet)
            # Check for IPv6 configuration
            ipv6_enabled = subnet.get("stackType") == "IPV4_IPV6"
            ipv6_details = {
                "externalIpv6Prefix": subnet.get('externalIpv6Prefix'),
                "ipv6AccessType": subnet.get('ipv6AccessType')
            } if ipv6_enabled else {}
            self.logger.info(f"Found Ipv6 details {ipv6_details} for native region {region} and subnet {subnet_name}")
        except Exception as E:
            self.logger.error(f"Failed with exception {str(E.args)}, locals: {locals()}")
        finally:
            return ipv6_enabled

    def get_pinned_inst_ref(self, custid, compute_region_idx, node_type):
        params = (custid, compute_region_idx, node_type)
        ok = False
        ret = []
        res = None
        sql = ("select id from instance_master "
               "where custid=%s and compute_region_idx=%s and node_type=%s "
               "and is_pinned_instance=1 and is_dynamic_instance=0 "
               "order by create_time limit 1" % params)
        """
            If there are more than one pinned instance, pick the old one.
            Only upgrade can create the second pinned instance.
        """
        self.logger.debug(sql)
        cursor = self.get_cursor()
        try:
            cursor.execute(sql)
            res = cursor.fetchone()
        except Exception as ex:
            self.logger.error("get_pinned_inst_ref failed %s" % (str(ex)))
        if res:
            ret.append(res[0])
            ok = True
        else:
            self.logger.error("get_pinned_inst_ref No record found")
        self.cursorclose(cursor)
        return DbResult(ok, ret)

    def get_pinned_cfg(self, custid, compute_region_idx, node_type):
        pinned_cfg = None
        res = self.get_pinned_inst_ref(custid, compute_region_idx, node_type)
        if not res.ok:
            self.logger.error(
                "get_pinned_inst_ref: Unable to find node_type %s pinned instance for custid %s in compute_region_idx %s"
                % (str(node_type), str(custid), str(compute_region_idx)))
            return None
        pinned_inst_id = res.result[0]
        pinned_inst_ref = InstanceModel(iid=pinned_inst_id, dbh=self)
        if not pinned_inst_ref.get_param("id"):
            self.logger.error(
                "get_pinned_inst_ref: Unable to find node_type %s pinned instance %s for custid %s in compute_region_idx %s"
                % (str(node_type), str(pinned_inst_id), str(custid), str(compute_region_idx)))
            return None
        try:
            pinned_cfg = json.loads(pinned_inst_ref.get_param("salt_profile"))
        except Exception as e:
            self.logger.error(
                "get_pinned_inst_ref: Failed to parse salt_profile on instance %s pinned_inst_ref.salt_profile: %s"
                % (str(pinned_inst_ref.get_param("id")), str(pinned_inst_ref.get_param("salt_profile"))))
        finally:
            return pinned_cfg


    def get_central_cache_service_endpoint(self, pa_env, tenant_id, region):
        """
        Retrieves the central cache service endpoint from RDS.
        First, this function checks the gpcs_tenant_region_mapping table for a tenant, region specific entry.
        If an entry is not found or if the value is an empty string,
        it checks the gpcs_region_mapping table for a region specific entry
        that applies to all tenants in that region.
        If the entry is not found or if the value is an empty string, it checks the services_endpoint_mapping
        that applies to all tenants in the environment as specified by the pa_env parameter.  (eg. dev15, qa6)
        Returns the central cache service endpoint string.  This string could be an empty string.
        If not successful, this function returns an empty string.
        """

        ccs_endpoint = ""
        try:
            sql = "SELECT central_cache_service_endpoint " \
                  "FROM gpcs_tenant_region_mapping " \
                  "WHERE tenant_id = %s AND region_id = %s"
            params = (tenant_id, region,)
            self.logger.info(f"SQL Query: {sql % params}")
            cursor = self.get_cursor()
            cursor.execute(sql, params)
            ret = cursor.fetchone()
            self.cursorclose(cursor)
            self.logger.info("Query gpcs_tenant_region_mapping. Return from SQL: %s" % str(ret))
            if not ret:
                errmsg = "Failed to get central_cache_service_endpoint from gpcs_tenant_region_mapping table in RDS"
                self.logger.info(errmsg)
            else:
                ccs_endpoint = ret[0]
                self.logger.info("Got central_cache_service_endpoint from gpcs_tenant_region_mapping: %s" % ccs_endpoint)

            if not ccs_endpoint:
                sql = "SELECT central_cache_service_endpoint " \
                    "FROM gpcs_region_mapping " \
                    "WHERE region_id = %s"
                params = (region,)
                self.logger.info(f"SQL Query: {sql % params}")
                cursor = self.get_cursor()
                cursor.execute(sql, params)
                ret = cursor.fetchone()
                self.cursorclose(cursor)
                self.logger.info("Query gpcs_region_mapping. Return from SQL: %s" % str(ret))
                if not ret or not ret[0]:
                    errmsg = "Failed to get central_cache_service_endpoint from gpcs_region_mapping table in RDS"
                    self.logger.error(errmsg)
                else:
                    ccs_endpoint = ret[0]
                    self.logger.info("Got central_cache_service_endpoint from gpcs_region_mapping: %s" % ccs_endpoint)

                if not ccs_endpoint:
                    self.logger.info("Falling back to central_cache_service_endpoint from services_endpoint_mapping table in RDS")
                    self.logger.info("ccs_endpoint: %s" % (ccs_endpoint))

                    sql = "SELECT central_cache_service_endpoint FROM services_endpoint_mapping_override WHERE pa_env=%s and tenant_id=%s\
                           union all\
                           SELECT central_cache_service_endpoint FROM services_endpoint_mapping WHERE pa_env=%s and \
                           not exists(SELECT 1 FROM services_endpoint_mapping_override WHERE tenant_id=%s)"
                    params = (pa_env,tenant_id,pa_env,tenant_id)
                    self.logger.info(f"SQL Query: {sql % params}")
                    cursor = self.get_cursor()
                    cursor.execute(sql, params)
                    ret = cursor.fetchone()
                    self.cursorclose(cursor)
                    self.logger.info("Return from SQL: %s" % str(ret))
                    if not ret or not ret[0]:
                        errmsg = "Failed to get central_cache_service_endpoint from services_endpoint_mapping table in RDS"
                        self.logger.error(errmsg)
                        raise Exception(errmsg)
                    ccs_endpoint = ret[0]
            self.logger.info("central_cache_service_endpoint: %s" % ccs_endpoint)
        except Exception as ex:
            errmsg = "Failed to get central_cache_service_endpoint from " \
                     "RDS: " + str(ex)
            self.logger.info("get_central_cache_service_endpoint caught "
                              "exception: %s" % str(ex))
            self.cursorclose(cursor)
        finally:
            self.logger.info("Returning central_cache_service_endpoint: %s"
                             % ccs_endpoint)
            return ccs_endpoint

    def get_central_cache_service_backup_endpoint(self, pa_env, tenant_id, region):
        """
        Retrieves the central cache service backup endpoint from RDS.
        First, this function checks the gpcs_tenant_region_mapping table for a tenant, region specific entry.
        If an entry is not found or if the value is an empty string,
        it checks the gpcs_region_mapping table for a region specific entry
        that applies to all tenants in that region.
        If the entry is not found or if the value is an empty string, it checks the services_endpoint_mapping
        that applies to all tenants in the environment as specified by the pa_env parameter.  (eg. dev15, qa6)
        Returns the central cache service backup endpoint string.  This string could be an empty string.
        If not successful, this function returns an empty string.
        """

        ccs_endpoint = ""
        try:
            sql = "SELECT central_cache_service_backup_endpoint " \
                  "FROM gpcs_tenant_region_mapping " \
                  "WHERE tenant_id = %s AND region_id = %s"
            params = (tenant_id, region,)
            self.logger.info(f"SQL Query: {sql % params}")
            cursor = self.get_cursor()
            cursor.execute(sql, params)
            ret = cursor.fetchone()
            self.cursorclose(cursor)
            self.logger.info("Query gpcs_tenant_region_mapping. Return from SQL: %s" % str(ret))
            if not ret:
                errmsg = "Failed to get central_cache_service_backup_endpoint from gpcs_tenant_region_mapping"
                self.logger.info(errmsg)
            else:
                ccs_endpoint = ret[0]
                self.logger.info("Got central_cache_service_backup_endpoint from "
                                "gpcs_tenant_region_mapping: %s" % ccs_endpoint)

            if not ccs_endpoint:
                sql = "SELECT central_cache_service_backup_endpoint " \
                    "FROM gpcs_region_mapping " \
                    "WHERE region_id = %s"
                params = (region,)
                self.logger.info(f"SQL Query: {sql % params}")
                cursor = self.get_cursor()
                cursor.execute(sql, params)
                ret = cursor.fetchone()
                self.cursorclose(cursor)
                self.logger.info("Query gpcs_region_mapping. Return from SQL: %s" % str(ret))
                if not ret or not ret[0]:
                    errmsg = "Failed to get central_cache_service_backup_endpoint from gpcs_region_mapping"
                    self.logger.error(errmsg)
                else:
                    ccs_endpoint = ret[0]
                    self.logger.info("Got central_cache_service_backup_endpoint from "
                                    "gpcs_region_mapping: %s" % ccs_endpoint)

                if  not ccs_endpoint:
                    self.logger.info("Falling back to global default ccs_endpoint")
                    self.logger.info("ccs_endpoint: %s" % (ccs_endpoint))

                    sql = "SELECT central_cache_service_backup_endpoint " \
                        "FROM services_endpoint_mapping " \
                        "WHERE pa_env=%s"
                    params = (pa_env,)
                    self.logger.info(f"SQL Query: {sql % params}")
                    cursor = self.get_cursor()
                    cursor.execute(sql, params)
                    ret = cursor.fetchone()
                    self.cursorclose(cursor)
                    self.logger.info("Return from SQL: %s" % str(ret))
                    if not ret or not ret[0]:
                        errmsg = "Failed to get central_cache_service_backup_endpoint from services_endpoint_mapping"
                        self.logger.error(errmsg)
                        raise Exception(errmsg)
                    ccs_endpoint = ret[0]
            self.logger.info("central_cache_service_backup_endpoint: %s" % ccs_endpoint)
        except Exception as ex:
            errmsg = "Failed to get central_cache_service_backup_endpoint from " \
                     "RDS: " + str(ex)
            self.logger.info("get_central_cache_service_backup_endpoint caught "
                              "exception: %s" % str(ex))
            self.cursorclose(cursor)
        finally:
            self.logger.info("Returning central_cache_service_backup_endpoint: %s"
                             % ccs_endpoint)
            return ccs_endpoint

    def get_ciam_service_endpoint(self, pa_env):
        """
        Retrieves the central cache service endpoint from RDS.
        it checks the services_endpoint_mapping
        that applies to all tenants in the environment as specified by the pa_env parameter.  (eg. dev15, qa6)
        Returns the ciam service endpoint string.  This string could be an empty string.
        If not successful, this function returns an empty string.
        """

        ciam_endpoint = ""
        try:

            sql = "SELECT ciam_service_endpoint " \
                      "FROM services_endpoint_mapping " \
                      "WHERE pa_env=%s"
            params = (pa_env,)
            self.logger.info(f"SQL Query: {sql % params}")
            cursor = self.get_cursor()
            cursor.execute(sql, params)
            ret = cursor.fetchone()
            self.cursorclose(cursor)
            self.logger.info("Query services_endpoint_mapping. Return from SQL: %s" % str(ret))
            if not ret:
                errmsg = "Failed to get ciam_service_endpoint from RDS"
                self.logger.info(errmsg)
            else:
                ciam_endpoint = ret[0]
                self.logger.info("Got ciam_service_endpoint from RDS: %s" % ciam_endpoint)
        except Exception as ex:
            self.logger.info("get_ciam_service_endpoint caught "
                              "exception: %s" % str(ex))
            self.cursorclose(cursor)
        finally:
            self.logger.info("Returning ciam_service_endpoint: %s"
                             % ciam_endpoint)
            return ciam_endpoint


    def update_pool_allocation_table(self, old_instance_id, new_instance_id):
        sql = "CALL pool_allocation_upgrade_transient(%s, %s)"
        params = (old_instance_id, new_instance_id,)
        self.logger.info("SQL: %s" % (sql % params))
        cursor = self.get_cursor()
        cursor.execute(sql, params)
        ret = cursor.fetchone()
        self.cursorclose(cursor)
        self.logger.info("Return from SQL: %s" % str(ret))

        # Verify that the "CALL" statement actually worked.
        sql = ("SELECT transient_inst_id FROM pool_allocation "
               "WHERE inst_id = %s AND is_in_use_by_transient_inst = 0 "
               "ORDER BY pool_id ASC LIMIT 1")
        params = (old_instance_id,)
        self.logger.info("SQL: %s" % (sql % params))
        cursor = self.get_cursor()
        cursor.execute(sql, params)
        ret = cursor.fetchone()
        self.cursorclose(cursor)
        self.logger.info("Return from SQL: %s" % str(ret))
        if not ret or not ret[0] or ret[0] != new_instance_id:
            self.logger.error("Failed during stored procedure invocation: "
                              "pool_allocation_upgrade_transient(%s, %s)" %
                              (old_instance_id, new_instance_id))
            return False
        self.logger.info("Successfully updated the pool allocation table "
                         "instance ids: (%s, %s)" %
                         (old_instance_id, new_instance_id))
        return True


    def update_status_msg_for_cust_topology_node(self, cust_topology_node_id,
                                                 status_msg):
        ret = False
        try:
            if cust_topology_node_id == 0:
                self.logger.info("Not updating cust_topology entry "
                                 "since id is 0")
                ret = True
            else:
                sql = ("UPDATE cust_topology SET status_msg = "
                       "CONCAT(status_msg, IF(status_msg = '', '', '; '), %s) "
                       "WHERE id = %s")
                params = (status_msg, cust_topology_node_id)
                self.logger.info("SQL: %s" % (sql % params))
                cursor = self.get_cursor()
                cursor.execute(sql, params)
                ret = cursor.fetchone()
                self.cursorclose(cursor)
                self.logger.info("Return from SQL: %s" % str(ret))

        except Exception as e:
            self.logger.info(f"Failed to update cust_topology in RDS; "
                             f"Error: {e}")

        finally:
            return ret

    # Returns index of hole in the mappings or highest mapping + 1 if no hole found
    # The sc_map_ids must be unique, contigious and increasing per region in order
    # to balance the MU to SC mappings
    def find_next_sc_map_id(self, mappings):
        try:
            sc_map_ids = [mapping[1] for mapping in mappings]

            # No instances in the tenant yet, thus return 1
            if len(mappings) == 0:
                return 1

            # No mu-sc mappings in the tenant yet, thus return 1
            if all(m == 0 for m in sc_map_ids):
                return 1

            # Remove all empty mappings and sort to simplify algoirthm
            sc_map_ids = [m for m in sc_map_ids if m != 0]
            sc_map_ids.sort()

            # Finds first non consecutive number ie the first hole in the sc_map_ids
            self.logger.info(f"sorted sc_map_ids {sc_map_ids}")
            for i in range(1, sc_map_ids[-1]):
                if i not in sc_map_ids:
                    return i

            # If got to this point, no hole exists. Return highest sc_map_id + 1
            return sc_map_ids[-1] + 1
        except Exception as e:
            self.logger.error(f"Error finding next sc_map_id: {traceback.format_exc()}")
            return 0

    def get_tenant_region_next_sc_map_id(self, tenant_id, region_id, node_type):
        next_sc_map_id = 0
        cursor = None
        try:
            self.logger.info(f"Getting next sc_map_id tenant {tenant_id} in region {region_id}")
            sql = f"""
                SELECT id, sc_map_id from instance_master
                WHERE acct_id = {tenant_id} AND compute_region_idx = {region_id} AND node_type = {node_type}
                ORDER BY sc_map_id;
            """
            cursor = self.get_cursor(prepared=True)
            cursor.execute(sql)
            ret = cursor.fetchall()
            next_sc_map_id = self.find_next_sc_map_id(ret)
            self.logger.info(ret)
            self.logger.info(f"next_sc_map_id: {next_sc_map_id}")
        except Exception as e:
            self.logger.error(f"Error while getting next map id in tenant region: {traceback.format_exc()}")
            next_sc_map_id = 0
        finally:
            if cursor:
                self.cursorclose(cursor)
                self.conn.close()
            return next_sc_map_id

    def set_sc_map_id(self, instance, tenant_id, node_type, cloud_provider, cfg, compute_region_idx, is_passive, sc_map_id):
        """Set sc_map_id on instance if conditions are met."""
        if node_type not in [ NODE_TYPE_GP_GATEWAY, NODE_TYPE_REMOTE_NET ]:
            return

        if cloud_provider in [ PROVIDER_VMWARE_ESXI ]:
            return

        if mu_sc_mapping_feature_flag_enabled(tenant_id, cfg, self.logger) and node_type == NODE_TYPE_GP_GATEWAY:
            instance.set_param("sc_map_id", self.get_tenant_region_next_sc_map_id(tenant_id, compute_region_idx, node_type))
        else:
            if self.get_colo_100g_no_gre_ff(tenant_id) and node_type == NODE_TYPE_GP_GATEWAY:
                instance.set_param("sc_map_id", self.get_tenant_region_next_sc_map_id(tenant_id, compute_region_idx, node_type))
        if self.get_colo_100g_no_gre_ff(tenant_id) and node_type == NODE_TYPE_REMOTE_NET:
            if is_passive and sc_map_id:
                instance.set_param("sc_map_id", sc_map_id)
            else:
                instance.set_param("sc_map_id", self.get_tenant_region_next_sc_map_id(tenant_id, compute_region_idx, node_type))

    def get_default_version(self, custnode, customer, plugin):
        '''
        Get Default Version for a Plugin
        If entry is not there in custnode, fallback to using cust_master
        '''
        if custnode is None or not isinstance(custnode.default_plugin_versions, dict):
            return customer.get_param(plugin)
        if plugin not in custnode.default_plugin_versions:
            return customer.get_param(plugin)
        return custnode.default_plugin_versions.get(plugin)

    def get_primary_instance(self, custid, region_id, node_type):
        logger = self.logger
        logger.info(f"Fetching primary instance for custid {str(custid)} and region {str(region_id)} "
                        f"and node_type {str(node_type)}")
        sql = "select id from instance_master where custid=%s and compute_region_idx=%s" \
            " and node_type=%s and is_dynamic_instance=0 order by create_time limit 1"
        params = (custid, region_id, node_type)
        cursor = self.get_cursor()
        try:
            cursor.execute(sql, params)
            ret = cursor.fetchone()
            self.cursorclose(cursor)
            if ret:
                return ret[0]
            return 0
        except Exception as ex:
            error = (
                f"Unable to fetch primary instance for customer id {str(custid)} region { str(region_id)}: {str(ex)}" )
            self.logger.error(error)
            self.cursorclose(cursor)
            return 0

    # This method checks if ep-geneve can be enabled on swgproxy
    def can_swg_proxy_enable_geneve(self, custid, compute_region_id, cloud_provider):
        proxy_bringup = swgproxy_bringup(self)
        ep_geneve_ff = proxy_bringup.is_ep_geneve_enabled(self, custid)

        if ep_geneve_ff == -1:
            #force disabled geneve
            self.logger.info("ep_geneve_ff is -1. will not enable geneve")
            return False
        
        cust_ep_cfg = CustEpaasConfigModel(self, custid, compute_region_id, cloud_provider, node_type=NODE_TYPE_SWG_PROXY, alt_node_type="-1")
        if cust_ep_cfg.get_entry():
            val = cust_ep_cfg.get_param("enable_panos_geneve")
            if val == '1':
                return True

        return False

    # This method checks if a service NIC can be added to the SWG Proxy instance
    def can_swg_proxy_add_service_nic(self, custid, compute_region_id, node_type, is_dynamic_instance):
        if node_type != NODE_TYPE_SWG_PROXY:
            # not swgproxy
            return True
        
        proxy_bringup = swgproxy_bringup(self)
        eproxy_outside_panos_enabled = proxy_bringup.gpcs_envoy_outside_panos_val(custid,
                                                                                int(compute_region_id),
                                                                                PROVIDER_GCP,
                                                                                NODE_TYPE_SWG_PROXY)
        if not eproxy_outside_panos_enabled:
            # service nic not needed on orch arch
            self.logger.info("Swgproxy old arch. Not adding service nic")
            return False

        # for swg proxy auto-scale or upgrade, check if original node has service nic
        inst_id = self.get_primary_instance(custid, compute_region_id, node_type)
        if inst_id == 0:
            if is_dynamic_instance:
                self.logger.error(f"Unable to find Swgproxy primary instance for custid {str(custid)} and region {str(compute_region_id)}")
            self.logger.info("Swgproxy new arch, primary instance. Adding service nic")
            return True
        inst = InstanceModel(iid=inst_id, dbh=self)
        if not inst.get_param("id"):
            self.logger.error(f"Swgproxy primary instance model not be found for inst_id={inst_id} custid {str(custid)} and region {str(compute_region_id)}")
            return False
        self.logger.info(f"Swgproxy primary instance is inst_id={inst_id}")
        cft_params = inst.get_param("salt_profile")
        try:
            cft_dict = json.loads(cft_params)
            is_service_nic_supported = cft_dict.get("is_service_nic_supported", False)
            self.logger.info(f"Swgproxy primary instance is inst_id={inst_id}. service_nic_supported={is_service_nic_supported}")
            return is_service_nic_supported
        except Exception as ex:
            self.logger.error(f"Invalid instance cft params for instance {inst_id}. {ex}")
            return False

    def get_new_instance_non_panos_int(self, version, node_type, region, machine_type,
                                           gpcs_instance_size=None, custid=0, is_passive=False, ha_peer_id=0,
                                           instance=None, cust_topology_id=0, spn_name=None, custnode=None):

        # README Note: The incoming region(edge_region) is a just a desired region of the customer, it could turn out that
        # its only a GPCS edge location of locality, The actual compute region(compute_region) is selected in this function.

        ret = False
        res = []
        allocated_instance = None
        self.logger.info(f"Locals: {locals()}")
        self.logger.info(
                "get_new_instance_int(): custnode_id: %d;" \
                "node_type: %s" % (cust_topology_id, node_type))
        try:
            instance_entry_dict = {}
            edge_region_name = None
            edge_region_idx = region
            compute_region_idx = None
            compute_region_name = None
            cloud_provider = PROVIDER_UNDECIDED

            if spn_name == None:
                raise Exception("spn_name is needed for the instance, no spn_name provided. Cannot continue.")

            if custnode is not None and custnode.default_version is not None and len(custnode.default_version) > 0:
                version = custnode.default_version

            # Node Type model.
            nodetypemodel = self.get_nodeTypeInstance(node_type, machine_type)
            if not nodetypemodel.node_id:
                self.error = (
                        "get_new_instance_int(): Unable to find nodetype id for node"
                        " type %s" % str(node_type))
                self.logger.error("%s" % str(self.error))
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                                  self.error)
                raise Exception(self.error)

            orch_cfg = OrchCfgModel(self).fields
            if orch_cfg == None:
                err_msg = ("Fatal error! Cannot retrieve the orchestrator "
                           "global configuration.")
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              err_msg)
                raise Exception(err_msg)
            else:
                self.logger.info("orch global config :%s\n" % str(orch_cfg))

            edge_region_name = gpcs_get_edge_region_name_from_edge_region_idx(self, edge_region_idx, custid=custid)
            if edge_region_name == None:
                err_msg = "Unable to get region name for id %s" % (str(edge_region_idx))
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                                  err_msg)
                raise Exception(err_msg)

            cloud_provider = gpcs_get_cloud_type_from_region_idx(self, edge_region_idx, custid=custid)
            if cloud_provider == None or cloud_provider == PROVIDER_UNDECIDED:
                err_msg = "Failed to get the cloud provider from region idx %s" % str(edge_region_idx)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                                  err_msg)
                raise Exception(err_msg)

            compute_region_idx = gpcs_get_compute_region_idx_from_edge_region_idx(self, edge_region_idx, custid=custid)
            compute_region_name = gpcs_get_edge_region_name_from_edge_region_idx(self, compute_region_idx, custid=custid)
            if compute_region_idx == None or compute_region_name == None:
                err_msg = (f"Failed to get the compute_region_idx or "
                           f"compute_region_name from edge region idx "
                           f"{str(edge_region_idx)}: {str(compute_region_idx)} "
                           f"or {str(compute_region_name)}")
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              err_msg)
                raise Exception(err_msg)

            # Create a customer object.
            customer = CustomerModel(custid=custid, dbh=self)
            if not customer.get_param("id"):
                self.error = ("get_new_instance_int():Unable to find customer in "
                              "the Database with id %d" % (custid,))
                self.logger.error(self.error)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              self.error)
                raise Exception(self.error)

            if self.avctx is not None:
                self.avctx.set_ctx(cust_id=custid,
                                   tenant_id=customer.get_param("acct_id"),
                                   sup_tenant_id=customer.get_param("super_acct_id"),
                                   tenant_name=customer.get_param("name"),
                                   region_id=compute_region_idx,
                                   region_name=compute_region_name,
                                   trace_id=(self.logger.get_enhanced_traceid() if not None else self.logger.uuid),
                                   node_type=nodetypemodel.node_id,
                                   cloud_provider=sys_get_cloud_provider_name(cloud_provider))

            pim_agent_loader_version = self.get_default_version(custnode, customer, "pim_agent_loader_version")
            pim_agent_version = self.get_default_version(custnode, customer, "pim_agent_version")

            if not instance:
                instance = InstanceModel(dbh=self)
                allocated_instance = instance

            instance.set_param("target_pim_agent_loader_version", pim_agent_loader_version)
            instance.set_param("target_pim_agent_version", pim_agent_version)

            instance.set_param("node_type", nodetypemodel.node_id)
            instance.set_param("state", 0)
            instance.set_param("custid", custid)
            instance.set_param("acct_id", customer.get_param("acct_id"))
            instance.set_param("slot_nr", 0)

            # TODO: Tejas - This might be needed for sending the logs.
            instance.set_param("cloudwatch_ns", "")
            instance.set_param("curr_tenants", 0)
            instance.set_param("pvt_ip", None)
            instance.set_param("public_ip", None)
            instance.set_param("username", nodetypemodel.username)

            instance.set_param("compute_region_idx", compute_region_idx)
            instance.set_param("compute_region_name", compute_region_name)

            # Set the correct cloud provider.
            instance.set_param("cloud_provider", self.get_db_enum_for_cloud_provider(cloud_provider))

            if cloud_provider in [PROVIDER_VMWARE_ESXI]:
                self.logger.info("Skipping VPC id related association since private clouds "
                                 "dont need vpc allocation from us")
                instance.set_param("vpc_id", None)
                instance.set_param("key_name", None)

            if node_type in [ NODE_TYPE_SASE_PRIV_REGION_LB_NAME, NODE_TYPE_SASE_PRIV_REGION_AGENT_NAME ]:
                # TODO: Set PIM agent version as well as pim agent loader.
                # pim_agent_version = self.get_default_version(custnode, customer, "pim_agent_version")
                # instance.set_param("target_pim_agent_version", pim_agent_version)
                self.logger.info("Setting default and target versions for PIM agent.")

            if not instance.save():
                self.error = ("get_new_instance_int(): Unable to save instance to"
                              " the Database\n")
                self.logger.error(self.error)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              self.error)
                raise Exception(self.error)

            # After the first invocation of "save", we can build
            # the name and alias for the instance.
            # Invoke "save" again so that the name is persisted.
            instance.set_param("version", version)
            instance.build_name()
            if ha_peer_id:
                instance.set_param("alias", spn_name + "-b")
                instance.set_param("spn_name", spn_name)
            else:
                instance.set_param("alias", spn_name + "-a")
                instance.set_param("spn_name", spn_name)
            instance.set_param("cloudwatch_ns", "cust%s/%s" %
                               (customer.get_param("acct_id"), instance.get_param("name")))
            if not instance.save():
                self.error = ("get_new_instance_int(): Unable to save instance to"
                              " the Database\n")
                self.logger.error(self.error)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              self.error)
                raise Exception(self.error)

            if ha_peer_id:
                instance.set_param("ha_peer", ha_peer_id)
                instance.set_param("clusterid", ha_peer_id)
            else:
                instance.set_param("ha_peer", 0)
                instance.set_param("clusterid", instance.get_param("id"))

            otp = generate_otp(self.logger, instance.get_param("name"), instance.get_param("id"))
            if otp is None:
                self.error = (f"Failed to generate otp for instance "
                              f"{str(instance.get_param('id'))}")
                self.logger.error(self.error)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                          self.error)
                raise Exception(self.error)

            instance_entry_dict['node_type'] = node_type
            instance_entry_dict['node_type_id'] = nodetypemodel.node_id
            instance_entry_dict['machine_type'] = machine_type

            # TODO: Tejas: This capacity_type to come from instance profile.
            instance_entry_dict['capacity_type'] = self.capacity_type
            instance_entry_dict['region'] = compute_region_idx
            instance_entry_dict['region_name'] = gpcs_get_edge_region_name_from_edge_region_idx(self,
                                                                                            compute_region_idx, custid=custid)
            instance_entry_dict['cust_id'] = customer.get_param("id")
            instance_entry_dict['version'] = version
            instance_entry_dict['acct_id'] = customer.get_param("acct_id")
            instance_entry_dict['customer_name'] = customer.get_param("name")
            instance_entry_dict['route53_acct'] = orch_cfg['route53acct']
            instance_entry_dict['bucket_name'] = orch_cfg['bucket-name']
            instance_entry_dict['panrepo_bucket_name'] = orch_cfg['panrepo_bucket_name']
            instance_entry_dict['serial_no'] = "Dummy_serial_no"

            # Note we are using the alias here, it is same as name at this point.
            instance_entry_dict['instance_name'] = instance.get_param("name")

            # TODO: Tejas: We need to get the alias from instance profile.
            instance_entry_dict['instance_alias'] = instance.get_param("alias")
            instance_entry_dict['instance_id'] = instance.get_param("id")
            instance_entry_dict['cert_fetch_otp'] = str(otp)
            instance_entry_dict['saas_gpcs_api_endpoint'] = orch_cfg.get('saas_gpcs_api_endpoint')
            instance_entry_dict['cloud_provider'] = instance.get_param("cloud_provider")
            instance_entry_dict['ha_peer'] = instance.get_param("ha_peer")
            instance_entry_dict['super_custid'] = customer.get_param("super_acct_id")
            instance_entry_dict['custnode_id'] = cust_topology_id
            instance_entry_dict['edge_region_idx'] = edge_region_idx
            if custnode:
                instance_entry_dict['host_profile_id'] = custnode.host_profile_id
                instance_entry_dict['service_node_type'] = custnode.svc_node_type

            # Name:
            salt_profile_name = get_salt_profile_name_for_instance_entry(self, instance_entry_dict,
                                                                         is_passive, False)
            if salt_profile_name != None:
                instance.set_param("salt_profile_name", salt_profile_name)
            else:
                self.error = (f"Failed to create the salt profile name. "
                              f"locals: {str(locals())}")
                self.logger.error(self.error)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              self.error)
                raise Exception(self.error)

            cft_dict = get_salt_profile_for_instance_entry(self, instance_entry_dict, cloud_provider,
                                                           is_passive, False, False, node_type)
            if cft_dict == None:
                self.error = (f"get_salt_profile_for_instance_entry: Failed "
                              f"to get the cft params. {str(locals())}")
                self.logger.error(self.error)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              self.error)
                raise Exception(self.error)
            cft_params = json.dumps(cft_dict)

            # populate the interface params now
            if self.populate_network_config_for_instance(cft_dict["NetworkConfig"], instance) == False:
                raise Exception("Failed to populate network config for instance!")

            res = [instance]
            self.logger.info("CFT_profile is \n%s" % str(cft_params))
            instance.set_param("salt_profile", cft_params)

            if not instance.save():
                self.error = ("get_new_instance_int(): Unable to save instance")
                self.logger.error(self.error)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              self.error)
                raise Exception(self.error)

            ret = True
        except Exception as E:
            self.logger.error(
                "Exception %s occured: %s %s\n" % (str(E.args), str(traceback.format_exc()), str(locals())))
            publish_avisar_event_with_tenant_region_nodetype_key(self.avctx, self.logger, ORCHESTRATION_INSTANCE_CREATION_FAILED, str(E.args), METRIC_SEVERITY_CRITICAL)
            ret = False
            res = []
            try:
                if allocated_instance != None and allocated_instance.get_param("id") != None:
                    alloc_instance_id = allocated_instance.get_param("id")
                    if allocated_instance.delete(self, allocation_failed=True) == True:
                        self.logger.error("Deleted allocated instance with id %s" % str(alloc_instance_id))
                    else:
                        self.logger.error("Failed to delete the allocated instance %s" % str(alloc_instance_id))
            except Exception as ex:
                self.logger.error("Exception occured in Exception %s. %s" %
                          (str(ex.args), str(traceback.format_exc())))

        finally:
            return DbResult(ret, res)

    def get_new_instance_int(self, version, node_type, region, machine_type,
                             gpcs_instance_size=None, custid=0, is_passive=False, ha_peer_id=0,
                             instance=None, is_dynamic_instance=None, upgrade_creation=0,
                             is_clean_pipe=0, cust_topology_id=0, avail_domain="AVAILABILITY_DOMAIN_1",
                             inbound_access=None, spn_name=None, instance_role=INSTANCE_ROLE_LEGACY,
                             slot_nr=0, inter_instance_psk=None,okyo_edge_site_id=0,
                             has_nat_gateway=False, sase_fabric=False, is_pa_connector_managed=False,
                             is_instance_behind_nlb=False, is_commit_validate=False,
                             is_two_phase_upgrade_call=False, old_instance_id=-1, is_premium_zone=False,
                             colo_sc=False, virtual_ip=None, alt_node_type=-1, custnode=None, sc_map_id=0):

        # README Note: The incoming region(edge_region) is a just a desired region of the customer, it could turn out that
        # its only a GPCS edge location of locality, The actual compute region(compute_region) is selected in this function.
        ret = False
        res = []
        allocated_instance = None
        self.logger.info(f"Locals: {locals()}")
        self.logger.info(
                "get_new_instance_int(): is_clean_pipe: %d, custnode_id: %d;" \
                "is_pa_connector_managed: %d node_type: %s, alt_node_type: %s" %
                    (is_clean_pipe, cust_topology_id, is_pa_connector_managed, node_type, alt_node_type))
        try:
            instance_entry_dict = {}
            edge_region_name = None
            edge_region_idx = region
            compute_region_idx = None
            compute_region_name = None
            cloud_provider = PROVIDER_UNDECIDED
            gw_interface_support = False
            colo_interface_support = False
            service_nic_supported = False
            datapath_on_shared_vpc = False
            is_interconnect_onramp = False
            frr_enabled = 0
            if custnode is not None and custnode.default_version is not None and len(custnode.default_version) > 0:
                version = custnode.default_version

            # Node Type model.
            nodetypemodel = self.get_nodeTypeInstance(node_type, machine_type)
            if not nodetypemodel.node_id:
                self.error = (
                    "get_new_instance_int(): Unable to find nodetype id for node"
                    " type %s" % str(node_type))
                self.logger.error("%s" % str(self.error))
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              self.error)
                raise Exception(self.error)

            if is_env_fedramp_il5(logger=self.logger) and nodetypemodel.node_id in [NODE_TYPE_SERVICE_CONN]:
                gw_interface_support = True

            if is_dynamic_instance and nodetypemodel.node_id in [NODE_TYPE_GP_GATEWAY]:
                # Get the PanOS version from the pinned instance, if found and valid
                res = self.get_pinned_inst_ref(custid, compute_region_idx, nodetypemodel.node_id)
                if res.ok:
                    pinned_inst_id = res.result[0]
                    pinned_inst_ref = InstanceModel(iid=pinned_inst_id, dbh=self)
                    if not pinned_inst_ref.get_param("id") and \
                            pinned_inst_ref.get_param("version") is not None and \
                            len(pinned_inst_ref.get_param("version")) > 0:
                        version = pinned_inst_ref.get_param("version")

            orch_cfg = OrchCfgModel(self).fields
            if orch_cfg == None:
                err_msg = ("Fatal error! Cannot retrieve the orchestrator "
                           "global configuration.")
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              err_msg)
                raise Exception(err_msg)
            else:
                self.logger.info("orch global config :%s\n" % str(orch_cfg))

            edge_region_name = gpcs_get_edge_region_name_from_edge_region_idx(self, edge_region_idx, custid=custid)
            if edge_region_name == None:
                err_msg = "Unable to get region name for id %s" % (str(edge_region_idx))
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              err_msg)
                raise Exception(err_msg)

            cloud_provider = gpcs_get_cloud_type_from_region_idx(self, edge_region_idx, custid=custid)
            if cloud_provider == None or cloud_provider == PROVIDER_UNDECIDED:
                err_msg = "Failed to get the cloud provider from region idx %s" % str(edge_region_idx)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              err_msg)
                raise Exception(err_msg)

            # CP GCP and Colo SC need colo intf
            if cloud_provider == PROVIDER_GCP \
                    and nodetypemodel.node_id in [NODE_TYPE_SERVICE_CONN] \
                    and colo_sc == True:
                # colo sc has extra colo intf and capacity type = 'PA-CAP700'
                colo_interface_support = True
            compute_region_idx = gpcs_get_compute_region_idx_from_edge_region_idx(self, edge_region_idx, custid=custid)
            compute_region_name = gpcs_get_edge_region_name_from_edge_region_idx(self, compute_region_idx, custid=custid)
            if compute_region_idx == None or compute_region_name == None:
                err_msg = (f"Failed to get the compute_region_idx or "
                       f"compute_region_name from edge region idx "
                       f"{str(edge_region_idx)}: {str(compute_region_idx)} "
                       f"or {str(compute_region_name)}")
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              err_msg)
                raise Exception(err_msg)

            # Service Nic is enabled only for MU and RN as of GPCS 5.0.0. Enable this for other FW as needed.
            supported_clouds = [PROVIDER_GCP, PROVIDER_AWS, PROVIDER_OCI]
            if (cloud_provider in supported_clouds \
                and nodetypemodel.node_id in self.get_service_nic_node_types(cloud_provider) ):
                if self.get_service_nic_feature_flag(cloud_provider) == True \
                    and self.can_swg_proxy_add_service_nic(custid, compute_region_idx, nodetypemodel.node_id, is_dynamic_instance):
                    service_nic_supported = True

                if self.get_is_dp_nic_on_shared_vpc_feature_flag() == True :
                    datapath_on_shared_vpc = True

            # Create a customer object.
            customer = CustomerModel(custid=custid, dbh=self)
            if not customer.get_param("id"):
                self.error = ("get_new_instance_int():Unable to find customer in "
                              "the Database with id %d" % (custid,))
                self.logger.error(self.error)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              self.error)
                raise Exception(self.error)

            if self.avctx is not None:
                self.avctx.set_ctx(cust_id=custid,
                                   tenant_id=customer.get_param("acct_id"),
                                   sup_tenant_id=customer.get_param("super_acct_id"),
                                   tenant_name=customer.get_param("name"),
                                   region_id=compute_region_idx,
                                   region_name=compute_region_name,
                                   trace_id=(self.logger.get_enhanced_traceid() if not None else self.logger.uuid),
                                   node_type=nodetypemodel.node_id,
                                   cloud_provider=sys_get_cloud_provider_name(cloud_provider))

            if not instance:
                instance = InstanceModel(dbh=self)
                allocated_instance = instance
            instance.set_param("node_type", nodetypemodel.node_id)
            instance.set_param("alt_node_type", alt_node_type)
            instance.set_param("state", 0)
            instance.set_param("custid", custid)
            instance.set_param("acct_id", customer.get_param("acct_id"))
            instance.set_param("cloudwatch_ns", "")
            instance.set_param("curr_tenants", 0)
            instance.set_param("pvt_ip", None)
            instance.set_param("public_ip", None)
            instance.set_param("username", nodetypemodel.username)
            #instance.edge_region_id = edge_region_idx
            #instance.edge_region_name = edge_region_name
            instance.set_param("is_dynamic_instance", is_dynamic_instance)
            instance.set_param("upgrade_creation", upgrade_creation)
            instance.set_param("spn_name", spn_name)
            instance.set_param("slot_nr", slot_nr)
            instance.set_param("has_nat_instance", has_nat_gateway)
            instance.set_param("is_pa_connector_managed", is_pa_connector_managed)
            instance.set_param("is_instance_behind_nlb", is_instance_behind_nlb)
            if (nodetypemodel.ha_reqd
                    and is_no_passive_instances_enabled(self.logger, tenant_id=customer.get_param("acct_id"),
                                                        is_pa_connector_managed=is_pa_connector_managed) == True):
                instance.set_param("no_passive_instance", True)

            if cloud_provider in [ PROVIDER_VMWARE_ESXI ] and nodetypemodel.node_id in [ NODE_TYPE_REMOTE_NET ]:
                # For RN's in ESXi we do not have a passive instance
                self.logger.info(f"For cloud_provider {PROVIDER_VMWARE_ESXI}, ha is not supported for instances.")
                instance.set_param("no_passive_instance", True)

            is_interconnect_onramp = False
            if custnode is not None and custnode.transport_type == "pa-connect":
                instance_entry_dict['interconnect_name'] = custnode.interconnect_name
                if custnode.node_type == NODE_TYPE_REMOTE_NET:
                    is_interconnect_onramp = True
                    is_instance_behind_nlb = True
                    instance.set_param("is_instance_behind_nlb", is_instance_behind_nlb)
                    if nodetypemodel.ha_reqd:
                        instance.set_param("no_passive_instance", True)
            instance.set_param("is_interconnect_onramp", is_interconnect_onramp)

            if colo_interface_support is True and virtual_ip is not None:
                # set the virtual ip for ip alias in gcp
                instance.set_param("vgw_vip", virtual_ip)

            self.set_sc_map_id(instance, customer.get_param("acct_id"), nodetypemodel.node_id,
                               cloud_provider, orch_cfg, compute_region_idx, is_passive, sc_map_id)

            # If this is an upgrade creation during 2-phase upgrade:
            #     - Set "upgrade_status" for the instance to be "PRE-UPGRADE".
            if is_two_phase_upgrade_call and upgrade_creation:
                self.logger.info("Two phase upgrade invocation; set "
                                 "upgrade status to PRE-UPGRADE")
                instance.set_param("upgrade_status", 'PRE-UPGRADE')

            if (instance_role == INSTANCE_ROLE_MP and cloud_provider != PROVIDER_AWS) \
                    or has_nat_gateway or is_instance_behind_nlb or cloud_provider == PROVIDER_OCI:
                self.logger.info("Set use_PBF as False for the instance")
                instance.set_param("use_PBF", False)

            if inbound_access is not None and inbound_access == 'dedicated':
                instance.set_param("is_dedicated_inbound_instance", 1)
            else:
                instance.set_param("is_dedicated_inbound_instance",  0)

            # This node is a pinned instance.
            if node_type == "GPGATEWAY" and instance.get_param("is_dynamic_instance") == False:
                instance.set_param("is_pinned_instance", True)

            if node_type == "SWGPROXY" and instance.get_param("is_dynamic_instance") == False:
                instance.set_param("is_pinned_instance", True)

            if node_type == "BINHPROXY" and instance.get_param("is_dynamic_instance") == False:
                instance.set_param("is_pinned_instance", True)

            if node_type == "PROBEVM" and instance.get_param("is_dynamic_instance") == False:
                instance.set_param("is_pinned_instance", True)

            # The compute region to be decided by the capacity manager.
            instance.set_param("compute_region_idx", compute_region_idx)

            instance.set_param("compute_region_name", compute_region_name)

            # set the right value for gw_capabilities
            #default->0 (None), 1(GPclients), 2 (OkyoClients). For MVP its either 1 or 2 for node_type GPGW. All others 0.
            if node_type == "GPGATEWAY":
                gw_capabilities = 2 if okyo_edge_site_id > 0 else 1
                instance.set_param("gw_capabilities", gw_capabilities)

            # Set the correct cloud provider.
            self.logger.info(f"get_new_instance_int: Get cloud_provider for cloud type {cloud_provider} for "
                             f"compute region {compute_region_name} id {compute_region_idx}")
            instance.set_param("cloud_provider", self.get_db_enum_for_cloud_provider(cloud_provider))
            self.logger.info(f"get_new_instance_int: Set the cloud provider to "
                             f"{self.get_db_enum_for_cloud_provider(cloud_provider)}"
                             f" in IM for compute region {compute_region_name} id {compute_region_idx}")

            if cloud_provider in [ PROVIDER_VMWARE_ESXI ]:
                self.logger.info("Skipping VPC id related association since private clouds "
                                 "dont need vpc allocation from us")
                instance.set_param("vpc_id", None)
                instance.set_param("key_name", None)
            else:
                vpc_id = self.get_vpc_id_for_instance(compute_region_idx,
                                  compute_region_name,
                                  False if gw_interface_support else is_passive,
                                  cloud_provider,
                                  custid=custid,
                                  is_premium_zone=is_premium_zone)
                if not vpc_id:
                    err_msg = (f"Failed to get VPC Id for region "
                               f"{str(edge_region_idx)}")
                    self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                                  err_msg)
                    raise Exception(err_msg)
                instance.set_param("vpc_id", vpc_id)

                instance.set_param("key_name", self.get_vpc_sec_key_name(compute_region_idx,
                                                              compute_region_name,
                                                              False if gw_interface_support else is_passive,
                                                              cloud_provider,
                                                              custid=custid, is_premium_zone=is_premium_zone))

            # This could change depending on max capacity supported for the cloud in which case
            # we need to use the cloud_provider
            if cloud_provider not in [PROVIDER_VMWARE_ESXI]:
                (update_max_capacity, new_max_capacity) = \
                self.get_updated_max_capacity_for_instance(custid, PROVIDER_COMMON_OLD_MAX_CAPACITY, spn_name)
                instance.set_param("max_capacity", new_max_capacity)
                if instance.get_param("max_capacity") == None or instance.get_param("max_capacity") == 0:
                    self.error = (f"Unable to get instance max_capacity for "
                                  f"(Node Type, Region id, Customer id): "
                                  f"({node_type}, {compute_region_idx}, {custid})")
                    self.logger.error(self.error)
                    self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                                  self.error)
                    raise Exception(self.error)

            if custnode and custnode.host_profile_id:
                self.logger.info(f"Setting host profile id {custnode.host_profile_id} "
                                 f"and service node type {custnode.svc_node_type} for private region")
                instance_entry_dict['host_profile_id'] = custnode.host_profile_id
                instance_entry_dict['service_node_type'] = custnode.svc_node_type

            saas_agent_version = self.get_default_version(custnode, customer, "saas_agent_version")
            mars_agent_profiles_version = self.get_default_version(custnode, customer, "mars_agent_profiles_version")
            wifclient_version = self.get_default_version(custnode, customer, "wifclient_version")
            dirsyncd_version = self.get_default_version(custnode, customer, "dirsyncd_version")
            collectd_version = self.get_default_version(custnode, customer, "collectd_version")
            cmd_executor_version = self.get_default_version(custnode, customer, "cmd_executor_version")
            ipsyncd_version = self.get_default_version(custnode, customer, "ipsyncd_version")
            healthd_version = self.get_default_version(custnode, customer, "healthd_version")
            pacached_version = self.get_default_version(custnode, customer, "pacached_version")
            ddns_version = self.get_default_version(custnode, customer, "ddns_version")
            zdns_fwdd_version = self.get_default_version(custnode, customer, "zdns_fwdd_version")
            sasemsgd_version = self.get_default_version(custnode, customer, "sasemsgd_version")
            mtdc_version = self.get_default_version(custnode, customer, "mtdc_version")

            instance.set_param("logging_fqdn", customer.get_param("logging_fqdn"))
            instance.set_param("dlp_server_fqdn", customer.get_param("dlp_server_fqdn"))
            instance.set_param("content_version", customer.get_param("contentver"))
            instance.set_param("target_av_version", customer.get_param("avver"))
            instance.set_param("collectd_cfg", customer.get_param("collectd_cfg"))

            instance.set_param("target_saas_agent_version", saas_agent_version)
            instance.set_param("target_mars_agent_profiles_version", mars_agent_profiles_version)
            instance.set_param("target_wifclient_version", wifclient_version)
            instance.set_param("target_dirsyncd_version", dirsyncd_version)
            instance.set_param("target_collectd_version", collectd_version)
            instance.set_param("target_cmd_executor_version", cmd_executor_version)
            instance.set_param("target_ipsyncd_version", ipsyncd_version)
            instance.set_param("target_healthd_version", healthd_version)
            instance.set_param("target_pacached_version", pacached_version)
            instance.set_param("target_ddns_version", ddns_version)
            instance.set_param("target_zdns_fwdd_version", zdns_fwdd_version)
            instance.set_param("target_sasemsgd_version", sasemsgd_version)
            instance.set_param("target_mtdc_version", mtdc_version)



            # RN/SC FQDN
            if cloud_provider in [PROVIDER_VMWARE_ESXI]:
                self.logger.info("FQDN is not supported for Private region")
            else:
                if nodetypemodel.node_id in [NODE_TYPE_SERVICE_CONN, NODE_TYPE_REMOTE_NET] and not colo_sc \
                        and self.get_rnsc_fqdn_ff(customer.get_param("acct_id")):
                    if saas_agent_version_supports_fqdn(self.logger, customer.get_param("saas_agent_version")):
                        instance.set_param("fqdn_enabled", True)
                        if plugin_version_supports_fqdn(self.logger, customer.get_param("panorama_plugin_version")):
                            instance.set_param("fqdn_only_flag", True)

            if node_type == 'NAT':
                nat_plugin_dict = customer.get_plugin_dict_for_nat_instance(compute_region_idx)
                self.logger.info("using plugin versions from cust_nat_mgmt_table: {}".format(nat_plugin_dict))
                for plugin, ver in nat_plugin_dict.items():
                    if ver:
                        instance.set_param(f"target_{plugin}_version", ver)

            # Populate sp-interconnect and check if VPC exists, Can throw exception on failure .
            self.populate_sp_interconnect_details(instance, customer, region, instance_entry_dict, nodetypemodel)

            # check if masque feature enabled
            instance_entry_dict['is_masque_enabled'] = is_masque_feature_enabled(dbh=self, custid=custid)

            if not instance.save():
                self.error = ("get_new_instance_int(): Unable to save instance to"
                              " the Database\n")
                self.logger.error(self.error)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              self.error)
                raise Exception(self.error)

            # After the first invocation of "save", we can build
            # the name and alias for the instance.
            # Invoke "save" again so that the name is persisted.
            instance.set_param("version", version)
            instance.build_name()
            if cloud_provider in [ PROVIDER_VMWARE_ESXI ]:
                if node_type in [ "GPGATEWAY", "FIREWALL" ]:
                    instance.set_param("alias", spn_name)
            else:
                instance.build_alias()
            instance.set_param("cloudwatch_ns", "cust%s/%s" %
                               (customer.get_param("acct_id"), instance.get_param("name")))
            if not instance.save():
                self.error = ("get_new_instance_int(): Unable to save instance to"
                              " the Database\n")
                self.logger.error(self.error)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              self.error)
                raise Exception(self.error)

            # Now that we have invoked "save()" on the instance object, we
            # are guaranteed to have the instance id.
            # If this is an upgrade creation during 2-phase upgrade,
            # and DHCP Pool allocation is enabled for this tenant:
            #     - Invoke stored-procedure "pool_allocation_upgrade_transient"
            #       in order to update the "transient_inst_id" to be the
            #       current instance id for the older instance being upgraded.
            if (is_two_phase_upgrade_call and upgrade_creation
                and not is_dhcp_pool_allocation_enabled(custid, dbh=self)
                and not is_ciam_enabled_from_ff(self.logger, custid, cfg)):
                self.logger.info("Two phase upgrade invocation; no "
                                 "DHCP Allocation enabled for tenant; "
                                 "invoke update_pool_allocation_table"
                                 "(%s, %s)" % (old_instance_id,
                                 instance.get_param("id")))
                ret = self.update_pool_allocation_table(old_instance_id,
                                            instance.get_param("id"))
                if not ret:
                    errmsg = ("Failed during updating pool allocation table "
                              "for instance ids: (%s, %s)" %
                              (old_instance_id, instance.get_param("id")))
                    self.logger.error(errmsg)
                    self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                                  errmsg)
                    raise Exception(errmsg)

            # Case 1: When SRE feature flag frr_feature_flag is enabled, set frr enabled
            if cloud_provider not in [PROVIDER_VMWARE_ESXI]:
                if is_frr_enabled_from_ff(self.logger, customer.get_param("acct_id")) == True:
                    frr_enabled = 1
                    self.logger.info("FRR enabled from SRE feature flag")
                # Case 2: When pba is enabled. set frr enabled
                if is_pba_enabled(self.logger, customer.get_param("super_acct_id")):
                    frr_enabled = 1
                    self.logger.info("FRR enabled when pba is enabled")

            if is_clean_pipe == 1 or node_type == "PROBEVM":
                instance.set_param("clusterid", instance.get_param("id"))
                instance.set_param("loopback_ip_offset", 0)
                instance.set_param("ha_peer", 0)
            else:
                if ha_peer_id:
                    instance.set_param("ha_peer", ha_peer_id)
                    # TODO add a check for GP Node which needs only one ip
                    instance.set_param("clusterid", ha_peer_id)
                else:
                    instance.set_param("ha_peer", 0)
                    instance.set_param("clusterid", instance.get_param("id"))

                # For DP instance we would skip allocation of the loopback IP address.
                # CYR-40724 NLB instances don't need loopback IP since it's not a real instance
                if instance_role not in [INSTANCE_ROLE_MP] \
                        and node_type not in [NODE_TYPE_NLB_INSTANCE_NAME, NODE_TYPE_SERVICE_ILB_NAME]:
                    # Get the flag to indicate if DHCP Pool allocation is
                    # enabled for the tenant.
                    is_dhcp_allocation_enabled = is_dhcp_pool_allocation_enabled(
                                                     custid,
                                                     dbh=self)

                    # CYR-44577 saas_agent is reserving ************* for loopback.253
                    if (is_china_env() or is_swg_aws_enabled(self.logger, customer.get_param("acct_id"))) and node_type == "SWGPROXY":
                        if is_china_env():
                            self.logger.info("Setting is_dhcp_allocation_enabled for china env")
                        is_dhcp_allocation_enabled=True

                    sql = "call allocate_infra_ip_v2 (%s, %s, %s, %s)"
                    params = (instance.get_param("id"), custid, ha_peer_id,
                              is_dhcp_allocation_enabled)
                    cursor = self.get_cursor()
                    sql_loopbk = "select loopback_ip_offset from instance_master where id=%s"
                    try:
                        cursor.execute(sql, params)
                        self.cursorclose(cursor)
                        cursor = self.get_cursor()
                        cursor.execute(sql_loopbk, (instance.get_param("id"),))
                        ret = cursor.fetchone()
                        self.cursorclose(cursor)
                        if not ret or not ret[0]:
                            self.error = ("Failed to allocate loopback ip for instance %s" % (str(instance.get_param("id")),))
                            self.logger.error("Failed to allocate loopback ip for instance %s" % (str(instance.get_param("id")),))
                            self.update_status_msg_for_cust_topology_node(
                                    cust_topology_id, self.error)
                            instance.set_avisar_ctx(self, self.avctx)
                            publish_avisar_event_with_tenant_region_nodetype_key(self.avctx, self.logger,
                                                                                 ORCHESTRATION_INSTANCE_LOOPBACK_IP_FETCH_FAILED,
                                                                                 "Failed to allocate loopback ip for instance %s" % str(
                                                                                     instance.get_param("id")),
                                                                                 METRIC_SEVERITY_CRITICAL)
                            raise Exception(self.error)
                        instance.set_param("loopback_ip_offset", ret[0])
                        self.logger.info("Allocated tunnel subnet %s to the instance" % str(instance.get_param("tunnelsubnet")))
                    except Exception as ex:
                        self.error = ("Failed to allocate loopback ip for instance %s: %s" % (str(instance.get_param("id")), str(ex)))
                        self.logger.error("Failed to allocate loopback ip for instance %s: %s" % (str(instance.get_param("id")), str(ex)))
                        self.cursorclose(cursor)
                        self.update_status_msg_for_cust_topology_node(
                                cust_topology_id, self.error)
                        instance.set_avisar_ctx(self, self.avctx)
                        publish_avisar_event_with_tenant_region_nodetype_key(self.avctx, self.logger,
                                                                             ORCHESTRATION_INSTANCE_LOOPBACK_IP_FETCH_FAILED,
                                                                             "Failed to allocate loopback ip for instance %s: %s" % (
                                                                             str(
                                                                                 instance.get_param("id")), str(ex)),
                                                                             METRIC_SEVERITY_CRITICAL)
                        raise Exception(self.error)

            otp = generate_otp(self.logger, instance.get_param("name"), instance.get_param("id"))
            if otp is None:
                self.error = (f"Failed to generate otp for instance "
                              f"{str(instance.get_param('id'))}")
                self.logger.error(self.error)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              self.error)
                raise Exception(self.error)

            if node_type == "PROBEVM":
                gp_gw_domain = orch_cfg['gcp_env']
            else:
                gp_gw_domain = get_portal_base(customer.get_param("acct_id"), self.logger)

            # TODO: Most of the below is cloud specific.
            instance_entry_dict['node_type'] = node_type
            instance_entry_dict['node_type_id'] = nodetypemodel.node_id
            instance_entry_dict['machine_type'] = machine_type
            instance_entry_dict['gpcs_instance_size'] = gpcs_instance_size

            if is_dynamic_instance and not upgrade_creation and nodetypemodel.node_id in MU_VERTICAL_SCALE_NODE_TYPES:
                # This is created by auto-scale. capacity_type and cloud_machine_type should copy from pinned instance
                pinned_cfg = self.get_pinned_cfg(custid, compute_region_idx, nodetypemodel.node_id)
                if pinned_cfg:
                    self.capacity_type = pinned_cfg.get('PrimaryCapacityType', self.capacity_type)
                    self.cloud_machine_type = pinned_cfg.get('InstType', self.cloud_machine_type)

            # TODO: Tejas: This capacity_type to come from instance profile.
            instance_entry_dict['capacity_type'] = self.capacity_type
            instance_entry_dict['cloud_machine_type'] = self.cloud_machine_type
            instance_entry_dict['cpu_platform'] = self.cpu_platform
            instance_entry_dict['market_type'] = self.market_type
            instance_entry_dict['dpdk_qcount'] = self.dpdk_qcount
            instance_entry_dict['region'] = compute_region_idx
            instance_entry_dict['region_name'] = gpcs_get_edge_region_name_from_edge_region_idx(self,
                                                                                                compute_region_idx,
                                                                                                custid=customer.get_param("id"))
            instance_entry_dict['cust_id'] = customer.get_param("id")
            instance_entry_dict['version'] = version
            instance_entry_dict['acct_id'] = customer.get_param("acct_id")
            instance_entry_dict['customer_name'] = customer.get_param("name")
            instance_entry_dict['gp_gw_domain'] = gp_gw_domain
            instance_entry_dict['route53_acct'] = orch_cfg['route53acct']
            instance_entry_dict['bucket_name'] = orch_cfg['bucket-name']
            instance_entry_dict['panrepo_bucket_name'] = orch_cfg['panrepo_bucket_name']
            instance_entry_dict['serial_no'] = "Dummy_serial_no"
            # Note we are using the alias here, it is same as name at this point.
            instance_entry_dict['instance_name'] = instance.get_param("name")
            instance_entry_dict['instance_alias'] = instance.get_param("alias")
            instance_entry_dict['instance_id'] = instance.get_param("id")
            instance_entry_dict['clusterid'] = instance.get_param("clusterid")
            instance_entry_dict['iam_role'] = customer.get_param("iam_role")
            instance_entry_dict['cert_fetch_otp'] = str(otp)
            instance_entry_dict['saas_gpcs_api_endpoint'] = orch_cfg.get('saas_gpcs_api_endpoint')
            instance_entry_dict['cloud_provider'] = instance.get_param("cloud_provider")
            instance_entry_dict['ha_peer'] = instance.get_param("ha_peer")
            instance_entry_dict['super_custid'] = customer.get_param("super_acct_id")
            instance_entry_dict['custnode_id'] = cust_topology_id
            instance_entry_dict['avail_domain'] = avail_domain
            instance_entry_dict['edge_region_idx'] = edge_region_idx
            instance_entry_dict['sase_fabric'] = sase_fabric
            instance_entry_dict['has_nat_gateway'] = has_nat_gateway
            instance_entry_dict["gw_interface_support"] = False
            instance_entry_dict["colo_interface_support"] = False
            instance_entry_dict["gw_interface_virtual_ip_support"] = False
            instance_entry_dict["is_using_sp_interconnect"] = instance.get_param("is_using_sp_interconnect")
            instance_entry_dict["no_passive_instance"] = instance.get_param("no_passive_instance")
            instance_entry_dict["is_interconnect_onramp"] = instance.get_param("is_interconnect_onramp")
            # Enabled gw interface support for SCs deployed in AWS Govcloud
            if gw_interface_support:
                instance_entry_dict["gw_interface_support"] = True
                if instance.get_param('cloud_provider') in [PROVIDER_AWS]:
                    instance_entry_dict["gw_interface_virtual_ip_support"] = True

            # Enabled gw interface support for SCs deployed in AWS Govcloud
            if colo_interface_support:
                instance_entry_dict["colo_interface_support"] = True
                if is_instance_behind_nlb and custnode is not None:
                    instance_entry_dict["colo_ilb_name"] = "colo-ilb-" + custnode.name.partition(":")[0] + "-" + compute_region_name
                if instance.get_param('cloud_provider') in [PROVIDER_GCP]:
                    instance_entry_dict["colo_virtual_ip_support"] = True

            # Add support for App Acceleration
            # CYR-36919 At this time we are not allowing service-nic for firewalls that belong to SP
            # root_tsg_id is set only for tenants that belong to SP
            # Only for 5G SP interconnect nodes we will enable service nic.
            if (customer.get_param("root_tsg_id") is not None
                    and str(customer.get_param("root_tsg_id")) != '0'
                    and not instance_entry_dict.get("is_5g_enabled", False)):
                instance_entry_dict["is_service_nic_supported"] = False
            else:
                instance_entry_dict["is_service_nic_supported"] = service_nic_supported

            self.logger.info(f"get_new_instance_int: Service NIC supported {instance_entry_dict.get('is_service_nic_supported', False)} for {instance.get_param('name')}")
            # check if CNAT support is present in case of GCP
            instance_entry_dict['is_gcp_cnat_support_enabled'] = self.is_gcp_cnat_supported(cloud_provider, custid, compute_region_idx)

            # In cases where the Datapath is already on shared VPC, we ignore the below flag.
            # At the time of CYR-32605: SP_Interconnect is the only known case for this.
            if instance_entry_dict["is_using_sp_interconnect"] == True:
                datapath_on_shared_vpc = False
                # SP Interconnect feature is not supported with GCPC NAT
                instance_entry_dict['is_gcp_cnat_support_enabled'] = False 

            instance_entry_dict["is_datapath_on_shared_vpc"] = datapath_on_shared_vpc

            instance_entry_dict['is_instance_behind_nlb'] = is_instance_behind_nlb
            if cloud_provider in [ PROVIDER_VMWARE_ESXI ]:
                self.logger.info("Skipping NLB and routing features for SASE private region")
            else:
                instance_entry_dict['commit_validate'] = is_commit_validate
                instance_entry_dict['frr-enabled'] = frr_enabled
                instance_entry_dict['is_nlb_supported'] = \
                    customer.get_param("is_nlb_supported")
                instance_entry_dict['is_ngpa_protocol_enabled'] = \
                    customer.get_param("is_ngpa_protocol_enabled")
                instance_entry_dict['is_central_cache_supported'] = \
                    customer.get_param("is_central_cache_supported")

                # services_endpoint_mapping uses aws_env values for pa_env
                # (e.g. prod2, prod3, qa, qa2 etc), hence using aws_env
                # value for lookup
                self.logger.info("Using pa_env: %s for cache services endpoint "
                                 "lookup" % orch_cfg.get('aws_env'))
                instance_entry_dict['central_cache_service_endpoint'] = \
                    self.get_central_cache_service_endpoint(
                        orch_cfg.get('aws_env'), customer.get_param("acct_id"), compute_region_idx)
                instance_entry_dict['central_cache_service_backup_endpoint'] = \
                    self.get_central_cache_service_backup_endpoint(
                        orch_cfg.get('aws_env'), customer.get_param("acct_id"), compute_region_idx)
                instance_entry_dict['ciam_service_endpoint'] = \
                    self.get_ciam_service_endpoint(orch_cfg.get('aws_env'))
                instance_entry_dict['perform_ingress_ip_reduction'] = \
                        is_ingress_ip_reduction_enabled(self,
                                                        customer.get_param("id"),
                                                        compute_region_idx)

            # Skip mgmt interface swap for OCI deployments
            if cloud_provider not in [PROVIDER_AZR, PROVIDER_OCI, PROVIDER_VMWARE_ESXI] and \
                    instance_role != INSTANCE_ROLE_MP:
                instance_entry_dict["mgmt-interface-swap"] = "enable"

            if instance_role > INSTANCE_ROLE_LEGACY:
                if instance_role == INSTANCE_ROLE_MP:
                    instance_entry_dict['instance_role'] = INSTANCE_ROLE_MP_STRING
                else:
                    instance_entry_dict['instance_role'] = INSTANCE_ROLE_DP_STRING
                instance_entry_dict['slot_nr'] = slot_nr
                instance_entry_dict['inter_instance_psk'] = inter_instance_psk
                self.logger.info("Adding extra params for MP DP seperation.")
            else:
                # Set for integrated. For MP/DP, it will be set later on while binding them
                instance.set_param("mp1_id", instance.get_param("id"))
            if self.get_agentless_uda_ff(customer.get_param("acct_id"), nodetypemodel.node_id):
                instance_entry_dict['proxy-protocol-enabled'] = 1
            else:
                instance_entry_dict['proxy-protocol-enabled'] = 0

            if node_type == "SWGPROXY" and self.can_swg_proxy_enable_geneve(custid, compute_region_idx, cloud_provider):
                instance_entry_dict['ep-geneve-enabled'] = '1'

            if (is_china_env() or is_swg_aws_enabled(self.logger, customer.get_param("acct_id"))) and node_type == "SWGPROXY":
                instance_entry_dict['proxy-protocol-enabled'] = 1

            parent_id = None
            parent_id_tuple = None
            if is_clean_pipe:
                sql = "SELECT parent_id FROM cust_topology WHERE id='{0}'".format(str(cust_topology_id))
                cursor = self.get_cursor()
                try:
                    cursor.execute(sql)
                    parent_id_tuple = cursor.fetchone()
                    self.cursorclose(cursor)
                except Exception as e:
                    self.logger.info("Failed to get parent_id from cust_topology for id='{0}'".format(str(cust_topology_id)))
            if parent_id_tuple is not None:
                if parent_id_tuple[0] is not None:
                    parent_id = parent_id_tuple[0]
            instance_entry_dict['parent_id'] = parent_id
            cust_site_cfg = custSiteSpecificCfgModel(self, customer.get_param("id"))
            val_exists, sessionAffinity = cust_site_cfg.get_value("sessionAffinity", int(compute_region_idx))
            if val_exists:
                instance_entry_dict['sessionAffinity'] = str(sessionAffinity)
            else:
                # Bad DB state?
                instance_entry_dict['sessionAffinity'] = "CLIENT_IP"

            # If instance behind NLB; then avoid setting external ipv6 flag as True
            if instance.get_param("is_using_sp_interconnect"):
                self.logger.info("External IPv6 is not supported for SP Interconnect, Skipping IPv6 enablement!")
            elif node_type in ["GPGATEWAY", "GPPORTAL", "FIREWALL", "NLB"] and customer.get_param("external_ipv6_support") == True:
                # We only allow IPv6 to be enabled for AWS if aws_ipv6_supported is True and if its not a premium zone.
                if is_ipv6_disabled_for_location(self, compute_region_idx, custid=customer.get_param("id")):
                    self.logger.info(f"IPv6 is disabled for compute region idx {compute_region_idx}")
                else:
                    if (cloud_provider == PROVIDER_AWS and orch_cfg.get("aws_ipv6_supported", False) in [True,'True']
                        and not is_premium_zone) or cloud_provider == PROVIDER_GCP or cloud_provider == PROVIDER_OCI:
                        if cloud_provider == PROVIDER_GCP:
                            native_compute_region_name = get_cloud_native_location_name_from_region_id(self,
                                                                               region_id=compute_region_idx, custid=custid)
                            subnet_name = 'subnet-dp-%s-%s' % (str(native_compute_region_name),
                                                               str(customer.get_param("acct_id")))
                            is_subnet_ipv6_enabled = self.is_ipv6_enabled_on_gcp_subnet(
                                                                            project_id=customer.get_param("project_id"),
                                                                            region=native_compute_region_name,
                                                                            subnet_name=subnet_name)
                            if is_subnet_ipv6_enabled == False:
                                self.logger.info(f"Subnet {subnet_name} is not dual stack so skipping Ipv6 enablement")
                            else:
                                instance_entry_dict["has_external_ipv6"] = True
                        else:
                            instance_entry_dict["has_external_ipv6"] = True

            else:
                self.logger.info(f"External IPv6 support is not enabled for instance: {instance.get_param('id')}")

            # Name:
            salt_profile_name = get_salt_profile_name_for_instance_entry(self, instance_entry_dict,
                                                                         is_passive, is_clean_pipe)
            if salt_profile_name != None:
                instance.set_param("salt_profile_name", salt_profile_name)
            else:
                self.error = (f"Failed to create the salt profile name. "
                              f"locals: {str(locals())}")
                self.logger.error(self.error)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              self.error)
                raise Exception(self.error)

            cft_dict = get_salt_profile_for_instance_entry(self, instance_entry_dict, cloud_provider,
                                                           is_passive, is_clean_pipe, is_premium_zone, node_type)
            if cft_dict == None:
                self.error = (f"get_salt_profile_for_instance_entry: Failed "
                              f"to get the cft params. {str(locals())}")
                self.logger.error(self.error)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              self.error)
                raise Exception(self.error)
            cft_params = json.dumps(cft_dict)

            if cloud_provider in [ PROVIDER_VMWARE_ESXI ]:
                # populate the interface params now for cloud provider ESXi
                if self.populate_network_config_for_instance(cft_dict["NetworkConfig"], instance) == False:
                    raise Exception("Failed to save info about network config to instance master!")

            res = [instance]
            self.logger.info("CFT_profile is \n%s" % str(cft_params))
            instance.set_param("salt_profile", cft_params)

            if 'static_ip' in cft_dict and (cft_dict['static_ip'] is not None or cft_dict['static_ip'] != '') and cloud_provider != PROVIDER_OCI:
                instance.set_param("public_ip", cft_dict['static_ip'])

            # Reload the instance_master model. We do this because we set some fields that
            # ip_mgmt manually updates (via direct SQL queries).
            # Reloading the values allows us to refresh our model to reflect what ip_mgmt did.
            # FIXME: ip_mgmt needs to use a model. Additionally, we either need to pass the model
            # object up the stack, or re-factor so that
            # we are not holding on to a model for too long to avoid our model going stale.
            # TODO: We can enhance our models such that a `.save()` only updates the columns
            # corresponding to the members we updated.
            instance.set_param("use_PBF", InstanceModel(dbh=self, iid=instance.get_param("id")).get_param("use_PBF"))
            if not instance.save():
                self.error = ("get_new_instance_int(): Unable to save instance")
                self.logger.error(self.error)
                self.update_status_msg_for_cust_topology_node(cust_topology_id,
                                                              self.error)
                raise Exception(self.error)

            ret = True

        except Exception as E:
            self.logger.error(
                "Exception %s occured: %s %s\n" % (str(E.args), str(traceback.format_exc()), str(locals())))
            publish_avisar_event_with_tenant_region_nodetype_key(self.avctx, self.logger,
                                                                 ORCHESTRATION_INSTANCE_CREATION_FAILED, str(E.args),
                                                                 METRIC_SEVERITY_CRITICAL)
            ret = False
            res = []
            try:
                if allocated_instance != None and allocated_instance.get_param("id") != None:
                    alloc_instance_id = allocated_instance.get_param("id")
                    if allocated_instance.delete(self, allocation_failed=True) == True:
                        self.logger.error("Deleted allocated instance with id %s" % str(alloc_instance_id))
                    else:
                        self.logger.error("Failed to delete the allocated instance %s" % str(alloc_instance_id))
            except:
                self.logger.error("Exception occured in Exception %s. %s" %
                                  (str(E.args), str(traceback.format_exc())))
        finally:
            return DbResult(ret, res)

    def populate_sp_interconnect_details(self, instance, customer, region, instance_entry_dict, nodetypemodel):
        """
        populates the instance with the value for is_using_sp_interconnect
        and verifies if the interconnect VPC exists else throw exception

        Parameters
        ----------
        instance: InstanceModel object
        customer: CustomerModel object
        region : int edge region id of the instance
        instance_entry_dict: dict of the instance entry details
        nodetypemodel: NodeTypeModel object

        Returns
        ----------
        bool: should the instance use interconnect or not.

        Throws
        ----------
        Exception on validation failures like VPC checks or parsing data.
        """
        try:
            self.logger.info(f"Start populating interconnect details for: {str(customer.get_param('id'))}")
            ret_dic = self.fetch_interconnect_details(instance, customer, region)
            if ret_dic['error_msg']:
                self.logger.error(f"Failed to set interconnect details for custid: {str(customer.get_param('id'))}")
                raise Exception(self.error)

            self.logger.info(f"Interconnect Details returned: {ret_dic}")

            instance.set_param("is_using_sp_interconnect", ret_dic['is_using_sp_interconnect'])
            instance_entry_dict['is_5g_enabled'] = ret_dic['is_5g_enabled']
            instance_entry_dict['sp_egress_type'] = ret_dic['sp_egress_type']
            cloud_provider = instance.get_param("cloud_provider")
            node_type_id = instance.get_param('node_type')

            if (nodetypemodel.ha_reqd and ret_dic['is_using_sp_interconnect']
                    and cloud_provider == PROVIDER_AWS_DB_ENUM_VALUE):
                instance.set_param("no_passive_instance", True)

            instance_entry_dict["has_dual_egress_nic"] = False
            has_dual_egress_nic = is_dual_egress_nic_supported(self.logger,
                                                               instance_entry_dict.get("sp_egress_type", "SP"),
                                                               node_type_id,
                                                               ret_dic['is_using_sp_interconnect'],
                                                               cloud_provider)
            self.logger.info(f"has_dual_egress_nic:{has_dual_egress_nic} value returned")
            if has_dual_egress_nic:
                instance_entry_dict["has_dual_egress_nic"] = True
        except Exception as E:
            self.error = f"Error during validate_set_inst_using_sp_interconnect : {E}"
            self.logger.error("Interconnect Details not populated by SRE")
            raise Exception(self.error)

    def fetch_interconnect_details(self, instance, customer, region):
        """
        Utility Method to check for a firewall for a given child tenant and compute region , should it be part of
        interconnect.

        Parameters
        ----------
        instance: InstanceModel object
        customer: CustomerModel object
        region: int edge region idx where firewall is brought up

        Returns
        ----------
        ret_dic: dict of the interconnect details
            contains error_msg key if error during operation
            interconnect_exists_in_region: if interconnect exists in a region
            is_using_sp_interconnect: if pupi ip is assigned to the instance
            is_5g_enabled: if 5g is enabled for this instance.
            sp_egress_type: transport type of the interconnect in regions
        """

        # Initialize ret_dic with default return values
        ret_dic = {
            "error_msg": "",
            "interconnect_exists_in_region": False,
            "is_using_sp_interconnect": False,
            "is_5g_enabled" : False,
            "sp_egress_type" : 'SP'
        }

        custid = customer.get_param("id")
        node_type_id = instance.get_param('node_type')
        supported_clouds = [PROVIDER_GCP_DB_ENUM_VALUE, PROVIDER_AWS_DB_ENUM_VALUE]
        cloud_provider = instance.get_param("cloud_provider")
        try:
            # STEP 1: Check if interconnect exists for a given tenant and cloud provider.
            # STEP 2: If present fetch the transport type based on the interconnect in corresponding cloud.
            if (cloud_provider in supported_clouds
                and customer.get_param("backbone_selection") != 0):
                # Check if the compute region is not in exclude list
                cloud_native_compute_region = get_cloud_native_location_name_from_region_id(self, region, custid=custid)
                if cloud_provider == PROVIDER_AWS_DB_ENUM_VALUE:
                    res = self.get_sp_tgw_state(custid, cloud_native_compute_region)
                else:
                    res = self.get_interconnect_vpc_details(custid, cloud_native_compute_region)
                if res is not None and res.ok:
                    if res.result[0] is not None:
                        ret_dic['interconnect_exists_in_region'] = True
                        # Fetch the transport type since interconnect exists in a region
                        if cloud_provider == PROVIDER_GCP_DB_ENUM_VALUE:
                            egress_res = self.get_sp_egress_type(custid, cloud_native_compute_region)
                            if egress_res is not None and egress_res.ok and egress_res.result[0] is not None:
                                ret_dic["sp_egress_type"] = egress_res.result[0]
                else:
                    msg = "Failed to fetch interconnect Details"
                    ret_dic["error_msg"] = msg
                    raise Exception(msg)
            else:
                self.logger.info(f"Interconnect doesn't exist for: {custid} in : "
                                 f"{region} and {cloud_provider} and {customer.get_param('backbone_selection')}")

            # STEP 3: If interconnect exists check if node type is valid for 5g or interconnect.
            # STEP 4: Based on 5g flag for cloud populate the 5g value.
            # STEP 5: Based on transport, cloud and node_type determine if interconnect should be enabled.
            if ret_dic['interconnect_exists_in_region']:
                interconnect_firewall_types = [NODE_TYPE_GP_GATEWAY, NODE_TYPE_GP_PORTAL,
                                               NODE_TYPE_SERVICE_CONN, NODE_TYPE_REMOTE_NET,
                                               NODE_TYPE_NAT_INSTANCE,
                                               NODE_TYPE_NLB_INSTANCE,
                                               NODE_TYPE_SERVICE_ILB
                                               ]
                if cloud_provider == PROVIDER_GCP_DB_ENUM_VALUE:
                    interconnect_firewall_types.append(NODE_TYPE_SWG_PROXY)

                # Based on the node type and cloud populate the 5g and interconnect flags.
                if node_type_id in interconnect_firewall_types:
                    supported_5g_node_list = [NODE_TYPE_SERVICE_ILB, NODE_TYPE_GP_GATEWAY]
                    is_5g_enabled_in_region = False
                    if cloud_provider == PROVIDER_GCP_DB_ENUM_VALUE:
                        res = self.get_enable_5g(custid, region)
                        if res is not None and res.ok:
                            self.logger.info(f"Returned 5G result for customer {custid} and "
                                             f"region {region} is: {res.result[0]}")
                            res_val = res.result[0]
                            if res_val and str(res_val) != "0":
                                is_5g_enabled_in_region = True

                    if is_5g_enabled_in_region and node_type_id in supported_5g_node_list:
                        ret_dic["is_5g_enabled"] = True

                    if ret_dic["sp_egress_type"] == "SP":
                        self.logger.info(f"Egress Type is SP, determine the value of is_using_sp_interconnect")
                        # In case of swg VM only for new arch we have to use interconnect
                        if node_type_id == NODE_TYPE_SWG_PROXY:
                            if cloud_provider == PROVIDER_GCP_DB_ENUM_VALUE:
                                proxy_bringup = swgproxy_bringup(self)
                                eproxy_outside_panos_enabled = proxy_bringup.gpcs_envoy_outside_panos_val(
                                    custid,
                                    int(instance.get_param('compute_region_idx')),
                                    PROVIDER_GCP,
                                    node_type_id)
                                self.logger.info(f"For custid:{str(custid)} checking for region: "
                                                 f"{str(instance.get_param('compute_region_idx'))}"
                                                 f"eproxy_outside_panos_enabled: {str(eproxy_outside_panos_enabled)}")

                                if eproxy_outside_panos_enabled:
                                    ret_dic["is_using_sp_interconnect"] = True
                        else:
                            ret_dic["is_using_sp_interconnect"] = True
                    elif ret_dic["sp_egress_type"] in ["PA", "HYBRID"]:
                        self.logger.info(f"Egress Type is {ret_dic['sp_egress_type']}, determine the value of is_using_sp_interconnect")
                        self.logger.info(f"cloud_provider: {cloud_provider} PROVIDER_GCP_DB_ENUM_VALUE: {PROVIDER_GCP_DB_ENUM_VALUE}")
                        self.logger.info(
                            f"node_type_id: {node_type_id} NODE_TYPE_NAT_INSTANCE: {NODE_TYPE_NAT_INSTANCE}")
                        if cloud_provider == PROVIDER_GCP_DB_ENUM_VALUE:
                            if node_type_id == NODE_TYPE_NAT_INSTANCE:
                                ret_dic["is_using_sp_interconnect"] = True
        except Exception as ex:
            self.logger.error(f"Failed to fetch the interconnect details: {ex}")
        finally:
            return ret_dic

    def get_sp_egress_type(self, custid, cloud_native_compute_region):
        """
        Utility Method to get the SP egress type for a given child tenant based on compute region.
        """
        cursor = self.get_cursor()
        try:
            self.logger.info(f"get_sp_egress_type for cust_id: {custid} and "
                             f"cloud_native_compute_region: {cloud_native_compute_region} ")
            cursor.execute(f"SELECT egress_type FROM interconnect_vpc_master WHERE (tsg_id, host_project_id) = "
                           f"(SELECT root_tsg_id, host_project_id from cust_master where "
                           f"id = {custid}) AND compute_region= '{cloud_native_compute_region}' ")
            egress_details = cursor.fetchone()
            self.logger.info(f"egress_details: {egress_details}")
            if egress_details is None or egress_details[0] == '':
                return DbResult(True, [None])
            return DbResult(True, [egress_details[0]])
        except Exception as ex:
            self.logger.error("Failed to get egress type, Exception: %s" % (str(ex),))
            return DbResult(False, [])

    def get_interconnect_vpc_details(self, custid, cloud_native_compute_region):
        """
        Utility Method to get the interconnect vpc details for a given child tenant based on compute region.

        Parameters
        ----------
        custid: int custid used in cust_master
        cloud_native_compute_region: str native_compute_region_name field of region_master

        Returns
        ----------
        DbResult : (true/false, [result]) result field [] contains two fields in success case.
        vpc_name: str sp shared dp vpc name
        subnet_name: str sp shared subnet name in the vpc for the region.

        If not able to fetch the VPC information returns ( False, [])
        """
        cursor = self.get_cursor()
        try:
            self.logger.info("Checking get_interconnect_vpc_details for cust_id: %s "
                             "and cloud_native_compute_region: %s " % (str(custid), str(cloud_native_compute_region)))
            cursor.execute("SELECT shared_vpc_name, subnet_name FROM interconnect_vpc_master WHERE "
                           "(tsg_id, host_project_id) = (SELECT root_tsg_id, host_project_id from cust_master where "
                           "id = '%s') AND compute_region= '%s' " % (str(custid), cloud_native_compute_region))
            vpc_details = cursor.fetchone()
            self.logger.info("get_interconnect_vpc_details output: %s" % (str(vpc_details)))
        except Exception as ex:
            self.logger.error("Failed to get get_interconnect_vpc_details, Exception: %s" % (str(ex),))
            return DbResult(False, [])

        if vpc_details is None or vpc_details[0] == '' or vpc_details[1] == '':
            self.logger.info("vpc_details: %s not found in interconnect_vpc_master table." % str(custid))
            return DbResult(True, [None, None])

        return DbResult(True, [vpc_details[0], vpc_details[1]])

    def get_tenant_interconnect_vpc_details(self, custid, cloud_native_compute_region, interconnect_name):
        """
        Utility method to retrieve tenant interconnect VPC details


        Parameters
        ----------
        custid : int, Customer ID from cust_master table
        cloud_native_compute_region: native_compute_region_name field of region_master
        interconnect_name: str interconnect_name field of tenant_interconnect_vpc_master

        Returns
        ----------
        DbResult : (true/false, [result]) result field [] contains two fields in success case.
        vpc: str onramp vpc name
        subnet_name: str onramp subnet name.
        vlan_bandwidth: int vlan attachment bandwidth
        """
        cursor = self.get_cursor()
        try:
            self.logger.info(f"get_tenant_interconnect_vpc_details for cust_id: {custid},  "
                             f"cloud_native_compute_region: {cloud_native_compute_region}, "
                             f"interconnect_name: {interconnect_name} ")
            sql = (f"SELECT vpc, subnet_name, vlan_bandwidth "
                   f"FROM tenant_interconnect_vpc_master "
                   f"WHERE tenant_id = (SELECT acct_id FROM cust_master WHERE id = '{custid}') "
                   f"AND compute_region = '{cloud_native_compute_region}' "
                   f"AND interconnect_name = '{interconnect_name}'")
            cursor.execute(sql)
            vpc_details = cursor.fetchone()
            self.logger.info("get_tenant_interconnect_vpc_details output: %s" % (str(vpc_details)))
        except Exception as ex:
            self.logger.error(f"Failed to get onramp vpc details, Exception: {str(ex)}")
            return DbResult(False, [])

        if vpc_details is None or vpc_details[0] == '' or vpc_details[1] == '':
            self.logger.info(f"vpc_details are not found in tenant_interconnect_vpc_master table for custid: {str(custid)}")
            return DbResult(True, [None, None])

        return DbResult(True, [vpc_details[0], vpc_details[1], vpc_details[2]])


    def get_interconnect_vpc_name(self, custid):
        """
        Given the cust_master id fetch the Shared DP VPC network name.
        A given tenant can be part of only one shared DP VPC.
        @param custid: cust_master id
        @return: DbResult : (true/false, [result]) result field [] contains field in success case.
                 vpc_name: str sp shared dp vpc name
                If not able to fetch the VPC information returns ( False, [])
        """
        cursor = self.get_cursor()
        try:
            self.logger.info("Checking get_interconnect_vpc_name for cust_id: %s" % (str(custid)))
            cursor.execute("SELECT shared_vpc_name FROM interconnect_vpc_master WHERE "
                           "tsg_id = (SELECT root_tsg_id from cust_master where id = '%s') " % (str(custid)))
            vpc_details = cursor.fetchone()
            self.logger.info("get_interconnect_vpc_name output: %s" % (str(vpc_details)))
        except Exception as ex:
            self.logger.error("Failed to get get_interconnect_vpc_name, Exception: %s" % (str(ex),))
            return DbResult(False, [])

        if vpc_details is None or vpc_details[0] == '':
            self.logger.error("get_interconnect_vpc_name: %s not found in interconnect_vpc_master table." % str(custid))
            return DbResult(True, [None])

        return DbResult(True, [vpc_details[0]])

    def get_sp_tgw_state(self, custid, cloud_native_compute_region):
        """
        Get state of TransitGateway Attachment to DirectConnect Gateway for a given child tenant based on compute region

        Parameters
        ----------
        custid: int custid used in cust_master
        cloud_native_compute_region: str native_compute_region_name field of region_master

        Returns
        ----------
        DbResult : (true/false, [result]) result field [] contains two fields in success case.
        state : str state of TGW creation. "added_route" is the terminal state for successful creation of
                TGW attachment

        If not able to fetch the state information returns ( False, [])
        """
        cursor = self.get_cursor()
        try:
            self.logger.info(f"Checking get_sp_tgw_state for cust_id: {custid} and cloud_native_compute_region: "
                             f"{cloud_native_compute_region}")
            cursor.execute("SELECT transit_gateway_id, state FROM interconnect_tgw_mapping WHERE "
                           "root_tsg_id = (SELECT root_tsg_id FROM cust_master WHERE "
                           "id = '%s') AND compute_region= '%s' " % (str(custid), cloud_native_compute_region))
            tgw_state = cursor.fetchone()
            self.logger.info(f"get_sp_tgw_state output: {tgw_state}")
        except Exception as ex:
            self.logger.error(f"Failed to get get_sp_tgw_state, Exception: {str(ex)}")
            return DbResult(False, [])

        if tgw_state is None or tgw_state[0] == '':
            self.logger.info(f"Transit Gateway is not yet created for {custid}")
            return DbResult(True, [None, None])

        return DbResult(True, [tgw_state[0], tgw_state[1]])

    def get_sp_subnet_details(self, custid, edge_location_region_name, public_ip):
        """
        Get the subnet id to which the public ip belong to.

        Parameters
        ----------
        custid: int custid used in cust_master
        edge_location_region_name: str edge_location_region_name field of region_master
        public_ip: public_ip of the instance

        Returns
        ----------
        DbResult : (true/false, [result]) result field [] contains two fields in success case.
        subnet_id: subnet id in PANOS VPC, to be used as DPActiveSubnet to spin EC2

        If not able to fetch the subnet information returns ( False, [])
        """
        cursor = self.get_cursor()
        try:
            self.logger.info(f"Checking get_sp_subnet_details for cust_id: {custid} and edge_location_region_name: "
                             f"{edge_location_region_name}")
            cursor.execute("SELECT subnet_id, cidr FROM interconnect_subnet_master WHERE "
                           "root_tsg_id = (SELECT root_tsg_id FROM cust_master WHERE "
                           "id = '%s') AND edge_location_region_name= '%s' " % (str(custid), edge_location_region_name))
            subnet_details = cursor.fetchall()
            self.logger.info(f"get_sp_subnet_details output: {str(subnet_details)}")
        except Exception as ex:
            self.logger.error(f"Failed to get get_sp_subnet_details, Exception: {str(ex)}")
            return DbResult(False, [])

        if subnet_details is None or len(subnet_details) == 0:
            self.logger.info(f"subnet_details: {custid} not found in interconnect_subnet_mapping table.")
            return DbResult(True, [None])
        # Check for corresponding subnet, the public_ip belong to
        for subnet_id, cidr in subnet_details:
            ip = ipaddress.ip_address(public_ip)
            network = ipaddress.ip_network(cidr, strict=True)
            if ip in network:
                return DbResult(True, [subnet_id])

        return DbResult(True, [None])

    def get_enable_5g(self, custid, region):
        """
        Get the enable_5g status for a given customer and region.
        In interconnect_vpc_master table , which contains GCP interconnect details to chek if 5g is enabled for region.

        Parameters
        ----------
        custid : int
            Customer ID used in cust_master table.
        region : int edge location region id.
            Region identifier.

        Returns
        -------
        DbResult
            A DbResult object containing:
            - success : bool
                True if the operation was successful, False otherwise.
            - result : list
                A list containing the enable_5g status if successful, empty list otherwise.
        """
        cursor = self.get_cursor()
        try:
            cloud_native_compute_region = get_cloud_native_location_name_from_region_id(self, region, custid=custid)
            self.logger.info(f"Checking get enable_5g for cust_id: {custid} "
                             f"and cloud_native_compute_region: {cloud_native_compute_region}")
            cursor.execute("SELECT sase_5g FROM interconnect_vpc_master WHERE "
                           "(tsg_id, host_project_id) = (SELECT root_tsg_id, host_project_id from cust_master where "
                           "id = %s) AND compute_region = %s", (custid, cloud_native_compute_region))
            enable_5g_res = cursor.fetchone()
            self.cursorclose(cursor)
            self.logger.info(f"get enable_5g output: {enable_5g_res}")
        except Exception as ex:
            self.cursorclose(cursor)
            self.logger.error(f"Failed to get enable_5g, Exception: {ex}")
            return DbResult(False, [])

        if enable_5g_res is None or enable_5g_res[0] == '':
            self.logger.info(f"enable_5g: {custid} not found in interconnect_vpc_master table.")
            return DbResult(False, [])

        return DbResult(True, [enable_5g_res[0]])


    def get_allocated_spn(self,
                          custid,
                          nodeid,
                          spn_name,
                          transient,
                          error_list):
        '''

        :param self:
        :param nodeid:
        :param spn_name:
        :param transient:
        :param error_list:
        :return:
        '''
        success = False
        instances = []
        cursor = None
        try:
            # First lookup the agg-bandwidth table and check if this instance is already registered for the customer.
            if custid == None:
                error_list.append((-1, "Fatal! get_allocated_spn - No custid provided! Cannot continue further..."))
                success=False
                return

            customer = CustomerModel(custid=custid, dbh=self)
            tenant_id=customer.get_param("acct_id")
            agg_bw_model=AggBandwidthMgmtModel(dbh=self)
            ret = agg_bw_model.is_agg_bw_spn_registered_for_customer(tenant_id,
                                                                     spn_name,
                                                                     region=None)
            if ret == False:
                error_list.append((-1, "Fatal! SPN is not allocated! Cannot continue further..."))
                success=False
                return

            # We can now check the instance master table to see if the spn_name is allocated.
            im = InstanceModel(iid=0, dbh=self)
            ret = im.is_spn_already_allocated_in_instance_master(custid, spn_name)
            if ret == False:
                instances=[]
                success = False
                return

            spn_ids = im.get_instances_by_custid_and_spn_name(custid, spn_name, region=None)
            if not len(spn_ids):
                error_list.append((-2, "Fatal! No instances belonging to spn found in the instance master table! "
                                       "Cannot continue further..."))
                instances=[]
                success=False
                return

            inst_id = spn_ids[0]
            instance = InstanceModel(iid=inst_id, dbh=self)
            if (not instance.get_param("id")):
                success=False
                error_list.append((-2, "Fatal! Instance belonging to spn not found in the instance master table! "
                                       "Cannot continue further..."))
                instances=[]
                return
            peer = None
            peer_id = 0
            if instance.get_param("no_passive_instance") == False:
                peer_id = spn_ids[1]
                peer = InstanceModel(iid=peer_id, dbh=self)
                if (not peer.get_param("id")):
                    success=False
                    error_list.append((-2, "Fatal! Peer Instance belonging to spn not found in the instance master table! "
                                           "Cannot continue further..."))
                    instances=[]
                    return

            # Bind the instance's to the nodeid.
            sql = "call agg_bandwith_bind_instance_id_to_node(%s, %s, %s, %s)"
            params = (nodeid, inst_id, peer_id, transient)
            cursor = self.get_cursor(prepared=True)
            self.logger.info("Executing: %s" % str(sql%params))

            try:
                cursor.execute(sql, params)
                ret = cursor.fetchone()
                self.logger.info("Return result: %s" % str(ret))
                self.cursorclose(cursor)
                self.conn.close()
            except Exception as ex:
                error="Unable to bind existing instances %s, %s with node %s: %s" % (str(spn_ids[0]),
                                                                                     str(spn_ids[1]),
                                                                                     str(nodeid),
                                                                                     str(ex))
                self.logger.error(error)
                self.cursorclose(cursor)
                self.conn.close()
                error_list.append(-3, error)
                return

            if not ret or ret[0] == 0:
                dberr = ""
                if ret and len(ret) > 1:
                    dberrr = ret[1]
                error = "Unable to bind new instance with node %s: %s" % (str(nodeid), str(dberr))
                self.logger.error(error)
                error_list.append(-3, error)
                return

            instances.append(instance)
            instances.append(peer)
            success = True

        except Exception as E:
            self.logger.error("get_allocate_spn: Failed to get free GP instance %s %s %s"
                              % (str(E.args), str(locals()), str(traceback.format_exc())))
            success = False
            instances = []
        finally:
            return DbResult(success, instances)


    def get_free_instance(self, nodeid, vmtype, transient=0, cloud_provider = PROVIDER_AWS):
        # TODO: We would need to choose the right compute node region for the given edge node region.
        if vmtype != "FIREWALL":
            return DbResult(False, [])
        sql = "call allocate_free_instance(%s, %s)"
        params = (nodeid, transient)
        cursor = self.get_cursor(prepared=True)
        try:
            cursor.execute(sql, params)
            ret = cursor.fetchall()
            self.cursorclose(cursor)
            self.conn.close()
        except Exception as ex:
            self.logger.info("locals: %s" % str(locals()))
            self.error = ("Error while allocating free instances in "
                          "instance_master : %s" % (str(ex),))
            self.logger.error(self.error)
            self.cursorclose(cursor)
            self.conn.close()
            return DbResult(False, [])

        self.logger.info("locals: %s" % str(locals()))

        if ret:
            instid = ret[0][0]
            peer_id = ret[0][1]
            instance = InstanceModel(iid=instid, dbh=self)
            if not instance.get_param("id"):
                self.error = ("get_free_instance:Unable to find instance with "
                              "id %d from db" % instid)
                self.logger.info("get_free_instance:Unable to find instance with "
                                  "id %d from db" % instid)
                return DbResult(False, [])
            peer = None
            if peer_id:
                peer = InstanceModel(iid=peer_id, dbh=self)
                if not peer.get_param("id"):
                    self.logger.error("get_free_instance: Unable to find the "
                                      "peer instance with id %s" % (instance.get_param("ha_peer")))

            if peer and peer.get_param("id"):
                instances = [instance, peer]
            else:
                instances = [instance]
            return DbResult(True, instances)
        return DbResult(False, [])

    def get_free_gp_gateway_instance(self, nodeid, vmtype, transient=0, max_retries=3):
        success = False
        instances = []
        self.logger.info(f" trying free gp instance for nodeid = {nodeid} and transient={transient}")

        if vmtype != "GPGATEWAY":
            return DbResult(False, [])

        try:
            for attempt in range(max_retries):
                sql = "call allocate_free_gp_gateway_instance(%s, %s)"
                params = (nodeid, transient)
                cursor = self.get_cursor(prepared=True)

                cursor.execute(sql, params)
                ret = cursor.fetchall()
                self.cursorclose(cursor)

                if ret:
                    self.logger.info(f"free gp instance result value = {str(ret)}")
                    instid, _, error_message = ret[0]
                    if "Lock wait timeout exceeded" in error_message:
                        self.logger.warning(f"Timeout occurred on attempt {attempt + 1}. Retrying...")
                        if attempt == max_retries - 1:
                            self.error = f"Max retries reached. Error while allocating free gp gateway instance: {error_message}"
                            self.logger.error(self.error)
                            raise Exception(error_message)
                        continue
                    if instid == 0:
                        # check error message for unusual errors
                        if "Instance is not found" in error_message:
                            self.logger.info("Did not find any reusable GP gateway instance.")
                            break
                        else:
                            raise Exception(error_message)

                    instance = InstanceModel(iid=instid, dbh=self)
                    if not instance.get_param("id"):
                        self.logger.error(f"get_free_gp_gateway_instance: Unable to find InstanceModel entry for instance ID {instid} Retrying InstanceModel fetch from IM call...")
                        # Sometimes we see an issue on dev environments wherein for edge location handling even if the compute GW instance
                        # entry exists in IM; instance model global fails to get any entry from instance_master causing failures
                        # Adding a second pass call to re-query with instance model object. The second attempt call works and fetches the compute region MU entry
                        # TODO: Need to investigate further why the 1st attempt fails to fetch the IM entry
                        instance = InstanceModel(iid=instid, dbh=self)
                        self.logger.info(f"get_free_gp_gateway_instance: Retried InstanceModel fetch from IM call. Instance entry returned {instance} with name {instance.get_param('name')} with ID {instid}")
                        if not instance.get_param("id"):
                            self.error = f"get_free_gp_gateway_instance: Unable to find instance with id {instid} from db even after 2 attempts. Returning failed operation"
                            self.logger.error(self.error)
                            raise Exception(self.error)

                    instances = [instance]
                    success = True
                    break
                elif attempt == max_retries - 1:
                    self.error = f"Max retries reached. Error while allocating free gp gateway instance: fetchall failed with params {params}"
                    self.logger.error(self.error)
                    instances = -1
                    raise Exception(error_message)


        except Exception as E:
            self.error = f"Error while allocating free gp gateway instance: {str(E)}"
            self.logger.error(self.error)
            instances = -1
            success = False

        finally:
            self.cursorclose(cursor)
            self.conn.close()

            if not success:
                self.logger.error(f"get_free_gp_gateway_instance: Failed to get free GP instance after {max_retries} attempts")

            return DbResult(success, instances)

    def get_free_swg_proxy_instance(self, nodeid, vmtype, transient=0):
        success = False
        instances = []
        try:
            if not is_swg_proxy(vmtype):
                return DbResult(False, [])
            sql = "call allocate_free_swg_proxy_instance(%s, %s)"
            params = (nodeid, transient)
            cursor = self.get_cursor(prepared=True)
            try:
                cursor.execute(sql, params)
                ret = cursor.fetchall()
                self.cursorclose(cursor)
                self.conn.close()
            except Exception as ex:
                self.error = ("Error while allocating free swg proxy instance in "
                              "instance_master : %s" % (str(ex),))
                self.logger.error(self.error)
                self.cursorclose(cursor)
                self.conn.close()
                raise Exception(self.error)
            if ret:
                instid = ret[0][0]
                if instid == 0:
                    self.logger.info("Did not find any reusable swg proxy instance.")
                    success = False
                    return
                instance = InstanceModel(iid=instid, dbh=self)
                if not instance.get_param("id"):
                    self.error = ("get_free_swg_proxy_instance:Unable to find instance with "
                                  "id %d from db" % instid)
                    self.logger.error("get_free_swg_proxy_instance:Unable to find instance with "
                                      "id %d from db" % instid)
                    raise Exception(self.error)

                instances = [instance]
                success = True

        except Exception as E:
            self.logger.error("get_free_swg_proxy_instance: Failed to get free GP instance %s %s %s"
                              % (str(E.args), str(locals()), str(traceback.format_exc())))
            success = False
            instances = []

        finally:
            return DbResult(success, instances)

    def get_cust_region_instance_params(self, custid, region_idx):
        sql = ("select distinct instance_master.name,instance_master.clusterid,"
               " instance_master.salt_profile"
               " from instance_master left join vpc_master on "
               "instance_master.vpc_id = vpc_master.vpc_id and "
               " vpc_master.ha_flag = 0 where "
               "custid = %s and vpc_master.zone=%s")
        params = (custid, region_idx)
        params_dict = {}
        # We can also use set session TRANSACTION ISOLATION LEVEL READ COMMITTED to get proper results
        self.conn.close()
        cursor = None
        try:
            cursor = self.get_cursor()
            cursor.execute(sql, params)
            ret = cursor.fetchall()
            self.cursorclose(cursor)
        except Exception as ex:
            self.error = (
            "Unable to query instances for customer id %s region %s: %s" % (str(custid), str(region_idx), str(ex)))
            self.logger.error(self.error)
            self.cursorclose(cursor)
            return None

        if not ret:
            return params_dict

        for name, clusterid, salt_profile in ret:
            try:
                cft_profile = json.loads(salt_profile)
            except Exception as ex:
                # Its better to not touch the stack if database has something wrong, than
                # Update stack with wrong values
                self.error = ("Unable to convert cft parameters from Json to dict\n%s" % str(salt_profile))
                self.logger.error(self.error)
                return None
            if clusterid in list(params_dict.keys()):
                params_dict[clusterid]['SecondaryInstanceName'] = cft_profile['PrimaryInstanceName']
                params_dict[clusterid]['SecondaryId'] = cft_profile['PrimaryId']
                params_dict[clusterid]['SecondaryImage'] = cft_profile['PrimaryImage']
                params_dict[clusterid]['MgmtPassiveSubnet'] = cft_profile['MgmtActiveSubnet']
                params_dict[clusterid]['DPPassiveSubnet'] = cft_profile['DPActiveSubnet']
                params_dict[clusterid]['SecondaryIntfSet'] = cft_profile['PrimaryIntfSet']
                if "HAActiveSubnet" in list(cft_profile.keys()):
                    params_dict[clusterid]['HAPassiveSubnet'] = cft_profile['HAActiveSubnet']
            else:
                params_dict[clusterid] = {}
                params_dict[clusterid]['CustId'] = cft_profile['CustId']
                params_dict[clusterid]['InstType'] = cft_profile['InstType']
                params_dict[clusterid]['AcctId'] = cft_profile['AcctId']
                params_dict[clusterid]['UserData'] = cft_profile['UserData']
                params_dict[clusterid]['PrimaryInstanceName'] = cft_profile['PrimaryInstanceName']
                params_dict[clusterid]['PrimaryId'] = cft_profile['PrimaryId']
                params_dict[clusterid]['SerialNo'] = cft_profile['SerialNo']
                params_dict[clusterid]['KeyName'] = cft_profile['KeyName']
                params_dict[clusterid]['PrimaryImage'] = cft_profile['PrimaryImage']
                params_dict[clusterid]['Eth0SecGrp'] = cft_profile['Eth0SecGrp']
                params_dict[clusterid]['Eth1SecGrp'] = cft_profile['Eth1SecGrp']
                params_dict[clusterid]['IamRole'] = cft_profile['IamRole']
                params_dict[clusterid]['PrimaryIntfSet'] = cft_profile['PrimaryIntfSet']
                if "Eth2SecGrp" in list(cft_profile.keys()):
                    params_dict[clusterid]['Eth2SecGrp'] = cft_profile['Eth2SecGrp']
                params_dict[clusterid]['MgmtActiveSubnet'] = cft_profile['MgmtActiveSubnet']
                params_dict[clusterid]['DPActiveSubnet'] = cft_profile['DPActiveSubnet']
                if 'HAActiveSubnet' in list(cft_profile.keys()):
                    params_dict[clusterid]['HAActiveSubnet'] = cft_profile['HAActiveSubnet']
        return params_dict

    ############################# VERSION API ################################
    def get_instance_id_from_upgrade_clusterid(self, clusterid):
        sql = ("select instance_master.id,instance_master.no_passive_instance,instance_master.ha_state from instance_master left join "
               "instance_upgrade on instance_master.vmid = "
               "instance_upgrade.old_instance_vmid  where "
               "instance_upgrade.cluster_id = %s and "
               "instance_upgrade.workflow_status <> 'done' and "
               "instance_master.ha_state in (1, 0)")
        params = (clusterid,)
        cursor = self.get_cursor()
        try:
            cursor.execute(sql, params)
            res = cursor.fetchone()
            self.cursorclose(cursor)
            if not res:
                self.error = ("Unable to find instance from corresponding to"
                              " vmid in instance upgrade table:clusterid %s" % str(clusterid))
                self.logger.error(self.error)
                return DbResult(False, [])
            self.logger.info(f'res = {res}')
            no_passive_instance = res[1]
            ha_state = res[2]
            if no_passive_instance == 1 and ha_state != 0:
                self.error = f"if no passive instance is enabled then ha_state should be zero. however, found non-zero value for ha_state for given cluster_id = {clusterid}. so upgrade not supported."
                self.logger.error(self.error)
                return DbResult(False, [])
        except Exception as ex:
            self.cursorclose(cursor)
            self.error = ("Unable to find instance from corresponding to vmid"
                          " in instance upgrade table:clusterid %s:ERROR: %s" % (clusterid, str(ex)))
            self.logger.error(self.error)
            return DbResult(False, [])
        return DbResult(True, [res[0]])

    #  TODO: This should have cloud specific param.
    def get_nodetype_sw_version_from_cust_version(self, vmtype, service_version):
        ok = False
        ret = None
        res = []
        params = (service_version, 'nodetype', vmtype)
        cursor = self.get_cursor()
        try:
            cursor.execute('select version from version_mapping where '
                           'service_version = %s and node_type in (select id from'
                           ' types_master where type = %s and name = %s)', params)

            ret = cursor.fetchone()
        except Exception as ex:
            self.logger.error(
                "get_nodetype_sw_version_from_cust_version: Failed to get software version for service version %s node_type %s:%s"
                % (service_version, vmtype, str(ex)))
        if ret:
            version, = ret
            res.append(version)
            ok = True
        else:
            self.logger.error("get_nodetype_sw_version_from_cust_version: no record found for software version for service version %s\
                    node_type %s" % (service_version, vmtype))
        return DbResult(ok, res)

    def get_ami_id_for_instance_version(self, instance):
        ret_dict = {}
        if not instance.get_param("id"):
            self.logger.error("Invalid instance object")
            return DbResult(False, [])

        sql = ("SELECT am.ami_id, am.version, am.arch "
               "FROM instance_upgrade iu join ami_mapping am "
               "on (iu.version=am.version and am.zone=iu.region) "
               "where (am.node_type=%s and iu.old_instance_vmid=%s)")
        node_type = instance.get_param("node_type")
        old_instance_vmid = instance.get_param("vmid")
        cursor = self.get_cursor()
        params = (node_type, old_instance_vmid)
        try:
            cursor.execute(sql, params)
            result = cursor.fetchall()
            self.cursorclose(cursor)
        except Exception as ex:
            error = ("get_ami_id_for_instance_version:Unable to find ami "
                     "for node_type %s vpc_id %s: Exception %s" %
                     (str(node_type), str(instance.get_param("vpc_id")), str(ex)))
            self.logger.error(error)
            return DbResult(False, [])
        if not result:
            return DbResult(False, [])
        # return a dictionary
        for line in result:
            ami_id, version, arch = line
            ret_dict[arch] = {"ami-id": ami_id, "version": version}

        return DbResult(True, ret_dict)

    def get_infra_version(self):
        ret = None
        cursor = self.get_cursor()
        try:
            cursor.execute("SELECT VALUE FROM global_settings WHERE SETTING = 'infra_version'")
            ret = cursor.fetchone()
            self.logger.info("Found infra version %s from global_settings table"
                    % ret)
        except Exception as ex:
            self.logger.error("Failed to get infra version from global_settings table")

        return ret

    def get_swm_package_name(self, version):
        ret = None
        cursor = self.get_cursor()
        try:
            cursor.execute(f"select package_location from pan_widget_mapping where widget_version='{version}'")
            ret = cursor.fetchone()
            self.logger.info("Found package_location %s from pan_widget_mapping table"
                    % ret)
        except Exception as ex:
            self.logger.error(f"Failed to getpackage_location from pan_widget_mapping table: {ex}")

        return ret

    def set_instance_extra_param(self, cloud_machine_type, capacity_type, market_type, dpdk_qcount):
        self.cloud_machine_type = cloud_machine_type
        self.capacity_type = capacity_type
        self.market_type = market_type
        self.dpdk_qcount = dpdk_qcount

    # The service_nic feature flag is global and multi tenant, so will simply work with its default_value
    # This needs to be set by sre for enabling the feature.
    # schema: insert into feature_flags (flag_name,flag_type,app, feature_name, release_version, default_value, err_msg) values ('service_nic', 'sre', 'saas_infra', 'app_acceleration', '5.0.0', 0, 'App Acceleration is not enabled by default');
    # sre action:  update feature_flags set default_value = 1 where flag_name = 'service_nic';
    def get_service_nic_feature_flag(self, cloud_provider):
        sn_ff_name = ''
        if cloud_provider == PROVIDER_AWS:
            sn_ff_name = 'aws_service_nic'
        elif cloud_provider == PROVIDER_GCP:
            sn_ff_name = 'service_nic'
        elif cloud_provider == PROVIDER_OCI:
            sn_ff_name = 'service_nic'
        else:
            self.logger.error("Feature service nic not suppoted for  [%s]" % cloud_provider)
            return False

        infra_version = self.extract_infra_version()
        if infra_version == "":
            self.logger.error("Error! Cannot extract saas_infra version from orch cfg")
            return False
        
        query = ("SELECT default_value FROM feature_flags WHERE flag_name='%s' AND "
                "app='%s' AND flag_type ='%s' AND release_version='%s' " % (str(sn_ff_name), str('saas_infra'), str('sre'), str(infra_version)))
        result = dbconn.find_one(query, logger=self.logger)
        if result is not None and len(result) != 0 and result[0] is not None \
            and len(result[0]) != 0:
            self.logger.info("service_nic feature flag is enabled in RDS.")
            return result[0][0] != 0
        else:
            self.logger.error("Failed to geta service_nic feature flag value from db")
            return False

    # Determins if the DP Nic is on a shared VPC .
    def get_is_dp_nic_on_shared_vpc_feature_flag(self):
        query = ("SELECT default_value FROM feature_flags WHERE flag_name='%s' AND "
                "app='%s' AND flag_type ='%s'" % (str('shared_datapath'), str('saas_infra'), str('sre')))
        result = dbconn.find_one(query, logger=self.logger)
        if result is not None and len(result) != 0 and result[0] is not None \
            and len(result[0]) != 0:
            self.logger.info("shared_datapath feature flag is enabled in RDS.")
            return result[0][0] != 0
        else:
            self.logger.info("shared_datapath feature flag is not enabled in RDS")
            return False

    def get_service_nic_node_types(self, cloud_provider):
        if cloud_provider == PROVIDER_AWS:
            return [NODE_TYPE_GP_GATEWAY, NODE_TYPE_REMOTE_NET]
        elif cloud_provider == PROVIDER_GCP:
            return [NODE_TYPE_GP_GATEWAY, NODE_TYPE_REMOTE_NET, NODE_TYPE_SWG_PROXY]
        elif cloud_provider == PROVIDER_OCI:
            return [NODE_TYPE_GP_GATEWAY, NODE_TYPE_REMOTE_NET, NODE_TYPE_SWG_PROXY]
        return []

    def get_gcp_host_project(self):
        orch_cfg = OrchCfgModel(self).fields
        if orch_cfg == None:
            self.logger.error("Fatal error! Cannot retrieve the orchestrator global configuration.")
            return None
        host_projects = orch_cfg.get('gcp_host_proj_list')
        if host_projects is None:
            self.logger.error("gcp_host_proj_list not found in host_cfg")
            return None
        host_projects_list = host_projects.split(',')
        return host_projects_list[0]


    # Prod host project would be " host-gpcs-prod-01" others would have host-gpcs-test-01 or host-gcps-dev-01 or host-gcps-pre-prod-01
    # vpc naming:  prod: "sase-shared-dp-vpc-prod-1" for prod, "sase-shared-dp-vpc-pre-prod-1" for pre-prod and "sase-shared-dp-vpc-non-prod-1" for all others
    def get_shared_vpc_prefix_from_deployed_gcp_env(self, is_sp_child_tenant=False):

        if is_sp_child_tenant:
            # All the sp-host projects share the same name across dev/prod/test env's
            return SHARED_VPC_EMPTY_PREFIX

        orch_cfg = OrchCfgModel(self).fields
        if orch_cfg == None:
            self.logger.error("Fatal error! Cannot retrieve the orchestrator global configuration.")
            return None
        deployed_env = orch_cfg.get('gcp_env')
        if  deployed_env == "mod-pre-prod":
            return SHARED_VPC_PRE_PROD_PREFIX
        elif "prod" in deployed_env:
            return SHARED_VPC_PROD_PREFIX
        elif (deployed_env == "test" or deployed_env == "dev"):
            return SHARED_VPC_NON_PROD_PREFIX
        else:
            return SHARED_VPC_EMPTY_PREFIX

    def get_user_flags(self, tenantid, app, release_version):
        query = ("SELECT flags FROM tenant_feature_flags WHERE tenantid='%s' AND "
                "app='%s' AND release_version='%s'" %
                (str(tenantid), app, str(release_version)))
        result = dbconn.find_all(query, logger=self.logger)
        if result is not None and len(result) != 0 and result[0] is not None and len(result[0]) != 0:
            try:
                json_data = b64decode(result[0][0])
                data = json.loads(json_data)
                return data
            except Exception as e:
                self.logger.error("Exception in decoding the user flags. Load default "
                            "value instead. Error: %s" % str(e))
                return None
        self.logger.info("No flags found for tenantid: %s" % str(tenantid))
        return None

    def extract_infra_version(self):
        orch_cfg = OrchCfgModel(self).fields
        if orch_cfg == None:
            self.logger.error("Error! Cannot retrieve the orchestrator global configuration.")
            return None
        rel_version = orch_cfg.get('version')
        return self.extract_major_version(rel_version)

    def extract_major_version(self, version):
        matchstr = "(\d+\.\d+\.\d+)"
        match = re.search(matchstr, version)
        if match is not None:
            return match.group(1)
        return ""

    def get_agentless_uda_ff(self, tenant_id, node_type):
        if node_type not in [NODE_TYPE_GP_GATEWAY]:
            return False
        major_version = self.extract_infra_version()
        if major_version == "":
            self.logger.error("Error! Cannot extract saas_infra version from orch cfg")
            return False
        flag_name = "tenant_agentless_uda_feature_flag_sre"
        app_name = "saas_infra"
        ff = self.get_user_flags(tenant_id, app_name, major_version)
        if ff is not None and ff.get(flag_name, {}).get("value", 0):
            self.logger.info(f"{flag_name} ff enabled for tenant:{tenant_id}")
            return True
        self.logger.info(f"{flag_name} ff not enabled for tenant:{tenant_id}")
        return False


    def get_rnsc_fqdn_ff(self, tenant_id):
        major_version = self.extract_infra_version()
        if major_version == "":
            self.logger.error("Error! Cannot extract saas_infra version from orch cfg")
            return False
        flag_name = "rnsc_fqdn_feature_flag"
        app_name = "saas_infra"
        ff = self.get_user_flags(tenant_id, app_name, major_version)
        if ff is not None and ff.get(flag_name, {}).get("value", 0):
            self.logger.info(f"{flag_name} ff enabled for tenant:{tenant_id}")
            return True
        self.logger.info(f"{flag_name} ff not enabled for tenant:{tenant_id}")
        return False

    def get_colo_100g_no_gre_ff(self, tenant_id):
        major_version = self.extract_infra_version()
        if major_version == "":
            self.logger.error("Error! Cannot extract saas_infra version from orch cfg")
            return False
        flag_name_dep = "colo_100g_no_gre_dep"
        flag_name_sre = "colo_100g_no_gre_sre"
        app_name = "saas_infra"
        ff = self.get_user_flags(tenant_id, app_name, major_version)
        if ff is not None and ff.get(flag_name_dep, {}).get("value", 0):
            if ff.get(flag_name_sre, {}).get("value", 0):
                self.logger.info(f"Colo 100G No GRE ff enabled for tenant:{tenant_id}")
                return True
        self.logger.info(f"Colo 100G No GRE ff not enabled for tenant:{tenant_id}")
        return False
    
    def get_gp_deployed_regions_for_cust_dbh(self, node_type, custid, compute_region_idx):
        #This is a clone of get_gp_deployed_regions_for_cust to decouple from dbconn which has gone BAD
        #This is a bad practice also but oh well we need to HF
        self.logger.info(f"Fetching deployed regions for custid: {custid}, node_type: {node_type}, compute_region_idx: {compute_region_idx}")

        sql = ("select distinct rm.gpaas_display_name, ct.region, im.compute_region_idx from instance_master im join cust_topology ct join region_master rm "
            "on im.id=ct.instance1_id and ct.region=rm.edge_location_region_id "
            "where ct.custid=%s and ct.node_type=%s "
            "and im.is_dynamic_instance=0")
        cursor = self.get_cursor()
        params = (custid, node_type)
        regions = []
        try:
            cursor.execute(
                sql,
                params)
            ret = cursor.fetchall()
            if not ret or len(ret) == 0:
                self.logger.error(f"Not able to get_gp_deployed_regions_for_cust, ret: {ret}")
                return None
            
            self.logger.info(f"get_gp_deployed_regions_for_cust_dbh, db result: {ret}")
            for row in ret:
                ct_region = row[1]   # ct.region
                deployed_compute = row[2] #  im.compute_region_idx
                if int(deployed_compute) == int(compute_region_idx):
                    regions.append(ct_region) 
            
        except Exception as e:
            self.logger.error(f"Failed to execute SQL: {sql}. Error: {str(e)}")
            return regions
        
        finally:
            self.cursorclose(cursor)
            return regions
    
##################### END DB HANDLE CLASS #################################
class DbResult(object):
    def __init__(self, retval, result):
        self.ok = retval  # True or False
        self.result = result  # List
'''
from libs.common.logger import logger
def test():
    mylogger = logger("instance.log", "/home/<USER>/")
    dbh = DbHandle(mylogger)
    #self, version, node_type, region, machine_type
    # ret = dbh.get_new_instance_int("PAN-OS-8.1.0", "FIREWALL", 36, 23)
    ret = dbh.get_infra_version()
    #is_passive=True, ha_peer_id=active.id, ha_tunnel_subnet=active.tunnelsubnet

if __name__ == '__main__':
    test()
'''
