import json
import traceback
from libs.apis.region_master_api import *
from libs.cloud_providers.gcp.instance_manager.gcp_instance_info import gcp_instance_info_handler, \
                                                                        gcp_is_clean_ip_project
from libs.cloud_providers.gcp.instance_manager.gcp_cp_instance_info import gcp_cp_instance_info_handler
from libs.common.shared.grpc.proto.avisarpb.avisar_pb2_client import ORCHESTRATION_INSTANCE_PUBLIC_IP_FETCH_FAILED, \
    METRIC_SEVERITY_CRITICAL, ORCHESTRATION_INSTANCE_PUBLIC_IPV6_FETCH_FAILED, ORCHESTRATION_NGPA_GCP_NLB_BRINGUP_FAILED_IP_RESERVATION_FAILURE, \
    ORCHESTRATION_NGPA_GCP_NAT_BRINGUP_FAILED_IP_RESERVATION_FAILURE
from libs.common.shared.sys_utils import *
from io import StringIO, BytesIO
from gzip import GzipFile
from collections import defaultdict
from libs.common.shared.sys_utils import NODE_TYPE_SWG_PROXY, \
                  INSTANCE_ROLE_MP_STRING, NODE_TYPE_GP_GATEWAY, \
                  NODE_TYPE_NLB_INSTANCE, NODE_TYPE_NAT_INSTANCE, NODE_TYPE_BI_NH_PROXY, LOAD_BALANCER_INSTANCE_LIST, LOAD_BALANCER_INSTANCE_NAME_LIST, NODE_TYPE_SERVICE_ILB
from libs.common.shared.py3_utils import convert_bytes_to_str, convert_str_to_bytes, b64encode, b64decode
from libs.common.utils import get_target_passive_machine_type, get_target_override_for_version, \
    publish_avisar_event_with_tenant_region_nodetype_key
from libs.common.utils import gcp_instance_has_nic_limit
from libs.cloud_providers.common.swgproxy_bringup import swgproxy_bringup
from libs.model.instancemodel import InstanceModel
from libs.model.instancemodel import copy_egress_ipv6_subnet_list_from_primary_instance, find_standalone_instances_by_custid_and_node_type_and_region_id, find_instances_by_custid_and_node_type_and_region_id
from orchestration_service.core.orchestrator_nat_mgmt import find_nr_nat_instances_by_custid_and_region_id, is_nat_gateway_nlb_needed
from orchestration_service.core.orchestrator_nlb_mgmt import update_instances_behind_nlb, update_instance_behind_nlb, \
                  forwarding_rule_protocol_dict, backend_service_protocol_dict, session_affinity_dict, conn_pers_on_unhealthy_backends_dict, \
                  health_check_protocol_dict, DEFAULT_FW_RULE_PROTOCOL, DEFAULT_BACKEND_SERV_PROTOCOL, DEFAULT_SESSION_AFFINITY, \
                  DEFAULT_CONN_PERS_ON_UNHEALTHY_BACKENDS, DEFAULT_HC_PROTOCOL, DEFAULT_HC_PORT, DEFAULT_HC_INTERVAL, DEFAULT_HC_TIMEOUT, DEFAULT_HC_UNHEALTHY_THRESHOLD, \
                  DEFAULT_HC_HEALTHY_THRESHOLD, DEFAULT_IS_STRONG_SESSION_AFFINITY_SUPPORTED, DEFAULT_NGPA_PROTO_SESSION_AFFINITY, \
                  DEFAULT_NGPA_NLB_IDLE_TIMEOUT
from libs.model.instancemodel import is_ingress_ip_reduction_enabled, find_instances_by_custid_and_region_id, \
    get_gp_deployed_regions_for_cust, is_nat_dummy_upgrade_ongoing_in_region
import libs.cloud_providers.common.ip_management.ipv4.ip_mgmt as ipv4_mgmt
import libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1 as ipv6_mgmt_1
from libs.model.IP_management_model import IPManagementModel
from libs.model.custmodel import CustomerModel
from libs.model.explicitProxyTenantInfoModel import ExplicitProxyTenantInfoModel
from libs.model.custEpaasConfigModel import CustEpaasConfigModel
from libs.cloud_providers.gcp.instance_manager.gcp_instance_param_utils import Fwdruletypes
from libs.cloud_providers.common.instance_ami_util import get_arch_for_cloud_machine_type
import utils.Exceptions as customException


def compresess_cfg(cfg):
    f = BytesIO()
    cfg = convert_str_to_bytes(cfg)
    gzf = GzipFile(mode="w", fileobj=f)
    gzf.write(cfg)
    gzf.close()
    data = f.getvalue()
    return b64encode(data)


def uncompress_cfg(cfg):
    orig_file_desc = GzipFile(mode='r',
                              fileobj=BytesIO(b64decode(cfg, decode=False)))
    # Get the original's file content to a variable
    orig_file_cont = orig_file_desc.read()
    return convert_bytes_to_str(orig_file_cont)


def gcp_mark_assigned_zone(event, zone):
    custnode_id = event['custnode_id']
    dbh = event['dbh']
    # The instance isn't saved yet. Use the custnode_id
    sql = "UPDATE cust_topology SET assigned_zone='{0}' WHERE id='{1}'".format(zone, str(custnode_id))
    dbh.conn.close()
    try:
        cursor = dbh.get_cursor()
        cursor.execute(sql)
        dbh.cursorclose(cursor)
    except Exception as e:
        dbh.logger.info("Failed to mark assigned zone on custnode_id=%s"
                        % custnode_id)


def gcp_update_zone_usage(event, zone_usage):
    parent_id = event['parent_id']
    dbh = event['dbh']
    instance_id = event['inst_id']
    # The parent node should have already been created
    sql = "UPDATE cust_topology SET zone_usage=%s WHERE id=%s"
    params = (json.dumps(zone_usage), parent_id)
    dbh.conn.close()
    try:
        cursor = dbh.get_cursor()
        cursor.execute(sql, params)
        dbh.cursorclose(cursor)
    except Exception as e:
        dbh.logger.info("Failed to update zone_usage on parent_id=%s"
                        % parent_id)


def get_gcp_assigned_zone_for_custnode(custnode_id, dbh):
    sql = "SELECT cust_topology.assigned_zone FROM cust_topology WHERE id=%s"
    params = (custnode_id,)
    cursor = dbh.get_cursor()
    ret = None
    try:
        cursor.execute(sql, params)
        ret = cursor.fetchall()
        dbh.cursorclose(cursor)
    except Exception as e:
        dbh.logger.info("Failed to get pre-assigned_zone for custnode.id={0}".format(custnode_id))
        return None
    if ret is None:
        return None
    if len(ret) == 0:
        return None
    (assigned_zone,) = ret[0]
    return assigned_zone


# The parent instance will keep track of the zone usage
# on a per customer-region basis. It is responsible for
# maintaining balance across zones on a customer-region
# basis. If there is no parent instance, we will just use
# the first zone
def get_gcp_cleanpipe_zone(event, zones_list):
    custnode_id = event['custnode_id']
    parent_id = event['parent_id']
    dbh = event['dbh']
    # Check if the cust_topology entry has already been assigned a zone
    assigned_zone = get_gcp_assigned_zone_for_custnode(custnode_id, dbh)
    if assigned_zone is not None:
        # Instance Transition. Maintain zone for cust_topology entry
        dbh.logger.info("Reusing zone assigned to cust_topology entry")
        return zones_list[int(assigned_zone)]
    if parent_id is None:
        dbh.logger.info("No parent_id specified. Using zone 0")
        gcp_mark_assigned_zone(event, zones_list[0])
        return zones_list[0]
    # If the parent_id is specified, the parent instance must
    # already have been saved. So, it's instance ID should exist
    sql = "SELECT zone_usage FROM cust_topology WHERE id='{0}'".format(str(parent_id))
    dbh.conn.close()
    cursor = None
    ret = None
    try:
        dbh.logger.info(sql)
        cursor = dbh.get_cursor()
        cursor.execute(sql)
        ret = cursor.fetchone()
        dbh.cursorclose(cursor)
    except Exception as e:
        dbh.logger.info("Failed to get zone_usage from cust_topology where id=%s" % parent_id)
    zone_usage = {}
    if not ret[0]:
        # This is the first time this parent instance has been mentioned
        # Set up the zone_usage attribute
        # Let the first child set up the parent's usage stats
        zone_usage["0"] = [zones_list[0], "0"]
        zone_usage["1"] = [zones_list[1], "0"]
    else:
        zone_usage = json.loads(ret[0])
    if int(zone_usage["0"][1]) <= int(zone_usage["1"][1]):
        zone = 0
        zone_usage["0"][1] = str(int(zone_usage["0"][1]) + 1)
    else:
        zone = 1
        zone_usage["1"][1] = str(int(zone_usage["1"][1]) + 1)

    gcp_mark_assigned_zone(event, str(zone))
    gcp_update_zone_usage(event, zone_usage)
    return zones_list[zone]

def get_zone_by_balancing_usage(dbh, my_inst, inst_prof):
    '''
    Called when creating the profile for a new VM. 
    if global_settings is disabled, use default zone, else
    Lookup current zone usage and try to balance deployment across zones equally
    '''
    zone_to_use = inst_prof['zone']
    zones_list = inst_prof['zonesList']
    sql = "SELECT value FROM global_settings WHERE setting='disable_zone_distribution'"
    cursor = dbh.get_cursor()
    try:
        cursor.execute(sql, None)
        ret = cursor.fetchall()
        dbh.cursorclose(cursor)
    except Exception as e:
        dbh.logger.info(f"Failed to read global_settings : {e}")
        dbh.cursorclose(cursor)
        return zone_to_use
    if ret and len(ret) > 0 and ret[0][0] in ("True", "true"):
        dbh.logger.info("disable_zone_distribution is set in global_settings")
        return zone_to_use
    sql = ("SELECT salt_profile FROM instance_master WHERE "
           "custid=%s AND compute_region_idx=%s AND node_type=%s AND cloud_provider='gcp' AND id!=%s")
    params = (my_inst.get("cust_id"),
              my_inst.get("region"),
              my_inst.get("node_type_id"),
              my_inst.get("instance_id"))
    cursor = dbh.get_cursor()
    ret = None
    try:
        cursor.execute(sql, params)
        ret = cursor.fetchall()
        dbh.cursorclose(cursor)
    except Exception as e:
        dbh.logger.error(f"Failed to read salt_profile from other instances in this tenants region: {e}")
        dbh.cursorclose(cursor)
        return zone_to_use
    if ret is None:
        return zone_to_use
    if len(ret) == 0:
        return zone_to_use
    usage_dict = {}
    for row in ret:
        salt_profile_str = row[0]
        if salt_profile_str is None:
            continue
        salt_profile = json.loads(salt_profile_str)
        usage = usage_dict.get(salt_profile.get("Zone"), None)
        if usage is None:
            usage_dict[salt_profile.get("Zone")] = 1
        else:
            usage_dict[salt_profile.get("Zone")] = usage + 1
    
    least_usage = None
    for zone in zones_list:
        usage = usage_dict.get(zone, 0)
        if least_usage is None:
            least_usage = usage
            zone_to_use = zone
        else:
            if usage < least_usage:
                least_usage = usage
                zone_to_use = zone
    return zone_to_use


def allocate_clean_pipe_public_ip_for_new_instance(dbh,
                                                   instance_entry_dict):
    success = False
    try:
        dbh.logger.info("Reserving IPs for GCP CLEANPIPE customer %s, "
                        "cluster id %s" %
                        (str(instance_entry_dict['acct_id']),
                         str(instance_entry_dict['instance_id'])))
        gcp_ip_mgmt = ipv4_mgmt.GCPIPHandler(dbh,
                                             str(instance_entry_dict['instance_id']),
                                             str(instance_entry_dict['acct_id']))

        ip_list = gcp_ip_mgmt.allocate_public_ip_for_customer(None)
        dbh.logger.info("Allocated GCP public IP for customer with IP list %s" % str(ip_list))

        success = True
    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                  str(traceback.format_exc()),
                                                                                  str(locals())))
        success = False
    finally:
        return success

def allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl(dbh,
                                                                   my_inst,
                                                                   egress_ip_list_loc,
                                                                   instance_entry_dict,
                                                                   is_clean_ip_project):
    success = False
    try:
        perform_ingress_ip_reduction = instance_entry_dict.get("perform_ingress_ip_reduction", False)
        dbh.logger.info("perform_ingress_ip_reduction is %s" % str(perform_ingress_ip_reduction))
        dbh.logger.info("The customers topology has edge locations %s on it" % str(egress_ip_list_loc))
        my_node_type = my_inst.get_param("node_type")

        cm = CustomerModel(custid=my_inst.get_param("custid"), dbh=dbh)
        _, _, _, is_allow_list_enabled = cm.get_auto_scale_options(NODE_TYPE_GP_GATEWAY)
        ip_mgmt_model = IPManagementModel(dbh=dbh)

        for my_edge_location in egress_ip_list_loc:
            dbh.logger.info(f"allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl: Processing {my_inst.get_param('name')} for edge {my_edge_location}")
            if my_node_type == NODE_TYPE_NAT_INSTANCE or not perform_ingress_ip_reduction:
                num_of_ips = get_num_of_ips_by_instance_type(dbh, my_inst, perform_ingress_ip_reduction)
                target_node_type = NODE_TYPE_GP_GATEWAY if my_node_type == NODE_TYPE_NAT_INSTANCE else my_node_type

                is_sp_transport_type_loopback = False
                if (my_inst.get_param("is_using_sp_interconnect")
                        and instance_entry_dict.get('sp_egress_type', "SP") not in ['PA', 'HYBRID']):
                    is_sp_transport_type_loopback = True
                dbh.logger.info(
                        f"allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl: Call reserve_ips_for_cust_region for instance {my_inst.get_param('name')} for edge {my_edge_location} NAT gateway handling")
                #For non NLB instances, keep behavior the same until more improvements pending in 5.2.1
                result = ip_mgmt_model.reserve_ips_for_cust_region(my_inst.get_param("custid"),
                                                                   my_inst.get_param("node_type"),
                                                                   my_edge_location,
                                                                   my_inst.get_param("compute_region_idx"), num_of_ips,
                                                                   target_node_type=target_node_type,
                                                                   reserve_pupi_ip=is_sp_transport_type_loopback)

                # {'ok': True / False, 'ip_list': [[IP, reservationStatus]]}
                if result['ok'] == True:
                    ip_list = result['ip_list']
                    dbh.logger.info("IP Reservation list: %s" % str(ip_list))
                else:
                    success = False
                    err_msg = ("Failed to reserver IP address for custid %s, node_type: %s, edge_region_idx: %s,\
                                                                                    inst.compute_region_id: %s" % (
                        str(my_inst.get_param("custid")), str(my_inst.get_param("node_type")),
                        str(my_edge_location), str(my_inst.get_param("compute_region_idx"))))
                    if is_clean_ip_project:
                        raise Exception(err_msg)
                    else:
                        dbh.logger.error(err_msg)

                is_nat_dummy_upgrade_ongoing = is_nat_dummy_upgrade_ongoing_in_region(dbh, my_inst.get_param("custid"), 
                                                                                      my_inst.get_param("compute_region_idx"))
                dbh.logger.info(f"allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl: Binding reserrved IPs for instance {my_inst.get_param('name')} for cloud {instance_entry_dict['cloud_provider']}")
                #NODE_TYPE_NLB_INSTANCE should always take reserved ips instead of allowlisted ips
                #If instance is NAT and nat dummy upgrade is on-going in region. Then dont bind allowlisted ips
                #Because the dummy upgrade will spin up new NAT instances and re-use exsting IPs anyway
                if is_allow_list_enabled and target_node_type != NODE_TYPE_NLB_INSTANCE and not is_nat_dummy_upgrade_ongoing:
                    result = ip_mgmt_model.bind_allow_listed_ips_to_instance(my_inst, my_edge_location, 1,
                                                                             target_node_type=target_node_type,
                                                                             reserve_pupi_ip=is_sp_transport_type_loopback)
                else:
                    result = ip_mgmt_model.bind_reserved_ips_to_instance(my_inst, my_edge_location, 1,
                                                                         target_node_type=target_node_type,
                                                                         reserve_pupi_ip=is_sp_transport_type_loopback)
                if result["ok"] == False:
                    err_msg = "Failed to bind reserved IP addresses to instance %s" % str(my_inst.get_param("name"))
                    if is_clean_ip_project:
                        raise Exception(err_msg)
                    else:
                        dbh.logger.error(err_msg)
                else:
                    dbh.logger.info("allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl: Successfully bound edge IP address for instance [%s]" % my_inst.get_param("name"))
                    dbh.logger.info(f"allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl: EIP list {my_inst.get_param('egress_ip_list')} for instance {my_inst.get_param('name')}")


            elif my_node_type == NODE_TYPE_NLB_INSTANCE and perform_ingress_ip_reduction:
                dbh.logger.info("No egress IPs are allocated to NLB if ingress_ip_reduction is enabled")

        success = True
    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success


def allocate_nat_edge_location_egress_ip_dp2_for_new_instance_impl(dbh,
                                                                   my_inst,
                                                                   egress_ip_list_loc,
                                                                   instance_entry_dict,
                                                                   is_clean_ip_project):
    """
    Allocate and bind the Edge locations / Egress IP's to the NAT DP2 interface.
    In case of the SP Interconnect and transport type is not SP then we need to allocated PUPI ip to DP2
    In this method we make sure only PUPI ip is currently assigned to DP2 interface.
    :param dbh:
    :param my_inst:
    :param egress_ip_list_loc:
    :param instance_entry_dict:
    :param is_clean_ip_project:
    :return:
    """
    success = False
    try:
        perform_ingress_ip_reduction = instance_entry_dict.get("perform_ingress_ip_reduction", False)
        dbh.logger.info("perform_ingress_ip_reduction is %s" % str(perform_ingress_ip_reduction))
        dbh.logger.info("The customers topology has edge locations %s on it" % str(egress_ip_list_loc))
        my_node_type = my_inst.get_param("node_type")

        cm = CustomerModel(custid=my_inst.get_param("custid"), dbh=dbh)
        _, _, _, is_allow_list_enabled = cm.get_auto_scale_options(NODE_TYPE_GP_GATEWAY)
        ip_mgmt_model = IPManagementModel(dbh=dbh)

        for my_edge_location in egress_ip_list_loc:
            if my_node_type == NODE_TYPE_NAT_INSTANCE or not perform_ingress_ip_reduction:
                num_of_ips = get_num_of_ips_by_instance_type(dbh, my_inst, perform_ingress_ip_reduction)
                target_node_type = NODE_TYPE_GP_GATEWAY

                # For non NLB instances, keep behavior the same until more improvements pending in 5.2.1
                result = ip_mgmt_model.reserve_ips_for_cust_region(my_inst.get_param("custid"),
                                                                   my_inst.get_param("node_type"),
                                                                   my_edge_location,
                                                                   my_inst.get_param("compute_region_idx"), num_of_ips,
                                                                   target_node_type=target_node_type,
                                                                   reserve_pupi_ip=True)

                # {'ok': True / False, 'ip_list': [[IP, reservationStatus]]}
                if result['ok'] == True:
                    ip_list = result['ip_list']
                    dbh.logger.info("IP Reservation list: %s" % str(ip_list))
                else:
                    success = False
                    err_msg = ("Failed to reserver IP address for custid %s, node_type: %s, edge_region_idx: %s,\
                                                                                    inst.compute_region_id: %s" % (
                        str(my_inst.get_param("custid")), str(my_inst.get_param("node_type")),
                        str(my_edge_location), str(my_inst.get_param("compute_region_idx"))))
                    if is_clean_ip_project:
                        raise Exception(err_msg)
                    else:
                        dbh.logger.error(err_msg)

                is_nat_dummy_upgrade_ongoing = is_nat_dummy_upgrade_ongoing_in_region(dbh, my_inst.get_param("custid"),
                                                                                      my_inst.get_param("compute_region_idx"))

                # NODE_TYPE_NLB_INSTANCE should always take reserved ips instead of allowlisted ips
                # If instance is NAT and nat dummy upgrade is on-going in region. Then dont bind allowlisted ips
                # Because the dummy upgrade will spin up new NAT instances and re-use exsting IPs anyway
                if is_allow_list_enabled and not is_nat_dummy_upgrade_ongoing:
                    result = ip_mgmt_model.bind_allow_listed_dp2_ips_to_instance(my_inst, my_edge_location, 1,
                                                                                 target_node_type=target_node_type)
                else:
                    result = ip_mgmt_model.bind_reserved_dp2_ips_to_instance(my_inst, my_edge_location, 1,
                                                                             target_node_type=target_node_type)
                if result["ok"] == False:
                    err_msg = "Failed to bind reserved IP addresses to instance %s" % str(my_inst.get_param("name"))
                    if is_clean_ip_project:
                        raise Exception(err_msg)
                    else:
                        dbh.logger.error(err_msg)
                else:
                    dbh.logger.info("Successfully bound edge IP address for instance [%s]" % my_inst.get_param("name"))

        success = True
    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success


def allocate_nat_nlb_egress_ip_for_new_instance_impl(dbh,
                                                     instance_entry_dict):
    success = False
    try:
        is_clean_ip_project = gcp_is_clean_ip_project(dbh, acct_id=instance_entry_dict['acct_id'])
        if is_clean_ip_project:
            dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Tenant {instance_entry_dict['acct_id']} is a clean IP project")
        else:
            dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Tenant {instance_entry_dict['acct_id']} is NOT a clean IP project")

        my_inst = InstanceModel(dbh=dbh, iid=instance_entry_dict['instance_id'])
        if my_inst.get_param("id") in [None, 0]:
            err_msg = "Failed to get the instance ref for instance id %s" % str(instance_entry_dict["instance_id"])
            raise Exception(err_msg)

        perform_ingress_ip_reduction = instance_entry_dict.get("perform_ingress_ip_reduction", False)

        # Special handling for GCP edge region Cloud NAT gateways as we will provision a new CNAT per edge
        # we will also update the egress IP details for the NLB instances behind the NAT here
        compute_region_idx = instance_entry_dict.get('region', '')
        edge_region_idx = instance_entry_dict.get('edge_region_idx', '')
        gcp_cnat_supported = instance_entry_dict.get('is_gcp_cnat_support_enabled', False)
        dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Handle NAT/NLB for Compute {compute_region_idx}, Edge {edge_region_idx} with gcp_cnat_supported set to {gcp_cnat_supported} for instance {my_inst.get_param('name')}")
        
        cm = CustomerModel(custid=my_inst.get_param("custid"), dbh=dbh)
        _, _, _, is_allow_list_enabled = cm.get_auto_scale_options(NODE_TYPE_GP_GATEWAY)

        dbh.logger.info("Allocating public IP and egress IP using new API")
        dbh.logger.info("Locals: %s" % str(locals()))

        # Assign public IP for NAT, GW or NLB
        ip_mgmt_model = IPManagementModel(dbh=dbh)
        result = {'ok': False}

        is_sp_transport_type_loopback = False
        if (my_inst.get_param("is_using_sp_interconnect")
                and instance_entry_dict.get('sp_egress_type', "SP") not in ['PA', 'HYBRID']):
            is_sp_transport_type_loopback = True

        num_of_ips = get_num_of_ips_by_instance_type(dbh, my_inst, perform_ingress_ip_reduction,
                                                     should_consider_gw_ips=is_sp_transport_type_loopback)

        #CYR-42950: all node_type NLB instances should use target_node_type=NODE_TYPE_NLB_INSTANCE(161)
        target_node_type = NODE_TYPE_GP_GATEWAY if my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE else my_inst.get_param("node_type")


        #For non NLB instances, keep behavior the same until more improvements pending in 5.2.1
        #Need to ensure that we do not double reserve IPs for edge NAT gateways
        #Reserve the IPs for NLB case, or PA-NAT case (edge/compute) and skip reserving of IPs for edge NAT in case GCP CNAT since it is handled further down.
        if my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE and str(compute_region_idx) != str(edge_region_idx) and gcp_cnat_supported:
            dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Call reserve_ips_for_cust_region skipped for {my_inst.get_param('name')} in compute {compute_region_idx}, Edge {edge_region_idx} for edge NAT gateway handling")
        else:
            dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Call reserve_ips_for_cust_region for instance {my_inst.get_param('name')} in compute {compute_region_idx}, Edge {edge_region_idx} for compute NAT gateway handling")
            result = ip_mgmt_model.reserve_ips_for_cust_region(my_inst.get_param("custid"), my_inst.get_param("node_type"),
                                                           my_inst.get_param("compute_region_idx"),
                                                           my_inst.get_param("compute_region_idx"),
                                                           num_of_ips,
                                                           target_node_type=target_node_type,
                                                           reserve_pupi_ip=is_sp_transport_type_loopback)
            if not result.get('ok'):
                dbh.logger.error(f"Failed to pre-allocate IPs for instance {my_inst.get_param('id')} in region {my_inst.get_param('compute_region_idx')} "
                                 f"for node_type {target_node_type}."
                                 f"Continuing anyway")
                
        # Bind the Public IP for the instance.
        # NLB IP doesn't support allowlist configuration. NLB IP should select reserved IP.
        # For GCP CNAT we do not need to call bind_reserved_public_ip_to_instance for edge NAT gateways
        if is_allow_list_enabled and target_node_type != NODE_TYPE_NLB_INSTANCE and str(compute_region_idx) == str(edge_region_idx):
            deployed_regions = dbh.get_gp_deployed_regions_for_cust_dbh(NODE_TYPE_GP_GATEWAY,
                                                                        my_inst.get_param("custid"),
                                                                        my_inst.get_param("compute_region_idx"))
            # if allowlist feature is enabled but compute region is not selected by customer or a dummy upgrade is on-going, the compute region
            # should pick ip from reserved ip pool
            is_nat_dummy_upgrade_ongoing = is_nat_dummy_upgrade_ongoing_in_region(dbh,my_inst.get_param("custid"),
                                                                            my_inst.get_param("compute_region_idx"))
            
            if my_inst.get_param("compute_region_idx") in deployed_regions and not is_nat_dummy_upgrade_ongoing:
                dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: allow_list_enabled: Call bind_allowed_public_ip_to_instance for instance {my_inst.get_param('name')} in compute {compute_region_idx}, Edge {edge_region_idx} for compute NAT gateway handling")
                result = ip_mgmt_model.bind_allowed_public_ip_to_instance(my_inst,
                                                                          my_inst.get_param("compute_region_idx"),
                                                                          target_node_type=target_node_type,
                                                                          is_using_sp_interconnect=my_inst.get_param("is_using_sp_interconnect"),
                                                                          sp_transport_type=instance_entry_dict.get('sp_egress_type', "SP"))
            else:
                dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: allow_list_enabled: Call bind_reserved_public_ip_to_instance for instance {my_inst.get_param('name')} in compute {compute_region_idx}, Edge {edge_region_idx} for compute NAT gateway handling")
                result = ip_mgmt_model.bind_reserved_public_ip_to_instance(my_inst,
                                                                           my_inst.get_param("compute_region_idx"),
                                                                           target_node_type=target_node_type,
                                                                           is_using_sp_interconnect=my_inst.get_param("is_using_sp_interconnect"),
                                                                           sp_transport_type=instance_entry_dict.get('sp_egress_type', "SP"))

        else:
            if my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE and str(compute_region_idx) != str(edge_region_idx) and gcp_cnat_supported:
                dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Skip Call to bind_reserved_public_ip_to_instance for instance {my_inst.get_param('name')} in compute {compute_region_idx}, Edge {edge_region_idx} for edge NAT gateway handling")
                result = {}
                result['ok'] = True
            else:
                dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Call bind_reserved_public_ip_to_instance for instance {my_inst.get_param('name')} in compute {compute_region_idx}, Edge {edge_region_idx} for compute node handling")
                result = ip_mgmt_model.bind_reserved_public_ip_to_instance(my_inst,
                                                                       my_inst.get_param("compute_region_idx"),
                                                                       target_node_type=target_node_type,
                                                                       is_using_sp_interconnect=my_inst.get_param(
                                                                           "is_using_sp_interconnect"),
                                                                       sp_transport_type=instance_entry_dict.get('sp_egress_type', "SP"))
        if result['ok'] == False:
            err_msg = (f"allocate_nat_nlb_egress_ip_for_new_instance_impl: allow_list_enabled: Failed to bind reserved IP to instance {str(my_inst.get_param('name'))}")
            if is_clean_ip_project:
                dbh.logger.error(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: allow_list_enabled: Failed to bind reserved IP to instance {str(my_inst.get_param('name'))}")
                raise Exception(err_msg)
            else:
                dbh.logger.info(err_msg)

        dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Completed binding public IP for instance {my_inst.get_param('name')} in region {my_inst.get_param('compute_region_idx')} with result: {result}")
        # We would be using node type as GP gateway since NAT and NLB is only for gateways.

        egress_ip_list_loc = \
            my_inst. \
                get_existing_egress_ip_region_list_for_pinned_instance(my_inst.get_param("custid"),
                                                                       my_inst.get_param("compute_region_idx"),
                                                                       node_type=NODE_TYPE_GP_GATEWAY,
                                                                       original_node_type=my_inst.get_param("node_type"))
        dbh.logger.info("The egress locations for the instance are %s" % str(egress_ip_list_loc))
        has_edge_region_idx = len(egress_ip_list_loc)

        if gcp_cnat_supported and str(compute_region_idx) == str(edge_region_idx):
            dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Skip egress-ip-list allocation in case of compute GCP CNAT {my_inst.get_param('name')}")
        elif has_edge_region_idx:
            dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Process node {my_inst.get_param('name')} for edge {edge_region_idx}")
            # For CNAT we have a NAT per edge and hence we need to reset egress_ip_list_loc to only have the current NAT edge region
            if gcp_cnat_supported and my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE:
                egress_ip_list_loc = [edge_region_idx]
                dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Process NAT {my_inst.get_param('name')} for edge {edge_region_idx} with egress_ip_list_loc {egress_ip_list_loc}")
            result = allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl(dbh,
                                                                                    my_inst,
                                                                                    egress_ip_list_loc,
                                                                                    instance_entry_dict,
                                                                                    is_clean_ip_project)
            if result == False:
                err_msg = ("Failed to add new egress IP for instance edge locations.")
                raise Exception(err_msg)
        else:
            dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Unaccounted scenario")

        dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Instance {my_inst.get_param('name')} Public IP {my_inst.get_param('public_ip')} and egress IP list set as {my_inst.get_param('egress_ip_list')}")
        #We want to invoke update_instances_behind_nlb which updates interface_ip_list
        #for all instances behind NLB for all cases:
        #compute_region and edge_region being onboarded:
        #at this point we have the ip address for the compute location
        #as well as the edge locations for the NLB
        #hence all instances behind NLB will have the interface_ip_list
        #updated to ip addresses of the NLB
        if my_inst.get_param("node_type") == NODE_TYPE_NLB_INSTANCE:
            ret = update_instances_behind_nlb(dbh,
                                                my_inst.get_param("custid"),
                                                my_inst.get_param("compute_region_idx"))
            if ret:
                dbh.logger.info(f"Updated all GW's behind NLB {my_inst.get_param('name')}")
            else:
                err_msg = (f"Failed to update all GW's behind NLB {my_inst.get_param('name')}")
                dbh.logger.error(err_msg)
                raise Exception(err_msg)

        # Set the use_pbf column to False for NAT/NLB
        my_inst.update_column_use_PBF(dbh, False)
        dbh.logger.info(f"Set the use_PBF to {False} for instance {my_inst.get_param('id')}")

        success = True
    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success


def allocate_nat_egress_ip_dp2_for_new_instance_impl(dbh, instance_entry_dict):
    """
    For NAT nodes assign the IP's for the NIC2 or dp2 interface.
    Currently, invoked only for Interconnect Nodes where transport type is not SP.
    For NIC2:
         We should assign PUPI IP's
    :param dbh:
    :param instance_entry_dict:
    :return:
    """
    success = False
    try:
        is_clean_ip_project = gcp_is_clean_ip_project(dbh, acct_id=instance_entry_dict['acct_id'])
        if is_clean_ip_project:
            dbh.logger.info("This is clean IP project")
        else:
            dbh.logger.info("This is not a clean IP project")

        my_inst = InstanceModel(dbh=dbh, iid=instance_entry_dict['instance_id'])
        if my_inst.get_param("id") in [None, 0]:
            err_msg = "Failed to get the instance ref for instance id %s" % str(instance_entry_dict["instance_id"])
            raise Exception(err_msg)

        perform_ingress_ip_reduction = instance_entry_dict.get("perform_ingress_ip_reduction", False)

        cm = CustomerModel(custid=my_inst.get_param("custid"), dbh=dbh)
        _, _, _, is_allow_list_enabled = cm.get_auto_scale_options(NODE_TYPE_GP_GATEWAY)

        dbh.logger.info("Allocating DP2 IP and egress IP using new API")
        dbh.logger.info("Locals: %s" % str(locals()))

        # Assign public IP for NAT, GW or NLB
        ip_mgmt_model = IPManagementModel(dbh=dbh)
        result = {'ok': False}

        # Since DP2 interface exists only in case of transport_type is SP
        # we have MU : nic0 : public ip we need not account for MU ip's as well when fetching the ip list count for DP2.
        num_of_ips = get_num_of_ips_by_instance_type(dbh, my_inst, perform_ingress_ip_reduction,
                                                     should_consider_gw_ips= False)

        # Since DP2 interface exists only in case of transport_type is SP we need to reserve PUPI ip's
        # For non NLB instances, keep behavior the same until more improvements pending in 5.2.1
        result = ip_mgmt_model.reserve_ips_for_cust_region(my_inst.get_param("custid"), my_inst.get_param("node_type"),
                                                           my_inst.get_param("compute_region_idx"),
                                                           my_inst.get_param("compute_region_idx"), num_of_ips,
                                                           target_node_type=NODE_TYPE_GP_GATEWAY,
                                                           reserve_pupi_ip=True)
        if not result.get('ok'):
            dbh.logger.error(
                f"Failed to pre-allocate IPs for instance {my_inst.get_param('id')} in region {my_inst.get_param('compute_region_idx')} "
                f"for node_type {NODE_TYPE_GP_GATEWAY}."
                f"Continuing anyway")

        # Bind the Public IP for the instance.
        if is_allow_list_enabled:
            deployed_regions = dbh.get_gp_deployed_regions_for_cust_dbh(NODE_TYPE_GP_GATEWAY,
                                                                        my_inst.get_param("custid"),
                                                                        my_inst.get_param("compute_region_idx"))

            # if allowlist feature is enabled but compute region is not selected by customer, the compute region
            # should pick ip from reserved ip pool
            if my_inst.get_param("compute_region_idx") in deployed_regions:
                result = ip_mgmt_model.bind_allowed_public_ip_dp2_to_instance(my_inst,
                                                                              my_inst.get_param("compute_region_idx"),
                                                                              target_node_type=NODE_TYPE_GP_GATEWAY)
            else:
                result = ip_mgmt_model.bind_reserved_public_ip_dp2_to_instance(my_inst,
                                                                               my_inst.get_param("compute_region_idx"),
                                                                               target_node_type=NODE_TYPE_GP_GATEWAY)

        else:
            result = ip_mgmt_model.bind_reserved_public_ip_dp2_to_instance(my_inst,
                                                                           my_inst.get_param("compute_region_idx"),
                                                                           target_node_type=NODE_TYPE_GP_GATEWAY)
        if result['ok'] == False:
            err_msg = ("Failed to bind reserved hybrid IP to instance %s" % str(my_inst.get_param("name")))
            if is_clean_ip_project:
                raise Exception(err_msg)
            else:
                dbh.logger.info(err_msg)

        # We would be using node type as GP gateway since NAT and NLB is only for gateways.

        egress_ip_list_loc = \
            my_inst. \
                get_existing_egress_ip_region_list_for_pinned_instance(my_inst.get_param("custid"),
                                                                       my_inst.get_param("compute_region_idx"),
                                                                       node_type=NODE_TYPE_GP_GATEWAY,
                                                                       original_node_type=my_inst.get_param(
                                                                           "node_type"))
        dbh.logger.info("The egress locations for the instance are %s" % str(egress_ip_list_loc))
        has_edge_region_idx = len(egress_ip_list_loc)
        if has_edge_region_idx:
            result = allocate_nat_edge_location_egress_ip_dp2_for_new_instance_impl(dbh,
                                                                                    my_inst,
                                                                                    egress_ip_list_loc,
                                                                                    instance_entry_dict,
                                                                                    is_clean_ip_project)
            if result == False:
                err_msg = ("Failed to add new egress IP for instance edge locations.")
                raise Exception(err_msg)
        # We want to invoke update_instances_behind_nlb which updates interface_ip_list
        # for all instances behind NLB for all cases:
        # compute_region and edge_region being onboarded:
        # at this point we have the ip address for the compute location
        # as well as the edge locations for the NLB
        # hence all instances behind NLB will have the interface_ip_list
        # updated to ip addresses of the NLB
        if my_inst.get_param("node_type") == NODE_TYPE_NLB_INSTANCE:
            ret = update_instances_behind_nlb(dbh,
                                              my_inst.get_param("custid"),
                                              my_inst.get_param("compute_region_idx"))
            if ret:
                dbh.logger.info(f"Updated all GW's behind NLB {my_inst.get_param('name')}")
            else:
                err_msg = (f"Failed to update all GW's behind NLB {my_inst.get_param('name')}")
                dbh.logger.error(err_msg)
                raise Exception(err_msg)

        # Set the use_pbf column to False for NAT/NLB
        my_inst.update_column_use_PBF(dbh, False)
        dbh.logger.info(f"Set the use_PBF to {False} for instance {my_inst.get_param('id')}")

        success = True
    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success


def allocate_gp_gateways_egress_ip_for_new_instance_behind_nlb_nat_impl(dbh,
                                                                        instance_entry_dict):
    success=True
    try:
        is_clean_ip_project = gcp_is_clean_ip_project(dbh, acct_id=instance_entry_dict['acct_id'])
        if is_clean_ip_project:
            dbh.logger.info("This is clean IP project")
        else:
            dbh.logger.info("This is not a clean IP project")

        perform_ingress_ip_reduction = instance_entry_dict.get("perform_ingress_ip_reduction", False)
        my_inst = InstanceModel(dbh=dbh, iid=instance_entry_dict['instance_id'])
        if my_inst.get_param("id") in [None, 0]:
            err_msg = "Failed to get the instance ref for instance id %s" % str(instance_entry_dict["instance_id"])
            raise Exception(err_msg)

        if my_inst.get_param("is_instance_behind_nlb"):
            # TODO: TEJAS :- check the return status ?
            # At this point the interface ip list is populated.
            update_instance_behind_nlb(dbh, my_inst)
        else:
            dbh.logger.info("GW Instance %s %s is not behind NLB; no update needed" %
                            (instance_entry_dict['instance_id'], my_inst.get_param("name")))

        dbh.logger.info("Allocating public IP and egress IP using new API")
        dbh.logger.info("Locals: %s" % str(locals()))
        # Assign public IP for NAT, GW or NLB
        ip_mgmt_model = IPManagementModel(dbh=dbh)
        result = {'ok': False}

        result = ip_mgmt_model.bind_non_allowed_public_ip_to_instance(my_inst,
                                                                      my_inst.get_param("compute_region_idx"),
                                                                      target_node_type=my_inst.get_param("node_type"))
        if result['ok'] == False:
            err_msg = ("Failed to bind non-allowed IP to instance %s" % str(my_inst.get_param("name")))
            if is_clean_ip_project:
                raise Exception(err_msg)
            else:
                dbh.logger.error(err_msg)

        egress_ip_list_loc = my_inst.get_existing_egress_ip_region_list_for_pinned_instance(
                                                                       my_inst.get_param("custid"),
                                                                       my_inst.get_param("compute_region_idx"),
                                                                       NODE_TYPE_GP_GATEWAY)
        dbh.logger.info("The egress locations for the instance are %s" % str(egress_ip_list_loc))
        has_edge_region_idx = len(egress_ip_list_loc)

        if has_edge_region_idx:
            dbh.logger.info("The customers topology has edge locations %s on it" % str(egress_ip_list_loc))
            for my_edge_location in egress_ip_list_loc:
                if not perform_ingress_ip_reduction:
                    dbh.logger.info("ingress_ip_reduction is not enabled for GW instance [%s]" %
                                                                                my_inst.get_param("name"))
                    if my_inst.get_param("is_instance_behind_nlb"):
                        dbh.logger.info("GW instance [%s] is behind NLB; will not allocate egress ip's" %
                                                                                        my_inst.get_param("name"))
                    else:
                        dbh.logger.info("GW instance [%s] is not behind NLB; will allocate egress ip's" %
                                                                                        my_inst.get_param("name"))

                        ip_mgmt_model.bind_non_allow_listed_ips_to_instance( my_inst,
                                                                             my_edge_location,
                                                                             num_of_ips=1,
                                                                             target_node_type=NODE_TYPE_GP_GATEWAY)
                        # Copy over the IP Address for this  edge location from "egress_ip_list" into
                        #  "interface_ip_list" for the gateway instance.
                        ret = my_inst.update_interface_ip_list_with_egress_ip(my_edge_location)
                        if not ret:
                            err_msg = ("Failed to update interface ip list with egress ip for %s" % my_edge_location)
                            dbh.logger.error("Error: %s" % err_msg)
                            raise Exception(err_msg)

                dbh.logger.info(f"GW instance {my_inst.get_param('name')} has NAT gateway set as {instance_entry_dict.get('has_nat_gateway', False)}")
                if instance_entry_dict.get('has_nat_gateway', False) == True:
                    # This is a case where we have NAT GW in front of the GPGW instance. We will set the value for the
                    # edge location to "ASSIGNED_TO_NAT"  in all cases.
                    dbh.logger.info("Set egress_ip_list value to ASSIGNED_TO_NAT for GW instance [%s]" %
                                                                                             my_inst.get_param("name"))
                    ip_mgmt_model.update_egress_ip_list_with_given_ipstr(my_inst, my_edge_location, "ASSIGNED_TO_NAT")

        success = True

    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success


def allocate_gp_gateways_egress_ip_dp2_for_new_instance_behind_nlb_nat_impl(dbh,
                                                                            instance_entry_dict):
    success=True
    try:
        is_clean_ip_project = gcp_is_clean_ip_project(dbh, acct_id=instance_entry_dict['acct_id'])
        if is_clean_ip_project:
            dbh.logger.info("This is clean IP project")
        else:
            dbh.logger.info("This is not a clean IP project")

        perform_ingress_ip_reduction = instance_entry_dict.get("perform_ingress_ip_reduction", False)
        my_inst = InstanceModel(dbh=dbh, iid=instance_entry_dict['instance_id'])
        if my_inst.get_param("id") in [None, 0]:
            err_msg = "Failed to get the instance ref for instance id %s" % str(instance_entry_dict["instance_id"])
            raise Exception(err_msg)

        dbh.logger.info("Allocating public IP and egress IP using new API")
        dbh.logger.info("Locals: %s" % str(locals()))
        # Assign public IP for NAT, GW or NLB
        ip_mgmt_model = IPManagementModel(dbh=dbh)
        result = {'ok': False}

        result = ip_mgmt_model.bind_non_allowed_public_ip_dp2_to_instance(my_inst,
                                                                          my_inst.get_param("compute_region_idx"),
                                                                          target_node_type=my_inst.get_param("node_type"))
        if result['ok'] == False:
            err_msg = ("Failed to bind non allowed IP_DP2 to instance %s" % str(my_inst.get_param("name")))
            if is_clean_ip_project:
                raise Exception(err_msg)
            else:
                dbh.logger.error(err_msg)
        success = True

    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success


def allocate_gp_gateways_egress_ip_for_new_instance_impl(dbh,
                                                         instance_entry_dict,
                                                         sp_egress_type='SP'):
    success = True
    try:
        my_instance_id = instance_entry_dict['instance_id']
        edge_region_idx = instance_entry_dict['edge_region_idx']
        perform_ingress_ip_reduction = instance_entry_dict.get("perform_ingress_ip_reduction", False)
        is_clean_ip_project = gcp_is_clean_ip_project(dbh, acct_id=instance_entry_dict['acct_id'])
        if is_clean_ip_project:
            dbh.logger.info("This is clean IP project")
        else:
            dbh.logger.info("This is not a clean IP project")

        my_inst = InstanceModel(dbh=dbh, iid=my_instance_id)
        if my_inst.get_param("id") in [None, 0]:
            err_msg = "Failed to get the instance ref for instance id %s" % str(my_instance_id)
            raise Exception(err_msg)

        # if the instance is behind, we will use the new API.
        if my_inst.get_param("is_instance_behind_nlb") == True \
                or instance_entry_dict.get("has_nat_gateway", False) == True:
            result = allocate_gp_gateways_egress_ip_for_new_instance_behind_nlb_nat_impl(dbh,
                                                                                         instance_entry_dict)
            if result == False:
                err_msg = "Failed to allocate egress ip fpr new gateway instance behind NLB/NAT"
                raise Exception(err_msg)
            if instance_entry_dict["is_using_sp_interconnect"] and sp_egress_type in ['PA', 'HYBRID']:
                dbh.logger.info("Interconnect is in PA/HYBRID mode, assigning DP2 IP")
                result = allocate_gp_gateways_egress_ip_dp2_for_new_instance_behind_nlb_nat_impl(dbh,
                                                                                                 instance_entry_dict)
            success = True
            return

        has_edge_region_idx = False if str(my_inst.get_param("compute_region_idx")) == str(edge_region_idx) else True
        dbh.logger.info("Instance %s has edge region idx set to %s" % (str(my_instance_id),
                                                                       str(has_edge_region_idx)))

        dbh.logger.info("Instance [%s] is a GW not in front of NAT and is not NLB/NAT node" %
                                                                        my_inst.get_param("name"))

        gcp_ip_mgmt = ipv4_mgmt.GCPIPHandler(dbh,
                                             str(instance_entry_dict['instance_id']),
                                             str(instance_entry_dict['acct_id']),
                                             node_id=instance_entry_dict['custnode_id'])

        # Handle ingress_ip_reduction here as well.
        ip_list = gcp_ip_mgmt.allocate_public_ip_for_customer(None,
                                                              instance_entry_dict['edge_region_idx'],
                                                              True)
        # Refresh InstanceModel object.
        my_inst = InstanceModel(dbh=dbh,
                                iid=instance_entry_dict['instance_id'])
        dbh.logger.info("IP list for instance name %s is %s" % (str(my_inst.get_param("name")),
                                                               str(my_inst.get_param("egress_ip_list"))))

        perform_ingress_ip_reduction = instance_entry_dict.get("perform_ingress_ip_reduction", False)
        dbh.logger.info(f"Perform ingress IP reduction flag is set as -> {perform_ingress_ip_reduction}")
        # Copy over the IP Address for each edge location that got updated from "egress_ip_list" into
        # "interface_ip_list" for the gateway instance.
        # This should be done for standlone MU instances only when ingress IP reduction feature is disabled
        if not perform_ingress_ip_reduction:
            dbh.logger.info(f"Updating interface_ip_list with egress_ip_list list as IIR feature is disabled")
            for ip_info in ip_list:
                region_idx = ip_info[0]
                dbh.logger.info("Handling ip_list entry: [%s]; region_idx: [%s]" % (ip_info, region_idx))
                ret = my_inst.update_interface_ip_list_with_egress_ip(region_idx)
                if not ret:
                    err_msg = ("Failed to update interface ip list with egress ip for %s" % region_idx)
                    dbh.logger.error("Error: %s" % err_msg)
                    raise Exception(err_msg)

                ret = my_inst.update_interface_ipv6_list_with_egress_ipv6(region_idx)
                if not ret:
                    err_msg = (f"Failed to update interface ipv6 list with egress ipv6 for {region_idx}")
                    dbh.logger.error(err_msg)
                    raise customException.InterfaceIpv6ListUpdateException(err_msg)
        else:
            dbh.logger.info(f"Skip updating interface_ip_list with egress_ip_list as IIR feature is enabled")

        settings = gcp_ip_mgmt.find_nlb_settings_by_custid_and_region_id(my_inst.get_param("custid"),
                                                                         my_inst.get_param("compute_region_idx"))

        is_nlb_ip_needed = settings['is_nlb_supported'] and settings['cust_is_nlb_supported']

        if is_nlb_ip_needed:
            # reserve IP NLB NODE_TYPE_NLB_INSTANCE after first GW bring up if enable ingress ip reduction
            ip_mgmt = IPManagementModel(dbh=dbh)
            is_sp_transport_type_loopback = False
            if (my_inst.get_param("is_using_sp_interconnect")
                    and instance_entry_dict.get('sp_egress_type', "SP") not in ['PA', 'HYBRID']):
                is_sp_transport_type_loopback = True
            result = ip_mgmt.reserve_ips_for_cust_region(my_inst.get_param("custid"),
                                                         my_inst.get_param("node_type"),
                                                         my_inst.get_param("compute_region_idx"),
                                                         my_inst.get_param("compute_region_idx"), 1,
                                                         target_node_type=NODE_TYPE_NLB_INSTANCE,
                                                         reserve_pupi_ip=is_sp_transport_type_loopback)
            dbh.logger.info("NLB reserved Public IP is : %s" % result)
            
        success = True

    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success

def allocate_egress_ip_for_new_instance_impl(dbh,
                                             instance_entry_dict,
                                             is_passive):
    success = True
    try:
        node_type = None
        my_instance_id = instance_entry_dict['instance_id']
        edge_region_idx = instance_entry_dict['edge_region_idx']
        is_clean_ip_project = gcp_is_clean_ip_project(dbh, acct_id=instance_entry_dict['acct_id'])
        if is_clean_ip_project:
            dbh.logger.info("This is clean IP project")
        else:
            dbh.logger.info("This is not a clean IP project")

        my_inst = InstanceModel(dbh=dbh, iid=my_instance_id)
        if my_inst.get_param("id") in [None, 0]:
            err_msg = "Failed to get the instance ref for instance id %s" % str(my_instance_id)
            raise Exception(err_msg)

        node_type = my_inst.get_param("node_type")

        if is_passive:
            dbh.logger.info("Copy IPs from active instance %s to passive instance %s" %
                                                                        (str(instance_entry_dict['ha_peer']),
                                                                         str(instance_entry_dict['instance_id'])))
            gcp_ip_mgmt = ipv4_mgmt.GCPIPHandler(dbh,
                                                 str(instance_entry_dict['ha_peer']),
                                                 str(instance_entry_dict['acct_id']),
                                                 node_id=instance_entry_dict['custnode_id'])
            gcp_ip_mgmt.copy_ip_from_active_instance(str(instance_entry_dict['instance_id']))
            success =  True
            return

        # Handle the primary instance first.
        has_edge_region_idx = False if str(my_inst.get_param("compute_region_idx")) == str(edge_region_idx) else True
        dbh.logger.info("Instance %s has edge region idx set to %s" % (str(my_instance_id),
                                                                       str(has_edge_region_idx)))

        gcp_ip_mgmt = ipv4_mgmt.GCPIPHandler(dbh,
                                             str(instance_entry_dict['instance_id']),
                                             str(instance_entry_dict['acct_id']),
                                             node_id=instance_entry_dict['custnode_id'])

        # Handle ingress_ip_reduction here as well.
        ip_list = gcp_ip_mgmt.allocate_public_ip_for_customer(None,
                                                              instance_entry_dict['edge_region_idx'],
                                                              True)
        # Refresh InstanceModel object.
        my_inst = InstanceModel(dbh=dbh,
                                iid=instance_entry_dict['instance_id'])
        dbh.logger.info("IP list for instance name %s is %s" % (str(my_inst.get_param("name")),
                                                               str(my_inst.get_param("egress_ip_list"))))

        success = True

    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success


def allocate_egress_ip_for_new_instance(dbh,
                                        instance_entry_dict,
                                        node_type,
                                        is_passive=False,
                                        sp_egress_type='SP'):
    success = False
    try:
        if node_type == NODE_TYPE_CLEAN_PIPE:
            success = allocate_clean_pipe_public_ip_for_new_instance(dbh,
                                                                     instance_entry_dict)
            return
        elif node_type in [NODE_TYPE_NAT_INSTANCE, NODE_TYPE_NLB_INSTANCE]:
            success = allocate_nat_nlb_egress_ip_for_new_instance_impl(dbh, instance_entry_dict)
            if success and instance_entry_dict['is_using_sp_interconnect'] and sp_egress_type in ['PA', 'HYBRID'] \
                    and node_type == NODE_TYPE_NAT_INSTANCE:
                success = allocate_nat_egress_ip_dp2_for_new_instance_impl(dbh, instance_entry_dict)

            return
        elif node_type == NODE_TYPE_GP_GATEWAY:
            # Handle GP gateway instance.
            success = allocate_gp_gateways_egress_ip_for_new_instance_impl(dbh, instance_entry_dict,
                                                                           sp_egress_type=sp_egress_type)
            return
        else:
            # node_type in [NODE_TYPE_REMOTE_NET, NODE_TYPE_SERVICE_CONN, NODE_TYPE_GP_PORTAL]:
            success = allocate_egress_ip_for_new_instance_impl(dbh,
                                                               instance_entry_dict,
                                                               is_passive)
            return

    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success


def allocate_egress_ipv6_for_new_instance_impl(dbh,
                                               instance_id,
                                               edge_region_idx,
                                               ha_peer,
                                               is_passive):
    success = False
    try:
        my_instance_id = instance_id
        #egress_ip_list_loc = []

        has_egress_ip_list_loc = False
        egress_ipv6_list_loc = []
        egress_ipv4_list_loc = []

        ha_peer_id = None
        if is_passive:
            ha_peer_id = ha_peer

        my_inst = InstanceModel(dbh=dbh, iid=my_instance_id)
        if my_inst.get_param("id") in [None, 0]:
            err_msg = "Failed to get the instance ref for instance id %s" % str(my_instance_id)
            raise Exception(err_msg)

        acct_id = my_inst.get_param("acct_id")
        is_clean_ip_project = gcp_is_clean_ip_project(dbh, acct_id=acct_id)
        if is_clean_ip_project:
            dbh.logger.info("This is clean IP project")
        else:
            dbh.logger.info("This is not a clean IP project")

        node_type = my_inst.get_param("node_type")
        compute_region_idx = my_inst.get_param("compute_region_idx")

        if is_passive:
            dbh.logger.info("Copy egress ipv6 subnet list from active instance %s to passive instance %s" %
                                                                      ( str(ha_peer_id), str(my_instance_id)))
            lret = copy_egress_ipv6_subnet_list_from_primary_instance(dbh,
                                                                      primary_instance_id=ha_peer_id,
                                                                      secondary_instance_id=my_instance_id)
            if lret == False:
                err_msg = ("Failed to copy IPv6 subnet list from instance %s to instance %s" %
                                                                                    (str(ha_peer_id),
                                                                                     str(my_instance_id)))
                raise Exception(err_msg)
            success =  True
            return

        # Let's allocate IPv6 address even if it is compute location:
        gcp_ipv6_mgmt = ipv6_mgmt_1.IPV6_Handler(dbh, acct_id=acct_id)
        success = gcp_ipv6_mgmt.upsert_instance_egress_ipv6_list_subnet_location(my_instance_id, compute_region_idx)
        if success == False:
            err_msg = ("Failed to upsert instance egress IPv6 list allocation for instance id %s and "
                       "compute_region_idx %s" % (str(my_instance_id), str(compute_region_idx)))
            raise Exception(err_msg)

        # pinned instance check is only valid for MU gateways.
        if node_type in [ NODE_TYPE_GP_GATEWAY, NODE_TYPE_NLB_INSTANCE ]:
            egress_ipv6_list_loc = my_inst.get_existing_egress_ipv6_region_list_for_pinned_instance(
                                                                                my_inst.get_param("custid"),
                                                                                my_inst.get_param("compute_region_idx"),
                                                                                NODE_TYPE_GP_GATEWAY)
            dbh.logger.info("The egress locations for the instance are %s" % str(egress_ipv6_list_loc))
            has_egress_ip_list_loc = True if (egress_ipv6_list_loc and len(egress_ipv6_list_loc)) else False

        # Now regardless of the node type we need to take care of the migration case where we look up edge location info
        # also

        egress_ipv4_list_loc = \
            my_inst.get_egress_ipv6_region_list_from_egress_ipv4_list(instance_id=str(my_inst.get_param("id")))

        egress_ip_list_loc = list(set(egress_ipv6_list_loc) | set(egress_ipv4_list_loc))

        has_edge_region_idx = False if str(my_inst.get_param("compute_region_idx")) == str(edge_region_idx) else True
        if has_edge_region_idx and str(edge_region_idx) not in egress_ip_list_loc:
            egress_ip_list_loc.append(str(edge_region_idx))

        for my_edge_region_idx in egress_ip_list_loc:
            gcp_ipv6_mgmt = ipv6_mgmt_1.IPV6_Handler(dbh, acct_id=acct_id)
            success = gcp_ipv6_mgmt.upsert_instance_egress_ipv6_list_subnet_location(my_instance_id, my_edge_region_idx)
            if success == False:
                err_msg = ("Failed to upsert instance egress IPv6 list allocation for instance id %s and "
                           "edge_region_idx %s" % (str(my_instance_id), str(my_edge_region_idx)))
                raise Exception(err_msg)

        # Set the success status as True.
        success = True

    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success

def allocate_egress_ipv6_for_new_instance(dbh,
                                          instance_entry_dict,
                                          node_type,
                                          is_passive=False):
    success = False
    try:
        dbh.logger.info("Allocating egress IPv6 address for node type %s" % str(node_type))
        if node_type in [ NODE_TYPE_GP_GATEWAY, NODE_TYPE_GP_PORTAL, NODE_TYPE_REMOTE_NET, NODE_TYPE_NLB_INSTANCE ]:
            # Handle GP gateway instance.
            success = allocate_egress_ipv6_for_new_instance_impl(dbh,
                                                                 instance_entry_dict['instance_id'],
                                                                 instance_entry_dict['edge_region_idx'],
                                                                 instance_entry_dict['ha_peer'],
                                                                 is_passive)
            return
        success = True
    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success

def get_swg_proxy_nlb_egress_ip(dbh, instance_entry_dict):
    nlb_ip = None
    try:
        gcp_ip_mgmt_ref = ipv4_mgmt.GCPIPHandler(dbh,
                                             str(instance_entry_dict['instance_id']),
                                             str(instance_entry_dict['acct_id']),
                                             node_id=instance_entry_dict['custnode_id'])

        inst_ref = InstanceModel(dbh=dbh, iid=instance_entry_dict['instance_id'])
        cm = CustomerModel(custid=inst_ref.get_param("custid"), dbh=dbh)
        if not cm.get_param("id"):
            err_msg = (f"Failed to retrieve cust_master model "
                       f"for custid {str(inst_ref.get_param('custid'))}")
            dbh.logger.error(
                f"get_gcp_salt_profile_for_instance_entry: "
                f"{err_msg}")
            raise Exception(err_msg)

        ept = ExplicitProxyTenantInfoModel(dbh=dbh,
                                           tenant_id=cm.get_param("acct_id"))
        if not ept.get_entry():
            err_msg = (f"Failed to retrieve explicit proxy "
                       f"tenant info for tenant_id {cm.get_param('acct_id')}")
            dbh.logger.error(
                f"get_gcp_salt_profile_for_instance_entry: "
                f"{err_msg}")
            raise Exception(err_msg)

        node_type = inst_ref.get_param("node_type")
        alt_node_type = inst_ref.get_param("alt_node_type")
        enable_tls_term_on_ep = ept.get_param("enable_tls_term_on_ep")
        enable_global_nlb = 0
        if not ept.get_param("enable_tls_term_on_ep"):
            cust_ep_cfg = CustEpaasConfigModel(dbh, cm.get_param("id"), inst_ref.get_param("compute_region_idx"),  PROVIDER_GCP, node_type=node_type, alt_node_type=alt_node_type)
            if cust_ep_cfg.get_entry():
                enable_tls_term_on_ep = cust_ep_cfg.get_param("enable_tls_term_on_ep")
                enable_global_nlb = cust_ep_cfg.get_param("enable_global_nlb")

        if node_type == NODE_TYPE_BI_NH_PROXY:
            is_global_ip = None
        else:
            is_global_ip = is_global_ip_needed(dbh, enable_tls_term_on_ep, enable_global_nlb, cm.get_param("id"), inst_ref.get_param("compute_region_idx"), node_type, alt_node_type)
        dbh.logger.info('Trying to allocate NLB IP with enable_tls_term_on_ep = %s is_global_ip = %s' % (enable_tls_term_on_ep, is_global_ip))
        nlb_ip = gcp_ip_mgmt_ref.get_nlb_ip(is_global_ip)
        if nlb_ip:
            dbh.logger.info('nlb_ingress_ip: %s added to salt_profile' % nlb_ip)
        else:
            dbh.logger.error("No nlb IP found!!")
    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        nlb_ip = None
    finally:
        return nlb_ip

def get_gcp_salt_profile_for_instance_entry(dbh, instance_entry_dict, is_passive, is_clean_pipe=0, node_type=None):
    salt_profile = {}
    dbh.logger.info("get_gcp_salt_profile_for_instance_entry is_clean_pipe: %d, node_type: %s" % (is_clean_pipe, node_type))
    try:
        '''
        ### Sample format for salt profile:- ###

        "InstType",
        "UserData",
        "PrimaryId",
        "SerialNo",
        "AcctId",
        "CustId",
        "PrimaryIntfSet"
        '''

        event = {}
        event['dbh'] = dbh
        event['inst_id'] = instance_entry_dict['instance_id']
        event['region'] = instance_entry_dict['region']
        event['isPassive'] = is_passive
        event['cloudtype'] = PROVIDER_GCP
        event['gpcs_instance_size'] = instance_entry_dict['gpcs_instance_size']
        event['version'] = instance_entry_dict['version']
        event['instance_name'] = instance_entry_dict['instance_name']
        event['saas_gpcs_api_endpoint'] = instance_entry_dict['saas_gpcs_api_endpoint']
        event['cert_fetch_otp'] = instance_entry_dict['cert_fetch_otp']
        event['cloud_provider'] = instance_entry_dict['cloud_provider']
        event['super_custid'] = instance_entry_dict['super_custid']
        event['custnode_id'] = instance_entry_dict['custnode_id']
        event['parent_id'] = instance_entry_dict['parent_id']
        event['avail_domain'] = instance_entry_dict['avail_domain']
        event['sessionAffinity'] = instance_entry_dict['sessionAffinity']
        event['gp_gw_domain'] = instance_entry_dict['gp_gw_domain']
        event['commit_validate'] = instance_entry_dict['commit_validate']
        event['is_nlb_supported'] = \
            instance_entry_dict['is_nlb_supported']
        event['is_ngpa_protocol_enabled'] = \
            instance_entry_dict['is_ngpa_protocol_enabled']
        event['perform_ingress_ip_reduction'] = \
            instance_entry_dict['perform_ingress_ip_reduction']
        event['is_central_cache_supported'] = \
            instance_entry_dict['is_central_cache_supported']
        event['is_instance_behind_nlb'] = \
            instance_entry_dict['is_instance_behind_nlb']
        event['central_cache_service_endpoint'] = \
            instance_entry_dict['central_cache_service_endpoint']
        event['central_cache_service_backup_endpoint'] = \
            instance_entry_dict['central_cache_service_backup_endpoint']
        event['ciam_service_endpoint'] = \
            instance_entry_dict['ciam_service_endpoint']
        event['is_using_sp_interconnect'] = instance_entry_dict['is_using_sp_interconnect']
        event['is_interconnect_onramp'] = instance_entry_dict.get('is_interconnect_onramp', False)
        dbh.logger.info(f"instance_entry_dict.get(is_interconnect_onramp) : {event['is_interconnect_onramp']}")
        if 'interconnect_name' in instance_entry_dict.keys():
            event['interconnect_name'] = instance_entry_dict['interconnect_name']
        if 'mgmt-interface-swap' in list(instance_entry_dict.keys()):
            event['mgmt-interface-swap'] = instance_entry_dict["mgmt-interface-swap"]
        if 'instance_role' in list(instance_entry_dict.keys()):
            event['instance_role'] = instance_entry_dict["instance_role"]
        if 'slot_nr' in list(instance_entry_dict.keys()):
            event['slot_nr'] = instance_entry_dict["slot_nr"]
        if 'inter_instance_psk' in list(instance_entry_dict.keys()):
            event['inter_instance_psk'] = instance_entry_dict["inter_instance_psk"]
        if 'colo_interface_support' in list(instance_entry_dict.keys()):
            event['colo_interface_support'] = instance_entry_dict["colo_interface_support"]
        if 'cloud_machine_type' in list(instance_entry_dict.keys()):
            event['cloud_machine_type'] = instance_entry_dict["cloud_machine_type"]
        if 'cpu_platform' in list(instance_entry_dict.keys()):
            event['cpu_platform'] = instance_entry_dict["cpu_platform"]
        if 'is_service_nic_supported' in list(instance_entry_dict.keys()):
            event['is_service_nic_supported'] = instance_entry_dict["is_service_nic_supported"]
        if 'has_dual_egress_nic' in list(instance_entry_dict.keys()):
            event['has_dual_egress_nic'] = instance_entry_dict["has_dual_egress_nic"]
        if 'is_datapath_on_shared_vpc' in list(instance_entry_dict.keys()):
            event['is_datapath_on_shared_vpc'] = instance_entry_dict["is_datapath_on_shared_vpc"]
        if 'frr-enabled' in list(instance_entry_dict.keys()):
            event['frr-enabled'] = instance_entry_dict['frr-enabled']
        if 'proxy-protocol-enabled' in list(instance_entry_dict.keys()):
            event['proxy-protocol-enabled'] = instance_entry_dict['proxy-protocol-enabled']
        if 'ep-geneve-enabled' in list(instance_entry_dict.keys()):
            event['ep-geneve-enabled'] = instance_entry_dict['ep-geneve-enabled']
        if 'colo_ilb_name' in list(instance_entry_dict.keys()):
            event['colo_ilb_name'] = instance_entry_dict['colo_ilb_name']

        # Get the ID of node type since its easier to work with.
        node_type_id = instance_entry_dict["node_type_id"]
        salt_profile["IsNLBSupported"] = instance_entry_dict['is_nlb_supported']

        event['sp_egress_type'] = 'SP'
        if instance_entry_dict['is_using_sp_interconnect']:
            if 'sp_egress_type' in list(instance_entry_dict.keys()):
                event['sp_egress_type'] = instance_entry_dict['sp_egress_type']

        if 'is_5g_enabled' in list(instance_entry_dict.keys()):
            event['is_5g_enabled'] = instance_entry_dict['is_5g_enabled']

        if is_clean_pipe:
            result = allocate_egress_ip_for_new_instance(dbh,
                                                        instance_entry_dict,
                                                        node_type_id,
                                                        sp_egress_type=event['sp_egress_type'])

            if result == False:
                raise customException.EgressIPAllocationException("Failed to allocate egress IP for instance node type %s" % str(node_type))

        else:
            if event.get('instance_role') in [INSTANCE_ROLE_MP_STRING] or is_service_ilb(node_type):
                dbh.logger.info(f"Not allocating any public IP for MP VM or {node_type}")

            elif not is_passive:
                result = allocate_egress_ip_for_new_instance(dbh,
                                                             instance_entry_dict,
                                                             node_type_id,
                                                             sp_egress_type=event['sp_egress_type'])
                if result == False:
                    if instance_entry_dict['is_nlb_supported'] == 1 and node_type == NODE_TYPE_NLB_INSTANCE:
                        err_msg = "Failed to allocate egress IP for instance node type %s for NGPA stack" % str(node_type)
                        raise customException.NGPAReservedIPAllocationExceptionNLB(err_msg)
                    elif instance_entry_dict['is_nlb_supported'] == 1 and node_type == NODE_TYPE_NAT_INSTANCE:
                        err_msg = "Failed to allocate egress IP for instance node type %s for NGPA stack" % str(node_type)
                        raise customException.NGPAReservedIPAllocationExceptionNAT(err_msg)
                    else:
                        err_msg = "Failed to allocate egress IP for instance node type %s" % str(node_type)
                        raise customException.EgressIPAllocationException(err_msg)

                # IPv6 is only for gateways, portals, remote networks for now.
                if instance_entry_dict.get("has_external_ipv6") == True:
                    dbh.logger.info("External IPv6 support is enabled for the instances %s" %
                                    str(instance_entry_dict['instance_name']))
                    result = allocate_egress_ipv6_for_new_instance(dbh, instance_entry_dict,
                                                                   node_type_id)
                else:
                    dbh.logger.info("External IPv6 support is not enabled for the instances %s" %
                                                        str(instance_entry_dict['instance_name']))
                    result = True

                if result == False:
                    err_msg = "Failed to allocate egress IPv6 for instance node type %s" % str(node_type)
                    raise customException.EgressIPv6AllocationException(err_msg)

                if is_swg_proxy(node_type) or is_binh_proxy(node_type):
                    nlb_ip  = get_swg_proxy_nlb_egress_ip(dbh, instance_entry_dict)
                    if nlb_ip != None:
                        salt_profile['nlb_ingress_ip'] = nlb_ip
                    else:
                        dbh.logger.error("No nlb IP found!!")
                        raise customException.EgressIPAllocationException(" Exception: No nlb IP found")

            else:
                result = allocate_egress_ip_for_new_instance(dbh, instance_entry_dict,
                                                             node_type_id, is_passive=is_passive,
                                                             sp_egress_type=event['sp_egress_type'])
                if result == False:
                    raise customException.EgressIPAllocationException("Failed to allocate egress IP for passive instance")

                if instance_entry_dict.get("has_external_ipv6") == True:
                    dbh.logger.info("External IPv6 support is enabled for the instances %s" %
                                    str(instance_entry_dict['instance_name']))
                    result = allocate_egress_ipv6_for_new_instance(dbh, instance_entry_dict,
                                                                   node_type_id, is_passive)
                else:
                    dbh.logger.info("External IPv6 support is not enabled for the instances %s" %
                                                        str(instance_entry_dict['instance_name']))
                    result = True

        # Populate the rest of the needed stuff.
        if is_clean_pipe:
            inst_prof = gcp_cp_instance_info_handler(event)
        else:
            inst_prof = gcp_instance_info_handler(event)

        if inst_prof == None:
            raise Exception("!!! Failed to generate the instance profile from event !!!")

        if dbh == None:
            raise Exception("No database handler to work with! cannot continue!")

        # General info:
        salt_profile["CustId"] = instance_entry_dict['cust_id']
        salt_profile["AcctId"] = instance_entry_dict['acct_id']
        salt_profile["SerialNo"] = instance_entry_dict['serial_no']
        instanceName = instance_entry_dict['instance_alias']
        salt_profile["InstanceName"] = instanceName
        salt_profile["InstanceId"] = instance_entry_dict['instance_id']
        salt_profile["is_clean_pipe"] = is_clean_pipe
        salt_profile['sase_fabric'] = instance_entry_dict['sase_fabric']
        salt_profile['is_gcp_cnat_support_enabled'] = instance_entry_dict['is_gcp_cnat_support_enabled']
        salt_profile['is_interconnect_onramp'] = instance_entry_dict.get('is_interconnect_onramp', False)

        # print inst_prof

        salt_profile["is_masque_enabled"] = instance_entry_dict.get('is_masque_enabled', False)

        # Balance VMs across available zones for GPGW, SWG
        if node_type_id in (NODE_TYPE_GP_GATEWAY, NODE_TYPE_SWG_PROXY, NODE_TYPE_NAT_INSTANCE):
            salt_profile["Zone"] = get_zone_by_balancing_usage(dbh, instance_entry_dict, inst_prof)
            salt_profile["ZonesList"] = inst_prof["zonesList"]
        elif node_type_id in LOAD_BALANCER_INSTANCE_LIST:
            # For NLB instances, need ZonesList to track UMIG placement
            salt_profile["Zone"] = inst_prof['zone']
            salt_profile["ZonesList"] = inst_prof["zonesList"]
        else:
            salt_profile["Zone"] = inst_prof['zone']
        salt_profile["RegionName"] = get_cloud_native_location_name_from_region_id(
                                                    dbh,
                                                    instance_entry_dict['region'])
        # Get cloud specific user data.
        salt_profile["UserData"] = inst_prof['userdata']
        salt_profile["ImageProject"] = inst_prof["image_project"]
        ami_ids = inst_prof['ami_ids']
        if 'SharedMgmtVPCProj' in list(inst_prof.keys()):
            salt_profile['SharedMgmtVPCProj'] = inst_prof['SharedMgmtVPCProj']
        salt_profile["svc_acct"] = inst_prof['svc_acct']

        if ('capacity_type' in list(instance_entry_dict.keys())):
            capacity_type = instance_entry_dict['capacity_type']
        else:
            capacity_type = "default"
        salt_profile["PrimaryCapacityType"] = capacity_type

        dpdk_qcount = instance_entry_dict.get('dpdk_qcount', 0)
        if dpdk_qcount > 0:
            salt_profile["PrimaryDpdkQcount"] = dpdk_qcount

        if ('cloud_machine_type' in list(instance_entry_dict.keys())):
            cloud_machine_type = instance_entry_dict['cloud_machine_type']
        else:
            cloud_machine_type = inst_prof['instType']

        cloud_machine_type = get_target_override_for_version(dbh,
                                                            "gcp",
                                                            instance_entry_dict['cust_id'],
                                                            node_type_id,
                                                            cloud_machine_type,
                                                            instance_entry_dict['version'],
                                                            instance_entry_dict['region'])
        if (is_passive == True):
            (cloud_machine_type, capacity_type) = get_target_passive_machine_type(dbh,
                                                                                  instance_entry_dict['instance_id'],
                                                                                  cloud_machine_type,
                                                                                  capacity_type)
            salt_profile["SecondaryCapacityType"] = capacity_type
            if dpdk_qcount > 0:
                salt_profile["SecondaryDpdkQcount"] = dpdk_qcount

        # Get the instance type to be spinned up.
        salt_profile["InstType"] = cloud_machine_type

        if 'cpu_platform' in inst_prof.keys() and inst_prof['cpu_platform'] != '':
            salt_profile["MinCpuPlatform"] = inst_prof['cpu_platform']

        # set ami id based on arch type
        arch_type = get_arch_for_cloud_machine_type(dbh, salt_profile["InstType"])
        try:
            if node_type_id in LOAD_BALANCER_INSTANCE_LIST or node_type in LOAD_BALANCER_INSTANCE_NAME_LIST:
                ami_id = ''
            else:
                ami_id = ami_ids[arch_type]
            salt_profile["ImageName"] = ami_id
        except Exception as E:
            dbh.logger.error(f"Missing AMI for version: {instance_entry_dict['version']}, "
                             f"cloud_machine_type: {cloud_machine_type} arch: {arch_type}")


        salt_profile["arch-type"] = arch_type

        #IPv6 support check for dp interface
        if "has_external_ipv6" in instance_entry_dict.keys():
            salt_profile["hasExternalIPv6"] = True

        if (event.get('has_dual_egress_nic', False)
            and not gcp_instance_has_nic_limit(dbh.logger, cloud_machine_type)
            and ("has_external_ipv6_dp2" in instance_entry_dict.keys())):
            salt_profile["hasExternalIPv6Dp2"] = instance_entry_dict['has_external_ipv6_dp2']

        if is_clean_pipe:
            # Overriding assigned zone based on usage
            salt_profile["Zone"] = get_gcp_cleanpipe_zone(event,
                                                          inst_prof['zones_list'])
            salt_profile["ZonesList"] = inst_prof['zones_list']
            salt_profile['MgmtSubnet'] = inst_prof['MgmtSubnet']
            salt_profile['TrustSubnet'] = inst_prof['TrustSubnet']
            salt_profile['UnTrustSubnet'] = inst_prof['UnTrustSubnet']
            salt_profile['MgmtNetwork'] = inst_prof['MgmtNetwork']
            salt_profile['TrustNetwork'] = inst_prof['TrustNetwork']
            salt_profile['UnTrustNetwork'] = inst_prof['UnTrustNetwork']

            salt_profile['MgmtInterfaceName'] = inst_prof['MgmtInterfaceName']
            salt_profile['TrustInterfaceName'] = inst_prof['TrustInterfaceName']
            salt_profile['UnTrustInterfaceName'] = inst_prof['UnTrustInterfaceName']

            salt_profile['MgmtHasExternalIP'] = inst_prof['MgmtHasExternalIP']
            salt_profile['TrustHasExternalIP'] = inst_prof['TrustHasExternalIP']
            salt_profile['UnTrustHasExternalIP'] = inst_prof['UnTrustHasExternalIP']
            salt_profile['AvailabilityDomain'] = inst_prof['AvailabilityDomain']

            salt_profile["PrimaryIntfSet"] = 0
            salt_profile["sessionAffinity"] = inst_prof["sessionAffinity"]
            salt_profile["static_ip"] = inst_prof["static_ip"]
            salt_profile["clean_ip_tag"] = inst_prof['clean_ip_tag']
        else:
            salt_profile["clean_ip_tag"] = inst_prof['clean_ip_tag']

            salt_profile["static_ip"] = inst_prof["static_ip"]
            salt_profile['MgmtSubnet'] = inst_prof['MgmtSubnet']
            salt_profile['DPSubnet'] = inst_prof['DPSubnet']
            salt_profile['HASubnet'] = inst_prof['HASubnet']
            # Subnet info:-
            salt_profile['MgmtNetwork'] = inst_prof['MgmtNetwork']
            salt_profile['DPNetwork'] = inst_prof['DPNetwork']
            salt_profile['HANetwork'] = inst_prof['HANetwork']

            salt_profile['MgmtInterfaceName'] = inst_prof['MgmtInterfaceName']
            salt_profile['DPInterfaceName'] = inst_prof['DPInterfaceName']
            salt_profile['HAInterfaceName'] = inst_prof['HAInterfaceName']

            salt_profile['MgmtHasExternalIP'] = inst_prof['MgmtHasExternalIP']
            salt_profile['DPHasExternalIP'] = inst_prof['DPHasExternalIP']
            salt_profile['HAHasExternalIP'] = inst_prof['HAHasExternalIP']

            if is_swg_proxy(node_type) or is_binh_proxy(node_type):
                salt_profile['ExtrnSubnet'] = inst_prof['ExtrnSubnet']
                salt_profile['ExtrnHasExternalIP'] = inst_prof['ExtrnHasExternalIP']
                salt_profile['ExtrnInterfaceName'] = inst_prof['ExtrnInterfaceName']
                salt_profile['ExtrnNetwork'] = inst_prof['ExtrnNetwork']

            if (event.get('colo_interface_support', None) != None) \
                    and event.get('colo_interface_support') == True:
                salt_profile['colo_interface_support'] = event['colo_interface_support']
                salt_profile['COLOSubnet'] = inst_prof['COLOSubnet']
                salt_profile['COLOHasExternalIP'] = inst_prof['COLOHasExternalIP']
                salt_profile['COLOInterfaceName'] = inst_prof['COLOInterfaceName']
                salt_profile['COLONetwork'] = inst_prof['COLONetwork']
                if event.get('colo_ilb_name') != None:
                    salt_profile['colo_ilb_name'] = event['colo_ilb_name']


            if (event.get('is_service_nic_supported', None) != None) \
                    and event.get('is_service_nic_supported') == True and gcp_instance_has_nic_limit(dbh.logger, cloud_machine_type) == False:
                salt_profile['is_service_nic_supported'] = event['is_service_nic_supported']
                salt_profile['serviceSubnet'] = inst_prof['serviceSubnet']
                salt_profile['serviceInterfaceName'] = inst_prof['serviceInterfaceName']
                salt_profile['serviceNetwork'] = inst_prof['serviceNetwork']
                salt_profile['serviceHasExternalIP'] = inst_prof['serviceHasExternalIP']
                salt_profile['serviceProjectOverride'] = inst_prof['serviceProjectOverride']

            if ((node_type_id == NODE_TYPE_SERVICE_ILB or node_type_id ==NODE_TYPE_GP_GATEWAY)
                and ((event.get('is_5g_enabled', None) != None)
                     and event.get('is_5g_enabled') == True
                     and gcp_instance_has_nic_limit(dbh.logger, cloud_machine_type) == False)):
                salt_profile['is_5g_enabled'] = event.get('is_5g_enabled')

            if (event.get('is_datapath_on_shared_vpc', None) != None) \
                    and event.get('is_datapath_on_shared_vpc') == True:
                salt_profile['is_datapath_on_shared_vpc'] = event['is_datapath_on_shared_vpc']
                salt_profile['DPProjectOverride'] = inst_prof['DPProjectOverride']

            salt_profile['frr-enabled'] = event.get('frr-enabled', 0)
            salt_profile['proxy-protocol-enabled'] = event.get('proxy-protocol-enabled', 0)

            # if salt_profile['is_sase_fabric_spn'] != 0:
            #     salt_profile['SASESubnet'] = inst_prof['SASESubnet']
            #     salt_profile['SASENetwork'] = inst_prof['SASENetwork']
            #     salt_profile['SASEInterfaceName'] = inst_prof['SASEInterfaceName']
            #     salt_profile['SASEHasExternalIP'] = inst_prof['SASEHasExternalIP']
            #     salt_profile['SASEHostProject'] = inst_prof['SASEHostProject']

            salt_profile['public_ip_dp2'] = inst_prof['public_ip_dp2']
            if (event.get('has_dual_egress_nic', False) == True
                    and gcp_instance_has_nic_limit(dbh.logger, cloud_machine_type) == False):
                salt_profile['DP2Subnet'] = inst_prof['DP2Subnet']
                salt_profile['DP2Network'] = inst_prof['DP2Network']
                salt_profile['DP2InterfaceName'] = inst_prof['DP2InterfaceName']
                salt_profile['DP2HasExternalIP'] = inst_prof['DP2HasExternalIP']

            if (event.get('is_interconnect_onramp', False) == True
                    and gcp_instance_has_nic_limit(dbh.logger, cloud_machine_type) == False):
                salt_profile['intOnrampSubnet'] = inst_prof['intOnrampSubnet']
                salt_profile['intOnrampNetwork'] = inst_prof['intOnrampNetwork']
                salt_profile['intOnrampInterfaceName'] = inst_prof['intOnrampInterfaceName']
                salt_profile['intOnrampHasExternalIP'] = inst_prof['intOnrampHasExternalIP']

            salt_profile["is_using_sp_interconnect"] = inst_prof['is_using_sp_interconnect']
            # Used during software upgrade for Site FW/SVC Conn. Not sure if its applicable to GCP.
            salt_profile["PrimaryIntfSet"] = 0
            salt_profile['perform_ingress_ip_reduction'] = int(event.get('perform_ingress_ip_reduction', False))

        if "is_using_sp_interconnect" in inst_prof.keys() and inst_prof["is_using_sp_interconnect"] \
                and (node_type_id == NODE_TYPE_NLB_INSTANCE or node_type == "NLB"):
            dbh.logger.info("Get DPSubnet from public_ip_pool")
            gcp_ip_mgmt = ipv4_mgmt.GCPIPHandler(dbh, str(instance_entry_dict['clusterid']),
                                                 str(instance_entry_dict['acct_id']))
            salt_profile["DPSubnet"] = gcp_ip_mgmt.get_subnet_name_from_ip(instance_entry_dict['clusterid'])
            # Replace instance name with ILB for SP-Interconnect, as we are using internal load balancer
            salt_profile["InstanceName"] = 'i' + salt_profile["InstanceName"][1:]
        # Dump the dict
        dbh.logger.info("Salt profile dump for GCP instance is %s\n%s" %
                        (str(salt_profile['InstanceId']), str(salt_profile)))

    except customException.EgressIPAllocationException as E:
        error_message = str(E.args)
        dbh.logger.error(f"EgressIPv6AllocationException: {error_message}")
        publish_avisar_event_with_tenant_region_nodetype_key(dbh.avctx, dbh.logger,
                                                             ORCHESTRATION_INSTANCE_PUBLIC_IP_FETCH_FAILED,
                                                             error_message, METRIC_SEVERITY_CRITICAL)
        salt_profile = None
        raise  # Re-raise the exception to be caught by the general Exception block
    except customException.NGPAReservedIPAllocationExceptionNLB as E:
        error_message = str(E.args)
        dbh.logger.error(f"NGPAReservedIPAllocationExceptionNLB: {error_message}")
        publish_avisar_event_with_tenant_region_nodetype_key(dbh.avctx, dbh.logger,
                                                             ORCHESTRATION_NGPA_GCP_NLB_BRINGUP_FAILED_IP_RESERVATION_FAILURE,
                                                             error_message, METRIC_SEVERITY_CRITICAL)
        salt_profile = None
        raise  # Re-raise the exception to be caught by the general Exception block
    except customException.NGPAReservedIPAllocationExceptionNAT as E:
        error_message = str(E.args)
        dbh.logger.error(f"NGPAReservedIPAllocationExceptionNAT: {error_message}")
        publish_avisar_event_with_tenant_region_nodetype_key(dbh.avctx, dbh.logger,
                                                             ORCHESTRATION_NGPA_GCP_NAT_BRINGUP_FAILED_IP_RESERVATION_FAILURE,
                                                             error_message, METRIC_SEVERITY_CRITICAL)
        salt_profile = None
        raise  # Re-raise the exception to be caught by the general Exception block
    except customException.EgressIPv6AllocationException as E:
        error_message = str(E.args)
        dbh.logger.error(f"EgressIPv6AllocationException: {error_message}")
        publish_avisar_event_with_tenant_region_nodetype_key(dbh.avctx, dbh.logger,
                                                             ORCHESTRATION_INSTANCE_PUBLIC_IPV6_FETCH_FAILED,
                                                             error_message, METRIC_SEVERITY_CRITICAL)
        salt_profile = None
        raise  # Re-raise the exception to be caught by the general Exception block
    except Exception as E:
        dbh.logger.error("get_gcp_salt_profile_for_instance_entry: Exception %s occured. Traceback: %s locals: %s" %
                         (str(E.args), str(traceback.format_exc()), str(locals())))
        # Override salt_profile with None
        salt_profile = None

    finally:
        return salt_profile

def is_inbound_access_list_none(inbound_access_list, logger):
    ret = False
    if (inbound_access_list is None):
        ret = True
    elif (str(inbound_access_list).lower() in ('none', 'null', '')):
        ret = True
    else:
        try:
            if (inbound_access_list.decode().lower() in
                ('none', 'null', '')):
                ret = True
        except Exception as E:
            logger.info("Exception when trying to decode [%s]: %s" %
                        (inbound_access_list, str(E.args)))

    logger.info("Returning %s from is_inbound_access_list_none for %s" %
                (ret, inbound_access_list))
    return ret

def get_gcp_cust_region_instance_params(dbh, custid, region_idx, is_clean_pipe, node_type=None, upgrade_ep_os=False, alt_node_type=-1):
    ret = None
    params_dict = None
    try:
        if node_type not in (None, 0):
            if upgrade_ep_os:
                sql = ("SELECT DISTINCT instance_master.name, "
                       "instance_master.alias, instance_master.clusterid, "
                       "instance_master.salt_profile, "
                       "instance_master.upgrade_creation, instance_master.ha_state, "
                       "instance_master.egress_ip_list, "
                       "instance_master.egress_ipv6_list_subnet, "
                       "instance_master.interface_ip_list, "
                       "instance_master.interface_ipv6_list, "
                       "instance_master.inbound_access_ip_list, "
                       "instance_master.node_type, instance_master.alt_node_type, instance_master.public_ip, "
                       "instance_master.is_instance_behind_nlb, "
                       "instance_master.has_nat_instance, "
                       "instance_master.delete_deployment, "
                       "instance_master.no_passive_instance "
                       "FROM instance_master "
                       "inner join cust_topology on cust_topology.instance1_id = instance_master.id "
                       "WHERE instance_master.custid = %s AND compute_region_idx = %s AND "
                       "instance_master.node_type = %s AND instance_master.alt_node_type = %s AND "
                       "instance_master.is_sase_fabric_spn = 0 AND "
                       "instance_master.mcw_enabled = 0")
            else:
                sql = ("SELECT DISTINCT instance_master.name, "
                    "instance_master.alias, instance_master.clusterid, "
                    "instance_master.salt_profile, "
                    "instance_master.upgrade_creation, instance_master.ha_state, "
                    "instance_master.egress_ip_list, "
                    "instance_master.egress_ipv6_list_subnet, "
                    "instance_master.interface_ip_list, "
                    "instance_master.interface_ipv6_list, "
                    "instance_master.inbound_access_ip_list, "
                    "instance_master.node_type, instance_master.alt_node_type, instance_master.public_ip, "
                    "instance_master.is_instance_behind_nlb, "
                    "instance_master.has_nat_instance, "
                    "instance_master.delete_deployment, "
                    "instance_master.no_passive_instance "
                    "FROM instance_master "
                    "WHERE custid = %s AND compute_region_idx = %s AND "
                    "instance_master.node_type = %s AND "
                    "instance_master.alt_node_type = %s AND "
                    "instance_master.is_sase_fabric_spn = 0 AND "
                    "instance_master.mcw_enabled = 0")

            params = (custid, region_idx, node_type, alt_node_type)
        else:
            sql = ("SELECT DISTINCT instance_master.name, "
                "instance_master.alias, instance_master.clusterid, "
                "instance_master.salt_profile, "
                "instance_master.upgrade_creation, instance_master.ha_state, "
                "instance_master.egress_ip_list, "
                "instance_master.egress_ipv6_list_subnet, "
                "instance_master.interface_ip_list, "
                "instance_master.interface_ipv6_list, "
                "instance_master.inbound_access_ip_list, "
                "instance_master.node_type, instance_master.alt_node_type, instance_master.public_ip, "
                "instance_master.is_instance_behind_nlb, "
                "instance_master.has_nat_instance, "
                "instance_master.delete_deployment, "
                "instance_master.no_passive_instance "
                "FROM instance_master "
                "WHERE custid = %s AND compute_region_idx = %s AND "
                "instance_master.is_sase_fabric_spn = 0 AND "
                "instance_master.mcw_enabled = 0 AND "
                "((instance_master.node_type in (48, 51) AND "
                "((instance_master.ha_peer is NOT NULL AND "
                "instance_master.ha_peer != 0) or "
                "instance_master.no_passive_instance = 1)) or "
                "(instance_master.node_type in (49, 50, 156, 161)))")
            params = (custid, region_idx)
        params_dict = {}
        # We can also use set session TRANSACTION ISOLATION LEVEL READ COMMITTED to get proper results
        dbh.logger.info("Executing %s" % str(sql%params))
        dbh.conn.close()
        cursor = None
        try:
            cursor = dbh.get_cursor()
            cursor.execute(sql, params)
            ret = cursor.fetchall()
            dbh.cursorclose(cursor)
        except Exception as ex:
            error = (
                "Unable to query instances for customer id %s region %s: %s" % (str(custid), str(region_idx), str(ex)))
            dbh.logger.error(error)
            dbh.cursorclose(cursor)
            return None
        if not ret:
            return params_dict

        cm = CustomerModel(custid = custid, dbh = dbh)
        if not cm.get_param("id"):
            dbh.logger.error("get_gcp_cust_region_instance_params: Failed to retrieve cust_master model for custid: %s" % str(custid))
            return None
        clusters_to_be_deleted = set()
        cft_profile = dict()
        for name, alias, clusterid, salt_profile, upgrade_creation, \
            ha_state, egress_ip_list, egress_ipv6_list_subnet, interface_ip_list, interface_ipv6_list, inbound_access_ip_cfg, \
            im_node_type, im_alt_node_type, public_ip, is_behind_nlb, has_nat_instance, \
            delete_from_deployment, no_passive_instance in ret:
            try:
                if salt_profile == None:
                    continue
                cft_profile = json.loads(salt_profile)
            except Exception as ex:
                # Its better to not touch the stack if database has something wrong, than
                # Update stack with wrong values
                error = ("Unable to convert cft parameters from Json to dict\n%s" % str(salt_profile))
                dbh.logger.error(error)
                # raise Exception(error)

            # If this cluster does not need to be part of deployment, add it to clusters_to_be_deleted.
            if delete_from_deployment == clusterid:
                clusters_to_be_deleted.add(clusterid)

            # Get the "enable_tls_term_on_ep" and "enable_rn_to_ep_ctx_pass" from the
            # "explicit_proxy_tenant_info" table.
            enable_tls_term_on_ep = 0
            enable_rn_to_ep_ctx_pass = 0
            if im_node_type == NODE_TYPE_SWG_PROXY:
                ept = ExplicitProxyTenantInfoModel(dbh=dbh,
                              tenant_id=cm.get_param("acct_id"))
                if ept.get_entry():
                    enable_tls_term_on_ep = ept.get_param("enable_tls_term_on_ep")
                    enable_rn_to_ep_ctx_pass = ept.get_param("enable_rn_to_ep_ctx_pass")
                else:
                    dbh.logger.info(f"Failed to retrieve explicit proxy "
                               f"tenant info for tenant_id cm.acct_id")
                if not enable_tls_term_on_ep:
                    cust_ep_cfg = CustEpaasConfigModel(dbh, custid, region_idx, PROVIDER_GCP, node_type=im_node_type, alt_node_type=im_alt_node_type)
                    if cust_ep_cfg.get_entry() and cust_ep_cfg.get_param("enable_tls_term_on_ep"):
                        enable_tls_term_on_ep = cust_ep_cfg.get_param("enable_tls_term_on_ep")


            if clusterid in list(params_dict.keys()):
                params_dict[clusterid]['SecondaryInstanceName'] = cft_profile['InstanceName']
                params_dict[clusterid]['SecondaryId'] = cft_profile['InstanceId']
                params_dict[clusterid]['SecondaryZone'] = cft_profile['Zone']
                params_dict[clusterid]['SecondaryUserData'] = cft_profile['UserData']
                params_dict[clusterid]['SecondaryImageName'] = cft_profile['ImageName']
                params_dict[clusterid]['NodeType'] = im_node_type
                params_dict[clusterid]['Name'] = name
                params_dict[clusterid]['PublicIp'] = public_ip
                params_dict[clusterid]['IsBehindNlb'] = is_behind_nlb
                params_dict[clusterid]['IsUpgradeCreation'] = upgrade_creation

                params_dict[clusterid]['ContactLabel'] = cm.get_param("contact_label")

                # Include "enable_tls_term_on_ep" and "enable_rn_to_ep_ctx_pass"
                # from EP tenant_info entry into the return dictionary.
                params_dict[clusterid]['enable_tls_term_on_ep'] = enable_tls_term_on_ep
                params_dict[clusterid]['enable_rn_to_ep_ctx_pass'] = enable_rn_to_ep_ctx_pass

                params_dict[clusterid]['interface_ip_list'] = interface_ip_list
                params_dict[clusterid]['interface_ipv6_list'] = interface_ipv6_list
                # params_dict[clusterid]['DPPassiveSubnet'] = cft_profile['DPSubnet']
                params_dict[clusterid]['SecondaryIntfSet'] = cft_profile['PrimaryIntfSet']
                if "HAActiveSubnet" in list(cft_profile.keys()):
                    params_dict[clusterid]['HAPassiveSubnet'] = cft_profile['HAActiveSubnet']
                params_dict[clusterid]['SecondaryMachineType'] = cft_profile['InstType']
                if ha_state == 1:
                    params_dict[clusterid]['SecAllowedAsTarget'] = False
                else:
                    params_dict[clusterid]['SecAllowedAsTarget'] = True
                params_dict[clusterid]['sase_fabric'] = cft_profile.get('sase_fabric', 0)
                params_dict[clusterid]['commit_validate'] = cft_profile.get('commit_validate', 0)

                # Use SencondaryCapacityType as new Infra indicator
                if ('SecondaryCapacityType' in list(cft_profile.keys())):
                    # New instance with new template format
                    params_dict[clusterid]['SecondaryCapacityType'] = cft_profile['SecondaryCapacityType']
                else:
                    # Old instances
                    params_dict[clusterid]['SecondaryCapacityType'] = "default"

                sec_dpdk_qcount = cft_profile.get('SecondaryDpdkQcount', 0)
                if sec_dpdk_qcount > 0:
                    params_dict[clusterid]['SecondaryDpdkQcount'] = sec_dpdk_qcount
            else:
                params_dict[clusterid] = {}
                params_dict[clusterid]['CustId'] = cft_profile['CustId']
                params_dict[clusterid]['MachineType'] = cft_profile['InstType']
                params_dict[clusterid]['AcctId'] = cft_profile['AcctId']
                params_dict[clusterid]['UserData'] = cft_profile['UserData']
                params_dict[clusterid]['PrimaryInstanceName'] = cft_profile['InstanceName']
                params_dict[clusterid]['PrimaryId'] = cft_profile['InstanceId']
                params_dict[clusterid]['SerialNo'] = cft_profile['SerialNo']
                params_dict[clusterid]['ImageProject'] = cft_profile['ImageProject']
                if 'SharedMgmtVPCProj' in list(cft_profile.keys()):
                    params_dict[clusterid]['SharedMgmtVPCProj'] = cft_profile['SharedMgmtVPCProj']
                params_dict[clusterid]['PrimaryImageName'] = cft_profile['ImageName']
                params_dict[clusterid]['svc_acct'] = cft_profile['svc_acct']
                params_dict[clusterid]['clean_ip_tag'] = cft_profile['clean_ip_tag']
                params_dict[clusterid]['NodeType'] = im_node_type
                params_dict[clusterid]['Name'] = name
                params_dict[clusterid]['PublicIp'] = public_ip
                params_dict[clusterid]['IsBehindNlb'] = is_behind_nlb
                params_dict[clusterid]['IsUpgradeCreation'] = upgrade_creation

                params_dict[clusterid]['ContactLabel'] = cm.get_param("contact_label")

                # Include "enable_tls_term_on_ep" and "enable_rn_to_ep_ctx_pass"
                # from EP tenant_info entry into the return dictionary.
                params_dict[clusterid]['enable_tls_term_on_ep'] = enable_tls_term_on_ep
                params_dict[clusterid]['enable_rn_to_ep_ctx_pass'] = enable_rn_to_ep_ctx_pass

                params_dict[clusterid]['hasExternalIPv6'] = cft_profile.get('hasExternalIPv6', False)
                if ha_state == 1:
                    params_dict[clusterid]['PriAllowedAsTarget'] = False
                else:
                    params_dict[clusterid]['PriAllowedAsTarget'] = True
                params_dict[clusterid]['sase_fabric'] = cft_profile.get('sase_fabric', 0)
                params_dict[clusterid]['commit_validate'] = cft_profile.get('commit_validate', 0)
                params_dict[clusterid]['is_using_sp_interconnect'] = cft_profile.get('is_using_sp_interconnect', False)
                params_dict[clusterid]['min_cpu_platform'] = cft_profile.get('MinCpuPlatform', '')
                if 'ZonesList' in cft_profile:
                    params_dict[clusterid]['ZonesList'] = cft_profile['ZonesList']

                if not is_inbound_access_list_none(inbound_access_ip_cfg,
                                                   dbh.logger):
                    inbound_access_ip_list_str = uncompress_cfg(inbound_access_ip_cfg)
                    inbound_access_ip_list = json.loads(inbound_access_ip_list_str)
                    _inbound_access_ip_list = []
                    _allowed_fw_rules = defaultdict(lambda: set())
                    for ip_item in inbound_access_ip_list:
                        if ip_item['public_ip']:
                            _ip_item = {'public_ip': str(ip_item['public_ip'])}
                            if ip_item['protocol']:
                                _ip_item['protocol'] = []
                                for protocol_item in ip_item['protocol']:
                                    if protocol_item['name'] and protocol_item['port']:
                                        _ip_item['protocol'].append({'name': str(protocol_item['name']), 'port': []})
                                        for port in protocol_item['port']:
                                            _ip_item['protocol'][-1]['port'].append(str(port))
                                            _allowed_fw_rules[protocol_item['name']].add(port)
                            _inbound_access_ip_list.append(_ip_item)

                    inbound_access_ip_list_str = str(_inbound_access_ip_list)
                    _allowed_fw_rules = dict(
                        {str(key): [str(x) for x in list(_allowed_fw_rules[key])] for key in _allowed_fw_rules})

                else:
                    inbound_access_ip_list_str = None
                    _inbound_access_ip_list = None
                    _allowed_fw_rules = None

                if im_node_type in (NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY) and cft_profile.get('nlb_ingress_ip', None) is not None:
                    params_dict[clusterid]['nlb_ingress_ip'] = cft_profile['nlb_ingress_ip']

                # If this is a GW instance that is behind NLB, or
                # has a NAT GW in front, use "interface_ip_list".
                # in place of "egress_ip_list" for the parameter dictionary.
                if (im_node_type == NODE_TYPE_GP_GATEWAY and
                    (is_behind_nlb or has_nat_instance)):
                    dbh.logger.info("Setting egress IP list in params dict as "
                        "this is a GPGW behind an NLB or in front of NAT")
                    params_dict[clusterid]['egress_ip_list'] = interface_ip_list
                else:
                    dbh.logger.info("Setting interface IP list in params dict as egress IP list")
                    params_dict[clusterid]['egress_ip_list'] = egress_ip_list

                params_dict[clusterid]['perform_ingress_ip_reduction'] = cft_profile.get('perform_ingress_ip_reduction', 0)

                # Set the egress IPv6 list subnet:
                params_dict[clusterid]['egress_ipv6_list_subnet'] = egress_ipv6_list_subnet

                params_dict[clusterid]['l3fwdrules'] = int(Fwdruletypes.ICMP_TCP_UDP_FWD)
                if ((cm.get_param("fwdrulesall") == "MU" and im_node_type == NODE_TYPE_GP_GATEWAY) or
                    (cm.get_param("fwdrulesall") == "RN" and im_node_type == NODE_TYPE_REMOTE_NET)):
                    params_dict[clusterid]['l3fwdrules'] = int(Fwdruletypes.ICMP_TCP_UDP_L3_FWD)
                elif im_node_type == NODE_TYPE_GP_GATEWAY:
                    params_dict[clusterid]['l3fwdrules'] = int(Fwdruletypes.L3_FWD) if cm.get_param("l3fwdrules_mu") \
                                                                            else int(Fwdruletypes.ICMP_TCP_UDP_FWD)
                elif im_node_type == NODE_TYPE_REMOTE_NET:
                    params_dict[clusterid]['l3fwdrules'] = int(Fwdruletypes.L3_FWD) if cm.get_param("l3fwdrules_rn")\
                                                                            else int(Fwdruletypes.ICMP_TCP_UDP_FWD)

                params_dict[clusterid]['inbound_access_ip_list'] = inbound_access_ip_list_str
                params_dict[clusterid]['allowed_fw_rules'] = _allowed_fw_rules

                params_dict[clusterid]['static_ip'] = cft_profile['static_ip']


                params_dict[clusterid]['PrimaryZone'] = cft_profile['Zone']
                params_dict[clusterid]['PrimaryIntfSet'] = cft_profile['PrimaryIntfSet']
                params_dict[clusterid]['RegionName'] = cft_profile['RegionName']
                params_dict[clusterid]['RegionId'] = region_idx

                # Subnet info:-
                params_dict[clusterid]['MgmtSubnet'] = cft_profile['MgmtSubnet']
                params_dict[clusterid]['DPSubnet'] = cft_profile['DPSubnet']
                params_dict[clusterid]['HASubnet'] = cft_profile['HASubnet']
                if im_node_type in (NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY) and cft_profile.get('ExtrnSubnet', None) is not None:
                    params_dict[clusterid]['ExtrnSubnet'] = cft_profile['ExtrnSubnet']

                params_dict[clusterid]['MgmtNetwork'] = cft_profile['MgmtNetwork']
                params_dict[clusterid]['DPNetwork'] = cft_profile['DPNetwork']
                params_dict[clusterid]['HANetwork'] = cft_profile['HANetwork']
                if im_node_type in (NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY) and cft_profile.get('ExtrnNetwork', None) is not None:
                    params_dict[clusterid]['ExtrnNetwork'] = cft_profile['ExtrnNetwork']

                params_dict[clusterid]['MgmtInterfaceName'] = cft_profile['MgmtInterfaceName']
                params_dict[clusterid]['DPInterfaceName'] = cft_profile['DPInterfaceName']
                params_dict[clusterid]['HAInterfaceName'] = cft_profile['HAInterfaceName']
                if im_node_type in (NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY) and cft_profile.get('ExtrnInterfaceName', None) is not None:
                    params_dict[clusterid]['ExtrnInterfaceName'] = cft_profile['ExtrnInterfaceName']

                params_dict[clusterid]['MgmtHasExternalIP'] = cft_profile['MgmtHasExternalIP']
                params_dict[clusterid]['DPHasExternalIP'] = cft_profile['DPHasExternalIP']
                params_dict[clusterid]['HAHasExternalIP'] = cft_profile['HAHasExternalIP']
                params_dict[clusterid]['DPInterfaceName'] = 'CLIENT_IP'  # cft_profile['DPInterfaceName']
                if im_node_type in (NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY) and cft_profile.get('ExtrnHasExternalIP', None) is not None:
                    params_dict[clusterid]['ExtrnHasExternalIP'] = cft_profile['ExtrnHasExternalIP']

                if im_node_type == NODE_TYPE_SERVICE_CONN and cft_profile.get('colo_interface_support', None) is not None:
                    params_dict[clusterid]['colo_interface_support'] = cft_profile['colo_interface_support']
                    params_dict[clusterid]['COLOSubnet'] = cft_profile['COLOSubnet']
                    params_dict[clusterid]['COLONetwork'] = cft_profile['COLONetwork']
                    params_dict[clusterid]['COLOInterfaceName'] = cft_profile['COLOInterfaceName']
                    params_dict[clusterid]['COLOHasExternalIP'] = cft_profile['COLOHasExternalIP']

                if cft_profile.get('is_datapath_on_shared_vpc', None) is not None \
                    and (cft_profile.get('DPProjectOverride', None) is not None) \
                    and (cft_profile['DPProjectOverride'] != "") :
                    #Move this interface to common host project if datapath is on shared VPC
                    params_dict[clusterid]['DPProjectOverride'] = cft_profile['DPProjectOverride']

                # if params_dict[clusterid]['sase_fabric'] != 0:
                #     params_dict[clusterid]['SASESubnet'] = cft_profile['SASESubnet']
                #     params_dict[clusterid]['SASENetwork'] = cft_profile['SASENetwork']
                #     params_dict[clusterid]['SASEInterfaceName'] = cft_profile['SASEInterfaceName']
                #     params_dict[clusterid]['SASEHasExternalIP'] = cft_profile['SASEHasExternalIP']
                #     params_dict[clusterid]['SASEHostProject'] = cft_profile['SASEHostProject']

                # Use PrimaryCapacityType as new Infra indicator
                if ('PrimaryCapacityType' in list(cft_profile.keys())):
                    # New instance with new template format
                    params_dict[clusterid]['PrimaryCapacityType'] = cft_profile['PrimaryCapacityType']
                else:
                    # Old instances
                    params_dict[clusterid]['PrimaryCapacityType'] = "default"

                pri_dpdk_qcount = cft_profile.get('PrimaryDpdkQcount', 0)
                if pri_dpdk_qcount > 0:
                    params_dict[clusterid]['PrimaryDpdkQcount'] = pri_dpdk_qcount
                if no_passive_instance == True:
                    params_dict[clusterid]['_no_passive_instance'] = True

            # Possible scenario where the service nic is only present in secondary and not in primary device,
            # during data plane upgrade of HA pair, one by one(passive first)
            # So add the service interface details if either of the salt profiles supports service_nic
            if cft_profile.get('is_service_nic_supported', None) is not None \
                and cft_profile['is_service_nic_supported'] == True:
                params_dict[clusterid]['serviceInterfaceName'] = cft_profile['serviceInterfaceName']
                params_dict[clusterid]['serviceSubnet'] = cft_profile['serviceSubnet']
                params_dict[clusterid]['serviceNetwork'] = cft_profile['serviceNetwork']
                params_dict[clusterid]['serviceProjectOverride'] = cft_profile['serviceProjectOverride']
                params_dict[clusterid]['serviceHasExternalIP'] = cft_profile['serviceHasExternalIP']

        for clusterid in clusters_to_be_deleted:
            if clusterid in params_dict:
                dbh.logger.info("Removing info about cluster id %s from params dict." % str(clusterid))
                params_dict.pop(clusterid)

    except Exception as E:
        if dbh:
            dbh.logger.error("get_gcp_cust_region_instance_params: %s Exception %s occured. locals: %s" %
                             (str(traceback.format_exc()), str(E.args), str(locals())))
        params_dict = None

    finally:
        return params_dict


def get_gcp_cp_cust_region_instance_params(dbh, custid, region_idx, is_clean_pipe):
    ret = None
    params_dict = None
    try:
        sql = ("SELECT distinct instance_master.name,instance_master.clusterid,"
               " instance_master.salt_profile"
               " FROM instance_master  WHERE "
               "custid = %s and compute_region_idx=%s ORDER BY id ASC")
        params = (custid, region_idx)
        params_dict = {}
        # We can also use set session TRANSACTION ISOLATION LEVEL READ COMMITTED to get proper results
        dbh.conn.close()
        cursor = None
        try:
            cursor = dbh.get_cursor()
            cursor.execute(sql, params)
            ret = cursor.fetchall()
            dbh.cursorclose(cursor)
        except Exception as ex:
            error = (
                "Unable to query instances for customer id %s region %s: %s" % (str(custid), str(region_idx), str(ex)))
            dbh.logger.error(error)
            dbh.cursorclose(cursor)
            return None
        if not ret:
            return params_dict
        for name, clusterid, salt_profile in ret:
            try:
                if salt_profile == None:
                    continue
                cft_profile = json.loads(salt_profile)
            except Exception as ex:
                # Its better to not touch the stack if database has something wrong, than
                # Update stack with wrong values
                error = ("Unable to convert cft parameters from Json to dict\n%s" % str(salt_profile))
                dbh.logger.error(error)
                raise Exception(error)

            if cft_profile['is_clean_pipe'] != 1:
                continue

            params_dict[clusterid] = {}
            params_dict[clusterid]['CustId'] = cft_profile['CustId']
            params_dict[clusterid]['MachineType'] = cft_profile['InstType']
            params_dict[clusterid]['AcctId'] = cft_profile['AcctId']
            params_dict[clusterid]['UserData'] = cft_profile['UserData']
            params_dict[clusterid]['PrimaryInstanceName'] = cft_profile['InstanceName']
            params_dict[clusterid]['PrimaryId'] = cft_profile['InstanceId']
            params_dict[clusterid]['SerialNo'] = cft_profile['SerialNo']
            params_dict[clusterid]['ImageProject'] = cft_profile['ImageProject']
            if 'SharedMgmtVPCProj' in list(cft_profile.keys()):
                params_dict[clusterid]['SharedMgmtVPCProj'] = cft_profile['SharedMgmtVPCProj']
            params_dict[clusterid]['ImageName'] = cft_profile['ImageName']
            params_dict[clusterid]['svc_acct'] = cft_profile['svc_acct']
            params_dict[clusterid]['PrimaryZone'] = cft_profile['Zone']
            params_dict[clusterid]['PrimaryIntfSet'] = cft_profile['PrimaryIntfSet']
            params_dict[clusterid]['RegionName'] = cft_profile['RegionName']
            params_dict[clusterid]['ZonesList'] = cft_profile['ZonesList']
            # We should have copied over the assigned_zone
            params_dict[clusterid]['Zone'] = cft_profile['Zone']
            params_dict[clusterid]['sessionAffinity'] = cft_profile['sessionAffinity']
            params_dict[clusterid]['static_ip'] = cft_profile['static_ip']
            params_dict[clusterid]['clean_ip_tag'] = cft_profile['clean_ip_tag']

            # Subnet info:-
            params_dict[clusterid]['MgmtSubnet'] = cft_profile['MgmtSubnet']
            params_dict[clusterid]['TrustSubnet'] = cft_profile['TrustSubnet']
            params_dict[clusterid]['UnTrustSubnet'] = cft_profile['UnTrustSubnet']

            params_dict[clusterid]['MgmtNetwork'] = cft_profile['MgmtNetwork']
            params_dict[clusterid]['TrustNetwork'] = cft_profile['TrustNetwork']
            params_dict[clusterid]['UnTrustNetwork'] = cft_profile['UnTrustNetwork']

            params_dict[clusterid]['MgmtInterfaceName'] = cft_profile['MgmtInterfaceName']
            params_dict[clusterid]['TrustInterfaceName'] = cft_profile['TrustInterfaceName']
            params_dict[clusterid]['UnTrustInterfaceName'] = cft_profile['UnTrustInterfaceName']

            params_dict[clusterid]['MgmtHasExternalIP'] = cft_profile['MgmtHasExternalIP']
            params_dict[clusterid]['TrustHasExternalIP'] = cft_profile['TrustHasExternalIP']
            params_dict[clusterid]['UnTrustHasExternalIP'] = cft_profile['UnTrustHasExternalIP']
            params_dict[clusterid]['AvailabilityDomain'] = cft_profile['AvailabilityDomain']

            # Use PrimaryCapacityType as new Infra indicator
            if ('PrimaryCapacityType' in list(cft_profile.keys())):
                # New instance with new template format
                params_dict[clusterid]['PrimaryCapacityType'] = cft_profile['PrimaryCapacityType']
            else:
                # Old instances
                params_dict[clusterid]['PrimaryCapacityType'] = "default"

    except Exception as E:
        if dbh:
            dbh.logger.error("get_gcp_cust_region_instance_params: Exception %s occured. locals: %s" %
                             (str(E.args), str(locals())))
        params_dict = None

    finally:
        return params_dict


# Function to check if an NLB needs to be present/spun up for the provided
# (customer, region) combination.
# Check if there is any GW instance in this region with the
# "is_instance_behind_nlb" flag set to TRUE. If so, return TRUE
# since this (customer, region) needs to have the NLB present
# (either brought up newly, or keep the existing NLB as-is).
# Else, return FALSE.
def is_nlb_bring_up(dbh, custid, region_id):
    dbh.logger.info("Checking if NLB bring up is needed for tenant: %s "
                    "and region: %s" % (custid, region_id))

    ret = False
    try:
        # Get the "is_instance_behind_nlb" flag for all GW instances for the
        # given (customer, region) combination.
        # Check if any of the GW instances has the "is_behind_nlb" flag set.
        # If so, this is the case where an NLB has already been spun up or
        # a new NLB needs to be spun up in the region. Return TRUE.
        # Else, this is the first instance in this region for this customer.
        # No need to spin up an NLB yet. Return FALSE.
        sql = ("SELECT COUNT(*) FROM instance_master "
               "WHERE custid = %s AND compute_region_idx = %s AND "
               "node_type = %s AND is_instance_behind_nlb = 1")
        params = (custid, region_id, NODE_TYPE_GP_GATEWAY)
        dbh.conn.close()
        try:
            cursor = dbh.get_cursor()
            dbh.logger.info("SQL: %s; params: %s" % (sql, str(params)))
            cursor.execute(sql, params)
            db_ret = cursor.fetchall()
            dbh.cursorclose(cursor)

            dbh.logger.info("Return from RDS: %s" % str(db_ret))
            no_of_instances = db_ret[0][0]
            dbh.logger.info("No. of instances: %s" % no_of_instances)
            if no_of_instances > 0:
                dbh.logger.info("There are GW instances with "
                                "is_instance_behind_nlb set; return TRUE")
                ret = True
            else:
                dbh.logger.info("No GW instances with "
                                "is_instance_behind_nlb set; return FALSE")

        except Exception as E:
            dbh.logger.info("Failed to get instances from RDS; "
                            "Exception %s occured. locals: %s" %
                             (str(E.args), str(locals())))
            return ret

    except Exception as E:
        dbh.logger.error("is_nlb_bring_up: Exception %s occured. locals: %s" %
                             (str(E.args), str(locals())))

    finally:
        return ret

def is_colo_ilb_bring_up_required(dbh, custid, region_id):
    dbh.logger.info(f"Checking if Colo ILB bring up is needed for tenant: {custid} and region: {region_id}")

    colo_ilb_names = []

    ret = False
    try:
        # Check if any of the Colo-SC instances has the "is_behind_nlb" flag set.
        # If so, this is the case where an ILB has already been spun up or
        # a new ILB needs to be spun up in the region. Return TRUE.
        sql = ("select Distinct(JSON_VALUE(salt_profile,'$.colo_ilb_name')) FROM instance_master "
               "WHERE custid = %s AND compute_region_idx = %s AND "
               "node_type = %s AND is_instance_behind_nlb = 1")
        params = (custid, region_id, NODE_TYPE_SERVICE_CONN)
        dbh.conn.close()
        try:
            cursor = dbh.get_cursor()
            dbh.logger.info("SQL: %s; params: %s" % (sql, str(params)))
            cursor.execute(sql, params)
            db_ret = cursor.fetchall()
            dbh.cursorclose(cursor)
            dbh.logger.info("Return from RDS: %s" % str(db_ret))
            if db_ret is not None and len(db_ret)> 0:
                for row in db_ret:
                    if row[0] is None:
                        continue
                    #store the ilb name in dict
                    colo_ilb_names.append(row[0])
        except Exception as E:
            dbh.logger.info("is_colo_ilb_bring_up_required: Failed to get colo-ILBS from RDS; "
                            "Exception %s occured. locals: %s" %
                            (str(E.args), str(locals())))
            return False, []
    except Exception as E:
        dbh.logger.error("is_colo_ilb_bring_up_required: Exception %s occured. locals: %s" %
                         (str(E.args), str(locals())))
        return False, []
    finally:
        dbh.logger.info("is_colo_ilb_bring_up_required: ret %s" % (str(colo_ilb_names)))
        return len(colo_ilb_names) > 0, colo_ilb_names

def is_rn_ilb_bring_up(dbh, custid, region_id):
    dbh.logger.info(f"Checking if RN ILB bring up is needed for tenant: {custid} and region: {region_id}")

    ret = False
    try:
        # Get the "is_instance_behind_nlb" flag for all RN onramp instances for the
        # given (customer, region) combination.
        # Check if any of the RN instances has the "is_behind_nlb" flag set.
        # If so, this is the case where an ILB has already been spun up or
        # a new ILB needs to be spun up in the region. Return TRUE.
        # Else, this is the first instance in this region for this customer.
        # No need to spin up an NLB yet. Return FALSE.
        sql = ("SELECT COUNT(*) FROM instance_master "
               "WHERE custid = %s AND compute_region_idx = %s AND "
               "node_type = %s AND is_instance_behind_nlb = 1 AND is_interconnect_onramp = 1")
        params = (custid, region_id, NODE_TYPE_REMOTE_NET)
        dbh.conn.close()
        try:
            cursor = dbh.get_cursor()
            dbh.logger.info("SQL: %s; params: %s" % (sql, str(params)))
            cursor.execute(sql, params)
            db_ret = cursor.fetchall()
            dbh.cursorclose(cursor)

            dbh.logger.info("Return from RDS: %s" % str(db_ret))
            no_of_instances = db_ret[0][0]
            dbh.logger.info("No. of instances: %s" % no_of_instances)
            if no_of_instances > 0:
                dbh.logger.info("There are RN instances with "
                                "is_instance_behind_nlb set; return TRUE")
                ret = True
            else:
                dbh.logger.info("No RN instances with "
                                "is_instance_behind_nlb set; return FALSE")

        except Exception as E:
            dbh.logger.info("Failed to get instances from RDS; "
                            "Exception %s occured. locals: %s" %
                             (str(E.args), str(locals())))
            return ret

    except Exception as E:
        dbh.logger.error("is_nlb_bring_up: Exception %s occured. locals: %s" %
                             (str(E.args), str(locals())))

    finally:
        return ret


# Function to return the NLB config parameters for the passed
# (custid, region) combination.
def get_gcp_cust_region_nlb_params(dbh, custid, region):
    ret = None
    cm_ret = None
    params_dict = None
    try:
        sql = ("SELECT forwarding_rule_protocol, backend_service_protocol, "
               "session_affinity, conn_persistence_on_unhealthy_backends, "
               "health_check_protocol, health_check_port, "
               "health_check_interval, health_check_timeout, "
               "health_check_unhealthy_threshold, "
               "health_check_healthy_threshold, is_strong_session_affinity_supported, idle_timeout_sec FROM "
               "network_load_balancer_config WHERE "
               "custid in (0,%s) and region_id in (0,%s) and cloud_provider='gcp' ORDER BY custid DESC, region_id DESC LIMIT 1")
        params = (custid, region)
        params_dict = {}
        cm_sql = ("SELECT is_ngpa_protocol_enabled from cust_master WHERE id=%s")
        cm_params = (custid,)
        # We can also use set session TRANSACTION ISOLATION LEVEL READ
        # COMMITTED to get proper results
        dbh.conn.close()
        cursor = None
        try:
            cursor = dbh.get_cursor()
            cursor.execute(sql, params)
            ret = cursor.fetchall()
            cursor.execute(cm_sql, cm_params)
            cm_ret = cursor.fetchone()
            dbh.cursorclose(cursor)
        except Exception as ex:
            error = (
                "Unable to query RDS for customer id %s region %s: %s" %
                (str(custid), str(region), str(ex)))
            dbh.logger.error(error)
            dbh.cursorclose(cursor)
            return None
        # If there is an entry in RDS, use it. Ese, use default values.
        if ret:
            forwarding_rule_protocol, backend_service_protocol, \
            session_affinity, conn_pers_on_unhealthy_backends, \
            health_check_protocol, health_check_port, \
            health_check_interval, health_check_timeout, \
            health_check_unhealthy_threshold, \
            health_check_healthy_threshold, is_strong_session_affinity_supported, idle_timeout_sec = ret[0]
        else:
            forwarding_rule_protocol = DEFAULT_FW_RULE_PROTOCOL
            backend_service_protocol = DEFAULT_BACKEND_SERV_PROTOCOL
            session_affinity = DEFAULT_SESSION_AFFINITY
            conn_pers_on_unhealthy_backends = \
                DEFAULT_CONN_PERS_ON_UNHEALTHY_BACKENDS
            health_check_protocol = DEFAULT_HC_PROTOCOL
            health_check_port = DEFAULT_HC_PORT
            health_check_interval = DEFAULT_HC_INTERVAL
            health_check_timeout = DEFAULT_HC_TIMEOUT
            health_check_unhealthy_threshold = DEFAULT_HC_UNHEALTHY_THRESHOLD
            health_check_healthy_threshold = DEFAULT_HC_HEALTHY_THRESHOLD
            is_strong_session_affinity_supported = DEFAULT_IS_STRONG_SESSION_AFFINITY_SUPPORTED
            idle_timeout_sec = 600
            if cm_ret:
                dbh.logger.info(f"NGPA protocol for customer: {custid} is {cm_ret}")
                if cm_ret[0] and cm_ret[0] == 1:
                    # Default session affinity is set as 5 tuple always
                    # Always set the idle_timeout_sec for NLB as 1800 seconds
                    idle_timeout_sec = DEFAULT_NGPA_NLB_IDLE_TIMEOUT

        params_dict = {}
        params_dict["ForwardingRuleProtocol"] = \
                forwarding_rule_protocol_dict[forwarding_rule_protocol]
        params_dict["BackendServiceProtocol"] = \
                backend_service_protocol_dict[backend_service_protocol]
        params_dict["SessionAffinity"] = \
                session_affinity_dict[session_affinity]
        params_dict["ConnectionPersistenceOnUnhealthyBackends"] = \
                conn_pers_on_unhealthy_backends_dict[
                    conn_pers_on_unhealthy_backends]
        params_dict["HealthCheckProtocol"] = \
                health_check_protocol_dict[health_check_protocol]
        params_dict["HealthCheckPort"] = health_check_port
        params_dict["HealthCheckInterval"] = health_check_interval
        params_dict["HealthCheckTimeout"] = health_check_timeout
        params_dict["HealthCheckUnhealthyThreshold"] = \
                health_check_unhealthy_threshold
        params_dict["HealthCheckHealthyThreshold"] = \
                health_check_healthy_threshold
        params_dict["EnableStrongAffinity"] = is_strong_session_affinity_supported
        params_dict["idleTimeoutSec"] = idle_timeout_sec


    except Exception as E:
        if dbh:
            dbh.logger.error("get_gcp_cust_region_nlb_params: Exception %s "
                             "occured. locals: %s" %
                             (str(E.args), str(locals())))
        params_dict = None

    finally:
        return params_dict


def get_num_of_ips_by_instance_type(dbh, my_inst, ingress_ip_reduction, should_consider_gw_ips : bool = False):
    """
    Determine how many IP's are required based on the instance type, currently handles only NLB and NAT
    should_consider_gw_ips currently is set to true only in interconnect NAT nodes and SP transport type.
    :param dbh:
    :param my_inst:
    :param ingress_ip_reduction:
    :param should_consider_gw_ips: bool should we take into consideration the GW's behind NLB aswell
    :return:
    """
    try:
        # E.g. Lets consider we have 2 gateways. The # of reserved IP addresses are 2 and # of active IP addresses are 2.
        # When two NAT instances are coming, the # of reserved IP addresses are 2 and # of active IP addresses become 4
        # When NLB instance is coming, the # of reserved IP addresses are 2 and # of active IP addresses become 5
        # In total, we have 7 IP in this edge region, 5 active (2 for standalone GW, 2 for NAT, and 1 for NLB),
        # and 2 reserved for future usage
        if my_inst.get_param("node_type") == NODE_TYPE_NLB_INSTANCE:
            num_of_ips = 1
            return num_of_ips

        if my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE:
            result, instances = find_instances_by_custid_and_region_id(dbh, my_inst.get_param("custid"),
                                                                       my_inst.get_param("compute_region_idx"),
                                                                       NODE_TYPE_NAT_INSTANCE)
            if not result:
                err_msg = ("Failed to get the NAT gateways for custid %s and region %s" % (
                str(my_inst.get_param("custid")),
                str(my_inst.get_param(
                    "compute_region_idx"))))
                dbh.logger.error(err_msg)
                raise Exception(err_msg)
            if len(instances) < 1:
                dbh.logger.info(
                    "There are no NAT instances for custid: %s in region name: %s" % (str(my_inst.get_param("custid")),
                                                                                      str(my_inst.get_param(
                                                                                          "compute_region_name"))))

            result, nr_instances, nr_nat_gateways_needed = find_nr_nat_instances_by_custid_and_region_id(
                dbh, my_inst.get_param("custid"), my_inst.get_param("compute_region_idx"))

            standalone_result, standalone_instances = find_standalone_instances_by_custid_and_node_type_and_region_id(dbh, 
                                                                                                                      my_inst.get_param("custid"), 
                                                                                                                      NODE_TYPE_GP_GATEWAY,
                                                                                                                      my_inst.get_param("compute_region_idx"))

            # Since the MU and NAT share the same node_type during public_ip_pool allocation we need to account for these ip's
            # In case of the SP Interconnect NAT nodes, since the MU uses PUPI ip's,  unlike normal case where MU Public ip's are not used
            # We need to account the gw IP's as well.
            # In interconnect nodes itself, in non SP transport we use GCP ip's so we need not account for MU IP's
            num_gw_instances = 0
            if should_consider_gw_ips:
                dbh.logger.info(f"should_consider_gw_ip is true so considering gw ip's aswell.")
                gw_result, gw_instances = find_instances_by_custid_and_node_type_and_region_id(dbh,
                                                                                               my_inst.get_param("custid"),
                                                                                               NODE_TYPE_GP_GATEWAY,
                                                                                               my_inst.get_param("compute_region_idx"))
                if gw_result:
                    num_gw_instances = len(gw_instances)

            num_standalone = 1
            if standalone_result:
                num_standalone = len(standalone_instances)
            else:
                num_standalone = nr_instances

            is_nat_dummy_upgrade_ongoing = is_nat_dummy_upgrade_ongoing_in_region(dbh,my_inst.get_param("custid"),
                                                                                      my_inst.get_param("compute_region_idx"),
                                                                                      for_ip_count=True)
            #Reserve additional +1 IP for scale 
            if is_nat_dummy_upgrade_ongoing:
                #When NAT dummy upgrade is ongoing and there are more than 1 standalone, 
                #then this is a brownfield multi GW migration case,
                #we do not need to reserve any extra IPs, as the NATs will eventually re-use freed up IPs from the GW
                num_of_ips = len(instances) + num_standalone + num_gw_instances
            else:
                num_of_ips = len(instances) + num_standalone + num_gw_instances + 1
            
            return num_of_ips

    except Exception as E:
        dbh.logger.error("get_num_of_ips_by_instance_type: Exception %s "
                         "occured. locals: %s" %
                         (str(E.args), str(locals())))
        if my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE:
            num_of_ips = DEFAULT_NR_NAT_GATEWAYS + 1
        else:
            num_of_ips = 0
        return num_of_ips


def is_global_ip_needed(dbh, enable_tls_term_on_ep, enable_global_nlb, custid, region, node_type, alt_node_type):
    is_global_ip_req = enable_tls_term_on_ep or enable_global_nlb
    try:
        dbh.logger.info(f"Checking if eproxy_outside_panos is enabled for custid {custid} and region {region}")
        proxy_bringup = swgproxy_bringup(dbh)
        is_eproxy_outside_panos = proxy_bringup.gpcs_envoy_outside_panos_val(custid,
                                                                             region,
                                                                             PROVIDER_GCP, node_type,
                                                                             alt_node_type=alt_node_type)
        is_global_ip_req = is_eproxy_outside_panos and (enable_tls_term_on_ep or enable_global_nlb)
        dbh.logger.info(f"eproxy_outside_panos value {is_eproxy_outside_panos} is set for custid {custid} and region {region}")
    except Exception as e:
        dbh.logger.info(f"Exception while trying to fetch eproxy_outside_panos for custid {custid} and region {region}: {str(e.args)}")
    finally:
        dbh.logger.info(f"is_global_ip_enabled for custid {custid} and region {region}: {is_global_ip_req}")
        return is_global_ip_req

import time
import logging, threading
import logging.handlers

'''
class dbh:
    def __init__(self, logger = None):
        self.logger = logger

class gcp_cp_instance_info_handler:
    def __init__(self, event = None):
        self.inst_prof = {}
        self.inst_prof = {'instType': '2', 'image_project': 'sample-proj', 'is_clean_pipe': 1, 'AcctId': '245150', 'userdata': 'instance_name=rmusunuri--us-west1-12345,custid=245150,lambdaprefix=pmangam-245150,custid_int=24,gp_domain=pancloud.dev,route53acct=a447084568087,bucket-name=panrepo-us-west-2-dev2,panrepo_bucket_name=panrepo-us-west-2-dev2,aws-access-key-id=None,aws-secret-access-key=None, mgmt-interface-swap=enable', 'TrustNetwork': 'gpcs-cp-vpc-trust-245150', 'zone': 'us-west1-a', 'TrustSubnet': 'subnet-trust-us-west1-245150', 'TrustInterfaceName': 'nic-trust', 'UnTrustHasExternalIP': True, 'MgmtHasExternalIP': False, 'PrimaryId': None, 'SerialNo': 'Dummy_serial_no', 'UnTrustNetwork': 'gpcs-cp-vpc-untrust-245150', 'image_name': 'panos-8.1', 'TrustHasExternalIP': False, 'UnTrustInterfaceName': 'nic-untrust', 'svc_acct': 'sample-account', 'UnTrustSubnet': 'subnet-untrust-us-west1-245150', 'MgmtNetwork': 'gpcs-cp-vpc-mgmt-245150', 'MgmtSubnet': 'subnet-mgmt-us-west1-245150', 'MgmtInterfaceName': 'nic-mgmt'}
    def get_prof(self):
        return self.inst_prof
'''


def main():
    from libs.db.dbhandle import DbHandle
    filename = "gcp_instance_param_mgmt.log"
    FORMAT = "[%(filename)s:%(lineno)s - %(funcName)20s() ] %(message)s"
    logging.basicConfig(format=FORMAT)
    handler = logging.handlers.RotatingFileHandler(filename, maxBytes=********, backupCount=1)
    logger = logging.getLogger(threading.currentThread().getName())
    logger.setLevel(logging.DEBUG)
    logger.addHandler(handler)
    db_h = DbHandle(logger)

    gcp_ip_mgmt = ipv4_mgmt.GCPIPHandler(db_h, str(3096), str(245139))
    ip_list = gcp_ip_mgmt.allocate_public_ip_for_customer(None,
                                                          3096,
                                                          False)

    '''
    instance_entry_dict = {}
    instance_entry_dict['cust_id'] = 24
    instance_entry_dict['acct_id'] = 245150
    instance_entry_dict['serial_no'] = 123456789
    instance_entry_dict['instance_name'] ='rmusunuri_us-west1_12345'
    instance_entry_dict['instance_id'] = 1000
    instance_entry_dict['region'] = 'us-west1'
    instance_entry_dict['region_name'] = 'us-west1'
    instance_entry_dict['gpcs_instance_size'] = 10
    instance_entry_dict['is_clean_pipe'] = 1
    instance_entry_dict['version'] = 10
    get_gcp_salt_profile_for_instance_entry(db_h, instance_entry_dict, 0, 1)
    '''


#if __name__ == '__main__':
#    main()
