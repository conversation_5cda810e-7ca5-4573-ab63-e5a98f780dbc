import json
import traceback
import oci
import time
from utils.oci_sdk.oci_sdk_api import OCISdkUtility
from libs.common.shared.utils import send_sqs_message
from libs.model.execute_orch_query import execute_orch_query
from libs.cloud_providers.common import utils
from libs.model.instancemodel import InstanceModel
from libs.apis.region_master_api import get_cloud_native_location_name_from_region_id
from libs.cloud_providers.common.exceptions import UnableToAllocatedReservedPublicIpException
from libs.model.orchcfgmodel_v2 import OrchCfgModel_v2 as OrchCfgModel
from libs.common.shared.gcp_utils import gcp_authenticate
from libs.common.shared.apis import gcpPubSub


def is_adem_notification_enabled(dbh, logger):
    """
     Args:
        Check adem probe v2 feature status
        dbh (object): Database handler object
        logger (logging.Logger): Logger object for logging events`

    Returns:
        nothing
    """
    feature_status = 'disabled'
    try:
        sql = ("SELECT value FROM global_settings WHERE setting='adem_probe_v2_orchestrator_feature_state'")
        ret, result = execute_orch_query(dbh, sql, None, "fetchone")
        if ret is False or result is None:
            logger.error("Failed to get adem probe v2 status from RDS")
            return False
        feature_status = str(result[0])
    except Exception as err:
        logger.error(f"Getting feature flag for ADEM-V2 service failed. Error:{err}, Trace: {traceback.format_exc()}")
        pass
    if feature_status == 'disabled':
        logger.info("ADEM PROBE V2 Feature is Disabled. Returning")
        return False
    elif feature_status == 'enabled':
        logger.info("ADEM PROBE V2 feature is enabled")
        return True


def send_pubsub_to_adem_service(dbh, instance_details, logger):
    logger.info(f"Sending payload: {instance_details} to pubsub")
    try:
        global_cfg = OrchCfgModel(dbh)

        cyr_env = global_cfg.fields.get('aws_env')
        instance_details.update({'aws_env': cyr_env})
        project_name = global_cfg.fields.get('adem_project_id')
        adem_pubsub_topic = global_cfg.fields.get('adem_pa_firewall_notifn_pubsub_topic')
        svc_acct = gcp_authenticate(global_cfg.fields, logger)
        if svc_acct is None:
            err_msg = "Missing service account information in orch_cfg"
            logger.error(err_msg)
            return

        pubsub_client = gcpPubSub.PubSubClient(logger=logger, project=project_name, svc_acct=svc_acct)
        pubsub_client.set_topic_path(adem_pubsub_topic)
        logger.info(f"PUBSUB Client: {pubsub_client}")
        err = pubsub_client.publish(instance_details)
        if not err:
            msg = f"Failed to publish data.{instance_details}"
            logger.error(msg)
            return
        logger.info(f"Successfully published payload {instance_details} to pubsub topic {adem_pubsub_topic}")
        return

    except Exception as err:
        logger.error(
            f"Failed to publish instance details to ADEM service. Error: {err}, Trace: {traceback.format_exc()}")
        return None


def notify_adem_service(instance_model_obj, dbh, logger):
    """
    Notify ADEM service about instance details

    Args:
        instance_model_obj (dict): Dictionary containing instance information
        dbh (object): Database handler object
        b_new_instance (bool): Flag indicating if it's a new instance
        logger (logging.Logger): Logger object for logging events

    Returns:
        nothing
    """
    try:
        # Validate input parameters
        if not instance_model_obj or not logger:
            logger.error("Invalid input parameters for ADEM notification")
            return

        logger.debug(f"!!!!!!INSTANCE Obj: {instance_model_obj}")
        # Prepare notification message
        tenant_id = instance_model_obj.get_param('acct_id')
        instance_details = {
            'instance_id': instance_model_obj.get_param('id'),
            'cloud_provider': instance_model_obj.get_param('cloud_provider'),
            'name': instance_model_obj.get_param('name'),
            'custid': instance_model_obj.get_param('custid'),
            'tenant_id': tenant_id,
            #'dns_servers': instance_dict.get_param('dns_servers'),
            'vmid': instance_model_obj.get_param('vmid'),
            #'serial_number': instance_dict.get_param('serial_number'),
            'public_ip': instance_model_obj.get_param('public_ip'),
            'vm_status': instance_model_obj.get_param('vm_status'),
            'node_type': instance_model_obj.get_param('node_type'),
            'compute_region_id': instance_model_obj.get_param('compute_region_idx'),
            'compute_region_name': instance_model_obj.get_param('compute_region_name'),
            'lb_details': instance_model_obj.get_param('lb_details'),
            'interface_details': instance_model_obj.get_param('interface_ip_list'),
            'is_instance_behind_nlb': instance_model_obj.get_param('is_instance_behind_nlb'),
            'workflow': "INSTANCE_CREATE"
        }
        # get serial_number
        sql = ("select serial_number from instance_master "
               "where acct_id=%s" % (tenant_id))
        ret, result = execute_orch_query(dbh, sql, None, "fetchone")
        if ret is False or result is None:
            logger.error(f"Failed to get serial number from instance_master for tenant {tenant_id}")
            return None
        serial_number = str(result[0])
        instance_details['serial_number'] = serial_number

        # get details from cust_master
        sql = ("select super_acct_id from cust_master "
               "where acct_id=%s" % (tenant_id))
        ret, result = execute_orch_query(dbh, sql, None, "fetchone")
        if ret is False or result is None:
            logger.error(f"Failed to get supper_acct_id from cust_master for tenant {tenant_id}")
            return None
        super_tenant_id = int(result[0])
        instance_details['super_tenant_id'] = super_tenant_id

        logger.info(f"Sending ADD notification: {instance_details}")
        send_pubsub_to_adem_service(dbh, instance_details, logger)

    except Exception as E:
        error_str = ("!!! Fatal !!!! notify_adem_service failed. %s "
                     "Exception %s" % (instance_model_obj, str(E.args)))
        logger.error("%s %s" % (error_str, str(traceback.format_exc())))

def delete_notify_adem_service(instance_id, dbh, logger):
    if instance_id is None:
        logger.info("Invalid instance_id for delete notification")
        return
    instance_details = {
        'instance_id': instance_id,
        'workflow': "INSTANCE_DELETE"
    }
    logger.info(f"Sending DELETE notification: {instance_details}")
    send_pubsub_to_adem_service(dbh, instance_details, logger)
