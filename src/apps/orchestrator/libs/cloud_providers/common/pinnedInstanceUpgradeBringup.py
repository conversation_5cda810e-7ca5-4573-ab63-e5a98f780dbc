import traceback
import time
import datetime
from libs.model.instancemodel import InstanceModel
import libs.model.custnodemodel as CN
from libs.common.shared.ngpa_utils import send_ngpa_cust_alert, NGPACustAlertReasons, publish_ngpa_notifications_to_cosmos, check_nat_tunnel_status_for_vmid
from libs.common.shared.pba_utils import is_dhcp_pool_allocation_enabled
from libs.common.shared.sys_utils import *
from libs.model.orchcfgmodel_v2 import OrchCfgModel_v2 as OrchCfgModel
from libs.model.pinnedInstanceUpgradeModel import PinnedInstanceUpgradeModel
from libs.cloud_providers.common.instance_trigger_update import trigger_update_for_instance_changes
from libs.cloud_providers.common.utils import send_sns_msg_for_r53_update
from orchestration_service.core.orchestrator_nat_mgmt import find_nat_configs_by_custid_and_region_id
from ngpa_extern.external_utils import is_ciam_enabled_from_ff
from libs.cloud_providers.common.adem_fw_notify import is_adem_notification_enabled, delete_notify_adem_service

class pinned_instance_upgarde_bringup_processing:
    def __init__(self, dbh, shard_id=None, replica_size=None):
        if dbh == None or dbh.logger == None:
            return
        self.logger = dbh.logger
        self.dbh = dbh
        self.err_msg = None
        self.shard_id = shard_id
        self.replica_size = replica_size

    def pinned_gp_gateway_instance_upgrade_bringup_error_log(self,
                                                             current_pinned_instance_ref):
        try:
            self.logger.error(self.err_msg)
            current_pinned_instance_ref.set_param("bringup_done", -1)
            current_pinned_instance_ref.set_param("bringup_status",
                                                  self.err_msg)
            current_pinned_instance_ref.save_stage3()
            #if this value is True, send alert cust details notification
            if current_pinned_instance_ref.get_param("is_ngpa_auto_migration"):
                custid = current_pinned_instance_ref.get_param("custid")
                compute_region_id = current_pinned_instance_ref.get_param("instance_region_id")
                send_ngpa_cust_alert(custid, compute_region_id, NGPACustAlertReasons.FAILURE, self.logger, invoker="pinned_gp_gateway_instance_upgrade_bringup_error_log", dbh=self.dbh)

        except Exception as E:
            self.logger.error("pinned_gp_gateway_instance_upgrade_bringup_error_log:"
                              "Failed with exception! %s" % str(E.args))
            return

    def pinned_gp_gateway_instance_upgrade_transition_error_log(self,
                                                                current_pinned_instance_ref):
        try:
            self.logger.error(self.err_msg)
            current_pinned_instance_ref.set_param("instance_transition_done", -1)
            current_pinned_instance_ref.set_param("instance_transition_status",
                                                  self.err_msg)
            current_pinned_instance_ref.save_stage4()
            #if this value is True, send alert cust details notification
            if current_pinned_instance_ref.get_param("is_ngpa_auto_migration"):
                custid = current_pinned_instance_ref.get_param("custid")
                compute_region_id = current_pinned_instance_ref.get_param("instance_region_id")
                send_ngpa_cust_alert(custid, compute_region_id, NGPACustAlertReasons.FAILURE, self.logger, invoker="pinned_gp_gateway_instance_upgrade_transition_error_log", dbh=self.dbh)
                
        except Exception as E:
            self.logger.error("pinned_gp_gateway_instance_upgrade_transition_error_log:"
                              "Failed with exception! %s" % str(E.args))
            return

    def pinned_gp_gateway_instance_upgrade_delete_old_error_log(self,
                                                                current_pinned_instance_ref):
        try:
            self.logger.error(self.err_msg)
            current_pinned_instance_ref.set_param("delete_old_done", -1)
            current_pinned_instance_ref.set_param("delete_old_status",
                                                  self.err_msg)
            current_pinned_instance_ref.save_stage5()
            #if this value is True, send alert cust details notification
            if current_pinned_instance_ref.get_param("is_ngpa_auto_migration"):
                custid = current_pinned_instance_ref.get_param("custid")
                compute_region_id = current_pinned_instance_ref.get_param("instance_region_id")
                send_ngpa_cust_alert(custid, compute_region_id, NGPACustAlertReasons.FAILURE, self.logger, invoker="pinned_gp_gateway_instance_upgrade_delete_old_error_log", dbh=self.dbh)
                
        except Exception as E:
            self.logger.error("pinned_gp_gateway_instance_upgrade_delete_old_error_log:"
                              "Failed with exception! %s" % str(E.args))
            return

    def process_instances_bringup(self):
        success = False
        try:
            pinnedInstanceUpgrade = PinnedInstanceUpgradeModel(dbh=self.dbh,
                                                               instance_id=None,
                                                               shard_id=self.shard_id,
                                                               replica_size=self.replica_size)
            nr_entries, entries = pinnedInstanceUpgrade.query_all_stage3_entries()

            if nr_entries == 0:
                self.logger.debug("process_instances_bringup: "
                                 "No entries found to work on, sleeping again!")
                success = True
                return success

            if len(entries) != nr_entries:
                raise Exception("process_instances_bringup: Fatal!  Numbed of entries in the list do "
                                "not match the entries reported.")

            for instance_entry in entries:
                try:

                    current_instance_id = instance_entry[0]
                    current_pinned_instance_ref = PinnedInstanceUpgradeModel(dbh=self.dbh,
                                                                             instance_id=current_instance_id)

                    if current_pinned_instance_ref.isvalid == False:
                        self.err_msg = ("process_instances_bringup Fatal! "
                                          "Failed to lookup the instance id %s." % (str(current_instance_id)))
                        self.pinned_gp_gateway_instance_upgrade_bringup_error_log(current_pinned_instance_ref)
                        continue

                    new_instance_id = current_pinned_instance_ref.get_param("new_instance_id")

                    curr_instance_reference = InstanceModel(iid=current_instance_id, dbh=self.dbh)
                    if not curr_instance_reference.get_param("id"):
                        self.err_msg = ("Fatal! Failed to locate instance with id %s from the database." %
                                          str(current_instance_id))
                        self.pinned_gp_gateway_instance_upgrade_bringup_error_log(current_pinned_instance_ref)
                        continue

                    new_instance_reference = InstanceModel(iid=new_instance_id, dbh=self.dbh)
                    if not new_instance_reference.get_param("id"):
                        self.err_msg = ("process_instances_bringup: Fatal! Failed to locate instance with id %s "
                                          "from the database." %
                                          str(new_instance_id))
                        self.pinned_gp_gateway_instance_upgrade_bringup_error_log(current_pinned_instance_ref)
                        continue

                    if new_instance_reference.get_param("vmid") != None and new_instance_reference.get_param("vmid") != 0:
                        # This means that the instance was brought up successfully.
                        sites = new_instance_reference.get_sites(dbh=self.dbh, transition=1)
                        if len(sites) == 0:
                            self.err_msg = ("Fatal! Failed to get sites for instance id %s" % (str(new_instance_id)))
                            self.pinned_gp_gateway_instance_upgrade_transition_error_log(current_pinned_instance_ref)
                            continue

                        # For each site let's move the transitioned instances.
                        for site_node_id in sites:
                            custnode = CN.CustNodeModel(site_node_id, self.dbh)
                            if custnode.id:
                                # Bind this node to the instance.
                                ret = new_instance_reference.bind(dbh=self.dbh,
                                                                  custid=None,
                                                                  node_type=None,
                                                                  node_id=None,
                                                                  is_new=0,
                                                                  custnode=custnode)
                                if ret == False:
                                    self.err_msg = ("process_instances_bringup: Fatal! "
                                                    "Failed to bind the new instance %s" % str(new_instance_id))
                                    self.logger.error(self.err_msg)
                                    self.pinned_gp_gateway_instance_upgrade_transition_error_log(
                                                                                        current_pinned_instance_ref
                                                                                        )
                                    new_instance_reference.delete(dbh=self.dbh)
                                    raise Exception(self.err_msg)

                        current_pinned_instance_ref.set_param("bringup_done", 1)
                        current_pinned_instance_ref.set_param("bringup_status", "Success!")
                        ret = current_pinned_instance_ref.save_stage3()
                        if ret == False:
                            self.err_msg = ("process_instances_bringup Fatal ! "
                                                "Failed to trigger save bringup state for  id %s" %
                                              (str(current_pinned_instance_ref.get_param("current_instance_id"))))
                            self.pinned_gp_gateway_instance_upgrade_bringup_error_log(current_pinned_instance_ref)
                            continue

                except Exception as E:
                    self.logger.error("Fatal! Failed to process instance_entry %s, Exception %s, Traceback %s"
                                      % (str(instance_entry), str(E.args), str(traceback.format_exc())))
                    continue

            success = True

        except Exception as E:
            success = False
            self.logger.error("process_instances_bringup: Failed with "
                              "Exception: %s "
                              "Traceback: %s "
                              "locals: %s"
                              % (str(E.args), str(traceback.print_exc()), str(locals())))
        finally:
            return success


    def process_instance_transition(self):
        success = False
        failure_count = 0
        try:
            custid = None
            compute_region_idx = None
            pinnedInstanceUpgrade = PinnedInstanceUpgradeModel(dbh=self.dbh,
                                                               instance_id=None,
                                                               shard_id=self.shard_id,
                                                               replica_size=self.replica_size)

            nr_entries, entries = pinnedInstanceUpgrade.query_all_stage4_entries()

            if nr_entries == 0:
                self.logger.info("process_instance_transition: "
                                 "No entries found to work on, sleeping again!")
                success = True
                return failure_count

            if len(entries) != nr_entries:
                raise Exception("process_instance_transition: Fatal!  Numbed of entries in the list do "
                                "not match the entries reported.")

            for instance_entry in entries:
                try:

                    current_instance_id = instance_entry[0]
                    current_pinned_instance_ref = PinnedInstanceUpgradeModel(dbh=self.dbh,
                                                                             instance_id=current_instance_id)

                    if current_pinned_instance_ref.isvalid == False:
                        self.err_msg = ("process_instance_transition: Fatal! "
                                        "Failed to lookup the instance id %s." % (str(current_instance_id)))
                        self.pinned_gp_gateway_instance_upgrade_transition_error_log(current_pinned_instance_ref)
                        failure_count += 1
                        continue

                    # If we are in 2 phase upgrade, set "delete_after" to
                    # current time + "wait_time", if not already set.
                    is_two_phase_upgrade_call = \
                        current_pinned_instance_ref.get_param(
                            "two_phase_upgrade_invocation")
                    delete_after = int(current_pinned_instance_ref.get_param(
                                           "delete_after"))
                    if (is_two_phase_upgrade_call and (delete_after == 0)):
                        wait_time = int(current_pinned_instance_ref.get_param(
                                            "wait_time"))
                        delete_after = int(time.time()) + wait_time
                        self.logger.info("Set delete_after to %s"
                                         % delete_after)
                        current_pinned_instance_ref.set_param(
                            "delete_after", delete_after)
                        ret = current_pinned_instance_ref.save_stage4()
                        if ret == False:
                            self.err_msg = ("process_instance_transition "
                                "Fatal ! Failed to set delete_after for "
                                "current instance id %s" % (str(
                                    current_pinned_instance_ref.get_param(
                                    "current_instance_id"))))
                            self.pinned_gp_gateway_instance_upgrade_transition_error_log(
                                current_pinned_instance_ref)
                            failure_count += 1
                            continue

                    new_instance_id = current_pinned_instance_ref.get_param("new_instance_id")

                    new_instance_reference = InstanceModel(iid=new_instance_id, dbh=self.dbh)
                    if not new_instance_reference.get_param("id"):
                        self.err_msg = ("process_instance_transition: Fatal! Failed to locate instance with id %s "
                                        "from the database." %
                                         str(current_instance_id))
                        self.pinned_gp_gateway_instance_upgrade_transition_error_log(current_pinned_instance_ref)
                        failure_count += 1
                        continue

                    if new_instance_reference.get_param("vm_status") == 0:
                        self.logger.info("Continue waiting for vm_status for instance id %s" % (
                            str(new_instance_id)))
                        current_pinned_instance_ref.set_param("instance_transition_status",
                                                              "vm_status: %s keep waiting."
                                                              % str(new_instance_reference.get_param("vm_status")))
                        ret = current_pinned_instance_ref.save_stage4()
                        if ret == False:
                            self.err_msg = ("process_instance_transition Fatal ! "
                                            "Failed to trigger save instance transition state for current instance id "
                                            "%s" %
                                            (str(current_pinned_instance_ref.get_param("current_instance_id"))))
                            self.pinned_gp_gateway_instance_upgrade_transition_error_log(
                                current_pinned_instance_ref)
                            failure_count += 1
                            continue
                        continue


                    elif new_instance_reference.get_param("vm_status") == 1:
                        # Optionaly bypass commit check CYR-12120.
                        bypass_commit_check = current_pinned_instance_ref.get_param("bypass_commit_check")
                        if not bypass_commit_check:
                            new_instance_commit_status, return_msg = new_instance_reference.check_commit_all()
                            if new_instance_commit_status == 1:
                                msg = "Commit success, continue to transit instance: %s"%return_msg
                                # We will save the instance_transition_status
                                # message at this point.
                                current_pinned_instance_ref.set_param(
                                    "instance_transition_status", msg)
                                ret = current_pinned_instance_ref.save_stage4()
                                if ret == False:
                                    self.err_msg = (
                                        "process_instance_transition Fatal ! "
                                        "Failed to trigger save instance "
                                        "transition status for current "
                                        "instance id %s" %
                                        (str(current_pinned_instance_ref.\
                                         get_param("current_instance_id"))))
                                    self.pinned_gp_gateway_instance_upgrade_transition_error_log(
                                        current_pinned_instance_ref)
                                    failure_count += 1
                                    continue

                                # Now, set the instance_transition_done flag.
                                # We will save this field explicitly in a
                                # separate call.
                                current_pinned_instance_ref.set_param("instance_transition_done", 1)
                            elif new_instance_commit_status == 0:
                                self.logger.info("Continue waiting for check_latest_commit for instance id %s, %s" % (
                                    str(new_instance_id), return_msg))
                                msg = "vm_status: %s, commit check status: %s, keep waiting for commit status." % (str(new_instance_reference.get_param("vm_status")), return_msg)
                                if is_two_phase_upgrade_call and current_pinned_instance_ref.get_param("is_ngpa_auto_migration"):
                                    if delete_after and delete_after > 0 and int(time.time()) - 7200 > delete_after:
                                        #send alert here saying commit stalled/failed
                                        msg = (f"Commit has not finished 2 hours after scheduled time for an auto-migration instance. Considering it stalled!" 
                                               f"vm_status: {str(new_instance_reference.get_param('vm_status'))}, commit check status: {return_msg}, keep waiting for commit status.")
                                        publish_ngpa_notifications_to_cosmos(self.logger, new_instance_reference.get_param('acct_id'),
                                        msg, severity_id=2, category="NGPA_FAILURE_MIGRATION_INSTANCE_COMMIT_STALL",
                                        compute_region_idx=new_instance_reference.get_param("compute_region_idx"),
                                        dbh=self.dbh)
                            else:
                                self.err_msg = ("Fatal! Failed to get check_latest_commit for instance id %s, %s" % (
                                    (str(new_instance_id), return_msg)))
                                self.pinned_gp_gateway_instance_upgrade_transition_error_log(
                                    current_pinned_instance_ref)
                                failure_count += 1
                                continue
                        else:
                            msg = ("Bypass commit check. node_type %s, vm_status: %s, continue to transit instance" %
                                   (str(new_instance_reference.get_param("node_type")), str(new_instance_reference.get_param("vm_status"))))
                            current_pinned_instance_ref.set_param("instance_transition_done", 1)

                        # "vm_status" is 1. If commit is also completed, and
                        # this was a 2 phase upgrade, set the "upgrade_status"
                        # to 'COMPLETED-PRE-UPGRADE' for the new instance.
                        is_commit_done = \
                            current_pinned_instance_ref.get_param(
                                "instance_transition_done")
                        if is_two_phase_upgrade_call:
                            if is_commit_done:
                                self.logger.info("Set upgrade_status to "
                                    "COMPLETED-PRE-UPGRADE for instance %s" %
                                    str(new_instance_id))
                                new_instance_reference.update_column_upgrade_status(
                                                            self.dbh,
                                                            'COMPLETED-PRE-UPGRADE')
                            else:
                                self.logger.info("Commit is not yet done; "
                                    "Not setting upgrade status for instance "
                                    "%s" % str(new_instance_id))

                            # We will continue waiting here till "wait_time"
                            # is done, or "complete_upgrade_now" is set.
                            # This will prevent us from performing
                            # "transition_instances" till the second phase is
                            # invoked, or "wait_time" is completed.
                            complete_upg_now = current_pinned_instance_ref.\
                                                   get_param(
                                                       "complete_upgrade_now")
                            delete_after = int(current_pinned_instance_ref.\
                                                   get_param("delete_after"))
                            if ((not complete_upg_now) and
                                ((delete_after != 0) and
                                 (delete_after >= int(time.time())))):
                                self.logger.info("Second phase of the 2 "
                                    "phase upgrade has not kicked in yet; "
                                    "Not transitioning instances yet...")
                                continue
                            else:
                                self.logger.info("complete_upgrade_now is "
                                    "set, or delete_after is reached; move "
                                    "on to save instance_transition_status "
                                    "and transition instances now")
                        else:
                            self.logger.info("Not 2 phase upgrade invocation "
                                "or commit is not yet done; Skip setting "
                                "upgrade status for instance %s"
                                % str(new_instance_id))

                        current_pinned_instance_ref.set_param("instance_transition_status", msg)
                        ret = current_pinned_instance_ref.save_stage4()
                        if ret == False:
                            self.err_msg = ("process_instance_transition Fatal ! "
                                            "Failed to trigger save instance transition state for current instance id "
                                            "%s" %
                                            (str(current_pinned_instance_ref.get_param("current_instance_id"))))
                            self.pinned_gp_gateway_instance_upgrade_transition_error_log(current_pinned_instance_ref)
                            failure_count += 1
                            continue

                except Exception as E:
                    success = False
                    self.logger.error("process_instance_transition: Failed with "
                                      "Exception: %s "
                                      "Traceback: %s "
                                      "locals: %s"
                                      % (str(E.args), str(traceback.print_exc()), str(locals())))
            success = True

        except Exception as E:
            success = False
            self.logger.error("process_instance_transition: Failed with "
                              "Exception: %s "
                              "Traceback: %s "
                              "locals: %s"
                              % (str(E.args), str(traceback.print_exc()), str(locals())))
        finally:
            return failure_count


    def process_old_instance_delete(self):
        success = False
        #skip_deletion_instances tracks the instances that have received r53 message
        #during this invocation, and should not proceed to deletion until the next cycle
        #This is due to this function having 2 loops r53 msg and deletion not being in the same loop
        #meaning sending r53 and 'continue' cannot guarantee that deletion is not done
        #NOTE: In 6.0 we should have a more graceful solution.
        skip_deletion_instances = []
        try:
            custid = None
            compute_region_idx = None
            cfg = OrchCfgModel(self.dbh).fields
            pinnedInstanceUpgrade = PinnedInstanceUpgradeModel(dbh=self.dbh,
                                                               instance_id=None,
                                                               shard_id=self.shard_id,
                                                               replica_size=self.replica_size)

            nr_entries, entries = pinnedInstanceUpgrade.query_all_stage5_entries()

            if nr_entries == 0:
                self.logger.debug("process_old_instance_delete: "
                                 "No entries found to work on, sleeping again!")
                success = True
                return success

            if len(entries) != nr_entries:
                raise Exception("process_old_instance_delete: Fatal!  Numbed of entries in the list do "
                                "not match the entries reported.")

            for instance_entry in entries:
                try:
                    current_instance_id = instance_entry[0]
                    current_pinned_instance_ref = PinnedInstanceUpgradeModel(dbh=self.dbh,
                                                                             instance_id=current_instance_id)
                    old_instance_reference = InstanceModel(iid=current_instance_id, dbh=self.dbh)
                    if not old_instance_reference.get_param("id"):
                        self.err_msg = (
                                "delete_old_status: Fatal! Failed to locate instance with id %s "
                                "from the database." %
                                str(current_instance_id))
                        self.pinned_gp_gateway_instance_upgrade_delete_old_error_log(
                            current_pinned_instance_ref)
                        continue

                    if current_pinned_instance_ref.isvalid == False:
                        self.err_msg = ("process_old_instance_delete: Fatal! "
                                        "Failed to lookup the instance id %s." % (str(current_instance_id)))
                        self.pinned_gp_gateway_instance_upgrade_delete_old_error_log(current_pinned_instance_ref)
                        continue

                    bypass_commit_check = current_pinned_instance_ref.get_param("bypass_commit_check")
                    wait_time = int(current_pinned_instance_ref.get_param("wait_time"))

                    _delete_after = current_pinned_instance_ref.get_param("delete_after")
                    delete_after = int(_delete_after)

                    # Get the new instance reference.
                    new_instance_id = current_pinned_instance_ref.get_param(
                                          "new_instance_id")
                    new_instance_reference = InstanceModel(
                                                 iid=new_instance_id,
                                                 dbh=self.dbh)
                    if not new_instance_reference.get_param("id"):
                        self.err_msg = ("process_old_instance_delete: Fatal! "
                                        "Failed to locate instance with id "
                                        "%s from the database." %
                                        str(new_instance_id))
                        self.pinned_gp_gateway_instance_upgrade_transition_error_log(current_pinned_instance_ref)
                        continue

                    compute_region_idx = new_instance_reference.get_param(
                                             "compute_region_idx")
                    custid = new_instance_reference.get_param("custid")

                    # Now we will transition the sites if the vm status
                    # is marked as UP.
                    # Addtionally, if it is 2phase upgrade and behind NLB,
                    # we will check for NAT tunnel status
                    is_two_phase_upgrade_call = \
                            current_pinned_instance_ref.get_param(
                                "two_phase_upgrade_invocation")
                    

                    if delete_after != 0 and delete_after <= int(time.time()):
                        self.logger.info("Current time [%s] reached the "
                            "time delete_after: [%s]; Complete all needed "
                            "actions and trigger the old instance deletion" %
                            (str(time.time()), delete_after))

                        # If this is two phase upgrade, but
                        # "complete_upgrade_now" flag is not set for the
                        # "current_pinned_instance_ref", we are in a state
                        # where the second phase of the 2 phase
                        # upgrade was not invoked, but the "wait_time" that
                        # was set in the pre-upgrade phase has been reached.
                        # We will just print out an error message, but proceed
                        # with the second phase.
                        # For the second phase, set the "upgrade_status" to
                        # "READY-TO-UPGRADE" for the "new_instance_reference".
                        # Also, trigger an SNS notification.
                        complete_upg_now = current_pinned_instance_ref.\
                                               get_param(
                                                   "complete_upgrade_now")

                        if is_two_phase_upgrade_call:
                            #If the instance upgrade_status is in ["READY-TO-UPGRADE", "COMPLETED-UPGRADE"], that means we have already sent an R53 upgdate message
                            #It meanst that previously NAT tunnel status were ok, so if any problem occurs, it should not block the upgrade.
                            #And at this point, the ship has sailed and we should just let the upgrade complete naturally.
                            if new_instance_reference.get_param("is_instance_behind_nlb") and new_instance_reference.get_param("upgrade_status") not in ["READY-TO-UPGRADE", "COMPLETED-UPGRADE"]:
                                self.logger.info(f"Checking for NAT tunnel status before transitioning instance")
                                success, _, nr_nat_instances, nat_dummy_upgrade_timeout = find_nat_configs_by_custid_and_region_id(self.dbh, current_pinned_instance_ref.get_param("custid"),
                                                                                                        current_pinned_instance_ref.get_param("instance_region_id"))
                                #NOTE nat_dummy_upgrade_timeout was designed to be used in nat_dummy_upgrade but is not re-used here, hence the naming mismatch
                                if not success or not nr_nat_instances or not nat_dummy_upgrade_timeout:
                                    self.logger.error(f"Failed to get NAT config from cust_nat_mgmt_table for new instance {new_instance_reference.get_param('id')}! "
                                                      f"we will assume nr_nat_instances=2 and nat_dummy_upgrade_timeout=7200 (tunnel check status timeout)")
                                    nr_nat_instances = 2
                                    nat_dummy_upgrade_timeout = 7200
                                all_good = check_nat_tunnel_status_for_vmid(new_instance_reference.get_param("vmid"), self.logger,  dbh=self.dbh, nat_count=nr_nat_instances)
                                self.logger.info(f"check_nat_tunnel_status_for_vmid returned all_good: {all_good}")
                                if not all_good: 
                                    self.logger.error(f"NAT tunnel status check failed for new instance {new_instance_reference.get_param('id')} "
                                                      f",  need to wait for a bit longer before transitioning instance.")
                                    if delete_after+int(nat_dummy_upgrade_timeout) < int(time.time()):
                                        #if NAT tunnel status is not ready after nat_dummy_upgrade_timeout seconds, fail the upgrade
                                        self.err_msg = (f"NAT tunnel status check failed for new instance {new_instance_reference.get_param('id')} "
                                                        f"{nat_dummy_upgrade_timeout} seconds after delete_after time, marking the upgrade as failed.")
                                        self.pinned_gp_gateway_instance_upgrade_transition_error_log(current_pinned_instance_ref)
                                    skip_deletion_instances.append(new_instance_reference.get_param("id"))
                                    continue

                            self.logger.info("Update upgrade_status; send R53 "
                                "update if needed and transition instance now")

                            if not complete_upg_now:
                                self.logger.error("!!!!! WARNING: Wait time "
                                    "reached for the first phase of the 2 "
                                    "phase upgrade! Will move on to the "
                                    "phase 2 of the upgrade and clean-up!!!!!")
                            else:
                                self.logger.info("complete_upgrade_now is set "
                                    "for pinned instance with id: %s" %
                                    current_instance_id)

                            # Check if the previous "upgrade_status" is
                            # "COMPLETED-PRE-UPGRADE" If so:
                            # - Set the "mark_delete3" flag to 3 for the old
                            #   instance - this removes DNS entry for the
                            #   instance.
                            # - Set the "upgrade_status" to 'READY-TO-UPGRADE'
                            #   for the new instance.
                            # - Send the Route53 update SNS notification
                            #   for the new instance.
                            new_inst_upg_status = new_instance_reference.\
                                                      get_param(
                                                          "upgrade_status")
                            self.logger.info("Upgrade_status for the new "
                                     "instance [%s]" % new_inst_upg_status)

                            if new_inst_upg_status == "COMPLETED-PRE-UPGRADE":
                                # Set the "upgrade_status" to
                                # 'READY-TO-UPGRADE' for the
                                # new instance.
                                self.logger.info("Update upgrade_status to "
                                                 "READY-TO-UPGRADE")
                                new_instance_reference.\
                                    update_column_upgrade_status(
                                        self.dbh, 'READY-TO-UPGRADE')
                                
                                #notify customer if it is auto migration
                                if current_pinned_instance_ref.get_param("is_ngpa_auto_migration"):
                                    custid = current_pinned_instance_ref.get_param("custid")
                                    compute_region_id = current_pinned_instance_ref.get_param("instance_region_id")
                                    send_ngpa_cust_alert(custid, compute_region_id, NGPACustAlertReasons.PHASE2, self.logger, dbh=self.dbh)
                                    
                                # Send the Route53 update SNS notification
                                # for the new instance.
                                is_new_inst_behind_nlb = \
                                    new_instance_reference.get_param(
                                        "is_instance_behind_nlb")
                                r53_ip = None
                                r53_ip_v6 = None
                                if is_new_inst_behind_nlb:
                                    r53_ip = new_instance_reference.get_param(
                                                "lb_details")
                                    r53_ip_v6 = new_instance_reference.get_param(
                                                "lb_details_v6")
                                    self.logger.info(f"New instance is behind "
                                        f"NLB; Sending R53 update with NLB IP: "
                                        f"{r53_ip} NLB IPv6: {r53_ip_v6}")
                                else:
                                    r53_ip = new_instance_reference.get_param(
                                                "public_ip")
                                    self.logger.info("New instance is not "
                                        "behind NLB; Sending R53 update with "
                                        "public IP: %s" % r53_ip)
                                send_sns_msg_for_r53_update(
                                    new_instance_reference.get_param("id"),
                                    new_instance_reference.get_param("custid"),
                                    r53_ip,
                                    self.logger,
                                    ipv6_addr=r53_ip_v6)
                                skip_deletion_instances.append(new_instance_reference.get_param("id"))
                                continue
                            elif new_inst_upg_status == "READY-TO-UPGRADE":
                                self.logger.info("Upgrade status is still 'READY-TO-UPGRADE'")
                                #this is hacky but will have to do for now
                                #CYR-38444 increasing retry time limit to 30mins as no better fix has been found
                                #11 tries counting the first try, each retry 180 seconds apart, total should be around 30 mins
                                #not counting time spent looping through entries
                                max_retries = 11
                                two_phase_upgrade_invocation = current_pinned_instance_ref.get_param("two_phase_upgrade_invocation")
                                if int(two_phase_upgrade_invocation) != 0 and int(two_phase_upgrade_invocation) < max_retries:
                                    self.logger.info(f"Retrying r53 message: max_retries={max_retries}")
                                    current_pinned_instance_ref.set_param("two_phase_upgrade_invocation", int(two_phase_upgrade_invocation)+1)
                                    # Send the Route53 update SNS notification
                                    # for the new instance.
                                    is_new_inst_behind_nlb = \
                                        new_instance_reference.get_param(
                                            "is_instance_behind_nlb")
                                    r53_ip = None
                                    r53_ip_v6 = None
                                    if is_new_inst_behind_nlb:
                                        r53_ip = new_instance_reference.get_param(
                                                    "lb_details")
                                        r53_ip_v6 = new_instance_reference.get_param(
                                                    "lb_details_v6")
                                        self.logger.info(f"New instance is behind "
                                            f"NLB; Sending R53 update with NLB IP: "
                                            f"{r53_ip} NLB IPv6: {r53_ip_v6}")
                                        msg = (f"send_sns_msg_for_r53_update retry was triggered"
                                               f"for an 2P upgrade instance, tenant_id:{new_instance_reference.get_param('acct_id')}"
                                               f"instance_id:{new_instance_reference.get_param('id')}")
                                        #send this msg to NGPA dashboard so someone will know, but we don't care if this publishing fails
                                        publish_ngpa_notifications_to_cosmos(self.logger, new_instance_reference.get_param('acct_id'),
                                                                            msg, severity_id=2, category="NGPA_INFO_R53_UPGRADE_RETRY",
                                                                            compute_region_idx=new_instance_reference.get_param("compute_region_idx"),
                                                                            dbh=self.dbh)
                                    else:
                                        r53_ip = new_instance_reference.get_param(
                                                    "public_ip")
                                        r53_ip_v6 = new_instance_reference.get_param("public_ipv6")

                                        self.logger.info(f"New instance is not behind "
                                            f"NLB; Sending R53 update with public IP: "
                                            f"{r53_ip} public IPv6: {r53_ip_v6}")
                                    send_sns_msg_for_r53_update(
                                        new_instance_reference.get_param("id"),
                                        new_instance_reference.get_param("custid"),
                                        r53_ip,
                                        self.logger,
                                        ipv6_addr=r53_ip_v6)
                                    if delete_after != 0:
                                        delete_after += 180
                                        self.logger.info(f"Incrementing delete_after by 180 secs, result:{delete_after}")
                                        current_pinned_instance_ref.set_param("delete_after", delete_after)
                                    current_pinned_instance_ref.save_stage5()
                                    skip_deletion_instances.append(new_instance_reference.get_param("id"))

                                elif int(two_phase_upgrade_invocation) >= max_retries:
                                    self.logger.info(f"Reached maximum allowed retries for "
                                                     f"instance_id:{new_instance_reference.get_param('id')}, sending alerts for cleanup!")
                                    msg = (f"send_sns_msg_for_r53_update reached maximum retry"
                                            f"for an 2P upgrade instance, tenant_id:{new_instance_reference.get_param('acct_id')}"
                                            f"instance_id:{new_instance_reference.get_param('id')}, instance will need manual recovery!")
                                    publish_ngpa_notifications_to_cosmos(self.logger, new_instance_reference.get_param('acct_id'),
                                                                        msg, severity_id=2, category="NGPA_FAILURE_R53_UPGRADE_RETRY",
                                                                        compute_region_idx=new_instance_reference.get_param("compute_region_idx"),
                                                                        dbh=self.dbh)
                                continue
                            else:
                                self.logger.info(f"May do no updates; since "
                                    f"upgrade_status for the new instance: "
                                    f"{new_instance_reference.get_param('id')} "
                                    f"may be invalid for the second phase of "
                                    f"the upgrade: {new_inst_upg_status}")
                                if new_inst_upg_status != "COMPLETED-UPGRADE":
                                    self.logger.info(f"New instance "
                                        f"{new_instance_reference.get_param('id')} "
                                        f"is still not in COMPLETED-UPGRADE "
                                        f"state; wait before deleting the "
                                        f"old instance")
                                    continue
                        else:
                            self.logger.info("Legacy upgrade; not doing any "
                                "two phase upgrade specific actions")
                            
                    elif is_two_phase_upgrade_call and (delete_after == 0 or delete_after > int(time.time())):
                        self.logger.info(f"Not yet time to process two phase upgrade instance")
                        continue

                    transition_failed = False
                    sites = new_instance_reference.get_sites(dbh=self.dbh,
                                                             transition=1)
                    if sites is None:
                        self.err_msg = ("process_instance_transition Fatal ! "
                                "Fatal! Failed to get sites for "
                                "instance id %s" % (str(new_instance_id)))
                        self.pinned_gp_gateway_instance_upgrade_transition_error_log(
                            current_pinned_instance_ref)
                        continue

                    # For each site let's move the transitioned instances.
                    self.logger.info(f"For instance {new_instance_reference.get_param('id')}: sites are: {sites}")
                    for site_node_id in sites:
                        custnode = CN.CustNodeModel(site_node_id, self.dbh)
                        self.logger.info(f"For instance {new_instance_reference.get_param('id')}: custnode is: {custnode}")
                        if custnode.id:
                            old_compute_region_idx = \
                                custnode.transition_instances(self.dbh, None,
                                                              unbind_old=False)
                            if not old_compute_region_idx:
                                transition_failed = True
                                self.logger.error("Fatal! Failed to "
                                    "transition instances for node %s, "
                                    "current instance %s, new instance %s" %
                                    (str(custnode.id),
                                     str(current_instance_id),
                                     str(new_instance_id)))
                        else:
                            #If it has gotten into this block
                            #Then sites were returned as non-empty, but custnode.id is not set correct
                            #This should be a cornor case and we should not continue pass this point
                            self.logger.error(f"For instance {new_instance_reference.get_param('id')}: could not get custnode.id! Cannot continue!!! Will try again in the next cycle"
                                              f"Given custnode.id is {custnode.id}")
                            skip_deletion_instances.append(new_instance_reference.get_param('id'))
                            continue


                    if transition_failed == False and new_instance_reference.get_param('id') not in skip_deletion_instances:
                        old_instance_reference = InstanceModel(
                                                     iid=current_instance_id,
                                                     dbh=self.dbh)
                        if not old_instance_reference.get_param("id"):
                            self.err_msg = ("process_instance_transition: "
                                            "Fatal! Failed to locate "
                                            "instance with id %s "
                                            "from the database." %
                                            str(current_instance_id))
                            self.pinned_gp_gateway_instance_upgrade_transition_error_log(
                                current_pinned_instance_ref)
                            continue

                        new_instance_reference.set_param("is_dynamic_instance",
                            old_instance_reference.get_param("is_dynamic_instance"))
                        new_instance_reference.save()

                        current_pinned_instance_ref.set_param(
                            "instance_transition_status", "Success!")
                        ret = current_pinned_instance_ref.save_stage4()
                        if ret == False:
                            self.err_msg = ("process_instance_transition "
                                            "Fatal! Failed to trigger save "
                                            "instance transition state for "
                                            "current instance id %s" %
                                            (str(current_pinned_instance_ref.\
                                                 get_param("current_instance_id"))))
                            self.pinned_gp_gateway_instance_upgrade_transition_error_log(
                                current_pinned_instance_ref)
                            continue
                    else:
                        self.err_msg = ("process_instance_transition! Fatal ! "
                                        "Failed to process transition of "
                                        "certain sites. Please check logs "
                                        "above!")
                        self.pinned_gp_gateway_instance_upgrade_transition_error_log(
                            current_pinned_instance_ref)
                        continue

                except Exception as E:
                    success = False
                    self.logger.error("process_instance_transition: Failed with "
                                      "Exception: %s "
                                      "Traceback: %s "
                                      "locals: %s"
                                      % (str(E.args), str(traceback.print_exc()), str(locals())))

            for instance_entry in entries:
                try:
                    current_instance_id = instance_entry[0]
                    current_pinned_instance_ref = PinnedInstanceUpgradeModel(dbh=self.dbh,
                                                                             instance_id=current_instance_id)
                    old_instance_reference = InstanceModel(iid=current_instance_id, dbh=self.dbh)
                    if not old_instance_reference.get_param("id"):
                        self.err_msg = (
                                "delete_old_status: Fatal! Failed to locate instance with id %s "
                                "from the database." %
                                str(current_instance_id))
                        self.pinned_gp_gateway_instance_upgrade_delete_old_error_log(
                            current_pinned_instance_ref)
                        continue

                    if current_pinned_instance_ref.isvalid == False:
                        self.err_msg = ("process_old_instance_delete: Fatal! "
                                        "Failed to lookup the instance id %s." % (str(current_instance_id)))
                        self.pinned_gp_gateway_instance_upgrade_delete_old_error_log(current_pinned_instance_ref)
                        continue

                    # Get the new instance reference.
                    new_instance_id = current_pinned_instance_ref.get_param(
                                          "new_instance_id")
                    new_instance_reference = InstanceModel(
                                                 iid=new_instance_id,
                                                 dbh=self.dbh)
                    if not new_instance_reference.get_param("id"):
                        self.err_msg = ("process_old_instance_delete: Fatal! "
                                        "Failed to locate instance with id "
                                        "%s from the database." %
                                        str(new_instance_id))
                        self.pinned_gp_gateway_instance_upgrade_transition_error_log(current_pinned_instance_ref)
                        continue

                    wait_time = int(current_pinned_instance_ref.get_param("wait_time"))
                    _delete_after = current_pinned_instance_ref.get_param("delete_after")
                    delete_after = int(_delete_after)

                    # Get the "is_two_phase_upgrade" flag
                    # for the "current_pinned_instance_ref".
                    is_two_phase_upgrade_call = \
                            current_pinned_instance_ref.get_param(
                                "two_phase_upgrade_invocation")
                    
                    #CYR-42951 preserve NAT ip flag
                    preserve_nat_ip = True if current_pinned_instance_ref.get_param("preserve_nat_ip") else False

                    if (not is_two_phase_upgrade_call and
                        delete_after != 0 and
                        delete_after - wait_time < int(time.time())):
                        self.logger.info("Current time [%s] + wait_time [%s] "
                            "reached the time delete_after: [%s]; Do DNS "
                            "update for old instance for legacy upgrade" %
                            (str(time.time()), wait_time, delete_after))

                        old_instance_reference.set_param("mark_delete", 3)
                        old_instance_reference.save()
                        self.logger.info("mark_delete set to 3 for "
                            "old instance %s" %
                            (str(current_pinned_instance_ref.get_param(
                             "current_instance_id"))))

                        
                        current_pinned_instance_ref.set_param("delete_old_status", "Wait for user logout")
                        ret = current_pinned_instance_ref.save_stage5()

                        if ret == False:
                            self.err_msg = ("process_old_instance_delete Fatal ! "
                                            "Failed to trigger save instance transition state for current instance id "
                                            "%s" %
                                            (str(current_pinned_instance_ref.get_param("current_instance_id"))))
                            self.pinned_gp_gateway_instance_upgrade_delete_old_error_log(current_pinned_instance_ref)
                            continue

                    if delete_after != 0 and delete_after < int(time.time()):
                        self.logger.info("Current time [%s] reached the "
                            "time delete_after: [%s]; Complete all needed "
                            "actions and trigger the old instance deletion" %
                            (str(time.time()), delete_after))
                        

                        if is_two_phase_upgrade_call:
                            if new_instance_reference.get_param("id") in skip_deletion_instances:
                                self.logger.info(f"Skipping deletion of old instance {old_instance_reference.get_param('id')} "
                                                 f"as new instance {new_instance_reference.get_param('id')} was sent r53 message in this cycle ")
                                continue
                                
                            new_inst_upg_status = new_instance_reference.get_param("upgrade_status")
                            if new_inst_upg_status == "COMPLETED-UPGRADE":
                                self.logger.info(f"Completed all actions "
                                    f"successfully for new instance "
                                    f"{new_instance_reference.get_param('id')}; "
                                    f"go ahead with "
                                    f"deletion of old instance")
                                # Set "mark_delete3" flag to 3 for the old
                                # instance.
                                old_instance_reference.set_param(
                                    "mark_delete", 3)
                                old_instance_reference.save()
                                self.logger.info(f"mark_delete set to 3 "
                                    f"for old instance "
                                    f"{current_pinned_instance_ref.get_param('current_instance_id')}")

                                current_pinned_instance_ref.set_param(
                                    "delete_old_status",
                                    "Wait for user logout")
                                ret = current_pinned_instance_ref.save_stage5()
                                if ret == False:
                                    self.err_msg = ("process_old_instance_delete "
                                        "Fatal! Failed to trigger save " 
                                        "instance transition state for "
                                        "current instance id "
                                        "{current_pinned_instance_ref.get_param('current_instance_id')}")
                                    self.pinned_gp_gateway_instance_upgrade_transition_error_log(
                                        current_pinned_instance_ref)
                            else:
                                self.logger.info(f"Upgrade status is still not COMPLETED-UPGRADE; deferring old instance deletion")
                                continue
                                    
                            if not is_dhcp_pool_allocation_enabled(
                                new_instance_reference.get_param("custid"),
                                self.dbh) and not is_ciam_enabled_from_ff(self.dbh.logger, new_instance_reference.get_param("custid"), cfg):
                                # Update the pool_allocation table.
                                # The IP address block used by the new
                                # instance as a "transient_inst_id" will be
                                # updated as follows:
                                #     - "inst_id" will be set to the value
                                #       stored in "transient_inst_id".
                                #     - "transient_inst_id" will be reset
                                #       to 0.
                                #     - "is_in_use_by_transient_inst" will be
                                #       reset to 0.
                                # We do this before just deleting the old
                                # instance.
                                ret = new_instance_reference.\
                                          update_pool_allocation_table(
                                              current_instance_id)
                                if ret == False:
                                    self.err_msg = ("process_old_instance_delete: "
                                        "Fatal! Failed to update the "
                                        "pool_allocation table after upgrade")
                                    self.logger.error(self.err_msg)
                                    self.pinned_gp_gateway_instance_upgrade_delete_old_error_log(
                                        current_pinned_instance_ref
                                    )
                                    raise Exception(self.err_msg)
                                self.logger.info("Successfully updated "
                                                 "pool_allocation_table")
                            else:
                                self.logger.info("DHCP Pool allocation "
                                    "enabled for tenant; no reuse of IP "
                                    "for old instances will be done")
                        else:
                            self.logger.info("Not 2 phase upgrade; nothing "
                                             "to clean up in pool_allocation")

                        # Trigger an instance delete.
                        self.logger.info("All conditions met for deletion of "
                            "old instance; go ahead with deletion/cleanup")
                        compute_region_idx = new_instance_reference.get_param("compute_region_idx")
                        custid = new_instance_reference.get_param("custid")
                        sites = new_instance_reference.get_sites(dbh=self.dbh)
                        if len(sites) == 0:
                            self.err_msg = ("Fatal! Failed to get sites for instance id %s" % (str(new_instance_id)))
                            self.pinned_gp_gateway_instance_upgrade_delete_old_error_log(current_pinned_instance_ref)
                            continue
                        
                        # For each site let's move the transitioned instances.
                        site_node_id = sites[0]
                        custnode = CN.CustNodeModel(site_node_id, self.dbh)
                        if custnode.id:
                            # Invoke "unbind" to clean-up the old instance.
                            ret = old_instance_reference.unbind(custnode, self.dbh, None, preserve_nat_ip=preserve_nat_ip)
                            if ret == False:
                                self.err_msg = ("process_old_instance_delete: Fatal! "
                                                "Failed to unbind the old instance %s" % str(current_instance_id))
                                self.logger.error(self.err_msg)
                                self.pinned_gp_gateway_instance_upgrade_delete_old_error_log(
                                                                                    current_pinned_instance_ref
                                                                                    )
                                raise Exception(self.err_msg)
                            #send ADEM service notification about this delete
                            if is_adem_notification_enabled(self.db_h, self.logger):
                                delete_notify_adem_service(inst_id, self.db_h, self.logger)


                        # Set the "upgrade_creation" flag to 0 for the
                        # new instance. This will ensure that the new
                        # instance will be moved behind the NLB, during
                        # template creation and trigger_update to the cloud.
                        new_instance_reference.set_param("upgrade_creation", 0)
                        ret = new_instance_reference.save()
                        if ret == False:
                            self.err_msg = ("process_old_instance_delete: "
                                "Fatal! Failed to set upgrade_creation to 0 "
                                "for new instance %s" % str(new_instance_id))
                            self.logger.error(self.err_msg)
                            self.pinned_gp_gateway_instance_upgrade_delete_old_error_log(
                                current_pinned_instance_ref
                            )
                            raise Exception(self.err_msg)

                        myres, _ = trigger_update_for_instance_changes(self.dbh,
                                                                    compute_region_idx,
                                                                    custid,
                                                                    node_type=custnode.node_type,
                                                                    alt_node_type=custnode.alt_node_type)
                        if myres != True:
                            self.err_msg = ("process_old_instance_delete Fatal ! "
                                            "Failed to trigger update for instance changes. "
                                            "%s" %
                                            (str(current_pinned_instance_ref.get_param("current_instance_id"))))
                            self.pinned_gp_gateway_instance_upgrade_delete_old_error_log(current_pinned_instance_ref)
                            continue

                        if new_instance_reference.get_param("is_dynamic_instance"):
                            new_instance_reference.set_param("is_pinned_instance", 0)

                        ret = new_instance_reference.save()
                        if ret == False:
                            self.err_msg = ("process_old_instance_delete: Fatal! "
                                            "Failed to set is_pinned_instance to 0 for new instance %s" % str(
                                new_instance_id))
                            self.logger.error(self.err_msg)
                            self.pinned_gp_gateway_instance_upgrade_delete_old_error_log(
                                current_pinned_instance_ref
                            )
                            raise Exception(self.err_msg)
                        self.logger.debug(
                            "process_old_instance_delete: transitioned is_pinned_instance back to is_dynamic instance")

                        current_pinned_instance_ref.set_param("delete_old_done", 1)
                        current_pinned_instance_ref.set_param("delete_old_status", "Success!")

                    elif delete_after == 0:
                        old_instance_reference.save()
                        bypass_commit_check = current_pinned_instance_ref.get_param("bypass_commit_check")
                        wait_time = int(current_pinned_instance_ref.get_param("wait_time"))
                        delete_after = int(time.time()) + wait_time
                        current_pinned_instance_ref.set_param("delete_old_status", "Wait %ss for user to logout "
                                                              "or for the new instance commit ready. "
                                                              "(bypass commit check %s)" %
                                                              (wait_time, bypass_commit_check))
                        current_pinned_instance_ref.set_param("delete_after", delete_after)
                    
                    if current_pinned_instance_ref.get_param("is_ngpa_auto_migration"):
                            custid = current_pinned_instance_ref.get_param("custid")
                            compute_region_id = current_pinned_instance_ref.get_param("instance_region_id")
                            send_ngpa_cust_alert(custid, compute_region_id, NGPACustAlertReasons.COMPLETED, self.logger, dbh=self.dbh)
                    
                    if preserve_nat_ip:
                        delete_old_time = int(time.time())
                        current_pinned_instance_ref.set_param("delete_old_time", delete_old_time+60)
                    else:
                        current_pinned_instance_ref.set_param("nat_dummy_upgrade_done_or_not_needed", 1)

                    ret = current_pinned_instance_ref.save_stage5()
                    if ret == False:
                        self.err_msg = ("process_old_instance_delete Fatal ! "
                                        "Failed to trigger save instance transition state for current instance id "
                                        "%s" %
                                        (str(current_pinned_instance_ref.get_param("current_instance_id"))))
                        self.pinned_gp_gateway_instance_upgrade_delete_old_error_log(current_pinned_instance_ref)
                        continue


                except Exception as E:
                    success = False
                    self.logger.error("process_old_instance_delete: Failed with "
                                      "Exception: %s "
                                      "Traceback: %s "
                                      "locals: %s"
                                      % (str(E.args), str(traceback.print_exc()), str(locals())))
                success = True

        except Exception as E:
            success = False
            self.logger.error("process_old_instance_delete: Failed with "
                              "Exception: %s "
                              "Traceback: %s "
                              "locals: %s"
                              % (str(E.args), str(traceback.print_exc()), str(locals())))
        finally:
            return success


                    