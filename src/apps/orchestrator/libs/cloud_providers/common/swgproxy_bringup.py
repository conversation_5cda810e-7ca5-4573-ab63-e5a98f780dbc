import traceback
import json
from libs.model.instancemodel import InstanceModel
from libs.common.shared.sys_utils import *
from libs.common.shared.gcp_utils import get_proxy_prefix
from libs.common.shared.utils import IPAMService, is_ipam_service_enabled, get_cdl_region_info, is_swg_aws_enabled
from libs.common.shared import dbconn
from ipam_factory import create_ipam_utils
from libs.model.custEpaasConfigModel import *
from libs.model.explicitProxyTenantInfoModel import *
from libs.model.orchcfgmodel_v2 import OrchCfgModel_v2 as OrchCfgModel
from libs.apis.region_master_api import gpcs_get_cloud_type_from_region_idx
from libs.apis.region_master_api import gpcs_get_compute_region_name_from_edge_region_idx
from libs.model.custmodel import CustomerModel
from libs.model.regionmastermodel import RegionMasterModel
from libs.cloud_providers.common.ep_cnat_scaleout_helper import reserve_ips_for_cloud_nat_old, \
    release_redundant_ips_for_tenant_region
from libs.common.shared.utils import get_aws_partition
from libs.common.shared.sys_utils import sys_get_cloud_provider_name
from libs.cloud_providers.common.adem_fw_notify import is_adem_notification_enabled, notify_adem_service

class swgproxy_bringup:
    def __init__(self, dbh, node_type=NODE_TYPE_SWG_PROXY, alt_node_type=-1):
        if dbh == None or dbh.logger == None:
            return
        self.logger = dbh.logger
        self._cust_id = None
        self._region_idx = None
        self._stack_name = None
        self._cloud_provider = None
        self.dbh = dbh
        self.node_type = node_type
        self.alt_node_type = alt_node_type
        self.ipam_utils = create_ipam_utils(dbh=dbh, logger=self.logger)

    def process_instance(self, proxy_inst_info):
        success = False
        inst_id = proxy_inst_info['PrimaryId']
        vmid = proxy_inst_info['PrimaryVmId']
        try:
            self.logger.info("swgproxy_site_bringup: process_instance id:%s vmid:%s proxy_inst_info: %s"
                             % (str(inst_id), str(vmid), proxy_inst_info))
            inst = InstanceModel(iid=inst_id, dbh=self.dbh)
            if not inst.get_param("id"):
                self.logger.error("swgproxy_site_bringup: process_instance() inst id %s not found in instance_master."
                                  % (str(inst_id)))
                
            #if (inst.id and inst.vmid != vmid):
            if (inst.get_param("id")):
                self.logger.info("swgproxy_site_bringup: process_instance() inst id %s updating in instance_master."
                                  % (str(inst_id)))
                old_lb_detail = inst.get_param("lb_details")
                self.logger.info("swgproxy_site_bringup: process_instance() old lb_details: %s" % old_lb_detail)
                inst.set_param("vmid", vmid)
                inst.set_param("mgt_ip", proxy_inst_info['PrimaryMgtIp'])
                inst.set_param("pvt_ip", proxy_inst_info['PrimaryPvtIp'])
                inst.set_param("extrn_ip", proxy_inst_info.get('PrimaryExtrnIp', None))
                inst.set_param("public_ip", proxy_inst_info.get('EIP', None))
                inst.set_param("lb_details", proxy_inst_info['lb_details'])
                # only set loopback_ip for old arch
                region_id = inst.get_param("compute_region_idx")
                custid = inst.get_param("custid")
                cloud_provider  = gpcs_get_cloud_type_from_region_idx(self.dbh, region_id, custid=custid)
                if not self.gpcs_envoy_outside_panos_val(custid, region_id, cloud_provider, self.node_type, alt_node_type=self.alt_node_type):
                    self.logger.info(f"old arch setting loopback_ip for custid {custid}")
                    inst.set_param("loopback_ip", proxy_inst_info['LoopBackIp'])
                else:
                    self.logger.info(f"new arch skip setting loopback_ip for custid {custid}")

                inst.set_param("native_machine_type", proxy_inst_info.get('PrimaryMachineType'))
                inst.set_param("state", 1)
                inst.save()

                # send notification to ADEM Service
                if is_adem_notification_enabled(self.dbh, self.logger):
                    notify_adem_service(inst, self.dbh, self.logger)

                # Now we need to update the dns manager to make sure entry is added/updated.
                if inst.get_param("node_type") == NODE_TYPE_SWG_PROXY or (inst.get_param("node_type") == NODE_TYPE_BI_NH_PROXY and inst.get_param("alt_node_type") == NODE_TYPE_UDA):
                    if (inst.get_param("is_dynamic_instance") == 0 and inst.get_param("is_pinned_instance") == 1
                            and inst.get_param("lb_details") != old_lb_detail):
                        ret = inst.process_dns_change_for_swg_proxy("insert")
                        if ret == False:
                            success = False
                            err_msg = ("Procesing SWG Proxy DNS change for insert failed for instance id %s !!!" % (str(inst.get_param("id"))))
                            raise Exception(err_msg)
                    else:
                        self.logger.info(f"Not calling dns insert function as its not a primary instance id: "
                                         f"{str(inst.get_param('id'))}, node_type: {str(inst.get_param('node_type'))},"
                                         f" is_dynamic_node: {inst.get_param('is_dynamic_instance')}, "
                                         f" is_pinned_instance: {inst.get_param('is_pinned_instance')}, "
                                         f"lb_details {inst.get_param('lb_details')}")
            success = True
        except Exception as E:
            self.logger.error(
                "swgproxy_site_bringup: process_instance() failed. exception %s %s" %
                (str(E.args), str(traceback.print_exc())))
        finally:
            return success

    def envoy_outside_pan_bool(self, val):
        str_to_bool_dict = {"0": False, "1": True}
        if val in str_to_bool_dict:
            return str_to_bool_dict[val]
        else:
            return False

    # 1. Check if a row for given customer, region and cloud provider exists in table
    # 'cust_epaas_config' and return the value from column 'eproxy_outside_panos' ELSE
    # 2. Check if a row exists for customer and cloud provider with region idx '0'
    # in 'cust_epaas_config' table then return the value from column 'eproxy_outside_panos' ELSE
    # 3. Return the value from 'orch_cfg' table where the name of the field is "eproxy_outside_panos"
    def gpcs_envoy_outside_panos_val(self, custid, region_idx, cloud_provider, node_type, alt_node_type=-1):
        str_to_bool_dict = {"0": False, "1": True}
        envoy_outside_pan = "0"
        self.logger.info(f"Checking if eproxy needs to be outside for alt_node_type={alt_node_type} node_type={node_type} region_idx={region_idx}")
        if node_type == NODE_TYPE_BI_NH_PROXY:
            self.logger.info("exproxy is always outside for BINHP node")
            return True
        elif cloud_provider == PROVIDER_OCI:
            self.logger.info("exproxy is always outside for EP in OCI")
            return True
        cust_ep_cfg = CustEpaasConfigModel(self.dbh, custid, region_idx, cloud_provider, node_type=node_type, alt_node_type=alt_node_type)
        if cust_ep_cfg.get_entry():
            val = cust_ep_cfg.get_param("eproxy_outside_panos")
            if val != None and len(val) > 0:
                self.logger.info("gpcs_envoy_outside_panos_val: %s for custid: %d, region_idx: %d , cloud_provider: %s node_type: %s , alt_node_type %s" %(val, custid, region_idx, cloud_provider, node_type, alt_node_type))
                return self.envoy_outside_pan_bool(val)

        cust_ep_cfg.set_param("compute_region_id", 0)
        if cust_ep_cfg.get_entry():
            val = cust_ep_cfg.get_param("eproxy_outside_panos")
            if val != None and len(val) > 0:
                self.logger.info("gpcs_envoy_outside_panos_val: %s for custid: %d, region_idx: 0, cloud_provider: %s node_type: %s , alt_node_type %s" %(val, custid, cloud_provider, node_type, alt_node_type))
                return self.envoy_outside_pan_bool(val)
        orch_cfg = OrchCfgModel(self.dbh)
        if orch_cfg.valid is True:
            cfg = orch_cfg.fields
            if 'eproxy_outside_panos' in cfg:
                val = cfg['eproxy_outside_panos']
                if val != None and len(val) > 0:
                    self.logger.info("gpcs_envoy_outside_panos_val: %s from orch_cfg for custid: %d, region_idx: %d , cloud_provider: %s node_type: %s , alt_node_type %s" %(val, custid, region_idx, cloud_provider, node_type, alt_node_type))
                    return self.envoy_outside_pan_bool(val)
        self.logger.info("returning default gpcs_envoy_outside_panos_val : %s for custid: %d, region_idx: %d , cloud_provider: %s node_type: %s , alt_node_type %s" %(envoy_outside_pan, custid, region_idx, cloud_provider, node_type, alt_node_type))
        return self.envoy_outside_pan_bool(envoy_outside_pan)

    #get cnat params for the tenant
    def gpcs_get_cnat_params(self, orch_cfg, custid, region_idx, cloud_provider, node_type, alt_node_type=-1):
        cnat_dict = {}
        try:
            if not cloud_provider:
                cloud_provider = gpcs_get_cloud_type_from_region_idx(self.dbh, region_idx, custid=custid)
                if cloud_provider != PROVIDER_GCP:
                    self.logger.info("Cloud Nat supported only for GCP, returning empty dict")
                    return cnat_dict

            cnat_enabled = None
            cnat_min_count = None
            cnat_max_count = None
            cnat_vm_min_port = None
            cnat_vm_max_port = None
            ip_range = None
            cnat_ntuple_hash = None
            cnat_public_ips = None
            cnat_mon_enabled = None
            cnat_exclsv_enabled = None

            if node_type != NODE_TYPE_SWG_PROXY:
                self.logger.info("Cloud Nat supported only for SWG PROXY, returning empty dict")
                return cnat_dict

            self.logger.info(f"Checking cnat_params for alt_node_type={alt_node_type} node_type={node_type}"
                             f" region_idx={region_idx}")

            cust_ep_cfg = CustEpaasConfigModel(self.dbh, custid, region_idx, cloud_provider, node_type=node_type,
                                               alt_node_type=alt_node_type)
            if cust_ep_cfg.get_entry():
                cnat_enabled = cust_ep_cfg.get_param("cnat_enabled")
                cnat_min_count = cust_ep_cfg.get_param("cnat_min_count")
                cnat_max_count = cust_ep_cfg.get_param("cnat_max_count")
                cnat_vm_min_port = cust_ep_cfg.get_param("cnat_vm_min_port")
                cnat_vm_max_port = cust_ep_cfg.get_param("cnat_vm_max_port")
                ip_range = cust_ep_cfg.get_param("ipCidrRange")
                cnat_ntuple_hash = cust_ep_cfg.get_param("cnat_ntuple_hash")
                cnat_public_ips = cust_ep_cfg.get_param("cnat_public_ips")
                cnat_mon_enabled = cust_ep_cfg.get_param("cnat_mon_enabled")
                cnat_exclsv_enabled = cust_ep_cfg.get_param("cnat_exclsv_enabled")

            cust_ep_cfg.set_param("compute_region_id", 0)
            if cust_ep_cfg.get_entry():
                if cnat_enabled not in ('0', '1'):
                    cnat_enabled = cust_ep_cfg.get_param("cnat_enabled")
                if not cnat_min_count or cnat_min_count == 'None':
                    cnat_min_count = cust_ep_cfg.get_param("cnat_min_count")
                if not cnat_max_count or cnat_max_count == 'None':
                    cnat_max_count = cust_ep_cfg.get_param("cnat_max_count")
                if not cnat_vm_min_port or cnat_vm_min_port == 'None':
                    cnat_vm_min_port = cust_ep_cfg.get_param("cnat_vm_min_port")
                if not cnat_vm_max_port or cnat_vm_max_port == 'None':
                    cnat_vm_max_port = cust_ep_cfg.get_param("cnat_vm_max_port")
                if not ip_range or ip_range == 'None':
                    ip_range = cust_ep_cfg.get_param("ipCidrRange")
                if not cnat_ntuple_hash or cnat_ntuple_hash == 'None':
                    cnat_ntuple_hash = cust_ep_cfg.get_param("cnat_ntuple_hash")
                if not cnat_mon_enabled not in ('0', '1'):
                    cnat_mon_enabled = cust_ep_cfg.get_param("cnat_mon_enabled")
                if cnat_exclsv_enabled not in ('0', '1'):
                    cnat_exclsv_enabled = cust_ep_cfg.get_param("cnat_exclsv_enabled")


            if cnat_enabled not in ('0', '1'):
                cnat_enabled = orch_cfg.get("cnat_enabled", '0')
            if not cnat_min_count or cnat_min_count == 'None':
                cnat_min_count = orch_cfg.get("cnat_min_count", 1)
            if not cnat_max_count or cnat_max_count == 'None':
                cnat_max_count = orch_cfg.get("cnat_max_count", 16)
            if not cnat_vm_min_port or cnat_vm_min_port == 'None':
                cnat_vm_min_port = orch_cfg.get("cnat_vm_min_port", 512)
            if not cnat_vm_max_port or cnat_vm_max_port == 'None':
                cnat_vm_max_port = orch_cfg.get("cnat_vm_max_port", 32768)
            if not ip_range or ip_range == 'None':
                ip_range =orch_cfg.get("ipCidrRange", '/32')
            if not cnat_ntuple_hash or cnat_ntuple_hash == 'None':
                cnat_ntuple_hash = orch_cfg.get("cnat_ntuple_hash", 2)
            if not cnat_mon_enabled not in ('0', '1'):
                cnat_mon_enabled = orch_cfg.get("cnat_mon_enabled", '1')
            if cnat_exclsv_enabled not in ('0', '1'):
                cnat_exclsv_enabled = orch_cfg.get("cnat_exclsv_enabled", '0')
            if cnat_public_ips == 'None':
                cnat_public_ips = None

            cnat_dict["cnat_enabled"] = cnat_enabled
            cnat_dict["cnat_min_count"] = cnat_min_count
            cnat_dict["cnat_max_count"] = cnat_max_count
            cnat_dict["cnat_vm_min_port"] = cnat_vm_min_port
            cnat_dict["cnat_vm_max_port"] = cnat_vm_max_port
            cnat_dict["ip_range"] = ip_range
            cnat_dict["cnat_ntuple_hash"] = cnat_ntuple_hash
            cnat_dict["cnat_public_ips"] = cnat_public_ips
            cnat_dict["cnat_mon_enabled"] = cnat_mon_enabled
            cnat_dict["cnat_exclsv_enabled"] = cnat_exclsv_enabled
        except Exception as e:
            self.logger.error(f"Error while trying to fetch cnat params for custid {custid} and region {region_idx}"
                             f"failed with exception {str(e)}")
        finally:
            return cnat_dict

    def add_salt_profile_for_ep_region(self, custid, region_idx, template_name=None, topology_wf=False, node_type=NODE_TYPE_SWG_PROXY, alt_node_type=-1):
        # Add salt_profile data to cust_epaas_config table
        try:
            cregion_name = gpcs_get_compute_region_name_from_edge_region_idx(self.dbh, region_idx, custid=custid)
            # get cloud_provider from region
            cloud_provider  = gpcs_get_cloud_type_from_region_idx(self.dbh, region_idx, custid=custid)
            if cloud_provider == None or cloud_provider == PROVIDER_UNDECIDED:
                err_msg = "Failed to get the cloud provider from region idx %s" % str(region_idx)
                raise Exception(err_msg)
            # create cust_epaas_config model object
            cust_ep_cfg = CustEpaasConfigModel(self.dbh, custid, region_idx, cloud_provider, node_type=node_type, alt_node_type=alt_node_type)

            if cust_ep_cfg.get_entry() and topology_wf:
                if cust_ep_cfg.get_param("salt_profile") and cust_ep_cfg.get_param("salt_profile") != 'None':
                    current_salt_profile = json.loads(cust_ep_cfg.get_param("salt_profile"))
                    # if no change to geneve configuration, dont rebuild salt profile
                    if (cust_ep_cfg.get_param("enable_envoy_geneve") == '1' and current_salt_profile.get('ep-enable-geneve', None) == "true") \
                        or (cust_ep_cfg.get_param("enable_envoy_geneve") == '0' and current_salt_profile.get('ep-enable-geneve', None) != "true"):
                        # handling updating slat profile incase existing value is null or empty string for ep image version
                        if current_salt_profile.get("envoy_image_version") and \
                            current_salt_profile.get("envoy_image_version") not in ('', 'None'):
                            self.logger.info("Skipping salt_profile update since salt_profile data is"
                                            "already present for custid %s and region %s and this is autoscaling"
                                            "workflow" % (str(custid), str(region_idx)))
                            return
            try:
                current_salt_profile = cust_ep_cfg.get_param("salt_profile")
                if current_salt_profile not in (None, 'None') and template_name is None:
                    current_salt_profile = json.loads(current_salt_profile)
                    template_name = current_salt_profile.get("template_name",None)
            except Exception as E:
                self.logger.info("could not fetch old template name in salt profile")
            # create salt_profile using data from orch_cfg table
            orch = OrchCfgModel(self.dbh)
            if orch.valid is False:
                raise Exception("Fatal error! cannot retrieve the orchestrator global configuration.")
            orch_cfg = orch.fields
            self.logger.info("add_salt_profile_for_ep_region: node_type %s, alt_node_type %s orch global config :%s\n" % (str(node_type), str(alt_node_type), str(orch_cfg)))

            ep_salt_profile = {}
            # Create a customer object.
            customer = CustomerModel(custid=custid, dbh=self.dbh)
            if not customer.get_param("id"):
                self.error = ("add_salt_profile_for_ep_region: Unable to find customer in "
                              "the Database with id %d" % (custid,))
                self.logger.error(self.error)
                raise Exception(self.error)
            if template_name:
                ep_salt_profile["template_name"] = template_name
            if ((cloud_provider == PROVIDER_GCP and cust_ep_cfg.get_param("upgrade_status") == "IN_PROGRESS") or \
                    (cloud_provider == PROVIDER_AWS and cust_ep_cfg.get_param("upgrade_status") == "SUCCESSFUL")) and \
                    cust_ep_cfg.get_param("target_envoy_image_version") not in (None, 'None'):
                ep_salt_profile["envoy_image_version"] = cust_ep_cfg.get_param("target_envoy_image_version")
            elif cust_ep_cfg.get_param("current_envoy_image_version") not in (None, 'None'):
                ep_salt_profile["envoy_image_version"] = cust_ep_cfg.get_param("current_envoy_image_version")
            elif cloud_provider == PROVIDER_OCI and cust_ep_cfg.get_param("target_envoy_image_version") not in (None, 'None'):
                ep_salt_profile["envoy_image_version"] = cust_ep_cfg.get_param("target_envoy_image_version")
            else:
                # NHP - use binhp_eproxy_image_version/uda_nhproxy_image_version
                # temp alt_node_type hack until UDA becomes DG
                if alt_node_type == NODE_TYPE_UDA:
                    ep_salt_profile["envoy_image_version"] = customer.get_param("uda_nhproxy_image_version")
                elif node_type == NODE_TYPE_BI_NH_PROXY:
                    ep_salt_profile["envoy_image_version"] = customer.get_param("binhp_eproxy_image_version")
                elif cloud_provider == PROVIDER_AWS and not is_swg_aws_enabled(self.logger, customer.get_param("acct_id")):
                    #only china env pulls from orch_cfg
                    ep_salt_profile["envoy_image_version"] = orch_cfg["aws_eproxy_image_version"]
                else:
                    ep_salt_profile["envoy_image_version"] = customer.get_param("eproxy_image_version")
            ep_salt_profile["project_id"] = customer.get_param("project_id")
            ep_salt_profile["envoy_image_project"] = orch_cfg["eproxy_image_project"]
            ep_salt_profile["image_project"] = orch_cfg["gcp_img_host"]
            ep_salt_profile["envoy_inital_cnt"] = orch_cfg["envoy_inital_cnt"]
            if cust_ep_cfg.get_param("ep_min_count"):
                ep_salt_profile["envoy_min_cnt"] = str(cust_ep_cfg.get_param("ep_min_count"))
            else:
                ep_salt_profile["envoy_min_cnt"] = orch_cfg["envoy_min_cnt"]
            if cust_ep_cfg.get_param("ep_max_count"):
                ep_salt_profile["envoy_max_cnt"] = str(cust_ep_cfg.get_param("ep_max_count"))
            else:
                ep_salt_profile["envoy_max_cnt"] = orch_cfg["envoy_max_cnt"]
            ep_salt_profile["envoy_traffic_port"] = orch_cfg["envoy_traffic_port"]
            ep_salt_profile["envoy_hc_port"] = orch_cfg["envoy_hc_port"]
            ep_salt_profile["envoy_disc_size_gb"] = orch_cfg["envoy_disc_size_gb"]
            if cust_ep_cfg.get_param("ep_autosclale_cpu_target"):
                ep_salt_profile["envoy_autosclale_cpu_target"] = str(cust_ep_cfg.get_param("ep_autosclale_cpu_target"))
            else:
                ep_salt_profile["envoy_autosclale_cpu_target"] = orch_cfg["envoy_autosclale_cpu_target"]
            # enables label creation in instance template for new BINH envoy deployments
            if node_type == NODE_TYPE_BI_NH_PROXY or node_type == NODE_TYPE_SWG_PROXY:
                ep_salt_profile["add_labels"] = True

            if node_type == NODE_TYPE_SWG_PROXY and cloud_provider == PROVIDER_OCI:
                ep_salt_profile["ext_lb_ipv4"] = self.get_regional_nlb_ip(custid, "OCI", region_idx)

            # create user_data used by envoy proxy
            eproxy_ud = {}
            if cloud_provider == PROVIDER_AWS and is_swg_aws_enabled(self.logger, customer.get_param("acct_id")):
                #ssh key is usually stored like admin:ssh-rsa .... in orch_cfg
                #but aws env doesn't accept keys with username in front 
                split_key = orch_cfg["envoy_ssh_key"].split(":")
                if len(split_key) == 2:
                    eproxy_ud ["ssh-keys"] = split_key[1]
                else: 
                    eproxy_ud ["ssh-keys"] = split_key[0]
            else:
                eproxy_ud ["ssh-keys"] = orch_cfg["envoy_ssh_key"]
            # Get mtls_certpkey from explicit_proxy_tenant_info table and column 'envoy_proxy_secret_id'
            ep_tenant_cfg = ExplicitProxyTenantInfoModel(dbh=self.dbh, tenant_id=customer.get_param("acct_id"))
            cdl_region = get_cdl_region_info(customer.get_param("acct_id"), self.logger)

            eproxy_ud ["mtls_certpkey"] = ""
            if node_type == NODE_TYPE_BI_NH_PROXY:
                eproxy_ud["tc_certpkey"] = ""
                eproxy_ud["tc_sni"] = ""
            if ep_tenant_cfg.get_entry():
                eproxy_ud ["mtls_certpkey"] = ep_tenant_cfg.get_param("envoy_proxy_secret_id")
                enable_tls_term_on_ep = ep_tenant_cfg.get_param("enable_tls_term_on_ep")
                if int(enable_tls_term_on_ep) == 1 or cust_ep_cfg.get_param("enable_tls_term_on_ep") == 1 \
                        or cust_ep_cfg.get_param("enable_global_nlb") == 1 or cloud_provider == PROVIDER_AWS \
                        or cloud_provider == PROVIDER_OCI:
                    eproxy_ud ["listener_proxy_protocol"] = 1
                gpname = customer.get_param("gpname").split(".")[0]
                if alt_node_type == NODE_TYPE_UDA:
                    fqdn = 'prisma-%s-nhp.%s' % (gpname, orch_cfg['uda_base_zone'])
                    eproxy_ud["tc_certpkey"] = ep_tenant_cfg.get_param("envoy_proxy_secret_id")
                    eproxy_ud["tc_sni"] = fqdn
                elif node_type == NODE_TYPE_BI_NH_PROXY:
                    fqdn = 'prisma-%s-nhp.%s' % (gpname, orch_cfg['rbi_base_zone'])
                    eproxy_ud["tc_certpkey"] = ep_tenant_cfg.get_param("envoy_proxy_secret_id")
                    eproxy_ud["tc_sni"] = fqdn
            # TODO: Recheck if this will be different project
            eproxy_ud ["secret_mgr_project"] = ep_salt_profile["image_project"]
            eproxy_ud ["cloud_provider"] = cloud_provider
            upppver = orch_cfg["upstream-proxyprotocol-version"]
            if node_type == NODE_TYPE_BI_NH_PROXY:
                upppver = orch_cfg.get("nhp-proxyprotocol-version", "v2")
            eproxy_ud ["upstream-proxyprotocol"] = upppver
            if cloud_provider != PROVIDER_OCI:
                eproxy_ud ["mgmt-interface-swap"] = "enable"
            eproxy_ud ["custid_int"] = custid
            eproxy_ud ["custid"] = customer.get_param("acct_id")
            eproxy_ud ["custsuperid"] = customer.get_param("super_acct_id")
            eproxy_ud ["svc_acct"] = customer.get_param("svc_acct")
            eproxy_ud ["pacfgds"] = orch_cfg["pa_proxy_config_discovery_fqdn"]
            eproxy_ud ["pagks"] = orch_cfg["pa_proxy_gatekeeper_fqdn"]
            eproxy_ud ["pacdls"] = orch_cfg["pa_cdl_proxy_fqdn"]
            eproxy_ud ["patgs"] = orch_cfg.get("pa_telegraph_fqdn")
            eproxy_ud["csp_id"] = customer.get_param("support_acct_id")
            eproxy_ud["pa_cs"] = self.get_cs_region(cregion_name, cloud_provider, orch_cfg)
            eproxy_ud["cdl_region"] = cdl_region
            if cloud_provider == PROVIDER_OCI:
                #on oci this has to be changed to include certpkey as prefix otherwise theres a conflict in vault
                #also ep_tenant_cfg must exist for oci so this shouldnt cause issue with Line316
                eproxy_ud ["mtls_ca"] = eproxy_ud["mtls_certpkey"]+"_"+orch_cfg["pa_proxy_services_ca_key_id"]
            else:
                eproxy_ud ["mtls_ca"] = orch_cfg["pa_proxy_services_ca_key_id"]
            eproxy_ud ["nodetype"] = node_type
            eproxy_ud ["dnsproxy"] = customer.get_param("subnet_end")
            eproxy_ud ["compute_region"] = cregion_name
            eproxy_ud ["tsgid"] = customer.get_param("tenant_service_group")
            pahcs = orch_cfg.get("pa_proxy_sinkhole_fqdn")
            eproxy_ud ["pahcs"] = pahcs
            eproxy_ud["pashs"] = ''
            if pahcs and pahcs.startswith("all."):
                eproxy_ud ["pashs"] = pahcs[4::]
            if node_type == NODE_TYPE_BI_NH_PROXY:
                eproxy_ud["servicetype"] =  f'{node_type}.{alt_node_type}'
            #### GENEVE Start
            if node_type == NODE_TYPE_SWG_PROXY:
                # TODO: change to use actual vni through lambda
                eproxy_ud ["ep-geneve-vni"] = int(customer.get_param("acct_id")) & 0xFFFFFF
                # -1 means force disabled
                if cust_ep_cfg.get_param("enable_envoy_geneve") == "1" and self.is_ep_geneve_enabled(self.dbh, custid) != -1:
                    eproxy_ud ["ep-geneve-enabled"] = "true"
                eproxy_ud ["compute_region_id"] = region_idx
            #### GENEVE END
            ep_salt_profile["user_data"] = json.dumps(eproxy_ud)
            ep_salt_profile_json = json.dumps(ep_salt_profile)
            cust_ep_cfg.set_param("salt_profile", ep_salt_profile_json)
            cust_ep_cfg.save_salt_profile()
            self.logger.info("add_salt_profile_for_ep_region, added explicit proxy salt-profile/user data for region %s" % str(region_idx))
        except Exception as ex:
            self.logger.error("Exception in add_salt_profile_for_ep_region %s" % str(ex))
            self.logger.error("add_salt_profile_for_ep_region() failed. exception %s %s" %
                (str(ex.args), str(traceback.print_exc())))

    def get_cs_region(self, cregion_name, cloud_provider, cfg):
        try:
            cloud_provider = sys_get_cloud_provider_name(cloud_provider)
            sql = ("select cs_endpoint from cs_region_mapping "
                   "where pa_compute_region = %s and cloud_provider = %s")
            params = (cregion_name, cloud_provider)
            #sql = sql % params
            self.logger.info("Query: %s" % (sql % params))
            ret, result = execute_orch_query(self.dbh, sql, params, "fetchall")
            self.logger.info(result)
            if not ret:
                self.logger.error(f"failed to fetch cs_region endpoint for region {cregion_name}")
                return None
            cs_region = result[0][0]
            if not cs_region:
                self.logger.info(f"cs_region_mapping not found for region {cregion_name}")
                return None
            else:
                return cs_region
        except Exception as e:
            self.logger.error(f"Error fetching cs_region for region {cregion_name} failed with exception {str(e.args)}")
            return None


    def ep_set_envoy_parameters(self, custid, region_idx, node_type, alt_node_type):
        try:
            self.logger.info("Updating ep parameters for custid %s, region %s and node_type %d alt_node_type %d" % (str(custid), str(region_idx), node_type, alt_node_type))
            cloud_provider = None
            cloud_provider = gpcs_get_cloud_type_from_region_idx(self.dbh, region_idx, custid=custid)
            if cloud_provider in (None, PROVIDER_UNDECIDED):
                err_msg = "Failed to get the cloud provider from custid %s and region idx %s" % (str(custid),
                                                                                                 str(region_idx))
                raise Exception(err_msg)
            # get cust_epaas_config model object
            cust_ep_cfg = CustEpaasConfigModel(self.dbh, custid, region_idx, cloud_provider, node_type=node_type, alt_node_type=alt_node_type)
            cust_ep_cfg_entry_for_0 = CustEpaasConfigModel(self.dbh, custid, 0, cloud_provider, node_type=node_type, alt_node_type=alt_node_type)
            '''
            check if cust_epaas_entry does not exist for the region and new architecture is enabled for the region
            check if cust_epaas_entry exists for region_id 0 , 
               1. if exists take enable_global_nlb value from there
               2. if not exists then set enable_global_nlb as 1
            '''
            new_cust_ep_cfg = False
            if not cust_ep_cfg.get_entry():
                new_cust_ep_cfg = True
                if self.gpcs_envoy_outside_panos_val(custid, region_idx, cloud_provider, node_type, alt_node_type=alt_node_type) \
                    and (not cust_ep_cfg_entry_for_0.get_entry() or
                        (cust_ep_cfg_entry_for_0.get_entry() and cust_ep_cfg_entry_for_0.get_param("enable_global_nlb"))) and node_type == NODE_TYPE_SWG_PROXY:
                    cust_ep_cfg.set_param("enable_global_nlb", 1)

            if node_type == NODE_TYPE_BI_NH_PROXY:
                cust_ep_cfg.set_param("eproxy_outside_panos", "1")
            elif cloud_provider == PROVIDER_OCI:
                cust_ep_cfg.set_param("eproxy_outside_panos", "1")
            orch = OrchCfgModel(self.dbh)
            if orch.valid is False:
                raise Exception("Fatal error! Cannot retrieve the orchestrator global configuration.")
            orch_cfg = orch.fields
            ep_min_count_region_0 = cust_ep_cfg_entry_for_0.get_param("ep_min_count")
            ep_max_count_region_0 = cust_ep_cfg_entry_for_0.get_param("ep_max_count")
            ep_autosclale_cpu_target_region_0 = cust_ep_cfg_entry_for_0.get_param("ep_autosclale_cpu_target")
            #Setting envoy parameters only if not present
            self.logger.info(f"Setting envoy params for custid {custid} and region {region_idx}")
            if not cust_ep_cfg.get_param("ep_autosclale_cpu_target"):
                if ep_autosclale_cpu_target_region_0 and ep_autosclale_cpu_target_region_0 != 'None':
                    cust_ep_cfg.set_param("ep_autosclale_cpu_target", ep_autosclale_cpu_target_region_0)
                else:
                    cust_ep_cfg.set_param("ep_autosclale_cpu_target", orch_cfg["envoy_autosclale_cpu_target"])
            if not cust_ep_cfg.get_param("ep_min_count"):
                if ep_min_count_region_0 and ep_min_count_region_0 != 'None':
                    cust_ep_cfg.set_param("ep_min_count", ep_min_count_region_0)
                else:
                    cust_ep_cfg.set_param("ep_min_count", orch_cfg["envoy_min_cnt"])
            if not cust_ep_cfg.get_param("ep_max_count"):
                if ep_max_count_region_0 and ep_max_count_region_0 != 'None':
                    cust_ep_cfg.set_param("ep_max_count", ep_max_count_region_0)
                else:
                    cust_ep_cfg.set_param("ep_max_count", orch_cfg["envoy_max_cnt"])
            self.logger.info(f"Setting cloud nat parameters for custid {custid} and region {region_idx}")
            cnat_min_count_0 = cust_ep_cfg_entry_for_0.get_param("cnat_min_count")
            cnat_max_count_0 = cust_ep_cfg_entry_for_0.get_param("cnat_max_count")
            cnat_vm_min_port_0 = cust_ep_cfg_entry_for_0.get_param("cnat_vm_min_port")
            cnat_vm_max_port_0 = cust_ep_cfg_entry_for_0.get_param("cnat_vm_max_port")
            ipCidrRange_0 = cust_ep_cfg_entry_for_0.get_param("ipCidrRange")
            cnat_ntuple_hash_0 = cust_ep_cfg_entry_for_0.get_param("cnat_ntuple_hash")
            cnat_mon_enabled_0 = cust_ep_cfg_entry_for_0.get_param("cnat_mon_enabled")

            if not cust_ep_cfg.get_param("cnat_min_count") or cust_ep_cfg.get_param("cnat_min_count") =='None':
                if cnat_min_count_0 and cnat_min_count_0 != 'None' :
                    cust_ep_cfg.set_param("cnat_min_count", cnat_min_count_0)
                else:
                    cust_ep_cfg.set_param("cnat_min_count", orch_cfg["cnat_min_count"])

            if not cust_ep_cfg.get_param("cnat_max_count") or cust_ep_cfg.get_param("cnat_max_count") == 'None':
                if cnat_min_count_0 and cnat_min_count_0 != 'None':
                    cust_ep_cfg.set_param("cnat_max_count", cnat_max_count_0)
                else:
                    cust_ep_cfg.set_param("cnat_max_count", orch_cfg["cnat_max_count"])

            if not cust_ep_cfg.get_param("cnat_vm_min_port") or cust_ep_cfg.get_param("cnat_vm_min_port") == 'None':
                if cnat_vm_min_port_0 and cnat_vm_min_port_0 !='None':
                    cust_ep_cfg.set_param("cnat_vm_min_port", cnat_vm_min_port_0)
                else:
                    cust_ep_cfg.set_param("cnat_vm_min_port", orch_cfg["cnat_vm_min_port"])

            if not cust_ep_cfg.get_param("cnat_vm_max_port") or cust_ep_cfg.get_param("cnat_vm_max_port") == 'None':
                if cnat_vm_max_port_0 and cnat_vm_max_port_0 != 'None':
                    cust_ep_cfg.set_param("cnat_vm_max_port", cnat_vm_max_port_0)
                else:
                    cust_ep_cfg.set_param("cnat_vm_max_port", orch_cfg["cnat_vm_max_port"])

            if not cust_ep_cfg.get_param("ipCidrRange") or cust_ep_cfg.get_param("ipCidrRange") == 'None':
                if ipCidrRange_0 and ipCidrRange_0 != 'None':
                    cust_ep_cfg.set_param("ipCidrRange", ipCidrRange_0)
                else:
                    cust_ep_cfg.set_param("ipCidrRange", orch_cfg["ipCidrRange"])

            if not cust_ep_cfg.get_param("cnat_ntuple_hash") or cust_ep_cfg.get_param("cnat_ntuple_hash") == 'None':
                if cnat_ntuple_hash_0 and cnat_ntuple_hash_0 != 'None':
                    cust_ep_cfg.set_param("cnat_ntuple_hash", cnat_ntuple_hash_0)
                else:
                    cust_ep_cfg.set_param("cnat_ntuple_hash", orch_cfg["cnat_ntuple_hash"])
            if cust_ep_cfg.get_param("cnat_mon_enabled") not in ('0', '1'):
                if cnat_mon_enabled_0 in ('0', '1'):
                    cust_ep_cfg.set_param("cnat_mon_enabled", cnat_mon_enabled_0)
                else:
                    cust_ep_cfg.set_param("cnat_mon_enabled", orch_cfg["cnat_mon_enabled"])

            if node_type == NODE_TYPE_SWG_PROXY and new_cust_ep_cfg and self.is_ep_geneve_enabled(self.dbh, custid) == 1:
                cust_ep_cfg.set_param("enable_envoy_geneve", "1")
                cust_ep_cfg.set_param("enable_panos_geneve", "1")

            if cloud_provider == PROVIDER_OCI:
                cust_ep_cfg.set_param("cnat_enabled", "1")
                cnat_public_ip_list = []
                if cust_ep_cfg.get_param("cnat_public_ips"):
                    cnat_public_ip_list = cust_ep_cfg.get_param("cnat_public_ips").split(", ")
                success, cnat_public_ip_list = self.get_cnat_ip_v2(custid, region_idx, 
                                                             int(cust_ep_cfg.get_param("cnat_min_count")),
                                                             cnat_public_ip_list)
                if not success:
                    raise Exception("Failed to provision ips.")
                cnat_public_ip_list_string = ", ".join(cnat_public_ip_list)
                cust_ep_cfg.set_param("cnat_public_ips", cnat_public_ip_list_string)
                self.logger.info(f"Cnat iplist for cloud nat = {cnat_public_ip_list_string}")

            cust_ep_cfg.save()

            return {"ok": True}
        except Exception as exc:
            self.logger.error("Exception while setting ep parameters for custid %s and region %s"
                              "with exception %s %s" %(str(custid), str(region_idx), str(exc.args), str(traceback.print_exc())))
            return {"ok": False, "err_msg": str(exc.args)}

    def perform_ip_reservation_for_cnat_gcp(self, dbh, custid, region, cnat_param_dict):
        """
        This function is used to perform ip reservation for cnat for gcp provider
        :param dbh: db handler
        :param custid: customer id
        :param region: region id
        :param cloud_nat_params: cloud nat parameters
        :return:
        """
        logger = dbh.logger
        try:
            logger.info("Trying to perform ip reservation for cnat for gcp")
            res = {"ok": False, "ip_list": []}
            cust_ep_model = CustEpaasConfigModel(dbh, custid, region, PROVIDER_GCP,
                                                             node_type=NODE_TYPE_SWG_PROXY)
            if not cust_ep_model.get_entry():
                raise Exception(f"Failed to fetch cust_epaas entry for tenant {custid} and region {region}")

            customer_model = CustomerModel(custid=custid, dbh=dbh)
            is_clean_ip_tag = customer_model.gcp_is_clean_ip_project()
            if not is_clean_ip_tag:
                logger.info("Ips created dynamically for non clean ip tenants hence skipping reservation")
                return res
            if not cnat_param_dict:
                logger.info("cloud nat feature not enabled. Skipping cloud nat ip reservation for the tenant")
                return {"ok": False, "err_msg": "Cloud nat feature not enabled"}
            logger.info("Calling function to reserve ips for cnat")
            cnat_min_count = cnat_param_dict["cnat_min_count"]
            cnat_public_ip_list = cnat_param_dict["cnat_public_ips"]
            success, cnat_public_ip_list = self.get_cnat_ip_v2(custid, region,
                                                         int(cnat_min_count),
                                                         cnat_public_ip_list)
            cnat_public_ip_list_string = ", ".join(cnat_public_ip_list)
            cust_ep_model.set_param("cnat_public_ips", cnat_public_ip_list_string)
            self.logger.info(f"Cnat iplist for cloud nat = {cnat_public_ip_list_string}")
            cust_ep_model.save()
            return {"ok": True, "ip_list": cnat_public_ip_list}
        except Exception as e:
            logger.error("Exception while getting cust_ep_model for custid %s and region %s with exception %s %s"
                         % (str(custid), str(region), str(e.args), str(traceback.print_exc())))
            return {"ok": False, "err_msg": str(e.args)}

    def ep_cust_region_salt_profile(self, custid, region_idx, node_type=NODE_TYPE_SWG_PROXY, alt_node_type=-1):
        ep_salt_profile = {}
        try:
            # get cloud_provider from region
            cloud_provider = gpcs_get_cloud_type_from_region_idx(self.dbh, region_idx, custid=custid)
            if cloud_provider == None or cloud_provider == PROVIDER_UNDECIDED:
                err_msg = "Failed to get the cloud provider from region idx %s" % str(region_idx)
                raise Exception(err_msg)
            # create cust_epaas_config model object
            cust_ep_cfg = CustEpaasConfigModel(self.dbh, custid, region_idx, cloud_provider, node_type=node_type, alt_node_type=alt_node_type)
            if cust_ep_cfg.get_entry():
                json_ep_cfg = cust_ep_cfg.get_param("salt_profile")
                self.logger.info("salt_profile json: %s for custid: %s region: %s" % (str(json_ep_cfg), str(custid), str(region_idx)))
                ep_salt_profile = json.loads(json_ep_cfg)
                if cust_ep_cfg.get_param("ep_min_count"):
                    ep_salt_profile["envoy_min_cnt"] = cust_ep_cfg.get_param("ep_min_count")
                if cust_ep_cfg.get_param("ep_max_count"):
                    ep_salt_profile["envoy_max_cnt"] = cust_ep_cfg.get_param("ep_max_count")
                if cust_ep_cfg.get_param("ep_autosclale_cpu_target"):
                    ep_salt_profile["envoy_autosclale_cpu_target"] = cust_ep_cfg.get_param("ep_autosclale_cpu_target")
                if ep_salt_profile["envoy_min_cnt"]:
                    ep_salt_profile["envoy_min_cnt"] = int(ep_salt_profile["envoy_min_cnt"])
                if ep_salt_profile["envoy_max_cnt"]:
                    ep_salt_profile["envoy_max_cnt"] = int(ep_salt_profile["envoy_max_cnt"])
                if ep_salt_profile["envoy_autosclale_cpu_target"]:
                    ep_salt_profile["envoy_autosclale_cpu_target"] = float(ep_salt_profile["envoy_autosclale_cpu_target"])
                ep_salt_profile["migrate_ep_status"] = cust_ep_cfg.get_param("migrate_ep_status")
                ep_salt_profile["enable_tls_term_on_ep"] = cust_ep_cfg.get_param("enable_tls_term_on_ep")
                ep_salt_profile["enable_global_nlb"] = cust_ep_cfg.get_param("enable_global_nlb")
        except Exception as ex:
            self.logger.error("Exception in ep_cust_region_salt_profile %s: " % str(ex))
            self.logger.error("ep_cust_region_salt_profile() failed. Exception %s %s" %
                (str(ex.args), str(traceback.print_exc())))
        finally:
            return ep_salt_profile


    def gpcs_envoy_service_timeout_for_lb(self, custid, region_idx, node_type, alt_node_type=-1):
        service_timeout = 86400
        self.logger.info(f"Checking if service_timeout for alt_node_type={alt_node_type} node_type={node_type} "
                         f"region_idx={region_idx}")
        cloud_provider = gpcs_get_cloud_type_from_region_idx(self.dbh, region_idx, custid=custid)
        cust_ep_cfg = CustEpaasConfigModel(self.dbh, custid, region_idx, cloud_provider, node_type=node_type,
                                           alt_node_type=alt_node_type)
        if cust_ep_cfg.get_entry():
            timeoutSec = cust_ep_cfg.get_param("service_timeout")
            if timeoutSec and timeoutSec not in ('None', '0'):
                self.logger.info(f"service_timeout: {timeoutSec} for custid: {custid}, region_idx: {region_idx} , "
                                 f"cloud_provider: {cloud_provider} node_type: {node_type} , "
                                 f"alt_node_type {alt_node_type}")
                return timeoutSec

        cust_ep_cfg.set_param("compute_region_id", 0)
        if cust_ep_cfg.get_entry():
            timeoutSec = cust_ep_cfg.get_param("service_timeout")
            if timeoutSec and timeoutSec not in ('None', '0'):
                self.logger.info(f"service_timeout: {timeoutSec} for custid: {custid}, region_idx: {region_idx} , "
                                 f"cloud_provider: {cloud_provider} node_type: {node_type} , "
                                 f"alt_node_type {alt_node_type}")
                return timeoutSec
        orch_cfg = OrchCfgModel(self.dbh)
        if orch_cfg.valid is True:
            cfg = orch_cfg.fields
            if 'service_timeout' in cfg:
                timeoutSec = cfg['service_timeout']
                if timeoutSec and timeoutSec not in ('None', '0'):
                    self.logger.info(f"service_timeout: {timeoutSec} from orch_cfg for custid: {custid}, "
                                     f"region_idx:{region_idx}, cloud_provider:{cloud_provider}, node_type:{node_type}"
                                     f", alt_node_type {alt_node_type}")
                    return int(timeoutSec)
        self.logger.info(f"returning default service_timeout : {service_timeout} from orch_cfg for custid: {custid}, "
                                     f"region_idx:{region_idx}, cloud_provider:{cloud_provider}, node_type:{node_type}"
                                     f", alt_node_type {alt_node_type}")
        return service_timeout

    def get_service_ip_for_country_city_cloud_swg(self, country, city, cloud_provider, service_type, service_id, region_name, acct_id, group_support_acct_id):
        try:
            args = (str(country), str(city), str(cloud_provider), acct_id, group_support_acct_id, service_type,
                    region_name, service_id)
            if None in args:
                self.logger.error("Failed to acquire IPs! Invalid args: %s" % str(args))
                return None
            if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                                       compute_region_info=region_name) == IPAMService.ENABLED:
                res = self.ipam_utils.ipam_allocate_address(match_location=region_name,
                                                            match_city=str(city),
                                                            match_country=str(country),
                                                            match_cloud=str(cloud_provider),
                                                            match_group_support_account_id=group_support_acct_id,
                                                            match_account_id=acct_id,
                                                            match_node_type=service_type,
                                                            match_count=1, set_status="active", set_service_id=service_id,
                                                            call_context="orch:get_service_ip_for_country_city_cloud_swg")
                if not res or len(res) == 0:
                    self.logger.error(f"0 IP acquire from the region {region_name}")
                    return None
                return res[0]
            else:
                sql = ["CALL service_acquire_public_ip('%s','%s','%s',%s,%s,%s,'%s','%s',@public_ip,@new_address,@address_type)" % args]
                self.logger.info("sql: %s " % str(sql))
                sql.append("SELECT @public_ip")
                sql_cmd = ";".join(sql)
                res = dbconn.execute_lambda_query(sql_cmd, {'multi_query': True})
                if not res['ok'] or not res['result']:
                    self.logger.error("Failed to acquire IPs! Failed sql: %s, %s" % (sql, res))
                    return None
                elif len(res['result']) == 0:
                    self.logger.error("0 IP acquire from the region. sql: %s" % sql)
                    return None
                else:
                    self.logger.info("IP %s acquired" % (res['result'][0][0]))
                    return res['result'][0][0]
        except Exception as e:
            self.logger.error("Unexpected error in get_service_ip_for_country_city_cloud: %s locals %s. Traceback: %s" % (str(e.args),
                                                                              str(locals()),
                                                                              str(traceback.format_exc())))
            return None

    def get_regional_nlb_ip(self, custid, cloud, compute_region_idx):
        """
        Check if nlb IP already exist (No matter if status is reserved/pre-allocated/active/NULL)
        Reserve nlb IP if necessary
        """
        active_nlb_ip_count = 0
        try:
            customer = CustomerModel(custid=custid, dbh=self.dbh)
            my_compute_region = RegionMasterModel(dbh=self.dbh, edge_location_region_id=compute_region_idx)
            acct_id = customer.get_param("acct_id")
            native_region_name = my_compute_region.get_param("native_compute_region_name")
            proxy_pfx = get_proxy_prefix(self.node_type, self.alt_node_type)
            nlb_node_type = SERVICE_TYPE_SWG_NLB_INGRESS_IP
            service_id = f"{proxy_pfx}nlb-{str(native_region_name)}-{str(acct_id)}"
            self.logger.info("Check if need to allocate NLB IP for acct_id "
                "%s in compute_region_idx %s" % (acct_id, compute_region_idx))
            if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                                        compute_region_info=native_region_name) == IPAMService.ENABLED:
                result = self.ipam_utils.ipam_update_address(match_cloud=cloud,
                                                                match_location=native_region_name,
                                                                match_account_id=acct_id,
                                                                match_node_type=nlb_node_type,
                                                                match_service_id=service_id,
                                                                set_status="active",
                                                                call_context="orch:get_regional_nlb_ip:get_nlb_ip")
                if result:
                    active_nlb_ip_count = len(result)
                if active_nlb_ip_count > 0:
                    self.logger.info("NLB service IP %s already exist for "
                                        "acct_id %s in compute_region_idx %s " %
                                        (result[0], acct_id, compute_region_idx))
                    return result[0]
            else:
                sql = ("select distinct ip, IP_type from public_ip_pool "
                        "where acct_id=%s and cloud='%s' and location='%s' "
                        "and node_type=%s and service_id='%s' " % (acct_id, cloud, native_region_name, nlb_node_type, service_id))
                self.logger.info("Query: %s" % sql)
                result = dbconn.execute_lambda_query(sql, None, logger=self.logger)
                if not result['ok']:
                    self.logger.error("Failed to find allocated NLB IP for "
                        "acct_id %s in compute_region_idx %s. "
                        "Failed SQL: %s %s" %
                        (acct_id, compute_region_idx, sql, result))
                    return None
                active_nlb_ip_count = len(result['result'])
                if active_nlb_ip_count > 0:
                    self.logger.info("NLB service IP %s(%s) already exist for "
                        "acct_id %s in compute_region_idx %s ." %
                        (result['result'][0][0], result['result'][0][1], acct_id,
                            compute_region_idx))
                    return result['result'][0][0]

            city = my_compute_region.get_param('city')
            country = my_compute_region.get_param('country')
            ip = self.get_service_ip_for_country_city_cloud_swg(country, city,
                        cloud, nlb_node_type, service_id, native_region_name, acct_id, customer.get_param("group_support_acct_id"))
            if ip:
                self.logger.info("Allocated NLB IP %s for acct_id %s in compute_region_idx %s"% (ip, acct_id, compute_region_idx))
                return ip
            else:
                self.logger.error("Failed to allocate NLB IP for acct_id %s in compute_region_idx %s"% (acct_id, compute_region_idx))
                return None

        except Exception as err:
            self.logger.error("Failed to reserve NLB IP for acct_id %s in compute_region_idx %s. %s" % (acct_id, compute_region_idx, err))
            return None

    def get_cnat_ip_v2(self, cust_id, region_id, num_cnat_ips_required, cnat_public_ip_list):
        if num_cnat_ips_required < 1:
            return True, []
        my_compute_region = RegionMasterModel(dbh=self.dbh, edge_location_region_id=region_id)
        native_region_name = my_compute_region.get_param('native_compute_region_name')
        cloud_provider = my_compute_region.get_param('cloud_provider')
        if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger, compute_region_info=native_region_name) == IPAMService.ENABLED:
            cnat_node_type = NODE_TYPE_SWG_NAT_INSTANCE #new nodetype for swg cnats
            city = my_compute_region.get_param('city')
            country = my_compute_region.get_param('country')
            customer = CustomerModel(custid=cust_id, dbh=self.dbh)
            acct_id = customer.get_param("acct_id")
            active_ip_list = self.ipam_utils.ipam_fetch_address(location=native_region_name,
                                                                city=str(city),
                                                                country=str(country),
                                                                cloud=str(cloud_provider),
                                                                account_id=acct_id,
                                                                node_type=cnat_node_type,
                                                                service_id=CLOUD_NAT_SERVICE_ID,
                                                                status = "active")
            ip_addrs_needed = num_cnat_ips_required - len(active_ip_list)
            if ip_addrs_needed > 0:
                self.logger.info(f"Need additional {ip_addrs_needed} ips from ipam for region {native_region_name}")
                res = self.ipam_utils.ipam_allocate_address(match_location=native_region_name,
                                                            match_city=str(city),
                                                            match_country=str(country),
                                                            match_cloud=str(cloud_provider),
                                                            match_account_id=acct_id,
                                                            match_node_type=cnat_node_type,
                                                            match_count=ip_addrs_needed,
                                                            set_status="active", 
                                                            set_service_id=CLOUD_NAT_SERVICE_ID,
                                                            call_context="orch:get_cnat_ip_v2")
                active_ip_list = active_ip_list + res
                self.logger.info(f"Allocated {len(res)} ips from ipam for region {native_region_name}")
            elif ip_addrs_needed < 0:
                self.logger.info(f"Need to return {-ip_addrs_needed} ips to ipam for region {native_region_name}")
                ips_to_return = active_ip_list[ip_addrs_needed:] #get the ips to return
                ips_to_return = [x+"/32" for x in ips_to_return]
                del active_ip_list[ip_addrs_needed:] #remove the ipaddrs from list
                res = self.ipam_utils.ipam_update_address(match_address=ips_to_return,
                                                          set_service_id='',
                                                          set_status="reserved", 
                                                          call_context="orch:get_cnat_ip_v2")
            if not active_ip_list or len(active_ip_list) == 0:
                return False, []
            return True, active_ip_list
        elif not cnat_public_ip_list or len(cnat_public_ip_list) < num_cnat_ips_required:
            success, ip_list = reserve_ips_for_cloud_nat_old(self.dbh, cust_id, region_id, num_cnat_ips_required)
            if success:
                if cnat_public_ip_list is None:
                    cnat_public_ip_list = []
                #only store ips if nothing went wrong during ip reservation
                cnat_public_ip_list = cnat_public_ip_list + [item for item in ip_list if item not in cnat_public_ip_list]
            # for now we will do nothing if we have more ips than needed. they may be required in the future
            # they will be returned when deboarding anyways
            # elif len(cnat_public_ip_list) > num_cnat_ips_required:
                #if release_cnat_ip_from_public_ip_pool(cust_id, cnat_public_ip_list[num_cnat_ips_required:], dbh.logger):
                    # cnat_public_ip_list = cnat_public_ip_list[:num_cnat_ips_required]
            return success, cnat_public_ip_list
        elif cnat_public_ip_list and len(cnat_public_ip_list) > num_cnat_ips_required and cloud_provider.lower() != "oci":
            self.logger.info(f"Releasing redundant ips from rds as ipam is not enabled for custid {cust_id} and region {region_id}")
            success, cnat_public_ip_list = release_redundant_ips_for_tenant_region(cust_id, region_id, cloud_provider, self.dbh)
            return True, cnat_public_ip_list
        self.logger.error("No need to get new ip.")
        return True, []

    def is_ep_geneve_enabled(self, dbh, custid):
        """
        check if geneve is enabled
        """
        logger = dbh.logger
        ret = 0
        AWS_PARTITION = get_aws_partition()
        try:
            orch = OrchCfgModel(dbh)
            orch_cfg = orch.fields
            logger.info(f"Checking ep_geneve flag for custid {custid}")
            cm = CustomerModel(custid=custid, dbh=dbh)
            tenantid = str(cm.get_param("acct_id"))
            lambda_arn = (f"arn:{AWS_PARTITION}:lambda:{orch_cfg['region']}:{orch_cfg['acct_id']}:function:getFeatureFlags")
            lambda_client = boto3.client('lambda', region_name=orch_cfg['region'])
            lambda_payload = dict()
            lambda_payload["tenantid"] = tenantid
            lambda_payload["app"] = "saas_infra"
            current_version = orch_cfg["version"]
            
            current_version = current_version.split('-')[0]
            rel_version_details = current_version.split('.')
            while len(rel_version_details) > 3:
                rel_version_details.pop()
            current_version = '.'.join(rel_version_details)
            logger.info(f"Current release version is {current_version}")

            lambda_payload["release_version"] = current_version
            response = lambda_client.invoke(
                    FunctionName=lambda_arn,
                    InvocationType='RequestResponse',
                    LogType='None',
                    Payload=json.dumps(lambda_payload),
                    )
            resp = json.loads(response["Payload"].read())
            logger.info(f"Response recived for feature flag for orchestrator ep_geneve is {resp}")
            ret_val = resp.get(tenantid, {}).get("ep_geneve", {}).get("value", 0)
            if isinstance(ret_val, str):
                ret = int(ret_val)
            else:
                ret = ret_val
        except Exception as e:
            dbh.logger.error(e)
        finally:
            return ret
