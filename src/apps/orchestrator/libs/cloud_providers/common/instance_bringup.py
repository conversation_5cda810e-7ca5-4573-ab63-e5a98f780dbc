# Autho:r Tejas P. 10/13/2018
import traceback
import json
import oci

from libs.model.instancemodel import InstanceModel
from utils.Exceptions import InvalidInstanceIdException, InvalidInterfaceIpv6ListException, InvalidInterfaceIpv6ListJsonException, DbUpdateException
from utils.oci_sdk.oci_sdk_api import OCISdkUtility
from libs.common.utils import upgradesvc_notification_send, send_notification_to_sase_svc
import libs.model.custnodemodel as CN
from libs.common.shared.sys_utils import *
from libs.cloud_providers.common.instance_trigger_update import trigger_update_for_instance_changes
from libs.cloud_providers.common.probevm_bringup import probevm_bringup
from libs.cloud_providers.common.utils import send_sns_msg_for_r53_update, send_sns_msg_for_cnat_fw_egress_rule_update
import libs.cloud_providers.common.ip_management.ipv4.ip_mgmt as ipv4_mgmt
import libs.cloud_providers.common.ip_management.ipv6.aws.ipv6_mgmt_1 as aws_v6_mgmt
from libs.common.shared.grpc.proto.avisarpb.avisar_pb2_client import \
                              INST_MGMT_AWS_IP_MGMT_BYOIP_ALLOC_FAILURE, \
                              METRIC_SEVERITY_CRITICAL
from libs.apis.api_utils import is_env_fedramp_il5
from libs.apis.region_master_api import get_cloud_native_location_name_from_region_id
from libs.common.shared.utils import IPAMService, is_ipam_service_enabled
from ipam_factory import create_ipam_utils
from libs.cloud_providers.common.exceptions import UnableToAllocatedReservedPublicIpException
from libs.model.custEpaasConfigModel import CustEpaasConfigModel
from libs.cloud_providers.common.adem_fw_notify import is_adem_notification_enabled, notify_adem_service

class instance_bringup_processing:
    def __init__(self, dbh, avctx = None):

        if dbh == None or dbh.logger == None:
            return
        self.finished_processing_seq_nr = None
        self.logger = dbh.logger
        self._cust_id = None
        self._region_idx = None
        self._stack_name = None
        self._cloud_provider = None
        self.avctx = avctx

        self.dbh = dbh
        self._primary_id = None
        self._primary_vmid = None
        self._primary_mgt_ip = None
        self._primary_pvt_ip = None
        self._primary_pvt_ip_id = None
        self._primary_public_ip = None
        self._reserved_public_ip_id = None
        self._primary_public_ip_dp2 = None
        self._primary_public_ipv6 = None
        self._primary_public_ipv6_prefix_len = None
        self._primary_public_ipv6_dp2 = None
        self._primary_public_ipv6_prefix_len_dp2 = None
        self._public_cloud_template_ip = None
        self._primary_nlb_domain_details = None
        self._primary_nlb_domain_details_v6 = None
        self._primary_egress_ip_list = None
        self._primary_egress_ipv6_list = None
        self._primary_nlb_public_ipv6 = None
        self._primary_interface_ip_list = None
        self._primary_interface_ipv6_list = None
        self._primary_machine_type = None
        self._primary_sase_svc_ilb_v4_address = None
        self._primary_rn_int_onramp_ilb_v4_address = None
        self._primary_rn_int_onramp_ilb_v6_address = None
        self._is_okyo_mbn = None
        self._sase_ip = None
        self._alias_ip_ranges = None
        self._alias_ip = None
        self._extrn_ip = None
        self._dp_private_vip = None
        self._vgw_private_vip = None
        self._gw_gre_peer_ip = None
        self._gw_gre_bgp_asn = None
        self._gw_gre_bgp_range = None
        self._primary_ingress_ip_mappings = None
        self._primary_egress_ip_mappings = None
        self._primary_pripub_ip_mappings = None
        self._primary_colo_interface_ip_address = None
        self._region_colo_ilbs_dict = {}

        self._secondary_id = None
        self._secondary_vmid = None
        self._secondary_mgt_ip = None
        self._secondary_pvt_ip = None
        self._secondary_public_ip = None
        self._secondary_machine_type = None
        self._secondary_pvt_ip2 = None
        self._secondary_vgw_vip = None
        self._secondary_gw_gre_peer_ip = None
        self._secondary_gw_gre_bgp_asn = None
        self._secondary_gw_gre_bgp_range = None
        self._secondary_colo_interface_ip_address = None
        self.oci_sdk_utility = None

        # Handle multiple NLB ingress public to private IP mappings for IIR disabled
        # scenarios for BF tenants and edge locations onboarding
        self._reserved_public_ip_ids = {}
        self._primary_pvt_ip_ids = {}

        # Holder for OCI NLB interface_ipv6_list
        self._nlb_interface_ipv6_list = {}

    def set_and_publish_avctx_byoip_alloc_error(self, err_msg, reset_ctx=False):
        if self.avctx:
            self.avctx.set_ctx(metric_severity=METRIC_SEVERITY_CRITICAL,
                               metric_state=err_msg,
                               metric_type=INST_MGMT_AWS_IP_MGMT_BYOIP_ALLOC_FAILURE)
            self.avctx.publish_event(reset_ctx=reset_ctx)
        else:
            self.logger.error("Failed to publish message, avctx not found.")

    def instance_management_refresh_event(self, old_region_idx):
        if old_region_idx:
            pass

    def process_instance_primary(self):
        from libs.cloud_providers.aws.instance_manager.aws_bringup import get_aws_il5_sc_instance_vips, \
            process_aws_il5_sc_instance_vips
        from libs.cloud_providers.aws.instance_manager.aws_tgw import il5_tgw_event_add_sc_instance, \
            il5_tgw_event_delete_sc_instance
        success = True
        self.error = ""
        send_update = False
        inst = None
        try:
            self.logger.info("process_instance_primary()")
            inst = InstanceModel(iid=self._primary_id, dbh=self.dbh)
            if not inst.get_param("id"):
                # TBD:Message shouldnt say Fatal here?
                self.logger.error("!!! Fatal !!!! process_instance_primary Instance with ID %s not found in database."
                                  % (str(self._primary_id)))
                raise Exception("!!! Fatal !!!! process_instance_primary Instance with ID %s not found in database."
                                  % (str(self._primary_id)))

            nlb_ip_address_update_done = False
            skip_lbaas_ip_update = False
            if inst.get_param("id") and (inst.get_param("state") == 0 or inst.get_param("state") == 1 or
                                (inst.get_param("state") == -2 and
                                 inst.get_param("vmid") != self._primary_vmid)):
                self._is_okyo_mbn = True if inst.get_param("gw_capabilities") == 2 and inst.get_param("node_type") == NODE_TYPE_GP_GATEWAY else False
                probe_vm_processor = None
                # Allocate public IP for AWS instances
                if self._cloud_provider == PROVIDER_AWS:
                    if not inst.get_param("public_ip"):
                        self.logger.info("Reserving IPs for account %s, "
                                         "cluster %s in AWS" %
                                         (str(inst.get_param("acct_id")), str(self._primary_id)))
                        aws_ip_mgmt = ipv4_mgmt.AWSIPHandler(
                            self.dbh, self._primary_id, inst.get_param("acct_id"))

                        ret, public_ip, err_msg = aws_ip_mgmt.allocate_public_IP_via_public_IP_pool(self._public_cloud_template_ip)
                        if ret == False or err_msg != None:
                            # Send an avisar Notification
                            self.set_and_publish_avctx_byoip_alloc_error(err_msg=
                                                                    f"{err_msg} Instance ID: {inst.get_param('id')}")
                        inst.set_param("public_ip", public_ip)

                        # save for inst object will overwrite
                        # allocate_public_IP_via_public_IP_pool sql call
                        inst.set_param("use_PBF", aws_ip_mgmt.use_PBF)
                        self._primary_public_ip = inst.get_param("public_ip")
                    else:
                        # it already has ip address in public_ip field
                        self.logger.info("AWS instance %s already has IP %s" %
                                         (str(self._primary_id), str(inst.get_param("public_ip"))))
                        self._primary_public_ip = inst.get_param("public_ip")

                    # IPv6 assignment:-
                    aws_ipv6_mgmt_ref = aws_v6_mgmt.IPV6_Handler(dbh=self.dbh, acct_id=inst.get_param("acct_id"))
                    ret, ipv6_address = aws_ipv6_mgmt_ref.get_ipv6_address_from_salt_profile(inst.get_param("id"))
                    if ret == False:
                        success = False
                        raise Exception("Failed to retrieve IPv6 address for instance id %s" % str(inst.get_param("id")))
                    if inst.get_param("public_ipv6") == ipv6_address:
                        self.logger.info("Public IPv6 address is already set to %s" % str(ipv6_address))
                    else:
                        ret = aws_ipv6_mgmt_ref.assign_ipv6_address_to_interface(instance_id=inst.get_param("id"),
                                                                           vmid=self._primary_vmid,
                                                                           ipv6_address=ipv6_address)
                        if ret == False:
                            raise Exception("Failed to assign IPv6 address %s to the interface for vmid %s" %
                                                                            (str(ipv6_address), str(self._primary_vmid)))
                        inst.set_param("public_ipv6", ipv6_address)

                    self.logger.info("node_type: %s, is_instance_behind_nlb: %s, _primary_nlb_domain_details: %s", \
                                     str(inst.get_param("node_type")), str(inst.get_param("is_instance_behind_nlb")), str(self._primary_nlb_domain_details))
                    # If this is SWG Proxy node type, we need to get the LB dns name and IP addreses.
                    if inst.get_param("node_type") in (NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY):
                        inst.set_param("lb_details", self._primary_nlb_domain_details)
                    elif inst.get_param("node_type") == NODE_TYPE_GP_GATEWAY and inst.get_param("is_instance_behind_nlb") == 1:
                        inst.set_param("lb_details", self._primary_nlb_domain_details)
                    elif inst.get_param("node_type") == NODE_TYPE_PROBE_VM:
                        inst.set_param("vm_status", 1)
                        # Explictily save VM Status
                        ret = inst.save_vm_status(1)
                        if not ret:
                            self.logger.error("Failed to set vm_status = 1 for instance")
                        probe_vm_processor = probevm_bringup(self.dbh)

                    if inst.get_param("node_type") in [NODE_TYPE_SERVICE_CONN]:
                        inst.set_param("pvt_ip2", self._dp_private_vip)
                        inst.set_param("vgw_vip", self._vgw_private_vip)
                        if (is_env_fedramp_il5(self.logger) == True):
                            region_name = inst.get_param("compute_region_name")
                            primary_vips = get_aws_il5_sc_instance_vips(self.logger, region_name, self._primary_vmid)
                            secondary_vips = get_aws_il5_sc_instance_vips(self.logger, region_name,
                                                                          self._secondary_vmid)

                            dp_vip, vgw_vip = process_aws_il5_sc_instance_vips(self.logger, primary_vips,
                                                                               secondary_vips,
                                                                               self._dp_private_vip,
                                                                               self._vgw_private_vip)
                            log_msg = f"IL5 SC primary instance dp_vip: {dp_vip} vgw_vip:{vgw_vip}"
                            self.logger.info(log_msg)
                            # set the values of the following to the derived dp_vip and vgw_vip
                            self._dp_private_vip = dp_vip
                            self._vgw_private_vip = vgw_vip
                            log_msg = f"setting vips for primary instance, pvt_ip2/dp_vip:{self._dp_private_vip} vgw_vip:{self._vgw_private_vip}"
                            self.logger.info(log_msg)

                            # TGW peer connect
                            nameTag = f"pa_{str(self._primary_id)}"
                            bgpAsnId = 65534
                            res = il5_tgw_event_add_sc_instance(self.logger, region_name, self._primary_vmid, vgw_vip,
                                                                None, None, bgpAsnId, nameTag)
                            if res is None or len(res) == 0:
                                self.logger.error(
                                    f"Failed to create/get TGW Peer connect for this SC cluster {str(self._primary_id)}")
                            else:
                                self._gw_gre_peer_ip = res['TgwAddress']
                                self._gw_gre_bgp_asn = res['TGWBGPAsnId']
                                FIRST_CIDR_BLOCK = 0
                                self._gw_gre_bgp_range = str(res['InsideCidrBlocks'][FIRST_CIDR_BLOCK])

                            log_msg = f"setting TGW connect peer info of primary instance peer_ip: {self._gw_gre_peer_ip}, " \
                                      f"BGP ASN: {self._gw_gre_bgp_asn}, BGP range: {self._gw_gre_bgp_range}"
                            self.logger.info(log_msg)

                            inst.set_param("pvt_ip2", self._dp_private_vip)
                            inst.set_param("vgw_vip", self._vgw_private_vip)
                            inst.set_param("gw_gre_peer_ip", self._gw_gre_peer_ip)
                            inst.set_param("gw_gre_bgp_asn", self._gw_gre_bgp_asn)
                            inst.set_param("gw_gre_bgp_range", self._gw_gre_bgp_range)
                elif self._cloud_provider == PROVIDER_GCP or self._cloud_provider == PROVIDER_OCI:
                    self.logger.info(f"process_instance_primary: The _reserved_public_ip_id is {self._reserved_public_ip_id}")
                    self.logger.info(f"process_instance_primary: The _reserved_public_ip_ids is {self._reserved_public_ip_ids}")
                    self.logger.info(f"process_instance_primary: The _primary_pvt_ip_ids is {self._primary_pvt_ip_ids}")
                    # If the provider is OCI the reserved public ip ocid
                    # is not empty then assign the reserved public ip
                    # if it's not a nat instance or GP Gateway
                    if (
                        self._cloud_provider == PROVIDER_OCI and
                        ((self._reserved_public_ip_id and len(self._reserved_public_ip_id) > 0) or (self._reserved_public_ip_ids and len(self._reserved_public_ip_ids) > 0)) and
                        inst.get_param("node_type") not in [NODE_TYPE_NAT_INSTANCE, NODE_TYPE_GP_GATEWAY]):
                         try:
                             if self.oci_sdk_utility is None:
                                 region_id = inst.get_param("compute_region_idx")
                                 self.logger.info(f"The region_id is {region_id}")
                                 native_compute_region_name = get_cloud_native_location_name_from_region_id(self.dbh, region_id)
                                 self.logger.info(
                                     f"The native_compute_region_name of the region_id {region_id} is {native_compute_region_name}"
                                 )
                                 self.oci_sdk_utility = OCISdkUtility(native_compute_region_name=native_compute_region_name)
                                 self.logger.info(f"process_instance_primary: The reserved_public_ip_id of the reserved public ip {inst.get_param('public_ip')} is {self._reserved_public_ip_id}")
                                 self.logger.info(f"process_instance_primary: The primary_pvt_ip_id is {self._primary_pvt_ip_id}")
                                 self.logger.info(f"process_instance_primary: Map for Regions -> Reserved public IP OCIDs is {self._reserved_public_ip_ids}")
                                 self.logger.info(f"process_instance_primary: Map for Regions -> Private IP OCIDs is {self._primary_pvt_ip_ids}")
                                 if inst.get_param("node_type") in [NODE_TYPE_SERVICE_CONN] and self._reserved_public_ip_id and self._primary_pvt_ip_id:
                                     self.logger.info(f"The reserved_public_ip_id of the reserved public ip {inst.get_param('public_ip')} is {self._reserved_public_ip_id} for {inst.get_param('name')} and private ip ID {self._primary_pvt_ip_id}")
                                     # Associate the reserved public IP
                                     response = self.oci_sdk_utility.update_public_ip(self._reserved_public_ip_id, self._primary_pvt_ip_id, self.logger)
                                 # Associate the reserved public IPs to private IP OCIDs
                                 # this is for handling IIR disabled scenarios for BF tenants where NLB will have multiple public to private IP associations
                                 # each associating to a compute or an edge region
                                 for region, private_ip_ocid in self._primary_pvt_ip_ids.items():
                                     reserved_public_ip_ocid = self._reserved_public_ip_ids.get(region, None)
                                     self.logger.info(f"process_instance_primary: Associating the reserved public IP OCID {reserved_public_ip_ocid} with the private IP OCID {private_ip_ocid}")
                                     if reserved_public_ip_ocid:
                                         try:
                                             ret = self.oci_sdk_utility.is_public_ip_associated_with_private_ip(reserved_public_ip_ocid, private_ip_ocid, self.logger)
                                             self.logger.info(f"process_instance_primary: Return is {ret} for {reserved_public_ip_ocid} to {private_ip_ocid} mapping")
                                             if not ret:
                                                 response = self.oci_sdk_utility.update_public_ip(reserved_public_ip_ocid, private_ip_ocid, self.logger)
                                                 ret = self.oci_sdk_utility.is_public_ip_associated_with_private_ip(reserved_public_ip_ocid, private_ip_ocid, self.logger)
                                                 self.logger.info(f"process_instance_primary: Post update_public_ip call. Return is {ret} for {reserved_public_ip_ocid} to {private_ip_ocid} mapping")
                                         except Exception as e:
                                             self.logger.error(f"process_instance_primary: Error while associating {reserved_public_ip_ocid} to {private_ip_ocid} -> {str(e)}")
                             else:
                                self.logger.info(
                                    f"There is no need to replace the public ipaddress with the reserved public "
                                    f"ip as the self.oci_sdk_utility is already initialized"
                                )
                         #Forced to catch the generic exception here as the
                         #existing api like get_cloud_native_location_name_from_region_id can throw
                         #a generic exception
                         except Exception as e:
                             self.logger.error(
                                 f"Exception occured when trying to update the reserved ip ocid "
                                 f"{self._reserved_public_ip_id} for the node type {inst.get_param('node_type')}. Reason: {str(e)}"
                             )
                             raise UnableToAllocatedReservedPublicIpException(f"Cannot allocate the reserved public ip {inst.get_param('public_ip')}")
                    else:
                        self.logger.info(
                            f"No need to replace the public ipaddress with the reserved public "
                            f"address as the condition was not met. The cloud provider "
                            f"is {self._cloud_provider}, reserved public ip ocid is {self._reserved_public_ip_id},"
                            f"node_type is {inst.get_param('node_type')}"
                        )

                    if not inst.get_param("public_ip"):
                        inst.set_param("public_ip", self._primary_public_ip)
                        self.logger.info("set public_ip field in "
                                         "instance_master as %s" %
                                        str(inst.get_param("public_ip")))
                        # notification to ADEM service for new instance creation
                        if is_adem_notification_enabled(self.dbh, self.logger):
                            notify_adem_service(inst, self.dbh, True, self.logger)
                    else:
                        self.logger.info("Instance %s already has IP %s" %
                                         (self._primary_id, inst.get_param("public_ip")))

                    # Currently SP interconnect is only supported for GCP cloud
                    if self._cloud_provider == PROVIDER_GCP:
                        if not inst.get_param("public_ip_dp2"):
                            inst.set_param("public_ip_dp2", self._primary_public_ip_dp2)
                            self.logger.info("set public_ip_dp2 field in instance_master as %s" % str(inst.get_param("public_ip_dp2")))
                    else:
                        self.logger.info("Instance %s already has DP2 IP %s" % (self._primary_id, inst.get_param("public_ip_dp2")))

                    # Update IM RDS columns for ingress/egress IP mappings for NGPA data plane support for OCI
                    if self._cloud_provider == PROVIDER_OCI:
                        if self._primary_ingress_ip_mappings not in [None, "", {}]:
                            self.logger.info(f"process_instance_primary: Setting ingress_ip_mappings to {self._primary_ingress_ip_mappings} for {inst.get_param('name')}")
                            inst.set_param("ingress_ip_mappings", json.dumps(self._primary_ingress_ip_mappings))

                        if self._primary_egress_ip_mappings not in [None, "", {}]:
                            self.logger.info(f"process_instance_primary: Setting egress_ip_mappings to {self._primary_egress_ip_mappings}")
                            inst.set_param("egress_ip_mappings", json.dumps(self._primary_egress_ip_mappings))

                        if self._primary_pripub_ip_mappings not in [None, "", {}]:
                            self.logger.info(f"process_instance_primary: Setting pripub_ip_mappings to {self._primary_pripub_ip_mappings}")
                            inst.set_param("pripub_ip_mappings", json.dumps(self._primary_pripub_ip_mappings))
                            if (not inst.get_param('public_ip') or inst.get_param('public_ip') in [None, '']) and inst.get_param('node_type') == NODE_TYPE_NAT_INSTANCE:
                                # pripub_ip_mappings is always a single IP mapping column as we add it per NAT instance
                                for pub_ip, priv_ip in self._primary_pripub_ip_mappings.items():
                                    self.logger.info(f"process_instance_primary: Setting public_ip to {pub_ip} for {inst.get_param('name')}")
                                    inst.set_param('public_ip', pub_ip)

                        if inst.get_param("node_type") == NODE_TYPE_GP_GATEWAY:
                            if self._primary_interface_ip_list not in [None, "", {}]:
                                self.logger.info(f"process_instance_primary: Setting interface_ip_list to {self._primary_interface_ip_list}")
                                inst.set_param("interface_ip_list", json.dumps(self._primary_interface_ip_list))

                            self.logger.info(f"process_instance_primary: Setting lb_details to {self._primary_nlb_domain_details}, lb_details_v6 to {self._primary_public_ipv6}")
                            if self._primary_nlb_domain_details not in [None, "", {}]:
                                self.logger.info(f"process_instance_primary: Setting lb_details to {self._primary_nlb_domain_details}")
                                inst.set_param("lb_details", self._primary_nlb_domain_details)

                            ## TNRC - is lb_details_v6 getting set from here ?
                            ## TODO: We need to revisit this for edge locations handling where interface_ipv6_list
                            ## could have multiple edge to IPv6 entries
                            if self._primary_nlb_domain_details_v6 not in [None, "", {}]:
                                self.logger.info(f"process_instance_primary: Setting lb_details_v6 to {self._primary_nlb_domain_details_v6}")
                                inst.set_param("lb_details_v6", self._primary_nlb_domain_details_v6)

                        # For OCI NLB node instance_master entry; we will add the OCI NLB private IPv6 address in interface_ipv6_list
                        # This will be used by SaaS agent on OCI firewalls to program the source IPv6 address for adding a security policy
                        # to allow NLB health check probes for IPv6 NLB
                        #if inst.get_param("node_type") == NODE_TYPE_NLB_INSTANCE:
                        #    if self._nlb_interface_ipv6_list:
                        #        self.logger.info(f"process_instance_primary: Setting interface_ipv6_list on NLB instance_master entry to {self._nlb_interface_ipv6_list}")
                        #        inst.set_param("interface_ipv6_list", json.dumps(self._nlb_interface_ipv6_list))

                        if inst.get_param("node_type") == NODE_TYPE_SWG_PROXY:
                            self.logger.info(f"process_instance_primary: Setting extrn_ip to {self._extrn_ip}")
                            if self._extrn_ip not in [None, ""]:
                                self.logger.info(f"process_instance_primary: Setting _extrn_ip to {self._extrn_ip}")
                                inst.set_param("extrn_ip", self._extrn_ip)
                            self.logger.info(f"process_instance_primary: Setting lb_details to {self._primary_nlb_domain_details}")
                            if self._primary_nlb_domain_details not in [None, "", {}, inst.get_param("lb_details")]:
                                self.logger.info(f"process_instance_primary: Setting lb_details to {self._primary_nlb_domain_details}")
                                inst.set_param("lb_details", self._primary_nlb_domain_details)
                            else:
                                skip_lbaas_ip_update = True

                    if inst.get_param("public_ipv6") in [None, "", "/96", "/128"]:
                        if self._primary_public_ipv6_prefix_len not in [None, "", "0"]:
                            # For OCI; we need to set the prefix length for IPv6 addresses differently based on node type
                            if self._cloud_provider == PROVIDER_OCI and inst.get_param("node_type") == NODE_TYPE_GP_GATEWAY:
                                self.logger.info(f"process_instance_primary: Setting default IPv6 prefix length to 96 for OCI GP Gateway")
                                self._primary_public_ipv6_prefix_len = 96
                            if self._cloud_provider == PROVIDER_OCI and inst.get_param("node_type") == NODE_TYPE_NLB_INSTANCE:
                                self.logger.info(f"process_instance_primary: Setting default IPv6 prefix length to 128 for OCI NLB")
                                self._primary_public_ipv6_prefix_len = 128
                            if self._cloud_provider == PROVIDER_OCI and inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE:
                                self.logger.info(f"process_instance_primary: Skip setting primary_public_ipv6 for OCI Cloud NAT IM entry")
                            # We skip setting public_ipv6 field for v4 only firewalls as this causes SaaS agent to wait in loop
                            # for lb_details_v6 to be set
                            elif not self._primary_interface_ipv6_list:
                                self.logger.info(f"process_instance_primary: intf_ipv6_list -> {self._primary_interface_ipv6_list} Skip setting primary_public_ipv6 for V4 only stack instances")
                            else:
                                inst.set_param("public_ipv6", self._primary_public_ipv6 + "/" + str(self._primary_public_ipv6_prefix_len))
                                self.logger.info(f"process_instance_primary: set public_ipv6 field in instance_master as {str(inst.get_param('public_ipv6'))}")
                        else:
                            # Not bailing here!
                            # Also for NGPA, this error is benign if seen for NAT instances
                            self.logger.error("public ipv6 prefix len not in correct format, expected int got %s" %
                                              str(self._primary_public_ipv6_prefix_len))
                    else:
                        self.logger.info("Instance %s already has IP %s" %
                                         (self._primary_id, inst.get_param("public_ipv6")))

                    if inst.get_param("public_ipv6_dp2") in [None, ""]:
                        if self._primary_public_ipv6_prefix_len_dp2 not in [None, "", "0"]:
                            inst.set_param("public_ipv6_dp2",
                                           self._primary_public_ipv6_dp2 + "/" + str(self._primary_public_ipv6_prefix_len_dp2))
                            self.logger.info("set public_ipv6_dp2 field in "
                                             "instance_master as %s" %
                                             str(inst.get_param("public_ipv6_dp2")))
                        else:
                            # Not bailing here!
                            # Also for NGPA, this error is benign if seen for NAT instances
                            self.logger.error("public ipv6 prefix len dp2 not in correct format, expected int got %s" %
                                              str(self._primary_public_ipv6_prefix_len_dp2))
                    else:
                        self.logger.info("Instance %s already has IP %s" %
                                         (self._primary_id, inst.get_param("public_ipv6_dp2")))

                    # We will need to force update here since we dont know what changed here.
                    if self._primary_egress_ipv6_list not in [None, "", {}]:
                        self.logger.info("Setting Egress IPV6 address to %s" %
                                                str(self._primary_egress_ipv6_list))
                        compute_region_idx = inst.get_param("compute_region_idx")
                        public_ipv6_address = self._primary_egress_ipv6_list.pop(str(compute_region_idx), None)
                        inst.set_param("egress_ipv6_list", json.dumps(self._primary_egress_ipv6_list))

                        #Override the public IPv6 address here.
                        if public_ipv6_address:
                            inst.set_param("public_ipv6", public_ipv6_address)
                        else:
                            self.logger.error("Cannot find compute idx %s in _primary_egress_ipv6_list %s" %
                                              (str(compute_region_idx),
                                               str(self._primary_egress_ipv6_list)))

                    # Update the interface_ipv6_list
                    self.logger.info(f"Updating primary interface IPv6 list for instance {self._primary_id} to {self._primary_interface_ipv6_list}")
                    inst.update_column_interface_ipv6_list(self._primary_interface_ipv6_list)
                    self.logger.info("set interface_ipv6_list field "
                                     f"in instance_master as {inst.get_param('interface_ipv6_list')}")
                    if inst.get_param("node_type") in [NODE_TYPE_SERVICE_CONN] and self._cloud_provider == PROVIDER_GCP:
                        inst.set_param("pvt_ip2", self._dp_private_vip)
                        # vgw_vip is set by orchestrator not by GCP
                        #inst.set_param("vgw_vip", self._vgw_private_vip)

                        # For colo SC, set the pvt_ip2 field with the secondary private IP on DP interface
                        # for TF case, this list will contains the alias ip list
                        if self._alias_ip_ranges:
                            self._alias_ip = self._alias_ip_ranges[0].get('ip_cidr_range', '')
                        # for DM case, _alias_ip will be assigned
                        if self._alias_ip:
                            self._alias_ip = self._alias_ip.split('/')[0]
                            if self._alias_ip:
                                self.logger.info(f"Setting pvt_ip2 for primary instance id {inst.get_param('id')} as {self._alias_ip}")
                                inst.set_param("pvt_ip2", self._alias_ip)
                        colo_ilb_name = json.loads(inst.get_param("salt_profile")).get("colo_ilb_name", "")
                        if colo_ilb_name:
                            self.logger.info("process_instance_primary: colo-ILB %s" % (str(colo_ilb_name)))
                            #For non-GRE colo-SC's set vgw_vip to nic3 IP assigned by GCP
                            if self._primary_colo_interface_ip_address:
                                inst.set_param("vgw_vip", self._primary_colo_interface_ip_address)
                            self.logger.info("processed regional colo-ILBs %s" % (str(self._region_colo_ilbs_dict)))
                            if colo_ilb_name in self._region_colo_ilbs_dict:
                                colo_ilb_ipv4_address = self._region_colo_ilbs_dict.get(colo_ilb_name)
                                if colo_ilb_ipv4_address:
                                    inst.set_param("lb_details", colo_ilb_ipv4_address)
                                    inst.set_param("interface_ip_list", json.dumps({str(inst.get_param('custid')): colo_ilb_ipv4_address}))

                    # Only if the instance has "is_instance_behind_nlb" flag
                    # set, and we have nlb details, fill up the "lb_details"
                    # and "interface_ip_list" as needed.
                    if inst.get_param("node_type") == NODE_TYPE_GP_GATEWAY and \
                           self._primary_nlb_domain_details and \
                           inst.get_param("is_instance_behind_nlb") == 1:
                        if not inst.get_param("lb_details") or inst.get_param("lb_details") == "None":
                            inst.set_param("lb_details" ,self._primary_nlb_domain_details)
                            nlb_ip_address_update_done = True
                            self.logger.info("set lb_details field in "
                                             "instance_master as %s" %
                                             str(inst.get_param("lb_details")))
                        else:
                            self.logger.info("Instance %s already has "
                                         "lb_details %s" %
                                         (self._primary_id, inst.get_param("lb_details")))
                        if self._primary_nlb_public_ipv6:
                            inst.set_param("lb_details_v6" ,self._primary_nlb_public_ipv6)
                            nlb_ip_address_update_done = True
                            self.logger.info("set lb_details_v6 field in "
                                            "instance_master as %s" %
                                            str(inst.get_param("lb_details_v6")))

                        if not inst.get_param("interface_ip_list") or inst.get_param("interface_ip_list") == "None" or inst.get_param("interface_ip_list") == '{}' or inst.get_param("interface_ip_list") is None:
                            self.logger.info(f"Updating primary interface IP list {self._primary_interface_ip_list} for the instance")
                            inst.update_column_interface_ip_list(self._primary_interface_ip_list)
                            self.logger.info("set interface_ip_list field "
                                             "in instance_master as %s" %
                                             str(inst.get_param("interface_ip_list")))
                        else:
                            self.logger.info("Instance %s already has "
                                             "interface_ip_list set" %
                                             self._primary_id)
                    
                    if inst.get_param("node_type") == NODE_TYPE_GP_GATEWAY and \
                            self._primary_sase_svc_ilb_v4_address and \
                            inst.get_param("is_instance_behind_nlb") == 1:
                        self.logger.info(f"self._primary_sase_svc_ilb_v4_address: {self._primary_sase_svc_ilb_v4_address}")
                        inst.set_param("sase_svc_lb_details", self._primary_sase_svc_ilb_v4_address)

                    if inst.get_param("node_type") == NODE_TYPE_REMOTE_NET and \
                            self._primary_rn_int_onramp_ilb_v4_address and \
                            inst.get_param("is_instance_behind_nlb") == 1:
                        self.logger.info(f"self._primary_rn_int_onramp_ilb_v4_address: {self._primary_rn_int_onramp_ilb_v4_address}")
                        inst.set_param("lb_details", self._primary_rn_int_onramp_ilb_v4_address)
                        if self._primary_rn_int_onramp_ilb_v6_address:
                            self.logger.info(
                                f"self._primary_rn_int_onramp_ilb_v6_address: {self._primary_rn_int_onramp_ilb_v6_address}")
                            inst.set_param("lb_details_v6", self._primary_rn_int_onramp_ilb_v6_address)

                elif self._cloud_provider == PROVIDER_AZR:
                    if inst.get_param("node_type") == NODE_TYPE_GP_GATEWAY and self._primary_nlb_domain_details:
                        if not inst.get_param("lb_details"):
                            inst.set_param("lb_details" ,self._primary_nlb_domain_details)
                            nlb_ip_address_update_done = True
                            self.logger.info("set lb_details field in "
                                             "instance_master as %s" %
                                             str(inst.get_param("lb_details")))
                        else:
                            self.logger.info("AZR instance %s already has lb_details %s" %
                                             (self._primary_id, inst.get_param("lb_details")))
                        if not inst.get_param("egress_ip_list"):
                            inst.save_egress_ip_list(self._primary_egress_ip_list)
                            self.logger.info("set egress_ip_list field in "
                                             "instance_master as %s" %
                                             str(inst.get_param("egress_ip_list")))
                        else:
                            self.logger.info("AZR instance %s already has egress_ip_list %s" %
                                             (self._primary_id, inst.get_param("egress_ip_list")))
                    elif self._primary_public_ip:
                        if not inst.get_param("public_ip"):
                            inst.set_param("public_ip" ,self._primary_public_ip)
                            self.logger.info("set public_ip field in "
                                             "instance_master as %s" %
                                             str(inst.get_param("public_ip")))
                        else:
                            self.logger.info("AZR instance %s already has IP %s" %
                                             (self._primary_id, inst.get_param("public_ip")))
                elif self._cloud_provider == PROVIDER_OPENSTACK:
                    if self._primary_public_ip:
                        inst.set_param("public_ip" ,self._primary_public_ip)
                        self.logger.info("set primary instance public_ip field in "
                                        "instance_master as %s" %
                                         str(inst.get_param("public_ip")))
                    else:
                        self.logger.info("openstack didn't return a public ip address "
                                "from primary instance")
                self._secondary_public_ip = inst.get_param("public_ip")
                if inst.get_param("node_type") in [NODE_TYPE_SERVICE_CONN]:
                    self._secondary_pvt_ip2 = inst.get_param("pvt_ip2")
                    self._secondary_vgw_vip = inst.get_param("vgw_vip")
                    log_msg = f"setting _secondary_pvt_ip2: {self._secondary_pvt_ip2}, _secondary_vgw_vip: {self._secondary_vgw_vip}"
                    self.logger.info(log_msg)
                    if (is_env_fedramp_il5(self.logger) == True):
                        self._secondary_gw_gre_peer_ip = inst.get_param("gw_gre_peer_ip")
                        self._secondary_gw_gre_bgp_asn = inst.get_param("gw_gre_bgp_asn")
                        self._secondary_gw_gre_bgp_range = inst.get_param("gw_gre_bgp_range")
                        log_msg = f"setting TGW connect peer info of secondary instance peer_ip: {self._secondary_gw_gre_peer_ip}, " \
                              f"BGP ASN: {self._secondary_gw_gre_bgp_asn}, BGP range: {self._secondary_gw_gre_bgp_range}"
                        self.logger.info(log_msg)

                # Set the basic configuration
                inst.set_param("vmid" , self._primary_vmid)
                inst.set_param("mgt_ip" , self._primary_mgt_ip)
                inst.set_param("pvt_ip" , self._primary_pvt_ip)

                # notification to ADEM service for new instance creation
                if is_adem_notification_enabled(self.dbh, self.logger):
                    notify_adem_service(inst, self.dbh, self.logger)

                inst.set_param("sase_fabric_ip", inst.get_param("public_ip"))
                inst.set_param("last_succ_salt_profile", inst.get_param("salt_profile"))

                if inst.get_param("state") == -2:
                    inst.set_param("vm_status", 0)
                    inst.set_param("ha_state" , 0)
                    if inst.is_vert_scale_replace_case(self.dbh, inst.get_param("native_machine_type"), \
                        inst.requested_native_machine_type()):
                            if self.finished_processing_seq_nr and  \
                                inst.is_instance_upgrade_update_needed(self.dbh, self.finished_processing_seq_nr):
                                inst.update_upgrade_table(self.dbh, 0, False, True)
                                send_update = True
                    else:
                        inst.update_upgrade_table(self.dbh)

                inst.set_param("state", 1)
                # Value of "upgrade_creation" will remain as-is. If the
                # value of "upgrade_creation" was 1, it will be reset
                # to 0, after upgrade is completed.
                if self._primary_machine_type is not None:
                    inst.set_param("native_machine_type", self._primary_machine_type)
                else:
                    inst.set_param("native_machine_type", inst.requested_native_machine_type())
                #print("!!!!!!!!!!!!!!! Saving the instance !!!!!!!!!!!!!!")
                ret = inst.save()
                if ret is False:
                    raise Exception("Failed to save instance entry in the database. Cannot continue!")
                # For Cloud NAT; we add egress pripub_ip_mappings which are not available during IM entry creation
                # we need to send SNS to SaaS agent to update / add the new NAT rule for edge NATs as edge regions can get onboarded
                # for existing compute region gateways
                # We need to handle similarly even for GCP CNAT support
                if inst.get_param('cloud_provider') == PROVIDER_OCI_DB_ENUM_VALUE and inst.get_param('node_type') == NODE_TYPE_NAT_INSTANCE:
                    self.logger.info(f"process_instance_primary: Send SNS notification to SaaS agent for NAT {inst.get_param('name')} for {inst.get_param('custid')} with mappings {inst.get_param('pripub_ip_mappings')}")
                    send_sns_msg_for_cnat_fw_egress_rule_update(inst.get_param('id'), inst.get_param('custid'), inst.get_param('compute_region_idx'), self.logger)

                # If the NLB IP Address was newly set for this instance,
                # send out an SNS notification that a Route53 update needs
                # to be done.
                if nlb_ip_address_update_done:
                    #For V4 logic, this sns message is no longer required
                    #But we keep it in for V6 logic
                    self.logger.info("Send SNS notification that a "
                                     "Route53 update needs to be done")
                    #We should avoid sending any R53 updates for two phase upgrade instances
                    #or when upgrade is complete in which case upgrade_status will be "COMPLETED-UPGRADE"
                    upgrade_status = inst.get_param("upgrade_status")
                    if not upgrade_status or upgrade_status == "NONE" or upgrade_status == "COMPLETED-UPGRADE":
                        send_sns_msg_for_r53_update(self._primary_id,
                                self._cust_id, self._primary_nlb_domain_details,
                                self.logger, ipv6_addr=self._primary_nlb_public_ipv6)

                # Now we need to update the dns manager to make sure entry is added/updated.
                if (inst.get_param("node_type") == NODE_TYPE_SWG_PROXY and not skip_lbaas_ip_update) or (inst.get_param("node_type") == NODE_TYPE_BI_NH_PROXY and inst.get_param("alt_node_type") == NODE_TYPE_UDA):
                    if (inst.get_param("is_dynamic_instance") == 0 and inst.get_param('is_pinned_instance') == 1):
                        ret = inst.process_dns_change_for_swg_proxy("insert")
                        if ret == False:
                            err_msg = ("Procesing SWG Proxy DNS change for insert failed for instance id %s !!!" % (str(inst.get_param("id"))))
                            raise Exception(err_msg)
                    else:
                        self.logger.info(f"Skipping DNS change for dynamic/pinned instance, "
                                         f"is_dynamic_instance: {inst.get_param('is_dynamic_instance')} "
                                         f"is_pinned_instance: {inst.get_param('is_pinned_instance')}")
                elif inst.get_param("node_type") == NODE_TYPE_PROBE_VM and probe_vm_processor:
                    probe_vm_processor.send_sns_msg(inst.get_param("id"))
                #from pprint import pprint
                #pprint(vars(inst))
            elif inst.get_param("state") == -3:
                self._secondary_public_ip = inst.get_param("public_ip")
                if inst.get_param("node_type") in [NODE_TYPE_SERVICE_CONN]:
                    self._secondary_pvt_ip2 = inst.get_param("pvt_ip2")
                    self._secondary_vgw_vip = inst.get_param("vgw_vip")
                    log_msg = f"setting _secondary_pvt_ip2 in state(-3): {self._secondary_pvt_ip2}, " \
                              f"_secondary_vgw_vip: {self._secondary_vgw_vip}"
                    self.logger.info(log_msg)
                    if (is_env_fedramp_il5(self.logger) == True):
                        self._secondary_gw_gre_peer_ip = inst.get_param("gw_gre_peer_ip")
                        self._secondary_gw_gre_bgp_asn = inst.get_param("gw_gre_bgp_asn")
                        self._secondary_gw_gre_bgp_range = inst.get_param("gw_gre_bgp_range")
                        log_msg = f"setting TGW connect peer info secondary " \
                              f"peer_ip: {self._secondary_gw_gre_peer_ip}, " \
                              f"BGP ASN: {self._secondary_gw_gre_bgp_asn}, " \
                              f"BGP range: {self._secondary_gw_gre_bgp_range}"
                    self.logger.info(log_msg)
                if self.finished_processing_seq_nr and  \
                    inst.is_instance_upgrade_update_needed(self.dbh, self.finished_processing_seq_nr):
                    if not inst.update_upgrade_table(self.dbh, 0, True, True):
                        success = False
                        raise Exception("Failed to update new vmid in the " \
                         "instance_upgrade for id: %s" % self._primary_id)

                    inst.set_param("vmid", self._primary_vmid)
                    if self._primary_machine_type is not None:
                        inst.set_param("native_machine_type", self._primary_machine_type)
                    else:
                        inst.set_param("native_machine_type", inst.requested_native_machine_type())
                    send_update = True
                else:
                    send_update = False
        except InvalidInstanceIdException as instanceE:
            self.error = (f"!!! Fatal !!!! process_primary_instance failed since instance cannot be found: {instanceE.args}")
            self.logger.error("%s %s" % (self.error, str(traceback.format_exc())))

            success = False
        except InvalidInterfaceIpv6ListException as instanceE:
            self.error = (f"!!! Fatal !!!! process_primary_instance failed since interface_ipv6_list is invalid: {instanceE.args}")
            self.logger.error("%s %s" % (self.error, str(traceback.format_exc())))

            success = False
        except InvalidInterfaceIpv6ListJsonException as instanceE:
            self.error = (f"!!! Fatal !!!! process_primary_instance failed since interface_ipv6_list cannot be converted to JSON: {instanceE.args}")
            self.logger.error("%s %s" % (self.error, str(traceback.format_exc())))

            success = False
        except DbUpdateException as instanceE:
            self.error = (f"!!! Fatal !!!! process_primary_instance failed since interface_ipv6_list cannot be updated in RDS: {instanceE.args}")
            self.logger.error("%s %s" % (self.error, str(traceback.format_exc())))

            success = False
        except Exception as E:
            self.error = ("!!! Fatal !!!! process_primary_instance failed. "
                "Exception %s" % str(E.args))
            self.logger.error("%s %s" % (self.error, str(traceback.format_exc())))

            success = False
        finally:
            if inst and inst.get_param("sase_fabric_ip") is not None:
                send_notification_to_sase_svc(self.dbh, inst)
            if inst and send_update == True:
                # Cleanup state to allow further cleanup of instance_upgrade
                status = "Success" if success else "Failed"
                success = upgradesvc_notification_send("BRINGUP", inst, status,
                    self.dbh, inst.get_upgrade_trace_id(), vmid=inst.get_param("vmid"), status_msg=self.error)

                #TBD: Use a string value instead
                inst.set_param("state", 1)
                ret = inst.save()
                if ret is False:
                    success = False
                    self.logger.error("Failed to save instance entry in the database. Cannot continue!")
            return success

    def process_instance_secondary(self):
        from libs.cloud_providers.aws.instance_manager.aws_bringup import get_aws_il5_sc_instance_vips, process_aws_il5_sc_instance_vips
        success = True
        inst = None
        send_update = False
        self.error = ""
        try:
            self.logger.info(f"process_instance_secondary: Processing secondary instance with ID: {self._secondary_id} and vmid {self._secondary_vmid}")
            if not self._secondary_id:
                self.logger.info("process_instance_secondary: No secondary instance ID found. Skipping processing.")
                return success

            inst = InstanceModel(iid=self._secondary_id, dbh=self.dbh)
            if not inst.get_param("id"):
                self.logger.error("!!! Fatal !!!! process_instance_secondary: Instance with ID %s not found in "
                                  "database." % (str(self._secondary_id)))
                raise Exception("!!! Fatal !!!! process_instance_secondary: Instance with ID %s not found in "
                                  "database." % (str(self._secondary_id)))

            self.logger.info(f"process_instance_secondary: Processing secondary instance {inst} -> {inst.get_param('name')} with state {inst.get_param('state')}")
            if inst.get_param("id") and (inst.get_param("state") == 0 or inst.get_param("state") == 1 or
                                (inst.get_param("state") == -2 and inst.get_param("vmid") != self._secondary_vmid)):

                # Set the basic configuration

                inst.set_param("vmid", self._secondary_vmid)
                inst.set_param("mgt_ip", self._secondary_mgt_ip)
                inst.set_param("pvt_ip", self._secondary_pvt_ip)
                inst.set_param("public_ip", self._secondary_public_ip)
                # TODO: Save inst.sase_fabric_ip - Will this be public or private IP moving forward?
                if inst.get_param("node_type") in [NODE_TYPE_SERVICE_CONN]:
                    inst.set_param("pvt_ip2", self._dp_private_vip)
                    if self._cloud_provider == PROVIDER_AWS:
                        # vgw_vip is set by orch for colo sc in GCP
                        inst.set_param("vgw_vip", self._vgw_private_vip)
                    log_msg = (f"setting vips for secondary instance, pvt_ip2/dp_vip:{self._dp_private_vip}, " \
                               f"vgw_vip:{self._vgw_private_vip}")
                    self.logger.info(log_msg)
                    inst.set_param("pvt_ip2", self._dp_private_vip)
                    log_msg = (f"set  pvt_ip2/dp_vip:{self._dp_private_vip}, " \
                               f"vgw_vip:{self._vgw_private_vip}")
                    self.logger.info(log_msg)
                    if (is_env_fedramp_il5(self.logger) == True):
                        inst.set_param("gw_gre_peer_ip", self._gw_gre_peer_ip)
                        inst.set_param("gw_gre_bgp_asn", self._gw_gre_bgp_asn)
                        inst.set_param("gw_gre_bgp_range", self._gw_gre_bgp_range)
                        log_msg = (f"set TGW peer connect on secondary instance, gw_gre_peer_ip:{self._gw_gre_peer_ip}, " \
                               f"gw_gre_bgp_asn:{self._gw_gre_bgp_asn}, gw_gre_bgp_range:{self._gw_gre_bgp_range}")
                        self.logger.info(log_msg)
                    # colo alias ip for eth1
                    if self._alias_ip_ranges:
                        self._alias_ip = self._alias_ip_ranges[0].get('ip_cidr_range', '')
                    if self._alias_ip:
                        self._alias_ip = self._alias_ip.split('/')[0]
                        if self._alias_ip:
                            self.logger.info(f"Setting pvt_ip2 for secondary instance id {inst.get_param('id')} as {self._alias_ip}")
                            inst.set_param("pvt_ip2", self._alias_ip)
                    if self._cloud_provider == PROVIDER_GCP:
                        colo_ilb_name = json.loads(inst.get_param("salt_profile")).get("colo_ilb_name", "")
                        if colo_ilb_name:
                            self.logger.info("process_instance_secondary: colo-ILB %s" % (str(colo_ilb_name)))
                            #For non-GRE colo-SC's set vgw_vip to nic3 IP assigned by GCP
                            if self._secondary_colo_interface_ip_address:
                                inst.set_param("vgw_vip", self._secondary_colo_interface_ip_address)
                            self.logger.info("processed regional colo-ILBs %s" % (str(self._region_colo_ilbs_dict)))
                            if colo_ilb_name in self._region_colo_ilbs_dict:
                                colo_ilb_ipv4_address = self._region_colo_ilbs_dict.get(colo_ilb_name)
                                if colo_ilb_ipv4_address:
                                    inst.set_param("lb_details", colo_ilb_ipv4_address)
                                    inst.set_param("interface_ip_list", json.dumps({str(inst.get_param('custid')): colo_ilb_ipv4_address}))

                self.logger.info("Public IP allocated is %s" % str(inst.get_param("public_ip")))

                if self._cloud_provider == PROVIDER_AWS:
                    # IPv6 assignment:-
                    aws_ipv6_mgmt_ref = aws_v6_mgmt.IPV6_Handler(dbh=self.dbh, acct_id=inst.get_param("acct_id"))
                    ret, ipv6_address = aws_ipv6_mgmt_ref.get_ipv6_address_from_salt_profile(inst.get_param("id"))
                    if ret == False:
                        success = False
                        raise Exception(
                            "Failed to retrieve IPv6 address for instance id %s" % str(inst.get_param("id")))
                    if inst.get_param("public_ipv6") == ipv6_address:
                        self.logger.info("Public IPv6 address is already set to %s" % str(ipv6_address))
                    else:
                        ret = aws_ipv6_mgmt_ref.assign_ipv6_address_to_interface(instance_id=inst.get_param("id"),
                                                                                 vmid=self._secondary_vmid,
                                                                                 ipv6_address=ipv6_address)
                        if ret == False:
                            raise Exception("Failed to assign IPv6 address %s to the interface for vmid %s" %
                                            (str(ipv6_address), str(self._primary_vmid)))
                        inst.set_param("public_ipv6", ipv6_address)

                elif self._cloud_provider == PROVIDER_GCP:
                    # We will need to force update here since we dont know what changed here.
                    # Primary and secondary need are the same here.
                    self.logger.info("Setting Egress IPV6 address to %s for secondary instance %s" %
                                     (str(self._primary_egress_ipv6_list), str(self._secondary_id)))
                    primary_instance_ref = InstanceModel(iid=self._primary_id, dbh=self.dbh )
                    public_ipv6_address = primary_instance_ref.get_param("public_ipv6")
                    egress_ipv6_list = primary_instance_ref.get_param("egress_ipv6_list")
                    # Set the secondary details here.
                    self.logger.info(f"Setting public_ipv6 for secondary instance id {inst.get_param('id')} "
                                     f"as {public_ipv6_address}")
                    inst.set_param("public_ipv6", public_ipv6_address)
                    self.logger.info(f"Setting egress_ipv6_list for secondary instance id {inst.get_param('id')} "
                                     f"as f{egress_ipv6_list}")
                    inst.set_param("egress_ipv6_list", egress_ipv6_list)
                    # For colo SC, set the pvt_ip2 field with the secondary private IP on DP interface
                    self._alias_ip = primary_instance_ref.get_param("pvt_ip2")
                    if self._alias_ip:
                        self._alias_ip = self._alias_ip.split('/')[0]
                        if self._alias_ip:                        
                            self.logger.info(f"Setting pvt_ip2 for secondary instance id {inst.get_param('id')} as {self._alias_ip}")
                            inst.set_param("pvt_ip2", self._alias_ip)

                elif self._cloud_provider == PROVIDER_OPENSTACK:
                    if self._secondary_public_ip:
                        inst.set_param("public_ip" ,self._secondary_public_ip)
                        self.logger.info("set secondary instance public_ip field in "
                                        "instance_master as %s" %
                                         str(inst.get_param("public_ip")))
                    else:
                        self.logger.info("openstack didn't return a public ip address from "
                                         "the secondary instance")

                self.logger.info(f"process_instance_secondary: Instance state is {inst.get_param('state')} for instance {inst.get_param('name')}")
                if inst.get_param("state") == -2:
                    inst.set_param("vm_status", 0)
                    inst.set_param("ha_state" , 0)
                    if inst.is_vert_scale_replace_case(self.dbh, inst.get_param("native_machine_type"),
                        inst.requested_native_machine_type()):
                            if self.finished_processing_seq_nr and  \
                                inst.is_instance_upgrade_update_needed(self.dbh, self.finished_processing_seq_nr):
                                inst.update_upgrade_table(self.dbh, 0, False, True)
                                send_update = True
                    else:
                        inst.update_upgrade_table(self.dbh)

                inst.set_param("state", 1)
                inst.set_param("upgrade_creation", 0)
                if self._secondary_machine_type is not None:
                    inst.set_param("native_machine_type", self._secondary_machine_type)
                else:
                    inst.set_param("native_machine_type", inst.requested_native_machine_type())
                #print("!!!!!!!!!!!!!!! Saving the instance !!!!!!!!!!!!!!")
                ret = inst.save()
                if ret is False:
                    raise Exception("Failed to save instance entry in the database. Cannot continue!")

                if inst.get_param("node_type") in [NODE_TYPE_SERVICE_CONN]:
                    log_msg = f"setting vips for secondary instance, " \
                              f"pvt_ip2/dp_vip:{self._dp_private_vip} vgw_vip:{self._vgw_private_vip}"
                    self.logger.info(log_msg)
                    inst.set_param("pvt_ip2", self._dp_private_vip)
                    if self._cloud_provider == PROVIDER_AWS:
                        # vgw_vip is set by orch for colo sc in GCP
                        inst.set_param("vgw_vip", self._vgw_private_vip)
                    if self._cloud_provider == PROVIDER_GCP and self._alias_ip:
                        # For colo SC, set the pvt_ip2 field with the secondary private IP on DP interface
                        self._alias_ip = self._alias_ip.split('/')[0]
                        if self._alias_ip:
                            self.logger.info(f"Setting pvt_ip2 for secondary instance id {inst.get_param('id')} as {self._alias_ip}")
                            inst.set_param("pvt_ip2", self._alias_ip)

                #from pprint import pprint
                #pprint(vars(inst))

            elif inst.get_param("state") == -3:
                if self.finished_processing_seq_nr and  \
                    inst.is_instance_upgrade_update_needed(self.dbh, self.finished_processing_seq_nr):
                    if not inst.update_upgrade_table(self.dbh, 0, True, True):
                        raise Exception("Failed to update new vmid in the " \
                         "instance_upgrade for id: %s" % self._secondary_id)

                    inst.set_param("vmid", self._secondary_vmid)
                    if inst.get_param("node_type") in [NODE_TYPE_SERVICE_CONN]:
                        log_msg = f"setting vips for secondary instance in state(-3), " \
                                  f"pvt_ip2/dp_vip:{self._dp_private_vip} vgw_vip:{self._vgw_private_vip}"
                        self.logger.info(log_msg)
                        inst.set_param("pvt_ip2", self._dp_private_vip)
                        inst.set_param("vgw_vip", self._vgw_private_vip)
                        if (is_env_fedramp_il5(self.logger) == True):
                            inst.set_param("gw_gre_peer_ip", self._gw_gre_peer_ip)
                            inst.set_param("gw_gre_bgp_asn", self._gw_gre_bgp_asn)
                            inst.set_param("gw_gre_bgp_range", self._gw_gre_bgp_range)
                            log_msg = (f"set TGW peer connect on secondary instance, " \
                                   f"gw_gre_peer_ip:{self._gw_gre_peer_ip}, gw_gre_bgp_asn:{self._gw_gre_bgp_asn}, " \
                                   f"gw_gre_bgp_range:{self._gw_gre_bgp_range}")
                            self.logger.info(log_msg)

                        # colo alias ip for eth1 interface
                        if self._alias_ip:
                            self._alias_ip = self._alias_ip.split('/')[0]
                            if self._alias_ip:
                                self.logger.info(f"Setting pvt_ip2 for secondary instance id {inst.get_param('id')} as {self._alias_ip}")
                                inst.set_param("pvt_ip2", self._alias_ip)
                    if self._secondary_machine_type is not None:
                        inst.set_param("native_machine_type", inst.requested_native_machine_type())
                    else:
                        inst.set_param("native_machine_type", inst.requested_native_machine_type())
                    send_update = True
                else:
                    send_update = False
            else:
                self.logger.info("!!! process_instance_secondary !!!. Instance params already set. Nothing to do.")

        except Exception as E:
            self.error = ("process_secondary_instance failed. Exception %s" % (str(E.args)))
            self.logger.error("%s %s" % (self.error, str(traceback.format_exc())))
            success = False
        finally:
            if inst and send_update:
                # Cleanup state to allow further cleanup of instance_upgrade
                status = "Success" if success else "Failed"
                success = upgradesvc_notification_send("BRINGUP", inst, status,
                        self.dbh, inst.get_upgrade_trace_id(), vmid=inst.get_param("vmid"), status_msg=self.error)

                inst.set_param("state", 1)
                ret = inst.save()
                if ret is False:
                    success = False
                    self.logger.error("Failed to save instance entry in the database. Cannot continue!")
            return success

    def __str__(self):
        #from pprint import pprint
        #pprint(vars(self))
        self.logger.info("instance_bringup_processing: Instance dump %s" % (str(locals())))

    def process_instances_in_transition(self):
        """
        This method is called for cases where ha_peer is set or for okyo MBN use case.
        For okyo we need to make sure that the transition of tunnel config happens only if instance1_transient is set
        and it's not during pinned_instance_upgrade workflow.
        """
        success = False
        try:
            custnode = None
            inst = InstanceModel(iid=self._primary_id, dbh=self.dbh)
            if not inst.get_param("id"):
                raise Exception("Instance with ID %s not found in database." % (str(self._primary_id)))

            ha_peer_name = None
            if not self._is_okyo_mbn:
                ha_peer = InstanceModel(iid=self._secondary_id, dbh=self.dbh)
                if not ha_peer.get_param("id"):
                    raise Exception("Instance with ID %s not found in database." % (str(self._secondary_id)))
                ha_peer_name = ha_peer.get_param("name")

            # Get the sites with transition instances set.
            self.logger.info("Transition ID is %s" % str(self._primary_id))
            sites = inst.get_sites(self.dbh, transition=1)
            self.logger.info("Sites are %s" % str(sites))
            self.logger.info("Transition sites are %s" % str(sites))

            transition_failed = False
            # If instance1_transient is set not MBN pinned_instance_upgrade workflow
            if sites and not self.mbn_pinned_instance_upgrade():
                for site in sites:
                    self.logger.info("Found Sites for transition %s" % str(site))
                    custnode = CN.CustNodeModel(site, self.dbh)
                    if custnode.id:
                        old_compute_region_idx = custnode.transition_instances(self.dbh, None)
                        if not old_compute_region_idx:
                            transition_failed = True
                            self.logger.info("No old region found. Site transition failed.")
                        else:
                            self.logger.info("Transitioned, Creating deployment trigger.")
                            # instance_management_refresh_event For old [COMPUTE] region.
                            # Passing in PROVIDER_AWS as a stub. It is overwritten inside
                            # trigger_update_for_instance_changes
                            trigger_update_for_instance_changes(self.dbh, old_compute_region_idx,
                                                                self._cust_id, PROVIDER_AWS)

                if transition_failed:
                    err = ("BRINGUP_POOL_HOOK",
                           "Transition of instance %s failed" % str(inst.get_param("name")),
                           {'Primary': inst.get_param("name"), 'Secondary': ha_peer_name})
                    self.logger.info("%s. Sending to failure queue." % str(err))
                    # TODO
                    # jmsg = self.to_json(err)
                    # self.send_to_next_q('failure_q.fifo', [jmsg])

            success = not transition_failed

        except Exception as E:
            self.logger.error(
                "Instance transition error! Exception occurred. %s %s" % (str(E.args), str(traceback.format_exc())))
        finally:
            return success

    # Main processing routine.

    def process_instance(self):
        success = False
        try:
            # TODO Set the logger context.
            ha_peer_id = None

            if self._secondary_id:
                ha_peer_id = self._secondary_id

            ret = self.process_instance_primary()
            if ret != True:
                raise Exception("!!! Fatal !!!! "
                                "process_instance Failed to process the primary instance, locals %s: Traceback: %s"
                                % (str(locals()), str(traceback.format_exc())))

            ret = self.process_instance_secondary()
            if ret != True:
                raise Exception("!!! Fatal !!!! process_instance Failed to process the secondary instance, locals %s:"
                                "Traceback %s"
                                % (str(locals()), str(traceback.format_exc())))

            # Calling transition instance for both MBN and if sec id is set.
            # For MBN we check if instance1_transient is set, and it's not pinned_instance_upgrade workflow .
            if self._secondary_id or self._is_okyo_mbn:
                ret = self.process_instances_in_transition()
                if ret != True:
                    raise Exception("Failed to process the instances in transition, locals %s: Traceback %s" %
                                    (str(locals()),
                                     str(traceback.format_exc())))

            # Mark the success as True.
            success = True

        except Exception as E:
            self.logger.error("!!! Fatal !!!! process_instance: Failed to process instance with Exception %s:"
                              " traceback: %s" % (str(E.args), str(traceback.format_exc())))

        finally:
            return success
        
    def process_instance_ep_ilb_ip(self, ilb_ip_address, region_id, cust_id):
        """
        EP deployments need ilb ipaddress to be set in the cust_epaas_config table. This function updates the field during post_process.py
        """
        self.logger.info(f"Processing ILB IP {ilb_ip_address} for region_id {region_id}, cust_id {cust_id}, cloud_provider {self._cloud_provider}")
        if not ilb_ip_address:
            self.logger.error("Invalid empty/null value for ilb_ip_address in process_instance_ep_ilb_ip")
            return
        cust_ep_cfg = CustEpaasConfigModel(dbh=self.dbh, custid=cust_id, compute_region_id=region_id, cloud_provider=self._cloud_provider)
        if cust_ep_cfg.get_entry():
            cust_ep_cfg.set_param("ilb_ip_address", ilb_ip_address)
            cust_ep_cfg.set_param("udp_ilb_address", ilb_ip_address) #same address for oci today
            cust_ep_cfg.save()
            self.logger.info(f"Saved ILB(+ULB) IP {ilb_ip_address} for region_id {region_id}, cust_id {cust_id}, cloud_provider {self._cloud_provider}")
        else:
            self.logger.info(f"No cust-epaas found. Will not save ILB IP {ilb_ip_address} for region_id {region_id}, cust_id {cust_id}, cloud_provider {self._cloud_provider}")
        return


    def process_instance_release_pending(self, instance_ids_seen, deployment_eidx, region_id):
        """
        Due to NGPA MUs using acct_id 0 IPs, we need to release these IPs once we confirm the instances have been deleted
        #NOTE: Sp-interconnect + NGPA does not use acct_id =0 IPs, and should not be handled here
        """
        self.logger.info(f"Processing release pending IPs for deployment {deployment_eidx}")
        no_more_instances = False
        if not instance_ids_seen or len(instance_ids_seen) == 0:
            self.logger.info(f"No instances found for deployment {deployment_eidx}, updating all IPs related to this deployment")
            no_more_instances = True

        try:
            native_compute_region_name = ""
            sql_cmd = ("SELECT native_compute_region_name FROM region_master "
                    "WHERE compute_region_id = %s LIMIT 1")
            params = (region_id,)
            self.logger.info(f"sql: {sql_cmd}, params:{params}")
            try:
                #cursor is left open here for later use
                cursor = self.dbh.get_cursor(prepared=True)
                cursor.execute(sql_cmd, params)
                res = cursor.fetchone()
                if res:
                    native_compute_region_name = res[0]
            except Exception as ex:
                self.logger.error(f"Failed in process_instance_release_pending: {str(ex)}")
                return

            #If native_compute_region_name is still empty
            if not native_compute_region_name:
                self.logger.error(f"Native compute region name not found for deployment {deployment_eidx}, unable to update IP status!!")
                return

            ip_status = "reserved"
            ipam_utils = create_ipam_utils(dbh=self.dbh, logger=self.logger)
            if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                                    compute_region_info=native_compute_region_name) == IPAMService.ENABLED:
                if not no_more_instances:
                    #Since match_not_cluster_id is yet to be implemented, we will need to do a look up on existing cluster_ids with eidx set
                    #Please NOTE: 
                    #   1. This is a temporary measure. Ideally we should switch to matching cluster_id_not_in inside IPAM instead of a separate SQL query.
                    #   2. No node_type filter here as node_type is guaranteed to be 49 currently in the logic to set status to release_pending. Change here if that changes.

                    sql_cmd = ("SELECT cluster_id FROM public_ip_pool WHERE eidx = %s and status='release_pending'")
                    params = (deployment_eidx,)
                    self.logger.info(f"In process_instance_release_pending, checking for release_pending cluster_ids sql: {sql_cmd}, params:{params}")
                    release_pending_cluster_ids = []
                    try:
                        #cursor is left open here for later use
                        cursor = self.dbh.get_cursor(prepared=True)
                        cursor.execute(sql_cmd, params)
                        res = cursor.fetchall()
                        if res:
                            for row in res:
                                #Filter out the NULL and 0 cases, if exists (they shouldn't)
                                if row[0] and row[0] != 0 and row[0] != "NULL":
                                    release_pending_cluster_ids.append(str(row[0]))
                        else:
                            self.logger.info(f"No release_pending_cluster_ids found!!")
                    except Exception as ex:
                        self.logger.error(f"Failed in process_instance_release_pending while trying to find release_pending_cluster_ids!, exception: {ex}")
                        return

                    #get all cluster_ids that are in release_pending_cluster_ids but not instance_ids_seen_list
                    #NOTE: The reason we cast to string here is because IPAM util has been casting cluster_ids to string inside ipam_update_address before sending to ipam when -
                    #only a single cluster_id is provided.
                    instance_ids_seen_list = [str(inst_id) for inst_id in instance_ids_seen]
                    cluster_ids_to_match = [str(cluster_id) for cluster_id in release_pending_cluster_ids if str(cluster_id) not in instance_ids_seen_list]
                    self.logger.info(f"Updating IPAM in process_instance_release_pending for deployment id {deployment_eidx} "
                                     f"with instance ids seen = {instance_ids_seen_list}")
                    ipam_utils.ipam_update_address(match_eidx=deployment_eidx,
                                                   match_cluster_id=cluster_ids_to_match,
                                                   match_status="release_pending",
                                                   set_status=ip_status,
                                                   set_service_id="",
                                                   set_cluster_id=0,
                                                   set_eidx=0,
                                                   call_context="orch:process_instance_release_pending")
                else:
                    self.logger.info(f"Updating IPAM in process_instance_release_pending for deployment id {deployment_eidx} "
                                     f"with no more instance_ids seen")
                    ipam_utils.ipam_update_address(match_eidx=deployment_eidx,
                                                   match_status="release_pending",
                                                   set_status=ip_status,
                                                   set_service_id="",
                                                   set_cluster_id=0,
                                                   set_eidx=0,
                                                   call_context="orch:process_instance_release_pending")
            else:
                #Force a cast here for datatype because we cannot join ints:
                instance_ids_seen = [str(inst_id) for inst_id in instance_ids_seen]
                set_to_str = "', '".join(instance_ids_seen)
                sql_cmd = ("UPDATE public_ip_pool SET cluster_id = 0, "
                            "status=%s, eidx=%s "
                            "WHERE eidx=%s AND status='release_pending'")
                params = (ip_status, 0, deployment_eidx)
                if not no_more_instances:
                    sql_cmd += " AND cluster_id not in " + f"('{set_to_str}')"

                self.logger.info(f"sql: {sql_cmd}, params:{params}")

                try:
                    cursor.execute(sql_cmd, params)
                except Exception as ex:
                    self.logger.error(f"Failed in process_instance_release_pending: err {str(ex)}")
                    return

        except Exception as e:
            self.logger.error(f"Error occurred during IP release_pending update for deployment {deployment_eidx}: {str(e)}")
        finally:
            if cursor:
                self.dbh.cursorclose(cursor)
            return

    def mbn_pinned_instance_upgrade(self):
        """
        Currently new MBN is brought and transition is required only during location update.
        But even during pinned_instance_upgrade instance1_transient will be set.
        This methods expects that cust_topology entries with instance1_transient is set is verified already.
        AMI upgrade: In bringup.py we properly handle case of Bring up, transition and delete.
        Location Upgrade: TODO currently we trigger topology_update workflow with location update, As part of this
                          if new MBN in region , need to perform transition instance. Few bugs here because MU topology
                          update is not handled, Need to look more here.

        returns : Boolean : True if this is case of MBN pinned_instance_upgrade.
        """
        if not self._is_okyo_mbn:
            self.logger.info("Not an MBN node.")
            return False

        pinned_upgrade = False

        query = ("select * from pinned_instance_upgrade_table where new_instance_id='%s'")
        cursor = self.dbh.get_cursor()
        try:
            self.logger.info("Executing MBN query: %s" % str(query % self._primary_id))
            cursor.execute(query, (self._primary_id,))
            res = cursor.fetchone()
            self.logger.info("Result MBN query: %s" % (str(res)))
            if not res:
                self.logger.info("No entry in pinned_instance_upgrade_table, "
                                 "But instance1_transient is set so from loc update "
                                 "for : [%s]" % (str(self._primary_id)))
            else:
                self.logger.info("In pinned_instance_upgrade workflow "
                                 "for: [%s]" % (str(self._primary_id)))
                pinned_upgrade = True

        except Exception as E:
            self.logger.error("Failed with error %s" % str(E))
        finally:
            if cursor:
                self.dbh.cursorclose(cursor)

        self.logger.info("MBN pinned_upgrade: [%s] for: [%s] " % (pinned_upgrade, str(self._primary_id)))
        return pinned_upgrade
