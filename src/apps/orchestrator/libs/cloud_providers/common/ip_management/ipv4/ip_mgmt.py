import boto3
import json
from libs.cfg import cfg
from libs.apis.region_master_api import gpcs_get_compute_region_idx_from_edge_region_idx, get_configured_gw_city_country_cloud_location_by_compute
from collections import defaultdict
import traceback
import ipaddress
from io import BytesIO
import base64
from gzip import GzipFile
import xml.etree.cElementTree as ET
from libs.common.shared import dbconn
from libs.common.shared.utils import IPAMService, is_ipam_service_enabled
from ipam_factory import create_ipam_utils
from libs.common.shared.sys_utils import *
from libs.common.shared.py3_utils import convert_bytes_to_str,convert_str_to_bytes,b64encode,b64decode
from libs.model.explicitProxyTenantInfoModel import ExplicitProxyTenantInfoModel
from libs.model.custmodel import CustomerModel
from libs.model.custEpaasConfigModel import CustEpaasConfigModel
from libs.apis.api_utils import is_aws_fedramp_env_govcloud, get_aws_govcloud_dp_acct_boto3_ec2_client
from utils.Exceptions import PupiAllocationFailureException
from libs.common.shared.sys_utils import CLOUD_SUPPORT_NAT, CLOUD_NAT_SERVICE_ID
from libs.common.shared.gcp_utils import get_proxy_prefix
from libs.model.regionmastermodel import RegionMasterModel

AWS = 'AWS'
GCP = 'GCP'
USE_PBF_FOR_CLEAN_IP_FROM_AWS = True
USE_PBF_FOR_CLEAN_IP_FROM_GCP = False


def compresess_cfg(cfg):
    if cfg is None or str(cfg).lower() in ('none', 'null', ''):
        return cfg
    cfg = convert_str_to_bytes(cfg)
    f = BytesIO()
    gzf = GzipFile(mode="w", fileobj=f)
    gzf.write(cfg)
    gzf.close()
    data = f.getvalue()
    return b64encode(data)


def uncompress_cfg(cfg):
    if cfg is None or str(cfg).lower() in ('none', 'null', ''):
        return cfg
    orig_file_desc = GzipFile(mode='r',
                              fileobj=BytesIO(b64decode(cfg,decode=False)))
    # Get the original's file content to a variable
    orig_file_cont = orig_file_desc.read()
    return convert_bytes_to_str(orig_file_cont)

def release_all_ip_to_customer_ip_pool(dbh, acct_id, cloud, location,
                                       node_type, service_id=None):
    logger = dbh.logger
    try:
        ipam_utils = create_ipam_utils(dbh=dbh, logger=logger)
        if service_id:
            if node_type == SERVICE_TYPE_BI_NHP_NLB_INGRESS_IP or service_id == CLOUD_NAT_SERVICE_ID:
                logger.info(f"Using actual location {location} for node_type {node_type}")
            else:
                location = "global"
                logger.info(f"Using global location for node_type {node_type}")
            args = (cloud, location, acct_id, node_type, service_id)
            sql = ["CALL release_public_ip_by_location_type_and_service_id('%s','%s',%s,%s,'%s',@updated_rows)" % args]
        else:
            logger.info(f"Using release_public_ip_by_location_and_type: {location} {node_type}.")
            args = (cloud, location, acct_id, node_type)
            sql = ["CALL release_public_ip_by_location_and_type('%s','%s',%s,%s,@updated_rows)" % args]

        if None in args:
            logger.error("Failed to release IPs! Invalid args: %s" % str(args))
            return -1
        sql.append("SELECT @updated_rows")
        sql_cmd = ";".join(sql)
        logger.info("sql: %s " % str(sql_cmd))
        if is_ipam_service_enabled(dbh=dbh, logger=logger, compute_region_info=location) == IPAMService.ENABLED:
            res = ipam_utils.ipam_update_address(match_cloud=cloud,
                                                 match_location=location,
                                                 match_account_id=acct_id,
                                                 match_node_type=node_type,
                                                 match_service_id=service_id,
                                                 set_cluster_id=0, 
                                                 set_status="reserved",
                                                 set_service_id="",
                                                 call_context="orch:release_all_ip_to_customer_ip_pool")
            if not res:
                logger.error("Failed to release BYOIP/public_cloud/PUPI prefixes!")
                return -1
            return len(res)
        else:
            res = dbconn.execute_lambda_query(sql_cmd, {'multi_query': True})
            if not res['ok'] or not res['result']:
                logger.error("Failed to release IPs! Failed sql: %s" % sql_cmd)
                return -1
            elif int(res['result'][0][0]) == 0:
                logger.error("0 IPs released from the region. sql: %s" % sql_cmd)
                return int(res['result'][0][0])
            else:
                logger.info("%s IPs released from the region." % (res['result'][0][0]))
                return int(res['result'][0][0])
    except Exception as e:
        logger.error("Unexpected error in release_all_ip_to_customer_ip_pool: %s locals %s. Traceback: %s" % (str(e.args),
                                                                              str(locals()),
                                                                              str(traceback.format_exc())))
        return -1

def get_service_id_for_nlb(dbh, acct_id, compute_region_idx, node_type, alt_node_type):
    logger = dbh.logger
    service_id = None

    try:
        from libs.model.regionmastermodel import RegionMasterModel
        my_compute_region = RegionMasterModel(dbh=dbh,
                                    edge_location_region_id=compute_region_idx)
        native_reg_name = my_compute_region.get_compute_region_native_name()
        proxy_pfx = get_proxy_prefix(node_type, alt_node_type)
        service_id = f"{proxy_pfx}nlb-{native_reg_name}-{str(acct_id)}"
    except Exception as e:
        logger.error(f"Unexpected exception when building service id: {e}")
    finally:
        logger.info(f"Returning service id: {service_id}")
        return service_id

def set_ip_status_active(ip, logger):
    try:
        logger.info(f"Setting status as active for the nlb ip {ip}")
        sql = ("update public_ip_pool set status='active' where ip = '%s' " % ip)
        result = dbconn.execute_lambda_query(sql, None, logger=logger)
        if not result['ok']:
            raise Exception(f"Error setting status to active for nlb ip {ip} result {result}")
        logger.info(f"Successfully set status as active for the nlb ip {ip}")
        return True
    except Exception as e:
        logger.info(f"Error setting nlb ip status as active for ip {ip} exception {str(e.args)}")
        return False


def is_enable_tls_term_on_ep_for_cust(dbh, acct_id, compute_region_idx, node_type, alt_node_type):
    logger = dbh.logger
    enable_tls_term_on_ep = False
    ept = ExplicitProxyTenantInfoModel(dbh=dbh, tenant_id=acct_id)
    if not ept.get_entry():
        logger.info(f"Failed to retrieve explicit proxy "
                    f"tenant info for tenant_id {acct_id}")
    else:
        enable_tls_term_on_ep = ept.get_param("enable_tls_term_on_ep")
    if not enable_tls_term_on_ep:
        custmodel = CustomerModel(dbh=dbh, acct_id=acct_id)
        cust_ep_cfg = CustEpaasConfigModel(dbh=dbh, custid=custmodel.get_param("id"),
                                           compute_region_id=compute_region_idx,
                                           cloud_provider=PROVIDER_GCP, node_type=node_type, alt_node_type=alt_node_type)
        if cust_ep_cfg.get_entry() and cust_ep_cfg.get_param("enable_tls_term_on_ep"):
            enable_tls_term_on_ep = cust_ep_cfg.get_param("enable_tls_term_on_ep")
        elif cust_ep_cfg.get_entry() and cust_ep_cfg.get_param("enable_global_nlb"):
            enable_tls_term_on_ep = cust_ep_cfg.get_param("enable_global_nlb")
    logger.info(f"enable_tls_term_on_ep for acct_id {acct_id} "
                f"is {enable_tls_term_on_ep}")
    return enable_tls_term_on_ep


def release_all_nlb_ips(dbh, acct_id, compute_region_idx, node_type, alt_node_type):
    logger = dbh.logger
    try:
        service_id = None
        enable_tls_term_on_ep = is_enable_tls_term_on_ep_for_cust(dbh, acct_id, compute_region_idx, node_type, alt_node_type)
        if enable_tls_term_on_ep:
            logger.info("enable_tls_term_on_ep is on; set "
                        "compute_region_idx to global")
            service_id = get_service_id_for_nlb(dbh, acct_id,
                             compute_region_idx, node_type, alt_node_type)
        if node_type == NODE_TYPE_BI_NH_PROXY:
            service_id = get_service_id_for_nlb(dbh, acct_id,
                             compute_region_idx, node_type, alt_node_type)
            logger.info(f"Got service id {service_id} for node type {node_type}.{alt_node_type}")

        from libs.model.regionmastermodel import RegionMasterModel
        my_compute_region = RegionMasterModel(dbh=dbh,
                                    edge_location_region_id=compute_region_idx)
        logger.info(f"Cloud type: {my_compute_region.get_cloud_type()}")
        if my_compute_region.get_cloud_type() not in (PROVIDER_GCP, PROVIDER_OCI):
            logger.info('Only GCP and OCI need to manage NLB IP')
            return 0

        nlb_node_type = SERVICE_TYPE_SWG_NLB_INGRESS_IP
        if node_type == NODE_TYPE_BI_NH_PROXY:
            nlb_node_type = SERVICE_TYPE_BI_NHP_NLB_INGRESS_IP

        if my_compute_region.get_cloud_type() == PROVIDER_OCI and nlb_node_type == SERVICE_TYPE_SWG_NLB_INGRESS_IP:
            service_id = None #to ensure the address service_id goes to null
        
        c = release_all_ip_to_customer_ip_pool(dbh, acct_id,
                my_compute_region.get_param("cloud_provider"),
                my_compute_region.get_compute_region_native_name(),
                nlb_node_type,
                service_id)

        if c == -1:
            logger.error('Failed to release_all_nlb_ips')
            return -1
        else:
            logger.info('%s nlb IPs released to customer pool.' % c)
            return c
    except Exception as e:
        logger.error("Unexpected error in release_all_ip_to_customer_ip_pool: %s" % str(e))
        return -1

class IPHandler(object):
    def __init__(self, dbh, cluster_id, acct_id, node_id=None):
        self.dbh = dbh
        self.logger = dbh.logger
        self.cluster_id = cluster_id
        self.acct_id = acct_id
        self.node_id = node_id
        self.group_support_acct_id = 0
        self.ipam_utils = create_ipam_utils(dbh=dbh, logger=self.logger)

        self.cloud_provider = None
        self.node_type = None
        self.alt_node_type = None
        self.compute_region_id = None
        self.get_instance_location_node_type_cloud()

        # If ip management is for firewalls which are part of interconnect populate the required field.
        self.is_using_sp_interconnect = False
        self.root_tsg_id = 0
        self.host_project_id = None
        self.populate_is_using_sp_interconnect()
        if self.is_using_sp_interconnect:
            custmodel = CustomerModel(dbh=dbh, acct_id=acct_id)
            self.root_tsg_id = custmodel.get_param("root_tsg_id")
            self.host_project_id = custmodel.get_param("host_project_id")

        if self.node_type != NODE_TYPE_SERVICE_CONN:
            self.get_group_support_acct_id()

        self.auto_reserve_ip = None
        if self.node_type in (NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY, NODE_TYPE_GP_GATEWAY, NODE_TYPE_GP_PORTAL):
            self.auto_reserve_ip = self.get_auto_reserve_ip_flag()

        self.native_region_name = None
        self.get_native_compute_region_name()
        self.use_PBF = True

    def get_edge_ip_location_info_for_reserve(self):
        # Fetch the remaining edge locations

        total_edge_location_list = []
        self.logger.info("get_edge_ip_location_info_for_reserve: called")
        sql_cmd = ("SELECT edge_location_region_id, country, city FROM "
                   "region_master WHERE compute_region_id = %s" % (self.compute_region_id))

        self.logger.info("sql command: %s" % sql_cmd)
        try:
            cursor1 = self.dbh.get_cursor()
            cursor1.execute(sql_cmd)
            ret = cursor1.fetchall()
            self.dbh.cursorclose(cursor1)
        except Exception as ex:
            self.logger.error("Failed to get edge location info from "
                              "region_master table for compute region %s. "
                              "Exception: %s" % (self.compute_region_id, ex))
            self.dbh.cursorclose(cursor1)
            return total_edge_location_list

        if not ret:
            self.logger.info("No edge location info found in region_master "
                             "table for compute region %s" %
                             self.compute_region_id)
            return total_edge_location_list

        self.logger.info("Edge location info for compute region %s "
                         "of node type %s: %s" %
                         (self.compute_region_id, self.node_type, str(ret)))
        for row in ret:
            if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                                       compute_region_info=self.native_region_name) == IPAMService.ENABLED:
                active_count = self.ipam_utils.ipam_fetch_address(location=self.native_region_name,
                                                                  account_id=self.acct_id,
                                                                  country=row[1],
                                                                  city=row[2],
                                                                  status='active')
                reserved_count = self.ipam_utils.ipam_fetch_address(location=self.native_region_name,
                                                                    account_id=self.acct_id,
                                                                    country=row[1],
                                                                    city=row[2],
                                                                    status='reserved')
                preallocated_count = self.ipam_utils.ipam_fetch_address(location=self.native_region_name,
                                                                        account_id=self.acct_id,
                                                                        country=row[1],
                                                                        city=row[2],
                                                                        status='pre_allocated')
                if active_count or reserved_count or preallocated_count:
                    self.logger.info("public ip pool has active, reserved or preallocated status %s %s %s " % \
                                     (self.native_region_name, row[1], row[2]))
                    total_edge_location_list.append([row[0], row[1], row[2]])
            else:
                sql_cmd = ("SELECT count(ip) FROM public_ip_pool WHERE "
                           "acct_id=%s and "
                           "location = '%s' and country = '%s'and  city = '%s' and status in ('active','reserved','pre_allocated')" %
                           (self.acct_id, self.native_region_name, row[1], row[2]))
                self.logger.info("sql command: %s" % sql_cmd)
                try:
                    cursor1 = self.dbh.get_cursor()
                    cursor1.execute(sql_cmd)
                    ip_count_result = cursor1.fetchall()
                    self.dbh.cursorclose(cursor1)
                except Exception as ex:
                    self.logger.error("Failed to get edge location info from "
                                      "region_master table for compute region %s. "
                                      "Exception: %s" % (self.compute_region_id, ex))
                    self.dbh.cursorclose(cursor1)
                    continue
                if int(ip_count_result[0][0]):
                    self.logger.info("public ip pool has active, reserved or preallocated status %s %s %s " % \
                    (self.native_region_name, row[1], row[2]))
                    total_edge_location_list.append([row[0], row[1], row[2]])

        return total_edge_location_list


    def get_edge_ip_location_info(self, edge_region_id, include_compute_region, egress_ip_list_loc=None):
        """
        From region master table, get the edge location city and country name
        which will be serviced by certain node type in this compute region.

        Depending on the node type and compute region id and cloud type,
        decide how many ip address, a.k.a, number of edge ip location, to
        allocate. Below is the design.
        GW/RN: allocate IP addresses based on region_master table. may have
            multiple IPs
        PT/SC: allocate IP address where PT/SC is located physically, no
            need to have multiple IPs

        @rtype: list of list of str
        @return: list of [edge_location_region_id, country, city]
            the country and city where the compute region locates should
            be the first one in the return list
        """
        self.logger.info("Get edge locations list by node type %s alt_node_type %s in "
                         "compute region %s edge_region_id %s include_compute_region %s" %
                         (self.node_type, self.alt_node_type, self.compute_region_id, edge_region_id, include_compute_region))

        total_edge_location_list = []

        try:
            ret = []
            if include_compute_region == True:

                self.logger.info("Get the compute location country and city")

                sql_cmd = ("SELECT edge_location_region_id, country, city FROM "
                           "region_master WHERE edge_location_region_id = %s" %
                           self.compute_region_id)
                self.logger.info("sql command: %s" % sql_cmd)

                cursor = None
                try:
                    cursor = self.dbh.get_cursor()
                    cursor.execute(sql_cmd)
                    ret = cursor.fetchone()
                    self.dbh.cursorclose(cursor)
                except Exception as ex:
                    self.logger.error("Failed to get edge location info from "
                                      "region_master table for edge location %s. "
                                      "Exception: %s" % (self.compute_region_id, ex))
                    if cursor:
                        self.dbh.cursorclose(cursor)
                    return []

                if not ret:
                    self.logger.info("No edge location info found in region_master "
                                     "table for edge location %s" %
                                     self.compute_region_id)
                    return []

                self.logger.info("Compute location info for edge %s: %s" %
                                 (self.compute_region_id, str(ret)))
                total_edge_location_list = [[ret[0], ret[1], ret[2]]]

            if (self.node_type == NODE_TYPE_SERVICE_CONN or self.node_type == NODE_TYPE_GP_PORTAL or
                    self.node_type == NODE_TYPE_CLEAN_PIPE):
                return total_edge_location_list
            else:
                cursor1 = None
                if egress_ip_list_loc != None:
                    for my_egress_ip_loc in egress_ip_list_loc:
                        sql_cmd = ("SELECT edge_location_region_id, country, city FROM "
                                   "region_master WHERE "
                                   "edge_location_region_id = %s" %
                                   (my_egress_ip_loc))
                        self.logger.info("sql command: %s" % sql_cmd)
                        try:
                            cursor1 = self.dbh.get_cursor()
                            cursor1.execute(sql_cmd)
                            ret = cursor1.fetchall()
                            self.dbh.cursorclose(cursor1)
                            for row in ret:
                                if [row[0], row[1], row[2]] not in total_edge_location_list:
                                    self.logger.info("Adding [%s, %s, %s] to egress_ip_list_loc" %
                                                     (str(row[0]), str(row[1]), str(row[2])))
                                    total_edge_location_list.append([row[0], row[1], row[2]])
                        except Exception as ex:
                            err_msg = ("Failed to get edge location info from "
                                       "region_master table for compute region %s. "
                                       "Exception: %s" % (my_egress_ip_loc, ex))
                            if cursor1:
                                self.dbh.cursorclose(cursor1)
                            raise Exception(err_msg)

                else:
                    # Fetch the remaining edge locations
                    if edge_region_id == None:
                        sql_cmd = ("SELECT edge_location_region_id, country, city FROM "
                                   "region_master WHERE compute_region_id = %s AND "
                                   "edge_location_region_id <> %s" %
                                   (self.compute_region_id, self.compute_region_id))
                    else:
                        sql_cmd = ("SELECT edge_location_region_id, country, city FROM "
                                   "region_master WHERE edge_location_region_id = %s AND "
                                   "edge_location_region_id <> %s" %
                                   (edge_region_id, self.compute_region_id))

                    self.logger.info("sql command: %s" % sql_cmd)
                    try:
                        cursor1 = self.dbh.get_cursor()
                        cursor1.execute(sql_cmd)
                        ret = cursor1.fetchall()
                        self.dbh.cursorclose(cursor1)
                    except Exception as ex:
                        self.logger.error("Failed to get edge location info from "
                                          "region_master table for compute region %s. "
                                          "Exception: %s" % (self.compute_region_id, ex))
                        self.dbh.cursorclose(cursor1)
                        return total_edge_location_list

                    if not ret:
                        self.logger.info("No edge location info found in region_master "
                                         "table for compute region %s" %
                                         self.compute_region_id)
                        return total_edge_location_list

                    self.logger.info("Edge location info for compute region %s "
                                     "of node type %s alt_node_type %s: %s" %
                                     (self.compute_region_id, self.node_type, self.alt_node_type, str(ret)))
                    for row in ret:
                        total_edge_location_list.append([row[0], row[1], row[2]])

        except Exception as E:
            self.logger.error("Failed with exception %s: %s" % (str(E.args), str(traceback.format_exc())))
            total_edge_location_list = []
        finally:
            return total_edge_location_list

    def allocate_ip_list_from_public_ip_pool(self, edge_id_country_city_list):
        """
        Pick IP address from public_ip_pool table which is registered
        in the same country and city, and preferrably is reserved by the
        same account.

        @type edge_id_country_city_list: list of list of str
        @param edge_id_country_city_list: list of
            [edge_location_region_id, country, city]
        @rtype: list of list of str
        @return: list of [edge id, IP, ip_type]
            In the list, the first IP will be stored in public_ip
            column in instance_master table. Other should be stored in
            egress_ip_list column.

        Throws Exception for cases where PUPI IP allocation fails.
        """
        self.logger.info("For account %s, try to acquire IP address registerd "
                         "in %s in %s and is_using_sp_interconnect: %s" %
                         (self.acct_id, edge_id_country_city_list,
                          self.cloud_provider, self.is_using_sp_interconnect))

        res = []
        for idx, entry in enumerate(edge_id_country_city_list):
            # Get the IP registered in the country and the city
            edge_id = str(entry[0])
            country = str(entry[1])
            city = str(entry[2])
            ip = None
            ip_type = None

            if self.cloud_provider == 'AWS' and idx > 0:
                self.logger.info("Not allocating IPs in %s, %s in AWS, since "
                                 "we do not support multiple IP in AWS." %
                                 (country, city))
                continue

            try:
                ip, ip_type = self.get_ip_for_country_city_cloud(
                    country, city, self.cloud_provider)
            except Exception as ex:
                self.logger.error("Failed to get IP for %s in %s, %s, %s. "
                                  "Exception: %s" %
                                  (self.acct_id, city, country,
                                   self.cloud_provider, ex))
                # If we cannot get one or more IP addresses, proceed to get
                # other addresses

                if self.is_using_sp_interconnect:
                    raise PupiAllocationFailureException(f"Failed to Allocate PUPI IP's for acct_id: {self.acct_id}")

                continue
            self.logger.info("Allocated IP %s" % ip)
            if not ip:
                # No IP available, might need to send a message to DevOps
                if self.is_using_sp_interconnect:
                    raise PupiAllocationFailureException(f"PUPI IP not found for acct_id: {self.acct_id}")

                self.logger.info("Failed to get IP for %s in %s, %s, %s. " %
                                 (self.acct_id, city, country,
                                  self.cloud_provider))
                res.append([edge_id, None, None])
            else:
                self.logger.info("Got %s IP %s for acct id %s. IP is "
                                 "registered in %s, %s" %
                                 (self.cloud_provider, str(ip), self.acct_id,
                                  city, country))
                res.append([edge_id, ip, ip_type])
        self.logger.info(res)
        return res

    def get_service_ip_for_country_city_cloud(self, country, city,
            cloud_provider, service_type, service_id, enable_tls_term_on_ep):
        try:
            if enable_tls_term_on_ep:
                region_name = 'global'
            else:
                region_name = self.native_region_name
            args = (str(country), str(city), str(cloud_provider), self.acct_id, self.group_support_acct_id, service_type,
                    region_name, service_id)
            if None in args:
                self.logger.error("Failed to acquire IPs! Invalid args: %s" % str(args))
                return None
            if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                                       compute_region_info=region_name) == IPAMService.ENABLED:
                res = self.ipam_utils.ipam_allocate_address(match_location=region_name,
                                                            match_city=str(city),
                                                            match_country=str(country),
                                                            match_cloud=str(cloud_provider),
                                                            match_group_support_account_id=self.group_support_acct_id,
                                                            match_account_id=self.acct_id,
                                                            match_node_type=service_type,
                                                            match_count=1, set_status="active", set_service_id=service_id,
                                                            call_context="orch:get_service_ip_for_country_city_cloud")
                if not res or len(res) == 0:
                    self.logger.error(f"0 IP acquire from the region {region_name}")
                    return None
                return res[0]
            else:
                sql = ["CALL service_acquire_public_ip('%s','%s','%s',%s,%s,%s,'%s','%s',@public_ip,@new_address,@address_type)" % args]
                self.logger.info("sql: %s " % str(sql))
                sql.append("SELECT @public_ip")
                sql_cmd = ";".join(sql)
                res = dbconn.execute_lambda_query(sql_cmd, {'multi_query': True})
                if not res['ok'] or not res['result']:
                    self.logger.error("Failed to acquire IPs! Failed sql: %s, %s" % (sql, res))
                    return None
                elif len(res['result']) == 0:
                    self.logger.error("0 IP acquire from the region. sql: %s" % sql)
                    return None
                else:
                    self.logger.info("IP %s acquired" % (res['result'][0][0]))
                    return res['result'][0][0]
        except Exception as e:
            self.logger.error("Unexpected error in get_service_ip_for_country_city_cloud: %s locals %s. Traceback: %s" % (str(e.args),
                                                                              str(locals()),
                                                                              str(traceback.format_exc())))
            return None

    def get_ip_for_country_city_cloud(self, country, city, cloud_provider):
        """
        Get one available IP address from public_ip_pool which is
        registered in the country/city/cloud provider. Preferrably
        get the IP address already allocated under this account. If
        not available, then allocate a new IP address.

        @type country: str
        @param country: country where IP is registered with
        @type city: str
        @param city: city where IP is registered with
        @type cloud_provider: str
        @param cloud_provider: name of cloud provider, such as AWS or GCP
        @rtype: str, str
        @return: IP address in public_ip_pool
            IP type, BYOIP or public_cloud or PUPI
        """

        self.logger.info("Try to acquire IP in %s, %s, %s" %
                         (str(country), str(city), str(cloud_provider)))
        ip = None
        ip_type = None

        if self.node_type == 48 or self.node_type == 51 or self.node_type == NODE_TYPE_CLEAN_PIPE:
            store_proc = 'fw_acquire_public_ip'
            args = [str(country), str(city), str(cloud_provider), self.acct_id, self.group_support_acct_id,
                    self.cluster_id, self.node_type, self.native_region_name, 0, 0, 0]
        elif self.node_type in (49, 50, 153, 154, NODE_TYPE_BI_NH_PROXY):
            store_proc = 'gp_acquire_public_ip'
            args = [str(country), str(city), str(cloud_provider), self.acct_id, self.group_support_acct_id,
                    self.cluster_id, self.node_type, self.native_region_name, 0, 0, 0]
        else:
            self.logger.error("Invalid node type %s" % self.node_type)
            return None, None

        if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                                   compute_region_info=self.native_region_name) == IPAMService.ENABLED:
            res = self.ipam_utils.ipam_allocate_address(match_cloud=cloud_provider,
                                                        match_location=self.native_region_name,
                                                        match_type='pupi' if self.is_using_sp_interconnect else None,
                                                        match_city=city,
                                                        match_country=country,
                                                        match_host_project_id=self.host_project_id if self.is_using_sp_interconnect and cloud_provider.lower() == 'gcp' else None,
                                                        match_group_support_account_id=self.group_support_acct_id,
                                                        match_account_id=self.acct_id,
                                                        match_node_type=self.node_type,
                                                        match_count=1, set_status="active", set_cluster_id=self.cluster_id,
                                                        set_node_type=self.node_type, call_context=f"orch:{store_proc}",
                                                        fetch_fields=['type'])

            if len(res) == 0 and self.is_using_sp_interconnect:
                res = self.ipam_utils.ipam_allocate_address(match_cloud=cloud_provider,
                                                            match_location=self.native_region_name,
                                                            match_type='pupi',
                                                            match_city=city,
                                                            match_country=country,
                                                            match_host_project_id=self.host_project_id if cloud_provider.lower() == 'gcp' else None,
                                                            match_group_support_account_id=self.group_support_acct_id,
                                                            match_account_id=self.root_tsg_id,
                                                            match_node_type="161" if str(self.node_type) == "161" else "0",
                                                            match_count=1, set_status="active", set_cluster_id=self.cluster_id,
                                                            set_account_id=self.acct_id,
                                                            set_node_type=self.node_type, call_context=f"orch:{store_proc}",
                                                            fetch_fields=['type'])

            if not res or len(res) == 0:
                self.logger.error(f"Unable to acquire public IP for {str(self.cluster_id)}. ")
                return None, None
            # res[0] is now [ip, type] when fetch_fields=['type'] is used
            ip = res[0][0]
            if res[0][1] != None:
                ip_type = res[0][1].upper()
        else:
            self.logger.info("stored procedure: %s, args: %s" %
                             (str(store_proc), str(args)))

            cursor = self.dbh.get_cursor(buffered=True)
            try:
                res = cursor.callproc(store_proc, args)
                self.dbh.cursorclose(cursor)
                self.dbh.conn.close()
            except Exception as ex:
                self.logger.error("Unable to acquire public IP for %s. "
                                  "Exception: %s" %
                                  (str(self.cluster_id), str(ex)))
                self.dbh.cursorclose(cursor)
                self.dbh.conn.close()
                return None, None
            self.logger.info("Return result: %s" % str(res))
            ip = res[8]
            ip_type = res[-1]
            if len(res) == 11:
                new_address = res[9]
                if new_address:
                    self.logger.info("Customer %s acquire a new IP %s in %s, "
                                     "%s, %s. Needs to whitelist it." %
                                     (self.acct_id, ip, city, country,
                                      self.cloud_provider))
        return ip, ip_type

    def get_instance_location_node_type_cloud(self):
        """
        Get the compute region id, node type and cloud provider
        from instance_master table based on cluster id.

        @return: None
        """
        self.logger.info("Try to get compute region id for cluster id %s" %
                         self.cluster_id)

        ret = None
        sql_cmd = ("SELECT compute_region_idx, node_type, cloud_provider, alt_node_type FROM "
                   "instance_master WHERE id = '%s'" %
                   str(self.cluster_id))
        self.logger.info("sql command: %s" % sql_cmd)
        try:
            cursor = self.dbh.get_cursor()
            cursor.execute(sql_cmd)
            ret = cursor.fetchone()
            self.dbh.cursorclose(cursor)
        except Exception as ex:
            self.logger.error("Failed to get compute location and node type "
                              "from instance_master table for cluster %s. "
                              "Exception: %s" % (self.cluster_id, ex))
            self.dbh.cursorclose(cursor)
            return
        self.logger.info("Return result: %s" % str(ret))
        if ret:
            self.cloud_provider = ret[2].upper()
            self.node_type = ret[1]
            self.alt_node_type = ret[3]
            # We overwrite the node_type for NAT instances to be that of a GPGW so that it
            # plays well with the IP Allow list queries
            if self.node_type == NODE_TYPE_NAT_INSTANCE:
                self.node_type = NODE_TYPE_GP_GATEWAY
            self.compute_region_id = ret[0]

    def populate_is_using_sp_interconnect(self):
        """
        Check if the sp_interconnect is used for this instance.
        populates is_using_sp_interconnect field with those details.
        Default is already set to False
        """
        self.logger.info("Try to get is_using_sp_interconnect for cluster id %s" %
                         self.cluster_id)

        sql_cmd = ("SELECT is_using_sp_interconnect FROM instance_master WHERE id = '%s'" %
                   str(self.cluster_id))
        self.logger.info("sql command: %s" % sql_cmd)
        try:
            cursor = self.dbh.get_cursor()
            cursor.execute(sql_cmd)
            ret = cursor.fetchone()
            self.dbh.cursorclose(cursor)
        except Exception as ex:
            self.logger.error("Failed to get is_using_sp_interconnect "
                              "from instance_master table for cluster %s. "
                              "Exception: %s" % (self.cluster_id, ex))
            self.dbh.cursorclose(cursor)
            return

        self.logger.info("Return populate_is_using_sp_interconnect result: %s" % str(ret))

        if ret:
            self.is_using_sp_interconnect = bool(ret[0])
            self.logger.info("Updating IPHandler object with "
                             "is_using_sp_interconnect: {}".format(self.is_using_sp_interconnect))

    def get_auto_reserve_ip_flag(self):
        """Check if the customer configured to add more IP to service when necessary"""
        key_map = {
            NODE_TYPE_GP_PORTAL: 'gp_auto_reserve_ip',
            NODE_TYPE_GP_GATEWAY: 'gp_auto_reserve_ip',
            NODE_TYPE_SWG_PROXY: 'swg_auto_reserve_ip',
            NODE_TYPE_BI_NH_PROXY: 'swg_auto_reserve_ip' # reusing swgproxy flag
        }
        sql = "select %s from cust_master where acct_id=%s" % (key_map[int(self.node_type)], self.acct_id)
        result = dbconn.execute_lambda_query(sql, None, logger=self.logger)
        if not result['ok']:
            self.logger.error(f"Failed to get auto_reserve_ip from RDS {sql}")
            return None
        return bool(int(result['result'][0][0]))

    @property
    def cloud_exclusion_list(self):
        try:
            return self._cloud_exclusion_list # e.g. ['aws']
        except:
            sql = "select excluded_clouds from cust_master where acct_id=%s" % self.acct_id
            result = dbconn.execute_lambda_query(sql, None, logger=self.logger)
            if not result['ok']:
                self.logger.error("Failed to _get_cloud_native_compute_location_name from RDS")
                return None
            try:
                self._cloud_exclusion_list = json.loads(result['result'], encoding="utf-8").get('excluded_clouds', [])
            except:
                self._cloud_exclusion_list = []
            return self._cloud_exclusion_list

    def get_group_support_acct_id(self):
        """
        Get the group support acct id
        from cust_master table based on acct id.

        @return: None
        """

        self.group_support_acct_id = 0
        self.logger.info("Try to get group support acct id from acct id %s" %
                         self.acct_id)

        ret = None
        sql_cmd = ("SELECT group_support_acct_id FROM "
                   "cust_master WHERE acct_id = '%s'" %
                   str(self.acct_id))
        self.logger.info("sql command: %s" % sql_cmd)
        try:

            cursor = self.dbh.get_cursor()
            cursor.execute(sql_cmd)
            ret = cursor.fetchone()
            self.dbh.cursorclose(cursor)
        except Exception as ex:
            self.logger.error("Failed to get group support acct id"
                              "from cust_master table for acct id %s. "
                              "Exception: %s" % (self.acct_id, ex))
            self.dbh.cursorclose(cursor)
            return
        self.logger.info("Return result: %s" % str(ret))
        if ret != None:
            self.group_support_acct_id = ret[0]
        return

    def write_ip_to_instance_master_table(self, ip_list, include_compute_region=True):
        """
        Write IP address and its edge_location_region_id to the entry
        of cluster id in instance_master table.

        @type ip_list: list of list of str
        @param ip_list: list of [IP, edge_location_region_id]
        @return: None
        """
        if self.cloud_provider in ('GCP', 'OCI') and include_compute_region != False:
            self.logger.info(f"Always set use_PBF as False for {self.cloud_provider}")
            self.use_PBF = USE_PBF_FOR_CLEAN_IP_FROM_GCP
            sql_cmd = ("UPDATE instance_master SET use_PBF = %s "
                       "WHERE id = '%s'" %
                       (self.use_PBF, str(self.cluster_id)))

            self.logger.info("sql command: %s" % sql_cmd)
            try:
                cursor = self.dbh.get_cursor()
                cursor.execute(sql_cmd)
                self.dbh.cursorclose(cursor)
                self.logger.info("Successfully update use_PBF field for "
                                 "cluster %s" % str(self.cluster_id))

            except Exception as ex:
                self.logger.error("Failed to update instance_master table "
                                  "use_PBF field for cluster %s. "
                                  "Exception: %s" % (self.cluster_id, ex))
                self.dbh.cursorclose(cursor)

        if include_compute_region != False:
            self.logger.info("Update public_ip and egress_ip_list column in "
                             "instance_master table for %s" % self.cluster_id)
            if len(ip_list) == 0:
                self.logger.error("No available IP.")
                return
            # Add the first IP to the public_ip field in instance_master table
            if not ip_list[0][1]:
                self.logger.error("Cannot get IP for public_ip field for %s" %
                                  self.cluster_id)
                return
            else:
                # self.update_column_use_PBF(self.use_PBF)
                sql_cmd = ("UPDATE instance_master SET use_PBF = %s, public_ip = '%s' "
                           "WHERE id = '%s'" %
                           (self.use_PBF, ip_list[0][1], str(self.cluster_id)))

                self.logger.info("sql command: %s" % sql_cmd)
                try:
                    cursor = self.dbh.get_cursor()
                    cursor.execute(sql_cmd)
                    self.dbh.cursorclose(cursor)
                except Exception as ex:
                    self.logger.error("Failed to update instance_master table "
                                      "public_ip field for cluster %s. "
                                      "Exception: %s" % (self.cluster_id, ex))
                    self.dbh.cursorclose(cursor)
                    return
                self.logger.info("Successfully update public_ip for cluster %s" %
                                 str(self.cluster_id))

        cursor = self.dbh.get_cursor(self.dbh)
        sql_cmd = ("SELECT public_ip, egress_ip_list, use_PBF FROM instance_master "
                   "WHERE id = %s" % self.cluster_id)
        self.logger.info("sql command: %s" % sql_cmd)

        try:
            cursor.execute(sql_cmd)
            ret = cursor.fetchone()
        except Exception as ex:
            self.logger.error("Failed to get public_ip and egress_ip_list from "
                              "instance_master table for %s. Exception: %s" %
                              (self.cluster_id, ex))
            self.dbh.cursorclose(cursor)
            return

        self.logger.info("Active instance %s public ip is %s, egress_ip_list "
                         "is %s, use_PBF is %s" %
                         (self.cluster_id, str(ret[0]), str(ret[1]), ret[2]))

        # Add the remaining IP addresses to egress_IP_list field
        ip_str = ""
        egress_ip_list = ret[1]
        edges = {}
        if include_compute_region==False:
           if egress_ip_list != None and len(egress_ip_list) != 0:
              edges = json.loads(egress_ip_list)
              self.logger.info("egress_ip_list already exists %s" % egress_ip_list)
           start_index = 0
        else:
            start_index = 1
        for i in range(start_index, len(ip_list)):
            if ip_list[i][1]:  # IP available for that edge location
                edges[ip_list[i][0]] = ip_list[i][1]
        ip_str = json.dumps(edges)
        self.logger.info("String to insert in egress_IP_list field: %s" %
                         ip_str)

        if len(ip_str) == 0:
            self.logger.info("No additional IP is associated with %s" %
                             self.cluster_id)
        else:
            sql_cmd = ("UPDATE instance_master SET egress_ip_list = '%s' "
                       "WHERE id = '%s'" %
                       (ip_str, str(self.cluster_id)))
            self.logger.info("sql command: %s" % sql_cmd)
            try:
                cursor = self.dbh.get_cursor()
                cursor.execute(sql_cmd)
                self.dbh.cursorclose(cursor)
            except Exception as ex:
                self.logger.error("Failed to update instance_master table "
                                  "egress_ip_list field for cluster %s. "
                                  "Exception: %s" % (self.cluster_id, ex))
                self.dbh.cursorclose(cursor)
                return
            self.logger.info("Successfully update egress_ip_list for cluster %s" %
                             str(self.cluster_id))

    def update_ip_status_to_reserve(self, edge_id_country_city_list):
        """
        Update ip status to reserve from pre_allocated if any
        :param all_edge_location_list:
        :return:
        """
        for item in edge_id_country_city_list:
            country = item[1]
            city = item[2]
            if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                                       compute_region_info=self.native_region_name) == IPAMService.ENABLED:
                self.ipam_utils.ipam_update_address(match_country=country,
                                                    match_city=city,
                                                    match_location=self.native_region_name,
                                                    match_status='pre_allocated',
                                                    match_account_id=self.acct_id,
                                                    match_group_support_account_id=self.group_support_acct_id,
                                                    match_node_type=self.node_type,
                                                    set_status="reserved", call_context="orch:update_ip_status_to_reserve")
            else:
                sql_cmd = ("UPDATE public_ip_pool SET status = 'reserved' "
                           "WHERE country = '%s' AND city= '%s' AND location = '%s' AND status='pre_allocated' AND acct_id = %s AND "
                "group_support_acct_id=%s AND node_type=%s " %
                           (country, city, self.native_region_name, self.acct_id, self.group_support_acct_id, self.node_type))
                self.logger.info(sql_cmd)

                try:
                    cursor = self.dbh.get_cursor(prepared=True)
                    cursor.execute(sql_cmd)
                    self.dbh.cursorclose(cursor)
                except Exception as ex:
                    self.logger.error("Failed to update status in public ip pool %s, "
                                      "exception %s" % (self.cluster_id, ex))
                    self.dbh.cursorclose(cursor)
                    return
                self.logger.info("Update status done in public ip pool")

    def allocate_public_ip_for_customer(self, old_public_ip, edge_region_id=None, include_compute_region=True):
        """
        Main entrance to allocate IPs for GP and FW, also reserve IPs
        if necessary

        @type old_public_ip: str
        @param old_public_ip: the public IP from CFT
        @rtype: list of list of str
        @return: list of [IP address, edge id]
            In the list, the first IP will be stored in public_ip
            column in instance_master table. Other should be stored in
            egress_ip_list table.

        can raise: PupiAllocationFailureException

        """
        # allocate IP address and write to the instance_master table

        # 0. Migration check e.g. Zurich and Switzerland were moved to another compute.
        #
        # Check if we have a compute instance where given edge_region_id = compute_region_id
        # How will we check ?
        inst_model = None
        edge_location_list = []
        if edge_region_id != None:
            my_compute_location_idx = \
                gpcs_get_compute_region_idx_from_edge_region_idx(dbh=self.dbh,
                                                                 edge_region_idx=edge_region_id, acct_id=self.acct_id)

            if str(my_compute_location_idx) == str(edge_region_id):
                # Here we need to find an instance that is pinned in our compute_location_idx and get the egress_ip_list
                # from it.
                from libs.model.instancemodel import InstanceModel
                inst_model = InstanceModel(iid=self.cluster_id, dbh=self.dbh)
                if inst_model and inst_model.get_param("id"):
                    if inst_model.get_param("node_type") == NODE_TYPE_GP_GATEWAY and (inst_model.get_param("is_pinned_instance")!=1 or inst_model.get_param("upgrade_creation") == 1):
                        egress_ip_list_loc = \
                            inst_model. \
                                get_existing_egress_ip_region_list_for_pinned_instance(inst_model.get_param("custid"),
                                                                                       inst_model.get_param("compute_region_idx"))
                        edge_location_list = self.get_edge_ip_location_info(edge_region_id,
                                                                            include_compute_region,
                                                                            egress_ip_list_loc)

        # 1. get edge location list of [edge_location_region_id, country, city]
        if not edge_location_list:
            self.logger.info("edge_location_list empty. Get ip location info "
                            "using edge id only")
            edge_location_list = self.get_edge_ip_location_info(edge_region_id, include_compute_region)

        # 2. for edge locations, get list of [IP address, edge id]
        # Below invocation will throw exception when IP allocation failure occurs for PUPI allocation.
        ip_list = self.allocate_ip_list_from_public_ip_pool(edge_location_list)

        self.logger.info("ip_list is: %s" % str(ip_list))

        if include_compute_region == True:
            if len(ip_list) == 0 or not ip_list[0][1]:
                self.logger.error("No available public ip in public_ip_pool "
                                  "table for %s. Will allocate elastic IP" %
                                  self.cluster_id)

                temp_ip = self.reserve_sticky_ip(1, self.cluster_id)
                if len(temp_ip) > 0:
                    self.logger.info("Allocated elastic IP %s" % temp_ip)
                    ip_list[0] = [self.compute_region_id, str(temp_ip[0]),
                                  'public_cloud']
                else:
                    self.logger.error("Failed to allocate elastic IP. "
                                      "Use EIP %s from CFT." % old_public_ip)
                    ip_list.append([self.compute_region_id, old_public_ip,
                                    'public_cloud'])
                    self.logger.info("Also release IPs in edge location")
                    self.reset_public_ip_pool_info()
            else:
                self.logger.info("Found IP %s in public_ip_pool to associate "
                                 "with %s" % (ip_list[0][1], self.cluster_id))

        # 3. write the ip address to instance_master table
        self.write_ip_to_instance_master_table(ip_list, include_compute_region)

        # 4. reserve ip addresses for GP
        # If it is under upgrade, skip
        # if it is GP, then check reserve IP addresses
        if self.node_type in (NODE_TYPE_GP_GATEWAY, NODE_TYPE_CLEAN_PIPE, NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY,
                              NODE_TYPE_GP_PORTAL, NODE_TYPE_NLB_INSTANCE, NODE_TYPE_NAT_INSTANCE):
            if not self.creation_by_upgrade():
                self.logger.info("Instance %s is created by new deployment or "
                                 "auto-scale. Need to reserve another set "
                                 "of IP addresses and node_type %s, type %s, alt_node_type %s" % (self.cluster_id, self.node_type, type(self.node_type), self.alt_node_type))

                if self.node_type == NODE_TYPE_CLEAN_PIPE: #for clean pipe reserve ip only in the compute region and not in other edge locations

                    #reserve the ip
                    self.reserve_ip_for_customer(edge_location_list)

                    #update the public_ip_pool table for pre allocated ips
                    self.update_ip_status_to_reserve(edge_location_list)

                elif (self.node_type in (NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY)) and (self.auto_reserve_ip is True):
                    city_country_set_list, cloud, location = self.get_deployed_city_country_cloud_location(self.acct_id, self.compute_region_id, self.node_type)
                    if not inst_model:
                        from libs.model.instancemodel import InstanceModel
                        inst_model = InstanceModel(iid=self.cluster_id, dbh=self.dbh)
                    self.reserve_double_if_necessary(self.acct_id, self.group_support_acct_id, self.compute_region_id, self.node_type, city_country_set_list, cloud, location, inst_model.get_param("has_nat_instance"), inst_model.get_param("custid"), inst_model.get_param("is_using_sp_interconnect"))
                    self.update_pre_allocate_to_reserve(self.acct_id, self.compute_region_id, self.node_type, city_country_set_list, cloud, location)

                elif ((self.node_type == NODE_TYPE_GP_GATEWAY or
                       self.node_type == NODE_TYPE_NLB_INSTANCE or self.node_type == NODE_TYPE_NAT_INSTANCE) and
                      (self.auto_reserve_ip is True)):
                    city_country_set_list, cloud, location = self.get_deployed_city_country_cloud_location(self.acct_id, self.compute_region_id, self.node_type)
                    if not inst_model:
                        from libs.model.instancemodel import InstanceModel
                        inst_model = InstanceModel(iid=self.cluster_id, dbh=self.dbh)
                    pa_to_c_c_c_l_map = get_configured_gw_city_country_cloud_location_by_compute(self.logger, self.acct_id, inst_model.get_param("custid"), self.compute_region_id, cloud_exclusion_list=self.cloud_exclusion_list)
                    city_country_set_set = set()
                    for pa in pa_to_c_c_c_l_map:
                        city, country, cloud, location = pa_to_c_c_c_l_map[pa]
                        city_country_set_set.add((city, country))
                    city_country_set_list = list(set(city_country_set_list).union(city_country_set_set))
                    self.reserve_double_if_necessary(self.acct_id, self.group_support_acct_id, self.compute_region_id, self.node_type, city_country_set_list, cloud, location, inst_model.get_param("has_nat_instance"), inst_model.get_param("custid"), inst_model.get_param("is_using_sp_interconnect"))
                    self.update_pre_allocate_to_reserve(self.acct_id, self.compute_region_id, self.node_type, city_country_set_list, cloud, location)

                # excpetion for portal sf, cust_topology sf, but instance_master LA
                elif (self.node_type == NODE_TYPE_GP_PORTAL) and (self.auto_reserve_ip is True):
                    city_country_set_list, cloud, location = self.get_deployed_city_country_cloud_location(self.acct_id, self.compute_region_id, self.node_type)
                    if not inst_model:
                        from libs.model.instancemodel import InstanceModel
                        inst_model = InstanceModel(iid=self.cluster_id, dbh=self.dbh)
                    self.reserve_double_if_necessary(self.acct_id, self.group_support_acct_id, self.compute_region_id, self.node_type, city_country_set_list, cloud, location, inst_model.get_param("has_nat_instance"), inst_model.get_param("custid"), inst_model.get_param("is_using_sp_interconnect"))
                    self.update_pre_allocate_to_reserve(self.acct_id, self.compute_region_id, self.node_type, city_country_set_list, cloud, location)

            else:
                self.logger.info("Instance %s is created by upgrade. "
                                 "No need to reserve another set "
                                 "of IP addresses" % self.cluster_id)

        if self.node_type == 48 and self.node_id:
            self.logger.info("Check if inbound_access is enabled for cust node %s. " % self.node_id)
            from libs.model.custnodemodel import CustNodeModel

            cust_node_model = CustNodeModel(iid=self.node_id, dbh=self.dbh)
            inbound_access = cust_node_model.inbound_access
            if inbound_access is not None and str(inbound_access).lower() != 'null' and str(
                    inbound_access).lower() != 'disabled':
                self.logger.info("inbound_access is enabled for cust node %s. " % self.node_id)
                self.init_inbound_access_ip_mapping(node_id=self.node_id)
            else:
                self.logger.info("inbound_access is not enabled for cust node %s. " % self.node_id)

        return ip_list

    def creation_by_upgrade(self):
        """
        Check if this instance is created by upgrade process

        @rtype: boolean
        @return: whether this instance is created by upgrade process
        """
        self.logger.info("Check if the instance is created by upgrade.")
        res = False

        sql_cmd = ("SELECT upgrade_creation FROM instance_master "
                   "WHERE id = %s" % self.cluster_id)
        self.logger.info("sql command: %s" % sql_cmd)
        try:
            cursor = self.dbh.get_cursor(self.dbh)
            cursor.execute(sql_cmd)
            cursor_return = cursor.fetchone()
            self.dbh.cursorclose(cursor)
            self.logger.info("Result: %s" % cursor_return)
        except Exception as ex:
            self.logger.error("Failed to get instance_master info for "
                              "instance %s, exception %s" %
                              (self.cluster_id, ex))
            self.dbh.cursorclose(cursor)
            return False

        if cursor_return:
            res = cursor_return[0]

        return res

    def _get_cloud_native_compute_location_name(self, compute_region_idx):
        self.logger.info('Try _get_cloud_native_compute_location_name')
        sql = ("select cloud_provider, native_compute_region_name from region_master "
               "where edge_location_region_id=%s" % (compute_region_idx))
        result = dbconn.execute_lambda_query(sql, None, logger=self.logger)
        if not result['ok']:
            self.logger.error("Failed to _get_cloud_native_compute_location_name from RDS")
            return None, None
        cloud = str(result['result'][0][0])
        location = str(result['result'][0][1])
        return cloud, location

    def _get_non_upgrade_inst_count(self, acct_id, compute_region_idx, node_type):
        self.logger.info('Try _get_non_upgrade_inst_count')
        sql = ("select count(id) from instance_master "
               "where upgrade_creation=0 "
               "and acct_id=%s and node_type=%s and compute_region_idx=%s and (has_nat_instance is NULL or has_nat_instance <> 1)" % (acct_id, node_type, compute_region_idx))
        self.logger.info(sql)
        result = dbconn.execute_lambda_query(sql, None, logger=self.logger)
        if not result['ok']:
            self.logger.error("Failed to _get_non_upgrade_inst_count from RDS")
            return -1
        return int(result['result'][0][0])

    def find_nlb_settings_by_custid_and_region_id(self, custid, compute_region_idx):
        """
        :param logger:
        :param custid:
        :param compute_region_idx:
        :return: success, my_nr_instances, my_nr_nat_gateways_needed
        """
        settings = {'success': False, 'is_nat_supported': 0, 'nr_instances': 1, 'is_nlb_supported': 1,
                    'nr_instances_nlb': 1, 'cust_is_nlb_supported': 0, 'ingress_ip_reduction': 0}

        try:
            sql = "SELECT is_enabled, nr_instances, is_nlb_supported, nr_instances_nlb, ingress_ip_reduction " \
                  "FROM cust_nat_mgmt_table " \
                  "WHERE node_type = %s and custid in (0, %s) and compute_region_id in (0, %s) " \
                  "ORDER BY custid DESC, compute_region_id DESC LIMIT 1"
            params = (NODE_TYPE_GP_GATEWAY, custid, compute_region_idx)
            self.logger.info("Trying to execute %s" % str(sql % params))

            result = dbconn.execute_lambda_query(sql % params, None, logger=self.logger)
            if not result['ok']:
                raise Exception("Failed to access cust_nat_mgmt_table table!")

            for row in result['result']:
                is_nat_supported, nr_instances, is_nlb_supported, nr_instances_nlb, ingress_ip_reduction = row
                self.logger.info("is_nat_supported is %s nr_instances is %s is_nlb_supported is %s "
                                 "and nr_instances_nlb is %s for custid %s "
                                 "and compute_region_id %s" % (str(is_nat_supported), str(nr_instances),
                                                               str(is_nlb_supported), str(nr_instances_nlb),
                                                               str(custid), str(compute_region_idx)))
                settings['is_nat_supported'] = is_nat_supported
                settings['nr_instances'] = nr_instances
                settings['is_nlb_supported'] = is_nlb_supported
                settings['nr_instances_nlb'] = nr_instances_nlb
                settings['ingress_ip_reduction'] = ingress_ip_reduction

            sql = "SELECT is_nlb_supported FROM cust_master WHERE id = %s" % custid
            self.logger.info("Trying to execute %s" % str(sql))

            result = dbconn.execute_lambda_query(sql, None, logger=self.logger)
            if not result['ok']:
                raise Exception("Failed to check is_nlb_supported from cust_master table!")

            for row in result['result']:
                cust_is_nlb_supported = row[0]
                self.logger.info("cust_is_nlb_supported is %s for custid %s" % (str(cust_is_nlb_supported), str(custid)))
                settings['cust_is_nlb_supported'] = cust_is_nlb_supported

            settings['success'] = True

        except Exception as e:
            self.logger.error("Failed to check is_nlb_supported and nr_instances_nlb : %s" % str(e))
        finally:
            return settings

    def _get_allocated_ip_count_in_city_country_in_location(self, city_country_set_list, cloud, location, acct_id, node_type):
        self.logger.info('Try _get_allocated_ip_count_in_city_country_in_location')
        sql = ("select city,country,count(*) from public_ip_pool "
               "where acct_id=%s and node_type=%s and cloud='%s' and location='%s' "
               "and (city,country) in ((%s)) "
               "group by city,country " % (acct_id, node_type, cloud, location, "),(".join(("'%s'"%"','".join([str(item) for item in row]) for row in city_country_set_list))))
        self.logger.info(sql)
        result = dbconn.execute_lambda_query(sql, None, logger=self.logger)
        if not result['ok']:
            self.logger.error("Failed to _get_allocated_ip_count_in_city_country_in_location from RDS")
            return None
        _r = {}
        for row in result['result']:
            key = (row[0], row[1])
            _r[key] = int(row[2])
        return _r

    def _reserve_public_ip(self, city, country, cloud, acct_id, group_support_acct_id, location, node_type):
        try:
            args = (country, city, cloud, acct_id, group_support_acct_id,
                    node_type, location, 'reserved')
            if None in args:
                self.logger.error("Failed to reserve_public_ip! Invalid args: %s" % str(args))
                return None
            sql = ["CALL reserve_public_ip('%s','%s','%s',%s,%s,%s,'%s','%s',@reserved_ip)" % args]
            self.logger.info("sql: %s " % str(sql))
            sql.append("SELECT @reserved_ip")
            sql_cmd = ";".join(sql)
            res = dbconn.execute_lambda_query(sql_cmd, {'multi_query': True})
            if not res['ok'] or not res['result']:
                self.logger.error("Failed to reserve_public_ip! Failed sql: %s" % sql)
                return None
            elif str(res['result'][0][0]).lower() in ('none', 'null'):
                self.logger.error("Failed to reserve_public_ip! Failed sql: %s" % sql)
                return None
            else:
                self.logger.info("Reserved IP %s for acct_id %s in %s, %s, %s, %s, node_type %s." % (res['result'][0][0], acct_id, city, country, cloud, location, node_type))
                return res['result'][0][0]
        except Exception as e:
            self.logger.error("Exception occurred in _reserve_public_ip. %s locals %s. Traceback: %s" % (str(e.args),
                                                                              str(locals()),
                                                                              str(traceback.format_exc())))
            return None

    def reserve_release_ip_to_n(self, city, country, cloud, acct_id, group_support_acct_id, location, node_type, n,
                                is_using_sp_interconnect=0, diff=0):
        if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                                   compute_region_info=location) == IPAMService.ENABLED:
            #diff = 0 should never come here, so returning 0 if it happens
            if not diff:
                self.logger.info(f"Given diff(num ips to reserve) is null or 0, nothing to do here! Diff: {diff}")
                return 0
            res = self.ipam_utils.ipam_allocate_address(match_cloud=cloud,
                                                        match_location=location,
                                                        match_type='pupi' if is_using_sp_interconnect else None,
                                                        match_city=city,
                                                        match_country=country,
                                                        match_host_project_id=self.host_project_id if is_using_sp_interconnect and cloud.lower() == 'gcp' else None,
                                                        match_group_support_account_id=group_support_acct_id,
                                                        match_account_id=self.acct_id,
                                                        match_node_type=self.node_type,
                                                        match_count=diff,
                                                        call_context="orch:reserve_public_ip_up_to_n")

            if is_using_sp_interconnect and len(res) < n:
                ips_to_reserve = n - len(res)
                res_with_root = self.ipam_utils.ipam_allocate_address(match_cloud=cloud,
                                                                      match_location=location,
                                                                      match_type='pupi',
                                                                      match_city=city,
                                                                      match_country=country,
                                                                      match_host_project_id=self.host_project_id if cloud.lower() == 'gcp' else None,
                                                                      match_group_support_account_id=group_support_acct_id,
                                                                      match_account_id=self.root_tsg_id,
                                                                      match_node_type="161" if str(self.node_type) == "161" else "0",
                                                                      match_count=ips_to_reserve,
                                                                      set_account_id=self.acct_id,
                                                                      set_node_type=self.node_type,
                                                                      call_context="orch:reserve_public_ip_up_to_n")
                res.extend(res_with_root)
            if not res:
                self.logger.error("Failed to reserve_public_ip!")
                return 0
            return len(res)
        else:
            sql = [
                "call reserve_public_ip_up_to_n('%s', '%s', '%s', %s, %s, %s, '%s', 'reserved', %s, %s, @reserved_ip_count)" % (
                country, city, cloud, acct_id, group_support_acct_id, node_type, location, n, is_using_sp_interconnect)]
            sql.append("SELECT @reserved_ip_count")
            self.logger.info("%s" % sql)

            sql_cmd = ";".join(sql)
            res = dbconn.execute_lambda_query(sql_cmd, {'multi_query': True})
            if not res['ok']:
                self.logger.error("Failed to reserve_public_ip! Failed sql: %s" % sql)
                return 0
            else:
                if res['result'][0][0] > 0:
                    self.logger.info("Reserved %s IPs" % res['result'][0][0])
                elif res['result'][0][0] < 0:
                    self.logger.info("Removed %s IPs" % res['result'][0][0])
                elif res['result'][0][0] == 0:
                    self.logger.info("No change to IPs")
                return res['result'][0][0]

    def update_pre_allocate_to_reserve(self, acct_id, compute_region_idx, node_type, city_country_set_list, cloud, location):
        """
        Get IP locations, could be old locations
        for each location, update pre_allocate to reserved
        """
        try:
            if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                                       compute_region_info=location) == IPAMService.ENABLED:
                self.ipam_utils.ipam_update_address(match_status='pre_allocated',
                                                    match_account_id=acct_id,
                                                    match_node_type=node_type,
                                                    match_location=location,
                                                    match_cloud=cloud,
                                                    match_city=[item[0] for item in city_country_set_list],
                                                    match_country=[item[1] for item in city_country_set_list],
                                                    set_status="reserved", call_context="orch:update_pre_allocate_to_reserve")
            else:
                # for each location, update pre_allocate to reserved
                sql = ("UPDATE public_ip_pool SET status='reserved' "
                       "where status='pre_allocated' and acct_id=%s and node_type=%s and cloud='%s' and location='%s' "
                       "and (city,country) in ((%s)) " % (acct_id, node_type, cloud, location, "),(".join(("'%s'"%"','".join([str(item) for item in row]) for row in city_country_set_list))))
                result = dbconn.execute_lambda_query(sql, None, logger=self.logger)
                if not result['ok']:
                    self.logger.error("Failed to update_pre_allocate_to_reserve! Failed sql: %s" % sql)
                    return
        except Exception as err:
            self.logger.error("Failed to update pre-allocated IP to reserved for acct_id %s in compute_region_idx %s. %s" % (acct_id, compute_region_idx, err))

    def get_nlb_ip(self, enable_tls_term_on_ep):
        """
        Check if nlb IP already exist (No matter if status is reserved/pre-allocated/active/NULL)
        Reserve nlb IP if necessary
        """
        acct_id=None
        compute_region_idx=None
        active_nlb_ip_count = 0
        try:
            # Get NLB IP count from compute
            acct_id = self.acct_id
            compute_region_idx = self.compute_region_id
            cloud = self.cloud_provider
            if enable_tls_term_on_ep:
                location = "global"
            else:
                location = self.native_region_name
            proxy_pfx = get_proxy_prefix(self.node_type, self.alt_node_type)
            nlb_node_type = SERVICE_TYPE_SWG_NLB_INGRESS_IP
            if self.node_type == NODE_TYPE_BI_NH_PROXY:
                nlb_node_type = SERVICE_TYPE_BI_NHP_NLB_INGRESS_IP
            service_id = f"{proxy_pfx}nlb-{str(self.native_region_name)}-{str(acct_id)}"
            self.logger.info("Check if need to allocate NLB IP for acct_id "
                "%s in compute_region_idx %s (enable_tls_term_on_ep: %s)" %
                (acct_id, compute_region_idx, enable_tls_term_on_ep))
            if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                                       compute_region_info=location) == IPAMService.ENABLED:
                result = self.ipam_utils.ipam_update_address(match_cloud=cloud,
                                                             match_location=location,
                                                             match_account_id=acct_id,
                                                             match_node_type=nlb_node_type,
                                                             match_service_id=service_id,
                                                             set_status="active",
                                                             call_context="orch:get_nlb_ip:get_nlb_ip")
                if result:
                    active_nlb_ip_count = len(result)
                if active_nlb_ip_count > 0:
                    self.logger.info("NLB service IP %s already exist for "
                                     "acct_id %s in compute_region_idx %s "
                                     "(enable_tls_term_on_ep: %s)." %
                                     (result[0], acct_id,
                                      compute_region_idx, enable_tls_term_on_ep))
                    return result[0]
            else:
                sql = ("select distinct ip, IP_type from public_ip_pool "
                       "where acct_id=%s and cloud='%s' and location='%s' "
                       "and node_type=%s and service_id='%s' " % (acct_id, cloud, location, nlb_node_type, service_id))
                self.logger.info("Query: %s" % sql)
                result = dbconn.execute_lambda_query(sql, None, logger=self.logger)
                if not result['ok']:
                    self.logger.error("Failed to find allocated NLB IP for "
                        "acct_id %s in compute_region_idx %s "
                        "(enable_tls_term_on_ep: %s). Failed SQL: %s %s" %
                        (acct_id, compute_region_idx,
                         enable_tls_term_on_ep, sql, result))
                    return None
                active_nlb_ip_count = len(result['result'])
                if active_nlb_ip_count > 0:
                    self.logger.info("NLB service IP %s(%s) already exist for "
                        "acct_id %s in compute_region_idx %s "
                        "(enable_tls_term_on_ep: %s)." %
                        (result['result'][0][0], result['result'][0][1], acct_id,
                         compute_region_idx, enable_tls_term_on_ep))
                    if enable_tls_term_on_ep or nlb_node_type == SERVICE_TYPE_BI_NHP_NLB_INGRESS_IP:
                        set_ip_status_active(result['result'][0][0], self.logger)
                    return result['result'][0][0]

            if enable_tls_term_on_ep:
                city = 'global'
                country = 'global'
            else:
                from libs.model.regionmastermodel import RegionMasterModel
                my_compute_region = RegionMasterModel(dbh=self.dbh, edge_location_region_id=compute_region_idx)
                city = my_compute_region.get_param('city')
                country = my_compute_region.get_param('country')
            ip = self.get_service_ip_for_country_city_cloud(country, city,
                     cloud, nlb_node_type, service_id,
                     enable_tls_term_on_ep)
            if ip:
                self.logger.info("Allocated NLB IP %s for acct_id %s in compute_region_idx %s"% (ip, acct_id, compute_region_idx))
                return ip
            else:
                self.logger.error("Failed to allocate NLB IP for acct_id %s in compute_region_idx %s"% (acct_id, compute_region_idx))
                return None

        except Exception as err:
            self.logger.error("Failed to reserve NLB IP for acct_id %s in compute_region_idx %s. %s" % (acct_id, compute_region_idx, err))
            return None


    def reserve_double_if_necessary(self, acct_id, group_support_acct_id, compute_region_idx, node_type,
                                    city_country_set_list, cloud, location, has_nat_gateway, custid,
                                    is_using_sp_interconnect=0):
        """
        Get instance count from compute
        Get IP locations, could be old locations
        Get IP count for each edge location
        for each location, reserve the diff
        """
        try:
            self.logger.info("Check if need to reserve IP for acct_id %s in compute_region_idx %s." % (acct_id, compute_region_idx))
            # Get instance count from compute
            inst_count = self._get_non_upgrade_inst_count(acct_id, compute_region_idx, node_type)
            if inst_count <= 0:
                self.logger.error("Failed to get instance count. Do not reserve more IPs.")
                # Returning true here.
                return True

            ip_count_by_city_country = self._get_allocated_ip_count_in_city_country_in_location(city_country_set_list, cloud, location, acct_id, node_type)

            result = self.find_nlb_settings_by_custid_and_region_id(custid, compute_region_idx)
            if not result['success']:
                self.logger.error("Failed to access cust_nat_mgmt_table for custid %s in compute_region_idx %s, "
                                  "will use the default value %s" % (custid, compute_region_idx, result))

            is_nat_supported = result['is_nat_supported']
            nr_instances = result['nr_instances']
            is_nlb_supported = result['is_nlb_supported']
            ingress_ip_reduction = result['ingress_ip_reduction']
            cust_is_nlb_supported = result['cust_is_nlb_supported']

            is_nlb_supported = is_nlb_supported and cust_is_nlb_supported
            #CYR-42950 no longer reserve NLB ips here, will be handled in gcp_instance_param_mgmt.py
            nr_ip_nlb = 0


            self.logger.info("ingress_ip_reduction is %s, nr_ip_nlb is %s "
                             "for acct_id %s in compute_region_idx %s." %
                             (ingress_ip_reduction, nr_ip_nlb, acct_id, compute_region_idx))

            new_ips = 0
            for key in ip_count_by_city_country:
                city, country = key
                # # if it is a NAT instance,
                # NAT instance type is overwrite by gateway

                # The following IP reservation logic was only triggered when standalone GWs bring up
                # After NAT brings up, ip reservation logic is in gcp_instance_param_mgmt.py
                # CYR-44307 There used to be logic here that reserves different amounts of IPs depending on NGPA setting
                # Now that NGPA only uses 2 IPs in a new region, this is no longer required so everything is the same
                
                nr_ip_required = inst_count * 2
                diff = nr_ip_required - ip_count_by_city_country[key]
                if diff > 0:
                    self.logger.info("Try reserve %s IP(s) in %s, %s, %s, %s" % (diff, city, country, cloud, location))
                    ip_reserved = self.reserve_release_ip_to_n(city, country, cloud, acct_id, group_support_acct_id, location, node_type, nr_ip_required, is_using_sp_interconnect, diff=diff)
                    new_ips += ip_reserved
            if new_ips == 0:
                self.logger.info("No IP reserved for acct_id %s in compute_region_idx %s." % (acct_id, compute_region_idx))
            if new_ips > 0:
                self.logger.info("%s IP reserved in total for acct_id %s in compute_region_idx %s." % (new_ips, acct_id, compute_region_idx))
            return True
        except Exception as err:
            self.logger.error("Failed to reserve IP for acct_id %s in compute_region_idx %s. %s" % (acct_id, compute_region_idx, err))
            return False

    def _get_cust_deployed_city_country(self, acct_id, compute_region_idx, node_type, node_id):
        """
        Get deployed city, country.
        For GCP, reserving IP is executed during salt profile creation.
        The edge location map could change. Use cust_topology rows to find deployed city and country

        For GCP new instance deployment, instance_master id has not been updated in the cust_toplogy table in this stage,
        fortunately, node_id should be available.

        Use is_dynamic_instance to check if this is a auto-scale created instance.
        If it is auto-scale created, use the pinned instance and its related cust_topology node in the same compute
        location to find out the deployed locations.

        If this is not is_dynamic_instance node, then it is a new edge location, use node_id to find the new edge location

        For AWS reserving IP is executed during instance bringup.
        instance id is already updated in the cust_topology.
        node_id is None
        """
        self.logger.info('Try _get_cust_deployed_city_country')
        city_country_set_list = []
        # This query returns AWS new/existing deployment location or GCP existing deployment locations
        sql = ("select distinct rm.city,rm.country from cust_topology ct join instance_master im join region_master rm "
               "on ct.instance1_id=im.id and ct.region=rm.edge_location_region_id "
               "where im.acct_id=%s and im.node_type=%s and im.compute_region_idx=%s "
               "and im.is_dynamic_instance=0 and ct.is_dynamic_node=0" % (acct_id, node_type, compute_region_idx))
        self.logger.info(sql)
        result = dbconn.execute_lambda_query(sql, None, logger=self.logger)
        self.logger.info(result)
        if not result['ok']:
            self.logger.error("Failed to _get_cust_deployed_city_country from RDS")
            return city_country_set_list
        city_country_set_list = [(row[0], row[1]) for row in result['result']]

        if node_id:
            # This query returns GCP new deployment location
            sql = ("select distinct rm.city,rm.country from cust_topology ct join region_master rm "
                   "on ct.region=rm.edge_location_region_id "
                   "where ct.id=%s and ct.is_dynamic_node=0" % (node_id))
            self.logger.info(sql)
            result = dbconn.execute_lambda_query(sql, None, logger=self.logger)
            self.logger.info(result)
            if not result['ok']:
                self.logger.error("Failed to _get_cust_deployed_city_country from RDS")
                return city_country_set_list

            city_country_set_list += [(row[0], row[1]) for row in result['result'] if
                                      (row[0], row[1]) not in city_country_set_list]


        self.logger.info("city_country_set_list %s" % city_country_set_list)
        return city_country_set_list

    def _get_panw_deployed_city_country(self, acct_id, compute_region_idx, node_type, node_id):
        """
        """
        self.logger.info('Try _get_panw_deployed_city_country')
        city_country_set_list = []
        # This query returns AWS new/existing deployment location or GCP existing deployment locations
        sql = ("select distinct rm.city,rm.country from instance_master im join region_master rm "
               "on im.compute_region_idx =rm.edge_location_region_id "
               "where im.acct_id=%s and im.node_type=%s and im.compute_region_idx=%s "
               "and im.is_dynamic_instance=0" % (acct_id, node_type, compute_region_idx))
        self.logger.info(sql)
        result = dbconn.execute_lambda_query(sql, None, logger=self.logger)
        self.logger.info(result)
        if not result['ok']:
            self.logger.error("Failed to _get_panw_deployed_city_country from RDS")
            return city_country_set_list
        city_country_set_list = [(row[0], row[1]) for row in result['result']]

        if node_id:
            # This query returns GCP new deployment location
            sql = ("select distinct rm.city,rm.country from region_master rm "
                   "where rm.edge_location_region_id in "
                   "(select rm.compute_region_id from cust_topology ct "
                   "join region_master rm on ct.region=rm.edge_location_region_id "
                   "where ct.id=%s and ct.is_dynamic_node=0)" % node_id)
            self.logger.info(sql)
            result = dbconn.execute_lambda_query(sql, None, logger=self.logger)
            self.logger.info(result)
            if not result['ok']:
                self.logger.error("Failed to _get_panw_deployed_city_country from RDS")
                return city_country_set_list

            city_country_set_list += [(row[0], row[1]) for row in result['result'] if
                                      (row[0], row[1]) not in city_country_set_list]


        self.logger.info("city_country_set_list %s" % city_country_set_list)
        return city_country_set_list


    def get_deployed_city_country_cloud_location(self, acct_id, compute_region_idx, node_type):
        if node_type == NODE_TYPE_GP_PORTAL:
            city_country_set_list = self._get_panw_deployed_city_country(acct_id, compute_region_idx,
                                                                     node_type, self.node_id)
        else:
            city_country_set_list = self._get_cust_deployed_city_country(acct_id, compute_region_idx,
                                                                         node_type, self.node_id)
        if len(city_country_set_list) == 0:
            self.logger.error("Failed to get deployed locations. Do not reserve more IPs.")
            return [], "", ""

        cloud, location = self._get_cloud_native_compute_location_name(compute_region_idx)
        return city_country_set_list, cloud, location

    def reserve_ip_for_customer(self, edge_id_country_city_list):
        """
        For each customer per region, reserve 2x (number of GP instances)
        BYOIP/public cloud clean addresses (allocated acct_id but
        cluster_id=0).

        @type edge_id_country_city_list: list of list of str
        @param edge_id_country_city_list: list of
            [edge_location_region_id, country, city]
        @rtype: None
        @return: None
        """
        self.logger.info("Reserve additional IP for account %s in "
                         "compute region %s" %
                         (self.acct_id, self.compute_region_id))
        num_of_GPs = self.get_num_of_GPs()
        if num_of_GPs == -1:
            self.logger.error("Unable to get the number of GP.")
            return

        num_of_GWs = self.get_num_of_GWs()
        if num_of_GWs == -1:
            self.logger.error("Unable to get the number of GW.")
            return

        #get number of cp instances
        num_of_CPs = self.get_num_of_CPs()
        if num_of_CPs == -1:
            self.logger.error("Unable to get the number of clean pipe instances.")
            return
        else:
            self.logger.info("Number of clean pipe instances %s" % num_of_CPs)

        for idx, item in enumerate(edge_id_country_city_list):
            country = item[1]
            city = item[2]

            self.logger.info("Check if need to reserve extra IP for "
                             "customer %s in %s, %s in %s" %
                             (self.acct_id, country, city,
                              self.cloud_provider))

            # FIXME: For AWS, only reserve IP address in its compute region,
            # that is, the first item in edge_id_country_city_list. Do not
            # reserve IPs in other country/city in 1.4 release. It might
            # change after 1.4 release.
            if self.cloud_provider == 'AWS' and idx > 0:
                self.logger.info("Not reserving IPs in %s, %s in AWS, since "
                                 "we do not support multiple IP in AWS." %
                                 (country, city))
                continue

            # Check the number of IP addresses reserved but not used
            # for this account in this country and city
            num_of_total_IP = self.get_num_of_total_IP_addr(country, city)
            if num_of_total_IP == -1:
                self.logger.error("Unable to get the number of reserved IP.")
                return

            if idx==0:
                total_num = num_of_GPs + num_of_GWs + num_of_CPs
            else:
                total_num = num_of_GWs

            if num_of_total_IP < total_num * 2:
                num_of_IPs_to_reserve = total_num * 2 - num_of_total_IP
            else:
                self.logger.info("No need to reserve extra IPs for customer "
                                 "%s in %s %s" %
                                 (self.acct_id, country, city))
                continue

            self.logger.info("Reserve %s extra IPs for customer %s in %s %s" %
                             (num_of_IPs_to_reserve, self.acct_id, country,
                              city))
            store_proc = 'reserve_public_ip'
            args = [country, city, self.cloud_provider, self.acct_id, self.group_support_acct_id, self.node_type, self.native_region_name,
                    "reserved", 0]
            self.logger.info("store procedure: %s, args: %s" %
                             (store_proc, args))

            reserved_ip_count = 0
            if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                                       compute_region_info=self.native_region_name) == IPAMService.ENABLED:
                res = self.ipam_utils.ipam_allocate_address(match_cloud=self.cloud_provider,
                                                            match_location=self.native_region_name,
                                                            match_type='pupi' if self.is_using_sp_interconnect else None,
                                                            match_city=city,
                                                            match_country=country,
                                                            match_host_project_id=self.host_project_id if self.is_using_sp_interconnect and self.cloud_provider.lower() == 'gcp' else None,
                                                            match_group_support_account_id=self.group_support_acct_id,
                                                            match_account_id=self.acct_id,
                                                            match_node_type=self.node_type,
                                                            match_count=num_of_IPs_to_reserve,
                                                            set_status="reserved",
                                                            call_context="orch:reserve_ip_for_customer")
                if len(res) < num_of_IPs_to_reserve and self.is_using_sp_interconnect:
                    ips_reserved = len(res)
                    res_with_root_check = self.ipam_utils.ipam_allocate_address(match_cloud=self.cloud_provider,
                                                                                match_location=self.native_region_name,
                                                                                match_type='pupi',
                                                                                match_city=city,
                                                                                match_country=country,
                                                                                match_host_project_id=self.host_project_id if self.cloud_provider.lower() == 'gcp' else None,
                                                                                match_group_support_account_id=self.group_support_acct_id,
                                                                                match_account_id=self.root_tsg_id,
                                                                                match_node_type="161" if str(self.node_type)=="161" else "0",
                                                                                match_count=num_of_IPs_to_reserve-ips_reserved,
                                                                                set_status="reserved",
                                                                                set_node_type=self.node_type,
                                                                                set_account_id=self.acct_id,
                                                                                call_context="orch:reserve_ip_for_customer")
                    res.extend(res_with_root_check)
                self.logger.info(f"Reserved byoip/pupi IPs: {res}")
                if not res:
                    self.logger.error("Unable to reserve public IP for "
                                      "customer %s in %s, %s in %s. " %
                                      (self.acct_id, country, city,
                                       self.cloud_provider))
                reserved_ip_count += len(res)
            else:
                cursor = self.dbh.get_cursor(buffered=True)
                try:
                    reserved_ip_count = 0
                    for _ in range(num_of_IPs_to_reserve):
                        res = cursor.callproc(store_proc, args)
                        self.logger.info("Reserved IP: %s" % res[5])
                        if not res[5]:
                            break
                        reserved_ip_count += 1
                    self.dbh.cursorclose(cursor)
                except Exception as ex:
                    self.logger.error("Unable to reserve public IP for "
                                      "customer %s in %s, %s in %s. "
                                      "Exception: %s" %
                                      (self.acct_id, country, city,
                                       self.cloud_provider, str(ex)))
                    self.dbh.cursorclose(cursor)
                    return
            if reserved_ip_count < num_of_IPs_to_reserve:
                self.logger.error("Not enough available IPs to reserve in "
                                  "%s, %s in %s. Still need %s IPs." %
                                  (country, city, self.cloud_provider,
                                   num_of_IPs_to_reserve - reserved_ip_count))
                _ = self.reserve_sticky_ip(num_of_IPs_to_reserve - reserved_ip_count, 0)
                continue

    def get_num_of_GWs(self):
        """
        Check the number of GW belonging to the customer in the region

        @rtype: int
        @return: number of GPs of this customers in the region
        """

        cursor = self.dbh.get_cursor()

        self.logger.info("Check number of gateways for customer %s in region %s" %
                         (self.acct_id, self.compute_region_id))
        sql_cmd = ("SELECT count(*) FROM instance_master WHERE acct_id = %s "
                   "AND compute_region_idx = %s AND "
                   "node_type = 49" %
                   (self.acct_id, self.compute_region_id))
        self.logger.info("sql command: %s" % sql_cmd)

        try:
            cursor.execute(sql_cmd)
            ret = cursor.fetchone()
            self.dbh.cursorclose(cursor)
        except Exception as ex:
            self.logger.error("Failed to get number of gateways for "
                              "acctid %s, compute region %s. Exception %s" %
                              (self.acct_id, self.compute_region_id, ex))
            self.dbh.cursorclose(cursor)
            return -1

        self.logger.info("%s Gateways found for customer %s in region %s" %
                         (ret[0], self.acct_id, self.compute_region_id))

        return int(ret[0])

    def get_num_of_CPs(self):
        """
        Check the number of CP belonging to the customer in the region

        @rtype: int
        @return: number of CPs of this customers in the region
        """

        cursor = self.dbh.get_cursor()

        self.logger.info("Check number of clean pipe for customer %s in region %s" %
                         (self.acct_id, self.compute_region_id))
        sql_cmd = ("SELECT count(*) FROM instance_master WHERE acct_id = %s "
                   "AND compute_region_idx = %s AND "
                   "node_type = %s" %
                   (self.acct_id, self.compute_region_id, NODE_TYPE_CLEAN_PIPE))
        self.logger.info("sql command: %s" % sql_cmd)

        try:
            cursor.execute(sql_cmd)
            ret = cursor.fetchone()
            self.dbh.cursorclose(cursor)
        except Exception as ex:
            self.logger.error("Failed to get number of clean pipe for "
                              "acctid %s, compute region %s. Exception %s" %
                              (self.acct_id, self.compute_region_id, ex))
            self.dbh.cursorclose(cursor)
            return -1

        self.logger.info("%s clean pipe sites found for customer %s in region %s" %
                         (ret[0], self.acct_id, self.compute_region_id))

        return int(ret[0])


    def get_num_of_GPs(self):
        """
        Check the number of GW belonging to the customer in the region

        @rtype: int
        @return: number of GPs of this customers in the region
        """

        cursor = self.dbh.get_cursor()

        self.logger.info("Check number of GP for customer %s in region %s" %
                         (self.acct_id, self.compute_region_id))
        sql_cmd = ("SELECT count(*) FROM instance_master WHERE acct_id = %s "
                   "AND compute_region_idx = %s AND "
                   "node_type = 50" %
                   (self.acct_id, self.compute_region_id))
        self.logger.info("sql command: %s" % sql_cmd)

        try:
            cursor.execute(sql_cmd)
            ret = cursor.fetchone()
            self.dbh.cursorclose(cursor)
        except Exception as ex:
            self.logger.error("Failed to get number of GPs for "
                              "acctid %s, compute region %s. Exception %s" %
                              (self.acct_id, self.compute_region_id, ex))
            self.dbh.cursorclose(cursor)
            return -1

        self.logger.info("%s GPs found for customer %s in region %s" %
                         (ret[0], self.acct_id, self.compute_region_id))

        return int(ret[0])

    def get_num_of_total_IP_addr(self, country, city):
        """
        Check the number of total IP addresses under acct_id in the
        specific country and city

        @type country: str
        @param country: country where IP is registered with
        @type city: str
        @param city: city where IP is registered with
        @rtype: integer
        @return: number of total IP addresses for this account in this
            country and city
        """
        cursor = self.dbh.get_cursor(self.dbh)

        self.logger.info("Check the number of total IP addresses for "
                         "customer %s in %s %s" %
                         (self.acct_id, country, city))
        sql_cmd = ("SELECT count(*) FROM public_ip_pool WHERE acct_id = %s "
                   "AND location = '%s' and country = '%s' AND city = '%s' "
                   "AND cloud = '%s' AND (node_type = 49 or node_type = 50 or node_type = 152 ) AND group_support_acct_id = %s" %
                   (self.acct_id, self.native_region_name, country, city, self.cloud_provider, self.group_support_acct_id))
        self.logger.info("sql command: %s" % sql_cmd)

        try:
            cursor.execute(sql_cmd)
            ret = cursor.fetchone()
            self.dbh.cursorclose(cursor)
        except Exception as ex:
            self.logger.error("Failed to get public_ip_pool info for "
                              "acct_id %s, location %s %s in %s. "
                              "Exception %s" %
                              (self.acct_id, country, city,
                               self.cloud_provider, ex))
            self.dbh.cursorclose(cursor)
            return -1

        self.logger.info("Customer %s has total %s IPs in %s, %s, %s " %
                         (self.acct_id, ret[0], country, city,
                          self.cloud_provider))
        return ret[0]

    def reserve_sticky_ip(self, num_of_IPs, cluster_id):
        pass

    def update_column_use_PBF(self, use_PBF):
        """
        Update column use_PBF in instance_master table for the
        instance.

        @type use_PBF: boolean
        @param use_PBF: enable PBF for this instance
        @return: None
        """

        sql = ("UPDATE instance_master SET use_PBF = %s "
               "WHERE clusterid = %s" %
               (use_PBF, self.cluster_id))
        self.logger.info(sql)

        try:
            cursor = self.dbh.get_cursor(prepared=True)
            cursor.execute(sql)
        except Exception as ex:
            self.logger.error("Update use_PBF field in instance_master table "
                              "failed. Exception: %s" % str(ex))
        self.dbh.cursorclose(cursor)
        return

    def update_release_pending_for_public_ip(self, eidx):
        if not eidx:
            self.logger.error(f"No eidx provided in: update_release_pending_for_public_ip!!")
            return

        #This catches cluster_id=0 case
        if not self.cluster_id or int(self.cluster_id) == 0:
            self.logger.error(f"Fatal! No cluster_id provided in: update_release_pending_for_public_ip!! Bailing out")
            return

        self.logger.info(f"Setting IP status to release_pending for cluster {self.cluster_id} with deployment ID {eidx}")

        if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                            compute_region_info=self.native_region_name) == IPAMService.ENABLED:
            #TODO EVELYN: Implement ipam_update_address method for set_eidx!
            self.ipam_utils.ipam_update_address(match_cluster_id=self.cluster_id,
                                                set_eidx=eidx,
                                                set_status='release_pending',
                                                call_context="orch:update_release_pending_for_public_ip")
        else:  
            self.logger.info(f"For cluster {self.cluster_id}, setting eidx={eidx} and status='release_pending' in public_IP_pool table")
            sql_cmd = ("UPDATE public_ip_pool SET eidx=%s, status='release_pending' "
                        "WHERE cluster_id = %s")
            params = (eidx, self.cluster_id)
            self.logger.info(f"sql: {sql_cmd}, params:{params}")

            try:
                cursor = self.dbh.get_cursor(prepared=True)
                cursor.execute(sql_cmd, params)
                self.dbh.cursorclose(cursor)
            except Exception as ex:
                self.logger.error("Failed to update public ip pool for cluster %s, "
                                  "exception %s" % (self.cluster_id, ex))
                self.dbh.cursorclose(cursor)
                return

        self.logger.info("Updated status to release_pending and eidx to %s for IPs belonging to cluster %s" %
                         (eidx, self.cluster_id))


    def reset_public_ip_pool_info(self, ip_list=None, preserve_nat_ip=False):
        """
        Clean up the information in public_ip_pool table, a.k.a., reset
        cluster_id to 0

        @return: None
        """
        #NOTE: IPAM side implemented status="transition" for IPs meant for NAT instances
        #Please do not set any other IPs to "transition" status without discussing it with Karthik
        if not self.cluster_id or int(self.cluster_id) == 0:
            self.logger.error("Fatal! No cluster_id provided in: reset_public_ip_pool_info!! Bailing out!!")
            return
        
        ip_status = "transition" if preserve_nat_ip else "reserved"
        if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                                   compute_region_info=self.native_region_name) == IPAMService.ENABLED:
            self.ipam_utils.ipam_update_address(match_cluster_id=self.cluster_id,
                                                set_status=ip_status,
                                                set_cluster_id=0,
                                                call_context="orch:reset_public_ip_pool_info")
        else:
            if ip_list:
                self.logger.info("For cluster %s, try to reset the following IP "
                                 "addresses back to public_IP_pool: %s " % (self.cluster_id, str(ip_list)))
                list_to_str = "', '".join(ip_list)

                sql_cmd = ("UPDATE public_ip_pool SET cluster_id = 0, "
                           "status = %s "
                           "WHERE cluster_id = %s and ip in ") + f"('{list_to_str}')"
                
                params = (ip_status, self.cluster_id)
            else:
                self.logger.info("For cluster %s, try to reset all its IP address info in "
                                 "public_IP_pool table." % self.cluster_id)
                sql_cmd = ("UPDATE public_ip_pool SET cluster_id = 0, "
                           "status=%s "
                           "WHERE cluster_id = %s")
                params = (ip_status, self.cluster_id)
            self.logger.info(f"sql: {sql_cmd}, params:{params}")

            try:
                cursor = self.dbh.get_cursor(prepared=True)
                cursor.execute(sql_cmd, params)
                self.dbh.cursorclose(cursor)
            except Exception as ex:
                self.logger.error("Failed to clear public ip pool for cluster %s, "
                                  "exception %s" % (self.cluster_id, ex))
                self.dbh.cursorclose(cursor)
                return
        self.logger.info("Released IPs belonged to cluster %s" %
                         self.cluster_id)

    def get_native_compute_region_name(self):
        """
        Get the cloud native name of compute region, such as europe-west2 and
        asia-south1.

        @rtype: None
        @return: None
        """
        if self.compute_region_id == None:
            self.logger.info("compute_region_id is not set.")
            return None 

        self.logger.info("Get native compute region name for region id %s" %
                         self.compute_region_id)

        cursor = self.dbh.get_cursor(self.dbh)

        sql_cmd = ("SELECT native_compute_region_name FROM region_master "
                   "WHERE compute_region_id = %s LIMIT 1" %
                   self.compute_region_id)
        self.logger.info("sql command: %s" % sql_cmd)

        try:
            cursor.execute(sql_cmd)
            ret = cursor.fetchone()
            self.dbh.cursorclose(cursor)
        except Exception as ex:
            self.logger.error("Failed to get native_compute_region_name from "
                              "region_master table for %s. It should be fine "
                              "during deletion process, since entry in "
                              "instance_master table is already removed. "
                              "Exception: %s" %
                              (self.compute_region_id, ex))
            self.dbh.cursorclose(cursor)
            return None

        self.logger.info("Compute region %s's native_compute_region_name is "
                         "%s" % (self.compute_region_id, str(ret[0])))
        self.native_region_name = str(ret[0])
        return

    def _remap_existing_app(self, existing_inbound_access_ip_mapping, inbound_access_ip_mapping):
        _exist_ip_to_app = {
        app_group_info['public_ip']: {(app_group['ip'], app_group['port'], app_group['protocol'], app_group['name']) for
                                      app_group in app_group_info['app_group']} for app_group_info in
        existing_inbound_access_ip_mapping if app_group_info['public_ip']}
        _exist_app_to_ip = {
        (app_group['ip'], app_group['port'], app_group['protocol'], app_group['name']): app_group_info['public_ip'] for
        app_group_info in existing_inbound_access_ip_mapping for app_group in app_group_info['app_group'] if app_group_info['public_ip']}
        for app_group_info in inbound_access_ip_mapping:
            for app_group in app_group_info['app_group']:
                key = (app_group['ip'], app_group['port'], app_group['protocol'], app_group['name'])
                if key in _exist_app_to_ip:
                    app_group_info['public_ip'] = _exist_app_to_ip[key]
                    # Remove apps from _exist_app_to_ip as the ip has been assigned here
                    for app_key in _exist_ip_to_app[app_group_info['public_ip']]:
                        del _exist_app_to_ip[app_key]
                    # Remove this ip from _exist_ip_to_app as the apps has been removed
                    del _exist_ip_to_app[app_group_info['public_ip']]
                    continue
        existing_inbound_access_ip_list = set(app_group_info['public_ip'] for app_group_info in existing_inbound_access_ip_mapping if app_group_info['public_ip'])
        new_inbound_access_ip_list = set(app_group_info['public_ip'] for app_group_info in inbound_access_ip_mapping if app_group_info['public_ip'])
        ip_list_to_release = existing_inbound_access_ip_list - new_inbound_access_ip_list
        return inbound_access_ip_mapping, list(ip_list_to_release)

    def init_inbound_access_ip_mapping(self, node_id, update_existing_mapping=False):

        """[{'public_ip':'*******',
                           'app_group': [{'ip': '***********',
                                            'port': '22',
                                            'protocol': 'tcp',
                                            'name': 'ssh3'},
                                           {'ip': '***********',
                                            'port': '80',
                                            'protocol': 'tcp',
                                            'name': 'web1'},
                                           {'ip': '***********',
                                            'port': '443',
                                            'protocol': 'tcp',
                                            'name': 'web2'}]},
                            {'public_ip':'*******',
                             'app_group': [{'ip': '***********',
                                            'port': '22',
                                            'protocol': 'tcp',
                                            'name': 'ssh4'},
                                           {'ip': '***********',
                                            'port': '80',
                                            'protocol': 'tcp',
                                            'name': 'web3'}]},
                            {'public_ip':'*******',
                             'app_group': [{'ip': '***********',
                                            'port': '22',
                                            'protocol': 'tcp',
                                            'name': 'ssh5'}]}]
        """

        self.logger.info("Allocate IP for inbound_access for cust node %s. " % node_id)
        from libs.model.custnodemodel import CustNodeModel

        cust_node_model = CustNodeModel(iid=node_id, dbh=self.dbh)
        site_name = cust_node_model.name
        region_idx = cust_node_model.region
        inbound_access_ip_mapping_raw = uncompress_cfg(cust_node_model.inbound_access_ip_mapping)
        inbound_access_ip_mapping = None
        # Get existing app_grouping from instance_master if available
        if inbound_access_ip_mapping_raw is not None:
            try:
                inbound_access_ip_mapping = json.loads(inbound_access_ip_mapping_raw)
            except Exception as e:
                self.logger.error("Failed to get existing inbound access app grouping: %s" % e)


        # Get new app_grouping from onboarding_cfg
        onboarding_cfg = self.get_onboarding_cfg(self.acct_id)
        try:
            shared_app_group, dedicated_app_group, num_eip = self.secure_inbound_access_apps_grouping(onboarding_cfg, site_name, inbound_access_ip_mapping)
        except Exception as e:
            self.logger.error("Failed to get inbound access app grouping: %s" % e)
            return

        cust_node_model.inbound_access_max_ip = num_eip

        """
        Process the shared_app_group first in public ip remapping.
        When an app changed from shared to dedicated, all other apps in the 
        group will keep using the existing public ip.
        """
        all_app_group = shared_app_group + [[app_group] for app_group in dedicated_app_group]

        # Format app_group
        inbound_access_ip_mapping = []

        for app_group in all_app_group:
            app_group_info = []
            for app in app_group:
                if len(app) >= 4:
                    app_group_info.append({'ip': app[0],
                                           'port': app[1],
                                           'protocol': app[2],
                                           'name': app[3]
                                           })
                else:
                    self.logger.error("Wrong format while parsing app info. received app info: %s" % str(app))

            if app_group_info:
                inbound_access_ip_mapping.append({'public_ip': '',
                                                  'app_group': app_group_info
                                                  })
            else:
                self.logger.error("No valid app info. Continue")
                continue

        if update_existing_mapping:
            ip_list_to_release = None
            try:
                org_cfg = uncompress_cfg(cust_node_model.inbound_access_ip_mapping)
                if org_cfg is not None and (str(org_cfg).lower() not in ('none', 'null', '')):
                    existing_inbound_access_ip_mapping = json.loads(org_cfg)
                    inbound_access_ip_mapping, ip_list_to_release = self._remap_existing_app(existing_inbound_access_ip_mapping,
                                                                                             inbound_access_ip_mapping)
            except Exception as e:
                self.logger.error("Failed to load and merge existing inbound_access_ip_mapping, "
                                  "create new inbound_access_ip_mapping. Exception: %s" % str(e))

            if ip_list_to_release:
                self.reset_public_ip_pool_info(ip_list_to_release)

        # Get country, city, cloud_provider for IP creation
        cursor = self.dbh.get_cursor(self.dbh)
        try:
            self.logger.info("Get country/city for compute region %s in %s." %
                             (self.native_region_name, self.cloud_provider))

            sql_cmd = ("SELECT country, city FROM region_master WHERE "
                       "edge_location_region_id = '%s'" %
                       (region_idx))
            self.logger.info("sql command: %s" % sql_cmd)
            cursor.execute(sql_cmd)
            country, city = cursor.fetchone()
            self.dbh.cursorclose(cursor)
        except Exception as ex:
            self.logger.error("Failed to get country and city "
                              "from region_master table for region %s in %s. "
                              "Exception: %s" %
                              (self.native_region_name, self.cloud_provider,
                               ex))
            self.dbh.cursorclose(cursor)
            return
        cloud_provider = self.cloud_provider

        # Prepare ips
        inbound_access_ip_list = []
        _all_success = True

        for app_group_info in inbound_access_ip_mapping:
            if not app_group_info['public_ip']:
                try:
                    _ip, _type = self.get_ip_for_country_city_cloud(
                        country, city, cloud_provider)
                except Exception as ex:
                    self.logger.error("Failed to get IP for %s in %s, %s, %s for inbound access app. "
                                      "Exception: %s" %
                                      (self.acct_id, city, country,
                                       self.cloud_provider, ex))
                    _ip = None

                if _ip:
                    self.logger.info("Got %s IP %s for acct id %s for inbound access app. IP is "
                                     "registered in %s, %s" %
                                     (self.cloud_provider, str(_ip), self.acct_id,
                                      city, country))
                else:
                    _ip = ''
                    _all_success = False
                    # No IP available, might need to send a message to DevOps
                    self.logger.error("Failed to get IP for %s in %s, %s, %s for inbound access app. " %
                                      (self.acct_id, city, country,
                                       self.cloud_provider))

                app_group_info['public_ip'] = _ip

            _protocols = defaultdict(lambda: set())
            for app in app_group_info['app_group']:
                _protocols[str(app['protocol'])].add(str(app['port']))

            _protocol_list = []
            for _protocol in _protocols:
                _protocol_list.append({'name': _protocol, 'port': list(_protocols[_protocol])})

            inbound_access_ip_list.append({'public_ip': app_group_info['public_ip'], 'protocol': _protocol_list})

        inbound_access_ip_mapping_blob = compresess_cfg(json.dumps(inbound_access_ip_mapping))
        cust_node_model.inbound_access_ip_mapping = inbound_access_ip_mapping_blob


        if _all_success:
            self.logger.info("Successfully allocated IPs for all inbound_access apps for cust node %s. " % node_id)
        _s = compresess_cfg(json.dumps(inbound_access_ip_list))
        cust_node_model.inbound_access_ip_list = _s

        sql = ("UPDATE instance_master SET inbound_access_ip_list = '%s' "
               "WHERE id = %s" %
               (_s, self.cluster_id))
        self.logger.info(sql)

        try:

            cursor = self.dbh.get_cursor(self.dbh)
            cursor.execute(sql)
            self.dbh.cursorclose(cursor)
        except Exception as ex:
            self.logger.error("Update inbound_access_ip_list field in instance_master table "
                              "failed. Exception: %s" % str(ex))
            self.dbh.cursorclose(cursor)

        cust_node_model.save(self.dbh)

    def get_onboarding_cfg(self, tenant_id):

        self.logger.info("Get get_onboarding_cfg for tenant_id %s" %
                         tenant_id)

        cursor = self.dbh.get_cursor(self.dbh)

        sql_cmd = ("SELECT onboarding from xxx_cfgserv_panorama_cfg_request "
                   "WHERE tenant_id = %s order by id DESC LIMIT 1" %
                   tenant_id)
        self.logger.info("sql command: %s" % sql_cmd)

        try:
            cursor.execute(sql_cmd)
            ret = cursor.fetchone()
            self.dbh.cursorclose(cursor)
        except Exception as ex:
            self.logger.error("Failed to get_onboarding_cfg for tenant_id %s "
                              "Exception: %s" %
                              (tenant_id, ex))
            self.dbh.cursorclose(cursor)
            return None

        onboarding_cfg = uncompress_cfg(ret[0])
        self.logger.info("onboarding_cfg for tenant_id %s : "
                         "%s" % (tenant_id, onboarding_cfg))

        return onboarding_cfg

    def secure_inbound_access_apps_grouping(self, orig_file_cont, name, inbound_access_ip_mapping):
        '''
        Used to extract IP grouping from onboard configuration entry.
        parse config in entry xml node. return entry_dict with the grouping info.

        Args:
            orig_file_cont(string): xml config
            name: site name
            inbound_access_ip_mapping(list): list of existing app groups
                e.g.
                [
                   {
                      "public_ip":"*************",
                      "app_group":[
                         {
                            "ip":"************",
                            "protocol":"TCP",
                            "port":"80",
                            "name":"apache"
                         },
                         {
                            "ip":"************",
                            "protocol":"TCP",
                            "port":"27",
                            "name":"ssh2"
                         }
                      ]
                   }
                ]
        '''

        self.logger.info("Input entry: %s name: %s " % (str(orig_file_cont), str(name)))

        dedicated_list = []
        max_eip = 0
        tmp_list = []
        shared_list = []
        if inbound_access_ip_mapping is None:
            inbound_access_ip_mapping = []

        entry = ET.fromstring(orig_file_cont)
        rn_on_boarding = entry.find('.//cloud_services/remote-networks/onboarding')
        self.logger.info("rn_on_boarding: %s " % (str(rn_on_boarding)))

        if rn_on_boarding is None:
            return [], [], 0
        
        for oentry in rn_on_boarding.findall('entry'):
            entry_name = oentry.get('name')
            self.logger.info("*** inbound access **** XML : %s" % (entry_name))
            if name == entry_name:

                inbound_access = oentry.find('inbound-access')
                if inbound_access is None:
                    break
                inbound_enabled = inbound_access.findtext('enabled')
                maxIP = inbound_access.findtext('public-ip')
                max_eip = int(maxIP)
                if inbound_enabled != 'yes':
                    break
                applications = oentry.find('inbound-access/applications')
                if applications is not None:
                    for entry2 in applications.findall('entry'):
                        app_name = entry2.get('name')
                        private_IP = entry2.findtext('private-ip')
                        port = entry2.findtext('port')
                        proto = entry2.findtext('protocol')
                        dedicated = entry2.findtext('dedicated-ip')

                        tmp_list = [(private_IP, port, proto, app_name)]
                        if dedicated == 'yes':
                            if tmp_list not in dedicated_list:
                                k = len(dedicated_list)
                                if k >= max_eip:
                                    break
                                dedicated_list.extend(tmp_list)
                        else:
                            if tmp_list not in dedicated_list:
                                shared_list.extend(tmp_list)
                break

        dedicated_list.sort(key=lambda x: (ipaddress.ip_address(x[0]),) + x[1:])
        shared_list.sort(key=lambda x: (ipaddress.ip_address(x[0]),) + x[1:])

        # group by (port, proto), same (port, proto) can not share IP,
        # take one from each group which needs one public IP

        existing_mapping = []
        ip_to_app = defaultdict(set)
        app_to_ip = defaultdict(str)

        for group in inbound_access_ip_mapping:
            pub_ip = group['public_ip']
            existing_mapping.append([])
            for group_mem in group['app_group']:
                tmp = (group_mem['ip'], group_mem['port'],
                       group_mem['protocol'], group_mem['name'])
                ip_to_app[pub_ip].add(tmp)
                app_to_ip[tmp] = group['public_ip']

                lst = [group_mem['ip'], group_mem['port'],
                       group_mem['protocol'], group_mem['name']]
                existing_mapping[-1].append(lst)

        # sort the dict by public ip. public IP can be empty
        ip_to_app = {k: ip_to_app[k] for k in sorted(ip_to_app, key=lambda x: ipaddress.ip_address(x) if x else 0)}

        intersect = set(tuple(col) for row in existing_mapping for col in row).intersection(set(tuple(row) for row in shared_list))

        # elements to be deleted from existing
        deleted = set(tuple(col) for row in existing_mapping for col in row) - set(intersect)

        for app in deleted:
            cur_pub = app_to_ip[tuple(app)]
            ip_to_app[cur_pub].remove(tuple(app))
            if not ip_to_app[cur_pub]:
                ip_to_app.pop(cur_pub)

        only_new = set(tuple(row) for row in shared_list) - set(intersect)
        # sort new apps by (private ip, port, protocol, name) in ascending order
        only_new = sorted(list(only_new), key=lambda x: (ipaddress.ip_address(x[0]),) + x[1:])

        # change format of ip_to_app
        updated = []
        for _, group in ip_to_app.items():
            updated.append([list(app) for app in group])

        keys_each_gp = []
        for group in updated:
            keys_each_gp.append([])
            for app in group:
                if (app[1], app[2]) not in keys_each_gp[-1]:
                    keys_each_gp[-1].append((app[1], app[2]))

        for app in only_new:
            for i in range(len(updated)):
                if (app[1], app[2]) not in keys_each_gp[i]:
                    updated[i].append(list(app))
                    keys_each_gp[i].append((app[1], app[2]))
                    break
            else:
                updated.append([list(app)])
                keys_each_gp.append([(app[1], app[2])])

        # dedicated apps in dedicated_list
        # shared apps in updated_shared_list

        updated_shared_list = updated
        self.logger.info("shared_list: %s dedicated_list %s " % (
            str(updated_shared_list), str(dedicated_list)))
        return updated_shared_list, dedicated_list, max_eip

    def get_subnet_name_from_ip(self, clusterid):
        subnet_name = None
        if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger, compute_region_info=self.native_region_name) == IPAMService.ENABLED:
            subnet_list = self.ipam_utils.ipam_fetch_address(cluster_id=clusterid, type='pupi',
                                                             fetch_fields=['subnet_range_name'])
            if subnet_list is not None and len(subnet_list) > 0 and len(subnet_list[0]) > 0 and subnet_list[0][1] != '':
                subnet_name = subnet_list[0][1]
        else:
            sql_cmd = f"SELECT subnet_range_name FROM public_ip_pool where cluster_id='{clusterid}'"
            self.logger.info("sql command to fetch subnet name: %s" % sql_cmd)
            try:
                cursor1 = self.dbh.get_cursor()
                cursor1.execute(sql_cmd)
                subnet_res = cursor1.fetchone()
                if subnet_res is not None and len(subnet_res) > 0 and subnet_res[0] != '':
                    subnet_name = subnet_res[0]
                self.dbh.cursorclose(cursor1)
            except Exception as ex:
                msg = f"Failed to get subnet name from public_ip_pool for cluster_id {clusterid}, exception: {ex}"
                self.logger.error(msg)
                self.dbh.cursorclose(cursor1)
        self.logger.info(f"subnet name for cluster_id {clusterid}: {subnet_name}")
        return subnet_name


class AWSIPHandler(IPHandler):
    """
    IP handler class for AWS
    """

    def allocate_public_ip_for_sp_tenant(self, instance_entry_dict):
        """
        Allocate PUPI IP from public_ip_pool.

        Returns:
            str: The PUPI IP allocated from the pool

        This function allocates a PUPI IP address from the public IP pool

        Throws Exception for cases where PUPI IP allocation fails.
        """
        ip_list = self.allocate_public_ip_for_customer(None)
        instance_entry_dict['public_ip'] = ip_list[0][1]
        return ip_list[0][1]

    def allocate_public_IP_via_public_IP_pool(self, old_public_ip):
        """Allocate EIP from public_ip_pool and disassociate the IP from CFT.

        Args:
            old_public_ip (str): EIP assigned by AWS CFT

        Returns:
            str: The new public IP allocated from the pool
            bool: True/False
            str: Error message if allocation failed

        This function allocates a new public IP address from the public IP pool
        and disassociates the old public IP address assigned by CloudFormation.
        It returns the new public IP address, a boolean indicating if the
        allocation was successful, and an error message if it failed.
        """

        public_ip = None
        ret = False
        err_msg = None
        try:
            self.logger.info("Try to allocate public IP from public IP pool "
                             "for cluster %s in account %s region %s." %
                             (self.cluster_id, self.acct_id,
                              self.native_region_name))

            ip_list = self.allocate_public_ip_for_customer(old_public_ip)

            if ip_list[0][1] == old_public_ip:
                self.logger.info("Will use IP from CFT.")
                self.reset_public_ip_pool_info()
                public_ip = old_public_ip
                ret = True
                err_msg = f"Failed to get the public IP for customer, using f{old_public_ip}"
                return

            # Grab the instance and network interfaceId of which the old
            # public ip is associated with
            old_ip_info = self.get_public_ip_info(old_public_ip)
            if not old_ip_info:
                self.logger.error("Failed to get old public ip info %s." %
                                  old_public_ip)
                self.reset_public_ip_pool_info()
                public_ip = old_public_ip
                ip_list[0][1] = old_public_ip
                self.write_ip_to_instance_master_table(ip_list, True)
                err_msg = f"Failed to get old public ip info {old_public_ip}"
                ret = True
                return

            old_ip_network_interface_id = old_ip_info.get('NetworkInterfaceId', None)
            old_ip_private_ip_address = old_ip_info.get('PrivateIpAddress', None)
            if (old_ip_network_interface_id is None or
                    old_ip_private_ip_address is None):
                self.logger.error("Failed to get the info of IP %s" %
                                  old_public_ip)
                self.reset_public_ip_pool_info()
                public_ip = old_public_ip
                ip_list[0][1] = old_public_ip
                self.write_ip_to_instance_master_table(ip_list, True)
                err_msg = f"Failed to get the info of IP {old_public_ip}"
                ret = True
                return

            clean_ip_info = self.get_public_ip_info(ip_list[0][1])
            if not clean_ip_info:
                self.logger.error("Failed to get info for IP %s." %
                                  ip_list[0][1])
                self.reset_public_ip_pool_info()
                public_ip = old_public_ip
                ip_list[0][1] = old_public_ip
                self.write_ip_to_instance_master_table(ip_list, True)
                err_msg = f"Failed to get the info of IP {ip_list[0][1]}"
                ret = True
                return

            clean_ip_allocation_id = clean_ip_info['AllocationId']

            # Disassociate and release public IP assigned by AWS
            self.disassociate_release_public_ip(old_public_ip)

            # Associate the clean IP with the instance
            try:
                if is_aws_fedramp_env_govcloud(logger=self.logger):
                    ec2client = get_aws_govcloud_dp_acct_boto3_ec2_client(logger=self.logger,
                                                        region_name=self.native_region_name)
                else:
                    ec2client = boto3.client('ec2', self.native_region_name,
                                             aws_access_key_id=cfg.get('aws_access_key_id', None),
                                             aws_secret_access_key=cfg.get('aws_secret_access_key', None))
                res = ec2client.associate_address(
                    AllocationId=str(clean_ip_allocation_id),
                    NetworkInterfaceId=str(old_ip_network_interface_id),
                    PrivateIpAddress=str(old_ip_private_ip_address))
            except Exception as e:
                self.logger.error("Failed to associate IP %s with "
                                  "instance %s. Exception %s" %
                                  (ip_list[0][1],
                                   str(old_ip_info['InstanceId']), e.args))
                self.reset_public_ip_pool_info([public_ip])
                err_msg = f"Failed with Exception, unable to associate IP {public_ip}"
                public_ip = old_public_ip
                ip_list[0][1] = old_public_ip
                self.write_ip_to_instance_master_table(ip_list, True)
                ret = True
            self.logger.info("Successfully associate IP %s with "
                             "instance %s networkInterface %s "
                             "Private IP %s" %
                             (str(ip_list[0][1]),
                              str(old_ip_info.get('InstanceId')),
                              str(old_ip_network_interface_id),
                              str(old_ip_private_ip_address)))

            if str(ip_list[0][2]) != "PUPI":
                if str(ip_list[0][2]) == 'BYOIP':
                    self.use_PBF = False
                else:  # Clean IP from public cloud
                    self.use_PBF = USE_PBF_FOR_CLEAN_IP_FROM_AWS

            # Ignore PBF for Explict Proxy Firewall
            if self.node_type in (NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY):
                self.use_PBF = False

            self.update_column_use_PBF(self.use_PBF)
            public_ip = ip_list[0][1]
            ret = True

        except Exception as E:
            self.logger.error(f"Failed with exception {E.args}, traceback: {traceback.format_exc()}, "
                              f"locals: {locals()}")
            err_msg = "Failed with Exception"
            public_ip = old_public_ip
            ip_list[0][1] = old_public_ip
            self.write_ip_to_instance_master_table(ip_list, True)
            ret = False
        finally:
            return ret, public_ip, err_msg

    def reserve_sticky_ip(self, num_of_IPs, cluster_id):
        """
        Allocate AWS elastic IP and add them to public_ip_pool table.

        @type num_of_IPs: int
        @param num_of_IPs: number of IPs to allocate
        @type cluster_id: int
        @param cluster_id: cluster id for this IP address, 0 means reserved
            IP for the customer but not used by any cluster
        @rtype: list of str
        @return: list of IPs
        """
        # Allocate elastic IP addresses from AWS
        self.logger.info("Allocate %s elastic IP addresses." % num_of_IPs)
        res = []

        if not self.native_region_name:
            self.logger.error("Unable to get native compute region name for "
                              "%s" % self.compute_region_id)
            return res
        self.logger.info("Native compute region %s" % self.native_region_name)

        if is_aws_fedramp_env_govcloud(logger=self.logger):
            ec2client = get_aws_govcloud_dp_acct_boto3_ec2_client(logger=self.logger,
                                                region_name=self.native_region_name)
        else:
            ec2client = boto3.client('ec2', self.native_region_name,
                                     aws_access_key_id=cfg.get('aws_access_key_id', None),
                                     aws_secret_access_key=cfg.get('aws_secret_access_key', None))
        try:
            for _ in range(num_of_IPs):
                allocation = ec2client.allocate_address(Domain='vpc')
                res.append(allocation['PublicIp'])
        except Exception as e:
            self.logger.error("Failed to allocate elastic IP in %s. "
                              "Exception %s" % (self.native_region_name, e))
            return res

            # Insert the allocated IP addresses to public_ip_pool table
        cursor = self.dbh.get_cursor(self.dbh)

        try:
            self.logger.info("Get country/city for compute region %s in %s." %
                             (self.native_region_name, self.cloud_provider))

            sql_cmd = ("SELECT country, city FROM region_master WHERE "
                       "native_compute_region_name = '%s' AND "
                       "cloud_provider = '%s'" %
                       (self.native_region_name, self.cloud_provider))
            self.logger.info("sql command: %s" % sql_cmd)
            cursor.execute(sql_cmd)
            country, city = cursor.fetchone()
        except Exception as ex:
            self.logger.error("Failed to get country and city "
                              "from region_master table for region %s in %s. "
                              "Exception: %s" %
                              (self.native_region_name, self.cloud_provider,
                               ex))
            self.dbh.cursorclose(cursor)
            return []

        try:
            for ip in res:
                if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                                           compute_region_info=self.native_region_name) == IPAMService.ENABLED:
                    # This is a special case where we need to add and allocate the IP at the same time,
                    # hence passing match_address and match_count both.
                    self.ipam_utils.ipam_allocate_address(match_type='public_cloud',
                                                          match_address=ip,
                                                          match_count=1,
                                                          set_country=country,
                                                          set_city=city,
                                                          set_cloud=self.cloud_provider,
                                                          set_account_id=self.acct_id,
                                                          set_cluster_id=cluster_id, set_node_type=self.node_type,
                                                          set_location=self.native_region_name,
                                                          set_type='public_cloud',
                                                          set_status='active',
                                                          call_context="orch:reserve_sticky_ip")
                else:
                    self.logger.info("Insert elastic IP %s into public_ip_pool "
                                     "table" % ip)
                    sql_cmd = ("INSERT INTO public_ip_pool "
                               "(ip, cloud, location, country, city, acct_id, "
                               "IP_type, cluster_id, node_type) values ('%s', '%s', '%s', "
                               " '%s', '%s', %s, 'public_cloud', %s, %s)" %
                               (ip, self.cloud_provider, self.native_region_name,
                                country, city, self.acct_id, cluster_id, self.node_type))
                    self.logger.info("sql command: %s" % sql_cmd)
                    cursor.execute(sql_cmd)
                    self.dbh.cursorclose(cursor)
        except Exception as ex:
            self.logger.error("Failed to insert elastic IP %s into "
                              "public_ip_pool table. Exception %s" % (ip, ex))
            self.dbh.cursorclose(cursor)
            return []

        return res

    def detach(self, dbh, inst, interface):
        pass

    def disassociate_release_public_ip(self, public_ip):
        """
        Disassociate and release the public IP assigned by public cloud

        @type dbh: db handler object
        @param dbh: db handler
        @type public_ip: str
        @param public_ip: EIP assigned by AWS CFT
        @return: None
        """
        self.logger.info("Try to disassociate and release EIP %s to AWS" %
                         public_ip)

        # Retrieve the allocation id and association id
        eip_info = self.get_public_ip_info(public_ip)

        if not eip_info:
            self.logger.error("Unable to find EIP record for %s" %
                              public_ip)
            return
        else:
            association_id = eip_info.get('AssociationId', None)
            allocation_id = eip_info.get('AllocationId', None)

        if not association_id:
            self.logger.error("IP %s does not have "
                              "association id." % public_ip)
        else:
            self.logger.info("Disassociate public IP %s, "
                             "association id: %s" %
                             (public_ip, association_id))
            res = self.disassociate_public_ip(association_id)
            if not res:
                self.logger.error("Failed to disassociate public "
                                  "IP %s" % public_ip)
            else:
                self.logger.info("Successfully disassociate public "
                                 "IP %s" % public_ip)

        if not allocation_id:
            self.logger.error("IP %s does not have allocation id." %
                              public_ip)
            return
        else:
            res = self.release_public_ip(allocation_id)
            if not res:
                self.logger.error("Failed to release public "
                                  "IP %s" % public_ip)
            else:
                self.logger.info("Successfully release public IP %s to AWS." %
                                 public_ip)

    def get_public_ip_info(self, public_ip):
        """
        Retrieve the public IP info, such as instance id, network interface
        id, association id and allocation id

        @type ip: str
        @param ip: public IP
        @return: dict
            dict of IP address info
            {
                'InstanceId': 'string',
                'PublicIp': 'string',
                'AllocationId': 'string',
                'AssociationId': 'string',
                'Domain': 'vpc'|'standard',
                'NetworkInterfaceId': 'string',
                'NetworkInterfaceOwnerId': 'string',
                'PrivateIpAddress': 'string',
                'Tags': [
                    {
                        'Key': 'string',
                        'Value': 'string'
                    },
                ]
            }
        """
        self.logger.info("Try to get info of EIP %s" %
                         public_ip)
        if is_aws_fedramp_env_govcloud(logger=self.logger):
            ec2client = get_aws_govcloud_dp_acct_boto3_ec2_client(logger=self.logger,
                                                region_name=self.native_region_name)
        else:
            ec2client = boto3.client('ec2', self.native_region_name,
                                     aws_access_key_id=cfg.get('aws_access_key_id', None),
                                     aws_secret_access_key=cfg.get('aws_secret_access_key', None))

        try:
            res = ec2client.describe_addresses(PublicIps=[str(public_ip)])
        except Exception as ex:
            self.logger.error("Failed to get EIP info for %s. Exception %s" %
                              (public_ip, ex))
            return None

        addresses = res.get('Addresses', [])
        if len(addresses) == 0:
            self.logger.error("Unable to find EIP record for %s" %
                              public_ip)
            return None
        else:
            return addresses[0]

    def disassociate_public_ip(self, association_id):
        """
        Disassociate a public IP based on its association id

        @type association_id: str
        @param association_id: association id of public IP
        @return: bool
            if disassociation is successful
        """
        if is_aws_fedramp_env_govcloud(logger=self.logger):
            ec2client = get_aws_govcloud_dp_acct_boto3_ec2_client(logger=self.logger,
                                                region_name=self.native_region_name)
        else:
            ec2client = boto3.client('ec2', self.native_region_name,
                                     aws_access_key_id=cfg.get('aws_access_key_id', None),
                                     aws_secret_access_key=cfg.get('aws_secret_access_key', None))

        try:
            _ = ec2client.disassociate_address(
                AssociationId=str(association_id))
        except Exception as ex:
            self.logger.error("Failed to disassociate public IP with "
                              "association id %s. Exception %s" %
                              (association_id, ex))
            return False

        self.logger.info("Successfully disassociate public IP with "
                         "association id %s" % association_id)
        return True

    def release_public_ip(self, allocation_id):
        """
        Release a public IP based on its allocation id

        @type allocation_id: str
        @param allocation_id: allocation id of public IP
        @return: bool
            if IP release is successful
        """
        if is_aws_fedramp_env_govcloud(logger=self.logger):
            ec2client = get_aws_govcloud_dp_acct_boto3_ec2_client(logger=self.logger,
                                                region_name=self.native_region_name)
        else:
            ec2client = boto3.client('ec2', self.native_region_name,
                                     aws_access_key_id=cfg.get('aws_access_key_id', None),
                                     aws_secret_access_key=cfg.get('aws_secret_access_key', None))

        try:
            ec2client.release_address(AllocationId=str(allocation_id))
        except Exception as ex:
            self.logger.error("Failed to release public IP for allocation_id "
                              "%s. Exception %s" % (allocation_id, ex))
            return False

        self.logger.info("Successfully release public IP whose allocation "
                         "id is %s" % allocation_id)
        return True

    def recycle_public_ip(self):
        """
        Return IPs to the public_ip_pool

        @return: None
        """
        self.logger.info("Try to return blic IP from %s "
                         "if it is from public_IP_pool." % self.cluster_id)

        self.reset_public_ip_pool_info()


class GCPIPHandler(IPHandler):

    def reserve_sticky_ip(self, num_of_IPs, cluster_id):
        self.logger.error("GCP does not allow to allocate IPs from general "
                          "IP pool. Hence DevOps need to allocate %s more "
                          "IPs for this location." % num_of_IPs)
        return []

    def allocate(self, dbh, inst, interface):
        pass

    def attach(self, dbh, inst, interface):
        pass

    def detach(self, dbh, inst, interface):
        pass

    def copy_ip_from_active_instance(self, passive_inst_id):
        """
        get public_ip and egress_ip_list from self.cluster_id
        then update the public_ip and egress_ip_list field in the entry
        of passive_inst_id
        """

        cursor = self.dbh.get_cursor(self.dbh)
        sql_cmd = ("SELECT public_ip, egress_ip_list, use_PBF, inbound_access_ip_list FROM instance_master "
                   "WHERE id = %s" % self.cluster_id)
        self.logger.info("sql command: %s" % sql_cmd)

        try:
            cursor.execute(sql_cmd)
            ret = cursor.fetchone()
        except Exception as ex:
            self.logger.error("Failed to get public_ip and egress_ip_list from "
                              "instance_master table for %s. Exception: %s" %
                              (self.cluster_id, ex))
            self.dbh.cursorclose(cursor)
            return

        self.logger.info("Active instance %s public ip is %s, egress_ip_list "
                         "is %s, use_PBF is %s, inbound_access_ip_list is %s " %
                         (self.cluster_id, str(ret[0]), str(ret[1]), ret[2], uncompress_cfg(ret[3])))

        self.logger.info("Update use_PBF flag")
        sql_cmd = ("UPDATE instance_master SET use_PBF = %s WHERE id = %s" %
                   (ret[2], passive_inst_id))
        self.logger.info("sql command: %s" % sql_cmd)
        try:
            cursor.execute(sql_cmd)
        except Exception as ex:
            self.logger.error("Failed to update use_PBF in "
                              "instance_master table for %s. Exception: %s" %
                              (passive_inst_id, ex))
            self.dbh.cursorclose(cursor)
            return
        self.logger.info("Successfully update use_PBF for %s in "
                         "instance_master table." % passive_inst_id)

        self.logger.info("Now updating public ip and egress ip list for "
                         "passive instance %s" % passive_inst_id)

        if ret[0]:
            sql_cmd = ("UPDATE instance_master SET public_ip = '%s' "
                       "WHERE id = %s" %
                       (str(ret[0]), passive_inst_id))
            self.logger.info("sql command: %s" % sql_cmd)
            try:
                cursor.execute(sql_cmd)
            except Exception as ex:
                self.logger.error("Failed to update public_ip in "
                                  "instance_master table for %s. Exception: %s" %
                                  (passive_inst_id, ex))
                self.dbh.cursorclose(cursor)
                return
            self.logger.info("Successfully update public ip for %s in "
                             "instance_master table." % passive_inst_id)

        if ret[1]:
            sql_cmd = ("UPDATE instance_master SET egress_ip_list = '%s' "
                       "WHERE id = %s" %
                       (str(ret[1]), passive_inst_id))
            self.logger.info("sql command: %s" % sql_cmd)
            try:
                cursor.execute(sql_cmd)
            except Exception as ex:
                self.logger.error("Failed to update egress_ip_list in "
                                  "instance_master table for %s. Exception: %s" %
                                  (passive_inst_id, ex))
                return
            self.logger.info("Successfully update egress ip list for %s in "
                             "instance_master table." % passive_inst_id)

        if ret[3]:
            sql_cmd = ("UPDATE instance_master SET inbound_access_ip_list = '%s' "
                       "WHERE id = %s" %
                       (convert_bytes_to_str(ret[3]), passive_inst_id))
            self.logger.info("sql command: %s" % sql_cmd)
            try:
                cursor.execute(sql_cmd)
                self.dbh.cursorclose(cursor)
            except Exception as ex:
                self.logger.error("Failed to update inbound_access_ip_list in "
                                  "instance_master table for %s. Exception: %s" %
                                  (passive_inst_id, ex))
                self.dbh.cursorclose(cursor)
                return
            self.logger.info("Successfully update inbound_access_ip_list for %s in "
                             "instance_master table." % passive_inst_id)

        self.dbh.cursorclose(cursor)

class AZRIPHandler(IPHandler):
    """
    This class is not used until Azure has BYOIP support
    """
    def reserve_sticky_ip(self, num_of_IPs, cluster_id):
        self.logger.error("AZR does not allow to allocate IPs from general "
                          "IP pool. Hence DevOps need to allocate %s more "
                          "IPs for this location." % num_of_IPs)
        return []

    def allocate(self, dbh, inst, interface):
        pass

    def attach(self, dbh, inst, interface):
        pass

    def detach(self, dbh, inst, interface):
        pass

    def copy_ip_from_active_instance(self, passive_inst_id):
        """
        get public_ip and egress_ip_list from self.cluster_id
        then update the public_ip and egress_ip_list field in the entry
        of passive_inst_id
        """

        cursor = self.dbh.get_cursor(self.dbh)
        sql_cmd = ("SELECT public_ip, egress_ip_list, use_PBF, inbound_access_ip_list FROM instance_master "
                   "WHERE id = %s" % self.cluster_id)
        self.logger.info("sql command: %s" % sql_cmd)

        try:
            cursor.execute(sql_cmd)
            ret = cursor.fetchone()
        except Exception as ex:
            self.logger.error("Failed to get public_ip and egress_ip_list from "
                              "instance_master table for %s. Exception: %s" %
                              (self.cluster_id, ex))
            self.dbh.cursorclose(cursor)
            return

        self.logger.info("Active instance %s public ip is %s, egress_ip_list "
                         "is %s, use_PBF is %s, inbound_access_ip_list is %s " %
                         (self.cluster_id, str(ret[0]), str(ret[1]), ret[2], uncompress_cfg(ret[3])))

        self.logger.info("Update use_PBF flag")
        sql_cmd = ("UPDATE instance_master SET use_PBF = %s WHERE id = %s" %
                   (ret[2], passive_inst_id))
        self.logger.info("sql command: %s" % sql_cmd)
        try:
            cursor.execute(sql_cmd)
        except Exception as ex:
            self.logger.error("Failed to update use_PBF in "
                              "instance_master table for %s. Exception: %s" %
                              (passive_inst_id, ex))
            self.dbh.cursorclose(cursor)
            return
        self.logger.info("Successfully update use_PBF for %s in "
                         "instance_master table." % passive_inst_id)

        self.logger.info("Now updating public ip and egress ip list for "
                         "passive instance %s" % passive_inst_id)

        if ret[0]:
            sql_cmd = ("UPDATE instance_master SET public_ip = '%s' "
                       "WHERE id = %s" %
                       (str(ret[0]), passive_inst_id))
            self.logger.info("sql command: %s" % sql_cmd)
            try:
                cursor.execute(sql_cmd)
            except Exception as ex:
                self.logger.error("Failed to update public_ip in "
                                  "instance_master table for %s. Exception: %s" %
                                  (passive_inst_id, ex))
                self.dbh.cursorclose(cursor)
                return
            self.logger.info("Successfully update public ip for %s in "
                             "instance_master table." % passive_inst_id)

        if ret[1]:
            sql_cmd = ("UPDATE instance_master SET egress_ip_list = '%s' "
                       "WHERE id = %s" %
                       (str(ret[1]), passive_inst_id))
            self.logger.info("sql command: %s" % sql_cmd)
            try:
                cursor.execute(sql_cmd)
            except Exception as ex:
                self.logger.error("Failed to update egress_ip_list in "
                                  "instance_master table for %s. Exception: %s" %
                                  (passive_inst_id, ex))
                return
            self.logger.info("Successfully update egress ip list for %s in "
                             "instance_master table." % passive_inst_id)

        if ret[3]:
            sql_cmd = ("UPDATE instance_master SET inbound_access_ip_list = '%s' "
                       "WHERE id = %s" %
                       (convert_bytes_to_str(ret[3]), passive_inst_id))
            self.logger.info("sql command: %s" % sql_cmd)
            try:
                cursor.execute(sql_cmd)
                self.dbh.cursorclose(cursor)
            except Exception as ex:
                self.logger.error("Failed to update inbound_access_ip_list in "
                                  "instance_master table for %s. Exception: %s" %
                                  (passive_inst_id, ex))
                self.dbh.cursorclose(cursor)
                return
            self.logger.info("Successfully update inbound_access_ip_list for %s in "
                             "instance_master table." % passive_inst_id)

        self.dbh.cursorclose(cursor)

class OpenstackIPHandler(IPHandler):
    def __init__(self, dbh, inst_model):
        self.inst_model = inst_model
        self.cluster_id = self.inst_model.get_param('clusterid')
        self.acct_id = self.inst_model.get_param('acct_id')
        super().__init__(dbh, self.cluster_id, self.acct_id)

        self.node_type = self.inst_model.get_param('node_type')
        self.cloud_provider = self.inst_model.get_param('cloud_provider')
        self.compute_region_idx = self.inst_model.get_param('compute_region_idx')
        self.compute_region_name = self.inst_model.get_param('compute_region_name')

        self.city = None
        self.country = None

    def get_region_country_city(self, region_id):
        cregion_model = RegionMasterModel(dbh=self.dbh, edge_location_region_id=region_id)
        self.country = cregion_model.get_param('country')
        self.city = cregion_model.get_param('city')

    def allocate_public_ip_for_instance(self):
        """
        Allocate ip from public_ip_pool and set ip status to active
        @return: ip for public_ip of the instance
        """

        # api uses native_region_name
        self.native_region_name = self.compute_region_name
        self.logger.info("openstack prepare to allocate public IP for "
                         "cluster: %d in account %s native_region_idx: %d "
                         "compute_region_name %s cloud_provider: %s "
                         "node_type: %d" %
                         (self.cluster_id, self.acct_id,
                         self.compute_region_idx, self.native_region_name,
                         self.cloud_provider, self.node_type))

        try:
            self.get_region_country_city(self.compute_region_idx)

            ip, ip_type = self.get_ip_for_country_city_cloud(
                    self.country, self.city, self.cloud_provider)
            self.logger.info("openstack allocated public_ip for instance "
                    "cluster_id: %d in country: %s city: %s "
                    "ip: %s ip_type: %s" %
                    (self.cluster_id, self.country, self.city,
                    str(ip), str(ip_type)))
        except Exception as ex:
            ip = None
            self.logger.error("Failed to allocate public ip for instance "
                    "cluster_id: %d in country: %s city: %s ex: %s" %
                    (self.cluster_id, self.country, self.city, str(ex)))

        return ip

    def update_public_ip_pool_for_instance(self):
        """
        udpdate public_ip_pool table to set cluster_id

        @return: None
        """
        ipaddr = self.inst_model.get_param("public_ip")
        if ipaddr is None or ipaddr == 'any' or ipaddr =='':
            return

        if is_ipam_service_enabled(dbh=self.dbh, logger=self.logger,
                                   compute_region_info=self.compute_region_name) == IPAMService.ENABLED:
            self.ipam_utils.ipam_update_address(match_address=ipaddr,
                                                set_cluster_id=self.cluster_id,
                                                call_context="orch:update_public_ip_pool_for_instance")
        else:
            sql = ("UPDATE public_ip_pool SET `cluster_id` = %d "
                   "WHERE `ip` = '%s'" %
                   (self.cluster_id, ipaddr))
            self.logger.info("prepare to update public ip pool sql: %s" % sql)
            try:
                cursor = self.dbh.get_cursor(prepared=True)
                cursor.execute(sql)
                self.dbh.cursorclose(cursor)
                self.logger.info("succesffully update public ip pool cluster_id: %d ip: %s" %
                        (self.cluster_id, ipaddr))
            except Exception as ex:
                self.logger.error("Failed to update public ip pool for cluster %d ip: %s ex: %s " %
                        (self.cluster_id, ipaddr, str(ex)))
                self.dbh.cursorclose(cursor)

        return
