import json
import traceback
import ipaddress
from libs.apis.region_master_api import *
from libs.cloud_providers.oci.instance_manager.oci_instance_info import oci_instance_info_handler, oci_is_clean_ip_project
from libs.common.shared.grpc.proto.avisarpb.avisar_pb2_client import ORCHESTRATION_INSTANCE_PUBLIC_IP_FETCH_FAILED, \
    METRIC_SEVERITY_CRITICAL, ORCHESTRATION_INSTANCE_PUBLIC_IPV6_FETCH_FAILED, ORCHESTRATION_NGPA_OCI_NAT_BRINGUP_FAILED_IP_RESERVATION_FAILURE
from libs.common.shared.sys_utils import *
from io import StringIO, Bytes<PERSON>
from gzip import GzipFile
from collections import defaultdict
from libs.common.shared.sys_utils import NODE_TYPE_SWG_PROXY, \
                  INSTANCE_ROLE_MP_STRING, NODE_TYPE_GP_GATEWAY, \
                  NODE_TYPE_NLB_INSTANCE, NODE_TYPE_BI_NH_PROXY, NODE_TYPE_NAT_INSTANCE, NODE_TYPE_SERVICE_CONN
from libs.common.shared.py3_utils import convert_bytes_to_str, convert_str_to_bytes, b64encode, b64decode
from libs.common.utils import get_target_passive_machine_type, get_target_override_for_version, \
    publish_avisar_event_with_tenant_region_nodetype_key, cloud_instance_has_nic_limit
from libs.cloud_providers.common.swgproxy_bringup import swgproxy_bringup
from libs.model.instancemodel import InstanceModel
from libs.model.instancemodel import copy_egress_ipv6_subnet_list_from_primary_instance, find_standalone_instances_by_custid_and_node_type_and_region_id,  find_instances_by_custid_and_node_type_and_region_id
from orchestration_service.core.orchestrator_nat_mgmt import find_nr_nat_instances_by_custid_and_region_id, is_nat_gateway_nlb_needed
from orchestration_service.core.orchestrator_nlb_mgmt import update_instances_behind_nlb, update_instance_behind_nlb, \
                  forwarding_rule_protocol_dict, backend_service_protocol_dict, session_affinity_dict, conn_pers_on_unhealthy_backends_dict, \
                  health_check_protocol_dict, DEFAULT_FW_RULE_PROTOCOL, DEFAULT_BACKEND_SERV_PROTOCOL, DEFAULT_SESSION_AFFINITY, \
                  DEFAULT_CONN_PERS_ON_UNHEALTHY_BACKENDS, DEFAULT_HC_PROTOCOL, DEFAULT_HC_PORT, DEFAULT_HC_INTERVAL, DEFAULT_HC_TIMEOUT, DEFAULT_HC_UNHEALTHY_THRESHOLD, \
                  DEFAULT_HC_HEALTHY_THRESHOLD, DEFAULT_IS_STRONG_SESSION_AFFINITY_SUPPORTED, DEFAULT_NGPA_PROTO_SESSION_AFFINITY, \
                  DEFAULT_NGPA_NLB_IDLE_TIMEOUT
from libs.model.instancemodel import is_ingress_ip_reduction_enabled, find_instances_by_custid_and_region_id, \
    get_gp_deployed_regions_for_cust, is_nat_dummy_upgrade_ongoing_in_region
import libs.cloud_providers.common.ip_management.ipv4.ip_mgmt as ipv4_mgmt
import libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1 as ipv6_mgmt_1
from libs.model.IP_management_model import IPManagementModel
from libs.model.custmodel import CustomerModel
from libs.model.explicitProxyTenantInfoModel import ExplicitProxyTenantInfoModel
from libs.model.custEpaasConfigModel import CustEpaasConfigModel
from libs.cloud_providers.common.instance_ami_util import get_arch_for_cloud_machine_type
import utils.Exceptions as customException

def get_zone_by_balancing_usage(dbh, my_inst, inst_prof):
    '''
    Called when creating the profile for a new VM. 
    if global_settings is disabled, use default zone, else
    Lookup current zone usage and try to balance deployment across zones equally
    '''
    zone_to_use = inst_prof['zone']
    zones_list = inst_prof['zonesList']
    sql = "SELECT value FROM global_settings WHERE setting='disable_zone_distribution'"
    cursor = dbh.get_cursor()
    try:
        cursor.execute(sql, None)
        ret = cursor.fetchall()
        dbh.cursorclose(cursor)
    except Exception as e:
        dbh.logger.info(f"Failed to read global_settings : {e}")
        dbh.cursorclose(cursor)
        return zone_to_use
    if ret and len(ret) > 0 and ret[0][0] in ("True", "true"):
        dbh.logger.info("disable_zone_distribution is set in global_settings")
        return zone_to_use
    sql = ("SELECT salt_profile FROM instance_master WHERE "
           "custid=%s AND compute_region_idx=%s AND node_type=%s AND cloud_provider='oci' AND id!=%s")
    params = (my_inst.get("cust_id"),
              my_inst.get("region"),
              my_inst.get("node_type_id"),
              my_inst.get("instance_id"))
    cursor = dbh.get_cursor()
    ret = None
    try:
        cursor.execute(sql, params)
        ret = cursor.fetchall()
        dbh.cursorclose(cursor)
    except Exception as e:
        dbh.logger.error(f"Failed to read salt_profile from other instances in this tenants region: {e}")
        dbh.cursorclose(cursor)
        return zone_to_use
    if ret is None:
        return zone_to_use
    if len(ret) == 0:
        return zone_to_use
    usage_dict = {}
    for row in ret:
        salt_profile_str = row[0]
        if salt_profile_str is None:
            continue
        salt_profile = json.loads(salt_profile_str)
        usage = usage_dict.get(salt_profile.get("Zone"), None)
        if usage is None:
            usage_dict[salt_profile.get("Zone")] = 1
        else:
            usage_dict[salt_profile.get("Zone")] = usage + 1
    
    least_usage = None
    for zone in zones_list:
        usage = usage_dict.get(zone, 0)
        if least_usage is None:
            least_usage = usage
            zone_to_use = zone
        else:
            if usage < least_usage:
                least_usage = usage
                zone_to_use = zone
    return zone_to_use

def allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl(dbh,
                                                                   my_inst,
                                                                   egress_ip_list_loc,
                                                                   instance_entry_dict,
                                                                   is_clean_ip_project, cloud_provider='gcp'):
    from orchestration_service.core.orchestrator_ip_mgmt import update_nlb_instances_egress_ip_list
    success = False
    try:
        perform_ingress_ip_reduction = instance_entry_dict.get("perform_ingress_ip_reduction", False)
        dbh.logger.info("perform_ingress_ip_reduction is %s" % str(perform_ingress_ip_reduction))
        dbh.logger.info("The customers topology has edge locations %s on it" % str(egress_ip_list_loc))
        my_node_type = my_inst.get_param("node_type")

        cm = CustomerModel(custid=my_inst.get_param("custid"), dbh=dbh)
        _, _, _, is_allow_list_enabled = cm.get_auto_scale_options(NODE_TYPE_GP_GATEWAY)
        ip_mgmt_model = IPManagementModel(dbh=dbh)

        for my_edge_location in egress_ip_list_loc:
            dbh.logger.info(f"allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl: Processing {my_inst.get_param('name')} for edge {my_edge_location}")
            if my_node_type == NODE_TYPE_NAT_INSTANCE or not perform_ingress_ip_reduction:
                num_of_ips = get_num_of_ips_by_instance_type(dbh, my_inst, perform_ingress_ip_reduction)
                target_node_type = NODE_TYPE_GP_GATEWAY if my_node_type == NODE_TYPE_NAT_INSTANCE else my_node_type

                is_sp_transport_type_loopback = False
                if (my_inst.get_param("is_using_sp_interconnect")
                        and instance_entry_dict.get('sp_egress_type', "SP") not in ['PA', 'HYBRID']):
                    is_sp_transport_type_loopback = True

                #For non NLB instances, keep behavior the same until more improvements pending in 5.2.1
                # For OCI NLB; we will follow the same IP reservation logic we use for GCP NLB for IIR disabled cases; if no IP could be allocated then we eventually
                # will fall back to using ephemeral IPs
                dbh.logger.info(f"allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl: Call reserve_ips_for_cust_region for instance {my_inst.get_param('name')} for edge {my_edge_location}")
                result = ip_mgmt_model.reserve_ips_for_cust_region(my_inst.get_param("custid"),
                                                                   my_inst.get_param("node_type"),
                                                                   my_edge_location,
                                                                   my_inst.get_param("compute_region_idx"), num_of_ips,
                                                                   target_node_type=target_node_type,
                                                                   reserve_pupi_ip=is_sp_transport_type_loopback)
                if result['ok'] == True:
                    ip_list = result['ip_list']
                    dbh.logger.info("IP Reservation list: %s" % str(ip_list))
                else:
                    success = False
                    err_msg = ("Failed to reserver IP address for custid %s, node_type: %s, edge_region_idx: %s,\
                                                                                    inst.compute_region_id: %s" % (
                        str(my_inst.get_param("custid")), str(my_inst.get_param("node_type")),
                        str(my_edge_location), str(my_inst.get_param("compute_region_idx"))))
                    # For OCI; we want to fall back to using ephemeral IPs for NLB even for clean IP tenants and only fail for NAT instances
                    if is_clean_ip_project and my_node_type == NODE_TYPE_NAT_INSTANCE:
                        err_msg = (f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Failed to allocate reserved IP to NAT instance {str(my_inst.get_param('name'))} for edge {my_edge_location} for custid {my_inst.get_param('custid')}")
                        raise Exception(err_msg)
                    else:
                        err_msg = (f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Failed to allocate reserved IP to NLB instance {str(my_inst.get_param('name'))} for edge {my_edge_location} for custid {my_inst.get_param('custid')} Will fall back to using ephemeral IPs.")
                        dbh.logger.info(err_msg)
        
                dbh.logger.info(f"allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl: Binding reserrved IPs for instance {my_inst.get_param('name')} for cloud {cloud_provider}")
                if is_allow_list_enabled and target_node_type != NODE_TYPE_NLB_INSTANCE:
                    result = ip_mgmt_model.bind_allow_listed_ips_to_instance(my_inst, my_edge_location, 1,
                                                                             target_node_type=target_node_type,
                                                                             reserve_pupi_ip=is_sp_transport_type_loopback)
                else:
                    result = ip_mgmt_model.bind_reserved_ips_to_instance(my_inst, my_edge_location, 1,
                                                                             target_node_type=target_node_type, cloud_provider=cloud_provider)
                if result["ok"] == False:
                    err_msg = "Failed to bind reserved IP addresses to instance %s" % str(my_inst.get_param("name"))
                    # For OCI; we want to fall back to using ephemeral IPs for NLB even for clean IP tenants and only fail for NAT instances
                    if is_clean_ip_project and my_node_type == NODE_TYPE_NAT_INSTANCE:
                        err_msg = (f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Failed to bind reserved IP to NAT instance {str(my_inst.get_param('name'))} for edge {my_edge_location} for custid {my_inst.get_param('custid')}")
                        raise Exception(err_msg)
                    else:
                        err_msg = (f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Failed to bind reserved IP to NLB instance {str(my_inst.get_param('name'))} for edge {my_edge_location} for custid {my_inst.get_param('custid')} Will fall back to using ephemeral IPs.")
                        dbh.logger.info(err_msg)
                else:
                    dbh.logger.info(f"allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl: Successfully bound edge IP address {my_inst.get_param('public_ip')} for instance {my_inst.get_param('name')}")
                    dbh.logger.info(f"allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl: EIP list {my_inst.get_param('egress_ip_list')} for instance {my_inst.get_param('name')}")

                # This is to handle cases wherein NLB IP could not be retrieved from tenant or general pool; in this case for OCI cloud
                # we will fall back to assigning OCI Cloud ephemeral IPs for each edge region on the NLB for IIR disabled BF tenants
                # Update NLB instance egress_ip_list with edge region indexes and empty IP values for further processing by provisioning service to plumb each
                # new edge ephemeral IPs on the NLB instance. This is not relevant for GCP as we never use NLB ephemeral IPs for GCP NLB
                if my_node_type == NODE_TYPE_NLB_INSTANCE:
                    ret = update_nlb_instances_egress_ip_list(dbh, my_inst, my_edge_location)
                    dbh.logger.info(f"allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl: Updated EIP list for {my_inst.get_param('name')} for region {my_edge_location} to {my_inst.get_param('egress_ip_list')}")

            elif my_node_type == NODE_TYPE_NLB_INSTANCE and perform_ingress_ip_reduction:
                dbh.logger.info("No egress IPs are allocated to NLB if ingress_ip_reduction is enabled")

        success = True
    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success

def allocate_nat_nlb_egress_ip_for_new_instance_impl(dbh,
                                                     instance_entry_dict, edge_region_idx=None):
    success = False
    try:
        is_clean_ip_project = oci_is_clean_ip_project(dbh, acct_id=instance_entry_dict['acct_id'])
        if is_clean_ip_project:
            dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Tenant {instance_entry_dict['acct_id']} is a clean IP project")
        else:
            dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Tenant {instance_entry_dict['acct_id']} is NOT a clean IP project")

        my_inst = InstanceModel(dbh=dbh, iid=instance_entry_dict['instance_id'])
        if my_inst.get_param("id") in [None, 0]:
            err_msg = "Failed to get the instance ref for instance id %s" % str(instance_entry_dict["instance_id"])
            raise Exception(err_msg)

        perform_ingress_ip_reduction = instance_entry_dict.get("perform_ingress_ip_reduction", False)

        # Special handling for OCI edge region Cloud NAT gateways as we will provision a new NATGW per edge
        # we will also update the egress IP details for the NLB instances behind the NAT here
        compute_region_idx = instance_entry_dict.get('region', '')
        edge_region_idx = instance_entry_dict.get('edge_region_idx', '')
        dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Handle NAT/NLB for Compute {compute_region_idx}, Edge {edge_region_idx} for instance {my_inst.get_param('name')}")
        #if str(compute_region_idx) != str(edge_region_idx) and my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE:
        #    dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Allocating egress IP for edge region {edge_region_idx} OCI NAT GW with instance info {instance_entry_dict}")
        #    success = allocate_new_egress_ip_for_nat_instances(dbh, my_inst, perform_ingress_ip_reduction, False, edge_region_idx, cloud_provider='oci')
        #    if not success:
        #        dbh.logger.error(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Failed to allocate egress IP for OCI NAT instance {my_inst.get_param('name')} for edge {edge_region_idx}")
        #        raise Exception(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Failed to allocate egress IP for OCI NAT instance {my_inst.get_param('name')} for edge {edge_region_idx}")

        cm = CustomerModel(custid=my_inst.get_param("custid"), dbh=dbh)
        _, _, _, is_allow_list_enabled = cm.get_auto_scale_options(NODE_TYPE_GP_GATEWAY)

        dbh.logger.info("Allocating public IP and egress IP using new API")
        dbh.logger.info("Locals: %s" % str(locals()))

        # Assign public IP for NAT, GW or NLB
        ip_mgmt_model = IPManagementModel(dbh=dbh)
        result = {'ok': False}

        num_of_ips = get_num_of_ips_by_instance_type(dbh, my_inst, perform_ingress_ip_reduction)

        #CYR-42950: all node_type NLB instances should use target_node_type=NODE_TYPE_NLB_INSTANCE(161)
        target_node_type = NODE_TYPE_GP_GATEWAY if my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE else my_inst.get_param("node_type")

        # For OCI cloud NLB instances we want to pick IPs if available from the tenant public_ip_pool
        # else always fallback to using OCI cloud ephemeral IPs pool instead of picking any IP from general pool acct_id 0
        # We need to ensure that we do not double reserve IPs for edge NAT gateways
        #Need to ensure that we do not double reserve IPs for edge NAT gateways
        #Reserve the IPs for NLB case, or PA-NAT case (edge/compute) and skip reserving of IPs for edge NAT in case GCP CNAT since it is handled further down.
        if my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE and str(compute_region_idx) != str(edge_region_idx):
            dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Call reserve_ips_for_cust_region skipped for {my_inst.get_param('name')} in compute {compute_region_idx}, Edge {edge_region_idx} for edge NAT gateway handling")
        else:
            # Even for OCI NLB we want to follow the same IP reservation logic we use for GCP NLB but eventually fall back to using ephemeral IPs
            dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Call reserve_ips_for_cust_region for instance {my_inst.get_param('name')} in compute {compute_region_idx}, Edge {edge_region_idx}")
            result = ip_mgmt_model.reserve_ips_for_cust_region(my_inst.get_param("custid"), my_inst.get_param("node_type"), my_inst.get_param("compute_region_idx"), my_inst.get_param("compute_region_idx"), num_of_ips, target_node_type=target_node_type, reserve_pupi_ip=(my_inst.get_param("is_using_sp_interconnect")!=0))
            if not result.get('ok'):
                dbh.logger.error(f"Failed to pre-allocate IPs for instance {my_inst.get_param('id')} in region {my_inst.get_param('compute_region_idx')} for node_type {target_node_type}.Continuing anyway")

        # Bind the Public IP for the instance.
        # NLB IP doesn't support allowlist configuration. NLB IP should select reserved IP.
        # For OCI Cloud NAT we do not need to call bind_reserved_public_ip_to_instance for edge NAT gateways
        if is_allow_list_enabled and target_node_type != NODE_TYPE_NLB_INSTANCE and str(compute_region_idx) == str(edge_region_idx):
            deployed_regions = dbh.get_gp_deployed_regions_for_cust_dbh(NODE_TYPE_GP_GATEWAY,
                                                                        my_inst.get_param("custid"),
                                                                        my_inst.get_param("compute_region_idx"))
            # if allowlist feature is enabled but compute region is not selected by customer, the compute region
            # should pick ip from reserved ip pool
            if my_inst.get_param("compute_region_idx") in deployed_regions:
                dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: allow_list_enabled: Call bind_allowed_public_ip_to_instance for instance {my_inst.get_param('name')} in compute {compute_region_idx}, Edge {edge_region_idx} for compute NAT gateway handling")
                result = ip_mgmt_model.bind_allowed_public_ip_to_instance(my_inst,
                                                                    my_inst.get_param("compute_region_idx"),
                                                                    target_node_type=target_node_type)
            else:
                dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: allow_list_enabled: Call bind_reserved_public_ip_to_instance for instance {my_inst.get_param('name')} in compute {compute_region_idx}, Edge {edge_region_idx} for compute NAT gateway handling")
                result = ip_mgmt_model.bind_reserved_public_ip_to_instance(my_inst,
                                                                           my_inst.get_param("compute_region_idx"),
                                                                           target_node_type=target_node_type)
        else:
            if my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE and str(compute_region_idx) != str(edge_region_idx):
                dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Skip Call to bind_reserved_public_ip_to_instance for instance {my_inst.get_param('name')} in compute {compute_region_idx}, Edge {edge_region_idx} for edge NAT gateway handling")
                result = {}
                result['ok'] = True
            else:
                dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Call bind_reserved_public_ip_to_instance for instance {my_inst.get_param('name')} in compute {compute_region_idx}, Edge {edge_region_idx} for compute node handling")
                result = ip_mgmt_model.bind_reserved_public_ip_to_instance(my_inst, my_inst.get_param("compute_region_idx"), target_node_type=target_node_type)

        if result['ok'] == False:
            # For OCI; we want to fall back to using ephemeral IPs for NLB even for clean IP tenants and only fail for NAT instances
            if is_clean_ip_project and my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE:
                err_msg = (f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Failed to allocate/bind reserved IP to NAT instance {str(my_inst.get_param('name'))}")
                dbh.logger.error(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Failed to allocate/bind reserved IP to NAT instance {str(my_inst.get_param('name'))}")
                raise Exception(err_msg)
            else:
                err_msg = (f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Failed to allocate/bind reserved IP to NLB instance {str(my_inst.get_param('name'))}. Will fall back to using ephemeral IPs.")
                dbh.logger.info(err_msg)

        dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Completed binding public IP for instance {my_inst.get_param('name')} in region {my_inst.get_param('compute_region_idx')} with result: {result}")
        # We would be using node type as GP gateway since NAT and NLB is only for gateways.

        egress_ip_list_loc = \
            my_inst. \
                get_existing_egress_ip_region_list_for_pinned_instance(my_inst.get_param("custid"),
                                                                       my_inst.get_param("compute_region_idx"),
                                                                       node_type=NODE_TYPE_GP_GATEWAY,
                                                                       original_node_type=my_inst.get_param("node_type"))
        dbh.logger.info("The egress locations for the instance are %s" % str(egress_ip_list_loc))
        has_edge_region_idx = len(egress_ip_list_loc)
        # Make sure we only call allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl for edge location
        # NAT gateways.
        # we also need to call allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl for NLB nodes IIR disabled scenarios
        # to add edge specific ingress IP entry on the NLB. This is to handle the case when BF tenants onboard only edge region
        if (has_edge_region_idx and str(compute_region_idx) != str(edge_region_idx) and my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE) or (has_edge_region_idx and my_inst.get_param('node_type') == NODE_TYPE_NLB_INSTANCE):
            dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Process node {my_inst.get_param('name')} for edge {edge_region_idx}")
            # For OCI Cloud NAT we have a NAT instance per edge and hence we need to send the updated egress_ip_list_loc to only have the NAT edge region
            if my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE:
                egress_ip_list_loc = [edge_region_idx]
                dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Process NAT {my_inst.get_param('name')} for edge {edge_region_idx} with egress_ip_list_loc {egress_ip_list_loc}")
            result = allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl(dbh,
                                                                                    my_inst,
                                                                                    egress_ip_list_loc,
                                                                                    instance_entry_dict,
                                                                                    is_clean_ip_project, cloud_provider='oci')
            if result == False:
                err_msg = ("Failed to add new egress IP for instance edge locations.")
                raise Exception(err_msg)
        else:
            dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Skip re-processing {my_inst.get_param('name')} for edge {edge_region_idx}")
        dbh.logger.info(f"allocate_nat_nlb_egress_ip_for_new_instance_impl: Instance {my_inst.get_param('name')} Public IP {my_inst.get_param('public_ip')} and egress IP list set as {my_inst.get_param('egress_ip_list')}")
        #We want to invoke update_instances_behind_nlb which updates interface_ip_list
        #for all instances behind NLB for all cases:
        #compute_region and edge_region being onboarded:
        #at this point we have the ip address for the compute location
        #as well as the edge locations for the NLB
        #hence all instances behind NLB will have the interface_ip_list
        #updated to ip addresses of the NLB
        if my_inst.get_param("node_type") == NODE_TYPE_NLB_INSTANCE:
            ret = update_instances_behind_nlb(dbh,
                                                my_inst.get_param("custid"),
                                                my_inst.get_param("compute_region_idx"))
            if ret:
                dbh.logger.info(f"Updated all GW's behind NLB {my_inst.get_param('name')}")
            else:
                err_msg = (f"Failed to update all GW's behind NLB {my_inst.get_param('name')}")
                dbh.logger.error(err_msg)
                raise Exception(err_msg)

        # Set the use_pbf column to False for NAT/NLB
        my_inst.update_column_use_PBF(dbh, False)
        dbh.logger.info(f"Set the use_PBF to {False} for instance {my_inst.get_param('id')}")

        success = True
    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success

def allocate_gp_gateways_egress_ip_for_new_instance_behind_nlb_nat_impl(dbh,#
                                                                        instance_entry_dict):
    success=True
    try:
        is_clean_ip_project = False
        perform_ingress_ip_reduction = instance_entry_dict.get("perform_ingress_ip_reduction", False)
        my_inst = InstanceModel(dbh=dbh, iid=instance_entry_dict['instance_id'])
        if my_inst.get_param("id") in [None, 0]:
            err_msg = "Failed to get the instance ref for instance id %s" % str(instance_entry_dict["instance_id"])
            raise Exception(err_msg)

        if my_inst.get_param("is_instance_behind_nlb"):
            # TODO: TEJAS :- check the return status ?
            # At this point the interface ip list is populated.
            update_instance_behind_nlb(dbh, my_inst)
        else:
            dbh.logger.info("GW Instance %s %s is not behind NLB; no update needed" %
                            (instance_entry_dict['instance_id'], my_inst.get_param("name")))

        dbh.logger.info("Allocating public IP and egress IP using new API")
        dbh.logger.info("Locals: %s" % str(locals()))
        # Assign public IP for NAT, GW or NLB
        ip_mgmt_model = IPManagementModel(dbh=dbh)
        result = {'ok': False}

        result = ip_mgmt_model.bind_non_allowed_public_ip_to_instance(my_inst,
                                                                      my_inst.get_param("compute_region_idx"),
                                                                      target_node_type=my_inst.get_param("node_type"))
        dbh.logger.info(f"allocate_gp_gateways_egress_ip_for_new_instance_behind_nlb_nat_impl: result from bind_non_allowed_public_ip_to_instance: {result}")
        if result['ok'] == False:
            err_msg = ("Failed to bind reserved IP to instance %s" % str(my_inst.get_param("name")))
            if is_clean_ip_project:
                raise Exception(err_msg)
            else:
                dbh.logger.error(err_msg)

        egress_ip_list_loc = my_inst.get_existing_egress_ip_region_list_for_pinned_instance(
                                                                       my_inst.get_param("custid"),
                                                                       my_inst.get_param("compute_region_idx"),
                                                                       NODE_TYPE_GP_GATEWAY)
        dbh.logger.info(f"allocate_gp_gateways_egress_ip_for_new_instance_behind_nlb_nat_impl: The egress locations for the instance for instance {str(my_inst.get_param('name'))} are {str(egress_ip_list_loc)}")
        has_edge_region_idx = len(egress_ip_list_loc)
        dbh.logger.info(f"allocate_gp_gateways_egress_ip_for_new_instance_behind_nlb_nat_impl: Has edge regions {has_edge_region_idx}")
        if has_edge_region_idx:
            dbh.logger.info("The customers topology has edge locations %s on it" % str(egress_ip_list_loc))
            for my_edge_location in egress_ip_list_loc:
                if not perform_ingress_ip_reduction:
                    dbh.logger.info("ingress_ip_reduction is not enabled for GW instance [%s]" %
                                                                                my_inst.get_param("name"))
                    if my_inst.get_param("is_instance_behind_nlb"):
                        dbh.logger.info("GW instance [%s] is behind NLB; will not allocate egress ip's" %
                                                                                        my_inst.get_param("name"))
                    else:
                        dbh.logger.info("GW instance [%s] is not behind NLB; will allocate egress ip's" %
                                                                                        my_inst.get_param("name"))

                        ip_mgmt_model.bind_non_allow_listed_ips_to_instance( my_inst,
                                                                             my_edge_location,
                                                                             num_of_ips=1,
                                                                             target_node_type=NODE_TYPE_GP_GATEWAY)
                        # Copy over the IP Address for this  edge location from "egress_ip_list" into
                        #  "interface_ip_list" for the gateway instance.
                        ret = my_inst.update_interface_ip_list_with_egress_ip(my_edge_location)
                        if not ret:
                            err_msg = ("Failed to update interface ip list with egress ip for %s" % my_edge_location)
                            dbh.logger.error("Error: %s" % err_msg)
                            raise Exception(err_msg)

                dbh.logger.info(f"GW instance {my_inst.get_param('name')} has NAT gateway set as {instance_entry_dict.get('has_nat_gateway', False)}")
                if instance_entry_dict.get('has_nat_gateway', False) == True:
                    # This is a case where we have NAT GW in front of the GPGW instance. We will set the value for the
                    # edge location to "ASSIGNED_TO_NAT"  in all cases.
                    dbh.logger.info("Set egress_ip_list value to ASSIGNED_TO_NAT for GW instance [%s]" %
                                                                                             my_inst.get_param("name"))
                    ip_mgmt_model.update_egress_ip_list_with_given_ipstr(my_inst, my_edge_location, "ASSIGNED_TO_NAT")

        success = True

    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success


def allocate_gp_gateways_egress_ip_for_new_instance_impl(dbh,
                                                         instance_entry_dict):
    success = True
    try:
        my_instance_id = instance_entry_dict['instance_id']
        my_inst = InstanceModel(dbh=dbh, iid=my_instance_id)
        if my_inst.get_param("id") in [None, 0]:
            err_msg = "Failed to get the instance ref for instance id %s" % str(my_instance_id)
            raise Exception(err_msg)

        # if the instance is behind, we will use the new API.
        if my_inst.get_param("is_instance_behind_nlb") == True \
                or instance_entry_dict.get("has_nat_gateway", False) == True:
            result = allocate_gp_gateways_egress_ip_for_new_instance_behind_nlb_nat_impl(dbh,
                                                                                         instance_entry_dict)
            if result == False:
                err_msg = "Failed to allocate egress ip for new gateway instance behind NLB/NAT"
                raise Exception(err_msg)
            success = True
    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success

def allocate_egress_ip_for_new_instance_impl(dbh,
                                             instance_entry_dict,
                                             is_passive):
    success = True
    try:
        node_type = None
        my_instance_id = instance_entry_dict['instance_id']
        edge_region_idx = instance_entry_dict['edge_region_idx']
        is_clean_ip_project = False
        dbh.logger.info("allocate_egress_ip_for_new_instance_impl: Setting clean IP project as False for OCI")
        #is_clean_ip_project = gcp_is_clean_ip_project(dbh, acct_id=instance_entry_dict['acct_id'])
        #if is_clean_ip_project:
        #    dbh.logger.info("This is clean IP project")
        #else:
        #    dbh.logger.info("This is not a clean IP project")

        my_inst = InstanceModel(dbh=dbh, iid=my_instance_id)
        if my_inst.get_param("id") in [None, 0]:
            err_msg = "Failed to get the instance ref for instance id %s" % str(my_instance_id)
            raise Exception(err_msg)

        node_type = my_inst.get_param("node_type")

        if is_passive:
            dbh.logger.info("Copy IPs from active instance %s to passive instance %s" %
                                                                        (str(instance_entry_dict['ha_peer']),
                                                                         str(instance_entry_dict['instance_id'])))
            gcp_ip_mgmt = ipv4_mgmt.GCPIPHandler(dbh,
                                                 str(instance_entry_dict['ha_peer']),
                                                 str(instance_entry_dict['acct_id']),
                                                 node_id=instance_entry_dict['custnode_id'])
            gcp_ip_mgmt.copy_ip_from_active_instance(str(instance_entry_dict['instance_id']))
            success =  True
            return

        # Handle the primary instance first.
        has_edge_region_idx = False if str(my_inst.get_param("compute_region_idx")) == str(edge_region_idx) else True
        dbh.logger.info("Instance %s has edge region idx set to %s" % (str(my_instance_id),
                                                                       str(has_edge_region_idx)))

        gcp_ip_mgmt = ipv4_mgmt.GCPIPHandler(dbh,
                                             str(instance_entry_dict['instance_id']),
                                             str(instance_entry_dict['acct_id']),
                                             node_id=instance_entry_dict['custnode_id'])

        # Handle ingress_ip_reduction here as well.
        ip_list = gcp_ip_mgmt.allocate_public_ip_for_customer(None,
                                                              instance_entry_dict['edge_region_idx'],
                                                              True)
        # Refresh InstanceModel object.
        my_inst = InstanceModel(dbh=dbh,
                                iid=instance_entry_dict['instance_id'])
        dbh.logger.info("IP list for instance name %s is %s" % (str(my_inst.get_param("name")),
                                                               str(my_inst.get_param("egress_ip_list"))))

        success = True

    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success

def allocate_egress_ip_for_new_instance(dbh,
                                        instance_entry_dict,
                                        node_type,
                                        is_passive=False, edge_region_idx=None):
    success = False
    try:
        if node_type in [NODE_TYPE_NAT_INSTANCE, NODE_TYPE_NLB_INSTANCE]:
            dbh.logger.info(f"allocate_egress_ip_for_new_instance: Allocating egress IP for edge {edge_region_idx} NAT/NLB instance with {instance_entry_dict}")
            success = allocate_nat_nlb_egress_ip_for_new_instance_impl(dbh, instance_entry_dict, edge_region_idx=edge_region_idx)
        elif node_type == NODE_TYPE_GP_GATEWAY:
            # For OCI GPGW nodes; we will skip IP reservation. We always use ephemeral IPs for
            # MU gateways nodes and update interface_ip_list and lb_details in TF post_processing stage
            dbh.logger.info(f"Skip IP reservation for OCI MU gateway nodes for {instance_entry_dict}")
            success = allocate_gp_gateways_egress_ip_for_new_instance_impl(dbh, instance_entry_dict)
            dbh.logger.info(f"allocate_egress_ip_for_new_instance: Allocating egress IP call for MU instance with {instance_entry_dict} returned {success}")
        elif node_type == NODE_TYPE_SWG_PROXY:
            # for OCI swg nat and nlb, the ip reservation happens during cust-epaas-config creation
            dbh.logger.info(f"Skip IP reservation for SWG OCI {instance_entry_dict}")
            success = True
        else:
            # node_type in [NODE_TYPE_REMOTE_NET, NODE_TYPE_SERVICE_CONN, NODE_TYPE_GP_PORTAL]:
            success = allocate_egress_ip_for_new_instance_impl(dbh,
                                                               instance_entry_dict,
                                                               is_passive)
            return

    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success

def allocate_egress_ipv6_for_new_instance_impl(dbh,
                                               instance_id,
                                               edge_region_idx,
                                               ha_peer,
                                               is_passive):
    success = False
    try:
        my_instance_id = instance_id
        #egress_ip_list_loc = []

        has_egress_ip_list_loc = False
        egress_ipv6_list_loc = []
        egress_ipv4_list_loc = []

        ha_peer_id = None
        if is_passive:
            ha_peer_id = ha_peer

        my_inst = InstanceModel(dbh=dbh, iid=my_instance_id)
        if my_inst.get_param("id") in [None, 0]:
            err_msg = "Failed to get the instance ref for instance id %s" % str(my_instance_id)
            raise Exception(err_msg)

        acct_id = my_inst.get_param("acct_id")
        is_clean_ip_project = False
        is_clean_ip_project = oci_is_clean_ip_project(dbh, acct_id=acct_id)
        if is_clean_ip_project:
            dbh.logger.info(f"allocate_egress_ipv6_for_new_instance_impl: Tenant {acct_id} is a clean IP project")
        else:
            dbh.logger.info(f"allocate_egress_ipv6_for_new_instance_impl: Tenant {acct_id} is NOT a clean IP project")

        node_type = my_inst.get_param("node_type")
        compute_region_idx = my_inst.get_param("compute_region_idx")

        if is_passive:
            dbh.logger.info("Copy egress ipv6 subnet list from active instance %s to passive instance %s" %
                                                                      ( str(ha_peer_id), str(my_instance_id)))
            lret = copy_egress_ipv6_subnet_list_from_primary_instance(dbh,
                                                                      primary_instance_id=ha_peer_id,
                                                                      secondary_instance_id=my_instance_id)
            if lret == False:
                err_msg = ("Failed to copy IPv6 subnet list from instance %s to instance %s" %
                                                                                    (str(ha_peer_id),
                                                                                     str(my_instance_id)))
                raise Exception(err_msg)
            success =  True
            return

        # Let's allocate IPv6 address even if it is compute location:
        gcp_ipv6_mgmt = ipv6_mgmt_1.IPV6_Handler(dbh, acct_id=acct_id)
        success = gcp_ipv6_mgmt.upsert_instance_egress_ipv6_list_subnet_location(my_instance_id, compute_region_idx)
        if success == False:
            err_msg = ("Failed to upsert instance egress IPv6 list allocation for instance id %s and "
                       "compute_region_idx %s" % (str(my_instance_id), str(compute_region_idx)))
            raise Exception(err_msg)

        # pinned instance check is only valid for MU gateways.
        if node_type in [ NODE_TYPE_GP_GATEWAY, NODE_TYPE_NLB_INSTANCE ]:
            egress_ipv6_list_loc = my_inst.get_existing_egress_ipv6_region_list_for_pinned_instance(
                                                                                my_inst.get_param("custid"),
                                                                                my_inst.get_param("compute_region_idx"),
                                                                                NODE_TYPE_GP_GATEWAY)
            dbh.logger.info("The egress locations for the instance are %s" % str(egress_ipv6_list_loc))
            has_egress_ip_list_loc = True if (egress_ipv6_list_loc and len(egress_ipv6_list_loc)) else False

        # Now regardless of the node type we need to take care of the migration case where we look up edge location info
        # also

        egress_ipv4_list_loc = \
            my_inst.get_egress_ipv6_region_list_from_egress_ipv4_list(instance_id=str(my_inst.get_param("id")))

        egress_ip_list_loc = list(set(egress_ipv6_list_loc) | set(egress_ipv4_list_loc))

        has_edge_region_idx = False if str(my_inst.get_param("compute_region_idx")) == str(edge_region_idx) else True
        if has_edge_region_idx and str(edge_region_idx) not in egress_ip_list_loc:
            egress_ip_list_loc.append(str(edge_region_idx))

        for my_edge_region_idx in egress_ip_list_loc:
            gcp_ipv6_mgmt = ipv6_mgmt_1.IPV6_Handler(dbh, acct_id=acct_id)
            success = gcp_ipv6_mgmt.upsert_instance_egress_ipv6_list_subnet_location(my_instance_id, my_edge_region_idx)
            if success == False:
                err_msg = ("Failed to upsert instance egress IPv6 list allocation for instance id %s and "
                           "edge_region_idx %s" % (str(my_instance_id), str(my_edge_region_idx)))
                raise Exception(err_msg)

        # Set the success status as True.
        success = True

    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success

def allocate_egress_ipv6_for_new_instance(dbh,
                                          instance_entry_dict,
                                          node_type,
                                          is_passive=False):
    success = False
    try:
        dbh.logger.info("Allocating egress IPv6 address for node type %s" % str(node_type))
        if node_type in [ NODE_TYPE_GP_GATEWAY, NODE_TYPE_GP_PORTAL, NODE_TYPE_REMOTE_NET, NODE_TYPE_NLB_INSTANCE ]:
            # Handle GP gateway instance.
            success = allocate_egress_ipv6_for_new_instance_impl(dbh,
                                                                 instance_entry_dict['instance_id'],
                                                                 instance_entry_dict['edge_region_idx'],
                                                                 instance_entry_dict['ha_peer'],
                                                                 is_passive)
            return
        success = True
    except Exception as E:
        dbh.logger.info("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                 str(traceback.format_exc()),
                                                                                 str(locals())))
        success = False
    finally:
        return success

def get_oci_salt_profile_for_instance_entry(dbh, instance_entry_dict, is_passive, is_clean_pipe=0, node_type=None):
    salt_profile = {}
    dbh.logger.info("get_oci_salt_profile_for_instance_entry is_clean_pipe: %d, node_type: %s" % (is_clean_pipe, node_type))
    try:
        '''
        ### Sample format for salt profile:- ###

        "InstType",
        "UserData",
        "PrimaryId",
        "SerialNo",
        "AcctId",
        "CustId",
        "PrimaryIntfSet"
        '''

        event = {}
        event['dbh'] = dbh
        event['inst_id'] = instance_entry_dict['instance_id']
        event['region'] = instance_entry_dict['region']
        event['isPassive'] = is_passive
        event['cloudtype'] = PROVIDER_OCI
        event['gpcs_instance_size'] = instance_entry_dict['gpcs_instance_size']
        event['version'] = instance_entry_dict['version']
        event['instance_name'] = instance_entry_dict['instance_name']
        event['saas_gpcs_api_endpoint'] = instance_entry_dict['saas_gpcs_api_endpoint']
        event['cert_fetch_otp'] = instance_entry_dict['cert_fetch_otp']
        event['cloud_provider'] = instance_entry_dict['cloud_provider']
        event['super_custid'] = instance_entry_dict['super_custid']
        event['custnode_id'] = instance_entry_dict['custnode_id']
        event['parent_id'] = instance_entry_dict['parent_id']
        event['avail_domain'] = instance_entry_dict['avail_domain']
        event['sessionAffinity'] = instance_entry_dict['sessionAffinity']
        event['gp_gw_domain'] = instance_entry_dict['gp_gw_domain']
        event['commit_validate'] = instance_entry_dict['commit_validate']
        event['is_nlb_supported'] = \
            instance_entry_dict['is_nlb_supported']
        event['is_ngpa_protocol_enabled'] = \
            instance_entry_dict['is_ngpa_protocol_enabled']
        event['perform_ingress_ip_reduction'] = \
            instance_entry_dict['perform_ingress_ip_reduction']
        event['is_central_cache_supported'] = \
            instance_entry_dict['is_central_cache_supported']
        event['is_instance_behind_nlb'] = \
            instance_entry_dict['is_instance_behind_nlb']
        event['central_cache_service_endpoint'] = \
            instance_entry_dict['central_cache_service_endpoint']
        event['central_cache_service_backup_endpoint'] = \
            instance_entry_dict['central_cache_service_backup_endpoint']
        event['ciam_service_endpoint'] = \
            instance_entry_dict['ciam_service_endpoint']
        event['is_using_sp_interconnect'] = instance_entry_dict['is_using_sp_interconnect']

        if 'mgmt-interface-swap' in list(instance_entry_dict.keys()):
            event['mgmt-interface-swap'] = instance_entry_dict["mgmt-interface-swap"]
        if 'instance_role' in list(instance_entry_dict.keys()):
            event['instance_role'] = instance_entry_dict["instance_role"]
        if 'slot_nr' in list(instance_entry_dict.keys()):
            event['slot_nr'] = instance_entry_dict["slot_nr"]
        if 'inter_instance_psk' in list(instance_entry_dict.keys()):
            event['inter_instance_psk'] = instance_entry_dict["inter_instance_psk"]
        if 'colo_interface_support' in list(instance_entry_dict.keys()):
            event['colo_interface_support'] = instance_entry_dict["colo_interface_support"]
        if 'cloud_machine_type' in list(instance_entry_dict.keys()):
            event['cloud_machine_type'] = instance_entry_dict["cloud_machine_type"]
        if 'cpu_platform' in list(instance_entry_dict.keys()):
            event['cpu_platform'] = instance_entry_dict["cpu_platform"]
        if 'is_service_nic_supported' in list(instance_entry_dict.keys()):
            event['is_service_nic_supported'] = instance_entry_dict["is_service_nic_supported"]
        if 'is_datapath_on_shared_vpc' in list(instance_entry_dict.keys()):
            event['is_datapath_on_shared_vpc'] = instance_entry_dict["is_datapath_on_shared_vpc"]
        if 'frr-enabled' in list(instance_entry_dict.keys()):
            event['frr-enabled'] = instance_entry_dict['frr-enabled']
        if 'proxy-protocol-enabled' in list(instance_entry_dict.keys()):
            event['proxy-protocol-enabled'] = instance_entry_dict['proxy-protocol-enabled']
        if 'ep-geneve-enabled' in list(instance_entry_dict.keys()):
            event['ep-geneve-enabled'] = instance_entry_dict['ep-geneve-enabled']
        if 'capacity_type' in list(instance_entry_dict.keys()):
            event['capacity_type'] = instance_entry_dict['capacity_type']
        else:
            event['capacity_type'] = "default"

        if ('cloud_machine_type' in list(instance_entry_dict.keys())):
            event['inst_type'] = instance_entry_dict['cloud_machine_type']

        # Get the ID of node type since its easier to work with.
        node_type_id = instance_entry_dict["node_type_id"]
        compute_region_idx = instance_entry_dict.get('region', '')
        edge_region_idx = instance_entry_dict.get('edge_region_idx', '')
        dbh.logger.info(f"get_oci_salt_profile_for_instance_entry: Salt profile for compute region {compute_region_idx}, edge {edge_region_idx} for {node_type_id}")
        # This is required to provision NAT in provisioning service to write the NAT IP mappings back to egress_ip_mappings column per MU node
        # For OCI language localization support; we will provision a new NATGW per edge region per tenant with seperate entry in IM
        if node_type_id == NODE_TYPE_NAT_INSTANCE:
            if compute_region_idx == edge_region_idx:
                salt_profile["RegionID"] = int(compute_region_idx)
                salt_profile["ComputeRegionID"] = int(compute_region_idx)
            else:
                salt_profile["RegionID"] = int(edge_region_idx)
                salt_profile["ComputeRegionID"] = int(compute_region_idx)
        else:
            salt_profile["RegionID"] = int(compute_region_idx)
            salt_profile["ComputeRegionID"] = int(compute_region_idx)
        dbh.logger.info(f"get_oci_salt_profile_for_instance_entry: Setting region info in salt profile -> {salt_profile['RegionID']}, {salt_profile['ComputeRegionID']}")
        salt_profile["IsNLBSupported"] = instance_entry_dict['is_nlb_supported']

        # BYOIP support for OCI
        if is_clean_pipe:
            result = allocate_egress_ip_for_new_instance(dbh,
                                                        instance_entry_dict,
                                                        node_type_id, edge_region_idx=edge_region_idx)
            if result == False:
                raise customException.EgressIPAllocationException("Failed to allocate egress IP for instance node type %s" % str(node_type))

        else:
            if event.get('instance_role') in [INSTANCE_ROLE_MP_STRING]:
                dbh.logger.info("Not allocating any public IP for MP VM.")
            elif not is_passive:
                result = allocate_egress_ip_for_new_instance(dbh,
                                                             instance_entry_dict,
                                                             node_type_id, edge_region_idx=edge_region_idx)
                if result == False:
                    if instance_entry_dict['is_nlb_supported'] == 1 and node_type == NODE_TYPE_NAT_INSTANCE:
                        err_msg = "Failed to allocate egress IP for instance node type %s for NGPA stack" % str(node_type)
                        raise customException.NGPAReservedIPAllocationExceptionNAT(err_msg)
                    else:
                        err_msg = "Failed to allocate egress IP for instance node type %s" % str(node_type)
                        raise customException.EgressIPAllocationException(err_msg)

                # IPv6 is only for gateways, portals, remote networks for now.
                if instance_entry_dict.get("has_external_ipv6") == True:
                    dbh.logger.info("External IPv6 support is enabled for the instances %s" %
                                    str(instance_entry_dict['instance_name']))
                    result = allocate_egress_ipv6_for_new_instance(dbh, instance_entry_dict,
                                                                   node_type_id)
                else:
                    dbh.logger.info("External IPv6 support is not enabled for the instances %s" %
                                                        str(instance_entry_dict['instance_name']))
                    result = True

                if result == False:
                    err_msg = "Failed to allocate egress IPv6 for instance node type %s" % str(node_type)
                    raise customException.EgressIPv6AllocationException(err_msg)
            else:
                result = allocate_egress_ip_for_new_instance(dbh, instance_entry_dict,
                                                             node_type_id, is_passive, edge_region_idx=edge_region_idx)
                if result == False:
                    raise customException.EgressIPAllocationException("Failed to allocate egress IP for passive instance")

                if instance_entry_dict.get("has_external_ipv6") == True:
                    dbh.logger.info("External IPv6 support is enabled for the instances %s" %
                                    str(instance_entry_dict['instance_name']))
                    # TBD: Commenting out for now until we finalize IPv6 handling for BYOIP
                    #result = allocate_egress_ipv6_for_new_instance(dbh, instance_entry_dict,
                    #                                               node_type_id, is_passive)
                    result = True
                else:
                    dbh.logger.info("External IPv6 support is not enabled for the instances %s" %
                                                        str(instance_entry_dict['instance_name']))
                    result = True

        # Populate the rest of the needed stuff.
        # TODO: Add support for clean_pipe deployments later for OCI
        inst_prof = oci_instance_info_handler(event)

        if inst_prof == None:
            raise Exception("!!! Failed to generate the instance profile from event !!!")

        if dbh == None:
            raise Exception("No database handler to work with! cannot continue!")

        # General info:
        salt_profile["CustId"] = instance_entry_dict['cust_id']
        salt_profile["AcctId"] = instance_entry_dict['acct_id']
        salt_profile["SerialNo"] = instance_entry_dict['serial_no']
        instanceName = instance_entry_dict['instance_alias']
        salt_profile["InstanceName"] = instanceName
        salt_profile["InstanceId"] = instance_entry_dict['instance_id']
        salt_profile["is_clean_pipe"] = is_clean_pipe
        salt_profile['sase_fabric'] = instance_entry_dict['sase_fabric']
        salt_profile['commit_validate'] = instance_entry_dict['commit_validate']

        if node_type_id == NODE_TYPE_GP_GATEWAY:
            salt_profile["Zone"] = get_zone_by_balancing_usage(dbh, instance_entry_dict, inst_prof)
            salt_profile["ZonesList"] = inst_prof.get('zonesList', [])
        elif node_type_id == NODE_TYPE_NLB_INSTANCE:
            # For NLB instances, need ZonesList to track backend groups placement
            salt_profile["Zone"] = inst_prof['zone']
            salt_profile["ZonesList"] = inst_prof["zonesList"]
        else:
            salt_profile["Zone"] = inst_prof['zone']
        dbh.logger.info(f"get_oci_salt_profile_for_instance_entry: Instance {instance_entry_dict['instance_id']} -> Zone: {salt_profile['Zone']}")

        salt_profile["RegionName"] = get_cloud_native_location_name_from_region_id(
                                                    dbh,
                                                    instance_entry_dict['region'])

        # Get cloud specific user data.
        salt_profile["UserData"] = inst_prof['userdata']
        #salt_profile["ImageProject"] = inst_prof["image_project"]
        ami_ids = inst_prof['ami_ids']
        if 'SharedMgmtVPCProj' in list(inst_prof.keys()):
            salt_profile['SharedMgmtVPCProj'] = inst_prof['SharedMgmtVPCProj']
        salt_profile["svc_acct"] = inst_prof['svc_acct']

        if ('capacity_type' in list(instance_entry_dict.keys())):
            capacity_type = instance_entry_dict['capacity_type']
        else:
            capacity_type = "default"
        salt_profile["PrimaryCapacityType"] = capacity_type

        dpdk_qcount = instance_entry_dict.get('dpdk_qcount', 0)
        if dpdk_qcount > 0:
            salt_profile["PrimaryDpdkQcount"] = dpdk_qcount

        if ('cloud_machine_type' in list(instance_entry_dict.keys())):
            cloud_machine_type = instance_entry_dict['cloud_machine_type']
            dbh.logger.info(f"get_oci_salt_profile_for_instance_entry: cloud_machine_type {cloud_machine_type} from instance_entry_dict")
        else:
            cloud_machine_type = inst_prof['instType']
            dbh.logger.info(f"get_oci_salt_profile_for_instance_entry: cloud_machine_type {cloud_machine_type} from instance profile")

        cloud_machine_type = get_target_override_for_version(dbh,
                                                            "oci",
                                                            instance_entry_dict['cust_id'],
                                                            node_type_id,
                                                            cloud_machine_type,
                                                            instance_entry_dict['version'],
                                                            instance_entry_dict['region'])
        dbh.logger.info(f"get_oci_salt_profile_for_instance_entry: cloud_machine_type {cloud_machine_type} from get_target_override_for_version")
        if (is_passive == True):
            (cloud_machine_type, capacity_type) = get_target_passive_machine_type(dbh,
                                                                                  instance_entry_dict['instance_id'],
                                                                                  cloud_machine_type,
                                                                                  capacity_type)
            salt_profile["SecondaryCapacityType"] = capacity_type
            if dpdk_qcount > 0:
                salt_profile["SecondaryDpdkQcount"] = dpdk_qcount

        # Get the instance type to be spinned up.
        salt_profile["InstType"] = cloud_machine_type
        # Extract the OCPU and Memory details from the instance type from instance type for OCI MU gateways and SC nodes
        # for VM type VM.Standard.E4.Flex-8-32; we will pass 8 ocpus and 32 GB as salt profile info
        if node_type == "GPGATEWAY" or node_type_id == NODE_TYPE_SWG_PROXY or node_type_id  == NODE_TYPE_GP_GATEWAY or node_type_id == NODE_TYPE_SERVICE_CONN or node_type == "SERVICECONNECTION" or node_type == "PROBEVM" or node_type_id  == NODE_TYPE_PROBE_VM:
            vm_instance_type = salt_profile["InstType"]
            vm_spec = salt_profile["InstType"].split('-')
            vm_vcpu = 0
            vm_memory = 0
            vm_burst_baseline = "BASELINE_1_1"
            if vm_spec and len(vm_spec) >= 3:
                vm_vcpu = int(int(vm_spec[1])/2)
                vm_memory = int(vm_spec[2])
                if len(vm_spec) >= 4:
                    if vm_spec[3] == "b2":
                        vm_burst_baseline = "BASELINE_1_2"
                        # Effective CPU will have to be twiced since this operates at 50%
                        vm_vcpu = vm_vcpu * 2 
                    elif vm_spec[3] == "b8":
                        vm_burst_baseline = "BASELINE_1_8"
                        # Effective CPU will have to be twiced since this operates at 12.5%
                        vm_vcpu = vm_vcpu * 8 

            salt_profile['CPUCount'] = vm_vcpu
            salt_profile['MemGB'] = vm_memory
            salt_profile['BurstBaseline'] = vm_burst_baseline
            # we need to set instance type as VM.Standard.E4.Flex-8-32 for VM type VM.Standard.E4.Flex-8-32
            salt_profile["InstType"] = vm_instance_type
            dbh.logger.info(f"get_oci_salt_profile_for_instance_entry: Instance type -> {salt_profile['InstType']} CPU: {salt_profile['CPUCount']} Memory: {salt_profile['MemGB']} GB")

        if 'cpu_platform' in inst_prof.keys() and inst_prof['cpu_platform'] != '':
            salt_profile["MinCpuPlatform"] = inst_prof['cpu_platform']

        # set ami id based on arch type
        # REVISIT THIS ; should we determine arch_type from any RDS entry?
        # arch_type = get_arch_for_cloud_machine_type(dbh, salt_profile["InstType"])
        arch_type = 'x86-64'
        ami_id = ''
        try:
            if node_type_id == NODE_TYPE_NLB_INSTANCE or node_type == "NLB" or node_type_id == NODE_TYPE_NAT_INSTANCE or node_type == "NAT":
                dbh.logger.info("Skip setting  AMI ID for NLB/NAT instances")
                ami_id = ''
            elif ami_ids:
                ami_id = ami_ids.get(arch_type, '')
            salt_profile["ImageName"] = ami_id
        except Exception as E:
            dbh.logger.error(f"Missing AMI for version: {instance_entry_dict['version']}, "
                             f"cloud_machine_type: {cloud_machine_type} arch: {arch_type}")

        dbh.logger.info(f"get_oci_salt_profile_for_instance_entry: Instance {instance_entry_dict['instance_id']} Image: {salt_profile.get('ImageName', '')}")
        salt_profile["arch-type"] = arch_type

        #IPv6 support check for dp interface
        if "has_external_ipv6" in instance_entry_dict.keys():
            salt_profile["hasExternalIPv6"] = True

        salt_profile["clean_ip_tag"] = inst_prof['clean_ip_tag']
        salt_profile["static_ip"] = inst_prof["static_ip"]
        dbh.logger.info(f"get_oci_salt_profile_for_instance_entry: Instance -> {instance_entry_dict['instance_id']} Static IP in salt_profile -> {salt_profile['static_ip']}")
        # Adding check to ensure static_ip is valid for NLB node type
        # we added this check to simplify the TF NLB IP processing for IIR disabled tenants for edge location handling
        #if node_type_id == NODE_TYPE_NLB_INSTANCE or node_type == "NLB":
        #    if is_valid_ip(salt_profile["static_ip"]):
        #        dbh.logger.info(f"get_oci_salt_profile_for_instance_entry: Instance {instance_entry_dict['instance_id']} has IP reserved for NLB node type {node_type_id}: {salt_profile['static_ip']}")
        #    else:
        #        raise Exception(f"get_oci_salt_profile_for_instance_entry: Instance {instance_entry_dict['instance_id']} has no IP reserved for NLB node type {node_type_id}. Failing salt_profile creation")

        # For non prod environments; based on the machine type we need to disable bringing up the
        # the shared DP VNIC for cost savings. For prod environments; we will always enable the shared VNIC
        if cloud_instance_has_nic_limit(dbh.logger, cloud_machine_type):
            dbh.logger.info(f"get_oci_salt_profile_for_instance_entry: Instance {instance_entry_dict['instance_id']} has NIC limit, setting is_service_nic_supported to False for cloud machine type {cloud_machine_type}")
            salt_profile['is_service_nic_supported'] = False
        elif event.get('is_service_nic_supported', False):
            salt_profile['is_service_nic_supported'] = event['is_service_nic_supported']
            salt_profile['serviceSubnet'] = inst_prof['serviceSubnet']
            salt_profile['serviceInterfaceName'] = inst_prof['serviceInterfaceName']
            salt_profile['serviceNetwork'] = inst_prof['serviceNetwork']
            salt_profile['serviceHasExternalIP'] = inst_prof['serviceHasExternalIP']
            salt_profile['serviceProjectOverride'] = inst_prof['serviceProjectOverride']

        dbh.logger.info(f"get_oci_salt_profile_for_instance_entry: Instance {instance_entry_dict['instance_id']} Service NIC supported -> {salt_profile.get('is_service_nic_supported', False)}")
        if (event.get('is_datapath_on_shared_vpc', None) != None) and event.get('is_datapath_on_shared_vpc') == True:
            salt_profile['is_datapath_on_shared_vpc'] = event['is_datapath_on_shared_vpc']
            salt_profile['DPProjectOverride'] = inst_prof['DPProjectOverride']

        salt_profile['frr-enabled'] = event.get('frr-enabled', 0)
        salt_profile['proxy-protocol-enabled'] = event.get('proxy-protocol-enabled', 0)

        salt_profile["is_using_sp_interconnect"] = inst_prof['is_using_sp_interconnect']
        salt_profile["PrimaryIntfSet"] = 0
        salt_profile['perform_ingress_ip_reduction'] = int(event.get('perform_ingress_ip_reduction', False))

        # Dump the dict
        dbh.logger.info("Salt profile dump for OCI instance is %s\n%s" %
                        (str(salt_profile['InstanceId']), str(salt_profile)))

    except customException.EgressIPAllocationException as E:
        error_message = str(E.args)
        dbh.logger.error(f"EgressIPAllocationException: {error_message}")
        publish_avisar_event_with_tenant_region_nodetype_key(dbh.avctx, dbh.logger,
                                                             ORCHESTRATION_INSTANCE_PUBLIC_IP_FETCH_FAILED,
                                                             error_message, METRIC_SEVERITY_CRITICAL)
        raise  # Re-raise the exception to be caught by the general Exception block
    except customException.NGPAReservedIPAllocationExceptionNAT as E:
        error_message = str(E.args)
        dbh.logger.error(f"NGPAReservedIPAllocationExceptionNAT: {error_message}")
        publish_avisar_event_with_tenant_region_nodetype_key(dbh.avctx, dbh.logger,
                                                             ORCHESTRATION_NGPA_OCI_NAT_BRINGUP_FAILED_IP_RESERVATION_FAILURE,
                                                             error_message, METRIC_SEVERITY_CRITICAL)
        salt_profile = None
        raise  # Re-raise the exception to be caught by the general Exception block
    except customException.EgressIPv6AllocationException as E:
        error_message = str(E.args)
        dbh.logger.error(f"EgressIPv6AllocationException: {error_message}")
        publish_avisar_event_with_tenant_region_nodetype_key(dbh.avctx, dbh.logger,
                                                             ORCHESTRATION_INSTANCE_PUBLIC_IPV6_FETCH_FAILED,
                                                             error_message, METRIC_SEVERITY_CRITICAL)
    except Exception as E:
        dbh.logger.error("get_oci_salt_profile_for_instance_entry: Exception %s occured. Traceback: %s locals: %s" %
                         (str(E.args), str(traceback.format_exc()), str(locals())))
        # Override salt_profile with None
        salt_profile = None

    finally:
        return salt_profile

def is_valid_ip(ip_address):
    try:
        ipaddress.ip_address(ip_address)
        return True
    except ValueError:
        return False

def is_inbound_access_list_none(inbound_access_list, logger):
    ret = False
    if (inbound_access_list is None):
        ret = True
    elif (str(inbound_access_list).lower() in ('none', 'null', '')):
        ret = True
    else:
        try:
            if (inbound_access_list.decode().lower() in
                ('none', 'null', '')):
                ret = True
        except Exception as E:
            logger.info("Exception when trying to decode [%s]: %s" %
                        (inbound_access_list, str(E.args)))

    logger.info("Returning %s from is_inbound_access_list_none for %s" %
                (ret, inbound_access_list))
    return ret

# Function to check if an NLB needs to be present/spun up for the provided
# (customer, region) combination.
# Check if there is any GW instance in this region with the
# "is_instance_behind_nlb" flag set to TRUE. If so, return TRUE
# since this (customer, region) needs to have the NLB present
# (either brought up newly, or keep the existing NLB as-is).
# Else, return FALSE.
def is_nlb_bring_up(dbh, custid, region_id):
    dbh.logger.info("Checking if NLB bring up is needed for tenant: %s "
                    "and region: %s" % (custid, region_id))

    ret = False
    try:
        # Get the "is_instance_behind_nlb" flag for all GW instances for the
        # given (customer, region) combination.
        # Check if any of the GW instances has the "is_behind_nlb" flag set.
        # If so, this is the case where an NLB has already been spun up or
        # a new NLB needs to be spun up in the region. Return TRUE.
        # Else, this is the first instance in this region for this customer.
        # No need to spin up an NLB yet. Return FALSE.
        sql = ("SELECT COUNT(*) FROM instance_master "
               "WHERE custid = %s AND compute_region_idx = %s AND "
               "node_type = %s AND is_instance_behind_nlb = 1")
        params = (custid, region_id, NODE_TYPE_GP_GATEWAY)
        dbh.conn.close()
        try:
            cursor = dbh.get_cursor()
            dbh.logger.info("SQL: %s; params: %s" % (sql, str(params)))
            cursor.execute(sql, params)
            db_ret = cursor.fetchall()
            dbh.cursorclose(cursor)

            dbh.logger.info("Return from RDS: %s" % str(db_ret))
            no_of_instances = db_ret[0][0]
            dbh.logger.info("No. of instances: %s" % no_of_instances)
            if no_of_instances > 0:
                dbh.logger.info("There are GW instances with "
                                "is_instance_behind_nlb set; return TRUE")
                ret = True
            else:
                dbh.logger.info("No GW instances with "
                                "is_instance_behind_nlb set; return FALSE")

        except Exception as E:
            dbh.logger.info("Failed to get instances from RDS; "
                            "Exception %s occured. locals: %s" %
                             (str(E.args), str(locals())))
            return ret

    except Exception as E:
        dbh.logger.error("is_nlb_bring_up: Exception %s occured. locals: %s" %
                             (str(E.args), str(locals())))

    finally:
        return ret

def get_num_of_ips_by_instance_type(dbh, my_inst, ingress_ip_reduction, is_using_sp_interconnect=0):
    try:
        # E.g. Lets consider we have 2 gateways. The # of reserved IP addresses are 2 and # of active IP addresses are 2.
        # When two NAT instances are coming, the # of reserved IP addresses are 2 and # of active IP addresses become 4
        # When NLB instance is coming, the # of reserved IP addresses are 2 and # of active IP addresses become 5
        # In total, we have 7 IP in this edge region, 5 active (2 for standalone GW, 2 for NAT, and 1 for NLB),
        # and 2 reserved for future usage
        if my_inst.get_param("node_type") == NODE_TYPE_NLB_INSTANCE:
            num_of_ips = 1
            return num_of_ips

        if my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE:
            result, instances = find_instances_by_custid_and_region_id(dbh, my_inst.get_param("custid"),
                                                                       my_inst.get_param("compute_region_idx"),
                                                                       NODE_TYPE_NAT_INSTANCE)
            if not result:
                err_msg = ("Failed to get the NAT gateways for custid %s and region %s" % (
                str(my_inst.get_param("custid")),
                str(my_inst.get_param(
                    "compute_region_idx"))))
                dbh.logger.error(err_msg)
                raise Exception(err_msg)
            if len(instances) < 1:
                dbh.logger.info(
                    "There are no NAT instances for custid: %s in region name: %s" % (str(my_inst.get_param("custid")),
                                                                                      str(my_inst.get_param(
                                                                                          "compute_region_name"))))

            result, nr_instances, nr_nat_gateways_needed = find_nr_nat_instances_by_custid_and_region_id(
                dbh, my_inst.get_param("custid"), my_inst.get_param("compute_region_idx"))

            standalone_result, standalone_instances = find_standalone_instances_by_custid_and_node_type_and_region_id(dbh, 
                                                                                                                      my_inst.get_param("custid"), 
                                                                                                                      NODE_TYPE_GP_GATEWAY,
                                                                                                                      my_inst.get_param("compute_region_idx"))
            # For SP-Interconnect we use PUPI IP to GW and NAT instances. So we need to account the ip assigned
            # to GW node as well while reserving ips for compute location
            # For edge_location, is_using_sp_interconnect is set to 0 as we need not account gw
            num_gw_instances = 0
            if is_using_sp_interconnect:
                gw_result, gw_instances = find_instances_by_custid_and_node_type_and_region_id(dbh,
                                                                                               my_inst.get_param("custid"),
                                                                                               NODE_TYPE_GP_GATEWAY,
                                                                                               my_inst.get_param("compute_region_idx"))
                if gw_result:
                    num_gw_instances = len(gw_instances)

            num_standalone = 1
            if standalone_result:
                num_standalone = len(standalone_instances)
            else:
                num_standalone = nr_instances

            is_nat_dummy_upgrade_ongoing = is_nat_dummy_upgrade_ongoing_in_region(dbh,my_inst.get_param("custid"),
                                                                                      my_inst.get_param("compute_region_idx"),
                                                                                      for_ip_count=True)
            #Reserve additional +1 IP for scale
            if is_nat_dummy_upgrade_ongoing:
                #When NAT dummy upgrade is ongoing and there are more than 1 standalone,
                #then this is a brownfield multi GW migration case,
                #we do not need to reserve any extra IPs, as the NATs will eventually re-use freed up IPs from the GW
                num_of_ips = len(instances) + num_standalone + num_gw_instances
            else:
                num_of_ips = len(instances) + num_standalone + num_gw_instances + 1

            return num_of_ips

    except Exception as E:
        dbh.logger.error("get_num_of_ips_by_instance_type: Exception %s "
                         "occured. locals: %s" %
                         (str(E.args), str(locals())))
        if my_inst.get_param("node_type") == NODE_TYPE_NAT_INSTANCE:
            num_of_ips = DEFAULT_NR_NAT_GATEWAYS + 1
        else:
            num_of_ips = 0
        return num_of_ips

import time
import logging, threading
import logging.handlers

'''
class dbh:
    def __init__(self, logger = None):
        self.logger = logger

class gcp_cp_instance_info_handler:
    def __init__(self, event = None):
        self.inst_prof = {}
        self.inst_prof = {'instType': '2', 'image_project': 'sample-proj', 'is_clean_pipe': 1, 'AcctId': '245150', 'userdata': 'instance_name=rmusunuri--us-west1-12345,custid=245150,lambdaprefix=pmangam-245150,custid_int=24,gp_domain=pancloud.dev,route53acct=a447084568087,bucket-name=panrepo-us-west-2-dev2,panrepo_bucket_name=panrepo-us-west-2-dev2,aws-access-key-id=None,aws-secret-access-key=None, mgmt-interface-swap=enable', 'TrustNetwork': 'gpcs-cp-vpc-trust-245150', 'zone': 'us-west1-a', 'TrustSubnet': 'subnet-trust-us-west1-245150', 'TrustInterfaceName': 'nic-trust', 'UnTrustHasExternalIP': True, 'MgmtHasExternalIP': False, 'PrimaryId': None, 'SerialNo': 'Dummy_serial_no', 'UnTrustNetwork': 'gpcs-cp-vpc-untrust-245150', 'image_name': 'panos-8.1', 'TrustHasExternalIP': False, 'UnTrustInterfaceName': 'nic-untrust', 'svc_acct': 'sample-account', 'UnTrustSubnet': 'subnet-untrust-us-west1-245150', 'MgmtNetwork': 'gpcs-cp-vpc-mgmt-245150', 'MgmtSubnet': 'subnet-mgmt-us-west1-245150', 'MgmtInterfaceName': 'nic-mgmt'}
    def get_prof(self):
        return self.inst_prof
'''


def main():
    from libs.db.dbhandle import DbHandle
    filename = "oci_instance_param_mgmt.log"
    FORMAT = "[%(filename)s:%(lineno)s - %(funcName)20s() ] %(message)s"
    logging.basicConfig(format=FORMAT)
    handler = logging.handlers.RotatingFileHandler(filename, maxBytes=********, backupCount=1)
    logger = logging.getLogger(threading.currentThread().getName())
    logger.setLevel(logging.DEBUG)
    logger.addHandler(handler)
    db_h = DbHandle(logger)

    gcp_ip_mgmt = ipv4_mgmt.GCPIPHandler(db_h, str(3096), str(245139))
    ip_list = gcp_ip_mgmt.allocate_public_ip_for_customer(None,
                                                          3096,
                                                          False)

    '''
    instance_entry_dict = {}
    instance_entry_dict['cust_id'] = 24
    instance_entry_dict['acct_id'] = 245150
    instance_entry_dict['serial_no'] = 123456789
    instance_entry_dict['instance_name'] ='rmusunuri_us-west1_12345'
    instance_entry_dict['instance_id'] = 1000
    instance_entry_dict['region'] = 'us-west1'
    instance_entry_dict['region_name'] = 'us-west1'
    instance_entry_dict['gpcs_instance_size'] = 10
    instance_entry_dict['is_clean_pipe'] = 1
    instance_entry_dict['version'] = 10
    get_oci_salt_profile_for_instance_entry(db_h, instance_entry_dict, 0, 1)
    '''


#if __name__ == '__main__':
#    main()
