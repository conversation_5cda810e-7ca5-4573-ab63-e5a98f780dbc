import mysql.connector
MAX_RETRY=2

def execute_orch_query(dbh, sql, params, opertype):
    ret=False
    res=None
    cursor=None
    retry = MAX_RETRY
    try:
        while retry > 0:
            try:
                cursor = dbh.get_cursor()
                if opertype == "insert" or opertype == "update":
                    cursor.execute(sql, params)
                    dbh.conn.commit()
                    res = cursor.lastrowid
                elif opertype == "fetchone":
                    cursor.execute(sql, params)
                    res = cursor.fetchone()
                elif opertype == "fetchall":
                    cursor.execute(sql, params)
                    res = cursor.fetchall()
                elif opertype == "delete":
                    cursor.execute(sql, params)
                    dbh.conn.commit()
                retry=0
                ret = True
            except mysql.connector.Error as err:
                # Handle the exception if the MySQL server is running with the --read-only option
                if err.errno in [1290, 1836]:
                    dbh.logger.error("The MySQL server is running with the --read-only option "
                                     "so it cannot execute this statement, Error no: %s, SQLSTATE: %s" % (str(err.errno),
                                                                                                          str(err.sqlstate)))
                    # Do a reconnect and execute the query again:
                    if cursor:
                        dbh.cursorclose(cursor)
                    dbh.logger.error("Reconnecting to the Database again.")
                    dbh.conn = dbh.connect()
                    if dbh.conn == None:
                        dbh.logger.error("Failed to connect to the database")
                    retry-=1
                else:
                    dbh.logger.error("No handler for exception %s" % str(err))
                    retry=0
    except Exception as E:
        ret = False
        dbh.logger.error("execute_orch_query: Exception occured %s %s %s" % (str(E.args), sql, params))
    finally:
        dbh.cursorclose(cursor)
        return ret, res
