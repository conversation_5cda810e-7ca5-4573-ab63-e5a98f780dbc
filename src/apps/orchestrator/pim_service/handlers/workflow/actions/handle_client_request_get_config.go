package actions

import (
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"orchestrator/libs/go/dbaccess/models/instance_master"

	"orchestrator/libs/go/terraform"
	"orchestrator/pim_service/handlers/models"
	"orchestrator/pim_service/infra/db"
	"strconv"
	"strings"

	"go.panw.local/pangolin/clogger"
)

const GW_NODE_TYPE = 49
const LOCAL_INSPECTION_NODE_TYPE = 48

// Constants for LBConfig
const (
	LB_FWMARK                      = 1
	LB_HC_INTERVAL                 = 5
	LB_ALGORITHM                   = "sh"
	LB_MODE                        = "NAT"
	LB_TARGET_WEIGHT               = 1
	LB_SESSION_PERSISTENCE_TIMEOUT = 3600
	LB_KEEPALIVED_HC_SCRIPT        = "/etc/keepalived/check_ports_v1.sh"
)

// Constants for HAConfig
const (
	HA_SRC_INTERFACE = "eth0"
	HA_MASTER_ROLE   = "MASTER"
	HA_BACKUP_ROLE   = "BACKUP"
	// HA backup and master VR needs to be the same!
	HA_MASTER_VRID     = 100
	HA_BACKUP_VRID     = 100
	HA_MASTER_PRIORITY = 200
	HA_BACKUP_PRIORITY = 100
)

// NetworkConfig represents the input network configuration
type NetworkConfigPvtRegionLB struct {
	MgmtAllocType      string `json:"mgmt.alloc_type"`
	MgmtIPAddress      string `json:"mgmt.ip_address"`
	MgmtDefaultGW      string `json:"mgmt.gateway"`
	MgmtDNSServer1     string `json:"mgmt.dns.1"`
	MgmtDNSServer2     string `json:"mgmt.dns.2"`
	DataplaneIPAddress string `json:"dataplane_outside.ip_address"`
	DataplaneDefaultGW string `json:"dataplane_outside.gateway"`
	PrivateVirtualIP   string `json:"dataplane_outside.private.vip_address"`
	PublicVirtualIP    string `json:"dataplane_outside.public.vip_address"`
}

type NetworkConfigPvtRegionAgent struct {
	MgmtAllocType  string `json:"mgmt.alloc_type"`
	MgmtIPAddress  string `json:"mgmt.ip_address"`
	MgmtDefaultGW  string `json:"mgmt.gateway"`
	MgmtDNSServer1 string `json:"mgmt.dns.1"`
	MgmtDNSServer2 string `json:"mgmt.dns.2"`
}

type HAConfig struct {
	NodeRole        string
	SrcInterface    string
	VirtualRouterID int
	Priority        int
	Password        string
}

type LBConfig struct {
	Fwmark                     int
	HCInterval                 int
	Algorithm                  string
	Mode                       string
	TargetWeight               int
	SessionPersistenceTimeout  int
	KeepalivedHCScriptLocation string
}

// generateHAPassword creates a unique password based on the cluster ID
func generateHAPassword(clusterID int64) string {
	// Create a hash of the cluster ID to generate a unique password
	data := fmt.Sprintf("ha-password-%d", clusterID)
	hash := sha256.Sum256([]byte(data))
	// Use base64 encoding to make it more readable and limit to 16 characters
	return base64.StdEncoding.EncodeToString(hash[:])[:16]
}

// determineHARole determines if this instance is a MASTER or BACKUP
func determineHARole(instanceID int64, clusterID int64, haPeer int64) string {
	if instanceID == clusterID && instanceID < haPeer {
		return HA_MASTER_ROLE
	}
	return HA_BACKUP_ROLE
}

// getHAVirtualRouterID returns the appropriate virtual router ID based on role
func getHAVirtualRouterID(role string) int {
	if role == HA_MASTER_ROLE {
		return HA_MASTER_VRID
	}
	return HA_BACKUP_VRID
}

// getHAPriority returns the appropriate priority based on role
func getHAPriority(role string) int {
	if role == HA_MASTER_ROLE {
		return HA_MASTER_PRIORITY
	}
	return HA_BACKUP_PRIORITY
}

// Below is called for Mobile users gateway case:
func createConfigJSONPvtRegionLB(eLog *clogger.EventLogger, netConfig NetworkConfigPvtRegionLB, peerDataplaneIP string, peerManagementIP string,
	targets []string, haConfig HAConfig) (string, error) {

	// Create LBConfig with the defined constants
	lbConfig := LBConfig{
		Fwmark:                     LB_FWMARK,
		HCInterval:                 LB_HC_INTERVAL,
		Algorithm:                  LB_ALGORITHM,
		Mode:                       LB_MODE,
		TargetWeight:               LB_TARGET_WEIGHT,
		SessionPersistenceTimeout:  LB_SESSION_PERSISTENCE_TIMEOUT,
		KeepalivedHCScriptLocation: LB_KEEPALIVED_HC_SCRIPT,
	}

	config := map[string]interface{}{
		"self.dataplane.ip-address":        netConfig.DataplaneIPAddress,
		"self.dataplane.default-gateway":   netConfig.DataplaneDefaultGW,
		"private.virtual-ip":               netConfig.PrivateVirtualIP,
		"peer.dataplane.ip-address":        peerDataplaneIP,
		"peer.mgmt.ip-address":             peerManagementIP,
		"public.virtual-ip":                netConfig.PublicVirtualIP,
		"targets":                          targets,
		"lb.fwmark":                        lbConfig.Fwmark,
		"lb.hc_interval":                   lbConfig.HCInterval,
		"lb.algorithm":                     lbConfig.Algorithm,
		"lb.mode":                          lbConfig.Mode,
		"lb.target.weight":                 lbConfig.TargetWeight,
		"lb.session_persistence_timeout":   lbConfig.SessionPersistenceTimeout,
		"lb.keepalived_hc_script_location": lbConfig.KeepalivedHCScriptLocation,
		"ha.node_role":                     haConfig.NodeRole,
		"ha.src_interface":                 haConfig.SrcInterface,
		"ha.virtual_router_id":             haConfig.VirtualRouterID,
		"ha.priority":                      haConfig.Priority,
		"ha.password":                      haConfig.Password,
	}

	jsonData, err := json.MarshalIndent(config, "", "    ")
	if err != nil {
		return "", fmt.Errorf("error marshaling JSON: %v", err)
	}
	eLog.LogInfo("cfg payload to send to client %s", string(jsonData))
	return string(jsonData), nil
}

// Below is called for Local inspection link load balancer case:
func createConfigJSONPvtRegionLLB(eLog *clogger.EventLogger, netConfig NetworkConfigPvtRegionLB, peerDataplaneIP string, peerManagementIP string,
	targets []string, haConfig HAConfig) (string, error) {

	config := map[string]interface{}{
		"self.dataplane.ip-address":      netConfig.DataplaneIPAddress,
		"self.dataplane.default-gateway": netConfig.DataplaneDefaultGW,
		"private.virtual-ip":             netConfig.PrivateVirtualIP,
		"peer.dataplane.ip-address":      peerDataplaneIP,
		"peer.mgmt.ip-address":           peerManagementIP,
		"targets":                        targets,
		"ha.node_role":                   haConfig.NodeRole,
		"ha.src_interface":               haConfig.SrcInterface,
		"ha.virtual_router_id":           haConfig.VirtualRouterID,
		"ha.priority":                    haConfig.Priority,
		"ha.password":                    haConfig.Password,
	}

	jsonData, err := json.MarshalIndent(config, "", "    ")
	if err != nil {
		return "", fmt.Errorf("error marshaling JSON: %v", err)
	}
	eLog.LogInfo("cfg payload to send to client %s", string(jsonData))
	return string(jsonData), nil
}

func createConfigJSONPvtRegionAgent(eLog *clogger.EventLogger, netConfig NetworkConfigPvtRegionAgent) (string, error) {

	config := map[string]interface{}{
		"self.management.alloc-type":      netConfig.MgmtAllocType,
		"self.management.ip-address":      netConfig.MgmtIPAddress,
		"self.management.default-gateway": netConfig.MgmtDefaultGW,
		"self.management.dns-1":           netConfig.MgmtDNSServer1,
		"self.management.dns-2":           netConfig.MgmtDNSServer2,
	}

	jsonData, err := json.MarshalIndent(config, "", "    ")
	if err != nil {
		return "", fmt.Errorf("error marshaling JSON: %v", err)
	}
	eLog.LogInfo("cfg payload to send to client %s", string(jsonData))
	return string(jsonData), nil
}

func extractPrivateIPs(target_map map[string]string) []string {
	var privateIPs []string
	for _, value := range target_map {
		parts := strings.Split(value, ",")
		if len(parts) >= 2 {
			ip := strings.TrimSpace(parts[1])
			if ip != "" {
				privateIPs = append(privateIPs, ip)
			}
		}
	}
	return privateIPs
}

func getRunningMode(payload string) (string, bool) {
	for _, pair := range strings.Split(payload, ",") {
		kv := strings.SplitN(pair, "=", 2)
		if len(kv) == 2 {
			key := strings.TrimSpace(kv[0])
			value := strings.TrimSpace(kv[1])
			if key == "running_mode" {
				return value, true
			}
		}
	}
	return "", false
}

func HandleGetConfigResponsePvtRegionLB(eLog *clogger.EventLogger, msg models.Message, instance *instance_master.Row) (string, []byte, error) {
	// Initialize the database accessor and event logger
	dbAcc := db.GetDbAccessor()
	running_mode_spr_llb := false

	// Create HAConfig based on instance details
	nodeRole := determineHARole(int64(instance.ID), int64(instance.ClusterID), int64(instance.HAPeer))
	haConfig := HAConfig{
		NodeRole:        nodeRole,
		SrcInterface:    HA_SRC_INTERFACE,
		VirtualRouterID: getHAVirtualRouterID(nodeRole),
		Priority:        getHAPriority(nodeRole),
		Password:        generateHAPassword(int64(instance.ClusterID)),
	}

	// Parse the Salt_profile to extract InterfaceConfig
	var saltProfile map[string]interface{}
	if err := json.Unmarshal([]byte(instance.SaltProfile), &saltProfile); err != nil {
		err_log := fmt.Sprintf("Error parsing Salt_profile: %v", err)
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Error parsing salt profile for the instance",
			err_log,
			nil)
		return "success", ret_payload, nil
	}

	// TODO: Salt profile - check if the salt profile has lb mode.
	// If lb mode is "ILB", we will need to set the targets but nothing related to Direct routing or NAT routing!

	userData, ok := saltProfile["UserData"]
	if !ok {
		err_log := fmt.Sprintf("Error parsing UserData from salt_profile")
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Error parsing UserData from salt_profile",
			err_log,
			nil)
		return "success", ret_payload, nil
	}

	running_mode, ok := getRunningMode(userData.(string))
	if !ok {
		err_log := fmt.Sprintf("Error parsing running_mode from salt_profile")
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Error parsing running_mode from salt_profile",
			err_log,
			nil)
		return "success", ret_payload, nil
	}

	if running_mode == "SPR-LLB" {
		running_mode_spr_llb = true
		eLog.LogInfo("Found the running mode as SPR-LLB.")
	}

	networkConfig, ok := saltProfile["NetworkConfig"]
	// once we have the network config, we need to parse it

	eLog.LogInfo("Network config from salt profile is %s", networkConfig)
	if !ok {
		// Log the entire Salt_profile for debugging
		saltProfileJSON, err := json.MarshalIndent(saltProfile, "", "  ")
		if err != nil {
			err_log := fmt.Sprintf("Error marshalling Salt_profile for logging: %v", err)
			eLog.LogError(err_log)
			ret_payload, _ := models.CreateResponseJSON(
				"Error parsing salt profile for the instance",
				err_log,
				nil)
			return "success", ret_payload, nil
		} else {
			err_log := fmt.Sprintf("networkConfig not found in Salt_profile. Full Salt_profile: %s",
				string(saltProfileJSON))
			eLog.LogError(err_log)
			ret_payload, _ := models.CreateResponseJSON(
				"Error parsing salt profile for the instance",
				"InterfaceConfig not found in Salt_profile",
				nil)
			return "success", ret_payload, nil
		}
	}

	// extract the required info now.
	jsonData, err := json.Marshal(networkConfig)
	if err != nil {
		// Handle error
		err_log := fmt.Sprintf("Failed to marshal networkConfig_peer %s", networkConfig)
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Error parsing salt profile for the instance",
			"NetworkConfig marshalling failed",
			nil)
		return "success", ret_payload, nil
	}

	var networkConfig_marshalled NetworkConfigPvtRegionLB
	err = json.Unmarshal(jsonData, &networkConfig_marshalled)
	if err != nil {
		// Handle error
		err_log := fmt.Sprintf("networkConfig unmarshalling failed: %s",
			string(jsonData))
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Error - networkConfig unmarshalling failed",
			"networkConfig unmarshalling failed for current instance",
			nil)
		return "success", ret_payload, nil
	}

	eLog.LogInfo("Network config for instance %d is %s", instance.ID, networkConfig_marshalled)

	// In this step lets collect the information from the peer instance about its IP address as well.
	peer_instanceID := instance.HAPeer

	// Now lookup the instance from instance master table:
	// Retrieve the instance from the database
	peer_instance, err := dbAcc.GetInstMasterById(int64(peer_instanceID), eLog)
	if err != nil {
		err_log := fmt.Sprintf("Error retrieving peer instance: %v with instance ID %v", err, peer_instanceID)
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Failed to get peer instance from instance master table",
			err_log,
			nil)
		return "success", ret_payload, nil
	}

	mgmt_ip_peer := peer_instance.ManagementIP

	// Parse the Salt_profile to extract InterfaceConfig
	var saltProfile_peer map[string]interface{}
	if err := json.Unmarshal([]byte(peer_instance.SaltProfile), &saltProfile_peer); err != nil {
		err_log := fmt.Sprintf("Error parsing Salt_profile: %v", err)
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Error parsing salt profile for the instance",
			err_log,
			nil)
		return "success", ret_payload, nil
	}

	networkConfig_peer, ok := saltProfile_peer["NetworkConfig"]
	// once we have the network config, we need to parse it

	if !ok {
		// Log the entire Salt_profile for debugging
		saltProfileJSON_peer, err := json.MarshalIndent(saltProfile_peer, "", "  ")
		if err != nil {
			err_log := fmt.Sprintf("Error marshalling Salt_profile for logging: %v", err)
			eLog.LogError(err_log)
			ret_payload, _ := models.CreateResponseJSON(
				"Error parsing salt profile for the instance",
				err_log,
				nil)
			return "success", ret_payload, nil
		} else {
			err_log := fmt.Sprintf("networkConfig not found in Salt_profile. Full Salt_profile: %s",
				string(saltProfileJSON_peer))
			eLog.LogError(err_log)
			ret_payload, _ := models.CreateResponseJSON(
				"Error parsing salt profile for the peer instance",
				"NetworkConfig not found in Salt_profile",
				nil)
			return "success", ret_payload, nil
		}
	}

	// extract the required info now.
	jsonData_peer, err := json.Marshal(networkConfig_peer)
	if err != nil {
		// Handle error
		err_log := fmt.Sprintf("Failed to marshal networkConfig_peer %s", networkConfig_peer)
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Error parsing salt profile for the instance",
			"NetworkConfig marshalling failed",
			nil)
		return "success", ret_payload, nil
	}

	var networkConfig_peer_marshalled NetworkConfigPvtRegionLB
	err = json.Unmarshal(jsonData_peer, &networkConfig_peer_marshalled)
	if err != nil {
		// Handle error
		err_log := fmt.Sprintf("networkConfig unmarshalling failed: %s",
			string(jsonData_peer))
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Error - networkConfig unmarshalling failed",
			"networkConfig unmarshalling failed for current instance",
			nil)
		return "success", ret_payload, nil
	}

	// Now lets get the targets for the config:
	target_map := map[string]string{}
	if running_mode == "SPR-LLB" {
		target_map, err = dbAcc.GetPrivateIpByNodeTypeAndRegion(eLog, int64(LOCAL_INSPECTION_NODE_TYPE),
			int64(instance.ComputeRegionIdx), int64(instance.CustID))
	} else {
		target_map, err = dbAcc.GetPrivateIpByNodeTypeAndRegion(eLog, int64(GW_NODE_TYPE),
			int64(instance.ComputeRegionIdx), int64(instance.CustID))
	}
	eLog.LogInfo("Target map is %v\n", target_map)
	if err != nil {
		err_log := fmt.Sprintf("Error getting targets: %v", err)
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Error getting targets for the config",
			err_log,
			nil)
		return "success", ret_payload, nil
	}

	targets := extractPrivateIPs(target_map)
	var finalConfigPayload string

	if running_mode_spr_llb {
		finalConfigPayload, err = createConfigJSONPvtRegionLLB(eLog, networkConfig_marshalled,
			networkConfig_peer_marshalled.DataplaneIPAddress,
			(string(mgmt_ip_peer)),
			targets,
			haConfig)
	} else {
		finalConfigPayload, err = createConfigJSONPvtRegionLB(eLog, networkConfig_marshalled,
			networkConfig_peer_marshalled.DataplaneIPAddress,
			(string(mgmt_ip_peer)),
			targets,
			haConfig)
	}

	// Relay the NetworkConfig to the caller
	if err != nil {
		err_log := fmt.Sprintf("Error generating finalConfigPayload: %v", err)
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Error generating finalConfigPayload",
			err_log,
			nil)
		return "success", ret_payload, nil
	}

	eLog.LogInfo("Network Config: %s", string(finalConfigPayload))
	ret_payload, _ := models.CreateResponseJSON(
		"responding to get_config request from the client.",
		"",
		[]byte(finalConfigPayload))

	return "success", ret_payload, nil
}

func HandleGetConfigResponsePvtRegionAgent(eLog *clogger.EventLogger, msg models.Message, instance *instance_master.Row) (string, []byte, error) {

	// Parse the Salt_profile to extract InterfaceConfig
	var saltProfile map[string]interface{}
	if err := json.Unmarshal([]byte(instance.SaltProfile), &saltProfile); err != nil {
		err_log := fmt.Sprintf("Error parsing Salt_profile: %v", err)
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Error parsing salt profile for the instance",
			err_log,
			nil)
		return "success", ret_payload, nil
	}

	networkConfig, ok := saltProfile["NetworkConfig"]
	// once we have the network config, we need to parse it

	eLog.LogInfo("Network config from salt profile is %s", networkConfig)
	if !ok {
		// Log the entire Salt_profile for debugging
		saltProfileJSON, err := json.MarshalIndent(saltProfile, "", "  ")
		if err != nil {
			err_log := fmt.Sprintf("Error marshalling Salt_profile for logging: %v", err)
			eLog.LogError(err_log)
			ret_payload, _ := models.CreateResponseJSON(
				"Error parsing salt profile for the instance",
				err_log,
				nil)
			return "success", ret_payload, nil
		} else {
			err_log := fmt.Sprintf("networkConfig not found in Salt_profile. Full Salt_profile: %s",
				string(saltProfileJSON))
			eLog.LogError(err_log)
			ret_payload, _ := models.CreateResponseJSON(
				"Error parsing salt profile for the instance",
				"InterfaceConfig not found in Salt_profile",
				nil)
			return "success", ret_payload, nil
		}
	}

	// extract the required info now.
	jsonData, err := json.Marshal(networkConfig)
	if err != nil {
		// Handle error
		err_log := fmt.Sprintf("Failed to marshal networkConfig_peer %s", networkConfig)
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Error parsing salt profile for the instance",
			"NetworkConfig marshalling failed",
			nil)
		return "success", ret_payload, nil
	}

	var networkConfig_marshalled NetworkConfigPvtRegionAgent
	err = json.Unmarshal(jsonData, &networkConfig_marshalled)
	if err != nil {
		// Handle error
		err_log := fmt.Sprintf("networkConfig unmarshalling failed: %s",
			string(jsonData))
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Error - networkConfig unmarshalling failed",
			"networkConfig unmarshalling failed for current instance",
			nil)
		return "success", ret_payload, nil
	}

	eLog.LogInfo("Network config for instance %d is %s", instance.ID, networkConfig_marshalled)

	finalConfigPayload, err := createConfigJSONPvtRegionAgent(eLog, networkConfig_marshalled)

	// Relay the NetworkConfig to the caller
	if err != nil {
		err_log := fmt.Sprintf("Error generating finalConfigPayload: %v", err)
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Error generating finalConfigPayload",
			err_log,
			nil)
		return "success", ret_payload, nil
	}

	eLog.LogInfo("Network Config: %s", string(finalConfigPayload))
	ret_payload, _ := models.CreateResponseJSON(
		"responding to get_config request from the client.",
		"",
		[]byte(finalConfigPayload))

	return "success", ret_payload, nil
}

func HandleGetConfigResponse(eLog *clogger.EventLogger, msg models.Message) (string, []byte, error) {
	clientID := msg.ClientID

	// Initialize the database accessor and event logger
	dbAcc := db.GetDbAccessor()

	// Split the client ID into tenant ID and instance ID
	parts := strings.Split(clientID, "-")
	if len(parts) != 2 {
		eLog.LogInfo("Invalid client ID format: %s", clientID)
		ret_payload, _ := models.CreateResponseJSON("invalid client ID format",
			"invalid client ID format",
			nil)
		return "success",
			ret_payload,
			nil
	}

	tenantID := parts[0]
	eLog.LogInfo("Handling GetConfig for tenant ID: %v", tenantID)
	instanceID, err := strconv.ParseInt(parts[1], 10, 64)
	if err != nil {
		eLog.LogError("Invalid instance ID format: %s", parts[1])
		ret_payload, _ := models.CreateResponseJSON(
			fmt.Sprintf("Invalid instance ID format: %s", parts[1]),
			"invalid client ID format",
			nil)
		return "success", ret_payload, nil
	}

	// Retrieve the instance from the database
	instance, err := dbAcc.GetInstMasterById(instanceID, eLog)
	if err != nil {
		err_log := fmt.Sprintf("Error retrieving instance: %v with instance ID %v", err, instanceID)
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Failed to get instance from instance master table",
			err_log,
			nil)
		return "success", ret_payload, nil
	}

	// check the nodetype here.
	if instance.NodeType == terraform.NodeTypePvtRegionLB {
		return HandleGetConfigResponsePvtRegionLB(eLog, msg, instance)
	} else if instance.NodeType == terraform.NodeTypePvtRegionAgent {
		return HandleGetConfigResponsePvtRegionAgent(eLog, msg, instance)
	} else {
		err_log := fmt.Sprintf("Error: Unknown/Unsupported node type %v for instance %v found", instance.NodeType, instanceID)
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Failed! Unknown/Unsupported node type for instance.",
			err_log,
			nil)
		return "success", ret_payload, nil
	}
}

func HandleGetConfigInfo(eLog *clogger.EventLogger, msg models.Message) (string, []byte, error) {
	// Initialize the database accessor and event logger
	dbAcc := db.GetDbAccessor()
	clientID := msg.ClientID

	// Split the client ID into tenant ID and instance ID
	parts := strings.Split(clientID, "-")
	if len(parts) != 2 {
		eLog.LogInfo("Invalid client ID format: %s", clientID)
		ret_payload, _ := models.CreateResponseJSON("invalid client ID format",
			"invalid client ID format",
			nil)
		return "success",
			ret_payload,
			nil
	}

	tenantID := parts[0]
	eLog.LogInfo("Handling setHAStatus for tenant ID: %v", tenantID)
	instanceID, err := strconv.ParseInt(parts[1], 10, 64)
	if err != nil {
		eLog.LogError("Invalid instance ID format: %s", parts[1])
		ret_payload, _ := models.CreateResponseJSON(
			fmt.Sprintf("Invalid instance ID format: %s", parts[1]),
			"invalid client ID format",
			nil)
		return "success", ret_payload, nil
	}

	// Retrieve the instance from the database
	instance, err := dbAcc.GetInstMasterById(instanceID, eLog)
	if err != nil {
		err_log := fmt.Sprintf("Error retrieving instance: %v with instance ID %v", err, instanceID)
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Failed to get instance from instance master table",
			err_log,
			nil)
		return "success", ret_payload, nil
	} else {
		eLog.LogInfo("Successfully got entry for instance master %d from the database", instance.ID)
	}

	// Simulate processing and return success or failure
	eLog.LogInfo("Parsing received payload %v of type  %T", msg.Payload, msg.Payload)
	message, errorStr, payload, err := models.ParsePayload(msg.Payload)
	eLog.LogInfo("Received message %v with payload %v and error %v", message, payload, errorStr)
	if err != nil {
		err_log := fmt.Sprintf("Error parsing incoming payload: %v", err)
		eLog.LogInfo(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Failed to parse incoming config.",
			err_log,
			nil)
		return "success", ret_payload, nil
	}

	if errorStr != "" {
		dbAcc.UpdateInstMasterState(eLog, instanceID, -1)
		eLog.LogError("responding to error received from client")
		ret_payload, _ := models.CreateResponseJSON(
			"Response ack message from the client acknowledged by the server.",
			"workflow failed due to error response from client. client may try again!!",
			nil)
		return "success", ret_payload, nil
	}

	eLog.LogInfo("Received message %s from the client with payload %s", message, payload)

	// Now add this state received into the instance master table.
	err = dbAcc.UpdateInstMasterState(eLog, instanceID, 1)
	if err != nil {
		// Handler error!
		err_log := fmt.Sprintf("Failed to set the state in the database due to error %v", err)
		eLog.LogError(err_log)
		ret_payload, _ := models.CreateResponseJSON(
			"Failed to set the state in the database.",
			err_log,
			nil)
		return "success", ret_payload, nil
	}

	// Updating the state of the vm.

	// Send the success message to the client.
	ret_payload, _ := models.CreateResponseJSON(
		"Response ack message from the client acknowledged by the server.",
		"",
		nil)
	return "success", ret_payload, nil
}
