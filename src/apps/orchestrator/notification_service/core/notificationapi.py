import boto3
import yaml
import json
import sys
import traceback
import time
import logging
import logging.handlers
import ast
import libs.db.dbhandle as db
from libs.msg_defs.interface import *
import notification_service.core.notificationhdlr as ntfy
from libs.comm.sqsapi import SqsApi
from libs.cfg import *
from libs.common.logger import logger
from libs.logging_infra.orch_context import OrchestratorContext
from libs.logging_infra.orch_context import orch_store_in_trace_db

class NotificationApi():
    def __init__(self, cfg):
        self.handler = None
        self.running = True
        self.rastro_ctx = OrchestratorContext(name="notificationapi", log_root_dir=cfg['log-root-dir'])
        self.logger = self.rastro_ctx.logger
        self.sqs = SqsApi(cfg['region'], [cfg['notification_q'], cfg['failure_q']], self.logger)
        self.qarn = self.sqs.get_queue_arn(cfg['notification_q']['name'])
        self.snsclient = boto3.client('sns', cfg['region'])
        self.qname = cfg['notification_q']['name']
        self.topics = []
        self.dbh = None
        self.cust_topology = None
        self.license_change = None
        self.cust_master = None
        self.cust_epaas = None
        self.ep_cnat_scaleout = None
        topic = None
        try:
            for topic in cfg['topics']:
                response = self.snsclient.subscribe(TopicArn=topic['arn'], Protocol='sqs', Endpoint=self.qarn)
                self.topics.append({'arn':topic['arn'], 'name':topic['name'], 'next_q':topic['next_q']})
                self.logger.info(str(response))
        except Exception as ex:
            self.logger.error(f"Failed with exception {ex}, Traceback: {traceback.format_exc()}")
            if topic:
                self.logger.error(f"Unable to subscribe to sns topic ARN: {topic['arn']}")
            sys.exit(-1)
    
    def update_trace_db(self):
        ret = False
        try:
            # Set the DB handler in the respective rastro context:-
            self.rastro_ctx.set_db_handler(self.dbh)
            # Store info about this logger in the trace DB.
            lret = orch_store_in_trace_db(self.dbh,
                                          self.rastro_ctx.get_trace_id(),
                                          tenant_id=999999999,
                                          workflow_type="Orch Notification API",
                                          workflow_id=int(time.time()))
            if lret == None:
                raise Exception("Failed to store info reg workflow type Orch Notification API")
            ret = True
        except Exception as E:
            self.logger.error("Failed to update trace id in the master database. %s %s" % (str(E.args),
                                                                                           str(traceback.format_exc())))
        finally:
            return ret

    def init_db(self):
        self.dbh = db.DbHandle(self.logger)
        if self.update_trace_db() == False:
            raise Exception("Failed to update the rastro trace DB. Cannot continue")

        for topic in self.topics:
            if (topic['name'] == 'cust_topology'):
                self.cust_topology = ntfy.CustTopologyTopic(topic['name'], self.dbh,
                        self.sqs, topic['next_q'], self.logger)
                topic['handler'] = self.cust_topology.handle_msg
            if (topic['name'] == 'cust_epaas'):
                self.cust_epaas = ntfy.CustEpaasTopic(topic['name'], self.dbh,
                        self.sqs, topic['next_q'], self.logger)
                topic['handler'] = self.cust_epaas.handle_msg

            if (topic['name'] == 'ep_cnat_scaleout'):
                self.ep_cnat_scaleout = ntfy.EPCnatScaleoutTopic(topic['name'], self.dbh,
                                                                 self.sqs, topic['next_q'], self.logger)
                topic['handler'] = self.ep_cnat_scaleout.handle_msg

            if (topic['name'] == 'egress_ip_scale'):
                self.egress_ip_scale_event_handler = ntfy.EgressIPScaleEventTopic(topic['name'], self.dbh, self.sqs,
                                                                       topic['next_q'], self.logger)
                topic['handler'] = self.egress_ip_scale_event_handler.handle_msg

            if (topic['name'] == 'migrate_cluster_to_ipv6'):
                self.migrate_cluster_to_ipv6_handler = ntfy.MigrateClusterToIPV6EventTopic(topic['name'], self.dbh, self.sqs,
                                                                                  topic['next_q'], self.logger)
                topic['handler'] = self.migrate_cluster_to_ipv6_handler.handle_msg

            if (topic['name'] == 'colo_vlan_onboard'):
                self.colo_vlan_onboard_event_handler = ntfy.ColoVlanOnboardEventTopic(topic['name'], self.dbh, self.sqs,
                                                                       topic['next_q'], self.logger)
                topic['handler'] = self.colo_vlan_onboard_event_handler.handle_msg

            if (topic['name'] == 'delete_instance'):
                self.delete_instance_event_handler = ntfy.DeleteInstanceEventTopic(topic['name'], self.dbh, self.sqs,
                                                                                  topic['next_q'], self.logger)
                topic['handler'] = self.delete_instance_event_handler.handle_msg

            if (topic['name'] == 'tenant_ipv6_settings'):
                self.tenant_ipv6_settings_event_handler = ntfy.TenantIpv6SettingsEventTopic(topic['name'], self.dbh, self.sqs,
                                                                    topic['next_q'], self.logger)
                topic['handler'] = self.tenant_ipv6_settings_event_handler.handle_msg

            if (topic['name'] == 'priv_sase_node_update'):
                self.priv_sase_node_update_event_handler = ntfy.PrivSaseNodeUpdateEventTopic(topic['name'], self.dbh, self.sqs,
                                                                       topic['next_q'], self.logger)
                topic['handler'] = self.priv_sase_node_update_event_handler.handle_msg


            '''
            if (topic['name'] == 'license_change'):
                self.license_change = ntfy.LicensingTopic(topic['name'], self.dbh,
                        self.sqs, topic['next_q'], self.logger)
                topic['handler'] = self.license_change.handle_msg
            '''
            if (topic['name'] == 'instance_replace'):
                self.instance_replace = ntfy.InstanceReplaceTopic(topic['name'], self.dbh,
                        self.sqs, topic['next_q'], self.logger)
                topic['handler'] = self.instance_replace.handle_msg
            if (topic['name'] == 'orchestration_event'):
                self.orchestration_event = ntfy.OrchestrationEventTopic(topic['name'], self.dbh,
                    self.sqs, topic['next_q'], self.logger)
                topic['handler'] = self.orchestration_event.handle_msg

    def get_topic_from_arn(self, arn):
        x = [q for q in self.topics if q['arn'] == arn]
        if x:
            return x[0]  
        else: 
            return None

    def get_topic(self, tname):
        x = [t for t in self.topics if t['name'] == tname]
        if x:
            return x[0]
        else:
            return None

    def get_notification_params(self, body):
        if not 'TopicArn' in body or not 'Message' in body:
            return (None, None)
        self.logger.debug(type(body))
        bb = ast.literal_eval(body)
        self.logger.debug(type(bb))
        topic_arn = bb['TopicArn']
        msg = json.loads(bb['Message'])
        t = self.get_topic_from_arn(topic_arn)
        topic = t['name']
        return (topic, msg)

    def handle(self):
        result = []
        msgs = []
        while(self.running):
            msgs = []
            if self.sqs.receive_msg (self.qname, msgs):
                for m in msgs:
                    try:
                        self.logger.info("Processing message %s Id: %s\n" % (m['Body'], m['MessageId']))
                        body = m['Body']
                        topic, msg = self.get_notification_params(body)
                        self.logger.info("Received message %s for topic %s" % (msg, topic))
                        t = self.get_topic(topic)
                        if t:
                            ret = t['handler'](msg)
                            if not ret:
                                self.logger.error("Failed to handle msg %s" % msg)
                                jmsg = json.dumps(m)
                                self.sqs.send_msg("failure_q.fifo", jmsg)         
                        else:
                            self.logger.error("Got message of Unknown Topic %s" % topic)
                            jmsg = json.dumps(m)
                            self.sqs.send_msg("failure_q.fifo", jmsg)         
                    except Exception as ex:
                        self.logger.error("Error while processing the message. "
                                "Possible bug %s" % (str(ex)))
                        self.logger.error(traceback.format_exc())
                        jmsg = json.dumps(m)
                        self.sqs.send_msg("failure_q.fifo", jmsg)         
                        pass
                    self.sqs.delete_msg(self.qname, m)
        if not self.running:
            self.logger.info("Exiting the thread handler")

        self.dbh.conn.close()