import logging
import logging.handlers
import sys
import json
import sqlite3
import jsonpickle
import base64
import traceback
import time
from libs.db.dbhandle import *
import libs.model.custmodel as CS
import libs.model.custnodemodel as CSN
import libs.model.custEpaasConfigModel as CEC
import libs.model.licensemodel as LS
import libs.model.gpaasmodel as GP
import libs.model.orchjobs as JOB
import libs.model.subtenantlicensemodel as STLM
from libs.model.instancemodel import InstanceModel
from libs.msg_defs.interface import *
from libs.msg_defs.workflows import *
from libs.comm.sqsapi import *
from libs.cfg import *
from libs.common.shared.sys_utils import *
from libs.apis.commit_job_status_api_orch import save_orch_started_state_to_commit_status_detail_table
from libs.apis.commit_job_status_api_orch import save_orch_failed_state_to_commit_status_detail_table
from libs.common.shared.grpc.proto.avisarpb.avisar_pb2_client import AvisarContext, \
    NOTIFICATION_STATUS_SUCCESS, \
    NOTIFICATION_STATUS_FAILED, METRIC_SEVERITY_CRITICAL, METRIC_SEVERITY_INFO


class TopicHdlr():
    def __init__(self, topic, dbh, sqs, next_q, logger):
        # Don't do exception handling here as this will be called in init.
        self.lastmsg_ts = 0
        self.logger = logger
        self.topic = topic
        self.sqs = sqs
        self.next_q = next_q
        self.status = "INIT"
        self.dbh = dbh
        self.err_msg = ""
        if (self.next_q):
            tmp = self.sqs.sqs.get_queue_by_name(QueueName=self.next_q)
            self.sqs.queues.append({'name': self.next_q, 'queue': tmp})
        self.avctx = AvisarContext(logger=self.logger,
                                   service_endpoint=cfg.get('avisar_service_endpoint'),
                                   forwarding_enabled=cfg.get('avisar_forwarding_enabled', False),
                                   sender="NotificationService",
                                   aws_env=cfg.get("aws_env"))

    def get_new_job(self, row_id, action):
        jtype = workflow_names[0]  # UNDICIDED
        job = JOB.OrchJobs(jtype, row_id, self.status,
                           "%s %s Event Row Id: %d" %
                           (self.topic, action.lower(), row_id))
        job.save_job(self.dbh)
        if not job.jobid:
            self.logger.error("Topic %s, Unable to save job" % (self.topic,))
            return False
        return job

    def handle_msg(self, msg):
        ret = False
        try:
            try:
                panorama_job_id = None
                timestamp = int(msg['timestamp'])
                row_id = int(msg['rowid'])
                action = msg['action']
                cnat_alert_metrics = {}
                if "cloud_provider" in list(msg.keys()):
                    cloud_provider = msg['cloud_provider']
                    if cloud_provider == "gcp":
                        cloud_provider = PROVIDER_GCP
                    elif cloud_provider == "oci":
                        cloud_provider = PROVIDER_OCI
                else:
                    cloud_provider = None
                if "compute_region_id" in list(msg.keys()):
                    compute_region_id = msg['compute_region_id']
                else:
                    compute_region_id = None
                if "node_type" in list(msg.keys()):
                    node_type = msg['node_type']
                else:
                    node_type = None
                if "table" in list(msg.keys()):
                    table = msg['table']
                else:
                    table = None
                if "project_id" in list(msg.keys()):
                    cnat_alert_metrics["project_id"] = msg['project_id']
                if "cloud_provider_region" in list(msg.keys()):
                    cnat_alert_metrics["cloud_provider_region"] = msg['cloud_provider_region']
                if "gateway_name" in list(msg.keys()):
                    cnat_alert_metrics["gateway_name"] = msg['gateway_name']
                if "metric" in list(msg.keys()):
                    cnat_alert_metrics["metric"] = msg['metric']

                if (self.lastmsg_ts >= timestamp):
                    self.logger.info("Ignoring stale message for topic %s for row %d action %s" %
                                     (self.topic, row_id, action))
                    return True
                if 'panorama_job_id' in list(msg.keys()):
                    panorama_job_id = msg['panorama_job_id']

                # Reuse or create a new rastro tag.
                if 'rastro_log_tag' in list(msg.keys()):
                    rastro_tag = msg['rastro_log_tag']
                    try:
                        rastro_tag = json.loads(base64.b64decode(rastro_tag).decode('utf-8'))
                        self.logger.set_enhanced_log_tag(rastro_tag)
                    except Exception as E:
                        self.logger.error("Failed to decode rastro tag Error: %s, %s" % (str(E.args),
                                                                                         str(traceback.format_exc())))
            except Exception as ex:
                # TODO: Need to log this failure
                self.logger.error("Invalid Message structure, ****Possible bug******: %s" % (str(ex.args)))
                return

            res = self.action_handler(action, row_id, table, panorama_job_id, cloud_provider, compute_region_id, node_type, cnat_alert_metrics)
            if not res:
                status = -1
            else:
                status = res

            if status:
                ret = True
            else:
                ret = False
        except Exception as ex:
            self.logger.error("Failed to handle message: Exception %s" % str(ex))
        finally:
            self.logger.reset_enhanced_log_tag()
            return ret

    def action_handler(self, action, row_id, table, panorama_job_id=None, cloud_provider=None, compute_region_id=None,
                       node_type=None, cnat_alert_metrics=None):
        self.logger.info("%s default handler got action %s for row %d" %
                         (self.topic, action.lower(), row_id))
        return False

    def to_json(self, msg):
        obj = jsonpickle.encode(msg)
        return obj

    def from_json(self, msg):
        obj = jsonpickle.decode(msg)
        return obj


class CustEpaasTopic(TopicHdlr):
    def get_e2e_rastro_tag(self):
        # e2e is end to end :)
        my_e2e_rastro_tag = self.logger.get_enhanced_log_tag()
        if my_e2e_rastro_tag:
            return self.logger.get_updated_log_tag(my_e2e_rastro_tag)
        else:
            return None

    def action_handler(self, action, row_id, table, panorama_job_id=None, cloud_provider=None, compute_region_id=None, node_type=None, cnat_alert_metrics=None):
        if action.lower() == "update_epaas" and int(node_type) in (NODE_TYPE_BI_NH_PROXY, NODE_TYPE_UDA):
            # In case of eproxy upgrade for BI stack, the request comes for node-type=173, alt type is not specified
            # because all alt-types are supposed to be upgraded by backend.
            # So cust epaas can not be determined here, so we let it flow
            # and finally gets checked per alt-type at orchestrator.py in process_update_envoy_version()
            self.logger.info(f"Bypassing cust epaas check for {node_type} and action {action}")
        else:
            cust_ep_cfg = CEC.CustEpaasConfigModel(self.dbh, custid=row_id, compute_region_id=compute_region_id,
                                                cloud_provider=cloud_provider, node_type=node_type)
            if not cust_ep_cfg.get_entry():
                err_msg = ("Topic %s, %s row %d: Unable to find cust_epaas_cfg record in"
                        " the table. node_type %d" % (self.topic, action, row_id, int(node_type)))
                self.logger.error(err_msg)
                raise Exception(err_msg)
        if action.lower() == "update_epaas" or action.lower() == "upgrade_ep_outside_panos":
            job = self.get_new_job(row_id, action)
            if not job:
                return False
            hdr = OrchHeader(job.job_type, job.jobid)
            msg = NtfyOnboardingMsg(hdr, self.topic, action.lower(), row_id, log_tag=self.get_e2e_rastro_tag(),
                                    cloud_provider=cloud_provider,
                                    compute_region_id=compute_region_id, node_type=node_type)
            jsonmsg = self.to_json(msg)
            self.sqs.send_msg(self.next_q, jsonmsg)


class EPCnatScaleoutTopic(TopicHdlr):
    def get_e2e_rastro_tag(self):
        # e2e is end to end :)
        my_e2e_rastro_tag = self.logger.get_enhanced_log_tag()
        if my_e2e_rastro_tag:
            return self.logger.get_updated_log_tag(my_e2e_rastro_tag)
        else:
            return None

    def action_handler(self, action, row_id, table, panorama_job_id=None, cloud_provider=None, compute_region_id=None, node_type=None, cnat_alert_metrics=None):
        if action.lower() == "ep_cnat_scaleout":
            if int(row_id) and int(compute_region_id):
                cust_ep_cfg = CEC.CustEpaasConfigModel(self.dbh, custid=row_id, compute_region_id=compute_region_id,
                                                   cloud_provider=PROVIDER_GCP)
                if not cust_ep_cfg.get_entry():
                    err_msg = ("Topic %s, %s row %d: Unable to find cust_epaas_cfg record in"
                               " the table. node_type %d" % (self.topic, action, row_id, int(node_type)))
                    self.logger.error(err_msg)
                    raise Exception(err_msg)
            elif not row_id:
                project_id = cnat_alert_metrics.get('project_id', None)
                if project_id:
                    cursor = self.dbh.get_cursor()
                    cursor.execute("select id from cust_master where project_id = %s", (project_id,))
                    ret = cursor.fetchone()
                    if ret:
                        row_id = ret[0]
                    else:
                        err_msg = f"could not fetch custid for the tenant project_id {project_id}"
                        self.logger.error(err_msg)
                        raise Exception(err_msg)
                else:
                    raise Exception("required param project_id/cust_id missing")
            elif not compute_region_id:
                if 'cloud_provider_region' in list(cnat_alert_metrics.keys()):
                    compute_region_name = cnat_alert_metrics.get("cloud_provider_region", None)
                    cursor = self.dbh.get_cursor()
                    cursor.execute("select compute_region_id from region_master where edge_location_region_id=compute_region_id"
                                   " and cloud_provider='gcp' and is_epaas_disabled=0 and native_compute_region_name= %s", (compute_region_name,))
                    ret = cursor.fetchone()
                    if ret:
                        row_id = ret[0]
                    else:
                        err_msg = f"could not fetch region_id for the tenant region {compute_region_name}"
                        self.logger.error(err_msg)
                        raise Exception(err_msg)
                else:
                    raise Exception("required param region_id/region_name missing")
            job = self.get_new_job(row_id, action)
            if not job:
                return False
            hdr = OrchHeader(job.job_type, job.jobid)
            msg = NtfyOnboardingMsg(hdr, self.topic, action.lower(), row_id, log_tag=self.get_e2e_rastro_tag(),
                                    cloud_provider=cloud_provider,
                                    compute_region_id=compute_region_id, node_type=node_type)
            jsonmsg = self.to_json(msg)
            self.sqs.send_msg(self.next_q, jsonmsg)


class EgressIPScaleEventTopic(TopicHdlr):
    def get_e2e_rastro_tag(self):
        # e2e is end to end :)
        my_e2e_rastro_tag = self.logger.get_enhanced_log_tag()
        if my_e2e_rastro_tag:
            return self.logger.get_updated_log_tag(my_e2e_rastro_tag)
        else:
            return None

    def egress_ip_scale_event_get_new_job(self, action, instance_id, region_id, scale_nr):
        jtype = workflow_names[0]  # UNDECIDED
        job = JOB.OrchJobs(jtype, 0, self.status,
                           "%s %s Event for instance: %s, scale_nr: %s, region_id: %s" %
                           (self.topic, action.lower(), str(instance_id), str(scale_nr), str(region_id)))
        job.save_job(self.dbh)
        if not job.jobid:
            self.logger.error("Topic %s, Unable to save job" % (self.topic,))
            return False
        return job

    def handle_msg(self, msg):
        ret = False
        try:
            try:
                timestamp = int(msg['timestamp'])
                action = msg['action']
                region_id = msg['region_id']
                nr_ip_addresses = msg['nr_ip_addresses']

                if self.lastmsg_ts >= timestamp:
                    self.logger.info("Ignoring stale message for topic %s for action %s" %
                                     (self.topic, action))
                    return True

                # Get the instance ID
                instance_id = int(msg['instance_id'])

                # Reuse or create a new rastro tag.
                if 'rastro_log_tag' in list(msg.keys()):
                    rastro_tag = msg['rastro_log_tag']
                    try:
                        rastro_tag = json.loads(base64.b64decode(rastro_tag).decode('utf-8'))
                        self.logger.set_enhanced_log_tag(rastro_tag)
                    except Exception as E:
                        self.logger.error("Failed to decode rastro tag Error: %s, %s" % (str(E.args),
                                                                                         str(traceback.format_exc())))
            except Exception as ex:
                # TODO: Need to log this failure
                self.logger.error("Invalid Message structure, ****Possible bug******: %s, locals: %s" %
                                  (str(ex.args), str(locals())))
                return

            res = self.egress_ip_scale_event_action_handler(action, instance_id, region_id, nr_ip_addresses)
            if not res:
                status = -1
            else:
                status = res

            if status:
                ret = True
            else:
                ret = False
        except Exception as ex:
            self.logger.error("Failed to handle message: Exception %s" % str(ex))
        finally:
            self.logger.reset_enhanced_log_tag()
            return ret

    def egress_ip_scale_event_action_handler(self, action, instance_id, region_id, nr_ip_addresses):

        if action.lower() == "egress_ip_scale_event":
            job = self.egress_ip_scale_event_get_new_job(action, instance_id, region_id, nr_ip_addresses)
            if not job:
                return False
            hdr = OrchHeader(job.job_type, job.jobid)
            egress_ip_scale_event_msg_instance = EgressIPScaleEventMsg()
            egress_ip_scale_event_msg_instance.set_EgressIPScaleEvent_metadata(instance_id=instance_id,
                                                                               region_id=region_id,
                                                                               nr_ip_addresses=nr_ip_addresses)
            msg = NtfyOnboardingMsg(hdr,
                                    self.topic,
                                    action.lower(),
                                    0,
                                    log_tag=self.get_e2e_rastro_tag(),
                                    cloud_provider=None,
                                    compute_region_id=None,
                                    node_type=None)
            msg.EgressIPScaleEventMsg = egress_ip_scale_event_msg_instance
            json_msg = self.to_json(msg)
            self.sqs.send_msg(self.next_q, json_msg)

class MigrateClusterToIPV6EventTopic(TopicHdlr):
    def get_e2e_rastro_tag(self):
        # e2e is end to end :)
        my_e2e_rastro_tag = self.logger.get_enhanced_log_tag()
        if my_e2e_rastro_tag:
            return self.logger.get_updated_log_tag(my_e2e_rastro_tag)
        else:
            return None

    def migrate_cluster_to_ipv6_event_get_new_job(self, action, cluster_id):
        jtype = workflow_names[0]  # UNDECIDED
        job = JOB.OrchJobs(jtype, 0, self.status,
                           "%s %s Event for instance: %s " %
                           (self.topic, action.lower(), str(cluster_id)))
        job.save_job(self.dbh)
        if not job.jobid:
            self.logger.error("Topic %s, Unable to save job" % (self.topic,))
            return False
        return job

    def handle_msg(self, msg):
        ret = False
        try:
            try:
                timestamp = int(msg['timestamp'])
                action = msg['action']

                if self.lastmsg_ts >= timestamp:
                    self.logger.info("Ignoring stale message for topic %s for action %s" %
                                     (self.topic, action))
                    ret = True
                    return

                # Get the instance ID
                cluster_id = int(msg['cluster_id'])

                # Reuse or create a new rastro tag.
                if 'rastro_log_tag' in list(msg.keys()):
                    rastro_tag = msg['rastro_log_tag']
                    try:
                        rastro_tag = json.loads(base64.b64decode(rastro_tag).decode('utf-8'))
                        self.logger.set_enhanced_log_tag(rastro_tag)
                    except Exception as E:
                        self.logger.error("Failed to decode rastro tag Error: %s, %s" % (str(E.args),
                                                                                         str(traceback.format_exc())))
            except Exception as ex:
                # TODO: Need to log this failure
                self.logger.error("Invalid Message structure, ****Possible bug******: %s, locals: %s" %
                                  (str(ex.args), str(locals())))
                return

            res = self.migrate_cluster_to_ipv6_event_action_handler(action, cluster_id)
            if not res:
                status = -1
            else:
                status = res

            if status:
                ret = True
            else:
                ret = False
        except Exception as ex:
            self.logger.error("Failed to handle message: Exception %s" % str(ex))
        finally:
            self.logger.reset_enhanced_log_tag()
            return ret

    def migrate_cluster_to_ipv6_event_action_handler(self, action, cluster_id):

        if action.lower() == "migrate_cluster_to_ipv6":
            job = self.migrate_cluster_to_ipv6_event_get_new_job(action, cluster_id)
            if not job:
                return False
            hdr = OrchHeader(job.job_type, job.jobid)
            migrate_cluster_to_ipv6_scale_event_msg_instance = MigrateClusterToIPV6EventMsg()
            migrate_cluster_to_ipv6_scale_event_msg_instance.set_MigrateClusterToIPV6_metadata(cluster_id=cluster_id)
            msg = NtfyOnboardingMsg(hdr,
                                    self.topic,
                                    action.lower(),
                                    0,
                                    log_tag=self.get_e2e_rastro_tag(),
                                    cloud_provider=None,
                                    compute_region_id=None,
                                    node_type=None)
            msg.MigrateClusterToIPV6EventMsg = migrate_cluster_to_ipv6_scale_event_msg_instance
            json_msg = self.to_json(msg)
            self.sqs.send_msg(self.next_q, json_msg)

class ColoVlanOnboardEventTopic(TopicHdlr):
    def get_e2e_rastro_tag(self):
        # e2e is end to end :)
        my_e2e_rastro_tag = self.logger.get_enhanced_log_tag()
        if my_e2e_rastro_tag:
            return self.logger.get_updated_log_tag(my_e2e_rastro_tag)
        else:
            return None

    def colo_vlan_onboard_event_get_new_job(self, action, colo_vlan_onb_id, region_id, custid):
        jtype = workflow_names[0]  # UNDECIDED
        job = JOB.OrchJobs(jtype, custid, self.status,
                           "%s %s Event for colo vlan onboard id: %s, region_id: %s" %
                           (self.topic, action.lower(), str(colo_vlan_onb_id), str(region_id)))
        job.save_job(self.dbh)
        if not job.jobid:
            self.logger.error("Topic %s, Unable to save job" % (self.topic,))
            return False
        return job

    def handle_msg(self, msg):
        ret = False
        try:
            try:
                timestamp = int(msg['timestamp'])
                action = msg['action']
                region_id = msg['region_id']

                if self.lastmsg_ts >= timestamp:
                    self.logger.info("Ignoring stale message for topic %s for action %s" %
                                     (self.topic, action))
                    ret = True
                    return

                # Get the instance ID
                colo_vlan_onb_id = int(msg['colo_vlan_onb_id'])
                custid = int(msg['custid'])
                update_bgp_peer = msg['update_bgp_peer']
                create_vlan_att = msg['create_vlan_att']

                # Reuse or create a new rastro tag.
                if 'rastro_log_tag' in list(msg.keys()):
                    rastro_tag = msg['rastro_log_tag']
                    try:
                        rastro_tag = json.loads(base64.b64decode(rastro_tag).decode('utf-8'))
                        self.logger.set_enhanced_log_tag(rastro_tag)
                    except Exception as E:
                        self.logger.error("Failed to decode rastro tag Error: %s, %s" % (str(E.args),
                                                                                         str(traceback.format_exc())))
            except Exception as ex:
                # TODO: Need to log this failure
                self.logger.error("Invalid Message structure, ****Possible bug******: %s, locals: %s" %
                                  (str(ex.args), str(locals())))
                return

            res = self.colo_onboard_scale_event_action_handler(action, colo_vlan_onb_id,
                                                               region_id, custid, update_bgp_peer, create_vlan_att)
            if not res:
                status = -1
            else:
                status = res

            if status:
                ret = True
            else:
                ret = False
        except Exception as ex:
            self.logger.error("Failed to handle message: Exception %s" % str(ex))
        finally:
            self.logger.reset_enhanced_log_tag()
            return ret

    def colo_onboard_scale_event_action_handler(self, action, colo_vlan_onb_id, region_id, custid, update_bgp_peer, create_vlan_att):

        if action.lower() == "colo_vlan_onboard_event":
            job = self.colo_vlan_onboard_event_get_new_job(action, colo_vlan_onb_id, region_id, custid)
            if not job:
                return False
            hdr = OrchHeader(job.job_type, job.jobid)
            colo_vlan_onb_event_msg_instance = ColoVlanOnboardEventMsg()
            colo_vlan_onb_event_msg_instance.set_ColoVlanOnboardingEvent_metadata(colo_vlan_onb_id=colo_vlan_onb_id,
                                                                               region_id=region_id,
                                                                               custid=custid,
                                                                               update_bgp_peer=update_bgp_peer,
                                                                               create_vlan_att=create_vlan_att)
            msg = NtfyOnboardingMsg(hdr,
                                    self.topic,
                                    action.lower(),
                                    0,
                                    log_tag=self.get_e2e_rastro_tag(),
                                    cloud_provider=None,
                                    compute_region_id=None)
            msg.ColoVlanOnboardEventMsg = colo_vlan_onb_event_msg_instance
            json_msg = self.to_json(msg)
            self.sqs.send_msg(self.next_q, json_msg)

class DeleteInstanceEventTopic(TopicHdlr):
    def get_e2e_rastro_tag(self):
        # e2e is end to end :)
        my_e2e_rastro_tag = self.logger.get_enhanced_log_tag()
        if my_e2e_rastro_tag:
            return self.logger.get_updated_log_tag(my_e2e_rastro_tag)
        else:
            return None

    def delete_instance_event_get_new_job(self, action, instance_id, delete_cluster, is_transient):
        jtype = workflow_names[0]  # UNDECIDED
        job = JOB.OrchJobs(jtype, 0, self.status,
                              "%s %s Event for instance: %s, delete_cluster: %s, is_transient: %s" %
                              (self.topic, action.lower(), str(instance_id), str(delete_cluster), str(is_transient)))
        job.save_job(self.dbh)
        if not job.jobid:
            self.logger.error("Topic %s, Unable to save job" % (self.topic,))
            return False
        return job

    def handle_msg(self, msg):
        ret = False
        try:
            try:
                timestamp = int(msg['timestamp'])
                action = msg['action']
                delete_cluster = msg.get('delete_cluster', False)
                is_transient = msg.get('is_transient', False)

                if self.lastmsg_ts >= timestamp:
                    self.logger.info("Ignoring stale message for topic %s for action %s" %
                                     (self.topic, action))
                    ret = True
                    return

                    # Get the instance ID
                instance_id = int(msg['instance_id'])

                # Reuse or create a new rastro tag.
                if 'rastro_log_tag' in list(msg.keys()):
                    rastro_tag = msg['rastro_log_tag']
                    try:
                        rastro_tag = json.loads(base64.b64decode(rastro_tag).decode('utf-8'))
                        self.logger.set_enhanced_log_tag(rastro_tag)
                    except Exception as E:
                        self.logger.error("Failed to decode rastro tag Error: %s, %s" %
                                          (str(E.args),
                                           str(traceback.format_exc())))
            except Exception as ex:
                # TODO: Need to log this failure
                self.logger.error("Invalid Message structure, ****Possible bug******: %s, locals: %s" %
                                  (str(ex.args), str(locals())))
                return

            res = self.delete_instance_event_action_handler(action, instance_id, delete_cluster, is_transient)
            if not res:
                status = -1
            else:
                status = res

            if status:
                ret = True
            else:
                ret = False
        except Exception as ex:
            self.logger.error("Failed to handle message: Exception %s" % str(ex))
        finally:
            self.logger.reset_enhanced_log_tag()
            return ret

    def delete_instance_event_action_handler(self, action, instance_id, delete_cluster, is_transient):

        if action.lower() == "delete_instance_event":
            job = self.delete_instance_event_get_new_job(action, instance_id, delete_cluster, is_transient)
            if not job:
                return False
            hdr = OrchHeader(job.job_type, job.jobid)
            delete_instance_event_msg_instance = DeleteInstanceEventMsg()
            delete_instance_event_msg_instance.set_DeleteInstanceEventMsg_metadata(instance_id=instance_id,
                                                                                   delete_cluster=delete_cluster,
                                                                                   is_transient=is_transient)
            msg = NtfyOnboardingMsg(hdr,
                                    self.topic,
                                    action.lower(),
                                    0,
                                    log_tag=self.get_e2e_rastro_tag(),
                                    cloud_provider=None,
                                    compute_region_id=None,
                                    node_type=None)
            msg.DeleteInstanceEventMsg = delete_instance_event_msg_instance
            json_msg = self.to_json(msg)
            self.sqs.send_msg(self.next_q, json_msg)




class PrivSaseNodeUpdateEventTopic(TopicHdlr):
    def get_e2e_rastro_tag(self):
        # e2e is end to end :)
        my_e2e_rastro_tag = self.logger.get_enhanced_log_tag()
        if my_e2e_rastro_tag:
            return self.logger.get_updated_log_tag(my_e2e_rastro_tag)
        else:
            return None

    def priv_sase_node_update_event_get_new_job(self, action, sase_private_region_id, custid, node_type, service_node_type):
        jtype = workflow_names[0]  # UNDECIDED
        job = JOB.OrchJobs(jtype, custid, self.status,
                           "%s %s Event for private sase node update sase_private_region_id: %s service_node_type: %s, node_type: %s, custid: %s" %
                           (self.topic, action.lower(), str(sase_private_region_id), str(service_node_type), str(node_type), str(custid)))
        job.save_job(self.dbh)
        if not job.jobid:
            self.logger.error("Topic %s, Unable to save job" % (self.topic,))
            return False
        return job

    def handle_msg(self, msg):
        ret = False
        try:
            try:
                timestamp = int(msg['timestamp'])
                action = msg['action']

                if self.lastmsg_ts >= timestamp:
                    self.logger.info("Ignoring stale message for topic %s for action %s" %
                                     (self.topic, action))
                    ret = True
                    return

                # Get the instance ID
                sase_private_region_id = int(msg['sase_private_region_id'])
                custid = int(msg['custid'])
                service_node_type = int(msg['service_node_type'])
                node_type = int(msg['node_type'])

                # Reuse or create a new rastro tag.
                if 'rastro_log_tag' in list(msg.keys()):
                    rastro_tag = msg['rastro_log_tag']
                    try:
                        rastro_tag = json.loads(base64.b64decode(rastro_tag).decode('utf-8'))
                        self.logger.set_enhanced_log_tag(rastro_tag)
                    except Exception as E:
                        self.logger.error("Failed to decode rastro tag Error: %s, %s" % (str(E.args),
                                                                                         str(traceback.format_exc())))
            except Exception as ex:
                # TODO: Need to log this failure
                self.logger.error("Invalid Message structure, ****Possible bug******: %s, locals: %s" %
                                  (str(ex.args), str(locals())))
                return

            res = self.priv_sase_node_update_event_action_handler(action, sase_private_region_id=sase_private_region_id,
                                                               service_node_type=service_node_type, custid=custid, node_type=node_type)
            if not res:
                status = -1
            else:
                status = res

            if status:
                ret = True
            else:
                ret = False
        except Exception as ex:
            self.logger.error("Failed to handle message: Exception %s" % str(ex))
        finally:
            self.logger.reset_enhanced_log_tag()
            return ret

    def priv_sase_node_update_event_action_handler(self, action, sase_private_region_id, custid, node_type, service_node_type):

        if action.lower() == "priv_sase_node_update_event":
            job = self.priv_sase_node_update_event_get_new_job(action, sase_private_region_id, custid, node_type, service_node_type)
            if not job:
                return False
            hdr = OrchHeader(job.job_type, job.jobid)
            priv_sase_update_event_msg_instance = PrivSaseNodeUpdateEventMsg()
            priv_sase_update_event_msg_instance.set_PrivSaseNodeUpdateEventMsg_metadata(sase_private_region_id=sase_private_region_id,
                                                                               service_node_type=service_node_type,
                                                                               custid=custid,
                                                                               node_type= node_type)
            msg = NtfyOnboardingMsg(hdr,
                                    self.topic,
                                    action.lower(),
                                    0,
                                    log_tag=self.get_e2e_rastro_tag(),
                                    cloud_provider=None,
                                    compute_region_id=None)
            msg.PrivSaseNodeUpdateEventMsg = priv_sase_update_event_msg_instance
            json_msg = self.to_json(msg)
            self.sqs.send_msg(self.next_q, json_msg)


class TenantIpv6SettingsEventTopic(TopicHdlr):
    def get_e2e_rastro_tag(self):
        # e2e is end to end :)
        my_e2e_rastro_tag = self.logger.get_enhanced_log_tag()
        if my_e2e_rastro_tag:
            return self.logger.get_updated_log_tag(my_e2e_rastro_tag)
        else:
            return None

    def tenant_ipv6_settings_event_get_new_job(self, action, tenant_id, region_name, mode):
        jtype = workflow_names[0]  # UNDECIDED
        job = JOB.OrchJobs(jtype, 0, self.status,
                           f"{self.topic} {action.lower} Event for tenant id: {tenant_id}, region_name: {region_name}, "
                           f"mode: {mode}")
        job.save_job(self.dbh)
        if not job.jobid:
            self.logger.error("Topic %s, Unable to save job" % (self.topic,))
            return False
        return job

    def handle_msg(self, msg):
        ret = False
        try:
            try:
                timestamp = int(msg['timestamp'])
                action = msg['action']
                tenant_id = msg.get('tenant_id', 0)
                region_name = msg.get('region_name', None)
                node_type = msg.get('node_type', 0)
                mode = msg.get('mode', None)

                if self.lastmsg_ts >= timestamp:
                    self.logger.info(f"Ignoring stale message for topic {self.topic} for action {action}")
                    ret = True
                    return

                if tenant_id in [None, 0] or region_name in [None, ""] or mode in [None, 0] or node_type in [None, 0]:
                    err_msg = (f"Some of the required args are not set. Locals: {locals()}")
                    raise Exception(err_msg)

                # Reuse or create a new rastro tag.
                if 'rastro_log_tag' in list(msg.keys()):
                    rastro_tag = msg['rastro_log_tag']
                    try:
                        rastro_tag = json.loads(base64.b64decode(rastro_tag).decode('utf-8'))
                        self.logger.set_enhanced_log_tag(rastro_tag)
                    except Exception as E:
                        self.logger.error(f"Failed to decode rastro tag Error: {E.args}, {traceback.format_exc()}")
            except Exception as ex:
                # TODO: Need to log this failure
                self.logger.error(f"Invalid Message structure, ****Possible bug******: Traceback: {ex.args}, "
                                  f"locals: {locals()}")
                return

            res = self.tenant_ipv6_settings_event_action_handler(action, tenant_id, region_name, mode, node_type)
            if not res:
                status = -1
            else:
                status = res

            if status:
                ret = True
            else:
                ret = False

        except Exception as ex:
            self.logger.error(f"Failed to handle message: Exception {ex}")
            ret = False
        finally:
            self.logger.reset_enhanced_log_tag()
            return ret

    def tenant_ipv6_settings_event_action_handler(self, action, tenant_id, region_name, mode, node_type):
        if action.lower() == "tenant_ipv6_settings_event":
            job = self.tenant_ipv6_settings_event_get_new_job(action, tenant_id, region_name, mode)
            if not job:
                return False
            hdr = OrchHeader(job.job_type, job.jobid)
            tenant_ipv6_settings_event_msg_instance = TenantIpv6SettingsEventMsg()
            tenant_ipv6_settings_event_msg_instance.set_TenantIpv6SettingsEventMsg_metadata(tenant_id=tenant_id,
                                                                                            region_name=region_name,
                                                                                            mode=mode,
                                                                                            node_type=node_type)
            msg = NtfyOnboardingMsg(hdr,
                                    self.topic,
                                    action.lower(),
                                    0,
                                    log_tag=self.get_e2e_rastro_tag(),
                                    cloud_provider=None,
                                    compute_region_id=None)
            msg.TenantIpv6SettingsEventMsg = tenant_ipv6_settings_event_msg_instance
            json_msg = self.to_json(msg)
            self.sqs.send_msg(self.next_q, json_msg)

class CustTopologyTopic(TopicHdlr):
    def get_e2e_rastro_tag(self):
        # e2e is end to end :)
        my_e2e_rastro_tag = self.logger.get_enhanced_log_tag()
        if my_e2e_rastro_tag:
            return self.logger.get_updated_log_tag(my_e2e_rastro_tag)
        else:
            return None

    def action_handler(self, action, row_id, table, panorama_job_id=None, cloud_provider=None, compute_region_id=None, node_type=None, cnat_alert_metrics=None):
        custnode = CSN.CustNodeModel(row_id, self.dbh)
        custnode_id = 0
        tenant_id = 0
        result = False
        try:
            if (custnode.id == None):
                err_msg = ("Topic %s, %s row %d: Unable to find node in"
                           " the table." % (self.topic, action, row_id))
                self.logger.error(err_msg)
                raise Exception(err_msg)

            custnode_id = custnode.id
            custnode.set_avisar_ctx(self.dbh, self.avctx)

            # Get the cust ID.
            customer = CS.CustomerModel(custid=custnode.custid, dbh=self.dbh)
            tenant_id = customer.get_param("acct_id")
            if (action.lower() == "insert"):
                self.avctx.metric_state = "Customer Topology Insertion notification"
                job = self.get_new_job(row_id, action)
                if not job:
                    result = False
                    return
                custnode.jobid = job.jobid
                custnode.save(self.dbh)

                hdr = OrchHeader(job.job_type, job.jobid)
                msg = NtfyOnboardingMsg(hdr, self.topic, action.lower(),
                                        row_id, panorama_job_id, self.get_e2e_rastro_tag())
                jsonmsg = self.to_json(msg)
                self.sqs.send_msg(self.next_q, jsonmsg)
                result = job.jobid
                return

            elif (action.lower() == "update"):
                if custnode.is_deleted:
                    self.avctx.metric_state = "Customer Topology Delete notification"
                else:
                    self.avctx.metric_state = "Customer Topology Update notification"

                if table == "cust_topology":
                    if not custnode.is_deleted:
                        self.logger.info(f"cust topology node details {custnode.__str__()}")
                        self.logger.info("Topic %s: Ignoring action Update for no delete"
                                         % (self.topic))
                        result = True
                        return

                    job = self.get_new_job(row_id, action)
                    if not job:
                        result = False
                        return
                    hdr = OrchHeader(job.job_type, job.jobid)
                    custnode.jobid = job.jobid
                    custnode.save(self.dbh)
                    msg = NtfyOnboardingMsg(hdr, self.topic, action.lower(), row_id, panorama_job_id,
                                            self.get_e2e_rastro_tag())
                    jsonmsg = self.to_json(msg)
                    self.sqs.send_msg(self.next_q, jsonmsg)
                    result = job.jobid
                    return

                elif (table == "cust_topology_license" or
                      table == "cust_topology_region" or table == "cust_topology_inbound_access"):
                    job = self.get_new_job(row_id, action)
                    if not job:
                        result = False
                        return

                    hdr = OrchHeader(job.job_type, job.jobid)
                    custnode.jobid = job.jobid
                    custnode.save(self.dbh)
                    msg = NtfyOnboardingMsg(hdr, self.topic, "cust_topology_update", row_id, panorama_job_id,
                                            self.get_e2e_rastro_tag())
                    jsonmsg = self.to_json(msg)
                    self.sqs.send_msg(self.next_q, jsonmsg)
                    result = job.jobid
                    return
            else:
                self.logger.info("Topic %s: Ignoring action %s" % (self.topic, action.lower()))

            result = True

        except Exception as E:
            if custnode_id and tenant_id and panorama_job_id:
                lret = save_orch_failed_state_to_commit_status_detail_table(self.dbh,
                                                                            tenant_id,
                                                                            panorama_job_id,
                                                                            custnode_id)
                if lret != True:
                    self.logger.error("Failed to update the commit job status table for tenant id %s and cust node "
                                      "id %s, Panorama job id %s"
                                      % (str(self.avctx.tenant_id), str(custnode_id), str(panorama_job_id)))
            result = False
            self.err_msg = ("Notification handling Failed with Exception %s. Traceback %s" % (str(E.args),
                                                                                              str(traceback.format_exc())))
            self.logger.error(self.err_msg)
        finally:
            self.avctx.set_ctx(panorama_job_id=panorama_job_id,
                               trace_id=self.logger.get_enhanced_traceid() if not None else self.logger.uuid,
                               tenant_id=tenant_id)
            if result == False:
                self.avctx.set_ctx(metric_type=NOTIFICATION_STATUS_FAILED,
                                   metric_severity=METRIC_SEVERITY_CRITICAL,
                                   metric_state=self.err_msg)
            else:
                self.avctx.set_ctx(metric_type=NOTIFICATION_STATUS_SUCCESS,
                                   metric_severity=METRIC_SEVERITY_INFO,
                                   metric_state="Notification Successful")

            # Publish the message to the Service.
            resp = self.avctx.publish_event()
            self.logger.info("Avisar Service publish event response: %s" % str(resp))

            if (result > 0) and (custnode_id and self.avctx.tenant_id and panorama_job_id):
                lret = save_orch_started_state_to_commit_status_detail_table(self.dbh,
                                                                             self.avctx.tenant_id,
                                                                             panorama_job_id,
                                                                             custnode_id)
                if lret != True:
                    self.logger.error("Failed to update the commit job status table for tenant id %s and cust node "
                                      "id %s, Panorama job id %s"
                                      % (str(self.avctx.tenant_id), str(custnode_id), str(panorama_job_id)))
            return result


class CustomerTopic(TopicHdlr):
    # Value of Table could also be different for different updates in same table
    def action_handler(self, action, row_id, table, panorama_job_id=None, cloud_provider=None, compute_region_id=None, node_type=None, cnat_alert_metrics=None):
        return True


class InstanceReplaceTopic(TopicHdlr):
    # Value of Table could also be different for different updates in same table
    def get_e2e_rastro_tag(self):
        # e2e is end to end :)
        my_e2e_rastro_tag = self.logger.get_enhanced_log_tag()
        if my_e2e_rastro_tag:
            return self.logger.get_updated_log_tag(my_e2e_rastro_tag)
        else:
            return None

    def action_handler(self, action, row_id, table, panorama_job_id=None, cloud_provider=None, compute_region_id=None, node_type=None, cnat_alert_metrics=None):
        res = self.dbh.get_instance_id_from_upgrade_clusterid(row_id)
        if not res.ok:
            error = ("Unable to find instance with cluster Id %s for replacement" % str(row_id))
            self.logger.error(error)
            return False
        instanceid = res.result[0]
        instance = InstanceModel(instanceid, self.dbh)
        if (instance.get_param("id") == None):
            self.logger.error("Unable to find instance with Id %s for replacement" % str(instanceid))
            return False
        if action.lower() == "update":
            job = self.get_new_job(instanceid, action)
            if not job:
                error = "Unable to create a job for instance replacement id %s" % str(instanceid)
                self.logger.error(error)
                return False
            ret = self.dbh.get_type_name_from_id(instance.get_param("node_type"))
            if not ret.ok:
                self.logger.error("Unable to find name for instance type %s" % str(instance.get_param("node_type")))
                return False
            nodetype = ret.result[0]
            if nodetype != "FIREWALL" and nodetype != "SERVICECONNECTION" and nodetype != "CLEANPIPE":
                self.logger.error(
                    "Ignoring repalce request (type = %s) as its not for FW or SVC CONN or CLEANPIPE" % str(nodetype))
                return True
            ret = instance.set_upgrade_jobid(self.dbh, job.jobid)

            if not ret:
                error = ("Upgrade Job id is not valid")
                self.logger.error(error)
                return False

            hdr = OrchHeader(job.job_type, job.jobid)
            msg = NtfyOnboardingMsg(hdr, self.topic, "update", instanceid, log_tag=self.get_e2e_rastro_tag())
            jsonmsg = self.to_json(msg)
            self.sqs.send_msg(self.next_q, jsonmsg)
            return True
        else:
            self.logger.error("Invalid action  %s" % action)
            return False

class OrchestrationEventTopic(TopicHdlr):
    '''
    Handle generic events pushed to the orchestrator
    Supported event types:
        * INSTANCE_CREATE
        * INSTANCE_DELETE
        * INSTANCE_UPDATE
    '''
    def get_e2e_rastro_tag(self):
        '''
        e2e is end to end :)
        '''
        my_e2e_rastro_tag = self.logger.get_enhanced_log_tag()
        if my_e2e_rastro_tag:
            return self.logger.get_updated_log_tag(my_e2e_rastro_tag)
        else:
            return None

    def handle_msg(self, msg):
        '''
        Override the `handle_msg` from TopicHdlr since we're decoupling events
        from a notion of table and rowid based trigger
        '''
        ret = False
        try:
            # Reuse or create a new rastro tag.
            if 'rastro_log_tag' in msg.keys():
                rastro_tag = msg['rastro_log_tag']
                try:
                    rastro_tag = ast.literal_eval(base64.b64decode(rastro_tag).decode('utf-8'))
                    self.logger.set_enhanced_log_tag(rastro_tag)
                except Exception as E:
                    self.logger.error("Failed to decode rastro tag Error: %s, %s" % (str(E.args),
                        str(traceback.format_exc())))
        except Exception as ex:
            self.logger.error("Invalid Message structure, ****Possible bug******: %s" % (str(ex.args)))
            self.logger.reset_enhanced_log_tag()
            return ret

        try:
            res = self.action_handler(msg)
            if not res:
                status = -1
            else:
                status = res

            if status:
                ret = True
            else:
                ret = False
        except Exception as ex:
            self.logger.error("Failed to handle message: Exception %s" % str(ex))
        finally:
            self.logger.reset_enhanced_log_tag()
            return ret

    def action_handler(self, event):
        '''
        Pass the event to the orchestrator_q.fifo
        The generic QueueHandler requires events to be 'JSON Pickled' objects.
        Not changing this right now, but this NEEDS to change. When we break the Orchestrator
        to individual services, a protobuf serialized event makes more sense.
        '''
        result = False
        try:
            tenant_id = event.get('tenant_id', 0)
            event_type = event.get('event_type', '')
            region_id = event.get('region', '')
            panorama_job_id = event.get('panorama_job_id', 0)
            if tenant_id == 0 or event_type == '' or region_id == '':
                err_msg = ("Event %s is incomplete." % (event))
                self.logger.error(err_msg)
                raise Exception(err_msg)
            job = self.get_new_job(tenant_id, event_type)
            if not job:
                err_msg = ("Failed to create job")
                self.logger.error(err_msg)
                raise Exception(err_msg)
            hdr = OrchHeader(job.job_type, job.jobid)
            msg = OrchestrationEventsMsg(hdr, self.topic, event_type, event,
                    tenant_id, panorama_job_id, self.get_e2e_rastro_tag())
            json_msg = self.to_json(msg)
            self.sqs.send_msg(self.next_q, json_msg)
            result = True
        except Exception as e:
            # TODO: Save failure somewhere?
            self.logger.error("Orchestration Event handler failed with %s. Traceback %s" % (
                str(e.args), str(traceback.format_exc())))
        finally:
            return result
