import logging
import logging.handlers
import time
from libs.msg_defs.handler import *
from libs.msg_defs.interface import *
from libs.msg_defs.workflows import *
import libs.model.custmodel as CS
import libs.model.instancemodel as INST
import libs.model.orchjobs as JOB
from libs.common.utils import prepare_job_status_msg

class CustomerHandler(GenericHandler):
    status = "CUSTOMER"
    def __init__(self, cfg):
        self.db_h = None
        self.qname = 'customer_q.fifo'
        # Init the logging queue
        super(CustomerHandler, self).init_generic_logger_rastro(qname=self.status.lower())
        self.sqs = SqsApi(cfg['region'], cfg['queues'], self.logger)
        self.valid_msgs = (CustomerChangeMsg,)
        self.ids = {}
        self.running = True
    
    def process_msg (self, msg, workflow, jobid, result):
        job = JOB.OrchJobs(jobid = jobid, dbh = self.db_h)
        self.logger.info ("Customer handler process_message called")
        job.status_msg = prepare_job_status_msg("Customer handler process_message called")
        if not job.jobid:
            self.error = ("CustomerHandler:process_msg():Unable to find job"
                " with id %d" % (jobid,))
            self.logger.error(self.error)
            return False
        job.status = self.status    
        job.status_msg = prepare_job_status_msg("Starting new customer provisioning")
        job.save_job(self.db_h)
        
        # CUSTOMER_ADD Workflow
        if workflow == 'CUSTOMER_ADD' or workflow == 'CUSTOMER_UPDATE':
            self.logger.info ("Orchestrator does not support CUSTOMER_ADD/CUSTOMER_UPDATE  workflow.")
            return False

        elif workflow == "TIMER_CB":
            self.logger.info("Customer_Q:Received message from TIMER %s" % (msg,))
            custid = msg.custid
            job.status_msg = prepare_job_status_msg("Received timer callback for instances %s" %
                    (str(msg.nodes),))
            job.save_job(self.db_h)
            failed = 0
            
            cust = CS.CustomerModel(custid=msg.custid, dbh=self.db_h)
            if not cust.get_param("id"):
                status_msg = "Customer with id %s not found" % msg.custid
                job.status_msg = prepare_job_status_msg(status_msg)
                self.error = status_msg
                self.logger.error(status_msg)
                job.save_job(self.db_h)
                return False
            
            self.error = ""
            if (True):
                for node in msg.nodes:
                    instance = INST.InstanceModel(node, self.db_h)
                    if instance.get_param("id"):
                        instance.set_param("content_version", cust.get_param("contentver"))
                        if not instance.save():
                            self.error += "Unable to update the content version for instance %s\n" % str(instance.get_param("name"))
                            self.error += self.db_h.error
                            status_msg = "content update for instance %s failed" % str(instance.get_param("name"))
                            failed = 1
                        else:
                            status_msg = "Updated content for instance %s:"% str(instance.get_param("name"))
                        job.status_msg = prepare_job_status_msg(status_msg)
                        job.save_job(self.db_h)
                    else:
                        status_msg = ("Content Update:Unable to get instance with id %d" 
                                % node)
                        failed = 1
                        job.status_msg = prepare_job_status_msg(status_msg)
                        self.error = status_msg
                        self.logger.error(status_msg)
                        job.save_job(self.db_h)
                #END LOOP
                if failed:
                    return False
                else:
                    return True
            else:
                self.error = ("Customer timer callback: Type %s not supported" % 
                    str(msg.type))
                self.logger.error("Customer timer callback: Type %s not supported" % 
                    str(msg.type))
                return False
        else:
            self.logger.error("Workflow %s not handled by CustometHandler" % (workflow,))
            self.error = ("Workflow %s not handled by CustometHandler" % (workflow,))
            return False
