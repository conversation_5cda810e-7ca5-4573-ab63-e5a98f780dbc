#!/usr/bin/python3
import logging
import logging.handlers
import threading
from libs.comm.sqsapi import SqsApi
from libs.common.shared.orch_utils import OrchState
from notification_service.core.notificationapi import NotificationApi
from notification_service.core.customer import CustomerHandler
from libs.cfg import *
import sys
import signal
import time
import traceback
notification = None
running = True
import subprocess
import boto3
import requests
from base64 import b64decode
import libs.db.dbhandle as DB
import os

class ThreadHandle():
    handle = None
    t_handle = None
    def __init__(self):
        pass

class NotificationModel():
    def __init__(self):
        self.sqs = None
        self.ntfy_q = ThreadHandle() # Notification Q
        self.cust_q = ThreadHandle() # Customer Q

        self.ntfy_q.handle = NotificationApi(cfg)
        self.cust_q.handle = CustomerHandler(cfg)
        
        self.ntfy_q.t_handle = threading.Thread(name="Notification<PERSON><PERSON>", 
            target=thread_handler, args = (self.ntfy_q.handle,), daemon=True)
        self.cust_q.t_handle = threading.Thread(name="CustomerHandler", 
                target=thread_handler, args = (self.cust_q.handle,), daemon=True)

        self.threads = {
            'ntfy_q_thread': self.ntfy_q.t_handle,
            'cust_q_thread': self.cust_q.t_handle,
        }
    def start_threads(self):
        self.ntfy_q.t_handle.start() 
        self.cust_q.t_handle.start() 
    
    def stop_threads(self):
        self.ntfy_q.handle.running = False
        self.cust_q.handle.running = False

def thread_handler(thread_h):
    try:
        print(("Starting sqs queue thread handler for %s " % str(thread_h.qname)))
        thread_h.init_db()
        thread_h.handle()
    except Exception as E:
        print("Fatal! Exiting all the threads and re-strating process due to exception %s, traceback: %s" %
              (str(E.args), str(traceback.format_exc())))
        sys.exit(-1)

def signal_handler(signal, frame):
    global notification
    global running 
    print(f"Received Signal {signum}: {signal_number_to_name(signum)}")
    for th in threading.enumerate():
        print(th)
        #traceback.print_stack(sys._current_frames()[th.ident])
        #print()
    if notification:
        notification.stop_threads()
    notification = None
    running = False

def copy_orchcfg_to_db(logger):
    dbh = DB.DbHandle(logger)
    cursor = dbh.get_cursor(dbh)

    withheld_keys = [
        'dbpassword',
        'aws_access_key_id',
        'aws_secret_access_key'
    ]
    for key, value in list(cfg.items()):
        if key in withheld_keys:
            logger.info(f"Do not insert {key} in to orch_cfg table")
            continue
        if isinstance(value, (str, dict, bool, list)):
            logger.info("Inserting orch_cfg setting into DB: %s" % key)
            params = (key, str(value), str(value))
            sql = ("insert into orch_cfg (name, value) values (%s, %s) on duplicate key update value=%s")
            try:
                cursor.execute(sql, params)
            except Exception as ex:
                logger.error("CustomerModel:save(): Update query failed: %s" % (str(ex),))

    dbh.cursorclose(cursor)

# Function to check if any thread is not alive
def any_thread_not_alive(threads):
    for thread_name, thread in threads.items():
        if not thread.is_alive():
            print(f"Thread {thread_name} is not alive!")
            return True
        else:
            print(f"Thread {thread_name} is alive!")
    return False

def main():
    global notification
    global running
    log_root_dir="/var/log/pan/"
    threads = {}
    try:
        if 'log-root-dir' in cfg:
            log_root_dir=cfg['log-root-dir']
        else:
            cfg['log-root-dir']=log_root_dir

        cmd = []
        cmd.append("mkdir")
        cmd.append("-p")
        cmd.append(log_root_dir)
        output = subprocess.check_output(cmd)
        print(("%s: %s" % (log_root_dir, str(output))))
    except Exception as ex:
        print(("Failed to start Orchestrator: %s" % str(ex)))
        return
    
    filename = log_root_dir+"/main.log"
    handler = logging.handlers.RotatingFileHandler(filename, maxBytes=10485760, backupCount=1)
    logger = logging.getLogger("main")
    logger.setLevel(logging.DEBUG)
    logger.addHandler(handler)
    logger.info("Starting....")
    logger.info (str(cfg))
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    orch_state = OrchState(logger)
    prev_orch_state = orch_state.NOT_INITIALIZED
    while (running):
        cur_orch_state = orch_state.get_orch_current_state()
        if prev_orch_state != cur_orch_state:
            logger.info("Orchestrator state changed from %s to %s" % (prev_orch_state, cur_orch_state))
            if cur_orch_state == orch_state.ACTIVE:
                try:
                    notification = NotificationModel()
                    threads = notification.threads
                    if cfg.get('dbpassword_decrypted', False) == False:
                        ENCRYPTED = cfg['dbpassword']
                        password = boto3.client('kms', cfg['region']).decrypt(CiphertextBlob=b64decode(ENCRYPTED))['Plaintext']
                        cfg['dbpassword'] = password.decode("utf-8")
                        cfg['dbpassword_decrypted']=True
                except Exception as ex:
                    logger.error("Unable to decrypt database password: %s" % str(ex))
                    return

                # TODO: Move this to a single execution layer during upgrade.
                #copy_orchcfg_to_db(logger)
                notification.start_threads()
            else:
                if notification:
                    notification.stop_threads()
                    notification = None
        prev_orch_state = cur_orch_state

        # Periodically check if threads are alive
        if running == True and cur_orch_state == orch_state.ACTIVE and any_thread_not_alive(threads):
            print("At least one thread that should be running is not running. Exiting...")
            sys.exit(-1)

        logger.debug("Back in main(notification_service), current orch_state %s" % (cur_orch_state))
        time.sleep(5)


if __name__ == "__main__":
    main()
