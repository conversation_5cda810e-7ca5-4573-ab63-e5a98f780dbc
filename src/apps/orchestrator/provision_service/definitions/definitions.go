package definitions

type ProtocolInfoProps struct {
	IpProtocol string   `json:"ip_protocol"`
	Ports      []string `json:"ports"` // each element must be either an integer string or a range string
}

type SecondaryIpRange struct {
	RangeName   string `json:"range_name"`
	IpCidrRange string `json:"ip_cidr_range"`
}

type FirewallRuleProps struct {
	Name         string               `json:"name"`
	Priority     string               `json:"priority"` // must be an integer string between 0 to 65535, both inclusive
	SourceRanges []string             `json:"source_ranges"`
	Allow        bool                 `json:"allow"` // true: Allow, false: Deny
	TargetTags   []string             `json:"target_tags"`
	ProtocolInfo []*ProtocolInfoProps `json:"protocol_info"`
}

type SubnetProps struct {
	Name                    string              `json:"name"`
	Region                  string              `json:"region"`
	IpRange                 string              `json:"ip_range"`
	NameServers             []string            `json:"name_servers"`
	PrivateIpProviderAccess bool                `json:"private_ip_provider_access"`
	StackType               string              `json:"stack_type"`
	Ipv6AccessType          string              `json:"ipv6_access_type"`
	ExternalIpv6Prefix      string              `json:"external_ipv6_prefix"`
	SecondaryIpRanges       []*SecondaryIpRange `json:"secondary_ip_ranges"`
}

type NetworkProps struct {
	Name            string               `json:"name"`
	Subnets         []*SubnetProps       `json:"subnets"`
	FirewallRules   []*FirewallRuleProps `json:"firewall_rules"`
	ExternalNetwork string               `json:"external_network"`
}

// gcp_provision_type possible values
const (
	GcpProvisionTypeDeploymentManager     = "deployment_manager"
	GcpProvisionTypeImport                = "terraform_import"
	GcpProvisionTypeMigration             = "terraform_migration"
	GcpProvisionTypeProvision             = "terraform_provision"
	GcpProvisionTypeRollback              = "terraform_rollback"
	GcpProvisionTypeInfrastructureManager = "infrastructure_manager"
)

type OciNetworkProps struct {
	VcnCidrBlock           string `json:"vcn_cidr_block"`
	HaSubnetCidrBlock      string `json:"ha_subnet_cidr_block"`
	DpSubnetCidrBlock      string `json:"dp_subnet_cidr_block"`
	EpLbaasSubnetCidrBlock string `json:"ep_lbaas_subnet_cidr_block"`
	EpEnvoySubnetCidrBlock string `json:"ep_envoy_subnet_cidr_block"`
	EpExtSubnetCidrBlock   string `json:"ep_ext_subnet_cidr_block"`
}
