package utils

import (
	"bufio"
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"orchestrator/libs/common/shared/grpc/proto/avisarpb"
	"orchestrator/libs/go/avisar"
	"orchestrator/libs/go/dbaccess/sql"
	pconfig "orchestrator/provision_service/infra/config"
	"orchestrator/provision_service/infra/db"
	"os"
	"reflect"
	"regexp"
	"strings"
	"time"

	"go.panw.local/provision/deployment"

	"github.com/oracle/oci-go-sdk/v65/common"
	"github.com/oracle/oci-go-sdk/v65/core"
	"go.panw.local/pangolin/clogger"
)

const (
	// ociAuthCredentialsConfig string = "/orch_aas/libs/oci_credentials.json"
	ociAuthCredentialsConfig string = "/orch_aas/libs/oci_credentials.cfg"
	//ociAuthCredentialsConfig string = "~/secrets/oci_config"
)

// EmptyValue returns true if a string is empty string, 'none', 'null', case-insensitive.
// This is typically used to examine the value read from RDS.
func EmptyValue(data string) bool {
	toLower := strings.ToLower(data)
	return toLower == "" || toLower == "none" || toLower == "null"
}

// CleanDirectory removes all files and directories in the specified path and creates a new directory with the same path.
func CleanDirectory(path string, perm os.FileMode) error {
	err := os.RemoveAll(path)
	if err != nil {
		return err
	}
	return os.MkdirAll(path, perm)
}

// UncompressBase64 first base64 decode the input and then gunzip the decoded value.
func UncompressBase64String(data string) ([]byte, error) {
	decoded, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		return nil, fmt.Errorf("failed base64 decode: %w", err)
	}

	zr, err := gzip.NewReader(bytes.NewBuffer(decoded))
	if err != nil {
		return nil, fmt.Errorf("failed gzip reader creation: %w", err)
	}

	output, err := ioutil.ReadAll(zr)
	if err != nil {
		return nil, fmt.Errorf("failed gzip reader readall: %w", err)
	}

	return output, nil
}

// ValidateInputNotEmpty validates that any field of the input does not contain the zero value
// unless it is explicitly specified in the emptyAllowed set. The input parameter kind must either be a struct
// or a pointer to a struct.
func ValidateInputNotEmpty(input interface{}, emptyAllowed map[string]bool) error {
	val := reflect.ValueOf(input)
	if val.Kind() == reflect.Pointer {
		val = val.Elem()
	}

	typ := val.Type()
	for i := 0; i < typ.NumField(); i++ {
		name := typ.Field(i).Name
		jsonField := typ.Field(i).Tag.Get("json")
		if val.FieldByName(name).IsZero() && (emptyAllowed == nil || !emptyAllowed[name]) {
			return fmt.Errorf("input '%v' cannot be empty", jsonField)
		}
	}

	return nil
}

// ValidateInputMustEmpty validates that any field of the input contains the zero value
// unless it is explicitly specified in the nonEmptyAllowed set. The input parameter kind must either be a struct
// or a pointer to a struct.
func ValidateInputMustEmpty(input interface{}, nonEmptyAllowed map[string]bool) error {
	val := reflect.ValueOf(input)
	if val.Kind() == reflect.Pointer {
		val = val.Elem()
	}

	typ := val.Type()
	for i := 0; i < typ.NumField(); i++ {
		name := typ.Field(i).Name
		jsonField := typ.Field(i).Tag.Get("json")
		if !val.FieldByName(name).IsZero() && (nonEmptyAllowed == nil || !nonEmptyAllowed[name]) {
			return fmt.Errorf("input '%v' must be empty", jsonField)
		}
	}

	return nil
}

// StripSurroundingQuotes removes the surrounding quotes of a string.
func StripSurroundingQuotes(input string) string {
	if l := len(input); l >= 2 && input[0] == '"' && input[l-1] == '"' {
		return input[1 : l-1]
	}
	return input
}

// getRpmBuckets returns the S3/GCS bucket names - if configured - for the VMs to use as a cache.
// If multiple buckets exist for the cloud provider, then we return a string with all the bucket
// names appended with comma as a delimiter.
func getRpmBuckets(name string, cloudProvider string, eLog *clogger.EventLogger) (string, error) {
	dbAccessor := db.GetDbAccessor()
	row, err := dbAccessor.GetOrchCfgByName(name, eLog)
	if errors.Is(err, sql.ErrNoRows) {
		return "", nil
	} else if err != nil {
		eLog.LogError("Error reading %v: %v", name, err)
		return "", err
	}
	val := row.Value.String()
	// ensure the JSON string uses double quotes instead single quotes as observed in some environments
	val = strings.ReplaceAll(val, "'", "\"")

	var data map[string][]map[string]string
	err = json.Unmarshal([]byte(val), &data)
	if err != nil {
		eLog.LogError("Error unmarshaling %v: %v, val=%v", name, err, val)
		return "", err
	}

	if cloudProviderBuckets, exist := data[cloudProvider]; !exist {
		return "", nil
	} else {
		var output []string
		for _, bucket := range cloudProviderBuckets {
			if bucketName, exist := bucket["bucket_name"]; exist {
				output = append(output, bucketName)
			}
		}
		return strings.Join(output, ","), nil
	}
}

// GetContentRpmBuckets returns the S3/GCS bucket names - if configured - for the VMs to use as a content cache.
// If multiple buckets exist for the cloud provider, then we return a string with all the bucket
// names appended with comma as a delimiter.
func GetContentRpmBuckets(cloudProvider string, eLog *clogger.EventLogger) (string, error) {
	return getRpmBuckets("content_rpm_buckets", cloudProvider, eLog)
}

// GetAvRpmBuckets returns the S3/GCS bucket names - if configured - for the VMs to use as an AV cache
// If multiple buckets exist for the cloud provider, then we return a string with all the bucket
// names appended with comma as a delimiter.
func GetAvRpmBuckets(cloudProvider string, eLog *clogger.EventLogger) (string, error) {
	return getRpmBuckets("av_rpm_buckets", cloudProvider, eLog)
}

// GetTerraformVersion returns the Terraform version to use.
// It looks into configuration for the version to use for the cloud provider. If none specified,
// then it returns an empty string signifying using the latest.
func GetTerraformVersion(cloudProvider string) string {
	cfg := pconfig.GetConfig()
	// Terraform version setting is enabled for GCP only
	if cloudProvider == "gcp" {
		return cfg.ProvisionService.GcpDeployConfig.TerraformVersion
	} else if cloudProvider == "oci" {
		return cfg.ProvisionService.OciDeployConfig.TerraformVersion
	} else {
		return ""
	}
}

func GetGoogleProviderVersion() string {
	cfg := pconfig.GetConfig()
	return cfg.ProvisionService.GoogleProviderVersion
}

// RemoveLineWithString reads in a file and removes any lines that contain the given string, return the leftover content.
// If inPlace is set, it overwrites the file in-place.
func RemoveLineWithString(filePath, str string, inPlace bool) (buf *bytes.Buffer, err error) {
	// Defer a function to recover from panic such as ErrTooLarge
	defer func() {
		if r := recover(); r != nil {
			buf = nil
			err = fmt.Errorf("panic occurred while processing '%s': %v", filePath, r)
		}
	}()

	// Open the file for reading and writing
	finfo, err := os.Stat(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to stat file '%s': %w", filePath, err)
	}
	f, err := os.OpenFile(filePath, os.O_RDWR, finfo.Mode())
	if err != nil {
		return nil, fmt.Errorf("failed to open file '%s': %w", filePath, err)
	}
	defer f.Close()

	// Read the original file line by line
	var bs []byte
	buf = bytes.NewBuffer(bs)
	scanner := bufio.NewScanner(f)
	scannerBuf := make([]byte, 0, finfo.Size())
	scanner.Buffer(scannerBuf, int(finfo.Size()))
	for scanner.Scan() {
		line := scanner.Text()
		// Skip lines containing "credentials.json"
		if !strings.Contains(line, str) {
			// Write the line to the temporary file
			buf.WriteString(line + "\n")
		}
	}

	if inPlace {
		err = f.Truncate(0)
		if err != nil {
			return nil, fmt.Errorf("failed to truncate file '%s': %w", filePath, err)
		}
		_, err = f.Seek(0, 0)
		if err != nil {
			return nil, fmt.Errorf("failed to seek in file '%s': %w", filePath, err)
		}
		_, err = buf.WriteTo(f)
		if err != nil {
			return nil, fmt.Errorf("failed to overwrite file '%s': %w", filePath, err)
		}
		err = f.Sync()
		if err != nil {
			return nil, fmt.Errorf("failed to sync file '%s': %w", filePath, err)
		}
	}
	return buf, nil
}

func HttpCall(eLog *clogger.EventLogger, client *http.Client, method, endpoint string, jsonPayload []byte) error {
	var err error
	var req *http.Request

	eLog.LogDebug("method=%s, endpoint=%s, payload=%s", method, endpoint, string(jsonPayload))

	if jsonPayload != nil {
		req, err = http.NewRequest(method, endpoint, bytes.NewBuffer(jsonPayload))
	} else {
		req, err = http.NewRequest(method, endpoint, nil)
	}
	if err != nil {
		eLog.LogError("Failed to prepare request: %v, payload=%s", err, jsonPayload)
		return fmt.Errorf("failed to prepare request")
	}

	if jsonPayload != nil {
		req.Header.Set("Content-Type", "application/json")
	}
	resp, err := client.Do(req)
	if err != nil {
		eLog.LogError("Failed to initiate request: %v, payload=%s", err, jsonPayload)
		return fmt.Errorf("failed to initiate import request")
	}
	defer resp.Body.Close()
	if status := resp.StatusCode; status != http.StatusOK {
		eLog.LogError("Failed HTTP call, status=%v", status)
		respBody, err := io.ReadAll(resp.Body)
		if err != nil {
			eLog.LogError("Failed to read response: %v", err)
		} else {
			eLog.LogError("Response from failed request: %s", respBody)
		}
		return fmt.Errorf("call failed with status code %d", status)
	}

	return nil
}

func GetPublicIPOCID(publicIP string, regionName string) (*string, error) {
	if publicIP == "" {
		return nil, nil
	}

	maxRetries := 5
	sleepPeriod := 2 * time.Second

	for attempt := 0; attempt <= maxRetries; attempt++ {
		// Set up the configuration
		configProvider := common.CustomProfileConfigProvider(ociAuthCredentialsConfig, "DEFAULT")

		// Create a new virtual network client using the configuration provider
		c, err := core.NewVirtualNetworkClientWithConfigurationProvider(configProvider)
		if err != nil {
			return nil, fmt.Errorf("GetPublicIPOCID: Failed to create virtual network client to fetch public ip ocid from oci %v", err)
		}

		c.SetRegion(regionName)
		req := core.GetPublicIpByIpAddressRequest{
			GetPublicIpByIpAddressDetails: core.GetPublicIpByIpAddressDetails{
				IpAddress: common.String(publicIP),
			},
		}

		r, err := c.GetPublicIpByIpAddress(context.Background(), req)
		if err != nil {
			if attempt == maxRetries {
				return nil, fmt.Errorf("GetPublicIPOCID: Failed to fetch public ip ocid from oci after %d attempts: %v", maxRetries+1, err)
			}
			time.Sleep(sleepPeriod)
			continue
		}
		return r.Id, nil
	}
	return nil, fmt.Errorf("GetPublicIPOCID: Unexpected end of retry loop when trying to fetch Public IP OCID from OCI")
}

// GetPrivateIPOCID gets the OCID of a private IP by its IP address and subnet OCID
func GetPrivateIPOCID(privateIP, subnetOCID, regionName string) (string, error) {
	if privateIP == "" || subnetOCID == "" {
		return "", nil
	}

	// Set up the configuration
	configProvider := common.CustomProfileConfigProvider(ociAuthCredentialsConfig, "DEFAULT")

	// Create a new virtual network client
	c, err := core.NewVirtualNetworkClientWithConfigurationProvider(configProvider)
	if err != nil {
		return "", fmt.Errorf("Failed to create virtual network client: %v", err)
	}
	c.SetRegion(regionName)

	// List private IPs in the subnet
	req := core.ListPrivateIpsRequest{
		SubnetId:  common.String(subnetOCID),
		IpAddress: common.String(privateIP),
	}

	resp, err := c.ListPrivateIps(context.Background(), req)
	if err != nil {
		return "", fmt.Errorf("Failed to list private IPs: %v", err)
	}

	if len(resp.Items) > 0 {
		return *resp.Items[0].Id, nil
	}

	return "", nil
}

// UpdatePublicIPAssignment associates a public IP with a private IP
func UpdatePublicIPAssignment(publicIPOCID, privateIPOCID, regionName string) error {
	if publicIPOCID == "" || privateIPOCID == "" {
		return fmt.Errorf("Public IP OCID or Private IP OCID is empty")
	}

	// Set up the configuration
	configProvider := common.CustomProfileConfigProvider(ociAuthCredentialsConfig, "DEFAULT")

	// Create a new virtual network client
	c, err := core.NewVirtualNetworkClientWithConfigurationProvider(configProvider)
	if err != nil {
		return fmt.Errorf("Failed to create virtual network client: %v", err)
	}
	c.SetRegion(regionName)

	// Check if the private IP already has a public IP associated
	var compartment string

	compartment = pconfig.GetConfig().OciPaTenantsCompartmentId
	// List public IPs in the region
	listPublicIPsReq := core.ListPublicIpsRequest{
		Scope:         core.ListPublicIpsScopeRegion,
		CompartmentId: &compartment,
	}

	publicIPsResp, err := c.ListPublicIps(context.Background(), listPublicIPsReq)
	if err != nil {
		return fmt.Errorf("Failed to list public IPs: %v", err)
	}

	// Find any public IPs assigned to this private IP
	for _, publicIP := range publicIPsResp.Items {
		if publicIP.AssignedEntityId != nil && *publicIP.AssignedEntityId == privateIPOCID &&
			publicIP.Id != nil && *publicIP.Id != publicIPOCID {
			// Unassign the current public IP
			updateReq := core.UpdatePublicIpRequest{
				PublicIpId: publicIP.Id,
				UpdatePublicIpDetails: core.UpdatePublicIpDetails{
					// Setting to nil will unassign it
					PrivateIpId: nil,
				},
			}

			_, err = c.UpdatePublicIp(context.Background(), updateReq)
			if err != nil {
				return fmt.Errorf("Failed to unassign existing public IP %s: %v", *publicIP.Id, err)
			}
			break
		}
	}

	// Update public IP
	updateReq := core.UpdatePublicIpRequest{
		PublicIpId: common.String(publicIPOCID),
		UpdatePublicIpDetails: core.UpdatePublicIpDetails{
			PrivateIpId: common.String(privateIPOCID),
		},
	}

	_, err = c.UpdatePublicIp(context.Background(), updateReq)
	if err != nil {
		return fmt.Errorf("Failed to update public IP assignment: %v", err)
	}

	return nil
}

type AvisarMetric struct {
	Type  string
	State string
}

func ErrorsToAvisarMetrics(cloudProvider deployment.SupportedCloudProvider, errors []error) []AvisarMetric {
	var metrics []AvisarMetric
	switch cloudProvider {
	case deployment.GCP:
		// currently we have following finder-grain, cause-based alarms (PROM-504):
		// - INST_MGMT_TEMPLATE_PARSING_ERROR: config parsing error
		// - INST_MGMT_BRINGUP_GCP_IP_REUSE_CYCLIC_ERROR: due to rapid IP reuse
		// - INST_MGMT_BRINGUP_GCP_IP_CONFLICT
		// - INSTMGMT_BRINGUP_GCP_ZONE_CAPACITY_EXCEEDED
		// - INST_MGMT_BRINGUP_GCP_QUOTA_EXCEEDED
		// - INSTMGMT_BRINGUP_GCP_NETWORK_UNAVAILABLE
		// - INST_MGMT_BRINGUP_GCP_NETWORK_SUBNET_UNAVAILABLE
		// - INST_MGMT_BRINGUP_GCP_RESOURCE_DEPENDENCY_ERROR
		// - INST_MGMT_BRINGUP_TERRAFORM_SERVER_ERROR: due to errors on the Terraform server side
		for _, err := range errors {
			eStr := err.Error()
			extIpInUseRegex := regexp.MustCompile(`External IP address: \d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3} is already in-use`)
			zoneCapReachedRegex := regexp.MustCompile(`The zone.*does not have enough resources available to fulfill the request`)
			quotaExceededRegex := regexp.MustCompile(`Quota '[^']+' exceeded`)
			subnetUnavailableRegex := regexp.MustCompile(`The resource 'projects/[^/]+/regions/[^/]+/subnetworks/[^']+' was not found`)
			rsrcDependErrRegex := regexp.MustCompile(`'.+'\s+is already being used|.*resourceInUseByAnotherResource`)
			if strings.Contains(eStr, "Error: Provider configuration not present") {
				metrics = append(metrics, AvisarMetric{Type: avisarpb.INST_MGMT_TEMPLATE_PARSING_ERROR, State: eStr})
			} else if strings.Contains(eStr, "Error creating instance") && extIpInUseRegex.MatchString(eStr) {
				metrics = append(metrics, AvisarMetric{Type: avisarpb.INST_MGMT_BRINGUP_GCP_IP_REUSE_CYCLIC_ERROR, State: eStr})
			} else if strings.Contains(eStr, "Error waiting to create Address") && strings.Contains(eStr, "Address is already allocated in another project") {
				metrics = append(metrics, AvisarMetric{Type: avisarpb.INST_MGMT_BRINGUP_GCP_IP_CONFLICT, State: eStr})
			} else if zoneCapReachedRegex.MatchString(eStr) {
				metrics = append(metrics, AvisarMetric{Type: avisarpb.INST_MGMT_BRINGUP_GCP_ZONE_CAPACITY_EXCEEDED, State: eStr})
			} else if quotaExceededRegex.MatchString(eStr) {
				metrics = append(metrics, AvisarMetric{Type: avisarpb.INST_MGMT_BRINGUP_GCP_QUOTA_EXCEEDED, State: eStr})
			} else if strings.Contains(eStr, "The referenced network resource cannot be found") {
				metrics = append(metrics, AvisarMetric{Type: avisarpb.INST_MGMT_BRINGUP_GCP_NETWORK_UNAVAILABLE, State: eStr})
			} else if subnetUnavailableRegex.MatchString(eStr) {
				metrics = append(metrics, AvisarMetric{Type: avisarpb.INST_MGMT_BRINGUP_GCP_NETWORK_SUBNET_UNAVAILABLE, State: eStr})
			} else if rsrcDependErrRegex.MatchString(eStr) {
				metrics = append(metrics, AvisarMetric{Type: avisarpb.INST_MGMT_BRINGUP_GCP_RESOURCE_DEPENDENCY_ERROR, State: eStr})
			} else if strings.Contains(eStr, "Error: Failed to request discovery document") {
				metrics = append(metrics, AvisarMetric{Type: avisarpb.INST_MGMT_BRINGUP_TERRAFORM_SERVER_ERROR, State: eStr})
			}
		}
	}
	return metrics
}

func PublishAvisarEvent(sender, tenantID, custID, nodeType, cloudProvider, regionID, regionName, projectID, tenantName, module, supTenantID, severity, action, stackName, metricType, metricState string, eLog *clogger.EventLogger) error {
	var err error
	if eLog == nil {
		return fmt.Errorf("PublishAvisarEvent: EventLogger is nil")
	}
	avisarCw := avisar.GetClientWrapper()
	if avisarCw == nil {
		eLog.LogError("PublishAvisarEvent: Failed to get Avisar client wrapper - returned nil")
		return fmt.Errorf("PublishAvisarEvent: Failed to get Avisar client wrapper")
	}
	eLog.LogInfo("PublishAvisarEvent: Publish event to Avisar: Avisar client wrapper -> %v", avisarCw)

	event := &avisarpb.Event{
		Sender: sender,
		//TraceId:       eventData["traceID"],
		TenantId:      tenantID,
		CustId:        custID,
		Timestamp:     uint64(time.Now().UnixMilli()),
		NodeType:      nodeType,
		CloudProvider: cloudProvider,
		RegionId:      regionID,
		RegionName:    regionName,
		StackName:     stackName,
		GcpProjectId:  projectID,
		AwsEnv:        pconfig.GetConfig().AwsEnv,
		TenantName:    tenantName,
		Module:        module,
		SupTenantId:   supTenantID,
		Severity:      severity,
		Action:        action,
		MetricType:    metricType,
		MetricState:   metricState,
	}

	sent, resp, err := avisarCw.SendEvent(event, eLog)
	if err != nil {
		eLog.LogError("PublishAvisarEvent: Failed to publish event to Avisar: %v, event=%#v", err, *event)
		return fmt.Errorf("PublishAvisarEvent: Failed to publish event to Avisar: %v", err)
	} else if sent {
		eLog.LogInfo("PublishAvisarEvent: Avisar event publish response: %v", resp)
	}
	return err
}
