package oci_deployment_config

import (
	"context"
	"fmt"
	"github.com/oracle/oci-go-sdk/v65/common"
	"github.com/oracle/oci-go-sdk/v65/core"
	"github.com/oracle/oci-go-sdk/v65/resourcemanager"
	"go.panw.local/pangolin/clogger"
	"go.panw.local/provision/deployment"
	"hash/fnv"
	"orchestrator/libs/go/terraform"
	"orchestrator/provision_service/api/deployment/definitions"
	pconfig "orchestrator/provision_service/infra/config"
	"orchestrator/provision_service/infra/tf"
	pterraform "orchestrator/provision_service/terraform"
	"orchestrator/provision_service/terraform/oci"
	putils "orchestrator/provision_service/utils"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"text/template"
)

const (
	templateFileName = "terraform.gotmpl"
	tfFileName       = "terraform.tfvars"
	credentials_file = "credentials.json"
)

func GetOrCreateOCICompartment(tfWrapper tf.TfWrapper, parentCompartmentId, tenantName string, eLog *clogger.EventLogger) (string, error) {
	// Get the ORM client from the TF wrapper
	ormClient := tfWrapper.GetORMClient()
	if ormClient == nil {
		return "", fmt.Errorf("ORM client is nil")
	}

	// Attempt to find the compartment by display name in the parent compartment
	compartmentId, err := ormClient.FindCompartmentByName(parentCompartmentId, tenantName)
	if err != nil {
		eLog.LogError("Error searching for compartment %s: %v", tenantName, err)
		return "", err
	}

	// If a compartment was found, return its ID
	if compartmentId != "" {
		eLog.LogInfo("Found existing compartment for %s with ID: %s", tenantName, compartmentId)
		return compartmentId, nil
	}

	// If compartment not found, create it
	eLog.LogInfo("Could not find tenant compartment for %s, creating", tenantName)
	compartmentId, err = ormClient.CreateCompartment(parentCompartmentId, tenantName)
	if err != nil {
		eLog.LogError("Error creating tenant compartment for %s: %v", tenantName, err)
		return "", err
	}
	eLog.LogInfo("Created new compartment with ID: %s", compartmentId)

	return compartmentId, nil
}

// Helper for generating unique id ints
func HashStringToInt64(s string) int64 {
	h := fnv.New64a()
	h.Write([]byte(s))
	return int64(h.Sum64())
}

// generateOciConfig generates OCI-specific Terraform configuration for deployment API
func GenerateOciConfig(cfg *deployment.Config, tfWrapper tf.TfWrapper, eLog *clogger.EventLogger, wsDir string, tplDir string, compartmentId string, jobData *definitions.DeploymentData) error {
	var err error

	// Make the provider directory path
	providerDir := filepath.Join(tplDir, string(cfg.CloudProvider), "instances")

	// Create or ensure workspace directory exists
	if err = putils.CleanDirectory(wsDir, 0750); err != nil {
		eLog.LogError("Error making workspace directory %v: %v", wsDir, err)
		return err
	}

	// Copy template files to workspace directory
	eLog.LogInfo("Copying from %v to %v", providerDir, wsDir)
	err = terraform.CpDir(providerDir, wsDir)
	if err != nil {
		eLog.LogError("error copying template directory %v", err)
		return err
	}

	// Prepare OCI provider parameters
	provider, err := oci.PrepareProviderParams(cfg.Region, eLog)
	if err != nil {
		eLog.LogError("Error preparing provider params: %v", err)
		return err
	}

	// Convert deployment VM instances to OCI format and prepare VM parameters
	clusterConfig, nlbs, natGws, _, err := prepareVmParamsFromDeployment(cfg, eLog, compartmentId)
	if err != nil {
		eLog.LogError("Error preparing vm instance params: %v", err)
		return err
	}

	// Prepare template arguments
	templArg := &oci.TfTemplateArgument{
		Provider:       provider,
		IdxScheme:      false,
		ClusterConfigs: clusterConfig,
		NlbConfigs:     nlbs,
		NatConfigs:     natGws,
	}

	// Produce terraform configuration files
	var goTempl *template.Template
	var tfFile *os.File
	goTempl, tfFile, err = pterraform.ParseGotmpl(providerDir, wsDir, templateFileName, tfFileName, eLog)
	if err != nil {
		eLog.LogError("Error parsing go template: %v", err)
		return err
	}
	err = goTempl.Execute(tfFile, templArg)
	if err != nil {
		eLog.LogError("Error processing template %v: %v", goTempl.Name(), err)
		return err
	}

	credentialSrc := filepath.Clean(tfWrapper.GetOciCredential())
	credentialDst := filepath.Join(wsDir, credentials_file)
	// A credential file is must for TF Call, if not present bail out.
	if err := terraform.File(credentialSrc, credentialDst); err != nil {
		eLog.LogError("Failed to write credential file : %s", err.Error())
		return err
	}

	// upload to S3
	if pconfig.GetConfig().ProvisionService.S3Upload {
		// don't upload credential file(s) to S3
		uploadSkip := []string{credentials_file}
		err = putils.WriteWorkspaceDirToS3(eLog, putils.TerraformPrefix, jobData.WorkspaceName, wsDir, uploadSkip)
		// If uploading to S3 fails, allow the job to continue but log an error
		if err != nil {
			eLog.LogError("Error uploading TF configuration to S3:%v", err)
		}
	}
	templateFilePath := filepath.Join(wsDir, templateFileName)
	err = os.Remove(templateFilePath)
	return nil
}

// Parses through received tags of the format "namespace.name=value"
func parseTags(Tags []string, eLog *clogger.EventLogger) []oci.TagData {
	definedTags := []oci.TagData{}

	for _, tag := range Tags {
		// Tags are in format "namespace.key=value"
		parts := strings.SplitN(tag, "=", 2)
		if len(parts) != 2 {
			eLog.LogError("Invalid tag format: %s", tag)
			continue
		}

		namespaceParts := strings.SplitN(parts[0], ".", 2)
		if len(namespaceParts) != 2 {
			eLog.LogError("Invalid tag namespace format: %s", parts[0])
			continue
		}

		namespace := namespaceParts[0]
		key := namespaceParts[1]
		value := parts[1]

		// Skip tags with empty values
		if value == "" {
			eLog.LogDebug("Skipping tag with empty value: %s", tag)
			continue
		}

		// Add tag to the DefinedTags slice
		definedTags = append(definedTags, oci.TagData{
			Namespace: namespace,
			Key:       key,
			Value:     value,
		})
	}

	return definedTags
}

func parseMachineType(machineType string) (string, int64, int64, error) {
	var instanceShape string
	var ocpus int64
	var memGB int64
	var err error

	parts := strings.Split(machineType, "-")
	if len(parts) < 3 {
		return "", 0, 0, fmt.Errorf("invalid machine type format: expected 3 parts separated by '-', got %d parts", len(parts))
	}
	instanceShape = parts[0] // e.g., VM.Standard.E5.Flex

	// Convert string to int64
	ocpus, err = strconv.ParseInt(parts[1], 10, 64)
	if err != nil {
		return "", 0, 0, fmt.Errorf("error parsing OCPU value from machine type: %w", err)
	}
	if ocpus == 0 {
		return "", 0, 0, fmt.Errorf("OCPU value cannot be zero")
	}

	// Convert string to int64
	memGB, err = strconv.ParseInt(parts[2], 10, 64)
	if err != nil {
		return "", 0, 0, fmt.Errorf("error parsing memory value from machine type: %w", err)
	}

	return instanceShape, ocpus, memGB, nil
}

func processMetadata(metadata []map[string]string, labels []map[string]string, eLog *clogger.EventLogger) (string, error) {
	// Combine labels and metadata into one slice
	var allMetadata []map[string]string

	// Add labels as metadata entries
	for _, label := range labels {
		metadataEntry := map[string]string{
			"name":  label["name"],
			"value": label["value"],
		}
		allMetadata = append(allMetadata, metadataEntry)
	}

	// Add existing metadata
	allMetadata = append(allMetadata, metadata...)

	if len(allMetadata) == 0 {
		return `""`, nil
	}

	var metadataPairs []string

	for _, m := range allMetadata {
		name := m["name"]
		value := m["value"]

		// Validate that name is not empty
		if name == "" {
			eLog.LogDebug("Skipping metadata entry with empty name")
			continue
		}

		// Skip metadata entries with empty values
		if value == "" {
			eLog.LogDebug("Skipping metadata entry with empty value: %s", name)
			continue
		}

		// Ensure keys are not longer than 255 characters
		if len(name) > 255 {
			eLog.LogDebug("Metadata key '%s' exceeds OCI's 255 character limit, truncating", name)
			name = name[:255]
		}

		// Trim trailing newlines from ssh-keys
		if name == "ssh-keys" {
			value = strings.TrimRight(value, "\n")
		}

		// Escape problematic characters manually
		value = strings.ReplaceAll(value, `\`, `\\`)
		value = strings.ReplaceAll(value, `"`, `\"`)
		value = strings.ReplaceAll(value, "\n", `\n`)
		value = strings.ReplaceAll(value, "\r", `\r`)
		value = strings.ReplaceAll(value, "\t", `\t`)

		// Create key=value pair
		pair := fmt.Sprintf("%s=%s", name, value)
		metadataPairs = append(metadataPairs, pair)
	}

	// Join all pairs with commas
	result := strings.Join(metadataPairs, defaultSeparator)

	// Return without wrapping in additional quotes
	return result, nil
}

// prepareVmParamsFromDeployment converts deployment VM instances to the format needed for OCI
func prepareVmParamsFromDeployment(cfg *deployment.Config, eLog *clogger.EventLogger, compartmentId string) (
	map[int64]*oci.ClusterConfig,
	map[int64]*oci.NlbConfig,
	map[int64]*oci.NatConfig,
	map[int64]*oci.ClusterConfig,
	error) {

	clusters := make(map[int64]*oci.ClusterConfig) // map[ClusterId]*VmInstance
	nlbs := make(map[int64]*oci.NlbConfig)         // map[ClusterId]*NlbConfig
	natgws := make(map[int64]*oci.NatConfig)       // map[ClusterId]*NatConfig
	var err error
	// Process VM instances
	for _, vmInst := range cfg.VMInstances {
		// instance ID will be unique name
		instanceId := HashStringToInt64(vmInst.Name)
		clusterId := instanceId

		// For regular VM instances
		vm := oci.VmInstance{}
		vm.TFLocalName = pterraform.OciTfLocalName(vmInst.Name)
		vm.CompartmentId = compartmentId
		vm.AcctId = cfg.TenantID
		vm.DisableDpPublicIp = true
		vm.IsDeploymentApi = true

		// Process tags from deployment configuration
		vm.DefinedTags = parseTags(vmInst.Tags, eLog)

		// Set Shape and Memory from machine type (e.g., VM.Standard.E5.Flex-6-16)
		// Parse CPU and Memory from the machine type string
		vm.InstanceShape, vm.ShapeConfig.Ocpus, vm.ShapeConfig.MemoryInGb, err = parseMachineType(vmInst.MachineType)
		vm.MachineType = vmInst.MachineType

		if err != nil {
			eLog.LogError("[GenerateOciConfig] Error in parsing machine type for deployment %v", cfg.ProjectID)
			return nil, nil, nil, nil, err
		}

		// Set instance details
		vm.InternalName = vmInst.Name
		vm.InstanceId = instanceId
		vm.AvailabilityDomain = vmInst.Zone
		vm.InstanceImageId = vmInst.Image
		vm.FaultDomain = vmInst.FaultZone

		vm.AssignIpV6 = false
		vm.Separator = defaultSeparator
		if len(vmInst.Metadata) > 0 {
			// Create a map for metadata
			vm.UserData, err = processMetadata(vmInst.Metadata, vmInst.Labels, eLog)
			if err != nil {
				eLog.LogError("Error in processing user metadata for deployment: %v: %v ", cfg.ProjectID, err)
			}
		}

		// Process network interfaces
		// Track the first network interface to make it primary
		var firstNicType string

		for i, netIf := range vmInst.NetworkInterface {
			// Remember the first NIC details
			if strings.Contains(netIf.ProjectOverride, "shared-dp") {
				// Shared DP interface
				sharedDpSubnet := netIf.Subnet
				vm.SharedDpSubnetId = &sharedDpSubnet

				if i == 0 {
					firstNicType = "shared-dp"
				}
			} else if strings.Contains(netIf.ProjectOverride, "dp") {
				// Data plane interface
				vm.DpSubnetId = netIf.Subnet

				if i == 0 {
					firstNicType = "dp"
				}
			} else if strings.Contains(netIf.ProjectOverride, "mgmt") {
				// Management interface
				vm.SharedMgmtSubnetId = netIf.Subnet

				if i == 0 {
					firstNicType = "shared-mgmt"
				}
			} else if strings.Contains(netIf.ProjectOverride, "service") || strings.Contains(netIf.ProjectOverride, "ha") {
				// Service/HA interface
				vm.HaSubnetId = netIf.Subnet

				if i == 0 {
					firstNicType = "service"
				}
			} else if strings.Contains(netIf.ProjectOverride, "ep-ext") {
				// EP external interface (for SWG Proxy)
				vm.EpExtSubnetId = netIf.Subnet

				if i == 0 {
					firstNicType = "ep-ext"
				}
			}
		}

		// Add a flag to indicate which network should be primary
		vm.PrimaryNicType = firstNicType

		//vm.AddToBackendSet = true
		// Add to regular clusters
		if _, exists := clusters[clusterId]; !exists {
			clusters[clusterId] = &oci.ClusterConfig{}
		}
		vm.AvailabilityDomain = vmInst.Zone
		(*clusters[clusterId])[instanceId] = vm
	}

	// Process ELBs to configure NLBs
	for idx, elb := range cfg.ELBs {
		nlbConfig := &oci.NlbConfig{}
		nlbConfig.TFLocalName = pterraform.OciTfLocalName(elb.Name)
		nlbConfig.InstanceId = int64(idx + 1)
		nlbConfig.CompartmentId = compartmentId
		nlbConfig.SubnetId = elb.Subnet
		nlbConfig.NlbBackendSetHashingPolicy = getElbHashingPolicy(string(elb.SessionAffinity))
		//nlbConfig.IsInstantFailoverTcpResetEnabled = elb.ConnTracking.InstantFailoverTcpReset
		nlbId := nlbConfig.InstanceId
		// Configure health check
		if len(elb.HealthChecks) > 0 {
			hc := elb.HealthChecks[0]
			nlbConfig.HealthChecker.Protocol = strings.ToUpper(hc.Type)
			nlbConfig.HealthChecker.Port = int64(hc.Port)
			nlbConfig.HealthChecker.IntervalInMillis = int64(hc.CheckIntervalSeconds * 1000) // Convert to MS
			nlbConfig.HealthChecker.TimeoutInMillis = int64(hc.TimeoutSeconds * 1000)        // Convert to MS
			nlbConfig.HealthChecker.Retries = 3
			nlbConfig.HealthChecker.UrlPath = hc.RequestPath
			nlbConfig.HealthChecker.ReturnCode = 200
		}

		nlbConfig.PreferOperationallyActiveBackends = elb.ConnTracking.PreferOperational
		nlbConfig.IsInstantFailoverTcpResetEnabled = true // Default value, can be overridden if available in the config

		nlbConfig.NLBEgressIPOCIDList = make(map[string]string)
		nlbConfig.NLBEgressIPList = make(map[string]string)

		nlbConfig.IsInstantFailoverTcpResetEnabled = elb.ConnTracking.EnableStrongAffinity

		// Configure listener
		idleTimeout := int64(defaultTimeout)
		if elb.ConnTracking.IdleTimeoutSec > 0 {
			idleTimeout = int64(elb.ConnTracking.IdleTimeoutSec)
		}

		// Create a listener for each IP forwarding rule
		for ipIdx, ipForward := range elb.IPForwardings {
			var protocol string
			forwardingKey := fmt.Sprintf("fwd-%d", ipIdx)

			if override, exists := protocolOverrideMap[string(ipForward.Protocol)]; exists {
				protocol = override
			} else {
				protocol = string(ipForward.Protocol)
			}

			// Determine the port
			port := 0 // Default for ANY
			if ipForward.Ports != "ANY" && !ipForward.AllPorts {
				// Parse port value from string
				portVal, err := strconv.Atoi(ipForward.Ports)
				if err == nil {
					port = portVal
				}
			}

			listener := oci.NlbListenerPort{
				Name:           ipForward.Name,
				Protocol:       protocol,
				Port:           port,
				TCPIdleTimeout: idleTimeout,
				UDPIdleTimeout: idleTimeout,
			}
			nlbConfig.NlbListenerPorts = append(nlbConfig.NlbListenerPorts, listener)

			if ipForward.Address != "" {
				// Get the IP address from the forwarding rule
				frontendIP := ipForward.Address

				// Convert the IP address to an OCID (Oracle Cloud ID)
				publicIPOCID, err := putils.GetPublicIPOCID(frontendIP, cfg.Region)
				if err != nil {
					eLog.LogError("Error getting public IP OCID for forwarding rule %s: %v", ipForward.Name, err)
				} else if publicIPOCID != nil {
					// Add this IP to the OCID map using a unique key
					nlbConfig.NLBEgressIPOCIDList[forwardingKey] = *publicIPOCID
					nlbConfig.NLBEgressIPList[forwardingKey] = frontendIP
					eLog.LogInfo("Added IP forwarding rule %s with IP %s to NLB config", ipForward.Name, frontendIP)
				} else {
					// If no OCID was found (may be a new IP), still add it to the list with empty OCID
					// This will signal the Terraform to create a new reserved IP
					nlbConfig.NLBEgressIPOCIDList[forwardingKey] = ""
					nlbConfig.NLBEgressIPList[forwardingKey] = frontendIP
					eLog.LogInfo("Added new IP forwarding rule %s with IP %s (no OCID) to NLB config", ipForward.Name, frontendIP)
				}
			}
		}

		// Configure connection persistence (Fail Open param)
		if elb.ConnTracking.ConnPersistence == "NEVER_PERSIST" {
			nlbConfig.ConnectionPersistenceOnUnhealthyBackends = true
		} else {
			nlbConfig.ConnectionPersistenceOnUnhealthyBackends = false
		}

		//Instant Failover
		nlbConfig.IsInstantFailoverEnabled = elb.FailoverPolicy.SkipDrainOnUnhealthy
		nlbConfig.IsFailOpen = elb.ConnTracking.PreferOperational

		if elb.SessionAffinity == ClientIPProto && elb.ConnTracking.PreferOperational {
			nlbConfig.IsSymmetricHashEnabled = true
		}
		// Process backend instances
		for _, backend := range elb.Backends {
			if backend.Type == "umig" {
				// Find the UMIG by name
				for _, umig := range cfg.UMIGs {
					if umig.Name == backend.Name {
						// Check if this backend is marked as a failover backend
						isFailoverBackend := backend.Failover

						// For each instance in the UMIG
						for _, instanceMap := range umig.Instances {
							// Extract the instance name from the map (key "name")
							instanceName, exists := instanceMap["name"]
							if !exists {
								eLog.LogError("Instance in UMIG %s does not have a name", umig.Name)
								continue
							}

							// Find the instance in the VM instances list
							for _, vmInst := range cfg.VMInstances {
								if vmInst.Name == instanceName {
									// Mark this instance to be added to the backend set
									instanceId := HashStringToInt64(vmInst.Name)
									clusterId := instanceId

									// Make sure the cluster exists in the clusters map
									if _, exists := clusters[clusterId]; exists {
										// Mark the instance for inclusion in the backend set
										vmInstance := (*clusters[clusterId])[instanceId]
										vmInstance.AvailabilityDomain = vmInst.Zone
										vmInstance.AddToBackendSet = true

										vmInstance.IsBackup = isFailoverBackend
										vmInstance.NlbId = nlbId
										(*clusters[clusterId])[instanceId] = vmInstance

										eLog.LogInfo("Adding instance %s from UMIG %s to NLB %s, backup: %v",
											instanceName, umig.Name, nlbConfig.TFLocalName, isFailoverBackend)

									}
								}
							}
						}
					}
				}
			}
		}

		for ruleKey, pubIPOCID := range nlbConfig.NLBEgressIPOCIDList {
			pubIP := nlbConfig.NLBEgressIPList[ruleKey]
			if pubIPOCID == "" {
				eLog.LogInfo("Will create new public IP for forwarding rule key: %s (requested IP: %s)",
					ruleKey, pubIP)
			} else {
				eLog.LogInfo("Will associate existing public IP (OCID: %s) for forwarding rule key: %s (IP: %s)",
					pubIPOCID, ruleKey, pubIP)
			}
		}

		nlbs[nlbConfig.InstanceId] = nlbConfig
	}

	return clusters, nlbs, natgws, nil, nil
}

var protocolOverrideMap = map[string]string{
	"l3_default": "ANY",
}

const (
	ClientIPProto     = "CLIENT_IP_PROTO"
	ClientIPPortProto = "CLIENT_IP_PORT_PROTO"
	ThreeTuple        = "THREE_TUPLE"
	FiveTuple         = "FIVE_TUPLE"
)

const (
	defaultTimeout   = 6000 // Default idle timeout in ms
	defaultSeparator = ";;"
)

var elbHashingPolicyMap = map[string]string{
	ClientIPProto:     ThreeTuple,
	ClientIPPortProto: FiveTuple,
}

func getElbHashingPolicy(policy string) string {
	if mappedPolicy, exists := elbHashingPolicyMap[policy]; exists {
		return mappedPolicy
	}
	// Default fallback to "FIVE_TUPLE" if an unknown policy is provided
	return FiveTuple
}

const (
	// ociAuthCredentialsConfig string = "/orch_aas/libs/oci_credentials.json"
	ociAuthCredentialsConfig string = "/orch_aas/libs/oci_credentials.cfg"
	//ociAuthCredentialsConfig string = "~/secrets/oci_config"
)

// DeleteAllInstancesInOCIStack deletes all compute instances that belong to a specific OCI stack
func DeleteAllInstancesInOCIStack(tfWrapper tf.TfWrapper, jobData *definitions.DeploymentData, regionName string, eLog *clogger.EventLogger) error {
	compartmentId := tfWrapper.GetORMClient().GetCompartmentId()

	stackName := jobData.WorkspaceName

	// Set up the configuration
	configProvider := common.CustomProfileConfigProvider(ociAuthCredentialsConfig, "DEFAULT")

	// Create Resource Manager client to get stack resources
	rmClient, err := resourcemanager.NewResourceManagerClientWithConfigurationProvider(configProvider)
	if err != nil {
		return fmt.Errorf("failed to create Resource Manager client: %v", err)
	}
	rmClient.SetRegion(regionName)

	listStacksReq := resourcemanager.ListStacksRequest{
		CompartmentId: common.String(compartmentId),
		DisplayName:   common.String(stackName),
	}

	stacksResp, err := rmClient.ListStacks(context.Background(), listStacksReq)
	if err != nil {
		return fmt.Errorf("failed to list stacks: %v", err)
	}

	if len(stacksResp.Items) == 0 {
		eLog.LogInfo("No stack found with name %s", stackName)
		return nil
	}

	stackID := *stacksResp.Items[0].Id
	eLog.LogInfo("Found stack %s with OCID: %s", stackName, stackID)

	// Create Compute client for instance operations
	computeClient, err := core.NewComputeClientWithConfigurationProvider(configProvider)
	if err != nil {
		return fmt.Errorf("failed to create Compute client: %v", err)
	}
	computeClient.SetRegion(regionName)

	// List all resources in the stack
	listResourcesReq := resourcemanager.ListStackAssociatedResourcesRequest{
		StackId:       common.String(stackID),
		CompartmentId: common.String(compartmentId),
	}

	resourcesResp, err := rmClient.ListStackAssociatedResources(context.Background(), listResourcesReq)
	if err != nil {
		eLog.LogError("Failed to list stack resources: %v", err)
		return fmt.Errorf("failed to list stack resources: %v", err)
	}

	// Filter for compute instances and collect their OCIDs
	var instanceOCIDs []string
	for _, resource := range resourcesResp.Items {
		if resource.ResourceType != nil && *resource.ResourceType == "oci_core_instance" {
			if resource.ResourceId != nil {
				instanceOCIDs = append(instanceOCIDs, *resource.ResourceId)
				eLog.LogInfo("Found instance in stack %s: %s", stackID, *resource.ResourceId)
			}
		}
	}

	if len(instanceOCIDs) == 0 {
		eLog.LogInfo("No instances found in stack %s", stackID)
		return nil
	}

	eLog.LogInfo("Found %d instances to delete in stack %s", len(instanceOCIDs), stackID)

	// Delete each instance
	for _, instanceOCID := range instanceOCIDs {
		eLog.LogInfo("Terminating instance %s from stack %s", instanceOCID, stackID)

		terminateReq := core.TerminateInstanceRequest{
			InstanceId: common.String(instanceOCID),
		}

		_, err = computeClient.TerminateInstance(context.Background(), terminateReq)
		if err != nil {
			eLog.LogError("Failed to terminate instance %s: %v", instanceOCID, err)
			return fmt.Errorf("failed to terminate instance %s: %v", instanceOCID, err)
		}

		eLog.LogInfo("Successfully initiated termination of instance %s", instanceOCID)
	}

	eLog.LogInfo("Successfully initiated termination of all %d instances in stack %s", len(instanceOCIDs), stackID)
	return nil
}

// DeleteInstancesByyName deletes specific compute instances by their names
func DeleteInstancesByName(tfWrapper tf.TfWrapper, jobData *definitions.DeploymentData, regionName string, instanceNames []string, eLog *clogger.EventLogger) error {
	if len(instanceNames) == 0 {
		eLog.LogInfo("No instances to delete")
		return nil
	}

	compartmentId := tfWrapper.GetORMClient().GetCompartmentId()

	// Set up the configuration
	configProvider := common.CustomProfileConfigProvider(ociAuthCredentialsConfig, "DEFAULT")

	// Create Compute client for instance operations
	computeClient, err := core.NewComputeClientWithConfigurationProvider(configProvider)
	if err != nil {
		return fmt.Errorf("failed to create Compute client: %v", err)
	}
	computeClient.SetRegion(regionName)

	eLog.LogInfo("Looking for instances to delete: %v", instanceNames)

	// Create a map for faster lookup
	instancesToDelete := make(map[string]bool)
	for _, name := range instanceNames {
		instancesToDelete[name] = true
	}

	// List all instances in the compartment
	listInstancesReq := core.ListInstancesRequest{
		CompartmentId: common.String(compartmentId),
	}

	instancesResp, err := computeClient.ListInstances(context.Background(), listInstancesReq)
	if err != nil {
		return fmt.Errorf("failed to list instances: %v", err)
	}

	var foundInstances []string
	var instanceOCIDs []string

	// Find instances that match the display names we want to delete
	for _, instance := range instancesResp.Items {
		if instance.DisplayName != nil && instancesToDelete[*instance.DisplayName] {
			foundInstances = append(foundInstances, *instance.DisplayName)
			instanceOCIDs = append(instanceOCIDs, *instance.Id)
			eLog.LogInfo("Found instance to delete: %s (OCID: %s)", *instance.DisplayName, *instance.Id)
		}
	}

	if len(foundInstances) == 0 {
		eLog.LogInfo("No matching instances found to delete")
		return nil
	}

	eLog.LogInfo("Found %d instances to delete: %v", len(foundInstances), foundInstances)

	// Delete each instance
	for i, instanceOCID := range instanceOCIDs {
		instanceName := foundInstances[i]
		eLog.LogInfo("Terminating instance %s (OCID: %s)", instanceName, instanceOCID)

		terminateReq := core.TerminateInstanceRequest{
			InstanceId: common.String(instanceOCID),
		}

		_, err = computeClient.TerminateInstance(context.Background(), terminateReq)
		if err != nil {
			eLog.LogError("Failed to terminate instance %s: %v", instanceName, err)
			return fmt.Errorf("failed to terminate instance %s: %v", instanceName, err)
		}

		eLog.LogInfo("Successfully initiated termination of instance %s", instanceName)
	}

	eLog.LogInfo("Successfully initiated termination of %d instances", len(instanceOCIDs))
	return nil
}

// GetInstancesToDelete compares existing stack instances with current config and returns instances to delete
func GetInstancesToDelete(tfWrapper tf.TfWrapper, jobData *definitions.DeploymentData, cfg *deployment.Config, regionName string, eLog *clogger.EventLogger) ([]string, error) {
	compartmentId := tfWrapper.GetORMClient().GetCompartmentId()
	stackName := jobData.WorkspaceName

	// Set up the configuration
	configProvider := common.CustomProfileConfigProvider(ociAuthCredentialsConfig, "DEFAULT")

	// Create Resource Manager client to get stack resources
	rmClient, err := resourcemanager.NewResourceManagerClientWithConfigurationProvider(configProvider)
	if err != nil {
		return nil, fmt.Errorf("failed to create Resource Manager client: %v", err)
	}
	rmClient.SetRegion(regionName)

	// Create Compute client for instance operations
	computeClient, err := core.NewComputeClientWithConfigurationProvider(configProvider)
	if err != nil {
		return nil, fmt.Errorf("failed to create Compute client: %v", err)
	}
	computeClient.SetRegion(regionName)

	// Find the stack
	listStacksReq := resourcemanager.ListStacksRequest{
		CompartmentId: common.String(compartmentId),
		DisplayName:   common.String(stackName),
	}

	stacksResp, err := rmClient.ListStacks(context.Background(), listStacksReq)
	if err != nil {
		return nil, fmt.Errorf("failed to list stacks: %v", err)
	}

	if len(stacksResp.Items) == 0 {
		eLog.LogInfo("No stack found with name %s", stackName)
		return []string{}, nil
	}

	stackID := *stacksResp.Items[0].Id

	// List all resources in the stack
	listResourcesReq := resourcemanager.ListStackAssociatedResourcesRequest{
		StackId:       common.String(stackID),
		CompartmentId: common.String(compartmentId),
	}

	resourcesResp, err := rmClient.ListStackAssociatedResources(context.Background(), listResourcesReq)
	if err != nil {
		return nil, fmt.Errorf("failed to list stack resources: %v", err)
	}

	// Get existing instance OCIDs from stack
	var existingInstanceOCIDs []string
	for _, resource := range resourcesResp.Items {
		if resource.ResourceType != nil && *resource.ResourceType == "oci_core_instance" {
			if resource.ResourceId != nil {
				existingInstanceOCIDs = append(existingInstanceOCIDs, *resource.ResourceId)
			}
		}
	}

	if len(existingInstanceOCIDs) == 0 {
		eLog.LogInfo("No existing instances found in stack")
		return []string{}, nil
	}

	// Get display names of existing instances
	var existingInstanceNames []string
	for _, instanceOCID := range existingInstanceOCIDs {
		getInstanceReq := core.GetInstanceRequest{
			InstanceId: common.String(instanceOCID),
		}

		instanceResp, err := computeClient.GetInstance(context.Background(), getInstanceReq)
		if err != nil {
			eLog.LogError("Failed to get instance details for OCID %s: %v", instanceOCID, err)
			continue
		}

		if instanceResp.DisplayName != nil {
			existingInstanceNames = append(existingInstanceNames, *instanceResp.DisplayName)
		}
	}

	// Create a set of current instance names from the config
	currentInstanceNames := make(map[string]bool)
	for _, vmInst := range cfg.VMInstances {
		currentInstanceNames[vmInst.Name] = true
	}

	// Find instances that exist in stack but not in current config
	var instancesToDelete []string
	for _, existingName := range existingInstanceNames {
		if !currentInstanceNames[existingName] {
			instancesToDelete = append(instancesToDelete, existingName)
			eLog.LogInfo("Instance %s exists in stack but not in current config - will be deleted", existingName)
		}
	}

	eLog.LogInfo("Found %d instances to delete: %v", len(instancesToDelete), instancesToDelete)
	return instancesToDelete, nil
}
