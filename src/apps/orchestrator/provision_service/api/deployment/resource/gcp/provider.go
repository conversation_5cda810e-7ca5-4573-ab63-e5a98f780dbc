package gcp

import (
	"fmt"
	"io"
	"path"
	"text/template"
)

// Provider stores all required fields needed to templatize the provider TF template
// for GCP
type Provider struct {
	ProjectID       string
	Region          string
	CredentialFile  string
	ProviderVersion string
}

// GenerateTFFile generates the GCP Provider TF template
func (p Provider) GenerateTFFile(tplDir string, f io.Writer) error {
	tplFile := fmt.Sprintf("%s/gcp/provider/provider.gotmpl", tplDir)
	name := path.Base(tplFile)
	provTpl, err := template.New(name).ParseFiles(tplFile)
	if err != nil {
		return err
	}
	if err := provTpl.Execute(f, p); err != nil {
		return err
	}
	return nil
}
