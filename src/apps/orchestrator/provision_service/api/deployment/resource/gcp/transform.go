package gcp

import (
	"orchestrator/provision_service/api/deployment/resource/gcp/instance"
	"orchestrator/provision_service/api/deployment/resource/gcp/instance_group/managed"
	"orchestrator/provision_service/api/deployment/resource/gcp/instance_group/unmanaged"
	"orchestrator/provision_service/api/deployment/resource/gcp/load_balancer"
	"orchestrator/provision_service/api/deployment/resource/gcp/load_balancer/external"
	"orchestrator/provision_service/api/deployment/resource/gcp/load_balancer/internallb"
	"orchestrator/provision_service/api/deployment/resource/gcp/network"
	"orchestrator/provision_service/api/deployment/resource/gcp/network/cloud_nat"
	"orchestrator/provision_service/api/deployment/resource/gcp/network/firewall"
	npbr "orchestrator/provision_service/api/deployment/resource/gcp/network/pbr"
	"orchestrator/provision_service/api/deployment/resource/gcp/network/private_connect_peering"
	putils "orchestrator/provision_service/utils"

	"go.panw.local/provision/deployment"
)

func TransformProvider(cfg deployment.Config) Provider {
	return Provider{
		ProjectID:       cfg.ProjectID,
		Region:          cfg.Region,
		CredentialFile:  "credentials.json",
		ProviderVersion: putils.GetGoogleProviderVersion(),
	}
}

func TransformInstance(cfg deployment.Config, inst deployment.Instance) instance.Instance {
	return instance.TransformInstance(cfg, inst)
}

func TransformFirewallRule(fr deployment.FirewallRule, network string) firewall.Rule {
	return firewall.TransformStandAloneRule(fr, network)
}

func TransformTemplate(cfg deployment.Config, tpl deployment.Template) instance.Template {
	return instance.TransformTemplate(cfg, tpl)
}

func TransformMIG(cfg deployment.Config, mig deployment.MIG) managed.InstanceGroup {
	return managed.Transform(cfg, mig)
}

func TransformUMIG(cfg deployment.Config, umig deployment.UMIG) unmanaged.InstanceGroup {
	return unmanaged.Transform(cfg, umig)
}

func TransformELB(cfg deployment.Config, elb deployment.ELB) external.LoadBalancer {
	lb := load_balancer.Config{
		ResourceName:        elb.ResourceName,
		Name:                elb.Name,
		IsGlobal:            elb.IsGlobal,
		Region:              elb.Region,
		Network:             elb.Network,
		Subnet:              elb.Subnet,
		Protocol:            elb.Protocol,
		SecurityPolicy:      elb.SecurityPolicy,
		ProxyHeader:         string(elb.ProxyHeader),
		SessionAffinity:     elb.SessionAffinity,
		LoadBalancingScheme: elb.LoadBalancingScheme,
		IPForwardings:       elb.IPForwardings,
		ConnDraining:        elb.ConnDraining,
		ConnTracking:        elb.ConnTracking,
		FailoverPolicy:      elb.FailoverPolicy,
		HealthChecks:        elb.HealthChecks,
		BackendServiceName:  elb.BackendServiceName,
		Backends:            elb.Backends,
	}
	return external.Transform(cfg, lb)
}

func TransformILB(cfg deployment.Config, ilb deployment.ILB) internallb.LoadBalancer {
	lb := load_balancer.Config{
		ResourceName:        ilb.ResourceName,
		Name:                ilb.Name,
		Network:             ilb.Network,
		Subnet:              ilb.Subnet,
		ProjectOverride:     ilb.ProjectOverride,
		IsGlobal:            false,
		Region:              ilb.Region,
		Protocol:            ilb.Protocol,
		SessionAffinity:     ilb.SessionAffinity,
		LoadBalancingScheme: ilb.LoadBalancingScheme,
		IPForwardings:       ilb.IPForwardings,
		ConnDraining:        ilb.ConnDraining,
		ConnTracking:        ilb.ConnTracking,
		FailoverPolicy:      ilb.FailoverPolicy,
		HealthChecks:        ilb.HealthChecks,
		BackendServiceName:  ilb.BackendServiceName,
		Backends:            ilb.Backends,
	}
	return internallb.Transform(cfg, lb)
}

func TransformPrivateConnectPeering(cfg deployment.Config, pcp deployment.PrivateConnectPeering) private_connect_peering.PrivateConnectPeering {
	return private_connect_peering.Transform(cfg, pcp)
}

func TransformNAT(cfg deployment.Config, nat deployment.NAT) cloud_nat.CloudNAT {
	return cloud_nat.Transform(cfg, nat)
}

func TransformNetwork(cfg deployment.Config, n deployment.Network) network.Network {
	return network.Transform(cfg, n)
}

func TransformPolicyBasedRoute(cfg deployment.Config, pbr deployment.PBR) npbr.PBR {
	return npbr.Transform(cfg, pbr)
}
