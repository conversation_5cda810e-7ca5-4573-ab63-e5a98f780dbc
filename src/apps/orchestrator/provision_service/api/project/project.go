package project

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"orchestrator/libs/go/dbaccess/models/orch_project_management_tf_table"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/orm"
	"orchestrator/libs/go/terraform"
	"orchestrator/libs/go/utils"
	pconfig "orchestrator/provision_service/infra/config"
	"orchestrator/provision_service/infra/db"
	"orchestrator/provision_service/infra/logger"
	"orchestrator/provision_service/infra/tf"
	syncmap "orchestrator/provision_service/libs/sync_map"
	pterraform "orchestrator/provision_service/terraform"
	"orchestrator/provision_service/terraform/gcp"
	"orchestrator/provision_service/terraform/oci"
	"orchestrator/provision_service/terraform/osp"
	putils "orchestrator/provision_service/utils"
	"os"
	"path/filepath"
	"text/template"
	"time"

	"github.com/gin-gonic/gin"
	"go.panw.local/pangolin/clogger"
)

var serializer = syncmap.New()

type serializerKey struct {
	projectId     string
	cloudProvider string
	ociTenancyId  string
}

func serialize(projectId, cloudProvider, ociTenancyId string, eLog *clogger.EventLogger) (bool, func()) {
	key := serializerKey{projectId, cloudProvider, ociTenancyId}
	if _, exists := serializer.Get(key); exists {
		// There is already a request in progress
		eLog.LogInfo("Request already in progress for project_id=%s, cloud_provider=%s, oci_tenancy_id=%s, reply with %d",
			projectId, cloudProvider, ociTenancyId, http.StatusConflict)
		return true, nil
	}
	serializer.Set(key, true)
	return false, func() { serializer.Delete(key) }
}

func NewProvisionJob(input *ProvisionPayload, eLog *clogger.EventLogger) *ProvisionJob {
	return &ProvisionJob{
		EventLogger:     eLog,
		ProjectId:       input.ProjectId,
		ProjectName:     input.ProjectName,
		CloudProvider:   input.CloudProvider,
		CustomerName:    input.CustomerName,
		HostProjectId:   input.HostProjectId,
		ImageProjectId:  input.ImageProjectId,
		OciTenancyId:    input.OciTenancyId,
		RegionName:      input.RegionName,
		OciParentCompId: input.OciParentCompId,
		DebugId:         input.DebugId,
		Preview:         input.Preview,
		Import:          input.Import,
		ImportData:      input.ImportData,
	}
}

type ProvisionJob struct {
	// logistics
	EventLogger      *clogger.EventLogger
	WorkspaceName    string
	ProviderDir      string
	WorkspaceDir     string
	S3UploadDone     bool
	TerraformVersion string
	TfWrapper        tf.TfWrapper

	// request input
	ProjectId      string
	ProjectName    string
	CloudProvider  string
	CustomerName   string
	HostProjectId  string
	ImageProjectId string
	DebugId        string
	Preview        bool
	Import         bool
	ImportData     *ImportData
	// OCI specific fields
	OciTenancyId    string
	RegionName      string
	OciParentCompId string

	// input DB data
	GcpOrchFolderId  string
	GcpOrchBillingId string
	GcpEnv           string
	AwsEnv           string

	// config run
	WorkspaceId string // Not used in case of OCI
	RunType     terraform.RunType
	RunId       string
	RunURL      string
	RunOutput   *terraform.RunOutput
	RunStatus   string

	// output data
	ProjectNumber    string
	OciCompartmentId string
	PrjMgmtEntry     *orch_project_management_tf_table.Row
}

func (job *ProvisionJob) String() string {
	if job.CloudProvider == "oci" {
		return fmt.Sprintf("Project.ProvisionJob{parentTenancyId=%v, region=%v, ociParentCompId=%v, workspace=%v}",
			job.OciTenancyId, job.RegionName, job.OciParentCompId, job.WorkspaceName)
	} else {
		return fmt.Sprintf("Project.ProvisionJob{projId=%v, workspace=%v}",
			job.ProjectId, job.WorkspaceName)
	}
}

// workspaceName returns the workspace name to use.
func workspaceName(cloudProvider, customerName, projectId string, eLog *clogger.EventLogger) string {
	var name string
	if cloudProvider == "oci" {
		name = fmt.Sprintf("%v", customerName)
	} else {
		name = fmt.Sprintf("%v_project_%v", projectId, cloudProvider)
	}
	// suffix workspace name with project name
	if prj := pconfig.GetConfig().ProvisionService.TfPrj; len(prj) > 0 {
		name = fmt.Sprintf("%v_%v", name, prj)
	}
	eLog.LogInfo("Workspace name: %v", name)
	return name
}

// updateDbEntry sync down shadow DB row
func updateDbEntry(job *ProvisionJob) error {
	eLog := job.EventLogger
	dbAccessor := db.GetDbAccessor()

	if err := dbAccessor.UpdatePrjMgmtTf(job.PrjMgmtEntry, eLog); err != nil {
		eLog.LogError("Failed UpdatePrjMgmtTf: %v, entry=+%v", err, job.PrjMgmtEntry)
		return err
	}
	eLog.LogInfo("Updated PrjMgmtEntry row associated with prjID=%v, cloudProvider=%v, workspace=%v: %+v",
		job.ProjectId, job.CloudProvider, job.WorkspaceName, job.PrjMgmtEntry)
	return nil
}

type ImportResult string

const (
	ImportResultNull       ImportResult = ""
	ImportResultAsExpected ImportResult = "AsExpected"
	ImportResultDeviation  ImportResult = "Deviation"
)

// evalImportResult evaluates whether an Import meets the expectation.
// Here it expects the import is done on TFC and relies on parsign the execution plan that may not be available
// from other Terraform services.
func evalImportResult(tfWrapper tf.TfWrapper, runOutput *terraform.RunOutput, eLog *clogger.EventLogger) (ImportResult, error) {
	if runOutput == nil {
		return "", errors.New("runOutput is nil")
	}
	if runOutput.ResourceAdditions == 0 && runOutput.ResourceChanges == 0 && runOutput.ResourceDestructions == 0 {
		return ImportResultAsExpected, nil
	}

	plan := runOutput.GetTfcPlan()
	if plan == nil {
		return "", errors.New("run output does not have a plan")
	}

	planID := plan.ID
	ep, err := tfWrapper.ReadJsonExecutionPlan(eLog, planID)
	if err != nil {
		eLog.LogError("Failed to read JSON execution plan (%v): %v", planID, err)
		return "", err
	}

	type ChangeDetail struct {
		Actions []string `json:"actions"`
	}
	type ResourceChange struct {
		Change ChangeDetail `json:"change"`
		Type   string       `json:"type"`
		Name   string       `json:"name"`
	}
	type ExecutionPlan struct {
		ResourceChanges []ResourceChange `json:"resource_changes"`
	}
	executionPlan := ExecutionPlan{}
	if err := json.Unmarshal([]byte(ep), &executionPlan); err != nil {
		eLog.LogError("Failed to unmarshal JSON execution plan %v): %v", planID, err)
		return "", err
	}

	expectedTypeCreates := map[string]bool{
		"google_project_service":                  true,
		"google_project_iam_member":               true,
		"google_project_default_service_accounts": true,
	}
	deviation := false
forLoop:
	for _, rc := range executionPlan.ResourceChanges {
		typ := rc.Type
		for _, action := range rc.Change.Actions {
			switch action {
			case "no-op":
			case "create":
				if !expectedTypeCreates[typ] {
					deviation = true
					eLog.LogError("Unexpected resource creation on '%v'", typ)
					break forLoop
				}
			default:
				deviation = true
				eLog.LogError("Unexpected resource action '%v' on '%v'", action, typ)
				break forLoop
			}
		}
	}
	if deviation {
		return ImportResultDeviation, nil
	} else {
		return ImportResultAsExpected, nil
	}
}

func retrieveData(job *ProvisionJob) error {
	eLog := job.EventLogger
	dbAccessor := db.GetDbAccessor()

	rowName := "gcp_orch_folder_id"
	row, err := dbAccessor.GetOrchCfgByName(rowName, eLog)
	if err != nil {
		eLog.LogError("Error GetOrchCfgByName: %v, name=%v", err, rowName)
		return err
	}
	job.GcpOrchFolderId = row.Value.String()

	rowName = "gcp_orch_billing_id"
	row, err = dbAccessor.GetOrchCfgByName(rowName, eLog)
	if err != nil {
		eLog.LogError("Error GetOrchCfgByName: %v, name=%v", err, rowName)
		return err
	}
	job.GcpOrchBillingId = row.Value.String()

	rowName = "gcp_env"
	row, err = dbAccessor.GetOrchCfgByName(rowName, eLog)
	if err != nil {
		eLog.LogError("Error GetOrchCfgByName: %v, name=%v", err, rowName)
		return err
	}
	job.GcpEnv = row.Value.String()

	rowName = "aws_env"
	row, err = dbAccessor.GetOrchCfgByName(rowName, eLog)
	if err != nil {
		eLog.LogError("Error GetOrchCfgByName: %v, name=%v", err, rowName)
		return err
	}
	job.AwsEnv = row.Value.String()

	return nil
}

func generateConfig(job *ProvisionJob) error {
	var err error
	var templArg any
	var credentialFilename, templateDir string
	eLog := job.EventLogger
	tfWrapper := job.TfWrapper

	// determine the template and the workspace directories
	if job.CloudProvider == "openstack" {
		templateDir = osp.OspDirName
	} else {
		templateDir = job.CloudProvider
	}
	job.ProviderDir = filepath.Join(tfWrapper.GetTemplateDir(), templateDir, "project")
	job.WorkspaceDir = filepath.Join(tfWrapper.GetStateDir(), job.WorkspaceName)

	// create per-workspace directory
	statinfo, err := os.Stat(job.ProviderDir)
	if err != nil {
		eLog.LogError("Error retrieving info on provider directory %v: %v", job.ProviderDir, err)
		return err
	}
	if err = putils.CleanDirectory(job.WorkspaceDir, statinfo.Mode()); err != nil {
		eLog.LogError("Error making workspace directory %v: %v", job.WorkspaceDir, err)
		return err
	}

	templateFileName := "main.gotmpl"
	tfFileName := "main.tf"
	switch job.CloudProvider {
	case "gcp":
		// copy Credentials - GCP
		credentialFilename = "credentials.json"
		credentialSrc := filepath.Clean(tfWrapper.GetGcpCredential())
		credentialDst := filepath.Join(job.WorkspaceDir, credentialFilename)
		err = terraform.File(credentialSrc, credentialDst)
		if err != nil {
			eLog.LogError("Error copying credentials file to workspace directory, src_file=%v, dst_file=%v: %v",
				credentialSrc, credentialDst, err)
			return err
		}

		// prepare configuration parameters
		provider, err := gcp.PrepareProviderParams(job.ProjectId, "", putils.GetGoogleProviderVersion(), eLog)
		if err != nil {
			eLog.LogError("Error preparing provider params: %v", err)
			return err
		}
		project, err := gcp.PrepareProject(gcp.PrepareProjectParams{
			ID:             job.ProjectId,
			Name:           job.ProjectName,
			FolderID:       job.GcpOrchFolderId,
			BillingAccount: job.GcpOrchBillingId,
			GCPEnv:         job.GcpEnv,
			AWSEnv:         job.AwsEnv,
			CustomerName:   job.CustomerName,
			HostProjectID:  job.HostProjectId,
			ImgProjectID:   job.ImageProjectId,
			Import:         job.Import,
			ELog:           eLog,
		})
		if err != nil {
			eLog.LogError("Error preparing project params: %v", err)
			return err
		}
		clSvcAcctEmail := ""
		var svcAcctsToImport []*gcp.ServiceAccount
		if job.ImportData != nil {
			clSvcAcctEmail = job.ImportData.CloudServiceAccountEmail

			svcAcctIds := make(map[string]bool)
			for _, id := range job.ImportData.ServiceAccountIds {
				svcAcctIds[id] = true
			}
			for _, svcAcct := range project.ServiceAccounts {
				if svcAcctIds[svcAcct.Id] {
					svcAcctsToImport = append(svcAcctsToImport, svcAcct)
				}
			}
		}
		templArg = struct {
			Provider          *gcp.Provider
			Project           *gcp.Project
			CloudSvcAcctEmail string
			SvcAcctsToImport  []*gcp.ServiceAccount
		}{provider, project, clSvcAcctEmail, svcAcctsToImport}

	case "openstack":
		hostProjectName := pconfig.GetConfig().OpenstackOrchProjectID
		if _, err := osp.SetupTfCommon(tfWrapper, job.WorkspaceDir, hostProjectName,
			eLog); err != nil {
			eLog.LogError("Failed to setup openstack project tf")
			return err
		}
		ospProject, err := osp.PrepareProject(job.ProjectId, job.ProjectName, hostProjectName,
			job.AwsEnv, job.CustomerName, eLog)
		if err != nil {
			eLog.LogError("Error preparing openstack project params %v", err)
			return err
		}
		templArg = ospProject
		credentialFilename = osp.CredentialFileName

	case "oci":
		eLog.LogInfo("Copying from %v to %v", job.ProviderDir, job.WorkspaceDir)
		err = terraform.CpDir(job.ProviderDir, job.WorkspaceDir)
		if err != nil {
			eLog.LogError("error copying template directory %v", err)
			return err
		}
		// prepare configuration parameters
		provider, err := oci.PrepareProviderParams(job.RegionName, eLog)
		if err != nil {
			eLog.LogError("Error preparing provider params: %v", err)
			return err
		}
		compartment, err := oci.PrepareCompartment(job.OciParentCompId, job.CustomerName, eLog)
		if err != nil {
			eLog.LogError("Error preparing project params: %v", err)
			return err
		}
		templArg = struct {
			Provider    *oci.Provider
			Compartment *oci.Compartment
		}{provider, compartment}
		templateFileName = "terraform.gotmpl"
		tfFileName = "terraform.tfvars"

	default:
		errStr := fmt.Sprintf("Unsupported  cloud provider %v", job.CloudProvider)
		eLog.LogError(errStr)
		return errors.New(errStr)
	}

	// produce terraform configuration files
	var mainTempl *template.Template
	var mainTfFile *os.File
	mainTempl, mainTfFile, err = pterraform.ParseGotmpl(job.ProviderDir, job.WorkspaceDir, templateFileName, tfFileName, eLog)
	if err != nil {
		eLog.LogError("Error parsing main go template: %v", err)
		return err
	}
	defer mainTfFile.Close()
	err = mainTempl.Execute(mainTfFile, templArg)
	if err != nil {
		eLog.LogError("Error processing template %v: %v", mainTempl.Name(), err)
		return err
	}

	if job.Import {
		var importsTempl *template.Template
		var importsTfFiletfFile *os.File
		importsTempl, importsTfFiletfFile, err = pterraform.ParseGotmpl(job.ProviderDir, job.WorkspaceDir, "imports.gotmpl", "imports.tf", eLog)
		if err != nil {
			eLog.LogError("Error parsing imports go template: %v", err)
			return err
		}
		defer importsTfFiletfFile.Close()
		err = importsTempl.Execute(importsTfFiletfFile, templArg)
		if err != nil {
			eLog.LogError("Error processing template %v: %v", importsTempl.Name(), err)
			return err
		}
	}

	// upload to S3
	if pconfig.GetConfig().ProvisionService.S3Upload {
		err = putils.WriteWorkspaceDirToS3(eLog, putils.TerraformPrefix, job.WorkspaceName, job.WorkspaceDir, []string{credentialFilename})
		// If uploading to S3 fails, allow the job to continue but log an error
		if err != nil {
			eLog.LogError("Error uploading TF configuration to S3: %v", err)
		} else {
			job.S3UploadDone = true
		}
	}

	// update progress in DB
	job.PrjMgmtEntry.FinishedUpdate = job.PrjMgmtEntry.TriggerUpdate
	err = updateDbEntry(job)
	if err != nil {
		return err
	}

	// determine Terraform RunType
	runType := terraform.RunTypeApply
	if job.Preview {
		runType = terraform.RunTypePlan
	}
	job.RunType = tfWrapper.OverrideRunType(runType)
	return nil
}

func applyConfig(job *ProvisionJob) error {
	var err error
	var execMode, execModeData string

	eLog := job.EventLogger
	tfWrapper := job.TfWrapper

	// reset DB fields of this task
	job.PrjMgmtEntry.StartedProcessing = job.PrjMgmtEntry.TriggerUpdate
	job.PrjMgmtEntry.CurrRunStatus = sql.String("")
	job.PrjMgmtEntry.PrevRunStatus = sql.String("")
	job.PrjMgmtEntry.RunStatusChangeTimestamp = sql.Int64(0)
	job.PrjMgmtEntry.RunId = sql.String("")
	job.PrjMgmtEntry.RunType = sql.String(job.RunType.String())
	job.PrjMgmtEntry.RunUrl = sql.String("")
	job.PrjMgmtEntry.ResourceCount = sql.Int64(0)
	job.PrjMgmtEntry.ResourceAdditions = sql.Int64(0)
	job.PrjMgmtEntry.ResourceChanges = sql.Int64(0)
	job.PrjMgmtEntry.ResourceDestructions = sql.Int64(0)
	job.PrjMgmtEntry.ResourceImports = sql.Int64(0)
	job.PrjMgmtEntry.ImportResult = sql.String("")
	err = updateDbEntry(job)
	if err != nil {
		return err
	}

	if job.CloudProvider == "openstack" {
		execMode = pconfig.GetConfig().ProvisionService.OspDeployConfig.ExecutionMode
		execModeData = pconfig.GetConfig().ProvisionService.OspDeployConfig.AgentPoolId
	}
	if job.CloudProvider != "oci" {
		wid, err := tfWrapper.CreateWorkspace(eLog, tfWrapper.GetProject(), job.WorkspaceName, job.TerraformVersion,
			execMode, execModeData)
		if err != nil {
			eLog.LogError("Failed to create workspace %v: %v", job.WorkspaceName, err)
			return err
		}
		job.WorkspaceId = *wid
	}

	runDesp := fmt.Sprintf("%v (%s)", job.WorkspaceDir, job.RunType)
	runMsg := fmt.Sprintf("Scheduling a run for %s", runDesp)
	runOption := terraform.RunOptions{
		Message:           &runMsg,
		Directory:         &job.WorkspaceDir,
		Type:              job.RunType,
		WaitForCompletion: false,
		TfVersion:         job.TerraformVersion,
	}
	job.RunOutput, err = tfWrapper.Run(eLog, job.WorkspaceName, false, runOption)
	if err != nil {
		eLog.LogError("Failed run %v: %v", runDesp, err)
		return err
	}
	job.RunId = job.RunOutput.Id
	job.RunURL = job.RunOutput.URI
	job.RunStatus = job.RunOutput.Status

	// Wait for the run to come to an EndStatus.
	// TODO: Currently there is no timeout on a Terraform Run. Revisit to see if needed.
	prevStatus := ""
	for {
		<-time.After(time.Second)
		job.RunOutput, err = tfWrapper.ReadRunStatus(eLog, job.RunOutput.Id)
		if err != nil {
			eLog.LogError("Failed to read status on run %v: %v", runDesp, err)
			return err
		}
		job.RunStatus = job.RunOutput.Status
		if prevStatus != job.RunStatus {
			// update DB
			job.PrjMgmtEntry.CurrRunStatus = sql.String(job.RunStatus)
			job.PrjMgmtEntry.PrevRunStatus = sql.String(prevStatus)
			job.PrjMgmtEntry.RunStatusChangeTimestamp = sql.Int64(time.Now().Unix())
			job.PrjMgmtEntry.RunId = sql.String(job.RunId)
			job.PrjMgmtEntry.RunUrl = sql.String(job.RunURL)
			err = updateDbEntry(job)
			if err != nil {
				return err
			}
		}
		prevStatus = job.RunStatus

		end, _, _, _ := tfWrapper.IsEndStatus(job.RunStatus)
		if end {
			break
		}
	}
	eLog.LogInfo("Run output: status=%v, runUrl=%s", job.RunOutput.Status, job.RunURL)
	if d := job.RunOutput.GetTfcRun(); d != nil {
		eLog.LogInfo("Run output TFC run: %+v", *d)
	}
	if d := job.RunOutput.GetImRevision(); d != nil {
		eLog.LogInfo("Run output IM deployment: %+v", *d)
	}
	if d := job.RunOutput.GetOrmJob(); d != nil {
		eLog.LogInfo("Run output ORM stack: %+v", *d)
	}

	_, plannedOnly, applied, errored := tfWrapper.IsEndStatus(job.RunStatus)
	if !plannedOnly && !applied {
		eLog.LogInfo("Unexpected run status %v, return error", job.RunStatus)
		if errored {
			// Dump error details for debugging
			tfWrapper.GetErrorDetails(eLog, job.RunId)
		}
		return pterraform.ErrTfRunFailed
	}

	importResult := ImportResultNull
	if job.Import {
		importResult, err = evalImportResult(tfWrapper, job.RunOutput, eLog)
		if err != nil {
			return err
		}
	}

	// save success to DB
	job.PrjMgmtEntry.ResourceCount = sql.Int64(job.RunOutput.ResourceCount)
	job.PrjMgmtEntry.ResourceAdditions = sql.Int64(job.RunOutput.ResourceAdditions)
	job.PrjMgmtEntry.ResourceChanges = sql.Int64(job.RunOutput.ResourceChanges)
	job.PrjMgmtEntry.ResourceDestructions = sql.Int64(job.RunOutput.ResourceDestructions)
	job.PrjMgmtEntry.ResourceImports = sql.Int64(job.RunOutput.ResourceImported)
	job.PrjMgmtEntry.ImportResult = sql.String(importResult)
	job.PrjMgmtEntry.FinishedProcessing = job.PrjMgmtEntry.StartedProcessing
	err = updateDbEntry(job)
	if err != nil {
		return err
	}

	if job.S3UploadDone {
		// clean up local copy of TF configuration, if fails allow the job to continue but log an error
		err = os.RemoveAll(job.WorkspaceDir)
		if err != nil {
			eLog.LogError("Failed to clean up workspace dir: %v, dir=%v", err, job.WorkspaceDir)
		}
	}
	return nil
}

func postProcessing(job *ProvisionJob) error {
	var err error
	eLog := job.EventLogger
	tfWrapper := job.TfWrapper

	// reset DB fields of this task
	job.PrjMgmtEntry.StartedPostProcessing = job.PrjMgmtEntry.FinishedProcessing
	job.PrjMgmtEntry.Output = sql.String("")
	err = updateDbEntry(job)
	if err != nil {
		return err
	}

	if !job.Preview {
		expRunId := ""
		_, _, applied, _ := tfWrapper.IsEndStatus(job.RunStatus)
		if applied {
			expRunId = job.RunId
		}
		outputs, err := tfWrapper.GetTerraformStateOutputs(eLog, job.WorkspaceName, expRunId)
		if err != nil {
			eLog.LogError("Failed to retrieve output: %v, workspace=%v", err, job.WorkspaceName)
			return err
		}

		if job.CloudProvider == "oci" {
			ormOutputs := outputs.GetOrmOutputs()
			if ormOutputs == nil {
				eLog.LogError("Failed to retrieve output: nil OCI outputs, workspace=%v", job.WorkspaceName)
				return err
			}
			output, err := orm.ParseJobOutputs(ormOutputs)
			if err != nil {
				eLog.LogError("Failed to parse ORM output %+v, workspace=%v", ormOutputs, job.WorkspaceName)
				return err
			}
			job.OciCompartmentId = output.CompartmentId
			job.PrjMgmtEntry.Output = sql.String(job.OciCompartmentId)
		} else {
			if outputs.StringValue == "" {
				eLog.LogError("Failed to retrieve project number, workspace=%v", job.WorkspaceName)
				return fmt.Errorf("error retrieving project number")
			}
			job.ProjectNumber = outputs.StringValue
			job.PrjMgmtEntry.Output = sql.String(job.ProjectNumber)
		}
	}

	// save success to DB
	job.PrjMgmtEntry.FinishedPostProcessing = job.PrjMgmtEntry.StartedPostProcessing
	if !job.Preview {
		job.PrjMgmtEntry.DeployedByProvSvc = sql.Bool(true) // signify this project is managed by prov svc, once set it is sticky
	}
	err = updateDbEntry(job)
	if err != nil {
		return err
	}
	return nil
}

type provisionTaskName string

const (
	retrieveDataTask   provisionTaskName = "retrieveData"
	generateConfigTask                   = "generateConfig"
	applyConfigTask                      = "applyConfig"
	postProcessingTask                   = "postProcessing"
)

type provisionTask struct {
	Name provisionTaskName         `json:"name"`
	Fn   func(*ProvisionJob) error `json:"-"`
}

var provisionTasks = []*provisionTask{
	{retrieveDataTask, retrieveData},
	{generateConfigTask, generateConfig},
	{applyConfigTask, applyConfig},
	{postProcessingTask, postProcessing},
}

func nextProvisionTask(curr *provisionTask) (task *provisionTask, done bool) {
	if curr == nil {
		return provisionTasks[0], false
	}
	for i, p := range provisionTasks {
		if curr.Name == p.Name && i < len(provisionTasks)-1 {
			return provisionTasks[i+1], false
		}
	}
	return nil, true
}

func provision(job *ProvisionJob) error {
	var err error
	eLog := job.EventLogger

	var currTask *provisionTask
	var done bool
	for {
		currTask, done = nextProvisionTask(currTask)
		if done {
			eLog.LogInfo("Job completed, job=%v", job)
			break
		}
		err = currTask.Fn(job)
		if err != nil {
			eLog.LogInfo("Failed %v: %v", currTask.Name, err)
			// save ErrorReason in DB
			job.PrjMgmtEntry.ErrorReason = sql.String(err.Error())
			_ = updateDbEntry(job) // best effort
			return err
		}
	}

	return nil
}

type ImportData struct {
	CloudServiceAccountEmail string   `json:"cloud_service_account_email"`
	ServiceAccountIds        []string `json:"service_account_names"`
}

type ProvisionPayload struct {
	ProjectId       string      `json:"project_id"`
	ProjectName     string      `json:"project_name"`
	CloudProvider   string      `json:"cloud_provider"`
	CustomerName    string      `json:"customer_name"`
	HostProjectId   string      `json:"host_project_id"`
	ImageProjectId  string      `json:"image_project_id"`
	OciTenancyId    string      `json:"oci_tenancy_id"`
	RegionName      string      `json:"region_name"`
	OciParentCompId string      `json:"oci_parent_comp_id"`
	DebugId         string      `json:"debug_id"`
	Preview         bool        `json:"preview"`
	Import          bool        `json:"import"`
	ImportData      *ImportData `json:"import_data"`
}

func (rp *ProvisionPayload) validate() error {
	var err error
	baseEmptyOk := map[string]bool{
		"ProjectName": true, "CustomerName": true, "HostProjectId": true, "ImageProjectId": true, "DebugId": true,
		"Preview": true, "Import": true, "ImportData": true}
	ociEmptyOk := map[string]bool{
		"ProjectId": true, "CustomerName": true}
	nonOciEmptyOk := map[string]bool{
		"OciTenancyId": true, "OciParentCompId": true, "RegionName": true}
	validateInputNotEmpty := func(additionals ...map[string]bool) error {
		emptyAllowed := make(map[string]bool)
		for k, v := range baseEmptyOk {
			emptyAllowed[k] = v
		}
		for _, m := range additionals {
			for k, v := range m {
				emptyAllowed[k] = v
			}
		}
		return putils.ValidateInputNotEmpty(rp, emptyAllowed)
	}
	noPreviewImport := func() error {
		if rp.Preview {
			return fmt.Errorf("preview is not supported for %s cloud provider", rp.CloudProvider)
		} else if rp.Import {
			return fmt.Errorf("import is not supported for %s cloud provider", rp.CloudProvider)
		}
		return nil
	}
	noOciSpecifics := func() error {
		if rp.OciTenancyId != "" {
			return fmt.Errorf("oci tenancy is not supported for %s cloud provider", rp.CloudProvider)
		} else if rp.OciParentCompId != "" {
			return fmt.Errorf("oci parent comp is not supported for %s cloud provider", rp.CloudProvider)
		} else if rp.RegionName != "" {
			return fmt.Errorf("region is not supported for %s cloud provider", rp.CloudProvider)
		}
		return nil
	}

	switch rp.CloudProvider {
	case "gcp":
		err = validateInputNotEmpty(nonOciEmptyOk)
		if err != nil {
			return err
		}
		err = noOciSpecifics()
		if err != nil {
			return err
		}
		if rp.Import && (rp.ImportData == nil || rp.ImportData.CloudServiceAccountEmail == "") {
			return fmt.Errorf("import is enabled but cloud_service_account_email is not provided")
		}
	case "openstack":
		err = validateInputNotEmpty(nonOciEmptyOk)
		if err != nil {
			return err
		}
		err = noPreviewImport()
		if err != nil {
			return err
		}
		err = noOciSpecifics()
		if err != nil {
			return err
		}

	case "oci":
		err = validateInputNotEmpty(ociEmptyOk)
		if err != nil {
			return err
		}
		err = noPreviewImport()
		if err != nil {
			return err
		}
	default:
		return fmt.Errorf("unexpected cloud provider '%v', only 'gcp', 'openstack' and 'oci' is supported", rp.CloudProvider)
	}
	return nil
}

type ProvisionResponse struct {
	Error            string `json:"error,omitempty"`
	ProjectNumber    string `json:"project_number,omitempty"`
	OciCompartmentId string `json:"oci_compartment_id,omitempty"`
}

func ProvisionHandler(c *gin.Context) {
	var err error
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	dbAccessor := db.GetDbAccessor()

	// retrieve and validate input
	payload := &ProvisionPayload{}
	err = c.ShouldBindJSON(payload)
	if err != nil {
		eLog.LogError("JSON ERROR: %v, payload=%#v", err, payload)
		c.JSON(http.StatusBadRequest, &ProvisionResponse{Error: err.Error()})
		return
	}
	err = payload.validate()
	if err != nil {
		eLog.LogError("VALIDATE ERROR: %v, payload=%#v", err, payload)
		c.JSON(http.StatusBadRequest, &ProvisionResponse{Error: err.Error()})
		return
	}
	eLog.SetLoggerTag(payload.DebugId)

	conflict, release := serialize(payload.ProjectId, payload.CloudProvider, payload.OciTenancyId, eLog)
	if conflict {
		c.JSON(http.StatusConflict, &ProvisionResponse{Error: "request already in progress"})
		return
	}
	defer release()

	job := NewProvisionJob(payload, eLog)
	job.WorkspaceName = workspaceName(job.CloudProvider, job.CustomerName, job.ProjectId, eLog)
	job.TerraformVersion = putils.GetTerraformVersion(job.CloudProvider)
	if job.CloudProvider == "oci" {
		job.TfWrapper, err = tf.NewTfWrapper(tf.WrapperContext{
			ServiceType:      terraform.ServiceTypeOrm,
			OciTenancyId:     job.OciTenancyId,
			OciCompartmentId: job.OciParentCompId,
			RegionName:       job.RegionName,
		})
		if err != nil {
			eLog.LogError("Client error: %v, payload=%#v", err, payload)
			c.JSON(http.StatusInternalServerError, &ProvisionResponse{Error: err.Error()})
			return
		}
	} else {
		job.TfWrapper = tf.NewTfWrapperDefault()
	}
	eLog.LogInfo("Workspace name: %v, TerraformVersion: %v", job.WorkspaceName, job.TerraformVersion)

	// load DB entry
	job.PrjMgmtEntry, err = dbAccessor.GetPrjMgmtTfByKey(job.ProjectId, job.CloudProvider, job.OciTenancyId, eLog)
	triggerUpdate := time.Now().Unix()
	setMigrationAttributes := func() {
		if job.Import {
			if job.Preview {
				job.PrjMgmtEntry.ImportCount = sql.Int64(job.PrjMgmtEntry.ImportCount.Int64() + 1)
				job.PrjMgmtEntry.ImportTriggerUpdate = sql.Int64(triggerUpdate)
			} else {
				job.PrjMgmtEntry.MigrationCount = sql.Int64(job.PrjMgmtEntry.MigrationCount.Int64() + 1)
				job.PrjMgmtEntry.MigrationTriggerUpdate = sql.Int64(triggerUpdate)
			}
		}
	}
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			eLog.LogInfo("GetPrjMgmtTfByKey returns ErrNoRows, creating one: prjID=%v, cloudProvider=%v",
				job.ProjectId, job.CloudProvider)
			job.PrjMgmtEntry = &orch_project_management_tf_table.Row{
				ProjectID:         sql.String(job.ProjectId),
				ProjectName:       sql.String(job.ProjectName),
				CustomerName:      sql.String(job.CustomerName),
				CloudProvider:     sql.String(job.CloudProvider),
				OciTenancyId:      sql.String(job.OciTenancyId),
				RegionName:        sql.String(job.RegionName),
				OciParentCompId:   sql.String(job.OciParentCompId),
				DebugID:           sql.String(job.DebugId),
				WorkspaceName:     sql.String(job.WorkspaceName),
				TriggerUpdate:     sql.Int64(triggerUpdate),
				DeployedByProvSvc: sql.Bool(!job.Import), // Greenfield: true, Brownfield: false -> true after migration
			}
			setMigrationAttributes()
			eLog.LogInfo("Creating %+v", job.PrjMgmtEntry)
			if err = dbAccessor.CreatePrjMgmtTf(job.PrjMgmtEntry, eLog); err != nil {
				eLog.LogError("Failed CreatePrjMgmtTf: %v", err)
				c.JSON(http.StatusBadRequest, &ProvisionResponse{Error: err.Error()})
				return
			}
		} else {
			eLog.LogError("Failed GetPrjMgmtTf: %v, job=%+v", err, job)
			c.JSON(http.StatusBadRequest, &ProvisionResponse{Error: err.Error()})
			return
		}
	} else {
		eLog.LogInfo("Found PrjMgmtTf row associated with prjID=%v, cloudProvider=%v: %+v",
			job.ProjectId, job.CloudProvider, job.PrjMgmtEntry)
		// reset TriggerUpdate and ErrorReason as this is a new invocation
		job.PrjMgmtEntry.TriggerUpdate = sql.Int64(triggerUpdate)
		job.PrjMgmtEntry.ErrorReason = sql.String("")
		setMigrationAttributes()
		err = updateDbEntry(job)
		if err != nil {
			c.JSON(http.StatusBadRequest, &ProvisionResponse{Error: err.Error()})
			return
		}
	}

	err = provision(job)
	if err != nil {
		c.Status(http.StatusInternalServerError)
	} else {
		c.JSON(http.StatusOK, &ProvisionResponse{ProjectNumber: job.ProjectNumber, OciCompartmentId: job.OciCompartmentId})
	}
}

func NewDeleteJob(input *DeletePayload, eLog *clogger.EventLogger) *DeleteJob {
	return &DeleteJob{
		EventLogger:   eLog,
		ProjectId:     input.ProjectId,
		CloudProvider: input.CloudProvider,
		OciTenancyId:  input.OciTenancyId,
		CustomerName:  input.CustomerName,
		RegionName:    input.RegionName,
		DebugId:       input.DebugId,
	}
}

type DeleteJob struct {
	// logistics
	EventLogger   *clogger.EventLogger
	WorkspaceName string
	TfWrapper     tf.TfWrapper

	// request input
	ProjectId       string
	CloudProvider   string
	OciTenancyId    string
	OciParentCompId string
	CustomerName    string
	RegionName      string
	DebugId         string

	// config run
	WorkspaceId string // Not used in case of OCI
	RunType     terraform.RunType
	RunId       string
	RunURL      string
	RunOutput   *terraform.RunOutput
	RunStatus   string
}

func (job *DeleteJob) String() string {
	return fmt.Sprintf("Project.DeleteJob{projId=%v, workspace=%v}",
		job.ProjectId, job.WorkspaceName)
}

func destroy(job *DeleteJob) error {
	var err error
	eLog := job.EventLogger
	tfWrapper := job.TfWrapper

	wid, _, err := tfWrapper.ReadWorkspaceIDs(job.WorkspaceName)
	if err != nil {
		eLog.LogError("Failed to read workspace %v: %v", job.WorkspaceName, err)
		return err
	} else if wid == nil {
		eLog.LogInfo("Workspace %v does not exist, skip destroy", job.WorkspaceName)
		return nil
	}
	job.WorkspaceId = *wid

	runDesp := fmt.Sprintf("%v (%s)", job.WorkspaceName, job.RunType)
	runMsg := fmt.Sprintf("Scheduling a run for %s", runDesp)
	runOption := terraform.RunOptions{
		Message:                 &runMsg,
		Type:                    job.RunType,
		WaitForCompletion:       false,
		UseCurrentConfiguration: true,
	}
	job.RunOutput, err = tfWrapper.Run(eLog, job.WorkspaceName, false, runOption)
	if err != nil {
		eLog.LogError("Failed run %v: %v", runDesp, err)
		return err
	}
	job.RunURL = job.RunOutput.URI

	// Wait for the run to come to an EndStatus.
	// TODO: Currently there is no timeout on a Terraform Run. Revisit to see if needed.
	for {
		<-time.After(time.Second)
		job.RunOutput, err = tfWrapper.ReadRunStatus(eLog, job.RunOutput.Id)
		if err != nil {
			eLog.LogError("Failed to read status on run %v: %v", runDesp, err)
			return err
		}
		job.RunStatus = job.RunOutput.Status

		end, _, _, _ := tfWrapper.IsEndStatus(job.RunStatus)
		if end {
			break
		}
	}
	eLog.LogInfo("Run output: status=%v, runUrl=%s", job.RunOutput.Status, job.RunURL)
	if d := job.RunOutput.GetTfcRun(); d != nil {
		eLog.LogInfo("Run output TFC run: %+v", *d)
	}
	if d := job.RunOutput.GetImRevision(); d != nil {
		eLog.LogInfo("Run output IM deployment: %+v", *d)
	}
	if d := job.RunOutput.GetOrmJob(); d != nil {
		eLog.LogInfo("Run output ORM stack: %+v", *d)
	}

	_, plannedOnly, applied, errored := tfWrapper.IsEndStatus(job.RunStatus)
	if !plannedOnly && !applied {
		eLog.LogInfo("Unexpected run status %v, return error", job.RunStatus)
		if errored {
			// Dump error details for debugging
			tfWrapper.GetErrorDetails(eLog, job.RunId)
		}
		return pterraform.ErrTfRunFailed
	}
	return nil
}

type DeletePayload struct {
	ProjectId       string `json:"project_id"`
	CloudProvider   string `json:"cloud_provider"`
	OciTenancyId    string `json:"oci_tenancy_id"`
	OciParentCompId string `json:"oci_parent_comp_id"`
	CustomerName    string `json:"customer_name"`
	RegionName      string `json:"region_name"`
	DebugId         string `json:"debug_id"`
}

func (rp *DeletePayload) validate() error {
	var err error
	ociEmptyOk := map[string]bool{
		"ProjectId": true, "CustomerName": true}
	nonOciEmptyOk := map[string]bool{
		"OciTenancyId": true, "OciParentCompId": true, "CustomerName": true, "RegionName": true}
	validateInputNotEmpty := func(additionals ...map[string]bool) error {
		emptyAllowed := make(map[string]bool)
		for _, m := range additionals {
			for k, v := range m {
				emptyAllowed[k] = v
			}
		}
		return putils.ValidateInputNotEmpty(rp, emptyAllowed)
	}
	noOciSpecifics := func() error {
		if rp.OciTenancyId != "" {
			return fmt.Errorf("oci tenancy is not supported for %s cloud provider", rp.CloudProvider)
		} else if rp.OciParentCompId != "" {
			return fmt.Errorf("oci parent comp is not supported for %s cloud provider", rp.CloudProvider)
		} else if rp.RegionName != "" {
			return fmt.Errorf("region is not supported for %s cloud provider", rp.CloudProvider)
		}
		return nil
	}
	switch rp.CloudProvider {
	case "gcp", "openstack":
		err = validateInputNotEmpty(nonOciEmptyOk)
		if err != nil {
			return err
		}
		err = noOciSpecifics()
		if err != nil {
			return err
		}
	case "oci":
		err = validateInputNotEmpty(ociEmptyOk)
		if err != nil {
			return err
		}
	default:
		return fmt.Errorf("unexpected cloud provider '%v', only 'gcp', 'openstack' and 'oci' is supported", rp.CloudProvider)
	}
	return nil
}

type DeleteResponse struct {
	Error string `json:"error,omitempty"`
}

func DeleteHandler(c *gin.Context) {
	var err error
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	dbAccessor := db.GetDbAccessor()

	// retrieve and validate input
	payload := &DeletePayload{}
	err = c.ShouldBindJSON(payload)
	if err != nil {
		c.JSON(http.StatusBadRequest, &DeleteResponse{Error: err.Error()})
		return
	}
	err = payload.validate()
	if err != nil {
		c.JSON(http.StatusBadRequest, &DeleteResponse{Error: err.Error()})
		return
	}
	eLog.SetLoggerTag(payload.DebugId)

	conflict, release := serialize(payload.ProjectId, payload.CloudProvider, payload.OciTenancyId, eLog)
	if conflict {
		c.JSON(http.StatusConflict, &ProvisionResponse{Error: "request already in progress"})
		return
	}
	defer release()

	job := NewDeleteJob(payload, eLog)
	job.WorkspaceName = workspaceName(job.CloudProvider, job.CustomerName, job.ProjectId, eLog)
	job.RunType = terraform.RunTypeDestroy
	if job.CloudProvider == "oci" {
		job.TfWrapper, err = tf.NewTfWrapper(tf.WrapperContext{
			ServiceType:      terraform.ServiceTypeOrm,
			OciTenancyId:     job.OciTenancyId,
			OciCompartmentId: job.OciParentCompId,
			RegionName:       job.RegionName,
		})
		if err != nil {
			c.JSON(http.StatusInternalServerError, &DeleteResponse{Error: err.Error()})
			return
		}
	} else {
		job.TfWrapper = tf.NewTfWrapperDefault()
	}

	err = destroy(job)
	if err != nil {
		c.Status(http.StatusInternalServerError)
	} else {
		// delete DB entry
		prjMgmtEntry, err := dbAccessor.GetPrjMgmtTfByKey(job.ProjectId, job.CloudProvider, job.OciTenancyId, eLog)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				// DB entry missing, should not happen: nothing to do just log an error
				eLog.LogError("DB entry missing at deletion: job=%+v", job)
			} else {
				eLog.LogError("Failed GetPrjMgmtTf: %v, job=%+v", err, job)
				c.JSON(http.StatusBadRequest, &ProvisionResponse{Error: err.Error()})
				return
			}
		}
		err = dbAccessor.DeletePrjMgmtTf(prjMgmtEntry, eLog)
		if err != nil {
			eLog.LogError("Failed DeletePrjMgmtTf: %v, job=%+v", err, job)
			c.JSON(http.StatusBadRequest, &ProvisionResponse{Error: err.Error()})
			return
		}
		c.Status(http.StatusOK)
	}
}
