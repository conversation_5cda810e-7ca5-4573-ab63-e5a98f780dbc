package instances

import (
	"encoding/json"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/terraform"
	pconfig "orchestrator/provision_service/infra/config"
	"orchestrator/provision_service/infra/db"
	"orchestrator/provision_service/terraform/gcp"
	putils "orchestrator/provision_service/utils"
	"os"
	"path"
	"path/filepath"
	"strings"
	"text/template"

	"go.panw.local/provision/deployment"
)

// Generate config geared towards deployment on GCP
func generateGcpConfig(job *ProvisionJob) error {
	var err error
	eLog := job.EventLogger
	tfWrapper := job.TfWrapper
	dbAccessor := db.GetDbAccessor()
	// determine the template and the workspace directories
	job.ProviderDir = filepath.Join(tfWrapper.GetTemplateDir(), job.CloudProvider, "instances")
	job.WorkspaceDir = filepath.Join(tfWrapper.GetStateDir(), job.WorkspaceName)

	// create per-workspace directory
	statinfo, err := os.Stat(job.ProviderDir)
	if err != nil {
		eLog.LogError("Error retrieving info on provider directory %v: %v", job.ProviderDir, err)
		return err
	}
	if err = putils.CleanDirectory(job.WorkspaceDir, statinfo.Mode()); err != nil {
		eLog.LogError("Error making workspace directory %v: %v", job.WorkspaceDir, err)
		return err
	}

	// copy credentials - GCP
	credentialFilename := "credentials.json"
	credentialSrc := filepath.Clean(tfWrapper.GetGcpCredential())
	credentialDst := filepath.Join(job.WorkspaceDir, credentialFilename)
	err = terraform.File(credentialSrc, credentialDst)
	if err != nil {
		eLog.LogError("Error copying credentials file to workspace directory, src_file=%v, dst_file=%v: %v",
			credentialSrc, credentialDst, err)
		return err
	}

	// prepare configuration parameters
	provider, err := gcp.PrepareProviderParams(job.ProjectId, job.RegionName, putils.GetGoogleProviderVersion(), eLog)
	if err != nil {
		eLog.LogError("Error preparing provider params: %v", err)
		return err
	}
	//return firewall rules objects as well.
	vmInstances, nlb, ngpaFirewallRules, err := gcp.PrepareVmParams(job.ProjectId, job.CustMasterEntry, job.InstMasterEntries,
		job.NlbConfigEntry, eLog)
	if err != nil {
		eLog.LogError("Error preparing vm instance params: %v", err)
		return err
	}

	// leverage deployment API support to generate terraform configuration ensuring consistency
	mainTfPath := filepath.Join(job.WorkspaceDir, "main.tf")
	f, err := os.Create(mainTfPath)
	if err != nil {
		eLog.LogError("Error creating destination TF configuration file: %v", err)
		return err
	}
	defer f.Close()

	di := gcp.DeploymentInput{
		CustId:                   job.CustId,
		TenantId:                 job.TenantId,
		Workspace:                job.WorkspaceName,
		Project:                  job.ProjectId,
		Region:                   job.RegionName,
		EdgeLocationRegion:       job.RegionMasterEntry.EdgeLocationRegionName.String(),
		Clusters:                 vmInstances,
		Nlb:                      nlb,
		ColoOnboardings:          job.ColoOnboardingMasterEntries,
		ColoLinks:                job.ColoConnLinkMasterEntries,
		ColoLinkProjects:         job.ColoConnLinkProjects,
		TrafficMirroringCfgEntry: job.TrafficMirroringCfgEntry,
		ZonalProvider:            false,
		DpNetworkFwRules:         ngpaFirewallRules,
		ELog:                     eLog,
	}
	dConfig, dTransformed, err := gcp.GenerateTfConfigFromDeployment(di, tfWrapper.GetTemplateDir(), f)
	if err != nil {
		eLog.LogError("Failed to generate TF config: %v", err)
		return err
	}
	eLog.LogInfo("Generated Terraform configuration with config = %+v, and transformed = %+v", dConfig, dTransformed)

	tfMigrated := job.TfImportEntry != nil
	var importZones []string
	if tfMigrated {
		if zs := job.TfImportEntry.Zones.String(); len(zs) > 0 {
			importZones = strings.Split(zs, ",")
		}
	}
	// produce own terraform output configuration file as it differs from what deployment API support offers
	gen := func(templName string) error {
		templFile := filepath.Join(job.ProviderDir, templName)
		t, e := template.New(templName).ParseFiles(templFile)
		if e != nil {
			eLog.LogError("Error parsing template file %v: %v", templFile, e)
			return e
		}
		// Derive TF Configuration filename by replacing the file extension of the src golang template file.
		ext := path.Ext(templName)
		tfName := templName[0:len(templName)-len(ext)] + ".tf"
		tfFile := filepath.Join(job.WorkspaceDir, tfName)
		f, e := os.Create(tfFile)
		if e != nil {
			eLog.LogError("Error creating TF configuraiton file %v: %v", tfFile, e)
			return e
		}
		defer f.Close()
		e = t.Execute(f,
			struct {
				Provider     *gcp.Provider
				VMs          map[int64][]*gcp.VMInstance
				ImportZones  []string
				DConfig      *deployment.Config
				DTransformed *gcp.TransformedResources
			}{provider, vmInstances, importZones, dConfig, dTransformed})
		if e != nil {
			eLog.LogError("Error processing template %v: %v", t.Name(), e)
			return e
		}
		return nil
	}
	if err = gen("outputs.gotmpl"); err != nil {
		return err
	}
	// Generate aliased provider blocks if this workspace has gone through terraform migration.
	// This is to avoid terraform plan errors if any resource is still associated with an aliased provider. See CYR-37468.
	if tfMigrated {
		if err = gen("import_providers.gotmpl"); err != nil {
			return err
		}
	}

	// upload to S3
	if pconfig.GetConfig().ProvisionService.S3Upload {
		err = putils.WriteWorkspaceDirToS3(eLog, putils.TerraformPrefix, job.WorkspaceName, job.WorkspaceDir, []string{credentialFilename})
		// If uploading to S3 fails, allow the job to continue but log an error
		if err != nil {
			eLog.LogError("Error uploading TF configuration to S3: %v", err)
		} else {
			job.S3UploadDone = true
		}
	}

	// update progress in DB
	var metadata []byte
	metadata, err = json.Marshal(job)
	if err != nil {
		eLog.LogError("Failed to marshal metadata: %v", err)
		return err
	}
	job.InstMgmtTfEntry.Metadata = sql.String(metadata)
	job.InstMgmtTfEntry.FinishedUpdate = sql.Int64(job.TriggerUpdate)
	eLog.LogDebug("Updating Stage 2 (FinishedUpdate): %+v", job.InstMgmtTfEntry)
	if err = dbAccessor.UpdateInstMgmtTfStage2(job.InstMgmtTfEntry, eLog); err != nil {
		eLog.LogError("Failed UpdateInstMgmtTfStage2 (FinishedUpdate): %v, entry=+%v", err, job.InstMgmtTfEntry)
		return err
	}

	// determine Terraform RunType
	if dTransformed.Empty() {
		job.RunType = terraform.RunTypeDestroy
	} else {
		job.RunType = terraform.RunTypeApply
	}
	job.RunType = tfWrapper.OverrideRunType(job.RunType)
	return nil
}
