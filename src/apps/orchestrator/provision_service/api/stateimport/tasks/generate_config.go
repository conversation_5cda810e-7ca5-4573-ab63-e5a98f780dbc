package tasks

import (
	"orchestrator/libs/go/dbaccess/models/colo_connect_link_master"
	"orchestrator/libs/go/dbaccess/models/colo_onboarding_master"
	"orchestrator/libs/go/dbaccess/models/cust_master"
	"orchestrator/libs/go/dbaccess/models/instance_master"
	"orchestrator/libs/go/dbaccess/models/network_load_balancer_config"
	"orchestrator/libs/go/dbaccess/models/traffic_mirroring_cfg"
	"orchestrator/libs/go/terraform"
	"orchestrator/provision_service/api/stateimport/definitions"
	pconfig "orchestrator/provision_service/infra/config"
	"orchestrator/provision_service/infra/tf"
	"orchestrator/provision_service/libs/job"
	"orchestrator/provision_service/terraform/gcp"
	putils "orchestrator/provision_service/utils"
	"os"
	"path"
	"path/filepath"
	"sort"
	"text/template"

	"go.panw.local/pangolin/clogger"
	"go.panw.local/provision/deployment"
)

type GenerateConfigJobCallback struct {
	SetProviderDir  func(providerDir string)
	SetWorkspaceDir func(workspaceDir string)
	SetZones        func(zones []string)
	S3UploadDone    func()
}
type GenerateConfigJobData struct {
	EventLogger                 *clogger.EventLogger
	TfWrapper                   tf.TfWrapper
	CustId                      int64
	TenantId                    int64
	CloudProvider               string
	WorkspaceName               string
	ProjectId                   string
	RegionName                  string
	EdgeLocationRegionName      string
	InstMasterEntries           []*instance_master.Row
	CustMasterEntry             *cust_master.Row
	NlbConfigEntry              *network_load_balancer_config.Row
	ColoOnboardingMasterEntries []*colo_onboarding_master.Row
	ColoConnLinkMasterEntires   []*colo_connect_link_master.Row
	ColoConnLinkProjects        map[int64]string
	TrafficMirroringCfgEntry    *traffic_mirroring_cfg.Row
}

type GenerateConfigJob interface {
	GenerateConfigData() GenerateConfigJobData
	GenerateConfigCallback() *GenerateConfigJobCallback
}

func GenerateConfig(job GenerateConfigJob) error {
	var err error
	input := job.GenerateConfigData()
	cb := job.GenerateConfigCallback()
	eLog := input.EventLogger
	tfWrapper := input.TfWrapper

	// determine the template and the workspace directories
	providerDir := filepath.Join(tfWrapper.GetTemplateDir(), input.CloudProvider, "instances")
	workspaceDir := filepath.Join(tfWrapper.GetStateDir(), input.WorkspaceName)
	if cb != nil {
		cb.SetProviderDir(providerDir)
		cb.SetWorkspaceDir(workspaceDir)
	}

	// create per-workspace directory
	statinfo, err := os.Stat(providerDir)
	if err != nil {
		eLog.LogError("Error retrieving info on provider directory %v: %v", providerDir, err)
		return err
	}
	if err = putils.CleanDirectory(workspaceDir, statinfo.Mode()); err != nil {
		eLog.LogError("Error making workspace directory %v: %v", workspaceDir, err)
		return err
	}

	// copy credentials - GCP
	credentialFilename := "credentials.json"
	credentialSrc := filepath.Clean(tfWrapper.GetGcpCredential())
	credentialDst := filepath.Join(workspaceDir, credentialFilename)
	err = terraform.File(credentialSrc, credentialDst)
	if err != nil {
		eLog.LogError("Error copying credentials file to workspace directory, src_file=%v, dst_file=%v: %v",
			credentialSrc, credentialDst, err)
		return err
	}

	// prepare configuration parameters
	provider, err := gcp.PrepareProviderParams(input.ProjectId, input.RegionName, putils.GetGoogleProviderVersion(), eLog)
	if err != nil {
		eLog.LogError("Error preparing provider params: %v", err)
		return err
	}
	vmInstances, nlb, ngpaFirewallRules, err := gcp.PrepareVmParams(input.ProjectId, input.CustMasterEntry, input.InstMasterEntries,
		input.NlbConfigEntry, eLog)
	if err != nil {
		eLog.LogError("Error preparing vm instance params: %v", err)
		return err
	}

	// take inventory of zones, needed by state import
	zoneMap := make(map[string]bool)
	for _, vms := range vmInstances {
		for _, vm := range vms {
			zone := vm.Zone
			zoneMap[zone] = true
		}
	}
	if nlb != nil {
		for _, nlbitem := range nlb {
			for _, zone := range nlbitem.ZonesList {
				zoneMap[zone] = true
			}
		}
	}
	// sort zones
	var zones []string
	for zone, _ := range zoneMap {
		zones = append(zones, zone)
	}
	sort.Strings(zones)
	if len(zones) > 0 {
		cb.SetZones(zones)
	}

	// leverage deployment API support to generate terraform configuration ensuring consistency
	f, err := os.Create(filepath.Join(workspaceDir, "main.tf"))
	if err != nil {
		eLog.LogError("Error creating destination TF configuration file: %v", err)
		return err
	}
	di := gcp.DeploymentInput{
		CustId:                   input.CustId,
		TenantId:                 input.TenantId,
		Workspace:                input.WorkspaceName,
		Project:                  input.ProjectId,
		Region:                   input.RegionName,
		EdgeLocationRegion:       input.EdgeLocationRegionName,
		Clusters:                 vmInstances,
		Nlb:                      nlb,
		ColoOnboardings:          input.ColoOnboardingMasterEntries,
		ColoLinks:                input.ColoConnLinkMasterEntires,
		ColoLinkProjects:         input.ColoConnLinkProjects,
		TrafficMirroringCfgEntry: input.TrafficMirroringCfgEntry,
		ZonalProvider:            true,
		DpNetworkFwRules:         ngpaFirewallRules,
		ELog:                     eLog,
	}
	dConfig, dTransformed, err := gcp.GenerateTfConfigFromDeployment(di, tfWrapper.GetTemplateDir(), f)
	if err != nil {
		eLog.LogError("Failed to generate TF config: %v", err)
		return err
	}
	eLog.LogInfo("Generated Terraform configuration with config = %+v, and transformed = %+v", dConfig, dTransformed)

	gen := func(templName string) error {
		templFile := filepath.Join(providerDir, templName)
		t, e := template.New(templName).ParseFiles(templFile)
		if e != nil {
			eLog.LogError("Error parsing template file %v: %v", templFile, e)
			return e
		}
		// Derive TF Configuration filename by replacing the file extension of the src golang template file.
		ext := path.Ext(templName)
		tfName := templName[0:len(templName)-len(ext)] + ".tf"
		tfFile := filepath.Join(workspaceDir, tfName)
		f, e := os.Create(tfFile)
		if e != nil {
			eLog.LogError("Error creating TF configuration file %v: %v", tfFile, e)
			return e
		}
		defer f.Close()
		e = t.Execute(f,
			struct {
				Provider     *gcp.Provider
				VMs          map[int64][]*gcp.VMInstance
				DConfig      *deployment.Config
				DTransformed *gcp.TransformedResources
				Zones        []string
			}{provider, vmInstances, dConfig, dTransformed, zones})
		if e != nil {
			eLog.LogError("Error processing template %v: %v", t.Name(), e)
			return e
		}
		return nil
	}
	// produce own terraform output configuration file as it is differs from what deployment API support offers
	if err = gen("outputs.gotmpl"); err != nil {
		return err
	}
	// produce import
	if err = gen("imports.gotmpl"); err != nil {
		return err
	}

	// upload to S3
	if pconfig.GetConfig().ProvisionService.S3Upload {
		err = putils.WriteWorkspaceDirToS3(eLog, putils.TerraformPrefix, input.WorkspaceName, workspaceDir, []string{credentialFilename})
		// If uploading to S3 fails, allow the job to continue but log an error
		if err != nil {
			eLog.LogError("Error uploading TF configuration to S3: %v", err)
		} else if cb != nil {
			cb.S3UploadDone()
		}
	}

	return nil
}

type wrapper struct {
	*job.Job
}

func (w wrapper) GenerateConfigData() GenerateConfigJobData {
	data := w.GetUserData().(*definitions.StateImportData)
	return GenerateConfigJobData{
		EventLogger:                 w.GetEventLogger(),
		TfWrapper:                   w.TfWrapper,
		CustId:                      data.CustId,
		TenantId:                    data.TenantId,
		CloudProvider:               data.CloudProvider,
		WorkspaceName:               data.WorkspaceName,
		ProjectId:                   data.ProjectId,
		RegionName:                  data.RegionName,
		EdgeLocationRegionName:      data.RegionMasterEntry.EdgeLocationRegionName.String(),
		InstMasterEntries:           data.InstMasterEntries,
		CustMasterEntry:             data.CustMasterEntry,
		NlbConfigEntry:              data.NlbConfigEntry,
		ColoOnboardingMasterEntries: data.ColoOnboardingMasterEntries,
		ColoConnLinkMasterEntires:   data.ColoConnLinkMasterEntries,
		ColoConnLinkProjects:        data.ColoConnLinkProjects,
		TrafficMirroringCfgEntry:    data.TrafficMirroringCfgEntry,
	}
}

func (w wrapper) GenerateConfigCallback() *GenerateConfigJobCallback {
	data := w.GetUserData().(*definitions.StateImportData)
	return &GenerateConfigJobCallback{
		SetProviderDir:  func(providerDir string) { data.ProviderDir = providerDir },
		SetWorkspaceDir: func(workspaceDir string) { data.WorkspaceDir = workspaceDir },
		SetZones:        func(zones []string) { data.Zones = zones },
		S3UploadDone:    func() { data.S3UploadDone = true },
	}
}

func GenerateConfigWrapper(job *job.Job) error {
	wrapper := wrapper{job}
	return GenerateConfig(wrapper)
}
