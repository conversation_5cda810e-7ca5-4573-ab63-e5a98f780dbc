package networks

import (
	"fmt"
	"net/http"
	"orchestrator/libs/go/dbaccess/models/oci_tenant_network_info"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/orm"
	"orchestrator/libs/go/terraform"
	"orchestrator/libs/go/utils"
	"orchestrator/provision_service/definitions"
	"orchestrator/provision_service/infra/config"
	pconfig "orchestrator/provision_service/infra/config"
	"orchestrator/provision_service/infra/db"
	"orchestrator/provision_service/infra/logger"
	"orchestrator/provision_service/infra/tf"
	syncmap "orchestrator/provision_service/libs/sync_map"
	pterraform "orchestrator/provision_service/terraform"
	"orchestrator/provision_service/terraform/gcp"
	"orchestrator/provision_service/terraform/oci"
	"orchestrator/provision_service/terraform/osp"
	putils "orchestrator/provision_service/utils"
	"os"
	"path/filepath"
	"strconv"
	"text/template"
	"time"

	"github.com/gin-gonic/gin"
	"go.panw.local/pangolin/clogger"
)

var serializer = syncmap.New()

type serializerKey struct {
	projectId     string
	tenantId      int64
	customerName  string
	cloudProvider string
	networkType   string
	regionName    string
}

func serialize(projectId string, tenantId int64, customerName, cloudProvider, networkType, regionName string, eLog *clogger.EventLogger) (bool, func()) {
	key := serializerKey{projectId, tenantId, customerName, cloudProvider, networkType, regionName}
	if _, exists := serializer.Get(key); exists {
		// There is already a request in progress
		eLog.LogInfo("Request already in progress for project_id=%s, tenant_id=%d, customer_name=%s, cloud_provider=%s, network_type=%s, region_name=%s, reply with %d",
			projectId, tenantId, customerName, cloudProvider, networkType, regionName, http.StatusConflict)
		return true, nil
	}
	serializer.Set(key, true)
	return false, func() { serializer.Delete(key) }
}

func NewProvisionJob(input *ProvisionPayload, eLog *clogger.EventLogger) *ProvisionJob {
	return &ProvisionJob{
		EventLogger:      eLog,
		ProjectId:        input.ProjectId,
		TenantId:         input.TenantId,
		CloudProvider:    input.CloudProvider,
		NetworkType:      input.NetworkType,
		DebugId:          input.DebugId,
		Networks:         input.Networks,
		CustomerName:     input.CustomerName,
		OciTenancyId:     input.OciTenancyId,
		OciCompartmentId: input.OciCompartmentId,
		OciNetworkProps:  input.OciNetworkProps,
		RegionName:       input.RegionName,
	}
}

type ProvisionJob struct {
	// logistics
	EventLogger      *clogger.EventLogger
	WorkspaceName    string
	ProviderDir      string
	WorkspaceDir     string
	S3UploadDone     bool
	TerraformVersion string
	TfWrapper        tf.TfWrapper

	// request input
	ProjectId        string
	TenantId         int64
	CustomerName     string
	OciTenancyId     string
	OciCompartmentId string
	OciNetworkProps  *definitions.OciNetworkProps
	CloudProvider    string
	NetworkType      string
	DebugId          string
	Networks         []*definitions.NetworkProps
	RegionName       string

	// config run
	WorkspaceId string // Not used in case of OCI
	RunType     terraform.RunType
	RunId       string
	RunURL      string
	RunOutput   *terraform.RunOutput
	RunStatus   string

	// output
	OciNetwork *orm.Network
}

func (job *ProvisionJob) String() string {
	if job.CloudProvider == "oci" {
		return fmt.Sprintf("Networks.ProvisionJob{customerName=%v, tenancyId=%v, region=%v, compartmentId=%v, workspace=%v}",
			job.CustomerName, job.OciTenancyId, job.RegionName, job.OciCompartmentId, job.WorkspaceName)
	} else {
		return fmt.Sprintf("Networks.ProvisionJob{projId=%v, workspace=%v}",
			job.ProjectId, job.WorkspaceName)
	}
}

// workspaceName retrurns the workspace name to use.
func WorkspaceName(cloudProvider, customerName, regionName, projectId, networkType string, tenantId int64,
	eLog *clogger.EventLogger) string {
	var name string
	if cloudProvider == "oci" {
		name = fmt.Sprintf("%v_network_%v", customerName, regionName)
	} else if cloudProvider == "openstack" {
		name = fmt.Sprintf("%v_networks_%v", projectId, cloudProvider)
	} else {
		name = fmt.Sprintf("%v_networks_%v", strconv.FormatInt(tenantId, 10), cloudProvider)
	}
	if networkType != "" {
		name = fmt.Sprintf("%v_%v", name, networkType)
	}
	if cloudProvider != "openstack" {
		// FIXME: This is meant to disambigurate workspace names in case two CYR environments
		//        use the same tenant as both may end up in the same TFC. Skip openstack
		//        case for now to avoid breaking workspaces already exist.
		// suffix workspace name with project name
		if prj := pconfig.GetConfig().ProvisionService.TfPrj; len(prj) > 0 {
			name = fmt.Sprintf("%v_%v", name, prj)
		}
	}
	eLog.LogInfo("Workspace name: %v, NetworkType: %v", name, networkType)
	return name
}

func generateConfig(job *ProvisionJob) error {
	var err error
	var credentialFilename, templateDir string
	var templateArg any
	eLog := job.EventLogger
	tfWrapper := job.TfWrapper

	// Determine the template and the workspace directories
	if job.CloudProvider == "openstack" {
		templateDir = osp.OspDirName
	} else {
		templateDir = job.CloudProvider
	}
	job.ProviderDir = filepath.Join(tfWrapper.GetTemplateDir(), templateDir, "networks")
	job.WorkspaceDir = filepath.Join(tfWrapper.GetStateDir(), job.WorkspaceName)

	// Create per-workspace directory
	statinfo, err := os.Stat(job.ProviderDir)
	if err != nil {
		eLog.LogError("Error retrieving info on provider directory %v: %v", job.ProviderDir, err)
		return err
	}
	if err = putils.CleanDirectory(job.WorkspaceDir, statinfo.Mode()); err != nil {
		eLog.LogError("Error making workspace directory %v: %v", job.WorkspaceDir, err)
		return err
	}

	templateFileName := "main.gotmpl"
	tfFileName := "main.tf"
	switch job.CloudProvider {
	case "gcp":
		// Copy Credentials - GCP
		credentialFilename = "credentials.json"
		credentialSrc := filepath.Clean(tfWrapper.GetGcpCredential())
		credentialDst := filepath.Join(job.WorkspaceDir, credentialFilename)
		err = terraform.File(credentialSrc, credentialDst)
		if err != nil {
			eLog.LogError("Error copying credentials file to workspace directory, src_file=%v, dst_file=%v: %v",
				credentialSrc, credentialDst, err)
			return err
		}

		// prepare configuration parameters
		provider, err := gcp.PrepareProviderParams(job.ProjectId, "", putils.GetGoogleProviderVersion(), eLog)
		if err != nil {
			eLog.LogError("Error preparing provider params: %v", err)
			return err
		}
		networks, err := gcp.PrepareNetworks(job.ProjectId, job.Networks, eLog)
		if err != nil {
			eLog.LogError("Error preparing network params: %v", err)
			return err
		}
		templateArg = struct {
			Provider *gcp.Provider
			Networks []*gcp.Network
		}{provider, networks}

	case "openstack":
		credentialFilename = osp.CredentialFileName
		if _, err := osp.SetupTfCommon(tfWrapper, job.WorkspaceDir, job.ProjectId, eLog); err != nil {
			eLog.LogError("Openstack project '%v' network tf setup failed", job.ProjectId)
			return err
		}
		networks, err := osp.PrepareNetworks(job.ProjectId, job.Networks, eLog)
		if err != nil {
			eLog.LogError("Openstack project '%v' network prepare failed", job.ProjectId)
			return err
		}
		templateArg = networks

	case "oci":
		eLog.LogInfo("Copying from %v to %v", job.ProviderDir, job.WorkspaceDir)
		err = terraform.CpDir(job.ProviderDir, job.WorkspaceDir)
		if err != nil {
			eLog.LogError("error copying template directory %v", err)
			return err
		}
		// prepare configuration parameters
		provider, err := oci.PrepareProviderParams(job.RegionName, eLog)
		if err != nil {
			eLog.LogError("Error preparing provider params: %v", err)
			return err
		}
		network, err := oci.PrepareNetworks(job.OciCompartmentId, job.CustomerName, job.OciNetworkProps, job.TenantId, eLog)
		if err != nil {
			eLog.LogError("Error preparing network params: %v", err)
			return err
		}
		templateFileName = "terraform.gotmpl"
		tfFileName = "terraform.tfvars"
		templateArg = struct {
			Provider *oci.Provider
			Network  *oci.Network
		}{provider, network}
	}

	// produce terraform configuration files
	var goTempl *template.Template
	var tfFile *os.File
	goTempl, tfFile, err = pterraform.ParseGotmpl(job.ProviderDir, job.WorkspaceDir, templateFileName, tfFileName, eLog)
	if err != nil {
		eLog.LogError("Error parsing go template: %v", err)
		return err
	}
	err = goTempl.Execute(tfFile, templateArg)
	if err != nil {
		eLog.LogError("Error processing template %v: %v", goTempl.Name(), err)
		return err
	}

	// upload to S3
	if pconfig.GetConfig().ProvisionService.S3Upload {
		err = putils.WriteWorkspaceDirToS3(eLog, putils.TerraformPrefix, job.WorkspaceName, job.WorkspaceDir, []string{credentialFilename})
		// If uploading to S3 fails, allow the job to continue but log an error
		if err != nil {
			eLog.LogError("Error uploading TF configuration to S3: %v", err)
		} else {
			job.S3UploadDone = true
		}
	}

	// determine Terraform RunType
	job.RunType = tfWrapper.OverrideRunType(terraform.RunTypeApply)
	return nil
}

func applyConfig(job *ProvisionJob) error {
	var err error
	var execMode, execModeData string
	eLog := job.EventLogger
	tfWrapper := job.TfWrapper

	if job.CloudProvider == "openstack" {
		execMode = config.GetConfig().ProvisionService.OspDeployConfig.ExecutionMode
		execModeData = config.GetConfig().ProvisionService.OspDeployConfig.AgentPoolId
	}

	if job.CloudProvider != "oci" {
		wid, err := tfWrapper.CreateWorkspace(eLog, tfWrapper.GetProject(), job.WorkspaceName, job.TerraformVersion,
			execMode, execModeData)
		if err != nil {
			eLog.LogError("Failed to create workspace %v: %v", job.WorkspaceName, err)
			return err
		}
		job.WorkspaceId = *wid
	}

	runDesp := fmt.Sprintf("%v", job.WorkspaceDir)
	runMsg := fmt.Sprintf("Scheduling a run for %s", runDesp)
	runOption := terraform.RunOptions{
		Message:           &runMsg,
		Directory:         &job.WorkspaceDir,
		Type:              job.RunType,
		WaitForCompletion: false,
		TfVersion:         job.TerraformVersion,
	}
	job.RunOutput, err = tfWrapper.Run(eLog, job.WorkspaceName, false, runOption)
	if err != nil {
		eLog.LogError("Failed run %v: %v", runDesp, err)
		return err
	}
	job.RunId = job.RunOutput.Id
	job.RunURL = job.RunOutput.URI
	job.RunStatus = job.RunOutput.Status

	// Wait for the run to come to an EndStatus.
	// TODO: Currently there is no timeout on a Terraform Run. Revisit to see if needed.
	for {
		<-time.After(time.Second)
		job.RunOutput, err = tfWrapper.ReadRunStatus(eLog, job.RunOutput.Id)
		if err != nil {
			eLog.LogError("Failed to read status on run %v: %v", runDesp, err)
			return err
		}
		job.RunStatus = job.RunOutput.Status

		end, _, _, _ := tfWrapper.IsEndStatus(job.RunStatus)
		if end {
			break
		}
	}
	eLog.LogInfo("Run output: status=%v, runUrl=%s", job.RunOutput.Status, job.RunURL)
	if d := job.RunOutput.GetTfcRun(); d != nil {
		eLog.LogInfo("Run output TFC run: %+v", *d)
	}
	if d := job.RunOutput.GetImRevision(); d != nil {
		eLog.LogInfo("Run output IM deployment: %+v", *d)
	}
	if d := job.RunOutput.GetOrmJob(); d != nil {
		eLog.LogInfo("Run output ORM stack: %+v", *d)
	}

	_, plannedOnly, applied, errored := tfWrapper.IsEndStatus(job.RunStatus)
	if applied {
		if job.CloudProvider == "oci" {
			outputs, err := tfWrapper.GetTerraformStateOutputs(eLog, job.WorkspaceName, job.RunId)
			if err != nil {
				eLog.LogError("Failed to retrieve output: %v, workspace=%v", err, job.WorkspaceName)
				return err
			}
			ormOutputs := outputs.GetOrmOutputs()
			if ormOutputs == nil {
				eLog.LogError("Failed to retrieve output: nil OCI outputs, workspace=%v", job.WorkspaceName)
				return err
			}
			output, err := orm.ParseJobOutputs(ormOutputs)
			if err != nil {
				eLog.LogError("Failed to parse ORM output %+v, workspace=%v", ormOutputs, job.WorkspaceName)
				return err
			}
			job.OciNetwork = output.Network
		}
	} else if !plannedOnly && !applied {
		eLog.LogInfo("Unexpected run status %v, return error", job.RunStatus)
		if errored {
			// Dump error details for debugging
			tfWrapper.GetErrorDetails(eLog, job.RunId)
		}
		return pterraform.ErrTfRunFailed
	} else if job.S3UploadDone {
		// clean up local copy of TF configuration, if fails allow the job to continue but log an error
		err = os.RemoveAll(job.WorkspaceDir)
		if err != nil {
			eLog.LogError("Failed to clean up workspace dir: %v, dir=%v", err, job.WorkspaceDir)
		}
	}
	return nil
}

func Provision(job *ProvisionJob) error {
	var err error
	eLog := job.EventLogger

	currTaskName := "generateConfig"
	eLog.LogInfo("Starting %v", currTaskName)
	err = generateConfig(job)
	if err != nil {
		eLog.LogInfo("Failed %v: %v", currTaskName, err)
		return err
	}
	currTaskName = "applyConfig"
	eLog.LogInfo("Starting %v", currTaskName)
	err = applyConfig(job)
	if err != nil {
		eLog.LogInfo("Failed %v: %v", currTaskName, err)
		return err
	}
	return nil
}

type ProvisionPayload struct {
	ProjectId        string                       `json:"project_id"`
	TenantId         int64                        `json:"tenant_id"`
	CustomerName     string                       `json:"customer_name"`
	OciTenancyId     string                       `json:"oci_tenancy_id"`
	OciCompartmentId string                       `json:"oci_compartment_id"`
	CloudProvider    string                       `json:"cloud_provider"`
	NetworkType      string                       `json:"network_type"`
	DebugId          string                       `json:"debug_id"`
	Networks         []*definitions.NetworkProps  `json:"networks"`
	OciNetworkProps  *definitions.OciNetworkProps `json:"oci_network"`
	RegionName       string                       `json:"region_name"`
}

func (rp *ProvisionPayload) validate() error {
	var err error
	if rp.CloudProvider == "oci" {
		err = putils.ValidateInputNotEmpty(rp, map[string]bool{"NetworkType": true, "DebugId": true, "ProjectId": true,
			"TenantId": true, "Networks": true})
		if err != nil {
			return err
		}
	} else {
		err = putils.ValidateInputNotEmpty(rp, map[string]bool{"NetworkType": true, "DebugId": true,
			"OciNetworkProps": true, "OciCompartmentId": true, "OciTenancyId": true, "CustomerName": true,
			"RegionName": true})
		if err != nil {
			return err
		}
	}
	if rp.CloudProvider != "gcp" && rp.CloudProvider != "openstack" && rp.CloudProvider != "oci" {
		return fmt.Errorf("unexpected cloud provider '%v', only 'gcp','openstack' and 'oci' are supported",
			rp.CloudProvider)
	}
	// TODO: additional validation of Networks input
	return nil
}

type ProvisionResponse struct {
	Error      string       `json:"error,omitempty"`
	OciNetwork *orm.Network `json:"oci_network,omitempty"`
}

// TODO: Currently API do not accept proxy info we have discontinue the use of proxy VM for new tenants.
//       Revisit when needed.

func ProvisionHandler(c *gin.Context) {
	var err error
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	// retrieve and validate input
	payload := &ProvisionPayload{}
	err = c.ShouldBindJSON(payload)
	if err != nil {
		c.JSON(http.StatusBadRequest, &ProvisionResponse{Error: err.Error()})
		return
	}
	err = payload.validate()
	if err != nil {
		c.JSON(http.StatusBadRequest, &ProvisionResponse{Error: err.Error()})
		return
	}
	eLog.SetLoggerTag(payload.DebugId)

	conflict, release := serialize(payload.ProjectId, payload.TenantId, payload.CustomerName, payload.CloudProvider,
		payload.NetworkType, payload.RegionName, eLog)
	if conflict {
		c.JSON(http.StatusConflict, &ProvisionResponse{Error: "request already in progress"})
		return
	}
	defer release()

	job := NewProvisionJob(payload, eLog)
	job.WorkspaceName = WorkspaceName(job.CloudProvider, job.CustomerName, job.RegionName, job.ProjectId, job.NetworkType, job.TenantId, eLog)
	job.TerraformVersion = putils.GetTerraformVersion(job.CloudProvider)
	if job.CloudProvider == "oci" {
		job.TfWrapper, err = tf.NewTfWrapper(tf.WrapperContext{
			ServiceType:      terraform.ServiceTypeOrm,
			OciTenancyId:     job.OciTenancyId,
			OciCompartmentId: job.OciCompartmentId,
			RegionName:       job.RegionName,
		})
		if err != nil {
			c.JSON(http.StatusBadRequest, &ProvisionResponse{Error: err.Error()})
			return
		}
	} else {
		job.TfWrapper = tf.NewTfWrapperDefault()
	}
	eLog.LogInfo("Workspace name: %v, TerraformVersion: %v", job.WorkspaceName, job.TerraformVersion)

	err = Provision(job)
	if err != nil {
		c.Status(http.StatusInternalServerError)
	} else {
		c.JSON(http.StatusOK, &ProvisionResponse{OciNetwork: job.OciNetwork})
	}
}

func NewDeleteJob(input *DeletePayload, eLog *clogger.EventLogger) *DeleteJob {
	return &DeleteJob{
		EventLogger:      eLog,
		TenantId:         input.TenantId,
		CloudProvider:    input.CloudProvider,
		ProjectId:        input.ProjectId,
		OciTenancyId:     input.OciTenancyId,
		OciCompartmentId: input.OciCompartmentId,
		CustomerName:     input.CustomerName,
		RegionName:       input.RegionName,
		NetworkType:      input.NetworkType,
		DebugId:          input.DebugId,
	}
}

type DeleteJob struct {
	// logistics
	EventLogger   *clogger.EventLogger
	WorkspaceName string
	TfWrapper     tf.TfWrapper

	// request input
	TenantId         int64
	CloudProvider    string
	ProjectId        string
	OciTenancyId     string
	OciCompartmentId string
	RegionName       string
	CustomerName     string
	NetworkType      string
	DebugId          string

	// config run
	WorkspaceId string
	RunType     terraform.RunType
	RunId       string
	RunURL      string
	RunOutput   *terraform.RunOutput
	RunStatus   string
}

func (job *DeleteJob) String() string {
	return fmt.Sprintf("Networks.DeleteJob{tenantId=%v, workspace=%v}",
		job.TenantId, job.WorkspaceName)
}

func deleteOciTenantNetwork(job *DeleteJob) error {
	eLog := job.EventLogger
	tfWrapper := job.TfWrapper
	dbAccessor := db.GetDbAccessor()
	ociTenantNetworkEntry := &oci_tenant_network_info.Row{
		CompartmentId: sql.String(job.OciCompartmentId),
		RegionName:    sql.String(job.RegionName),
	}
	if err := dbAccessor.DeleteTenantNetwork(ociTenantNetworkEntry, eLog); err != nil {
		eLog.LogError("Failed DeleteTenantNetwork: %v, entry=%+v", err, ociTenantNetworkEntry)
		return err
	}
	err := tfWrapper.DeleteWorkspace(eLog, job.WorkspaceName)
	return err
}

func destroy(job *DeleteJob) error {
	var err error
	eLog := job.EventLogger
	tfWrapper := job.TfWrapper

	wid, _, err := tfWrapper.ReadWorkspaceIDs(job.WorkspaceName)
	if err != nil {
		eLog.LogError("Failed to read workspace %v: %v", job.WorkspaceName, err)
		return err
	} else if wid == nil {
		eLog.LogInfo("Workspace %v does not exist, skip destroy", job.WorkspaceName)
		return nil
	}
	job.WorkspaceId = *wid

	runDesp := fmt.Sprintf("%v (%s)", job.WorkspaceName, job.RunType)
	runMsg := fmt.Sprintf("Scheduling a run for %s", runDesp)
	runOption := terraform.RunOptions{
		Message:                 &runMsg,
		Type:                    job.RunType,
		WaitForCompletion:       false,
		UseCurrentConfiguration: true,
	}
	job.RunOutput, err = tfWrapper.Run(eLog, job.WorkspaceName, false, runOption)
	if err != nil {
		eLog.LogError("Failed run %v: %v", runDesp, err)
		return err
	}
	job.RunId = job.RunOutput.Id
	job.RunURL = job.RunOutput.URI
	job.RunStatus = job.RunOutput.Status

	// Wait for the run to come to an EndStatus.
	// TODO: Currently there is no timeout on a Terraform Run. Revisit to see if needed.
	for {
		<-time.After(time.Second)
		job.RunOutput, err = tfWrapper.ReadRunStatus(eLog, job.RunOutput.Id)
		if err != nil {
			eLog.LogError("Failed to read status on run %v: %v", runDesp, err)
			return err
		}
		job.RunStatus = job.RunOutput.Status

		end, _, _, _ := tfWrapper.IsEndStatus(job.RunStatus)
		if end {
			break
		}
	}
	eLog.LogInfo("Run output: status=%v, runUrl=%s", job.RunOutput.Status, job.RunURL)
	if d := job.RunOutput.GetTfcRun(); d != nil {
		eLog.LogInfo("Run output TFC run: %+v", *d)
	}
	if d := job.RunOutput.GetImRevision(); d != nil {
		eLog.LogInfo("Run output IM deployment: %+v", *d)
	}
	if d := job.RunOutput.GetOrmJob(); d != nil {
		eLog.LogInfo("Run output ORM stack: %+v", *d)
	}

	_, plannedOnly, applied, errored := tfWrapper.IsEndStatus(job.RunStatus)
	if !plannedOnly && !applied {
		eLog.LogInfo("Unexpected run status %v, return error", job.RunStatus)
		if errored {
			// Dump error details for debugging
			tfWrapper.GetErrorDetails(eLog, job.RunId)
		}
		return pterraform.ErrTfRunFailed
	}
	if job.RunType == terraform.RunTypeDestroy {
		deleteOciTenantNetwork(job)
	}
	return nil
}

type DeletePayload struct {
	TenantId         int64  `json:"tenant_id"`
	CloudProvider    string `json:"cloud_provider"`
	ProjectId        string `json:"project_id"`
	OciTenancyId     string `json:"oci_tenancy_id"`
	OciCompartmentId string `json:"oci_compartment_id"`
	RegionName       string `json:"region"`
	CustomerName     string `json:"customer_name"`
	NetworkType      string `json:"network_type"`
	DebugId          string `json:"debug_id"`
}

func (rp *DeletePayload) validate() error {
	var err error
	if rp.CloudProvider == "oci" {
		err = putils.ValidateInputNotEmpty(rp, map[string]bool{"ProjectId": true, "NetworkType": true, "DebugId": true})
		if err != nil {
			return err
		}
	} else {
		err = putils.ValidateInputNotEmpty(rp, map[string]bool{"ProjectId": true, "NetworkType": true, "DebugId": true,
			"OciTenancyId": true, "OciCompartmentId": true, "RegionName": true, "CustomerName": true})
		if err != nil {
			return err
		}
		if rp.CloudProvider != "gcp" && rp.CloudProvider != "openstack" && rp.CloudProvider != "oci" {
			return fmt.Errorf("unexpected cloud provider '%v', only 'gcp', 'openstack' and 'oci' are supported",
				rp.CloudProvider)
		}
	}
	return nil
}

type DeleteResponse struct {
	Error string `json:"error,omitempty"`
}

func DeleteHandler(c *gin.Context) {
	var err error
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	// retrieve and validate input
	payload := &DeletePayload{}
	err = c.ShouldBindJSON(payload)
	if err != nil {
		c.JSON(http.StatusBadRequest, &DeleteResponse{Error: err.Error()})
		return
	}
	err = payload.validate()
	if err != nil {
		c.JSON(http.StatusBadRequest, &DeleteResponse{Error: err.Error()})
		return
	}
	eLog.SetLoggerTag(payload.DebugId)

	conflict, release := serialize(payload.ProjectId, payload.TenantId, payload.CustomerName, payload.CloudProvider,
		payload.NetworkType, payload.RegionName, eLog)
	if conflict {
		c.JSON(http.StatusConflict, &ProvisionResponse{Error: "request already in progress"})
		return
	}
	defer release()

	job := NewDeleteJob(payload, eLog)
	job.WorkspaceName = WorkspaceName(job.CloudProvider, job.CustomerName, job.RegionName, job.ProjectId, job.NetworkType, job.TenantId, eLog)
	job.RunType = terraform.RunTypeDestroy
	if job.CloudProvider == "oci" {
		job.TfWrapper, err = tf.NewTfWrapper(tf.WrapperContext{
			ServiceType:      terraform.ServiceTypeOrm,
			OciTenancyId:     job.OciTenancyId,
			OciCompartmentId: job.OciCompartmentId,
			RegionName:       job.RegionName,
		})
		if err != nil {
			c.JSON(http.StatusBadRequest, &DeleteResponse{Error: err.Error()})
			return
		}
	} else {
		job.TfWrapper = tf.NewTfWrapperDefault()
	}

	err = destroy(job)
	if err != nil {
		c.Status(http.StatusInternalServerError)
	} else {
		c.Status(http.StatusOK)
	}
}
