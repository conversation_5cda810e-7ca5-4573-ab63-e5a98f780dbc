  {{.LogicalName}}:
    Type: 'AWS::EC2::LaunchTemplate'
    Properties:
      LaunchTemplateName: {{.Name}}
      LaunchTemplateData:
        MetadataOptions:
          InstanceMetadataTags: enabled
        ImageId: {{.ImageID}}
        InstanceType: {{.InstanceType}}
{{if .KeyName}}{{"        "}}KeyName: {{.KeyName}}
{{end}}{{/* if .KeyName */ -}}
{{if len .BlockDeviceMapping.DeviceName}}
{{with .BlockDeviceMapping}}{{"        "}}BlockDeviceMappings:
          - DeviceName: {{.DeviceName}}
            Ebs:
              VolumeType: {{.Ebs.VolumeType}}
              VolumeSize: {{.Ebs.VolumeSize}}
              DeleteOnTermination: {{.Ebs.DeleteOnTermination}}
              Encrypted: {{.Ebs.Encrypted}}
{{end}}{{/* with .BlockDeviceMapping */ -}}
{{end}}{{/*{{if len .BlockDeviceMapping.DeviceName}}*/}}
{{if .Credentials.IamRole}}{{"        "}}IamInstanceProfile:
          Name: {{.Credentials.IamRole}}
{{end}}{{/* if .Credentials.IamRole */ -}}
{{if len .Interfaces}}{{"        "}}NetworkInterfaces:
{{range $ifc := .Interfaces }}{{"          - "}}DeviceIndex: {{$ifc.DeviceIndex}}
{{if $ifc.Subnet -}}{{"            "}}SubnetId: {{$ifc.Subnet}}
{{end}}
{{- if $ifc.Network -}}{{"            "}}NetworkInterfaceId: {{$ifc.Network}}
{{end}}
{{- if $ifc.PublicIPArg -}}
{{"            "}}AssociatePublicIpAddress: {{$ifc.AssociatePublicIpAddress}}
{{- end -}}{{/* - if $ifc.PublicIPArg */}}
{{ if len $ifc.SecurityGroups -}}{{"            "}}Groups:{{ "\n" -}}
{{- range $index, $group := $ifc.SecurityGroups -}}
{{"              -  "}}{{$group}}
{{end}}{{/*- range $index, $group := $ifc.SecurityGroups */}}
{{- end -}}{{/*- if len $ifc.SecurityGroups */}}
{{- end -}}{{/* range $ifc := .Interfaces */ -}}
{{- end -}}{{/* if len .Interfaces */ -}}
{{ if len .Tags -}}{{ "\n" -}}{{"        "}}TagSpecifications:
          - ResourceType: instance
            Tags:
{{range $key, $value := .Tags -}}
{{"              - Key: "}}{{$key}}
{{"                Value: "}}{{$value}}
{{end}}{{/* range $key, $value := .Tags */ -}}
{{end}}{{/* if len .Tags */ -}}
{{if .UserData}}{{"        "}}UserData:
{{"          "}}Fn::Base64: {{.UserData}}
{{end}}{{/* if .UserData */ -}}
