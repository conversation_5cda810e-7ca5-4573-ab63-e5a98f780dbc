  {{ .Name }}:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      VpcId: {{ .VpcId }}
      TargetType: {{ .TargetType }}
      Port: {{ .Port }}
      Protocol: {{ .Protocol }}
      HealthCheckProtocol: {{ .HCParams.HealthCheckProtocol }}
      HealthCheckPort: {{ .HCParams.HealthCheckPort }}
      HealthCheckPath: {{ .HCParams.HealthCheckPath }}
      {{- if .HCParams.HealthCheckIntervalSeconds }}
      HealthCheckIntervalSeconds: {{ .HCParams.HealthCheckIntervalSeconds }}
      {{- end }}
      {{- if .HCParams.HealthCheckTimeoutSeconds }}
      HealthCheckTimeoutSeconds: {{ .HCParams.HealthCheckTimeoutSeconds }}
      {{- end }}
      {{- if .HCParams.HealthyThresholdCount }}
      HealthyThresholdCount: {{ .HCParams.HealthyThresholdCount }}
      {{- end }}
      {{- if .HCParams.UnhealthyThresholdCount }}
      UnhealthyThresholdCount: {{ .HCParams.UnhealthyThresholdCount }}
      {{- end }}
      Matcher:
        HttpCode: {{ .Matcher.HttpCode }}
      Name: {{ .Name }}
