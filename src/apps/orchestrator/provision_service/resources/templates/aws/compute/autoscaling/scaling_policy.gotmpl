  {{.LogicalName}}:
    Type: 'AWS::AutoScaling::ScalingPolicy'
    Properties:
      AutoScalingGroupName: !Ref {{.ScalingGroupName}}
      Cooldown: {{.Cooldown}}  # Cooldown time in seconds
{{with .TargetTrackingConfiguration}}{{"      "}}PolicyType: TargetTrackingScaling
      AdjustmentType: ChangeInCapacity
      TargetTrackingConfiguration:
        PredefinedMetricSpecification:
          PredefinedMetricType: ASGAverageCPUUtilization
        TargetValue: {{.TargetValue}}
{{if .DisableScaleIn}}{{"        "}}DisableScaleIn: true
{{end}}{{/* if .DisableScaleIn */}}
{{end}}{{/* with .TargetTrackingConfiguration */ -}}
{{"  "}}{{.LogicalName}}Predictive:
    Type: 'AWS::AutoScaling::ScalingPolicy'
    Properties:
      AutoScalingGroupName: !Ref {{.ScalingGroupName}}
      Cooldown: {{.Cooldown}}  # Cooldown time in seconds
{{with .PredictiveScalingConfiguration}}{{"      "}}PolicyType: PredictiveScaling
      PredictiveScalingConfiguration:
        Mode: ForecastAndScale
        MetricSpecifications:
          - PredefinedMetricPairSpecification:
               PredefinedMetricType: ASGCPUUtilization
            TargetValue: {{.TargetValue}}
{{end}}{{/* with .PredictiveScalingConfiguration */ -}}



