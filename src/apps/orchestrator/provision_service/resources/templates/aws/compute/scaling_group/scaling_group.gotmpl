  {{.LogicalName}}:
    Type: 'AWS::AutoScaling::AutoScalingGroup'
    Properties:
{{- if len .VPCZoneIdentifier}}
      VPCZoneIdentifier:
{{range $zones := .VPCZoneIdentifier  -}}
        {{"      - "}}{{$zones}}
{{end -}}
{{- end}}
      DesiredCapacity: {{.DesiredCapacity}}
      MaxSize: {{.MaxSize}}
      MinSize: {{.MinSize}}
{{ if len .Tags -}}{{"      "}}Tags:
{{range $key, $value := .Tags -}}
{{"        - Key: "}}{{$key}}
{{"          Value: "}}{{$value}}
{{"          PropagateAtLaunch: false"}}
{{end}}{{/* range $key, $value := .Tags */ -}}
{{end}}{{/* if len .Tags */ -}}
{{with .Notifications }}      NotificationConfigurations:
        - TopicARN: {{ .TopicARN}}
          NotificationTypes:
{{if .TriggerScaleUp}}          - 'autoscaling:EC2_INSTANCE_LAUNCH'
{{end}}{{/* if .TriggerScaleUp */ -}}
{{if .TriggerScaleDown}}          - 'autoscaling:EC2_INSTANCE_TERMINATE'
{{end}}{{/* if .TriggerScaleDown */ -}}
{{end}}{{/* with .Notifications */ -}}
{{"      "}}LaunchTemplate:
        LaunchTemplateName: {{.LaunchTemplate.Name}}
        Version: !GetAtt {{.LaunchTemplate.LogicalName}}.LatestVersionNumber
{{- if .IsBehindLoadBalancer}}
      TargetGroupARNs:
        - !Ref {{.TGName}}
{{end}}
{{- if .TargetGroupArn}}
      TargetGroupARNs:
        - {{.TargetGroupArn}}
{{end}}{{/* if .IsBehindExistingLoadBalancer */ -}}
{{- if len .HealthCheckType}}
      HealthCheckType: {{.HealthCheckType}}
      HealthCheckGracePeriod: {{.HealthCheckGracePeriod}}
{{end}}
{{if .RollingUpdate -}}{{"    "}}UpdatePolicy:
      AutoScalingRollingUpdate:
        MinInstancesInService: {{.RollingUpdatePolicy.MinInstancesInService}}
        MinActiveInstancesPercent: {{.RollingUpdatePolicy.MinActiveInstancesPercent}}
{{end}}{{/* with .RollingUpdate */}}
