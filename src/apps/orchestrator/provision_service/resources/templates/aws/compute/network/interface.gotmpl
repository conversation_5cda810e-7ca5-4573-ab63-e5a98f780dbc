  {{ .InterfaceName }}:
    Type: 'AWS::EC2::NetworkInterface'
    Properties:
{{if .Subnet -}}{{"      "}}SubnetId: {{.Subnet}}
{{- end -}}
{{ if .PublicIPArg }}
{{"      "}}AssociatePublicIpAddress: {{.AssociatePublicIpAddress}}
{{- end -}}{{/* - if $ifc.PublicIPArg */}}
{{ if len .SecurityGroups -}}{{"      "}}GroupSet:{{ "\n" -}}
{{- range $index, $group := .SecurityGroups -}}
{{"              -  "}}{{$group}}
{{end}}{{/*- range $index, $group := .SecurityGroups */ -}}
{{- end -}}{{/*- if len .SecurityGroups */ -}}
