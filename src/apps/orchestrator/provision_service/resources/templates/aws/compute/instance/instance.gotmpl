
  {{ .Name }}:
    Type: 'AWS::EC2::Instance'
    Properties:
      InstanceType: {{.InstanceType}}
      ImageId: {{ .ImageID }}
{{if .KeyName}}{{"      "}}KeyName: {{.KeyName}}
{{end}}{{/* if .KeyName */ -}}
{{if len .BlockDeviceMapping.DeviceName}}
{{with .BlockDeviceMapping}}{{"      "}}BlockDeviceMappings:
        - DeviceName: {{.DeviceName}}
          Ebs:
            VolumeType: {{.Ebs.VolumeType}}
            VolumeSize: {{.Ebs.VolumeSize}}
            DeleteOnTermination: {{.Ebs.DeleteOnTermination}}
            Encrypted: {{.Ebs.Encrypted}}
{{end}}{{/* with .BlockDeviceMapping */ -}}
{{end}}{{/*{{if len .BlockDeviceMapping.DeviceName}}*/ -}}
{{if .Credentials.IamRole}}{{"      "}}IamInstanceProfile: {{.Credentials.IamRole}}
{{end}}{{/* if .Credentials.IamRole */ -}}
{{if len .Interfaces}}
{{"      "}}NetworkInterfaces:
{{range $ifc := .Interfaces }}{{"        - "}}NetworkInterfaceId: !Ref {{ $ifc.InterfaceName }}
{{"          "}}DeviceIndex: {{$ifc.DeviceIndex}}
{{end}}{{/* range $ifc := .Interfaces */ -}}
{{end}}{{/* if len .Interfaces */ -}}

{{ if len .Tags -}}{{ "\n" -}}{{"      "}}Tags:
{{range $key, $value := .Tags -}}
{{"        - Key: "}}{{$key}}
{{"          Value: "}}{{$value}}
{{end}}{{/* range $key, $value := .Tags */ -}}
{{end}}{{/* if len .Tags */ -}}
{{if .UserData}}{{"      "}}UserData:
{{"        "}}Fn::Base64: {{.UserData}}
{{end}}{{/* if .UserData */ -}}
{{- "\n" -}}