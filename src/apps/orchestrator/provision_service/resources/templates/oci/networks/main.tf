locals {
    cleaned_tenant_chars = regexall("[a-z0-9]", lower(trimspace(var.tenant_name)))
    cleaned_tenant_name = substr(join("", local.cleaned_tenant_chars), 0, 15)
    # Ensure the DNS label starts with a letter
    vcn_dns_label = (
        regex("^([a-z])", local.cleaned_tenant_name) != "" ?
        local.cleaned_tenant_name :
        "v${local.cleaned_tenant_name}"
        )
    # Generate DNS labels
    tenant_dns_label = substr("${local.vcn_dns_label}", 0, 15)
    ha_subnet_dns_label = substr("ha${local.vcn_dns_label}", 0, 15)
    dp_subnet_dns_label = substr("dp${local.vcn_dns_label}", 0, 15)
    ep_lbaas_subnet_dns_label = substr("eplbaas${local.vcn_dns_label}", 0, 15)
    ep_envoy_subnet_dns_label = substr("epenvoy${local.vcn_dns_label}", 0, 15)
    ep_ext_subnet_dns_label = substr("epext${local.vcn_dns_label}", 0, 15)
}

# VCN
resource "oci_core_vcn" "tenant_dp_vcn" {
    compartment_id = var.tenant_compartment_id
    display_name   = "${var.tenant_name}_dp_vcn"
    cidr_block     = var.vcn_cidr_block
    dns_label      = local.tenant_dns_label
    is_ipv6enabled = true  # Enable IPv6
    is_oracle_gua_allocation_enabled = true
}


# Internet Gateway
resource "oci_core_internet_gateway" "tenant_dp_igw" {
    compartment_id = var.tenant_compartment_id
    vcn_id         = oci_core_vcn.tenant_dp_vcn.id
    display_name   = "${var.tenant_name}_igw"
}

# HA Subnet Route Table (Private)
resource "oci_core_route_table" "ha_route_table" {
    compartment_id = var.tenant_compartment_id
    vcn_id         = oci_core_vcn.tenant_dp_vcn.id
    display_name   = "HA_Subnet_Route_Table"
}

# DP Subnet Route Table (Public)
resource "oci_core_route_table" "dp_route_table" {
    compartment_id = var.tenant_compartment_id
    vcn_id         = oci_core_vcn.tenant_dp_vcn.id
    display_name   = "DP_Subnet_Route_Table"
    route_rules {
        destination       = "0.0.0.0/0"
        destination_type  = "CIDR_BLOCK"
        network_entity_id = oci_core_internet_gateway.tenant_dp_igw.id
    }

    # IPv6 route rule
    route_rules {
        destination       = "::/0"
        destination_type  = "CIDR_BLOCK"
        network_entity_id = oci_core_internet_gateway.tenant_dp_igw.id
    }
}

# HA Subnet (Private)
resource "oci_core_subnet" "ha_subnet" {
    compartment_id             = var.tenant_compartment_id
    vcn_id                     = oci_core_vcn.tenant_dp_vcn.id
    display_name               = "ha"
    cidr_block                 = var.ha_subnet_cidr_block
    prohibit_public_ip_on_vnic = true   # Private subnet
    dns_label                  = local.ha_subnet_dns_label
    availability_domain        = null   # Regional subnet
    route_table_id             = oci_core_route_table.ha_route_table.id
}

# Local values for IPv6 and DNS labels
locals {
    vcn_ipv6_cidr_block = oci_core_vcn.tenant_dp_vcn.ipv6cidr_blocks[0]
}

resource "oci_core_security_list" "dp_security_list" {
  display_name   = "dp-security-list"
  compartment_id = var.tenant_compartment_id
  vcn_id         = oci_core_vcn.tenant_dp_vcn.id

  ingress_security_rules {
      protocol = "all"
      source   = "0.0.0.0/0"
  }

  ingress_security_rules {
      protocol = "all"
      source   = "::/0"
  }

  egress_security_rules {
      protocol    = "all"
      destination = "0.0.0.0/0"
  }

  egress_security_rules {
      protocol    = "all"
      destination = "::/0"
  }
}

# DP Subnet (Public)
resource "oci_core_subnet" "dp_subnet" {
    compartment_id             = var.tenant_compartment_id
    vcn_id                     = oci_core_vcn.tenant_dp_vcn.id
    display_name               = "dp"
    cidr_block                 = var.dp_subnet_cidr_block
    ipv6cidr_block             = cidrsubnet(local.vcn_ipv6_cidr_block, 8, 1)
    prohibit_public_ip_on_vnic = false  # Public subnet
    dns_label                  = local.dp_subnet_dns_label
    availability_domain        = null   # Regional subnet
    route_table_id             = oci_core_route_table.dp_route_table.id
    security_list_ids          = [oci_core_security_list.dp_security_list.id]
}

resource "oci_core_subnet" "ep_lbaas_subnet" {
    compartment_id             = var.tenant_compartment_id
    vcn_id                     = oci_core_vcn.tenant_dp_vcn.id
    display_name               = "ep_lbaas"
    cidr_block                 = var.ep_lbaas_subnet_cidr_block
    ipv6cidr_block             = cidrsubnet(local.vcn_ipv6_cidr_block, 8, 2)
    prohibit_public_ip_on_vnic = false  # Public subnet
    dns_label                  = local.ep_lbaas_subnet_dns_label
    availability_domain        = null   # Regional subnet
}

resource "oci_core_subnet" "ep_envoy_subnet" {
    compartment_id             = var.tenant_compartment_id
    vcn_id                     = oci_core_vcn.tenant_dp_vcn.id
    display_name               = "ep_envoy"
    cidr_block                 = var.ep_envoy_subnet_cidr_block
    ipv6cidr_block             = cidrsubnet(local.vcn_ipv6_cidr_block, 8, 3)
    prohibit_public_ip_on_vnic = false  # Public subnet
    dns_label                  = local.ep_envoy_subnet_dns_label
    availability_domain        = null   # Regional subnet
}

resource "oci_core_subnet" "ep_ext_subnet" {
    compartment_id             = var.tenant_compartment_id
    vcn_id                     = oci_core_vcn.tenant_dp_vcn.id
    display_name               = "ep_ext"
    cidr_block                 = var.ep_ext_subnet_cidr_block
    ipv6cidr_block             = cidrsubnet(local.vcn_ipv6_cidr_block, 8, 4)
    prohibit_public_ip_on_vnic = false  # Public subnet
    dns_label                  = local.ep_ext_subnet_dns_label
    availability_domain        = null   # Regional subnet
}

# NATGW alerting terraform changes
resource "oci_ons_notification_topic" "natgw_port_exhaustion_topic" {
    name          = "natgw_port_exhaustion_topic_${var.tenant_name}"
    compartment_id  = var.tenant_compartment_id
    description     = "NAT gateway port exhaustion topic"
}

resource "oci_ons_subscription" "natgw_port_exhaustion_subscription" {
    compartment_id = var.tenant_compartment_id
    topic_id     = oci_ons_notification_topic.natgw_port_exhaustion_topic.topic_id
    protocol     = "CUSTOM_HTTPS"
    endpoint     = var.nat_port_exhaustion_alerting_url
}

resource "oci_monitoring_alarm" "natgw_port_exhaustion_alarm" {
    display_name         = "natgw_metrics_alarm_${var.tenant_name}"
    namespace            = "oci_nat_gateway"
    query                = "highPortUsageWatermark[1m].max() >= 1 || DropsToNATgw[1m]{dropType = \"NoPorts\"}.sum() >= 1"
    severity             = "CRITICAL"
    compartment_id       = var.tenant_compartment_id
    metric_compartment_id = var.tenant_compartment_id
    body                 = "NAT Gateway Ports Exhausted. Need to auto scale NAT gateway in your region"
    is_enabled           = true
    is_notifications_per_metric_dimension_enabled = true
    message_format       = "PRETTY_JSON"
    # Destinations for alarm notifications
    destinations = [
      oci_ons_notification_topic.natgw_port_exhaustion_topic.topic_id
    ]
}
