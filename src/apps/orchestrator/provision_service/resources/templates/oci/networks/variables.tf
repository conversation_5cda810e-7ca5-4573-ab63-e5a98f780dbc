variable "region" {
    description = "OCI Region"
    type        = string
}

variable "tenant_name" {
  description = "Name of the tenant"
  type        = string
  default     = "Tenant"
}

variable "tenant_compartment_id" {
  description = "OCID of the tenant compartment"
  type        = string
}

variable "vcn_cidr_block" {
  description = "CIDR block for the VCN"
  type        = string
  default     = "**********/18"
}

variable "ha_subnet_cidr_block" {
  description = "CIDR block for the high-availability subnet"
  type        = string
  default     = "**********/21"
}

variable "dp_subnet_cidr_block" {
  description = "CIDR block for the data plane subnet"
  type        = string
  default     = "**********/21"
}

variable "ep_lbaas_subnet_cidr_block" {
  description = "CIDR block for the EP Lbaas subnet"
  type        = string
  default     = "***********/21"
}

variable "ep_envoy_subnet_cidr_block" {
  description = "CIDR block for the EP Envoy subnet"
  type        = string
  default     = "***********/21"
}

variable "ep_ext_subnet_cidr_block" {
  description = "CIDR block for the EP Ext subnet"
  type        = string
  default     = "***********/21"
}

variable "nat_port_exhaustion_alerting_url" {
  description = "NAT gateway port exhaustion alerting APIGW URL"
  type        = string
}
