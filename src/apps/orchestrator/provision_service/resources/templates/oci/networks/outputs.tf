output "oci_network" {
    description = "All OCI network-related outputs"

    value = {
        vcn_id                         = oci_core_vcn.tenant_dp_vcn.id
        vcn_ipv6_cidr_block            = oci_core_vcn.tenant_dp_vcn.ipv6cidr_blocks[0]
        ha_subnet_id                   = oci_core_subnet.ha_subnet.id
        dp_subnet_id                   = oci_core_subnet.dp_subnet.id
        dp_subnet_ipv6_cidr_block      = oci_core_subnet.dp_subnet.ipv6cidr_blocks[0]
        internet_gateway_id            = oci_core_internet_gateway.tenant_dp_igw.id
        ep_lbaas_subnet_id             = oci_core_subnet.ep_lbaas_subnet.id
        ep_lbaas_subnet_ipv6_cidr_block = oci_core_subnet.ep_lbaas_subnet.ipv6cidr_blocks[0]
        ep_envoy_subnet_id             = oci_core_subnet.ep_envoy_subnet.id
        ep_envoy_subnet_ipv6_cidr_block = oci_core_subnet.ep_envoy_subnet.ipv6cidr_blocks[0]
        ep_ext_subnet_id               = oci_core_subnet.ep_ext_subnet.id
        ep_ext_subnet_ipv6_cidr_block  = oci_core_subnet.ep_ext_subnet.ipv6cidr_blocks[0]
    }
}
