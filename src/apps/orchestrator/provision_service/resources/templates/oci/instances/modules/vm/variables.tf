# VM Module Variables

variable "vm_name" {
    description = "Name of the VM instance (used as an identifier)"
    type        = string
}

variable "compartment_id" {
    description = "Compartment OCID for the VM instance"
    type        = string
}

variable "availability_domain" {
    description = "Availability Domain"
    type        = string
}

variable "instance_shape" {
    description = "Instance Shape"
    type        = string
    default     = "VM.Standard.E4.Flex"
}

variable "instance_image_id" {
    description = "Image OCID"
    type        = string
}

variable "user_data" {
    description = "User Data"
    type        = string
    default     = ""
}

variable "machine_type" {
    description = "Machine Type"
    type        = string
    default     = ""
}

variable "nlb_id" {
    description = "nlb that backend is associated with"
    type        = string
    default     = ""
}

variable "shape_config" {
    type = object({
        ocpus              = number
        memory_in_gbs      = number
        baseline_ocpu_utilization = string
    })
    default = {
        ocpus         = 2
        memory_in_gbs = 16
        baseline_ocpu_utilization = "BASELINE_1_1"
    }
    description = "Configuration for VM shape, including the number of OCPUs and memory in GBs"
}

variable "vnic_config" {
    description = "VNIC configuration"
    type = map(object({
        subnet_id            = string
        compartment_id       = optional(string)
    }))
}

variable "add_to_backend_set" {
    description = "Whether to add this VM's DP VNIC to the NLB backend set"
    type        = bool
    default     = false
}

variable "assign_ipv6ip" {
    description = "Whether to assign an IPv6 IP address to the instance"
    type        = bool
    default     = true
}

variable "is_secondary" {
    description = "Is Secondary Instance"
    type        = bool
    default     = false
}

variable "separator" {
    description = "Separator for metadata"
    type        = string
    default     = ","
}

variable "instance_display_name" {
    description = "Display name of the VM instance"
    type        = string
}

variable "reserved_public_ip_id" {
    description = "The public IP address to associate with the DP VNIC (optional)"
    type        = string
    default     = null
}

variable "disable_dp_public_ip" {
    description = "The public IP address to associate with the DP VNIC (optional)"
    type        = bool
    default     = false
}

variable "is_backup" {
    description = "this umig will be backup instance"
    type        = bool
    default     = false
}

variable "region" {
    description = "OCI Region"
    type        = string
}

variable "add_image_filter" {
    description = "Whether to add instance_source_image_filter_details for a VM instance based on node type"
    type        = bool
    default     = false
}

variable "enable_ipv6" {
    description = "Enable IPv6 addressing for the VM instance"
    type        = bool
    default     = false
}
