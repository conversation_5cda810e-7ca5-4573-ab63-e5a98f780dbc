resource "oci_core_nat_gateway" "nat_gw" {
  compartment_id     = var.compartment_id
  display_name       = var.nat_display_name
  vcn_id             = var.vcn_id
  public_ip_id       = coalesce(var.reserved_public_ip_id, try(oci_core_public_ip.nat_public_ip[0].id, null))
}

resource "oci_core_route_table" "nat_route_table" {
    compartment_id = var.compartment_id
    vcn_id         = var.vcn_id
    display_name   = "${var.nat_display_name}-rt"
    route_rules {
        #Required
        network_entity_id = oci_core_nat_gateway.nat_gw.id

        #Optional
        destination       = "0.0.0.0/0"
        destination_type  = "CIDR_BLOCK"
    }

    lifecycle {
        replace_triggered_by = [ oci_core_nat_gateway.nat_gw.id ]
    }
}

# Create a secondary private IP on the VM's VNIC
resource "oci_core_private_ip" "nat_private_ip" {
    for_each = var.vm_vnic_ids
    vnic_id = each.value
    route_table_id = oci_core_route_table.nat_route_table.id

    lifecycle {
        replace_triggered_by = [ oci_core_route_table.nat_route_table.id ]
    }
}

resource "oci_core_public_ip" "nat_public_ip" {
    count = var.reserved_public_ip_id == null ? 1 : 0
    
    #Required
    compartment_id    = var.compartment_id
    lifetime          = "RESERVED"
}