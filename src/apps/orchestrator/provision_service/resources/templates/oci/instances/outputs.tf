# Outputs
output "cluster_details" {
    value = merge({
        for cluster_id, cluster_value in var.cluster_configs :
        cluster_id => {
            for instance_id, vm_module in module.vm_instances:
            instance_id => merge(vm_module.vm_info,
                {
                    "egress_ip_mappings" = {
                        for nat_key, nat_gw in module.nat_gws:
                        nat_gw.nat_gw_region => lookup(nat_gw.nat_private_ips, instance_id, null)...
                        if lookup(nat_gw.nat_private_ips, instance_id, null) != null
                    }
                }
                )
            if contains(keys(var.cluster_configs[cluster_id]), instance_id)
        }
    },{
        for cluster_id, cluster_value in var.ep_cluster_configs :
        cluster_id => {
            for instance_id, vm_module in module.ep_vm_instances:
            instance_id => merge(vm_module.vm_info,
                {
                    "lb_ip" = try(module.ep_lbaas["0"].lbaas_public_ip, null)
                },
                {
                    "extrn_ip" = join(",", [
                        for gw in module.ep_nat_gws : 
                        lookup(gw.nat_private_ips, instance_id, null) 
                        if lookup(gw.nat_private_ips, instance_id, null) != null
                    ])
                }
                )
            if contains(keys(var.ep_cluster_configs[cluster_id]), instance_id)
        }
    })
    description = "Mapping from VM names to VM details with interface IPs"
}

output "nlb_details" {
    value = { for nlb_key, nlb_module in module.nlbs:
        nlb_key => {
            nlb_id                       = nlb_module.nlb_id
            public_ipv6_address          = nlb_module.public_ipv6_address
            private_ip_address           = nlb_module.private_ip_address
            ingress_ip_mappings          = nlb_module.ingress_ip_mappings
            private_ip_ids                = nlb_module.private_ip_ids
            reserved_public_ip_ids       = nlb_module.reserved_public_ip_ids
            region_id_ingress_ip_mappings = nlb_module.region_id_ingress_ip_mappings
            private_ip_ids_private_ips_mappings = nlb_module.private_ip_ids_private_ips_mappings
            public_ip_ids_public_ips_mappings = nlb_module.public_ip_ids_public_ips_mappings
            nlb_vnic_id = nlb_module.nlb_vnic_id
            nlb_vnic_ipv6_addresses = nlb_module.nlb_vnic_ipv6_addresses
            nlb_vnic_primary_ipv6 = nlb_module.nlb_vnic_primary_ipv6
            nlb_vnic_secondary_ipv6 = nlb_module.nlb_vnic_secondary_ipv6
        }
    }
    description = "Mapping from NLB identifiers to NLB details"
}

output "nat_details" {
    value = { for nat_key, nat_gw in module.nat_gws:
        nat_key => {
            nat_id                       = nat_gw.nat_gw_id
            region                       = nat_gw.nat_gw_region
            pripub_ip_mappings           = nat_gw.pripub_ip_mappings
        }
    }
    description = "Mapping from NAT identifiers to NAT details"
}

output "ep_details" {
    value = {
        "ilb_ip_address" = try(module.ep_ilb_networking["0"].ilb_ip_address, null)
    }
    description = "EP specific field output"
}

# output "networking" {
#     value = module.networking.nat_info
#     description = "Mapping from NAT identifiers to NAT details"
# }
