# VM Module Main Configuration

data "oci_identity_availability_domain" "this" {
    compartment_id = var.compartment_id
    ad_number      = 1
}

locals {
    separator = var.separator // Default is ','

    metadata = length(var.user_data) > 0 ? zipmap(
        [for pair in split(local.separator, var.user_data) : trim(split("=", pair)[0], " ")],
        [for pair in split(local.separator, var.user_data) : trim(join("=", slice(split("=", pair), 1, length(split("=", pair)))), " ")]
    ) : {}
}

# VM Instance
resource "oci_core_instance" "vm_instance" {
    availability_domain = var.availability_domain
    compartment_id      = var.compartment_id
    shape               = var.instance_shape
    display_name        = var.instance_display_name

    # Primary VNIC: shared-mgmt
    create_vnic_details {
        subnet_id        = var.vnic_config["shared-mgmt"].subnet_id
        assign_public_ip = false
        hostname_label   = "${var.vm_name}-mgmt-vnic"
        display_name     = "${var.instance_display_name}-mgmt"
    }

    # Instance configuration
    source_details {
        source_type = "image"
        source_id   = var.instance_image_id
        # Conditional instance_source_image_filter_details block
        dynamic "instance_source_image_filter_details" {
            for_each = var.add_image_filter ? [1] : []
            content {
                compartment_id           = var.compartment_id
                operating_system_version = var.instance_image_id
            }
        }
    }

    metadata = local.metadata

    # Instance Shape Configuration (for flexible shapes)
    shape_config {
        ocpus                       = var.shape_config.ocpus
        memory_in_gbs               = var.shape_config.memory_in_gbs
        baseline_ocpu_utilization   = var.shape_config.baseline_ocpu_utilization
    }
}

# Secondary VNIC: DP VNIC
resource "oci_core_vnic_attachment" "vnic_dp_attachment" {
    count = contains(keys(var.vnic_config), "dp") && var.vnic_config["dp"].subnet_id != null && var.vnic_config["dp"].subnet_id != "" ? 1 : 0

    instance_id = oci_core_instance.vm_instance.id

    create_vnic_details {
        subnet_id        = var.vnic_config["dp"].subnet_id
        assign_public_ip = false
        hostname_label   = "${var.vm_name}-dp-vnic"
        display_name     = "${var.instance_display_name}-dp"
        skip_source_dest_check = true
        assign_ipv6ip    = var.enable_ipv6 ? true : false
    }
    depends_on = [oci_core_instance.vm_instance]
}

data "oci_core_vnic" "vnic_dp" {
    count = contains(keys(var.vnic_config), "dp") && var.vnic_config["dp"].subnet_id != null && var.vnic_config["dp"].subnet_id != "" ? 1 : 0
    vnic_id = oci_core_vnic_attachment.vnic_dp_attachment[0].vnic_id
    depends_on = [oci_core_vnic_attachment.vnic_dp_attachment]
}

# Assign IPv6 /96 Secondary vnic IPV6 address on the vnic
resource "oci_core_ipv6" "secondary_vnic_ipv6" {
  count = var.enable_ipv6 ? 1 : 0
  vnic_id = data.oci_core_vnic.vnic_dp[0].vnic_id
  display_name = "${var.vm_name}-dp-vnic-ipv6"
  cidr_range = "/96"
}

# Retrieve the primary private IP ID of the DP VNIC
data "oci_core_private_ips" "dp_vnic_private_ips" {
    count = contains(keys(var.vnic_config), "dp") && var.vnic_config["dp"].subnet_id != null && var.vnic_config["dp"].subnet_id != "" ? 1 : 0
    vnic_id = oci_core_vnic_attachment.vnic_dp_attachment[0].vnic_id
    depends_on = [oci_core_vnic_attachment.vnic_dp_attachment]
}

resource "oci_core_vnic_attachment" "vnic_ha_attachment" {
    count = contains(keys(var.vnic_config), "ha") ? 1 : 0

    instance_id = oci_core_instance.vm_instance.id
    create_vnic_details {
        subnet_id   = var.vnic_config["ha"].subnet_id
        assign_public_ip = false
        hostname_label   = "${var.vm_name}-ha-vnic"
        display_name     = "${var.instance_display_name}-ha"
        assign_ipv6ip    = false
    }
    depends_on = [oci_core_vnic_attachment.vnic_dp_attachment]
}

data "oci_core_vnic" "vnic_ha" {
    count = contains(keys(var.vnic_config), "ha") ? 1 : 0
    vnic_id = oci_core_vnic_attachment.vnic_ha_attachment[0].vnic_id
    depends_on = [oci_core_vnic_attachment.vnic_ha_attachment]
}

resource "oci_core_vnic_attachment" "vnic_shared_dp_attachment" {
    count = contains(keys(var.vnic_config), "shared-dp") ? 1 : 0

    instance_id = oci_core_instance.vm_instance.id
    create_vnic_details {
        subnet_id   = var.vnic_config["shared-dp"].subnet_id
        assign_public_ip = false
        hostname_label   = "${var.vm_name}-shared-dp-vnic"
        display_name     = "${var.instance_display_name}-shared-dp"
        skip_source_dest_check = true
        assign_ipv6ip    = false
    }
    depends_on = [oci_core_vnic_attachment.vnic_dp_attachment]
}

data "oci_core_vnic" "vnic_shared_dp" {
    count = contains(keys(var.vnic_config), "shared-dp") ? 1 : 0
    vnic_id = oci_core_vnic_attachment.vnic_shared_dp_attachment[0].vnic_id
    depends_on = [oci_core_vnic_attachment.vnic_shared_dp_attachment]
}

# Use a local to filter out the primary IP
locals {
    primary_private_ip = length(data.oci_core_private_ips.dp_vnic_private_ips) > 0 ? flatten([
        for ip in data.oci_core_private_ips.dp_vnic_private_ips[0].private_ips :
        ip if ip.is_primary
    ])[0] : null
}

# Create an EPHEMERAL public IP if public_ip_address is not provided
resource "oci_core_public_ip" "dp_ephemeral_public_ip" {
    count = contains(keys(var.vnic_config), "dp") && var.reserved_public_ip_id == null && !var.disable_dp_public_ip ? 1 : 0

    compartment_id = var.compartment_id
    lifetime       = "EPHEMERAL"

    # Associate with the DP VNIC's private IP
    private_ip_id = local.primary_private_ip != null ? local.primary_private_ip.id : null
}


################# EP EXT VNIC ##################

# Extra Secondary VNIC for EP: EXT VNIC
resource "oci_core_vnic_attachment" "vnic_ext_attachment" {
    count = contains(keys(var.vnic_config), "ext") ? 1 : 0
    instance_id = oci_core_instance.vm_instance.id

    create_vnic_details {
        subnet_id        = var.vnic_config["ext"].subnet_id
        assign_public_ip = false
        hostname_label   = "${var.vm_name}-ext-vnic"
        display_name     = "EXT VNIC"
        skip_source_dest_check = false
        nsg_ids = [oci_core_network_security_group.ep_ext_nsg[0].id]
    }
    depends_on = [oci_core_network_security_group.ep_ext_nsg,
                  oci_core_instance.vm_instance,
                  oci_core_vnic_attachment.vnic_dp_attachment]
}

data "oci_core_vnic" "vnic_ext" {
    count      = length(oci_core_vnic_attachment.vnic_ext_attachment) > 0 ? 1 : 0
    vnic_id    = oci_core_vnic_attachment.vnic_ext_attachment[0].vnic_id
    depends_on = [oci_core_vnic_attachment.vnic_ext_attachment]
}

# Use a local to filter out the primary extrn IP
locals {
    extrn_private_ip = try(flatten([
        for ip in data.oci_core_private_ips.ext_vnic_private_ips[0].private_ips :
        ip if ip.is_primary
    ])[0], null)
}

# Retrieve the primary private IP ID of the EXT VNIC
data "oci_core_private_ips" "ext_vnic_private_ips" {
    count      = length(oci_core_vnic_attachment.vnic_ext_attachment) > 0 ? 1 : 0
    vnic_id    = oci_core_vnic_attachment.vnic_ext_attachment[0].vnic_id
    depends_on = [oci_core_vnic_attachment.vnic_ext_attachment]
}

resource "oci_core_public_ip" "extrn_ephemeral_public_ip" {
    count          = length(oci_core_vnic_attachment.vnic_ext_attachment) > 0 ? 1 : 0
    compartment_id = var.compartment_id
    lifetime       = "EPHEMERAL"

    # Associate with the EXT VNIC's private IP
    private_ip_id  = try(local.extrn_private_ip.id, null)
}

data "oci_core_subnet" "ext_subnet" {
    count = contains(keys(var.vnic_config), "ext") ? 1 : 0
    #Required
    subnet_id        = var.vnic_config["ext"].subnet_id
}

resource "oci_core_network_security_group" "ep_ext_nsg" {
    count = contains(keys(var.vnic_config), "ext") ? 1 : 0
    #Required
    compartment_id = var.compartment_id
    vcn_id         = data.oci_core_subnet.ext_subnet[0].vcn_id

    #Optional
    display_name = "EP_Ext_NSG"
}

resource "oci_core_network_security_group_security_rule" "ep_ext_nsg_rules" {
    count = contains(keys(var.vnic_config), "ext") ? 1 : 0
    #Required
    network_security_group_id = oci_core_network_security_group.ep_ext_nsg[0].id
    direction = "INGRESS"
    protocol = "all"
    
    source = "0.0.0.0/0"
    source_type = "CIDR_BLOCK"
    stateless = false
}

# Add data sources for subnet information
data "oci_core_subnet" "shared_mgmt_subnet" {
    subnet_id = var.vnic_config["shared-mgmt"].subnet_id
}

data "oci_core_subnet" "dp_subnet" {
    count = contains(keys(var.vnic_config), "dp") && var.vnic_config["dp"].subnet_id != null && var.vnic_config["dp"].subnet_id != "" ? 1 : 0
    subnet_id = var.vnic_config["dp"].subnet_id
}

data "oci_core_subnet" "shared_dp_subnet" {
    count = contains(keys(var.vnic_config), "shared-dp") ? 1 : 0
    subnet_id = var.vnic_config["shared-dp"].subnet_id
}
