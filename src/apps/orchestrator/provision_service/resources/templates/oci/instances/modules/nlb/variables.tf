# NLB Module Variables

variable "compartment_id" {
  description = "Compartment OCID for the NLB"
  type        = string
}

variable "subnet_id" {
  description = "Subnet OCID where the NLB will be created"
  type        = string
}

variable "backend_ips" {
  description = "Map of backend configurations including IP addresses and backup status"
  type = map(object({
    ip_address = string
    is_backup  = optional(bool, false)
  }))
}

variable "backend_ipv6_ips" {
  description = "List of backend IPv6 addresses"
  type        = map(string)
}

variable "nlb_display_name" {
  description = "Display name for the NLB"
  type        = string
}

variable "nlb_listener_ports" {
  description = "List of listener configurations"
  type = list(object({
    name     = string
    protocol = string
    port     = number
    tcp_idle_timeout = number
    udp_idle_timeout = number
  }))
  default = [
    {
      name     = "tcp-listener"
      protocol = "TCP"
      port     = 80
      tcp_idle_timeout = 1800
      udp_idle_timeout = 1800
    }
  ]
}

variable "health_checker" {
  description = "Health checker configuration"
  type = object({
    protocol           = string
    port               = number
    retries            = number
    timeout_in_millis  = number
    interval_in_millis = number
    url_path           = optional(string)
    return_code        = optional(number)
  })
  default = {
    protocol           = "TCP"
    port               = 80
    retries            = 3
    timeout_in_millis  = 10000
    interval_in_millis = 10000
    url_path           = "/"
    return_code        = 200
  }
}

variable "are_operationally_active_backends_preferred" {
  description = "Whether operationally active backends should be preferred"
  type        = bool
  default     = false
}

variable "is_instant_failover_tcp_reset_enabled" {
  description = "Whether to enable instant failover TCP reset"
  type        = bool
  default     = true
}

variable "nlb_backend_set_hashing_policy" {
  description = "Load Balancing policy for NLB backend sets"
  type        = string
}

variable "nlb_conn_persistence_on_unhealthy_backends" {
  description = "Persist connections on unhealthy backends"
  type        = bool
}

variable "reserved_public_ip_id" {
  description = "reserved public IP address OCID to assign to the NLB Gateway"
  type        = string
}

variable "region_egress_ip_ocid_map" {
  description = "Map of region IDs to their corresponding egress IP OCIDs"
  type        = map(string)
  default     = {}
}

variable "region_egress_ip_map" {
  description = "Map of region IDs to their corresponding egress IPs for compute/edge regions"
  type        = map(string)
  default     = {}
}

variable "is_instant_failover_enabled" {
  description = "Whether the load balancer should attempt to fail over to a functioning backend as soon as possible"
  type        = bool
  default     = false
}

variable "is_fail_open" {
  description = "Whether to enable fail open mode when no healthy backends are available"
  type        = bool
  default     = false
}
variable "nlb_enable_ipv6" {
  description = "Enable IPv6 support for Network Load Balancer"
  type        = bool
  default     = false
}

variable "is_symmetric_hash_enabled" {
  description = "Whether symmetric hash is enabled for backend traffic routing"
  type        = bool
  default     = false
}

