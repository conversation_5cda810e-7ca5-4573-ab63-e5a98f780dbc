resource "oci_load_balancer_load_balancer" "ep_lbaas" {
    compartment_id = var.compartment_id
    display_name = "EP_External_LBaaS"
    shape = "flexible"
    subnet_ids = [ var.subnet_id ]

    shape_details {
        maximum_bandwidth_in_mbps = 8000
        minimum_bandwidth_in_mbps = 1000
    }

    reserved_ips {
        id = coalesce(var.reserved_public_ip_id, try(oci_core_public_ip.ep_lbaas_public_ip[0].id, null))
    }

    network_security_group_ids = [ oci_core_network_security_group.lbaas_nsg.id ]

    is_private = false
    ip_mode    = "IPV4"
}

resource "oci_core_public_ip" "ep_lbaas_public_ip" {
    count = var.reserved_public_ip_id != null ? 0 : 1

    compartment_id    = var.compartment_id
    lifetime          = "RESERVED"
    display_name      = "EP_LBaaS_IP"

    lifecycle {
        ignore_changes = [ private_ip_id ]
    }
}

data "oci_core_public_ip" "ep_lbaas_reserved_ip" {
    count = var.reserved_public_ip_id != null ? 1 : 0
    id = var.reserved_public_ip_id
}

resource "oci_load_balancer_certificate" "lbaas_certificate" {
    certificate_name   = var.certificate_certificate_name
    load_balancer_id   = oci_load_balancer_load_balancer.ep_lbaas.id

    private_key        = var.certificate_private_key
    public_certificate = var.certificate_public_certificate

    lifecycle {
        create_before_destroy = true
    }
}

resource "oci_load_balancer_backend_set" "backend_set_8080" {
    health_checker {
        protocol          = "TCP"

        interval_ms       = 5000
        port              = 8080
        retries           = 2
        timeout_in_millis = 5000
    }
    load_balancer_id = oci_load_balancer_load_balancer.ep_lbaas.id
    name             = "backend_set_8080"
    policy           = "LEAST_CONNECTIONS"
}

resource "oci_load_balancer_backend_set" "backend_set_8081" {
    health_checker {
        protocol          = "TCP"

        interval_ms       = 5000
        port              = 8081
        retries           = 2
        timeout_in_millis = 5000
    }
    load_balancer_id = oci_load_balancer_load_balancer.ep_lbaas.id
    name             = "backend_set_8081"
    policy           = "LEAST_CONNECTIONS"
}

resource "oci_load_balancer_backend_set" "backend_set_8888" {
    health_checker {
        protocol          = "TCP"

        interval_ms       = 5000
        port              = 8888
        retries           = 2
        timeout_in_millis = 5000
    }
    load_balancer_id = oci_load_balancer_load_balancer.ep_lbaas.id
    name             = "backend_set_8888"
    policy           = "LEAST_CONNECTIONS"
}

resource "oci_load_balancer_listener" "port_8080_listener" {
    default_backend_set_name = oci_load_balancer_backend_set.backend_set_8080.name
    load_balancer_id         = oci_load_balancer_load_balancer.ep_lbaas.id
    name                     = "port_8080_listener"
    port                     = 8080
    protocol                 = "TCP"

    connection_configuration {
        idle_timeout_in_seconds = 86400
        backend_tcp_proxy_protocol_version = 2
    }
}

resource "oci_load_balancer_listener" "port_443_listener" {
    count = var.enable_443 ? 1 : 0
    default_backend_set_name = oci_load_balancer_backend_set.backend_set_8080.name
    load_balancer_id         = oci_load_balancer_load_balancer.ep_lbaas.id
    name                     = "port_443_listener"
    port                     = 443
    protocol                 = "TCP"

    connection_configuration {
        idle_timeout_in_seconds = 86400
        backend_tcp_proxy_protocol_version = 2
    }

    ssl_configuration {
        certificate_name        = oci_load_balancer_certificate.lbaas_certificate.certificate_name
        protocols               = var.backend_set_ssl_configuration_protocols
        has_session_resumption  = false
        verify_peer_certificate = false
    }
}

resource "oci_load_balancer_listener" "port_8081_listener" {
    default_backend_set_name = oci_load_balancer_backend_set.backend_set_8081.name
    load_balancer_id         = oci_load_balancer_load_balancer.ep_lbaas.id
    name                     = "port_8081_listener"
    port                     = 8081
    protocol                 = "TCP"

    connection_configuration {
        idle_timeout_in_seconds = 86400
        backend_tcp_proxy_protocol_version = 2
    }
}

resource "oci_load_balancer_listener" "port_8082_listener" {
    count = var.enable_8082 ? 1 : 0
    default_backend_set_name = oci_load_balancer_backend_set.backend_set_8080.name
    load_balancer_id         = oci_load_balancer_load_balancer.ep_lbaas.id
    name                     = "port_8082_listener"
    port                     = 8082
    protocol                 = "TCP"

    connection_configuration {
        idle_timeout_in_seconds = 86400
    }
}

resource "oci_load_balancer_listener" "port_8083_listener" {
    count = var.enable_8083 ? 1 : 0
    default_backend_set_name = oci_load_balancer_backend_set.backend_set_8081.name
    load_balancer_id         = oci_load_balancer_load_balancer.ep_lbaas.id
    name                     = "port_8083_listener"
    port                     = 8083
    protocol                 = "TCP"

    connection_configuration {
        idle_timeout_in_seconds = 86400
    }
}

resource "oci_load_balancer_listener" "port_8888_listener" {
    default_backend_set_name = oci_load_balancer_backend_set.backend_set_8888.name
    load_balancer_id         = oci_load_balancer_load_balancer.ep_lbaas.id
    name                     = "port_8888_listener"
    port                     = 8888
    protocol                 = "TCP"

    connection_configuration {
        idle_timeout_in_seconds = 86400
        backend_tcp_proxy_protocol_version = 2
    }
}

resource "oci_core_network_security_group" "lbaas_nsg" {
    #Required
    compartment_id = var.compartment_id
    vcn_id = var.vcn_id

    #Optional
    display_name = "EP_LBAAS_NSG"
}

resource "oci_core_network_security_group_security_rule" "lbaas_nsg_rule_80s" {
    #Required
    network_security_group_id = oci_core_network_security_group.lbaas_nsg.id
    direction = "INGRESS"
    protocol = 6
    
    source = "0.0.0.0/0"
    source_type = "CIDR_BLOCK"
    stateless = false
    tcp_options {
        destination_port_range {
            #Required
            max = 8083
            min = 8080
        }
    }
}

resource "oci_core_network_security_group_security_rule" "lbaas_nsg_rule_8888" {
    #Required
    network_security_group_id = oci_core_network_security_group.lbaas_nsg.id
    direction = "INGRESS"
    protocol = 6
    
    source = "0.0.0.0/0"
    source_type = "CIDR_BLOCK"
    stateless = false
    tcp_options {
        destination_port_range {
            #Required
            max = 8888
            min = 8888
        }
    }
}

resource "oci_core_network_security_group_security_rule" "lbaas_nsg_rule_443" {
    #Required
    network_security_group_id = oci_core_network_security_group.lbaas_nsg.id
    direction = "INGRESS"
    protocol = 6
    
    source = "0.0.0.0/0"
    source_type = "CIDR_BLOCK"
    stateless = false
    tcp_options {
        destination_port_range {
            #Required
            max = 443
            min = 443
        }
    }
}