output "nat_gw_id" {
    description = "The OCID of the created NAT Gateway."
    value       = oci_core_nat_gateway.nat_gw.id
}

output "nat_gw_region" {
    description = "The public IP address of the NAT Gateway."
    value       = var.region
}

output "nat_gw_ip_address" {
    description = "The public IP address of the NAT Gateway."
    value       = oci_core_nat_gateway.nat_gw.nat_ip
}

output "nat_private_ips" {
    description = "Map of private IPs indexed by the keys of vm_vnic_ids"
    value = { for key, private_ip in oci_core_private_ip.nat_private_ip : key => private_ip.ip_address }
}

output "pripub_ip_mappings" {
  value = {
      "${oci_core_nat_gateway.nat_gw.nat_ip}" = [for _, ip in oci_core_private_ip.nat_private_ip : ip.ip_address]
  }
}
