# Outputs
output "nlb_id" {
    value       = oci_network_load_balancer_network_load_balancer.this.id
    description = "The OCID of the Network Load Balancer"
}

output "public_ip_address" {
    value = try(oci_core_public_ip.associate_nlb_public_ip[0].ip_address, null)
    description = "The public IP address assigned to the NLB"
}

output "private_ip_address" {
    description = "The IP address of the Network Load Balancer"
    value       = length([for ip in oci_network_load_balancer_network_load_balancer.this.ip_addresses : ip.ip_address if ip.ip_version == "IPV4"]) > 0 ? [for ip in oci_network_load_balancer_network_load_balancer.this.ip_addresses : ip.ip_address if ip.ip_version == "IPV4"][0] : null
}

output "private_ip_ids" {
    description = "The Private IP OCID list of the Network Load Balancer for Edge location IIR disabled scenarios handling"
    value       = {
        for regionidx, ip in local.region_to_privip_resource_map :
        regionidx => ip.id
    }
}

output "reserved_public_ip_ids" {
    description = "The Public IP OCID list of the Network Load Balancer for Edge location IIR disabled scenarios handling"
    value       = {
        for regionidx, ipocid in var.region_egress_ip_ocid_map :
        regionidx => "${try(local.region_to_pubip_resource_map[regionidx].id, ipocid)}"
    }
}

output "public_ipv6_address" {
    description = "The IPv6 address of the Network Load Balancer"
    value       = length([for ip in oci_network_load_balancer_network_load_balancer.this.ip_addresses : ip.ip_address if ip.ip_version == "IPV6"]) > 0 ? [for ip in oci_network_load_balancer_network_load_balancer.this.ip_addresses : ip.ip_address if ip.ip_version == "IPV6"][0] : null
}

output "ingress_ip_mappings" {
    description = "Public to private IP address mapping for the NLB"
    value = {
        for regionidx, ipocid in var.region_egress_ip_ocid_map :
        "${try(local.region_to_pubip_resource_map[regionidx].ip_address, var.region_egress_ip_map[regionidx], "0.0.0.0")}" => local.region_to_privip_resource_map[regionidx].ip_address
    }
}

output "private_ip_ids_private_ips_mappings" {
    description = "The Private IP OCIDs to Private IP address mappings"
    value       = {
        for regionidx, ip in local.region_to_privip_resource_map :
        ip.id => ip.ip_address
    }
}

output "public_ip_ids_public_ips_mappings" {
    description = "The Public IP OCIDs to Public IP address mappings"
    value       = {
        for regionidx, ipocid in var.region_egress_ip_ocid_map :
        "${try(local.region_to_pubip_resource_map[regionidx].id, ipocid)}" => "${try(local.region_to_pubip_resource_map[regionidx].ip_address, var.region_egress_ip_map[regionidx], "0.0.0.0")}"
    }
}

output "region_id_ingress_ip_mappings" {
    description = "Region ID to Ingress IP mappings for the NLB"
    value = {
        for regionidx, ipocid in var.region_egress_ip_ocid_map :
        regionidx => "${try(local.region_to_pubip_resource_map[regionidx].ip_address, var.region_egress_ip_map[regionidx], "0.0.0.0")}"
    }
}

# Add this data source to refresh VNIC info after IPv6 assignment
data "oci_core_vnic" "nlb_vnic_output_with_ipv6" {
  vnic_id = local.nlb_vnic_ocid
  depends_on = [
    oci_core_ipv6.nlb_vnic_ipv6
  ]
}

output "nlb_vnic_id" {
  value = local.nlb_vnic_ocid
}

# New output to extract IPv6 addresses from the NLB VNIC with dependency
output "nlb_vnic_ipv6_addresses" {
  description = "The IPv6 addresses assigned to the NLB VNIC"
  value = var.nlb_enable_ipv6 ? [
    for ip in data.oci_core_vnic.nlb_vnic_output_with_ipv6.ipv6addresses : ip
  ] : []
}

# Extract NLB primary IPv6 address (auto-assigned when NLB is created)
output "nlb_vnic_primary_ipv6" {
  description = "The primary IPv6 address of the NLB VNIC"
  value = var.nlb_enable_ipv6 && length(data.oci_core_vnic.nlb_vnic_output_with_ipv6.ipv6addresses) > 0 ? data.oci_core_vnic.nlb_vnic_output_with_ipv6.ipv6addresses[0] : null
}

# Extract NLB secondary IPv6 address (from the oci_core_ipv6 resource)
output "nlb_vnic_secondary_ipv6" {
  description = "The secondary IPv6 address of the NLB VNIC"
  value = try(oci_core_ipv6.nlb_vnic_ipv6[0].ip_address, null)
}
