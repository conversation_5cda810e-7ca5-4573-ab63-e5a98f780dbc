region = "{{ .Provider.Region }}"
idxScheme = {{ .IdxScheme }}

cluster_configs = {
{{- range $clusterId, $value := .ClusterConfigs }}
    "{{ $clusterId }}" = {
    {{- range $vmId, $vmInstance := $value }}
        "{{ $vmId  }}" = {
            account_id                    = "{{ $vmInstance.AcctId }}"
            compartment_id                = "{{ $vmInstance.CompartmentId }}"
            availability_domain           = "{{ $vmInstance.AvailabilityDomain }}"
            instance_shape                = "{{ $vmInstance.InstanceShape }}"
            display_name                  = "{{ $vmInstance.TFLocalName }}"
            shape_config                  = {
                ocpus             = {{ $vmInstance.ShapeConfig.Ocpus }}
                memory_in_gbs     = {{ $vmInstance.ShapeConfig.MemoryInGb }}
                baseline_ocpu_utilization = "{{ $vmInstance.ShapeConfig.BurstBaseline }}"
            }
            instance_image_id             = "{{ $vmInstance.InstanceImageId }}"
            shared_mgmt_subnet_id         = "{{ $vmInstance.SharedMgmtSubnetId }}"
            dp_subnet_id                  = "{{ $vmInstance.DpSubnetId }}"
            add_to_backend_set            = {{ $vmInstance.AddToBackendSet }}
            add_image_filter              = {{ $vmInstance.AddImageFilter }}
            {{- if $vmInstance.ReservedPublicIpOcid }}
            reserved_public_ip_id  = "{{ $vmInstance.ReservedPublicIpOcid }}"
            {{- end }}
            {{- if $vmInstance.SharedDpSubnetId }}
            shared_dp_subnet_id           = "{{ $vmInstance.SharedDpSubnetId }}"
            {{- end }}
            {{- if $vmInstance.HaSubnetId }}
            ha_subnet_id           = "{{ $vmInstance.HaSubnetId }}"
            {{- end }}
            user_data                     = "{{ $vmInstance.UserData }}"
            {{- if $vmInstance.MachineType }}
            machine_type                  = "{{ $vmInstance.MachineType }}"
            {{- end }}
            {{- if $vmInstance.IsSecondary }}
            is_secondary                 = {{ $vmInstance.IsSecondary }}
            {{- end }}
            {{- if $vmInstance.IsBackup }}
            is_backup = {{ $vmInstance.IsBackup }}
            {{- end }}
            {{- if ne $vmInstance.DisableDpPublicIp false }}
            disable_dp_public_ip = {{ $vmInstance.DisableDpPublicIp }}
            {{- end }}
            {{- if ne $vmInstance.AssignIpV6 true}}
            assign_ipv6ip = {{ $vmInstance.AssignIpV6 }}
            {{- end }}
            {{- if $vmInstance.Separator }}
            separator =  "{{ $vmInstance.Separator }}"
            {{- end }}
            {{- if $vmInstance.NlbId }}
            nlb_id =  "{{ $vmInstance.NlbId }}"
            {{- end }}
            enable_ipv6              = {{ $vmInstance.EnableIPv6 }}
        }
    {{- end }}
    }
{{- end }}
}

nlb_configs = {
{{- range $key, $value := .NlbConfigs }}
    "{{ $key }}" = {
        display_name         = "{{ $value.TFLocalName }}"
        compartment_id = "{{ $value.CompartmentId }}"
        subnet_id      = "{{ $value.SubnetId }}"
        nlb_listener_ports = [
          {{- range $index, $listener := $value.NlbListenerPorts }}
          {
            name     = "{{ $listener.Name }}"
            protocol = "{{ $listener.Protocol }}"
            port     = "{{ $listener.Port }}"
            tcp_idle_timeout = "{{ $listener.TCPIdleTimeout }}"
            udp_idle_timeout = "{{ $listener.UDPIdleTimeout }}"
          }
          {{- end }}
        ]
        {{- if $value.NlbReservedPublicIpOcid }}
        reserved_public_ip_id  = "{{ $value.NlbReservedPublicIpOcid }}"
        {{- end }}
        {{- if $value.NLBEgressIPOCIDList }}
        region_egress_ip_ocid_map = {
            {{- range $key, $ocid := $value.NLBEgressIPOCIDList }}
            "{{ $key }}" = "{{ $ocid }}"
            {{- end }}
        }
        {{- end }}
        {{- if $value.NLBEgressIPList }}
        region_egress_ip_map = {
            {{- range $key, $eip := $value.NLBEgressIPList }}
            "{{ $key }}" = "{{ $eip }}"
            {{- end }}
        }
        {{- end }}
        health_checker      = {
          protocol = "{{ $value.HealthChecker.Protocol }}"
          port = {{ $value.HealthChecker.Port }}
          retries = {{ $value.HealthChecker.Retries }}
          timeout_in_millis = {{ $value.HealthChecker.TimeoutInMillis }}
          interval_in_millis = {{ $value.HealthChecker.IntervalInMillis }}
          {{- if $value.HealthChecker.UrlPath }}
          url_path = "{{ $value.HealthChecker.UrlPath }}"
          {{- end }}
          {{- if $value.HealthChecker.ReturnCode }}
          return_code = {{ $value.HealthChecker.ReturnCode }}
          {{- end }}
        }
        nlb_backend_set_hashing_policy = "{{ $value.NlbBackendSetHashingPolicy }}"
        nlb_conn_persistence_on_unhealthy_backends = "{{ $value.ConnectionPersistenceOnUnhealthyBackends }}"

        {{- if $value.PreferOperationallyActiveBackends }}
        are_operationally_active_backends_preferred = {{ $value.PreferOperationallyActiveBackends }}
        {{- end }}
        {{- if $value.IsInstantFailoverTcpResetEnabled }}
        is_instant_failover_tcp_reset_enabled = {{ $value.IsInstantFailoverTcpResetEnabled }}
        {{- end }}
        {{- if $value.IsInstantFailoverEnabled }}
        is_instant_failover_enabled = {{ $value.IsInstantFailoverEnabled }}
        {{- end }}
        nlb_enable_ipv6 = "{{ $value.EnableIPv6 }}"
        {{- if $value.IsSymmetricHashEnabled }}
        is_symmetric_hash_enabled = {{ $value.IsSymmetricHashEnabled }}
        {{- end }}
    }
{{- end }}
}

nat_configs = {
{{- range $key, $value := .NatConfigs }}
    "{{ $key }}" = {
        display_name         = "{{ $value.TFLocalName }}"
        compartment_id = "{{ $value.CompartmentId }}"
        vcn_id         = "{{ $value.VcnId }}"
        region         = {{ $value.RegionId }}
        {{- if $value.NatReservedPublicIpOcid }}
        reserved_public_ip_id  = "{{ $value.NatReservedPublicIpOcid }}"
        {{- end }}
    }
{{- end }}
}

ep_cluster_configs = {
{{- range $clusterId, $value := .EpClusterConfigs }}
    "{{ $clusterId }}" = {
    {{- range $vmId, $vmInstance := $value }}
        "{{ $vmId  }}" = {
            account_id                    = "{{ $vmInstance.AcctId }}"
            compartment_id                = "{{ $vmInstance.CompartmentId }}"
            availability_domain           = "{{ $vmInstance.AvailabilityDomain }}"
            instance_shape                = "{{ $vmInstance.InstanceShape }}"
            display_name                  = "{{ $vmInstance.TFLocalName }}"
            shape_config                  = {
                ocpus             = {{ $vmInstance.ShapeConfig.Ocpus }}
                memory_in_gbs     = {{ $vmInstance.ShapeConfig.MemoryInGb }}
            }
            instance_image_id             = "{{ $vmInstance.InstanceImageId }}"
            shared_mgmt_subnet_id         = "{{ $vmInstance.SharedMgmtSubnetId }}"
            dp_subnet_id                  = "{{ $vmInstance.DpSubnetId }}"
            ep_ext_subnet_id              = "{{ $vmInstance.EpExtSubnetId }}"
            {{- if $vmInstance.ReservedPublicIpOcid }}
            reserved_public_ip_id         = "{{ $vmInstance.ReservedPublicIpOcid }}"
            {{- end }}
            {{- if $vmInstance.SharedDpSubnetId }}
            shared_dp_subnet_id           = "{{ $vmInstance.SharedDpSubnetId }}"
            {{- end }}
            {{- if $vmInstance.HaSubnetId }}
            ha_subnet_id           = "{{ $vmInstance.HaSubnetId }}"
            {{- end }}
            user_data                     = "{{ $vmInstance.UserData }}"
            {{- if $vmInstance.MachineType }}
            machine_type                  = "{{ $vmInstance.MachineType }}"
            {{- end }}
        }
    {{- end }}
    }
{{- end }}
}

ep_ilb_config = {
  {{- if .EpSpecificConf }}
    {{- if .EpSpecificConf.IlbConfig }}
    "0" = {
      compartment_id  = "{{ .EpSpecificConf.IlbConfig.CompartmentId }}"
      vcn_id          = "{{ .EpSpecificConf.IlbConfig.VcnId }}"
      ilb_subnet_id   = "{{ .EpSpecificConf.IlbConfig.IlbSubnetId }}"
      envoy_subnet_id = "{{ .EpSpecificConf.IlbConfig.EnvoySubnetId }}"
      lbaas_subnet_id = "{{ .EpSpecificConf.IlbConfig.LbaasSubnetId }}"
      ext_subnet_id   = "{{ .EpSpecificConf.IlbConfig.ExtSubnetId }}"
    }
    {{- end }}
  {{- end }}
}

ep_nat_config = {
  {{- if .EpSpecificConf }}
    {{- range $key, $value := .EpSpecificConf.NatConfig }}
      "{{ $key }}" = {
        display_name     = "{{ $value.TFLocalName }}"
        compartment_id   = "{{ $value.CompartmentId }}"
        vcn_id           = "{{ $value.VcnId }}"
        region           = {{ $value.RegionId }}
        {{- if $value.NatReservedPublicIpOcid }}
        reserved_public_ip_id = "{{ $value.NatReservedPublicIpOcid }}"
        {{- end }}
      }
    {{- end }}
  {{- end }}
}

ep_lbaas_config = {
  {{- if .EpSpecificConf }}
    {{- if .EpSpecificConf.LbaasConfig }}
    "0" = {
      compartment_id  = "{{ .EpSpecificConf.LbaasConfig.CompartmentId }}"
      vcn_id          = "{{ .EpSpecificConf.LbaasConfig.VcnId }}"
      lbaas_subnet_id = "{{ .EpSpecificConf.LbaasConfig.LbaasSubnetId }}"
      enable_443      = {{ .EpSpecificConf.LbaasConfig.Enable443 }}
      enable_8082     = {{ .EpSpecificConf.LbaasConfig.Enable8082 }}
      enable_8083     = {{ .EpSpecificConf.LbaasConfig.Enable8083 }}

      {{- if .EpSpecificConf.LbaasConfig.ReservedPublicIpOcid }}
      reserved_public_ip_id = "{{ .EpSpecificConf.LbaasConfig.ReservedPublicIpOcid }}"
      {{- end }}

      {{- if .EpSpecificConf.LbaasConfig.CertificateName }}
      certificate_certificate_name = "{{ .EpSpecificConf.LbaasConfig.CertificateName }}"
      {{- end }}

      {{- if .EpSpecificConf.LbaasConfig.CertificatePrivateKey }}
      certificate_private_key = <<EOT
{{ .EpSpecificConf.LbaasConfig.CertificatePrivateKey }}
EOT
      {{- end }}

      {{- if .EpSpecificConf.LbaasConfig.PublicCertificate }}
      certificate_public_certificate = <<EOT
{{ .EpSpecificConf.LbaasConfig.PublicCertificate }}
EOT
      {{- end }}
    }
    {{- end }}
  {{- end }}
}

ep_envoy_config = {
  {{- if .EpSpecificConf }}
    {{- if .EpSpecificConf.EnvoyConfig }}
    "0" = {
      envoy_ocpus           = {{ .EpSpecificConf.EnvoyConfig.ShapeConfig.Ocpus }}
      envoy_memory          = {{ .EpSpecificConf.EnvoyConfig.ShapeConfig.MemoryInGb }}
      envoy_instance        = "{{ .EpSpecificConf.EnvoyConfig.InstanceShape }}"
      compartment_id        = "{{ .EpSpecificConf.EnvoyConfig.CompartmentId }}"
      vcn_id                = "{{ .EpSpecificConf.EnvoyConfig.VcnId }}"
      envoy_user_data       = "{{ .EpSpecificConf.EnvoyConfig.UserData }}"
      envoy_instance_name   = "{{ .EpSpecificConf.EnvoyConfig.InstanceName }}"
      envoy_image_id        = "{{ .EpSpecificConf.EnvoyConfig.ImageId }}"
      envoy_subnet_id       = "{{ .EpSpecificConf.EnvoyConfig.SubnetId }}"
      envoy_pool_size       = {{ .EpSpecificConf.EnvoyConfig.PoolSize }}
      shared_mgmt_subnet_id = "{{ .EpSpecificConf.EnvoyConfig.SharedMgmtSubnetId }}"
      envoy_pool_size_max   = {{ .EpSpecificConf.EnvoyConfig.PoolSizeMax }}
      envoy_pool_size_min   = {{ .EpSpecificConf.EnvoyConfig.PoolSizeMin }}
    }
    {{- end }}
  {{- end }}
}
