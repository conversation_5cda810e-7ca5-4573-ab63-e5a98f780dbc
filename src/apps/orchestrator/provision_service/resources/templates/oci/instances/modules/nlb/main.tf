# NLB Module Main Configuration

# Create the NLB
resource "oci_network_load_balancer_network_load_balancer" "this" {
  compartment_id = var.compartment_id
  display_name   = var.nlb_display_name
  is_private     = true
  subnet_id      = var.subnet_id
  nlb_ip_version = var.nlb_enable_ipv6 ? "IPV4_AND_IPV6" : "IPV4"

  # Enable source/destination header preservation
  is_preserve_source_destination = true
  # Disable symmetric hashing policy
  is_symmetric_hash_enabled = var.is_symmetric_hash_enabled
}

# Separate resource for backend set
resource "oci_network_load_balancer_backend_set" "dp_backend_set" {
  name                     = "dp-backend-set"
  network_load_balancer_id = oci_network_load_balancer_network_load_balancer.this.id
  # for NGPA; we need to set this to 2 tuple for BF tenants and 5 tuple for GF tenants
  policy                   = var.nlb_backend_set_hashing_policy

  are_operationally_active_backends_preferred = var.are_operationally_active_backends_preferred
  is_instant_failover_tcp_reset_enabled = var.is_instant_failover_tcp_reset_enabled

  # Health Checker
  health_checker {
    protocol           = var.health_checker.protocol
    port               = var.health_checker.port
    retries            = var.health_checker.retries
    timeout_in_millis  = var.health_checker.timeout_in_millis
    interval_in_millis = var.health_checker.interval_in_millis
    url_path           = var.health_checker.url_path
    return_code        = 200
  }
  is_fail_open = var.nlb_conn_persistence_on_unhealthy_backends
  is_instant_failover_enabled = var.is_instant_failover_enabled
}

# Separate resource for backend set for IPv6
resource "oci_network_load_balancer_backend_set" "dp_backend_set_ipv6" {
  count = var.nlb_enable_ipv6 ? 1 : 0
  name                     = "dp-backend-set-ipv6"
  network_load_balancer_id = oci_network_load_balancer_network_load_balancer.this.id
  # for NGPA; we need to set this to 2 tuple for BF tenants and 5 tuple for GF tenants
  policy                   = var.nlb_backend_set_hashing_policy

  # Health Checker
  health_checker {
    protocol           = var.health_checker.protocol
    port               = var.health_checker.port
    retries            = var.health_checker.retries
    timeout_in_millis  = var.health_checker.timeout_in_millis
    interval_in_millis = var.health_checker.interval_in_millis
    return_code        = 200
  }
  ip_version = "IPV6"
  is_fail_open = var.nlb_conn_persistence_on_unhealthy_backends
}


# Separate resource for backends
resource "oci_network_load_balancer_backend" "dp_backends" {
  for_each = var.backend_ips

  network_load_balancer_id = oci_network_load_balancer_network_load_balancer.this.id
  backend_set_name         = oci_network_load_balancer_backend_set.dp_backend_set.name
  ip_address               = each.value.ip_address
  port                     = 0
  is_backup                = lookup(each.value, "is_backup", false)
}

# Separate resource for backends for ipv6
resource "oci_network_load_balancer_backend" "dp_backends_ipv6" {
  for_each = var.nlb_enable_ipv6 ? var.backend_ipv6_ips : {}

  network_load_balancer_id = oci_network_load_balancer_network_load_balancer.this.id
  backend_set_name         = oci_network_load_balancer_backend_set.dp_backend_set_ipv6[0].name
  ip_address               = each.value
  port                     = 0
}

# Separate resource for listeners
resource "oci_network_load_balancer_listener" "dp_listeners" {
  for_each                 = { for l in var.nlb_listener_ports : l.name => l }
  network_load_balancer_id = oci_network_load_balancer_network_load_balancer.this.id
  name                     = each.value.name
  default_backend_set_name = oci_network_load_balancer_backend_set.dp_backend_set.name
  port                     = each.value.port
  protocol                 = each.value.protocol
  tcp_idle_timeout         = each.value.tcp_idle_timeout
  udp_idle_timeout         = each.value.udp_idle_timeout
}

# Separate resource for listeners for IPv6
resource "oci_network_load_balancer_listener" "dp_listeners_ipv6" {
  for_each = var.nlb_enable_ipv6 ? { for l in var.nlb_listener_ports : l.name => l } : {}
  network_load_balancer_id = oci_network_load_balancer_network_load_balancer.this.id
  name                     = "${each.value.name}-ipv6"
  default_backend_set_name = oci_network_load_balancer_backend_set.dp_backend_set_ipv6[0].name
  port                     = each.value.port
  protocol                 = each.value.protocol
  tcp_idle_timeout         = each.value.tcp_idle_timeout
  udp_idle_timeout         = each.value.udp_idle_timeout
  ip_version               = "IPV6"
}

# Data source to get the NLB's private IP and VNIC ID
data "oci_core_private_ips" "nlb_private_ips" {
    ip_address = oci_network_load_balancer_network_load_balancer.this.ip_addresses[0].ip_address
    subnet_id  = var.subnet_id
}

locals {
    nlb_private_ip = flatten([
        for ip in data.oci_core_private_ips.nlb_private_ips.private_ips :
        ip if ip.is_primary
    ])[0]
}

# Create a secondary private IP on the NLB's VNIC
resource "oci_core_private_ip" "nlb_secondary_private_ip" {
    for_each = { for regionidx, ipocid in var.region_egress_ip_ocid_map : regionidx => ipocid }
    vnic_id = local.nlb_private_ip.vnic_id
}

data "oci_core_vnic" "nlb_vnic" {
  vnic_id = data.oci_core_private_ips.nlb_private_ips.private_ips[0].vnic_id
}

data "oci_core_subnet" "nlb_primary_subnet" {
  subnet_id = data.oci_core_private_ips.nlb_private_ips.private_ips[0].subnet_id
}

# Assign IPv6 address in each IPv6-enabled subnet using the NLB VNIC
resource "oci_core_ipv6" "nlb_vnic_ipv6" {
  count = var.nlb_enable_ipv6 ? 1 : 0
  # Use the NLB VNIC for all IPv6 assignments
  vnic_id = data.oci_core_vnic.nlb_vnic.id

  # Use ipv6subnet_cidr field instead of letting OCI to auto assign in that particular subnet
  ipv6subnet_cidr = data.oci_core_subnet.nlb_primary_subnet.ipv6cidr_blocks[0]

  # Display name for identification
  display_name = "nlb-ipv6-${var.nlb_display_name}"

  # Ensure dependencies are met
  depends_on = [
    oci_network_load_balancer_network_load_balancer.this,
    data.oci_core_private_ips.nlb_private_ips,
    data.oci_core_vnic.nlb_vnic
  ]
}


locals {
  region_to_privip_resource_map = { for k, v in oci_core_private_ip.nlb_secondary_private_ip : k => v }
}

# Create an EPHEMERAL public IP if public_ip_address OCID is empty value from the region -> IP OCID map passed from provisioning service
resource "oci_core_public_ip" "associate_nlb_public_ip" {
    for_each = { for regionidx, ipocid in var.region_egress_ip_ocid_map : regionidx => ipocid if ipocid == "" }

    compartment_id = var.compartment_id
    lifetime       = "RESERVED"

    # Associate with the DP VNIC's private IP
    private_ip_id = local.region_to_privip_resource_map[each.key].id
}

locals {
  region_to_pubip_resource_map = { for key, val in oci_core_public_ip.associate_nlb_public_ip : key => val }
}

locals {
  nlb_vnic_ocid = data.oci_core_vnic.nlb_vnic.id
}
