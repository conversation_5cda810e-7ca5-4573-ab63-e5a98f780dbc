# VM Information with Interface IPs
output "vm_info" {
    value = merge(
        {
            vm_id                  = oci_core_instance.vm_instance.id
            shared_mgmt_private_ip = oci_core_instance.vm_instance.private_ip
            mgmt_subnet_cidr_block = data.oci_core_subnet.shared_mgmt_subnet.cidr_block
            mgmt_virtual_router_ip = data.oci_core_subnet.shared_mgmt_subnet.virtual_router_ip
            dp_vnic_id             = try(data.oci_core_vnic.vnic_dp[0].vnic_id, null)
            dp_private_ip          = try(data.oci_core_vnic.vnic_dp[0].private_ip_address, null)
            dp_private_ip_id       = local.primary_private_ip != null ? local.primary_private_ip.id : null
            dp_subnet_cidr_block   = try(data.oci_core_subnet.dp_subnet[0].cidr_block, null)
            dp_virtual_router_ip   = try(data.oci_core_subnet.dp_subnet[0].virtual_router_ip, null)
            ha_private_ip          = try(data.oci_core_vnic.vnic_ha[0].private_ip_address, null)
            reserved_public_ip_id  = var.reserved_public_ip_id
            ep_ext_vnic_id         = try(data.oci_core_vnic.vnic_ext[0].vnic_id, null)
            dp_public_ip           = try(
                coalesce(
                    try(oci_core_public_ip.extrn_ephemeral_public_ip[0].ip_address, null),
                    try(oci_core_public_ip.dp_ephemeral_public_ip[0].ip_address, null)
                ),
                null
            )
            dp_publicv6_ip         = try(data.oci_core_vnic.vnic_dp[0].ipv6addresses[0], null)
            dp_publicv6_ip_secondary = try(oci_core_ipv6.secondary_vnic_ipv6[0].ip_address, null)
            shared_dp_private_ip   = try(data.oci_core_vnic.vnic_shared_dp[0].private_ip_address, null)
            shared_dp_subnet_cidr_block = try(data.oci_core_subnet.shared_dp_subnet[0].cidr_block, null)
            shared_dp_virtual_router_ip = try(data.oci_core_subnet.shared_dp_subnet[0].virtual_router_ip, null)
            machine_type           = var.machine_type
        },
        var.is_secondary == true ? { is_secondary = var.is_secondary } : {}
    )
    description = "VM instance information with interface IPs"
}
