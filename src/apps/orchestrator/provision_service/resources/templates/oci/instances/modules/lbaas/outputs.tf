output "lbaas_id" {
    description = "The OCID of the lbaas."
    value       = oci_load_balancer_load_balancer.ep_lbaas.id
}

output "lbaas_public_ip" {
    description = "The public ip address of the lbaas."
    value       = try(oci_core_public_ip.ep_lbaas_public_ip[0].ip_address, data.oci_core_public_ip.ep_lbaas_reserved_ip[0].ip_address)
}

output "backend_sets" {
  description = "List of backend sets"
  value = {
    8080 = {
        name = oci_load_balancer_backend_set.backend_set_8080.name,
        lbaas_id = oci_load_balancer_load_balancer.ep_lbaas.id,
        port = 8080
    },
    8081 = {
        name = oci_load_balancer_backend_set.backend_set_8081.name,
        lbaas_id = oci_load_balancer_load_balancer.ep_lbaas.id,
        port = 8081
    },
    8888 = {
        name = oci_load_balancer_backend_set.backend_set_8888.name,
        lbaas_id = oci_load_balancer_load_balancer.ep_lbaas.id,
        port = 8888
    }
  }
}