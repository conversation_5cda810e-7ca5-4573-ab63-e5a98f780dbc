variable "compartment_id" {
  description = "The OCID of the compartment where the NAT Gateway will be created."
  type        = string
}

variable "nat_display_name" {
  description = "The display name of the NAT Gateway."
  type        = string
}

variable "vcn_id" {
  description = "The OCID of the VCN where the NAT Gateway will be attached."
  type        = string
}

variable "region" {
  description = "The region(language) which the NAT represents"
  type        = string
}

variable "vm_vnic_ids" {
  description = "List of VM vnic IDs to associate the NAT-GW"
  type        = map(string)
}

variable "reserved_public_ip_id" {
  description = "BYOIP public IP address OCID to assign to the NAT Gateway"
  type        = string
  nullable    = true
}
