# Root Module Variables

variable "region" {
    description = "OCI Region"
    type        = string
}

variable "idxScheme" {
    description = "Whether to use instance id for instance keys or a more generic identifier"
    type        = bool
}

variable "cluster_configs" {
    description = "Map of clusters, each containing VM configurations"
    type = map(
        map(object({
            account_id                    = number
            display_name                  = string
            compartment_id                = string
            availability_domain           = string
            instance_shape                = optional(string)
            shape_config                  = optional(object({
                ocpus            = number
                memory_in_gbs    = number
                baseline_ocpu_utilization    = optional(string)
            }))
            instance_image_id             = string
            shared_mgmt_subnet_id         = string
            dp_subnet_id                  = string
            dp_mgmt_subnet_id             = optional(string)
            user_data                     = optional(string)
            ha_subnet_id                  = optional(string)
            shared_dp_subnet_id           = optional(string)
            add_to_backend_set            = bool
            machine_type                  = optional(string)
            is_secondary                  = optional(string)
            reserved_public_ip_id         = optional(string)
            add_image_filter              = bool
            is_backup                     = optional(bool, false)
            are_operationally_active_backends_preferred = optional(bool, false)
            is_instant_failover_tcp_reset_enabled = optional(bool, true)
            disable_dp_public_ip          = optional(bool, false)
            assign_ipv6ip                 = optional(bool, true)
            separator                     = optional(string, ",")
            nlb_id                        = optional(string)
            enable_ipv6              = bool
        }))
        )
}

variable "nlb_configs" {
    description = "Map of NLB configurations indexed by NLB identifiers"
    type = map(object({
        display_name     = string
        compartment_id     = string
        subnet_id          = string
        nlb_listener_ports = optional(list(object({
            name     = string
            protocol = string
            port     = number
            tcp_idle_timeout = number
            udp_idle_timeout = number
        })))
        health_checker = optional(object({
            protocol           = string
            port               = number
            retries            = number
            timeout_in_millis  = number
            interval_in_millis = number
            url_path = optional(string)
            return_code = optional(number, 200)
        }))
        reserved_public_ip_id = optional(string)
        nlb_backend_set_hashing_policy = optional(string)
        nlb_conn_persistence_on_unhealthy_backends = optional(bool)
        region_egress_ip_ocid_map = optional(map(string))
        region_egress_ip_map = optional(map(string))
        are_operationally_active_backends_preferred = optional(bool, false)
        is_instant_failover_tcp_reset_enabled = optional(bool, true)
        is_instant_failover_enabled = optional(bool, false)
        is_fail_open = optional(bool, false)
        nlb_enable_ipv6 = optional(bool)
        is_symmetric_hash_enabled = optional(bool, false)
    }))
}

variable "nat_configs" {
    description      = "Map of NAT configurations indexed by NAT identifiers"
    type = map(object({
        display_name              = string
        compartment_id            = string
        vcn_id                    = string
        region                    = string
        reserved_public_ip_id     = optional(string)
    }))
}

################## BEGIN EP CONFIGS ##################

variable "ep_cluster_configs" {
    description = "Map of VM configurations indexed by vm_names"
    type = map(
        map(object({
            account_id                    = number
            display_name                  = string
            compartment_id                = string
            availability_domain           = string
            instance_shape                = optional(string)
            shape_config                  = optional(object({
                ocpus            = number
                memory_in_gbs    = number
                baseline_ocpu_utilization = optional(string)
            }))
            instance_image_id             = string
            shared_mgmt_subnet_id         = string
            dp_subnet_id                  = string
            ep_ext_subnet_id              = string
            reserved_public_ip_id         = optional(string)
            shared_dp_subnet_id           = optional(string)
            ha_subnet_id                  = optional(string)
            user_data                     = optional(string)
            machine_type                  = optional(string)
        }))
    )
}

variable "ep_nat_config" {
    description      = "Map of Ep nat configurations"
    type = map(object({
        display_name              = string
        compartment_id            = string
        vcn_id                    = string
        region                    = string
        reserved_public_ip_id     = optional(string)
    }))
}

variable "ep_ilb_config" {
    description      = "Map of ILB configurations"
    type = map(object({
        compartment_id     = string
        vcn_id             = string
        ilb_subnet_id      = string
        envoy_subnet_id    = string
        lbaas_subnet_id    = string
        ext_subnet_id      = string
    }))
}

variable "ep_envoy_config" {
    description      = "Map of Envoy configurations"
    type = map(object({
        envoy_ocpus         = number
        envoy_memory        = number
        envoy_instance      = string
        compartment_id      = string
        vcn_id              = string
        envoy_user_data     = string
        envoy_instance_name = string
        envoy_image_id      = string
        envoy_subnet_id      = string
        envoy_pool_size      = number
        shared_mgmt_subnet_id  = string
        envoy_pool_size_max    = number
        envoy_pool_size_min    = number
    }))
}

variable "ep_lbaas_config" {
    description      = "Map of LBaaS configurations"
    type = map(object({
        compartment_id     = string
        vcn_id             = string
        lbaas_subnet_id    = string
        enable_443         = bool
        enable_8082        = bool
        enable_8083        = bool
        reserved_public_ip_id = optional(string)
        certificate_certificate_name = optional(string)
        certificate_private_key = optional(string)
        certificate_public_certificate = optional(string)
    }))
    sensitive = true
}



################## END EP CONFIGS ##################
