# data "oci_core_subnet" "nat_subnet" {
#     for_each       = var.nat_info
#     subnet_id      = each.value["nat_subnet_id"]  # Provide the OCID of the existing subnet
# }

# data "oci_core_route_tables" "subnet_route_tables" {
#     for_each       = var.nat_info
#     compartment_id = data.oci_core_subnet.nat_subnet[each.key].compartment_id
#     filter {
#         name   = "id"
#         values = [data.oci_core_subnet.nat_subnet[each.key].route_table_id]
#     }
# }

# locals {
#     new_route_rules = {
#         for nat_key, nat_data in var.nat_info : nat_key => {
#             destination       = "0.0.0.0/0"
#             destination_type  = "CIDR_BLOCK"
#             network_entity_id = nat_data["nat_id"]
#             description       = "Default route to NAT Gateway"
#         }
#     }

#     should_add_route = {
#         for nat_key, nat_data in var.nat_info : nat_key =>
#         alltrue([
#             for route in data.oci_core_route_tables.subnet_route_tables[nat_key].route_tables[0].route_rules :
#             route.destination != local.new_route_rules[nat_key].destination ||
#             route.network_entity_id != local.new_route_rules[nat_key].network_entity_id
#         ])
#     }
# }

# resource "oci_core_route_table" "updated_route_table" {
#     for_each       = var.nat_info
#     compartment_id = data.oci_core_subnet.nat_subnet[each.key].compartment_id
#     vcn_id         = data.oci_core_subnet.nat_subnet[each.key].vcn_id

#     route_rules = concat(
#         data.oci_core_route_tables.subnet_route_tables[each.key].route_tables[0].route_rules,
#         local.should_add_route[each.key] ? [local.new_route_rules[nat_key]] : []
#         )
# }
