# Root Module Main Configuration

locals {
    all_vms = merge([
        for cluster_id, cluster_vms in var.cluster_configs :
        {
            for instance_id, vm in cluster_vms :
            instance_id => {
                cluster_id           = cluster_id
                instance_id          = instance_id
                account_id           = vm.account_id
                display_name         = vm.display_name
                compartment_id       = vm.compartment_id
                availability_domain  = vm.availability_domain
                instance_shape       = vm.instance_shape
                shape_config         = vm.shape_config
                instance_image_id    = vm.instance_image_id
                shared_mgmt_subnet_id = vm.shared_mgmt_subnet_id
                dp_subnet_id          = vm.dp_subnet_id
                dp_mgmt_subnet_id     = vm.dp_mgmt_subnet_id
                user_data             = vm.user_data
                ha_subnet_id          = vm.ha_subnet_id
                shared_dp_subnet_id   = vm.shared_dp_subnet_id
                add_to_backend_set    = vm.add_to_backend_set
                add_image_filter      = vm.add_image_filter
                machine_type          = vm.machine_type
                is_secondary          = vm.is_secondary
                reserved_public_ip_id = vm.reserved_public_ip_id
                disable_dp_public_ip  =  vm.disable_dp_public_ip
                is_backup             = vm.is_backup
                assign_ipv6ip         = vm.assign_ipv6ip
                separator             = vm.separator
                nlb_id                = vm.nlb_id
                enable_ipv6      = vm.enable_ipv6
            }
        }
    ]...)
}

# Instantiate VM Modules
module "vm_instances" {
    source = "./modules/vm"

    for_each = var.idxScheme ? { for idx, vm in local.all_vms : idx => vm } : { for instance_id, vm in local.all_vms : instance_id => vm }


    vm_name                = "${each.value.account_id}-${each.key}"
    compartment_id         = each.value.compartment_id
    availability_domain    = each.value.availability_domain
    instance_shape         = lookup(each.value, "instance_shape", null)
    shape_config           = {
        ocpus              = each.value.shape_config.ocpus
        memory_in_gbs      = each.value.shape_config.memory_in_gbs
        baseline_ocpu_utilization = each.value.shape_config.baseline_ocpu_utilization
    }
    instance_image_id     = each.value.instance_image_id
    user_data             = each.value.user_data
    instance_display_name = each.value.display_name
    add_to_backend_set    = each.value.add_to_backend_set
    add_image_filter      = each.value.add_image_filter
    machine_type          = each.value.machine_type
    is_secondary          = each.value.is_secondary
    reserved_public_ip_id = lookup(each.value, "reserved_public_ip_id", null)
    region                = var.region
    disable_dp_public_ip  =  each.value.disable_dp_public_ip
    is_backup             = each.value.is_backup
    assign_ipv6ip         = each.value.assign_ipv6ip
    separator             = each.value.separator
    nlb_id                = each.value.nlb_id
    enable_ipv6      = each.value.enable_ipv6

    vnic_config = merge(
        {
            "shared-mgmt" = {
                subnet_id = each.value.shared_mgmt_subnet_id
            },
            "dp" = {
                subnet_id = each.value.dp_subnet_id
            }
        },
        each.value.shared_dp_subnet_id != null ? {
            "shared-dp" = {
                subnet_id = each.value.shared_dp_subnet_id
            }
        } : {},
        each.value.ha_subnet_id != null ? {
            "ha" = {
                subnet_id = each.value.ha_subnet_id
            }
        } : {}
        )
}

# Collect DP VNIC IPs and Subnet IDs for VMs to be added to the backend set
locals {
    dp_vnic_ips = {
        for instance_id, vm in module.vm_instances:
        instance_id => {
            ip_address = vm.vm_info["dp_private_ip"]
            is_backup  = lookup(local.all_vms[instance_id], "is_backup", false)
        }
        if local.all_vms[instance_id].add_to_backend_set
    }
    dp_vnic_ids = {
        for instance_id, vm in module.vm_instances:
        instance_id => vm.vm_info["dp_vnic_id"]
        if local.all_vms[instance_id].add_to_backend_set
    }

    nlb_specific_backends = {
        for nlb_key, nlb_config in var.nlb_configs : nlb_key => {
            for instance_id, vm in module.vm_instances:
            instance_id => {
                ip_address = vm.vm_info["dp_private_ip"]
                is_backup  = lookup(local.all_vms[instance_id], "is_backup", false)
            }
            if local.all_vms[instance_id].add_to_backend_set &&
            try(coalesce(local.all_vms[instance_id].nlb_id, keys(var.nlb_configs)[0]), keys(var.nlb_configs)[0]) == nlb_key
        }
    }
    dp_vnic_ipv6_ips = {
        for instance_id, vm in module.vm_instances:
        instance_id => vm.vm_info["dp_publicv6_ip"]
        if local.all_vms[instance_id].add_to_backend_set
    }
}

# Instantiate NLB Modules
module "nlbs" {
    source = "./modules/nlb"

    for_each = var.nlb_configs

    compartment_id      = each.value["compartment_id"]
    subnet_id           = each.value["subnet_id"]
    nlb_display_name    = each.value["display_name"]
    backend_ips         = local.nlb_specific_backends[each.key]
    backend_ipv6_ips    = local.dp_vnic_ipv6_ips
    nlb_listener_ports  = each.value["nlb_listener_ports"]
    health_checker      = each.value["health_checker"]

    nlb_backend_set_hashing_policy    = each.value["nlb_backend_set_hashing_policy"]
    nlb_conn_persistence_on_unhealthy_backends    = each.value["nlb_conn_persistence_on_unhealthy_backends"]
    reserved_public_ip_id    = each.value["reserved_public_ip_id"]
    region_egress_ip_ocid_map = each.value["region_egress_ip_ocid_map"]
    region_egress_ip_map = each.value["region_egress_ip_map"]

    are_operationally_active_backends_preferred   = lookup(each.value, "are_operationally_active_backends_preferred", false)
    is_instant_failover_tcp_reset_enabled         = lookup(each.value, "is_instant_failover_tcp_reset_enabled", false)
    is_instant_failover_enabled = lookup(each.value, "is_instant_failover_enabled", false)
    nlb_enable_ipv6 = each.value["nlb_enable_ipv6"]
    is_symmetric_hash_enabled = lookup(each.value, "is_symmetric_hash_enabled", false)
    depends_on          = [module.vm_instances]
}

module "nat_gws" {
    source = "./modules/nat_gw"

    for_each = var.nat_configs

    compartment_id         = each.value["compartment_id"]
    vcn_id                 = each.value["vcn_id"]
    vm_vnic_ids            = local.dp_vnic_ids
    nat_display_name       = each.value["display_name"]
    reserved_public_ip_id  = each.value["reserved_public_ip_id"]
    region                 = each.value["region"]
    depends_on             = [module.vm_instances]
}

locals {
    nat_info = { for nat_key, nat_gw in module.nat_gws:
        nat_key => merge(
            {
                nat_id                       = nat_gw.nat_gw_id
                region                       = nat_gw.nat_gw_region
                public_ip_address            = nat_gw.nat_gw_ip_address
                nat_gw_private_ips           = nat_gw.nat_private_ips
            },
            {
                "compartment_id" = lookup(var.nat_configs[nat_key], "compartment_id", null)
                "vcn_id" = lookup(var.nat_configs[nat_key], "vcn_id", null)
            }
            )
    }
}

module "networking" {
    source              = "./modules/networking"
    nat_info            = local.nat_info
    depends_on          = [module.vm_instances]
}


################################ BEGIN EP CONFIGURATION ##################################
locals {
    ep_vms = merge([
        for cluster_id, ep_cluster_vms in var.ep_cluster_configs :
        {
            for instance_id, vm in ep_cluster_vms :
            instance_id => {
                cluster_id           = cluster_id
                instance_id          = instance_id
                account_id           = vm.account_id
                display_name         = vm.display_name
                compartment_id       = vm.compartment_id
                availability_domain  = vm.availability_domain
                instance_shape       = vm.instance_shape
                shape_config         = vm.shape_config
                instance_image_id    = vm.instance_image_id
                shared_mgmt_subnet_id = vm.shared_mgmt_subnet_id
                dp_subnet_id         = vm.dp_subnet_id
                ep_ext_subnet_id     = vm.ep_ext_subnet_id
                user_data            = vm.user_data
                ha_subnet_id         = vm.ha_subnet_id
                shared_dp_subnet_id  = vm.shared_dp_subnet_id
                reserved_public_ip_id = vm.reserved_public_ip_id
                machine_type         = vm.machine_type
            }
        }
    ]...)
}


module "ep_vm_instances" {
    source = "./modules/vm"

    for_each = { for idx, vm in local.ep_vms : idx => vm }

    vm_name               = "${each.value.account_id}-${each.key}"
    compartment_id        = each.value.compartment_id
    availability_domain   = each.value.availability_domain
    instance_shape        = lookup(each.value, "instance_shape", null)
    shape_config          = {
        ocpus             = each.value.shape_config.ocpus
        memory_in_gbs     = each.value.shape_config.memory_in_gbs
        baseline_ocpu_utilization = each.value.shape_config.baseline_ocpu_utilization
    }
    instance_image_id     = each.value.instance_image_id
    user_data             = each.value.user_data
    instance_display_name = each.value.display_name
    machine_type          = each.value.machine_type
    is_secondary          = false
    reserved_public_ip_id = each.value.reserved_public_ip_id
    disable_dp_public_ip  = true
    # region isnt used in vm instances as of today Nov182024
    region                = var.region

    vnic_config = {
        "shared-mgmt" = {
            subnet_id = each.value.shared_mgmt_subnet_id
        },
        "dp" = {
            subnet_id = each.value.dp_subnet_id
        },
        "ext" = {
            subnet_id = each.value.ep_ext_subnet_id
        }
    }
}

# Collect DP VNIC IPs for VMs to be added to the backend set
# EXT VNIC Ids go to nat gateway config for external access
locals {
    ep_dp_vnic_ips = {
        for vm_name, vm in module.ep_vm_instances:
        vm_name => vm.vm_info["dp_private_ip"]
    }
    ep_ext_vnic_ids = {
        for vm_name, vm in module.ep_vm_instances:
        vm_name => vm.vm_info["ep_ext_vnic_id"]
    }
}

module "ep_ilb_networking" {
    source              = "./modules/ep_ilb_networking"
    for_each            = var.ep_ilb_config
    
    compartment_id      = each.value.compartment_id
    vcn_id              = each.value.vcn_id
    ep_ilb_subnet_id    = each.value.ilb_subnet_id
    ep_envoy_subnet_id  = each.value.envoy_subnet_id
    ep_lbaas_subnet_id  = each.value.lbaas_subnet_id
    ep_ext_subnet_id    = each.value.ext_subnet_id
    backend_ips         = local.ep_dp_vnic_ips
}

module "ep_nat_gws" {
    source              = "./modules/nat_gw"
    for_each            = var.ep_nat_config

    compartment_id      = each.value.compartment_id
    vcn_id              = each.value.vcn_id
    nat_display_name    = each.value.display_name
    vm_vnic_ids         = local.ep_ext_vnic_ids
    reserved_public_ip_id = try(each.value.reserved_public_ip_id, null)
    region              = each.value.region
}

module "ep_lbaas" {
    source = "./modules/lbaas"
    count = length(var.ep_lbaas_config) > 0 ? 1 : 0

    compartment_id                 = var.ep_lbaas_config.0.compartment_id
    vcn_id                         = var.ep_lbaas_config.0.vcn_id
    subnet_id                      = var.ep_lbaas_config.0.lbaas_subnet_id
    reserved_public_ip_id          = try(var.ep_lbaas_config.0.reserved_public_ip_id, null)
    certificate_certificate_name   = var.ep_lbaas_config.0.certificate_certificate_name
    certificate_private_key        = var.ep_lbaas_config.0.certificate_private_key
    certificate_public_certificate = var.ep_lbaas_config.0.certificate_public_certificate
    enable_443                     = var.ep_lbaas_config.0.enable_443
    enable_8082                    = var.ep_lbaas_config.0.enable_8082
    enable_8083                    = var.ep_lbaas_config.0.enable_8083
}

module "ep_envoy_instance_pool" {
    source = "./modules/instance_pool"
    for_each              = var.ep_envoy_config

    backend_sets          = module.ep_lbaas[0].backend_sets
    envoy_user_data       = each.value.envoy_user_data
    vcn_id                = each.value.vcn_id
    compartment_id        = each.value.compartment_id
    envoy_instance_name   = each.value.envoy_instance_name
    envoy_image_id        = each.value.envoy_image_id
    envoy_pool_size       = each.value.envoy_pool_size
    envoy_pool_size_max   = each.value.envoy_pool_size_max
    envoy_pool_size_min   = each.value.envoy_pool_size_min
    ep_envoy_subnet_id    = each.value.envoy_subnet_id
    shared_mgmt_subnet_id = each.value.shared_mgmt_subnet_id
    envoy_instance_shape  = each.value.envoy_instance
    envoy_memory          = each.value.envoy_memory
    envoy_ocpus           = each.value.envoy_ocpus

}

################################ END EP CONFIGURATION ##################################
