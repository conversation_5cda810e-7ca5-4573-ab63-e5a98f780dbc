data "oci_core_internet_gateways" "internet_gateway_data" {
    #Required
    compartment_id = var.compartment_id
    vcn_id         = var.vcn_id
}

data "oci_core_private_ips" "nlb_private_ip" {
    #Optional
    ip_address = oci_network_load_balancer_network_load_balancer.passthrough_nlb.ip_addresses[0].ip_address
    subnet_id  = var.ep_ilb_subnet_id
    depends_on = [
        oci_network_load_balancer_network_load_balancer.passthrough_nlb
    ]
}

data "oci_core_vcn" "ilb_vcn" {
    #Required
    vcn_id = var.vcn_id
}

resource "oci_core_network_security_group" "ilb_nsg" {
    #Required
    compartment_id = var.compartment_id
    vcn_id = var.vcn_id

    #Optional
    display_name = "EP_ILB_NSG"
}

resource "oci_core_network_security_group_security_rule" "ilb_nsg_rules" {
    for_each = toset(data.oci_core_vcn.ilb_vcn.cidr_blocks)

    #Required
    network_security_group_id = oci_core_network_security_group.ilb_nsg.id
    direction = "INGRESS"
    protocol = "all"
    
    source = each.value
    source_type = "CIDR_BLOCK"
    stateless = false
}

# Create the NLB
resource "oci_network_load_balancer_network_load_balancer" "passthrough_nlb" {
    compartment_id = var.compartment_id
    display_name   = "EP Passthrough NLB"
    is_private     = true
    subnet_id      = var.ep_ilb_subnet_id
    nlb_ip_version = "IPV4"

    # Enable source/destination header preservation
    is_preserve_source_destination = true
    # Disable symmetric hashing policy
    is_symmetric_hash_enabled      = false
    # Network Security Group for NLB
    network_security_group_ids = [ oci_core_network_security_group.ilb_nsg.id ]
}

# Data source to get the NLB's private IP and VNIC ID
data "oci_core_private_ips" "nlb_private_ips" {
    ip_address = oci_network_load_balancer_network_load_balancer.passthrough_nlb.ip_addresses[0].ip_address
    subnet_id  = var.ep_ilb_subnet_id
}

locals {
    nlb_private_ip = flatten([
        for ip in data.oci_core_private_ips.nlb_private_ips.private_ips :
        ip if ip.is_primary
    ])[0]
}

# Separate resource for backend set
resource "oci_network_load_balancer_backend_set" "dp_backend_set" {
  name                     = "ep-dp-backend-set"
  network_load_balancer_id = oci_network_load_balancer_network_load_balancer.passthrough_nlb.id
  policy                   = "FIVE_TUPLE"

  # Health Checker
  health_checker {
    protocol           = "TCP"
    port               = 80
    retries            = 3
    timeout_in_millis  = 10000
    interval_in_millis = 10000
  }

  # preserving the source ip so the LB is invisible
  is_preserve_source = true
  # keep sending traffic even if all backends are unhealthy
  is_fail_open       = true
}

# Separate resource for backend
resource "oci_network_load_balancer_backend" "dp_backends" {
  for_each = var.backend_ips

  network_load_balancer_id = oci_network_load_balancer_network_load_balancer.passthrough_nlb.id
  backend_set_name         = oci_network_load_balancer_backend_set.dp_backend_set.name
  ip_address               = each.value
  port                     = 0
  is_backup                = lookup(each.value, "is_backup", false)
}

# Separate resource for listener
resource "oci_network_load_balancer_listener" "dp_listeners" {
  network_load_balancer_id = oci_network_load_balancer_network_load_balancer.passthrough_nlb.id
  name                     = "AnyProtocolListener"
  default_backend_set_name = oci_network_load_balancer_backend_set.dp_backend_set.name
  port                     = 0
  protocol                 = "ANY"
}

# EP_ENVOY Subnet Route Table (Private)
resource "oci_core_route_table" "ep_envoy_route_table" {
    compartment_id = var.compartment_id
    vcn_id         = var.vcn_id
    display_name   = "EP_Envoy_Route_Table"

    route_rules {
        #Required
        network_entity_id = data.oci_core_private_ips.nlb_private_ip.private_ips[0].id

        #Optional
        destination       = "0.0.0.0/0"
        destination_type  = "CIDR_BLOCK"
    }
}

resource "oci_core_route_table_attachment" "envoy_route_table_attachment" {
    #Required
    subnet_id      = var.ep_envoy_subnet_id
    route_table_id = oci_core_route_table.ep_envoy_route_table.id
}

# EP LBAAS Subnet Route Table (Public)
resource "oci_core_route_table" "ep_lbaas_route_table" {
    compartment_id = var.compartment_id
    vcn_id         = var.vcn_id
    display_name   = "EP_LBaaS_Route_Table"

    route_rules {
        #Required
        network_entity_id = data.oci_core_internet_gateways.internet_gateway_data.gateways[0].id

        #Optional
        destination       = "0.0.0.0/0"
        destination_type  = "CIDR_BLOCK"
    }
}

resource "oci_core_route_table_attachment" "lbaas_route_table_attachment" {
    #Required
    subnet_id      = var.ep_lbaas_subnet_id
    route_table_id = oci_core_route_table.ep_lbaas_route_table.id
}

# EP Ext Subnet Route Table (Public)
resource "oci_core_route_table" "ep_ext_route_table" {
    compartment_id = var.compartment_id
    vcn_id         = var.vcn_id
    display_name   = "EP_Ext_Route_Table"

    route_rules {
        #Required
        network_entity_id = data.oci_core_internet_gateways.internet_gateway_data.gateways[0].id

        #Optional
        destination       = "0.0.0.0/0"
        destination_type  = "CIDR_BLOCK"
    }
}

resource "oci_core_route_table_attachment" "ext_route_table_attachment" {
    #Required
    subnet_id      = var.ep_ext_subnet_id
    route_table_id = oci_core_route_table.ep_ext_route_table.id
}