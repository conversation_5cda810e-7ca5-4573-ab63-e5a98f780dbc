variable reserved_public_ip_id {
    type = string
    nullable = true
}

variable subnet_id {
    type = string
}

variable vcn_id {
    type = string
}

variable compartment_id {
    type = string
}

variable enable_443 {
    type = bool
}

variable enable_8082 {
    type = bool
}

variable enable_8083 {
    type = bool
}

variable certificate_certificate_name {
    type = string
    nullable = true
}

variable certificate_private_key {
    type = string
    sensitive = true
    nullable = true
}

variable certificate_public_certificate {
    type = string
    sensitive = true
    nullable = true
}

variable backend_set_ssl_configuration_protocols {
    type = list(string)
    default = ["TLSv1.2", "TLSv1.3"]
}