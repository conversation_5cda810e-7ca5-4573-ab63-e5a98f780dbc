# Instance pool Module Variables

variable "compartment_id" {
    description = "Compartment OCID for the VM instance"
    type        = string
}

variable "vcn_id" {
    description = "Vcn OCID for the Security group configuration"
    type        = string
}

variable "envoy_instance_shape" {
    description = "Instance Shape"
    type        = string
    default     = "VM.Standard.E5.Flex"
}

variable "envoy_instance_name" {
    description = "Envoy Instance name"
    type        = string
}

variable "envoy_memory" {
    description = "Envoy Instance Memory"
    type        = number
}

variable "envoy_ocpus" {
    description = "Envoy Instance Ocpus"
    type        = number
}

variable "envoy_image_id" {
    description = "Envoy Image Id"
    type        = string
}

variable "envoy_user_data" {
    description = "User Data"
    type        = string
    default     = ""
}

variable "backend_sets" {
    type = map(object({
        name         = string
        lbaas_id     = string
        port         = number
    }))
    description = "Backend set config."
}

variable "shared_mgmt_subnet_id" {
    description = "Shared mgmt subnet OCID"
    type        = string
}

variable "ep_envoy_subnet_id" {
    description = "envoy subnet OCID"
    type        = string
}

variable "envoy_pool_size" {
    description = "Envoy pool initial size"
    type        = number
}

variable "envoy_pool_size_max" {
    description = "Envoy pool max size"
    type        = number
}

variable "envoy_pool_size_min" {
    description = "Envoy pool min size"
    type        = number
}