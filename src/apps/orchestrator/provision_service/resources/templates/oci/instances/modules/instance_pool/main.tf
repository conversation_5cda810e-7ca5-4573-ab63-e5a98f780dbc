locals {
  # Process user_data only if it has content; otherwise, set as an empty map
  metadata = length(var.envoy_user_data) > 0 ? zipmap(
    [for pair in split(",", var.envoy_user_data) : trim(element(split("=", pair), 0), " ")],
    [for pair in split(",", var.envoy_user_data) : trim(element(split("=", pair), 1), " ")]
  ) : {}
}

data "oci_identity_availability_domain" "this" {
    compartment_id = var.compartment_id
    ad_number      = 1
}

resource "oci_core_instance_pool" "envoy_instance_pool" {
    compartment_id            = var.compartment_id
    display_name              = var.envoy_instance_name
    instance_configuration_id = oci_core_instance_configuration.envoy_instance_configuration.id
    size                      = var.envoy_pool_size

    dynamic "load_balancers" {
        for_each         = var.backend_sets
        content {
            backend_set_name = load_balancers.value.name
            load_balancer_id = load_balancers.value.lbaas_id
            port             = load_balancers.value.port
            vnic_selection   = "Ep-Envoy-dp-vnic"
        }
    }

    placement_configurations {
        availability_domain = data.oci_identity_availability_domain.this.name

        primary_vnic_subnets {
            is_assign_ipv6ip = false
            subnet_id        = var.shared_mgmt_subnet_id
        }

        secondary_vnic_subnets {
            display_name     = "Ep-Envoy-dp-vnic"
            is_assign_ipv6ip = false
            subnet_id        = var.ep_envoy_subnet_id
        }
    }
    
    lifecycle {
        create_before_destroy = true
        replace_triggered_by = [
            oci_core_instance_configuration.envoy_instance_configuration.id
        ]
    }
}

resource "oci_core_instance_configuration" "envoy_instance_configuration" {
    compartment_id  = var.compartment_id
    display_name    = "EP-Envoy-Instance-Config"

    instance_details {
        instance_type = "compute"

        launch_details {
            availability_domain                 = data.oci_identity_availability_domain.this.name
            compartment_id                      = var.compartment_id
            metadata                            = local.metadata
            shape                               = var.envoy_instance_shape

            agent_config {
                are_all_plugins_disabled = false
                is_management_disabled   = false
                is_monitoring_disabled   = false

                plugins_config {
                    desired_state = "ENABLED"
                    name          = "Compute Instance Monitoring"
                }
                plugins_config {
                    desired_state = "DISABLED"
                    name          = "Vulnerability Scanning"
                }
                plugins_config {
                    desired_state = "DISABLED"
                    name          = "Management Agent"
                }
                plugins_config {
                    desired_state = "DISABLED"
                    name          = "Custom Logs Monitoring"
                }
                plugins_config {
                    desired_state = "DISABLED"
                    name          = "Compute RDMA GPU Monitoring"
                }
                plugins_config {
                    desired_state = "DISABLED"
                    name          = "Compute HPC RDMA Auto-Configuration"
                }
                plugins_config {
                    desired_state = "DISABLED"
                    name          = "Compute HPC RDMA Authentication"
                }
                plugins_config {
                    desired_state = "DISABLED"
                    name          = "Cloud Guard Workload Protection"
                }
                plugins_config {
                    desired_state = "DISABLED"
                    name          = "Block Volume Management"
                }
                plugins_config {
                    desired_state = "DISABLED"
                    name          = "Bastion"
                }
            }

            availability_config {
                is_live_migration_preferred = true
                recovery_action             = "RESTORE_INSTANCE"
            }

            create_vnic_details {
                assign_ipv6ip             = false
                assign_private_dns_record = false
                assign_public_ip          = false
                display_name              = "Ep-Envoy-mgmt-vnic"
                skip_source_dest_check    = false
                subnet_id                 = var.shared_mgmt_subnet_id
            }

            instance_options {
                are_legacy_imds_endpoints_disabled = false
            }

            shape_config {
                memory_in_gbs = var.envoy_memory
                ocpus         = var.envoy_ocpus
            }

            source_details {
                image_id    = var.envoy_image_id
                source_type = "image"
            }
        }

        secondary_vnics {
            display_name = "Ep-Envoy-dp-vnic"

            create_vnic_details {
                assign_ipv6ip             = false
                assign_private_dns_record = false
                assign_public_ip          = false
                display_name              = "Ep-Envoy-dp-vnic"
                skip_source_dest_check    = false
                subnet_id                 = var.ep_envoy_subnet_id
                nsg_ids                   = [oci_core_network_security_group.envoy_nsg.id]
            }
        }
    }
}

resource "oci_autoscaling_auto_scaling_configuration" "auto_scaling_config" {
    auto_scaling_resources {
        id = oci_core_instance_pool.envoy_instance_pool.id
        type = "instancePool"
    }
    compartment_id = var.compartment_id
    cool_down_in_seconds = 300
    display_name = "Envoy-Autoscaling"
    is_enabled = true
    policies {
        policy_type = "threshold"
        capacity {
            initial = var.envoy_pool_size
            max = var.envoy_pool_size_max
            min = var.envoy_pool_size_min
        }
        display_name = "Cpu-Based-Autoscaling"
        is_enabled = true
        rules {
            action {
                type = "CHANGE_COUNT_BY"
                value = 1
            }
            display_name = "scale-out-rule"
            metric {
                metric_type = "CPU_UTILIZATION"
                threshold {
                    operator = "GT"
                    value    = 70
                }
            }
        }
        
        rules {
            action {
                type = "CHANGE_COUNT_BY"
                value = -1
            }
            display_name = "scale-in-rule"
            metric {
                metric_type = "CPU_UTILIZATION"
                threshold {
                    operator = "LT"
                    value    = 50
                }
            }
        }
    }
}

data "oci_core_vcn" "instance_pool_vcn" {
    #Required
    vcn_id = var.vcn_id
}

resource "oci_core_network_security_group" "envoy_nsg" {
    #Required
    compartment_id = var.compartment_id
    vcn_id = var.vcn_id

    #Optional
    display_name = "EP_Envoy_NSG"
}

resource "oci_core_network_security_group_security_rule" "envoy_nsg_rules" {
    for_each = toset(data.oci_core_vcn.instance_pool_vcn.cidr_blocks)

    #Required
    network_security_group_id = oci_core_network_security_group.envoy_nsg.id
    direction = "INGRESS"
    protocol = "all"
    
    source = each.value
    source_type = "CIDR_BLOCK"
    stateless = false
}