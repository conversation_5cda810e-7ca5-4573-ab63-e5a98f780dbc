{{$proj := .Project -}}

terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "5.42.0"
    }
  }
}

provider "google" {
  credentials = file("credentials.json")
  project     = "{{.Provider.Project}}"
}

resource "google_project" "{{$proj.TFLocalName}}" {
  name       = "{{$proj.Name}}"
  project_id = "{{$proj.Id}}"
  folder_id  = "{{$proj.FolderId}}"
{{if len $proj.Labels -}}
{{"  "}}labels = {
{{range $key, $val := $proj.Labels -}}
{{"    "}}{{$key}} = "{{$val}}"
{{end}}{{/* range $key, $val := $proj.Labels */ -}}
{{"  "}}}
{{end}}{{/* if gt (len $proj.Labels) 0 */ -}}
{{"  "}}billing_account = "{{$proj.BillingAccount}}"
{{if $proj.IgnoreChangesArguments -}}
{{"  "}}lifecycle {
    ignore_changes = [
{{range $arg := $proj.IgnoreChangesArguments -}}
{{"      "}}{{$arg}},
{{end}}{{/* range $arg := $proj.IgnoreChangesArguments */ -}}
{{"    "}}]
  }
{{end}}{{/* if $proj.IgnoreChangesArguments */ -}}
}

data "google_project" "{{$proj.TFLocalName}}" {
  project_id = google_project.{{$proj.TFLocalName}}.project_id
}

{{range $idx, $svc := $proj.Services -}}
resource "google_project_service" "service_{{$idx}}" {
  disable_on_destroy = true
  project = google_project.{{$proj.TFLocalName}}.project_id
  service = "{{$svc}}"
{{if $proj.ServicesIgnoreChangesArguments -}}
{{"  "}}lifecycle {
    ignore_changes = [
{{range $arg := $proj.ServicesIgnoreChangesArguments -}}
{{"      "}}{{$arg}},
{{end}}{{/* range $arg := $proj.ServicesIgnoreChangesArguments */ -}}
{{"    "}}]
  }
{{end}}{{/* if $proj.ServicesIgnoreChangesArguments */ -}}
}

{{end}}{{/* $proj.Services */ -}}

{{if $proj.RemoveDefaultSa -}}
resource "google_project_default_service_accounts" "{{$proj.TFLocalName}}" {
  project = google_project.{{$proj.TFLocalName}}.project_id
  action = "DELETE"
  restore_policy = "NONE"
{{if len $proj.Services -}}
{{"  "}}depends_on = [{{range $i, $e := $proj.Services}}{{if gt $i 0}}, {{end}}google_project_service.service_{{$i}}{{end}}]
{{end}}{{/* if len $proj.Services */ -}}
}

{{end}}{{/* if $proj.RemoveDefaultSa */ -}}

{{range $sa := $proj.ServiceAccounts -}}
resource "google_service_account" "{{$sa.TFLocalName}}" {
  account_id   = "{{$sa.Id}}"
  display_name = "{{$sa.DisplayName}}"
  project = google_project.{{$proj.TFLocalName}}.project_id
{{if len $proj.Services -}}
{{"  "}}depends_on = [{{range $i, $e := $proj.Services}}{{if gt $i 0}}, {{end}}google_project_service.service_{{$i}}{{end}}]
{{end}}{{/* if len $proj.Services */ -}}
{{if $sa.IgnoreChangesArguments -}}
{{"  "}}lifecycle {
    ignore_changes = [
{{range $arg := $sa.IgnoreChangesArguments -}}
{{"      "}}{{$arg}},
{{end}}{{/* range $arg := $sa.IgnoreChangesArguments */ -}}
{{"    "}}]
  }
{{end}}{{/* if $sa.IgnoreChangesArguments */ -}}
}

{{range $role := $sa.Roles -}}
resource "google_project_iam_member" "{{$role.TFLocalName}}" {
  project = google_project.{{$proj.TFLocalName}}.project_id
  role    = "roles/{{$role.Name}}"
  member  = "{{$sa.Identity}}"
  depends_on = [google_service_account.{{$sa.TFLocalName}}]
}

{{end}}{{/* range $role = $sa.Roles */ -}}
{{end}}{{/* range $sa = $proj.ServiceAccounts */ -}}

{{if $proj.HostProjectId -}}
resource "google_compute_shared_vpc_service_project" "{{$proj.TFLocalName}}" {
  host_project    = "{{$proj.HostProjectId}}"
  service_project = "{{$proj.Id}}"
}

{{end}}{{/* if $proj.HostProjectId */ -}}

{{range $role := $proj.CloudServicesAgentRoles -}}
resource "google_project_iam_member" "{{$role.TFLocalName}}" {
  project = "{{$role.ProjectId}}"
  role    = "roles/{{$role.Name}}"
  member  = "serviceAccount:${data.google_project.{{$proj.TFLocalName}}.number}@cloudservices.gserviceaccount.com"
}

{{end}}{{/* range $role := $proj.CloudServicesAgentRoles */ -}}

output "project_number" {
  value = data.google_project.{{$proj.TFLocalName}}.number
}