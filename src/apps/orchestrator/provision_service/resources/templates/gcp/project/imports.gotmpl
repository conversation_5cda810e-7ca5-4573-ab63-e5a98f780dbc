{{/* skip importing of google_project_service as creation is a no-op if API is already enabled */ -}}
{{/* skip importing of google_project_iam_member as it does not create a new GCP resource, but to adjust role binding */ -}}
{{/* skip importing of google_project_default_service_accounts as it does not support import */ -}}

{{$proj := .Project -}}
{{$clsvcemail := .CloudSvcAcctEmail -}}
{{$svcAcctsToImport := .SvcAcctsToImport -}}
import {
  id = "{{$proj.Id}}"
  to = google_project.{{$proj.TFLocalName}}
}

{{range $sa := $svcAcctsToImport -}}
import {
  id = "projects/{{$proj.Id}}/serviceAccounts/{{$sa.Id}}@{{$proj.Id}}.iam.gserviceaccount.com"
  to = google_service_account.{{$sa.TFLocalName}}
}

{{end}}{{/* range $sa = $proj.ServiceAccounts */ -}}

{{if $proj.HostProjectId -}}
import {
  id = "{{$proj.HostProjectId}}/{{$proj.Id}}"
  to = google_compute_shared_vpc_service_project.{{$proj.TFLocalName}}
}

{{end}}{{/* if $proj.HostProjectId */ -}}