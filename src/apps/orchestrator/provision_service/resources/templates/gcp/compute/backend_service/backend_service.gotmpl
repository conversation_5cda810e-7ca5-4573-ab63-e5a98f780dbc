resource "google_compute_region_backend_service" "{{.TfResourceName}}" {
  name = "{{.Name}}"
  provider = google-beta
  region = "{{.Region}}"
  protocol = "{{.Protocol}}"
  load_balancing_scheme = "{{.LoadBalancingScheme}}"
  session_affinity = "{{.SessionAffinity}}"
  health_checks = [{{range $hc := .HealthChecks}}google_compute_region_health_check.{{$hc.Name}}.self_link,{{end}}]
  {{- range $b := .Backends}}
  {{- if eq $b.Type "umig"}}
  backend {
    group = google_compute_instance_group.{{$b.Name}}.self_link
    balancing_mode = "{{$b.BalancingMode}}"
    failover = {{$b.Failover}}
  }
  {{- end}}{{/* if eq $b.Type "umig" */}}
  {{- if eq $b.Type "mig"}}
  backend {
    group = google_compute_region_instance_group_manager.{{$b.Name}}.instance_group
    balancing_mode = "{{$b.BalancingMode}}"
    failover = {{$b.Failover}}
  }
  {{- end}}{{/* if eq $b.Type "mig" */}}
  {{- end}}{{/* range $b := .Backends */}}
  {{- if .ConnTracking }}
  connection_tracking_policy {
      {{- if .ConnTracking.IdleTimeoutSec}}
      idle_timeout_sec = {{.ConnTracking.IdleTimeoutSec}}
      {{- end}}{{/* IdleTimeoutSec */}}
      {{- if .ConnTracking.TrackingMode}}
      tracking_mode = "{{.ConnTracking.TrackingMode}}"
      {{- end}}{{/* TrackingMode */}}
      {{- if .ConnTracking.Persistence}}
      connection_persistence_on_unhealthy_backends = "{{.ConnTracking.Persistence}}"
      {{- end}}{{/* Persistence */}}
      {{- if .ConnTracking.EnableStrongAffinity}}
      enable_strong_affinity = true
      {{- end}}{{/* EnableStrongAffinity */}}
  }
  {{- end}}{{/* if .ConnTracking */}}
  {{- if .DrainingTimeoutSec }}
  connection_draining_timeout_sec = {{.DrainingTimeoutSec}}
  {{- end}}{{/* if .DrainingTimeoutSec */}}
  {{- if .FailoverPolicy }}
  failover_policy {
      disable_connection_drain_on_failover = {{.FailoverPolicy.SkipDrainOnUnhealthy}}
  }
  {{- end}}{{/* if .FailoverPolicy */}}
  {{- if .SecurityPolicy }}
  security_policy = data.google_compute_security_policy.{{.TfResourceName}}-sp.self_link
  {{- end}}{{/* if .SecurityPolicy */}}
  {{- if .Network}}
  network = "{{.Network}}"
  {{- end}}{{/* if .Network */}}

  {{if .IgnoreNetworkChanges}}
  lifecycle {
  ignore_changes = [network]
  }
  {{end}}
}

{{- if .SecurityPolicy }}
data "google_compute_security_policy" "{{.TfResourceName}}-sp" {
  name = "{{.SecurityPolicy}}"
}
{{- end}}{{/* if .SecurityPolicy */}}

