resource "google_compute_region_autoscaler" "{{.TfResourceName}}" {
  name   = "{{.Name}}"
  provider = google-beta
  region = "{{.Region}}"
  target = {{.TargetType}}.{{.Target}}.self_link

  autoscaling_policy {
{{- if .Mode}}
    mode = "{{.Mode}}"
{{- end}}{{/* if .Mode */}}
    max_replicas    = {{.MaxReplicas}}
    min_replicas    = {{.MinReplicas}}
{{- if .CooldownPeriod}}
{{"    "}}cooldown_period = {{.CooldownPeriod}}
{{- end}}{{/* if .CooldownPeriod */}}
{{- if .CPUUtilization }}
    cpu_utilization {
      target = {{.CPUUtilization.Target}}
      predictive_method = "{{.CPUUtilization.PredictiveMethod}}"
    }
{{- end}}{{/* if .CPUUtilization */}}
{{- if .ScaleInControl }}
    scale_in_control {
      max_scaled_in_replicas {
        {{- if .ScaleInControl.MaxScaledInReplicas.Fixed}}
        fixed = {{.ScaleInControl.MaxScaledInReplicas.Fixed}}
        {{- end}}
        {{- if .ScaleInControl.MaxScaledInReplicas.Percent}}
        percent = {{.ScaleInControl.MaxScaledInReplicas.Percent}}
        {{- end}}
      }
      time_window_sec = {{.ScaleInControl.TimeWindowSec}}
    }
{{- end}}{{/* if .ScaleInControl */}}
{{- range $id, $cm := .CustomMetrics}}
    metric {
      name = "{{$cm.Name}}"
      type = "{{$cm.Type}}"
      target = {{$cm.Target}}
      filter = "{{$cm.Filter}}"
    }
{{- end}}{{/* range $id, $cm := .CustomMetrics */}}
{{- range $id, $ss := .ScalingSchedules}}
    scaling_schedules {
      name = "{{$ss.Name}}"
      min_required_replicas = {{$ss.MinRequiredReplicas}}
      schedule = "{{$ss.Schedule}}"
      duration_sec = {{$ss.DurationSec}}
      disabled = "{{$ss.Disabled}}"
      description = "{{$ss.Description}}"
{{- if $ss.Timezone}}
      time_zone = "{{$ss.Timezone}}"
{{- end}}
    }
{{- end}}{{/* range $id, $ss := .ScalingSchedules */}}
  }
}
