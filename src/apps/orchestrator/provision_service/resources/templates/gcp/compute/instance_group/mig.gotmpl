resource "google_compute_region_instance_group_manager" "{{.TfResourceName}}" {
  provider = google-beta
  name   = "{{.Name}}"
  base_instance_name = "{{.BaseInstanceName}}"
  region = "{{.Region}}"
{{- if .TargetSize}}
  target_size = {{.TargetSize}}
{{- end}}
{{- if len .DistributionPolicyZones}}
  distribution_policy_zones = [
{{- range $dpz := .DistributionPolicyZones}}
    "{{$dpz}}",
{{- end}}{{/* range $dpz := .DistributionPolicyZones */}}
  ]
{{- end}}{{/* if len .distribution_policy_zones */}}
{{- if .DistributionPolicyTargetShape }}
  distribution_policy_target_shape = "{{.DistributionPolicyTargetShape}}"
{{- end}}{{/* if .DistributionPolicyTargetShape */}}
{{- range $tpl := .Version}}
  version {
    instance_template  = google_compute_instance_template.{{$tpl.Name}}.self_link
    name               = "{{$tpl.Name}}"
{{- if $tpl.TargetSize.Fixed}}
    target_size {
      fixed = {{$tpl.TargetSize.Fixed}}
    }
{{- end}} {{/* if $tpl.TargetSize.Fixed */}}
  }
{{- end}} {{/* range $tpl := .Version */}}
{{- if len .AutoHealing.HealthChecks}}
{{- $initial_delay_sec := .AutoHealing.InitialDelay}}
{{- range $hc := .AutoHealing.HealthChecks}}
  auto_healing_policies {
    health_check = google_compute_region_health_check.{{$hc.TfResourceName}}.self_link
{{- if $initial_delay_sec}}
    initial_delay_sec = {{$initial_delay_sec}}
{{- end}}{{/* if $initial_delay_sec*/}}
  }
{{- end}}{{/* range $hc := .AutoHealing.HealthChecks */}}
{{- end}}{{/* if len .AutoHealing.HealtchChecks*/}}

{{- if .UpdatePolicy }}
  update_policy {
{{- if .UpdatePolicy.Type }}
    type = "{{.UpdatePolicy.Type}}"
{{- end}}{{/* if .UpdatePolicy.Type */ -}}
{{- if .UpdatePolicy.InstanceRedistType }}
    instance_redistribution_type = "{{.UpdatePolicy.InstanceRedistType}}"
{{- end}}{{/* if .UpdatePolicy.InstanceRedistType */ -}}
{{- if .UpdatePolicy.MinimalAction }}
    minimal_action = "{{.UpdatePolicy.MinimalAction}}"
{{- end}}{{/* if .UpdatePolicy.MinimalAction */ -}}
{{- if .UpdatePolicy.MinReadySeconds }}
    min_ready_sec = {{.UpdatePolicy.MinReadySeconds}}
{{- end}}{{/* if .UpdatePolicy.MinReadySeconds */ -}}
{{- if .UpdatePolicy.ReplacementMethod }}
    replacement_method = "{{.UpdatePolicy.ReplacementMethod}}"
{{- end}}{{/* if .UpdatePolicy.ReplacementMethod*/}}
{{- if .UpdatePolicy.MostDisruptiveAllowedAction }}
    most_disruptive_allowed_action = "{{.UpdatePolicy.MostDisruptiveAllowedAction}}"
{{- end}}{{/* if .UpdatePolicy.MostDisruptiveAllowedAction*/}}
{{- if .UpdatePolicy.MaxSurgePercent }}
    max_surge_percent = {{.UpdatePolicy.MaxSurgePercent}}
{{- end}}{{/* if .UpdatePolicy.MaxSurgePercent*/}}
{{- if .UpdatePolicy.MaxUnavailableFixed }}
    max_unavailable_fixed = {{.UpdatePolicy.MaxUnavailableFixed}}
{{- end}}{{/* if .UpdatePolicy.MaxUnavailableFixed*/}}
{{- if .UpdatePolicy.MaxSurgeFixed }}
    max_surge_fixed = {{.UpdatePolicy.MaxSurgeFixed}}
{{- end}}{{/* if .UpdatePolicy.MaxSurgeFixed*/}}
{{- if .UpdatePolicy.MaxUnavailablePercent }}
    max_unavailable_fixed = {{.UpdatePolicy.MaxUnavailablePercent}}
{{- end}}{{/* if .UpdatePolicy.MaxUnavailablePercent*/}}
  }
{{- end}}{{/* if .UpdatePolicy*/}}
{{- range $np := .NamedPorts }}
  named_port {
      name = "{{$np.Name}}"
      port = {{$np.Port}}
  }
{{- end}}{{/* range $np := .NamedPorts*/}}
{{- if .InstanceLifecyclePolicy }}
  instance_lifecycle_policy {
{{- if .InstanceLifecyclePolicy.ForceUpdateOnRepair }}
    force_update_on_repair = "YES"
{{- end }}{{/* if InstanceLifecyclePolicy.ForceUpdateOnRepair */}}
{{- if .InstanceLifecyclePolicy.DefaultActionOnFailure }}
    default_action_on_failure = "{{.InstanceLifecyclePolicy.DefaultActionOnFailure}}"
{{- end }}{{/* if .InstanceLifecyclePolicy.DefaultActionOnFailure */}}
  }
{{- end }}{{/* if .InstanceLifecyclePolicy */}}
{{- if .StandbyPolicy}}
  standby_policy {
    initial_delay_sec = {{.StandbyPolicy.InitialDelaySec}}
    mode = "{{.StandbyPolicy.Mode}}"
  }
  {{- if .TargetSuspendedSize}}
  target_suspended_size = {{.TargetSuspendedSize}}
  {{- end}}
  {{- if .TargetStoppedSize}}
  target_stopped_size = {{.TargetStoppedSize}}
  {{- end}}
{{- end}}
}
