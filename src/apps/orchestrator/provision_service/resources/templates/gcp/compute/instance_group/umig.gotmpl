resource "google_compute_instance_group" "{{.TfResourceName}}" {
{{if .Provider -}}
  provider = {{.Provider}}
{{end}}{{/* if .Provider */ -}}
  name = "{{.Name}}"
  instances = [{{range $instanceName := .Instances}}
    google_compute_instance.{{$instanceName}}.self_link,{{end}} {{/* range $instanceName := .Instances */}}
  ]
  zone = "{{.Zone}}"
{{- if .Network}}
  network = "{{.Network}}"
{{- end}}{{/* if .Network */}}
}
