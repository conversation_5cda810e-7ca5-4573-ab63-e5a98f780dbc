resource "google_compute_region_health_check" "{{.TfResourceName}}" {
  name = "{{.Name}}"
  provider = google-beta
  region = "{{.Region}}"
{{- if .CheckIntervalSec}}
{{"  "}}check_interval_sec = {{.CheckIntervalSec}}
{{- end}}{{/* if .CheckIntervalSec */}}
{{- if .TimeoutSec}}
{{"  "}}timeout_sec = {{.TimeoutSec}}
{{- end}}{{/* if .TimeoutSec */}}
{{- if .UnhealthyThreshold}}
{{"  "}}unhealthy_threshold = {{.UnhealthyThreshold}}
{{- end}}{{/* if .UnhealthyThreshold */}}
{{- if .HealthyThreshold}}
{{"  "}}healthy_threshold = {{.HealthyThreshold}}
{{- end}}{{/* if .HealthyThreshold */}}

{{- if eq .Type "http" -}}
{{- if .HTTPHealthCheck}}
{{"  "}}http_health_check {
{{- if .HTTPHealthCheck.Port}}
{{"    "}}port = {{.HTTPHealthCheck.Port}}
{{- end}}{{/* if .HTTPHealthCheck.Port */}}
{{- if .HTTPHealthCheck.RequestPath}}
{{"    "}}request_path = "{{.HTTPHealthCheck.RequestPath}}"
{{- end}}{{/* if .HTTPHealthCheck.RequestPath */}}
{{"  "}}}
{{- end}}{{/* if .HTTPHealthCheck */}}
{{- end}}{{/* if eq .Type "http" */}}
{{- if eq .Type "https" -}}
{{- if .HTTPSHealthCheck}}
{{"  "}}https_health_check {
{{- if .HTTPSHealthCheck.Port}}
{{"    "}}port = {{.HTTPSHealthCheck.Port}}
{{- end}}{{/* if .HTTPSHealthCheck.Port */}}
{{- if .HTTPSHealthCheck.RequestPath}}
{{"    "}}request_path = "{{.HTTPSHealthCheck.RequestPath}}"
{{- end}}{{/* if .HTTPSHealthCheck.RequestPath */}}
{{"  "}}}
{{- end}}{{/* if .HTTPSHealthCheck */}}
{{- end}}{{/* if eq .Type "https" */}}
{{- if eq .Type "tcp" -}}
{{- if .TCPHealthCheck}}
{{"  "}}tcp_health_check {
{{- if .TCPHealthCheck.Port}}
{{"    "}}port = {{.TCPHealthCheck.Port}}
{{- end}}{{/* if .TCPHealthCheck.Port */}}
{{"  "}}}
{{- end}}{{/* if .TCPHealthCheck */}}
{{- end}}{{/* if eq .Type "tcp" */}}
{{- if .IgnoreChangesArguments}}
  lifecycle {
    ignore_changes = [
{{- range $arg := .IgnoreChangesArguments}}
      {{$arg}},
{{- end}}
    ]
  }
{{- end}}{{/* if IgnoreChangesArguments */}}
}
