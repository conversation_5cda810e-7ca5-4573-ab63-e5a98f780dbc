{{- $VMname := .Name -}}
output "{{$VMname}}" {
  value = {
    type = "vm"
    name = "{{$VMname}}"
    vm_id = google_compute_instance.{{$VMname}}.instance_id
{{- if len .Labels}}
    labels = {
{{- range $name, $value := .Labels}}
      {{$name}} = {{$value}}
{{- end}}{{/* range $name, $value := .Labels */}}
    }
{{- end}}{{/* if len/Labels */}}
{{- if len .Interfaces }}
    interfaces = [
{{- range $id, $ifc := .Interfaces}}
      {
        name = "{{$ifc.InterfaceName}}"
        pvt_ip = google_compute_instance.{{$VMname}}.network_interface[{{$id}}].network_ip
{{- if $ifc.AccessConfig.NATIP}}
        public_ip = google_compute_instance.{{$VMname}}.network_interface[{{$id}}].access_config[0].nat_ip
{{- end}}{{/* if $ifc.AccessConfig.NATIP */}}
      },
{{- end}}{{/* range $ifc := .Interfaces */}}
    ]
{{- end}}{{/* if len .Interfaces */}}
    shutdown = {{eq .DesiredStatus "TERMINATED"}}
  }
}
