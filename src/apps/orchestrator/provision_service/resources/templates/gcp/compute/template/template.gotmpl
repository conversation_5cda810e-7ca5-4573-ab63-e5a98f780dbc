resource "google_compute_instance_template" "{{.TfResourceName}}" {
{{- if not .UpdateDefaultVersion}}
{{/* Only add name if we're not supporting UpdateDefaultVersion */}}
  name = "{{.Name}}"
{{- else }}
{{/* Else support a prefix */}}
  name_prefix = "{{.Name}}"
{{- end}}
  machine_type = "{{.MachineType}}"
  can_ip_forward = true
{{if len .Labels -}}
{{"  "}}labels = {
{{range $name, $value := .Labels -}}
{{"    "}}{{$name}} = {{$value}}
{{end}}{{/* range $name, $value := .Labels */ -}}
{{"  "}}}
{{end}}{{/* if len .Labels */ -}}
{{"  "}}disk {
    auto_delete = true
    source_image = "{{.BootDisk.InitializeParams.Image}}"
{{- if .BootDisk.InitializeParams.Type}}
    disk_type = "{{.BootDisk.InitializeParams.Type}}"
{{- end}}{{/* if .BootDisk.InitializeParams.Type */}}
{{- if .BootDisk.InitializeParams.Size}}
    disk_size_gb = {{.BootDisk.InitializeParams.Size}}
{{- end}}{{/* if .BootDisk.InitializeParams.Size */}}
  }
{{range $ifc := .Interfaces -}}
{{"  "}}network_interface {
    network = "{{$ifc.Network}}"
    subnetwork = "{{$ifc.Subnet}}"
{{if $ifc.AliasIpRanges}}
  {{range $aliasIp := $ifc.AliasIpRanges}}
    alias_ip_range {
      ip_cidr_range = "{{$aliasIp.IpCidrRange}}"
      subnetwork_range_name = "{{$aliasIp.SubnetworkRangeName}}"
    }
  {{end}}
{{end}}{{/* if $ifc.AliasIPRange */}}
{{if $ifc.QueueCount -}}
{{"    "}}queue_count = {{$ifc.QueueCount}}
{{end}}{{/* if $ifc.QueueCount */ -}}
{{if $ifc.AccessConfig.NATIP -}}
{{"    "}}access_config {
{{- if ne $ifc.AccessConfig.NATIP "any"}}
{{"      "}}nat_ip = {{$ifc.AccessConfig.NATIP}}
{{- end}}
{{"    "}}}
{{end}}{{/* if $ifc.AccessConfig.NATIP */ -}}
{{if $ifc.StackType -}}
{{"    "}}stack_type = "{{$ifc.StackType}}"
{{end}}{{/* if $ifc.StackType */ -}}
{{"  "}}}
{{end}}{{/* range $ifc := .Interfaces */ -}}

{{if .Metadata -}}
{{"  "}}metadata = {
{{range $name, $value := .Metadata -}}
{{"    "}}{{$name}} = {{$value}}
{{end}}{{/* range $name, $value := .Metadata */ -}}
{{"  "}}}
{{end}}{{/* if .Metadata */ -}}
{{- if .Credential.Email}}
{{"  "}}service_account {
    email = "{{.Credential.Email}}"
    scopes = ["https://www.googleapis.com/auth/cloud-platform"]
  }
{{- end}}
{{- if len .Tags}}
  tags = [
{{- range $tag := .Tags}}
    "{{$tag}}",
{{- end}}{{/* range $tag := .Tags */}}
  ]
{{- end}}{{/* if len .Tags */}}
{{- if .MinCpuPlatform}}
  min_cpu_platform = "{{.MinCpuPlatform}}"
{{- end}}{{/* if .MinCpuPlatform */}}
{{- if .ShieldInstanceConfig}}
  shielded_instance_config {
    enable_secure_boot = {{.ShieldInstanceConfig.EnableSecureBoot}}
    enable_vtpm = {{.ShieldInstanceConfig.EnableVTPM}}
    enable_integrity_monitoring = {{.ShieldInstanceConfig.EnableIntegrityMonitoring}}
  }
{{- end}}{{/* if .ShieldInstanceConfig */}}
{{- if or .UpdateDefaultVersion .IgnoreChangesArguments}}
  lifecycle {
{{- if .UpdateDefaultVersion }}
    create_before_destroy = true
{{- end}}{{/* if .UpdateDefaultVersion */}}
{{- if .IgnoreChangesArguments}}
    ignore_changes = [
{{- range $arg := .IgnoreChangesArguments}}
      {{$arg}},
{{- end}}
    ]
{{- end}}{{/* if IgnoreChangesArguments */}}
  }
{{- end}}{{/* if or .UpdateDefaultVersion .IgnoreChangesArguments */}}
{{- if len .GuestAccelerator}}
{{- range $ga := .GuestAccelerator}}
  guest_accelerator {
    type = "{{$ga.Type}}"
    count = {{$ga.Count}}
  }
{{- end}}{{/* range $ga := .GuestAccelerator */}}
{{- end}}{{/* if len .GuestAccelerator */}}
{{- if .Scheduling}}
  scheduling {
{{- if not .Scheduling.AutomaticRestart}}
    automatic_restart = false
{{- end}}{{/* if not .Scheduling.AutomaticRestart */}}
    on_host_maintenance = "{{.Scheduling.OnHostMaintenance}}"
{{- if .Scheduling.Preemptible}}
    preemptible = true
{{- end}}{{/* if .Scheduling.Preemptible */}}
  }
{{- end}}{{/* if .Scheduling */}}
}
