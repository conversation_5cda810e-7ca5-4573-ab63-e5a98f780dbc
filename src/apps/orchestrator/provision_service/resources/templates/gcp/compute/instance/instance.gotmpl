resource "google_compute_instance" "{{.TfResourceName}}" {
{{if .Provider -}}
  provider = {{.Provider}}
{{end}}{{/* if .Provider */ -}}
  name = "{{.Name}}"
  zone = "{{.Zone}}"
  machine_type = "{{.MachineType}}"
  can_ip_forward = true
  allow_stopping_for_update = true
{{if len .Labels -}}
  labels = {
{{range $name, $value := .Labels -}}
    {{$name}} = {{$value}}
{{end}}{{/* range $name, $value := .Labels */ -}}
  }
{{end}}{{/* if len .Labels */ -}}
{{"  "}}boot_disk {
    device_name = "boot"
    auto_delete = true
    initialize_params {
      image = "{{.BootDisk.InitializeParams.Image}}"
{{- if .BootDisk.InitializeParams.Type}}
      type = "{{.BootDisk.InitializeParams.Type}}"
{{- end}}{{/* if .BootDisk.InitializeParams.Type */}}
{{- if .BootDisk.InitializeParams.Size}}
      size = {{.BootDisk.InitializeParams.Size}}
{{- end}}{{/* if .BootDisk.InitializeParams.Size */}}
    }
  }
{{range $ifc := .Interfaces -}}
  network_interface {
    network = "{{$ifc.Network}}"
    subnetwork = "{{$ifc.Subnet}}"
{{if $ifc.StackType -}}
    stack_type = "{{$ifc.StackType}}"
{{end}}{{/* if $ifc.StackType */ -}}
{{if $ifc.AliasIpRanges}}
  {{range $aliasIp := $ifc.AliasIpRanges}}
    alias_ip_range {
      ip_cidr_range = "{{$aliasIp.IpCidrRange}}"
      {{if $aliasIp.SubnetworkRangeName}}
      subnetwork_range_name = "{{$aliasIp.SubnetworkRangeName}}"
      {{end}}
    }
  {{end}}
{{end}}{{/* if $ifc.AliasIpRanges */}}
{{if $ifc.QueueCount -}}
    queue_count = {{$ifc.QueueCount}}
{{end}}{{/* if $ifc.QueueCount */ -}}
{{if $ifc.AccessConfig.NATIP -}}
    access_config {
{{- if $ifc.PublicIPReference}}
      nat_ip = google_compute_address.{{$ifc.PublicIPReference}}.address
{{- else if ne $ifc.AccessConfig.NATIP "any"}}
      nat_ip = "{{$ifc.AccessConfig.NATIP}}"
{{- end}}
  }
{{- end}}{{/* if $ifc.AccessConfig.NATIP */}}
{{- if eq $ifc.StackType "IPV4_IPV6"}}
    ipv6_access_config {
      network_tier = "PREMIUM"
    }
{{- end}}
{{- if $ifc.PrivateIPReference}}
    network_ip = {{$ifc.PrivateIPReference}}
{{- end}}
    }
{{end}}{{/* range $ifc := .Interfaces */ -}}

{{if .Metadata -}}
  metadata = {
{{range $name, $value := .Metadata -}}
    {{$name}} = {{$value}}
{{end}}{{/* range $name, $value := .Metadata */ -}}
  }
{{end}}{{/* if .Metadata */ -}}
{{- if .Credential.Email}}
  service_account {
    email = "{{.Credential.Email}}"
    scopes = ["https://www.googleapis.com/auth/cloud-platform"]
  }
{{- end}}{{/* if .Credential.Email */}}
{{- if len .Tags}}
  tags = [
{{- range $tag := .Tags}}
    "{{$tag}}",
{{- end}}{{/* range $tag := .Tags */}}
  ]
{{- end}}{{/* if len .Tags */}}

{{- if .MinCpuPlatform}}
  min_cpu_platform = "{{.MinCpuPlatform}}"
{{- end}}{{/* if .MinCpuPlatform */}}
{{- if .ShieldInstanceConfig}}
  shielded_instance_config {
    enable_secure_boot = {{.ShieldInstanceConfig.EnableSecureBoot}}
    enable_vtpm = {{.ShieldInstanceConfig.EnableVTPM}}
    enable_integrity_monitoring = {{.ShieldInstanceConfig.EnableIntegrityMonitoring}}
  }
{{- end}}{{/* if .ShieldInstanceConfig */}}

{{- $npcfg := .NetworkPerformanceConfig}}
{{- if $npcfg}}
  network_performance_config {
    total_egress_bandwidth_tier = "{{$npcfg.TotalEgressBandwidthTier}}"
  }
{{- end}}{{/* if $npcfg */}}
{{- if .IgnoreChangesArguments}}
  lifecycle {
    ignore_changes = [
{{- range $arg := .IgnoreChangesArguments}}
      {{$arg}},
{{- end}}
    ]
  }
{{- end}}{{/* if IgnoreChangesArguments */}}
{{- if .DesiredStatus}}
  desired_status = "{{.DesiredStatus}}"
{{- end}}{{/* if DesiredStatus */}}
{{- if len .GuestAccelerator}}
{{- range $ga := .GuestAccelerator}}
  guest_accelerator {
    type = "{{$ga.Type}}"
    count = {{$ga.Count}}
  }
{{- end}}{{/* range $ga := .GuestAccelerator */}}
{{- end}}{{/* if len .GuestAccelerator */}}
{{- if .Scheduling}}
  scheduling {
{{- if not .Scheduling.AutomaticRestart}}
    automatic_restart = false
{{- end}}{{/* if not .Scheduling.AutomaticRestart */}}
    on_host_maintenance = "{{.Scheduling.OnHostMaintenance}}"
{{- if .Scheduling.Preemptible}}
    preemptible = true
{{- end}}{{/* if .Scheduling.Preemptible */}}
  }
{{- end}}{{/* if .Scheduling */}}
}
