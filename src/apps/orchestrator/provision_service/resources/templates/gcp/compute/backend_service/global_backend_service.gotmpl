resource "google_compute_backend_service" "{{.TfResourceName}}" {
  name = "{{.Name}}"
  provider = google-beta
  protocol = "{{.Protocol}}"
  load_balancing_scheme = "{{.LoadBalancingScheme}}"
  session_affinity = "{{.SessionAffinity}}"
  timeout_sec = 86400
  health_checks = [{{range $hc := .HealthChecks}}google_compute_health_check.{{$hc.Name}}.self_link,{{end}}]
  {{- range $b := .Backends}}
  {{- if eq $b.Type "mig"}}
  backend {
    group = google_compute_region_instance_group_manager.{{$b.Name}}.instance_group
    balancing_mode = "{{$b.BalancingMode}}"
    {{- if $b.MaxUtilization}}
    max_utilization = {{$b.MaxUtilization}}
    {{- end}}{{/* $b.MaxUtilization */}}
    {{- if eq $b.BalancingMode "CONNECTION"}}
    max_connections_per_instance = 5000
    {{- end}}{{/* if eq $b.BalancingMode "CONNECTION" */}}

  }
  {{- end}}{{/* if eq $b.Type "mig" */}}
  {{- end}}{{/* range $b := .Backends */}}
  {{- if .DrainingTimeoutSec }}
  connection_draining_timeout_sec = {{.DrainingTimeoutSec}}
  {{- end}}{{/* if .DrainingTimeoutSec */}}
  {{- if .PortName }}
  port_name = "{{.PortName}}"
  {{- end}}{{/* if .PortName */}}
  {{- if .SecurityPolicy }}
  security_policy = data.google_compute_security_policy.{{.TfResourceName}}-sp.self_link
  {{- end}}{{/* if .SecurityPolicy */}}
}

{{- if .SecurityPolicy }}
data "google_compute_security_policy" "{{.TfResourceName}}-sp" {
  name = "{{.SecurityPolicy}}"
}
{{- end}}{{/* if .SecurityPolicy */}}
