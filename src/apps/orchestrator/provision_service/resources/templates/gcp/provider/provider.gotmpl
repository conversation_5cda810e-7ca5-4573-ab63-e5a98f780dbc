terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "{{.ProviderVersion}}"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "{{.ProviderVersion}}"
    }
  }
}
provider "google" {
    credentials = file("{{.CredentialFile}}")
    project = "{{.ProjectID}}"
    region = "{{.Region}}"
    user_project_override = true
    billing_project = "{{.ProjectID}}"
}

provider "google-beta" {
    credentials = file("{{.CredentialFile}}")
    project = "{{.ProjectID}}"
    region = "{{.Region}}"
    user_project_override = true
    billing_project = "{{.ProjectID}}"
}
