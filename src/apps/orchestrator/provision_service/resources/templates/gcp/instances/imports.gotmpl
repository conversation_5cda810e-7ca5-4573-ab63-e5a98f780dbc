{{$provider := .Provider -}}
{{range $zone := .Zones -}}
provider "google" {
    alias = "{{$zone}}"
    credentials = file("credentials.json")
    project = "{{$provider.Project}}"
    region = "{{$provider.Region}}"
    zone = "{{$zone}}"
    user_project_override = true
    billing_project = "{{$provider.Project}}"
}

{{end}}{{/* range $zone := .Zones */ -}}

{{range $clusterId, $clusterVMs := .VMs -}}
{{range $clusterVMs -}}
{{$vm := . -}}

{{range $addrs := $vm.Addrs -}}
{{range $addr := $addrs -}}
import {
  id = "{{$addr.Name}}"
  to = google_compute_address.{{$addr.TFLocalName}}
}

{{end}}{{/* range $addr := $addrs */ -}}
{{end}}{{/* range $addrs := $vm.Addrs */ -}}

import {
  id = "{{$vm.Name}}"
  to = google_compute_instance.{{$vm.TFLocalName}}
  provider = google.{{$vm.Zone}}
}

{{if $vm.TargetInstance -}}
{{$target := $vm.TargetInstance -}}
import {
  id = "{{$target.Name}}"
  to = google_compute_target_instance.{{$target.TFLocalName}}
  provider = google.{{$vm.Zone}}
}

{{end}}{{/* if $vm.TargetInstance */ -}}

{{range $fwdRule := $vm.L3FwdRules -}}
import {
  id = "{{$fwdRule.Name}}"
  to = google_compute_forwarding_rule.{{$fwdRule.TFLocalName}}
}

{{end}}{{/* range $fwdRule := $vm.L3FwdRules */ -}}

{{range $fwRule := $vm.FwRules -}}
import {
  id = "{{$fwRule.Name}}"
  to = google_compute_firewall.{{$fwRule.TFLocalName}}
}

{{end}}{{/* range $fwRule := $vm.FwRules */ -}}
{{end}}{{/* range $clusterVMs */ -}}
{{end}}{{/* range $clusterId, $clusterVMs := .VMs */ -}}

{{$cfg := .DConfig -}}
{{$cleanIP := .DConfig.IsCleanIP -}}
{{range $umig := $cfg.UMIGs -}}
import {
  id = "{{$umig.Zone}}/{{$umig.Name}}"
  to = google_compute_instance_group.{{$umig.Name}}
  provider = google.{{$umig.Zone}}
}

{{end}}{{/* range $umig := $cfg.UMIGs */ -}}

{{range $elb := $cfg.ELBs -}}
import {
  id = "{{$elb.BackendServiceName}}"
  to = google_compute_region_backend_service.{{$elb.BackendServiceName}}
}

{{range $hc := $elb.HealthChecks -}}
import {
  id = "{{$hc.Name}}"
  to = google_compute_region_health_check.{{$hc.Name}}
}

{{end}}{{/* range $hc := $elb.HealthChecks */ -}}

{{range $fwdRule := $elb.IPForwardings -}}
{{if $fwdRule.AddressResourceName -}}
import {
  id = "{{$fwdRule.AddressName}}"
  to = google_compute_address.{{$fwdRule.AddressResourceName}}
}

{{end}}{{/* if $cleanIP */ -}}
import {
  id = "{{$fwdRule.Name}}"
  to = google_compute_forwarding_rule.{{$fwdRule.ResourceName}}
}

{{end}}{{/* range $fwdRule := $elb.IPForwardings */ -}}
{{end}}{{/* range $elb := $cfg.ELBs */ -}}

{{range $att := $cfg.PrivateConnectPeerings -}}
import {
  id = "{{$att.Name}}"
  to = google_compute_interconnect_attachment.{{$att.Name}}
}

{{end}}{{/* range $att := $cfg.PrivateConnectPeerings */ -}}

{{if and $cfg.NetworkServices.Networks (gt (len $cfg.NetworkServices.Networks) 0) -}}
{{range $dpNetworkFwRule := (index $cfg.NetworkServices.Networks 0).FirewallRules -}}
import {
  id = "projects/{{$cfg.ProjectID}}/global/firewalls/{{$dpNetworkFwRule.Name}}"
  to = google_compute_firewall.{{$dpNetworkFwRule.ResourceName}}
}
{{end}}{{/* range $dpNetworkFwRule := index $cfg.NetworkServices.Networks 0 | .FirewallRules */ -}}
{{end}}{{/* if and $cfg.NetworkServices.Networks (gt (len $cfg.NetworkServices.Networks) 0) */ -}}