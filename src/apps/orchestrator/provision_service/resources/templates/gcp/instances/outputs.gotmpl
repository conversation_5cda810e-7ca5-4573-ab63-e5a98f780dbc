{{$dcfg := .DConfig -}}
{{$trRsrc := .DTransformed -}}

{{range $clusterId, $clusterVMs := .VMs -}}
{{range $clusterVMs -}}
{{$vm := . -}}
output "{{$vm.TFLocalName}}" {
  value = {
    type = "vm"
    cluster_id = {{$vm.ClusterId}}
    name = "{{$vm.Name}}"
    id = {{$vm.Id}}
    provisioned_as_primary = {{$vm.IsPrimary}}
    vm_id = google_compute_instance.{{$vm.TFLocalName}}.instance_id
    machine_type = "{{$vm.MachineType}}"
    alias_ip_ranges = google_compute_instance.{{$vm.TFLocalName}}.network_interface[0].alias_ip_range
{{if $vm.MpInstance -}}
{{"    "}}mgmt_ip_address = google_compute_instance.{{$vm.TFLocalName}}.network_interface[0].network_ip
{{else -}}
{{"    "}}mgmt_ip_address = google_compute_instance.{{$vm.TFLocalName}}.network_interface[1].network_ip
    pvt_ip_address = google_compute_instance.{{$vm.TFLocalName}}.network_interface[0].network_ip
{{end}}{{/* if $vm.MpInstance */ -}}
{{if ne $vm.PublicIp "" -}}
{{"    "}}public_ip_address = "{{$vm.PublicIp}}"
{{else if $vm.SetNatIpAsPublicIpOutput -}}
{{"    "}}public_ip_address = google_compute_instance.{{$vm.TFLocalName}}.network_interface[0].access_config[0].nat_ip
{{end}}{{/* if ne $vm.PublicIp "" */ -}}
{{if $vm.IsColoSC -}}
{{"    "}}colo_interface_ip_address = google_compute_instance.{{$vm.TFLocalName}}.network_interface[3].network_ip
{{end}}{{/* if ne $vm.IsColoSC"" */ -}}
{{if $vm.HasExternalIpv6 -}}
{{"    "}}public_ip_v6_address = google_compute_instance.{{$vm.TFLocalName}}.network_interface[0].ipv6_access_config[0].external_ipv6
{{"    "}}public_ip_v6_prefix_len = google_compute_instance.{{$vm.TFLocalName}}.network_interface[0].ipv6_access_config[0].external_ipv6_prefix_length
{{end}}{{/* if $vm.HasExternalIpv6 */ -}}
{{if $vm.HasExternalIpv6Dp2 -}}
{{"    "}}public_ip_v6_address_dp2 = google_compute_instance.{{$vm.TFLocalName}}.network_interface[2].ipv6_access_config[0].external_ipv6
{{"    "}}public_ip_v6_prefix_len_dp2 = google_compute_instance.{{$vm.TFLocalName}}.network_interface[2].ipv6_access_config[0].external_ipv6_prefix_length
{{end}}{{/* if $vm.HasExternalIpv6Dp2 */ -}}
{{if and $vm.IsPrimary $vm.EgressIpv6Subnets -}}
{{"    "}}egress_ip_v6_list = {
{{range $region, $m := $vm.EgressIpv6Subnets -}}
{{range $rule := $m -}}
{{"      "}}"{{$region}}" = google_compute_forwarding_rule.{{$rule.TFLocalName}}.ip_address
{{end}}{{/* range $region, $m := $vm.EgressIpv6Subnets */ -}}
{{end}}{{/* range $rule := $m */ -}}
{{"    "}}}
{{end}}{{/* if $vm.EgressIpv6Subnets */ -}}
{{"  "}}
{{if ne $vm.PublicIpDp2 "" -}}
{{"    "}}public_dp2_ip_address = "{{$vm.PublicIpDp2}}"
{{else if $vm.SetNatIpAsPublicIpDP2Output -}}
{{"    "}}public_dp2_ip_address = google_compute_instance.{{$vm.TFLocalName}}.network_interface[2].access_config[0].nat_ip
{{end}}{{/* if ne $vm.PublicIpDp2 "" */ -}}}
}

{{end}}{{/* range $clusterVMs */ -}}
{{end}}{{/* range $clusterId, $clusterVMs := .VMs */ -}}

{{range $peering := $trRsrc.Peerings -}}
output "{{$peering.Name}}" {
  value = {
    type = "interconnect_attachment"
    name = "{{$peering.Name}}"
    self_link = google_compute_interconnect_attachment.{{$peering.Name}}.self_link
    state = google_compute_interconnect_attachment.{{$peering.Name}}.state
    region = "{{$dcfg.Region}}"
    router = "{{$peering.Router}}"
{{if eq $peering.Type "PARTNER" -}}
{{"    "}}pairing_key = google_compute_interconnect_attachment.{{$peering.Name}}.pairing_key
{{end}}{{/* if eq $peering.Type "PARTNER" */ -}}
{{"  "}}}
}
{{end}}{{/* range $peering := $trRsrc.Peerings */ -}}

{{range $Elb := $trRsrc.Elbs -}}
output "{{$Elb.Name}}" {
  value = {
    type = "Elb"
    name = "{{$Elb.Name}}"
    egress_ip_v6_list = {
{{range $rule:= $Elb.ForwardingRules -}}
{{if eq $rule.IPVersion "IPV6" -}}
{{"      "}}"{{$rule.Name}}" = google_compute_forwarding_rule.{{$rule.Name}}.ip_address
{{end}}{{/* if eq $rule.IPVersion "IPV6"  */ -}}
{{end}}{{/* range $rule:= $Elb.ForwardingRules  */ -}}
{{"    "}}}}
}
{{end}}{{/* range $Elb := $trRsrc.Elbs */ -}}

{{range $Ilb := $trRsrc.Ilbs -}}
output "{{$Ilb.Name}}" {
  value = {
    type = "Ilb"
    name = "{{$Ilb.Name}}"
    lb_ipv4_list = {
{{range $rule:= $Ilb.ForwardingRules -}}
{{if ne $rule.IPVersion "IPV6" -}}
{{"      "}}"{{$rule.Name}}" = google_compute_forwarding_rule.{{$rule.Name}}.ip_address
{{end}}{{/* if ne $rule.IPVersion "IPV6"  */ -}}
{{end}}{{/* range $rule:= $Ilb.ForwardingRules  */ -}}
{{"    "}}}
    lb_ipv6_list = {
{{range $rule:= $Ilb.ForwardingRules -}}
{{if eq $rule.IPVersion "IPV6" -}}
{{"      "}}"{{$rule.Name}}" = google_compute_forwarding_rule.{{$rule.Name}}.ip_address
{{end}}{{/* if eq $rule.IPVersion "IPV6"  */ -}}
{{end}}{{/* range $rule:= $Ilb.ForwardingRules  */ -}}
{{"    "}}}
  }
}
{{end}}{{/* range $Ilb := $trRsrc.Ilbs */ -}}