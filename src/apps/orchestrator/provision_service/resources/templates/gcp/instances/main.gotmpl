provider "google" {
  credentials = file("credentials.json")
  project = "{{.Provider.Project}}"
  user_project_override = true
  billing_project = "{{.Provider.Project}}"
}

{{range $clusterId, $clusterVMs := .VMs -}}
{{range $clusterVMs -}}
{{$vm := . -}}

{{range $addrs := $vm.Addrs -}}
{{range $addr := $addrs -}}
resource "google_compute_address" "{{$addr.TFLocalName}}" {
  name = "{{$addr.Name}}"
{{if $addr.ExplicitAddress -}}
{{/* no need to add quotes as $addr.Address already contains quotes if needed */ -}}
{{"  "}}address =  "{{$addr.Address}}"
{{end}}{{/* if $addr.ExplicitAddress */ -}}
{{"  "}}region = "{{$addr.Region}}"
}

{{end}}{{/* range $addr := $addrs */ -}}
{{end}}{{/* range $addrs := $vm.Addrs */ -}}

resource "google_compute_instance" "{{$vm.TFLocalName}}" {
  name = "{{$vm.Name}}"
  zone = "{{$vm.Zone}}"
  machine_type = "{{$vm.MachineType}}"
  can_ip_forward = true
  allow_stopping_for_update = true
{{if len $vm.NetworkTags -}}
{{"  "}}tags = [{{range $idx, $tag := $vm.NetworkTags}}{{if gt $idx 0}}, {{end}}"{{$tag}}"{{end}}]
{{end}}{{/* if len $vm.NetworkTags */ -}}
{{if len $vm.Labels -}}
{{"  "}}labels = {
{{range $label := $vm.Labels -}}
{{"    "}}{{$label}}
{{end}}{{/* range $label := $vm.Labels */ -}}
{{"  "}}}
{{end}}{{/* if len $vm.Labels */ -}}
{{"  "}}boot_disk {
    device_name = "boot"
    auto_delete = true
    initialize_params {
      image = "{{$vm.ImageUrl}}"
    }
  }
{{range $intf := $vm.NetIntfs -}}
{{"  "}}network_interface {
    network = "{{$intf.NetworkUrl}}"
    subnetwork = "{{$intf.SubnetUrl}}"
{{if $intf.ExplicitQueueCount -}}
{{"    "}}queue_count = {{$intf.QueueCount}}
{{end}}{{/* if $intf.ExplicitQueueCount */ -}}
{{if $intf.ExplicitNatIpOrRef -}}
{{"    "}}access_config {
      nat_ip = {{$intf.NatIpOrRef}}
    }
{{end}}{{/* if $intf.ExplicitNatIpOrRef */ -}}
{{"  "}}}
{{end}}{{/* range $intf := $vm.NetIntfs */ -}}
{{if $vm.Metadata -}}
{{"  "}}metadata = {
{{range $metadata := $vm.Metadata -}}
{{"    "}}{{$metadata}}
{{end}}{{/* range $metadata := $vm.Metadata */ -}}
{{"  "}}}
{{end}}{{/* if $vm.Metadata */ -}}
{{"  "}}service_account {
    email = "{{$vm.ServiceAccount}}"
    scopes = ["https://www.googleapis.com/auth/cloud-platform"]
  }
}

{{if $vm.TargetInstance -}}
{{$target := $vm.TargetInstance -}}
resource "google_compute_target_instance" "{{$target.TFLocalName}}" {
  name = "{{$target.Name}}"
  instance = google_compute_instance.{{$target.Instance.TFLocalName}}.id
  zone = "{{$target.Zone}}"
}

{{end}}{{/* if $vm.TargetInstance */ -}}

{{range $fwdRule := $vm.L3FwdRules -}}
resource "google_compute_forwarding_rule" "{{$fwdRule.TFLocalName}}" {
  name = "{{$fwdRule.Name}}"
  region = "{{$fwdRule.Region}}"
{{/* no need to add quotes as $fwdRule.IpAddressOrRef already contains quotes if needed */ -}}
{{"  "}}ip_address = {{$fwdRule.IpAddressOrRef}}
  ip_protocol = "{{$fwdRule.IpProtocol}}"
  target = google_compute_target_instance.{{$fwdRule.Target.TFLocalName}}.id
{{if $fwdRule.AllPorts -}}
{{"  "}}all_ports = true
{{end}}{{/* if $fwdRule.AllPorts */ -}}
{{if $fwdRule.DependsOn -}}
{{"  "}}depends_on = [google_compute_target_instance.{{$fwdRule.DependsOn.TFLocalName}}]
{{end}}{{/* if $fwdRule.DependsOn */ -}}
}

{{end}}{{/* range $fwdRule := $vm.L3FwdRules */ -}}

{{range $fwRule := $vm.FwRules -}}
resource "google_compute_firewall" "{{$fwRule.TFLocalName}}" {
  name    = "{{$fwRule.Name}}"
  network = "{{$fwRule.NetworkRef}}"
  source_ranges = [{{range $i, $e := $fwRule.SourceRanges}}{{if gt $i 0}}, {{end}}"{{$e}}"{{end}}]
  direction = "INGRESS"
{{range $allow := $fwRule.Allowed -}}
{{"  "}}allow {
    protocol = "{{$allow.IpProtocol}}"
{{if len $allow.Ports -}}
{{"    "}}ports = [{{range $i, $e := $allow.Ports}}{{if gt $i 0}}, {{end}}"{{$e}}"{{end}}]
{{end}}{{/* if len $allow.Ports */ -}}
{{"  "}}}
{{end}}{{/* range $allow := $fwRule.Allowed */ -}}

{{if $fwRule.TargetTags -}}
{{"  "}}target_tags = [{{range $i, $e := $fwRule.TargetTags}}{{if gt $i 0}}, {{end}}"{{$e}}"{{end}}]
{{end}}{{/* if $fwRule.TargetTags */ -}}
{{if $fwRule.Priority -}}
{{"  "}}priority = {{$fwRule.Priority}}
{{end}}{{/* if $fwRule.Priority */ -}}
}

{{end}}{{/* range $fwRule := $vm.FwRules */ -}}
{{end}}{{/* range $clusterVMs */ -}}
{{end}}{{/* range $clusterId, $clusterVMs := .VMs */ -}}
