provider "google" {
  credentials = file("credentials.json")
  project     = "{{.Provider.Project}}"
  user_project_override = true
  billing_project = "{{.Provider.Project}}"
}

{{range $net := .Networks -}}
resource "google_compute_network" "{{$net.TFLocalName}}" {
  name                    = "{{$net.Name}}"
  auto_create_subnetworks = false
  routing_mode            = "REGIONAL"
}

{{range $subnet := $net.Subnets -}}
resource "google_compute_subnetwork" "{{$subnet.TFLocalName}}" {
  name                     = "{{$subnet.Name}}"
  ip_cidr_range            = "{{$subnet.IpRange}}"
  region                   = "{{$subnet.Region}}"
  private_ip_google_access = {{$subnet.PrivateIpProviderAccess}}

{{if $subnet.StackType -}}
{{"  "}}stack_type               = "{{$subnet.StackType}}"
{{end}}{{/* if subnet.StackType */ -}}

{{if $subnet.Ipv6AccessType -}}
{{"  "}}ipv6_access_type         = "{{$subnet.Ipv6AccessType}}"
{{end}}{{/* if subnet.Ipv6AccessType */ -}}

{{if $subnet.ExternalIpv6Prefix -}}
{{"  "}}external_ipv6_prefix     = "{{$subnet.ExternalIpv6Prefix}}"
{{end}}{{/* if $subnet.ExternalIpv6Prefix */ -}}

{{if $subnet.SecondaryIpRanges -}}
{{range $secondaryIpRange := $subnet.SecondaryIpRanges -}}
  secondary_ip_range {
    range_name    = "{{$secondaryIpRange.RangeName}}"
    ip_cidr_range = "{{$secondaryIpRange.IpCidrRange}}"
  }
{{end}}{{/* range $secondaryIpRange := $subnet.SecondaryIpRanges */ -}}
{{end}}{{/* if $subnet.SecondaryIpRanges */ -}}

{{"  "}}network                  = google_compute_network.{{$net.TFLocalName}}.id
}

{{end}}{{/* range $subnet := $net.Subnets */ -}}

{{range $fwRule := $net.FirewallRules -}}
resource "google_compute_firewall" "{{$fwRule.TFLocalName}}" {
  name    = "{{$fwRule.Name}}"
  network = google_compute_network.{{$net.TFLocalName}}.name
  source_ranges = [{{range $i, $e := $fwRule.SourceRanges}}{{if gt $i 0}}, {{end}}"{{$e}}"{{end}}]
  direction = "INGRESS"
{{if $fwRule.Allowed -}}
{{range $allow := $fwRule.Allowed -}}
{{"  "}}allow {
    protocol = "{{$allow.IpProtocol}}"
{{if len $allow.Ports -}}
{{"    "}}ports = [{{range $i, $e := $allow.Ports}}{{if gt $i 0}}, {{end}}"{{$e}}"{{end}}]
{{end}}{{/* if len $allow.Ports */ -}}
{{"  "}}}
{{end}}{{/* range $allow := $fwRule.Allowed */ -}}

{{else}}{{/* if $fwRule.Allowed */ -}}
{{range $deny := $fwRule.Denied -}}
{{"  "}}deny {
    protocol = "{{$deny.IpProtocol}}"
{{if len $deny.Ports -}}
{{"    "}}ports = [{{range $i, $e := $deny.Ports}}{{if gt $i 0}}, {{end}}"{{$e}}"{{end}}]
{{end}}{{/* if len $deny.Ports */ -}}
{{"  "}}}
{{end}}{{/* range $deny := $fwRule.Denied */ -}}
{{end}}{{/* if $fwRule.Allowed */ -}}

{{if $fwRule.TargetTags -}}
{{"  "}}target_tags = [{{range $i, $e := $fwRule.TargetTags}}{{if gt $i 0}}, {{end}}"{{$e}}"{{end}}]
{{end}}{{/* if $fwRule.TargetTags */ -}}
{{if $fwRule.Priority -}}
{{"  "}}priority = {{$fwRule.Priority}}
{{end}}{{/* if $fwRule.Priority */ -}}
}

{{end}}{{/* range $fwRule := $net.FirewallRules */ -}}
{{end}}{{/* range $net := .Networks */ -}}