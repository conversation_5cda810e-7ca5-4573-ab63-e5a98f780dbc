resource "google_compute_router_nat" "{{.TfResourceName}}" {
  name   = "{{.Name}}"
  router = "{{.Router}}"
  region = "{{.Region}}"
  enable_dynamic_port_allocation = {{.DynamicPortAllocation}}
  enable_endpoint_independent_mapping = {{.EndpointIndependentMapping}}
  min_ports_per_vm = {{.MinPortsPerVm}}
  max_ports_per_vm = {{.MaxPortsPerVm}}
  nat_ip_allocate_option = "{{.NatIpAllocateOption}}"
{{- if len .EndpointTypes}}
  endpoint_types = [
{{- range $et := .EndpointTypes}}
    "{{$et}}",
{{- end}}{{/* range $et := .EndpointTypes */}}
  ]
{{- end}}{{/* if len .EndpointTypes */}}
{{- if len .NatIpAddrs}}
  nat_ips = [
{{- range $nips := .NatIpAddrs}}
    google_compute_address.{{$nips.TfResourceName}}.self_link,
{{- end}}{{/* range $nips := .NatIpAddrs */}}
  ]
{{- end}}{{/* if len .NatIpAddrs */}}
{{- if len .NatIpNames}}
  nat_ips = [
{{- range $nips := .NatIpNames}}
    "{{$nips}}",
{{- end}}{{/* range $nips := .NatIpNames */}}
  ]
{{- end}}{{/* if len .NatIpNames */}}
  source_subnetwork_ip_ranges_to_nat = "{{.SourceSubnetworkIpRangesToNat}}"
{{- if .LogConfig}}
  log_config {
    enable = {{.LogConfig.Enable}}
    filter = "{{.LogConfig.Filter}}"
  }
{{- end}} {{/* if .LogConfig */}}
{{- range $np := .SubNetworks}}
  subnetwork {
    name = "{{$np.Name}}"
{{- if len .SourceIpRangesToNat}}
    source_ip_ranges_to_nat = [
{{- range $sirtn := .SourceIpRangesToNat}}
      "{{$sirtn}}",
{{- end}}{{/* range $sirtn := .SourceIpRangesToNat */}}
    ]
{{- end}}{{/* if len .SourceIpRangesToNat */}}
{{- if len .SecondaryIpRangeNames}}
    secondary_ip_range_names = [
{{- range $sirn := .SecondaryIpRangeNames}}
      "{{$sirn}}",
{{- end}}{{/* range $sirn := .SecondaryIpRangeNames */}}
    ]
{{- end}}{{/* if len .SecondaryIpRangeNames */}}
  }
{{- end}}{{/* range $np := .SubNetworks */}}
}
