resource "google_compute_interconnect_attachment" "{{.TfResourceName}}" {
  name = "{{.Name}}"
  router = "{{.Router}}"
  admin_enabled = {{.AdminEnabled}}
{{- if .Interconnect}}
  interconnect = "{{.Interconnect}}"
{{- end}}
{{- if .Mtu}}
  mtu = {{.Mtu}}
{{- end}}
{{- if .Bandwidth}}
  bandwidth = "{{.Bandwidth}}"
{{- end}}
{{- if .EdgeAvailabilityDomain}}
  edge_availability_domain = "{{.EdgeAvailabilityDomain}}"
{{- end}}
{{- if .Type}}
  type = "{{.Type}}"
{{- end}}
{{- if .VlanTag8021q}}
  vlan_tag8021q = {{.VlanTag8021q}}
{{- end}}
  ipsec_internal_addresses = []
}
