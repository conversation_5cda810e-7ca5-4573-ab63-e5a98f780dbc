resource "google_compute_global_forwarding_rule" "{{.TfResourceName}}" {
  name = "{{.Name}}"
  provider = google-beta
  ip_protocol = "{{.Protocol}}"
  load_balancing_scheme = "{{.LoadBalancingScheme}}"
  target = {{.TargetType}}.{{.Target}}.self_link
{{- if .PortRange}}
{{"  "}}port_range = "{{.PortRange}}"
{{- end}}{{/* if .PortRange*/}}
{{- if .IPAddressReference}}
{{"  "}}ip_address = google_compute_global_address.{{.IPAddressReference}}.id
{{- else if and (.IPAddress) (ne .IPAddress "any")}}
{{"  "}}ip_address = "{{.IPAddress}}"
{{- end}}
{{- if .DependsOn}}
{{"  "}}depends_on = [{{.DependsOnType}}.{{.DependsOn}}]
{{- end}}{{/* - if .DependsOn */}}
{{- if .Network}}
  network =  "{{.Network}}"
{{- end}}{{/* - if .Network */}}
{{- if .Subnet}}
  subnetwork =  "{{.Subnet}}"
{{- end}}{{/* - if .Subnet */}}
{{- if .IPVersion}}
  ip_version =  "{{.IPVersion}}"
{{- end}}{{/* - if .IPVersion */}}
{{- if .IgnoreChangesArguments}}
  lifecycle {
    ignore_changes = [
{{- range $arg := .IgnoreChangesArguments}}
      {{$arg}},
{{- end}}
    ]
  }
{{- end}}{{/* if IgnoreChangesArguments */}}
}
