resource google_compute_firewall "{{.TfResourceName}}" {
    name = "{{.Name}}"
    network = "{{.Network}}"
{{- if .Project}}
    project = "{{.Project}}"
{{- end}}{{/* if .Project */}}
{{- if .Direction}}
    direction = "{{.Direction}}"
{{- end}}{{/* if .Direction */}}
{{- if len .SourceRanges}}
    source_ranges = [
{{- range $srcRange := .SourceRanges}}
        "{{$srcRange}}",
{{- end}}{{/* range $srcRange := .SourceRanges */}}
    ]
{{- end}}{{/* if .SourceRanges */}}
{{- if len .DestinationRanges}}
    destination_ranges = [
{{- range $dstRange := .DestinationRanges}}
        "{{$dstRange}}",
{{- end}}{{/* range $dstRange := .DestinationRanges */}}
    ]
{{- end}}{{/* if .DestinationRanges */}}
{{- if len .TargetTags}}
    target_tags = [
{{- range $tgt := .TargetTags}}
        "{{$tgt}}",
{{- end}}{{/* range $tgt := .TargetTags */}}
    ]
{{- end}}{{/* if .TargetTags */}}
{{- range $pp := .Allow}}
    allow {
{{- if $pp.Protocol }}
        protocol = "{{$pp.Protocol}}"
{{- end}}{{/* if $pp.Protocol */}}
{{- if len $pp.Ports}}
        ports = [
{{- range $port := $pp.Ports}}
            "{{$port}}",
{{- end}}{{/* range $ports := $pp.Ports */}}
        ]
{{- end}}{{/* if len $pp.Ports */}}
    }
{{- end}}{{/* range $pp := .Allow */}}
{{- range $pp := .Deny}}
    deny {
{{- if $pp.Protocol }}
        protocol = "{{$pp.Protocol}}"
{{- end}}
{{- if len $pp.Ports}}
        ports = [
{{- range $port := $pp.Ports}}
            "{{$port}}",
{{- end}}{{/* range $ports := $pp.Ports */}}
        ]
{{- end}}{{/* if len $pp.Ports */}}
    }
{{- end}}{{/* range $pp := .Deny */}}
{{- if .Disabled}}
    disabled = {{.Disabled}}
{{- end}}{{/* if .Disabled */}}
}
