resource "google_compute_forwarding_rule" "{{.TfResourceName}}" {
  name = "{{.Name}}"
  provider = google-beta
  region = "{{.Region}}"
  ip_protocol = "{{.Protocol}}"
  load_balancing_scheme = "{{.LoadBalancingScheme}}"
  all_ports = {{.AllPorts}}
{{- if eq .TargetType "google_compute_target_instance"}}
{{"  "}}target = {{.TargetType}}.{{.Target}}.self_link
{{- else}}
{{"  "}}backend_service = {{.TargetType}}.{{.BackendService}}.self_link
{{- end}}{{/* TargetType is backend service */}}
{{- if .PortRange}}
{{"  "}}port_range = "{{.PortRange}}"
{{- end}}
{{- if len .Ports}}
{{"  "}}ports = [
{{- range $port := .Ports}}
{{"    "}}"{{$port}}",
{{- end}}{{/* range $port := .Ports */}}
{{"  "}}]
{{- end}}{{/* if len .Ports */}}
{{- if .IPAddressReference}}
{{"  "}}ip_address = google_compute_address.{{.IPAddressReference}}.address
{{- else if and (.IPAddress) (ne .IPAddress "any")}}
{{"  "}}ip_address = "{{.IPAddress}}"
{{- end}}
{{- if .DependsOn}}
{{"  "}}depends_on = [{{.DependsOnType}}.{{.DependsOn}}]
{{- end}}{{/* - if .DependsOn */}}
{{- if .Network}}
  network =  "{{.Network}}"
{{- end}}{{/* - if .Network */}}
{{- if .Subnet}}
  subnetwork =  "{{.Subnet}}"
{{- end}}{{/* - if .Subnet */}}
{{- if .IPVersion}}
  ip_version =  "{{.IPVersion}}"
{{- end}}{{/* - if .IPVersion */}}
{{- if .IgnoreChangesArguments}}
  lifecycle {
    ignore_changes = [
{{- range $arg := .IgnoreChangesArguments}}
      {{$arg}},
{{- end}}
    ]
  }
{{- end}}{{/* if IgnoreChangesArguments */}}
}
