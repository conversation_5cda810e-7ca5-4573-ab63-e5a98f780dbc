resource "google_compute_subnetwork" "{{.Name}}" {
  name = "{{.Name}}"
  project = "{{.Project}}"
  region = "{{.Region}}"
  network = {{.Network}}
{{- if .IPCIDRRange}}
  ip_cidr_range = "{{.IPCIDRRange}}"
{{- end}}{{/* if .IPCIDRRange */}}
{{- if .PrivateIPGoogleAccess}}
  private_ip_google_access = true
{{- end}}
{{- range $ar := .SecondaryIPRanges}}
  secondary_ip_range {
    range_name = "{{$ar.RangeName}}"
    ip_cidr_range = "{{$ar.CIDRRange}}"
  }
{{- end}}
{{- if .StackType}}
  stack_type = "{{.StackType}}"
{{- end}}{{/* if .StackType */}}
{{- if .IPv6AccessType}}
  ipv6_access_type = "{{.IPv6AccessType}}"
{{- end}}{{/* if .IPv6AccessType */}}
}
