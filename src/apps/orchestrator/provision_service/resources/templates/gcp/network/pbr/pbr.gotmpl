resource "google_network_connectivity_policy_based_route" "{{.Name}}" {
  name = "{{.Name}}"
  network = {{.Network}}
{{- if .Priority}}
  priority = {{.Priority}}
{{- end}}{{/* if .Priority*/}}
  filter {
    protocol_version = "IPV4"
    ip_protocol = "{{.Filter.IPProtocol}}"
    src_range = "{{.Filter.SrcRange}}"
    dest_range = "{{.Filter.DestRange}}"
  }
  next_hop_ilb_ip = {{.NextHopILBIP}}
{{- if .VirtualMachine}}
  virtual_machine {
    tags = [
{{- range $tag := .VirtualMachine.Tags}}
      "{{$tag}}",
{{- end}}{{/* range $tag := .VirtualMachine.Tags*/}}
    ]
  }
{{- end}}{{/* if .VirtualMachine*/}}


{{- if .InterconnectAttachment}}
  interconnect_attachment {
    region = "{{.InterconnectAttachment.Region}}"
  }
{{- end}}{{/* if .InterconnectAttachment*/}}

}