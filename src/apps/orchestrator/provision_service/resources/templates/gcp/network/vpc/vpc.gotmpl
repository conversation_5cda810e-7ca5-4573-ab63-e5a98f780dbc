resource "google_compute_network" "{{.Name}}" {
  name = "{{.Name}}"
  project = "{{.Project}}"
{{- if gt .MTU 0}}
  mtu = {{.MTU}}
{{- end}}{{/* if .MTU > 0 */}}
{{- if .AutoCreateSubnets}}
  auto_create_subnetworks = true
{{- else}}
  auto_create_subnetworks = false
{{- end}}{{/* if .AutoCreateSubnets */}}
{{- if .RoutingMode}}
  routing_mode = "{{.RoutingMode}}"
{{- end}}{{/* if .RoutingMode */}}
{{- if .InternalIPv6Range}}
  internal_ipv6_range = "{{.InternalIPv6Range}}"
{{- end}}{{/* if .InternalIPv6Range */}}
}
