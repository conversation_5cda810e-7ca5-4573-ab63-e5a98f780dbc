data "openstack_identity_project_v3" parent_proj {
    name = "{{.HostProjectId}}"
}

resource "openstack_identity_project_v3" "{{.TFLocalName}}" {
  name        = "{{.Id}}"
  parent_id   = data.openstack_identity_project_v3.parent_proj.id
{{if len .Tags -}}
{{"  "}}tags = [
    {{range $idx, $val := .Tags -}}
    {{"    "}}"{{$val}}",
    {{end}}{{/* range $val := .Tags */ -}}
    {{"  "}}]
{{end}}{{/* if len .Tags */ -}}
}

output "project_id" {
    value = openstack_identity_project_v3.{{.TFLocalName}}.id
}
