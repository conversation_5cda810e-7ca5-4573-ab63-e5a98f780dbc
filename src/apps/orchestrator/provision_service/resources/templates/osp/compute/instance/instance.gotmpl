resource "openstack_compute_instance_v2" "{{.Name}}" {
  name = "{{.Name}}"
  flavor_name = "{{.MachineType}}"
  security_groups   = ["default"]
  key_pair = "{{.KeyPair}}"
  image_id = "{{.BootDisk.InitializeParams.Image}}"
{{if len .Labels -}}
{{"  "}}labels = {
{{range $name, $value := .Labels -}}
{{"    "}}{{$name}} = "{{$value}}"
{{end}}{{/* range $name, $value := .Labels */ -}}
{{"  "}}}
{{end}}{{/* if len .Labels */ -}}
{{range $ifc := .Interfaces -}}
{{"  "}}network {
    name = "{{$ifc.Network}}"
{{if $ifc.QueueCount -}}
{{"    "}}queue_count = {{$ifc.QueueCount}}
{{end}}{{/* if $ifc.QueueCount */ -}}
{{"  "}}}
{{end}}{{/* range $ifc := .Interfaces */ -}}
{{if .Metadata -}}
{{"  "}}metadata = {
{{range $name, $value := .Metadata -}}
{{"    "}}{{$name}} = {{$value}}
{{end}}{{/* range $name, $value := .Metadata */ -}}
{{"  "}}}
{{end}}{{/* if .Metadata */ -}}
}
{{if .Name -}}
{{$A := .Name}}
{{range $ifc := .Interfaces -}}
{{if $ifc.AccessConfig.NATIP -}}
resource "openstack_networking_floatingip_v2" "{{$ifc.PublicIPReference}}" {
  pool = "external"
}
resource "openstack_compute_floatingip_associate_v2" "{{$ifc.PublicIPReference}}" {
  floating_ip = "${openstack_networking_floatingip_v2.{{$ifc.PublicIPReference}}.address}"
  instance_id = "${openstack_compute_instance_v2.{{$A}}.id}"
  fixed_ip    = "${openstack_compute_instance_v2.{{$A}}.network.1.fixed_ip_v4}"
}
{{end}}{{/* if $ifc.AccessConfig.NATIP */ -}}
{{"  "}}
{{end}}{{/* range $ifc := .Interfaces */ -}}
{{end}}{{/* if .Name */ -}}
