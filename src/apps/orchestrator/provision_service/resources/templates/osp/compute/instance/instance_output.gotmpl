{{- $VMname := .Name -}}
output "{{$VMname}}" {
  value = {
    type = "vm"
    name = "{{$VMname}}"
    vm_id = openstack_compute_instance_v2.{{$VMname}}.id
{{- if len .Labels}}
    labels = {
{{- range $name, $value := .Labels}}
      {{$name}} = "{{$value}}"
{{- end}}{{/* range $name, $value := .Labels */}}
    }
{{- end}}{{/* if len/Labels */}}
{{- if len .Interfaces }}
    interfaces = [
{{- range $id, $ifc := .Interfaces}}
      {
        name = "{{$ifc.InterfaceName}}"
        pvt_ip = openstack_compute_instance_v2.{{$VMname}}.network.{{$id}}.fixed_ip_v4
{{- if $ifc.AccessConfig.NATIP}}
        public_ip = openstack_networking_floatingip_v2.{{$ifc.PublicIPReference}}.address
{{- end}}{{/* if $ifc.AccessConfig.NATIP */}}
      },
{{- end}}{{/* range $ifc := .Interfaces */}}
    ]
{{- end}}{{/* if len .Interfaces */}}
  }
}
