{{range $net := .}}
{{$extNet := $net.ExternalNetwork}}
resource "openstack_networking_network_v2" "{{$net.TFLocalName}}" {
  name = "{{$net.Name}}"
  admin_state_up = "true"
}

{{if $extNet}}
data "openstack_networking_network_v2" "{{$extNet}}" {
  name = "{{$extNet}}"
}
{{end}}

resource "openstack_networking_router_v2" "{{$net.Router}}" {
  name = "{{$net.Router}}"
  admin_state_up = true
{{if $extNet}}
  external_network_id = data.openstack_networking_network_v2.{{$extNet}}.id
  enable_snat = false
{{end}}
}

{{range $subnet := $net.Subnets}}
resource "openstack_networking_subnet_v2" "{{$subnet.TFLocalName}}" {
  name = "{{$subnet.Name}}"
  network_id = openstack_networking_network_v2.{{$net.TFLocalName}}.id
  cidr = "{{$subnet.IpRange}}"
{{if $subnet.NameServers}}
  dns_nameservers = [
{{range $ns := $subnet.NameServers -}}
	"{{$ns}}",
{{end}}
  ] 
{{end}}
}

resource "openstack_networking_router_interface_v2" "{{$subnet.TFLocalName}}_intf" {
  router_id = openstack_networking_router_v2.{{$net.Router}}.id
  subnet_id = openstack_networking_subnet_v2.{{$subnet.TFLocalName}}.id
}
{{end}}{{/* range $subnet */}}

{{end}}{{/* range $net */}}
