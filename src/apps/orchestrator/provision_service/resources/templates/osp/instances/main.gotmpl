{{- $MGMT_SHARED_SECGROUP := "mgmt-shared-security-group"}} {{- /* predefined in openstack */}}
{{- $DP_SHARED_SECGROUP   :=   "dp-shared-security-group"}} {{- /* predefined in openstack */}}
{{- $HA_SHARED_SECGROUP   :=   "ha-shared-security-group"}} {{- /* predefined in openstack */}}

{{- $PROJ_NAME := .Project}}
{{- $INST_NAME := .Name}}
{{- $SECGROUP_NAME := (printf "%s-secgroup" $INST_NAME)}}
{{- $NETWORK_NAME := (printf "%s-network" $INST_NAME)}}
{{- $PORT_NAME := (printf "%s-port" $INST_NAME)}}
{{- $hasHA := true }}
{{- if le (len .Interfaces) 2}}
  {{- $hasHA = false}}
{{- end -}} {{- /* if le (len .Interfaces) 2 */ -}}

{{- /* tenant project is predefined in openstack */}}
data "openstack_identity_project_v3" "{{$INST_NAME}}" {
  name = "{{$PROJ_NAME}}"
}

{{$PARENT_PROJ_ID_STR := (printf "${data.openstack_identity_project_v3.%s.parent_id}" $INST_NAME)}}

{{- $network_name := ""}}
{{- /* tenant networks are predefined in openstack */}}
{{range $i, $ifc := .Interfaces}}
  {{- $network_name = (printf "%s-network-%d" $INST_NAME $i)}}
  data "openstack_networking_network_v2" "{{$network_name}}" {
    name = "{{$ifc.Network}}"
  }
{{- end}} {{/* range $ifc := .Interfaces */}}

{{$mgmt_secgroup_name := (printf "mgmt-%s" $SECGROUP_NAME)}}
{{- /* shared security groups are predefined in openstack */}}
data "openstack_networking_secgroup_v2" "{{$mgmt_secgroup_name}}" {
  name      = "{{$MGMT_SHARED_SECGROUP}}"
  tenant_id = "{{$PARENT_PROJ_ID_STR}}"
}

{{$mgmt_port_name    := (printf "%s-port-0" $INST_NAME)}}
{{- $mgmt_network_name := (printf "%s-network-0" $INST_NAME)}}
resource "openstack_networking_port_v2" {{$mgmt_port_name}} {
  name               = "{{$mgmt_port_name}}"
  network_id         = "${data.openstack_networking_network_v2.{{$mgmt_network_name}}.id}"
  security_group_ids = ["${data.openstack_networking_secgroup_v2.{{$mgmt_secgroup_name}}.id}"]
}

{{$dp_secgroup_name := (printf "dp-%s" $SECGROUP_NAME)}}
data "openstack_networking_secgroup_v2" "{{$dp_secgroup_name}}" {
  name = "{{$DP_SHARED_SECGROUP}}"
  tenant_id = "{{$PARENT_PROJ_ID_STR}}"
}

{{$dp_port_name    := (printf "%s-port-1" $INST_NAME)}}
{{- $dp_network_name := (printf "%s-network-1" $INST_NAME)}}
resource "openstack_networking_port_v2" {{$dp_port_name}} {
  name               = "{{$dp_port_name}}"
  network_id         = "${data.openstack_networking_network_v2.{{$dp_network_name}}.id}"
  security_group_ids = ["${data.openstack_networking_secgroup_v2.{{$dp_secgroup_name}}.id}"]
}

{{if $hasHA}}
  {{- $ha_secgroup_name := (printf "ha-%s" $SECGROUP_NAME)}}
  data "openstack_networking_secgroup_v2" "{{$ha_secgroup_name}}" {
    name = "{{$HA_SHARED_SECGROUP}}"
    tenant_id = "{{$PARENT_PROJ_ID_STR}}"
  }

  {{- $ha_port_name    := (printf "%s-port-2" $INST_NAME)}}
  {{$ha_network_name := (printf "%s-network-2" $INST_NAME)}}
  resource "openstack_networking_port_v2" {{$ha_port_name}} {
    name               = "{{$ha_port_name}}"
    network_id         = "${data.openstack_networking_network_v2.{{$ha_network_name}}.id}"
    security_group_ids = ["${data.openstack_networking_secgroup_v2.{{$ha_secgroup_name}}.id}"]
  }
{{- end}}{{/* if hasHA */}}

{{""}}

resource "openstack_compute_instance_v2" "{{$INST_NAME}}" {
  {{$pubip := "" -}}
  {{$anyaddr := false -}}
  {{if len .IPAddresses -}}
    {{$addrinfo := index .IPAddresses 0}}
    {{$pubip = $addrinfo.Address}}
    {{$anyaddr = $addrinfo.AnyAddress}}
  {{end}}{{/* if len .IPAdresses */ -}}

  name = "{{.Name}}"
  flavor_name = "{{.MachineType}}"
  availability_zone = "{{.Zone}}"
  security_groups  = ["default"]
  key_pair = "{{.KeyPair}}"
  image_id = "{{.BootDisk.InitializeParams.Image}}"

  {{if len .Labels -}}
  tags = [
  {{range $name, $value := .Labels -}}
    "{{$name}} = {{$value}}",
  {{end}}{{/* range $name, $value := .Labels */ -}}
  ]
  {{end}}{{/* if len .Labels */ -}}

  {{""}}
  {{$port_name := ""}}
  {{range $i, $ifc := .Interfaces -}}
    {{$port_name = (printf "%s-port-%d" $INST_NAME $i)}}
    network {
      port = "${openstack_networking_port_v2.{{$port_name}}.id}"
  }
  {{end}}{{/* range $ifc := .Interfaces */ -}}

  {{if .Metadata -}}
  {{""}}
  metadata = {
    {{range $name, $value := .Metadata -}}
    {{$name}} = {{$value}}
    {{end}}{{/* range $name, $value := .Metadata */ -}}
  }
  {{end}}{{/* if .Metadata */ -}}
{{- if .PowerState}}
  power_state = "{{.PowerState}}"
{{- end}}{{/* if PowerState */}}
}

{{if .Name -}}
{{$A := .Name}}
{{range $ifc := .Interfaces -}}
{{if $ifc.AccessConfig.NATIP -}}
resource "openstack_networking_floatingip_v2" "{{$ifc.PublicIPReference}}" {
  pool = "external"
  description = "{{$PROJ_NAME}}::{{$INST_NAME}}"
  {{if not $anyaddr}}
    address = "{{$pubip}}"
  {{end}}{{/* if $anyaddr */}}
}

  {{if not $hasHA -}}
    resource "openstack_compute_floatingip_associate_v2" "{{$ifc.PublicIPReference}}" {
      floating_ip = "${openstack_networking_floatingip_v2.{{$ifc.PublicIPReference}}.address}"
      instance_id = "${openstack_compute_instance_v2.{{$A}}.id}"
      fixed_ip    = "${openstack_compute_instance_v2.{{$A}}.network.1.fixed_ip_v4}"
    }
  {{end}}{{/* if not hasHA */ -}}

{{end}}{{/* if $ifc.AccessConfig.NATIP */ -}}
{{end}}{{/* range $ifc := .Interfaces */ -}}
{{end}}{{/* if .Name */ -}}
{{""}}
{{""}}
