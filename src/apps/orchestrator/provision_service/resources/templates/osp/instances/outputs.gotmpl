{{range $clusterId, $clusterVMs := .VMs -}}
{{range $clusterVMs -}}
{{$vm := . -}}
output "{{$vm.Name}}" {
  value = {
    type = "vm"
    cluster_id = {{$vm.ClusterId}}
    name = "{{$vm.Name}}"
    id = {{$vm.Id}}
    provisioned_as_primary = {{$vm.IsPrimary}}
    vm_id = openstack_compute_instance_v2.{{$vm.Name}}.id
    mgmt_ip_address = openstack_compute_instance_v2.{{$vm.Name}}.network.0.fixed_ip_v4
    pvt_ip_address = openstack_compute_instance_v2.{{$vm.Name}}.network.1.fixed_ip_v4
    {{if $vm.HACluster -}}
      ha_ip_address = openstack_compute_instance_v2.{{$vm.Name}}.network.2.fixed_ip_v4
    {{end}} {{/* $vm.VMCluster */ -}}
    {{if $vm.IsPrimary -}}
      public_ip_address = openstack_networking_floatingip_v2.public-ip-{{$vm.Name}}.address
    {{end}} {{/* $vm.IsPrimary */ -}}
  }
}
{{end}}{{/* range $clusterVMs */ -}}
{{end}}{{/* range $clusterId, $clusterVMs := .VMs */ -}}
