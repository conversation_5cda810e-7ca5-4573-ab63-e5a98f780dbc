package osp

import (
	"fmt"
	"io"
	"path"
	"text/template"

	"go.panw.local/pangolin/clogger"
)

func PrepareProviderParams(tforg string, eLog *clogger.EventLogger) (*Provider, error) {
	provider := &Provider{}
	provider.TfOrg = tforg

	return provider, nil
}

// Generate Provider TFFile generates the OPENSTACK Provider TF template
func GenerateProviderTF(p *Provider, tplDir string, f io.Writer) error {
	tplFile := fmt.Sprintf("%s/provider.gotmpl", tplDir)

	name := path.Base(tplFile)
	provTpl, err := template.New(name).ParseFiles(tplFile)
	if err != nil {
		fmt.Println("Openstack Provider gotmpl Parse Error")
		return err
	}

	if err := provTpl.Execute(f, p); err != nil {
		return err
	}
	return nil
}

// Generate Openstack Credential TFFile
func GenerateCredentialTF(cred *Credentials, tplDir string, f io.Writer) error {
	tplFile := fmt.Sprintf("%s/openstack.gotmpl", tplDir)

	name := path.Base(tplFile)

	credTpl, err := template.New(name).ParseFiles(tplFile)
	if err != nil {
		fmt.Println("Openstack credentials gotmpl Parse Error")
		return err
	}

	if err := credTpl.Execute(f, cred); err != nil {
		return err
	}
	return nil
}
