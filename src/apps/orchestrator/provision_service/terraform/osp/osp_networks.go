package osp

import (
	"orchestrator/provision_service/definitions"
	pterraform "orchestrator/provision_service/terraform"

	"go.panw.local/pangolin/clogger"
)

type Subnet struct {
	definitions.SubnetProps
	TFLocalName string
}

type Network struct {
	Name            string
	ExternalNetwork string
	Subnets         []*Subnet
	Router          string
	TFLocalName     string
}

func PrepareNetworks(project string, networkProps []*definitions.NetworkProps,
	eLog *clogger.EventLogger) ([]*Network, error) {
	var networks []*Network

	for _, nProps := range networkProps {
		eLog.LogInfo("Updating openstack Network config for %v", nProps.Name)
		network := &Network{
			Name:            nProps.Name,
			ExternalNetwork: nProps.ExternalNetwork,
		}
		network.Router = network.Name + "_router"
		network.TFLocalName = pterraform.TFLocalName(network.Name)

		for _, sProps := range nProps.Subnets {
			network.Subnets = append(network.Subnets, prepareSubnet(project, sProps))
		}

		networks = append(networks, network)
	}

	return networks, nil
}

func prepareSubnet(project string, subnetProps *definitions.SubnetProps) *Subnet {
	subnet := &Subnet{SubnetProps: *subnetProps}
	subnet.TFLocalName = pterraform.TFLocalName(subnet.Name)
	return subnet
}
