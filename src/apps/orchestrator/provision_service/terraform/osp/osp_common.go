package osp

import (
	"encoding/json"
	"orchestrator/provision_service/infra/tf"
	"os"
	"path/filepath"

	"orchestrator/libs/go/terraform"

	"go.panw.local/pangolin/clogger"
)

const (
	CredentialFileName = "openstack.tf"
	OspDirName         = "osp"
)

type UserPassword struct {
	UserName string `json:"user_name"`
	Password string `json:"password`
}

func SetupProviderTf(tfWrapper tf.TfWrapper, workspaceDir string,
	eLog *clogger.EventLogger) (*Provider, error) {
	// prepare terraform provider configuration parameters
	tforg := tfWrapper.GetOrganization()
	provider, err := PrepareProviderParams(tforg, eLog)
	if err != nil {
		eLog.LogError("Error preparing provider params: %v", err)
		return provider, err
	}
	pf, err := os.Create(filepath.Join(workspaceDir, "provider.tf"))
	if err != nil {
		eLog.LogError("Error creating destination provider configuration file: %v", err)
		return provider, err
	}
	pTmplDir := filepath.Join(tfWrapper.GetTemplateDir(), OspDirName, "provider")
	GenerateProviderTF(provider, pTmplDir, pf)
	return provider, nil
}

func SetupCredentials(tfWrapper tf.TfWrapper, workspaceDir, projectId string,
	eLog *clogger.EventLogger) error {

	var usrpass UserPassword

	pf, err := os.Create(filepath.Join(workspaceDir, "openstack.tf"))
	if err != nil {
		eLog.LogError("Error creating destination openstack credential file: %v", err)
		return err
	}
	pTmplDir := filepath.Join(tfWrapper.GetTemplateDir(), OspDirName, "provider")

	certfile := filepath.Clean(tfWrapper.GetOspCertificate())
	credfile := tfWrapper.GetOspCredential()
	authurl := tfWrapper.GetOspAuthUrl()
	eLog.LogInfo("OSP SetupCredentials certfile: %s credfile: %s authurl: %s",
		certfile, credfile, authurl)

	certbytes, err := terraform.ReadTextFile(certfile)
	if err != nil {
		eLog.LogError("Error reading openstack certifite file: %s %v", certfile, err)
	}
	cacert := string(certbytes)

	credbytes, err := terraform.ReadTextFile(credfile)
	if err != nil {
		eLog.LogError("Error reading openstack credential file: %s %v", credfile, err)
	}
	err = json.Unmarshal(credbytes, &usrpass)
	if err != nil {
		eLog.LogError("Error parsing openstack credentials from json: %v", err)
	}

	cred := &Credentials{
		UserName:   usrpass.UserName,
		Password:   usrpass.Password,
		TenantName: projectId,
		AuthUrl:    authurl,
		CACert:     cacert,
	}

	GenerateCredentialTF(cred, pTmplDir, pf)

	return nil
}

func SetupTfCommon(tfWrapper tf.TfWrapper, workspaceDir, tenantId string,
	eLog *clogger.EventLogger) (*Provider, error) {
	err := SetupCredentials(tfWrapper, workspaceDir, tenantId, eLog)
	if err == nil {
		return SetupProviderTf(tfWrapper, workspaceDir, eLog)
	}
	return nil, err
}
