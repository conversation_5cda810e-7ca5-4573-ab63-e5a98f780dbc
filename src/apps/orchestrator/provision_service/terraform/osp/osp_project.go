package osp

import (
	pterraform "orchestrator/provision_service/terraform"

	"go.panw.local/pangolin/clogger"
)

type Project struct {
	Id            string
	HostProjectId string
	TFLocalName   string
	Tags          []string
}

func PrepareProject(id, name, hostProjectId, awsEnv, customerName string,
	eLog *clogger.EventLogger) (*Project, error) {
	project := &Project{}
	project.Id = id
	project.HostProjectId = hostProjectId

	project.TFLocalName = pterraform.TFLocalName(project.Id)
	project.Tags = []string{
		"cyr_" + awsEnv,
	}
	if customerName != "" {
		project.Tags = append(project.Tags, "customer_"+customerName)
	}

	return project, nil
}
