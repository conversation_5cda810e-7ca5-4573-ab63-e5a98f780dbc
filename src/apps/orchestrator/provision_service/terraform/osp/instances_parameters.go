package osp

import (
	"orchestrator/libs/go/dbaccess/models/network_load_balancer_config"
	"orchestrator/provision_service/definitions"
)

type IPProtocol string

const (
	TCP        IPProtocol = "TCP"
	UDP                   = "UDP"
	ICMP                  = "ICMP"
	L3_DEFAULT            = "L3_DEFAULT"
)

type AddressType string

const (
	ApiAssigned AddressType = ""
	CleanIp                 = "public-ip"
	Egress                  = "forw-ip"
	Inbound                 = "inbound-ip"
)

// Address contains per-Ip-address parameters
type Address struct {
	// Params collected
	Name    string
	Address string
	Region  string

	// Params below are additional processed params based on the params above.
	// In the case of DM, these are derived in fwtemplate.py.
	TFLocalName     string
	ExplicitAddress bool
}

// NetworkInterface contains per-network-interface parameters
type NetworkInterface struct {
	// Params collected
	Name          string
	Network       string
	Subnet        string
	HasExternalIp bool
	StaticIp      string

	ExplicitNatIpOrRef bool
	Addr               *Address
}

// VMInstance contains per-VM parameters
type VMInstance struct {
	// Identification
	TenantId    string
	ClusterId   int64
	TFLocalName string
	NodeType    int
	Primary     *VMInstance
	IsPrimary   bool
	HACluster   bool
	MpInstance  bool

	// Params collected
	Name                 string
	Id                   int64
	Zone                 string
	Region               string
	ImageProject         string
	SharedMgmtVPCProj    string
	Image                string
	KeyPair              string
	ServiceAccount       string
	CleanIpTag           bool
	IsBehindNlb          bool
	HasNatInstance       bool
	PublicIp             string
	EgressIpList         string
	InboundAccessList    string
	MachineType          string
	CapacityType         string
	UserData             string
	Addrs                map[AddressType][]*Address
	NetIntfs             []*NetworkInterface
	Labels               []string
	IsSpInterconnect     bool
	IsInterconnectOnramp bool
	// Params below are additional processed params based on the params collected.
	// In the case of DM, these are derived in get_gcp_cust_region_instance_params() or generate_template().
	AllowedAsTarget   bool
	Metadata          []string
	InboundIps        map[string][]string                       // map[IP][]protocol(string)
	InboundIpKeys     []string                                  // sorted list of inbound IPs
	AllowedFwRules    map[string]*definitions.ProtocolInfoProps // map[protocol]*ProtocolInfoProps
	AllowedFwRuleKeys []string                                  // sorted list of protocol names

	// Params below are additional processed params based on the params collected.
	// In the case of DM, these are derived in fwtemplate.py.
	MachineTypeUrl           string
	ImageUrl                 string
	EgressIpsSansNat         []string // flat list of Egress IPs with entries having IP as "ASSIGNED_TO_NAT" removed
	NetworkTags              []string
	SetNatIpAsPublicIpOutput bool // output NAT IP as its public IP
	TargetInstance           *TargetInstance
	L3FwdRules               []*ForwardingRule
}

// TargetInstance defines an endpoint instance that terminates traffic of certain protocols.
// corresponding configuration: google_compute_target_instance
type TargetInstance struct {
	Name     string
	Zone     string
	Instance *VMInstance

	// Params below are additional processed params based on the params above.
	TFLocalName string
}

// ForwardingRule specifies which pool of target VM to forward a pocket.
// corresponding configuration: google_compute_forwarding_rule
type ForwardingRule struct {
	// Params collected
	Name           string
	Region         string
	IpAddressOrRef string
	IpProtocol     string
	AllPorts       bool
	Target         *TargetInstance
	DependsOn      *TargetInstance

	// Params below are additional processed params based on the params above.
	TFLocalName string
	Addr        *Address
}

type NLB struct {
	// Params collected
	Cfg          *network_load_balancer_config.Row
	Name         string
	PublicIp     string
	EgressIpList string
	Instances    []*VMInstance

	// Params below are additional processed params based on the params collected.
	EgressIpsSansNat []string // flat list of Egress IPs with entries having IP as "ASSIGNED_TO_NAT" removed
}
