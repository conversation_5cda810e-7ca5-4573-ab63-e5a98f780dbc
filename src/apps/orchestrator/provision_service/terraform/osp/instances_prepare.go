package osp

import (
	"encoding/json"
	"fmt"
	"io"
	"orchestrator/libs/go/dbaccess/models/cust_master"
	"orchestrator/libs/go/dbaccess/models/instance_master"
	"orchestrator/libs/go/dbaccess/models/network_load_balancer_config"
	"orchestrator/libs/go/terraform"
	dosp "orchestrator/provision_service/api/deployment/resource/osp"
	pterraform "orchestrator/provision_service/terraform"
	"orchestrator/provision_service/utils"
	"strconv"
	"strings"

	"go.panw.local/pangolin/clogger"
	"go.panw.local/provision/deployment"
)

const (
	MgmtNicName           string = "nic-mgmt"
	ClientIpNicName       string = "nic-dp"
	egressIpAssignedToNat        = "ASSIGNED_TO_NAT" // egress ip value is 'ASSIGNED_TO_NAT' if a NAT GW is in front of MU
)

func prepareTargetInstanceParams(vm *VMInstance) *TargetInstance {
	ti := &TargetInstance{}
	ti.Name = fmt.Sprintf("target-instance-%s", vm.Name)
	ti.Zone = vm.Zone
	ti.Instance = vm
	ti.TFLocalName = vm.TFLocalName
	return ti
}

func prepareAddrParams(vm *VMInstance, name, address string, explicitAddress bool) *Address {
	addr := &Address{}
	addr.Name = name
	addr.TFLocalName = pterraform.TFLocalName(addr.Name)
	addr.Address = address
	addr.Region = vm.Region
	addr.ExplicitAddress = explicitAddress
	return addr
}

func prepareNetIntfParams(project string, vm *VMInstance, name, network, subnet string, hasExternalIp bool,
	staticIp string) (*NetworkInterface, error) {
	intf := &NetworkInterface{}
	intf.Name = name
	intf.Network = network
	intf.Subnet = subnet
	intf.HasExternalIp = hasExternalIp
	intf.StaticIp = staticIp

	if vm.IsPrimary && hasExternalIp && !vm.IsSpInterconnect {
		// Only if not SP Interconnect.
		// For SP Interconnect, PUPI ip's will be configured on saas-agent boot up.
		// This was to workaround a DM update issue on alias IP, follow the same logic for now.
		var natIpOrRef string
		if staticIp == "" && !vm.CleanIpTag {
			addr := vm.Addrs[ApiAssigned]
			if addr == nil || len(addr) == 0 {
				return nil, fmt.Errorf("error preparing Network Interface params: corresponding API Assigned Addr missing")
			}
			intf.Addr = addr[0]
			natIpOrRef = fmt.Sprintf("google_compute_address.%s.address", addr[0].TFLocalName)
		} else if vm.CleanIpTag && staticIp != "" {
			addr := vm.Addrs[CleanIp]
			if addr == nil || len(addr) == 0 {
				return nil, fmt.Errorf("error preparing Network Interface params: corresponding Clean IP Addr missing")
			}
			intf.Addr = addr[0]
			natIpOrRef = fmt.Sprintf("google_compute_address.%s.address", addr[0].TFLocalName)
		} else {
			natIpOrRef = fmt.Sprintf("\"%s\"", staticIp)
		}
		intf.ExplicitNatIpOrRef = natIpOrRef != "" && (staticIp == "" || !vm.HACluster)
	}

	return intf, nil
}

func PrepareVmParams(project string, custMasterEntry *cust_master.Row, instMasterEntries []*instance_master.Row,
	nlbConfigEntry *network_load_balancer_config.Row, eLog *clogger.EventLogger) (map[int64][]*VMInstance, *NLB, error) {
	var err error
	vms := make(map[int64][]*VMInstance) // map[ClusterId]*VMInstance

	nlb := &NLB{Cfg: nlbConfigEntry}
	hasNlb := false

	clustersToBeDeleted := make(map[int64]bool)
	for _, myRow := range instMasterEntries {
		eLog.LogInfo("Updating VM config for %+v", myRow)
		project := project
		var saltProfile instance_master.SaltProfile
		err = json.Unmarshal([]byte(myRow.SaltProfile), &saltProfile)
		if err != nil {
			eLog.LogError("Error unmarshalling salt profile: %v, row: %+v", err, myRow)
			return nil, nil, err
		}
		eLog.LogInfo("salt-profile: %+v", saltProfile)

		vm := &VMInstance{}
		vm.TenantId = strconv.FormatInt(myRow.AcctID.Int64(), 10)
		vm.ClusterId = myRow.ClusterID.Int64()
		// If this cluster does not need to be part of deployment, add it to clustersToBeDeleted.
		if vm.ClusterId == myRow.DeleteDeployment.Int64() {
			clustersToBeDeleted[vm.ClusterId] = true
		}
		vm.NodeType = int(myRow.NodeType.Int64())

		switch vm.NodeType {
		case terraform.NodeTypeProbeVM:
			vm.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("probevm%v", saltProfile.InstanceId))
		case terraform.NodeTypeGPPortal:
			vm.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("portal%v", saltProfile.InstanceId))
		case terraform.NodeTypeGPGateway:
			vm.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("gpgw%v", saltProfile.InstanceId))
		case terraform.NodeTypeRemoteNetwork:
			vm.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("rn%v", saltProfile.InstanceId))
		case terraform.NodeTypeServiceConnection:
			vm.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("sc%v", saltProfile.InstanceId))
		default:
			eLog.LogError("Wrong NodeType: %d", vm.NodeType)
			continue
		}

		vm.Name = myRow.Name.String()
		vm.Id = saltProfile.InstanceId
		vm.Zone = saltProfile.Zone
		vm.Image = saltProfile.ImageId
		vm.KeyPair = saltProfile.KeyPair
		vm.IsBehindNlb = myRow.IsInstanceBehindNlb.Bool()
		vm.HasNatInstance = myRow.HasNatInstance.Int64() != 0
		vm.PublicIp = myRow.PublicIp.String()

		vm.HACluster = (vm.NodeType == terraform.NodeTypeRemoteNetwork || vm.NodeType == terraform.NodeTypeServiceConnection)
		vm.IsPrimary = (vm.Id == vm.ClusterId)
		vm.IsInterconnectOnramp = saltProfile.IsInterconnectOnramp
		if vm.IsPrimary {
			vm.Region = saltProfile.RegionName
			vm.ImageProject = saltProfile.ImageProject
			vm.SharedMgmtVPCProj = saltProfile.SharedMgmtVPCProj
			vm.ServiceAccount = saltProfile.SvcAcct
			vm.CleanIpTag = saltProfile.CleanIpTag
			vm.IsSpInterconnect = saltProfile.IsUsingSpInterconnect
			// If this is a GW instance that is behind NLB or has a NAT GW in front, use "interface_ip_list".
			if vm.NodeType == terraform.NodeTypeGPGateway && (vm.IsBehindNlb || vm.HasNatInstance) {
				eLog.LogInfo("Setting interface IP list (%v) as egress IP list (%v): "+
					"nodeType=%v, isBehindNlb=%v, hasNatInstance=%v",
					myRow.InterfaceIpList.String(), myRow.EgressIpList.String(), vm.NodeType, vm.IsBehindNlb,
					vm.HasNatInstance)
				vm.EgressIpList = myRow.InterfaceIpList.String()
			} else {
				vm.EgressIpList = myRow.EgressIpList.String()
			}
		} else {
			vm.Region = saltProfile.RegionName
			vm.SharedMgmtVPCProj = saltProfile.SharedMgmtVPCProj
			vm.ServiceAccount = saltProfile.SvcAcct
			vm.CleanIpTag = saltProfile.CleanIpTag
			vm.IsSpInterconnect = saltProfile.IsUsingSpInterconnect
		}

		vm.MachineType = saltProfile.InstType
		vm.AllowedAsTarget = true
		if vm.HACluster && (myRow.UpgradeCreation.Int64() == 1 || myRow.HAState.Int64() == 1) {
			vm.AllowedAsTarget = false
		}
		if vm.IsPrimary {
			vm.CapacityType = saltProfile.PrimaryCapacityType
		} else {
			vm.CapacityType = saltProfile.SecondaryCapacityType
		}
		vm.UserData = saltProfile.UserData
		// Derive metadata from userData
		metadata := func(key, value string) string {
			metadataFmt := "%v = \"%v\""
			return fmt.Sprintf(metadataFmt, strings.TrimSpace(key), strings.TrimSpace(value))
		}
		for _, kv := range strings.Split(vm.UserData, ",") {
			key, value, found := strings.Cut(kv, "=")
			if !found {
				eLog.LogWarn("Unexpected user data, missing key-value separator '=': kv=%s, userData=%s",
					kv, vm.UserData)
				continue
			}
			vm.Metadata = append(vm.Metadata, metadata(key, value))
		}
		key := "capacity_type"
		var value string
		if vm.IsPrimary {
			value = saltProfile.PrimaryCapacityType
		} else {
			value = saltProfile.SecondaryCapacityType
		}
		vm.Metadata = append(vm.Metadata, metadata(key, value))

		// All instance types need a mgmt interface.
		mgmtVpcPrj := project
		if vm.SharedMgmtVPCProj != "" {
			mgmtVpcPrj = vm.SharedMgmtVPCProj
		}
		mgmtIntf, err := prepareNetIntfParams(mgmtVpcPrj, vm,
			saltProfile.MgmtInterfaceName, saltProfile.MgmtNetwork, saltProfile.MgmtSubnet,
			saltProfile.MgmtHasExternalIP, "")
		if err != nil {
			eLog.LogError("Error preparing MGMT Network Interface: %v, row: %+v", err, myRow)
			return nil, nil, err
		}
		vm.NetIntfs = append(vm.NetIntfs, mgmtIntf)

		// Static IP (compute address)
		// In the case of DM, it is processed in fwtemplate.py.
		vm.Addrs = make(map[AddressType][]*Address)
		hasDpNic := saltProfile.DPNetwork != ""
		if vm.IsPrimary && !vm.IsSpInterconnect {
			// Only if not SP Interconnect.
			// For SP Interconnect no static IP config exists. Using PUPI IP's as alias IP's.
			if hasDpNic {
				if saltProfile.StaticIp == "" && !saltProfile.CleanIpTag {
					addr := prepareAddrParams(vm, vm.Name+"-address", "", false)
					vm.Addrs[ApiAssigned] = append(vm.Addrs[ApiAssigned], addr)
				} else if saltProfile.CleanIpTag && saltProfile.StaticIp != "" {
					addrName := fmt.Sprintf("%v-%v", CleanIp, strings.ReplaceAll(saltProfile.StaticIp, ".", "-"))
					addr := prepareAddrParams(vm, addrName, saltProfile.StaticIp, true)
					vm.Addrs[CleanIp] = append(vm.Addrs[CleanIp], addr)
				}
			}

			if vm.CleanIpTag {
				// Skip adding compute addresses for egress IP list if the instance is behind NLB.
				if !vm.IsBehindNlb {
					for _, ip := range vm.EgressIpsSansNat {
						addrName := fmt.Sprintf("%v-%v", Egress, strings.ReplaceAll(ip, ".", "-"))
						addr := prepareAddrParams(vm, addrName, ip, true)
						vm.Addrs[Egress] = append(vm.Addrs[Egress], addr)
					}
				}
				for _, ip := range vm.InboundIpKeys {
					addrName := fmt.Sprintf("%v-%v", Inbound, strings.ReplaceAll(ip, ".", "-"))
					addr := prepareAddrParams(vm, addrName, ip, true)
					vm.Addrs[Inbound] = append(vm.Addrs[Inbound], addr)
				}
			}
		}

		if hasDpNic {
			hasExtIp := false
			staticIp := ""
			if vm.IsPrimary {
				hasExtIp = saltProfile.DPHasExternalIP
				staticIp = saltProfile.StaticIp
			}
			dpIntf, err := prepareNetIntfParams(project, vm,
				saltProfile.DPInterfaceName, saltProfile.DPNetwork, saltProfile.DPSubnet,
				hasExtIp, staticIp)
			if err != nil {
				eLog.LogError("Error preparing DP Network Interface: %v, row: %+v", err, myRow)
				return nil, nil, err
			}
			vm.NetIntfs = append(vm.NetIntfs, dpIntf)
		}

		if saltProfile.HAInterfaceName != "" {
			haIntf, err := prepareNetIntfParams(project, vm,
				saltProfile.HAInterfaceName, saltProfile.HANetwork, saltProfile.HASubnet,
				saltProfile.HAHasExternalIP, "")
			if err != nil {
				eLog.LogError("Error preparing HA Network Interface: %v, row: %+v", err, myRow)
				return nil, nil, err
			}
			vm.NetIntfs = append(vm.NetIntfs, haIntf)
		}

		// Labels
		label := func(key, value string) string {
			labelFmt := "%v = \"%v\""
			return fmt.Sprintf(labelFmt, strings.TrimSpace(key), strings.TrimSpace(value))
		}
		vm.Labels = []string{label("customer", vm.TenantId)}
		if vm.CapacityType != "" {
			vm.Labels = append(vm.Labels, label("capacity_type", strings.ToLower(vm.CapacityType)))
		} else {
			vm.Labels = append(vm.Labels, label("capacity_type", "default"))
		}

		vms[vm.ClusterId] = append(vms[vm.ClusterId], vm)
		eLog.LogInfo("Final VM: %+v", vm)

	}

	for clusterId, _ := range vms {
		if clustersToBeDeleted[clusterId] {
			delete(vms, clusterId)
			eLog.LogInfo("Remove cluster %v as it is marked as to-be-deleted", clusterId)
		}
	}

	if hasNlb {
		return vms, nlb, nil
	} else {
		return vms, nil, nil
	}
}

func PrepareDeploymentConfig(tenantId int64, workspace, project, region string, clusters map[int64][]*VMInstance,
	nlb *NLB, zonalProvider bool, eLog *clogger.EventLogger) (*deployment.Config, error) {
	var err error
	config := &deployment.Config{
		ResourceName:  workspace,
		TenantID:      tenantId,
		ProjectID:     project,
		Region:        region,
		CloudProvider: deployment.OPENSTACK,
		ServiceType:   "PA",
		ImageProject:  "", // overwritten by the actual value below, this dummy val is used in 'destroy' case
	}

	var instances []deployment.Instance
	for _, cluster := range clusters {
		for _, vm := range cluster {
			// In deployment IsCleanIP and ImageProject is a config-level attribute.
			// Here we assume they are consistant across instances.
			config.IsCleanIP = vm.CleanIpTag
			config.ImageProject = vm.ImageProject

			var inst deployment.Instance
			inst.ResourceName = vm.Name
			inst.Name = vm.Name
			inst.Zone = vm.Zone
			inst.MachineType = vm.MachineType
			inst.Tags = vm.NetworkTags

			labels := make([]map[string]string, len(vm.Labels))
			for idx, vmLabel := range vm.Labels {
				kv := strings.SplitN(vmLabel, "=", 2)
				label := make(map[string]string)
				label["name"] = strings.TrimSpace(kv[0])
				label["value"] = utils.StripSurroundingQuotes(strings.TrimSpace(kv[1]))
				labels[idx] = label
			}
			inst.Labels = labels

			inst.BootDisk = deployment.BootDisk{
				DeviceName:        "boot",
				DisableAutoDelete: false,
			}
			inst.Image = vm.Image
			inst.KeyPair = vm.KeyPair

			interfaces := make([]deployment.Interface, len(vm.NetIntfs))
			for idx, vmIntf := range vm.NetIntfs {
				eLog.LogInfo("inst name: %s vmIntf: %+v", inst.Name, vmIntf)

				intf := &interfaces[idx]
				intf.Name = vmIntf.Name
				intf.Network = vmIntf.Network
				intf.Subnet = vmIntf.Subnet
				if vm.SharedMgmtVPCProj != "" && (vmIntf.Name == MgmtNicName || (vmIntf.Name == ClientIpNicName && vm.IsSpInterconnect)) {
					intf.ProjectOverride = vm.SharedMgmtVPCProj
				}
				if addr := vmIntf.Addr; addr != nil {
					if addr.Address != "" {
						// In clean-IP projects, addresses are always statically set.
						intf.PublicIP = addr.Address
					} else {
						// If address is not static, then set to "any" so Openstack will pick one.
						// This only happens in non-clean-IP projects with static IP missing in the salt profile.
						intf.PublicIP = "any"
					}
					intf.PublicIPResourceName = addr.TFLocalName
					intf.PublicIPName = addr.Name
				} else {
					intf.PublicIP = vmIntf.StaticIp
				}

				eLog.LogInfo("intf name: %s pubip: %s", intf.Name, intf.PublicIP)
			}

			inst.NetworkInterface = interfaces

			metadata := make([]map[string]string, len(vm.Metadata))
			for idx, meta := range vm.Metadata {
				kv := strings.SplitN(meta, "=", 2)
				datum := make(map[string]string)
				datum["name"] = strings.TrimSpace(kv[0])
				datum["value"] = utils.StripSurroundingQuotes(strings.TrimSpace(kv[1]))
				metadata[idx] = datum
			}
			inst.Metadata = metadata

			inst.Credentials = deployment.Credential{
				ServiceAccount: vm.ServiceAccount,
			}

			if vm.TargetInstance != nil {
				inst.CreateTarget = true
			}

			ipForwardings := make([]deployment.IPForwarding, len(vm.L3FwdRules))
			mappingIpProtocolToForwardingProtocol := func(proto IPProtocol) (deployment.ForwardingProtocol, error) {
				switch proto {
				case TCP:
					return deployment.TCPForwarding, nil
				case UDP:
					return deployment.UDPForwarding, nil
				case ICMP:
					return deployment.ICMPForwarding, nil
				case L3_DEFAULT:
					return deployment.L3DefaultForwarding, nil
				default:
					return "", fmt.Errorf("unexpected IPProtocl '%v'", proto)
				}
			}
			for idx, vmFwdRule := range vm.L3FwdRules {
				proto, err := mappingIpProtocolToForwardingProtocol(IPProtocol(vmFwdRule.IpProtocol))
				if err != nil {
					eLog.LogError("Failed to prepare IPForwarding: %v", err)
					return config, err
				}
				fwdRule := &ipForwardings[idx]
				fwdRule.ResourceName = vmFwdRule.TFLocalName
				fwdRule.Name = vmFwdRule.Name
				fwdRule.Protocol = proto
				fwdRule.AllPorts = vmFwdRule.AllPorts
				if addr := vmFwdRule.Addr; addr != nil {
					// In clean-IP projects, addresses are always statically set in a "google_compute_address" resource.
					fwdRule.Address = addr.Address
					fwdRule.AddressResourceName = addr.TFLocalName
					fwdRule.AddressName = addr.Name
				} else {
					// In non-clean-IP projects, addresses are directly set in the "google_compute_forwarding_rule" resource.
					fwdRule.Address = utils.StripSurroundingQuotes(vmFwdRule.IpAddressOrRef)
				}
				if ti := vmFwdRule.DependsOn; ti != nil {
					fwdRule.DependsOnResourceName = ti.TFLocalName
				}
			}
			inst.IPForwardings = ipForwardings
			instances = append(instances, inst)
		}
	}
	config.VMInstances = instances

	err = config.Validate()
	if err != nil {
		eLog.LogError("Failed to validate Config: %v, Config=%#v", err, *config)
		return config, err
	}
	return config, nil
}

func GenerateTfConfigFromDeployment(tenantId int64, workspaceName, projectId, regionName string,
	clusters map[int64][]*VMInstance, nlb *NLB, zonalProvider bool, tplDir string, w io.Writer,
	eLog *clogger.EventLogger) (*deployment.Config, error) {
	dConfig, err := PrepareDeploymentConfig(tenantId, workspaceName, projectId, regionName, clusters, nlb,
		zonalProvider, eLog)

	eLog.LogInfo("GenerateTfConfigFromDeployment() Deployment Config: %+v", dConfig)

	if err != nil {
		eLog.LogError("Error preparing deployment config: %v", err)
		return nil, err
	}

	for _, inst := range dConfig.VMInstances {
		rs := dosp.TransformInstance(*dConfig, inst)

		eLog.LogInfo("GenerateTfConfigFromDeployment() Final rs: %+v", rs)

		err = rs.GenerateTFFile(tplDir, w, io.Discard)
		if err != nil {
			eLog.LogError("Error generating instance TF config: %v, inst=%#v", err, inst)
			return nil, err
		}
	}

	return dConfig, nil
}
