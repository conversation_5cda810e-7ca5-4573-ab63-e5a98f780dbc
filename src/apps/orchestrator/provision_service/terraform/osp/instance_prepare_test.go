package osp

// UT Logs

/* trigger_update.json
{
    "cust_id": 80,
    "tenant_name": "renault3402-767988308",
    "tenant_id": 767988308,
    "project_id": "renault3402-767988308",
    "compute_region_id": 700,
    "native_compute_region_name": "us-west-1",
    "cloud_provider": "openstack",
    "service_type": "",
    "service_type_id": 0,
    "debug_id": "016227DC4586E52BF6D1D7E2718B4E",
    "trigger_update": **********
}

// Terraform files:

terraform {
  required_version = ">= 0.14.0"
  required_providers {
    openstack = {
      source = "terraform-provider-openstack/openstack"
      version = "1.49.0"
    }
  }

  cloud {
    organization = "PANW-SASE-NonProd"
  }
}

resource "openstack_compute_instance_v2" "GPGW_9532_us-west-700_renault3402-767988308" {
  name = "GPGW_9532_us-west-700_renault3402-767988308"
  flavor_name = "panos.c4.m16.60gb"
  security_groups  = ["default"]
  key_pair = "orchestrator"
  image_id = "b512a983-882a-43f4-8316-4b4712b40a40"

  tags = [
  "capacity_type = pa-cap530",
  "customer = 767988308",
  ]

  network {
    name = "overlay-management"
  }
  network {
    name = "dp-767988308"
  }

  metadata = {
    aws-access-key-id = ""
    aws-secret-access-key = ""
    bucket-name = "pan-content-dev10"
    capacity_type = "PA-CAP530"
    cert_fetch_otp = "661ed956-f1ba-4426-9a8d-5e5442de368c"
    cloud_provider = "openstack"
    custid = "767988308"
    custid_int = "80"
    edge-compute-region = "us-west-700"
    gp_domain = "panclouddev.com"
    instance_name = "GPGW_9532_us-west-700_renault3402-767988308"
    is_next_gen_pa = "0"
    lambdaprefix = "renault3402-767988308"
    panrepo_bucket_name = "panrepo-us-west-2-dev10"
    region = "us-west-1"
    route53acct = "a447084568087"
    saas_gpcs_api_endpoint = "dev10.panclouddev.com/api"
    super_custid = "767988308"
    zone = "us-west-1-a"
    zone-ha = "us-west-1-b"
    }
  }


resource "openstack_networking_floatingip_v2" "public-ip-GPGW_9532_us-west-700_renault3402-767988308" {
    pool = "external"
}
resource "openstack_compute_floatingip_associate_v2" "public-ip-GPGW_9532_us-west-700_renault3402-767988308" {
  floating_ip = "${openstack_networking_floatingip_v2.public-ip-GPGW_9532_us-west-700_renault3402-767988308.address}"
  instance_id = "${openstack_compute_instance_v2.GPGW_9532_us-west-700_renault3402-767988308.id}"
  fixed_ip    = "${openstack_compute_instance_v2.GPGW_9532_us-west-700_renault3402-767988308.network.1.fixed_ip_v4}"
}


output "GPGW_9532_us-west-700_renault3402-767988308" {
  value = {
    type = "vm"
    cluster_id = 9532
    name = "GPGW_9532_us-west-700_renault3402-767988308"
    id = 9532
    provisioned_as_primary = true
    vm_id = openstack_compute_instance_v2.GPGW_9532_us-west-700_renault3402-767988308.id
    mgmt_ip_address = openstack_compute_instance_v2.GPGW_9532_us-west-700_renault3402-767988308.network.0.fixed_ip_v4
    pvt_ip_address = openstack_compute_instance_v2.GPGW_9532_us-west-700_renault3402-767988308.network.1.fixed_ip_v4
    public_ip_address = openstack_networking_floatingip_v2.public-ip-GPGW_9532_us-west-700_renault3402-767988308.address
  }
}
*/

/* sample instance_master.json to test gpgw, rn and sc

[
    {
        "id": 124461,
        "vmid": "db2122a3-aa69-4b05-8828-6fd95546e312",
        "name": "FW_124461_us-west-1000_renault3402-767988308",
        "alias": "fw-124461-us-west-1000-renault3402",
        "serial_number": "7C449506893451B",
        "node_type": 48,
        "alt_node_type": -1,
        "vpc_id": "openstack-region-us-west-1000",
        "custid": 80,
        "acct_id": 767988308,
        "state": 1,
        "vm_status": 1,
        "shutdown": 0,
        "mark_delete": 0,
        "cloudwatch_ns": "cust767988308/FW_124461_us-west-1000_renault3402-767988308",
        "curr_tenants": 1000,
        "max_capacity": 1000,
        "mgt_ip": "************",
        "pvt_ip": "************",
        "extrn_ip": "",
        "pvt_ip2": "",
        "public_ip": "**************",
        "vgw_vip": "",
        "gw_gre_peer_ip": "",
        "gw_gre_bgp_asn": "",
        "gw_gre_bgp_range": "",
        "public_ipv6": "",
        "sase_fabric_ip": "**************",
        "egress_ip_list": "",
        "egress_ipv6_list": "",
        "egress_ipv6_list_subnet": "",
        "use_PBF": 0,
        "clusterid": 124461,
        "ha_peer": 124462,
        "ha_state": 0,
        "ha_state_detail": "",
        "ha2_ip": "",
        "username": "admin",
        "key_name": "",
        "loopback_ip": "*********",
        "loopback_ip_offset": 3,
        "timestamp": "",
        "qname": "fw_queue_80_124461",
        "retries": "",
        "tunnelsubnet": "",
        "is_pinned_instance": 0,
        "is_overloaded": 0,
        "is_dedicated_inbound_instance": 0,
        "upgrade_creation": 0,
        "salt_profile_name": "pan_FIREWALL_PA-VM-SaaS-10.2.6-c50.sb28_saas_1000_80",
        "salt_profile": "{\"CustId\": 80, \"AcctId\": \"767988308\", \"SerialNo\": \"Dummy_serial_no\", \"InstanceName\": \"fw-124461-us-west-1000-renault3402\", \"InstanceId\": 124461, \"RegionName\": \"us-west-1000\", \"Zone\": \"us-west-1000-a\", \"InstType\": \"panos.c8.m32.60gb\", \"webhook-callback\": \"http://inst-mgmt:8080/api/instance_management/deployment_post_process/0\", \"key-pair\": \"orchestrator\", \"UserData\": \"instance_name=FW_124461_us-west-1000_renault3402-767988308, instance-type=panos.c8.m32.60gb, saas_gpcs_api_endpoint=dev10.panclouddev.com/api, cert_fetch_otp=7403a19f-bd64-42af-b6c7-a6f65cfb8fa2, cloud_provider=openstack,custid=767988308,lambdaprefix=renault3402-767988308,custid_int=80,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-us-west-2-dev10,panrepo_bucket_name=panrepo-us-west-2-dev10,aws-access-key-id=,aws-secret-access-key=,zone=us-west-1000-a,zone-ha=us-west-1000-b, region=us-west-1000, edge-compute-region=us-west-1000, super_custid=767988308, is_next_gen_pa=0\", \"ProjectId\": \"cust-gpcs-wybapd6twafop7jzy9yd\", \"ImageProject\": \"cust-gpcs-wybapd6twafop7jzy9yd\", \"ImageName\": \"PA-VM-SaaS-10.2.6-c50.sb28_saas\", \"ImageId\": \"b512a983-882a-43f4-8316-4b4712b40a40\", \"svc_acct\": \"<EMAIL>\", \"PrimaryCapacityType\": \"PA-CAP550\", \"MgmtInterfaceName\": \"nic-mgmt\", \"DPInterfaceName\": \"nic-dp\", \"HAInterfaceName\": \"nic-ha\", \"MgmtNetwork\": \"overlay-management\", \"DPNetwork\": \"dp-cust-gpcs-wybapd6twafop7jzy9yd\", \"HANetwork\": \"ha-cust-gpcs-wybapd6twafop7jzy9yd\", \"MgmtSubnet\": \"mgmt-cust-gpcs-wybapd6twafop7jzy9yd-subnet\", \"DPSubnet\": \"subnet-dp-us-west-1000-767988308\", \"HASubnet\": \"ha-us-west-1000-subnet-1a\", \"MgmtHasExternalIP\": false, \"DPHasExternalIP\": true, \"HAHasExternalIP\": false, \"static_ip\": \"**************\", \"PrimaryIntfSet\": 0}",
        "logging_fqdn": "api.lcaas.qa.ap.paloaltonetworks.com",
        "version": "PA-VM-SaaS-10.2.6-c50.sb28_saas",
        "content_uri": "",
        "content_version": "panupv2-all-contents-8788-8445",
        "running_content": "panupv2-all-contents-8788-8445",
        "target_av_version": "panup-pa-all-antivirus-5132-632.candidate",
        "node_fqdn": "",
        "iot_certid": "099e93237e97264ee8fb55a746a510426a3ecdc15ba8d78f549fe0342c73d80f",
        "iot_certarn": "arn:aws:iot:us-west-2:398351886823:cert/099e93237e97264ee8fb55a746a510426a3ecdc15ba8d78f549fe0342c73d80f",
        "healthcheck_id": "",
        "target_saas_agent_version": "5.0.0-48.pan_27be410309",
        "current_saas_agent_version": "5.0.0-48.pan_27be410309",
        "create_time": "2023-12-14 02:28:54",
        "update_time": "2023-12-14 02:45:17",
        "delete_time": "",
        "compute_region_idx": 1000,
        "compute_region_name": "us-west-1000",
        "cloud_provider": "openstack",
        "current_collectd_version": "4.0.0-1.pan_1b2b2d6745",
        "target_collectd_version": "4.0.0-1.pan_1b2b2d6745",
        "tagging_status": "not_yet_tagged",
        "spn_name": "us-west-1000-sorrel",
        "lb_details": "",
        "lb_details_v6": "",
        "target_dirsyncd_version": "1.1.0-46_4cd8f59846",
        "inbound_access_ip_list": "",
        "collectd_cfg": "{\"collectdThreadMonitorTask\":\"3600\",\"panCoreDumpMonitorTask\":\"-1\",\"panHealthMonitorTask\":\"-1\",\"collectdCMDTask\":\"30\",\"collectdTunnelTask\":\"-1\",\"collectdSaasApplicationTask\":\"3600\",\"collectdTraceRouteTask\":\"1800\",\"tunnelStateMonitoringTask\":\"60\",\"pathMonitorTask\":\"60\",\"natTask\":\"60\",\"log_file\":[\"saas_agent.log\",\"eproxy.log\"]}",
        "mp1_id": 124461,
        "slot_nr": 0,
        "mpdp_clusterid": 0,
        "gw_capabilities": 0,
        "has_nat_instance": 0,
        "is_pa_connector_managed": 0,
        "no_passive_instance": 0,
        "ha_priority": "",
        "ha_ssh_key": "",
        "current_sasemsgd_version": "4.0.0-11_d4bab5460f",
        "is_instance_behind_nlb": false,
        "interface_ip_list": "",
        "interface_ipv6_list": "",
        "upgrade_status": "NONE",
        "mtdc_telemetry_cmd_list": "",
        "ibgp_med_value": 0,
        "is_two_phase_upgrade_supported": 0,
        "delete_deployment": 0,
        "is_key_rotation_supported": 0,
        "is_sase_fabric_spn": 0,
        "is_using_sp_interconnect": 0,
        "mtdc_cfg_update_epoc": 0,
        "mcw_enabled": 0,
        "current_mars_agent_profiles_version": "*******-3.c72139f159",
        "target_mars_agent_profiles_version": "*******-3.c72139f159",
        "fqdn_enabled": 0,
        "fqdn_only_flag": 0,
        "IsDynamicInstance": 0
    },
    {
        "id": 124462,
        "vmid": "ec0f6c56-b8e3-4a21-a562-c4536060b228",
        "name": "FW_124462_us-west-1000_renault3402-767988308",
        "alias": "fw-124462-us-west-1000-renault3402",
        "serial_number": "7335A218451BC96",
        "node_type": 48,
        "alt_node_type": -1,
        "vpc_id": "openstack-region-us-west-1000",
        "custid": 80,
        "acct_id": 767988308,
        "state": 1,
        "vm_status": 1,
        "shutdown": 0,
        "mark_delete": 0,
        "cloudwatch_ns": "cust767988308/FW_124462_us-west-1000_renault3402-767988308",
        "curr_tenants": 1000,
        "max_capacity": 1000,
        "mgt_ip": "************",
        "pvt_ip": "10**********",
        "extrn_ip": "",
        "pvt_ip2": "",
        "public_ip": "**************",
        "vgw_vip": "",
        "gw_gre_peer_ip": "",
        "gw_gre_bgp_asn": "",
        "gw_gre_bgp_range": "",
        "public_ipv6": "",
        "sase_fabric_ip": "",
        "egress_ip_list": "",
        "egress_ipv6_list": "",
        "egress_ipv6_list_subnet": "",
        "use_PBF": 0,
        "clusterid": 124461,
        "ha_peer": 124461,
        "ha_state": 0,
        "ha_state_detail": "",
        "ha2_ip": "",
        "username": "admin",
        "key_name": "",
        "loopback_ip": "*********",
        "loopback_ip_offset": 3,
        "timestamp": "",
        "qname": "fw_queue_80_124462",
        "retries": "",
        "tunnelsubnet": "",
        "is_pinned_instance": 0,
        "is_overloaded": 0,
        "is_dedicated_inbound_instance": 0,
        "upgrade_creation": 0,
        "salt_profile_name": "pan_FIREWALL_PA-VM-SaaS-10.2.6-c50.sb28_saas_1000_80_passive",
        "salt_profile": "{\"CustId\": 80, \"AcctId\": \"767988308\", \"SerialNo\": \"Dummy_serial_no\", \"InstanceName\": \"fw-124462-us-west-1000-renault3402\", \"InstanceId\": 124462, \"RegionName\": \"us-west-1000\", \"Zone\": \"us-west-1000-b\", \"InstType\": \"panos.c8.m32.60gb\", \"webhook-callback\": \"http://inst-mgmt:8080/api/instance_management/deployment_post_process/0\", \"key-pair\": \"orchestrator\", \"UserData\": \"instance_name=FW_124462_us-west-1000_renault3402-767988308, instance-type=panos.c8.m32.60gb, saas_gpcs_api_endpoint=dev10.panclouddev.com/api, cert_fetch_otp=a85ee30e-0003-4a79-af63-83e3e1607089, cloud_provider=openstack,custid=767988308,lambdaprefix=renault3402-767988308,custid_int=80,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-us-west-2-dev10,panrepo_bucket_name=panrepo-us-west-2-dev10,aws-access-key-id=,aws-secret-access-key=,zone=us-west-1000-a,zone-ha=us-west-1000-b, region=us-west-1000, edge-compute-region=us-west-1000, super_custid=767988308, is_next_gen_pa=0\", \"ProjectId\": \"cust-gpcs-wybapd6twafop7jzy9yd\", \"ImageProject\": \"cust-gpcs-wybapd6twafop7jzy9yd\", \"ImageName\": \"PA-VM-SaaS-10.2.6-c50.sb28_saas\", \"ImageId\": \"b512a983-882a-43f4-8316-4b4712b40a40\", \"svc_acct\": \"<EMAIL>\", \"PrimaryCapacityType\": \"PA-CAP550\", \"SecondaryCapacityType\": \"PA-CAP550\", \"MgmtInterfaceName\": \"nic-mgmt\", \"DPInterfaceName\": \"nic-dp\", \"HAInterfaceName\": \"nic-ha\", \"MgmtNetwork\": \"overlay-management\", \"DPNetwork\": \"dp-cust-gpcs-wybapd6twafop7jzy9yd\", \"HANetwork\": \"ha-cust-gpcs-wybapd6twafop7jzy9yd\", \"MgmtSubnet\": \"mgmt-cust-gpcs-wybapd6twafop7jzy9yd-subnet\", \"DPSubnet\": \"subnet-dp-us-west-1000-767988308\", \"HASubnet\": \"ha-us-west-1000-subnet-1a\", \"MgmtHasExternalIP\": false, \"DPHasExternalIP\": false, \"HAHasExternalIP\": false, \"static_ip\": \"\", \"PrimaryIntfSet\": 0}",
        "logging_fqdn": "api.lcaas.qa.ap.paloaltonetworks.com",
        "version": "PA-VM-SaaS-10.2.6-c50.sb28_saas",
        "content_uri": "",
        "content_version": "panupv2-all-contents-8788-8445",
        "running_content": "panupv2-all-contents-8788-8445",
        "current_av_version": "panup-pa-all-antivirus-5132-632.candidate",
        "target_av_version": "panup-pa-all-antivirus-5132-632.candidate",
        "node_fqdn": "",
        "iot_certid": "ae4742baedba6fadd18f2b2ecba9f86ed01e528b87f742c83f41b7be271a4103",
        "iot_certarn": "arn:aws:iot:us-west-2:398351886823:cert/ae4742baedba6fadd18f2b2ecba9f86ed01e528b87f742c83f41b7be271a4103",
        "healthcheck_id": "",
        "target_saas_agent_version": "5.0.0-48.pan_27be410309",
        "current_saas_agent_version": "5.0.0-48.pan_27be410309",
        "create_time": "2023-12-14 02:29:01",
        "update_time": "2023-12-14 02:46:55",
        "delete_time": "",
        "compute_region_idx": 1000,
        "compute_region_name": "us-west-1000",
        "cloud_provider": "openstack",
        "current_collectd_version": "4.0.0-1.pan_1b2b2d6745",
        "target_collectd_version": "4.0.0-1.pan_1b2b2d6745",
        "tagging_status": "not_yet_tagged",
        "spn_name": "us-west-1000-sorrel",
        "lb_details": "",
        "lb_details_v6": "",
        "target_dirsyncd_version": "1.1.0-46_4cd8f59846",
        "inbound_access_ip_list": "",
        "collectd_cfg": "{\"collectdThreadMonitorTask\":\"3600\",\"panCoreDumpMonitorTask\":\"-1\",\"panHealthMonitorTask\":\"-1\",\"collectdCMDTask\":\"30\",\"collectdTunnelTask\":\"-1\",\"collectdSaasApplicationTask\":\"3600\",\"collectdTraceRouteTask\":\"1800\",\"tunnelStateMonitoringTask\":\"60\",\"pathMonitorTask\":\"60\",\"natTask\":\"60\",\"log_file\":[\"saas_agent.log\",\"eproxy.log\"]}",
        "mp1_id": 124462,
        "slot_nr": 0,
        "mpdp_clusterid": 0,
        "native_machine_type": "panos.c8.m32.60gb",
        "gw_capabilities": 0,
        "has_nat_instance": 0,
        "is_pa_connector_managed": 0,
        "no_passive_instance": 0,
        "ha_priority": "",
        "ha_ssh_key": "",
        "current_sasemsgd_version": "4.0.0-11_d4bab5460f",
        "is_instance_behind_nlb": false,
        "interface_ip_list": "",
        "interface_ipv6_list": "",
        "upgrade_status": "NONE",
        "mtdc_telemetry_cmd_list": "",
        "ibgp_med_value": 0,
        "is_two_phase_upgrade_supported": 0,
        "delete_deployment": 0,
        "is_key_rotation_supported": 0,
        "is_sase_fabric_spn": 0,
        "is_using_sp_interconnect": 0,
        "mtdc_cfg_update_epoc": 0,
        "mcw_enabled": 0,
        "current_mars_agent_profiles_version": "*******-3.c72139f159",
        "target_mars_agent_profiles_version": "*******-3.c72139f159",
        "fqdn_enabled": 0,
        "fqdn_only_flag": 0,
        "IsDynamicInstance": 0
    },
    {
        "id": 124463,
        "vmid": "e2a95a9d-278c-40f2-9d16-b4df30661761",
        "name": "SFW_124463_us-west-1000_renault3402-767988308",
        "alias": "sfw-124463-us-west-1000-renault3402",
        "node_type": 51,
        "alt_node_type": -1,
        "vpc_id": "openstack-region-us-west-1000",
        "custid": 80,
        "acct_id": 767988308,
        "state": 1,
        "vm_status": 0,
        "shutdown": 0,
        "mark_delete": 0,
        "cloudwatch_ns": "cust767988308/SFW_124463_us-west-1000_renault3402-767988308",
        "curr_tenants": 0,
        "max_capacity": 500,
        "mgt_ip": "**********",
        "pvt_ip": "***********",
        "extrn_ip": "",
        "pvt_ip2": "",
        "public_ip": "**************",
        "vgw_vip": "",
        "gw_gre_peer_ip": "",
        "gw_gre_bgp_asn": "",
        "gw_gre_bgp_range": "",
        "public_ipv6": "",
        "sase_fabric_ip": "**************",
        "egress_ip_list": "",
        "egress_ipv6_list": "",
        "egress_ipv6_list_subnet": "",
        "use_PBF": 0,
        "clusterid": 124463,
        "ha_peer": 124464,
        "ha_state": 0,
        "ha_state_detail": "",
        "ha2_ip": "",
        "username": "admin",
        "key_name": "",
        "loopback_ip": "",
        "loopback_ip_offset": 9,
        "timestamp": "",
        "qname": "fw_queue_80_124463",
        "retries": "",
        "tunnelsubnet": "",
        "is_pinned_instance": 0,
        "is_overloaded": 0,
        "is_dedicated_inbound_instance": 0,
        "upgrade_creation": 0,
        "salt_profile_name": "pan_SERVICECONNECTION_PA-VM-SaaS-10.2.6-c50.sb28_saas_1000_80",
        "salt_profile": "{\"CustId\": 80, \"AcctId\": \"767988308\", \"SerialNo\": \"Dummy_serial_no\", \"InstanceName\": \"sfw-124463-us-west-1000-renault3402\", \"InstanceId\": 124463, \"RegionName\": \"us-west-1000\", \"Zone\": \"us-west-1000-a\", \"InstType\": \"panos.c4.m16.60gb\", \"webhook-callback\": \"http://inst-mgmt:8080/api/instance_management/deployment_post_process/0\", \"key-pair\": \"orchestrator\", \"UserData\": \"instance_name=SFW_124463_us-west-1000_renault3402-767988308, instance-type=panos.c4.m16.60gb, saas_gpcs_api_endpoint=dev10.panclouddev.com/api, cert_fetch_otp=5c52cd61-61e0-4803-8311-9779797480c7, cloud_provider=openstack,custid=767988308,lambdaprefix=renault3402-767988308,custid_int=80,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-us-west-2-dev10,panrepo_bucket_name=panrepo-us-west-2-dev10,aws-access-key-id=,aws-secret-access-key=,zone=us-west-1000-a,zone-ha=us-west-1000-b, region=us-west-1000, edge-compute-region=us-west-1000, super_custid=767988308, is_next_gen_pa=0\", \"ProjectId\": \"cust-gpcs-wybapd6twafop7jzy9yd\", \"ImageProject\": \"cust-gpcs-wybapd6twafop7jzy9yd\", \"ImageName\": \"PA-VM-SaaS-10.2.6-c50.sb28_saas\", \"ImageId\": \"b512a983-882a-43f4-8316-4b4712b40a40\", \"svc_acct\": \"<EMAIL>\", \"PrimaryCapacityType\": \"PA-CAP530\", \"MgmtInterfaceName\": \"nic-mgmt\", \"DPInterfaceName\": \"nic-dp\", \"HAInterfaceName\": \"nic-ha\", \"MgmtNetwork\": \"overlay-management\", \"DPNetwork\": \"dp-cust-gpcs-wybapd6twafop7jzy9yd\", \"HANetwork\": \"ha-cust-gpcs-wybapd6twafop7jzy9yd\", \"MgmtSubnet\": \"mgmt-cust-gpcs-wybapd6twafop7jzy9yd-subnet\", \"DPSubnet\": \"subnet-dp-us-west-1000-767988308\", \"HASubnet\": \"ha-us-west-1000-subnet-1a\", \"MgmtHasExternalIP\": false, \"DPHasExternalIP\": true, \"HAHasExternalIP\": false, \"static_ip\": \"**************\", \"PrimaryIntfSet\": 0}",
        "logging_fqdn": "api.lcaas.qa.ap.paloaltonetworks.com",
        "version": "PA-VM-SaaS-10.2.6-c50.sb28_saas",
        "content_uri": "",
        "content_version": "panupv2-all-contents-8788-8445",
        "target_av_version": "panup-pa-all-antivirus-5132-632.candidate",
        "node_fqdn": "",
        "iot_certid": "",
        "iot_certarn": "",
        "healthcheck_id": "",
        "target_saas_agent_version": "5.0.0-48.pan_27be410309",
        "create_time": "2023-12-14 03:03:44",
        "update_time": "2023-12-14 03:05:07",
        "delete_time": "",
        "compute_region_idx": 1000,
        "compute_region_name": "us-west-1000",
        "cloud_provider": "openstack",
        "target_collectd_version": "3.2.0-1.pan_a2086e7b60",
        "tagging_status": "not_yet_tagged",
        "spn_name": "",
        "lb_details": "",
        "lb_details_v6": "",
        "target_dirsyncd_version": "1.1.0-46_4cd8f59846",
        "inbound_access_ip_list": "",
        "collectd_cfg": "{\"collectdThreadMonitorTask\":\"3600\",\"panCoreDumpMonitorTask\":\"-1\",\"panHealthMonitorTask\":\"-1\",\"collectdCMDTask\":\"30\",\"collectdTunnelTask\":\"-1\",\"collectdSaasApplicationTask\":\"3600\",\"collectdTraceRouteTask\":\"1800\",\"tunnelStateMonitoringTask\":\"60\",\"pathMonitorTask\":\"60\",\"natTask\":\"60\",\"log_file\":[\"saas_agent.log\",\"eproxy.log\"]}",
        "mp1_id": 124463,
        "slot_nr": 0,
        "mpdp_clusterid": 0,
        "gw_capabilities": 0,
        "has_nat_instance": 0,
        "is_pa_connector_managed": 0,
        "no_passive_instance": 0,
        "ha_priority": "",
        "ha_ssh_key": "",
        "is_instance_behind_nlb": false,
        "interface_ip_list": "",
        "interface_ipv6_list": "",
        "upgrade_status": "NONE",
        "mtdc_telemetry_cmd_list": "",
        "ibgp_med_value": 0,
        "is_two_phase_upgrade_supported": 0,
        "delete_deployment": 0,
        "is_key_rotation_supported": 0,
        "is_sase_fabric_spn": 0,
        "is_using_sp_interconnect": 0,
        "mtdc_cfg_update_epoc": 0,
        "mcw_enabled": 0,
        "target_mars_agent_profiles_version": "*******-3.c72139f159",
        "fqdn_enabled": 0,
        "fqdn_only_flag": 0,
        "IsDynamicInstance": 0
    },
    {
        "id": 124464,
        "vmid": "83f0e9b7-a4cc-4d62-bfd0-70ccf7fe8124",
        "name": "SFW_124464_us-west-1000_renault3402-767988308",
        "alias": "sfw-124464-us-west-1000-renault3402",
        "node_type": 51,
        "alt_node_type": -1,
        "vpc_id": "openstack-region-us-west-1000",
        "custid": 80,
        "acct_id": 767988308,
        "state": 1,
        "vm_status": 0,
        "shutdown": 0,
        "mark_delete": 0,
        "cloudwatch_ns": "cust767988308/SFW_124464_us-west-1000_renault3402-767988308",
        "curr_tenants": 0,
        "max_capacity": 500,
        "mgt_ip": "************",
        "pvt_ip": "***********",
        "extrn_ip": "",
        "pvt_ip2": "",
        "public_ip": "**************",
        "vgw_vip": "",
        "gw_gre_peer_ip": "",
        "gw_gre_bgp_asn": "",
        "gw_gre_bgp_range": "",
        "public_ipv6": "",
        "sase_fabric_ip": "",
        "egress_ip_list": "",
        "egress_ipv6_list": "",
        "egress_ipv6_list_subnet": "",
        "use_PBF": 0,
        "clusterid": 124463,
        "ha_peer": 124463,
        "ha_state": 0,
        "ha_state_detail": "",
        "ha2_ip": "",
        "username": "admin",
        "key_name": "",
        "loopback_ip": "",
        "loopback_ip_offset": 9,
        "timestamp": "",
        "qname": "fw_queue_80_124464",
        "retries": "",
        "tunnelsubnet": "",
        "is_pinned_instance": 0,
        "is_overloaded": 0,
        "is_dedicated_inbound_instance": 0,
        "upgrade_creation": 0,
        "salt_profile_name": "pan_SERVICECONNECTION_PA-VM-SaaS-10.2.6-c50.sb28_saas_1000_80_passive",
        "salt_profile": "{\"CustId\": 80, \"AcctId\": \"767988308\", \"SerialNo\": \"Dummy_serial_no\", \"InstanceName\": \"sfw-124464-us-west-1000-renault3402\", \"InstanceId\": 124464, \"RegionName\": \"us-west-1000\", \"Zone\": \"us-west-1000-b\", \"InstType\": \"panos.c4.m16.60gb\", \"webhook-callback\": \"http://inst-mgmt:8080/api/instance_management/deployment_post_process/0\", \"key-pair\": \"orchestrator\", \"UserData\": \"instance_name=SFW_124464_us-west-1000_renault3402-767988308, instance-type=panos.c4.m16.60gb, saas_gpcs_api_endpoint=dev10.panclouddev.com/api, cert_fetch_otp=25ca68c4-639a-4c2a-ab6f-29f54e65964f, cloud_provider=openstack,custid=767988308,lambdaprefix=renault3402-767988308,custid_int=80,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-us-west-2-dev10,panrepo_bucket_name=panrepo-us-west-2-dev10,aws-access-key-id=,aws-secret-access-key=,zone=us-west-1000-a,zone-ha=us-west-1000-b, region=us-west-1000, edge-compute-region=us-west-1000, super_custid=767988308, is_next_gen_pa=0\", \"ProjectId\": \"cust-gpcs-wybapd6twafop7jzy9yd\", \"ImageProject\": \"cust-gpcs-wybapd6twafop7jzy9yd\", \"ImageName\": \"PA-VM-SaaS-10.2.6-c50.sb28_saas\", \"ImageId\": \"b512a983-882a-43f4-8316-4b4712b40a40\", \"svc_acct\": \"<EMAIL>\", \"PrimaryCapacityType\": \"PA-CAP530\", \"SecondaryCapacityType\": \"PA-CAP530\", \"MgmtInterfaceName\": \"nic-mgmt\", \"DPInterfaceName\": \"nic-dp\", \"HAInterfaceName\": \"nic-ha\", \"MgmtNetwork\": \"overlay-management\", \"DPNetwork\": \"dp-cust-gpcs-wybapd6twafop7jzy9yd\", \"HANetwork\": \"ha-cust-gpcs-wybapd6twafop7jzy9yd\", \"MgmtSubnet\": \"mgmt-cust-gpcs-wybapd6twafop7jzy9yd-subnet\", \"DPSubnet\": \"subnet-dp-us-west-1000-767988308\", \"HASubnet\": \"ha-us-west-1000-subnet-1a\", \"MgmtHasExternalIP\": false, \"DPHasExternalIP\": false, \"HAHasExternalIP\": false, \"static_ip\": \"\", \"PrimaryIntfSet\": 0}",
        "logging_fqdn": "api.lcaas.qa.ap.paloaltonetworks.com",
        "version": "PA-VM-SaaS-10.2.6-c50.sb28_saas",
        "content_uri": "",
        "content_version": "panupv2-all-contents-8788-8445",
        "target_av_version": "panup-pa-all-antivirus-5132-632.candidate",
        "node_fqdn": "",
        "iot_certid": "",
        "iot_certarn": "",
        "healthcheck_id": "",
        "target_saas_agent_version": "5.0.0-48.pan_27be410309",
        "create_time": "2023-12-14 03:03:50",
        "update_time": "2023-12-14 03:05:07",
        "delete_time": "",
        "compute_region_idx": 1000,
        "compute_region_name": "us-west-1000",
        "cloud_provider": "openstack",
        "target_collectd_version": "3.2.0-1.pan_a2086e7b60",
        "tagging_status": "not_yet_tagged",
        "spn_name": "",
        "lb_details": "",
        "lb_details_v6": "",
        "target_dirsyncd_version": "1.1.0-46_4cd8f59846",
        "inbound_access_ip_list": "",
        "collectd_cfg": "{\"collectdThreadMonitorTask\":\"3600\",\"panCoreDumpMonitorTask\":\"-1\",\"panHealthMonitorTask\":\"-1\",\"collectdCMDTask\":\"30\",\"collectdTunnelTask\":\"-1\",\"collectdSaasApplicationTask\":\"3600\",\"collectdTraceRouteTask\":\"1800\",\"tunnelStateMonitoringTask\":\"60\",\"pathMonitorTask\":\"60\",\"natTask\":\"60\",\"log_file\":[\"saas_agent.log\",\"eproxy.log\"]}",
        "mp1_id": 124464,
        "slot_nr": 0,
        "mpdp_clusterid": 0,
        "native_machine_type": "panos.c4.m16.60gb",
        "gw_capabilities": 0,
        "has_nat_instance": 0,
        "is_pa_connector_managed": 0,
        "no_passive_instance": 0,
        "ha_priority": "",
        "ha_ssh_key": "",
        "is_instance_behind_nlb": false,
        "interface_ip_list": "",
        "interface_ipv6_list": "",
        "upgrade_status": "NONE",
        "mtdc_telemetry_cmd_list": "",
        "ibgp_med_value": 0,
        "is_two_phase_upgrade_supported": 0,
        "delete_deployment": 0,
        "is_key_rotation_supported": 0,
        "is_sase_fabric_spn": 0,
        "is_using_sp_interconnect": 0,
        "mtdc_cfg_update_epoc": 0,
        "mcw_enabled": 0,
        "target_mars_agent_profiles_version": "*******-3.c72139f159",
        "fqdn_enabled": 0,
        "fqdn_only_flag": 0,
        "IsDynamicInstance": 0
    },
    {
        "id": 124465,
        "vmid": "dcf9aaa3-369b-4f9a-bd1c-8dda25630396",
        "name": "GPGW_124465_us-west-1000_renault3402-767988308",
        "alias": "gpgw-124465-us-west-1000-renault340",
        "node_type": 49,
        "alt_node_type": -1,
        "vpc_id": "openstack-region-us-west-1000",
        "custid": 80,
        "acct_id": 767988308,
        "state": 1,
        "vm_status": 0,
        "shutdown": 0,
        "mark_delete": 0,
        "cloudwatch_ns": "cust767988308/GPGW_124465_us-west-1000_renault3402-767988308",
        "curr_tenants": 0,
        "max_capacity": 500,
        "mgt_ip": "***********",
        "pvt_ip": "***********",
        "extrn_ip": "",
        "pvt_ip2": "",
        "public_ip": "**************",
        "vgw_vip": "",
        "gw_gre_peer_ip": "",
        "gw_gre_bgp_asn": "",
        "gw_gre_bgp_range": "",
        "public_ipv6": "",
        "sase_fabric_ip": "**************",
        "egress_ip_list": "",
        "egress_ipv6_list": "",
        "egress_ipv6_list_subnet": "",
        "use_PBF": 0,
        "clusterid": 124465,
        "ha_peer": 0,
        "ha_state": 0,
        "ha_state_detail": "",
        "ha2_ip": "",
        "username": "admin",
        "key_name": "",
        "loopback_ip": "",
        "loopback_ip_offset": 10,
        "timestamp": "",
        "qname": "fw_queue_80_124465",
        "retries": "",
        "tunnelsubnet": "",
        "is_pinned_instance": 1,
        "is_overloaded": 0,
        "is_dedicated_inbound_instance": 0,
        "upgrade_creation": 0,
        "salt_profile_name": "pan_GPGATEWAY_PA-VM-SaaS-10.2.6-c50.sb28_saas_1000_80",
        "salt_profile": "{\"CustId\": 80, \"AcctId\": \"767988308\", \"SerialNo\": \"Dummy_serial_no\", \"InstanceName\": \"gpgw-124465-us-west-1000-renault340\", \"InstanceId\": 124465, \"RegionName\": \"us-west-1000\", \"Zone\": \"us-west-1000-a\", \"InstType\": \"panos.c2.m8.60gb\", \"webhook-callback\": \"http://inst-mgmt:8080/api/instance_management/deployment_post_process/0\", \"key-pair\": \"orchestrator\", \"UserData\": \"instance_name=GPGW_124465_us-west-1000_renault3402-767988308, instance-type=panos.c2.m8.60gb, saas_gpcs_api_endpoint=dev10.panclouddev.com/api, cert_fetch_otp=e115307e-742f-4ed7-82d6-6005bcc42759, cloud_provider=openstack,custid=767988308,lambdaprefix=renault3402-767988308,custid_int=80,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-us-west-2-dev10,panrepo_bucket_name=panrepo-us-west-2-dev10,aws-access-key-id=,aws-secret-access-key=,zone=us-west-1000-a,zone-ha=us-west-1000-b, region=us-west-1000, edge-compute-region=us-west-1000, super_custid=767988308, is_next_gen_pa=0\", \"ProjectId\": \"cust-gpcs-wybapd6twafop7jzy9yd\", \"ImageProject\": \"cust-gpcs-wybapd6twafop7jzy9yd\", \"ImageName\": \"PA-VM-SaaS-10.2.6-c50.sb28_saas\", \"ImageId\": \"b512a983-882a-43f4-8316-4b4712b40a40\", \"svc_acct\": \"<EMAIL>\", \"PrimaryCapacityType\": \"PA-CAP310\", \"MgmtInterfaceName\": \"nic-mgmt\", \"DPInterfaceName\": \"nic-dp\", \"HAInterfaceName\": \"\", \"MgmtNetwork\": \"overlay-management\", \"DPNetwork\": \"dp-cust-gpcs-wybapd6twafop7jzy9yd\", \"HANetwork\": \"\", \"MgmtSubnet\": \"mgmt-cust-gpcs-wybapd6twafop7jzy9yd-subnet\", \"DPSubnet\": \"subnet-dp-us-west-1000-767988308\", \"HASubnet\": \"\", \"MgmtHasExternalIP\": false, \"DPHasExternalIP\": true, \"HAHasExternalIP\": false, \"static_ip\": \"**************\", \"PrimaryIntfSet\": 0}",
        "logging_fqdn": "api.lcaas.qa.ap.paloaltonetworks.com",
        "version": "PA-VM-SaaS-10.2.6-c50.sb28_saas",
        "content_uri": "",
        "content_version": "panupv2-all-contents-8788-8445",
        "target_av_version": "panup-pa-all-antivirus-5132-632.candidate",
        "node_fqdn": "",
        "iot_certid": "",
        "iot_certarn": "",
        "healthcheck_id": "",
        "target_saas_agent_version": "5.0.0-48.pan_27be410309",
        "create_time": "2023-12-14 03:06:14",
        "update_time": "2023-12-14 03:07:18",
        "delete_time": "",
        "compute_region_idx": 1000,
        "compute_region_name": "us-west-1000",
        "cloud_provider": "openstack",
        "target_collectd_version": "3.2.0-1.pan_a2086e7b60",
        "tagging_status": "not_yet_tagged",
        "spn_name": "",
        "lb_details": "",
        "lb_details_v6": "",
        "target_dirsyncd_version": "1.1.0-46_4cd8f59846",
        "inbound_access_ip_list": "",
        "collectd_cfg": "{\"collectdThreadMonitorTask\":\"3600\",\"panCoreDumpMonitorTask\":\"-1\",\"panHealthMonitorTask\":\"-1\",\"collectdCMDTask\":\"30\",\"collectdTunnelTask\":\"-1\",\"collectdSaasApplicationTask\":\"3600\",\"collectdTraceRouteTask\":\"1800\",\"tunnelStateMonitoringTask\":\"60\",\"pathMonitorTask\":\"60\",\"natTask\":\"60\",\"log_file\":[\"saas_agent.log\",\"eproxy.log\"]}",
        "mp1_id": 124465,
        "slot_nr": 0,
        "mpdp_clusterid": 0,
        "gw_capabilities": 1,
        "has_nat_instance": 0,
        "is_pa_connector_managed": 0,
        "no_passive_instance": 0,
        "ha_priority": "",
        "ha_ssh_key": "",
        "is_instance_behind_nlb": false,
        "interface_ip_list": "",
        "interface_ipv6_list": "",
        "upgrade_status": "NONE",
        "mtdc_telemetry_cmd_list": "",
        "ibgp_med_value": 0,
        "is_two_phase_upgrade_supported": 0,
        "delete_deployment": 0,
        "is_key_rotation_supported": 0,
        "is_sase_fabric_spn": 0,
        "is_using_sp_interconnect": 0,
        "mtdc_cfg_update_epoc": 0,
        "mcw_enabled": 0,
        "target_mars_agent_profiles_version": "*******-3.c72139f159",
        "fqdn_enabled": 0,
        "fqdn_only_flag": 0,
        "IsDynamicInstance": 0
    }
]

End of sample instance_master.json to test gpgw, rn and sc */
