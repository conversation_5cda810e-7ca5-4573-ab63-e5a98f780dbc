package oci

import (
	"encoding/json"
	"fmt"
	"orchestrator/libs/common/shared/grpc/proto/avisarpb"
	"orchestrator/libs/go/dbaccess/models/cust_master"
	"orchestrator/libs/go/dbaccess/models/instance_master"
	"orchestrator/libs/go/dbaccess/models/network_load_balancer_config"
	"orchestrator/libs/go/dbaccess/models/oci_shared_network_info"
	"orchestrator/libs/go/dbaccess/models/oci_tenant_network_info"
	"orchestrator/libs/go/terraform"
	pterraform "orchestrator/provision_service/terraform"
	"orchestrator/provision_service/utils"
	putils "orchestrator/provision_service/utils"
	"reflect"
	"strconv"
	"strings"

	"go.panw.local/pangolin/clogger"
)

const (
	egressIpAssignedToNat = "ASSIGNED_TO_NAT" // egress ip value is 'ASSIGNED_TO_NAT' if a NAT GW is in front of MU
)

func PrepareVmParams(custMasterEntry *cust_master.Row, tenantNetworkEntry *oci_tenant_network_info.Row,
	sharedMgmtEntry *oci_shared_network_info.Row, sharedDpEntry *oci_shared_network_info.Row,
	instMasterEntries []*instance_master.Row, nlbConfigEntry *network_load_balancer_config.Row,
	eLog *clogger.EventLogger) (map[int64]*ClusterConfig, map[int64]*NlbConfig,
	map[int64]*NatConfig, map[int64]*ClusterConfig, error) {

	var err error
	var env string
	clusters := make(map[int64]*ClusterConfig) // map[ClusterId]*VmInstance
	nlbs := make(map[int64]*NlbConfig)         // map[ClusterId]*NlbConfig
	natgws := make(map[int64]*NatConfig)       // map[ClusterId]*NatConfig
	regionName := tenantNetworkEntry.RegionName.String()
	epclusters := make(map[int64]*ClusterConfig)

	// Generate TF stack name for ORM
	parts := strings.SplitN(custMasterEntry.URL.String(), ".", 2)
	if len(parts) > 0 {
		env = parts[0]
	}
	stackName := fmt.Sprintf("%d_%s_oci_%s", custMasterEntry.AcctID.Int64(), regionName, env)
	eLog.LogInfo("PrepareVmParams: Stack %s being processed now", stackName)
	for _, myRow := range instMasterEntries {
		NodeType := int(myRow.NodeType.Int64())
		ClusterId := myRow.ClusterID.Int64()
		InstanceId := myRow.ID.Int64()
		var saltProfile instance_master.SaltProfile
		err = json.Unmarshal([]byte(myRow.SaltProfile), &saltProfile)
		if err != nil {
			eLog.LogError("Error unmarshalling salt profile: %v, row: %+v", err, myRow)
			return nil, nil, nil, nil, err
		}
		// If this cluster does not need to be part of deployment, add it to clustersToBeDeleted.
		if ClusterId == myRow.DeleteDeployment.Int64() {
			eLog.LogInfo("Skipping config for entry %+v since delete deployment is set", myRow)
			continue
		}
		if myRow.McwEnabled || myRow.IsSaseFabricSpn {
			eLog.LogInfo("Skipping VM config for %v (McwEnabled or ISSaseFabricSpn)", myRow.Name.String())
			continue
		} else if len(myRow.SaltProfile.String()) == 0 {
			eLog.LogInfo("Skipping VM config for %+v (Empty SaltProfile)", myRow)
			continue
		}
		if NodeType == terraform.NodeTypeNAT {
			natConfig := &NatConfig{}
			natConfig.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("%v", myRow.Name))
			eLog.LogInfo("Updating NAT config for %v: %+v", natConfig.TFLocalName, myRow)
			natConfig.InstanceId = ClusterId
			natConfig.CompartmentId = tenantNetworkEntry.CompartmentId.String()
			natConfig.VcnId = tenantNetworkEntry.VcnId.String()
			natConfig.RegionId = saltProfile.RegionId
			// Use GO SDK API here to fetch the OCI public IP OCID
			// TODO: Test the API and terraform provisioning
			natPublicIPOCID, err := putils.GetPublicIPOCID(saltProfile.StaticIp, regionName)
			if natPublicIPOCID == nil {
				eLog.LogInfo("PrepareVmParams: Salt Profile Static IP set to %s: OCID for the IP is nil, regionName: %s", saltProfile.StaticIp, regionName)
			} else {
				eLog.LogInfo("PrepareVmParams: Salt Profile Static IP set to %s: OCID for the IP: %s, regionName: %s", saltProfile.StaticIp, *natPublicIPOCID, regionName)
			}
			if err != nil {
				eLog.LogError("PrepareVmParams: Error getting public IP OCID for NAT configuration: %v", err)
				action := "NAT-" + strconv.FormatInt(myRow.ID.Int64(), 10) + " Provi​sionin​g in Region" + strconv.FormatInt(myRow.ComputeRegionIdx.Int64(), 10)
				err := putils.PublishAvisarEvent("ProvisionService_PrepareVMParams", strconv.FormatInt(myRow.AcctID.Int64(), 10), strconv.FormatInt(myRow.CustID.Int64(), 10), strconv.FormatInt(myRow.NodeType.Int64(), 10),
					myRow.CloudProvider.String(), strconv.FormatInt(myRow.ComputeRegionIdx.Int64(), 10), myRow.ComputeRegionName.String(), tenantNetworkEntry.CompartmentId.String(), myRow.Name.String(),
					"PrepareVMParams", strconv.FormatInt(myRow.AcctID.Int64(), 10), "CRITICAL", action, stackName, avisarpb.ORCHESTRATION_NGPA_OCI_NAT_BRINGUP_FAILED_PUBLIC_IP_OCID_FETCH_FAILURE, "failure", eLog)
				if err != nil {
					eLog.LogError("PrepareVmParams: Error %v publishing IP OCID fetch failure event to Avisar for row %v", err, myRow)
				}
			}
			natConfig.NatReservedPublicIpOcid = natPublicIPOCID
			natgws[InstanceId] = natConfig
		} else if NodeType == terraform.NodeTypeNLB {
			nlbConfig := &NlbConfig{}
			nlbConfig.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("%v", myRow.Name))
			eLog.LogInfo("Updating NLB config for %v: %+v", nlbConfig.TFLocalName, myRow)
			nlbConfig.InstanceId = InstanceId
			nlbConfig.CompartmentId = tenantNetworkEntry.CompartmentId.String()
			nlbConfig.SubnetId = tenantNetworkEntry.DpSubnetId.String()
			nlbConfig.HealthChecker.Protocol = "HTTP"
			nlbConfig.HealthChecker.Port = nlbConfigEntry.HealthCheckPort.Int64()
			// Set the NLB health check probes time interval to be read from the network_load_balancer_config table
			nlbConfig.HealthChecker.IntervalInMillis = nlbConfigEntry.HealthCheckInterval.Int64()
			nlbConfig.HealthChecker.TimeoutInMillis = nlbConfigEntry.HealthCheckTimeout.Int64()
			nlbConfig.HealthChecker.Retries = 3
			listener := NlbListenerPort{
				Name:           "ListnerAny",
				Port:           0,
				Protocol:       "ANY",
				TCPIdleTimeout: nlbConfigEntry.IdleTimeoutSec.Int64(),
				UDPIdleTimeout: nlbConfigEntry.IdleTimeoutSec.Int64(),
			}
			nlbConfig.NlbListenerPorts = append(nlbConfig.NlbListenerPorts, listener)
			nlbPublicIPOCID, err := putils.GetPublicIPOCID(myRow.PublicIp.String(), regionName)
			if nlbPublicIPOCID == nil {
				eLog.LogInfo("PrepareVmParams: Salt Profile Static IP set to %s: OCID for the IP is nil, regionName: %s", saltProfile.StaticIp, regionName)
			} else {
				eLog.LogInfo("PrepareVmParams: Salt Profile Static IP set to %s: OCID for the IP: %s, regionName: %s", saltProfile.StaticIp, *nlbPublicIPOCID, regionName)
			}
			if err != nil {
				eLog.LogError("PrepareVmParams: Error getting public IP OCID for NLB configuration: %v", err)
				action := "NLB-" + strconv.FormatInt(myRow.ID.Int64(), 10) + " Provi​sionin​g in Region" + strconv.FormatInt(myRow.ComputeRegionIdx.Int64(), 10)
				err := putils.PublishAvisarEvent("ProvisionService_PrepareVMParams", strconv.FormatInt(myRow.AcctID.Int64(), 10), strconv.FormatInt(myRow.CustID.Int64(), 10), strconv.FormatInt(myRow.NodeType.Int64(), 10),
					myRow.CloudProvider.String(), strconv.FormatInt(myRow.ComputeRegionIdx.Int64(), 10), myRow.ComputeRegionName.String(), tenantNetworkEntry.CompartmentId.String(), myRow.Name.String(),
					"PrepareVMParams", strconv.FormatInt(myRow.AcctID.Int64(), 10), "CRITICAL", action, stackName, avisarpb.ORCHESTRATION_NGPA_OCI_NLB_BRINGUP_FAILED_PUBLIC_IP_OCID_FETCH_FAILURE, "failure", eLog)
				if err != nil {
					eLog.LogError("PrepareVmParams: Error %v publishing IP OCID fetch failure event to Avisar for row %v", err, myRow)
				}
			}
			nlbConfig.NlbReservedPublicIpOcid = nlbPublicIPOCID
			// For OCI IIR disabled BF tenants; we need to be able to associate multiple edge IPaddresses on the NLB as IP mappings
			// These IP addresses then need to be added into NLB ingress_ip_mappings IM column for processing by SaaS agent
			// for DP handling for ingress traffic
			nlbConfig.NLBEgressIPOCIDList = make(map[string]string)
			nlbConfig.NLBEgressIPList = make(map[string]string)
			nlbConfig.NLBEgressIPList, err = deriveEgressIpAddresses(myRow.EgressIpList.String(), eLog)
			if err != nil {
				eLog.LogError("PrepareVmParams: Error deriving Egress IP addresses: %v, row: %+v, Egress IP list %s", err, myRow, myRow.EgressIpList.String())
			} else {
				eLog.LogInfo("PrepareVmParams: Success parsing Egress IP addresses: %v for NLB egress IP list %v", nlbConfig.NLBEgressIPList, myRow.EgressIpList.String())
			}
			eLog.LogInfo("PrepareVmParams: Per edge region Egress IP addresses: %v for NLB", nlbConfig.NLBEgressIPList)
			for regionID, ipAddr := range nlbConfig.NLBEgressIPList {
				nlbOCID, err := putils.GetPublicIPOCID(ipAddr, regionName)
				if nlbOCID == nil {
					eLog.LogInfo("PrepareVmParams: OCID for the IP %s is nil, regionName: %s, regionID: %s", ipAddr, regionName, regionID)
					nlbConfig.NLBEgressIPOCIDList[regionID] = ""
				} else {
					eLog.LogInfo("PrepareVmParams: OCID for the IP %s : %s, regionName: %s, regionID: %s", ipAddr, *nlbOCID, regionName, regionID)
					nlbConfig.NLBEgressIPOCIDList[regionID] = *nlbOCID
				}
				if err != nil {
					eLog.LogError("PrepareVmParams: Error getting public IP OCID for NLB configuration for IP %s for region ID %s Error %v", ipAddr, regionID, err)
					nlbConfig.NLBEgressIPOCIDList[regionID] = ""
					action := "NLB-" + strconv.FormatInt(myRow.ID.Int64(), 10) + " Provi​sionin​g in Region" + regionID
					err := putils.PublishAvisarEvent("ProvisionService_PrepareVMParams", strconv.FormatInt(myRow.AcctID.Int64(), 10), strconv.FormatInt(myRow.CustID.Int64(), 10), strconv.FormatInt(myRow.NodeType.Int64(), 10),
						myRow.CloudProvider.String(), strconv.FormatInt(myRow.ComputeRegionIdx.Int64(), 10), myRow.ComputeRegionName.String(), tenantNetworkEntry.CompartmentId.String(), myRow.Name.String(),
						"PrepareVMParams", strconv.FormatInt(myRow.AcctID.Int64(), 10), "CRITICAL", action, stackName, avisarpb.ORCHESTRATION_NGPA_OCI_NLB_BRINGUP_FAILED_PUBLIC_IP_OCID_FETCH_FAILURE, "failure", eLog)
					if err != nil {
						eLog.LogError("PrepareVmParams: Error %v publishing IP OCID fetch failure event to Avisar for row %v", err, myRow)
					}
				}
			}
			ipExists := false
			for _, ipAddr := range nlbConfig.NLBEgressIPOCIDList {
				if nlbConfig.NlbReservedPublicIpOcid != nil {
					if ipAddr == *nlbConfig.NlbReservedPublicIpOcid {
						ipExists = true
						break
					}
				}
			}
			eLog.LogInfo("PrepareVmParams: Updating Region ID to Egress IP OCID map: %v with compute region ID entry", nlbConfig.NLBEgressIPOCIDList)
			if !ipExists || len(nlbConfig.NLBEgressIPOCIDList) == 0 {
				computeRegionID := strconv.FormatInt(myRow.ComputeRegionIdx.Int64(), 10)
				if nlbConfig.NlbReservedPublicIpOcid != nil {
					nlbConfig.NLBEgressIPOCIDList[computeRegionID] = *nlbConfig.NlbReservedPublicIpOcid
					eLog.LogInfo("PrepareVmParams: Setting NLB reserved public IP OCID to the region to egress IPs map: %s", *nlbConfig.NlbReservedPublicIpOcid)
				} else {
					eLog.LogInfo("PrepareVmParams: Setting NLB reserved public IP OCID to the region to egress IPs map to empty string as no reserved public IP OCID exists")
					nlbConfig.NLBEgressIPOCIDList[computeRegionID] = ""
				}
			}
			eLog.LogInfo("PrepareVmParams: Adding NLB compute region public IP %s to the region IDs to egress IPs map: %v", myRow.PublicIp.String(), nlbConfig.NLBEgressIPList)
			computeRegionID := strconv.FormatInt(myRow.ComputeRegionIdx.Int64(), 10)
			// Check if nlbConfig.NLBEgressIPList is nil and initialize it if needed
			if nlbConfig.NLBEgressIPList == nil {
				nlbConfig.NLBEgressIPList = make(map[string]string)
			}
			nlbConfig.NLBEgressIPList[computeRegionID] = myRow.PublicIp.String()
			eLog.LogInfo("PrepareVmParams: NLB Reserved Public IP OCID list for edge regions: %v. NLB egress IP list for edge regions %v", nlbConfig.NLBEgressIPOCIDList, nlbConfig.NLBEgressIPList)

			// Configure NLB session affinity based on the network_load_balancer_config session affinity settings.
			if nlbConfigEntry.SessionAffinity.Int64() == 1 {
				nlbConfig.NlbBackendSetHashingPolicy = "TWO_TUPLE"
			} else {
				nlbConfig.NlbBackendSetHashingPolicy = "FIVE_TUPLE"
			}
			eLog.LogInfo("PrepareVmParams: NLB session affinity is set as %d and will be configured as %s", nlbConfigEntry.SessionAffinity.Int64(), nlbConfig.NlbBackendSetHashingPolicy)
			// For NGPA stack; we need the OCI NLB to operate in FailOpen mode to achieve
			// connection persistence across unhealthy backends.
			//  1: 'NEVER_PERSIST', 2: 'ALWAYS_PERSIST' as per the RDS table
			nlbConfig.ConnectionPersistenceOnUnhealthyBackends = false
			nlbConfig.IsFailOpen = false
			if nlbConfigEntry.ConnPersistenceOnUnhealthyBackends.Int64() == 2 {
				nlbConfig.ConnectionPersistenceOnUnhealthyBackends = true
				nlbConfig.IsFailOpen = true
			}
			if custMasterEntry.ExternalIPv6Support.Int64() == 1 {
				// Enable IPv6 configuration if supported
				nlbConfig.EnableIPv6 = true
				eLog.LogInfo("PrepareVmParams: IPv6 will be enabled for OCI NLB")
			} else {
				nlbConfig.EnableIPv6 = false
				eLog.LogInfo("PrepareVmParams: IPv6 will NOT be enabled for OCI NLB")
			}
			nlbs[InstanceId] = nlbConfig
		} else if NodeType == terraform.NodeTypeRemoteNetwork {
			// TODO add support for RN-HP instead
			eLog.LogInfo("Skipping VM config for remote networks %+v", myRow)
			continue
		} else if NodeType == terraform.NodetypeSWGProxy {

			vm := VmInstance{}
			vm.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("%v", myRow.Name))
			vm.AddToBackendSet = true
			vm.AddImageFilter = false
			vm.EnableIPv6 = false
			vm.CompartmentId = tenantNetworkEntry.CompartmentId.String()
			vm.DpSubnetId = tenantNetworkEntry.DpSubnetId.String()
			vm.SharedMgmtSubnetId = sharedMgmtEntry.SubnetId.String()
			vm.EpExtSubnetId = tenantNetworkEntry.EpExtSubnetId.String()
			vm.ShapeConfig.Ocpus = saltProfile.CPUCount
			vm.ShapeConfig.MemoryInGb = saltProfile.MemGB
			vm.ShapeConfig.BurstBaseline = saltProfile.BurstBaseline
			// From OCI VM salt profile, we will get the InstType as VM.Standard.E4.Flex-8-32
			// we want to only use the VM.Standard.E4.Flex part as the InstanceShape
			vmShape := strings.Split(saltProfile.InstType, "-")
			if len(vmShape) > 0 {
				vm.InstanceShape = vmShape[0]
				eLog.LogInfo("PrepareVmParams: Setting VM shape as %s", vm.InstanceShape)
			} else {
				eLog.LogError("PrepareVmParams: Invalid VM Shape found %v", vmShape)
			}
			eLog.LogInfo("Updating VM config for %v: %+v", vm.TFLocalName, myRow)
			vm.ClusterId = ClusterId
			vm.InternalName = saltProfile.InstName
			vm.InstanceId = saltProfile.InstanceId
			vm.AvailabilityDomain = saltProfile.Zone
			vm.InstanceImageId = saltProfile.ImageName
			vm.UserData = saltProfile.UserData
			vm.MachineType = saltProfile.InstType
			if _, exists := clusters[ClusterId]; !exists {
				epclusters[ClusterId] = &ClusterConfig{}
			}
			(*epclusters[ClusterId])[InstanceId] = vm
		} else {
			vm := VmInstance{}
			vm.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("%v", myRow.Name))
			vm.AddToBackendSet = false
			vm.AddImageFilter = false
			vm.EnableIPv6 = false
			vm.AcctId = custMasterEntry.AcctID.Int64()
			vm.CompartmentId = tenantNetworkEntry.CompartmentId.String()
			vm.DpSubnetId = tenantNetworkEntry.DpSubnetId.String()
			vm.SharedMgmtSubnetId = sharedMgmtEntry.SubnetId.String()
			vm.ShapeConfig.Ocpus = saltProfile.CPUCount
			vm.ShapeConfig.MemoryInGb = saltProfile.MemGB
			vm.ShapeConfig.BurstBaseline = saltProfile.BurstBaseline
			vmPublicIPOCID, err := putils.GetPublicIPOCID(saltProfile.StaticIp, regionName)
			if vmPublicIPOCID == nil {
				eLog.LogInfo("PrepareVmParams: Salt Profile Static IP set to %s: OCID for the IP is nil, regionName: %s", saltProfile.StaticIp, regionName)
			} else {
				eLog.LogInfo("PrepareVmParams: Salt Profile Static IP set to %s: OCID for the IP: %s, regionName: %s", saltProfile.StaticIp, *vmPublicIPOCID, regionName)
			}
			if err != nil {
				eLog.LogError("PrepareVmParams: Error getting public IP OCID for VM configuration: %v", err)
			}
			vm.ReservedPublicIpOcid = vmPublicIPOCID
			noPassiveInstance := myRow.NoPassiveInstance.Bool()
			eLog.LogInfo("PrepareVmParams: Service NIC supported for instance %v -> %v", InstanceId, saltProfile.IsServiceNicSupported)
			if NodeType == terraform.NodeTypeServiceConnection && noPassiveInstance == false && saltProfile.IsServiceNicSupported {
				eLog.LogInfo("PrepareVmParams: Configuring service NIC for SC node for instance %v", InstanceId)
				vm.HaSubnetId = tenantNetworkEntry.HaSubnetId.String()
			} else if NodeType == terraform.NodeTypeGPGateway {
				// Only NGPA is supported in OCI and MU instances are always behind an NLB
				vm.AddToBackendSet = true
			}
			if custMasterEntry.ExternalIPv6Support.Int64() == 1 {
				eLog.LogInfo("PrepareVmParams: IPv6 will be enabled for VM")
				vm.EnableIPv6 = true
			}
			if NodeType == terraform.NodeTypeServiceConnection {
				// This is to handle non SWM upgrade cases for SC nodes to be able
				// to delete and recreate upgraded VM instances
				eLog.LogInfo("PrepareVmParams: Add image filter for SC node upgrade in OCI")
				vm.AddImageFilter = true
			}
			// Enable the shared DP VNIC only if the salt profile indicates that it is supported
			// using field is_service_nic_supported
			if saltProfile.IsServiceNicSupported {
				vm.SharedDpSubnetId = setStringOrNil(sharedDpEntry.SubnetId.String())
			}
			eLog.LogInfo("Updating VM config for %v: %+v", vm.TFLocalName, myRow)
			vm.ClusterId = ClusterId
			vm.InternalName = saltProfile.InstName
			vm.InstanceId = saltProfile.InstanceId
			vm.AvailabilityDomain = saltProfile.Zone
			vm.InstanceImageId = saltProfile.ImageName
			// From OCI VM salt profile, we will get the InstType as VM.Standard.E4.Flex-8-32
			// we want to only use the VM.Standard.E4.Flex part as the InstanceShape
			vmShape := strings.Split(saltProfile.InstType, "-")
			if len(vmShape) > 0 {
				vm.InstanceShape = vmShape[0]
				eLog.LogInfo("PrepareVmParams: Setting VM shape as %s", vm.InstanceShape)
			} else {
				eLog.LogError("PrepareVmParams: Invalid VM Shape found %v : %v", vmShape, err)
			}
			vm.UserData = saltProfile.UserData
			vm.MachineType = saltProfile.InstType
			vm.IsSecondary = (ClusterId != InstanceId)
			if _, exists := clusters[ClusterId]; !exists {
				clusters[ClusterId] = &ClusterConfig{}
			}
			// vm.NoPassiveInstance = myRow.NoPassiveInstance.Bool()
			(*clusters[ClusterId])[InstanceId] = vm
		}
	}

	return clusters, nlbs, natgws, epclusters, nil
}

func deriveEgressIpAddresses(egressIpList string, eLog *clogger.EventLogger) (map[string]string, error) {
	if utils.EmptyValue(egressIpList) {
		return nil, nil
	}

	list := make(map[string]interface{})
	eipMap := make(map[string]string)
	if err := json.Unmarshal([]byte(egressIpList), &list); err != nil {
		eLog.LogError("Error unmarshalling egress IP list: %v", err)
		return nil, err
	}
	for regionID, elem := range list {
		if ip, isStr := elem.(string); isStr {
			if ip != egressIpAssignedToNat {
				eipMap[string(regionID)] = ip
			}
		} else if ipList, isList := elem.([]interface{}); isList {
			for _, e := range ipList {
				if ip, isStr := e.(string); isStr {
					if ip != egressIpAssignedToNat {
						eipMap[string(regionID)] = ip
					}
				} else {
					eLog.LogError("Error unmarshalling egress IP list, unknown list element type '%v', "+
						"list=%v", reflect.TypeOf(e), ipList)
				}
			}
		} else {
			eLog.LogError("Error unmarshalling egress IP list, unknown element type '%v', "+
				"element=%v", reflect.TypeOf(elem), elem)
		}
	}
	eLog.LogInfo("deriveEgressIpAddresses: Region ID to Egress IPs map %v", eipMap)
	return eipMap, nil
}
