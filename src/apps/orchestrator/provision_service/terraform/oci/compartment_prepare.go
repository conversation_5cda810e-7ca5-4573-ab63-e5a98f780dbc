package oci

import (
	"go.panw.local/pangolin/clogger"
	pterraform "orchestrator/provision_service/terraform"
)

func PrepareCompartment(parentCompId string, name string, eLog *clogger.EventLogger) (*Compartment, error) {
	compartment := &Compartment{}
	compartment.ParentCompId = parentCompId
	compartment.Name = name
	compartment.TFLocalName = pterraform.TFLocalName(name)
	// Add any IAM role policies, labels etc for the PA customer here

	return compartment, nil
}
