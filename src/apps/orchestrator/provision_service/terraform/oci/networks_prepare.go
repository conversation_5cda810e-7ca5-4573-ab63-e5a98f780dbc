package oci

import (
	"errors"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/provision_service/definitions"
	"orchestrator/provision_service/infra/db"
	pterraform "orchestrator/provision_service/terraform"
	"strings"

	"go.panw.local/pangolin/clogger"
)

func PrepareNetworks(compartmentId string, customerName string, ociNetworkProps *definitions.OciNetworkProps, tenantID int64, eLog *clogger.EventLogger) (*Network, error) {
	var network Network

	network.CompartmentId = compartmentId
	network.CustomerName = pterraform.TFLocalName(customerName)
	network.VcnCidrBlock = ociNetworkProps.VcnCidrBlock
	network.HaSubnetCidrBlock = ociNetworkProps.HaSubnetCidrBlock
	network.DpSubnetCidrBlock = ociNetworkProps.DpSubnetCidrBlock
	network.EpLbaasSubnetCidrBlock = ociNetworkProps.EpLbaasSubnetCidrBlock
	network.EpEnvoySubnetCidrBlock = ociNetworkProps.EpEnvoySubnetCidrBlock
	network.EpExtSubnetCidrBlock = ociNetworkProps.EpExtSubnetCidrBlock

	dbAccessor := db.GetDbAccessor()
	custMasterEntry, err := dbAccessor.GetCustMasterByAcctId(tenantID, eLog)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			eLog.LogError("PrepareNetworks: Error when fetching cust_master entry for tenant: %d, row: %+v, Error: %+v", tenantID, custMasterEntry, err)
		}
	}

	var urlParts = strings.SplitN(custMasterEntry.URL.String(), ".", 2) // to split the envName and domainName into two parts from the URL
	network.NatPortExhaustionAlertingURL = "https://api." + urlParts[0] + ".datapath." + urlParts[1] + "/ngpa_infra/cnat_alarm/v1/oci/latest"
	eLog.LogInfo("PrepareNetworks: Setting NAT GW port exhaustion URL to %s for tenant %d", network.NatPortExhaustionAlertingURL, tenantID)

	return &network, nil
}
