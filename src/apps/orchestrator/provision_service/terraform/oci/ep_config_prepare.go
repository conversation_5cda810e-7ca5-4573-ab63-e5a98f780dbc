package oci

import (
	"encoding/json"
	"fmt"
	"orchestrator/libs/go/dbaccess/models/cust_epaas_config"
	"orchestrator/libs/go/dbaccess/models/eproxy_mapping"
	"orchestrator/libs/go/dbaccess/models/explicit_proxy_tenant_info"
	"orchestrator/libs/go/dbaccess/models/oci_shared_network_info"
	"orchestrator/libs/go/dbaccess/models/oci_tenant_network_info"
	"orchestrator/libs/go/dbaccess/models/orch_cfg"
	putils "orchestrator/provision_service/utils"

	"strconv"
	"strings"

	"go.panw.local/pangolin/clogger"
)

func setStringOrNil(s string) *string {
	if s == "" {
		return nil
	}
	result := s
	return &result
}

func simplifyUserData(userdata string, eLog *clogger.EventLogger) (*string, error) {
	userdata = strings.ReplaceAll(userdata, "\\\"", "\"")
	var resultMap map[string]interface{}
	err := json.Unmarshal([]byte(userdata), &resultMap)
	if err != nil {
		eLog.LogError("Error unmarshalling userdata: %v , err: %v", userdata, err)
		return nil, err
	}
	var resultEntries []string
	for key, value := range resultMap {
		// Convert the value to a string (handling both string and integer values)
		valStr := fmt.Sprintf("%v", value)
		// Format each key-value pair as "key=value"
		resultEntries = append(resultEntries, fmt.Sprintf("%s=%s", key, valStr))
	}
	//Join the formatted key-value pairs with commas
	resultString := strings.Join(resultEntries, ",")
	return &resultString, nil
}

func PrepareEpConfig(tenantNetworkEntry *oci_tenant_network_info.Row,
	sharedMgmtEntry *oci_shared_network_info.Row, sharedVaultEntry *oci_shared_network_info.Row,
	explicitProxyTenantInfo *explicit_proxy_tenant_info.Row, custEpaasConfigEntry *cust_epaas_config.Row,
	envoyImage *eproxy_mapping.Row, itCaCert *orch_cfg.Row, certificateKey *string, certificatePub *string,
	epVmConfigs map[int64]*ClusterConfig, eLog *clogger.EventLogger) (*EpSpecificConfig, error) {

	//return early if delete task in terraform needs to be destroyed and we will not validate vault or policy
	if len(epVmConfigs) == 0 {
		return nil, nil
	}

	//default shape
	envoyShapeConfig := &VmShapeConfig{
		Ocpus:      2,
		MemoryInGb: 12,
	}
	envoyShape := "VM.Standard.E5.Flex"
	//pick the largest shape of all the ep instances so scale up happens rapidly
	//in a gradual scale down scenario, it will eventually pick the smallest shape
	for _, config := range epVmConfigs {
		for _, instance := range *config {
			//pick the larger instance size
			if instance.ShapeConfig.Ocpus > envoyShapeConfig.Ocpus {
				envoyShapeConfig = &instance.ShapeConfig
				envoyShape = instance.InstanceShape
			}
		}
	}

	var err error
	var saltProfile cust_epaas_config.SaltProfile
	err = json.Unmarshal([]byte(custEpaasConfigEntry.SaltProfile), &saltProfile)
	if err != nil {
		eLog.LogError("Error unmarshalling salt profile: %v, row: %+v", err, custEpaasConfigEntry)
		return nil, err
	}
	simplifiedUserData, err := simplifyUserData(saltProfile.UserData, eLog)
	if err != nil {
		eLog.LogError("Error simplifying userdata input: %v, row: %+v", err, saltProfile)
		return nil, err
	}
	poolSize, err := strconv.ParseInt(saltProfile.EnvoyInitialCnt, 10, 64)
	if err != nil {
		eLog.LogError("Error parsing poolSize: %v, row: %+v", err, saltProfile)
		return nil, err
	}
	poolSizeMax, err := strconv.ParseInt(saltProfile.EnvoyMaxCnt, 10, 64)
	if err != nil {
		eLog.LogError("Error parsing poolSizeMax: %v, row: %+v", err, saltProfile)
		return nil, err
	}
	poolSizeMin, err := strconv.ParseInt(saltProfile.EnvoyMinCnt, 10, 64)
	if err != nil {
		eLog.LogError("Error parsing poolSizeMin: %v, row: %+v", err, saltProfile)
		return nil, err
	}

	regionName := tenantNetworkEntry.RegionName.String()

	if poolSizeMax == poolSizeMin {
		poolSizeMax += 1 // Ensure pool size max is always greater than pool size min
	}

	if poolSizeMin > poolSize {
		poolSize = poolSizeMin // Ensure pool size is greater than or equal to pool size min
	}

	natList := make([]NatConfig, 0)
	natIpsList := strings.Split(custEpaasConfigEntry.CnatPublicIps.String(), ", ")
	natMinCount := max(1, custEpaasConfigEntry.CnatMinCount.Int64())
	var ct = 0
	for ct < int(natMinCount) {
		var natPublicIpOcid *string
		if ct < (len(natIpsList)) {
			natPublicIpOcid, err = putils.GetPublicIPOCID(natIpsList[ct], regionName)
			if natPublicIpOcid == nil {
				eLog.LogInfo("EpConfigPrepare: nat ips set to %s:, regionName: %s", custEpaasConfigEntry.CnatPublicIps, regionName)
			} else {
				eLog.LogInfo("EpConfigPrepare: nat ip set to %s: OCID for the IP: %s, regionName: %s", custEpaasConfigEntry.CnatPublicIps, *natPublicIpOcid, regionName)
			}
			if err != nil {
				eLog.LogError("PrepareVmParams: Error getting public IP OCID for NAT configuration: %v", err)
			}
		}
		ct++
		natList = append(natList, NatConfig{
			TFLocalName:             "EP-Nat-GW-" + strconv.Itoa(ct),
			CompartmentId:           tenantNetworkEntry.CompartmentId.String(),
			VcnId:                   tenantNetworkEntry.VcnId.String(),
			RegionId:                custEpaasConfigEntry.ComputeRegionId.Int64(),
			NatReservedPublicIpOcid: natPublicIpOcid,
		})
	}

	eLog.LogInfo("EpConfigPrepare, nat list: %v", natList)

	nlbPublicIpOcid, err := putils.GetPublicIPOCID(saltProfile.ExtLbIpv4, regionName)
	if nlbPublicIpOcid == nil {
		eLog.LogInfo("EpConfigPrepare: nlb ips set to %s:, regionName: %s", saltProfile.ExtLbIpv4, regionName)
	} else {
		eLog.LogInfo("EpConfigPrepare: nlb ip set to %s: OCID for the IP: %s, regionName: %s", saltProfile.ExtLbIpv4, *nlbPublicIpOcid, regionName)
	}
	if err != nil {
		eLog.LogError("PrepareVmParams: Error getting public IP OCID for NLB configuration: %v", err)
	}

	epConfig := &EpSpecificConfig{
		IlbConfig: &EpIlbConfig{
			CompartmentId: tenantNetworkEntry.CompartmentId.String(),
			VcnId:         tenantNetworkEntry.VcnId.String(),
			IlbSubnetId:   tenantNetworkEntry.DpSubnetId.String(),
			EnvoySubnetId: tenantNetworkEntry.EpEnvoySubnetId.String(),
			LbaasSubnetId: tenantNetworkEntry.EpLbaasSubnetId.String(),
			ExtSubnetId:   tenantNetworkEntry.EpExtSubnetId.String(),
		},
		LbaasConfig: &EpLbaasConfig{
			CompartmentId:         tenantNetworkEntry.CompartmentId.String(),
			VcnId:                 tenantNetworkEntry.VcnId.String(),
			LbaasSubnetId:         tenantNetworkEntry.EpLbaasSubnetId.String(),
			ReservedPublicIpOcid:  nlbPublicIpOcid,
			CertificateName:       fmt.Sprintf("ep-lbaas-cert-%d", tenantNetworkEntry.AcctId.Int64()),
			CertificatePrivateKey: *certificateKey,
			PublicCertificate:     *certificatePub,
			Enable443:             explicitProxyTenantInfo.EnableTlsTerm.Int64() == 1,
			Enable8082:            explicitProxyTenantInfo.EnableRnToEpCtx.Int64() == 1,
			Enable8083:            explicitProxyTenantInfo.EnableRnToEpCtx.Int64() == 1,
		},
		EnvoyConfig: &EpEnvoyConfig{
			ShapeConfig:        *envoyShapeConfig,
			InstanceShape:      envoyShape,
			CompartmentId:      tenantNetworkEntry.CompartmentId.String(),
			VcnId:              tenantNetworkEntry.VcnId.String(),
			UserData:           *simplifiedUserData,
			ImageId:            envoyImage.AmiId.String(),
			InstanceName:       fmt.Sprintf("gpcs-proxy-instance-%s-%d", regionName, tenantNetworkEntry.AcctId.Int64()),
			SubnetId:           tenantNetworkEntry.EpEnvoySubnetId.String(),
			SharedMgmtSubnetId: sharedMgmtEntry.SubnetId.String(),
			PoolSize:           poolSize,
			PoolSizeMax:        poolSizeMax,
			PoolSizeMin:        poolSizeMin,
		},
		VaultConfig: &EpVaultConfig{
			CompartmentId:    tenantNetworkEntry.CompartmentId.String(),
			TenancyId:        tenantNetworkEntry.TenancyId.String(),
			VaultId:          sharedVaultEntry.VaultId.String(),
			VaultPolicyName:  fmt.Sprintf("policy-tenant-%d", tenantNetworkEntry.AcctId.Int64()),
			MtlsSecret:       explicitProxyTenantInfo.MtlsCertPkey.String(),
			MtlsSecretName:   explicitProxyTenantInfo.EnvoyProxySecretId.String(),
			ItCaCert:         itCaCert.Value.String(),
			KeyDisplayName:   "TenantSecretKey",
			DynamicGroupName: fmt.Sprintf("dynamicgroup-tenant-%d", tenantNetworkEntry.AcctId.Int64()),
		},
		NatConfig: natList,
	}
	return epConfig, nil
}
