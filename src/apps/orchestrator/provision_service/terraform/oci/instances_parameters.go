package oci

type VmShapeConfig struct {
	Ocpus         int64
	MemoryInGb    int64
	BurstBaseline string
}

type VmInstance struct {
	AcctId               int64
	InstanceId           int64
	ClusterId            int64
	InternalName         string
	TFLocalName          string
	CompartmentId        string
	AvailabilityDomain   string
	InstanceShape        string
	ShapeConfig          VmShapeConfig
	InstanceImageId      string
	DpSubnetId           string
	HaSubnetId           string
	ByoipPoolId          string //optional
	EpExtSubnetId        string
	SharedMgmtSubnetId   string
	SharedDpSubnetId     *string //optional
	AddToBackendSet      bool
	AddImageFilter       bool
	UserData             string
	MachineType          string
	IsSecondary          bool
	ReservedPublicIpOcid *string
	FaultDomain          string // Fault domains provide anti-affinity: they let you distribute your instances so that the instances are not on the same physical hardware within a single availability domain
	DisableDpPublicIp    bool
	DefinedTags          []TagData // Used to load non UserData related tags into instance
	IsBackup             bool
	PrimaryNicType       string
	IsDeploymentApi      bool
	AssignIpV6           bool
	Separator            string
	NlbId                int64
	EnableIPv6           bool
}

type TagData struct {
	Namespace string
	Key       string
	Value     string
}

type ClusterConfig map[int64]VmInstance

type NlbListenerPort struct {
	Name           string
	Protocol       string
	Port           int
	TCPIdleTimeout int64
	UDPIdleTimeout int64
}

type HealthChecker struct {
	Protocol         string
	Port             int64
	Retries          int64
	TimeoutInMillis  int64
	IntervalInMillis int64
	UrlPath          string
	ReturnCode       int64
}

type NlbConfig struct {
	TFLocalName                              string
	InstanceId                               int64
	CompartmentId                            string
	SubnetId                                 string
	ByoipPoolId                              *string
	NlbListenerPorts                         []NlbListenerPort
	HealthChecker                            HealthChecker
	NlbBackendSetHashingPolicy               string
	ConnectionPersistenceOnUnhealthyBackends bool
	NlbReservedPublicIpOcid                  *string
	NLBEgressIPOCIDList                      map[string]string // Required for IIR disabled scenarios handling for BF tenants
	NLBEgressIPList                          map[string]string // Required for IIR disabled scenarios handling for BF tenants
	PreferOperationallyActiveBackends        bool
	IsInstantFailoverTcpResetEnabled         bool
	IsInstantFailoverEnabled                 bool
	IsFailOpen                               bool
	EnableIPv6                               bool
	IsSymmetricHashEnabled                   bool
}

type NatConfig struct {
	TFLocalName             string
	InstanceId              int64
	CompartmentId           string
	VcnId                   string
	RegionId                int64
	ByoipPoolId             *string
	NatReservedPublicIpOcid *string
}

type TFVars struct {
	Region         string
	ClusterConfigs map[int64]ClusterConfig
	NlbConfigs     map[int64]NlbConfig
	NatConfigs     map[int64]NatConfig
}

type EpSpecificConfig struct {
	IlbConfig   *EpIlbConfig
	LbaasConfig *EpLbaasConfig
	EnvoyConfig *EpEnvoyConfig
	VaultConfig *EpVaultConfig
	NatConfig   []NatConfig
}

type EpIlbConfig struct {
	CompartmentId string
	VcnId         string
	IlbSubnetId   string
	EnvoySubnetId string
	LbaasSubnetId string
	ExtSubnetId   string
}

type EpLbaasConfig struct {
	CompartmentId         string
	VcnId                 string
	LbaasSubnetId         string
	ReservedPublicIpOcid  *string
	CertificateName       string
	CertificatePrivateKey string
	PublicCertificate     string
	Enable443             bool
	Enable8082            bool
	Enable8083            bool
}

type EpEnvoyConfig struct {
	ShapeConfig        VmShapeConfig
	InstanceShape      string
	CompartmentId      string
	VcnId              string
	UserData           string
	InstanceName       string
	ImageId            string
	SubnetId           string
	SharedMgmtSubnetId string
	PoolSize           int64
	PoolSizeMax        int64
	PoolSizeMin        int64
}

type EpVaultConfig struct {
	CompartmentId    string
	TenancyId        string
	VaultId          string
	VaultPolicyName  string
	MtlsSecret       string
	MtlsSecretName   string
	ItCaCert         string
	KeyDisplayName   string
	DynamicGroupName string
}

type TfTemplateArgument struct {
	Provider         *Provider
	IdxScheme        bool
	ClusterConfigs   map[int64]*ClusterConfig
	NlbConfigs       map[int64]*NlbConfig
	NatConfigs       map[int64]*NatConfig
	EpClusterConfigs map[int64]*ClusterConfig
	EpSpecificConf   *EpSpecificConfig
}
