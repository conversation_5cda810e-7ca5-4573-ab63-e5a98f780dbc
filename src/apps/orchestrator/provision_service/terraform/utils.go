package terraform

import (
	"fmt"
	"go.panw.local/pangolin/clogger"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strings"
	"text/template"
)

var (
	ErrTfRunFailed = fmt.Errorf("unexpected terraform Run status")
)

// TFLocalName takes in a raw name string and changes it to fit Terraform naming conventions:
// 1. Use _ (underscore) instead of - (dash) and . (dot).
// 2. Prefer to use lowercase letters and numbers (even though UTF-8 is supported).
func TFLocalName(rawName string) string {
	return strings.ToLower(strings.ReplaceAll(strings.ReplaceAll(rawName, ".", "_"), "-", "_"))
}

// Just like TFLocalName, but allows -
func OciTfLocalName(rawName string) string {
	return strings.ToLower(strings.ReplaceAll(rawName, ".", "-"))
}

func ParseGotmpl(providerDir, workspaceDir, templName string, tfName string, eLog *clogger.EventLogger) (*template.Template, *os.File, error) {
	templFile := filepath.Join(providerDir, templName)
	t, e := template.New(templName).ParseFiles(templFile)
	if e != nil {
		eLog.LogError("Error parsing template file %v: %v", templFile, e)
		return nil, nil, e
	}

	// Derive TF Configuration filename by replacing the file extension of the src golang template file.
	_ = path.Ext(templName)
	tfFile := filepath.Join(workspaceDir, tfName)
	f, e := os.Create(tfFile)
	if e != nil {
		eLog.LogError("Error creating TF configuration file %v: %v", tfFile, e)
		return nil, nil, e
	}

	return t, f, nil
}

// GcpInstanceHasNicLimit returns whether the input machine type only supports 2 interfaces.
// The regular expression used here is ported from gcp_instance_has_nic_limit(logger, size) in the python code.
func GcpInstanceHasNicLimit(machineType string) (bool, error) {
	return regexp.Match(`-large-|-2$|-2-|custom-medium`, []byte(machineType))
}
