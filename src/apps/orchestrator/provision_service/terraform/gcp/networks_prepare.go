package gcp

import (
	"fmt"
	"go.panw.local/pangolin/clogger"
	"orchestrator/provision_service/definitions"
	pterraform "orchestrator/provision_service/terraform"
)

func prepareFirewallRule(networkRef string, props *definitions.FirewallRuleProps) *FirewallRule {
	fwRule := &FirewallRule{FirewallRuleProps: *props}
	fwRule.NetworkRef = networkRef
	fwRule.TFLocalName = pterraform.TFLocalName(fwRule.Name)
	for _, piProps := range props.ProtocolInfo {
		if fwRule.Allow {
			fwRule.Allowed = append(fwRule.Allowed, piProps)
		} else {
			fwRule.Denied = append(fwRule.Denied, piProps)
		}
	}
	return fwRule
}

func prepareSubnet(project string, subnetProps *definitions.SubnetProps) *Subnet {
	subnet := &Subnet{SubnetProps: *subnetProps}
	subnet.TFLocalName = pterraform.TFLocalName(subnet.Name)
	subnet.SubnetUrl = fmt.Sprintf("https://www.googleapis.com/compute/v1/projects/%s/regions/%s/subnetworks/%s",
		project, subnet.Region, subnet.Name)
	return subnet
}

func PrepareNetworks(project string, networkProps []*definitions.NetworkProps, eLog *clogger.EventLogger) ([]*Network, error) {
	var networks []*Network

	for _, nProps := range networkProps {
		eLog.LogInfo("Updating Network config for %v", nProps.Name)
		network := &Network{NetworkProps: *nProps}
		network.TFLocalName = pterraform.TFLocalName(network.Name)
		network.NetworkUrl = fmt.Sprintf("https://www.googleapis.com/compute/v1/projects/%s/global/networks/%s",
			project, network.Name)

		for _, sProps := range nProps.Subnets {
			network.Subnets = append(network.Subnets, prepareSubnet(project, sProps))
		}

		for _, fwProps := range nProps.FirewallRules {
			network.FirewallRules = append(network.FirewallRules, prepareFirewallRule(network.NetworkUrl, fwProps))
		}
		networks = append(networks, network)
	}

	return networks, nil
}
