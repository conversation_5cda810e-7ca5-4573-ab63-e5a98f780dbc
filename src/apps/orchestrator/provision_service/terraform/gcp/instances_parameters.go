package gcp

import (
	"orchestrator/libs/go/dbaccess/models/network_load_balancer_config"
	"orchestrator/provision_service/definitions"
)

type IPProtocol string

const (
	TCP        IPProtocol = "TCP"
	UDP                   = "UDP"
	ICMP                  = "ICMP"
	L3_DEFAULT            = "L3_DEFAULT"
)

type IPVersion string

const (
	IPV4 IPVersion = "IPV4"
	IPV6           = "IPV6"
)

type AddressType string

const (
	ApiAssigned    AddressType = ""
	DP2ApiAssigned             = "dp2-api-assigned"
	Dp2CleanIp                 = "dp2-ip"
	CleanIp                    = "public-ip"
	Egress                     = "forw-ip"
	DP2Egress                  = "forw-ip-dp2"
	Inbound                    = "inbound-ip"
)

// Address contains per-Ip-address parameters
type Address struct {
	// Params collected
	Name    string
	Address string
	Region  string

	// Params below are additional processed params based on the params above.
	// In the case of DM, these are derived in fwtemplate.py.
	TFLocalName     string
	ExplicitAddress bool
}

type AliasIpRange struct {
	IpCidrRange         string
	SubnetworkRangeName string
}

// NetworkInterface contains per-network-interface parameters
type NetworkInterface struct {
	// Params collected
	Name          string
	Network       string
	Subnet        string
	HasExternalIp bool
	StaticIp      string
	QueueCount    int64
	DualStack     bool

	// Params below are additional processed params based on the params above.
	// In the case of DM, these are derived in fwtemplate.py.
	Project                     string
	NetworkUrl                  string
	SubnetUrl                   string
	ExplicitQueueCount          bool
	NatIpOrRef                  string
	ExplicitNatIpOrRef          bool
	Addr                        *Address
	IgnoreChangesOnAccessConfig bool
	IgnoreChangesOnAliasIpRange bool
	AliasIpRanges               []AliasIpRange
}

// NetworkPerformanceConfig
type NetworkPerformanceConfig struct {
	TotalEgressBandwidthTier string
}

// VMInstance contains per-VM parameters
type VMInstance struct {
	// Identification
	TenantId    string
	ClusterId   int64
	TFLocalName string
	NodeType    int
	AltNodeType int
	Primary     *VMInstance
	IsPrimary   bool
	HACluster   bool
	MpInstance  bool
	IsColoSC    bool

	// Params collected
	Name                     string
	Id                       int64
	Zone                     string
	ZonesList                []string
	Region                   string
	ImageProject             string
	SharedMgmtVPCProj        string
	Image                    string
	ServiceAccount           string
	CleanIpTag               bool
	IsBehindNlb              bool
	HasNatInstance           bool
	HasExternalIpv6          bool
	HasExternalIpv6Dp2       bool
	PublicIp                 string
	EgressIpList             string
	EgressIpv6ListSubnet     string
	InboundAccessList        string
	MachineType              string
	CapacityType             string
	UserData                 string
	Addrs                    map[AddressType][]*Address
	NetIntfs                 []*NetworkInterface
	Labels                   []string
	IsSpInterconnect         bool
	IsInterconnectOnramp     bool
	MinCpuPlatform           string
	NetworkPerformanceConfig *NetworkPerformanceConfig
	NoPassiveInstance        bool
	// Params below are additional processed params based on the params collected.
	// In the case of DM, these are derived in get_gcp_cust_region_instance_params() or generate_template().
	AllowedAsTarget   bool
	Metadata          []string
	InboundIps        map[string][]string                       // map[IP][]protocol(string)
	InboundIpKeys     []string                                  // sorted list of inbound IPs
	AllowedFwRules    map[string]*definitions.ProtocolInfoProps // map[protocol]*ProtocolInfoProps
	AllowedFwRuleKeys []string                                  // sorted list of protocol names
	DpProject         string

	// Params below are additional processed params based on the params collected.
	// In the case of DM, these are derived in fwtemplate.py.
	HasDpNic                    bool
	MachineTypeUrl              string
	ImageUrl                    string
	EgressIpsSansNat            []string // flat list of Egress IPs with entries having IP as "ASSIGNED_TO_NAT" removed
	EgressIpv6Subnets           map[string]map[string]*ForwardingRule
	Tags                        []string
	SetNatIpAsPublicIpOutput    bool // output NAT IP as its public IP
	TargetInstance              *TargetInstance
	L3FwdRules                  []*ForwardingRule
	FwRules                     []*FirewallRule
	EgressIpsSansNatDP2         []string
	PublicIpDp2                 string
	SetNatIpAsPublicIpDP2Output bool
	Sase5gEnabled               bool
	MasqueEnabled               bool
	HasDualEgress               bool
}

// TargetInstance defines an endpoint instance that terminates traffic of certain protocols.
// corresponding configuration: google_compute_target_instance
type TargetInstance struct {
	Name     string
	Zone     string
	Instance *VMInstance

	// Params below are additional processed params based on the params above.
	TFLocalName string
}

// ForwardingRule specifies which pool of target VM to forward a pocket.
// corresponding configuration: google_compute_forwarding_rule
type ForwardingRule struct {
	// Params collected
	Name           string
	Region         string
	Subnet         string
	IPVersion      IPVersion
	IpAddressOrRef string
	IpProtocol     string
	AllPorts       bool
	Target         *TargetInstance
	DependsOn      *TargetInstance

	// Params below are additional processed params based on the params above.
	TFLocalName string
	SubnetUrl   string
	Addr        *Address
}

type NLB struct {
	// Params collected
	Cfg              *network_load_balancer_config.Row
	CfgSource        *VMInstance // source for retrieving Zone, Region, CleanIpTag, Network, Subnet
	Region           string
	CleanIpTag       bool
	Network          string
	Subnet           string
	Name             string
	PublicIp         string
	EgressIpList     string
	ZonesList        []string
	ZonalInstanceMap map[string][]*VMInstance
	HasExternalIpv6  bool
	ComputeRegionIdx string

	// Params below are additional processed params based on the params collected.
	EgressIpsSansNat     []string // flat list of Egress IPs with entries having IP as "ASSIGNED_TO_NAT" removed
	EgressIpv6ListSubnet string
	IngressIpReduction   int64

	// Common base name used for the load balancers.
	CommonBaseName string
	IsColoILB      bool
	// If UMIG network is different than LB network
	UMIGNetwork string
	UMIGSubnet  string
}
