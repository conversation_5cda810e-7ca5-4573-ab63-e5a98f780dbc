package gcp

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"orchestrator/libs/go/dbaccess/models/colo_connect_link_master"
	"orchestrator/libs/go/dbaccess/models/colo_onboarding_master"
	"orchestrator/libs/go/dbaccess/models/cust_master"
	"orchestrator/libs/go/dbaccess/models/instance_master"
	"orchestrator/libs/go/dbaccess/models/network_load_balancer_config"
	"orchestrator/libs/go/dbaccess/models/traffic_mirroring_cfg"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/terraform"
	dgcp "orchestrator/provision_service/api/deployment/resource/gcp"
	"orchestrator/provision_service/api/deployment/resource/gcp/instance"
	"orchestrator/provision_service/api/deployment/resource/gcp/instance_group/unmanaged"
	"orchestrator/provision_service/api/deployment/resource/gcp/load_balancer/external"
	"orchestrator/provision_service/api/deployment/resource/gcp/load_balancer/internallb"
	"orchestrator/provision_service/api/deployment/resource/gcp/network/firewall"
	"orchestrator/provision_service/api/deployment/resource/gcp/network/pbr"
	"orchestrator/provision_service/api/deployment/resource/gcp/network/private_connect_peering"
	"orchestrator/provision_service/definitions"
	pterraform "orchestrator/provision_service/terraform"
	"orchestrator/provision_service/utils"
	"reflect"
	"sort"
	"strconv"
	"strings"

	"go.panw.local/pangolin/clogger"
	"go.panw.local/provision/deployment"
)

const (
	MgmtNicName           string = "nic-mgmt"
	ClientIpNicName       string = "CLIENT_IP"
	egressIpAssignedToNat        = "ASSIGNED_TO_NAT" // egress ip value is 'ASSIGNED_TO_NAT' if a NAT GW is in front of MU
	DP2NicName            string = "dp2_nic"
	intOnrampNic          string = "int_onramp_nic"
)

var ignoreLabels = []string{"goog-dm", "goog-ccm"} // labels added external of orchestrator provisioning by GCP, to ignore
// labels added externally for instance to ignore, besides "goog-dm", the rest is added by resource tagging lambda
// see dervice_tag_info() in resource_tagger.py
var ignoreInstanceLabels = []string{
	"goog-dm", "goog-ccm", "name", "instancetype", "tenantid", "supertenantid", "tenantname", "computeregion", "theater",
	"environment", "cloudprovider", "resource_domain", "using_backbone", "interconnect_resource", "licensetype",
	"nativemachinetype", "instancesize", "contactinfo", "timestamp"}

// As some older VMs may not have these labels, ignore them during import to avoid flagging them as unexpected changes
var ignoreInstanceLabelsAtImport = []string{"app_threat_content_buckets", "av_content_buckets", "capacity_type", "customer"}

// attributes to ignore during import, this is to avoid flagging these known differences as unexpected changes
var ignoreInstanceAttributesAtImport = []string{"allow_stopping_for_update", "desired_status"}

// attributes to ignore when traffic mirroring is enabled
var ignoreInstanceAttributesWithTrafficMirroring = []string{"tags"}

// attributes to ignore during import, this is to avoid flagging these known differences as unexpected changes
var ignoreHealthCheckAttributesAtImport = []string{"http_health_check[0].request_path, https_health_check[0].request_path"}

func prepareAddrParams(vm *VMInstance, name, address string, explicitAddress bool) *Address {
	addr := &Address{}
	addr.Name = name
	addr.TFLocalName = pterraform.TFLocalName(addr.Name)
	addr.Address = address
	addr.Region = vm.Region
	addr.ExplicitAddress = explicitAddress
	return addr
}

func deriveEgressIpAddresses(egressIpList string, eLog *clogger.EventLogger) ([]string, error) {
	if utils.EmptyValue(egressIpList) {
		return nil, nil
	}

	var ips []string
	list := make(map[string]interface{})
	if err := json.Unmarshal([]byte(egressIpList), &list); err != nil {
		eLog.LogError("Error unmarshalling egress IP list: %v", err)
		return nil, err
	}
	for _, elem := range list {
		if ip, isStr := elem.(string); isStr {
			if ip != egressIpAssignedToNat {
				ips = append(ips, ip)
			}
		} else if ipList, isList := elem.([]interface{}); isList {
			for _, e := range ipList {
				if ip, isStr := e.(string); isStr {
					if ip != egressIpAssignedToNat {
						ips = append(ips, ip)
					}
				} else {
					eLog.LogError("Error unmarshalling egress IP list, unknown list element type '%v', "+
						"list=%v", reflect.TypeOf(e), ipList)
				}
			}
		} else {
			eLog.LogError("Error unmarshalling egress IP list, unknown element type '%v', "+
				"element=%v", reflect.TypeOf(elem), elem)
		}
	}
	return ips, nil
}

func deriveEgressIpv6Addresses(egressIpv6List string, eLog *clogger.EventLogger) (map[string]map[string]*ForwardingRule, error) {
	if utils.EmptyValue(egressIpv6List) {
		return nil, nil
	}

	m := make(map[string]string)
	if err := json.Unmarshal([]byte(egressIpv6List), &m); err != nil {
		eLog.LogError("Error unmarshalling egress IPV6 list: %v", err)
		return nil, err
	}
	tbl := make(map[string]map[string]*ForwardingRule)
	for region, subnet := range m {
		inner := make(map[string]*ForwardingRule)
		// set subnet here and add ForwardingRule later in VM preparation only for primary
		// for secondary, no ForwardingRule will be crated or associated
		inner[subnet] = nil
		tbl[region] = inner
	}
	return tbl, nil
}

func prepareNetIntfParams(project string, vm *VMInstance, name, network, subnet string, hasExternalIp bool,
	staticIp string, queueCount int64, dualStack bool, ignoreAliasIpRange bool, aliasIpRanges []AliasIpRange) (*NetworkInterface, error) {
	intf := &NetworkInterface{}
	intf.Name = name
	intf.Network = network
	intf.Subnet = subnet
	intf.HasExternalIp = hasExternalIp
	intf.StaticIp = staticIp
	intf.QueueCount = queueCount
	intf.DualStack = dualStack

	prj := project
	if vm.SharedMgmtVPCProj != "" &&
		(name == MgmtNicName || name == DP2NicName ||
			(name == ClientIpNicName && vm.IsSpInterconnect && !vm.HasDualEgress)) {
		prj = vm.SharedMgmtVPCProj
	}
	intf.Project = prj
	intf.NetworkUrl = fmt.Sprintf("https://www.googleapis.com/compute/v1/projects/%s/global/networks/%s",
		prj, network)
	intf.SubnetUrl = fmt.Sprintf("https://www.googleapis.com/compute/v1/projects/%s/regions/%s/subnetworks/%s",
		prj, vm.Region, subnet)
	if queueCount > 0 {
		intf.ExplicitQueueCount = true
	}
	if vm.IsPrimary && hasExternalIp {
		/*
			In general for PUPI IP's no need of the access configuration
			In case of NAT nodes in interconnect region, if it has dual egress then :
				nic0 will be having GCP public ip so , still access config is required.
				nic1 which has dp2 nic name will have PUPI ip, avoid access config changes for DP2 nic.
		*/
		if (vm.IsSpInterconnect &&
			!(name == ClientIpNicName && vm.HasDualEgress)) ||
			name == DP2NicName {
			intf.IgnoreChangesOnAccessConfig = true
		} else {
			var natIpOrRef string
			var explicitNatOrRef bool

			if staticIp == "" && !vm.CleanIpTag {
				addr := vm.Addrs[ApiAssigned]

				if addr == nil || len(addr) == 0 {
					return nil, fmt.Errorf("error preparing Network Interface params: corresponding API Assigned Addr missing")
				}
				intf.Addr = addr[0]
				natIpOrRef = fmt.Sprintf("google_compute_address.%s.address", addr[0].TFLocalName)
				explicitNatOrRef = true
			} else if vm.CleanIpTag && staticIp != "" {
				// FIXME: Keeping condition as-is to match the DM-path code, however, will staticIP ever be empty
				//        for CleanIP prj (redundant check)?
				addr := vm.Addrs[CleanIp]

				if addr == nil || len(addr) == 0 {
					return nil, fmt.Errorf("error preparing Network Interface params: corresponding Clean IP Addr missing")
				}
				intf.Addr = addr[0]
				natIpOrRef = fmt.Sprintf("google_compute_address.%s.address", addr[0].TFLocalName)
				explicitNatOrRef = true
			} else {
				// This covers the case that static IP is assigned in a non-clean-IP project.
				natIpOrRef = fmt.Sprintf("\"%s\"", staticIp)
			}
			intf.NatIpOrRef = natIpOrRef
			// For non-RN/SC:
			//     Always explicitly add accessConfig.
			// For RN/SC:
			//     Non-CleanIP: explicit access_config configuration only for NAT-IP (not for static IP).
			//     CleanIP: explicit access_config configuration only for clean-IP
			//   Although Saas_agent will set it up, need to be explicit here so Deployment API will add
			//   the corresponding "google_compute_address" resource in TF configuration.
			// FIXME: Keeping the condition (staticIP == "") from the DM-path code to cover CleanIP with an empty staticIP.
			//        Will there be such a case (redundant check)?
			intf.ExplicitNatIpOrRef = !vm.HACluster || explicitNatOrRef || staticIp == ""
			if vm.HACluster {
				// For RN/SC, accessConfig is managed by the FW cluster itself.
				// We manage access config if it is a no passive instance
				intf.IgnoreChangesOnAccessConfig = true
			}
		}
	}
	if ignoreAliasIpRange {
		intf.IgnoreChangesOnAliasIpRange = true
	}
	intf.AliasIpRanges = aliasIpRanges

	return intf, nil
}

func prepareTargetInstanceParams(vm *VMInstance) *TargetInstance {
	ti := &TargetInstance{}
	ti.Name = fmt.Sprintf("target-instance-%s", vm.Name)
	ti.Zone = vm.Zone
	ti.Instance = vm
	ti.TFLocalName = vm.TFLocalName
	return ti
}

func prepareForwardingRuleParams(region string, addr *Address, addrStr, protocol string, allPorts bool,
	target, dependsOn *TargetInstance) (*ForwardingRule, error) {
	if target == nil {
		return nil, fmt.Errorf("error preparing Forwarding Rule params: corresponding Target Instance missing")
	}

	fwdRule := &ForwardingRule{}
	if addr != nil {
		fwdRule.IpAddressOrRef = fmt.Sprintf("google_compute_address.%s.id", addr.TFLocalName)
		fwdRule.Addr = addr
	} else {
		fwdRule.IpAddressOrRef = fmt.Sprintf("\"%s\"", addrStr)
	}
	fwdRule.Name = fmt.Sprintf("forwarding-rule-%s-%s", strings.ReplaceAll(addrStr, ".", "-"),
		strings.ReplaceAll(strings.ToLower(protocol), "_", ""))
	fwdRule.Region = region
	fwdRule.IpProtocol = protocol
	fwdRule.AllPorts = allPorts
	fwdRule.Target = target
	fwdRule.DependsOn = dependsOn
	fwdRule.TFLocalName = pterraform.TFLocalName(fwdRule.Name)
	return fwdRule, nil
}

func prepareV6ForwardingRuleParams(id int64, project, region, subnet string, target, dependsOn *TargetInstance) (*ForwardingRule, error) {
	if target == nil {
		return nil, fmt.Errorf("error preparing Forwarding Rule params: corresponding Target Instance missing")
	}

	fwdRule := &ForwardingRule{}
	fwdRule.Name = fmt.Sprintf("fwd-rule-%v-%v-all", id, strings.ReplaceAll(subnet, ".", "-"))
	// Existing code in fwtemplate.py cap the name to 63 characters.
	nameMaxLen := 63
	if len(fwdRule.Name) > nameMaxLen {
		fwdRule.Name = fwdRule.Name[:nameMaxLen]
	}
	fwdRule.Name = strings.TrimRight(fwdRule.Name, "-")
	fwdRule.Region = region
	fwdRule.Subnet = subnet
	fwdRule.IPVersion = IPV6
	fwdRule.IpProtocol = L3_DEFAULT
	fwdRule.AllPorts = true
	fwdRule.Target = target
	fwdRule.DependsOn = dependsOn
	fwdRule.SubnetUrl = fmt.Sprintf("https://www.googleapis.com/compute/v1/projects/%s/regions/%s/subnetworks/%s",
		project, region, subnet)

	fwdRule.TFLocalName = pterraform.TFLocalName(fwdRule.Name)
	return fwdRule, nil
}

func PrepareVmParams(project string, custMasterEntry *cust_master.Row, instMasterEntries []*instance_master.Row,
	nlbConfigEntry *network_load_balancer_config.Row, eLog *clogger.EventLogger) (map[int64][]*VMInstance, []*NLB, []*FirewallRule, error) {
	var err error
	vms := make(map[int64][]*VMInstance) // map[ClusterId]*VMInstance

	nlb, svcilb, intOnrampILB := &NLB{Cfg: nlbConfigEntry}, &NLB{Cfg: nlbConfigEntry}, &NLB{Cfg: nlbConfigEntry}
	nlb.ZonalInstanceMap, svcilb.ZonalInstanceMap = make(map[string][]*VMInstance), make(map[string][]*VMInstance)
	intOnrampILB.ZonalInstanceMap = make(map[string][]*VMInstance)
	hasNlb, hasIlb := false, false
	var retnlb []*NLB
	var retNgpaFirewallRules []*FirewallRule
	ngpaFirewallRuleMap := make(map[string]*FirewallRule)

	coloILBResources := make(map[string]*NLB)
	hasColoILB := false
	//hasIlb := false
	clustersToBeDeleted := make(map[int64]bool)
	for _, myRow := range instMasterEntries {
		if myRow.McwEnabled || myRow.IsSaseFabricSpn {
			eLog.LogInfo("Skipping VM config for %v (McwEnabled or ISSaseFabricSpn)", myRow.Name.String())
			continue
		} else if len(myRow.SaltProfile.String()) == 0 {
			eLog.LogInfo("Skipping VM config for %+v (Empty SaltProfile)", myRow)
			continue
		}
		eLog.LogInfo("Updating VM config for %+v", myRow)
		var saltProfile instance_master.SaltProfile
		err = json.Unmarshal([]byte(myRow.SaltProfile), &saltProfile)
		if err != nil {
			eLog.LogError("Error unmarshalling salt profile: %v, row: %+v", err, myRow)
			return nil, nil, nil, err
		}
		vm := &VMInstance{}
		vm.TenantId = strconv.FormatInt(myRow.AcctID.Int64(), 10)
		vm.ClusterId = myRow.ClusterID.Int64()
		// If this cluster does not need to be part of deployment, add it to clustersToBeDeleted.
		if vm.ClusterId == myRow.DeleteDeployment.Int64() {
			clustersToBeDeleted[vm.ClusterId] = true
		}
		vm.Name = saltProfile.InstName
		vm.Id = saltProfile.InstanceId
		vm.Zone = saltProfile.Zone
		vm.ZonesList = saltProfile.ZonesList
		vm.NodeType = int(myRow.NodeType.Int64())
		vm.AltNodeType = int(myRow.AltNodeType.Int64())
		vm.NoPassiveInstance = myRow.NoPassiveInstance.Bool()

		var isColoSCBehingILB = false
		if saltProfile.ColoInterfaceSupport {
			vm.IsColoSC = true
			if myRow.IsInstanceBehindNlb.Bool() {
				isColoSCBehingILB = true
				myRow.IsInstanceBehindNlb = false //reset this flag to prevent further nlb code getting kicked in for coloSC.
			}
		}

		switch vm.NodeType {
		case terraform.NodeTypeRemoteNetwork, terraform.NodeTypeServiceConnection:
			vm.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("firewall%v", vm.Id))
		case terraform.NodeTypeNAT:
			vm.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("nat%v", vm.Id))
		case terraform.NodeTypeNLB:
			// Glean and store NLB parameters.
			vm.Region = saltProfile.RegionName
			vm.CleanIpTag = saltProfile.CleanIpTag
			vm.IsPrimary = true
			vm.HasDpNic = saltProfile.DPNetwork != ""
			// Following fields are required in NLB nodes for sp interconnect region additionally.
			vm.IsSpInterconnect = saltProfile.IsUsingSpInterconnect
			vm.SharedMgmtVPCProj = saltProfile.SharedMgmtVPCProj
			if vm.HasDpNic {
				vm.NetIntfs = append(vm.NetIntfs, &NetworkInterface{Network: saltProfile.DPNetwork, Subnet: saltProfile.DPSubnet})
			}
			gleanNlbParameters(nlb, vm, eLog)

			nlb.Name = strings.ToLower(strings.ReplaceAll(myRow.Name.String(), "_", "-"))
			nlb.PublicIp = myRow.PublicIp.String()
			nlb.EgressIpList = myRow.EgressIpList.String()
			nlb.ComputeRegionIdx = strconv.FormatInt(myRow.ComputeRegionIdx.Int64(), 10)
			nlb.IngressIpReduction = saltProfile.PerformIngressIPReduction
			// Derive EgressIp values, in the case of DM, these are derived in fwtemplate.py.
			nlb.EgressIpsSansNat, err = deriveEgressIpAddresses(nlb.EgressIpList, eLog)
			if err != nil {
				eLog.LogError("Error deriving Egress IP addresses: %v, row: %+v", err, myRow)
				return nil, nil, nil, err
			}
			hasNlb = true
			eLog.LogInfo("NLB exists in this region: NLB Name=%v, IP=%v Egress ip list=%v(%v)",
				nlb.Name, nlb.PublicIp, nlb.EgressIpList, nlb.EgressIpsSansNat)
			continue
		case terraform.NodeTypeSvcILB:
			switch vm.AltNodeType {
			case terraform.NodeTypeGPGateway:
				svcilb.Name = strings.ToLower(strings.ReplaceAll(myRow.Name.String(), "_", "-"))
				svcilb.CfgSource = vm
				hasIlb = true
				svcilb.Region = saltProfile.RegionName
				vm.HasDpNic = saltProfile.DPNetwork != ""
				vm.SharedMgmtVPCProj = saltProfile.SharedMgmtVPCProj
				vm.Sase5gEnabled = saltProfile.Is5gEnabled
				vm.MasqueEnabled = saltProfile.MasqueEnabled
				if vm.HasDpNic {
					// 5G_INFO: Different NIC: If 5G has to use different NIC modify here.
					svcilb.Network = saltProfile.DPNetwork
					svcilb.Subnet = saltProfile.DPSubnet
					eLog.LogInfo("The subnet details for NodeTypeSvcILB GW are: '%v' , '%v'", svcilb.Network, svcilb.Subnet)
				}
				gleanNlbParameters(svcilb, vm, eLog)
				continue
			case terraform.NodeTypeRemoteNetwork:
				intOnrampILB.Name = strings.ToLower(strings.ReplaceAll(myRow.Name.String(), "_", "-"))
				intOnrampILB.CfgSource = vm
				hasIlb = true
				intOnrampILB.Region = saltProfile.RegionName
				vm.HasDpNic = saltProfile.DPNetwork != ""
				vm.SharedMgmtVPCProj = saltProfile.SharedMgmtVPCProj
				vm.IsSpInterconnect = saltProfile.IsUsingSpInterconnect
				vm.IsInterconnectOnramp = true
				if vm.HasDpNic {
					// 5G_INFO: Different NIC: If 5G has to use different NIC modify here.
					intOnrampILB.Network = saltProfile.DPNetwork
					intOnrampILB.Subnet = saltProfile.DPSubnet
					eLog.LogInfo("The subnet details for NodeTypeSvcILB RN are: '%v' , '%v'", intOnrampILB.Network, intOnrampILB.Subnet)
				}
				gleanNlbParameters(intOnrampILB, vm, eLog)
				continue
			}
		case terraform.NodeTypeProbeVM:
			vm.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("probevm%v", vm.Id))
		default:
			vm.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("gp%v", vm.Id))
		}

		if cluster := vms[vm.ClusterId]; cluster != nil {
			vm.Primary = cluster[0]
		}

		//hasDp2Nic := false
		// CYR-43917: For 5G use case, MMS traffic has to be loop-backed and all other traffic should be routed through DP2.
		// Currently only NAT node to have DP2 nic, in future when other nodes also need DP2 nic, this logic needs to be generalized.
		if (vm.NodeType == terraform.NodeTypeNAT) && saltProfile.DP2Subnet != "" {
			eLog.LogInfo("Dual Egress: Setting hasDp2Nic to true for VM %v", vm.Name)
			vm.HasDualEgress = true
		}

		vm.IsPrimary = (vm.Primary == nil)
		vm.HACluster = !vm.NoPassiveInstance && (vm.NodeType == terraform.NodeTypeRemoteNetwork || vm.NodeType == terraform.NodeTypeServiceConnection)

		vm.Image = saltProfile.ImageName
		vm.IsBehindNlb = myRow.IsInstanceBehindNlb.Bool()
		vm.HasNatInstance = myRow.HasNatInstance.Int64() != 0
		vm.PublicIp = myRow.PublicIp.String()
		vm.IsInterconnectOnramp = saltProfile.IsInterconnectOnramp
		if vm.IsPrimary {
			vm.Region = saltProfile.RegionName
			vm.ImageProject = saltProfile.ImageProject
			vm.SharedMgmtVPCProj = saltProfile.SharedMgmtVPCProj
			vm.ServiceAccount = saltProfile.SvcAcct
			vm.CleanIpTag = saltProfile.CleanIpTag
			vm.IsSpInterconnect = saltProfile.IsUsingSpInterconnect
			// If this is a GW instance that is behind NLB or has a NAT GW in front, use "interface_ip_list".
			if vm.NodeType == terraform.NodeTypeGPGateway && (vm.IsBehindNlb || vm.HasNatInstance) {
				eLog.LogInfo("Setting interface IP list (%v) as egress IP list (%v): "+
					"nodeType=%v, isBehindNlb=%v, hasNatInstance=%v",
					myRow.InterfaceIpList.String(), myRow.EgressIpList.String(), vm.NodeType, vm.IsBehindNlb,
					vm.HasNatInstance)
				vm.EgressIpList = myRow.InterfaceIpList.String()
			} else {
				vm.EgressIpList = myRow.EgressIpList.String()
			}
			vm.EgressIpv6ListSubnet = myRow.EgressIpv6ListSubnet.String()
			vm.HasExternalIpv6 = saltProfile.HasExternalIPv6
			vm.HasExternalIpv6Dp2 = saltProfile.HasExternalIPv6Dp2
			if saltProfile.ColoInterfaceSupport {
				if p := saltProfile.MinCpuPlatform; len(p) > 0 {
					vm.MinCpuPlatform = p
				}
				vm.NetworkPerformanceConfig = &NetworkPerformanceConfig{TotalEgressBandwidthTier: "TIER_1"}
			}
		} else {
			vm.Region = vm.Primary.Region
			vm.ImageProject = vm.Primary.ImageProject
			vm.SharedMgmtVPCProj = vm.Primary.SharedMgmtVPCProj
			vm.ServiceAccount = vm.Primary.ServiceAccount
			vm.CleanIpTag = vm.Primary.CleanIpTag
			vm.IsSpInterconnect = vm.Primary.IsSpInterconnect
			vm.EgressIpList = vm.Primary.EgressIpList
			vm.EgressIpv6ListSubnet = vm.Primary.EgressIpv6ListSubnet
			vm.HasExternalIpv6 = vm.Primary.HasExternalIpv6
			vm.HasExternalIpv6Dp2 = vm.Primary.HasExternalIpv6Dp2
			vm.MinCpuPlatform = vm.Primary.MinCpuPlatform
			vm.NetworkPerformanceConfig = vm.Primary.NetworkPerformanceConfig
		}

		// If the instance is behind an NLB, add the instance to behindNlb to be moved behind NLB.
		// Even when the "is_instance_behind_nlb" flag is set for the instance, we will move the instance
		// into a backend_set only if the instance does not have "upgrade_creation" flag set. If set,
		// the instance will remain a standalone instance and will not be part of any backend set of the NLB.
		if vm.IsBehindNlb {
			if myRow.UpgradeCreation.Int64() == 1 {
				eLog.LogInfo("Instance '%v' still has IsUpgradeCreation flag set; will not move behind NLB yet",
					vm.Name)
			} else {
				eLog.LogInfo("Instance '%v' will be moved behind NLB via a UMIG in Zone %s",
					vm.Name, vm.Zone)
				if vm.NodeType == terraform.NodeTypeGPGateway {
					nlb.ZonalInstanceMap[vm.Zone] = append(nlb.ZonalInstanceMap[vm.Zone], vm)
					if saltProfile.Is5gEnabled {
						// If the VM is 5g node then add this to SVC_ILB zone map.
						svcilb.ZonalInstanceMap[vm.Zone] = append(nlb.ZonalInstanceMap[vm.Zone], vm)
					}
				} else if vm.NodeType == terraform.NodeTypeRemoteNetwork {
					intOnrampILB.ZonalInstanceMap[vm.Zone] = append(intOnrampILB.ZonalInstanceMap[vm.Zone], vm)
					intOnrampILB.UMIGNetwork = saltProfile.DPNetwork
				}
			}
		}

		// Derive EgressIp values, in the case of DM, these are derived in fwtemplate.py.
		vm.EgressIpsSansNat, err = deriveEgressIpAddresses(vm.EgressIpList, eLog)
		if err != nil {
			eLog.LogError("Error deriving Egress IP addresses: %v, row: %+v", err, myRow)
			return nil, nil, nil, err
		}

		if vm.HasDualEgress {
			dp2EgressIpList := myRow.EgressIpListDP2.String()
			vm.EgressIpsSansNatDP2, err = deriveEgressIpAddresses(dp2EgressIpList, eLog)
			if err != nil {
				eLog.LogError("Error deriving Egress IP addresses for DP2: %v, row: %+v", err, myRow)
				return nil, nil, nil, err
			}
		}

		vm.EgressIpv6Subnets, err = deriveEgressIpv6Addresses(vm.EgressIpv6ListSubnet, eLog)
		if err != nil {
			eLog.LogError("Error deriving Egress IPv6 addresses: %v, row: %+v", err, myRow)
		}
		vm.InboundAccessList = myRow.InboundAccessIpList.String()
		vm.MachineType = saltProfile.InstType
		vm.AllowedAsTarget = true
		if vm.HACluster && myRow.HAState.Int64() == 1 {
			vm.AllowedAsTarget = false
		}
		if vm.IsPrimary {
			vm.CapacityType = saltProfile.PrimaryCapacityType
		} else {
			vm.CapacityType = saltProfile.SecondaryCapacityType
		}
		vm.UserData = saltProfile.UserData
		// Derive metadata from userData
		metadata := func(key, value string) string {
			metadataFmt := "%v = \"%v\""
			return fmt.Sprintf(metadataFmt, strings.TrimSpace(key), strings.TrimSpace(value))
		}
		for _, kv := range strings.Split(vm.UserData, ",") {
			key, value, found := strings.Cut(kv, "=")
			if !found {
				eLog.LogWarn("Unexpected user data, missing key-value separator '=': kv=%s, userData=%s",
					kv, vm.UserData)
				continue
			}
			vm.Metadata = append(vm.Metadata, metadata(key, value))
		}

		// Derive inbound IPs and allowed FW rules,
		// in the case of DM, it is derived in gcp_instance_param_mgmt.py.
		if vm.IsPrimary && !vm.IsSpInterconnect {
			if !utils.EmptyValue(vm.InboundAccessList) {
				buf, err := utils.UncompressBase64String(vm.InboundAccessList)
				if err != nil {
					eLog.LogError("Error decoding inbound access list: %v, row: %+v", err, myRow)
					return nil, nil, nil, err
				}
				type InboundAccessProtocol struct {
					Name string   `json:"name"`
					Port []string `json:"port"`
				}
				type InboundAccess struct {
					PublicIp string                  `json:"public_ip"`
					Protocol []InboundAccessProtocol `json:"protocol"`
				}
				var inboundList []InboundAccess
				err = json.Unmarshal(buf, &inboundList)
				if err != nil {
					eLog.LogError("Error unmarshaling inbound access list: %v, data: %v, row: %+v", err, buf, myRow)
					return nil, nil, nil, err
				}

				vm.InboundIps = make(map[string][]string)
				vm.AllowedFwRules = make(map[string]*definitions.ProtocolInfoProps)
				for _, item := range inboundList {
					if ip := item.PublicIp; ip != "" {
						for _, proto := range item.Protocol {
							if name := proto.Name; name != "" {
								protoInfo := vm.AllowedFwRules[name]
								for i, port := range proto.Port {
									if i == 0 {
										vm.InboundIps[ip] = append(vm.InboundIps[ip], name)
									}
									if protoInfo == nil {
										vm.AllowedFwRules[name] = &definitions.ProtocolInfoProps{IpProtocol: name}
										protoInfo = vm.AllowedFwRules[name]
									}
									protoInfo.Ports = append(protoInfo.Ports, port)
								}
							}
						}
					}
				}
				// sort InboundIpKeys to generate a consistent TF configuration
				for ip := range vm.InboundIps {
					vm.InboundIpKeys = append(vm.InboundIpKeys, ip)
				}
				sort.Slice(vm.InboundIpKeys, func(i, j int) bool {
					return bytes.Compare(net.ParseIP(vm.InboundIpKeys[i]), net.ParseIP(vm.InboundIpKeys[j])) < 0
				})
				// sort AllowedFwRuleKeys to generate a consistent TF configuration
				for proto := range vm.AllowedFwRules {
					vm.AllowedFwRuleKeys = append(vm.AllowedFwRuleKeys, proto)
				}
				sort.Strings(vm.AllowedFwRuleKeys)
				// sort and dedupe ProtoInfoProps.Ports to generate a consistent TF configuration
				for _, protoInfo := range vm.AllowedFwRules {
					sort.Strings(protoInfo.Ports)
					if l := len(protoInfo.Ports); l > 1 {
						uniqPtr := 0
						for i := 1; i < l; i++ {
							// compare current item with the item under the unique ptr,
							// if different, add it to the right
							curr := protoInfo.Ports[i]
							if protoInfo.Ports[uniqPtr] != curr {
								uniqPtr++
								protoInfo.Ports[uniqPtr] = curr
							}
						}
						protoInfo.Ports = protoInfo.Ports[:uniqPtr+1]
					}
				}
			}
		}

		// Static IP (compute address)
		// In the case of DM, it is processed in fwtemplate.py.
		vm.Addrs = make(map[AddressType][]*Address)
		vm.HasDpNic = saltProfile.DPNetwork != ""
		hasServiceNic := saltProfile.ServiceNetwork != ""
		if vm.IsPrimary {
			// Only if not SP Interconnect.
			// For SP Interconnect no static IP config exists. Using PUPI IP's as alias IP's.
			if vm.HasDpNic {
				if saltProfile.StaticIp == "" && !saltProfile.CleanIpTag {
					addr := prepareAddrParams(vm, vm.Name+"-address", "", false)
					vm.Addrs[ApiAssigned] = append(vm.Addrs[ApiAssigned], addr)
				} else if saltProfile.CleanIpTag && saltProfile.StaticIp != "" {
					addrName := fmt.Sprintf("%v-%v", CleanIp, strings.ReplaceAll(saltProfile.StaticIp, ".", "-"))
					addr := prepareAddrParams(vm, addrName, saltProfile.StaticIp, true)
					vm.Addrs[CleanIp] = append(vm.Addrs[CleanIp], addr)
				}
			}

			if vm.CleanIpTag {
				// Skip adding compute addresses for egress IP list if the instance is behind NLB.
				if !vm.IsBehindNlb {
					for _, ip := range vm.EgressIpsSansNat {
						addrName := fmt.Sprintf("%v-%v", Egress, strings.ReplaceAll(ip, ".", "-"))
						addr := prepareAddrParams(vm, addrName, ip, true)
						vm.Addrs[Egress] = append(vm.Addrs[Egress], addr)
					}
				}
				for _, ip := range vm.InboundIpKeys {
					addrName := fmt.Sprintf("%v-%v", Inbound, strings.ReplaceAll(ip, ".", "-"))
					addr := prepareAddrParams(vm, addrName, ip, true)
					vm.Addrs[Inbound] = append(vm.Addrs[Inbound], addr)
				}
			}
		}

		// Configuring the DP2 (Second Data Plane) network interface.
		// Not needed now since DP2 is for PUPI ip and no configuration is required for it.
		/*if vm.IsPrimary && vm.HasDualEgress {
			if saltProfile.PublicIpDp2 == "" && !saltProfile.CleanIpTag {
				// For non-clean IP projects without a specified IP, use a GCP-provided address.
				addr := prepareAddrParams(vm, vm.Name+"-address", "", false)
				vm.Addrs[DP2ApiAssigned] = append(vm.Addrs[DP2ApiAssigned], addr)
			} else if saltProfile.CleanIpTag && saltProfile.PublicIpDp2 != "" {
				// For clean IP projects with a specified IP, use the provided IP address.
				addrName := fmt.Sprintf("%v-%v", Dp2CleanIp, strings.ReplaceAll(saltProfile.StaticIp, ".", "-"))
				addr := prepareAddrParams(vm, addrName, saltProfile.PublicIpDp2, true)
				vm.Addrs[Dp2CleanIp] = append(vm.Addrs[Dp2CleanIp], addr)
			}

			if vm.CleanIpTag {
				// Handle egress IPs for DP2 in clean IP projects.
				if !vm.IsBehindNlb {
					// Add compute addresses for egress IPs only if the instance is not behind a Network Load Balancer.
					for _, ip := range vm.EgressIpsSansNatDP2 {
						addrName := fmt.Sprintf("%v-%v", DP2Egress, strings.ReplaceAll(ip, ".", "-"))
						addr := prepareAddrParams(vm, addrName, ip, true)
						vm.Addrs[DP2Egress] = append(vm.Addrs[DP2Egress], addr)
					}
				}
			}
		}*/

		// Derive target instance
		if (!vm.IsSpInterconnect || vm.HasDualEgress) &&
			(len(vm.EgressIpsSansNat) > 0 && !vm.IsBehindNlb) || len(vm.EgressIpv6Subnets) > 0 ||
			len(vm.InboundIpKeys) > 0 || (!vm.IsPrimary && len(vm.Primary.InboundIpKeys) > 0) {
			// Note it does not explicitly check for !IsSpInterconnect here since there shouldn't be
			// any egress nor inbound IP
			vm.TargetInstance = prepareTargetInstanceParams(vm)
		}

		// Derive forwarding rules associated with egress IP and inbound IP
		// In the case of DM, it is processed both in get_gcp_cust_region_instance_params() and fwtemplate.py.
		if vm.IsPrimary && (!vm.IsSpInterconnect || vm.HasDualEgress) {
			if !vm.IsBehindNlb && len(vm.EgressIpsSansNat) > 0 {
				fwdRuleProtos := []IPProtocol{TCP, UDP, ICMP}
				if vm.NodeType == terraform.NodeTypeGPGateway {
					if custMasterEntry.Fwdrulesall == "MU" {
						fwdRuleProtos = []IPProtocol{L3_DEFAULT, TCP, UDP, ICMP}
					} else if custMasterEntry.L3FwdrulesMU {
						fwdRuleProtos = []IPProtocol{L3_DEFAULT}
					}
				} else if vm.NodeType == terraform.NodeTypeRemoteNetwork {
					if custMasterEntry.Fwdrulesall == "RN" {
						fwdRuleProtos = []IPProtocol{L3_DEFAULT, TCP, UDP, ICMP}
					} else if custMasterEntry.L3FwdrulesRN {
						fwdRuleProtos = []IPProtocol{L3_DEFAULT}
					}
				}
				for _, proto := range fwdRuleProtos {
					allPorts := false
					if proto == L3_DEFAULT {
						allPorts = true
					}
					if vm.CleanIpTag {
						egressAddr := vm.Addrs[Egress]
						// TODO: no need for NAT of egress PUPI ip's
						/*if hasDp2Nic && vm.IsSpInterconnect {
							// in case of DP2 only the dp2 ips need to be added to the forwarding rules. DP1 ips are PUPI
							egressAddr = vm.Addrs[DP2Egress]
						}*/

						for _, addr := range egressAddr {
							rule, err := prepareForwardingRuleParams(vm.Region, addr, addr.Address, string(proto), allPorts, vm.TargetInstance, nil)
							if err != nil {
								eLog.LogError("Error preparing forwarding rule: %v, proto: %v, addr: %+v, row: %+v",
									err, proto, addr, myRow)
								return nil, nil, nil, err
							}
							vm.L3FwdRules = append(vm.L3FwdRules, rule)
						}
					} else {
						egressIpsNat := vm.EgressIpsSansNat
						// TODO: no need for NAT of egress PUPI ip's
						/*if hasDp2Nic && vm.IsSpInterconnect {
							egressIpsNat = vm.EgressIpsSansNatDP2
						}*/
						for _, addrStr := range egressIpsNat {
							rule, err := prepareForwardingRuleParams(vm.Region, nil, addrStr, string(proto), allPorts, vm.TargetInstance, nil)
							if err != nil {
								eLog.LogError("Error preparing forwarding rule: %v, proto: %v, addrStr: %+v, row: %+v",
									err, proto, addrStr, myRow)
								return nil, nil, nil, err
							}
							vm.L3FwdRules = append(vm.L3FwdRules, rule)
						}
					}
				}
			}

			for _, inner := range vm.EgressIpv6Subnets {
				for subnet, _ := range inner {
					rule, err := prepareV6ForwardingRuleParams(vm.Id, project, vm.Region, subnet, vm.TargetInstance, nil)
					if err != nil {
						eLog.LogError("Error preparing V6 forwarding rule: %v, subnet: %v, row: %+v", err, subnet, myRow)
						return nil, nil, nil, err
					}
					inner[subnet] = rule
					vm.L3FwdRules = append(vm.L3FwdRules, rule)
				}
			}

			for _, ip := range vm.InboundIpKeys {
				if vm.CleanIpTag {
					var target *Address
					for _, addr := range vm.Addrs[Inbound] {
						if ip == addr.Address {
							target = addr
						}
					}
					if target == nil {
						err = fmt.Errorf("error preparing Forwarding Rule params: corresponding Inbound Address missing")
						eLog.LogError("Error preparing forwarding rule: %v, ip: %v, row: %+v", err, ip, myRow)
						return nil, nil, nil, err
					}
					for _, proto := range vm.InboundIps[ip] {
						rule, err := prepareForwardingRuleParams(vm.Region, target, target.Address, proto, false, vm.TargetInstance, nil)
						if err != nil {
							eLog.LogError("Error preparing forwarding rule: %v, proto: %v, addr: %+v, row: %+v",
								err, proto, target, myRow)
							return nil, nil, nil, err
						}
						vm.L3FwdRules = append(vm.L3FwdRules, rule)
					}
				} else {
					for _, proto := range vm.InboundIps[ip] {
						rule, err := prepareForwardingRuleParams(vm.Region, nil, ip, proto, false, vm.TargetInstance, nil)
						if err != nil {
							eLog.LogError("Error preparing forwarding rule: %v, proto: %v, addrStr: %+v, row: %+v",
								err, proto, ip, myRow)
							return nil, nil, nil, err
						}
						vm.L3FwdRules = append(vm.L3FwdRules, rule)
					}
				}
			}
		} else if !vm.IsPrimary && len(vm.Primary.L3FwdRules) > 0 {
			// For VMs in an HA cluster, if vm.Primary.AllowedAsTarget is false, fix the fwd rules to have itself as
			// the target and the primary as dependsOn. Otherwise, simply add itself as the dependsOn
			if vm.TargetInstance == nil {
				err = fmt.Errorf("error preparing Forwarding Rule params: secondary Target Instance missing")
				eLog.LogError("Error preparing forwarding rule: %v, primary: %+v, row: %+v", err, vm.Primary, myRow)
				return nil, nil, nil, err
			}
			for _, rule := range vm.Primary.L3FwdRules {
				if vm.Primary.AllowedAsTarget {
					rule.DependsOn = vm.TargetInstance
				} else {
					rule.DependsOn, rule.Target = rule.Target, vm.TargetInstance
				}
			}
		}

		// Network interfaces
		find := func(intfName string) *NetworkInterface {
			for _, pIntf := range vm.Primary.NetIntfs {
				if intfName == pIntf.Name {
					return pIntf
				}
			}
			return nil
		}
		dpdkQcount := int64(0)
		if vm.NodeType != terraform.NodeTypeProbeVM {
			if vm.IsPrimary {
				dpdkQcount = saltProfile.PrimaryDpdkQcount
			} else {
				// For VMs in an HA cluster, both primary and secondary uses SecondaryDpdkQcount. This follows the
				// logic in generate_template(). Fix the dpdkQcount in the Primary net interfaces here.
				dpdkQcount = saltProfile.SecondaryDpdkQcount
				for _, intf := range vm.Primary.NetIntfs {
					intf.QueueCount = dpdkQcount
					if dpdkQcount == 0 {
						intf.ExplicitQueueCount = false
					} else {
						intf.ExplicitQueueCount = true
					}
				}
			}
		}
		if vm.HasDpNic {
			// Follow the same logic as existing DM code to use "CLIENT_IP" as DP interface name
			// instead of the DPInterfaceName in the salt profile.
			if vm.IsPrimary {
				vm.DpProject = project
				if saltProfile.IsDatapathOnSharedVpc && saltProfile.DPProjectOverride != "" {
					vm.DpProject = saltProfile.DPProjectOverride
				}
			} else {
				vm.DpProject = vm.Primary.DpProject
			}
			// Colo case, do not ignore alias IP, add alias IP ranges to dp interface
			ignoreAliasIp := !saltProfile.ColoInterfaceSupport
			aliasIpRanges := []AliasIpRange(nil)
			// only add to primary instance
			if saltProfile.ColoInterfaceSupport && vm.IsPrimary {
				aliasIpRanges = []AliasIpRange{{
					IpCidrRange:         "/32",
					SubnetworkRangeName: "",
				}}
			}
			dpIntf, err := prepareNetIntfParams(vm.DpProject, vm,
				ClientIpNicName, saltProfile.DPNetwork, saltProfile.DPSubnet,
				saltProfile.DPHasExternalIP, saltProfile.StaticIp, dpdkQcount, vm.HasExternalIpv6, ignoreAliasIp, aliasIpRanges)
			if err != nil {
				eLog.LogError("Error preparing DP Network Interface: %v, row: %+v", err, myRow)
				return nil, nil, nil, err
			}
			// In the case FW controlling AccessConfig of DP intf, set IgnoreChangesOnAccessConfig
			// on both nodes of the cluster.
			if !vm.IsPrimary {
				pIntf := find(dpIntf.Name)
				dpIntf.IgnoreChangesOnAccessConfig = pIntf.IgnoreChangesOnAccessConfig
			}
			vm.NetIntfs = append(vm.NetIntfs, dpIntf)
		}
		// All instance types need a mgmt interface.
		mgmtVpcPrj := project
		if vm.SharedMgmtVPCProj != "" {
			mgmtVpcPrj = vm.SharedMgmtVPCProj
		}
		mgmtIntf, err := prepareNetIntfParams(mgmtVpcPrj, vm,
			saltProfile.MgmtInterfaceName, saltProfile.MgmtNetwork, saltProfile.MgmtSubnet,
			saltProfile.MgmtHasExternalIP, "", 0, false, false, nil)
		if err != nil {
			eLog.LogError("Error preparing MGMT Network Interface: %v, row: %+v", err, myRow)
			return nil, nil, nil, err
		}
		vm.NetIntfs = append(vm.NetIntfs, mgmtIntf)
		if saltProfile.HAInterfaceName != "" {
			hasLimit, err := pterraform.GcpInstanceHasNicLimit(vm.MachineType)
			if err != nil {
				eLog.LogError("Error invoking GcpInstanceHasNicLimit: %v, machineType=%v", err, vm.MachineType)
				return nil, nil, nil, err
			}
			if hasLimit {
				eLog.LogWarn("GCP instance of machine type '%v' has NIC limit, skipping HA intf", vm.MachineType)
			} else {
				haIntf, err := prepareNetIntfParams(project, vm,
					saltProfile.HAInterfaceName, saltProfile.HANetwork, saltProfile.HASubnet,
					saltProfile.HAHasExternalIP, "", 0, false, false, nil)
				if err != nil {
					eLog.LogError("Error preparing HA Network Interface: %v, row: %+v", err, myRow)
					return nil, nil, nil, err
				}
				vm.NetIntfs = append(vm.NetIntfs, haIntf)
			}
		}
		if saltProfile.ColoInterfaceSupport {
			coloIntf, err := prepareNetIntfParams(project, vm, saltProfile.ColoInterfaceName, saltProfile.ColoNetwork,
				saltProfile.ColoSubnet, saltProfile.ColoHasExternalIp, "", 0, false, true, nil)
			if err != nil {
				eLog.LogError("Error preparing Colo Network Interface: %v, row: %+v", err, myRow)
				return nil, nil, nil, err
			}
			vm.NetIntfs = append(vm.NetIntfs, coloIntf)
		}
		if hasServiceNic {
			serviceIntf, err := prepareNetIntfParams(saltProfile.ServiceProjectOverride, vm,
				saltProfile.ServiceInterfaceName, saltProfile.ServiceNetwork, saltProfile.ServiceSubnet,
				saltProfile.ServiceHasExternalIP, "", dpdkQcount, false, true, nil)
			if err != nil {
				eLog.LogError("Error preparing DP Network Interface: %v, row: %+v", err, myRow)
				return nil, nil, nil, err
			}
			vm.NetIntfs = append(vm.NetIntfs, serviceIntf)
		}
		if !vm.IsPrimary && !vm.Primary.AllowedAsTarget {
			// For VM in an HA cluster, if vm.Primary.AllowedAsTarget is false, fix the netIntfs to associate the external IPs
			// instead of the netIntfs of the primary.
			for _, sIntf := range vm.NetIntfs {
				pIntf := find(sIntf.Name)
				if pIntf == nil {
					err = fmt.Errorf("primary VM network interface '%s' missing", sIntf.Name)
					eLog.LogWarn("Skipping preparing network interface: %v, primary: %+v, row: %+v", err, vm.IsPrimary, myRow)
					continue
				}
				pIntf.HasExternalIp, sIntf.HasExternalIp = sIntf.HasExternalIp, pIntf.HasExternalIp
				pIntf.NatIpOrRef, sIntf.NatIpOrRef = sIntf.NatIpOrRef, pIntf.NatIpOrRef
				pIntf.ExplicitNatIpOrRef, sIntf.ExplicitNatIpOrRef = sIntf.ExplicitNatIpOrRef, pIntf.ExplicitNatIpOrRef
				pIntf.Addr, sIntf.Addr = sIntf.Addr, pIntf.Addr
			}
		}

		if vm.HasDualEgress {
			dp2Intf, err := prepareNetIntfParams(vm.DpProject, vm,
				DP2NicName, saltProfile.DP2Network, saltProfile.DP2Subnet,
				saltProfile.DP2HasExternalIP, saltProfile.PublicIpDp2, dpdkQcount, vm.HasExternalIpv6Dp2, true, nil)
			if err != nil {
				eLog.LogError("Dual Egress: Error preparing NAT DP Network Interface: %v, row: %+v", err, myRow)
				return nil, nil, nil, err
			}
			vm.NetIntfs = append(vm.NetIntfs, dp2Intf)
		}

		if vm.IsInterconnectOnramp {
			onrampIntf, err := prepareNetIntfParams(vm.DpProject, vm,
				intOnrampNic, saltProfile.IntOnrampNetwork, saltProfile.IntOnrampSubnet,
				saltProfile.IntOnrampHasExternalIp, saltProfile.PublicIpDp2, dpdkQcount, vm.HasExternalIpv6Dp2, true, nil)
			if err != nil {
				eLog.LogError("Dual Egress: Error preparing NAT DP Network Interface: %v, row: %+v", err, myRow)
				return nil, nil, nil, err
			}
			vm.NetIntfs = append(vm.NetIntfs, onrampIntf)
		}

		// TODO: Assume it is a MP Instance if only one interface.
		//       This matches the logic in fwtemplate.py, revisit.
		vm.MpInstance = len(vm.NetIntfs) == 1

		if len(vm.AllowedFwRuleKeys) > 0 || vm.IsBehindNlb {
			fwRuleName := fmt.Sprintf("firewall-rule-%s", vm.Name)
			fwRuleTag := fmt.Sprintf("fw-%d", vm.Id)
			// GCP does not allow a mix of V4 and V6 in one rule
			var sourceRanges []string
			var sourceRangesV6 []string
			var protoInfo []*definitions.ProtocolInfoProps

			// Derive additional allowed ingress firewall rules to attach to the VPC.
			if len(vm.AllowedFwRuleKeys) > 0 {
				sourceRanges = append(sourceRanges, "0.0.0.0/0")
				for _, proto := range vm.AllowedFwRuleKeys {
					protoInfo = append(protoInfo, vm.AllowedFwRules[proto])
				}
			}
			// Only if this instance is behind NLB, create firewall rules to allow traffic from NLB health check probes:
			// Source IP ranges (copied from generate_template() in gcp_instance_mgmt_utils.py):
			//     **********/16
			//     ************/22
			//     ************/22
			//	   2600:1901:8001::/48 (if ipv6 is enabled)
			// For internal Pass through ILB in sp-interconnect this additional cidr is added. (***********/22)
			// Protocol and port will be the same as health check IP and port.
			// One rule for ipv4 and one rule for ipv6. These two rules are created at the VPC level, and not per VM instance.
			if vm.IsBehindNlb {
				if len(vm.EgressIpv6Subnets) > 0 {
					sourceRangesV6 = append(sourceRangesV6, "2600:1901:8001::/48")
					sourceRangesV6 = append(sourceRangesV6, "2600:2d00:1:b029::/64")
				}
				sourceRanges = append(sourceRanges, "**********/16")
				sourceRanges = append(sourceRanges, "************/22")
				sourceRanges = append(sourceRanges, "************/22")
				sourceRanges = append(sourceRanges, "***********/22")

				protoInfo = append(protoInfo,
					&definitions.ProtocolInfoProps{"TCP",
						[]string{strconv.FormatInt(nlbConfigEntry.HealthCheckPort.Int64(), 10)},
					})

				// Overwriting the ruleName and ruleTag to not be VM specific( we only want 1 firewall rule per VPC, per region)
				fwRuleName = fmt.Sprintf("firewall-rule-%s-nlb-health-check-ipv4", strconv.FormatInt(myRow.ComputeRegionIdx.Int64(), 10))
				fwRuleTag = fmt.Sprintf("nlb-health-check")
			}

			fwProps := &definitions.FirewallRuleProps{
				Name:         fwRuleName,
				SourceRanges: sourceRanges,
				Allow:        true,
				ProtocolInfo: protoInfo,
				TargetTags:   []string{fwRuleTag},
			}
			fwRule := prepareFirewallRule(vm.NetIntfs[0].NetworkUrl, fwProps)
			if strings.Contains(fwRule.Name, "nlb-health-check-ipv4") {
				//using hashmap to store the fwRules since we are processing this per VM entry and we do not want duplicates.
				//we only want two rules per VPC per region: 1 for ipv4 and 1 for ipv6
				if _, exists := ngpaFirewallRuleMap["nlb-health-check-ipv4"]; !exists {
					ngpaFirewallRuleMap["nlb-health-check-ipv4"] = fwRule
				}
			} else {
				vm.FwRules = append(vm.FwRules, fwRule)
			}

			// Add separate second rule if there is V6, with a new rule name and sourceRangesV6
			if len(sourceRangesV6) > 0 {
				fwRuleNameV6 := fmt.Sprintf("firewall-rule-%s-nlb-health-check-ipv6", strconv.FormatInt(myRow.ComputeRegionIdx.Int64(), 10))
				fwPropsV6 := &definitions.FirewallRuleProps{
					Name:         fwRuleNameV6,
					SourceRanges: sourceRangesV6,
					Allow:        true,
					ProtocolInfo: protoInfo,
					TargetTags:   []string{fwRuleTag},
				}
				fwRuleV6 := prepareFirewallRule(vm.NetIntfs[0].NetworkUrl, fwPropsV6)
				//using hashmap to store the fwRules since we are processing this per VM entry and we do not want duplicates.
				//we only want two rules per VPC per region: 1 for ipv4 and 1 for ipv6
				if _, exists := ngpaFirewallRuleMap["nlb-health-check-ipv6"]; !exists {
					ngpaFirewallRuleMap["nlb-health-check-ipv6"] = fwRuleV6
				}
			}
		}

		// Labels
		label := func(key, value string) string {
			labelFmt := "%v = \"%v\""
			return fmt.Sprintf(labelFmt, strings.TrimSpace(key), strings.TrimSpace(value))
		}
		vm.Labels = []string{label("customer", vm.TenantId)}
		if vm.CapacityType != "" {
			vm.Labels = append(vm.Labels, label("capacity_type", strings.ToLower(vm.CapacityType)))
		} else {
			vm.Labels = append(vm.Labels, label("capacity_type", "default"))
		}
		contentRpmBkts, err := utils.GetContentRpmBuckets("gcp", eLog)
		if err != nil {
			// log the consequence but do not error out
			eLog.LogError("Error GetContentRpmBuckets: %v, content RPM bucket names will not be added to the instance labels", err)
		} else if contentRpmBkts != "" {
			vm.Labels = append(vm.Labels, label("app_threat_content_buckets", contentRpmBkts))
		}
		avRpmBkts, err := utils.GetAvRpmBuckets("gcp", eLog)
		if err != nil {
			// log the consequence but do not error out
			eLog.LogError("Error GetAvRpmBuckets: %v, av RPM bucket names will not be added to the instance labels", err)
		} else if avRpmBkts != "" {
			vm.Labels = append(vm.Labels, label("av_content_buckets", avRpmBkts))
		}

		// Derive additional params, in the case of DM, these are derived in fwtemplate.py.
		vm.MachineTypeUrl = fmt.Sprintf("https://www.googleapis.com/compute/v1/projects/%s/zones/%s/machineTypes/%s",
			project, vm.Zone, vm.MachineType)
		vm.ImageUrl = fmt.Sprintf("https://www.googleapis.com/compute/v1/projects/%s/global/images/%s",
			vm.ImageProject, vm.Image)
		if vm.Primary != nil {
			vm.Tags = []string{fmt.Sprintf("fw-%v", vm.Primary.Id)}
		} else {
			vm.Tags = []string{fmt.Sprintf("fw-%v", vm.Id)}
		}
		// add "gcp-gppt" network tags for GCP Portals
		if vm.NodeType == terraform.NodeTypeGPPortal {
			vm.Tags = append(vm.Tags, "gcp-gppt")
		}
		if vm.IsBehindNlb {
			vm.Tags = append(vm.Tags, fmt.Sprintf("nlb-health-check"))
		}
		// for SP interconnect: fetch env from metadata and add as tag to only interconnect RN firewalls
		if vm.IsSpInterconnect && vm.NodeType == terraform.NodeTypeRemoteNetwork {
			key := "saas_gpcs_api_endpoint"
			for _, meta := range vm.Metadata {
				if strings.HasPrefix(meta, key) {
					endpointName := utils.StripSurroundingQuotes(strings.TrimSpace(strings.SplitN(meta, "=", 2)[1]))
					if strs := strings.Split(endpointName, "."); len(strs) > 1 {
						envPrefix := strs[0]
						id := vm.Id
						if vm.Primary != nil {
							id = vm.Primary.Id
						}
						vm.Tags = append(vm.Tags, fmt.Sprintf("%v-fw-%v", envPrefix, id))
					}
				}
			}
		}

		// Derive public IP output, in the case of DM, it is derived in fwtemplate.py.
		if vm.IsPrimary {
			if ip := vm.NetIntfs[0].StaticIp; ip != "" {
				vm.PublicIp = ip
			} else if !vm.MpInstance && !vm.CleanIpTag {
				vm.SetNatIpAsPublicIpOutput = true
			}

			/*if hasDp2Nic {
				if ip := vm.NetIntfs[2].StaticIp; ip != "" {
					vm.PublicIpDp2 = ip
				} else if !vm.MpInstance && !vm.CleanIpTag {
					vm.SetNatIpAsPublicIpDP2Output = true
				}
			}*/
		}

		// NOTE: Glean selected NLB parameters from other instances in the same (tenant, region) as well.
		//       This mimics the DM code (generate_template()).
		if vm.NodeType == terraform.NodeTypeGPGateway {
			gleanNlbParameters(nlb, vm, eLog)
			gleanNlbParameters(svcilb, vm, eLog)
		} else if vm.NodeType == terraform.NodeTypeRemoteNetwork {
			gleanNlbParameters(intOnrampILB, vm, eLog)
		}
		if isColoSCBehingILB && (myRow.DeleteDeployment.Int64() == 0) {
			if saltProfile.ColoILBName != "" {
				eLog.LogInfo("Adding colo-instance %v behind colo-ILB %v:", vm.Name, saltProfile.ColoILBName)
				coloILBName := saltProfile.ColoILBName
				coloILB, ilbExists := coloILBResources[coloILBName]
				if !ilbExists {
					eLog.LogInfo("Frame ColoILB: '%v'", coloILBName)
					eLog.LogInfo("The subnet details for ColoILB are: '%v' , '%v'", saltProfile.ColoNetwork, saltProfile.ColoSubnet)
					coloILB = frameColoILBNetworkLoadBalancerConfig(coloILBName, vm, myRow, &saltProfile)
					coloILBResources[coloILBName] = coloILB
					hasColoILB = true
				}
				if _, coloZonalInstanceExists := coloILB.ZonalInstanceMap[vm.Zone]; !coloZonalInstanceExists {
					eLog.LogInfo("Adding zone %v behind ILB: '%v'", vm.Zone, coloILBName)
					coloILB.ZonesList = append(coloILB.ZonesList, saltProfile.Zone)
					sort.Strings(coloILB.ZonesList)
					coloILB.ZonalInstanceMap[vm.Zone] = make([]*VMInstance, 0)
				}
				coloILB.ZonalInstanceMap[vm.Zone] = append(coloILB.ZonalInstanceMap[vm.Zone], vm)
				eLog.LogInfo("ColoILB: '%v' adding instance: %v into zone: %v", coloILBName, vm.Name, vm.Zone)
			}
		}

		vms[vm.ClusterId] = append(vms[vm.ClusterId], vm)
	}
	for clusterId, _ := range vms {
		if clustersToBeDeleted[clusterId] {
			delete(vms, clusterId)
			eLog.LogInfo("Remove cluster %v as it is marked as to-be-deleted", clusterId)
		}
	}
	//convert the rulesMap into an array to return the values
	for _, rule := range ngpaFirewallRuleMap {
		retNgpaFirewallRules = append(retNgpaFirewallRules, rule)
	}
	eLog.LogInfo("NGPA NLB health check rules to be configured are %+v", retNgpaFirewallRules)

	if hasNlb {
		if nlb != nil {
			retnlb = append(retnlb, nlb)
			if hasIlb && svcilb != nil {
				// Currently exists only with NLB so copying the common objects or else we have to have another method
				svcilb.CommonBaseName = nlb.Name
				retnlb = append(retnlb, svcilb)
			}
		}
	}
	if hasIlb && intOnrampILB != nil && intOnrampILB.CfgSource != nil {
		retnlb = append(retnlb, intOnrampILB)
	}
	if hasColoILB {
		for _, v := range coloILBResources {
			retnlb = append(retnlb, v)
		}
	}
	if hasNlb || hasIlb || hasColoILB {
		return vms, retnlb, retNgpaFirewallRules, nil
	} else {
		return vms, nil, retNgpaFirewallRules, nil
	}
}

// gleanNlbParameters gleans NLB cfg parameters from instances.
// The parameters retrieved should be the same for all instances in (tenant, region), and it seems even the NLB instance
// has those parameters so no need to glean from others. On the other hand, in the DM path
// the code caches these parameter values and continue to override them with each instance traversed.
// Here we follow the DM-path gleaning the parameters from other instances just in case the NLB's salt-file
// is not always populated, however, here we enforce the following source preference:
// 1. NLB instance itself
// 2. instances behind the NLB
// 3. other instances
func gleanNlbParameters(nlb *NLB, vm *VMInstance, eLog *clogger.EventLogger) {
	if !vm.IsPrimary {
		eLog.LogDebug("Glean NLB parameters: skip '%v' as it is not a primary", vm.Name)
		return
	} else if !vm.HasDpNic {
		eLog.LogDebug("Glean NLB parameters: skip '%v' as it does not have a DP NIC", vm.Name)
		return
	}

	// sanity check
	if nlb == nil {
		eLog.LogError("Glean NLB parameters: empty NLB input, cannot continue")
		return
	}
	if len(vm.Zone) == 0 || len(vm.Region) == 0 || len(vm.NetIntfs[0].Network) == 0 || len(vm.NetIntfs[0].Subnet) == 0 {
		eLog.LogError("Glean NLB parameters: skip '%v' as parameter(s) not present: "+
			"zone=%v, region=%v, cleanIp=%v, network=%v, subnet=%v",
			vm.Name, vm.Zone, vm.Region, vm.CleanIpTag, vm.NetIntfs[0].Network, vm.NetIntfs[0].Subnet)
		return
	}

	precedence := func(vm *VMInstance) int {
		if vm == nil {
			return 10
		} else if vm.NodeType == terraform.NodeTypeNLB || vm.NodeType == terraform.NodeTypeSvcILB {
			return 1
		} else if vm.IsBehindNlb {
			return 2
		} else {
			return 3
		}
	}
	currP := precedence(nlb.CfgSource)
	newP := precedence(vm)
	if newP < currP {
		nlb.Region = vm.Region
		nlb.CleanIpTag = vm.CleanIpTag
		nlb.Network = vm.NetIntfs[0].Network
		nlb.Subnet = vm.NetIntfs[0].Subnet
		logStr := fmt.Sprintf("Glean NLB parameters from '%v': region=%v, cleanIp=%v, network=%v, subnet=%v",
			vm.Name, nlb.Region, nlb.CleanIpTag, nlb.Network, nlb.Subnet)
		if nlb.CfgSource == nil {
			eLog.LogInfo(logStr)
		} else {
			eLog.LogInfo(logStr+", overriding values from %v", nlb.CfgSource.Name)
		}
		nlb.CfgSource = vm
	} else {
		eLog.LogDebug("Skip gleaning NLB parameters from '%v' since '%v' has a higher or equal precedence", vm.Name, nlb.CfgSource.Name)
	}
	if vm.NodeType == terraform.NodeTypeNLB {
		// If NLB has ZonesList, use it, else gather the list from the instances behind the NLB in the else if case.
		if len(vm.ZonesList) > 0 {
			nlb.ZonesList = vm.ZonesList
			eLog.LogInfo("Glean NLB parameters: set ZonesList from NLB instance '%v': %v", vm.Name, nlb.ZonesList)
		}
	} else if vm.IsBehindNlb {
		// Non NLB VM that is behind the NLB. Look at the Zone Distribution and update nlb.ZonesList to contain the
		// superset. ZonesList is an ordered list. Index 0 should have the primary zone
		if len(nlb.ZonesList) <= 1 {
			nlb.ZonesList = vm.ZonesList
			eLog.LogInfo("Glean NLB parameters: set ZonesList from VM instance '%v': %v", vm.Name, nlb.ZonesList)
		}
		if len(nlb.ZonesList) == 0 {
			// Older VMs might not have ZonesList
			nlb.ZonesList = append(nlb.ZonesList, vm.Zone)
			eLog.LogInfo("Glean NLB parameters: aggregate Zone '%s' from VM instance '%v': %v", vm.Zone, vm.Name, nlb.ZonesList)
		}
		if utils.EmptyValue(nlb.EgressIpv6ListSubnet) && !utils.EmptyValue(vm.EgressIpv6ListSubnet) {
			nlb.EgressIpv6ListSubnet = vm.EgressIpv6ListSubnet
		}
	}
}

func frameColoILBNetworkLoadBalancerConfig(coloILBName string, vm *VMInstance, myRow *instance_master.Row, saltprofile *instance_master.SaltProfile) (nlb *NLB) {
	coloILB := &NLB{}
	coloILB.Cfg = &network_load_balancer_config.Row{
		CustID:                             myRow.CustID,
		RegionID:                           myRow.ComputeRegionIdx,
		ForwardingRuleProtocol:             sql.Int64(0), //L3Default
		BackendServiceProtocol:             sql.Int64(0), //L3Default
		SessionAffinity:                    sql.Int64(2), // CLIENT_IP_PROTO
		HealthCheckProtocol:                sql.Int64(0), // HTTP
		HealthCheckPort:                    sql.Int64(8000),
		HealthCheckInterval:                sql.Int64(2),
		HealthCheckTimeout:                 sql.Int64(2),
		HealthCheckUnhealthyThreshold:      sql.Int64(2),
		HealthCheckHealthyThreshold:        sql.Int64(2),
		ConnPersistenceOnUnhealthyBackends: sql.Int64(1), // NEVER_PERSIST
		IsStrongSessionAffinitySupported:   sql.Int64(0),
		IdleTimeoutSec:                     sql.Int64(600),
		LoadBalancerType:                   "INTERNAL",
		LoadBalancerListenerType:           "TCP_UDP_ICMP", //??
		DualStackSupported:                 sql.Int64(1),   //??
		CloudProvider:                      "gcp",
		EnableSrcDestIPPreservation:        sql.Int64(1), //??
	}
	coloILB.CfgSource = vm
	coloILB.Name = coloILBName
	coloILB.Region = vm.Region
	coloILB.Network = saltprofile.ColoNetwork
	coloILB.Subnet = saltprofile.ColoSubnet
	coloILB.UMIGNetwork = saltprofile.DPNetwork
	coloILB.UMIGSubnet = saltprofile.DPSubnet
	coloILB.ComputeRegionIdx = strconv.FormatInt(myRow.ComputeRegionIdx.Int64(), 10)
	coloILB.ZonalInstanceMap = make(map[string][]*VMInstance)
	coloILB.ZonesList = make([]string, 0)
	coloILB.IsColoILB = true
	return coloILB
}

func PrepareDeploymentLoadBalancer(lbs []*NLB, zonalProvider bool, eLog *clogger.EventLogger) ([]deployment.ELB, []deployment.ILB, []deployment.UMIG, []deployment.PBR, error) {
	// declare the return variables for elb and ilb

	var retELB []deployment.ELB
	var retILB []deployment.ILB
	var retUMIG []deployment.UMIG
	var retPBR []deployment.PBR

	// Create a map to track UMIGs by their unique identifier (name+zone) to avoid duplicates.
	// This is essential because:
	// 1. Multiple load balancers (NLB + SVC ILB) may share the same UMIG instances
	// 2. Different zones may have UMIGs with the same name but are separate resources
	// Key format: "{umigName}-{zone}" (e.g., "nlb-umig-us-central1-a")
	umigMap := make(map[string]deployment.UMIG)

	for _, lb := range lbs {
		lbCfg := lb.Cfg
		lbDesp := fmt.Sprintf("NLB{Name: %v, CustId: %v. RegionId: %v}", lb.Name, lbCfg.CustID.Int64(), lbCfg.RegionID.Int64())
		if lb.CfgSource == nil {
			eLog.LogError("%v without instances, skip creation: %+v", lbDesp, *lb)
			return nil, nil, nil, nil, nil
		}

		var projectOverride string
		var umigProjectOverride string
		// we need to override the project for svc ilb so reusing the same.
		if lb.CfgSource.IsSpInterconnect || lb.CfgSource.Sase5gEnabled || lb.CfgSource.IsInterconnectOnramp || lb.CfgSource.MasqueEnabled {

			// ILB doesn't support strong session affinity, so always set it to 0
			lb.Cfg.IsStrongSessionAffinitySupported = 0
			lbCfg.IdleTimeoutSec = 600
			if lb.CfgSource.IsInterconnectOnramp {
				lbCfg.SessionAffinity = 1
				lbCfg.HealthCheckPort = 80
				if lb.CfgSource.IsSpInterconnect {
					umigProjectOverride = lb.CfgSource.SharedMgmtVPCProj
				}
			} else {
				// override the default host project to interconnect host project.
				projectOverride = lb.CfgSource.SharedMgmtVPCProj
				umigProjectOverride = lb.CfgSource.SharedMgmtVPCProj
			}
		}
		region := lb.Region
		cleanIpTag := lb.CleanIpTag
		network := lb.Network
		subnet := lb.Subnet
		// NOTE: Evelyn: remove if not needed
		// subnetUrl = fmt.Sprintf("https://www.googleapis.com/compute/v1/projects/%s/regions/%s/subnetworks/%s",project, region, subnet)

		// Existing code in gcp_instance_mgmt_utils.py cap the base NLB name to first 51 characters
		// with the explanation below. Follow the same algorithm to preserve names.
		//     GCP has a limit of 61 characters on the full healthcheck / umig names.
		//     Regex match GCP is using is: '(?:[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?)'
		//     We will reduce the length of the incoming nlb_name to be 61 - 10 = 51 characters.
		nameMaxLen := 51
		baseName := lb.Name
		if len(baseName) > nameMaxLen {
			baseName = baseName[:nameMaxLen]
		}

		if len(lb.CommonBaseName) > nameMaxLen {
			lb.CommonBaseName = lb.CommonBaseName[:nameMaxLen]
		}

		// Create UMIGs for this load balancer. This function handles:
		// 1. UMIG name generation based on CommonBaseName or baseName
		// 2. Deduplication through umigMap to avoid creating duplicate UMIGs when shared between load balancers
		// 3. Zone-specific UMIG creation (each zone gets its own UMIG even with same name)
		// 4. Instance assignment to appropriate UMIGs based on zone distribution
		umigNetwork := network
		umigSubnet := subnet
		//if explicit network and subnet is specified for UMIG, use that.
		if lb.UMIGNetwork != "" {
			umigNetwork = lb.UMIGNetwork
		}
		if lb.UMIGSubnet != "" {
			umigSubnet = lb.UMIGSubnet
		}
		var lbUMIGs []deployment.UMIG
		lbUMIGs = createUMIGsForLoadBalancer(lb, baseName, umigNetwork, umigSubnet, umigProjectOverride, zonalProvider, umigMap)
		// Follow existing code in gcp_network_load_balancer_template.py and cap base backend service name to 41
		bsBaseNameMaxLen := 41
		bsBaseName := baseName
		if len(bsBaseName) > bsBaseNameMaxLen {
			bsBaseName = bsBaseName[:bsBaseNameMaxLen]
		}
		dBsProtocol, err := func() (deployment.SupportedLBProtocol, error) {
			protocolVal := lbCfg.BackendServiceProtocol.Int64()
			protocol, err := network_load_balancer_config.GetBackendServiceProtocol(protocolVal)
			if err != nil {
				eLog.LogError("%v failed to retrieve backend service protocol(%v): %v", lbDesp, protocolVal, err)
				return "", err
			}

			switch protocol {
			case "UNSPECIFIED":
				return deployment.L3Default, nil
			case "TCP":
				return deployment.TCP, nil
			case "UDP":
				return deployment.UDP, nil
			default:
				e := fmt.Errorf("%v backend service protocol '%v' not supported by deployment API", lbDesp, protocol)
				eLog.LogError("%v", e)
				return "", e
			}
		}()
		if err != nil {
			return nil, nil, nil, nil, err
		}

		dSessionAffinity, err := func() (deployment.SupportedSA, error) {
			saVal := lbCfg.SessionAffinity.Int64()
			sa, err := network_load_balancer_config.GetSessionAffinity(saVal)
			if err != nil {
				eLog.LogError("%v failed to retrieve session affinity(%v): %v", lbDesp, saVal, err)
				return "", err
			}

			switch sa {
			case "NONE":
				return deployment.SANone, nil
			case "CLIENT_IP":
				return deployment.SAClientIP, nil
			case "CLIENT_IP_PROTO":
				return deployment.SAClientIPProto, nil
			case "CLIENT_IP_PORT_PROTO":
				return deployment.SAClientIPPortProto, nil
			default:
				e := fmt.Errorf("%v session affinity '%v' not supported by deployment API", lbDesp, sa)
				eLog.LogError("%v", e)
				return "", e
			}
		}()
		if err != nil {
			return nil, nil, nil, nil, err
		}

		connDraining := deployment.ConnDraining{
			DrainingTimeoutSec: 30,
		}

		dConnPersistence, err := func() (deployment.SupportedConnPersistence, error) {
			cpVal := lbCfg.ConnPersistenceOnUnhealthyBackends.Int64()
			cp, err := network_load_balancer_config.GetConnPersistenceOnUnhealthyBackends(cpVal)
			if err != nil {
				eLog.LogError("%v failed to retrieve conn persistence on unhealthy backends setting(%v): %v",
					lbDesp, cpVal, err)
				return "", err
			}

			switch cp {
			case "DEFAULT_FOR_PROTOCOL":
				return deployment.SCPDefault, nil
			case "NEVER_PERSIST":
				return deployment.SCPNeverPersist, nil
			case "ALWAYS_PERSIST":
				return deployment.SCPAlwaysPersist, nil
			default:
				e := fmt.Errorf("%v conn persistence on unhealthy backends setting '%v' not supported by deployment API", lbDesp, cp)
				eLog.LogError("%v", e)
				return "", e
			}
		}()
		if err != nil {
			return nil, nil, nil, nil, err
		}
		connTracking := deployment.ConnTrackingPolicy{
			ConnPersistence:      dConnPersistence,
			EnableStrongAffinity: (lbCfg.IsStrongSessionAffinitySupported == 1),
			IdleTimeoutSec:       int(lbCfg.IdleTimeoutSec),
		}

		dHealthCheckType, dHealthCheckRequestPath, err := func() (string, string, error) {
			hctVal := lbCfg.HealthCheckProtocol.Int64()
			hct, err := network_load_balancer_config.GetHealthCareProtocol(hctVal)
			if err != nil {
				eLog.LogError("%v failed to retrieve health check protocol(%v): %v",
					lbDesp, hctVal, err)
				return "", "", err
			}

			switch hct {
			case "HTTP":
				return "http", "/", nil
			case "HTTPS":
				return "https", "/", nil
			case "TCP":
				return "tcp", "", nil
			default:
				e := fmt.Errorf("%v health check protocol '%v' not supported by deployment API", lbDesp, hct)
				eLog.LogError("%v", e)
				return "", "", e
			}
		}()
		if err != nil {
			return nil, nil, nil, nil, err
		}
		healthChecks := []deployment.HealthCheck{deployment.HealthCheck{
			Name:                 baseName + "-hc",
			Type:                 dHealthCheckType,
			CheckIntervalSeconds: int(lbCfg.HealthCheckInterval.Int64()),
			TimeoutSeconds:       int(lbCfg.HealthCheckTimeout.Int64()),
			UnhealthyThreshold:   int(lbCfg.HealthCheckUnhealthyThreshold.Int64()),
			HealthyThreshold:     int(lbCfg.HealthCheckHealthyThreshold.Int64()),
			Port:                 int(lbCfg.HealthCheckPort),
			RequestPath:          dHealthCheckRequestPath,
		}}
		// Add attribute changes to ignore at import.
		if zonalProvider {
			healthChecks[0].IgnoreChangesOnAttributes = ignoreHealthCheckAttributesAtImport
		}

		var backends []deployment.Backend
		for _, umig := range lbUMIGs {
			backend := deployment.Backend{
				Name: umig.Name,
				Type: deployment.TypeUMIG,
			}
			backends = append(backends, backend)
		}
		// Follow existing code in gcp_network_load_balancer_template.py and cap base backend service name to 41
		fwdRuleBaseNameMaxLen := 41
		fwdRuleBaseName := baseName
		if len(fwdRuleBaseName) > fwdRuleBaseNameMaxLen {
			fwdRuleBaseName = fwdRuleBaseName[:fwdRuleBaseNameMaxLen]
		}
		dFwdProtocol, err := func() (deployment.ForwardingProtocol, error) {
			protocolVal := lbCfg.ForwardingRuleProtocol.Int64()
			protocol, err := network_load_balancer_config.GetForwardingRuleProtocol(protocolVal)
			if err != nil {
				eLog.LogError("%v failed to retrieve forwarding rule protocol(%v): %v", lbDesp, protocolVal, err)
				return "", err
			}

			switch protocol {
			case "L3_DEFAULT":
				return deployment.L3DefaultForwarding, nil
			case "TCP":
				return deployment.TCPForwarding, nil
			case "UDP":
				return deployment.UDPForwarding, nil
			default:
				e := fmt.Errorf("%v forwarding rule protocol '%v' not supported by deployment API", lbDesp, protocol)
				eLog.LogError("%v", e)
				return "", e
			}
		}()

		var ipForwarding []deployment.IPForwarding
		addFwdRule := func(ruleName, ip string, ipVersion deployment.IPVersion, subnet string) {
			fwdRule := deployment.IPForwarding{
				ResourceName: ruleName,
				Name:         ruleName,
				IPVersion:    ipVersion,
				Protocol:     dFwdProtocol,
				AllPorts:     true,
			}
			if ipVersion == deployment.IPV6 {
				fwdRule.Subnet = subnet
			} else if ip == "" {
				fwdRule.Address = ""
			} else {
				fwdRule.Address = ip
			}
			// If ipVersion is V6, do not set this as "ip" is the V4 ip
			if cleanIpTag && ipVersion != deployment.IPV6 && ip != "" {
				addressName := fmt.Sprintf("%v-%v", Egress, strings.ReplaceAll(ip, ".", "-"))
				fwdRule.AddressResourceName = addressName
				fwdRule.AddressName = addressName
			}
			fwdRule.IgnoreChangesOnLabels = ignoreLabels
			ipForwarding = append(ipForwarding, fwdRule)
		}

		m := make(map[string]string)
		if !utils.EmptyValue(lb.EgressIpv6ListSubnet) && (!lb.CfgSource.Sase5gEnabled && !lb.CfgSource.MasqueEnabled) {
			if err := json.Unmarshal([]byte(lb.EgressIpv6ListSubnet), &m); err != nil {
				eLog.LogError("Error unmarshalling NLB egress IPV6 list: %v", err)
			} else {
				for region, subnet := range m {
					// If IIR is enabled, skip this region
					if lb.IngressIpReduction == 1 && region != lb.ComputeRegionIdx {
						continue
					}
					addFwdRule(fwdRuleBaseName+"-fw-v6-"+region, lb.PublicIp, deployment.IPV6, subnet)
				}
			}
		}

		// Note: We purposely leave IpVersion as empty for V4. This matches the DM code behavior thus avoiding Terraform
		//       from flagging it as breaking change requiring delete-then-add during the migration process.
		addFwdRule(fwdRuleBaseName+"-fw-0", lb.PublicIp, "", subnet)
		// Add policy based route for RN and colo-ILB
		if lb.CfgSource.IsInterconnectOnramp || lb.IsColoILB {
			fwdRuleName := fwdRuleBaseName + "-fw-0"
			pbrName := "pbr-" + fwdRuleName
			filter := deployment.PBRFilter{
				IPProtocol: "ALL",
				SrcRange:   "0.0.0.0/0",
				DstRange:   "0.0.0.0/0",
			}
			applyOn := deployment.PBRApplyOn{
				CloudInterconnect: &deployment.PBRCloudInterconnect{
					Region: lb.Region,
				},
				//CloudInterconnect *PBRCloudInterconnect `yaml:"cloud-interconnect"`
			}
			pbrRule := deployment.PBR{
				Name:       pbrName,
				Network:    lb.Network,
				Filter:     &filter,
				NextHopILB: fwdRuleName,
				Priority:   100,
				ApplyOn:    applyOn,
			}
			retPBR = append(retPBR, pbrRule)
			//also tweak params for ILB
			connDraining.DrainingTimeoutSec = 300
			if pbrRule.ApplyOn.CloudInterconnect != nil {
			}
		}

		// if lb.EgressIpList
		for _, ip := range lb.EgressIpsSansNat {
			// Note: We purposely leave IpVersion as empty for V4. This matches the DM code behavior thus avoiding Terraform
			//       from flagging it as breaking change requiring delete-then-add during the migration process.
			addFwdRule(fwdRuleBaseName+"-fw-"+strings.ReplaceAll(ip, ".", "-"), ip, "", subnet)
		}

		if shouldCreateNlb(lb) {
			nlb := deployment.ELB{
				Name:                baseName,
				Region:              region,
				Network:             network,
				Subnet:              subnet,
				Protocol:            dBsProtocol,
				SessionAffinity:     dSessionAffinity,
				LoadBalancingScheme: deployment.LBExternal,
				BackendServiceName:  bsBaseName + "-bs",
				HealthChecks:        healthChecks,
				Backends:            backends,
				ConnTracking:        connTracking,
				ConnDraining:        connDraining,
				IPForwardings:       ipForwarding,
			}
			retELB = append(retELB, nlb)
		} else {
			eLog.LogInfo("In interconnect Region, or 5G enabled populating ILB")
			var ilb deployment.ILB
			ilb = deployment.ILB{
				Name:                baseName,
				Region:              region,
				Network:             network,
				Subnet:              subnet,
				Protocol:            dBsProtocol,
				SessionAffinity:     dSessionAffinity,
				LoadBalancingScheme: deployment.LBInternal,
				BackendServiceName:  bsBaseName + "-bs",
				HealthChecks:        healthChecks,
				Backends:            backends,
				ConnTracking:        connTracking,
				ConnDraining:        connDraining,
				ProjectOverride:     projectOverride,
				IPForwardings:       ipForwarding,
			}
			retILB = append(retILB, ilb)
		}
	}

	// Convert umigMap to slice for return. This ensures we return each unique UMIG only once,
	// even if it's shared between multiple load balancers (e.g., NLB and SVC ILB)
	for _, umig := range umigMap {
		retUMIG = append(retUMIG, umig)
	}

	return retELB, retILB, retUMIG, retPBR, nil
}

func shouldCreateNlb(nlb *NLB) bool {
	if nlb.CfgSource.IsSpInterconnect || nlb.CfgSource.Sase5gEnabled || nlb.CfgSource.IsInterconnectOnramp || nlb.IsColoILB || nlb.CfgSource.MasqueEnabled {
		return false
	}
	return true
}

// createUMIGsForLoadBalancer creates UMIGs for a specific load balancer while handling deduplication.
// This function is essential for scenarios where multiple load balancers (e.g., NLB + 5G ILB) share the same UMIG instances.
//
// Parameters:
//   - lb: The load balancer configuration containing zone distribution and instance mapping
//   - baseName: Base name for UMIG generation (already truncated to meet GCP limits)
//   - network, subnet: Network configuration for the UMIGs
//   - projectOverride: Optional project override for interconnect scenarios
//   - zonalProvider: Whether to use zonal provider configuration
//   - umigMap: Global map tracking all UMIGs by unique key to prevent duplicates
//
// Returns:
//   - []deployment.UMIG: List of UMIGs created/reused for this specific load balancer
//
// Key Logic:
//   - UMIG names are generated based on zone index (primary zone uses base name, others get numbered suffix)
//   - Each UMIG is uniquely identified by "{name}-{zone}" key since UMIGs are zone-specific resources
//   - If a UMIG with the same name+zone already exists (from another load balancer), it's reused
//   - This enables proper sharing of UMIGs between NLB and SVC ILB when they use CommonBaseName
func createUMIGsForLoadBalancer(lb *NLB, baseName, network, subnet, projectOverride string, zonalProvider bool, umigMap map[string]deployment.UMIG) []deployment.UMIG {
	var lbUMIGs []deployment.UMIG

	for idx, zone := range lb.ZonesList {
		var umigName string
		// Generate UMIG name based on CommonBaseName (for sharing) or baseName (load balancer specific)
		// CommonBaseName is used when multiple load balancers (e.g., NLB + SVC ILB) should share UMIGs
		if lb.CommonBaseName != "" {
			umigName = lb.CommonBaseName + "-umig"
			if idx >= 1 {
				umigName = lb.CommonBaseName + "-umig-" + strconv.Itoa(idx)
			}
		} else {
			umigName = baseName + "-umig"
			if idx >= 1 {
				umigName = baseName + "-umig-" + strconv.Itoa(idx)
			}
		}

		// Create unique key for UMIG to avoid duplicates
		umigKey := fmt.Sprintf("%s-%s", umigName, zone)

		// Check if UMIG already exists (created by another load balancer sharing the same instances)
		// This happens when NLB and SVC ILB both reference the same CommonBaseName and zone
		if existingUMIG, exists := umigMap[umigKey]; exists {
			lbUMIGs = append(lbUMIGs, existingUMIG)
			continue
		}

		// Create new UMIG for this zone
		umig := deployment.UMIG{
			Name:    umigName,
			Zone:    zone,
			Network: network,
			Subnet:  subnet,
		}

		if projectOverride != "" {
			umig.ProjectOverride = projectOverride
		}
		if zonalProvider {
			umig.ProviderOverride = "google." + umig.Zone
		}

		// Add all instances from this zone to the UMIG
		for _, vm := range lb.ZonalInstanceMap[zone] {
			umig.Instances = append(umig.Instances, map[string]string{"resource-name": vm.TFLocalName, "name": vm.Name})
		}

		// Store in global deduplication map. This ensures:
		// 1. Same UMIG (name+zone) is not created multiple times
		// 2. Subsequent load balancers can reuse this UMIG if they share the same CommonBaseName and zone
		// 3. Final return list contains each unique UMIG only once
		umigMap[umigKey] = umig
		lbUMIGs = append(lbUMIGs, umig)
	}

	return lbUMIGs
}

func PrepareColoPrivateConnectPeering(custId int64, project, edgeLocRegion string, onboardingEntries []*colo_onboarding_master.Row,
	linkEntires []*colo_connect_link_master.Row, linkPrjs map[int64]string, eLog *clogger.EventLogger) ([]deployment.PrivateConnectPeering, error) {
	var err error
	linkMap := make(map[int64]struct {
		Name    string
		Project string
	}) // map: LinkID -> {LinkName, LinkPrj}
	for _, entry := range linkEntires {
		linkMap[entry.ID.Int64()] = struct {
			Name    string
			Project string
		}{
			entry.Name.String(), linkPrjs[entry.CustID.Int64()]}
	}
	rtrName := fmt.Sprintf("colo-rtr-%v-%v", edgeLocRegion, custId)

	var peerings []deployment.PrivateConnectPeering
	for _, entry := range onboardingEntries {
		desp := fmt.Sprintf("Colo onboarding entry (%v, %v, %v)", entry.ID.Int64(), entry.Name.String(), entry.Type.String())
		if entry.IsDeleted {
			eLog.LogInfo("Entry with is_deleted set, skip %v", desp)
			continue
		} else if len(entry.Bgp) == 0 {
			eLog.LogInfo("Entry with empty BGP profile, skip %v", desp)
			continue
		}
		bgpProfile := make(map[string]interface{})
		if err = json.Unmarshal([]byte(entry.Bgp.String()), &bgpProfile); err != nil {
			eLog.LogError("Error unmarshalling BGP profile: %v, entry=%v", err, desp)
			return nil, err
		}

		privateConnect := ""
		privateConnectPrj := ""
		if lid := entry.LinkID.Int64(); lid != 0 {
			privateConnect = linkMap[lid].Name
			privateConnectPrj = linkMap[lid].Project
		}
		peering := deployment.PrivateConnectPeering{
			Name:         fmt.Sprintf("colo-int-att-%v", entry.Name.String()),
			Type:         deployment.PrivateConnectType(strings.ToUpper(entry.Type.String())),
			Gateway:      rtrName,
			AdminEnabled: true,
			Mtu:          1500,
		}

		if peering.Type == deployment.PPPartner {
			switch z := strings.ToLower(entry.InterconnectZone.String()); z {
			case "zone1":
				peering.EdgeAvailabilityDomain = "AVAILABILITY_DOMAIN_1"
			case "zone2":
				peering.EdgeAvailabilityDomain = "AVAILABILITY_DOMAIN_2"
			default:
				err = fmt.Errorf("unexpected interconnect zone '%v'", z)
				eLog.LogError("Error preparing interconnect attachment: %v, entry=%v", err, desp)
				return nil, err
			}
		} else if peering.Type == deployment.PPDedicated {
			if len(privateConnect) == 0 {
				err = fmt.Errorf("unknown dedicated interconnect name")
				eLog.LogError("Error preparing dedicated interconnect attachment: %v, entry=%v", err, desp)
				return nil, err
			}
			if val, exist := bgpProfile["vlan_id"]; exist {
				if vidStr, isStr := val.(string); isStr && len(vidStr) > 0 {
					vid, err := strconv.ParseUint(vidStr, 10, 32)
					if err != nil {
						eLog.LogError("Error converting VLAN ID '%v' in BGP profile: %v, entry=%v", vidStr, err, desp)
						return nil, err
					}
					peering.VlanTag8021q = uint(vid)
				}
				peering.PrivateConnect = privateConnect
				peering.PrivateConnectProject = privateConnectPrj

				switch bw := entry.Bandwidth.Int64(); bw {
				case 1:
					peering.Bandwidth = deployment.PPBw1G
				case 2:
					peering.Bandwidth = deployment.PPBw2G
				case 5:
					peering.Bandwidth = deployment.PPBw5G
				case 10:
					peering.Bandwidth = deployment.PPBw10G
				case 20:
					peering.Bandwidth = deployment.PPBw20G
				default:
					err = fmt.Errorf("unexpected bandwidth '%v'", bw)
					eLog.LogError("Error preparing interconnect attachment: %v", err)
					return nil, err
				}
			}
		}
		peerings = append(peerings, peering)
	}
	return peerings, nil
}

type DeploymentInput struct {
	CustId                   int64
	TenantId                 int64
	Workspace                string
	Project                  string
	Region                   string
	EdgeLocationRegion       string
	Clusters                 map[int64][]*VMInstance
	Nlb                      []*NLB
	ColoOnboardings          []*colo_onboarding_master.Row
	ColoLinks                []*colo_connect_link_master.Row
	ColoLinkProjects         map[int64]string
	TrafficMirroringCfgEntry *traffic_mirroring_cfg.Row
	ZonalProvider            bool
	DpNetworkFwRules         []*FirewallRule
	ELog                     *clogger.EventLogger
}

func PrepareNgpaFirewallConfig(rules []*FirewallRule, eLog *clogger.EventLogger) ([]deployment.FirewallRule, string) {
	fwRules := make([]deployment.FirewallRule, len(rules))
	eLog.LogInfo("Starting to prepare NGPA Firewall Config for rules: %+v", rules)
	var networkRef string
	for i, vmFwRule := range rules {
		fwRule := &fwRules[i]
		fwRule.ResourceName = vmFwRule.TFLocalName
		fwRule.Name = vmFwRule.Name
		fwRule.Allow = make([]deployment.PortProtocols, len(vmFwRule.Allowed))
		for j, protoInfo := range vmFwRule.Allowed {
			fwRule.Allow[j].Protocol = protoInfo.IpProtocol
			fwRule.Allow[j].Ports = protoInfo.Ports
		}
		fwRule.Direction = "INGRESS"
		fwRule.SrcRanges = vmFwRule.SourceRanges
		fwRule.TargetTags = vmFwRule.TargetTags
		networkRef = vmFwRule.NetworkRef
	}
	eLog.LogInfo("Created NGPA Firewall Config with rules: %+v and networkUrl: %s", fwRules, networkRef)
	//get the NetwrokId name from networkRef
	parts := strings.Split(networkRef, "/")
	networkId := parts[len(parts)-1]
	eLog.LogInfo("Extracted networkId %s from networkUrl %s", networkId, networkRef)
	return fwRules, networkId
}

func PrepareDeploymentConfig(input DeploymentInput) (*deployment.Config, error) {
	var err error
	eLog := input.ELog
	config := &deployment.Config{
		ResourceName:  input.Workspace,
		TenantID:      input.TenantId,
		ProjectID:     input.Project,
		Region:        input.Region,
		CloudProvider: "gcp",
		ServiceType:   "PA",
		ImageProject:  "image-gpcs", // overwritten by the actual value below, this dummy val is used in 'destroy' case
	}

	var instances []deployment.Instance
	for _, cluster := range input.Clusters {
		for _, vm := range cluster {
			// In deployment IsCleanIP and ImageProject is a config-level attribute.
			// Here we assume they are consistent across instances.
			config.IsCleanIP = vm.CleanIpTag
			config.ImageProject = vm.ImageProject

			var inst deployment.Instance
			inst.ResourceName = vm.TFLocalName
			inst.Name = vm.Name
			inst.Zone = vm.Zone
			inst.MachineType = vm.MachineType
			inst.Tags = vm.Tags
			inst.MinCpuPlatform = vm.MinCpuPlatform

			labels := make([]map[string]string, len(vm.Labels))
			for idx, vmLabel := range vm.Labels {
				kv := strings.SplitN(vmLabel, "=", 2)
				label := make(map[string]string)
				label["name"] = strings.TrimSpace(kv[0])
				label["value"] = utils.StripSurroundingQuotes(strings.TrimSpace(kv[1]))
				labels[idx] = label
			}
			inst.Labels = labels

			inst.BootDisk = deployment.BootDisk{
				DeviceName:        "boot",
				DisableAutoDelete: false,
			}
			inst.Image = vm.Image

			interfaces := make([]deployment.Interface, len(vm.NetIntfs))
			for idx, vmIntf := range vm.NetIntfs {
				intf := &interfaces[idx]
				intf.Name = vmIntf.Name
				intf.ProjectOverride = vmIntf.Project
				intf.Network = vmIntf.Network
				intf.Subnet = vmIntf.Subnet
				if vmIntf.DualStack {
					intf.StackType = deployment.IPV4_IPV6
				}
				if vmIntf.ExplicitQueueCount {
					intf.QueueCount = uint(vmIntf.QueueCount)
				}
				if vmIntf.ExplicitNatIpOrRef {
					if addr := vmIntf.Addr; addr != nil {
						if addr.Address != "" {
							// In clean-IP projects, addresses are always statically set.
							intf.PublicIP = addr.Address
						} else {
							// If address is not static, then set to "any" so GCP will pick one.
							// This only happens in non-clean-IP projects with static IP missing in the salt profile.
							intf.PublicIP = "any"
						}
						intf.PublicIPResourceName = addr.TFLocalName
						intf.PublicIPName = addr.Name
					} else {
						intf.PublicIP = vmIntf.StaticIp
					}
				}
				intf.IgnoreChangesOnAccessConfig = vmIntf.IgnoreChangesOnAccessConfig
				intf.IgnoreChangesOnAliasIpRange = vmIntf.IgnoreChangesOnAliasIpRange
				if len(vmIntf.AliasIpRanges) > 0 {
					intf.AliasIpRanges = make([]deployment.AliasIpRange, len(vmIntf.AliasIpRanges))
					for i, aliasIp := range vmIntf.AliasIpRanges {
						intf.AliasIpRanges[i] = deployment.AliasIpRange{
							IpCidrRange:         aliasIp.IpCidrRange,
							SubnetworkRangeName: aliasIp.SubnetworkRangeName,
						}
					}
				}

				// In deployment FirewallRule is an interface-level attribute.
				// For our use case, firewall rules are associated with DP interface (NetIntfs[0]).
				if idx == 0 && len(vm.FwRules) != 0 {
					fwRules := make([]deployment.FirewallRule, len(vm.FwRules))
					for i, vmFwRule := range vm.FwRules {
						fwRule := &fwRules[i]
						fwRule.ResourceName = vmFwRule.TFLocalName
						fwRule.Name = vmFwRule.Name
						fwRule.Allow = make([]deployment.PortProtocols, len(vmFwRule.Allowed))
						for j, protoInfo := range vmFwRule.Allowed {
							fwRule.Allow[j].Protocol = protoInfo.IpProtocol
							fwRule.Allow[j].Ports = protoInfo.Ports
						}
						fwRule.Direction = "INGRESS"
						fwRule.SrcRanges = vmFwRule.SourceRanges
						fwRule.TargetTags = vmFwRule.TargetTags
					}
					intf.FwRules = fwRules
				}
			}
			inst.NetworkInterface = interfaces

			metadata := make([]map[string]string, len(vm.Metadata))
			for idx, meta := range vm.Metadata {
				kv := strings.SplitN(meta, "=", 2)
				datum := make(map[string]string)
				datum["name"] = strings.TrimSpace(kv[0])
				datum["value"] = utils.StripSurroundingQuotes(strings.TrimSpace(kv[1]))
				metadata[idx] = datum
			}
			inst.Metadata = metadata

			inst.Credentials = deployment.Credential{
				ServiceAccount: vm.ServiceAccount,
			}

			if vm.TargetInstance != nil {
				inst.CreateTarget = true
			}

			// For HA clusters, L3FwdRules are always under the first FW instance of the cluster regardless
			// HA state (this matches DM code). However, to use deployment API the fwd rules need
			// to be with the active (primary) FW instance to have the TF configuration generated as expected.
			l3FwdRules := vm.L3FwdRules
			if vm.HACluster {
				if vm.IsPrimary && !vm.AllowedAsTarget {
					l3FwdRules = nil
				} else if !vm.IsPrimary && !vm.Primary.AllowedAsTarget {
					l3FwdRules = vm.Primary.L3FwdRules
				}
			}
			ipForwardings := make([]deployment.IPForwarding, len(l3FwdRules))
			mappingIpProtocolToForwardingProtocol := func(proto IPProtocol) (deployment.ForwardingProtocol, error) {
				switch proto {
				case TCP:
					return deployment.TCPForwarding, nil
				case UDP:
					return deployment.UDPForwarding, nil
				case ICMP:
					return deployment.ICMPForwarding, nil
				case L3_DEFAULT:
					return deployment.L3DefaultForwarding, nil
				default:
					return "", fmt.Errorf("unexpected IPProtocl '%v'", proto)
				}
			}
			for idx, vmFwdRule := range l3FwdRules {
				proto, err := mappingIpProtocolToForwardingProtocol(IPProtocol(vmFwdRule.IpProtocol))
				if err != nil {
					eLog.LogError("Failed to prepare IPForwarding: %v", err)
					return config, err
				}
				fwdRule := &ipForwardings[idx]
				fwdRule.ResourceName = vmFwdRule.TFLocalName
				fwdRule.Name = vmFwdRule.Name
				fwdRule.Protocol = proto
				fwdRule.AllPorts = vmFwdRule.AllPorts
				if addr := vmFwdRule.Addr; addr != nil {
					// In clean-IP projects, addresses are always statically set in a "google_compute_address" resource.
					fwdRule.Address = addr.Address
					fwdRule.AddressResourceName = addr.TFLocalName
					fwdRule.AddressName = addr.Name
				} else {
					// In non-clean-IP projects, addresses are directly set in the "google_compute_forwarding_rule" resource.
					fwdRule.Address = utils.StripSurroundingQuotes(vmFwdRule.IpAddressOrRef)
				}
				if ti := vmFwdRule.DependsOn; ti != nil {
					fwdRule.DependsOnResourceName = ti.TFLocalName
				}
				if vmFwdRule.IPVersion == IPV6 {
					fwdRule.IPVersion = deployment.IPV6
					fwdRule.Subnet = vmFwdRule.SubnetUrl
				}
				// Ignore labels added external of orchestrator provisioning
				fwdRule.IgnoreChangesOnLabels = ignoreLabels
			}
			inst.IPForwardings = ipForwardings

			if vmCfg := vm.NetworkPerformanceConfig; vmCfg != nil {
				inst.NetworkPerformanceConfig = &deployment.NetworkPerformanceConfig{
					TotalEgressBandwidthTier: deployment.EgressBandwidthTier(vmCfg.TotalEgressBandwidthTier),
				}
			}

			// Ignore labels added external of orchestrator provisioning
			inst.IgnoreChangesOnLabels = ignoreInstanceLabels

			// Explicit provider reference to a zonal provider alias, needed for state importing.
			// Also add attribute and label changes to ignore at import
			if input.ZonalProvider {
				inst.ProviderOverride = "google." + inst.Zone
				inst.IgnoreChangesOnAttributes = ignoreInstanceAttributesAtImport
				inst.IgnoreChangesOnLabels = append(inst.IgnoreChangesOnLabels, ignoreInstanceLabelsAtImport...)
			}

			// Ignore tags if traffic mirroring is enabled for this (tenant, region, cloudProvider)
			//   ignoring all the tags for now as those tags are added externally without a fixed pattern
			//   TODO: revisit once we provide a mean to retrieve the exact tags added by traffic mirroring lambda
			if input.TrafficMirroringCfgEntry != nil {
				inst.IgnoreChangesOnAttributes = append(inst.IgnoreChangesOnAttributes, ignoreInstanceAttributesWithTrafficMirroring...)
			}

			instances = append(instances, inst)
		}
	}
	config.VMInstances = instances
	var retPBR []deployment.PBR
	if nlb := input.Nlb; nlb != nil {
		config.ELBs, config.ILBs, config.UMIGs, retPBR, err = PrepareDeploymentLoadBalancer(nlb, input.ZonalProvider, eLog)
		if retPBR != nil {
			config.NetworkServices = deployment.NetworkServices{PBRs: retPBR}
		}
		if err != nil {
			return config, err
		}
	}

	if colo := input.ColoOnboardings; len(colo) > 0 {
		config.PrivateConnectPeerings, err = PrepareColoPrivateConnectPeering(
			input.CustId, input.Project, input.EdgeLocationRegion, input.ColoOnboardings, input.ColoLinks, input.ColoLinkProjects, eLog)
		if err != nil {
			return config, err
		}
	}

	err = config.Validate()
	if err != nil {
		eLog.LogError("Failed to validate Config: %v, Config=%#v", err, *config)
		return config, err
	}
	return config, nil
}

type TransformedResources struct {
	Instances        []*instance.Instance
	Umigs            []*unmanaged.InstanceGroup
	Elbs             []*external.LoadBalancer
	Peerings         []*private_connect_peering.PrivateConnectPeering
	Ilbs             []*internallb.LoadBalancer
	PBRs             []*pbr.PBR
	DpNetworkFwRules []*firewall.Rule
}

func (tr *TransformedResources) Empty() bool {
	totalLen := len(tr.Instances) + len(tr.Umigs) + len(tr.Elbs) + len(tr.Peerings)
	return totalLen == 0
}

func GenerateTfConfigFromDeployment(input DeploymentInput, tplDir string, w io.Writer) (*deployment.Config, *TransformedResources, error) {
	eLog := input.ELog
	dConfig, err := PrepareDeploymentConfig(input)
	if err != nil {
		eLog.LogError("Error preparing deployment config: %v", err)
		return nil, nil, err
	}
	eLog.LogInfo("Generating Terraform configuration for resources with config = %+v, deploymentInput = %+v", dConfig, input)
	transformed := &TransformedResources{}
	dProvider := dgcp.TransformProvider(*dConfig)
	dProvider.GenerateTFFile(tplDir, w)
	if err != nil {
		eLog.LogError("Error generating provider TF config: %v, provider=%#v", err, dProvider)
		return nil, nil, err
	}
	for _, inst := range dConfig.VMInstances {
		rs := dgcp.TransformInstance(*dConfig, inst)
		err = rs.GenerateTFFile(tplDir, w, io.Discard)
		if err != nil {
			eLog.LogError("Error generating instance TF config: %v, inst=%#v", err, inst)
			return nil, nil, err
		}
		transformed.Instances = append(transformed.Instances, &rs)
	}
	//prepare and process firewallRules for NGPA
	fwRules, networkId := PrepareNgpaFirewallConfig(input.DpNetworkFwRules, eLog)
	network := deployment.Network{
		Name:                     networkId,
		SkipResourceProvisioning: true,
	}
	if dConfig.NetworkServices.Networks == nil {
		dConfig.NetworkServices.Networks = make([]deployment.Network, 0)
	}
	dConfig.NetworkServices.Networks = append(dConfig.NetworkServices.Networks, network)

	for _, fwRule := range fwRules {
		dConfig.NetworkServices.Networks[0].FirewallRules = append(dConfig.NetworkServices.Networks[0].FirewallRules, fwRule)
		rs := dgcp.TransformFirewallRule(fwRule, networkId)
		err = rs.GenerateTFFile(tplDir, w, io.Discard)
		if err != nil {
			eLog.LogError("Error generating NLB heathcheck firewall rules TF config: %v, inst=%#v", err, fwRule)
			return nil, nil, err
		}
		transformed.DpNetworkFwRules = append(transformed.DpNetworkFwRules, &rs)
	}
	for _, umig := range dConfig.UMIGs {
		rs := dgcp.TransformUMIG(*dConfig, umig)
		err = rs.GenerateTFFile(tplDir, w, io.Discard)
		if err != nil {
			eLog.LogError("Error generating umig TF config: %v, umig=%#v", err, umig)
			return nil, nil, err
		}
		transformed.Umigs = append(transformed.Umigs, &rs)
	}
	for _, elb := range dConfig.ELBs {
		rs := dgcp.TransformELB(*dConfig, elb)
		err = rs.GenerateTFFile(tplDir, w, io.Discard)
		if err != nil {
			eLog.LogError("Error generating elb TF config: %v, elb=%#v", err, elb)
			return nil, nil, err
		}
		transformed.Elbs = append(transformed.Elbs, &rs)
	}
	for _, ilb := range dConfig.ILBs {
		rs := dgcp.TransformILB(*dConfig, ilb)
		err = rs.GenerateTFFile(tplDir, w, io.Discard)
		if err != nil {
			eLog.LogError("Error generating ilb TF config: %v, elb=%#v", err, ilb)
			return nil, nil, err
		}
		transformed.Ilbs = append(transformed.Ilbs, &rs)
	}
	for _, peering := range dConfig.PrivateConnectPeerings {
		rs := dgcp.TransformPrivateConnectPeering(*dConfig, peering)
		err = rs.GenerateTFFile(tplDir, w, io.Discard)
		if err != nil {
			eLog.LogError("Error generating private connect perring TF config: %v, elb=%#v", err, peering)
			return nil, nil, err
		}
		transformed.Peerings = append(transformed.Peerings, &rs)
	}

	for _, nspbr := range dConfig.NetworkServices.PBRs {
		rs := dgcp.TransformPolicyBasedRoute(*dConfig, nspbr)
		err = rs.GenerateTFFile(tplDir, w, io.Discard)
		if err != nil {
			eLog.LogError("Error generating pbr config: %v, elb=%#v", err, nspbr)
			return nil, nil, err
		}
		transformed.PBRs = append(transformed.PBRs, &rs)

	}
	return dConfig, transformed, nil
}
