package gcp

import "orchestrator/provision_service/definitions"

// FirewallRule defines access control to and from the instances of a network (VPC).
// corresponding configuration: google_compute_firewall
type FirewallRule struct {
	// Params collected
	definitions.FirewallRuleProps
	NetworkRef string // network to attach to

	// Params below are additional processed params based on the params above.
	// In the case of DM, these are derived in gcp_firewall_template.py.
	TFLocalName string
	Allowed     []*definitions.ProtocolInfoProps
	Denied      []*definitions.ProtocolInfoProps
}

type Subnet struct {
	// Params collected
	definitions.SubnetProps

	// Params below are additional processed params based on the params above.
	TFLocalName string
	SubnetUrl   string
}

type Network struct {
	// Params collected
	definitions.NetworkProps
	Subnets       []*Subnet
	FirewallRules []*FirewallRule

	// Params below are additional processed params based on the params above.
	TFLocalName string
	NetworkUrl  string
}
