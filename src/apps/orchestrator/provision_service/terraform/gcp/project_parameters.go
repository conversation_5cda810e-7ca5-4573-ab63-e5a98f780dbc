package gcp

type Role struct {
	ProjectId string
	Name      string

	// Params below are additional processed params based on the params above.
	TFLocalName string
}

type ServiceAccount struct {
	Id          string
	DisplayName string
	Roles       []*Role

	// Params below are additional processed params based on the params above.
	TFLocalName            string
	Identity               string
	IgnoreChangesArguments []string // tracks arguments to ignore changes for during TF apply, currently hardcoded
}

type Project struct {
	Name           string
	Id             string
	FolderId       string
	BillingAccount string
	HostProjectId  string

	// Params below are additional processed params based on the params above.
	TFLocalName                    string
	Labels                         map[string]string
	Services                       []string // currently this is a hardcoded list
	ServicesIgnoreChangesArguments []string
	RemoveDefaultSa                bool              // currently hardcoded to true
	ServiceAccounts                []*ServiceAccount // currently this is a hardcoded list
	CloudServicesAgentRoles        []*Role           // tracks additional roles to grant for cloud services agent, currently hardcoded
	IgnoreChangesArguments         []string          // tracks arguments to ignore changes for during TF apply, currently hardcoded
}
