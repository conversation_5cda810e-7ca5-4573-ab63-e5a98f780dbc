package gcp

import (
	"fmt"
	"log"
	"orchestrator/libs/go/dbaccess/models/colo_connect_link_master"
	"orchestrator/libs/go/dbaccess/models/colo_onboarding_master"
	"orchestrator/libs/go/dbaccess/models/network_load_balancer_config"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/terraform"
	"orchestrator/libs/go/utils"
	"orchestrator/provision_service/definitions"
	"regexp"
	"strconv"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.panw.local/pangolin/clogger"
	"go.panw.local/provision/deployment"
)

func TestGleanNlbParameters(t *testing.T) {
	// test VMs
	vmNotPrimary := VMInstance{
		Name:      "vmNotPrimary",
		IsPrimary: false,
	}
	vmNoDpNic := VMInstance{
		Name:      "vmNoDpNic",
		IsPrimary: true,
	}
	vmBehindNlbGood := VMInstance{
		Name:        "vmBehindNlbGood",
		IsPrimary:   true,
		HasDpNic:    true,
		IsBehindNlb: true,
		Zone:        "zone1",
		Region:      "region1",
		CleanIpTag:  true,
		NetIntfs:    []*NetworkInterface{{Network: "network1", Subnet: "subnet1"}},
	}
	vmBehindNlbBad := vmBehindNlbGood
	vmBehindNlbBad.Name = "vmBehindNlbBad"
	vmBehindNlbBad.Region = ""
	vmNotBehindNlbGood := vmBehindNlbGood
	vmNotBehindNlbGood.Name = "vmNotBehindNlbGood"
	vmNotBehindNlbGood.IsBehindNlb = false
	vmNlbGood := vmBehindNlbGood
	vmNlbGood.Name = "vmNlbGood"
	vmNlbGood.NodeType = terraform.NodeTypeNLB
	type testcase struct {
		description string
		vm          *VMInstance
		cfgSource   *VMInstance
	}

	testcases := []testcase{
		{
			description: "should not glean from not primary",
			vm:          &vmNotPrimary,
			cfgSource:   nil,
		},
		{
			description: "should not glean from VM without DP NIC",
			vm:          &vmNoDpNic,
			cfgSource:   nil,
		},
		{
			description: "should not glean from bad VM (info unavailable)",
			vm:          &vmBehindNlbBad,
			cfgSource:   nil,
		},
		{
			description: "should glean from good VM behind NLB",
			vm:          &vmBehindNlbGood,
			cfgSource:   &vmBehindNlbGood,
		},
		{
			description: "should skip gleaning from lower-precedence VM",
			vm:          &vmNotBehindNlbGood,
			cfgSource:   &vmBehindNlbGood,
		},
		{
			description: "should glean from NLB VM (highest precedence)",
			vm:          &vmNlbGood,
			cfgSource:   &vmNlbGood,
		},
	}

	eLog := utils.GetEventLogger(clogger.NewLogger(log.Writer()))
	nlb := &NLB{}
	for _, tc := range testcases {
		gleanNlbParameters(nlb, tc.vm, eLog)
		assert.Equal(t, tc.cfgSource, nlb.CfgSource, tc.description)
		if src := tc.cfgSource; src != nil {
			assert.Equal(t, src.Region, nlb.Region, "should glean Region correctly")
			assert.Equal(t, src.CleanIpTag, nlb.CleanIpTag, "should glean CleanIpTag correctly")
			assert.Equal(t, src.NetIntfs[0].Network, nlb.Network, "should glean Network correctly")
			assert.Equal(t, src.NetIntfs[0].Subnet, nlb.Subnet, "should glean Subnet correctly")
		}
	}
}

func compareTfConfig(t *testing.T, di DeploymentInput, actual strings.Builder, expectedResult string) {
	_, _, err := GenerateTfConfigFromDeployment(di, "../../resources/templates", &actual)
	assert.NoError(t, err, "GenerateTfConfigFromDeployment should not return an error")
	// log.Printf("Generated TF config:\n%v", string(actual.String()))

	// Remove empty lines
	regex := regexp.MustCompile("\n+")
	expectedResult = regex.ReplaceAllLiteralString(strings.TrimSpace(expectedResult), "\n")
	actualResult := regex.ReplaceAllLiteralString(strings.TrimSpace(actual.String()), "\n")

	expectedLines := strings.Split(expectedResult, "\n")
	actualLines := strings.Split(actualResult, "\n")
	l := len(expectedLines)
	if len(actualLines) < l {
		l = len(actualLines)
	}
	for i := 0; i < l; i++ {
		aLine := actualLines[i]
		eLine := expectedLines[i]
		// remove redundant spaces
		aLine = strings.Join(strings.Fields(strings.TrimSpace(aLine)), " ")
		eLine = strings.Join(strings.Fields(strings.TrimSpace(eLine)), " ")
		assert.Equal(t, eLine, aLine, fmt.Sprintf("Expecting line # %v to be the same", i))
	}
	assert.Equal(t, len(expectedLines), len(actualLines), "Expecting number of lines in result to match")
}

func TestGenerateTfConfigFromDeployment(t *testing.T) {
	eLog := utils.GetEventLogger(clogger.NewLogger(log.Writer()))
	project := "pa-sase-insights-dev-01"
	custId := int64(20001439)
	tenantId := int64(**********)
	workspace := "**********_asia-south1_gcp"
	region := "asia-south1"
	edgeLocationRegion := "ap-south-1"
	expectedResult := `terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "5.42.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "5.42.0"
    }
  }
}
provider "google" {
    credentials = file("credentials.json")
    project = "pa-sase-insights-dev-01"
    region = "asia-south1"
    user_project_override = true
    billing_project = "pa-sase-insights-dev-01"
}

provider "google-beta" {
    credentials = file("credentials.json")
    project = "pa-sase-insights-dev-01"
    region = "asia-south1"
    user_project_override = true
    billing_project = "pa-sase-insights-dev-01"
}

resource "google_compute_address" "gpgw_8593802_ap_south_1_transcontin_address" {
  name = "gpgw-8593802-ap-south-1-transcontin-address"
  address_type = "EXTERNAL"
  region = "asia-south1"
}

resource "google_compute_instance" "gp8593802" {
  name = "gpgw-8593802-ap-south-1-transcontin"
  zone = "asia-south1-a"
  machine_type = "e2-standard-4"
  can_ip_forward = true
  allow_stopping_for_update = true
  labels = {
    customer = "**********"
  }
  boot_disk {
    device_name = "boot"
    auto_delete = true
    initialize_params {
      image = "https://www.googleapis.com/compute/v1/projects/image-gpcs-nonprod-01/global/images/pa-vm-saas-gcp-10-2-3-c378-saas"
    }
  }
  network_interface {
    network = "https://www.googleapis.com/compute/v1/projects/pa-sase-insights-dev-01/global/networks/gpcs-vpc-dp-**********"
    subnetwork = "https://www.googleapis.com/compute/v1/projects/pa-sase-insights-dev-01/regions/asia-south1/subnetworks/subnet-dp-asia-south1-**********"
    access_config {
      nat_ip = "**************"
    }
  }
  network_interface {
    network = "https://www.googleapis.com/compute/v1/projects/host-gpcs-test-01/global/networks/shared-mgmt-vpc"
    subnetwork = "https://www.googleapis.com/compute/v1/projects/host-gpcs-test-01/regions/asia-south1/subnetworks/shared-mgmt-asia-south1-01"
    access_config {
      nat_ip = google_compute_address.gpgw_8593802_ap_south_1_transcontin_address.address
    }
  }
  metadata = {
    cloud_provider = "gcp"
  }

  service_account {
    email = "<EMAIL>"
    scopes = ["https://www.googleapis.com/auth/cloud-platform"]
  }
  tags = [
    "fw-8593802",
  ]
  lifecycle {
    ignore_changes = [
      labels["goog-dm"],
      labels["goog-ccm"],
      labels["name"],
      labels["instancetype"],
      labels["tenantid"],
      labels["supertenantid"],
      labels["tenantname"],
      labels["computeregion"],
      labels["theater"],
      labels["environment"],
      labels["cloudprovider"],
      labels["resource_domain"],
      labels["using_backbone"],
      labels["interconnect_resource"],
      labels["licensetype"],
      labels["nativemachinetype"],
      labels["instancesize"],
      labels["contactinfo"],
      labels["timestamp"],
    ]
  }
}

resource "google_compute_forwarding_rule" "forwarding_rule_34_100_138_238_l3default" {
  name = "forwarding-rule-34-100-138-238-l3default"
  provider = google-beta
  region = "asia-south1"
  ip_protocol = "TCP"
  load_balancing_scheme = "EXTERNAL"
  all_ports = false
  target = google_compute_target_instance.gp8593802.self_link
  ip_address = "**************"
  depends_on = [google_compute_target_instance.gp8593802]
  lifecycle {
    ignore_changes = [
      labels["goog-dm"],
      labels["goog-ccm"],
    ]
  }
}

resource google_compute_firewall "firewall_rule_gpgw_8593802_ap_south_1_transcontin" {
    name = "firewall-rule-gpgw-8593802-ap-south-1-transcontin"
    network = "gpcs-vpc-dp-**********"
    project = "pa-sase-insights-dev-01"
    direction = "INGRESS"
    source_ranges = [
        "0.0.0.0/0",
    ]
    target_tags = [
        "fw-8593802",
    ]
    allow {
        protocol = "tcp"
        ports = [
            "22",
        ]
    }
}

resource "google_compute_target_instance" "gp8593802" {
  name = "target-instance-gpgw-8593802-ap-south-1-transcontin"
  instance = google_compute_instance.gp8593802.self_link
  zone = "asia-south1-a"
}`

	vmInstance := &VMInstance{
		TenantId:    "**********",
		ClusterId:   8593802,
		TFLocalName: "gp8593802",
		NodeType:    49,
		Primary:     nil,
		IsPrimary:   true,
		HACluster:   false,
		MpInstance:  false,

		Name:              "gpgw-8593802-ap-south-1-transcontin",
		Id:                8593802,
		Zone:              "asia-south1-a",
		Region:            "asia-south1",
		ImageProject:      "image-gpcs-nonprod-01",
		SharedMgmtVPCProj: "host-gpcs-test-01",
		Image:             "pa-vm-saas-gcp-10-2-3-c378-saas",
		ServiceAccount:    "<EMAIL>",
		CleanIpTag:        false,
		IsBehindNlb:       false,
		PublicIp:          "**************",
		EgressIpList:      "\"271\": \"**************\"",
		InboundAccessList: "",
		MachineType:       "e2-standard-4",
		CapacityType:      "PA-CAP530",
		UserData:          "cloud_provider=gcp",
		Labels:            []string{"customer = \"**********\""},

		AllowedAsTarget:   true,
		Metadata:          []string{"cloud_provider = \"gcp\""},
		InboundIps:        nil,
		InboundIpKeys:     nil,
		AllowedFwRules:    nil,
		AllowedFwRuleKeys: nil,

		MachineTypeUrl:           "https://www.googleapis.com/compute/v1/projects/pa-sase-insights-dev-01/zones/asia-south1-a/machineTypes/e2-standard-4",
		ImageUrl:                 "https://www.googleapis.com/compute/v1/projects/image-gpcs-nonprod-01/global/images/pa-vm-saas-gcp-10-2-3-c378-saas",
		EgressIpsSansNat:         []string{"**************"},
		Tags:                     []string{"fw-8593802"},
		SetNatIpAsPublicIpOutput: false,
	}
	vmInstance.Addrs = map[AddressType][]*Address{
		ApiAssigned: []*Address{
			&Address{
				Name:            "gpgw-8593802-ap-south-1-transcontin-address",
				TFLocalName:     "gpgw_8593802_ap_south_1_transcontin_address",
				Address:         "",
				Region:          "asia-south1",
				ExplicitAddress: false,
			},
		},
	}
	vmInstance.NetIntfs = []*NetworkInterface{
		&NetworkInterface{
			Name:               "nic-dp",
			Network:            "gpcs-vpc-dp-**********",
			Subnet:             "subnet-dp-asia-south1-**********",
			HasExternalIp:      true,
			StaticIp:           "**************",
			Project:            "pa-sase-insights-dev-01",
			NetworkUrl:         "https://www.googleapis.com/compute/v1/projects/pa-sase-insights-dev-01/global/networks/gpcs-vpc-dp-**********",
			SubnetUrl:          "https://www.googleapis.com/compute/v1/projects/pa-sase-insights-dev-01/regions/asia-south1/subnetworks/subnet-dp-asia-south1-**********",
			NatIpOrRef:         "\"**************\"",
			ExplicitNatIpOrRef: true,
		},
		&NetworkInterface{
			Name:               "nic-mgmt",
			Network:            "shared-mgmt-vpc",
			Subnet:             "shared-mgmt-asia-south1-01",
			HasExternalIp:      true,
			Project:            "host-gpcs-test-01",
			NetworkUrl:         "https://www.googleapis.com/compute/v1/projects/host-gpcs-test-01/global/networks/shared-mgmt-vpc",
			SubnetUrl:          "https://www.googleapis.com/compute/v1/projects/host-gpcs-test-01/regions/asia-south1/subnetworks/shared-mgmt-asia-south1-01",
			NatIpOrRef:         fmt.Sprintf("google_compute_address.%s.address", vmInstance.Addrs[ApiAssigned][0].TFLocalName),
			ExplicitNatIpOrRef: true,
			Addr:               vmInstance.Addrs[ApiAssigned][0],
		},
	}
	vmInstance.TargetInstance = &TargetInstance{
		Name:        "target-instance-gpgw-8593802-ap-south-1-transcontin",
		Zone:        "asia-south1-a",
		Instance:    vmInstance,
		TFLocalName: "gp8593802",
	}
	vmInstance.L3FwdRules = []*ForwardingRule{
		&ForwardingRule{
			Name:           "forwarding-rule-34-100-138-238-l3default",
			Region:         "asia-south1",
			IpAddressOrRef: "\"**************\"",
			IpProtocol:     "TCP",
			AllPorts:       false,
			Target:         vmInstance.TargetInstance,
			DependsOn:      vmInstance.TargetInstance,
			TFLocalName:    "forwarding_rule_34_100_138_238_l3default",
		},
	}
	protoInfo := &definitions.ProtocolInfoProps{"tcp", []string{"22"}}
	vmInstance.FwRules = []*FirewallRule{
		&FirewallRule{
			FirewallRuleProps: definitions.FirewallRuleProps{
				Name:         "firewall-rule-gpgw-8593802-ap-south-1-transcontin",
				SourceRanges: []string{"0.0.0.0/0"},
				Allow:        true,
				ProtocolInfo: []*definitions.ProtocolInfoProps{protoInfo},
				TargetTags:   []string{"fw-8593802"},
			},
			NetworkRef:  vmInstance.NetIntfs[0].NetworkUrl,
			TFLocalName: "firewall_rule_gpgw_8593802_ap_south_1_transcontin",
			Allowed:     []*definitions.ProtocolInfoProps{protoInfo},
			Denied:      nil,
		},
	}
	vmInstances := map[int64][]*VMInstance{vmInstance.ClusterId: []*VMInstance{vmInstance}}

	var actual strings.Builder
	di := DeploymentInput{
		CustId:             custId,
		TenantId:           tenantId,
		Workspace:          workspace,
		Project:            project,
		Region:             region,
		EdgeLocationRegion: edgeLocationRegion,
		Clusters:           vmInstances,
		Nlb:                nil,
		ColoOnboardings:    nil,
		ColoLinks:          nil,
		ZonalProvider:      false,
		ELog:               eLog,
	}
	compareTfConfig(t, di, actual, expectedResult)
}

func TestGenerateTfConfigColo(t *testing.T) {
	eLog := utils.GetEventLogger(clogger.NewLogger(log.Writer()))
	project := "cust-gpcs-7z6vt4hzvt3lz582870e"
	custId := int64(500003436)
	tenantId := int64(**********)
	workspace := "**********_us-west2_gcp_test"
	region := "us-west2"
	edgeLocationRegion := "us-west-201"
	expectedResult := `terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "5.42.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "5.42.0"
    }
  }
}
provider "google" {
  credentials           = file("credentials.json")
  project               = "cust-gpcs-7z6vt4hzvt3lz582870e"
  region                = "us-west2"
  user_project_override = true
  billing_project       = "cust-gpcs-7z6vt4hzvt3lz582870e"
}

provider "google-beta" {
  credentials           = file("credentials.json")
  project               = "cust-gpcs-7z6vt4hzvt3lz582870e"
  region                = "us-west2"
  user_project_override = true
  billing_project       = "cust-gpcs-7z6vt4hzvt3lz582870e"
}
resource "google_compute_interconnect_attachment" "colo-int-att-test1" {
  name                     = "colo-int-att-test1"
  router                   = "https://www.googleapis.com/compute/v1/projects/cust-gpcs-7z6vt4hzvt3lz582870e/regions/us-west2/routers/colo-rtr-us-west-201-500003436"
  admin_enabled            = true
  mtu                      = 1500
  edge_availability_domain = "AVAILABILITY_DOMAIN_1"
  type                     = "PARTNER"
  ipsec_internal_addresses = []
}
resource "google_compute_interconnect_attachment" "colo-int-att-test2" {
  name                     = "colo-int-att-test2"
  router                   = "https://www.googleapis.com/compute/v1/projects/cust-gpcs-7z6vt4hzvt3lz582870e/regions/us-west2/routers/colo-rtr-us-west-201-500003436"
  admin_enabled            = true
  interconnect = "https://www.googleapis.com/compute/v1/projects/cust-gpcs-supertenant/global/interconnects/us-west2"
  mtu                      = 1500
  bandwidth                = "BPS_2G"
  type                     = "DEDICATED"
  vlan_tag8021q            = 10
  ipsec_internal_addresses = []
}`

	coloOnboardings := []*colo_onboarding_master.Row{
		&colo_onboarding_master.Row{
			ID:               sql.Int64(521),
			Name:             sql.String("test1"),
			Type:             sql.String("partner"),
			ComputeRegionID:  sql.Int64(201),
			LinkID:           sql.Int64(562),
			InterconnectZone: sql.String("Zone1"),
			Bandwidth:        sql.Int64(2),
			Bgp:              sql.String("{\"bgp_peer_asn\": \"20001001\", \"md5_secret\": null, \"vlan_id\": null, \"bgp_bfd\": null}"),
			IsDeleted:        sql.Bool(false),
			CustID:           sql.Int64(500003436),
			CrState:          sql.Int64(2),
			VlanState:        sql.Int64(2),
			SaltProfile:      sql.String("{\"peer\":\"colo-int-att-test1\"}"),
		},
		&colo_onboarding_master.Row{
			ID:               sql.Int64(523),
			Name:             sql.String("test2"),
			Type:             sql.String("dedicated"),
			ComputeRegionID:  sql.Int64(201),
			LinkID:           sql.Int64(561),
			InterconnectZone: sql.String("Zone2"),
			Bandwidth:        sql.Int64(2),
			Bgp:              sql.String("{\"bgp_peer_asn\": \"20001001\", \"md5_secret\": null, \"vlan_id\": \"10\", \"bgp_bfd\": null}"),
			IsDeleted:        sql.Bool(false),
			CustID:           sql.Int64(500003436),
			CrState:          sql.Int64(2),
			VlanState:        sql.Int64(2),
			SaltProfile:      sql.String("{\"peer\":\"colo-int-att-test2\"}"),
		},
	}
	coloLinks := []*colo_connect_link_master.Row{
		&colo_connect_link_master.Row{
			ID:     sql.Int64(561),
			Name:   sql.String("us-west2"),
			CustID: sql.Int64(500003435),
		},
	}
	coloLinkPtjd := map[int64]string{500003436: "cust-gpcs-7z6vt4hzvt3lz582870e", 500003435: "cust-gpcs-supertenant"}
	var actual strings.Builder
	di := DeploymentInput{
		CustId:             custId,
		TenantId:           tenantId,
		Workspace:          workspace,
		Project:            project,
		Region:             region,
		EdgeLocationRegion: edgeLocationRegion,
		Clusters:           nil,
		Nlb:                nil,
		ColoOnboardings:    coloOnboardings,
		ColoLinks:          coloLinks,
		ColoLinkProjects:   coloLinkPtjd,
		ZonalProvider:      false,
		ELog:               eLog,
	}
	compareTfConfig(t, di, actual, expectedResult)
}

func TestPrepareDeploymentLoadBalancerBasic(t *testing.T) {
	lb := &NLB{
		Name:   "test-nlb",
		Region: "us-west1",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(123),
			RegionID:                           sql.Int64(1),
			BackendServiceProtocol:             sql.Int64(1), // TCP
			SessionAffinity:                    sql.Int64(1), // CLIENT_IP
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1), // NEVER_PERSIST
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1), // HTTP
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1), // TCP
		},
		CfgSource: &VMInstance{
			TFLocalName: "test_vm",
			Name:        "test-vm",
		},
		Network:    "test-network",
		Subnet:     "test-subnet",
		ZonesList:  []string{"us-west1-a"},
		PublicIp:   "*******",
		CleanIpTag: true,
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {
				{
					TFLocalName: "test_vm",
					Name:        "test-vm",
				},
			},
		},
	}
	lbs := []*NLB{lb}

	eLog := utils.GetEventLogger(clogger.NewLogger(nil))
	elbs, _, umigs, pbrs, err := PrepareDeploymentLoadBalancer(lbs, false, eLog)

	assert.NoError(t, err)
	assert.Len(t, elbs, 1)
	assert.Len(t, umigs, 1)
	assert.Len(t, pbrs, 0)

	assert.Equal(t, "test-nlb", elbs[0].Name)
	assert.Equal(t, "us-west1", elbs[0].Region)
	assert.Equal(t, "test-network", elbs[0].Network)
	assert.Equal(t, "test-subnet", elbs[0].Subnet)
	assert.Equal(t, deployment.TCP, elbs[0].Protocol)
	assert.Equal(t, deployment.SupportedSA("CLIENT_IP"), elbs[0].SessionAffinity)
	assert.Equal(t, deployment.LBExternal, elbs[0].LoadBalancingScheme)

	assert.Equal(t, "test-nlb-umig", umigs[0].Name)
	assert.Equal(t, "us-west1-a", umigs[0].Zone)
	assert.Equal(t, "test-network", umigs[0].Network)
	assert.Equal(t, "test-subnet", umigs[0].Subnet)
}

func TestPrepareDeploymentLoadBalancerWithMultipleZones(t *testing.T) {
	lb := &NLB{
		Name:   "test-nlb",
		Region: "us-west1",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(123),
			RegionID:                           sql.Int64(1),
			BackendServiceProtocol:             sql.Int64(1), // TCP
			SessionAffinity:                    sql.Int64(1), // CLIENT_IP
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1), // NEVER_PERSIST
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1), // HTTP
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1), // TCP
		},
		CfgSource: &VMInstance{
			TFLocalName: "test_vm1",
			Name:        "test-vm1",
		},
		Network:   "test-network",
		Subnet:    "test-subnet",
		ZonesList: []string{"us-west1-a", "us-west1-b"},
		PublicIp:  "*******",
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {
				{
					TFLocalName: "test_vm1",
					Name:        "test-vm1",
				},
			},
			"us-west1-b": {
				{
					TFLocalName: "test_vm2",
					Name:        "test-vm2",
				},
			},
		},
	}
	lbs := []*NLB{lb}

	eLog := utils.GetEventLogger(clogger.NewLogger(nil))
	elbs, _, umigs, pbrs, err := PrepareDeploymentLoadBalancer(lbs, false, eLog)

	assert.NoError(t, err)
	assert.Len(t, elbs, 1)
	assert.Len(t, umigs, 2)
	assert.Len(t, pbrs, 0)

	assert.Equal(t, "test-nlb-umig", umigs[0].Name)
	assert.Equal(t, "test-nlb-umig-1", umigs[1].Name)
}

func TestPrepareDeploymentLoadBalancerWithIPv6(t *testing.T) {
	lb := &NLB{
		Name:   "test-nlb",
		Region: "us-west1",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(123),
			RegionID:                           sql.Int64(1),
			BackendServiceProtocol:             sql.Int64(1), // TCP
			SessionAffinity:                    sql.Int64(1), // CLIENT_IP
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1), // NEVER_PERSIST
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1), // HTTP
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1), // TCP
		},
		CfgSource:            &VMInstance{TFLocalName: "test_vm", Name: "test-vm"},
		Network:              "test-network",
		Subnet:               "test-subnet",
		ZonesList:            []string{"us-west1-a"},
		PublicIp:             "*******",
		EgressIpv6ListSubnet: `{"1": "2001:db8::/64"}`,
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {{TFLocalName: "test_vm", Name: "test-vm"}},
		},
		ComputeRegionIdx: "1",
		CleanIpTag:       true,
	}
	lbs := []*NLB{lb}
	eLog := utils.GetEventLogger(clogger.NewLogger(nil))
	elbs, _, _, _, err := PrepareDeploymentLoadBalancer(lbs, false, eLog)

	assert.NoError(t, err)
	assert.Len(t, elbs, 1)
	assert.Len(t, elbs[0].IPForwardings, 2)
	assert.Equal(t, deployment.IPVersion("IPV6"), elbs[0].IPForwardings[0].IPVersion)
	assert.Equal(t, "", elbs[0].IPForwardings[0].AddressName)
	assert.Equal(t, "2001:db8::/64", elbs[0].IPForwardings[0].Subnet)
}

func TestPrepareDeploymentLoadBalancerWithEgressIpsSansNat(t *testing.T) {
	lb := &NLB{
		Name:   "test-nlb",
		Region: "us-west1",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(123),
			RegionID:                           sql.Int64(1),
			BackendServiceProtocol:             sql.Int64(1), // TCP
			SessionAffinity:                    sql.Int64(1), // CLIENT_IP
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1), // NEVER_PERSIST
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1), // HTTP
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1), // TCP
		},
		CfgSource:        &VMInstance{TFLocalName: "test_vm", Name: "test-vm"},
		Network:          "test-network",
		Subnet:           "test-subnet",
		ZonesList:        []string{"us-west1-a"},
		PublicIp:         "*******",
		EgressIpsSansNat: []string{"*******", "**********"},
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {{TFLocalName: "test_vm", Name: "test-vm"}},
		},
	}
	lbs := []*NLB{lb}

	eLog := utils.GetEventLogger(clogger.NewLogger(nil))
	elbs, _, _, _, err := PrepareDeploymentLoadBalancer(lbs, false, eLog)

	assert.NoError(t, err)
	assert.Len(t, elbs, 1)
	assert.Len(t, elbs[0].IPForwardings, 3)
	assert.Equal(t, "*******", elbs[0].IPForwardings[0].Address)
	assert.Equal(t, "*******", elbs[0].IPForwardings[1].Address)
	assert.Equal(t, "**********", elbs[0].IPForwardings[2].Address)
}

func TestPrepareDeploymentLoadBalancerBasicSP(t *testing.T) {
	lb := &NLB{
		Name:   "test-nlb",
		Region: "us-west1",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(123),
			RegionID:                           sql.Int64(1),
			BackendServiceProtocol:             sql.Int64(1), // TCP
			SessionAffinity:                    sql.Int64(1), // CLIENT_IP
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1), // NEVER_PERSIST
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1), // HTTP
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1), // TCP
		},
		CfgSource: &VMInstance{
			TFLocalName:       "test_vm",
			Name:              "test-vm",
			IsSpInterconnect:  true,
			SharedMgmtVPCProj: "host-gpcs-dev-01",
		},
		Network:    "test-network",
		Subnet:     "test-subnet",
		ZonesList:  []string{"us-west1-a"},
		PublicIp:   "*******",
		CleanIpTag: true,
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {
				{
					TFLocalName: "test_vm",
					Name:        "test-vm",
				},
			},
		},
	}
	lbs := []*NLB{lb}

	eLog := utils.GetEventLogger(clogger.NewLogger(log.Writer()))
	elbs, ilbs, umigs, pbrs, err := PrepareDeploymentLoadBalancer(lbs, false, eLog)

	assert.NoError(t, err)
	assert.Len(t, ilbs, 1)
	assert.Len(t, elbs, 0)
	assert.Len(t, umigs, 1)
	assert.Len(t, pbrs, 0)

	assert.Equal(t, "test-nlb", ilbs[0].Name)
	assert.Equal(t, "us-west1", ilbs[0].Region)
	assert.Equal(t, "test-network", ilbs[0].Network)
	assert.Equal(t, "test-subnet", ilbs[0].Subnet)
	assert.Equal(t, deployment.TCP, ilbs[0].Protocol)
	assert.Equal(t, deployment.SupportedSA("CLIENT_IP"), ilbs[0].SessionAffinity)
	assert.Equal(t, "INTERNAL", string(ilbs[0].LoadBalancingScheme))

	assert.Equal(t, "test-nlb-umig", umigs[0].Name)
	assert.Equal(t, "us-west1-a", umigs[0].Zone)
	assert.Equal(t, "test-network", umigs[0].Network)
	assert.Equal(t, "test-subnet", umigs[0].Subnet)

	// For interconnect ILB it doesn't support strong session affinity
	assert.Equal(t, false, ilbs[0].ConnTracking.EnableStrongAffinity)
	assert.Equal(t, "host-gpcs-dev-01", umigs[0].ProjectOverride)
	assert.Equal(t, "host-gpcs-dev-01", ilbs[0].ProjectOverride)
}

func TestPrepareDeploymentLoadBalancerBasic5G(t *testing.T) {
	lb := &NLB{
		Name:   "test-nlb",
		Region: "us-west1",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(123),
			RegionID:                           sql.Int64(1),
			BackendServiceProtocol:             sql.Int64(1), // TCP
			SessionAffinity:                    sql.Int64(1), // CLIENT_IP
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1), // NEVER_PERSIST
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1), // HTTP
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1), // TCP
		},
		CfgSource: &VMInstance{
			TFLocalName:       "test_vm",
			Name:              "test-vm",
			IsSpInterconnect:  true,
			SharedMgmtVPCProj: "host-gpcs-dev-01",
		},
		Network:    "test-network",
		Subnet:     "test-subnet",
		ZonesList:  []string{"us-west1-a"},
		PublicIp:   "*******",
		CleanIpTag: true,
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {
				{
					TFLocalName: "test_vm",
					Name:        "test-vm",
				},
			},
		},
	}

	lb5g := &NLB{
		Name:   "test-5g-ilb",
		Region: "us-west1",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(123),
			RegionID:                           sql.Int64(1),
			BackendServiceProtocol:             sql.Int64(1), // TCP
			SessionAffinity:                    sql.Int64(1), // CLIENT_IP
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1), // NEVER_PERSIST
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1), // HTTP
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1), // TCP
		},
		CfgSource: &VMInstance{
			TFLocalName:       "svc-ilb",
			Name:              "svc-ilb",
			IsSpInterconnect:  true,
			SharedMgmtVPCProj: "host-gpcs-dev-01",
			Sase5gEnabled:     true,
		},
		Network:    "test-5g-network",
		Subnet:     "test-5g-subnet",
		ZonesList:  []string{"us-west1-a"},
		PublicIp:   "*******",
		CleanIpTag: true,
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {
				{
					TFLocalName: "test_vm",
					Name:        "test-vm",
				},
			},
		},
	}

	// Both the load balancers will share the same name.
	lb5g.CommonBaseName = lb.Name
	lbs := []*NLB{lb, lb5g}

	eLog := utils.GetEventLogger(clogger.NewLogger(log.Writer()))
	elbs, ilbs, umigs, pbrs, err := PrepareDeploymentLoadBalancer(lbs, false, eLog)

	assert.NoError(t, err)
	assert.Len(t, ilbs, 2)
	assert.Len(t, elbs, 0)
	assert.Len(t, umigs, 1)
	assert.Len(t, pbrs, 0)

	assert.Equal(t, "test-nlb", ilbs[0].Name)
	assert.Equal(t, "us-west1", ilbs[0].Region)
	assert.Equal(t, "test-network", ilbs[0].Network)
	assert.Equal(t, "test-subnet", ilbs[0].Subnet)
	assert.Equal(t, deployment.TCP, ilbs[0].Protocol)
	assert.Equal(t, deployment.SupportedSA("CLIENT_IP"), ilbs[0].SessionAffinity)
	assert.Equal(t, "INTERNAL", string(ilbs[0].LoadBalancingScheme))

	assert.Equal(t, "test-nlb-umig", umigs[0].Name)
	assert.Equal(t, "us-west1-a", umigs[0].Zone)
	assert.Equal(t, "test-network", umigs[0].Network)
	assert.Equal(t, "test-subnet", umigs[0].Subnet)

	// For interconnect ILB it doesn't support strong session affinity
	assert.Equal(t, false, ilbs[0].ConnTracking.EnableStrongAffinity)
	assert.Equal(t, "host-gpcs-dev-01", umigs[0].ProjectOverride)
	assert.Equal(t, "host-gpcs-dev-01", ilbs[0].ProjectOverride)

	assert.Equal(t, "test-5g-ilb", ilbs[1].Name)
	assert.Equal(t, "us-west1", ilbs[1].Region)
	assert.Equal(t, "test-5g-network", ilbs[1].Network)
	assert.Equal(t, "test-5g-subnet", ilbs[1].Subnet)
	assert.Equal(t, deployment.TCP, ilbs[1].Protocol)
	assert.Equal(t, deployment.SupportedSA("CLIENT_IP"), ilbs[1].SessionAffinity)
	assert.Equal(t, "INTERNAL", string(ilbs[1].LoadBalancingScheme))
}

func TestCreateUMIGsForLoadBalancerBasic(t *testing.T) {
	// Test single zone UMIG creation
	lb := &NLB{
		Name:   "test-nlb",
		Region: "us-west1",
		CfgSource: &VMInstance{
			TFLocalName: "test_vm",
			Name:        "test-vm",
		},
		ZonesList: []string{"us-west1-a"},
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {
				{
					TFLocalName: "test_vm",
					Name:        "test-vm",
				},
			},
		},
	}

	umigMap := make(map[string]deployment.UMIG)
	baseName := "test-nlb"
	network := "test-network"
	subnet := "test-subnet"

	result := createUMIGsForLoadBalancer(lb, baseName, network, subnet, "", false, umigMap)

	assert.Len(t, result, 1, "should create one UMIG for single zone")
	assert.Len(t, umigMap, 1, "should add one UMIG to global map")

	umig := result[0]
	assert.Equal(t, "test-nlb-umig", umig.Name, "should generate correct UMIG name")
	assert.Equal(t, "us-west1-a", umig.Zone, "should set correct zone")
	assert.Equal(t, network, umig.Network, "should set correct network")
	assert.Equal(t, subnet, umig.Subnet, "should set correct subnet")
	assert.Len(t, umig.Instances, 1, "should have one instance")
	assert.Equal(t, "test_vm", umig.Instances[0]["resource-name"], "should set correct instance resource name")
	assert.Equal(t, "test-vm", umig.Instances[0]["name"], "should set correct instance name")

	// Verify the key in umigMap
	expectedKey := "test-nlb-umig-us-west1-a"
	_, exists := umigMap[expectedKey]
	assert.True(t, exists, "should store UMIG with correct key format")
}

func TestCreateUMIGsForLoadBalancerSharedUMIGsWithCommonBaseName(t *testing.T) {
	// Test two load balancers sharing UMIGs using CommonBaseName
	commonBaseName := "shared-lb"

	// First load balancer (NLB)
	nlb := &NLB{
		Name:           "nlb-external",
		CommonBaseName: commonBaseName,
		Region:         "us-west1",
		CfgSource: &VMInstance{
			TFLocalName: "test_vm",
			Name:        "test-vm",
		},
		ZonesList: []string{"us-west1-a", "us-west1-b"},
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {
				{TFLocalName: "test_vm_a", Name: "test-vm-a"},
			},
			"us-west1-b": {
				{TFLocalName: "test_vm_b", Name: "test-vm-b"},
			},
		},
	}

	// Second load balancer (5G ILB) sharing the same instances
	ilb := &NLB{
		Name:           "ilb-5g",
		CommonBaseName: commonBaseName, // Same CommonBaseName to enable sharing
		Region:         "us-west1",
		CfgSource: &VMInstance{
			TFLocalName:   "test_vm",
			Name:          "test-vm",
			Sase5gEnabled: true,
		},
		ZonesList: []string{"us-west1-a", "us-west1-b"}, // Same zones
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {
				{TFLocalName: "test_vm_a", Name: "test-vm-a"}, // Same instances
			},
			"us-west1-b": {
				{TFLocalName: "test_vm_b", Name: "test-vm-b"},
			},
		},
	}

	umigMap := make(map[string]deployment.UMIG)
	network := "test-network"
	subnet := "test-subnet"

	// Create UMIGs for first load balancer (NLB)
	nlbUMIGs := createUMIGsForLoadBalancer(nlb, "nlb-external", network, subnet, "", false, umigMap)

	// Create UMIGs for second load balancer (5G ILB) - should reuse existing UMIGs
	ilbUMIGs := createUMIGsForLoadBalancer(ilb, "ilb-5g", network, subnet, "", false, umigMap)

	// Verify NLB created UMIGs
	assert.Len(t, nlbUMIGs, 2, "NLB should create two UMIGs")
	assert.Len(t, umigMap, 2, "should have two unique UMIGs in global map after NLB")

	// Verify ILB reused the same UMIGs
	assert.Len(t, ilbUMIGs, 2, "ILB should get two UMIGs (reused)")
	assert.Len(t, umigMap, 2, "should still have only two unique UMIGs in global map after ILB")

	// Verify both load balancers got the same UMIG instances (by reference)
	for i := 0; i < 2; i++ {
		assert.Equal(t, nlbUMIGs[i], ilbUMIGs[i], "UMIG %d should be identical between NLB and ILB", i)

		// Both should use CommonBaseName for naming
		expectedName := commonBaseName + "-umig"
		if i >= 1 {
			expectedName = commonBaseName + "-umig-" + strconv.Itoa(i)
		}
		assert.Equal(t, expectedName, nlbUMIGs[i].Name, "NLB UMIG %d should use CommonBaseName", i)
		assert.Equal(t, expectedName, ilbUMIGs[i].Name, "ILB UMIG %d should use CommonBaseName", i)
	}

	// Verify the keys in umigMap
	expectedKeys := []string{
		"shared-lb-umig-us-west1-a",
		"shared-lb-umig-1-us-west1-b",
	}

	for _, expectedKey := range expectedKeys {
		_, exists := umigMap[expectedKey]
		assert.True(t, exists, "should have UMIG with key: %s", expectedKey)
	}
}

func TestCreateUMIGsForLoadBalancerDifferentNamesNoSharing(t *testing.T) {
	// Test two load balancers with different names creating separate UMIGs
	// This scenario occurs when load balancers don't share CommonBaseName

	// First load balancer (NLB)
	nlb := &NLB{
		Name: "external-nlb",
		// No CommonBaseName - uses its own name for UMIG generation
		Region: "us-west1",
		CfgSource: &VMInstance{
			TFLocalName: "nlb_vm",
			Name:        "nlb-vm",
		},
		ZonesList: []string{"us-west1-a", "us-west1-b"},
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {
				{TFLocalName: "nlb_vm_a", Name: "nlb-vm-a"},
			},
			"us-west1-b": {
				{TFLocalName: "nlb_vm_b", Name: "nlb-vm-b"},
			},
		},
	}

	// Second load balancer (ILB) with different name and instances
	ilb := &NLB{
		Name: "internal-ilb",
		// No CommonBaseName - uses its own name for UMIG generation
		Region: "us-west1",
		CfgSource: &VMInstance{
			TFLocalName:   "ilb_vm",
			Name:          "ilb-vm",
			Sase5gEnabled: true,
		},
		ZonesList: []string{"us-west1-a", "us-west1-b"}, // Same zones but different instances
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {
				{TFLocalName: "ilb_vm_a", Name: "ilb-vm-a"}, // Different instances
			},
			"us-west1-b": {
				{TFLocalName: "ilb_vm_b", Name: "ilb-vm-b"},
			},
		},
	}

	umigMap := make(map[string]deployment.UMIG)
	network := "test-network"
	subnet := "test-subnet"

	// Create UMIGs for first load balancer (NLB)
	nlbUMIGs := createUMIGsForLoadBalancer(nlb, "external-nlb", network, subnet, "", false, umigMap)

	// Create UMIGs for second load balancer (ILB) - should create separate UMIGs
	ilbUMIGs := createUMIGsForLoadBalancer(ilb, "internal-ilb", network, subnet, "", false, umigMap)

	// Verify each load balancer created its own UMIGs
	assert.Len(t, nlbUMIGs, 2, "NLB should create two UMIGs")
	assert.Len(t, ilbUMIGs, 2, "ILB should create two UMIGs")
	assert.Len(t, umigMap, 4, "should have four unique UMIGs in global map (no sharing)")

	// Verify NLB UMIGs use NLB-specific names
	expectedNLBNames := []string{"external-nlb-umig", "external-nlb-umig-1"}
	for i, umig := range nlbUMIGs {
		assert.Equal(t, expectedNLBNames[i], umig.Name, "NLB UMIG %d should use NLB-specific name", i)
		assert.Equal(t, []string{"us-west1-a", "us-west1-b"}[i], umig.Zone, "NLB UMIG %d should be in correct zone", i)
		assert.Len(t, umig.Instances, 1, "NLB UMIG %d should have one instance", i)

		// Verify NLB instances
		expectedNLBInstances := []string{"nlb_vm_a", "nlb_vm_b"}
		assert.Equal(t, expectedNLBInstances[i], umig.Instances[0]["resource-name"], "NLB UMIG %d should have correct instance", i)
	}

	// Verify ILB UMIGs use ILB-specific names
	expectedILBNames := []string{"internal-ilb-umig", "internal-ilb-umig-1"}
	for i, umig := range ilbUMIGs {
		assert.Equal(t, expectedILBNames[i], umig.Name, "ILB UMIG %d should use ILB-specific name", i)
		assert.Equal(t, []string{"us-west1-a", "us-west1-b"}[i], umig.Zone, "ILB UMIG %d should be in correct zone", i)
		assert.Len(t, umig.Instances, 1, "ILB UMIG %d should have one instance", i)

		// Verify ILB instances
		expectedILBInstances := []string{"ilb_vm_a", "ilb_vm_b"}
		assert.Equal(t, expectedILBInstances[i], umig.Instances[0]["resource-name"], "ILB UMIG %d should have correct instance", i)
	}

	// Verify that UMIGs are completely separate (no sharing)
	for i := 0; i < 2; i++ {
		assert.NotEqual(t, nlbUMIGs[i], ilbUMIGs[i], "NLB and ILB UMIGs should be separate objects for zone %d", i)
		assert.NotEqual(t, nlbUMIGs[i].Name, ilbUMIGs[i].Name, "NLB and ILB UMIG names should be different for zone %d", i)
		assert.NotEqual(t, nlbUMIGs[i].Instances, ilbUMIGs[i].Instances, "NLB and ILB should have different instances for zone %d", i)
	}

	// Verify all UMIGs are stored with correct unique keys in the global map
	expectedKeys := []string{
		// NLB UMIGs
		"external-nlb-umig-us-west1-a",
		"external-nlb-umig-1-us-west1-b",
		// ILB UMIGs
		"internal-ilb-umig-us-west1-a",
		"internal-ilb-umig-1-us-west1-b",
	}

	for _, expectedKey := range expectedKeys {
		storedUMIG, exists := umigMap[expectedKey]
		assert.True(t, exists, "should have UMIG with key: %s", expectedKey)
		assert.NotEmpty(t, storedUMIG.Name, "stored UMIG should have a name for key: %s", expectedKey)
	}

	// Verify key uniqueness - no conflicts between NLB and ILB keys
	nlbKeys := []string{
		"external-nlb-umig-us-west1-a",
		"external-nlb-umig-1-us-west1-b",
	}
	ilbKeys := []string{
		"internal-ilb-umig-us-west1-a",
		"internal-ilb-umig-1-us-west1-b",
	}

	for i := 0; i < 2; i++ {
		nlbUMIG := umigMap[nlbKeys[i]]
		ilbUMIG := umigMap[ilbKeys[i]]

		assert.NotEqual(t, nlbUMIG, ilbUMIG, "UMIGs with different keys should be different objects for zone %d", i)
		assert.Contains(t, nlbUMIG.Name, "external-nlb", "NLB UMIG should contain NLB name for zone %d", i)
		assert.Contains(t, ilbUMIG.Name, "internal-ilb", "ILB UMIG should contain ILB name for zone %d", i)
	}
}

func TestPrepareDeploymentLoadBalancerInterconnectOnramp(t *testing.T) {
	lb := &NLB{
		Name:   "test-onramp-nlb",
		Region: "us-west1",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(123),
			RegionID:                           sql.Int64(1),
			BackendServiceProtocol:             sql.Int64(1), // TCP
			SessionAffinity:                    sql.Int64(1), // CLIENT_IP
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1), // NEVER_PERSIST
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1), // HTTP
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1), // TCP
		},
		CfgSource: &VMInstance{
			TFLocalName:          "test_onramp_vm",
			Name:                 "test-onramp-vm",
			IsInterconnectOnramp: true,
			SharedMgmtVPCProj:    "host-gpcs-dev-01",
		},
		Network:    "test-onramp-network",
		Subnet:     "test-onramp-subnet",
		ZonesList:  []string{"us-west1-a"},
		PublicIp:   "*******",
		CleanIpTag: true,
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {
				{
					TFLocalName:          "test_onramp_vm",
					Name:                 "test-onramp-vm",
					IsInterconnectOnramp: true,
				},
			},
		},
	}
	lbs := []*NLB{lb}

	eLog := utils.GetEventLogger(clogger.NewLogger(log.Writer()))
	elbs, ilbs, umigs, pbrs, err := PrepareDeploymentLoadBalancer(lbs, false, eLog)

	assert.NoError(t, err)
	assert.Len(t, ilbs, 1, "should create internal load balancer for interconnect onramp")
	assert.Len(t, elbs, 0, "should not create external load balancer for interconnect onramp")
	assert.Len(t, umigs, 1, "should create one UMIG")
	assert.Len(t, pbrs, 1, "should create one policy-based route for interconnect onramp")

	// Verify ILB configuration
	ilb := ilbs[0]
	assert.Equal(t, "test-onramp-nlb", ilb.Name)
	assert.Equal(t, "us-west1", ilb.Region)
	assert.Equal(t, "test-onramp-network", ilb.Network)
	assert.Equal(t, "test-onramp-subnet", ilb.Subnet)
	assert.Equal(t, deployment.TCP, ilb.Protocol)
	assert.Equal(t, deployment.LBInternal, ilb.LoadBalancingScheme)
	assert.Equal(t, "host-gpcs-dev-01", ilb.ProjectOverride)

	// For interconnect onramp, strong session affinity should be disabled
	assert.Equal(t, false, ilb.ConnTracking.EnableStrongAffinity)

	// Connection draining timeout should be 300 seconds for onramp
	assert.Equal(t, 300, ilb.ConnDraining.DrainingTimeoutSec)

	// Verify UMIG configuration
	umig := umigs[0]
	assert.Equal(t, "test-onramp-nlb-umig", umig.Name)
	assert.Equal(t, "us-west1-a", umig.Zone)
	assert.Equal(t, "test-onramp-network", umig.Network)
	assert.Equal(t, "test-onramp-subnet", umig.Subnet)
	assert.Equal(t, "host-gpcs-dev-01", umig.ProjectOverride)

	// Verify PBR configuration
	pbr := pbrs[0]
	assert.Equal(t, "pbr-test-onramp-nlb-fw-0", pbr.Name)
	assert.Equal(t, "test-onramp-network", pbr.Network)
	assert.Equal(t, "test-onramp-nlb-fw-0", pbr.NextHopILB)
	assert.Equal(t, 100, pbr.Priority)
	assert.NotNil(t, pbr.Filter)
	assert.Equal(t, "ALL", pbr.Filter.IPProtocol)
	assert.Equal(t, "0.0.0.0/0", pbr.Filter.SrcRange)
	assert.Equal(t, "0.0.0.0/0", pbr.Filter.DstRange)
	assert.NotNil(t, pbr.ApplyOn.CloudInterconnect)
	assert.Equal(t, "us-west1", pbr.ApplyOn.CloudInterconnect.Region)
}

func TestPrepareDeploymentLoadBalancerInterconnectOnrampWithSpInterconnect(t *testing.T) {
	lb := &NLB{
		Name:   "test-sp-onramp-nlb",
		Region: "us-west1",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(123),
			RegionID:                           sql.Int64(1),
			BackendServiceProtocol:             sql.Int64(1), // TCP
			SessionAffinity:                    sql.Int64(1), // CLIENT_IP
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1), // NEVER_PERSIST
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1), // HTTP
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1), // TCP
		},
		CfgSource: &VMInstance{
			TFLocalName:          "test_sp_onramp_vm",
			Name:                 "test-sp-onramp-vm",
			IsInterconnectOnramp: true,
			IsSpInterconnect:     true,
			SharedMgmtVPCProj:    "host-gpcs-dev-01",
		},
		Network:   "test-sp-onramp-network",
		Subnet:    "test-sp-onramp-subnet",
		ZonesList: []string{"us-west1-a"},
		PublicIp:  "*******",
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {
				{
					TFLocalName:          "test_sp_onramp_vm",
					Name:                 "test-sp-onramp-vm",
					IsInterconnectOnramp: true,
					IsSpInterconnect:     true,
				},
			},
		},
	}
	lbs := []*NLB{lb}

	eLog := utils.GetEventLogger(clogger.NewLogger(log.Writer()))
	elbs, ilbs, umigs, pbrs, err := PrepareDeploymentLoadBalancer(lbs, false, eLog)

	assert.NoError(t, err)
	assert.Len(t, ilbs, 1, "should create internal load balancer")
	assert.Len(t, elbs, 0, "should not create external load balancer")
	assert.Len(t, umigs, 1, "should create one UMIG")
	assert.Len(t, pbrs, 1, "should create one policy-based route")

	// Verify that UMIG has project override for SP interconnect
	umig := umigs[0]
	assert.Equal(t, "host-gpcs-dev-01", umig.ProjectOverride)

	// Verify session affinity is CLIENT_IP for SP interconnect onramp
	ilb := ilbs[0]
	assert.Equal(t, deployment.SAClientIP, ilb.SessionAffinity)
}

func TestPrepareDeploymentLoadBalancerInterconnectOnrampMultiZone(t *testing.T) {
	lb := &NLB{
		Name:   "test-multizone-onramp-nlb",
		Region: "us-west1",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(123),
			RegionID:                           sql.Int64(1),
			BackendServiceProtocol:             sql.Int64(1), // TCP
			SessionAffinity:                    sql.Int64(1), // CLIENT_IP
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1), // NEVER_PERSIST
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1), // HTTP
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1), // TCP
		},
		CfgSource: &VMInstance{
			TFLocalName:          "test_multizone_onramp_vm1",
			Name:                 "test-multizone-onramp-vm1",
			IsInterconnectOnramp: true,
			SharedMgmtVPCProj:    "host-gpcs-dev-01",
		},
		Network:   "test-multizone-onramp-network",
		Subnet:    "test-multizone-onramp-subnet",
		ZonesList: []string{"us-west1-a", "us-west1-b"},
		PublicIp:  "*******",
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west1-a": {
				{
					TFLocalName:          "test_multizone_onramp_vm1",
					Name:                 "test-multizone-onramp-vm1",
					IsInterconnectOnramp: true,
				},
			},
			"us-west1-b": {
				{
					TFLocalName:          "test_multizone_onramp_vm2",
					Name:                 "test-multizone-onramp-vm2",
					IsInterconnectOnramp: true,
				},
			},
		},
	}
	lbs := []*NLB{lb}

	eLog := utils.GetEventLogger(clogger.NewLogger(log.Writer()))
	elbs, ilbs, umigs, pbrs, err := PrepareDeploymentLoadBalancer(lbs, false, eLog)

	assert.NoError(t, err)
	assert.Len(t, ilbs, 1, "should create one internal load balancer")
	assert.Len(t, elbs, 0, "should not create external load balancer")
	assert.Len(t, umigs, 2, "should create two UMIGs for multi-zone setup")
	assert.Len(t, pbrs, 1, "should create one policy-based route")

	// Verify UMIG names for multi-zone (primary zone gets base name, others get numbered suffix)
	umigNames := []string{umigs[0].Name, umigs[1].Name}
	assert.Contains(t, umigNames, "test-multizone-onramp-nlb-umig")
	assert.Contains(t, umigNames, "test-multizone-onramp-nlb-umig-1")

	// Verify zones
	zones := []string{umigs[0].Zone, umigs[1].Zone}
	assert.Contains(t, zones, "us-west1-a")
	assert.Contains(t, zones, "us-west1-b")

	// Verify all UMIGs have project override
	for _, umig := range umigs {
		assert.Equal(t, "host-gpcs-dev-01", umig.ProjectOverride)
	}

	// Verify both UMIGs are listed as backends in the ILB
	ilb := ilbs[0]
	assert.Len(t, ilb.Backends, 2, "ILB should have two backends")
	backendNames := []string{ilb.Backends[0].Name, ilb.Backends[1].Name}
	assert.Contains(t, backendNames, "test-multizone-onramp-nlb-umig")
	assert.Contains(t, backendNames, "test-multizone-onramp-nlb-umig-1")

func TestIsExternalLoadBalancer(t *testing.T) {
	type testcase struct {
		description      string
		isSpInterconnect bool
		sase5gEnabled    bool
		isInterconnectOnramp bool
		expected         bool
	}

	testcases := []testcase{
		{
			description:      "should return true for regular VM (no special flags)",
			isSpInterconnect: false,
			sase5gEnabled:    false,
			isInterconnectOnramp: false,
			expected:         true,
		},
		{
			description:      "should return false for SP interconnect VM",
			isSpInterconnect: true,
			sase5gEnabled:    false,
			isInterconnectOnramp: false,
			expected:         false,
		},
		{
			description:      "should return false for 5G enabled VM",
			isSpInterconnect: false,
			sase5gEnabled:    true,
			isInterconnectOnramp: false,
			expected:         false,
		},
		{
			description:      "should return false for interconnect onramp VM",
			isSpInterconnect: false,
			sase5gEnabled:    false,
			isInterconnectOnramp: true,
			expected:         false,
		},
		{
			description:      "should return false when SP interconnect and 5G both enabled",
			isSpInterconnect: true,
			sase5gEnabled:    true,
			isInterconnectOnramp: false,
			expected:         false,
		},
		{
			description:      "should return false when SP interconnect and interconnect onramp both enabled",
			isSpInterconnect: true,
			sase5gEnabled:    false,
			isInterconnectOnramp: true,
			expected:         false,
		},
		{
			description:      "should return false when 5G and interconnect onramp both enabled",
			isSpInterconnect: false,
			sase5gEnabled:    true,
			isInterconnectOnramp: true,
			expected:         false,
		},
		{
			description:      "should return false when all flags are enabled",
			isSpInterconnect: true,
			sase5gEnabled:    true,
			isInterconnectOnramp: true,
			expected:         false,
		},
	}

	for _, tc := range testcases {
		nlb := &NLB{
			CfgSource: &VMInstance{
				IsSpInterconnect:     tc.isSpInterconnect,
				Sase5gEnabled:        tc.sase5gEnabled,
				IsInterconnectOnramp: tc.isInterconnectOnramp,
			},
		}

		actual := isExternalLoadBalancer(nlb)
		assert.Equal(t, tc.expected, actual, tc.description)
	}
}

func TestIsExternalLoadBalancerNilCfgSource(t *testing.T) {
	nlb := &NLB{
		CfgSource: nil,
	}

	defer func() {
		if r := recover(); r != nil {
			t.Errorf("Function should handle nil CfgSource gracefully, but panicked: %v", r)
		}
	}()

	result := isExternalLoadBalancer(nlb)
	assert.True(t, result, "should return true when CfgSource is nil")
}

func isExternalLoadBalancer(nlb *NLB) bool {
	if nlb.CfgSource == nil {
		return true
	}

	if nlb.CfgSource.IsSpInterconnect || nlb.CfgSource.Sase5gEnabled || nlb.CfgSource.IsInterconnectOnramp {
		return false
	}

	return true
}

func TestPrepareVMInstanceSvcILBGatewayNode(t *testing.T) {
	eLog := utils.GetEventLogger(clogger.NewLogger(log.Writer()))

	myRow := &colo_onboarding_master.Row{
		Name: sql.String("test_svc_ilb_gateway"),
	}

	saltProfile := SaltProfile{
		RegionName:              "us-west1",
		DPNetwork:               "test-dp-network",
		DPSubnet:                "test-dp-subnet",
		SharedMgmtVPCProj:      "host-gpcs-test-01",
		Is5gEnabled:            true,
		IsUsingSpInterconnect:  false,
	}

	vm := &VMInstance{
		NodeType:    terraform.NodeTypeSvcILB,
		AltNodeType: terraform.NodeTypeGPGateway,
		TFLocalName: "test_svc_ilb_vm",
		Name:        "test-svc-ilb-vm",
		Region:      "us-west1",
		CleanIpTag:  true,
		NetIntfs:    []*NetworkInterface{{Network: "dp-network", Subnet: "dp-subnet"}},
	}

	svcilb := &NLB{}
	intOnrampILB := &NLB{}

	switch vm.NodeType {
	case terraform.NodeTypeSvcILB:
		switch vm.AltNodeType {
		case terraform.NodeTypeGPGateway:
			svcilb.Name = strings.ToLower(strings.ReplaceAll(myRow.Name.String(), "_", "-"))
			svcilb.CfgSource = vm
			svcilb.Region = saltProfile.RegionName
			vm.HasDpNic = saltProfile.DPNetwork != ""
			vm.SharedMgmtVPCProj = saltProfile.SharedMgmtVPCProj
			vm.Sase5gEnabled = saltProfile.Is5gEnabled
			if vm.HasDpNic {
				svcilb.Network = saltProfile.DPNetwork
				svcilb.Subnet = saltProfile.DPSubnet
				eLog.LogInfo("The subnet details for NodeTypeSvcILB GW are: '%v' , '%v'", svcilb.Network, svcilb.Subnet)
			}
			gleanNlbParameters(svcilb, vm, eLog)
		}
	}

	assert.Equal(t, "test-svc-ilb-gateway", svcilb.Name)
	assert.Equal(t, vm, svcilb.CfgSource)
	assert.Equal(t, "us-west1", svcilb.Region)
	assert.True(t, vm.HasDpNic)
	assert.Equal(t, "host-gpcs-test-01", vm.SharedMgmtVPCProj)
	assert.True(t, vm.Sase5gEnabled)
	assert.Equal(t, "test-dp-network", svcilb.Network)
	assert.Equal(t, "test-dp-subnet", svcilb.Subnet)
}

func TestPrepareVMInstanceSvcILBRemoteNetworkNode(t *testing.T) {
	eLog := utils.GetEventLogger(clogger.NewLogger(log.Writer()))

	myRow := &colo_onboarding_master.Row{
		Name: sql.String("test_interconnect_onramp"),
	}

	saltProfile := SaltProfile{
		RegionName:             "us-east1",
		DPNetwork:              "interconnect-dp-network",
		DPSubnet:               "interconnect-dp-subnet",
		SharedMgmtVPCProj:     "host-gpcs-prod-01",
		Is5gEnabled:           false,
		IsUsingSpInterconnect: true,
	}

	vm := &VMInstance{
		NodeType:    terraform.NodeTypeSvcILB,
		AltNodeType: terraform.NodeTypeRemoteNetwork,
		TFLocalName: "test_interconnect_vm",
		Name:        "test-interconnect-vm",
		Region:      "us-east1",
		CleanIpTag:  true,
		NetIntfs:    []*NetworkInterface{{Network: "interconnect-network", Subnet: "interconnect-subnet"}},
	}

	svcilb := &NLB{}
	intOnrampILB := &NLB{}

	switch vm.NodeType {
	case terraform.NodeTypeSvcILB:
		switch vm.AltNodeType {
		case terraform.NodeTypeRemoteNetwork:
			intOnrampILB.Name = strings.ToLower(strings.ReplaceAll(myRow.Name.String(), "_", "-"))
			intOnrampILB.CfgSource = vm
			intOnrampILB.Region = saltProfile.RegionName
			vm.HasDpNic = saltProfile.DPNetwork != ""
			vm.SharedMgmtVPCProj = saltProfile.SharedMgmtVPCProj
			vm.IsSpInterconnect = saltProfile.IsUsingSpInterconnect
			vm.IsInterconnectOnramp = true
			if vm.HasDpNic {
				intOnrampILB.Network = saltProfile.DPNetwork
				intOnrampILB.Subnet = saltProfile.DPSubnet
				eLog.LogInfo("The subnet details for NodeTypeSvcILB RN are: '%v' , '%v'", intOnrampILB.Network, intOnrampILB.Subnet)
			}
			gleanNlbParameters(intOnrampILB, vm, eLog)
		}
	}

	assert.Equal(t, "test-interconnect-onramp", intOnrampILB.Name)
	assert.Equal(t, vm, intOnrampILB.CfgSource)
	assert.Equal(t, "us-east1", intOnrampILB.Region)
	assert.True(t, vm.HasDpNic)
	assert.Equal(t, "host-gpcs-prod-01", vm.SharedMgmtVPCProj)
	assert.True(t, vm.IsSpInterconnect)
	assert.True(t, vm.IsInterconnectOnramp)
	assert.Equal(t, "interconnect-dp-network", intOnrampILB.Network)
	assert.Equal(t, "interconnect-dp-subnet", intOnrampILB.Subnet)
}

func TestPrepareVMInstanceSvcILBGatewayWithoutDPNetwork(t *testing.T) {
	eLog := utils.GetEventLogger(clogger.NewLogger(log.Writer()))

	myRow := &colo_onboarding_master.Row{
		Name: sql.String("test_gateway_no_dp"),
	}

	saltProfile := SaltProfile{
		RegionName:              "us-central1",
		DPNetwork:               "",
		DPSubnet:                "",
		SharedMgmtVPCProj:      "host-gpcs-dev-01",
		Is5gEnabled:            false,
		IsUsingSpInterconnect:  false,
	}

	vm := &VMInstance{
		NodeType:    terraform.NodeTypeSvcILB,
		AltNodeType: terraform.NodeTypeGPGateway,
		TFLocalName: "test_gateway_no_dp_vm",
		Name:        "test-gateway-no-dp-vm",
		Region:      "us-central1",
		CleanIpTag:  false,
		NetIntfs:    []*NetworkInterface{{Network: "mgmt-network", Subnet: "mgmt-subnet"}},
	}

	svcilb := &NLB{}

	switch vm.NodeType {
	case terraform.NodeTypeSvcILB:
		switch vm.AltNodeType {
		case terraform.NodeTypeGPGateway:
			svcilb.Name = strings.ToLower(strings.ReplaceAll(myRow.Name.String(), "_", "-"))
			svcilb.CfgSource = vm
			svcilb.Region = saltProfile.RegionName
			vm.HasDpNic = saltProfile.DPNetwork != ""
			vm.SharedMgmtVPCProj = saltProfile.SharedMgmtVPCProj
			vm.Sase5gEnabled = saltProfile.Is5gEnabled
			if vm.HasDpNic {
				svcilb.Network = saltProfile.DPNetwork
				svcilb.Subnet = saltProfile.DPSubnet
				eLog.LogInfo("The subnet details for NodeTypeSvcILB GW are: '%v' , '%v'", svcilb.Network, svcilb.Subnet)
			}
			gleanNlbParameters(svcilb, vm, eLog)
		}
	}

	assert.Equal(t, "test-gateway-no-dp", svcilb.Name)
	assert.Equal(t, vm, svcilb.CfgSource)
	assert.Equal(t, "us-central1", svcilb.Region)
	assert.False(t, vm.HasDpNic)
	assert.Equal(t, "host-gpcs-dev-01", vm.SharedMgmtVPCProj)
	assert.False(t, vm.Sase5gEnabled)
	assert.Empty(t, svcilb.Network)
	assert.Empty(t, svcilb.Subnet)
}

func TestPrepareVMInstanceSvcILBRemoteNetworkWithoutDPNetwork(t *testing.T) {
	eLog := utils.GetEventLogger(clogger.NewLogger(log.Writer()))

	myRow := &colo_onboarding_master.Row{
		Name: sql.String("remote_network_no_dp"),
	}

	saltProfile := SaltProfile{
		RegionName:             "asia-south1",
		DPNetwork:              "",
		DPSubnet:               "",
		SharedMgmtVPCProj:     "host-gpcs-staging-01",
		Is5gEnabled:           true,
		IsUsingSpInterconnect: false,
	}

	vm := &VMInstance{
		NodeType:    terraform.NodeTypeSvcILB,
		AltNodeType: terraform.NodeTypeRemoteNetwork,
		TFLocalName: "test_rn_no_dp_vm",
		Name:        "test-rn-no-dp-vm",
		Region:      "asia-south1",
		CleanIpTag:  true,
		NetIntfs:    []*NetworkInterface{{Network: "mgmt-only-network", Subnet: "mgmt-only-subnet"}},
	}

	intOnrampILB := &NLB{}

	switch vm.NodeType {
	case terraform.NodeTypeSvcILB:
		switch vm.AltNodeType {
		case terraform.NodeTypeRemoteNetwork:
			intOnrampILB.Name = strings.ToLower(strings.ReplaceAll(myRow.Name.String(), "_", "-"))
			intOnrampILB.CfgSource = vm
			intOnrampILB.Region = saltProfile.RegionName
			vm.HasDpNic = saltProfile.DPNetwork != ""
			vm.SharedMgmtVPCProj = saltProfile.SharedMgmtVPCProj
			vm.IsSpInterconnect = saltProfile.IsUsingSpInterconnect
			vm.IsInterconnectOnramp = true
			if vm.HasDpNic {
				intOnrampILB.Network = saltProfile.DPNetwork
				intOnrampILB.Subnet = saltProfile.DPSubnet
				eLog.LogInfo("The subnet details for NodeTypeSvcILB RN are: '%v' , '%v'", intOnrampILB.Network, intOnrampILB.Subnet)
			}
			gleanNlbParameters(intOnrampILB, vm, eLog)
		}
	}

	assert.Equal(t, "remote-network-no-dp", intOnrampILB.Name)
	assert.Equal(t, vm, intOnrampILB.CfgSource)
	assert.Equal(t, "asia-south1", intOnrampILB.Region)
	assert.False(t, vm.HasDpNic)
	assert.Equal(t, "host-gpcs-staging-01", vm.SharedMgmtVPCProj)
	assert.False(t, vm.IsSpInterconnect)
	assert.True(t, vm.IsInterconnectOnramp)
	assert.Empty(t, intOnrampILB.Network)
	assert.Empty(t, intOnrampILB.Subnet)
}

func TestPrepareDeploymentLoadBalancerInterconnectOnrampPBRCreation(t *testing.T) {
	lb := &NLB{
		Name:   "test-pbr-nlb",
		Region: "us-central1",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(123),
			RegionID:                           sql.Int64(1),
			BackendServiceProtocol:             sql.Int64(1),
			SessionAffinity:                    sql.Int64(1),
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1),
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1),
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1),
		},
		CfgSource: &VMInstance{
			TFLocalName:          "test_pbr_vm",
			Name:                 "test-pbr-vm",
			IsInterconnectOnramp: true,
			SharedMgmtVPCProj:    "host-gpcs-prod-01",
		},
		Network:   "test-pbr-network",
		Subnet:    "test-pbr-subnet",
		ZonesList: []string{"us-central1-a"},
		PublicIp:  "********",
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-central1-a": {
				{
					TFLocalName:          "test_pbr_vm",
					Name:                 "test-pbr-vm",
					IsInterconnectOnramp: true,
				},
			},
		},
	}
	lbs := []*NLB{lb}

	eLog := utils.GetEventLogger(clogger.NewLogger(nil))
	elbs, ilbs, umigs, pbrs, err := PrepareDeploymentLoadBalancer(lbs, false, eLog)

	assert.NoError(t, err)
	assert.Len(t, elbs, 0)
	assert.Len(t, ilbs, 1)
	assert.Len(t, umigs, 1)
	assert.Len(t, pbrs, 1)

	pbr := pbrs[0]
	assert.Equal(t, "pbr-test-pbr-nlb-fw-0", pbr.Name)
	assert.Equal(t, "test-pbr-network", pbr.Network)
	assert.Equal(t, "test-pbr-nlb-fw-0", pbr.NextHopILB)
	assert.Equal(t, 100, pbr.Priority)

	assert.NotNil(t, pbr.Filter)
	assert.Equal(t, "ALL", pbr.Filter.IPProtocol)
	assert.Equal(t, "0.0.0.0/0", pbr.Filter.SrcRange)
	assert.Equal(t, "0.0.0.0/0", pbr.Filter.DstRange)

	assert.NotNil(t, pbr.ApplyOn.CloudInterconnect)
	assert.Equal(t, "us-central1", pbr.ApplyOn.CloudInterconnect.Region)

	ilb := ilbs[0]
	assert.Equal(t, 300, ilb.ConnDraining.DrainingTimeoutSec)
}

func TestPrepareDeploymentLoadBalancerNonInterconnectOnrampNoPBR(t *testing.T) {
	lb := &NLB{
		Name:   "test-no-pbr-nlb",
		Region: "us-east1",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(456),
			RegionID:                           sql.Int64(2),
			BackendServiceProtocol:             sql.Int64(1),
			SessionAffinity:                    sql.Int64(1),
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1),
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1),
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1),
		},
		CfgSource: &VMInstance{
			TFLocalName:          "test_regular_vm",
			Name:                 "test-regular-vm",
			IsInterconnectOnramp: false,
			SharedMgmtVPCProj:    "host-gpcs-prod-01",
		},
		Network:   "test-regular-network",
		Subnet:    "test-regular-subnet",
		ZonesList: []string{"us-east1-a"},
		PublicIp:  "***********",
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-east1-a": {
				{
					TFLocalName:          "test_regular_vm",
					Name:                 "test-regular-vm",
					IsInterconnectOnramp: false,
				},
			},
		},
	}
	lbs := []*NLB{lb}

	eLog := utils.GetEventLogger(clogger.NewLogger(nil))
	elbs, ilbs, umigs, pbrs, err := PrepareDeploymentLoadBalancer(lbs, false, eLog)

	assert.NoError(t, err)
	assert.Len(t, pbrs, 0)
	assert.Len(t, ilbs, 1)

	ilb := ilbs[0]
	assert.NotEqual(t, 300, ilb.ConnDraining.DrainingTimeoutSec)
}

func TestPrepareDeploymentLoadBalancerInterconnectOnrampDifferentRegions(t *testing.T) {
	regions := []string{"asia-east1", "europe-west1", "australia-southeast1"}

	for _, region := range regions {
		lb := &NLB{
			Name:   "test-region-nlb",
			Region: region,
			Cfg: &network_load_balancer_config.Row{
				CustID:                             sql.Int64(789),
				RegionID:                           sql.Int64(3),
				BackendServiceProtocol:             sql.Int64(1),
				SessionAffinity:                    sql.Int64(1),
				ConnPersistenceOnUnhealthyBackends: sql.Int64(1),
				IsStrongSessionAffinitySupported:   1,
				IdleTimeoutSec:                     sql.Int64(300),
				HealthCheckProtocol:                sql.Int64(1),
				HealthCheckInterval:                sql.Int64(5),
				HealthCheckTimeout:                 sql.Int64(5),
				HealthCheckUnhealthyThreshold:      sql.Int64(2),
				HealthCheckHealthyThreshold:        sql.Int64(2),
				HealthCheckPort:                    sql.Int64(80),
				ForwardingRuleProtocol:             sql.Int64(1),
			},
			CfgSource: &VMInstance{
				TFLocalName:          "test_region_vm",
				Name:                 "test-region-vm",
				IsInterconnectOnramp: true,
			},
			Network:   "test-region-network",
			Subnet:    "test-region-subnet",
			ZonesList: []string{region + "-a"},
			PublicIp:  "**********",
			ZonalInstanceMap: map[string][]*VMInstance{
				region + "-a": {
					{
						TFLocalName:          "test_region_vm",
						Name:                 "test-region-vm",
						IsInterconnectOnramp: true,
					},
				},
			},
		}
		lbs := []*NLB{lb}

		eLog := utils.GetEventLogger(clogger.NewLogger(nil))
		_, _, _, pbrs, err := PrepareDeploymentLoadBalancer(lbs, false, eLog)

		assert.NoError(t, err)
		assert.Len(t, pbrs, 1)

		pbr := pbrs[0]
		assert.Equal(t, region, pbr.ApplyOn.CloudInterconnect.Region)
		assert.Equal(t, "pbr-test-region-nlb-fw-0", pbr.Name)
		assert.Equal(t, "test-region-nlb-fw-0", pbr.NextHopILB)
	}
}

func TestPrepareDeploymentLoadBalancerInterconnectOnrampPBRFilterConfiguration(t *testing.T) {
	lb := &NLB{
		Name:   "test-filter-nlb",
		Region: "us-west2",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(999),
			RegionID:                           sql.Int64(4),
			BackendServiceProtocol:             sql.Int64(1),
			SessionAffinity:                    sql.Int64(1),
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1),
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1),
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1),
		},
		CfgSource: &VMInstance{
			TFLocalName:          "test_filter_vm",
			Name:                 "test-filter-vm",
			IsInterconnectOnramp: true,
		},
		Network:   "test-filter-network",
		Subnet:    "test-filter-subnet",
		ZonesList: []string{"us-west2-b"},
		PublicIp:  "***********",
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-west2-b": {
				{
					TFLocalName:          "test_filter_vm",
					Name:                 "test-filter-vm",
					IsInterconnectOnramp: true,
				},
			},
		},
	}
	lbs := []*NLB{lb}

	eLog := utils.GetEventLogger(clogger.NewLogger(nil))
	_, _, _, pbrs, err := PrepareDeploymentLoadBalancer(lbs, false, eLog)

	assert.NoError(t, err)
	assert.Len(t, pbrs, 1)

	pbr := pbrs[0]
	filter := pbr.Filter

	assert.NotNil(t, filter)
	assert.Equal(t, "ALL", filter.IPProtocol)
	assert.Equal(t, "0.0.0.0/0", filter.SrcRange)
	assert.Equal(t, "0.0.0.0/0", filter.DstRange)
	assert.Equal(t, 100, pbr.Priority)
}

func TestPrepareDeploymentLoadBalancerInterconnectOnrampMultipleLBs(t *testing.T) {
	lb1 := &NLB{
		Name:   "test-multi-nlb-1",
		Region: "us-central1",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(111),
			RegionID:                           sql.Int64(1),
			BackendServiceProtocol:             sql.Int64(1),
			SessionAffinity:                    sql.Int64(1),
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1),
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1),
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1),
		},
		CfgSource: &VMInstance{
			TFLocalName:          "test_multi_vm_1",
			Name:                 "test-multi-vm-1",
			IsInterconnectOnramp: true,
		},
		Network:   "test-multi-network-1",
		Subnet:    "test-multi-subnet-1",
		ZonesList: []string{"us-central1-a"},
		PublicIp:  "************",
		ZonalInstanceMap: map[string][]*VMInstance{
			"us-central1-a": {
				{
					TFLocalName:          "test_multi_vm_1",
					Name:                 "test-multi-vm-1",
					IsInterconnectOnramp: true,
				},
			},
		},
	}

	lb2 := &NLB{
		Name:   "test-multi-nlb-2",
		Region: "us-central1",
		Cfg: &network_load_balancer_config.Row{
			CustID:                             sql.Int64(222),
			RegionID:                           sql.Int64(2),
			BackendServiceProtocol:             sql.Int64(1),
			SessionAffinity:                    sql.Int64(1),
			ConnPersistenceOnUnhealthyBackends: sql.Int64(1),
			IsStrongSessionAffinitySupported:   1,
			IdleTimeoutSec:                     sql.Int64(300),
			HealthCheckProtocol:                sql.Int64(1),
			HealthCheckInterval:                sql.Int64(5),
			HealthCheckTimeout:                 sql.Int64(5),
			HealthCheckUnhealthyThreshold:      sql.Int64(2),
			HealthCheckHealthyThreshold:        sql.Int64(2),
			HealthCheckPort:                    sql.Int64(80),
			ForwardingRuleProtocol:             sql.Int64(1),
		},
		CfgSource: &VMInstance{
			TFLocalName:          "test_multi_vm_2",
			Name:
}
