package gcp

import (
	"fmt"
	"go.panw.local/pangolin/clogger"
	pterraform "orchestrator/provision_service/terraform"
)

// labels added external of orchestrator GCP project provisioning, to ignore. See resource_tagger.py.
var ignoreProjectLabels = []string{"c7n-contact", "c7n_bizorg-unit", "c7n_cost-center", "c7n_environment", "ccoe-contact",
	"cloudprovider", "environment", "licensetype", "resource_domain", "supertenantid", "tenantid", "tenantname", "timestamp"}

// As some older projects may not have these labels, ignore them during import to avoid flagging them as unexpected changes
var ignoreProjectLabelsAtImport = []string{"app", "env", "aws_env", "provision_type", "customer"}

// Project service attributes to ignore during import to avoid flagging them as unexpected changes
var ignoreProjectServiceAttributesAtImport = []string{"disable_on_destroy"}

// Service account attributes to ignore during import to avoid flagging them as unexpected changes
var ignoreServiceAccountAttributesAtImport = []string{"display_name"}

type PrepareProjectParams struct {
	ID             string
	Name           string
	FolderID       string
	BillingAccount string
	GCPEnv         string
	AWSEnv         string
	CustomerName   string
	HostProjectID  string
	ImgProjectID   string
	Import         bool
	ELog           *clogger.EventLogger
}

func PrepareProject(input PrepareProjectParams) (*Project, error) {
	project := &Project{}
	project.Id = input.ID
	project.Name = input.Name
	if input.Name == "" {
		project.Name = input.ID
	}
	project.FolderId = input.FolderID
	project.BillingAccount = input.BillingAccount
	project.HostProjectId = input.HostProjectID

	project.TFLocalName = pterraform.TFLocalName(project.Id)
	project.Labels = map[string]string{
		"app":            "gpcs",
		"env":            input.GCPEnv,
		"aws_env":        input.AWSEnv,
		"provision_type": "terraform", // additional label to differentiate between terraform-provisioned vs DM
	}
	if input.CustomerName != "" {
		project.Labels["customer"] = input.CustomerName
	}
	project.Services = []string{"compute.googleapis.com", "storage-api.googleapis.com", "secretmanager.googleapis.com",
		"networksecurity.googleapis.com", "monitoring.googleapis.com"}
	if input.Import {
		project.ServicesIgnoreChangesArguments = append(project.ServicesIgnoreChangesArguments, ignoreProjectServiceAttributesAtImport...)
	}
	project.RemoveDefaultSa = true

	// labels to ignore
	labelsToIgnore := ignoreProjectLabels
	if input.Import {
		labelsToIgnore = append(labelsToIgnore, ignoreProjectLabelsAtImport...)
	}
	for _, label := range labelsToIgnore {
		project.IgnoreChangesArguments = append(project.IgnoreChangesArguments, fmt.Sprintf("labels[\"%v\"]", label))
	}

	// ServiceAccounts and CloudServiceAgentRoles are hardcoded, they are present when customerName is present
	// (thus proj is already allocated to a customer).
	if input.CustomerName != "" {
		project.ServiceAccounts = []*ServiceAccount{
			&ServiceAccount{
				Id:          "customer",
				DisplayName: "customer service account",
				Roles: []*Role{
					&Role{ProjectId: input.ID, Name: "compute.instanceAdmin"},
					&Role{ProjectId: input.ID, Name: "compute.loadBalancerAdmin"},
				},
			},
			&ServiceAccount{
				Id:          "proxy-vm",
				DisplayName: "proxy-vm service account",
				Roles: []*Role{
					&Role{ProjectId: input.ID, Name: "compute.instanceAdmin"},
				},
			},
			&ServiceAccount{
				Id:          "rbistoragecreate",
				DisplayName: "Storage admin service account",
				Roles: []*Role{
					&Role{ProjectId: input.ID, Name: "storage.admin"},
				},
			},
			&ServiceAccount{
				Id:          "rbistoragerw",
				DisplayName: "Storage object admin service account",
				Roles: []*Role{
					&Role{ProjectId: input.ID, Name: "storage.objectUser"},
				},
			},
		}

		for _, sa := range project.ServiceAccounts {
			sa.TFLocalName = pterraform.TFLocalName(sa.Id)
			sa.Identity = fmt.Sprintf("serviceAccount:%s@%s.iam.gserviceaccount.com", sa.Id, project.Id)
			for _, r := range sa.Roles {
				r.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("%s_%s", sa.Id, r.Name))
			}
			if input.Import {
				sa.IgnoreChangesArguments = append(sa.IgnoreChangesArguments, ignoreServiceAccountAttributesAtImport...)
			}
		}

		// These roles are needed so cloud services (such as GCP APIs service agent) can perform the work on our behalf.
		if input.ImgProjectID != "" {
			project.CloudServicesAgentRoles = append(project.CloudServicesAgentRoles,
				&Role{ProjectId: input.ImgProjectID, Name: "compute.imageUser"})
		}
		if input.HostProjectID != "" {
			project.CloudServicesAgentRoles = append(project.CloudServicesAgentRoles,
				&Role{ProjectId: input.HostProjectID, Name: "compute.networkUser"})
		}

		for _, er := range project.CloudServicesAgentRoles {
			er.TFLocalName = pterraform.TFLocalName(fmt.Sprintf("cloudservices_%s_%s", er.ProjectId, er.Name))
		}
	}

	return project, nil
}
