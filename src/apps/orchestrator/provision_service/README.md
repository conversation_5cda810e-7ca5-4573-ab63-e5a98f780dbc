# Provision Service

## Local Build and Run

To run provision service locally for development or quick testing:
1. Fill in the values in **devenv/cfg.yaml**:
   * gcp_orch_project_id: Typically either **orchestrator-gpcs-dev** or **orchestrator-gpcs-test**
   * tf_server: URL of the Terraform Server. For TFC, it is **https://app.terraform.io/**
   * tf_token: Terraform Server API access token/key
   * tf_org: Name of the organization to use on the Terrform Server, typically **PANW-SASE-NonProd**
   * tf_proj: CYR env name such as **dev**, **dev2...dev20**, **qa1..qa7**
   * gcp/deploy_credential: Path to the GCP JSON service account key file
   * post_processing_uri: HTTP endpoint for post-processing, currently this is done by the instance_mgmt service. Post-processing is skipped if not set.
2. Provision Service depends on own proprietary PA go library, **go-prismaaccess**, **git clone** it to the **go-prismaaccess** folder as a sibling of **saas-infra**
   * https://bitbucket.paloaltonetworks.local/projects/PRIAC/repos/go-prismaaccess/browse
3. Uncomment line 5 of **src/apps/orchestrator/go.mod** (full text of the line included below for reference)
   - replace go.panw.local/pangolin => ../../../../go-prismaaccess/pangolin
4. cd to the provision service's root directory, then **go build**
5. **./provision_service -base-path=./devenv/ -template-dir=./resources/templates -state-dir=./devenv/cft**