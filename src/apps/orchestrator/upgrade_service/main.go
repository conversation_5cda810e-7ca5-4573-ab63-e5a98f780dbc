/*
 * Upgrade Service
 * ---------------
 *
 * The Upgrade Service is an event based FSM that works off the primitive that state transitions
 * are also events. Events are currently fired through state based subscriptions.
 * If a notification is received on a topic a state subscribes to, the state will be invoked as
 * a task asynchronously. Each state can set the concurrency level (number of workers) to control
 * the concurrency rate at the state level.
 * Multiple states can subscribe to the same topic. In this case, there is no guarantee as to the
 * order in which state tasks are invoked. The only guarantee is that all subscribed state tasks
 * will be invoked.
 */
package main

import (
	"flag"
	"fmt"
	"log"
	"orchestrator/libs/go/avisar"
	"orchestrator/libs/go/aws/partition"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	_ "crypto/tls/fipsonly"

	"orchestrator/libs/go/aws/kms"
	"orchestrator/libs/go/aws/lambda"
	"orchestrator/libs/go/aws/region"
	"orchestrator/libs/go/aws/utils"
	"orchestrator/libs/go/cogs"
	"orchestrator/libs/go/config"
	"orchestrator/libs/go/gcp/pubsub"
	"orchestrator/terraform_service/rest"
	cfgUtil "orchestrator/upgrade_service/config"
	"orchestrator/upgrade_service/db"
	"orchestrator/upgrade_service/definitions"
	muAPI "orchestrator/upgrade_service/states/autoscale/api"
	"orchestrator/upgrade_service/states/autoscale/cleanup"
	"orchestrator/upgrade_service/states/autoscale/scheduled"
	epAPI "orchestrator/upgrade_service/states/ep_cnat_scale/api"
	muCnatAPI "orchestrator/upgrade_service/states/mu_cnat_scale/api"
	"orchestrator/upgrade_service/states/mu_cnat_scale/monitor"
	"orchestrator/upgrade_service/states/rn_sc_vertscale/api"
	"orchestrator/upgrade_service/states/rn_sc_vertscale/failover_mon"
	"orchestrator/upgrade_service/states/rn_sc_vertscale/ha_sync_mon"
	"orchestrator/upgrade_service/states/rn_sc_vertscale/instance_checker"
	"orchestrator/upgrade_service/states/rn_sc_vertscale/orch_client"
	"orchestrator/upgrade_service/states/rn_sc_vertscale/reporting"

	"go.panw.local/pangolin/clogger"
)

var (
	cfg      *config.Config
	gLogger  *clogger.Clogger
	basePath = flag.String("base-path", "/orch_aas/libs", "Base path to orchestrator")
	runType  = flag.String("run-type", "kubernetes", "Default to the kubernetes run type")
	logMode  = flag.String("log-mode", "stdout", "Log to stdout or file")
	logFile  = flag.String("log-file", "/var/log/pan/upgrade_service.log", "Log file to log to")
)

type stateConfig struct {
	name         string
	concurrency  uint32
	pollInterval cogs.CronPeriod
}

func setupLogger(cfg *config.Config) {
	deployedEnv := cfg.UpgradeService.DeployedEnv
	if deployedEnv == "dev" {
		gLogger = clogger.NewConsoleLogger()
	} else {
		gLogger = clogger.NewLogger(os.Stdout)
	}
	gLogger.AddContext(clogger.RastroCtx)
}

func setupFileLogger(cfg *config.Config, filePath string) {
	writer := &clogger.RotateWriter{
		FileName:     filePath,
		MaxSize:      50 * 1024 * 1024, // 50MB
		MaxRetained:  10,               // Keep 10 archived logs
		NeedCompress: true,             // Compress the archived logs
	}
	gLogger = clogger.NewLogger(writer)
	gLogger.AddContext(clogger.RastroCtx)
}

func init() {
	var err error
	flag.Parse()

	cfg, err = cfgUtil.ReadConfig(fmt.Sprintf("%s/cfg.yaml", *basePath))
	if err != nil {
		log.Fatalf("Failed to read cfg.yaml : %v\n", err)
	}
	region.Set(cfg.Region)
	if cfg.AwsPartition != "" {
		partition.Set(cfg.AwsPartition)
	}
	kms.CreateContext()
	lambda.CreateContext()
	utils.CreateContext()

	if *logMode != "stdout" {
		setupFileLogger(cfg, *logFile)
	} else {
		setupLogger(cfg)
	}
	gLogger.LogDebug("%+v", cfg.UpgradeService)
	if *runType == definitions.RunTypeVM {
		utilsContext := utils.GetContext()
		utilsContext.ReadFromS3ToFile(gLogger, cfg.KeysBucketName,
			fmt.Sprintf("gcp/%s.json.enc", cfg.GCPOrchProjectID),
			fmt.Sprintf("%s/google_application_credentials.json", *basePath),
			true)
		os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", fmt.Sprintf(
			"%s/google_application_credentials.json", *basePath))
		// Ensure the PubSub topics and subscriptions are present
		// HACK: Need to update cfg.yaml to have 2 fields, one for topic name and another for the filter.
		// Changes there are dependant on a SRE task. This hack is to make forward progress while we're blocked there.
		// We NEED to switch back to using clearly configured values.
		pubsubTopic := "event_vscale_topic"
		filter := strings.TrimPrefix(cfg.UpgradeService.VertScalePubSubTopic, pubsubTopic+"_")
		subscriptionName := fmt.Sprintf("%s-%s-sub", pubsubTopic, filter)
		cyrFilter := fmt.Sprintf(`attributes.cyr_env="%s"`, filter)
		gLogger.LogInfo("Setting up Topic %s and subscription %s with filter %s", pubsubTopic,
			subscriptionName, cyrFilter)
		if err := pubsub.CreateTopicAndSubscription(cfg.UpgradeService.GCPProjectID,
			pubsubTopic, subscriptionName, cyrFilter); err != nil {
			gLogger.LogFatal("Failed to create Pub/Sub topic and subscription : %v", err)
		}
	} else {
		os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", fmt.Sprintf(
			"%s/google_application_credentials.json", *basePath))
	}
	rest.Initialize()
	// initialize Avisar client wrapper
	_, err = avisar.InitClientWrapperFromCfg(cfg, gLogger)
	if err != nil {
		gLogger.LogFatal("Failed Avisar client wrapper setup: %v", err)
	}
}

func setupDBConnection() {
	for {
		// Initialize the DB - Keep retrying if there is no connectivity
		if err := db.Initialize(cfg, gLogger); err != nil {
			gLogger.LogError("Failed to setup DB connection : %v", err)
			time.Sleep(5 * time.Second)
		} else {
			gLogger.LogInfo("DB Connection setup to RDS")
			break
		}
	}
}

func startRNSCVerticalScaler() cogs.Machine {
	stateCfg := make(map[string]*stateConfig)
	for _, state := range cfg.UpgradeService.States {
		gLogger.LogDebug("Read state configuration : %+v", state)
		stateCfg[state.Name] = &stateConfig{
			name:        state.Name,
			concurrency: state.Concurrency,
			pollInterval: cogs.CronPeriod{
				Hours:   state.PollInterval.Hours,
				Minutes: state.PollInterval.Minutes,
				Seconds: state.PollInterval.Seconds,
			},
		}
		if stateCfg[state.Name].concurrency == 0 {
			stateCfg[state.Name].concurrency = 1
		}
	}
	// HACK: FIX SRE generated cfg.yaml
	pubsubTopic := "event_vscale_topic"
	pubsubSubscriptionName := fmt.Sprintf("%s-%s-sub", "event_vscale_topic",
		strings.TrimPrefix(cfg.UpgradeService.VertScalePubSubTopic, pubsubTopic+"_"))
	gLogger.LogInfo("Setting up a subscriber on %s", pubsubSubscriptionName)

	// The DB handler is not setup with the machine definition. We will set it up once
	// we know if we're the Active Orchestrator.
	m := cogs.NewMachine(
		cogs.WithLogger(gLogger),
		cogs.WithState(
			cogs.NewState(
				"api",
				"Proxy GCP Pub/Sub API Calls",
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithPubSubSubscriber(
					cfg.UpgradeService.GCPProjectID, pubsubSubscriptionName)),
					cogs.WithHandler(api.PubSubHandler)),
				cogs.WithPublisher(cogs.WithSNS(cfg.AcctID)),
				cogs.WithMaxConcurrency(stateCfg["api"].concurrency),
			),
		),
		cogs.WithState(
			cogs.NewState(
				"instance_checker",
				"Check SPN health",
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithSQS("instance_checker_q.fifo")),
					cogs.WithHandler(instance_checker.Handler)),
				cogs.WithCronJob(instance_checker.HealthCheckCron, stateCfg["instance_checker"].pollInterval),
				cogs.WithPublisher(cogs.WithSNS(cfg.AcctID)),
				cogs.WithMaxConcurrency(stateCfg["instance_checker"].concurrency),
			),
		),
		cogs.WithState(
			cogs.NewState(
				"orch_client",
				"Client to the Orchestrator",
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithSQS("orch_client_q.fifo")),
					cogs.WithHandler(orch_client.Handler)),
				cogs.WithPublisher(cogs.WithSNS(cfg.AcctID)),
			),
		),
		cogs.WithState(
			cogs.NewState(
				"ha_sync_mon",
				"Monitor HA Sync notifications",
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithSQS("ha_sync_mon_q.fifo")),
					cogs.WithHandler(ha_sync_mon.Handler)),
				cogs.WithCronJob(ha_sync_mon.LazyPoller, stateCfg["ha_sync_mon"].pollInterval),
				cogs.WithPublisher(cogs.WithSNS(cfg.AcctID)),
			),
		),
		cogs.WithState(
			cogs.NewState(
				"failover_mon",
				"Monitor HA Failover notifications",
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithSQS("failover_mon_q.fifo")),
					cogs.WithHandler(failover_mon.Handler)),
				cogs.WithCronJob(failover_mon.LazyPoller, stateCfg["failover_mon"].pollInterval),
				cogs.WithPublisher(cogs.WithSNS(cfg.AcctID)),
			),
		),
		cogs.WithState(
			cogs.NewState(
				"reporting",
				"Publish status and metrics for Cosmos' consumption",
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithSQS("reporting_q.fifo")),
					cogs.WithHandler(reporting.Handler)),
				// cogs.WithCronJob(reporting.PeriodicPublisher, stateCfg["reporting"].pollInterval),
				cogs.WithPublisher(cogs.WithPubSubPublisher(cfg.UpgradeService.GCPProjectID,
					definitions.TopicMetricsPublish)),
			),
		),
	)
	return m
}

func startMUAutoScaler() cogs.Machine {
	pubsubTopic := "event_vscale_topic"
	pubsubSubscriptionName := fmt.Sprintf("%s-%s-sub", "event_autoscale_topic",
		strings.TrimPrefix(cfg.UpgradeService.VertScalePubSubTopic, pubsubTopic+"_"))
	gLogger.LogInfo("Setting up a subscriber on %s", pubsubSubscriptionName)
	m := cogs.NewMachine(
		cogs.WithLogger(gLogger),
		cogs.WithState(
			cogs.NewState(
				"api",
				"Proxy GCP Pub/Sub API Calls",
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithPubSubSubscriber(
					cfg.UpgradeService.GCPProjectID, pubsubSubscriptionName)),
					cogs.WithHandler(muAPI.PubSubHandler)),
				cogs.WithPublisher(cogs.WithPubSubPublisher(cfg.UpgradeService.GCPProjectID,
					definitions.TopicMetricsPublish)),
				cogs.WithMaxConcurrency(100),
			),
		),
		cogs.WithState(
			cogs.NewState(
				"cleanup",
				"Monitor and Cleanup processed scale events",
				cogs.WithCronJob(cleanup.Cleanup, cogs.CronPeriod{Minutes: 1}),
				cogs.WithPublisher(cogs.WithPubSubPublisher(cfg.UpgradeService.GCPProjectID,
					definitions.TopicMetricsPublish)),
				cogs.WithMaxConcurrency(1),
			),
		),
		cogs.WithState(
			cogs.NewState(
				"scheduled scaler",
				"Trigger Scheduled Scale Events",
				cogs.WithCronJob(scheduled.Scaler, cogs.CronPeriod{Minutes: 5}),
				cogs.WithPublisher(cogs.WithPubSubPublisher(cfg.UpgradeService.GCPProjectID,
					definitions.TopicMetricsPublish)),
				cogs.WithMaxConcurrency(1),
			),
		),
	)
	return m
}

func startEPCNATScaler() cogs.Machine {
	pubsubTopic := "event_epcnatscale_topic"
	pubsubSubscriptionName := fmt.Sprintf("%s-%s-sub", "event_epcnatscale_topic",
		strings.TrimPrefix(cfg.UpgradeService.EPCnatScalePubSubTopic,
			pubsubTopic+"_"))
	gLogger.LogInfo("Setting up a subscriber on %s", pubsubSubscriptionName)

	mcStates := []cogs.MachineOption{
		cogs.WithLogger(gLogger),
		cogs.WithState(
			cogs.NewState(
				"ep cnat",
				"Scaleout Cloud NAT for EP Tenant",
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithPubSubSubscriber(
					cfg.UpgradeService.GCPProjectID, pubsubSubscriptionName)),
					cogs.WithHandler(epAPI.PubSubHandler)),
				cogs.WithPublisher(cogs.WithSNS(cfg.AcctID)),
				cogs.WithMaxConcurrency(10),
			),
		),
	}

	paAlertPubsubTopic := "csp_to_pa_alert_topic"
	for shard := 1; shard <= cfg.UpgradeService.CSPToPAAlertPubSubTopicNumShards; shard++ {
		pubsubSubscriptionName := fmt.Sprintf("%s_%d-%s-sub", paAlertPubsubTopic, shard,
			strings.TrimPrefix(cfg.UpgradeService.CSPToPAAlertPubSubTopic, paAlertPubsubTopic+"_"))
		mcStates = append(mcStates,
			cogs.WithState(
				cogs.NewState(
					"ep cnat",
					"Scaleout Cloud NAT for EP Tenant",
					cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithPubSubSubscriber(
						cfg.UpgradeService.GCPProjectID, pubsubSubscriptionName)),
						cogs.WithHandler(epAPI.PubSubHandler)),
					cogs.WithPublisher(cogs.WithSNS(cfg.AcctID)),
					cogs.WithMaxConcurrency(10),
				),
			),
		)
		gLogger.LogInfo("Setting up a subscriber on %s", pubsubSubscriptionName)
	}
	m := cogs.NewMachine(
		mcStates...,
	)
	return m
}

// TODO: Hook the OCI alerting workflow into cogs here to receive NAT port exhaustion alerts
func startMUCNATScaler() cogs.Machine {
	gLogger.LogInfo("startMUCNATScaler: Starting MU Cloud NAT scaler state machine...")
	m := cogs.NewMachine(
		cogs.WithLogger(gLogger),
		cogs.WithRestAPI(rest.GetAPIRouter(), 8080),
		cogs.WithState(
			cogs.NewState(
				"mu cnat scaler",
				"MU CNAT Scaler state machine",
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithRestCall(
					"POST", "/api/orch-upgrade-service/oci-alarm/")),
					cogs.WithHandler(muCnatAPI.PubSubHandler)),
				cogs.WithMaxConcurrency(10),
			),
		),
		cogs.WithState(
			cogs.NewState(
				"scheduled nat auto scale status monitor",
				"Monitor Cloud NAT auto scaled instances and update their status",
				cogs.WithCronJob(monitor.UpdateCNATAutoScaleStatus, cogs.CronPeriod{Seconds: 15}),
				cogs.WithMaxConcurrency(1),
			),
		),
		cogs.WithState(
			cogs.NewState(
				"scheduled nat auto scale cleanup from mu_cnat_scaleout_status RDS",
				"Sync Cloud NAT auto scaled instances with entries in instance_master database and clean up any stale entries",
				cogs.WithCronJob(monitor.CleanupSyncMUCNATScaleStatusWithIM, cogs.CronPeriod{Minutes: 5}),
				cogs.WithMaxConcurrency(1),
			),
		),
		cogs.WithState(
			cogs.NewState(
				"scheduled nat auto scale cleanup from mu_cnat_scaleout RDS",
				"Sync Cloud NAT auto scaled instances with entries in instance_master database and clean up any stale entries",
				cogs.WithCronJob(monitor.CleanupSyncMUCNATScaleOutWithIM, cogs.CronPeriod{Minutes: 5}),
				cogs.WithMaxConcurrency(1),
			),
		),
	)
	return m
}

func main() {
	gLogger.LogInfo("Starting the upgrade_service...")
	utilsContext := utils.GetContext()

	// go reporting.ServePrometheusMetrics()
	vertScaleMachine := startRNSCVerticalScaler()
	horScaleMachine := startMUAutoScaler()
	epCnatScaleMachine := startEPCNATScaler()
	muCnatScaleMachine := startMUCNATScaler()
	curOrchState := utils.OrchStateInit
	prevOrchState := utils.OrchStateInit
	sigChan := make(chan os.Signal, 2)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)
	for {
		select {
		case sig := <-sigChan:
			switch sig {
			case os.Interrupt:
				// Handle SIGINT
				fallthrough
			case syscall.SIGTERM:
				// Handle SIGTERM
				gLogger.LogInfo("Got %s. Attempting Graceful Termination", sig.String())
				vertScaleMachine.Stop()
				vertScaleMachine.Wait()
				gLogger.LogInfo("Terminated the vertScaleMachine")
				horScaleMachine.Stop()
				horScaleMachine.Wait()
				gLogger.LogInfo("Terminated the horScaleMachine")
				epCnatScaleMachine.Stop()
				epCnatScaleMachine.Wait()
				gLogger.LogInfo("Terminated the EP CNAT Scaleout machine")
				gLogger.LogInfo("Finished stopping the cogs machines. Exiting now...")
				muCnatScaleMachine.Stop()
				muCnatScaleMachine.Wait()
				gLogger.LogInfo("Terminated the MU CNAT Scaleout machine")
				gLogger.LogInfo("Finished stopping the cogs machines. Exiting now...")
				os.Exit(1)
			default:
				gLogger.LogInfo("Unhandled Signal: %s", sig.String())
			}
		case <-time.After(10 * time.Second):
			curOrchState = utilsContext.GetOrchCurrentState(gLogger)
			if prevOrchState != curOrchState {
				if curOrchState == utils.OrchStateActive {
					gLogger.LogInfo("Orchestrator is Active. Starting service...")
					setupDBConnection()
					// Reset the DB pointer used by the state machine
					cogs.WithSQLConn(db.GetDBConn())
					vertScaleMachine.Start()
					horScaleMachine.Start()
					epCnatScaleMachine.Start()
					muCnatScaleMachine.Start()
					gLogger.LogInfo("Started the Upgrade Service")
				} else {
					gLogger.LogInfo("Orchestrator has moved to Pasive or Init. Stopping service...")
					vertScaleMachine.Stop()
					vertScaleMachine.Wait()
					horScaleMachine.Stop()
					horScaleMachine.Wait()
					epCnatScaleMachine.Stop()
					epCnatScaleMachine.Wait()
					muCnatScaleMachine.Stop()
					muCnatScaleMachine.Wait()
					db.ResetDBConn(gLogger)
					gLogger.LogInfo("Stopped the Upgrade Service")
				}
			}
			prevOrchState = curOrchState
		}
	}
}
