package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"orchestrator/libs/go/avisar"
	"orchestrator/libs/go/dbaccess/models/cloud_allowed_machine_types"
	"orchestrator/libs/go/dbaccess/models/instance_master"
	"orchestrator/libs/go/dbaccess/sql"
	"strconv"
	"strings"
	"time"

	"orchestrator/libs/common/shared/grpc/proto/avisarpb"

	"github.com/oracle/oci-go-sdk/v65/common"
	"github.com/oracle/oci-go-sdk/v65/core"
	"go.panw.local/pangolin/clogger"
)

const (
	//ociAuthCredentialsConfig string = "/orch_aas/libs/oci_credentials.json"
	ociAuthCredentialsConfig string = "/orch_aas/libs/oci_credentials.cfg"
)

func DeriveMachineTypeInfo(i *instance_master.Row) (*cloud_allowed_machine_types.Row, error) {
	mt := cloud_allowed_machine_types.Row{}
	if i.CloudProvider.String() == "gcp" {
		// Supported naming scheme
		//		<family>-standard-<cpu>
		//		<family>-highmem-<cpu>
		//		<family>-highcpu-<cpu>
		//		<family>-custom-<cpu>-<memory>
		nameList := strings.Split(i.NativeMachineType.String(), "-")
		if len(nameList) > 2 {
			switch nameList[1] {
			case "standard":
				vcpu, err := strconv.Atoi(nameList[2])
				if err != nil {
					return nil, err
				}
				mt.VCPU = sql.Int64(vcpu)
				mt.Memory = sql.Int64(vcpu * 4)
			case "highmem":
				vcpu, err := strconv.Atoi(nameList[2])
				if err != nil {
					return nil, err
				}
				mt.VCPU = sql.Int64(vcpu)
				mt.Memory = sql.Int64(vcpu * 8)
			case "highcpu":
				vcpu, err := strconv.Atoi(nameList[2])
				if err != nil {
					return nil, err
				}
				mt.VCPU = sql.Int64(vcpu)
				mt.Memory = sql.Int64(vcpu)
			case "custom":
				if len(nameList) != 4 {
					return nil, fmt.Errorf("unsupported machine type name %v", nameList)
				}
				vcpu, err := strconv.Atoi(nameList[2])
				if err != nil {
					return nil, err
				}
				mem, err := strconv.Atoi(nameList[3])
				if err != nil {
					return nil, err
				}
				mt.VCPU = sql.Int64(vcpu)
				mt.Memory = sql.Int64(mem / 1024)
			default:
				return nil, fmt.Errorf("unsupported machine type name %v", nameList)
			}
		}
	} else if i.CloudProvider.String() == "aws" {
		// Supported naming scheme
		//		<family>.<size>large
		//	If family starts with 'r', then memory multiplier is 8 instead of 4
		multiplier := sql.Int64(4)
		nameList := strings.Split(i.NativeMachineType.String(), ".")
		if len(nameList) == 2 {
			if nameList[0][0] == 'r' {
				multiplier = 8
			}
			switch nameList[1] {
			case "large":
				mt.VCPU = 2
				mt.Memory = mt.VCPU * multiplier
			case "xlarge":
				mt.VCPU = 4
				mt.Memory = mt.VCPU * multiplier
			case "2xlarge":
				mt.VCPU = 8
				mt.Memory = mt.VCPU * multiplier
			case "4xlarge":
				mt.VCPU = 16
				mt.Memory = mt.VCPU * multiplier
			default:
				return nil, fmt.Errorf("unsupported AWS machine type: %v", nameList)
			}
		}
	}
	if mt.VCPU == 0 {
		return nil, fmt.Errorf("Didn't find VCPU information for %s in %s",
			i.NativeMachineType.String(), i.CloudProvider.String())
	}
	var sp instance_master.SaltProfile
	if err := json.Unmarshal([]byte(i.SaltProfile.String()), &sp); err != nil {
		return nil, err
	}
	if i.ID == i.ClusterID {
		mt.DefaultCapType = sql.String(sp.PrimaryCapacityType)
	} else {
		mt.DefaultCapType = sql.String(sp.SecondaryCapacityType)
	}
	return &mt, nil
}

func GetNATInfoFromOCID(natOCID string, regionName string) (string, string, error) {
	var err error
	if natOCID == "" {
		return "", "", err
	}
	// Set up the configuration
	configProvider := common.CustomProfileConfigProvider(ociAuthCredentialsConfig, "DEFAULT")
	// Create a new virtual network client using the configuration provider
	c, err := core.NewVirtualNetworkClientWithConfigurationProvider(configProvider)
	if err != nil {
		return "", "", fmt.Errorf("Failed to create virtual network client to fetch NATGW info from oci %v", err)
	}
	c.SetRegion(regionName)
	// Create a request to get the NAT Gateway
	request := core.GetNatGatewayRequest{NatGatewayId: common.String(natOCID)}

	// Send the request and retrieve the NAT Gateway
	response, err := c.GetNatGateway(context.Background(), request)
	if err != nil {
		return "", "", fmt.Errorf("Failed to fetch NATGW info from OCI using NAT OCID %s : %v", natOCID, err)
	}

	natGateway := response.NatGateway
	natGatewayName := *natGateway.DisplayName
	natGatewayCompartmentID := *natGateway.CompartmentId
	return natGatewayName, natGatewayCompartmentID, err
}

func PublishAvisarEvent(sender, tenantID, custID, nodeType, cloudProvider, regionID, regionName, projectID, tenantName, module, supTenantID, severity, action, stackName, metricType, metricState, awsEnv string, eLog *clogger.EventLogger) error {
	var err error
	if eLog == nil {
		return fmt.Errorf("PublishAvisarEvent: EventLogger is nil")
	}
	avisarCw := avisar.GetClientWrapper()
	if avisarCw == nil {
		eLog.LogError("PublishAvisarEvent: Failed to get Avisar client wrapper - returned nil")
		return fmt.Errorf("PublishAvisarEvent: Failed to get Avisar client wrapper")
	}
	eLog.LogInfo("PublishAvisarEvent: Publish event to Avisar: Avisar client wrapper -> %v", avisarCw)

	event := &avisarpb.Event{
		Sender:        sender,
		TenantId:      tenantID,
		CustId:        custID,
		Timestamp:     uint64(time.Now().UnixMilli()),
		NodeType:      nodeType,
		CloudProvider: cloudProvider,
		RegionId:      regionID,
		RegionName:    regionName,
		StackName:     stackName,
		GcpProjectId:  projectID,
		AwsEnv:        awsEnv,
		TenantName:    tenantName,
		Module:        module,
		SupTenantId:   supTenantID,
		Severity:      severity,
		Action:        action,
		MetricType:    metricType,
		MetricState:   metricState,
	}

	sent, resp, err := avisarCw.SendEvent(event, eLog)
	if err != nil {
		eLog.LogError("PublishAvisarEvent: Failed to publish event to Avisar: %v, event=%#v", err, *event)
		return fmt.Errorf("PublishAvisarEvent: Failed to publish event to Avisar: %v", err)
	} else if sent {
		eLog.LogInfo("PublishAvisarEvent: Avisar event publish response: %v", resp)
	}
	return err
}
