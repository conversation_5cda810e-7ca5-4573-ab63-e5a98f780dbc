package api

import (
	"crypto"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"orchestrator/libs/common/shared/grpc/proto/avisarpb"
	"orchestrator/libs/go/cogs"
	"orchestrator/libs/go/config"
	"orchestrator/libs/go/dbaccess/models/cust_master"
	"orchestrator/libs/go/dbaccess/models/instance_master"
	"orchestrator/libs/go/dbaccess/models/mu_cnat_scaleout"
	"orchestrator/libs/go/dbaccess/models/mu_cnat_scaleout_status"
	"orchestrator/libs/go/dbaccess/models/oci_tenant_network_info"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/utils"
	cfgUtil "orchestrator/upgrade_service/config"
	ascaleapi "orchestrator/upgrade_service/states/autoscale/api"
	"orchestrator/upgrade_service/states/autoscale/concurrency"
	usUtil "orchestrator/upgrade_service/utils"
	"strconv"
	"strings"
	"time"
)

type NATInfo struct {
	RegionID        int64
	RegionIDStr     string
	InstanceID      int64
	InstanceIDStr   string
	TenantID        int64
	TenantIDStr     string
	InstanceOCID    string
	IsEdge          bool
	CompartmentID   string
	EgressIPList    string
	ComputeRegionID int64
	NATAlarmStatus  string
}

var (
	emptyResp cogs.PublishMessage
	cfg       *config.Config
)

const (
	ActionScaleout                         = "mu_cnat_scaleout"
	NATGWAlertOKToFiring                   = "OK_TO_FIRING"
	NATGWAlertFiring                       = "FIRING"
	NATGWAlertFiringToOK                   = "FIRING_TO_OK"
	NATGWAlertReset                        = "RESET"
	NATGWMetricNamespace                   = "oci_nat_gateway"
	OCISubscriptionConfirmHeader           = "X-OCI-NS-ConfirmationURL"
	OCISigningCertURLHeader                = "X-OCI-NS-SigningCertURL"
	OCISignatureHeader                     = "X-OCI-NS-Signature"
	OCITimestampHeader                     = "X-OCI-NS-Timestamp"
	OCISignatureVersion                    = "X-OCI-NS-SignatureVersion"
	OCITopicOCIDHeader                     = "X-OCI-NS-TopicOcid"
	OCITopicNameHeader                     = "X-OCI-NS-TopicName"
	OCIMessageIDHeader                     = "X-OCI-NS-MessageId"
	OCIMessageTypeHeader                   = "X-OCI-NS-MessageType"
	SaaSAgentSNSTrigger                    = "mysql_trigger_to_sns"
	SaaSAgentSNSTriggerAction              = "update_egress_nat_rule_update_egress_ips"
	NATNoPortDrops                         = "NoPorts"
	NATProvisioningInProgress              = "PROVISIONING_IN_PROGRESS"
	NATProvisioningCompleted               = "PROVISIONING_COMPLETED"
	OCICloud                               = "oci"
	StatusOK                               = "OK"
	StatusFiring                           = "FIRING"
	NATPortsExhaustionAlarmMsg             = "NAT Gateway Ports Exhausted. Need to auto scale NAT gateway in your region"
	AvisarEventCNATPortsExhaustion         = "Cloud NAT ports exhaustion"
	AvisarEventCNATPortsExhaustionResolved = "Cloud NAT ports exhaustion resolved"
)

func init() {
	var err error
	cfg, err = cfgUtil.ReadConfig("/orch_aas/libs/cfg.yaml")
	if err != nil {
		log.Fatalf("mu_cnat_scale init(): Failed to read cfg.yaml : %v\n", err)
	}
}

func getNATRegionIDFromEgressIPList(dbCtx *sql.DbContext, eipList, computeRegionID string) (int64, bool) {
	eLog := dbCtx.Logger
	var regionID int64
	regionID, _ = strconv.ParseInt(computeRegionID, 10, 64)
	eLog.LogInfo("getNATRegionIDEgressIPList: Parse egress_ip_list for the NAT %s for compute region %s", eipList, computeRegionID)
	ipList := make(map[string][]string)
	if err := json.Unmarshal([]byte(eipList), &ipList); err != nil {
		eLog.LogError("getNATRegionIDFromEgressIPList: Error unmarshalling egress IP list: %v. Return compute region %d for egress_ip_list %s", err, regionID, eipList)
		return regionID, false
	}
	if len(ipList) == 0 {
		eLog.LogInfo("getNATRegionIDEgressIPList: No egress IP list found for compute region %s. Return compute region %d for egress_ip_list %s", computeRegionID, regionID, eipList)
		return regionID, false
	}

	for region, _ := range ipList {
		regionID, _ = strconv.ParseInt(region, 10, 64)
		eLog.LogInfo("getNATRegionIDEgressIPList: Found edge region %s for NAT for compute region %s. Return edge region %d for egress_ip_list %s", region, computeRegionID, regionID, eipList)
		return regionID, true
	}
	return regionID, false
}

/*
// sendUpdatedEgressNATIPsToSaaSAgent is invoked on when new egress IP mappings are available.
// It sends the updated egress IP mappings to the SaaS agent to update the NAT rule
func sendUpdatedEgressNATIPsToSaaSAgent(tenantID, regionID string, eipMappings []string, eipMappingsEdge map[string][]string, dbCtx *sql.DbContext) error {
	eLog := dbCtx.Logger
	eLog.LogInfo("sendUpdatedEgressNATIPsToSaaSAgent: Trigger SNS to SaaS agent with EIP mappings %v EIP mappings edge %v for tenant %s, region %s", eipMappings, eipMappingsEdge, tenantID, regionID)
	// Build the payload for lambda invocation
	var eipMappingsEdgeStr string
	var eipMappingsStr string
	if len(eipMappingsEdge) > 0 {
		jsonData, err := json.Marshal(eipMappingsEdge)
		if err != nil {
			fmt.Println("sendUpdatedEgressNATIPsToSaaSAgent: Error marshalling JSON:", err)
			return err
		}
		eipMappingsEdgeStr = string(jsonData)
	}
	if len(eipMappings) > 0 {
		eipMappingsStr = strings.Join(eipMappings, ",")
	}

	payload, err := json.Marshal(map[string]string{"table": "instance_master", "compute_region_id": regionID, "tenant_id": tenantID, "action": SaaSAgentSNSTriggerAction, "egress_ip_mappings": eipMappingsStr, "cloud_provider": "oci", "row_id": tenantID, "timestamp": time.Now().Format("2006-01-02 15:04:05"), "egress_ip_mappings_edge": eipMappingsEdgeStr})
	if err != nil {
		eLog.LogError("sendUpdatedEgressNATIPsToSaaSAgent: json.Marshal failed : %v", err)
		return err
	}

	// Invoke the "mysql_trigger_to_sns" lambda with the payload
	eLog.LogInfo("sendUpdatedEgressNATIPsToSaaSAgent: Payload for %s lambda: %s", SaaSAgentSNSTrigger, string(payload))
	lambdaContext := lambda.GetContext()
	result, err := lambdaContext.Invoke(SaaSAgentSNSTrigger, payload)
	if err != nil {
		eLog.LogError("sendUpdatedEgressNATIPsToSaaSAgent: Error calling %s lambda, error: %v, result %v", SaaSAgentSNSTrigger, err, result)
		return err
	}

	var parsedResponse interface{}
	eLog.LogInfo("sendUpdatedEgressNATIPsToSaaSAgent: Result from lambda invocation %v", result)
	if err := json.Unmarshal(result, &parsedResponse); err != nil {
		eLog.LogError("sendUpdatedEgressNATIPsToSaaSAgent: Failed to unmarshal response from mysql_trigger_to_sns lambda: %v", err)
		return err
	}
	eLog.LogInfo("sendUpdatedEgressNATIPsToSaaSAgent: Response from mysql_trigger_to_sns lambda: %s", parsedResponse.(string))
	if parsedResponse.(string) != "published event to sns" {
		eLog.LogError("sendUpdatedEgressNATIPsToSaaSAgent: mysql_trigger_to_sns lambda failed. Response : %v, Error: %v", parsedResponse, err)
		return err
	}
	eLog.LogInfo("sendUpdatedEgressNATIPsToSaaSAgent: mysql_trigger_to_sns lambda completed successfully for tenant %s for region %s", tenantID, regionID)
	return err
}
*/

func confirmOCISubscriptionURL(dbCtx *sql.DbContext, url string) error {
	eLog := dbCtx.Logger
	eLog.LogInfo("confirmOCISubscriptionURL: Performing HTTP get on OCI subscription URL: %s", url)
	// Perform an HTTP GET request to the ConfirmationURL
	response, err := http.Get(url)
	if err != nil {
		eLog.LogError("confirmOCISubscriptionURL: Error while making GET request: %v for URL %s", err, url)
		return err
	}
	eLog.LogInfo("confirmOCISubscriptionURL: Successfully Performed HTTP get on OCI subscription URL: %s", url)
	defer response.Body.Close()
	return err
}

func ociApiContainsConfirmSubscriptionURLHeader(mc *cogs.MessageContext) (bool, string) {
	eLog := utils.GetEventLogger(mc.Logger)
	if mc.Request != nil {
		for headerName, headerValues := range mc.Request.Header {
			// eLog.LogInfo("ociApiContainsConfirmSubscriptionURLHeader: Header is = %s", headerName)
			if strings.EqualFold(OCISubscriptionConfirmHeader, headerName) {
				eLog.LogInfo("ociApiContainsConfirmSubscriptionURLHeader: Value is = %s", headerValues[0])
				return true, headerValues[0]
			}
		}
	}
	return false, ""
}

/*
func areIPMappingsEqual(lastEIPMappings, currentEIPMappings []string) bool {
	if len(lastEIPMappings) != len(currentEIPMappings) {
		return false
	}
	sort.Strings(lastEIPMappings)
	sort.Strings(currentEIPMappings)
	return reflect.DeepEqual(lastEIPMappings, currentEIPMappings)
}
*/

func isNATScaleOutInProgress(dbCtx *sql.DbContext, tenantID int64, regionID string) bool {
	eLog := dbCtx.Logger
	eLog.LogInfo("isNATScaleOutInProgress: Check if there is any ongoing NAT provisioning for tenant_id: %d, region_id: %s", tenantID, regionID)
	natRows, err := mu_cnat_scaleout_status.GetRowsByTenantIDRegionID(dbCtx, tenantID, regionID)
	if err != nil {
		eLog.LogInfo("isNATScaleOutInProgress: Failed to find NAT data from DB %v Error %v", natRows, err)
		return false
	}
	var natCount int
	for _, row := range natRows {
		if row.NATAutoScaleStatus.String() == NATProvisioningInProgress {
			eLog.LogInfo("isNATScaleOutInProgress: Found ongoing NAT %s provisioning for tenant_id: %d, region_id: %s", row.NATGWInstanceID.String(), tenantID, regionID)
			natCount++
		}
	}
	if natCount > 0 {
		eLog.LogInfo("isNATScaleOutInProgress: Found %d ongoing NAT provisioning for tenant_id: %d, region_id: %s", natCount, tenantID, regionID)
		return true
	}
	eLog.LogInfo("isNATScaleOutInProgress: No ongoing NAT provisioning found for tenant_id: %d, region_id: %s. Continue to scale out..", tenantID, regionID)
	return false
}

func checkEgressIPsForComputeNATEgress(dbCtx *sql.DbContext, natGWRegionID, natGWTenantIDInt64, computeRegionID int64) ([]string, []string, error) {
	eLog := dbCtx.Logger
	var eipMappings []string
	var configuredEipMappings []string
	eLog.LogInfo("checkEgressIPsForComputeNATEgress: NATGW Region %d, Tenant ID %d", natGWRegionID, natGWTenantIDInt64)
	// Fetch all existing running NAT gateways from IM for the given region and tenant combination
	natDBData, err := instance_master.GetNATGWInfoPerTenantPerRegion(dbCtx, natGWRegionID, natGWTenantIDInt64, computeRegionID, false, "", OCICloud)
	if err != nil {
		eLog.LogError("checkEgressIPsForComputeNATEgress: Failed to find NAT data from DB %v Error %v", natDBData, err)
		return eipMappings, configuredEipMappings, err
	}
	eLog.LogInfo("checkEgressIPsForComputeNATEgress: NATGW data: %v, NATGW Tenant ID: %d for region ID: %d", natDBData, natGWTenantIDInt64, natGWRegionID)

	// Here we will determine if we really need to scale up NAT in this region for the tenant
	// or we can steer traffic to use other lightly loaded NAT gateways instead
	for vmid, ip_mappings := range natDBData {
		eLog.LogInfo("checkEgressIPsForComputeNATEgress: Processing ip mappings %v for VM ID %s for NAT scale up", ip_mappings, vmid)
		natRow, err := mu_cnat_scaleout.GetRowByNATOCID(dbCtx, vmid)
		eLog.LogInfo("checkEgressIPsForComputeNATEgress: NAT row from mu_cnat_scaleout %v Err %v", natRow, err)
		if (err == nil && natRow.NATAlarmStatus.String() != NATGWAlertOKToFiring) || err != nil {
			if err != nil {
				eLog.LogInfo("checkEgressIPsForComputeNATEgress: RDS entry absent for NAT OCID in mu_cnat_scaleout %s for tenant %d", vmid, natGWTenantIDInt64)
			} else {
				eLog.LogInfo("checkEgressIPsForComputeNATEgress: RDS entry present for NAT OCID in mu_cnat_scaleout %s for tenant %d in alarm state %s", vmid, natGWTenantIDInt64, natRow.NATAlarmStatus.String())
			}
			for pub_ip, _ := range ip_mappings {
				eipMappings = append(eipMappings, pub_ip)
			}
			eLog.LogInfo("checkEgressIPsForComputeNATEgress: Updated EIP mappings %v after processing VM ID %s IP mappings %v", eipMappings, vmid, ip_mappings)
		} else {
			eLog.LogInfo("checkEgressIPsForComputeNATEgress: Found RDS entry for NAT OCID from DB %s for tenant %d in alarming state %s", vmid, natGWTenantIDInt64, NATGWAlertOKToFiring)
			if natRow.EIPMappings.String() != "" && len(configuredEipMappings) == 0 {
				eLog.LogInfo("checkEgressIPsForComputeNATEgress: Extracting existing EIP mappings from RDS entry %s", natRow.EIPMappings.String())
				configuredEipMappings = strings.Split(natRow.EIPMappings.String(), ",")
			}
		}
	}
	return eipMappings, configuredEipMappings, err
}

func checkEgressIPsForEdgeNATEgress(dbCtx *sql.DbContext, natGWRegionID, natGWTenantIDInt64, computeRegionID int64, eipList string) ([]string, error) {
	eLog := dbCtx.Logger
	var eipMappings []string
	eLog.LogInfo("checkEgressIPsForEdgeNATEgress: NATGW Region %d, Tenant ID %d, Egress IP list %s", natGWRegionID, natGWTenantIDInt64, eipList)
	// var configuredEipMappings []string
	// Fetch all existing running NAT gateways from IM for the given region and tenant combination
	natDBData, err := instance_master.GetNATGWInfoPerTenantPerRegion(dbCtx, natGWRegionID, natGWTenantIDInt64, computeRegionID, true, eipList, OCICloud)
	if err != nil {
		eLog.LogError("checkEgressIPsForEdgeNATEgress: Failed to find NAT data from DB %v Error %v", natDBData, err)
		return eipMappings, err
	}
	eLog.LogInfo("checkEgressIPsForEdgeNATEgress: NATGW data: %v, NATGW Tenant ID: %d for region ID: %d", natDBData, natGWTenantIDInt64, natGWRegionID)

	// Here we will determine if we really need to scale up NAT in this region for the tenant
	// or we can steer traffic to use other lightly loaded NAT gateways instead
	for vmid, ip_mappings := range natDBData {
		eLog.LogInfo("checkEgressIPsForEdgeNATEgress: Processing ip mappings %v for VM ID %s for NAT scale up", ip_mappings, vmid)
		natRow, err := mu_cnat_scaleout.GetRowByNATOCID(dbCtx, vmid)
		eLog.LogInfo("checkEgressIPsForEdgeNATEgress: NAT row from mu_cnat_scaleout %v Err %v", natRow, err)
		if (err == nil && natRow.NATAlarmStatus.String() != NATGWAlertOKToFiring) || err != nil {
			if err != nil {
				eLog.LogInfo("checkEgressIPsForEdgeNATEgress: RDS entry absent for NAT OCID in mu_cnat_scaleout %s for tenant %d", vmid, natGWTenantIDInt64)
			} else {
				eLog.LogInfo("checkEgressIPsForEdgeNATEgress: RDS entry present for NAT OCID in mu_cnat_scaleout %s for tenant %d in alarm state %s", vmid, natGWTenantIDInt64, natRow.NATAlarmStatus.String())
			}
			for pub_ip, _ := range ip_mappings {
				eipMappings = append(eipMappings, pub_ip)
			}
			eLog.LogInfo("checkEgressIPsForEdgeNATEgress: Updated EIP mappings %v after processing VM ID %s IP mappings %v", eipMappings, vmid, ip_mappings)
		} else {
			eLog.LogInfo("checkEgressIPsForEdgeNATEgress: Found RDS entry for NAT OCID from DB %s for tenant %d in alarming state %s", vmid, natGWTenantIDInt64, NATGWAlertOKToFiring)
			/*
				if natRow.EIPMappings.String() != "" && len(configuredEipMappings) == 0 {
					eLog.LogInfo("checkEgressIPsForEdgeNATEgress: Extracting existing EIP mappings from RDS entry %s", natRow.EIPMappings.String())
					//configuredEipMappings = strings.Split(natRow.EIPMappings.String(), ",")
				}
			*/
		}
	}
	return eipMappings, err
}

func updateNATAlarmStatus(dbCtx *sql.DbContext, msg ascaleapi.OCICnatScaleMessage, natInfo []NATInfo, alertID string, custID int64) error {
	var err error
	var alarmStatus string
	eLog := dbCtx.Logger
	eLog.LogInfo("updateNATAlarmStatus: Publish alert data to DB with msg %v for NAT info %v", msg, natInfo)
	msgJSON, err := json.Marshal(msg)
	if err != nil {
		eLog.LogError("updateNATAlarmStatus: Failed to marshal NAT alarm message to JSON: %v", err)
		return err
	}
	msgJSONStr := string(msgJSON)
	for _, natEntry := range natInfo {
		natGWInstIDInt64 := natEntry.InstanceID
		natGWTenantIDInt64 := natEntry.TenantID
		natGWInstID := natEntry.InstanceIDStr
		if natEntry.NATAlarmStatus == StatusOK {
			alarmStatus = NATGWAlertFiringToOK
		} else {
			alarmStatus = NATGWAlertOKToFiring
		}
		// RDS update for mu_cnat_scaleout table for NATGW information
		if msg.Type == NATGWAlertOKToFiring {
			_, err := mu_cnat_scaleout.GetRowByNATOCID(dbCtx, natEntry.InstanceOCID)
			if err != nil {
				eLog.LogInfo("updateNATAlarmStatus: No existing RDS entry found for NATGW OCID %s, creating new row", natEntry.InstanceOCID)
				dbRow := &mu_cnat_scaleout.Row{
					TenantID:        sql.Int64(natEntry.TenantID),
					ProjectID:       sql.String(natEntry.CompartmentID),
					RegionID:        sql.String(strconv.FormatInt(natEntry.ComputeRegionID, 10)),
					NATGWUID:        sql.String(natEntry.InstanceOCID),
					NATGWInstanceID: sql.String(natEntry.InstanceIDStr),
					AlertID:         sql.String(alertID),
					NATAlarmStatus:  sql.String(alarmStatus),
					NATGWRegionID:   sql.String(natEntry.RegionIDStr),
					CustID:          sql.Int64(custID),
				}
				eLog.LogInfo("updateNATAlarmStatus: Publishing alert data to RDS %v for NATGW IM ID %d for tenant %d as alarm status is %s", dbRow, natGWInstIDInt64, natGWTenantIDInt64, alarmStatus)
				err = dbRow.Save(dbCtx)
				if err != nil {
					eLog.LogError("updateNATAlarmStatus: Failed Publishing alert data to RDS %v for NATGW IM ID %d for tenant %d Error: %v", dbRow, natGWInstIDInt64, natGWTenantIDInt64, err)
					return err
				}
				err = mu_cnat_scaleout.UpdateNATGWAlarmDataForInstanceID(dbCtx, alarmStatus, natEntry.InstanceOCID, msgJSONStr)
				if err != nil {
					eLog.LogError("updateNATAlarmStatus: Failed to update entry from mu_cnat_scaleout for NATGW %s entry alarm status", natGWInstID)
					return err
				}
				eLog.LogInfo("updateNATAlarmStatus: Success Publishing alert data to RDS %v for NATGW IM ID %d for tenant %d", dbRow, natGWInstIDInt64, natGWTenantIDInt64)
			} else {
				eLog.LogInfo("updateNATAlarmStatus: Found existing RDS entry found for NATGW OCID %s, update existing row with status %s", natEntry.InstanceOCID, alarmStatus)
				err = mu_cnat_scaleout.UpdateNATGWAlarmDataForInstanceID(dbCtx, alarmStatus, natEntry.InstanceOCID, msgJSONStr)
				if err != nil {
					eLog.LogError("updateNATAlarmStatus: Failed to update entry from mu_cnat_scaleout for NATGW %s entry alarm status", natGWInstID)
					return err
				}
			}
		} else if msg.Type == NATGWAlertFiringToOK {
			natIMEntry, err := instance_master.GetRowByNATOCID(dbCtx, natEntry.InstanceOCID)
			if err != nil {
				eLog.LogError("updateNATAlarmStatus: Failed to fetch NATGW entry %v for OCID %s from IM. Err: %v", natIMEntry, natEntry.InstanceOCID, err)
			}
			cust, err := cust_master.GetRowByAcctID(dbCtx, natEntry.TenantID)
			if err != nil {
				eLog.LogError("updateNATAlarmStatus: Failed to find cust_master entry for %d: %v", natEntry.TenantID, err)
			}
			ociTenantNWInfo, err := oci_tenant_network_info.GetRowForTenantCompartment(dbCtx, natEntry.TenantID)
			if err != nil {
				eLog.LogError("updateNATAlarmStatus: Failed to fetch NATGW compartment OCID for NAT OCID %s", natEntry.InstanceOCID)
			}
			// We need to clear the alert raised earlier for this NATGW as port exhaustion is now resolved for this NATGW
			eLog.LogInfo("updateNATAlarmStatus: Sending Avisar alert event for clearing port exhaustion alert for NAT %s for tenant %d", natEntry.InstanceIDStr, natEntry.TenantID)
			err = raiseAvisarAlertEvent(dbCtx, cust, natIMEntry, ociTenantNWInfo, AvisarEventCNATPortsExhaustionResolved)
			if err != nil {
				eLog.LogError("updateNATAlarmStatus: Failed to send Avisar clearing alert for NAT %d for tenant %d Error -> %v", natEntry.InstanceID, natEntry.TenantID, err)
			}
			err = mu_cnat_scaleout.UpdateNATGWAlarmDataForInstanceID(dbCtx, alarmStatus, natEntry.InstanceOCID, msgJSONStr)
			if err != nil {
				eLog.LogError("updateNATAlarmStatus: Failed to update entry from mu_cnat_scaleout for NATGW %s entry alarm status", natGWInstID)
				return err
			}
			eLog.LogInfo("updateNATAlarmStatus: Skip CNAT scaling workflow for NATGW IM ID %d for tenant %d as the alarm status is %s", natGWInstIDInt64, natGWTenantIDInt64, msg.Type)
		} else if msg.Type == NATGWAlertReset {
			eLog.LogInfo("updateNATAlarmStatus: Alarm Reset Message with type %s received for NATGW IM ID %d for tenant %d Alarm status is %s. Process and update the NATGW DB status accordingly", msg.Type, natGWInstIDInt64, natGWTenantIDInt64, alarmStatus)
			err = mu_cnat_scaleout.UpdateNATGWAlarmDataForInstanceID(dbCtx, alarmStatus, natEntry.InstanceOCID, msgJSONStr)
			if err != nil {
				eLog.LogError("updateNATAlarmStatus: Failed to update entry from mu_cnat_scaleout for NATGW %s entry alarm status", natGWInstID)
				return err
			}
			eLog.LogInfo("updateNATAlarmStatus: Skip CNAT scaling workflow for NATGW IM ID %d for tenant %d as the alarm status is %s", natGWInstIDInt64, natGWTenantIDInt64, alarmStatus)
		} else {
			eLog.LogInfo("updateNATAlarmStatus: Skip CNAT scaling workflow for NATGW IM ID %d for tenant %d as the alarm status is %s", natGWInstIDInt64, natGWTenantIDInt64, msg.Type)
			return err
		}
	}
	return err
}

func parseOCIHttpRequestHeaders(mc *cogs.MessageContext, requestHeader string) string {
	eLog := utils.GetEventLogger(mc.Logger)
	if mc.Request != nil {
		for headerName, headerValues := range mc.Request.Header {
			// eLog.LogInfo("ociApiContainsConfirmSubscriptionURLHeader: Header is = %s", headerName)
			if strings.EqualFold(requestHeader, headerName) {
				eLog.LogInfo("ociApiContainsConfirmSubscriptionURLHeader: Value is = %s", headerValues[0])
				return headerValues[0]
			}
		}
	}
	return ""
}

func verifySignatureJSONMsg(ctx *cogs.MessageContext) error {
	eLog := utils.GetEventLogger(ctx.Logger)

	signature := parseOCIHttpRequestHeaders(ctx, OCISignatureHeader)
	signingCertURL := parseOCIHttpRequestHeaders(ctx, OCISigningCertURLHeader)
	timestamp := parseOCIHttpRequestHeaders(ctx, OCITimestampHeader)
	signVersion := parseOCIHttpRequestHeaders(ctx, OCISignatureVersion)
	topicName := parseOCIHttpRequestHeaders(ctx, OCITopicNameHeader)
	topicOCID := parseOCIHttpRequestHeaders(ctx, OCITopicOCIDHeader)
	notificationType := parseOCIHttpRequestHeaders(ctx, OCIMessageTypeHeader)
	notificationID := parseOCIHttpRequestHeaders(ctx, OCIMessageIDHeader)
	eLog.LogInfo("verifySignatureJSONMsg: Signature -> %s, Signing Certificate URL -> %s, Timestamp -> %s, OCI Signature version -> %s, Topic name -> %s, Topic OCID -> %s, notification type -> %s, notification ID -> %s",
		signature, signingCertURL, timestamp, signVersion, topicName, topicOCID, notificationType, notificationID)
	if signature == "" || signingCertURL == "" || timestamp == "" || signVersion != "2.0" || topicName == "" || topicOCID == "" || notificationType == "" || notificationID == "" {
		eLog.LogError("verifySignatureJSONMsg: Missing required header fields in JSON payload message for NAT port exhaustion")
		return fmt.Errorf("verifySignatureJSONMsg: missing required header fields. Skip processing the JSON payload message for NAT port exhaustion")
	}

	// Fetch public key from the provided URL
	resp, err := http.Get(signingCertURL)
	if err != nil {
		eLog.LogError("verifySignatureJSONMsg: fetching public key from URL: %v", err)
		return err
	}
	defer resp.Body.Close()

	pemData, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		eLog.LogError("verifySignatureJSONMsg: reading PEM data from response: %v", err)
		return err
	}

	// Decode PEM data
	block, _ := pem.Decode(pemData)
	if block == nil {
		eLog.LogError("verifySignatureJSONMsg: decoding PEM block failed")
		return err
	}

	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		eLog.LogError("verifySignatureJSONMsg: Parsing certificate failed: %v", err)
		return err
	}
	eLog.LogInfo("verifySignatureJSONMsg: Successfully parsed public cert data")

	publicKey, ok := cert.PublicKey.(*rsa.PublicKey)
	if !ok {
		eLog.LogError("verifySignatureJSONMsg: Error converting to RSA public key %v", err)
		return fmt.Errorf("public key is not RSA")
	}
	eLog.LogInfo("verifySignatureJSONMsg: Successfully converted parsed public cert to RSA public key")

	// Decrypt the signature
	decodedSignature, err := base64.StdEncoding.DecodeString(signature)
	if err != nil {
		eLog.LogError("verifySignatureJSONMsg: Error decoding signature: %v", err)
		return err
	}

	// Verify the signature
	hashed := crypto.SHA256.New()
	eLog.LogInfo("verifySignatureJSONMsg: Message to verify against OCI signature: %s", notificationType+notificationID+topicOCID+topicName+string(ctx.Payload)+timestamp)
	hashed.Write([]byte(notificationType + notificationID + topicOCID + topicName + string(ctx.Payload) + timestamp))

	// Verify the signature using RSA-PSS
	opts := &rsa.PSSOptions{SaltLength: rsa.PSSSaltLengthAuto, Hash: crypto.SHA256}
	err = rsa.VerifyPSS(publicKey, crypto.SHA256, hashed.Sum(nil), decodedSignature, opts)
	if err != nil {
		eLog.LogError("verifySignatureJSONMsg: OCI message Signature verification failed: %v", err)
		return err
	}

	eLog.LogInfo("verifySignatureJSONMsg: OCI message Signature verification worked ok.....continue processing")

	/*
		err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hashed.Sum(nil), decodedSignature)
		if err != nil {
			eLog.LogError("verifySignatureJSONMsg: OCI message Signature verification failed: %v", err)
		}
	*/

	return nil
}

func raiseAvisarAlertEvent(dbCtx *sql.DbContext, cm *cust_master.Row, im *instance_master.Row, otn *oci_tenant_network_info.Row, event string) error {
	eLog := dbCtx.Logger
	var env string
	var metricType string
	var metricState string
	var severity string
	parts := strings.SplitN(cm.URL.String(), ".", 2)
	if len(parts) > 0 {
		env = parts[0]
	}
	if event == AvisarEventCNATPortsExhaustion {
		metricType = avisarpb.ORCHESTRATION_NGPA_OCI_CLOUD_NAT_HITTING_PORT_EXHAUSTION
		metricState = "failure"
		severity = "Critical"
	} else if event == AvisarEventCNATPortsExhaustionResolved {
		metricType = avisarpb.ORCHESTRATION_NGPA_OCI_CLOUD_NAT_HITTING_PORT_EXHAUSTION_RESOLVED_SUCCESS
		metricState = "success"
		severity = "Info"
	}
	stackName := fmt.Sprintf("%d_%s_oci_%s", cm.AcctID.Int64(), otn.RegionName.String(), env)
	eLog.LogInfo("raiseAvisarAlertEvent: Stack Name %s for Avisar alert event", stackName)

	eLog.LogInfo("raiseAvisarAlertEvent: Raise Avisar Alert event for instance %d for tenant %d", im.ID.Int64(), im.AcctID.Int64())
	action := fmt.Sprintf("NAT-%d %s in Region %d", im.ID.Int64(), event, im.ComputeRegionIdx.Int64())
	err := usUtil.PublishAvisarEvent("UpgradeService_MuCNATScalerPubSubHandler", strconv.FormatInt(im.AcctID.Int64(), 10), strconv.FormatInt(im.CustID.Int64(), 10), strconv.FormatInt(im.NodeType.Int64(), 10),
		im.CloudProvider.String(), strconv.FormatInt(im.ComputeRegionIdx.Int64(), 10), im.ComputeRegionName.String(), otn.CompartmentId.String(), im.Name.String(),
		"MuCNATScalerPubSubHandler", strconv.FormatInt(im.AcctID.Int64(), 10), severity, action, stackName, metricType, metricState, cfg.AwsEnv, eLog)
	if err != nil {
		eLog.LogError("PrepareVmParams: Error %v publishing IP %s event to Avisar for row %v", err, event, im)
	}

	return err
}

// PubSubHandler is invoked for every alert notification generated by
// OCI/GCP Alert Policy for MU stack Cloud NAT instances.
func PubSubHandler(ctx *cogs.MessageContext) cogs.PublishMessage {
	var natOCID string
	var err error
	var natGWTenantIDInt64 int64
	var natGWComputeRegionID int64
	var cust *cust_master.Row
	var natIMEntry *instance_master.Row
	var ociTenantNWInfo *oci_tenant_network_info.Row
	var natGWCompartmentID string
	var natGWComputeRegionIDStr string
	var eipMappings []string
	var configuredEipMappings []string
	eLog := utils.GetEventLogger(ctx.Logger)
	eLog.NewLoggerEvent()
	natInfoMap := make(map[int64][]NATInfo)
	eipMappingsEdge := make(map[string][]string)

	eLog.LogInfo("mu_cnat_scale PubSubHandler: Received raw message: %s", string(ctx.Payload))
	dbCtx := &sql.DbContext{
		DbConn: ctx.DBConn,
		Logger: eLog,
	}
	var (
		msg      ascaleapi.OCICnatScaleMessage
		alertMsg ascaleapi.ScaleMessage
	)

	// Handle the OCI subscription by confirming the sub-URL (1 time thing)
	isOciConfirmSubHeaderPresent, ociSubConfirmationURL := ociApiContainsConfirmSubscriptionURLHeader(ctx)
	if isOciConfirmSubHeaderPresent {
		eLog.LogInfo("mu_cnat_scale PubSubHandler: OCI subscription URL found: %s", ociSubConfirmationURL)
		err = confirmOCISubscriptionURL(dbCtx, ociSubConfirmationURL)
		if err != nil {
			// TODO: Add retry logic to try the HTTP get if fails initially ?
			eLog.LogError("mu_cnat_scale PubSubHandler: Failed to perform HTTP get on the confirmation URL - %s : %v Return..", ociSubConfirmationURL, err)
		} else {
			eLog.LogInfo("mu_cnat_scale PubSubHandler: Successfully performed HTTP get on the confirmation URL - %s Return..", ociSubConfirmationURL)
		}
		return emptyResp
	}

	// Verify OCI NAT port exhasution alarm JSON message signature validation
	err = verifySignatureJSONMsg(ctx)
	if err != nil {
		eLog.LogError("mu_cnat_scale PubSubHandler: Failed verifying OCI JSON message signature %v", err)
		return emptyResp
	}

	if err := json.Unmarshal(ctx.Payload, &msg); err != nil {
		eLog.LogError("mu_cnat_scale PubSubHandler: Failed to unmarshal message : %v", err)
		return emptyResp
	}

	if len(msg.UUID) > 0 {
		eLog.SetLoggerTag(msg.UUID)
		alertMsg.UUID = msg.UUID
	}

	// Ignore alarms that are not related to NAT port exhaustion
	if msg.Body != NATPortsExhaustionAlarmMsg {
		eLog.LogError("mu_cnat_scale PubSubHandler: Alarm message body found : %s which is not required for OCI Cloud NAT horizontal scaling. Ignore processing this alarm", msg.Body)
		return emptyResp
	}
	eLog.LogInfo("mu_cnat_scale PubSubHandler: Received OCI NAT port exhaustion alarm: %v", msg)
	for _, metadata := range msg.AlarmMetaData {
		if metadata.Namespace != NATGWMetricNamespace {
			eLog.LogInfo("mu_cnat_scale PubSubHandler: Alarm received for OCI metric namespace: %s. Ignoring the alarm", metadata.Namespace)
			continue
		}
		eLog.LogInfo("mu_cnat_scale PubSubHandler: NAT port exhaustion alarm ID: %s", metadata.ID)
		// Step 1: Extract all NAT OCIDs alerting for port exhaustion
		for _, dimension := range metadata.Dimensions {
			natOCID = dimension.ResourceID
			eLog.LogInfo("mu_cnat_scale PubSubHandler: NAT GW OCID under port exhaustion: %s", natOCID)
			if natOCID == "" {
				eLog.LogInfo("mu_cnat_scale PubSubHandler: NAT OCID is empty, skipping further processing")
				continue
			}
			eLog.LogInfo("mu_cnat_scale PubSubHandler: Received Alert for Port exhaustion for NATGW OCID %s with status %s", natOCID, msg.Type)

			natIMEntry, err = instance_master.GetRowByNATOCID(dbCtx, natOCID)
			if err != nil {
				eLog.LogError("mu_cnat_scale PubSubHandler: Failed to fetch NATGW entry %v for OCID %s from IM. Err: %v", natIMEntry, natOCID, err)
				continue
			}

			ociTenantNWInfo, err = oci_tenant_network_info.GetRowForTenantCompartment(dbCtx, natIMEntry.AcctID.Int64())
			if err != nil {
				eLog.LogError("mu_cnat_scale PubSubHandler: Failed to fetch NATGW compartment OCID for NAT OCID %s", natOCID)
			}
			natGWCompartmentID = ociTenantNWInfo.CompartmentId.String()
			natGWComputeRegionIDStr = strconv.FormatInt(natIMEntry.ComputeRegionIdx.Int64(), 10)
			natGWComputeRegionID = natIMEntry.ComputeRegionIdx.Int64()
			natGWTenantIDInt64 = natIMEntry.AcctID.Int64()

			// Setup Avisar alerting message payload
			alertMsg.TenantID = natGWTenantIDInt64
			alertMsg.SubTenantID = natGWTenantIDInt64
			alertMsg.CloudProvider = OCICloud
			alertMsg.EventType = ascaleapi.EventTypeScaleUp
			alertMsg.RegionID = natGWComputeRegionID
			alertMsg.InstanceID = natIMEntry.ID.Int64()
			alertMsg.EventTimeEpoc = strconv.FormatInt(time.Now().Unix(), 10)
			eLog.LogInfo("mu_cnat_scale PubSubHandler: Avisar scale up event alerting message payload %v", alertMsg)

			region, isEdge := getNATRegionIDFromEgressIPList(dbCtx, natIMEntry.EgressIpList.String(), strconv.FormatInt(natIMEntry.ComputeRegionIdx.Int64(), 10))
			eLog.LogInfo("mu_cnat_scale PubSubHandler: Region %d, Is Edge NAT %v for egress IP list %s", region, isEdge, natIMEntry.EgressIpList.String())
			_, ok := natInfoMap[region]
			if ok {
				eLog.LogInfo("mu_cnat_scale PubSubHandler: NAT info already exists in the map for region %v appending new NAT entry", region)
				natInfoMap[region] = append(natInfoMap[region], NATInfo{RegionID: region, RegionIDStr: strconv.FormatInt(region, 10), InstanceID: natIMEntry.ID.Int64(), InstanceIDStr: strconv.FormatInt(natIMEntry.ID.Int64(), 10), InstanceOCID: natIMEntry.VMID.String(), TenantID: natIMEntry.AcctID.Int64(), IsEdge: isEdge, CompartmentID: natGWCompartmentID, EgressIPList: natIMEntry.EgressIpList.String(), ComputeRegionID: natGWComputeRegionID, NATAlarmStatus: metadata.Status})
			} else {
				natInfoMap[region] = []NATInfo{{RegionID: region, RegionIDStr: strconv.FormatInt(region, 10), InstanceID: natIMEntry.ID.Int64(), InstanceIDStr: strconv.FormatInt(natIMEntry.ID.Int64(), 10), InstanceOCID: natIMEntry.VMID.String(), TenantID: natIMEntry.AcctID.Int64(), IsEdge: isEdge, CompartmentID: natGWCompartmentID, EgressIPList: natIMEntry.EgressIpList.String(), ComputeRegionID: natGWComputeRegionID, NATAlarmStatus: metadata.Status}}
			}
			if cust == nil {
				cust, err = cust_master.GetRowByAcctID(dbCtx, natIMEntry.AcctID.Int64())
				if err != nil {
					eLog.LogError("mu_cnat_scale PubSubHandler: Failed to find cust_master entry for %d: %v", natGWTenantIDInt64, err)
					return ascaleapi.ErrorMessage(alertMsg, err, ascaleapi.AlertCloudNATScaleUpDBError)
				}
				eLog.LogInfo("mu_cnat_scale PubSubHandler: Found customer %s for tenant %d", cust.Name, natGWTenantIDInt64)
			}
			eLog.LogInfo("mu_cnat_scale PubSubHandler: NATGW compartment OCID %s for NAT OCID %s", natOCID, natGWCompartmentID)
			eLog.LogInfo("mu_cnat_scale PubSubHandler: Created new entry in the natInfoMap for region %v NAT info map %v", region, natInfoMap)
			// Raise Avisar alert when Cloud NAT notification for port exhaustion is confirmed
			eLog.LogInfo("mu_cnat_scale PubSubHandler: Publishing Avisar alert for NAT port exhaustion with alert msg: %v", alertMsg)
			// We should only raise Avisar alert here when the alarm status is FIRING or OK_TO_FIRING from OCI
			if msg.Type == NATGWAlertOKToFiring || msg.Type == NATGWAlertFiring {
				err = raiseAvisarAlertEvent(dbCtx, cust, natIMEntry, ociTenantNWInfo, AvisarEventCNATPortsExhaustion)
				if err != nil {
					eLog.LogError("mu_cnat_scale PubSubHandler: Failed to send Avisar alert for NAT %d for tenant %d Error -> %v", natIMEntry.ID.Int64(), natIMEntry.AcctID.Int64(), err)
				}
			}
		}

		scopeKey := fmt.Sprintf("%s-%s", strconv.FormatInt(natGWTenantIDInt64, 10), natGWComputeRegionIDStr)
		scope := concurrency.GetActionScope(scopeKey)
		scope.Lock()
		defer scope.Unlock()
		eLog.LogInfo("mu_cnat_scale PubSubHandler: Processing NAT Info map %v", natInfoMap)

		// Step 2: Update the NAT alarm status in the mu_cnat_scaleout with NATGWs that have alerted for port exhaustion
		for region, natInfo := range natInfoMap {
			eLog.LogInfo("mu_cnat_scale PubSubHandler: Updating DB with alert data for NAT info map for region %d", region)
			err = updateNATAlarmStatus(dbCtx, msg, natInfo, metadata.ID, cust.ID.Int64())
			if err != nil {
				eLog.LogError("mu_cnat_scale PubSubHandler: Failed Updating DB with alert data for NAT info map for region %d", region)
				return ascaleapi.ErrorMessage(alertMsg, err, ascaleapi.AlertCloudNATScaleUpDBError)
			}
		}

		// Step 3: Fetch existing IP mappings for compute NAT egress from instance_master
		for _, natInfo := range natInfoMap {
			for _, natEntry := range natInfo {
				if natEntry.IsEdge {
					eLog.LogInfo("mu_cnat_scale PubSubHandler: Processing Edge NAT ID %s in region %d tenant %d", natEntry.InstanceIDStr, natEntry.RegionID, natEntry.TenantID)
					eipMappingsForEdge, err := checkEgressIPsForEdgeNATEgress(dbCtx, natEntry.RegionID, natEntry.TenantID, natEntry.ComputeRegionID, natEntry.EgressIPList)
					eLog.LogInfo("mu_cnat_scale PubSubHandler: Fetched existing IP mappings for NAT ID %s in region %d tenant %d. Existing Mappings: %v", natEntry.InstanceIDStr, natEntry.RegionID, natEntry.TenantID, eipMappingsForEdge)
					if err != nil {
						eLog.LogError("mu_cnat_scale PubSubHandler: Failed to fetch existing IP mappings for NAT ID %s in region %d tenant %d: %v", natEntry.InstanceIDStr, natEntry.RegionID, natEntry.TenantID, err)
						return ascaleapi.ErrorMessage(alertMsg, err, ascaleapi.AlertCloudNATScaleUpDBError)
					}
					if len(eipMappingsForEdge) >= 1 {
						eipMappingsEdge[natEntry.RegionIDStr] = eipMappingsForEdge
					}
					break
				} else {
					eLog.LogInfo("mu_cnat_scale PubSubHandler: Processing Compute NAT ID %s in region %d tenant %d", natEntry.InstanceIDStr, natEntry.RegionID, natEntry.TenantID)
					eipMappings, configuredEipMappings, err = checkEgressIPsForComputeNATEgress(dbCtx, natEntry.RegionID, natEntry.TenantID, natEntry.ComputeRegionID)
					eLog.LogInfo("mu_cnat_scale PubSubHandler: Fetched existing IP mappings for NAT ID %s in region %d tenant %d. Configured Mappings: %v, Existing Mappings: %v", natEntry.InstanceIDStr, natEntry.RegionID, natEntry.TenantID, configuredEipMappings, eipMappings)
					if err != nil {
						eLog.LogError("mu_cnat_scale PubSubHandler: Failed to fetch existing IP mappings for NAT ID %s in region %d tenant %d: %v", natEntry.InstanceIDStr, natEntry.RegionID, natEntry.TenantID, err)
						return ascaleapi.ErrorMessage(alertMsg, err, ascaleapi.AlertCloudNATScaleUpDBError)
					}
					break
				}
			}
		}
		eLog.LogInfo("mu_cnat_scale PubSubHandler: Compute Region NAT IP mappings: %v", eipMappings)
		eLog.LogInfo("mu_cnat_scale PubSubHandler: Edge Region NAT IP mappings: %v", eipMappingsEdge)

		// Step 5: Trigger scale out event
		// Skip scale out for 2 conditions 1) There is atleast 1 healthy NAT egress alias IP available 2) Ongoing scale out is in progress
		for region, natInfo := range natInfoMap {
			for _, natEntry := range natInfo {
				if !natEntry.IsEdge {
					eLog.LogInfo("mu_cnat_scale PubSubHandler: Handling scale out for compute NAT in region %d with mappings %v for NAT info %v", region, eipMappings, natInfo)
					if len(eipMappings) >= 1 {
						eLog.LogInfo("mu_cnat_scale PubSubHandler: Skip Cloud NAT scale out for compute NAT ID %s in region %d tenant %d as there is atleast 1 healthy compute region NAT gateway", natEntry.InstanceIDStr, region, natEntry.TenantID)
						break
					} else if isNATScaleOutInProgress(dbCtx, natEntry.TenantID, natEntry.RegionIDStr) {
						eLog.LogInfo("mu_cnat_scale PubSubHandler: Skipping Cloud NAT scale out for compute NAT ID %s in region %d tenant %d as a scale out is already in progress", natEntry.InstanceIDStr, region, natEntry.TenantID)
						break
					} else {
						eLog.LogInfo("mu_cnat_scale PubSubHandler: Triggering Cloud NAT scale out for compute NAT ID %s in region %d tenant %d", natEntry.InstanceIDStr, region, natEntry.TenantID)
						err = ascaleapi.HandleCNATScaleUp(dbCtx, msg, cust, natEntry.InstanceOCID, natEntry.InstanceID, natEntry.TenantID, region)
						if err != nil {
							eLog.LogError("mu_cnat_scale PubSubHandler: Failed to trigger Cloud NAT scale out for compute NAT ID %s in region %d tenant %d: %v", natEntry.InstanceIDStr, region, natEntry.TenantID, err)
							return ascaleapi.ErrorMessage(alertMsg, err, ascaleapi.AlertCloudNATScaleUpFailed)
						} else {
							eLog.LogInfo("mu_cnat_scale PubSubHandler: Successful Cloud NAT scale out for compute NAT ID %s in region %d tenant %d: %v", natEntry.InstanceIDStr, region, natEntry.TenantID, err)
							return ascaleapi.SuccessMessage(alertMsg)
						}
					}
				} else {
					eLog.LogInfo("mu_cnat_scale PubSubHandler: Handling scale out for edge NAT in region %d with mappings %v for NAT info %v", region, eipMappingsEdge, natInfo)
					_, ok := eipMappingsEdge[strconv.FormatInt(region, 10)]
					if ok {
						eLog.LogInfo("mu_cnat_scale PubSubHandler: Skip Cloud NAT scale out for edge NAT ID %s in region %d tenant %d as there is atleast 1 healthy edge region NAT gateway", natEntry.InstanceIDStr, region, natEntry.TenantID)
						break
					} else if isNATScaleOutInProgress(dbCtx, natEntry.TenantID, natEntry.RegionIDStr) {
						eLog.LogInfo("mu_cnat_scale PubSubHandler: Skipping Cloud NAT scale out for edge NAT ID %s in region %d tenant %d as a scale out is already in progress", natEntry.InstanceIDStr, region, natEntry.TenantID)
						break
					} else {
						eLog.LogInfo("mu_cnat_scale PubSubHandler: Triggering Cloud NAT scale out for edge NAT ID %s in region %d tenant %d", natEntry.InstanceIDStr, region, natEntry.TenantID)
						err = ascaleapi.HandleCNATScaleUp(dbCtx, msg, cust, natEntry.InstanceOCID, natEntry.InstanceID, natEntry.TenantID, region)
						if err != nil {
							eLog.LogError("mu_cnat_scale PubSubHandler: Failed to trigger Cloud NAT scale out for compute NAT ID %s in region %d tenant %d: %v", natEntry.InstanceIDStr, region, natEntry.TenantID, err)
							return ascaleapi.ErrorMessage(alertMsg, err, ascaleapi.AlertCloudNATScaleUpFailed)
						} else {
							eLog.LogInfo("mu_cnat_scale PubSubHandler: Successful Cloud NAT scale out for compute NAT ID %s in region %d tenant %d: %v", natEntry.InstanceIDStr, region, natEntry.TenantID, err)
							return ascaleapi.SuccessMessage(alertMsg)
						}
					}
				}
			}
		}
	}
	return emptyResp
}
