#!/usr/bin/python3
import logging
import logging.handlers
import threading
from libs.comm.sqsapi import SqsApi
import libs.pac_utils.utils as pac_utils
from libs.common.utils import signal_number_to_name
from libs.common.shared.orch_utils import OrchState
from orchestration_service.core.orchestrator import OrchstrationHandler
from orchestration_service.core.onboarding import OnboardingHandler
from orchestration_service.core.instance import InstanceHandler
from libs.cfg import *
import sys
import signal
import time
import traceback
notification = None
running = True
import subprocess
import boto3
import requests
from base64 import b64decode
import libs.db.dbhand<PERSON> as DB
import os
import json

INIT_FEATURE_FLAGS= '/utils/feature_flag_declarations/initialize_flags.py'

if not os.path.isfile(INIT_FEATURE_FLAGS):
    # HACK: Needed for VM mode, for which support will be deprecated
    INIT_FEATURE_FLAGS= os.getcwd() + '/utils/feature_flag_declarations/initialize_flags.py'

class ThreadHandle():
    handle = None 
    t_handle = None
    def __init__(self):
        pass

class NotificationModel():
    def __init__(self):
        self.sqs = None
        
        self.inst_q = ThreadHandle()  # Instance replace Q
        self.oc_q = ThreadHandle()    # Orchestration Q
        self.cp_q = ThreadHandle()    # Onboarding Q
        
        self.cp_q.handle = OnboardingHandler(cfg)
        self.inst_q.handle = InstanceHandler(cfg)
        self.oc_q.handle = OrchstrationHandler(cfg)
        
        self.cp_q.t_handle = threading.Thread(name="OnboardingHandler",
                        target=thread_handler, args = (self.cp_q.handle,), daemon=True)
        self.oc_q.t_handle = threading.Thread(name="OrchstrationHandler",
                        target=thread_handler, args = (self.oc_q.handle,), daemon=True)
        self.inst_q.t_handle = threading.Thread(name="InstanceHandler",
                        target=thread_handler, args = (self.inst_q.handle,), daemon=True)

        self.threads = {
            'cp_q_thread': self.cp_q.t_handle,
            'oc_q_thread': self.oc_q.t_handle,
            'inst_q_thread': self.inst_q.t_handle,
        }

    def start_threads(self):
        self.cp_q.t_handle.start()
        self.oc_q.t_handle.start()
        self.inst_q.t_handle.start()
    
    def stop_threads(self):
        self.cp_q.handle.running = False
        self.oc_q.handle.running = False
        self.inst_q.handle.running = False

def thread_handler(thread_h):
    try:
        print(("Starting sqs queue thread handler for %s " % str(thread_h.qname)))
        thread_h.init_db()
        thread_h.handle()
    except Exception as E:
        print("Fatal! Exiting all the threads and re-strating process due to exception %s, traceback: %s" %
              (str(E.args), str(traceback.format_exc())))
        sys.exit(-1)

def signal_handler(signal, frame):
    global notification
    global running 
    print(f"Received Signal {signum}: {signal_number_to_name(signum)}")
    for th in threading.enumerate():
        print(th)
        #traceback.print_stack(sys._current_frames()[th.ident])
        print()
    if notification:
        notification.stop_threads()
        notification = None
    running = False

def copy_orchcfg_to_db(logger):
    dbh = DB.DbHandle(logger)
    cursor = dbh.get_cursor(dbh)

    withheld_keys = [
        'dbpassword',
        'aws_access_key_id',
        'aws_secret_access_key'
    ]
    for key, value in list(cfg.items()):
        if key in withheld_keys:
            logger.info(f"Do not insert {key} in to orch_cfg table")
            continue

        if isinstance(value, (str, dict, bool, list)):
            logger.info("Inserting orch_cfg setting into DB: %s" % key)
            params = (key, str(value), str(value))
            sql = ("insert into orch_cfg (name, value) values (%s, %s) on duplicate key update value=%s")
            try:
                cursor.execute(sql, params)
            except Exception as ex:
                logger.error("orch_cfg:save(): Update query failed: %s" % (str(ex),))

    dbh.cursorclose(cursor)

def copy_cfg_to_db_orchcfg_v2(logger):
    cursor = None
    dbh = DB.DbHandle(logger)
    cursor = dbh.get_cursor(dbh)
    region = os.getenv("AWS_REGION")

    for key, value in list(cfg.items()):
        if key in ['dbpassword']:
            logger.info(f"Do not insert {key} in to orch_cfg table")
            continue

        if isinstance(value, (str, dict, bool, list)):
            logger.info(f"Inserting {value} for setting {key} and region {region} into orch_cfg_v2")
            params = (key, str(value), "regional", region, str(value), "regional", region)
            sql = ("insert into orch_cfg_v2 (name, value, scope, region) values (%s, %s, %s, %s) on duplicate key "
                   "update value=%s, scope=%s, region=%s")
            try:
                cursor.execute(sql, params)
            except Exception as ex:
                logger.error("orch_cfg_v2:save(): Update query failed: %s" % (str(ex),))
    if cursor:
        dbh.cursorclose(cursor)

def get_aws_region(logger):
    # Check if AWS_REGION environment variable is set
    if not os.environ.get('AWS_REGION'):
        try:
            # Try to fetch the region from EC2 metadata
            aws_region = requests.get('http://***************/latest/meta-data/placement/region',
                                      timeout=2).text.strip()

            # Set the AWS_REGION environment variable
            os.environ['AWS_REGION'] = aws_region
            logger.info(f"AWS_REGION is set to {aws_region}")
        except requests.RequestException:
            logger.error("Error: Unable to determine AWS region.")
            sys.exit(-1)
        except Exception as E:
            logger.error(f"Except: {E.args}")
            sys.exit(-1)
    else:
        logger.info(f"AWS_REGION is already set to {os.environ['AWS_REGION']}")

# Function to check if any thread is not alive
def any_thread_not_alive(threads):
    for thread_name, thread in threads.items():
        if not thread.is_alive():
            print(f"Thread {thread_name} is not alive!")
            return True
        else:
            print(f"Thread {thread_name} is alive!")
    return False

def setup_sqs_permission(logger):
    """
    Adds permissions to an SQS queue.
    """
    aws_partition = cfg.get("aws_partition") or "aws"
    if not aws_partition:
        aws_partition = "aws"
    acct_id = cfg.get("acct_id")
    region = cfg.get("region")
    queue_url = f"https://sqs.{region}.amazonaws.com/{acct_id}/orchestrator_ntfy_q"

    sqs = boto3.client('sqs', region_name=region)
    # Define the policy
    policy = {
            "Version": "2012-10-17",
            "Id": f"arn:{aws_partition}:sqs:{region}:{acct_id}:orchestrator_ntfy_q/SQSDefaultPolicy",
            "Statement": [
                {
                    "Sid": "Sid1581050882480",
                    "Effect": "Allow",
                    "Principal": {
                        "AWS": f"arn:{aws_partition}:iam::{acct_id}:root"
                        },
                    "Action": "SQS:*",
                    "Resource": f"arn:{aws_partition}:sqs:{region}:{acct_id}:orchestrator_ntfy_q"
                    },
                {
                    "Sid": "Sid1581051022565",
                    "Effect": "Allow",
                    "Principal": "*",
                    "Action": "SQS:SendMessage",
                    "Resource": f"arn:{aws_partition}:sqs:{region}:{acct_id}:orchestrator_ntfy_q",
                    "Condition": {
                        "ArnEquals": {
                            "aws:SourceArn": f"arn:{aws_partition}:sns:{region}:{acct_id}:*"
                            }
                        }
                    }
                ]
            }

    # Convert the policy to a JSON string
    policy_json = json.dumps(policy)

    # Set the queue attributes
    try:
        response = sqs.set_queue_attributes(
                QueueUrl=queue_url,
                Attributes={
                    'Policy': policy_json
                    }
                )
        logger.info(f"Set SQS attribute. Response: {response}")
    except Exception as e:
        logger.error(f"Failed to set SQS attribute: {e}")


def main():
    global notification
    global running
    log_root_dir="/var/log/pan/"
    threads = {}
    try:
        if 'log-root-dir' in cfg:
            log_root_dir=cfg['log-root-dir']
        else:
            cfg['log-root-dir']=log_root_dir

        cmd = []
        cmd.append("mkdir")
        cmd.append("-p")
        cmd.append(log_root_dir)
        output = subprocess.check_output(cmd)
        print(("%s: %s" % (log_root_dir, str(output))))
    except Exception as ex:
        print(("Failed to start Orchestrator: %s" % str(ex)))
        return
    
    filename = log_root_dir+"/main.log"
    handler = logging.handlers.RotatingFileHandler(filename, maxBytes=10485760, backupCount=1)
    logger = logging.getLogger("main")
    logger.setLevel(logging.DEBUG)
    logger.addHandler(handler)
    logger.info("Starting....")
    logger.info (str(cfg))
    get_aws_region(logger)
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    orch_state = OrchState(logger)
    prev_orch_state = orch_state.NOT_INITIALIZED
    while (running):
        cur_orch_state = orch_state.get_orch_current_state()
        if prev_orch_state != cur_orch_state:
            logger.info("Orchestrator state changed from %s to %s" % (prev_orch_state, cur_orch_state))
            if cur_orch_state == orch_state.ACTIVE:
                try:
                    notification = NotificationModel()
                    threads = notification.threads
                    if cfg.get('dbpassword_decrypted', False) == False:
                        boto3.setup_default_session(region_name=cfg['region'])
                        ENCRYPTED = cfg['dbpassword']
                        password = boto3.client('kms', cfg['region']).decrypt(CiphertextBlob=b64decode(ENCRYPTED))['Plaintext']
                        cfg['dbpassword'] = password.decode("utf-8")
                        cfg['dbpassword_decrypted']=True
                except Exception as ex:
                    logger.error("Unable to decrypt database password: %s" % str(ex))
                    return
                # TODO: Move this to a single execution layer during upgrade.
                if int(os.environ["HOSTNAME"].split("orchestration-", 1)[1]) == 0:
                    # Only setup DB and feature flags if we are the first pod
                    copy_orchcfg_to_db(logger)
                    copy_cfg_to_db_orchcfg_v2(logger)
                    try:
                        logger.info("Initializing feature flags init")
                        out = subprocess.check_output(["/usr/bin/python3", INIT_FEATURE_FLAGS]).decode('ascii')
                        if 'Failed' in out or 'Exception' in out:
                            msg = "Failed to initialize geature flags. Err: %s" % str(out)
                            logger.error(msg)
                            raise Exception(msg)
                        else:
                            logger.info("Feature Falgs initialized: %s" % str(out))
                    except Exception as e:
                        msg = ("Exception while initializing feature flags. Err: %s" % str(e))
                        logger.error(msg)
                        raise Exception(msg)
                    dbh = DB.DbHandle(logger)
                    pac_utils.upload_sample_pac_file(dbh)
                    setup_sqs_permission(logger)
                notification.start_threads()
            else:
                if notification:
                    notification.stop_threads()
                    notification = None
        prev_orch_state = cur_orch_state
        logger.debug("Back in main(orchestration_service), current orch_state %s" % (cur_orch_state))

        # Periodically check if threads are alive
        if running == True and cur_orch_state == orch_state.ACTIVE and any_thread_not_alive(threads):
            print("At least one thread that should be running is not running. Exiting...")
            sys.exit(-1)

        time.sleep(5)

if __name__ == "__main__":
    main()
