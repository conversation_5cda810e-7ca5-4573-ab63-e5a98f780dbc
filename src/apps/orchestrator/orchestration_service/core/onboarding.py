import logging
import logging.handlers
import time
import threading
import base64
import json
from libs.msg_defs.handler import *
from libs.msg_defs.workflows import *
import libs.model.licensemodel as LS
import libs.model.custmodel as CS
import libs.model.custnodemodel as CSN
import libs.model.orchjobs as JOB
import libs.model.instancemodel as InstModel
from libs.common.utils import prepare_job_status_msg, get_capacity_type, get_default_license_sku_type
from libs.logging_infra.orch_context import orch_store_in_trace_db
from libs.common.shared.sys_utils import *
from libs.cfg import *
from libs.apis.commit_job_status_api_orch import save_orch_in_progress_state_to_commit_status_table
from libs.apis.commit_job_status_api_orch import save_orch_failed_state_to_commit_status_detail_table
from libs.common.shared.sys_utils import DeploymentType, HAModel
from libs.common.shared.grpc.proto.avisarpb.avisar_pb2_client import AvisarContext, \
    ONBOARDING_STATUS_SUCCESS, ONBOARDING_STATUS_FAILED, \
    AvisarService, METRIC_SEVERITY_INFO, METRIC_SEVERITY_CRITICAL

DEFAULT_NLB_VERSION = "DEFAULT_NLB_VERSION"

class OnboardingHandler(GenericHandler):
    status = "ONBOARDING"
    running = True

    def __init__(self, cfg):
        self.db_h = None
        self.qname = 'onboarding_q.fifo'
        # Init the logging queue
        super(OnboardingHandler, self).init_generic_logger_rastro(qname=self.status.lower())
        self.sqs = SqsApi(cfg['region'], cfg['queues'], self.logger)
        self.valid_msgs = (NtfyOnboardingMsg, OrchestrationEventsMsg)
        queues = [q for q in cfg['queues'] if q['name'] == self.qname]
        self.notification_hdlr = {
            'cust_topology': self.topology_handler,
            'license_master': self.license_handler,
            'cust_master': self.customer_handler,
            'instance_replace': self.instance_handler,
            'orchestration_event': self.orchestration_event_handler,
            'cust_epaas': self.cust_epaas_handler,
            'ep_cnat_scaleout': self.ep_cnat_scaleout_handler,
            'migrate_cluster_to_ipv6' : self.migrate_cluster_to_ipv6_handler,
            'egress_ip_scale': self.egress_ip_scale_handler,
            'colo_vlan_onboard': self.colo_vlan_onboard_handler,
            'delete_instance': self.delete_instance_handler,
            'tenant_ipv6_settings': self.tenant_ipv6_settings_handler,
            'priv_sase_node_update':  self.priv_sase_node_update_handler,
        }
        self.ids = {}
        # Init the failure queue.
        super(OnboardingHandler, self).init_failure_queue_rastro('onboarding_failq')

        # Init the Database.
        self.init_db()

        # Update the rastro trace DB table.
        if self.db_h:
            if self.update_trace_db() == False:
                raise Exception("Failed to update rastro trace DB. Cannot continue...")

        self.avctx = AvisarContext(logger=self.logger,
                                   service_endpoint=cfg.get('avisar_service_endpoint'),
                                   forwarding_enabled=cfg.get('avisar_forwarding_enabled', False),
                                   sender="OnboardingService",
                                   aws_env=cfg.get("aws_env"))

    def update_trace_db(self):
        ret = False
        try:
            # Set the DB handler in the respective rastro context:-
            self.rastro_ctx.set_db_handler(self.db_h)
            # Store info about this logger in the trace DB.
            lret = orch_store_in_trace_db(self.db_h,
                                          self.rastro_ctx.get_trace_id(),
                                          tenant_id=999999999,
                                          workflow_type="Orch OnboardingQ",
                                          workflow_id=int(time.time()))
            if lret == None:
                raise Exception("Failed to store info reg workflow type Orch OnboardingQ")
            ret = True
        except Exception as E:
            self.logger.error(
                "Failed to update trace id in the master database. %s %s" % (str(E.args), str(traceback.format_exc())))
        finally:
            return ret

    def instance_handler(self, job, operation, row_id, result, panorama_job_id=None, cloud_provider=None,
                         compute_region_id=None, node_type=None):
        self.logger.info("OnboardingHandler:instance_handler(): "
                          "Got job id %d operation %s row %d" % (job.jobid, operation,
                                                                 row_id))
        # Insert operation
        if operation == "update":
            workflow = "INSTANCE_REPLACE"
            job.status_msg = prepare_job_status_msg("Initiating workflow %s." % (workflow))
            job.job_type = workflow
            job.save_job(self.db_h)
            orchdr = OrchHeader(workflow, job.jobid)
            instmsg = InstanceReplaceMsg(orchdr, row_id, self.logger.get_updated_log_tag(self.rastro_log_tag))
            obj = self.to_json(instmsg)
            result.append(obj)
            return True
        else:
            self.error = "Invalid operation %s" % str(operation)
            return False

    def cust_epaas_handler(self, job, operation, row_id, result, panorama_job_id=None,
                           cloud_provider=None, compute_region_id=None, node_type=None):
        self.logger.info("OnboardingHandler:cust_epaas_handler(): "
                          "Got job id %d operation %s row %d" % (job.jobid, operation,
                                                                 row_id))
        # Insert operation
        if operation == "update_epaas":
            workflow = "UPGRADE_ENVOY_VERSION"
            job.status_msg = prepare_job_status_msg("Initiating workflow %s." % (workflow))
            job.job_type = workflow
            job.save_job(self.db_h)
            orchhdr = OrchHeader(workflow, job.jobid)
            orchmsg = OrchestratorMsg(orchhdr, row_id, None,
                                      log_tag=self.logger.get_updated_log_tag(self.rastro_log_tag),
                                      cloud_provider=cloud_provider, compute_region_id=compute_region_id, node_type=node_type)
            obj = self.to_json(orchmsg)
            result.append(obj)
            return True
        if operation == "upgrade_ep_outside_panos":
            workflow = "UPGRADE_EP_OUTSIDE_PANOS"
            job.status_msg = prepare_job_status_msg("Initiating workflow %s." % (workflow))
            job.job_type = workflow
            job.save_job(self.db_h)
            orchhdr = OrchHeader(workflow, job.jobid)
            orchmsg = OrchestratorMsg(orchhdr, row_id, None,
                                      log_tag=self.logger.get_updated_log_tag(self.rastro_log_tag),
                                      cloud_provider=cloud_provider, compute_region_id=compute_region_id, node_type=node_type)
            obj = self.to_json(orchmsg)
            result.append(obj)
            return True
        else:
            self.error = "Invalid operation %s" % str(operation)
            return False

    def ep_cnat_scaleout_handler(self, job, operation, row_id, result, panorama_job_id=None,
                                 cloud_provider=None, compute_region_id=None, node_type=None):
        self.logger.info("OnboardingHandler:cust_epaas_handler(): "
                         "Got job id %d operation %s row %d" % (job.jobid, operation,
                                                                row_id))
        # Insert operation
        if operation == "ep_cnat_scaleout":
            workflow = "EP_CNAT_SCALEOUT"
            job.status_msg = prepare_job_status_msg("Initiating workflow %s." % (workflow))
            job.job_type = workflow
            job.save_job(self.db_h)
            orchhdr = OrchHeader(workflow, job.jobid)
            orchmsg = OrchestratorMsg(orchhdr, row_id, None,
                                      log_tag=self.logger.get_updated_log_tag(self.rastro_log_tag),
                                      compute_region_id=compute_region_id)
            obj = self.to_json(orchmsg)
            result.append(obj)
            return True
        else:
            self.error = "Invalid operation %s" % str(operation)
            return False

    def license_handler(self, job, operation, row_id, result, panorama_job_id=None,
                        cloud_provider=None, compute_region_id=None, node_type=None):
        self.logger.debug("LinceseHandler:license_handler(): "
                          "Got job id %d operation %s row %d" % (job.jobid, operation,
                                                                 row_id))
        # Insert operation
        if operation == "insert":
            workflow = "LICENSE_ADD"
            licensemodel = LS.LicenseModel(id=row_id, dbh=self.db_h)
        return True

    def customer_handler(self, job, operation, row_id, result, panorama_job_id=None,
                         cloud_provider=None, compute_region_id=None, node_type=None):
        self.logger.debug("OnboardingHandler:customer_handler(): "
                          "Got job id %d operation %s row %s" % (job.jobid, operation,
                                                                 row_id))
        workflow = None
        if operation == "insert":
            workflow = workflow_names[4]
            job.status_msg = prepare_job_status_msg("Initiating workflow %s." % (workflow))
            job.job_type = workflow
            job.save_job(self.db_h)
        elif operation == "update":
            workflow = workflow_names[5]
            job.status_msg = prepare_job_status_msg("Initiating workflow %s." % (workflow))
            job.job_type = workflow
            job.save_job(self.db_h)
        elif operation == "contentupdate":
            # Create timer zones
            customer = CS.CustomerModel(custid=row_id, dbh=self.db_h)
            job.job_type = "CONTENT_UPGRADE"
            job.save_job(self.db_h)
            if not customer.get_param("id"):
                job.status_msg = prepare_job_status_msg("Error: content update: Customer id %d not "
                                                        "found in database")
                self.error = job.status_msg
                self.logger.error(job.status_msg)
                job.save_job(self.db_h)
                return False
            self.db_h.error = ""
            ret = customer.schedule_content_update(job, self.db_h, self)
            if not ret:
                job.status_msg = prepare_job_status_msg("Unable to schedule content update for customer"
                                                        " %s\n" % customer.get_param("name"))
                self.logger.error(job.status_msg)
                self.error = job.status_msg + self.db_h.error
                self.db_h.error = ""
                job.save_job(self.db_h)
                return False
            return True

        hdr = OrchHeader(workflow, job.jobid)
        custmsg = CustomerChangeMsg(hdr, row_id, self.logger.get_updated_log_tag(self.rastro_log_tag))
        obj = self.to_json(custmsg)
        result.append(obj)
        return True

    def topology_handler(self, job, operation, row_id, result, panorama_job_id=None,
                         cloud_provider=None, compute_region_id=None, node_type=None):
        res = False
        tenant_id = 0
        custnode_id = row_id
        skip_version_check = False
        node_version = None

        try:
            self.logger.info("OnboardingHandler:topology_handler(): "
                              "Got job id %d operation %s row %d" % (job.jobid, operation, row_id))
            # Insert operation
            if operation == "insert":
                workflow = workflow_names[1]
                nodemodel = CSN.CustNodeModel(row_id, self.db_h)
                if not nodemodel.id:
                    self.error = ("Onboarding:process_msg():Unable to find node id"
                                  " = %d in the database\n" % (row_id,))
                    self.logger.error("Onboarding:process_msg():Unable to find node id"
                                      " = %d in the database\n" % (row_id,))
                    raise Exception(self.error)
                custid = nodemodel.custid
                nodemodel.set_avisar_ctx(self.db_h, self.avctx)
                ret = self.db_h.get_type_name_from_id(nodemodel.node_type)
                if not ret.ok:
                    self.error = ("Onboarding:process_msg():Unable to get Node typename"
                                  "from id %d" % (nodemodel.node_type,))
                    self.logger.error("Onboarding:process_msg():Unable to get Node typename"
                                      "from id %d" % (nodemodel.node_type,))
                    raise Exception(self.error)
                node_type = ret.result[0]
                custmodel = CS.CustomerModel(custid=custid, dbh=self.db_h)
                if not custmodel.get_param("id"):
                    self.error = ("Onboarding:process_msg(): Unable to find"
                                  " customer with id %s" % (custid,))
                    self.logger.error("Onboarding:process_msg(): Unable to find"
                                      " customer with id %s" % (custid,))
                    raise Exception(self.error)
                tenant_id = custmodel.get_param("acct_id")
                cfg_name = ("%s_%s" % (custmodel.get_param("name"), nodemodel.id))

                # Skip version check for SASE Private region node type.
                if node_type in [NODE_TYPE_SASE_PRIV_REGION_LB_NAME, NODE_TYPE_SASE_PRIV_REGION_AGENT_NAME]:
                    self.logger.info(f"Skipping version check for node_type {node_type}")
                    skip_version_check = True

                if not skip_version_check:
                    if node_type == NODE_TYPE_NAT_INSTANCE_NAME:
                        my_version = custmodel.get_sw_version_for_nat_instance(nodemodel.region)
                    else:
                        my_version = custmodel.get_param("version")

                    if node_type in [NODE_TYPE_NLB_INSTANCE_NAME, NODE_TYPE_SERVICE_ILB_NAME]:
                        self.logger.info("NLB Node does not need version info; "
                                         "Using default version value: %s" % DEFAULT_NLB_VERSION)
                        node_version = DEFAULT_NLB_VERSION
                    else:
                        ret = self.db_h.get_nodetype_sw_version_from_cust_version(
                            node_type, my_version)
                        if not ret.ok:
                            self.error = ("Unable to get software version for "
                                          "node type  %s and service version %s"
                                        % (node_type, custmodel.get_param("version")))
                            self.logger.error("Unable to get software version "
                                              "for node type %s and service "
                                              "version %s" % (node_type, custmodel.get_param("version")))
                            raise Exception(self.error)
                        node_version = ret.result[0]
                node = [VmNodes(str(nodemodel.id), node_type, nodemodel.region,
                                node_version, logger=self.logger)]
                orchhdr = OrchHeader(workflow, job.jobid)
                orchmsg = OrchestratorMsg(orchhdr, custid, node, panorama_job_id,
                                          self.logger.get_updated_log_tag(self.rastro_log_tag),
                                          node_type=nodemodel.node_type)
                obj = self.to_json(orchmsg)
                result.append(obj)
                job.status_msg = prepare_job_status_msg("Initiating workflow %s." % (str(workflow)))
                job.job_type = workflow
                job.save_job(self.db_h)
            elif operation == "update":
                workflow = 'TOPOLOGY_DELETE'
                job.job_type = workflow
                job.save_job(self.db_h)
                nodemodel = CSN.CustNodeModel(row_id, self.db_h)
                if not nodemodel.id:
                    self.error = ("Onboarding:process_msg():Unable to find node id"
                                  " = %d in the database" % (int(row_id),))
                    self.logger.error("Onboarding:process_msg():Unable to find node id"
                                      " = %d in the database" % (int(row_id),))
                    raise Exception(self.error)
                custid = nodemodel.custid
                nodemodel.set_avisar_ctx(self.db_h, self.avctx)
                custmodel = CS.CustomerModel(custid=custid, dbh=self.db_h)
                if not custmodel.get_param("id"):
                    self.error = ("Onboarding:process_msg(): Unable to find"
                                  " customer with id %s" % (custid,))
                    self.logger.error("Onboarding:process_msg(): Unable to find"
                                      " customer with id %s" % (custid,))
                    raise Exception(self.error)
                tenant_id = custmodel.get_param("acct_id")
                orchhdr = OrchHeader(workflow, job.jobid)
                orchmsg = OrchestratorMsg(orchhdr, custid, [row_id], panorama_job_id,
                                          self.logger.get_updated_log_tag(self.rastro_log_tag),
                                          node_type=nodemodel.node_type)
                obj = self.to_json(orchmsg)
                result.append(obj)
                job.status_msg = prepare_job_status_msg("Initiating workflow %s." % (str(workflow)))
                job.save_job(self.db_h)
            elif operation == "delete":
                job.job_type = 'DO_NOTHING'
                job.save_job(self.db_h)
            elif operation == "cust_topology_update":
                workflow = 'TOPOLOGY_UPDATE'
                job.job_type = workflow
                job.save_job(self.db_h)
                nodemodel = CSN.CustNodeModel(row_id, self.db_h)
                if not nodemodel.id:
                    self.error = ("Onboarding:process_msg():Unable to find node id"
                                  " = %d in the database" % (int(row_id),))
                    self.logger.error("Onboarding:process_msg():Unable to find node id"
                                      " = %d in the database" % (int(row_id),))
                    raise Exception(self.error)
                custid = nodemodel.custid
                nodemodel.set_avisar_ctx(self.db_h, self.avctx)
                ret = self.db_h.get_type_name_from_id(nodemodel.node_type)
                if not ret.ok:
                    self.error = ("Onboarding:process_msg():Unable to get Node typename"
                                  "from id %d" % (nodemodel.node_type,))
                    self.logger.error("Onboarding:process_msg():Unable to get Node typename"
                                      "from id %d" % (nodemodel.node_type,))
                    raise Exception(self.error)
                node_type = ret.result[0]
                custmodel = CS.CustomerModel(custid=custid, dbh=self.db_h)
                if not custmodel.get_param("id"):
                    self.error = ("Onboarding:process_msg(): Unable to find"
                                  " customer with id %s" % (custid,))
                    self.logger.error("Onboarding:process_msg(): Unable to find"
                                      " customer with id %s" % (custid,))
                    raise Exception(self.error)
                tenant_id = custmodel.get_param("acct_id")
                cfg_name = ("%s_%s" % (custmodel.get_param("name"), nodemodel.id))

                # Skip version check for SASE Private region node type.
                if node_type in [NODE_TYPE_SASE_PRIV_REGION_LB_NAME, NODE_TYPE_SASE_PRIV_REGION_AGENT_NAME, NODE_TYPE_SERVICE_ILB_NAME]:
                    self.logger.info(f"Skipping version check for node_type {node_type}")
                    skip_version_check = True

                if not skip_version_check:
                    if node_type == NODE_TYPE_NAT_INSTANCE_NAME:
                        my_version = custmodel.get_sw_version_for_nat_instance(nodemodel.region)
                    else:
                        my_version = custmodel.get_param("version")

                    ret = self.db_h.get_nodetype_sw_version_from_cust_version(node_type, my_version)
                    if not ret.ok:
                        self.error = ("Unable to get software version for node type"
                                        " %s and service version %s" % (
                                        vmtypes['firewall'], custmodel.get_param("version")))
                        self.logger.error("Unable to get software version for node type"
                                          " %s and service version %s" % (
                            vmtypes['firewall'], custmodel.get_param("version")))
                        raise Exception(self.error)
                    node_version = ret.result[0]

                node = [VmNodes(str(nodemodel.id), node_type, nodemodel.region,
                                node_version, logger=self.logger)]

                orchhdr = OrchHeader(workflow, job.jobid)
                orchmsg = OrchestratorMsg(orchhdr, custid, node, panorama_job_id,
                                          self.logger.get_updated_log_tag(self.rastro_log_tag),
                                          node_type=nodemodel.node_type)
                obj = self.to_json(orchmsg)
                result.append(obj)
                job.status_msg = prepare_job_status_msg("Initiating workflow %s." % (str(workflow)))
                job.save_job(self.db_h)
            else:
                self.error = ("OnboardingHandler:topology_handler: Got invalid"
                              " operation %s" % (operation,))
                self.logger.error("OnboardingHandler:topology_handler: Got invalid"
                                  " operation %s" % (operation,))
                raise Exception(self.error)
            res = True

        except Exception as E:
            self.err_msg = "Onboarding Failed with Exception: %s" % str(E.args)
            # Now update the failed status.
            if custnode_id and tenant_id and panorama_job_id:
                lret = save_orch_failed_state_to_commit_status_detail_table(self.db_h,
                                                                            tenant_id,
                                                                            panorama_job_id,
                                                                            custnode_id)
                if lret != True:
                    self.logger.error("Failed to update the commit job status table for tenant id %s and cust node "
                                      "id %s, Panorama job id %s"
                                      % (str(tenant_id), str(custnode_id), str(panorama_job_id)))
            res = False
            self.logger.error("Failed with Exception %s, Traceback: %s" % (str(E.args), str(traceback.format_exc())))

        finally:
            if (res == True) and (custnode_id and tenant_id and panorama_job_id):
                lret = save_orch_in_progress_state_to_commit_status_table(self.db_h,
                                                                          tenant_id,
                                                                          panorama_job_id,
                                                                          custnode_id)
                if lret != True:
                    self.err_msg = ("Failed to update the commit job status table for tenant id %s and cust node "
                                    "id %s, Panorama job id %s"
                                    % (str(tenant_id), str(custnode_id), str(panorama_job_id)))
                    self.logger.error(self.err_msg)

            self.avctx.set_ctx(panorama_job_id=panorama_job_id, tenant_id=tenant_id,
                               trace_id=self.logger.get_enhanced_traceid() if not None else self.logger.uuid)
            if res == True:
                self.avctx.set_ctx(metric_type=ONBOARDING_STATUS_SUCCESS,
                                   metric_state="Onboarding Successful",
                                   metric_severity=METRIC_SEVERITY_INFO)
            else:
                self.avctx.set_ctx(metric_type=ONBOARDING_STATUS_FAILED,
                                   metric_state="Onboarding Failed with Exception: " + str(self.err_msg),
                                   metric_severity=METRIC_SEVERITY_CRITICAL)

            resp = self.avctx.publish_event()
            self.logger.info("Avisar Service publish event response: %s" % str(resp))

            return res

    def get_deployment_nodes(self, msg, custmodel):
        '''
        Generate the DeploymentNodes based on customer config and SKU overrides
        Input:
            msg: OrchestratorEventMsg
            custmodel: CustomerModel
        Returns
            A list of DeploymentNode entries
        '''
        ret = list()
        # TODO: If MP-DP split is enabled, we need to have 2 different deployment templates.
        # One for the MP, and the other for the DP (assuming all DPs are the same size)
        # Maybe this should be a list of DeploymentNodes? In the orchestrator, we can simply
        # walk through all DeploymentNodes and allocate an instance accordingly.
        # Is MP-DP supported without HA?
        # FIXME: Remove hardcoding here.
        ha_model = msg.event.get('metadata', {}).get('ha_model', HAModel.ACTIVE_PASSIVE)
        node_type_id = 48
        sku = get_default_license_sku_type(node_type_id)
        region_id = msg.event.get('region_id')
        market_type, gpcs_instance_size, dp_gpcs_instance_size, \
                capacity_type, dpdk_qcount, ha_mode = get_capacity_type(self.db_h,
                                                                        custmodel.get_param("id"),
                                                                        region_id, node_type_id,
                                                                        sku,
                                                                        version=custmodel.get_param("version"))
        self.logger.info("Got gpcs_instance_size({}), dp_gpcs_instance_size({}), cap_type({}), ha({})".format(
            gpcs_instance_size, dp_gpcs_instance_size, capacity_type, ha_mode))
        deployment_node = DeploymentNode(
                node_type="FIREWALL",
                machine_type="24",
                version="PA-VM-SaaS-10.0.5-c52.saas",
                capacity_type="PA-CAP550",
                cloud_machine_type="e2-standard-4",
                pa_instance_size="gpcs-2xlarge-m5",
                market_type=market_type,
                mp_dp_split=False,
                deployment_type=DeploymentType.INSTANCE_TYPE_LEGACY,
                ha_type=ha_model)
        # If MP-DP Split, append multiple DeploymentNodes. Each node corresponds to either an
        # MP or DP. We can take a call here on a bunch of params that dictate our orchestration
        ret.append(deployment_node)
        return ret

    def orchestration_event_handler(self, job, msg, result):
        '''
        Receive the event in the Onboarding queue.
        Identify the `cust_master.id` for the passed event. Also identify if the event
        needs to be rewritten for an MP-DP split deployment based on the override
        '''
        ret = False
        try:
            custmodel = CS.CustomerModel(acct_id=msg.tenant_id, dbh=self.db_h)
            if not custmodel.get_param("id"):
                err_msg = ("Onboarding:orchestration_event_handler(): Unable to find"
                    " customer with tenant_id %s" % (msg.tenant_id,))
                self.logger.error(err_msg)
                raise Exception(err_msg)
            msg.custid = custmodel.get_param("id")
            deployment_node_list = list()
            if msg.event_type == "INSTANCE_CREATE":
                deployment_node_list = self.get_deployment_nodes(msg, custmodel)
            msg.deployment_nodes.extend(deployment_node_list)
            # Bubble up the event type
            job.job_type = msg.event_type
            job.save_job(self.db_h)
            self.logger.info("Adding event of type %s to the orchestration_q" % job.job_type)
            new_orch_hdr = OrchHeader(msg.event_type, job.jobid)
            msg.hdr = new_orch_hdr
            result.append(self.to_json(msg))
            ret = True
        except Exception as E:
            # TODO: Save failure?
            self.logger.error("Onboarding:orchestration_event_handler() Exception : %s" % E)

        return ret

    def egress_ip_scale_handler(self, job, operation, result, egress_ip_scale_event_msg):
        self.logger.info("OnboardingHandler:egress_ip_scale_handler(): "
                         "Got job id %d operation %s row %s" % (job.jobid, operation,
                                                                str(egress_ip_scale_event_msg)))
        # Insert operation
        if operation == "egress_ip_scale_event":
            workflow = "EGRESS_IP_SCALE_EVENT"
            job.status_msg = prepare_job_status_msg("Initiating workflow %s." % str(workflow))
            job.job_type = workflow
            job.save_job(self.db_h)

            egress_ip_scale_event_msg_instance = EgressIPScaleEventMsg()
            egress_ip_scale_event_msg_instance.set_EgressIPScaleEvent_metadata \
                (instance_id=egress_ip_scale_event_msg.instance_id,
                 region_id=egress_ip_scale_event_msg.region_id,
                 nr_ip_addresses=egress_ip_scale_event_msg.nr_ip_addresses)
            orchhdr = OrchHeader(workflow, job.jobid)
            orchmsg = OrchestratorMsg(orchhdr, 0, None,
                                      log_tag=self.logger.get_updated_log_tag(self.rastro_log_tag))
            orchmsg.EgressIPScaleEventMsg = egress_ip_scale_event_msg_instance

            obj = self.to_json(orchmsg)
            result.append(obj)
            return True
        else:
            self.error = "Invalid operation %s" % str(operation)
            job.status_msg = self.error
            job.job_type = "UNDECIDED"
            job.save_job(self.db_h)
            self.logger.error("Failed to process operation %s" % str(operation))
            return False

    def get_custid_from_cluster_id(self, cluster_id):
        custid=0
        try:
            im = InstModel.InstanceModel(iid=cluster_id, dbh=self.db_h)
            if im.get_param("id") == None:
                raise Exception("Failed to get the instance id %s from the database" % str(cluster_id))
            custid = im.get_param("custid")
        except Exception as E:
            self.logger.error("Failed with exception %s" % str(E.args))
            custid=0
        finally:
            return custid

    def migrate_cluster_to_ipv6_handler(self, job, operation, result, migrate_cluster_to_ipv6_event_msg):
        self.logger.info("OnboardingHandler: migrate_cluster_to_ipv6_handler(): "
                         "Got job id %d operation %s row %s" % (job.jobid, operation,
                                                                str(migrate_cluster_to_ipv6_event_msg)))
        # Insert operation
        if operation == "migrate_cluster_to_ipv6":
            workflow = "MIGRATE_CLUSTER_TO_IPV6_EVENT"
            job.status_msg = prepare_job_status_msg("Initiating workflow %s." % str(workflow))
            job.job_type = workflow
            job.save_job(self.db_h)

            ipv6_migration_event_msg_instance = MigrateClusterToIPV6EventMsg()
            ipv6_migration_event_msg_instance.set_MigrateClusterToIPV6_metadata(
                cluster_id=migrate_cluster_to_ipv6_event_msg.cluster_id)

            # Get the custid from the cluster ID.
            custid = self.get_custid_from_cluster_id(cluster_id=migrate_cluster_to_ipv6_event_msg.cluster_id)
            if custid == 0:
                self.logger.error("Failed to get the custid from the cluster id %s" %
                                       str(migrate_cluster_to_ipv6_event_msg.cluster_id))
                return False
            orchhdr = OrchHeader(workflow, job.jobid)
            orchmsg = OrchestratorMsg(orchhdr, custid, None,
                                      log_tag=self.logger.get_updated_log_tag(self.rastro_log_tag))
            orchmsg.MigrateClusterToIPV6EventMsg = ipv6_migration_event_msg_instance

            obj = self.to_json(orchmsg)
            result.append(obj)
            return True
        else:
            self.error = "Invalid operation %s" % str(operation)
            job.status_msg = self.error
            job.job_type = "UNDECIDED"
            job.save_job(self.db_h)
            self.logger.error("Failed to process operation %s" % str(operation))
            return False

    def colo_vlan_onboard_handler(self, job, operation, result, colo_vlan_onboard_event_msg):
        self.logger.info("OnboardingHandler:colo_vlan_onboard_handler(): "
                         "Got job id %d operation %s row %s" % (job.jobid, operation,
                                                                str(colo_vlan_onboard_event_msg)))
        # Insert operation
        if operation == "colo_vlan_onboard_event":
            workflow = "COLO_VLAN_ONBOARD_EVENT"
            job.status_msg = prepare_job_status_msg("Initiating workflow %s." % str(workflow))
            job.job_type = workflow
            job.save_job(self.db_h)

            colo_vlan_onb_event_msg_instance = ColoVlanOnboardEventMsg()
            colo_vlan_onb_event_msg_instance.set_ColoVlanOnboardingEvent_metadata \
                (colo_vlan_onb_id=colo_vlan_onboard_event_msg.colo_vlan_onb_id,
                 region_id=colo_vlan_onboard_event_msg.region_id,
                 custid=colo_vlan_onboard_event_msg.custid,
                 update_bgp_peer=colo_vlan_onboard_event_msg.update_bgp_peer,
                 create_vlan_att= colo_vlan_onboard_event_msg.create_vlan_att)
            orchhdr = OrchHeader(workflow, job.jobid)
            orchmsg = OrchestratorMsg(orchhdr, colo_vlan_onboard_event_msg.custid, None,
                                      log_tag=self.logger.get_updated_log_tag(self.rastro_log_tag))
            orchmsg.ColoVlanOnboardEventMsg = colo_vlan_onb_event_msg_instance

            obj = self.to_json(orchmsg)
            result.append(obj)
            return True
        else:
            self.error = "Invalid operation %s" % str(operation)
            job.status_msg = self.error
            job.job_type = "UNDECIDED"
            job.save_job(self.db_h)
            self.logger.error("Failed to process operation %s" % str(operation))
            return False

    def delete_instance_handler(self, job, operation, result, delete_instance_event_msg):
        self.logger.info("OnboardingHandler:delete_instance_handler(): "
                         "Got job id %d operation %s row %s" % (job.jobid, operation,
                                                                str(delete_instance_event_msg)))
        # Insert operation
        if operation == "delete_instance_event":
            workflow = "DELETE_INSTANCE_EVENT"
            job.status_msg = prepare_job_status_msg("Initiating workflow %s." % str(workflow))
            job.job_type = workflow
            job.save_job(self.db_h)

            delete_instance_event_msg_instance = DeleteInstanceEventMsg()
            delete_instance_event_msg_instance.set_DeleteInstanceEventMsg_metadata(
                instance_id=delete_instance_event_msg.instance_id,
                delete_cluster=delete_instance_event_msg.delete_cluster,
                is_transient=delete_instance_event_msg.is_transient)
            orchhdr = OrchHeader(workflow, job.jobid)
            orchmsg = OrchestratorMsg(orchhdr, 0, None,
                                      log_tag=self.logger.get_updated_log_tag(self.rastro_log_tag))
            orchmsg.DeleteInstanceEventMsg = delete_instance_event_msg_instance

            obj = self.to_json(orchmsg)
            result.append(obj)
            return True
        else:
            self.error = "Invalid operation %s" % str(operation)
            job.status_msg = self.error
            job.job_type = "UNDECIDED"
            job.save_job(self.db_h)
            self.logger.error("Failed to process operation %s" % str(operation))
            return False

    def tenant_ipv6_settings_handler(self, job, operation, result, tenant_ipv6_settings_event_msg):
        self.logger.info("OnboardingHandler:tenant_ipv6_settings_handler(): "
                         "Got job id %d operation %s row %s" % (job.jobid, operation,
                                                                str(tenant_ipv6_settings_event_msg)))
        # Insert operation
        if operation == "tenant_ipv6_settings_event":
            workflow = "TENANT_IPV6_SETTINGS_EVENT"
            job.status_msg = prepare_job_status_msg("Initiating workflow %s." % str(workflow))
            job.job_type = workflow
            job.save_job(self.db_h)

            tenant_ipv6_settings_event_msg_instance = TenantIpv6SettingsEventMsg()
            tenant_ipv6_settings_event_msg_instance.set_TenantIpv6SettingsEventMsg_metadata(
                tenant_id=tenant_ipv6_settings_event_msg.tenant_id,
                region_name=tenant_ipv6_settings_event_msg.region_name,
                mode=tenant_ipv6_settings_event_msg.mode,
                node_type=tenant_ipv6_settings_event_msg.node_type)
            orchhdr = OrchHeader(workflow, job.jobid)
            orchmsg = OrchestratorMsg(orchhdr, 0, None,
                                      log_tag=self.logger.get_updated_log_tag(self.rastro_log_tag))
            orchmsg.TenantIpv6SettingsEventMsg = tenant_ipv6_settings_event_msg_instance

            obj = self.to_json(orchmsg)
            result.append(obj)
            return True
        else:
            self.error = "Invalid operation %s" % str(operation)
            job.status_msg = self.error
            job.job_type = "UNDECIDED"
            job.save_job(self.db_h)
            self.logger.error("Failed to process operation %s" % str(operation))
            return False


    def priv_sase_node_update_handler(self, job, operation, result, priv_sase_node_update_event_msg):
        self.logger.info("OnboardingHandler:priv_sase_node_update_handler(): "
                         f"Got job {job} operation {operation} priv_sase_node_update_event_msg {priv_sase_node_update_event_msg}")
        # Insert operation
        if operation == "priv_sase_node_update_event":
            workflow = "PRIV_SASE_NODE_UPDATE_EVENT"
            job.status_msg = prepare_job_status_msg("Initiating workflow %s." % str(workflow))
            job.job_type = workflow
            job.save_job(self.db_h)

            priv_sase_node_event_msg_instance = PrivSaseNodeUpdateEventMsg()
            priv_sase_node_event_msg_instance.set_PrivSaseNodeUpdateEventMsg_metadata \
                (sase_private_region_id= priv_sase_node_update_event_msg.sase_private_region_id,
                 service_node_type=priv_sase_node_update_event_msg.service_node_type,
                 custid=priv_sase_node_update_event_msg.custid,
                 node_type=priv_sase_node_update_event_msg.node_type)
            orchhdr = OrchHeader(workflow, job.jobid)
            orchmsg = OrchestratorMsg(orchhdr, priv_sase_node_update_event_msg.custid, None,
                                      log_tag=self.logger.get_updated_log_tag(self.rastro_log_tag))
            orchmsg.PrivSaseNodeUpdateEventMsg = priv_sase_node_event_msg_instance

            obj = self.to_json(orchmsg)
            result.append(obj)
            return True
        else:
            self.error = "Invalid operation %s" % str(operation)
            job.status_msg = self.error
            job.job_type = "UNDECIDED"
            job.save_job(self.db_h)
            self.logger.error("Failed to process operation %s" % str(operation))
            return False


    def process_msg(self, msg, workflow, jobid, result):
        panorama_job_id = None
        self.rastro_log_tag = None
        cloud_provider = None
        compute_region_id = None
        node_type = None

        if msg.operation.lower() == "update_epaas" or msg.operation.lower() == "upgrade_ep_outside_panos" or msg.operation.lower() == "ep_cnat_scaleout":
            cloud_provider = msg.cloud_provider
            compute_region_id = msg.compute_region_id
            node_type = NODE_TYPE_SWG_PROXY
            if msg.node_type in (NODE_TYPE_BI_NH_PROXY, str(NODE_TYPE_BI_NH_PROXY)):
                node_type = NODE_TYPE_BI_NH_PROXY
            elif msg.node_type in (NODE_TYPE_UDA, str(NODE_TYPE_UDA)):
                node_type = NODE_TYPE_UDA

        ret = False
        lret = False
        try:
            job = JOB.OrchJobs(jobid=jobid, dbh=self.db_h)
            if not job.jobid:
                self.error = ("OnboardingHandler:process_msg:Unable to find job"
                              " with id %d" % (jobid,))
                self.logger.error("OnboardingHandler:process_msg:Unable to find job"
                                  " with id %d" % (jobid,))
                return

            if not msg.topic in self.notification_hdlr:
                self.error = ("OnboardingHandler:process_msg():Got unsupported "
                              "event %s for processing, Job id %d" % (msg.topic, jobid))
                self.logger.error("OnboardingHandler:process_msg():Got unsupported "
                                  "event %s for processing, Job id %d" % (msg.topic, jobid))
                return

            if msg.panorama_job_id != None:
                panorama_job_id = msg.panorama_job_id
                self.logger.info("Found the panorama job id as %s for msg %s" % (str(panorama_job_id), str(msg)))

            if msg.rastro_log_tag != None:
                try:
                    self.rastro_log_tag = json.loads(base64.b64decode(msg.rastro_log_tag).decode('utf-8'))
                    self.logger.set_enhanced_log_tag(self.rastro_log_tag)
                except Exception as E:
                    self.logger.info("Failed to do a base64 decode for %s" % str(msg.rastro_log_tag))
                    
            lret = False
            try:
                if msg.topic == "orchestration_event":
                    # Explicit Orchestration events have more information than just the rowid
                    # Pass the entire payload (OrchestrationEventsMsg) to the orchestration_q
                    lret = self.orchestration_event_handler(job, msg, result)
                elif msg.topic in ["egress_ip_scale"]:
                    lret = self.egress_ip_scale_handler(job, msg.operation, result, msg.EgressIPScaleEventMsg)
                elif msg.topic in ["colo_vlan_onboard"]:
                    lret = self.colo_vlan_onboard_handler(job, msg.operation, result, msg.ColoVlanOnboardEventMsg)
                elif msg.topic in ["delete_instance"]:
                    lret = self.delete_instance_handler(job, msg.operation, result, msg.DeleteInstanceEventMsg)
                elif msg.topic in ["migrate_cluster_to_ipv6"]:
                    lret = self.migrate_cluster_to_ipv6_handler(job, msg.operation, result, msg.MigrateClusterToIPV6EventMsg)
                elif msg.topic in ["tenant_ipv6_settings"]:
                    lret = self.tenant_ipv6_settings_handler(job, msg.operation, result, msg.TenantIpv6SettingsEventMsg)
                elif msg.topic in ["priv_sase_node_update"]:
                    lret = self.priv_sase_node_update_handler(job, msg.operation, result, msg.PrivSaseNodeUpdateEventMsg)
                else:
                    lret = self.notification_hdlr[msg.topic](job, msg.operation, msg.id, result, panorama_job_id,
                                                             cloud_provider, compute_region_id, node_type)
            except Exception as E:
                self.logger.info("Unable to handle msg : %s" % str(msg))

            ret = lret
        except Exception as E:
            self.logger.error(
                "process_msg: Failed with exception %s, traceback %s" % (str(E.args), str(traceback.format_exc())))

        finally:
            self.logger.reset_enhanced_log_tag()
            return ret
