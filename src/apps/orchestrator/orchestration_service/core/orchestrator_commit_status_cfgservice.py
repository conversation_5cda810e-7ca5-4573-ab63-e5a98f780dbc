from libs.common.shared import sys_utils
from libs.model.orchcfgmodel_v2 import OrchCfgModel_v2 as OrchCfgModel
from libs.common.shared.apis import gcpPubSub
from libs.common.shared.gcp_utils import gcp_authenticate
from libs.common.shared.utils import get_pac_project_name, is_china_env
from libs.common.shared.sys_utils import DEPLOYMENT_ENV_FEDRAMP_IL5
from libs.common.shared.py3_utils import b64encode
from google.api_core.exceptions import NotFound as googleResNotFoundException
from libs.cfg import cfg
import json

CFGNOTIFY_INSTANCE_UP_TOPIC = "cfgservRequest"
PUBLISH_INSTANCE_UP_MSG_FILTER = "instanceUp-wrk-cfgNotifyCfg"


def _get_publish_payload(input_payload):
    '''
    Creates a payload that is understood by the Configservice.
    Returns:
        valid json data string and 0 return code on success.
        exception string and 1 on failure
    '''
    try:
        input_payload["client_id"] = "orchestrator"
        input_payload["request_type"] = "InstanceOnboardingUpdateRequest"
        input_payload["sub_tenant_id"] = input_payload.pop("tenant_id")
        # sub_tenant_id is tenant_id in Cosmos
        data = b64encode(input_payload)
        send_payload = dict()
        send_payload["data"] = data
        send_payload["data_type"] = 1 #JsonType
        send_payload["data_encoding"] = 0 #String
        send_payload["log_tag"] = input_payload["log_id"]
        return send_payload, 0, ""
    except Exception as e:
        return {}, 1, str(e)


def send_configservice_commit_status(input_payload, dbh):
    '''
    Checks if all required parameters are present in payload and will send data on matching tenant, node_type and workflow criteria
    to send data to configservice.
    For china deployment or IL5 deployment no need to send message since , GCP deployment doesn't exist.
    '''
    if dbh is None:
        print("dbh is none. Cannot proceed")
        return
    logger = dbh.logger
    if cfg.get('aws_env_type') != DEPLOYMENT_ENV_FEDRAMP_IL5 \
            and not is_china_env():
        reqrd_keys = {"workflow_type", "tenant_id", "panorama_job_id", "workflow_status", "workflow_status_details",
                      "instance1_id", "instance2_id", "cust_topology_id", "orchestrator_status",
                      "orchestrator_status_details", "node_type", "log_id"}
        input_keys = set(input_payload.keys())
        if not reqrd_keys.issubset(input_keys):
            logger.error(f"missing one of the reqrd_keys: {reqrd_keys} in input_payload: {input_keys}")
            return
        tenant_id = input_payload["tenant_id"]
        if tenant_id is None or tenant_id == "":
            logger.error(f"tenant_id is missing or empty in input_payload: {input_payload}")
            return
        node_type = input_payload["node_type"]
        if node_type != sys_utils.NODE_TYPE_REMOTE_NET and node_type != sys_utils.NODE_TYPE_SERVICE_CONN \
            and node_type != sys_utils.NODE_TYPE_GP_GATEWAY and node_type != sys_utils.NODE_TYPE_GP_PORTAL:
            logger.info(f"Skip sending info to configservice for node_type: {node_type}")
            return
        workflow_type = input_payload["workflow_type"]
        if workflow_type not in ["TOPOLOGY_DELETE", "TOPOLOGY_UPDATE", "TOPOLOGY_ADD", "INSTANCE_UPGRADE"]:
            logger.info(f"Skip sending info to configservice for workflow_type: {workflow_type}")
            return
        try:
            global_cfg = OrchCfgModel(dbh=dbh)
            svc_acct = gcp_authenticate(global_cfg.fields, logger)
            if svc_acct is None:
                err_msg = "Missing service account information in orch_cfg"
                logger.error(err_msg)
                return
            logger.info(f"input_payload: {input_payload}")
            gcp_project = get_pac_project_name(tenant_id, logger, cfg.get("aws_env", None))
            if gcp_project == "":
                logger.error(f"did not get gcp_project for tenant: {tenant_id}")
            try:
                publish_client = gcpPubSub.PubSubClient(logger=logger, project=gcp_project, svc_acct=svc_acct)
            except Exception as e:
                logger.error(f"failed to create pubsub client: {str(e)}")
                return
            tenant_topic_name = f"notify_{tenant_id}_req"
            publish_client.set_topic_path(tenant_topic_name)
            # Instead of checking if tenant is enabled for configservice, assuming here if the topic is created
            # tenant should be enabled for configservice.
            try:
                _ = publish_client.list_topic_subscriptions()
            except googleResNotFoundException as e:
                logger.info(f"did not find topic: {tenant_topic_name}. Mostly tenant not enabled for cfgservice. err: {str(e)}")
                return
            except Exception as e:
                logger.info(f"Generic Exception to validate topic: {tenant_topic_name}. Mostly tenant not enabled for cfgservice. err: {str(e)}")
                return
            send_payload, err_code, err_msg = _get_publish_payload(input_payload)
            if err_code:
                logger.error(f"failed to get publish payload err: {err_msg}")
                return
            if not publish_client.publish(payload=send_payload, msgFor=PUBLISH_INSTANCE_UP_MSG_FILTER):
                logger.error(f"failed to publish to topic: {tenant_topic_name}")
                return
            publish_client.set_topic_path(CFGNOTIFY_INSTANCE_UP_TOPIC)
            if not publish_client.publish(payload=send_payload, msgFor=PUBLISH_INSTANCE_UP_MSG_FILTER):
                logger.error(f"failed to publish to topic: {CFGNOTIFY_INSTANCE_UP_TOPIC}")
                return
            logger.info("successfully published payload for tenant.")
        except Exception as e:
            logger.error(f"Generic Exception while trying to send data to configservice. Err: {str(e)}")
    else:
        logger.info("Avoid sending message to config service since env doesn't support config service.")
