import traceback

from orchestration_service.core.orchestrator_spr_inst_profile_sync import ProfileSyncManager
from libs.common.shared.sys_utils import NODE_TYPE_GP_GATEWAY, NODE_TYPE_SASE_PRIV_REGION_LB, NODE_TYPE_SASE_PRIV_REGION_AGENT,NODE_TYPE_REMOTE_NET

def handle_priv_sase_node_update_event(db_h, sase_private_region_id, service_node_type, custid, node_type):
    """
    Handle a private SASE node update event by synchronizing the instance profile with instance masters.

    This function is called when there's a change in a private region instance profile,
    and it triggers the synchronization process to update all affected instances.

    Args:
        db_h: Database handler with connection and cursor methods
        sase_private_region_id (int): ID of the SASE private region
        service_node_type (int): Service node type ID
        custid (int): Customer ID
        node_type (int): Node type ID (49 for mobile users gateway, 179 for SASE private region load balancer)

    Returns:
        dict: A dictionary containing the results of the synchronization process
    """
    logger = db_h.logger
    logger.info(f"Handling private SASE node update event: "
                f"sase_private_region_id={sase_private_region_id}, "
                f"service_node_type={service_node_type}, "
                f"custid={custid}, "
                f"node_type={node_type}")

    try:
        # Validate the input parameters
        if not all([isinstance(sase_private_region_id, int),
                    isinstance(service_node_type, int),
                    isinstance(custid, int),
                    isinstance(node_type, int)]):
            error_msg = "Invalid parameter types. All parameters must be integers."
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }

        # Check if node_type is supported
        if node_type not in [NODE_TYPE_GP_GATEWAY, NODE_TYPE_SASE_PRIV_REGION_LB, NODE_TYPE_SASE_PRIV_REGION_AGENT, NODE_TYPE_REMOTE_NET]:
            error_msg = f"Unsupported node_type: {node_type}. Supported types: 48, 49, 179, 180"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }

        # Create the sync manager and perform the synchronization
        sync_manager = ProfileSyncManager(dbh=db_h,
                                          node_type=node_type,
                                          service_node_type=service_node_type,
                                          sase_private_region_id=sase_private_region_id,
                                          custid=custid)
        result = sync_manager.sync_profile_to_instances()
        # Log the synchronization results
        if result["success"]:
            changes = result["changes"]
            change_types = result["change_types"]

            logger.info(f"Successfully synchronized profile for node_type={node_type}, "
                        f"service_node_type={service_node_type}, "
                        f"sase_private_region_id={sase_private_region_id}, "
                        f"custid={custid}")

            logger.info(f"Synchronization summary: "
                        f"VIP changes: {changes['vip_changes']}, "
                        f"IP changes: {changes['ip_changes']}, "
                        f"Gateway changes: {changes['gateway_changes']}, "
                        f"Total instances updated: {changes['total_instances_updated']}, "
                        f"Change types: {', '.join(change_types)}")
        else:
            logger.error(f"Failed to synchronize profile: {result.get('error', 'Unknown error')}")

        return result

    except Exception as e:
        error_msg = f"Exception in handle_priv_sase_node_update_event: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())

        return {
            "success": False,
            "error": error_msg
        }
