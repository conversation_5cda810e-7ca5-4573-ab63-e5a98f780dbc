import traceback
import json
import os
import libs.model.instancemodel as INST
import libs.model.custnodemodel as CSN
import libs.model.custmodel as CM
from libs.common.shared.sys_utils import NODE_TYPE_GP_GATEWAY, \
    NODE_TYPE_NAT_INSTANCE_NAME
from orchestration_service.core.orchestrator_nlb_mgmt import has_nlb_in_region, allocate_nlb
from orchestration_service.core.orchestrator_nat_mgmt import has_nat_gateways, \
    is_nat_gateway_nlb_needed, allocate_nat_gateways
from libs.cloud_providers.common.instance_trigger_update import trigger_update_for_instance_changes
from libs.model.pinnedInstanceUpgradeModel import PinnedInstanceUpgradeModel
from libs.apis.region_master_api import get_cloud_instance_types_for_region_idx, is_region_id_premium_zone
from libs.common.shared.soft_upgrade.ami_plugin_mapping import AmiPluginMapping
from libs.common.shared.soft_upgrade.soft_upgrade_util import get_fw_version,is_ami_plugin_mapping_feature_enabled
from libs.common.shared.utils import isBlank
from libs.common.shared.ngpa_utils import send_ngpa_cust_alert, NGPACustAlertReasons
from libs.model.instancemodel import get_pinned_instance_by_custid_and_node_type_and_region_id, \
                                     compare_egress_ip_list_validity
from libs.cfg import *

def pinned_gp_gateway_instance_upgrade_cloud_formation_error_log(logger,
                                                                 err_msg,
                                                                 current_pinned_instance_ref):
    try:
        logger.error(err_msg)
        current_pinned_instance_ref.set_param("cloud_formation_done", -1)
        current_pinned_instance_ref.set_param("cloud_formation_status",
                                              err_msg)
        current_pinned_instance_ref.save_stage2()
        #if this value is True, send alert cust details notification
        if current_pinned_instance_ref.get_param("is_ngpa_auto_migration"):
            custid = current_pinned_instance_ref.get_param("custid")
            compute_region_id = current_pinned_instance_ref.get_param("instance_region_id")
            send_ngpa_cust_alert(custid, compute_region_id, NGPACustAlertReasons.FAILURE, logger, invoker="pinned_gp_gateway_instance_upgrade_cloud_formation_error_log")

    except Exception as E:
        logger.error("pinned_gp_gateway_instance_upgrade_cloud_formation_error_log:"
                     "Failed with exception! %s" % str(E.args))
        return

def update_mu_instance_plugin_version(logger,new_instance_id,current_pinned_instance_ref):
    '''
                update plugin version for new instance_id
    '''
    try:
        logger.info("calling update_mu_instance_plugin_version")
        custid = current_pinned_instance_ref.get_param('custid')
        version = current_pinned_instance_ref.get_param('version')
        if is_ami_plugin_mapping_feature_enabled(logger):
            if not isBlank(new_instance_id) and not isBlank(custid):
                logger.info("new_instance_id = %s, custid = %s, version = %s " %
                            (new_instance_id, custid, version))
                ami_plugin_mapping_obj = AmiPluginMapping(None, None, logger, cust_id=custid,
                                                          instance_id=new_instance_id, found_metadata=True)
                if ami_plugin_mapping_obj.is_feature_enabled_for_tenant():
                    fw_version = get_fw_version(version)
                    ami_plugin_map = ami_plugin_mapping_obj.get_ami_plugin_mapping_from_db()
                    ami_plugin_mapping_obj.update_target_plugin_version_in_instance_master(ami_plugin_map, fw_version)
    except Exception as ex:
        logger.info(traceback.format_exc())
        logger.info("somehow update instance_master with target_plugin_version failed = %s - "
                    "%s " % (str(ex), str(locals()))) # don't break the workflow at this stage to update the target plugin version.

def pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger,
                                                               err_msg,
                                                               current_pinned_instance_ref):
    try:
        logger.error(err_msg)
        current_pinned_instance_ref.set_param("orchestration_done", -1)
        current_pinned_instance_ref.set_param("orchestration_status",
                                              err_msg)
        current_pinned_instance_ref.save_stage1()
        #if this value is True, send alert cust details notification
        if current_pinned_instance_ref.get_param("is_ngpa_auto_migration"):
            custid = current_pinned_instance_ref.get_param("custid")
            compute_region_id = current_pinned_instance_ref.get_param("instance_region_id")
            send_ngpa_cust_alert(custid, compute_region_id, NGPACustAlertReasons.FAILURE, logger, invoker="pinned_gp_gateway_instance_upgrade_orchestration_error_log")
            
    except Exception as E:
        logger.error("pinned_gp_gateway_instance_upgrade_orchestration_error_log:"
                     "Failed with exception! %s" % str(E.args))
        return


def set_target_capacity_type(logger, capacity_type, target_capacity_type, curr_instance_reference,
                             current_pinned_instance_ref):
    try:
        if target_capacity_type == "CURRENT_TYPE":
            current_capacity_type = json.loads(curr_instance_reference.get_param("salt_profile")).get("PrimaryCapacityType", None)
            if not current_capacity_type:
                err_msg = (
                            "Failed to get PrimaryCapacityType from the current instance! salt_profile: %s" % curr_instance_reference.get_param("salt_profile"))
                raise Exception(err_msg)
            capacity_type = current_capacity_type
            logger.info("Use target_capacity_type: %s (CURRENT_TYPE)" % capacity_type)
        elif target_capacity_type is not None:
            capacity_type = target_capacity_type
            logger.info("Use target_capacity_type: %s" % capacity_type)
    except Exception as e:
        err_msg = "Failed in set_target_capacity_type! %s" % str(e)
        logger.error(err_msg)
    finally:
        return capacity_type


def set_target_cloud_machine_type(logger, cloud_machine_type, target_cloud_machine_type, curr_instance_reference,
                                  current_pinned_instance_ref, db_h, region, custid=0):
    try:
        if target_cloud_machine_type == "CURRENT_TYPE":
            current_cloud_machine_type = json.loads(curr_instance_reference.get_param("salt_profile")).get("InstType", None)
            if not current_cloud_machine_type:
                err_msg = (
                            "Failed to get native_machine_type from the current instance! salt_profile: %s" % curr_instance_reference.get_param("salt_profile"))
                raise Exception(err_msg)
            cloud_machine_type = current_cloud_machine_type
            logger.info("Use target_cloud_machine_type: %s (CURRENT_TYPE)" % cloud_machine_type)
        elif target_cloud_machine_type is not None:
            cloud_instances = get_cloud_instance_types_for_region_idx(db_h, region, custid=custid)
            cloud_instance_types = json.loads(cloud_instances)
            if target_cloud_machine_type not in cloud_instance_types.values():
                err_msg = ("%s is not a valid cloud_machine_type. "
                           "Please check available values in the cloud_instance_types "
                           "from region_master table!" % target_cloud_machine_type)
                raise Exception(err_msg)
            cloud_machine_type = target_cloud_machine_type
            logger.info("Use target_cloud_machine_type: %s" % cloud_machine_type)
    except Exception as e:
        err_msg = "Failed in set_target_cloud_machine_type! %s" % str(e)
        logger.error(err_msg)
    finally:
        return cloud_machine_type


def pinned_gp_gateway_instance_upgrade_orchestration_impl(db_h,
                                                          current_pinned_instance_ref,
                                                          current_instance_id,
                                                          target_cloud_machine_type,
                                                          target_capacity_type):
    success = False
    new_instance_reference = None
    logger = db_h.logger
    try:
        # Get the current instance reference
        curr_instance_reference = INST.InstanceModel(iid=current_instance_id, dbh=db_h)
        if curr_instance_reference.get_param("id") == 0:
            err_msg = "Fatal! Failed to locate instance with id %s from the database." % str(
                current_instance_id)
            pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, err_msg, current_pinned_instance_ref)
            raise Exception(err_msg)
        # Get the cust topology nodes for this instance.
        all_sites = curr_instance_reference.get_sites(dbh=db_h)
        logger.info("Found %s sites for instance with id %s" % (str(all_sites), str(current_instance_id)))
        if len(all_sites) == 0:
            err_msg = ("Fatal! No sites were found corresponding to instance %s" %
                       str(current_instance_id))
            pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, err_msg, current_pinned_instance_ref)
            raise Exception(err_msg)
        # Let's pick up an entry in the cust topology.
        site_id = all_sites[0]
        cust_node_reference = CSN.CustNodeModel(iid=site_id, dbh=db_h,
                                                compute_region_id=curr_instance_reference.get_param("compute_region_idx"))
        if cust_node_reference.id == None or cust_node_reference.id == 0:
            err_msg = ("No instance node reference %s found." % str(site_id))
            pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, err_msg, current_pinned_instance_ref)
            raise Exception(err_msg)
        cust_reference = CM.CustomerModel(custid=cust_node_reference.custid, dbh=db_h)
        if not cust_reference.get_param("id"):
            err_msg = ("No customer id found for site ID %s. Cannot continue" % str(site_id))
            pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, err_msg, current_pinned_instance_ref)
            raise Exception(err_msg)
        # Allocate the instance.
        node_version = None
        node_type = None
        region = curr_instance_reference.get_param("compute_region_idx")
        machine_type = cust_node_reference.machine_type
        gpcs_instance_size = cust_node_reference.gpcs_instance_size
        custid = cust_node_reference.custid
        cloud_machine_type = cust_node_reference.cloud_machine_type
        db_h.cpu_platform = cust_node_reference.cpu_platform
        capacity_type = cust_node_reference.capacity_type
        market_type = cust_node_reference.market_type
        dpdk_qcount = cust_node_reference.dpdk_qcount
        _has_nat_gateways = False
        is_premium_zone = is_region_id_premium_zone(db_h, region, custid=custid)
        ret = db_h.get_type_name_from_id(cust_node_reference.node_type)
        if not ret.ok:
            err_msg = ("pinned_gp_gateway_instance_upgrade: Unable to get Node typename"
                       "from id %d" % (cust_node_reference.node_type,))
            pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, err_msg, current_pinned_instance_ref)
            raise Exception(err_msg)
        node_type = ret.result[0]
        if current_pinned_instance_ref.get_param("version"):
            _service_version = current_pinned_instance_ref.get_param("version")
        else:
            if node_type == NODE_TYPE_NAT_INSTANCE_NAME:
                _service_version = cust_reference.get_sw_version_for_nat_instance(region)
            else:
                _service_version = cust_reference.get_param("version")
        ret = db_h.get_nodetype_sw_version_from_cust_version(
            node_type, _service_version)
        if not ret.ok:
            err_msg = ("Unable to get software version for node type"
                       " %s and service version %s" % (str(node_type), str(cust_reference.get_param("version"))))
            pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, err_msg, current_pinned_instance_ref)
            raise Exception(err_msg)

        node_version = ret.result[0]

        if target_cloud_machine_type:
            cloud_machine_type = set_target_cloud_machine_type(logger, cloud_machine_type, target_cloud_machine_type,
                                                               curr_instance_reference, current_pinned_instance_ref,
                                                               db_h, region, custid=custid)
        logger.info("cloud_machine_type: %s" % cloud_machine_type)

        if target_capacity_type:
            capacity_type = set_target_capacity_type(logger, capacity_type, target_capacity_type,
                                                     curr_instance_reference, current_pinned_instance_ref)
        logger.info("capacity_type: %s" % capacity_type)

        db_h.set_instance_extra_param(cloud_machine_type, capacity_type, market_type, dpdk_qcount)
        _has_nlb = False
        if cust_node_reference.node_type in [NODE_TYPE_GP_GATEWAY]:
            # If NAT Gateways are present, we will bring up the
            # new instance in front of the NAT Gateway.
            _has_nat_gateways = has_nat_gateways(db_h, custid, region)
            if _has_nat_gateways == True:
                logger.info("NAT gateways are present for cust id %s and compute_region_idx %s" %
                            (str(custid), str(region)))
            else:
                logger.info("NAT gateways are not present for cust id %s and compute_region_idx %s" %
                            (str(custid), str(region)))

            # If the NLB is present, we will bring up the new instance
            # behind the NLB.
            _has_nlb = has_nlb_in_region(db_h, custid, region)
            if _has_nlb == True:
                logger.info("NLB is present for cust id %s and region %s" %
                            (str(custid), str(region)))
            else:
                logger.info("NLB is not present for cust id %s and "
                            "region %s" % (str(custid), str(region)))

        _two_phs_upg = current_pinned_instance_ref.get_param(
                                         "two_phase_upgrade_invocation")
        res = db_h.get_new_instance_int(node_version,
                                        node_type,
                                        region,
                                        machine_type,
                                        gpcs_instance_size,
                                        custid,
                                        is_dynamic_instance=0,
                                        is_clean_pipe=cust_node_reference.is_clean_pipe,
                                        cust_topology_id=cust_node_reference.id,
                                        upgrade_creation=1,
                                        avail_domain=cust_node_reference.avail_domain,
                                        okyo_edge_site_id=cust_node_reference.okyo_edge_site_id,
                                        has_nat_gateway=_has_nat_gateways,
                                        is_instance_behind_nlb=_has_nlb,
                                        is_two_phase_upgrade_call=_two_phs_upg,
                                        old_instance_id=current_instance_id,
                                        is_premium_zone=is_premium_zone,
                                        alt_node_type=cust_node_reference.alt_node_type)
        if not res.ok:
            err_msg = (f"pinned_gp_gateway_instance_upgrade: "
                       f"Unable to get new instance for node type: {str(node_type)}, alt node type: {str(cust_node_reference.alt_node_type)}")
            pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, err_msg, current_pinned_instance_ref)
            raise Exception(err_msg)

        new_instance_reference = res.result[0]
        new_instance_id = new_instance_reference.get_param("id")
        # Bind this node to the instance.
        ret = new_instance_reference.bind(dbh=db_h,
                                          custid=None,
                                          node_type=None,
                                          node_id=None,
                                          is_new=1,
                                          custnode=cust_node_reference)
        if ret == False:
            err_msg = ("pinned_gp_gateway_instance_upgrade: Fatal! "
                       "Failed to bind the new instance %s" % str(new_instance_id))
            logger.error(err_msg)
            pinned_gp_gateway_instance_upgrade_orchestration_error_log(
                logger,
                err_msg,
                current_pinned_instance_ref
            )
            raise Exception(err_msg)

        if str(node_type) != "PROBEVM":
            # new instance master entry is created. let's insert the job information for this instance in
            # firewall_summary
            ret = new_instance_reference.insert_last_successfull_job_info(db_h,
                                                                          current_instance_id,
                                                                          customer=cust_reference)
            if ret == False:
                err_msg = ("pinned_gp_gateway_instance_upgrade: Fatal! "
                           "Failed to insert the job info for in firewall summary table for the new instance %s" % str(
                    new_instance_id))
                logger.error(err_msg)
                pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger,
                                                                           err_msg,
                                                                           current_pinned_instance_ref)
                raise Exception(err_msg)

        # Set the instance as transient for the associated node id's.
        for site_id in all_sites:
            cust_node_reference = CSN.CustNodeModel(iid=site_id, dbh=db_h)
            if cust_node_reference.id == None or cust_node_reference.id == 0:
                err_msg = ("pinned_gp_gateway_instance_upgrade:"
                           "Fatal! No instance node reference %s found." % str(site_id))
                logger.error(err_msg)
                pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger,
                                                                           err_msg,
                                                                           current_pinned_instance_ref)
                raise Exception(err_msg)
            ret = cust_node_reference.set_instance1_transient(dbh=db_h,
                                                              instance1_id=new_instance_id)
            if not ret:
                err_msg = ("pinned_gp_gateway_instance_upgrade:"
                           "Fatal! Failed to set transient instance to %s for node %s."
                           % (str(new_instance_id),
                              str(site_id)))
                logger.error(err_msg)
                pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, err_msg, current_pinned_instance_ref)
                raise Exception(err_msg)

        current_pinned_instance_ref.set_param("new_instance_id", new_instance_id)
        current_pinned_instance_ref.set_param("orchestration_done", 1)
        current_pinned_instance_ref.set_param("orchestration_status", "Success!")
        ret = current_pinned_instance_ref.save_stage1()
        if ret == False:
            err_msg = ("pinned_gp_gateway_instance_upgrade Fatal ! "
                       "Failed to save the new instance id %s details for stage 1."
                       % (str(new_instance_id)))
            logger.error(err_msg)
            pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, err_msg, current_pinned_instance_ref)
            raise Exception(err_msg)

        update_mu_instance_plugin_version(logger, new_instance_id, current_pinned_instance_ref)

        myres, _ = trigger_update_for_instance_changes(db_h,
                                                       region,
                                                       custid,
                                                       node_type=cust_node_reference.node_type,
                                                       alt_node_type=cust_node_reference.alt_node_type)
        if myres == False:
            err_msg = ("pinned_gp_gateway_instance_upgrade Fatal ! "
                       "Failed to trigger new instance id's %s updates for "
                       "CloudFormation/DeploymantManager." % (str(new_instance_id)))
            logger.error(err_msg)
            pinned_gp_gateway_instance_upgrade_cloud_formation_error_log(logger,
                                                                         err_msg,
                                                                         current_pinned_instance_ref)
            raise Exception(err_msg)
        current_pinned_instance_ref.set_param("cloud_formation_done", 1)
        current_pinned_instance_ref.set_param("cloud_formation_status", "Success!")
        ret = current_pinned_instance_ref.save_stage2()
        if not ret:
            err_msg = ("pinned_gp_gateway_instance_upgrade Fatal ! "
                       "Failed to save the new instance id %s details for stage 2."
                       % (str(new_instance_id)))
            logger.error(err_msg)
            pinned_gp_gateway_instance_upgrade_cloud_formation_error_log(logger,
                                                                         err_msg,
                                                                         current_pinned_instance_ref)
            raise Exception(err_msg)
        success = True
    except Exception as E:
        err_msg = ("pinned_gp_gateway_instance_upgrade: Failed with Exception: %s "
                     "Traceback: %s "
                     "locals: %s"
                     % (str(E.args), str(traceback.print_exc()), str(locals())))
        logger.error(err_msg)
        success = False
        # Update the orchestration_done or cloud_formation_done
        if current_pinned_instance_ref:
            if current_pinned_instance_ref.get_param("orchestration_done") == 0:
                pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger,
                                                                           err_msg,
                                                                           current_pinned_instance_ref)
            elif current_pinned_instance_ref.get_param("cloud_formation_done") == 0:
                pinned_gp_gateway_instance_upgrade_cloud_formation_error_log(logger,
                                                                             err_msg,
                                                                             current_pinned_instance_ref)
        if new_instance_reference != None:
            ret = new_instance_reference.delete(db_h)
            if ret:
                logger.error("Deleted instance that was created")
            else:
                logger.error("Failed to delete instance that was created")
    finally:
        return success

def get_replica_sharding_info(db_h):
    '''
    Get replica and shard_id
    '''
    if os.environ.get("AWS_ENV", "") == "":
        # Running in VM mode - IL5 requirement
        return None, None
    replica_size = os.environ.get("REPLICA_SIZE", "")
    if replica_size == "":
        return None, None
    shard_id = int(os.environ["HOSTNAME"].split("orchestration-", 1)[1])
    return shard_id, int(replica_size)

def pinned_gp_gateway_instance_upgrade_orchestration(OrchstrationHandlerReference, db_h):
    success = False
    logger = db_h.logger
    try:
        shard_id, replica_size = get_replica_sharding_info(db_h)
        # Get the list of new instances to be processed.
        pinnedInstanceUpgrade = PinnedInstanceUpgradeModel(dbh=db_h,
                                                           instance_id=None,
                                                           shard_id=shard_id,
                                                           replica_size=replica_size)
        nr_entries, entries = pinnedInstanceUpgrade.query_all_stage1_entries()
        if nr_entries == 0:
            # logger.info("No entries found to work on, sleeping again!")
            success = True
            return success
        if len(entries) != nr_entries:
            raise Exception("pinned_gp_gateway_instance_upgrade: Fatal!  Numbed of entries in the list do "
                            "not match the entries reported.")
        for instance_entry in entries:
            if not OrchstrationHandlerReference.running:
                logger.info("pinned_gp_gateway_instance_upgrade_orchestration: "
                            "Orchestrator thread is no longer running. Exiting!")
                break
            current_instance_id = instance_entry[0]
            new_instance_id = instance_entry[1]
            target_cloud_machine_type = instance_entry[2]
            target_capacity_type = instance_entry[3]
            if target_capacity_type == "None":
                target_capacity_type = None
            # Once this is done. We save the new instance id.
            current_pinned_instance_ref = PinnedInstanceUpgradeModel(dbh=db_h,
                                                                     instance_id=current_instance_id)
            if current_pinned_instance_ref.isvalid == False:
                logger.error("pinned_gp_gateway_instance_upgrade Fatal! "
                             "Failed to lookup the new instance id %s to be saved." % (
                                 str(new_instance_id)))
                continue
            sub_uuid = current_pinned_instance_ref.get_param("batch_id")
            if sub_uuid:
                logger.set_sub_uuid(sub_uuid)
            else:
                logger.reset_sub_uuid()

            curr_instance_reference = INST.InstanceModel(iid=current_instance_id, dbh=db_h)
            if curr_instance_reference.get_param("id") == 0:
                err_msg = "Fatal! Failed to locate instance with id %s from the database." % str(
                    current_instance_id)
                pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, err_msg, current_pinned_instance_ref)
                continue

            if curr_instance_reference.get_param("node_type") == NODE_TYPE_GP_GATEWAY:
                # Check if the NAT gateways and NLB need to be created,
                # for the (customer, region) combination, based on no.
                # of gateways (threshold reached).
                nat_gw_needed, nlb_needed = is_nat_gateway_nlb_needed(
                                                db_h,
                                                curr_instance_reference.get_param("custid"),
                                                curr_instance_reference.get_param("compute_region_idx"))
                if nat_gw_needed:
                    lret = allocate_nat_gateways(db_h,
                                                 curr_instance_reference.get_param("custid"),
                                                 curr_instance_reference.get_param("compute_region_idx"))
                    if lret < 0:
                        err_msg = ("Failed to allocated NAT gateways. Cannot continue")
                        pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, err_msg,
                                                                                   current_pinned_instance_ref)
                        continue
                    if lret == 1:
                        logger.info("Successfully allocated NAT gateway.")
                else:
                    logger.info("NAT gateways are not needed for "
                                     "custid %s and region is %s" %
                                     (str(curr_instance_reference.get_param("custid")),
                                      str(curr_instance_reference.get_param("compute_region_idx"))))

                if nlb_needed:
                    lret = allocate_nlb(db_h, curr_instance_reference.get_param("custid"),
                                        curr_instance_reference.get_param("compute_region_idx"))
                    if lret < 0:
                        err_msg = ("Failed to allocated NLB. Cannot continue")
                        pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, err_msg,
                                                                                   current_pinned_instance_ref)
                        continue
                    if lret == 1:
                        has_nlb = True
                else:
                    logger.info("NLB is not needed for custid %s in "
                                     "region %s" % (str(curr_instance_reference.get_param("custid")),
                                                    str(curr_instance_reference.get_param("compute_region_idx"))))

            if new_instance_id > 0:
                err_msg = ("Looks like instance %s is already allocated for old instance %s."
                           % (str(new_instance_id),
                              str(current_instance_id)))
                logger.error(err_msg)
                continue

            if curr_instance_reference.get_param("node_type") == NODE_TYPE_GP_GATEWAY:
                # For GP gateways, Before the instance upgrade orchestration, check if the associated pinned
                # instance entry exits. if it exists it should have upgrade creation as 0, maybe we should only
                # do this for dynamic instances but skipping for now in case we find interesting cases.

                ret, pinned_instance_id = get_pinned_instance_by_custid_and_node_type_and_region_id(db_h,
                                                                    curr_instance_reference.get_param("custid"),
                                                                    curr_instance_reference.get_param("node_type"),
                                                                    curr_instance_reference.get_param("compute_region_idx"))
                if ret == False:
                    err_msg = (f"Cannot find the pinned instance id for the given id {current_instance_id}")
                    logger.error(err_msg)
                    pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, err_msg, current_pinned_instance_ref)
                    continue

                # Now Lets lookup the instance id from instance master.
                pinned_instance_master_entry_ref = INST.InstanceModel(iid=pinned_instance_id, dbh=db_h)
                if pinned_instance_master_entry_ref.get_param("id") in [ 0 , None ] :
                    err_msg = "Fatal! Failed to locate instance with id %s from the database." % str(
                        pinned_instance_id)
                    pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, err_msg, current_pinned_instance_ref)
                    continue

                # validate the egress IP keys list with the existing instance that is being upgraded and make sure we
                # don't have a mismatch. If there is a mismatch report it and block the upgrade.
                success, err_msg = compare_egress_ip_list_validity(logger,
                                                             pinned_instance_master_entry_ref.get_param("egress_ip_list"),
                                                             curr_instance_reference.get_param("egress_ip_list"))
                if success == False:
                    pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger,
                                                                               err_msg,
                                                                               current_pinned_instance_ref)
                    continue

            ret = pinned_gp_gateway_instance_upgrade_orchestration_impl(db_h,
                                                                        current_pinned_instance_ref,
                                                                        current_instance_id,
                                                                        target_cloud_machine_type,
                                                                        target_capacity_type)
            if ret == False:
                logger.error("Failed to orchestrate the instance with instance id %s" %
                             str(current_instance_id))
                continue
    except Exception as E:
        logger.error("pinned_gp_gateway_instance_upgrade: Failed with Exception: %s "
                     "Traceback: %s "
                     "locals: %s"
                     % (str(E.args), str(traceback.print_exc()), str(locals())))
        success = False
    finally:
        logger.reset_sub_uuid()
        return success


def delete_pinned_instance_entry_by_new_inst_id(dbh, new_instance_id):
    pinned_inst_ref = PinnedInstanceUpgradeModel(dbh=dbh)
    return pinned_inst_ref.delete_entry_by_new_instance_id(new_instance_id)


def is_current_upgrade_instance(logger, dbh, instance_id):
    found = False
    sql = ("SELECT * FROM pinned_instance_upgrade_table "
           "WHERE current_instance_id=%s "
           "OR new_instance_id=%s AND delete_old_done != 1")
    params = (instance_id, instance_id)
    logger.info("SQL: %s" % (sql % params))

    cursor = dbh.get_cursor()
    try:
        cursor.execute(sql, params)
        ret = cursor.fetchall()
        dbh.cursorclose(cursor)
        logger.info("SQL response: %s" % str(ret))
    except Exception as ex:
        err_msg = ("Unable to get information from "
            "pinned_instance_upgrade_table table in RDS "
            "for instance [%s]:  %s" % (instance_id, str(ex)))
        logger.error(err_msg)
        dbh.cursorclose(cursor)
        raise Exception(err_msg)

    if not ret:
        logger.info("Unable to find any entry in "
            "pinned_instance_upgrade_table for instance [%s] "
            "with two_phase_upgrade_invocation = 1" % instance_id)
    else:
        logger.info("Found entry in pinned_instance_upgrade_table "
            "for instance [%s] with two_phase_upgrade_invocation = 1"
            % instance_id)
        found = True

    logger.info("Return [%s] from is_current_upgrade_instance "
        "for instance [%s]" % (found, instance_id))
    return found
