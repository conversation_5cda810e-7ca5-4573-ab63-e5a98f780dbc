import requests
import time
from libs.cfg import *
from libs.cloud_providers.common.masque_helper import is_masque_feature_enabled
from libs.common.shared.sys_utils import (PROVIDER_GCP)

def handle_zti_location_add(dbh, logger, custid, edge_region_idx, compute_region_idx, cloud_provider):
    """
    Checks if the MASQUE feature is enabled and, if so, sends a POST request
    to the ZTI controller to add a new location.

    Args:
        dbh: Database handler instance.
        logger: Logger instance for logging messages.
        custid (str): The customer ID.
        edge_region_idx (int): The index for the edge region.
        compute_region_idx (int): The index for the compute region.
    """
    if cloud_provider != PROVIDER_GCP:
        logger.info(f"Cloud provider not supported")
        return

    if is_masque_feature_enabled(dbh=dbh, custid=custid):
        logger.info(f"MASQUE feature flag enabled for custid {custid}.")
        logger.info(f"Sending POST request to ZTI controller to add location.")
        success, data = _send_zti_locations_request_post(logger,
                                                         custid,
                                                         edge_region_idx,
                                                         compute_region_idx,
                                                         retry_count=4)
        if not success:
            logger.error(f"process_add_topology: Failed to process ZTI location add for custid={custid}, "
                         f"edge_region_idx={edge_region_idx}, compute_region_idx={compute_region_idx}")
        else:
            logger.info(f"process_add_topology: Successfully processed ZTI location add for cust_id={custid}")
    else:
        logger.info(f"process_add_topology: MASQUE feature flag disabled for custid {custid}, skipping ZTI POST request.")

def handle_zti_location_delete(dbh, logger, custid, compute_region_idx, cloud_provider):
    """
    Checks if the MASQUE feature is enabled and, if so, sends a DELETE request
    to the ZTI controller to remove a location.

    Args:
        dbh: Database handler instance.
        logger: Logger instance for logging messages.
        custid (str): The customer ID.
        compute_region_idx (int): The index for the compute region to be deleted.
    """
    # TODO FIX me. Cloud provider comes as none
    # if cloud_provider != PROVIDER_GCP:
    #     logger.info(f"Cloud provider not supported")
    #     return

    if is_masque_feature_enabled(dbh=dbh, custid=custid):
        logger.info(f"MASQUE feature flag enabled for custid {custid}.")
        logger.info(f"Sending DELETE request to ZTI controller to remove location.")
        success, data = _send_zti_locations_delete_request(logger,
                                                           custid,
                                                           compute_region_idx,
                                                           retry_count=4)
        if not success:
            logger.error(f"Failed to process ZTI location delete for custid={custid}, "
                         f"compute_region_idx={compute_region_idx}")
        else:
            logger.info(f"Successfully processed ZTI location delete for cust_id={custid}")
    else:
        logger.info(f"MASQUE feature flag disabled for custid {custid}, skipping ZTI DELETE request.")

def _send_zti_locations_request_post(logger, cust_id, edge_region_idx, compute_region_idx, retry_count=3, backoff_factor=0.5):
    """
    Send POST API request to ZTI controller with JSON payload, retries, and exponential backoff.

    This function incorporates several improvements:
    1.  A single exception block for all request-related errors.
    2.  A backoff mechanism to wait before retrying.
    3.  A simplified structure using a for loop for retries.
    4.  More specific and secure logging.

    Args:
        cust_id (str): The customer ID for the API endpoint.
        edge_region_idx (int): The index for the edge region.
        compute_region_idx (int): The index for the compute region.
        retry_count (int, optional): The maximum number of attempts. Defaults to 3.
        backoff_factor (float, optional): Factor to calculate sleep time.
                                         Sleeps for (backoff_factor * (2 ** attempt)) seconds.
                                         Defaults to 0.5.

    Returns:
        tuple: A tuple containing (bool, dict or None).
               - (True, response_json) on success.
               - (False, None) on failure after all retries.
    """

    # 1. Construct the API endpoint URL
    zti_url = f"http://zti-controller:8080/zti/iapi/v1/cust/{cust_id}/locations"

    logger.info(f"edge_region_idx: {edge_region_idx}, compute_region_idx: {compute_region_idx}")

    # Validate input parameters
    if not cust_id or not edge_region_idx or not compute_region_idx:
        logger.error("Invalid input parameters for ZTI locations request")
        return False, None

    # 2. Construct the payload from the function parameters
    payload = {
        "edge_location_region_id": int(edge_region_idx),
        "compute_region_id": int(compute_region_idx)
    }

    # 3. Loop for the specified number of retries
    for attempt in range(retry_count):
        try:
            logger.info(
                f"Attempt {attempt + 1}/{retry_count} for customer {cust_id}: "
                f"POST to {zti_url} with payload {payload}"
            )

            # Make the HTTP POST request
            response = requests.post(zti_url, json=payload, timeout=10)

            # Raise an HTTPError if the request returned an unsuccessful status code (4xx or 5xx)
            response.raise_for_status()

            # If the request was successful, parse JSON, log, and return
            result = response.json()
            logger.info(
                f"Success on attempt {attempt + 1}. Status: {response.status_code}, "
                f"Response for customer {cust_id}: {result}"
            )
            return True, result

        # 4. Consolidated exception handling for all requests-related issues
        except requests.exceptions.RequestException as e:
            # Log the specific error from the request
            logger.error(
                f"Attempt {attempt + 1} failed for customer {cust_id}. Error: {e}"
            )

            # If the exception has a response object, log its content for better debugging
            if e.response is not None:
                logger.error(f"Error response body: {e.response.text}")

        # 5. Backoff logic: Wait before the next attempt
        # Only sleep if this is not the last attempt
        if attempt < retry_count - 1:
            # Exponential backoff: 0.5s, 1s, 2s, etc.
            sleep_time = backoff_factor * (2 ** attempt)
            logger.info(f"Waiting for {sleep_time:.2f} seconds before retrying...")
            time.sleep(sleep_time)

    # 6. This part is reached only if all retries fail
    logger.error(
        f"All {retry_count} attempts failed for customer {cust_id}. "
        f"Aborting request to {zti_url}."
    )
    return False, None

def _send_zti_locations_delete_request(logger, cust_id, compute_region_id, retry_count=3, backoff_factor=0.5):
    """
    Send DELETE API request to ZTI controller to remove a location, with retries.

    This function handles the API call with retries and exponential backoff.
    It is designed to be resilient to transient network issues.

    Args:
        cust_id (str): The customer ID for the API endpoint.
        compute_region_id (int): The specific ID of the location to be deleted.
        retry_count (int, optional): The maximum number of attempts. Defaults to 3.
        backoff_factor (float, optional): Factor to calculate sleep time.
                                         Sleeps for (backoff_factor * (2 ** attempt)) seconds.
                                         Defaults to 0.5.

    Returns:
        tuple: A tuple containing (bool, dict or None).
               - (True, response_json) on success (if response has a body).
               - (True, None) on success (if response has no body, e.g., 204 No Content).
               - (False, None) on failure after all retries.
    """

    # 1. Construct the API endpoint URL from the function parameters
    # Note: In a production system, the base URL should come from a config file.
    base_url = "http://zti-controller:8080"
    zti_url = f"{base_url}/zti/iapi/v1/cust/{cust_id}/locations/{compute_region_id}"

    # Validate input parameters
    if not cust_id or not compute_region_id:
        logger.error("Invalid input parameters: cust_id and location_id are required.")
        return False, None

    # 2. Loop for the specified number of retries
    for attempt in range(retry_count):
        try:
            logger.info(
                f"Attempt {attempt + 1}/{retry_count} for customer {cust_id}: "
                f"DELETE to {zti_url}"
            )

            # Make the HTTP DELETE request. DELETE requests typically don't have a payload.
            response = requests.delete(zti_url, timeout=10)

            # Raise an HTTPError if the request returned an unsuccessful status code (4xx or 5xx)
            response.raise_for_status()

            # If the request was successful, handle the response
            logger.info(
                f"Success on attempt {attempt + 1}. Status: {response.status_code}"
            )

            # A successful DELETE may return 204 No Content, which has no JSON body.
            # Only try to parse JSON if there is content.
            if response.status_code != 204 and response.content:
                result = response.json()
                logger.info(f"Response for customer {cust_id}: {result}")
                return True, result
            else:
                logger.info(f"Request successful with no response body.")
                return True, None

        # 3. Consolidated exception handling for all requests-related issues
        except requests.exceptions.RequestException as e:
            # Log the specific error from the request
            logger.error(
                f"Attempt {attempt + 1} failed for customer {cust_id}. Error: {e}"
            )

            # If the exception has a response object, log its content for better debugging
            if e.response is not None:
                logger.error(f"Error response body: {e.response.text}")

        # 4. Backoff logic: Wait before the next attempt
        # Only sleep if this is not the last attempt
        if attempt < retry_count - 1:
            # Exponential backoff: 0.5s, 1s, 2s, etc.
            sleep_time = backoff_factor * (2 ** attempt)
            logger.info(f"Waiting for {sleep_time:.2f} seconds before retrying...")
            time.sleep(sleep_time)

    # 5. This part is reached only if all retries fail
    logger.error(
        f"All {retry_count} attempts failed for customer {cust_id}. "
        f"Aborting request to {zti_url}."
    )
    return False, None