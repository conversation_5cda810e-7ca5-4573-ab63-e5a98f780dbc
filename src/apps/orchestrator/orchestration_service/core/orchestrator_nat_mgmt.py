import traceback

from libs.model.instancemodel import find_non_transient_entries_by_custid_and_region
from libs.model.custnodemodel import CustNodeModel
from libs.model.custmodel import CustomerModel
from libs.model.regionmastermodel import RegionMasterModel
from libs.common.shared.rastro.rastro_logging_wrapper import generate_sample_log_tag
from libs.common.shared.py3_utils import b64encode
from libs.common.shared.sys_utils import NODE_TYPE_GP_GATEWAY, NODE_TYPE_NAT_INSTANCE, \
                                         is_nat_gw_supported_in_cloud_provider, \
                                         is_nlb_supported_in_cloud_provider, PROVIDER_VMWARE_ESXI, PROVIDER_GCP, PROVIDER_OCI
from libs.model.execute_orch_query import execute_orch_query
from libs.apis.region_master_api import gpcs_get_cloud_type_from_region_idx
from libs.common.utils import get_capacity_type

import re


def allocate_nat_gateways(db_h, custid, compute_region_idx, edge_region_idx=None, cloud_provider=PROVIDER_GCP):
    """

    :param custid:
    :param compute_region_idx:
    :return: 1 on 'successfully adding NAT gateways or
                   NAT gateways already present',
             -1 on 'error'
    """
    result = -1
    logger = db_h.logger
    try:
        # We have already completed checks that indicate that
        # we need NAT gateways in the region for this customer.
        # Check if NAT gateways are already present.
        # If not, allocate new NAT Gateways.
        logger.info(f"allocate_nat_gateways: Invoked for custnode region {edge_region_idx} for custid {custid} for {cloud_provider} cloud for compute {compute_region_idx} edge {edge_region_idx}")
        gcp_cnat_supported = db_h.is_gcp_cnat_supported(cloud_provider, custid, compute_region_idx)
        # Handle compute location NATs first.
        # We want to skip provisioning the compute region NAT gateway for edge location ONLY onboarding cases for OCI/GCP cloud (CNAT is deployed per edge)
        if ((cloud_provider == PROVIDER_OCI and edge_region_idx and str(edge_region_idx) == str(compute_region_idx)) or
            (cloud_provider == PROVIDER_GCP and (not gcp_cnat_supported or (gcp_cnat_supported and (str(edge_region_idx) == str(compute_region_idx)))))): 
            _has_nat_gateways = has_nat_gateways(db_h, custid, compute_region_idx)
            if _has_nat_gateways:
                logger.info(f"allocate_nat_gateways: Compute region {compute_region_idx} NAT gateways already present for custid {custid} for {cloud_provider} cloud")
                result = 1
            else:
                # Now create the NAT Gateways.
                lret = create_nat_instances_for_cust_and_region(db_h, custid, compute_region_idx)
                if lret < 0:
                    # This is a failure case.
                    raise Exception("Failed to create NAT gateways. Cannot progress further.")
                else:
                    logger.info(f"Successfully created NAT gateways for custid {custid} for {cloud_provider} cloud for compute {compute_region_idx} edge {edge_region_idx}")
                    result = 1

        # Special handling for edge region NAT gateways in OCI and GCP with CNAT support
        if (cloud_provider == PROVIDER_OCI or (cloud_provider == PROVIDER_GCP and gcp_cnat_supported) ):
            _has_edge_nat_gateways = has_nat_gateways(db_h, custid, compute_region_idx, edge_region_idx=edge_region_idx, cloud_provider=cloud_provider)
            logger.info(f"allocate_nat_gateways: _has_edge_nat_gateways {_has_edge_nat_gateways} for cloud {cloud_provider} for edge {edge_region_idx}")
            if _has_edge_nat_gateways:
                logger.info(f"allocate_nat_gateways: Edge region {edge_region_idx} NAT gateways already present for custid {custid}, edge region idx {edge_region_idx} for {cloud_provider} cloud")
                result = 1
            else:
                # Now create the NAT Gateways.
                lret = create_nat_instances_for_cust_and_region(db_h, custid, edge_region_idx)
                if lret < 0:
                    # This is a failure case.
                    raise Exception(f"allocate_nat_gateways: Failed to create NAT gateways for custid {custid}, edge region idx {edge_region_idx} for {cloud_provider} cloud")
                else:
                    logger.info(f"allocate_nat_gateways: Successfully created NAT gateways for custid {custid}, edge region idx {edge_region_idx} for {cloud_provider} cloud")
                    result = 1
    except Exception as E:
        logger.error("Failed with exception %s, locals %s "
                     "traceback %s" % (str(E.args), str(locals()),
                     str(traceback.format_exc())))
    finally:
        return result

def is_nat_gateway_nlb_needed(dbh, custid, compute_region_idx, edge_region_idx=None):
    from orchestration_service.core.orchestrator_nlb_mgmt import has_nlb_in_region
    """
    :param dbh:
    :param custid:
    :param compute_region_idx:
    :return: nat_gw_needed (True if needed, False if not),
             nlb_needed (True if needed, False if not)
    """
    logger = dbh.logger
    is_nat_gw_supported = False
    is_nlb_supported_in_region = False
    is_nlb_supported_for_customer = False
    gateways_threshold = 0
    is_gateways_threshold_reached_for_nat = False
    is_gateways_threshold_reached_for_nlb = False
    nat_gw_needed = False
    nlb_needed = False

    try:
        # From the compute_region_idx, determine the cloud provider. If the provider platform is Vmware ESXi
        # NAT gateways are not needed.
        cloud_provider = gpcs_get_cloud_type_from_region_idx(dbh, compute_region_idx, custid=custid)
        logger.info(f"Cloud provider for compute region {compute_region_idx} is {cloud_provider}")
        if cloud_provider in [ PROVIDER_VMWARE_ESXI ]:
            logger.info(f"NAT gateways are not need for {cloud_provider}, NLB is needed but would be added separately")
            is_nat_gw_supported = False
            is_nlb_supported_in_region = False
            return

        # Read the "cust_nat_mgmt_table" to get "is_enabled",
        # "is_nlb_supported", "nr_instances" and "nr_instances_nlb"
        # for the custid/region combination (or default values of 0/0).
        sql = "SELECT is_enabled, is_nlb_supported, nr_instances, " \
              "nr_instances_nlb, custid, compute_region_id " \
              "FROM cust_nat_mgmt_table " \
              "WHERE node_type = %s AND custid IN (0, %s) "\
              "AND compute_region_id IN (0, %s) " \
              "ORDER BY custid DESC, compute_region_id DESC LIMIT 1"
        params = (NODE_TYPE_GP_GATEWAY, custid, compute_region_idx)
        logger.info("Trying to execute %s" % str(sql % params))

        # We will just use fetchall
        ret, res = execute_orch_query(dbh, sql, params, "fetchall")
        if ret == False:
            error = ("!!! Fatal! Failed to execute query or no entries "
                     "found. Return status %s/%s !!!" %
                     (str(ret), str(res)))
            raise Exception(error)
        logger.info("Result: %s" % str(res))
        for row in res:
            if isinstance(row, tuple):
                my_is_nat_gw_enabled, my_is_nlb_supported, my_nr_instances, \
                my_nr_instances_nlb, my_custid, my_compute_region_id = row
                logger.info("Found is_nat_gw_enabled as [%s], "
                    "is_nlb_supported as [%s], gw_threshold as [%s], "
                    "gw_threshold_nlb as [%s] for custid [%s] and "
                    "compute_region_id [%s]" %
                    (str(my_is_nat_gw_enabled), str(my_is_nlb_supported),
                    str(my_nr_instances), str(my_nr_instances_nlb),
                    str(my_custid), str(my_compute_region_id)))
                is_nat_gw_supported = my_is_nat_gw_enabled
                gateways_threshold = my_nr_instances
                gateways_threshold_nlb = my_nr_instances_nlb
                is_nlb_supported_in_region = my_is_nlb_supported

        # Read the "cust_master" table to get "is_nlb_supported".
        sql = "SELECT is_nlb_supported FROM cust_master WHERE id = %s"
        params = (custid,)
        logger.info("Trying to execute %s" % str(sql % params))
        ret, res = execute_orch_query(dbh, sql, params, "fetchone")
        if ret == False:
            error = ("!!! Fatal! Failed to execute query or no entries "
                     "found. Return status %s/%s !!!" %
                     (str(ret), str(res)))
            raise Exception(error)
        logger.info("Result: %s" % str(res))
        supported = False
        if isinstance(res, tuple):
            is_nlb_supported_for_customer = res[0]
            logger.info("is_nlb_supported is %s for customer %s" %
                        (str(is_nlb_supported_for_customer), str(custid)))

        # Compute "is_nlb_supported_in_cloud" and "is_nlb_supported_in_cloud"
        # by finding out the cloud provider where we will be deploying
        # the nodes, and if that cloud provider supports NAT/NLB or not.
        logger.info("Cloud provider that will be used: [%s]" % cloud_provider)
        is_nat_gw_supported_in_cloud = is_nat_gw_supported_in_cloud_provider(
                                        logger,
                                        cloud_provider)
        is_nlb_supported_in_cloud = is_nlb_supported_in_cloud_provider(logger,
                                        cloud_provider)
        logger.info("is_nat_gw_supported_in_cloud is %s; "
            "is_nlb_supported_in_cloud is %s for cloud: [%s]"
            % (is_nat_gw_supported_in_cloud, is_nlb_supported_in_cloud,
            cloud_provider))

        # Compute "is_gateways_threshold_reached" by comparing the
        # current no. of gateways in the region for the customer,
        # with the "gateways_threshold" value.
        result, inst_list = find_non_transient_entries_by_custid_and_region(
                                   dbh=dbh,
                                   custid=custid,
                                   region=compute_region_idx,
                                   node_type=NODE_TYPE_GP_GATEWAY,
                                   edge_region_idx=edge_region_idx)
        logger.info("For getting non-upgrade instances from RDS, "
                    "got return code [%s]; instance id list: [%s]"
                    % (result, inst_list))
        if (result == True):
            if (len(inst_list) > gateways_threshold):
                logger.info("Reached threshold for no. of gateways for NAT")
                is_gateways_threshold_reached_for_nat = True
            if (len(inst_list) > gateways_threshold_nlb):
                logger.info("Reached threshold for no. of gateways for NLB")
                is_gateways_threshold_reached_for_nlb = True

        # Check if an NLB is already present.
        has_nlb = has_nlb_in_region(dbh, custid,
                                    compute_region_idx)
        logger.info("Has NLB in region: [%s]" % has_nlb)

        # Check if NAT GW is already present.
        has_nat_gw = has_nat_gateways(dbh, custid,
                                      compute_region_idx)
        logger.info("Has NAT GW in region: [%s]" % has_nat_gw)

        # "nat_gw_needed_now" will be an "AND" of 3 values:
        #     - "is_enabled" from "cust_nat_mgmt_table"
        #     - "is_nat_gw_supported_in_cloud" value that we have computed
        #     - "is_gateways_threshold_reached_for_nat" value that we
        #       have computed
        nat_gw_needed_now = (is_nat_gw_supported and
                             is_nat_gw_supported_in_cloud and
                             is_gateways_threshold_reached_for_nat)
        logger.info("nat_gw_needed_now = [%s]" % nat_gw_needed_now)

        # "nlb_needed_now" will be an "AND" of 4 values:
        #     - "is_nlb_supported" from "cust_master" table
        #     - "is_nlb_supported" from "cust_nat_mgmt_table"
        #     - "is_nlb_supported_in_cloud" value that we have computed
        #     - "is_gateways_threshold_reached_for_nlb" value that we
        #       have computed
        nlb_needed_now = (is_nlb_supported_in_region and
                          is_nlb_supported_for_customer and
                          is_nlb_supported_in_cloud and
                          is_gateways_threshold_reached_for_nlb)
        logger.info("nlb_needed_now = [%s]" % nlb_needed_now)

        # "nlb_needed" will be an OR of "nlb_needed_now" and "has_nlb".
        # That is because we need to use the NLB if one exists for the
        # (custid, compute_region_idx) already, irrespective of the
        # checks if NLB is currently needed or not.
        nlb_needed = (has_nlb or nlb_needed_now)
        logger.info("Will return nlb_needed: [%s]" % nlb_needed)

        # "nat_gw_needed" will be an OR of "nat_gw_needed_now" and
        # "has_nat_gw". That is because we need to use the NLB if
        # one exists for the (custid, compute_region_idx) already,
        # irrespective of the checks if NAT GW is currently needed
        # or not.
        nat_gw_needed = (has_nat_gw or nat_gw_needed_now)
        logger.info("Will return nat_gw_needed: [%s]" % nat_gw_needed)

    except Exception as E:
        logger.error("Failed with exception %s, traceback: %s, locals: %s"
                     % (str(E.args), str(traceback.format_exc()),
                     str(locals())))
    finally:
        return nat_gw_needed, nlb_needed

def find_nr_nat_instances_by_custid_and_region_id(dbh, custid, compute_region_idx):
    """
    :param dbh:
    :param custid:
    :param compute_region_idx:
    :return: success, my_nr_instances, my_nr_nat_gateways_needed
    """

    logger = dbh.logger
    success = False
    my_nr_instances = 3
    my_nr_nat_gateways_needed = 2

    try:
        sql = "SELECT nr_instances, nr_nat_gateways_needed " \
              "FROM cust_nat_mgmt_table " \
              "WHERE node_type = %s and custid in (0, %s) and compute_region_id in (0, %s) " \
              "ORDER BY custid DESC, compute_region_id DESC LIMIT 1"
        params = (NODE_TYPE_GP_GATEWAY, custid, compute_region_idx)
        logger.info("Trying to execute %s" % str(sql % params))

        # We will just use fetchall
        ret, res = execute_orch_query(dbh, sql, params, "fetchall")
        if ret == False:
            error = "!!! Fatal! Failed to execute query or no entries found. Return status %s/%s !!!" % \
                    (str(ret), str(res))
            raise Exception(error)
        logger.info("Result: %s" % str(res))
        for row in res:
            if isinstance(row, tuple):
                my_nr_instances, my_nr_nat_gateways_needed = row
                logger.info("nr_instances is %s and nr_nat_gateways_needed is %s for custid %s "
                            "and compute_region_id %s" % (str(my_nr_instances), str(my_nr_nat_gateways_needed),
                                                          str(custid), str(compute_region_idx)))
        success = True
    except Exception as E:
        logger.error("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                              str(traceback.format_exc()),
                                                                              str(locals())))
    finally:
        return success, my_nr_instances, my_nr_nat_gateways_needed


def find_nat_configs_by_custid_and_region_id(dbh, custid, compute_region_idx):
    """
    :param dbh:
    :param custid:
    :param compute_region_idx:
    :return: success, my_nr_instances, my_nr_nat_gateways_needed, nat_dummy_upgrade_max_time
    """

    logger = dbh.logger
    success = False
    my_nr_instances = 3
    my_nr_nat_gateways_needed = 2
    nat_dummy_upgrade_max_time = 7200
    
    try:
        sql = "SELECT nr_instances, nr_nat_gateways_needed, nat_dummy_upgrade_timeout " \
              "FROM cust_nat_mgmt_table " \
              "WHERE node_type = %s and custid in (0, %s) and compute_region_id in (0, %s) " \
              "ORDER BY custid DESC, compute_region_id DESC LIMIT 1"
        params = (NODE_TYPE_GP_GATEWAY, custid, compute_region_idx)
        logger.info(f"Trying to execute: {str(sql % params)}")

        # We will just use fetchall
        ret, res = execute_orch_query(dbh, sql, params, "fetchall")
        if ret == False:
            error = f"!!! Fatal! Failed to execute query or no entries found. Return status {str(ret)}/{str(res)} !!!"
            raise Exception(error)
        logger.info(f"Result: {str(res)}")
        for row in res:
            if isinstance(row, tuple):
                my_nr_instances, my_nr_nat_gateways_needed, nat_dummy_upgrade_max_time = row
                logger.info(f"Returning: my_nr_instances, my_nr_nat_gateways_needed, nat_dummy_upgrade_timeout: {my_nr_instances}"
                            f", {my_nr_nat_gateways_needed}, {nat_dummy_upgrade_max_time}")
        success = True
    except Exception as E:
        logger.error("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                              str(traceback.format_exc()),
                                                                              str(locals())))
    finally:
        return success, my_nr_instances, my_nr_nat_gateways_needed, nat_dummy_upgrade_max_time


def create_nat_instances_for_cust_and_region(dbh, custid, compute_region_idx):
    """
    :param dbh: DB handler
    :param custid: Customer ID
    :param compute_region_idx: Compute region ID.
    :return: < 0 : Failure, > 0 : Success
    """
    result = False
    logger = dbh.logger
    has_error = False

    try:
        _, _, NR_NAT_INSTANCES = find_nr_nat_instances_by_custid_and_region_id(dbh, custid, compute_region_idx)
        # Create the NAT instance.
        custmodel = CustomerModel(custid=custid, dbh=dbh)
        acct_id = custmodel.get_param("acct_id")
        if acct_id == None:
            has_error = True
            err_msg = "Failed to get the account id for customer %s" % str()
        cust_name = custmodel.get_param("name")

        region_master_model = RegionMasterModel(dbh, edge_location_region_id=compute_region_idx)
        if region_master_model.valid_tuple == False:
            has_error = True
            raise Exception("Failed to get the region master details for compute region idx %s" %
                            str(compute_region_idx))
        compute_region_name = region_master_model.get_param("edge_location_region_name")
        cloud_provider = region_master_model.get_param("cloud_provider")
        theater_id = region_master_model.get_param("primary_theater_id")
        logger.info(f"create_nat_instances_for_cust_and_region for region {compute_region_name}, cloud_provider {cloud_provider}, theater_id {theater_id}")
        #This gets the CAP type override for GPGW or the CAP type for GPAAS-SMALL if no override is found
        pa_capacity_type = get_capacity_type(dbh, custid, compute_region_idx, NODE_TYPE_GP_GATEWAY, license_type="GPAAS-SMALL")[3]
        logger.info(f"create_nat_instances_for_cust_and_region for region {compute_region_name} for cap type {pa_capacity_type}")
        for idx in range(0, NR_NAT_INSTANCES):
            cust_topo_model = CustNodeModel(dbh=dbh)
            cust_topo_model.custid = custid
            cust_topo_model.name = "NAT-%s-%s-%s-%s" % (str(idx), str(acct_id),
                                                        str(compute_region_name),
                                                        str(cloud_provider))
            cust_topo_model.region = compute_region_idx
            cust_topo_model.theater = theater_id
            cust_topo_model.old_region = compute_region_idx
            try:
                pa_cap = re.findall("\d+", pa_capacity_type)[0]
                if int(pa_cap) < 550:
                    cust_topo_model.license_type = 'NAAS-SMALL'
                else:
                    cust_topo_model.license_type = "NAAS"
            except:
                cust_topo_model.license_type = "NAAS-SMALL"
            cust_topo_model.is_hq = 1
            cust_topo_model.is_deleted = 0
            cust_topo_model.node_type = NODE_TYPE_NAT_INSTANCE
            my_log_tag = generate_sample_log_tag(sub_trace_id=logger.get_uuid())
            cust_topo_model.rastro_log_tag = b64encode(my_log_tag)
            lret = cust_topo_model.save(dbh)
            if lret == False:
                has_error = True
                err_msg = "Failed to add the cust topology entry for NAT instance %s" % str(cust_topo_model.name)
                raise Exception(err_msg)
            else:
                logger.info("Added the cust topology entry for NAT instance %s" % str(cust_topo_model.name))

        result = True
    except Exception as E:
        logger.error("Failed with exception %s, traceback %s, locals %s" % (str(E.args),
                                                                            str(traceback.format_exc()),
                                                                            str(locals())))
    finally:
        if has_error:
            return -1
        return result


def delete_nat_instances_by_custid_and_region_id(dbh, custid, compute_region_idx):
    """
    :param dbh:
    :param custid:
    :param compute_region_idx:
    :return:  True if success, False on failure
    """
    success = False
    logger = dbh.logger
    try:
        if custid == None or compute_region_idx == None:
            raise Exception("Fatal, cannot continue. Either custid or compute region idx is not specified.")

        sql = "UPDATE cust_topology set is_deleted = 1 where custid = %s and node_type = %s and region = %s"
        params = (custid, NODE_TYPE_NAT_INSTANCE, compute_region_idx)
        cursor = dbh.get_cursor()
        logger.info("Trying to executing %s" % (str(sql % params)))
        try:
            cursor.execute(sql, params)
            dbh.cursorclose(cursor)
        except Exception as ex:
            logger.error("Failed with exception %s" % str(ex.args))
            dbh.cursorclose(cursor)
            raise Exception(dbh.error)

        # Mark the status as success.
        success = True
    except Exception as E:
        logger.error("Failed with exception %s, traceback %s, locals %s" % (str(E.args),
                                                                            str(traceback.format_exc()),
                                                                            str(locals())))
    finally:
        return success


def has_nat_gateways(dbh, custid, compute_region_idx, edge_region_idx=None, cloud_provider=PROVIDER_GCP):
    '''
        Check if the given custid and compute region idx already have NAT gateways.
        :param custid:
        :param compute_region_idx:
        :return: True/False
    '''
    # Inline import
    from libs.model.custnodemodel import find_topology_entries_by_custid_and_region_id

    has_nat_gateway = False
    logger = dbh.logger
    logger.info(f"has_nat_gateways: compute region {compute_region_idx}, edge region {edge_region_idx} for custid {custid} for cloud {cloud_provider}")
    # Special handling for CNAT in GCP/OCI cloud wherein we create a new cust_topology entry for each NATGW per edge region
    if edge_region_idx and str(compute_region_idx) != str(edge_region_idx) and (cloud_provider == PROVIDER_OCI or (cloud_provider == PROVIDER_GCP and dbh.is_gcp_cnat_supported(cloud_provider, custid, compute_region_idx))):
        logger.info(f"has_nat_gateways: Check for edge NAT gateway entry in edge region {edge_region_idx} for custid {custid} for cloud {cloud_provider}")
        result, topology_id_list = find_topology_entries_by_custid_and_region_id(dbh=dbh, custid=custid, compute_region_idx=edge_region_idx, node_type=NODE_TYPE_NAT_INSTANCE)
        if result == True:
            if len(topology_id_list):
                logger.info(f"has_nat_gateways: Topology ID for NAT gateway are {topology_id_list}")
                has_nat_gateway = True
        else:
            logger.error(f"has_nat_gateways: Failed to lookup NAT instances for custid {custid} and region {edge_region_idx} from the DB")

        logger.info(f"has_nat_gateways: has_nat_gateway is set to {has_nat_gateway}")
        return has_nat_gateway

    result, topology_id_list = find_topology_entries_by_custid_and_region_id(dbh=dbh,
                                                                             custid=custid,
                                                                             compute_region_idx=compute_region_idx,
                                                                             node_type=NODE_TYPE_NAT_INSTANCE)
    if result == True:
        if len(topology_id_list):
            logger.info("Topology ID for NAT gateway are %s" % str(topology_id_list))
            has_nat_gateway = True
    else:
        raise Exception("Failed to lookup NAT instances for custid %s and region %s from the DB" %
                                                            (str(custid), str(compute_region_idx)))

    return has_nat_gateway

def is_nat_gateway_ilb_needed(dbh, custid, compute_region_idx, edge_region_idx=None):
    """
    :param edge_region_idx:
    :param dbh:
    :param custid:
    :param compute_region_idx:
    :return: nat_gw_needed (True if needed, False if not),
             nlb_needed (True if needed, False if not)
    """
    logger = dbh.logger
    is_5g_enabled_in_region = False
    is_ilb_required = False
    try:
        cloud_provider = gpcs_get_cloud_type_from_region_idx(dbh, compute_region_idx, custid=custid)
        logger.info(f"Cloud provider for compute region {compute_region_idx} is {cloud_provider}")
        if cloud_provider in [ PROVIDER_GCP ]:
            res = dbh.get_enable_5g(custid, edge_region_idx)
            if res is not None and res.ok:
                logger.info(f"During NAT check Returned 5G result for customer {custid} and "
                                 f"region {edge_region_idx} is: {res.result[0]}")
                res_val = res.result[0]
                if res_val and str(res_val) != "0":
                    is_5g_enabled_in_region = True

        is_ilb_required = is_ilb_required or is_5g_enabled_in_region
    except Exception as E:
        logger.error("Failed with exception during check for is 5g enabled %s, traceback: %s, locals: %s"
                     % (str(E.args), str(traceback.format_exc()),
                     str(locals())))
    finally:
        return is_ilb_required
