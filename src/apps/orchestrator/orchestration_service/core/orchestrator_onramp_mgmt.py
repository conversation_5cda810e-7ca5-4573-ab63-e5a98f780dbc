from libs.cfg import *
import math
import traceback
import json
from libs.model.custnodemodel import CustNodeModel
from libs.common.shared.sys_utils import NODE_TYPE_ONRAMP_REMOTE_NETWORK, NODE_TYPE_REMOTE_NET
from libs.apis.region_master_api import get_cloud_native_location_name_from_region_id



def get_onramp_rn_count(db_h, custnode):
    """
    Calculate the number of onramp remote network (RN) nodes needed based on
    VLAN attachment bandwidth and per-RN capacity.
    """
    logger = db_h.logger
    num_of_rns_needed = -1
    try:
        logger.info(f"get_onramp_rn_count for node:{custnode.name}, interconnect-name: {custnode.interconnect_name}")
        cloud_native_compute_region = get_cloud_native_location_name_from_region_id(db_h, custnode.region)
        res = db_h.get_tenant_interconnect_vpc_details(custnode.custid, cloud_native_compute_region,
                                                       custnode.interconnect_name)
        if res is not None and res.ok and res.result[0] is not None:
            interconnect_bandwidth = int(res.result[2])
        else:
            logger.warning(f"Invalid or missing interconnect_bandwidth for node {custnode.name}")
            return num_of_rns_needed

        # custnode.qos has the bandwidth that can be handled by single RN node
        # license_sku_type_mapping table: sku - FWAAS_INTERCONNECT
        if interconnect_bandwidth < custnode.qos:
            num_of_rns_needed = 2
        else:
            num_of_rns_needed = math.ceil(interconnect_bandwidth / int(custnode.qos)) + 1
        logger.info(f"Number of Interconnect onRamp RNs needed: {num_of_rns_needed}")

    except Exception as e:
        logger.error(f"Failed to calculate onramp RN count for node {custnode.name}, "
                     f"failed with exception, {str(e.args)}, traceback: {str(traceback.format_exc())}")
    return int(num_of_rns_needed)


def allocate_onramp_rn_nodes(db_h, custnode, num_rn_nodes):
    """
    Allocate the specified number of onramp remote network (RN) nodes.

    Args:
        custnode: Original customer node object
        num_rn_nodes: Number of RN nodes to create

    Returns:
        number of RN nodes created
    """
    rn_nodes_created = 0
    logger = db_h.logger
    try:
        custid = custnode.custid
        logger.info(f"Allocating {num_rn_nodes} onramp RN nodes for custid {str(custid)}, in region {str(custnode.region)}")

        # Create each RN node
        for rn_index in range(num_rn_nodes):
            try:
                # Create new RN node
                lret = create_onramp_rn_child_node(db_h, custnode, rn_index)
                if lret:
                    rn_nodes_created += 1

            except Exception as e:
                logger.error(f"Failed to create onramp RN node at index {rn_index}: {str(e)}")
                return -1

        return rn_nodes_created

    except Exception as e:
        logger.error(f"Failed to allocate onramp RN nodes: {str(e)}")
        return rn_nodes_created


def create_onramp_rn_child_node(db_h, cust_topo_parent_node, rn_index):
    """
    Create a single onramp RN node.

    Args:
        custnode: Original customer node
        rn_index: Index of this RN node (0-based)
        compute_region_idx: Compute region index

    Returns: True if created successfully
    """
    logger = db_h.logger
    success = False
    try:

        # Create new RN node
        cust_topo_model = CustNodeModel(dbh=db_h)

        cust_topo_model.custid = cust_topo_parent_node.custid
        cust_topo_model.name = f"{cust_topo_parent_node.name}-child-{rn_index}"
        cust_topo_model.node_type = cust_topo_parent_node.node_type
        cust_topo_model.alt_node_type = NODE_TYPE_ONRAMP_REMOTE_NETWORK
        cust_topo_model.is_hq = cust_topo_parent_node.is_hq
        cust_topo_model.region = cust_topo_parent_node.region
        cust_topo_model.old_region = cust_topo_parent_node.old_region
        cust_topo_model.theater = cust_topo_parent_node.theater
        #cust_topo_model.is_dynamic_node = 1
        cust_topo_model.panorama_job_id = cust_topo_parent_node.panorama_job_id
        cust_topo_model.curr_qos = cust_topo_parent_node.curr_qos
        cust_topo_model.rastro_log_tag = cust_topo_parent_node.rastro_log_tag
        cust_topo_model.license_type = cust_topo_parent_node.license_type
        cust_topo_model.transport_type = cust_topo_parent_node.transport_type
        cust_topo_model.interconnect_name = cust_topo_parent_node.interconnect_name
        cust_topo_model.is_deleted = 0
        cust_topo_model.parent_id = cust_topo_parent_node.id

        lret = cust_topo_model.save(db_h)
        if not lret:
            logger.error(f"Failed to create child onramp node for {cust_topo_parent_node.name}, index: {rn_index}")
            success = False
        else:
            logger.info(f"Added cust_topology entry for {cust_topo_model.name}, index: {rn_index}")
            success = True

    except Exception as e:
        logger.error(f"Exception creating child onramp RN node at index {rn_index}, exception: {str(e.args)}, "
                     f"traceback: {str(traceback.format_exc())}, locals: {str(locals())}")
    return success


def delete_child_onramp_rn(db_h, cust_topo_id, custid, compute_region_idx):
    """
    Delete onramp ilb instance for remote networks
    :param db_h:
    :param custid:
    :param compute_region_idx:
    :return:  True if success, False on failure
    """
    success = False
    logger = db_h.logger
    try:
        if custid == None or compute_region_idx == None:
            raise Exception("Fatal, cannot continue. Either custid or compute region idx is not specified.")

        sql = "UPDATE cust_topology SET is_deleted = 1 WHERE custid = %s " \
              "AND node_type = %s AND region = %s AND parent_id = %s"
        params = (custid, NODE_TYPE_REMOTE_NET, compute_region_idx,cust_topo_id)
        cursor = db_h.get_cursor()
        logger.info("Trying to execute %s" % (str(sql % params)))
        try:
            cursor.execute(sql, params)
            db_h.cursorclose(cursor)
        except Exception as ex:
            logger.error("Failed with exception %s" % str(ex.args))
            db_h.cursorclose(cursor)
            raise Exception(db_h.error)

        # Mark the status as success.
        success = True
    except Exception as E:
        logger.error(f"Failed with exception: {str(E.args)}, traceback: {str(traceback.format_exc())}, locals: {str(locals())}")
    finally:
        return success
