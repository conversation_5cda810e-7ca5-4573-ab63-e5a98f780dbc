import traceback
import json
from libs.model.custnodemodel import CustNodeModel
from libs.model.custnodemodel import find_topology_entries_by_custid_and_region_id
from libs.model.regionmastermodel import RegionMasterModel
from libs.common.shared.rastro.rastro_logging_wrapper import generate_sample_log_tag
from libs.common.shared.py3_utils import b64encode
from libs.model.instancemodel import InstanceModel, is_ingress_ip_reduction_enabled
from libs.model.custmodel import CustomerModel
from libs.model.execute_orch_query import execute_orch_query
from libs.common.shared.sys_utils import (NODE_TYPE_NLB_INSTANCE, PROVIDER_VMWARE_ESXI, NODE_TYPE_SASE_PRIV_REGION_LB,
                                          NODE_TYPE_GP_GATEWAY, NODE_TYPE_SERVICE_ILB, NODE_TYPE_REMOTE_NET)
from libs.apis.region_master_api import gpcs_get_cloud_type_from_region_idx

# Load balancer configuration options
forwarding_rule_protocol_dict = {
    0: 'L3_DEFAULT',
    1: 'TCP',
    2: 'UDP'
}

backend_service_protocol_dict = {
    0: 'UNSPECIFIED',
    1: 'TCP',
    2: 'UDP'
}

session_affinity_dict = {
    0: 'NONE',                # 5-tuple hash of source IP address,
                              # source port, protocol, destination
                              # IP address, and destination port
    1: 'CLIENT_IP',           # 2-tuple hash of source IP address and
                              # destination IP address
    2: 'CLIENT_IP_PROTO',     # 3-tuple hash of source IP address,
                              # destination IP address, and protocol
    3: 'CLIENT_IP_PORT_PROTO' # 5-tuple hash of source IP address, source
                              # port, protocol, destination IP address
                              # and destination port
}

conn_pers_on_unhealthy_backends_dict = {
    0: 'DEFAULT_FOR_PROTOCOL',
    1: 'NEVER_PERSIST',
    2: 'ALWAYS_PERSIST'
}

health_check_protocol_dict = {
    0: 'HTTP',
    1: 'HTTPS',
    2: 'TCP'
}

ip_protocol_dict = {
    'HTTP': 'TCP',
    'HTTPS': 'TCP',
    'TCP': 'TCP'
}

# Default values:
#    forwarding_rule_protocol:           'L3_DEFAULT'
#    backend_service_protocol:           'UNSPECIFIED'
#    session_affinity:                   'CLIENT_IP'
#    conn_pers_on_unhealthy_backends:    'ALWAYS_PERSIST'
#    health_check_protocol:              'HTTP'
#    health_check_port:                  8000
#    health_check_interval:              2
#    health_check_timeout:               2
#    health_check_unhealthy_threashold:  2
#    health_check_healthy_threshold:     2
#    is_strong_session_affinity_supported 1
DEFAULT_FW_RULE_PROTOCOL = 0
DEFAULT_BACKEND_SERV_PROTOCOL = 0
DEFAULT_SESSION_AFFINITY = 1
DEFAULT_NGPA_PROTO_SESSION_AFFINITY = 3
DEFAULT_CONN_PERS_ON_UNHEALTHY_BACKENDS = 2
DEFAULT_HC_PROTOCOL = 0
DEFAULT_HC_PORT = 8000
DEFAULT_HC_INTERVAL = 2
DEFAULT_HC_TIMEOUT = 2
DEFAULT_HC_UNHEALTHY_THRESHOLD = 2
DEFAULT_HC_HEALTHY_THRESHOLD = 2
DEFAULT_IS_STRONG_SESSION_AFFINITY_SUPPORTED = 1
DEFAULT_NGPA_NLB_IDLE_TIMEOUT = 1800

def allocate_nlb(db_h, custid, compute_region_idx):
    """
    :param db_h:
    :param custid:
    :param compute_region_idx:
    :return: 1 on 'successfully added NLB or NLB already present',
             -1 on 'error'
    """
    result = -1
    logger = db_h.logger
    try:
        # We have already completed checks that indicate that
        # we need NLB in the region for this customer.
        # Check if NLB is already present.
        # If not, allocate new NLB.
        _has_nlb = has_nlb_in_region(db_h, custid,
                                     compute_region_idx)
        if _has_nlb:
            logger.info("NLB already present, continuing")
            result = 1

        else:
            # Now create the NLB.
            lret = create_nlb_instance_for_cust_and_region(db_h,
                                   custid, compute_region_idx)
            if lret < 0:
                # This is a failure case.
                raise Exception("Failed to create NLB instance. "
                                "Cannot progress further.")
            else:
                logger.info("Successfully created an NLB "
                         "instance for custid %s and compute "
                         "region idx %s" % (str(custid),
                         str(compute_region_idx)))
                result = 1
    except Exception as E:
        logger.error("Failed with exception %s, locals %s "
                     "traceback %s" % (str(E.args), str(locals()),
                     str(traceback.format_exc())))
    finally:
        return result

def allocate_ilb(db_h, custid, compute_region_idx):
    """
    :param db_h:
    :param custid:
    :param compute_region_idx:
    :return: 1 on 'successfully added NLB or NLB already present',
             -1 on 'error'
    """
    result = -1
    logger = db_h.logger
    try:
        # We have already completed checks that indicate that
        # we need NLB in the region for this customer.
        # Check if ILB is already present.
        # If not, allocate new ILB.
        _has_ilb = has_ilb_in_region(db_h, custid,
                                     compute_region_idx)
        if _has_ilb:
            logger.info("ILB already present, continuing")
            result = 1

        else:
            # Now create the ILB.
            lret = create_ilb_instance_for_cust_and_region(db_h,
                                   custid, compute_region_idx)
            if lret < 0:
                # This is a failure case.
                raise Exception("Failed to create NLB instance. "
                                "Cannot progress further.")
            else:
                logger.info("Successfully created an NLB "
                         "instance for custid %s and compute "
                         "region idx %s" % (str(custid),
                         str(compute_region_idx)))
                result = 1
    except Exception as E:
        logger.error("Failed with exception %s, locals %s "
                     "traceback %s" % (str(E.args), str(locals()),
                     str(traceback.format_exc())))
    finally:
        return result

def create_nlb_instance_for_cust_and_region(dbh,
                                            custid,
                                            compute_region_idx):
    """
    :param dbh: DB handler
    :param custid: Customer ID
    :param compute_region_idx: Compute region ID.
    :return: < 0 : Failure, > 0 : Success
    """
    result = False
    logger = dbh.logger
    has_error = False
    node_type = None

    try:
        # Create the NLB instance.
        custmodel = CustomerModel(custid=custid, dbh=dbh)
        acct_id = custmodel.get_param("acct_id")
        if acct_id == None:
            has_error = True
            err_msg = "Failed to get the account id for customer %s" % str()
        cust_name = custmodel.get_param("name")
        node_type = NODE_TYPE_NLB_INSTANCE
        region_master_model = RegionMasterModel(dbh,
                                  edge_location_region_id=compute_region_idx)
        if region_master_model.valid_tuple == False:
            has_error = True
            raise Exception("Failed to get the region master details for "
                            "compute region idx %s" % str(compute_region_idx))
        compute_region_name = region_master_model.get_param(
                                  "edge_location_region_name")
        cloud_provider = region_master_model.get_param("cloud_provider")
        theater_id = region_master_model.get_param("primary_theater_id")
        cust_topo_model = CustNodeModel(dbh=dbh)
        cust_topo_model.custid = custid
        cust_topo_model.name = "NLB-%s-%s-%s" % (str(acct_id),
                                                 str(compute_region_name),
                                                 str(cloud_provider))
        cust_topo_model.region = compute_region_idx
        cust_topo_model.theater = theater_id
        cust_topo_model.old_region = compute_region_idx
        cust_topo_model.license_type = "NAAS"
        cust_topo_model.is_hq = 1
        cust_topo_model.is_deleted = 0
        cust_topo_model.node_type = node_type
        my_log_tag = generate_sample_log_tag(sub_trace_id=logger.get_uuid())
        cust_topo_model.rastro_log_tag = b64encode(my_log_tag)
        lret = cust_topo_model.save(dbh)
        if lret == False:
            has_error = True
            err_msg = "Failed to add the cust topology entry for " \
                      "NLB instance %s" % str(cust_topo_model.name)
            raise Exception(err_msg)
        else:
            logger.info("Added the cust topology entry for NLB instance %s"
                        % str(cust_topo_model.name))

        result = True
    except Exception as E:
        logger.error("Failed with exception %s, traceback %s, locals %s" % (
                     str(E.args), str(traceback.format_exc()), str(locals())))
    finally:
        if has_error:
            return -1
        return result

def create_ilb_instance_for_cust_and_region(dbh,
                                            custid,
                                            compute_region_idx):
    """
    :param dbh: DB handler
    :param custid: Customer ID
    :param compute_region_idx: Compute region ID.
    :return: < 0 : Failure, > 0 : Success
    """
    result = False
    logger = dbh.logger
    has_error = False
    node_type = None

    try:
        # Create the ILB instance.
        custmodel = CustomerModel(custid=custid, dbh=dbh)
        acct_id = custmodel.get_param("acct_id")
        if acct_id is None:
            has_error = True
            err_msg = "Failed to get the account id for customer %s" % str()
        cust_name = custmodel.get_param("name")
        node_type = NODE_TYPE_SERVICE_ILB
        region_master_model = RegionMasterModel(dbh,
                                  edge_location_region_id=compute_region_idx)
        if not region_master_model.valid_tuple:
            has_error = True
            raise Exception("Failed to get the region master details for "
                            "compute region idx %s" % str(compute_region_idx))
        compute_region_name = region_master_model.get_param(
                                  "edge_location_region_name")
        cloud_provider = region_master_model.get_param("cloud_provider")
        theater_id = region_master_model.get_param("primary_theater_id")
        cust_topo_model = CustNodeModel(dbh=dbh)
        cust_topo_model.custid = custid
        cust_topo_model.name = "SVC-ILB-%s-%s-%s" % (str(acct_id),
                                                 str(compute_region_name),
                                                 str(cloud_provider))
        cust_topo_model.region = compute_region_idx
        cust_topo_model.theater = theater_id
        cust_topo_model.old_region = compute_region_idx
        cust_topo_model.license_type = "NAAS"
        cust_topo_model.is_hq = 1
        cust_topo_model.is_deleted = 0
        cust_topo_model.node_type = node_type
        cust_topo_model.alt_node_type = NODE_TYPE_GP_GATEWAY
        my_log_tag = generate_sample_log_tag(sub_trace_id=logger.get_uuid())
        cust_topo_model.rastro_log_tag = b64encode(my_log_tag)
        lret = cust_topo_model.save(dbh)
        if not lret:
            has_error = True
            err_msg = "Failed to add the cust topology entry for " \
                      "ILB instance %s" % str(cust_topo_model.name)
            raise Exception(err_msg)
        else:
            logger.info("Added the cust topology entry for ILB instance %s"
                        % str(cust_topo_model.name))

        result = True
    except Exception as E:
        logger.error("Failed with exception %s, traceback %s, locals %s" % (
                     str(E.args), str(traceback.format_exc()), str(locals())))
    finally:
        if has_error:
            return -1
        return result

def delete_nlb_instance_by_custid_and_region_id(dbh, custid, compute_region_idx, node_type=NODE_TYPE_NLB_INSTANCE):
    """
    :param dbh:
    :param custid:
    :param compute_region_idx:
    :param node_type: Default is NLB instance
    :return:  True if success, False on failure
    """
    success = False
    logger = dbh.logger
    try:
        if custid == None or compute_region_idx == None:
            raise Exception("Fatal, cannot continue. Either custid or "
                            "compute region idx is not specified.")

        sql = "UPDATE cust_topology SET is_deleted = 1 WHERE custid = %s " \
              "AND node_type = %s AND region = %s"
        params = (custid, node_type, compute_region_idx)

        if node_type == NODE_TYPE_SERVICE_ILB:
            sql = "UPDATE cust_topology SET is_deleted = 1 WHERE custid = %s " \
                  "AND node_type = %s AND region = %s AND alt_node_type = %s"
            params = (custid, node_type, compute_region_idx, NODE_TYPE_GP_GATEWAY)

        cursor = dbh.get_cursor()
        logger.info("Trying to execute %s" % (str(sql % params)))
        try:
            cursor.execute(sql, params)
            dbh.cursorclose(cursor)
        except Exception as ex:
            logger.error(f"Failed with exception {str(ex.args)}")
            dbh.cursorclose(cursor)
            raise Exception(dbh.error)

        # Mark the status as success.
        success = True
    except Exception as E:
        logger.error("Failed with exception %s, traceback %s, locals %s" % (
                     str(E.args), str(traceback.format_exc()), str(locals())))
    finally:
        return success


def allocate_interconnect_onramp_ilb(db_h, custid, compute_region_idx, custnode):
    """
    :param db_h:
    :param custid:
    :param compute_region_idx:
    :param custnode: cust_topology node of the parent
    :return: 1 on 'successfully added NLB or NLB already present',
             -1 on 'error'
    """
    result = -1
    logger = db_h.logger
    try:
        _has_ilb = has_interconnect_onramp_ilb_in_region(db_h, custid, compute_region_idx)
        if _has_ilb:
            logger.info("ILB already present, continuing")
            result = 1

        else:
            # Now create the NLB.
            lret = create_int_onramp_ilb_instance_by_custid_and_region_id(db_h, custid, compute_region_idx, custnode)
            if lret < 0:
                # This is a failure case.
                raise Exception("Failed to create onramp ILB instance, cannot progress further.")
            else:
                logger.info(f"Successfully created an ILB instance for custid {str(custid)} and compute region idx {str(compute_region_idx)}")
                result = 1
    except Exception as E:
        logger.error(f"Failed with exception: {str(E.args)}, locals: {str(locals())}, traceback: {str(traceback.format_exc())}")
    finally:
        return result


def create_int_onramp_ilb_instance_by_custid_and_region_id(dbh, custid, compute_region_idx, custnode_parent):
    """
    Create ILB for onramp remote networks
    :param dbh: DB handler
    :param custid: Customer ID
    :param compute_region_idx: Compute region ID.
    :return: < 0 : Failure, > 0 : Success
    """
    result = False
    logger = dbh.logger
    has_error = False
    node_type = None

    try:
        # Create the onramp ILB instance.
        custmodel = CustomerModel(custid=custid, dbh=dbh)
        acct_id = custmodel.get_param("acct_id")
        if acct_id is None:
            has_error = True
            err_msg = "Failed to get the account id for customer %s" % str()
        cust_name = custmodel.get_param("name")
        node_type = NODE_TYPE_SERVICE_ILB
        region_master_model = RegionMasterModel(dbh, edge_location_region_id=compute_region_idx)
        if region_master_model.valid_tuple is False:
            has_error = True
            raise Exception("Failed to get the region master details for "
                            "compute region idx %s" % str(compute_region_idx))
        compute_region_name = region_master_model.get_param("edge_location_region_name")
        cloud_provider = region_master_model.get_param("cloud_provider")
        theater_id = region_master_model.get_param("primary_theater_id")
        cust_topo_model = CustNodeModel(dbh=dbh)
        cust_topo_model.custid = custid
        cust_topo_model.name = f"ILB-ONRAMP-{str(acct_id)}-{str(compute_region_name)}-{str(cloud_provider)}"
        cust_topo_model.region = compute_region_idx
        cust_topo_model.theater = theater_id
        cust_topo_model.old_region = compute_region_idx
        cust_topo_model.license_type = custnode_parent.license_type
        cust_topo_model.is_hq = 1
        cust_topo_model.is_deleted = 0
        cust_topo_model.node_type = node_type
        cust_topo_model.alt_node_type = NODE_TYPE_REMOTE_NET
        cust_topo_model.transport_type = custnode_parent.transport_type
        cust_topo_model.interconnect_name = custnode_parent.interconnect_name
        cust_topo_model.parent_id = custnode_parent.id
        my_log_tag = generate_sample_log_tag(sub_trace_id=logger.get_uuid())
        cust_topo_model.rastro_log_tag = b64encode(my_log_tag)
        lret = cust_topo_model.save(dbh)
        if lret is False:
            has_error = True
            err_msg = f"Failed to add the cust topology entry for onramp ILB instance {str(cust_topo_model.name)}"
            raise Exception(err_msg)
        else:
            logger.info(f"Added the cust topology entry for Onramp ILB instance {str(cust_topo_model.name)}")
        result = True
    except Exception as E:
        logger.error(f"Failed with exception: {str(E.args)}, traceback: {str(traceback.format_exc())}, locals: {str(locals())}")
    finally:
        if has_error:
            return -1
        return result


def delete_onramp_ilb_instance_by_custid_and_region_id(db_h, cust_topo_parent_id, custid, compute_region_idx):
    """
    Delete onramp ilb instance for remote networks
    :param db_h:
    :param custid:
    :param compute_region_idx:
    :param cust_topo_parent_id: parent cust_topology ID
    :return:  True if success, False on failure
    """
    success = False
    logger = db_h.logger
    try:
        if custid == None or compute_region_idx == None:
            raise Exception("Fatal, cannot continue. Either custid or compute region idx is not specified.")

        sql = "UPDATE cust_topology SET is_deleted = 1 WHERE custid = %s " \
              "AND node_type = %s AND region = %s AND parent_id = %s AND alt_node_type = %s"
        params = (custid, NODE_TYPE_SERVICE_ILB, compute_region_idx, str(cust_topo_parent_id), NODE_TYPE_REMOTE_NET)
        cursor = db_h.get_cursor()
        logger.info("Trying to execute %s" % (str(sql % params)))
        try:
            cursor.execute(sql, params)
            db_h.cursorclose(cursor)
        except Exception as ex:
            logger.error("Failed with exception %s" % str(ex.args))
            db_h.cursorclose(cursor)
            raise Exception(db_h.error)

        # Mark the status as success.
        success = True
    except Exception as E:
        logger.error(f"Failed with exception: {str(E.args)}, traceback: {str(traceback.format_exc())}, locals: {str(locals())}")
    finally:
        return success


def has_nlb_in_region(dbh, custid, compute_region_idx):
    '''
        Check if the given (custid, compute_region_idx) already has NLB.
        :param custid:
        :param compute_region_idx:
        :return: True if there is a NLB in the egion, False otherwise.
    '''
    # Inline import
    has_nlb = False
    logger = dbh.logger

    # Get the provider from the compute_region_idx
    cloud_provider = gpcs_get_cloud_type_from_region_idx(dbh,
                                                        compute_region_idx, custid=custid)
    logger.info(f"Cloud provider for compute region {compute_region_idx} is {cloud_provider}")
    if cloud_provider in [PROVIDER_VMWARE_ESXI]:
        node_type = NODE_TYPE_SASE_PRIV_REGION_LB
    else:
        node_type = NODE_TYPE_NLB_INSTANCE

    result, topology_id_list = find_topology_entries_by_custid_and_region_id(
                                   dbh=dbh,
                                   custid=custid,
                                   compute_region_idx=compute_region_idx,
                                   node_type=node_type)
    if result == True:
        if len(topology_id_list):
            logger.info("Topology ID for NLB are %s" % str(topology_id_list))
            has_nlb = True
    else:
        raise Exception("Failed to lookup NLB instance for custid %s and "
                        "region %s from the DB" %
                        (str(custid), str(compute_region_idx)))

    return has_nlb

def has_ilb_in_region(dbh, custid, compute_region_idx):
    """
        Check if the given (custid, compute_region_idx) already has NLB.
        :param custid:
        :param compute_region_idx:
        :return: True if there is a NLB in the egion, False otherwise.
    """
    # Inline import
    has_ilb = False
    logger = dbh.logger

    # Get the provider from the compute_region_idx
    cloud_provider = gpcs_get_cloud_type_from_region_idx(dbh,
                                                        compute_region_idx, custid=custid)
    logger.info(f"Cloud provider for compute region {compute_region_idx} is {cloud_provider}")

    node_type = NODE_TYPE_SERVICE_ILB
    alt_node_type = NODE_TYPE_GP_GATEWAY
    result, topology_id_list = find_topology_entries_by_custid_and_region_id(
                                   dbh=dbh,
                                   custid=custid,
                                   compute_region_idx=compute_region_idx,
                                   node_type=node_type,
                                   alt_node_type=alt_node_type)
    if result:
        if len(topology_id_list):
            logger.info(f"Topology ID for NLB are: {str(topology_id_list)}")
            has_ilb = True
    else:
        raise Exception(f"Failed to lookup NLB instance for custid: {str(custid)} "
                        f"and region: {str(compute_region_idx)} from the DB")

    return has_ilb


def has_interconnect_onramp_ilb_in_region(dbh, custid, compute_region_idx):
    '''
        Check if the given (custid, compute_region_idx) already has NLB.
        :param custid:
        :param compute_region_idx:
        :return: True if there is a NLB in the egion, False otherwise.
    '''
    # Inline import
    has_onramp_ilb = False
    logger = dbh.logger
    node_type = NODE_TYPE_SERVICE_ILB
    alt_node_type = NODE_TYPE_REMOTE_NET
    result, topology_id_list = find_topology_entries_by_custid_and_region_id(
                                   dbh=dbh,
                                   custid=custid,
                                   compute_region_idx=compute_region_idx,
                                   node_type=node_type,
                                   alt_node_type=alt_node_type)
    if result == True:
        if len(topology_id_list):
            logger.info(f"Topology ID for OnRamp ILB: {str(topology_id_list)}")
            has_onramp_ilb = True
    else:
        raise Exception(f"Failed to lookup OnRamp ILB instance for custid: {str(custid)}, compute_region: {str(compute_region_idx)}")

    return has_onramp_ilb


def get_nlb_ip_address_map(dbh, custid, compute_region_idx):
    '''
        Return the compute IP and egress_ip_list for the NLB instance
        in the given (custid, compute_region_idx).
        :param custid:
        :param compute_region_idx:
        :return:
    '''
    nlb_ip_address_map = {}
    logger = dbh.logger
    sql = ("SELECT public_ip, egress_ip_list FROM instance_master WHERE "
           "custid = %s AND compute_region_idx = %s AND node_type = %s"
           % (custid, compute_region_idx, NODE_TYPE_NLB_INSTANCE))
    logger.info("SQL: %s" % sql)
    ret, result = execute_orch_query(dbh, sql, None, "fetchone")
    logger.info("Return from RDS: %s: %s" % (ret, result))
    if ret is False or result is None:
        logger.info("Failed to get info from RDS")
        return nlb_ip_address_map
    public_ip, egress_ip_list = str(result[0]), str(result[1])
    logger.info("Public IP: %s" % public_ip)
    logger.info("Egress IP List: %s" % egress_ip_list)

    # Copy over "egress_ip_list" into nlb_ip_address_map
    nlb_ip_address_map = {}
    if ((egress_ip_list != None) and (egress_ip_list != 'None') and
        (egress_ip_list != 'NULL')):
        egress_ip_list_obj = json.loads(egress_ip_list)
        if isinstance(egress_ip_list_obj, dict):
            logger.info("Egress IP List is a valid dictionary")
            nlb_ip_address_map = egress_ip_list_obj.copy()
        else:
            logger.error("Invalid value for egress_ip_list: %s"
                          % egress_ip_list_obj)
    else:
        logger.info("Empty egress IP List")

    # Add the compute_region_idx:Public IP into nlb_ip_address_map
    nlb_ip_address_map[str(compute_region_idx)] = public_ip

    logger.info("NLB IP Address map: %s" %
                str(nlb_ip_address_map))

    return nlb_ip_address_map


def get_nlb_public_ip_address(dbh, custid, compute_region_idx, node_type=NODE_TYPE_NLB_INSTANCE):
    """
        Return the public_ip for the NLB instance in the given
        (custid, compute_region_idx) combination for a given node type of load balancer
        :param custid:
        :param compute_region_idx:
        :return:
    """
    public_ip = None
    logger = dbh.logger
    sql = ("SELECT public_ip FROM instance_master WHERE "
           "custid = %s AND compute_region_idx = %s AND node_type = %s"
           % (custid, compute_region_idx, node_type))
    logger.info("SQL: %s" % sql)
    ret, result = execute_orch_query(dbh, sql, None, "fetchone")
    logger.info("Return from RDS: %s: %s" % (ret, result))
    if ret is False or result is None:
        logger.info("Failed to get info from RDS; or result was None")
        return public_ip
    public_ip = str(result[0])
    logger.info(f"Return {node_type} Public IP:{public_ip}")

    return public_ip

def get_is_ngpa_protocol_enabled(dbh, custid):
    '''
        Return is_ngpa_protocol_enabled for the custid
        :param custid:
        :return:
    '''
    is_ngpa_protocol_enabled = None
    logger = dbh.logger
    sql = ("SELECT is_ngpa_protocol_enabled FROM cust_master WHERE "
           "custid = %s"
           % (custid))
    logger.info("SQL: %s" % sql)
    ret, result = execute_orch_query(dbh, sql, None, "fetchone")
    logger.info("Return from RDS: %s: %s" % (ret, result))
    if ret is False or result is None:
        logger.info("Failed to get info from RDS cust_master; or result was None")
        return is_ngpa_protocol_enabled
    is_ngpa_protocol_enabled = str(result[0])
    logger.info("Return is_ngpa_protocol_enabled: %s" % is_ngpa_protocol_enabled)

    return is_ngpa_protocol_enabled


def update_nlb_state(dbh, custid, compute_region_idx, node_type=NODE_TYPE_NLB_INSTANCE, alt_node_type=-1):
    '''
        Updates "state=1" and "vm_status=1" for the NLB instance in RDS
        for the given (custid, compute_region_idx) combination.
    '''
    logger = dbh.logger
    sql = ("UPDATE instance_master SET state=1, vm_status=1 WHERE "
           "custid = %s AND compute_region_idx = %s AND node_type = %s"
           % (custid, compute_region_idx, node_type))
    if alt_node_type != -1:
        sql += f" AND alt_node_type = {alt_node_type}"
    logger.info("SQL: %s" % sql)
    ret, result = execute_orch_query(dbh, sql, None, "fetchone")
    logger.info("Return from RDS: %s: %s" % (ret, result))


def get_nlb_egress_ip_list(dbh, custid, compute_region_idx):
    '''
        Return the egress_ip_list for the NLB instance in the given
        (custid, compute_region_idx) combination.
        :param custid:
        :param compute_region_idx:
        :return:
    '''
    egress_ip_list = {}
    logger = dbh.logger
    sql = ("SELECT egress_ip_list FROM instance_master WHERE "
           "custid = %s AND compute_region_idx = %s AND node_type = %s"
           % (custid, compute_region_idx, NODE_TYPE_NLB_INSTANCE))
    logger.info("SQL: %s" % sql)
    ret, result = execute_orch_query(dbh, sql, None, "fetchone")
    logger.info("Return from RDS: %s: %s" % (ret, result))
    if ret is False or result is None:
        logger.info("Failed to get info from RDS; or result was None")
        return egress_ip_list
    egress_ip_list = str(result[0])
    logger.info("Return egress IP List: %s" % egress_ip_list)

    return egress_ip_list


def get_nlb_egress_ip(dbh, custid, compute_region_idx, edge_region_idx):
    '''
        Return the egress_ip for the NLB instance in the given
        (custid, compute_region_idx) for the given edge_region_idx.
        :param custid:
        :param compute_region_idx:
        :return:
    '''
    nlb_egress_ip = None
    logger = dbh.logger
    egress_ip_list = get_nlb_egress_ip_list(dbh, custid, compute_region_idx)
    logger.info("Egress IP List: %s" % egress_ip_list)

    # Append each of the IP Addresses in egress_ip_list
    # to the NLB IP Address list
    if egress_ip_list and egress_ip_list != "None":
        egress_list_dict = json.loads(egress_ip_list)
        logger.info("Egress IP list is [%s]" % (str(egress_list_dict)))
        if str(edge_region_idx) in egress_list_dict:
            nlb_egress_ip = egress_list_dict[str(edge_region_idx)]
            logger.info("Found IP: [%s] for edge region [%s]"
                         % (nlb_egress_ip, edge_region_idx))
        else:
            logger.info("No edge IP found in egress_ip_list for edge "
                        "location [%s]" % edge_region_idx)
    else:
        logger.info("egress_ip_list is not set (None)")
    logger.info("Returning NLB Egress IP: [%s] for custid: [%s]; "
                "compute_region_idx: [%s]; edge_region_idx: [%s]"
                % (nlb_egress_ip, custid, compute_region_idx,
                edge_region_idx))

    return nlb_egress_ip


def update_instances_behind_nlb(db_h, custid,
                                compute_region_idx):
    '''
        Uses the values from the NLB and updates the "interface_ip_list"
        for instances that are behind the NLB for the given
        (custid, compute_region_idx) combination.
    '''
    logger = db_h.logger
    logger.info("Updating instances behind NLB for custid: [%s], "
                "compute_region_idx: [%s]"
                % (custid, compute_region_idx))
    interface_ip_list = get_nlb_ip_address_map(db_h, custid,
                                               compute_region_idx)

    my_inst_model = InstanceModel(dbh=db_h)
    all_gw_instances = my_inst_model.get_all_gp_gw_instance_for_region_and_custid(
                           custid, compute_region_idx)
    for gw_inst_id in all_gw_instances:
        gw_inst = InstanceModel(dbh=db_h, iid=gw_inst_id[0])
        if gw_inst.get_param("is_instance_behind_nlb"):
            logger.info("GW Instance %s is behind NLB"
                         % gw_inst.get_param("name"))

            # Update the interface_ip_list.
            logger.info("Update the interface ip list to %s" %
                        interface_ip_list)
            ret = gw_inst.update_column_interface_ip_list(interface_ip_list)
            if ret == False:
                err_msg = ("Failed to update interface ip list for "
                           "instance [%s]" % gw_inst.get_param("name"))
                logger.error(err_msg)
                return False

            #CYR-38476 ALSO set lb_details early
            logger.info("Checking if lb_details needs to be set for GW")
            if not gw_inst.get_param("lb_details") or gw_inst.get_param("lb_details") == "None":
                gw_inst.update_column_lb_details(interface_ip_list.get(compute_region_idx))
                logger.info(f"Set lb_details for instance:{gw_inst} to {interface_ip_list.get(compute_region_idx)}")
            # We never update the egress_ip_list for the instance.
            # All required info is already present in the
            # "interface_ip_list" field.
            logger.info("Successfully updated instance [%s]"
                        % gw_inst.get_param("name"))
        else:
            logger.info("GW Instance %s %s is not "
                        "behind NLB; no update needed" %
                        (gw_inst_id, gw_inst.get_param("name")))
    logger.info("Completed updating all gateways based on NLB, for "
                "customer [%s] in region [%s]"
                % (custid, compute_region_idx))
    return True


def update_instance_behind_nlb(db_h, inst_ref):
    '''
        Uses the values from the NLB and updates the
        "interface_ip_list" for the passed-in instance for the
        given (custid, compute_region_idx) combination.
    '''
    logger = db_h.logger
    interface_ip_list = get_nlb_ip_address_map(db_h,
                            inst_ref.get_param("custid"),
                            inst_ref.get_param("compute_region_idx"))
    # Update the "interface_ip_list" to be
    # the same as that for the NLB.
    logger.info("GW Instance %s %s is behind NLB; "
                "Update the interface ip list to %s" %
                (inst_ref.get_param("id"), inst_ref.get_param("name"),
                interface_ip_list))
    inst_ref.update_column_interface_ip_list(interface_ip_list)

    #CYR-38476 ALSO set lb_details early
    logger.info("Checking if lb_details needs to be set for GW")
    compute_region_idx = inst_ref.get_param("compute_region_idx")
    if not inst_ref.get_param("lb_details") or inst_ref.get_param("lb_details") == "None":
        inst_ref.update_column_lb_details(interface_ip_list.get(compute_region_idx))
        logger.info(f"Set lb_details for instance:{inst_ref} to {interface_ip_list.get(compute_region_idx)}")
    
    # We never update the egress_ip_list for the instance. All the
    # required info is already present in the "interface_ip_list" field.
    logger.info("Completed updating the gateway [%s] based on NLB, for "
                "customer [%s] in region [%s]"
                % (inst_ref.get_param("name"),
                   inst_ref.get_param("custid"),
                   inst_ref.get_param("compute_region_idx")))
    return True

def disable_nlb_strong_session_affinity_configuration(dbh, custid, region_id):
    '''
        Updates "is_strong_session_affinity_supported" flag for the NLB instance in RDS
        based on the error that occurs during GCP NLB creation
    '''
    logger = dbh.logger
    session_affinity_to_use = DEFAULT_SESSION_AFFINITY
    is_ngpa_protocol = None
    is_ngpa_protocol = get_is_ngpa_protocol_enabled(dbh, custid)
    if is_ngpa_protocol and is_ngpa_protocol == "1":
        session_affinity_to_use = DEFAULT_NGPA_PROTO_SESSION_AFFINITY
    
    sql = ("INSERT INTO network_load_balancer_config (custid, region_id, "
           "is_strong_session_affinity_supported, session_affinity, "
           "health_check_port, health_check_interval, health_check_timeout, "
           "conn_persistence_on_unhealthy_backends, "
           "health_check_unhealthy_threshold, "
           "health_check_healthy_threshold, health_check_protocol, "
           "forwarding_rule_protocol, backend_service_protocol) VALUES "
           "(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s) "
           "ON DUPLICATE KEY UPDATE is_strong_session_affinity_supported=0")
    params = (custid, region_id, "0", session_affinity_to_use,
              DEFAULT_HC_PORT, DEFAULT_HC_INTERVAL,
              DEFAULT_HC_TIMEOUT, DEFAULT_CONN_PERS_ON_UNHEALTHY_BACKENDS,
              DEFAULT_HC_UNHEALTHY_THRESHOLD, DEFAULT_HC_HEALTHY_THRESHOLD,
              DEFAULT_HC_PROTOCOL, DEFAULT_FW_RULE_PROTOCOL,
              DEFAULT_BACKEND_SERV_PROTOCOL)
    logger.info("SQL: [%s]" % (sql % params))
    ret, result = execute_orch_query(dbh, (sql % params), None, "fetchone")
    logger.info("Return from RDS: %s: %s" % (ret, result))

def update_nlb_egress_ip_list(dbh, custid, compute_region_idx, region_ingress_ip_mappings):
    '''
        Update the egress_ip_list for the NLB instance in the given
        (custid, compute_region_idx)
        :param custid:
        :param compute_region_idx:
        :return:
    '''
    logger = dbh.logger
    logger.info(f"update_nlb_egress_ip_list: Updating NLB EIP list for custid {custid} for compute {compute_region_idx} for region mappings {region_ingress_ip_mappings}")
    if not region_ingress_ip_mappings:
        logger.info(f"update_nlb_egress_ip_list: No region_ingress_ip_mappings provided, skipping NLB egress IP list update for custid {custid}, region {compute_region_idx}")
        return False
    if is_ingress_ip_reduction_enabled(dbh, custid, compute_region_idx):
        logger.info(f"update_nlb_egress_ip_list: Ingress IP reduction is enabled for custid {custid}, region {compute_region_idx}. Skip updating NLB egress_ip_list.")
        return True
    logger.info(f"update_nlb_egress_ip_list: Ingress IP reduction disabled for custid {custid}, region {compute_region_idx}. Continue updating NLB egress_ip_list.")
    egress_ip_list = get_nlb_egress_ip_list(dbh, custid, compute_region_idx)
    logger.info(f"update_nlb_egress_ip_list: Existing Egress IP List: {egress_ip_list} for NLB for custid {custid} for compute {compute_region_idx} for region mappings {region_ingress_ip_mappings}")

    if egress_ip_list and egress_ip_list != "None":
        egress_list_dict = json.loads(egress_ip_list)
        logger.info(f"update_nlb_egress_ip_list: Egress IP list is {egress_list_dict} for NLB for custid {custid} for compute {compute_region_idx}")
    else:
        logger.info("update_nlb_egress_ip_list: egress_ip_list is empty for NLB instance. Return")
        return True

    update_eip_list = False
    for regionid, ipaddr in region_ingress_ip_mappings.items():
        logger.info(f"update_nlb_egress_ip_list: Processing region {regionid} IP mapping {ipaddr}")
        if str(regionid) in egress_ip_list and egress_list_dict.get(str(regionid), '') != ipaddr:
            egress_list_dict[str(regionid)] = str(ipaddr)
            logger.info(f"update_nlb_egress_ip_list: Updated Egress IP list is {egress_list_dict} for region {regionid}")
            update_eip_list = True

    # Avoid re-updating egress_ip_list if there was no update made to existing egress_ip_list
    if not update_eip_list:
        logger.info("update_nlb_egress_ip_list: Skip updating egress_ip_list. Return")
        return True

    eip_str = json.dumps(egress_list_dict)
    logger.info(f"update_nlb_egress_ip_list: Update Egress IP list JSON str to {eip_str} for NLB instance")

    sql = ("UPDATE instance_master set egress_ip_list='%s' WHERE custid = %s AND compute_region_idx = %s AND node_type = %s" % (eip_str, custid, compute_region_idx, NODE_TYPE_NLB_INSTANCE))
    logger.info(f"update_nlb_egress_ip_list SQL: {sql}")
    ret, result = execute_orch_query(dbh, sql, None, "update")
    logger.info(f"update_nlb_egress_ip_list: RDS ret {ret} result {result}")

    return True
