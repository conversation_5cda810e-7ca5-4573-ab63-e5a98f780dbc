import json
import traceback

import libs.model.instancemodel as INST
import utils.Exceptions as customExceptions
from orchestration_service.core.orchestrator_nlb_mgmt import update_instances_behind_nlb
import libs.model.custmodel as CM
from libs.apis.region_master_api import get_cloud_native_location_name_from_region_id
from libs.model.instancemodel import is_ingress_ip_reduction_enabled, \
    find_instances_by_custid_and_region_id, \
    copy_egress_ipv6_subnet_list_from_primary_instance
import libs.model.instancemodel as instancemodel
from orchestration_service.core.orchestrator_nat_mgmt import find_nr_nat_instances_by_custid_and_region_id
from libs.model.IP_management_model import IPManagementModel
# IP_MGMT is legacy API.
from libs.common.shared.sys_utils import *
from libs.cloud_providers.common.instance_trigger_update import trigger_update_for_instance_changes
import libs.cloud_providers.common.ip_management.ipv4.ip_mgmt as ipv4_mgmt
from libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1 import IPV6_Handler
from libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt import get_num_of_ips_by_instance_type
from utils.Exceptions import PupiAllocationFailureException
import libs.model.custEpaasConfigModel as CustEpModel

'''
       allocate_new_egress_ip

       This function to be only called in case of existing instance change
       (Do not call when we allocate a brand new instance)
'''

def get_egress_ip_dict_from_str(logger, egress_ip_list_str):
    my_egress_ip_list = {}

    if isinstance(egress_ip_list_str, dict):
        return egress_ip_list_str

    if isinstance(egress_ip_list_str, str):
        try:
            my_egress_ip_list = json.loads(egress_ip_list_str)
        except Exception as E:
            logger.error("Failed to load json %s, exception %s" % (str(egress_ip_list_str),
                                                                   str(E.args)))
    else:
        logger.info("incorrect type found for egress_ip_list_str %s" % str(my_egress_ip_list))
    return my_egress_ip_list

def allocate_new_egress_ip_gateways_behind_nat(db_h,
                                               inst,
                                               perform_ingress_ip_reduction,
                                               is_clean_ip_tag,
                                               edge_region_idx):
    logger = db_h.logger
    success = False
    try:
        # Handle the instances that don't have a NAT gateway.
        logger = db_h.logger
        logger.info("Instance %s is in front of NAT GW" %
                    str(inst.get_param("id")))

        # When ingress IP reduction is off and instance is behind NLB,
        # we allocate edge IP

        if perform_ingress_ip_reduction == False and \
                inst.get_param("is_instance_behind_nlb") == False:
            # Should be just grab from general pool and
            # save for future NAT IP scale up.
            logger.info("perform_ingress_ip_reduction ais disabled and "
                        "GW [%s] not behind NLB; allocate edge IP"
                        % (inst.get_param("name")))
            ipmgmt = IPManagementModel(db_h)
            result = ipmgmt.bind_non_allow_listed_ips_to_instance(
                inst, edge_region_idx, 1,
                target_node_type=NODE_TYPE_GP_GATEWAY)
            if result['ok'] == False:
                err_msg = ("Failed to bind non allow listed IP's "
                           "to instance: %s"
                           % str(inst.get_param("name")))
                if is_clean_ip_tag:
                    raise Exception(err_msg)
                else:
                    logger.info(err_msg)
            else:
                logger.info("Successfully bound non-allow "
                            "listed IP's to instance %s from "
                            "general pool" %
                            str(inst.get_param("name")))

            # Copy over the IP Address for this edge location
            # from "egress_ip_list" into "interface_ip_list"
            # for the gateway instance.
            ret = inst.update_interface_ip_list_with_egress_ip(edge_region_idx)
            if not ret:
                err_msg = ("Failed to update interface ip list with "
                           "egress ip for %s" % edge_region_idx)
                logger.error("Error: %s" % err_msg)
                raise Exception(err_msg)
        else:
            logger.info("perform_ingress_ip_reduction is on; GW [%s] "
                        "will not get edge IP"
                        % (inst.get_param("name")))

            # If an instance has NAT GW in front of it, we will always
            # set "egress_ip_list" entry to be "ASSIGNED_TO_NAT".
            logger.info("Use ASSIGNED_TO_NAT in egress_ip_list for [%s]"
                        % (inst.get_param("name")))
            ipmgmt = IPManagementModel(db_h)
            result = ipmgmt.update_egress_ip_list_with_given_ipstr(
                inst, edge_region_idx, "ASSIGNED_TO_NAT")
            if result['ok'] == False:
                raise Exception(f"allocate_new_egress_ip_gateways_behind_nat: Failed to update egress IP list with ASSIGNED_TO_NAT for instance {str(inst.get_param('name'))}")
            else:
                logger.info(f"allocate_new_egress_ip_gateways_behind_nat: Successfully updated egress IP list with ASSIGNED_TO_NAT for {str(inst.get_param('name'))}")
        success=True
    except Exception as E:
        logger.error("Failed with exception: %s, Traceback: %s, locals: %s" % (str(E.args),
                                                                               str(traceback.format_exc()),
                                                                               str(locals())))
        success=False
    finally:
        return success

def allocate_new_egress_ip_standalone_gateways(db_h,
                                               inst, perform_ingress_ip_reduction, edge_region_idx):
    logger = db_h.logger
    success = False
    try:
        logger.info("Instance %s is not behind NAT GW; allocate "
                    "edge IP if needed" % str(inst.get_param("id")))
        '''
        result = allocate_new_egress_ip_gateways(db_h,
                                                 inst,
                                                 edge_region_idx,
                                                 NODE_TYPE_GP_GATEWAY)
        '''
        # Handle ingress_ip_reduction here as well.
        gcp_ip_mgmt = ipv4_mgmt.GCPIPHandler(db_h,
                                           str(inst.get_param("id")),
                                           str(inst.get_param("acct_id")))
        my_egress_ip_list = get_egress_ip_dict_from_str(logger,
                                                        inst.get_param("egress_ip_list"))
        if str(edge_region_idx) not in my_egress_ip_list:
            ip_list = gcp_ip_mgmt.allocate_public_ip_for_customer(
                None, edge_region_idx, False)

            # Refresh InstanceModel object.
            my_inst = INST.InstanceModel(dbh=db_h, iid=inst.get_param("id"))
            logger.info("IP list for instance name %s is %s" %
                        (str(my_inst.get_param("name")),
                         str(my_inst.get_param("egress_ip_list"))))

            # Copy over the IP Address for this edge location
            # from "egress_ip_list" into "interface_ip_list"
            # for the gateway instance only when Ingress IP reduction is disabled
            if perform_ingress_ip_reduction == False:
                logger.info("Updating IIL for instance %s with EIL %s" % (str(my_inst.get_param("name")), str(my_inst.get_param("egress_ip_list"))))
                ret = my_inst.update_interface_ip_list_with_egress_ip(edge_region_idx)
                if not ret:
                    err_msg = ("Failed to update interface ip list with egress ip for %s" % edge_region_idx)
                    logger.error("Error: %s" % err_msg)
                    raise Exception(err_msg)
            else:
                logger.info("IIR is enabled. Skip Updating IIL for instance %s" % (str(my_inst.get_param("name"))))
        else:
            logger.info("Instance already has IP allocated for "
                        "edge location: %s" %
                        str(inst.get_param("egress_ip_list")))

        success = True
    except Exception as E:
        logger.error("Failed with exception: %s, Traceback: %s, locals: %s" % (str(E.args),
                                                                               str(traceback.format_exc()),
                                                                               str(locals())))
        success = False
    finally:
        return success


def update_nlb_instances_egress_ip_list(db_h, inst, edge_region_idx):
    logger = db_h.logger
    logger.info(f"update_nlb_instances_egress_ip_list: Handle NLB instance: {inst} for edge region idx {edge_region_idx}")
    try:
        eip = json.loads(inst.get_param('egress_ip_list'))
    except Exception as E:
        eip = {}

    logger.info(f"update_nlb_instances_egress_ip_list: Egress IP list for instance {inst.get_param('name')} is {eip} for edge region idx {edge_region_idx}")
    if str(edge_region_idx) not in eip.keys():
        eip[str(edge_region_idx)] = ''
    else:
        logger.info(f"update_nlb_instances_egress_ip_list: Edge region ID {edge_region_idx} already exists in egress IP list {eip}. Skip update and return")
        return

    eip_str = json.dumps(eip)
    inst.set_param("egress_ip_list", eip_str)
    inst.save()
    logger.info(f"update_nlb_instances_egress_ip_list: Updated IM NLB instance entry -> {inst} for edge region idx {edge_region_idx} egress_ip_list to {inst.get_param('egress_ip_list')}")
    return True

def allocate_new_egress_ip_for_nlb_instances(db_h,
                                             inst,
                                             perform_ingress_ip_reduction,
                                             is_clean_ip_tag,
                                             edge_region_idx):
    logger = db_h.logger
    success=False
    try:
        # If there is an NLB in the region, add the edge location
        # to the NLB as well.
        result, nlb_instances = find_instances_by_custid_and_region_id(
                                                db_h, inst.get_param("custid"),
                                                inst.get_param("compute_region_idx"),
                                                NODE_TYPE_NLB_INSTANCE)
        if result == False:
            err_msg = ("Failed to get the NLB for custid %s and region %s"
                       % (str(inst.get_param("custid")),
                          str(inst.get_param("compute_region_idx"))))
            logger.error(err_msg)
            raise Exception(err_msg)

        if len(nlb_instances) < 1:
            logger.info("There are no NLB's for custid: %s "
                        "in region name: %s" % (str(inst.get_param("custid")),
                                                str(inst.get_param("compute_region_name"))))
        else:
            nlb_inst_id = nlb_instances[0]
            logger.info("allocate_new_egress_ip_for_nlb_instances: Handle NLB instance: %s" % nlb_inst_id)

            nlb_inst = INST.InstanceModel(dbh=db_h, iid=nlb_inst_id)

            # We will not reserve IP's for NLB if ingress IP reduction is on.
            # If ingress IP reduction is off, then we reserve public and
            # edge IP for the NLB instance.
            logger.info(f"allocate_new_egress_ip_for_nlb_instances: Handle NLB instance {nlb_inst} for IIR {perform_ingress_ip_reduction} for edge {edge_region_idx} for cloud {nlb_inst.get_param('cloud_provider')}")
            if perform_ingress_ip_reduction == False:
                ipmgmt = IPManagementModel(db_h)
                num_of_ips = 1

                result = ipmgmt.reserve_ips_for_cust_region(inst.get_param("custid"), NODE_TYPE_NLB_INSTANCE,
                                                                edge_region_idx,
                                                                inst.get_param("compute_region_idx"), num_of_ips,
                                                                target_node_type=NODE_TYPE_NLB_INSTANCE)            

                if not result.get('ok'):
                    logger.error(f"Failed to pre-allocate IPs for instance {nlb_inst_id} in region {inst.get_param('compute_region_idx')} "
                                    f"for node_type {NODE_TYPE_NLB_INSTANCE}. "
                                    f"Continuing anyway")
                        
                result = ipmgmt.bind_reserved_ips_to_instance(nlb_inst,
                                                              edge_region_idx, 1,
                                                              target_node_type=NODE_TYPE_NLB_INSTANCE, cloud_provider=nlb_inst.get_param('cloud_provider'))
                if result['ok'] == False:
                    err_msg = "Failed to bind non allow listed " \
                              "IP's to instance: %s" % \
                              str(nlb_inst.get_param("name"))
                    if is_clean_ip_tag:
                        raise Exception(err_msg)
                    else:
                        logger.info(err_msg)
                else:
                    logger.info("Successfully bound non-allow "
                                "listed IP's to instance %s from "
                                "general pool" % str(nlb_inst.get_param("name")))

                # This is to handle cases wherein NLB IP could not be retrieved from tenant or general pool; in this case for OCI cloud
                # we will fall back to assigning OCI Cloud ephemeral IPs for each edge region on the NLB for IIR disabled BF tenants
                # Update NLB instance egress_ip_list with edge region indexes and empty IP values for further processing by provisioning service to plumb each
                # new edge ephemeral IPs on the NLB instance. This is not relevant for GCP as we never use NLB ephemeral IPs for GCP NLB
                if nlb_inst.get_param('cloud_provider') == 'oci':
                    ret = update_nlb_instances_egress_ip_list(db_h, nlb_inst, edge_region_idx)
                    logger.info(f"allocate_new_egress_ip_for_nlb_instances: Updated EIP list for {nlb_inst.get_param('name')} for region {edge_region_idx} to {nlb_inst.get_param('egress_ip_list')}")

                # Now that we have completed updating the "egress_ip_list"
                # for the NLB, update the "interface_ip_list" for all
                # GW instances behind the NLB.
                ret = update_instances_behind_nlb(db_h,
                                                  inst.get_param("custid"),
                                                  inst.get_param("compute_region_idx"))
                if ret:
                    logger.info("Updated all GW's behind NLB [%s]" %
                                nlb_inst.get_param("name"))
                else:
                    logger.info("Failed to update all GW's behind NLB [%s]" %
                                nlb_inst.get_param("name"))
            else:
                logger.info("No egress IPs are allocated to NLB for edge "
                            "location since ingress_ip_reduction is enabled")

        success = True
    except Exception as E:
        logger.error("Failed with exception: %s, Traceback: %s, locals: %s" % (str(E.args),
                                                                               str(traceback.format_exc()),
                                                                               str(locals())))
        success = False
    finally:
        return success


def allocate_new_egress_ip_for_nat_instances(db_h,
                                             inst,
                                             perform_ingress_ip_reduction,
                                             is_clean_ip_tag,
                                             edge_region_idx, cloud_provider='gcp'):
    logger = db_h.logger
    success=False
    try:
        # Now find out all NAT gateways and add this edge location if not seen.
        result, instances = find_instances_by_custid_and_region_id(db_h, inst.get_param("custid"),
                                                                   inst.get_param("compute_region_idx"),
                                                                   NODE_TYPE_NAT_INSTANCE)
        if result == False:
            err_msg = ("Failed to get the NAT gateways for custid %s and region %s" % (str(inst.get_param("custid")),
                                                                          str(inst.get_param("compute_region_idx"))))
            logger.error(err_msg)
            raise Exception(err_msg)

        if len(instances) < 1:
            logger.info("There are no NAT instances for custid: %s in region name: %s" % (str(inst.get_param("custid")),
                                                                            str(inst.get_param("compute_region_name"))))

        logger.info(f"allocate_new_egress_ip_for_nat_instances: NAT instances found {instances} for edge {edge_region_idx} and compute {inst.get_param('compute_region_idx')}")
        for my_inst_id in instances:
            edge_region_idx = int(edge_region_idx)
            my_inst = INST.InstanceModel(dbh=db_h, iid=my_inst_id)
            result, nr_instances, nr_nat_gateways_needed = find_nr_nat_instances_by_custid_and_region_id(db_h,
                                                                            my_inst.get_param("custid"),
                                                                            my_inst.get_param("compute_region_idx"))

            num_of_ips = get_num_of_ips_by_instance_type(db_h, my_inst, perform_ingress_ip_reduction)
            logger.info(f"allocate_new_egress_ip_for_nat_instances: Reserve ips {num_of_ips} for custid {my_inst.get_param('custid')}, edge region {edge_region_idx}, compute {my_inst.get_param('compute_region_idx')}")
            if edge_region_idx:
                logger.info("allocate_new_egress_ip_for_nat_instances: Need to reserve %s IPs in total for edge location %s" % (
                num_of_ips, edge_region_idx))
                reserve_pupi_ip = False
                sp_egress_type = "SP"
                if my_inst.get_param("is_using_sp_interconnect"):
                    egress_res = db_h.get_sp_egress_type(my_inst.get_param("custid"),
                                                         get_cloud_native_location_name_from_region_id(db_h,
                                                                                                       my_inst.get_param(
                                                                                                           "compute_region_idx")))
                    if egress_res is not None and egress_res.ok and egress_res.result[0] is not None:
                        sp_egress_type = egress_res.result[0]

                    if sp_egress_type not in ['PA', 'HYBRID']:
                        reserve_pupi_ip = True
                ret = allocate_new_egress_ip_nat_gateways(db_h,
                                                          my_inst,
                                                          edge_region_idx,
                                                          is_clean_ip_tag,
                                                          num_of_ips_to_reserve=num_of_ips,
                                                          num_of_ips_to_bind=1,
                                                          target_node_type=NODE_TYPE_GP_GATEWAY,
                                                          cloud_provider=cloud_provider,
                                                          reserve_pupi_ip=reserve_pupi_ip)
                if ret == False:
                    logger.error("Failed to allocate egress IP for NAT gateway for edge_region_idx %s" %
                                 str(edge_region_idx))
                    raise Exception("Failed to allocate egress IP for NAT gateway for edge_region_idx %s" %
                                 str(edge_region_idx))
                else:
                    logger.info("Successfully allocated egress IP for NAT gateway for edge_region_idx %s" %
                                str(edge_region_idx))

                if my_inst.get_param("is_using_sp_interconnect"):
                    if sp_egress_type in ['PA', 'HYBRID']:
                        logger.info(f"Need to reserve {num_of_ips} IPs in total for edge location {edge_region_idx}")
                        ret = allocate_new_egress_ip_dp2_nat_gateways(db_h,
                                                                      my_inst,
                                                                      edge_region_idx,
                                                                      is_clean_ip_tag,
                                                                      num_of_ips_to_reserve=num_of_ips,
                                                                      num_of_ips_to_bind=1,
                                                                      target_node_type=NODE_TYPE_GP_GATEWAY)
                        if ret == False:
                            logger.error(f"Failed to allocate SP hybrid IP for NAT gateway for edge_region_idx {edge_region_idx}")
                            raise Exception(f"Failed to allocate SP hybrid IP for NAT gateway for edge_region_idx {edge_region_idx}")
                        else:
                            logger.info(f"Successfully allocated SP hybrid IP for NAT gateway for edge_region_idx {edge_region_idx}")

        success = True
    except Exception as E:
        logger.error("Failed with exception: %s, Traceback: %s, locals: %s" % (str(E.args),
                                                                               str(traceback.format_exc()),
                                                                               str(locals())))
        success = False
    finally:
        return success


def allocate_new_egress_ip_gateways(db_h, inst, edge_region_idx, node_type):
    '''
    :param inst:
    :param edge_region_idx:
    :param node_type:
    :return: True if success, False of failure
    Raises Exception on PUPI allocation failure.
    '''

    success = False
    error = False
    logger = db_h.logger
    pupi_ip_allocation_failure = False

    logger.info(f"allocate_new_egress_ip_gateways: Handling IP reservation for {inst} for edge region {edge_region_idx}")
    custid = inst.get_param("custid")
    compute_region_idx = inst.get_param("compute_region_idx")
    cloud_provider = inst.get_param("cloud_provider")
    try:
        perform_ingress_ip_reduction = is_ingress_ip_reduction_enabled(db_h, custid, compute_region_idx)
    except customExceptions.DbReadException as dbE:
        logger.error(f"Failed to check for ingress IP reduction with exception: {dbE.args}")
        return success
    try:
        egress_ip_list = {}
        if inst.get_param("egress_ip_list") is not None:
            egress_ip_list = json.loads(inst.get_param("egress_ip_list"))
        logger.info("Egress IP list for instance id %s is %s" % (str(inst.get_param("id")),
                                                                 str(inst.get_param("egress_ip_list"))))
        customer_model = CM.CustomerModel(custid=inst.get_param("custid"), dbh=db_h)
        is_clean_ip_tag = customer_model.gcp_is_clean_ip_project()

        # if str(edge_region_idx) not in egress_ip_list:
        my_inst_model = INST.InstanceModel(dbh=db_h)
        all_gw_instances = my_inst_model.get_all_gp_gw_instance_for_region_and_custid(custid, compute_region_idx)
        for my_inst_id in all_gw_instances:
            my_inst = INST.InstanceModel(dbh=db_h, iid=my_inst_id[0])
            if not my_inst.get_param("id"):
                logger.info("Instance %s is invalid in instance_master"
                            % my_inst_id[0])
                continue

            # Handle the instances that don't have a NAT gateway.
            if my_inst.get_param("has_nat_instance") == True:
                logger.info("Instance %s is in front of NAT GW" %
                            str(my_inst.get_param("id")))
                # When ingress IP reduction is off, we no more allocate
                # IP to GW if GW is behind NLB.
                result = allocate_new_egress_ip_gateways_behind_nat(db_h,
                                                           my_inst,
                                                           perform_ingress_ip_reduction,
                                                           is_clean_ip_tag,
                                                           edge_region_idx)
                if result == False:
                    raise Exception("Failed to allocate egress IP behind NAT gateways")

            else:
                # This is an instance that does not have a NAT gateway.
                logger.info("Instance %s is not behind NAT GW; allocate "
                        "edge IP if needed" % str(my_inst.get_param("id")))
                result = allocate_new_egress_ip_standalone_gateways(db_h,
                                                                    my_inst, perform_ingress_ip_reduction, edge_region_idx)
                if result == False:
                    raise Exception("Failed to allocate egress IP for the gateway")

        # If there is an NLB in the region, add the edge location
        # to the NLB as well.

        logger.info(f"allocate_new_egress_ip_gateways: Handling IP reservation for NLB nodes for {inst} for edge region {edge_region_idx}")
        result = allocate_new_egress_ip_for_nlb_instances(db_h,
                                                          inst,
                                                          perform_ingress_ip_reduction,
                                                          is_clean_ip_tag,
                                                          edge_region_idx)
        if result == False:
            raise Exception("Failed to allocate egress IP for the NLB instances")

        # For OCI cloud & GCP CNAT: we will trigger the NAT IP allocation logic when we are creating a new IM entry
        # during salt profile creation for NAT gateways, hence there is no need to allocate the NAT IP here.
        if (cloud_provider == PROVIDER_OCI_DB_ENUM_VALUE) or \
           (cloud_provider == PROVIDER_GCP_DB_ENUM_VALUE and db_h.is_gcp_cnat_supported(cloud_provider, custid, compute_region_idx)):
            logger.info(f"allocate_new_egress_ip_gateways: Skip NAT egress IP allocation logic for custid {custid} for instance {inst} for compute region {compute_region_idx} for edge region {edge_region_idx} for cloud provider {cloud_provider}")
            success = True
            return success
        result = allocate_new_egress_ip_for_nat_instances(db_h,
                                                          inst,
                                                          perform_ingress_ip_reduction,
                                                          is_clean_ip_tag,
                                                          edge_region_idx)
        if result == False:
            raise Exception("Failed to allocate egress IP for the NAT instances")

        success = True

    except PupiAllocationFailureException as pe:
        logger.error("PUPI IP allocation failed with Exception %s, Traceback: %s, locals: %s" %
                     (str(pe.args), str(traceback.format_exc()), str(locals())))
        pupi_ip_allocation_failure = True
    except Exception as E:
        logger.error("Failed with Exception %s, Traceback: %s, locals: %s" %
                     (str(E.args), str(traceback.format_exc()), str(locals())))
        success = False
    finally:
        if pupi_ip_allocation_failure:
            raise PupiAllocationFailureException("PUPI IP not found for custid: %s" % (inst.get_param("custid")))
        return success

def update_egress_ips_gateways_behind_nlb_nat(db_h, inst, edge_region_idx, cloud_provider='gcp'):
    '''
    :param inst:
    :param edge_region_idx:
    :param node_type:
    :return: True if success, False of failure
    Raises Exception on update egress_ip_list update failure in IM for MU gatewy instances.
    '''

    success = False
    logger = db_h.logger
    try:
        logger.info(f"update_egress_ips_gateways_behind_nlb_nat: Processing gateways eip_list for edge region {edge_region_idx} in {cloud_provider} cloud in compute {inst.get_param('compute_region_idx')}")
        perform_ingress_ip_reduction = is_ingress_ip_reduction_enabled(db_h, inst.get_param("custid"), inst.get_param("compute_region_idx"))
    except customExceptions.DbReadException as dbE:
        logger.error(f"update_egress_ips_gateways_behind_nlb_nat: Failed to check for ingress IP reduction with exception: {dbE.args}")
        return success
    try:
        my_inst_model = INST.InstanceModel(dbh=db_h)
        all_gw_instances = my_inst_model.get_all_gp_gw_instance_for_region_and_custid(inst.get_param("custid"), inst.get_param("compute_region_idx"))
        for my_inst_id in all_gw_instances:
            my_inst = INST.InstanceModel(dbh=db_h, iid=my_inst_id[0])
            if not my_inst.get_param("id"):
                logger.info(f"update_egress_ips_gateways_behind_nlb_nat: Instance {my_inst_id[0]} is invalid in instance_master")
                continue
            logger.info(f"update_egress_ips_gateways_behind_nlb_nat: Processing GPGW instance {my_inst_id}")
            result = allocate_new_egress_ip_gateways_behind_nat(db_h, my_inst, perform_ingress_ip_reduction, False, edge_region_idx)
            if result == False:
                logger.error(f"update_egress_ips_gateways_behind_nlb_nat: Failed to update eip_list for MU gateways {my_inst_id} in edge region {edge_region_idx}")
                raise Exception(f"update_egress_ips_gateways_behind_nlb_nat: Failed to update eip_list for MU gateways {my_inst_id} in edge region {edge_region_idx}")
            logger.info(f"update_egress_ips_gateways_behind_nlb_nat: Successfully updated eip_list for MU gateways {my_inst_id} in edge region {edge_region_idx}")
            success = True
    except Exception as E:
        logger.error("update_egress_ips_gateways_behind_nlb_nat: Failed with Exception %s, Traceback: %s, locals: %s" % (str(E.args), str(traceback.format_exc()), str(locals())))
        success = False
    finally:
        return success

def allocate_new_egress_ip_non_gateways(db_h, inst, edge_region_idx, node_type):
    '''
    :param inst:
    :param edge_region_idx:
    :param node_type:
    :return: True if success, False of failure
    '''

    success = False
    error = False
    logger = db_h.logger
    pupi_ip_allocation_failure = False
    try:
        egress_ip_list = {}
        if inst.get_param("egress_ip_list") is not None:
            egress_ip_list = json.loads(inst.get_param("egress_ip_list"))
        logger.info("Egress IP list for instance id %s is %s" % (str(inst.get_param("id")), str(inst.get_param("egress_ip_list"))))

        gcp_ip_mgmt = ipv4_mgmt.GCPIPHandler(db_h, str(inst.get_param("id")), str(inst.get_param("acct_id")))
        if str(edge_region_idx) not in egress_ip_list:
            logger.info("Reserving IPs for GCP customer %s, "
                        "Updating cluster id %s for edge_region id %s" %
                        (str(inst.get_param("acct_id")),
                         str(inst.get_param("id")), str(edge_region_idx)))
            ip_list = gcp_ip_mgmt.allocate_public_ip_for_customer(None, edge_region_idx, False)
            if len(ip_list) == 0:
                logger.info("IP list is empty")
        else:
            logger.info("Region %s is already present in egress IP list" % str(edge_region_idx))

        logger.info("Copy IPs from active instance %s to "
                    "passive instance %s for edge region id %s" %
                    (str(inst.get_param("id")), str(inst.get_param("ha_peer")), str(edge_region_idx)))
        gcp_ip_mgmt.copy_ip_from_active_instance(str(inst.get_param("ha_peer")))

        success = True
    except PupiAllocationFailureException as pe:
        logger.error("PUPI IP allocation failed with Exception %s, Traceback: %s, locals: %s" %
                     (str(pe.args), str(traceback.format_exc()), str(locals())))
        raise pe
    except Exception as E:
        logger.error("Failed with Exception %s, Traceback: %s, locals: %s" %
                     (str(E.args), str(traceback.format_exc()), str(locals())))
        success = False
    finally:
        if pupi_ip_allocation_failure:
            raise PupiAllocationFailureException("PUPI IP not found for custid: %s" % (inst.get_param("custid")))
        return success


def allocate_new_egress_ip_nat_gateways(db_h,
                                        inst,
                                        edge_region_idx,
                                        is_clean_ip_tag=False,
                                        num_of_ips_to_reserve=1,
                                        num_of_ips_to_bind=1,
                                        target_node_type=NODE_TYPE_GP_GATEWAY,
                                        cloud_provider='gcp',
                                        reserve_pupi_ip = False):
    success = False
    logger = db_h.logger
    try:
        num_of_ips_to_reserve = int(num_of_ips_to_reserve)
        ipmgmt = IPManagementModel(db_h)
        result = ipmgmt.reserve_ips_for_cust_region(inst.get_param("custid"), inst.get_param("node_type"), edge_region_idx,
                                                    inst.get_param("compute_region_idx"), num_of_ips_to_reserve,
                                                    target_node_type, reserve_pupi_ip=reserve_pupi_ip)
        # {'ok': True / False, 'ip_list': [[IP, reservationStatus]]}
        if result['ok'] == True:
            ip_list = result['ip_list']
            logger.info("IP Reservation list: %s" % str(ip_list))
        else:
            success = False
            err_msg = ("Failed to reserve IP address for custid %s, node_type: %s, edge_region_idx: %s,\
                                            inst.compute_region_id: %s" % (str(inst.get_param("custid")), str(inst.get_param("node_type")),
                                                                           str(edge_region_idx),
                                                                           str(inst.get_param("compute_region_idx"))))

            if is_clean_ip_tag:
                raise Exception(err_msg)
            else:
                logger.info(err_msg)
                result = False
                return

        cm = CM.CustomerModel(custid=inst.get_param("custid"), dbh=db_h)
        _, _, _, is_allow_list_enabled = cm.get_auto_scale_options(NODE_TYPE_GP_GATEWAY)
        num_of_ips_to_bind = int(num_of_ips_to_bind)
        if is_allow_list_enabled:
            result = ipmgmt.bind_allow_listed_ips_to_instance(inst, edge_region_idx,
                                                              num_of_ips_to_bind, target_node_type,
                                                              cloud_provider=cloud_provider,
                                                              reserve_pupi_ip=reserve_pupi_ip)
        else:
            result = ipmgmt.bind_reserved_ips_to_instance(inst, edge_region_idx,
                                                          num_of_ips_to_bind, target_node_type,
                                                          cloud_provider=cloud_provider,
                                                          reserve_pupi_ip=reserve_pupi_ip)
        if result["ok"] == False:
            err_msg = "Failed to bind reserved IP addresses to instance %s" % str(inst.get_param("name"))
            if is_clean_ip_tag:
                raise Exception(err_msg)
            else:
                logger.info(err_msg)
                result = False
                return

        success = True
    except Exception as E:
        logger.error("allocate_new_egress_ip_nat_gateways: Failed with exception %s, locals: %s"
                     % (str(E.args), str(locals())))
    finally:
        return success


def allocate_new_egress_ip_dp2_nat_gateways(db_h,
                                            inst,
                                            edge_region_idx,
                                            is_clean_ip_tag=False,
                                            num_of_ips_to_reserve=1,
                                            num_of_ips_to_bind=1,
                                            target_node_type=NODE_TYPE_GP_GATEWAY):
    success = False
    logger = db_h.logger
    try:
        num_of_ips_to_reserve = int(num_of_ips_to_reserve)
        ipmgmt = IPManagementModel(db_h)
        result = ipmgmt.reserve_ips_for_cust_region(inst.get_param("custid"), inst.get_param("node_type"), edge_region_idx,
                                                    inst.get_param("compute_region_idx"), num_of_ips_to_reserve,
                                                    target_node_type, reserve_pupi_ip=True)
        # {'ok': True / False, 'ip_list': [[IP, reservationStatus]]}
        if result['ok'] == True:
            ip_list = result['ip_list']
            logger.info("DP2 IP Reservation list: %s" % str(ip_list))
        else:
            success = False
            err_msg = ("Failed to reserve IP address for custid %s, node_type: %s, edge_region_idx: %s,\
                                            inst.compute_region_id: %s" % (str(inst.get_param("custid")), str(inst.get_param("node_type")),
                                                                           str(edge_region_idx),
                                                                           str(inst.get_param("compute_region_idx"))))

            if is_clean_ip_tag:
                raise Exception(err_msg)
            else:
                logger.info(err_msg)
                result = False
                return

        cm = CM.CustomerModel(custid=inst.get_param("custid"), dbh=db_h)
        _, _, _, is_allow_list_enabled = cm.get_auto_scale_options(NODE_TYPE_GP_GATEWAY)
        num_of_ips_to_bind = int(num_of_ips_to_bind)
        if is_allow_list_enabled:
            result = ipmgmt.bind_allow_listed_dp2_ips_to_instance(inst, edge_region_idx,
                                                                  num_of_ips_to_bind, target_node_type)
        else:
            result = ipmgmt.bind_reserved_dp2_ips_to_instance(inst, edge_region_idx,
                                                              num_of_ips_to_bind, target_node_type)
        if result["ok"] == False:
            err_msg = "Failed to bind reserved IP addresses to instance %s" % str(inst.get_param("name"))
            if is_clean_ip_tag:
                raise Exception(err_msg)
            else:
                logger.info(err_msg)
                result = False
                return

        success = True
    except Exception as E:
        logger.error("allocate_new_egress_ip_nat_gateways: Failed with exception %s, locals: %s"
                     % (str(E.args), str(locals())))
    finally:
        return success


def unbind_egress_ips_from_node_impl(db_h, custid, compute_region_id,
                                     edge_region_id, node_type,
                                     delete_all=False):
    success = False
    logger = db_h.logger

    try:
        # We need to free all the egress IP addresses back to the pool.
        result, instances = find_instances_by_custid_and_region_id(
                                db_h, custid, compute_region_id,
                                node_type)
        if result == False:
            err_msg = ("Failed to get the nodes of type %s for custid %s "
                       "and region %s" % (str(node_type), str(custid),
                       str(compute_region_id)))
            ret = False
            logger.error(err_msg)
            raise Exception(err_msg)

        if len(instances) == 0:
            logger.info("No nodes of type %s were found for region "
                        "%s and custid %s" % (str(node_type),
                        str(custid), str(compute_region_id)))
            success = True
            return

        for my_inst_id in instances:
            my_inst = INST.InstanceModel(dbh=db_h, iid=my_inst_id)
            logger.info("For instance %s, egress_ip_list = %s"
                        % (str(my_inst.get_param("id")), str(my_inst.get_param("egress_ip_list"))))
            if ((my_inst.get_param("egress_ip_list") == None) or
                (len(my_inst.get_param("egress_ip_list")) == 0)):
                logger.info("egress IP list for instance %s is null/empty"
                            % str(my_inst.get_param("id")))
                continue

            egress_ip_list_dict = {}
            try:
                egress_ip_list_dict = json.loads(my_inst.get_param("egress_ip_list"))
            except Exception as E:
                logger.info("egress IP list %s is not in a json "
                             "format. Exception: %s" %
                             (str(my_inst.get_param("egress_ip_list")), str(E.args)))
                continue

            if delete_all == True:
                ipmgmt = IPManagementModel(dbh=db_h)
                result = ipmgmt.unbind_ips_from_instance(my_inst,
                             egress_ip_list_dict.keys())
                if result['ok'] == True:
                    logger.info("Successfully unbound IP addresses "
                        "from instance %s" % str(my_inst.get_param("name")))
                else:
                    err_msg = ("Failed to unbind IP addresses from "
                        "instance %s" % str(my_inst.get_param("name")))
                    raise Exception(err_msg)

            elif edge_region_id != None:

                ipmgmt = IPManagementModel(dbh=db_h)
                result = ipmgmt.unbind_ips_from_instance(my_inst,
                             [edge_region_id])
                if result['ok'] == True:
                    logger.info("Successfully unbound IP "
                        "addresses from instance %s" % str(my_inst.get_param("name")))
                else:
                    # Not excepting here!
                    logger.error("Failed to unbind IP addresses "
                        "from instance %s" % str(my_inst.get_param("name")))
            else:
                info_msg = ("Nothing to delete for instance %s" %
                            str(my_inst.get_param("name")))
                logger.info(info_msg)

            # If this is an NLB instance, then update "interface_list"
            # for all instances behind the NLB.
            if node_type == NODE_TYPE_NLB_INSTANCE and not delete_all:
                ret = update_instances_behind_nlb(db_h,
                                                 custid,
                                                 compute_region_id)
                if ret:
                    logger.info("Updated all GW's "
                                "behind NLB [%s]" %
                                my_inst.get_param("name"))
                else:
                    logger.info("Failed to update "
                                "all GW's behind NLB [%s]" %
                                my_inst.get_param("name"))
            else:
                logger.info("Skip updating interface IP list for instances behind the NLB as we are deleting all instances")

        success = True
    except Exception as E:
        logger.error("Failed with exception: %s, traceback: %s, "
                     "locals: %s" % (str(E.args),
                     str(traceback.format_exc()), str(locals())))
        success = False
    finally:
        return success


def delete_gp_gateway_edge_location_references_impl(db_h,
                                                    custid,
                                                    edge_loc_deleted_idx,
                                                    compute_idx,
                                                    avctx,
                                                    node_type=None):
    '''
        This function deletes the gp gw edge location references from all the instances

        in:
            custid: well, custid
            edge_loc_deleted_idx: Idx of location that is deleted.
            compute_idx: Idx of the compute location on which the edge location is deleted.
            node_type: if provided, one of the numerical values (48, 49, 50, 51 or 152)
        out:
            status: True if success; False if failed

    '''

    success = False
    logger = db_h.logger
    FNAME = "delete_gp_gateway_edge_location_references_impl"
    try:
        logger.info("%s: custid: %s, edge_idx: %s, compute_idx: %s, \
                    node_type: %s" % (FNAME, custid, edge_loc_deleted_idx,
                                      compute_idx, node_type))

        if edge_loc_deleted_idx == None or compute_idx == None:
            raise Exception("Either edge_loc_deleted_idx or compute_idx is not specified %s/%s" %
                            (str(edge_loc_deleted_idx), str(compute_idx)))

        if compute_idx == edge_loc_deleted_idx:
            logger.info(
                "Compute idx %s is same as edge location idx %s" % (str(compute_idx), str(edge_loc_deleted_idx)))
            success = True
            return success

        instance_ids = INST.get_instance_ids_having_egress_ip_by_edge_loc(db_h,
                                                                          custid,
                                                                          edge_loc_deleted_idx,
                                                                          compute_idx,
                                                                          node_type)

        # Get the "ingress_ip_reduction" flag from RDS.
        perform_ingress_ip_reduction = INST.is_ingress_ip_reduction_enabled(
                                           db_h, custid, compute_idx)

        if instance_ids != None and len(instance_ids):
            for my_instance in instance_ids:
                my_instance_model = INST.InstanceModel(iid=my_instance, dbh=db_h)
                if (my_instance_model.get_param("id") != None and
                    my_instance_model.get_param("id") != 0):
                    my_egress_list = my_instance_model.get_param("egress_ip_list")
                    my_egress_v6_list = my_instance_model.get_param("egress_ipv6_list")
                    my_iip_list = my_instance_model.get_param("interface_ip_list")
                    my_iipv6_list = my_instance_model.get_param("interface_ipv6_list")
                    try:
                        # my_egress_list is a unicode string, when read from
                        # DB. Convert (or deserialize) it to python dict.
                        if my_egress_list in [None]:
                            my_egress_list = "{}"
                        if my_egress_v6_list in [None]:
                            my_egress_v6_list = "{}"
                        if my_iip_list in [None]:
                            my_iip_list = "{}"
                        if my_iipv6_list in [None]:
                            my_iipv6_list = "{}"

                        my_egress_list_dict = json.loads(my_egress_list)
                        logger.info("Egress IP list for instance %s is %s" % (str(my_instance_model.get_param("id")),
                                                                              str(my_egress_list_dict)))

                        my_egress_v6_list_dict = json.loads(my_egress_v6_list)
                        logger.info("Egress IPv6 list for instance %s is %s" % (str(my_instance_model.get_param("id")),
                                                                              str(my_egress_v6_list_dict)))

                        my_iip_list_dict = {}
                        if my_iip_list != None and my_iip_list != "None":
                            if isinstance(my_iip_list, str):
                                my_iip_list_dict = json.loads(my_iip_list)
                            else:
                                my_iip_list_dict = my_iip_list
                            logger.info("interface_ip_list for instance %s is "
                                    "%s" % (str(my_instance_model.get_param("id")),
                                    str(my_iip_list_dict)))
                        else:
                            logger.info("Interface IP list for instance %s is None"
                                        % str(my_instance_model.get_param("id")))

                        my_iipv6_list_dict = {}
                        if my_iipv6_list != None and my_iipv6_list != "None":
                            if isinstance(my_iipv6_list, str):
                                my_iipv6_list_dict = json.loads(my_iipv6_list)
                            else:
                                my_iipv6_list_dict = my_iipv6_list
                            logger.info("interface_ipv6_list for instance %s is "
                                    "%s" % (str(my_instance_model.get_param("id")),
                                    str(my_iipv6_list_dict)))
                        else:
                            logger.info("Interface IPv6 list for instance %s is None"
                                        % str(my_instance_model.get_param("id")))

                        to_be_del_egress_list_dict = dict()
                        if str(edge_loc_deleted_idx) in my_egress_list_dict:
                            if my_egress_list_dict[str(edge_loc_deleted_idx)] not in ["ASSIGNED_TO_NAT"]:
                                to_be_del_egress_list_dict[str(edge_loc_deleted_idx)] = \
                                    my_egress_list_dict[str(edge_loc_deleted_idx)]
                            else:
                                # In case the ingress IP reduction is off, get the values
                                # from "interface_ip_list".
                                if not perform_ingress_ip_reduction:
                                    logger.info("Instance has ingress_ip_reduction disabled; get the edge IP from the interface_ip_list")
                                    if (str(edge_loc_deleted_idx) in
                                            my_iip_list_dict):
                                        to_be_del_egress_list_dict[str(
                                            edge_loc_deleted_idx)] = \
                                                my_iip_list_dict[str(
                                                    edge_loc_deleted_idx)]
                                    else:
                                        logger.info("No entry for edge "
                                            "location [%s] in "
                                            "interface_ip_list"
                                            % edge_loc_deleted_idx)
                                else:
                                    logger.info("egress_ip_list has "
                                        "ASSIGNED_TO_NAT for edge location "
                                        "[%s]; instance has ingress_ip_reduction enabled "
                                        % edge_loc_deleted_idx)

                        # Update FQDN and delete the region from the Route53.
                        # This needs to be done for instances behind NLB
                        # as well.
                        logger.info("Trying to cleanup FQDN entries for egress_ip_dict %s"
                                    % (str(to_be_del_egress_list_dict)))
                        if len(to_be_del_egress_list_dict):
                            my_instance_model.cleanup_fqdn(db_h,
                                                           selective_egress_ip_deletion=True,
                                                           egress_ip_dict=to_be_del_egress_list_dict)

                        # Delete this region in the "egress_ip_list".
                        if str(edge_loc_deleted_idx) in my_egress_list_dict:
                            del my_egress_list_dict[str(edge_loc_deleted_idx)]

                            # Save python dict as string in to DB table
                            my_instance_model.save_egress_ip_list(
                                json.dumps(my_egress_list_dict))

                        # Delete this region in the "egress_ipv6_list".
                        if str(edge_loc_deleted_idx) in my_egress_v6_list_dict:
                            logger.info(f"Removing region {edge_loc_deleted_idx} from egress ipv6 list {my_egress_v6_list_dict}") 
                            del my_egress_v6_list_dict[str(edge_loc_deleted_idx)]
                            # Save python dict as string in to DB table
                            res = False
                            try:
                                res = my_instance_model.save_egress_ipv6_list(
                                    json.dumps(my_egress_v6_list_dict))
                            except TypeError as E:
                                error = (f"Cannot convert egress_v6_list_dict {my_egress_v6_list_dict} to JSON string due to type exception: {E.args}")
                                logger.error(error)
                            except ValueError as E:
                                error = (f"Cannot convert egress_v6_list_dict {my_egress_v6_list_dict} to JSON string due to value exception: {E.args}")
                                logger.error(error)
                            except OverflowError as E:
                                error = (f"Cannot convert egress_v6_list_dict {my_egress_v6_list_dict} to JSON string due to overflow exception: {E.args}")
                                logger.error(error)
                            if res == False:
                                logger.error(f"Failed to save egress IPv6 list {my_egress_v6_list_dict} to RDS")

                        # Let's call for delete of edge location from egress_ipv6_list_subnet as well.
                        logger.info("Trying to cleanup egress IPv6 list edge location for instance id %s" %
                                                                            str(my_instance_model.get_param("id")))
                        ipv6_mgmt = IPV6_Handler(dbh=db_h, acct_id=my_instance_model.get_param("acct_id"))
                        res = ipv6_mgmt.delete_instance_egress_ipv6_list_subnet_location(
                                                                        my_instance_model.get_param("id"),
                                                                        edge_location_region_id=edge_loc_deleted_idx)
                        if res == False:
                            # TODO: Bail out here or not ? This is delete of edge location.
                            logger.error("Failed to delete instance egress IPv6 list edge region idx %s." % (edge_loc_deleted_idx))

                        # The rest of the actions need to be done only
                        # if the instance is not behind NLB, or the instance
                        # did not have a NAT instance in front.
                        if ((my_instance_model.get_param(
                             "is_instance_behind_nlb") != 1) or
                            (my_instance_model.get_param(
                             "has_nat_instance") != 1)):
                            logger.info("Instance is not behind NLB, or "
                                "instance is not in front of NAT GW; "
                                "cleaning up egress_ip_list and removing "
                                "fw-rules in the cloud as well")

                            # Delete this region in the "interface_ip_list",
                            # if present, and save it in RDS.
                            if str(edge_loc_deleted_idx) in my_iip_list_dict:
                                del my_iip_list_dict[str(
                                    edge_loc_deleted_idx)]
                                my_instance_model.update_column_interface_ip_list(
                                    my_iip_list_dict)

                            # Delete this region in the "interface_ipv6_list",
                            # if present, and save it in RDS.
                            if str(edge_loc_deleted_idx) in my_iipv6_list_dict:
                                del my_iipv6_list_dict[str(
                                    edge_loc_deleted_idx)]
                                try:
                                    my_instance_model.update_column_interface_ipv6_list(
                                        my_iipv6_list_dict)
                                except customExceptions.InvalidInstanceIdException as instanceE:
                                    logger.error(f"Failed to update interface_ipv6_list since instance is not found {instanceE.args}")
                                except customExceptions.InvalidInterfaceIpv6ListException as listInvalidE:
                                    logger.error(f"Failed to update interface_ipv6_list since interface_ipv6_list is invalid {listInvalidE.args}")
                                except customExceptions.InvalidInterfaceIpv6ListJsonException as jsonE:
                                    logger.error(f"Failed to update interface_ipv6_list since interface_ipv6_list cannot be converted to json {jsonE.args}")
                                except customExceptions.DbUpdateException as dbE:
                                    logger.error(f"Failed to update interface_ipv6_list in RDS {dbE.args}")

                            # Trigger the changes for the instance, i.e.,
                            # remove the forwarding rules.
                            myres, _ = trigger_update_for_instance_changes(
                                           db_h,
                                           my_instance_model.get_param(
                                               "compute_region_idx"),
                                           custid,
                                           node_type=node_type,
                                           avctx=avctx)

                            if myres == False:
                                logger.error("Failed to trigger instance "
                                    "changes for compute idx %s" %
                                    (str(my_instance_model.get_param(
                                    "compute_region_idx"))))

                            # Recycle the public ip now.
                            if (str(edge_loc_deleted_idx) in
                                to_be_del_egress_list_dict):
                                to_be_recycled_ip = \
                                    to_be_del_egress_list_dict[str(
                                        edge_loc_deleted_idx)]
                                my_instance_model.return_ip_to_public_ip_pool(
                                    dbh=db_h, ip=to_be_recycled_ip)

                    except Exception as E:
                        logger.error("egress ip list update failed with exception %s %s %s"
                                     % (str(my_egress_list), str(E.args), str(traceback.format_exc())))

                else:
                    logger.info("Not updating egress_ip_list for instance "
                        "as id is 0/None; id: [%s]"
                        % (my_instance_model.get_param("id")))

        success = True
    except Exception as E:
        if logger:
            logger.error("Failed with exception %s: %s" % (str(E.args), str(traceback.format_exc())))
        success = False
    finally:
        return success

def allocate_new_egress_ipv6_standalone_gateways(dbh, inst, edge_location_id):
    success = False
    logger = dbh.logger
    try:
        acct_id = inst.get_param("acct_id")
        if acct_id in [None, 0]:
            err_msg = "Invalid account id %s found, cannot continue" % str(acct_id)
            raise Exception(err_msg)

        gcp_v6_handler = IPV6_Handler(dbh, acct_id)
        ret = gcp_v6_handler.upsert_instance_egress_ipv6_list_subnet_location(inst.get_param("id"),
                                                                              edge_location_id)
        if ret == False:
            err_msg = "Failed to upsert egress IPv6 location for instance id %s on edge location %s" % (
                                        str(inst.get_param("id")), str(edge_location_id))
            raise Exception(err_msg)

        success = True
    except Exception as E:
        if logger:
            logger.error("Failed with exception %s: %s" % (str(E.args), str(traceback.format_exc())))
        success = False
    finally:
        return success


def allocate_new_egress_ipv6_gateways(db_h, inst, edge_region_idx, node_type):
    '''
    :param inst:
    :param edge_region_idx:
    :param node_type:
    :return: True if success, False of failure
    '''

    success = False
    error = False
    logger = db_h.logger

    try:
        egress_ipv6_list = {}
        if inst.get_param("egress_ipv6_list_subnet") is not None:
            egress_ipv6_list_subnet = json.loads(inst.get_param("egress_ipv6_list_subnet"))
        logger.info("Egress IPv6 list for instance id %s is %s" % (str(inst.get_param("id")),
                                                                   str(inst.get_param("egress_ipv6_list_subnet"))))

        customer_model = CM.CustomerModel(custid=inst.get_param("custid"), dbh=db_h)
        is_clean_ip_tag = customer_model.gcp_is_clean_ip_project()
        is_ipv6_enabled = customer_model.get_param("external_ipv6_support")

        if is_ipv6_enabled != True:
            logger.info("external IPv6 support is disabled for cust id %s" % str(customer_model.get_param("id")))
            success = True
            return

        my_inst_model = INST.InstanceModel(dbh=db_h)
        all_gw_instances = my_inst_model.get_all_gp_gw_instance_for_region_and_custid(inst.get_param("custid"),
                                                                                      inst.get_param("compute_region_idx"))
        for my_inst_id in all_gw_instances:
            my_inst = INST.InstanceModel(dbh=db_h, iid=my_inst_id[0])
            if not my_inst.get_param("id"):
                logger.info("Instance %s is invalid in instance_master"
                            % my_inst_id[0])
                continue

            # Handle addition in GP gateway instances.
            logger.info("Instance %s is not behind NAT GW; allocate edge IPv6 if needed" % str(my_inst.get_param("id")))
            result = allocate_new_egress_ipv6_standalone_gateways(db_h,
                                                                  my_inst,
                                                                  edge_region_idx)
            if result == False:
                raise Exception("Failed to allocate egress IP for the gateway")

        success=True
    except Exception as E:
        logger.error("Failed with Exception %s, Traceback: %s, locals: %s" %
             (str(E.args), str(traceback.format_exc()), str(locals())))
        success = False
    finally:
        return success

def allocate_new_egress_ipv6_firewalls(dbh,
                                       inst,
                                       edge_location_id):
    '''
        Allocate new egress ipv6 address for edge location for firewall instances.
        We also need to copy the egress_ipv6_list_subnet from primary instance to secondary instance if allocation
        to primary instance is successful.
    '''
    success = False
    logger = dbh.logger
    try:
        acct_id = inst.get_param("acct_id")
        if acct_id in [None, 0]:
            err_msg = "Invalid account id %s found, cannot continue" % str(acct_id)
            raise Exception(err_msg)

        gcp_v6_handler = IPV6_Handler(dbh, acct_id)
        ret = gcp_v6_handler.upsert_instance_egress_ipv6_list_subnet_location(inst.get_param("id"),
                                                                              edge_location_id)
        if ret == False:
            err_msg = "Failed to upsert egress IPv6 location for instance id %s on edge location %s" % (
                str(inst.get_param("id")), str(edge_location_id))
            raise Exception(err_msg)

        # Now copy the list to the secondary instance.
        result = copy_egress_ipv6_subnet_list_from_primary_instance(dbh,
                                                                    inst.get_param("id"),
                                                                    inst.get_param("ha_peer"))

        if result == False:
            raise Exception("Failed to update the secondary instance's %s egress ipv6 list subnet"
                                                                    % str(inst.get_param("ha_peer")))

        success = True
    except Exception as E:
        if logger:
            logger.error("Failed with exception %s: %s" % (str(E.args), str(traceback.format_exc())))
        success = False
    finally:
        return success

def release_cnat_public_ips(dbh, compute_region_idx, custid):
    logger = dbh.logger
    try:
        service_id = CLOUD_NAT_SERVICE_ID
        from libs.model.regionmastermodel import RegionMasterModel
        my_compute_region = RegionMasterModel(dbh=dbh,
                                              edge_location_region_id=compute_region_idx)
        logger.info(f"Cloud type: {my_compute_region.get_cloud_type()}")
        customer = CM.CustomerModel(custid=custid, dbh=dbh)
        acct_id = customer.get_param("acct_id")
        if my_compute_region.get_cloud_type() not in (PROVIDER_GCP,PROVIDER_OCI):
            logger.info('Only GCP and OCI need to manage NAT IP')
            return 0
        node_type = NODE_TYPE_SWG_NAT_INSTANCE
        c = IPManagementModel(dbh).release_cnat_ip_to_customer_ip_pool(logger, acct_id,
                                               my_compute_region.get_param("cloud_provider"),
                                               compute_region_idx, node_type, service_id)

        if c == -1:
            logger.error('Failed to release_cnat_public_ips')
            return -1
        else:
            logger.info('%s nat IPs released to customer pool.' % c)
            return c
    except Exception as e:
        logger.error("Exception occurred. %s locals %s. Traceback: %s" % (str(e.args),
                                                                          str(locals()),
                                                                          str(traceback.format_exc())))
        # TODO: add avisar alert
        return -1


def perform_ip_reservation_for_compute_location(db_h, inst_model_ref):
    success=False
    try:
        node_type = inst_model_ref.get_param("node_type")
        if node_type not in (NODE_TYPE_GP_GATEWAY,):
            success = True
            return
        acct_id = str(inst_model_ref.get_param("acct_id"))
        custid = inst_model_ref.get_param("custid")
        cloud_provider = inst_model_ref.get_param("cloud_provider")
        compute_region_id=str(inst_model_ref.get_param("compute_region_idx"))

        if cloud_provider in [PROVIDER_VMWARE_ESXI]:
            db_h.logger.info("No ip mgmt/reservation needed for SASE private region!")
            success = True
            return

        # Get the group support acct id.
        customer_model = CM.CustomerModel(custid=custid, dbh=db_h)
        group_support_acct_id = customer_model.get_param("group_support_acct_id")
        is_clean_ip_tag = customer_model.gcp_is_clean_ip_project()

        ip_mgmt = ipv4_mgmt.IPHandler(db_h, str(inst_model_ref.get_param("clusterid")), acct_id)
        city_country_set_list, cloud, location = ip_mgmt.get_deployed_city_country_cloud_location(acct_id,
                                                                                                  compute_region_id,
                                                                                                  node_type)
        result = ip_mgmt.reserve_double_if_necessary(acct_id,
                                            group_support_acct_id,
                                            compute_region_id,
                                            node_type,
                                            city_country_set_list,
                                            cloud,
                                            location,
                                            inst_model_ref.get_param("has_nat_instance"),
                                            custid)

        if result == False and is_clean_ip_tag == True:
            db_h.logger.error("Failed to perform reservation for compute region idx")
            raise Exception("Failed to perform reservation for compute region idx")

        success=True
    except Exception as E:
        db_h.logger.error("Failed with exception %s, traceback %s, locals: %s" % (str(E.args),
                                                                                  str(traceback.format_exc()),
                                                                                  str(locals())))
        success=False
    finally:
        return success


def perform_ip_reservation_for_cloud_nat_region(db_h, cust_id, region_id, cnat_param_dict):
    logger = db_h.logger
    try:
        res = {"ok": False, "ip_list": []}
        cust_ep_model = CustEpModel.CustEpaasConfigModel(db_h, cust_id, region_id, PROVIDER_GCP,
                                                       node_type=NODE_TYPE_SWG_PROXY)

        customer_model = CM.CustomerModel(custid=cust_id, dbh=db_h)
        is_clean_ip_tag = customer_model.gcp_is_clean_ip_project()
        if not is_clean_ip_tag:
            logger.info("Ips created dynamically for non clean ip tenants hence skipping reservation")
            return res
        if not cust_ep_model.get_entry() or cust_ep_model.get_params("is_inst_autoscaled") != 1:
            logger.info("cloud nat feature not enabled. Skipping cloud nat ip reservation for the tenant")
            return {"ok": False, "err_msg": "Cloud nat feature not enabled"}
        if not cnat_param_dict:
            logger.info("cloud nat feature not enabled. Skipping cloud nat ip reservation for the tenant")
            return {"ok": False, "err_msg": "Cloud nat feature not enabled"}
        active_cnat_count = 0
        cnat_min_count = 0
        num_of_available_ips = 0
        cnat_public_ip_list = []

        if cnat_param_dict.get("cnat_enabled", None) == "1":
            logger.info("Getting number of ips needed for cloud nat architecture")

            cnat_public_ips = cnat_param_dict["cnat_public_ips"]
            cnat_min_count = cnat_param_dict["cnat_min_count"]
            cnat_max_count = cnat_param_dict["cnat_max_count"]

            if cnat_public_ips and cnat_public_ips != 'None':
                logger.info(f"Active cloud nats present for tenant {cust_id} and region {region_id}")
                cnat_public_ip_list = cnat_public_ips.split(", ")
                active_cnat_count = len(cnat_public_ip_list)
                logger.info(f"Number of active CNATs : {active_cnat_count}")

            cnat_min_count = min(cnat_min_count, cnat_max_count)
            logger.info(f"Total number of cnats to bringup for tenant {cust_id} region {region_id} is {cnat_min_count}")

            num_of_ips_required = cnat_min_count - active_cnat_count
            logger.info(f"Number of additional ips required {num_of_ips_required}")

            ip_manager = IPManagementModel(db_h)
            res = ip_manager.num_of_avail_ips_for_cust_region(cust_id, NODE_TYPE_SWG_PROXY, region_id, region_id)
            num_of_instances = ip_manager.count_non_upgrade_instance(cust_id, region_id, NODE_TYPE_SWG_PROXY)
            if res["ok"]:
                num_of_available_ips = res["total_num_of_ips"]

            if num_of_available_ips >= num_of_ips_required + num_of_instances:
                logger.info("ips already present in customer pool")

            else:
                total_num_of_ips_needed = (2 * num_of_instances) + cnat_min_count

                logger.info(f"Total number of ips needed including double ips reserved for active instances"
                            f" {total_num_of_ips_needed}")

                ip_manager.reserve_ips_for_cust_region(cust_id, NODE_TYPE_SWG_PROXY, region_id, region_id,
                                                       total_num_of_ips_needed, NODE_TYPE_SWG_PROXY)

            logger.info("setting status active for the cnat ips")
            res = ip_manager.reserve_cnat_ips_for_cust_region(cust_id, NODE_TYPE_SWG_PROXY, region_id,
                                                              num_of_ips_required)

            if res['ok']:
                ip_list = res["ip_list"]
                cust_ep_cfg = CustEpModel.CustEpaasConfigModel(db_h, cust_id, region_id, PROVIDER_GCP,
                                                   node_type=NODE_TYPE_SWG_PROXY)
                cnat_public_ip_list += ip_list
                cnat_public_ip_list_string = ", ".join(cnat_public_ip_list)
                cust_ep_cfg.set_param("cnat_public_ips", cnat_public_ip_list_string)
                logger.info(f"cnat ips used for cloud nat : {cnat_public_ip_list_string}")
                cust_ep_cfg.save()

        return res

    except Exception as e:
        logger.info(f"Error reserving ips for tenant {cust_id} region {region_id} . Failed with exception {e.args}")
        return {"ok": False, "err_msg": str(e.args)}

