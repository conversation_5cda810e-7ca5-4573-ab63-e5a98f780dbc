from libs.model.execute_orch_query import execute_orch_query

def get_is_central_cache_supported(dbh, custid):
    """
    Returns the value of is_central_cache_supported column in string form after
    reading the value from the cust_master table for the specied customer id.
    The returned value should be "0" or "1" on success.
    Returns None on failure.
    """

    is_central_cache_supported = None
    logger = dbh.logger
    sql = ("SELECT is_central_cache_supported FROM cust_master "
           "WHERE id = %s")
    params = (custid, )
    logger.info(f"SQL: [{(sql % params)}]")
    ret, result = execute_orch_query(dbh, sql, params, "fetchone")
    logger.info("Return from RDS: %s: %s" % (ret, result))
    if ret is False or result is None or len(result) == 0:
        logger.info("Failed to get is_central_cache_supported from RDS cust_master; or result was None")
        return is_central_cache_supported
    is_central_cache_supported = str(result[0])
    logger.info(f"Return is_central_cache_supported: {is_central_cache_supported}")
    return is_central_cache_supported

def add_central_cache_tenant_region_mapping(dbh, custid, tenant_id, region_id):
    """
    Inserts a mapping that indicates which central cache service endpoints will
    be used for the specified tenant in the specified region.
    If there is already an existing mapping present for the tenant and region,
    then the mapping will not be changed.
    """

    logger = dbh.logger
    is_central_cache_supported = get_is_central_cache_supported(dbh, custid)
    if is_central_cache_supported != "1":
        logger.info(f"Skip adding central cache service tenant region mapping for customer {custid}")
        return

    sql = ("INSERT IGNORE INTO gpcs_tenant_region_mapping (tenant_id, region_id, central_cache_service_endpoint, central_cache_service_backup_endpoint) "
            f"SELECT {tenant_id} AS tenant_id, {region_id} AS region_id, r.central_cache_service_endpoint, r.central_cache_service_backup_endpoint "
            "FROM gpcs_region_mapping AS r "
            "WHERE r.region_id = %s")
    params = (region_id, )
    logger.info(f"SQL: [{(sql % params)}]")
    ret, result = execute_orch_query(dbh, sql, params, "insert")
    logger.info(f"Return from RDS: {ret}: {result}")
    if ret is False:
        logger.info(f"Failed to insert record in gpcs_tenant_region_mapping for tenant {tenant_id}, region {region_id}: {ret}: {result}")
    else:
        logger.info(f"Successfully inserted record into gpcs_tenant_region_mapping for tenant {tenant_id}, region {region_id}: {ret}: {result}")

def remove_central_cache_tenant_region_mapping(dbh, custid, tenant_id, region_id):
    """
    Delete the mapping that indicates which central cache service endpoints will
    be used for the specified tenant in the specified region.
    If the entry does not exist, ignore the error.
    """

    logger = dbh.logger
    is_central_cache_supported = get_is_central_cache_supported(dbh, custid)
    if is_central_cache_supported != "1":
        logger.info(f"Skip deleting central cache service tenant region mapping for customer {custid}")
        return
    
    sql = ("DELETE FROM gpcs_tenant_region_mapping "
            "WHERE tenant_id = %s and region_id = %s")
    params = (tenant_id, region_id, )
    logger.info(f"SQL: [{(sql % params)}]")
    ret, result = execute_orch_query(dbh, sql, params, "delete")
    logger.info(f"Return from RDS: {ret}: {result}")
    if ret is False:
        logger.info(f"Failed to delete record from gpcs_tenant_region_mapping for tenant {tenant_id}, region {region_id}: {ret}: {result}")
    else:
        logger.info(f"Successfully deleted record from gpcs_tenant_region_mapping for tenant {tenant_id}, region {region_id}: {ret}: {result}")

