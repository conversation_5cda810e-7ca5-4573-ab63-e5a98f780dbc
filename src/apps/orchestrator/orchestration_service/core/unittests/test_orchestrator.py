import pytest
import traceback
import sys
from unittest import TestCase
from unittest.mock import patch
from unittest.mock import MagicMock as <PERSON><PERSON>
from orchestration_service.core.orchestrator import Orchs<PERSON>Handler

def print_me_one_arg(info_msg):
    print("MSG: " + info_msg)

def set_cfg(cfg):
    cfg['log-root-dir']= '/tmp/',
    cfg['region'] = 'us-west-2',
    cfg['queues'] =  []
    cfg['dbpassword'] = "mankind"
    cfg['unittest'] = True

class MockGCPIPHandlerClass():
    def __init__(self, dbh, cluster_id, acct_id):
        print("\t* MockIPHandlerClass called with locals %s" % str(locals()))

    def allocate_public_ip_for_customer(self, *args):
        print("\t* Mock_allocate_public_ip_for_customer called with args %s" % str(args))
        return ["************"]
    def copy_ip_from_active_instance(self, *args):
        print("\t* Copy_ip_from_active_instance called with args %s" % str(args))

class logger():
    def info(self, *args):
        print(args)

    def error(self, *args):
        print(args)

    def warn(self, *args):
        print(args)


class Test_OrchestrationHandler(TestCase):

    @patch('libs.cloud_providers.common.ip_mgmt.GCPIPHandler')
    @patch('libs.cloud_providers.common.ip_mgmt.IPHandler')
    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper')
    def test_allocate_new_egress_ip_non_gateways_success(self, mocked_IPHandler, mocked_GCPIPHandler):
        print("Running test 'test_allocate_new_egress_ip_non_gateways_success ...'")

        # These are the side effects
        mocked_GCPIPHandler.side_effect = MockGCPIPHandlerClass

        # This is is the Instance mock object.
        instCtxMock = Mock()
        instCtxMock.egress_ip_list = '{"319": "**************", "339": "**************", ' \
                                     '"301": "**************", "337": ' \
                                     '"************", "299": "*************", "331": "**************", ' \
                                     '"361": "**************", "333": "************", "305": "************", ' \
                                     '"381": "*************", "315": "*************", "297": "*************", ' \
                                     '"235": "**************", "237": "**************", "233": "**************", ' \
                                     '"379": "************", "329": "************", "349": "************", "363": ' \
                                     '"*************", "289": "*************", "321": "************", ' \
                                     '"353": "**************", "359": "*************", "351": "*************", ' \
                                     '"325": "**************", "327": "34.141.46.117", "367": "34.159.193.82", ' \
                                     '"373": "34.159.197.58", "365": "34.159.231.77", "341": "34.159.200.213", ' \
                                     '"377": "34.159.91.228", "291": "34.141.93.161", "317" : "************"}'
        instCtxMock.logger.info.side_effect=print_me_one_arg
        instCtxMock.logger.error.side_effect = print_me_one_arg
        instCtxMock.id = 7000
        instCtxMock.ha_peer = 7001
        orchHandler=None

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = logger()
            orchHandler.db_h = None
        except Exception as E:
            print("Exception %s, Traceback %s" % (str(E.args), str(traceback.format_exc())))

        print(orchHandler)
        #"317": "**************"
        success = orchHandler.allocate_new_egress_ip_non_gateways(instCtxMock, 317, 50)

        assert 1 == success
