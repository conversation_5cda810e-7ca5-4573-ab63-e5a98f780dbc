import json
import traceback
from libs.common.shared.sys_utils import NODE_TYPE_GP_GATEWAY, NODE_TYPE_REMOTE_NET, PROVIDER_AWS_DB_ENUM_VALUE
from libs.model.instancemodel import InstanceModel
from libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt import allocate_egress_ipv6_for_new_instance_impl
from libs.cloud_providers.common.instance_trigger_update import trigger_update_for_instance_changes
from libs.model.vpcmodel import VpcModel
from libs.cloud_providers.common.ip_management.ipv6.aws.ipv6_mgmt_1 import IPV6_Handler as AWS_IPV6_Handler
from libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt import get_zone_for_vpc

def add_aws_ipv6_address_to_salt_profile(dbh, salt_profile, instance, zone_id):
    success =False
    try:
        ipv6_handler = AWS_IPV6_Handler(dbh=dbh, acct_id=instance.get_param('acct_id'))
        ret, ipv6_address = ipv6_handler.get_ipv6_address_by_account_id(
            compute_region_idx=instance.get_param("compute_region_idx"),
            zone="zone-" + zone_id,
            instance_id=instance.get_param('id'))
        if ret == False:
            error_msg = ("Failed to get IPv6 address for instance id %s" %
                         (str(instance.get_param('id'))))
            dbh.logger.error(error_msg)
            raise Exception(error_msg)
        salt_profile["public_ipv6_address"] = ipv6_address + '/80'
        success = True
    except Exception as E:
        dbh.logger.error("Failed with exception %s, %s"  % (str(E.args), str(traceback.format_exc())))
    finally:
        return success

def get_zone_name_from_vpc_id(dbh, vpc_id, ha_flag):
    """
    Get the zone ID for a given VPC ID.

    Args:
        dbh: The database handler
        vpc_id: The VPC ID
        ha_flag: The HA flag

    Returns:
        success: Boolean indicating if lookup was successful
        zone_id: The zone Name for the VPC

    This function calls the VPC model to get the zone name and VPC type
    for the provided VPC ID. It checks the returned VPC type to determine
    the zone ID:

    - If VPC type is premium_zone, return None
    - If VPC type is dp, return the looked up zone name
    - Otherwise, return False

    The function handles logging and exceptions appropriately.
    """
    success = False
    vpcmodel = VpcModel()
    success, zone_name, vpc_type = vpcmodel.get_zone_name_and_vpc_type_from_vpc_id(dbh, vpc_id, ha_flag)
    dbh.logger.info("Found zone name as %s for vpc type %s" % (str(zone_name), str(vpc_type)))
    if success == False:
        return False, None

    if vpc_type == "premium_zone":
        return True, None
    elif vpc_type =="dp":
        return True, zone_name
    else:
        return False, None

def handle_aws_ipv6_instance_change(dbh, salt_profile, instance):
    success = False
    try:
        if instance.get_param("id") == instance.get_param("clusterid"):
            ha_flag = 0
        else:
            ha_flag = 1
        # Get the vpc zone for the instance.
        region = instance.get_param("compute_region_idx")
        custid = instance.get_param("custid")
        vpc_instance = VpcModel(region, dbh, ha_flag=ha_flag, custid=custid)
        zone_id = get_zone_for_vpc(dbh, vpc_instance)

        if zone_id == None:
            dbh.logger.error("IPv6 is not supported for zone id %s" % str(zone_id))
            success = True
            return
        if zone_id:
            dbh.logger.info("Found zone id %s for instance id %s" % (zone_id, str(instance.get_param('id'))))
            lret = add_aws_ipv6_address_to_salt_profile(dbh, salt_profile, instance, zone_id)
            if lret == False:
                raise  Exception("Failed to fetch IPv6 address for the instance id %s" % str(instance.get_param("id")))
            salt_profile["has_external_ipv6"] = True
            success = True
            return
        success = False
    except Exception as E:
        dbh.logger.error("Failed with exception %s, %s" % (str(E.args), str(traceback.format_exc())))
        success = False
    finally:
        return success

def set_salt_profile_external_ipv6(dbh, instance):
    """Sets the salt profile external IPv6 for an instance.

    :param dbh: The database handle.   # database handle object
    :type dbh: object
    :param instance: The instance object. # instance object
    :type instance: object
    :returns: True if successful, False otherwise.
    """
    logger = dbh.logger   # logger object

    try:
        cft_params = instance.get_param("salt_profile") # get salt_profile parameter
        logger.info("Instance %s, old salt profile %s" % (str(instance.get_param("name")),
                                                          str(instance.get_param("salt_profile"))))
        cft_dict = json.loads(cft_params)  # load JSON string into dict
        if instance.get_param("cloud_provider") in [PROVIDER_AWS_DB_ENUM_VALUE]:
            # we need to acquire public IPv6 address as well.
            lret = handle_aws_ipv6_instance_change(dbh, salt_profile=cft_dict, instance=instance)
            if lret == False:
                raise  Exception("Failed to migrate cluster %s to IPv6" % str(instance.get_param("id")))
        else:
            cft_dict["hasExternalIPv6"] = True # set hasExternalIPv6 to True

        cft_params = json.dumps(cft_dict) # dump dict back to JSON string
        instance.set_param("salt_profile", cft_params) # set salt_profile parameter
        logger.info("Instance %s, new salt profile %s" % (str(instance.get_param("name")),
                                                          str(instance.get_param("salt_profile")))) # log new salt_profile

        instance.save()  # save instance
        return True
    except Exception as e:
        logger.error(
            "Failed to set salt profile external IPv6 for instance %s: %s" % (instance.get_param('name'), str(e)))
        logger.error(traceback.format_exc())
        logger.error(locals())
        return False

def migrate_cluster_to_ipv6(dbh, clusterid):
    """Migrates an existing cluster to use IPv6 networking.

        Args:
            cluster_name (str): The name of the cluster to migrate.

        Returns:
            bool: True if the migration succeeded, False otherwise.
    """

    # Initialize logger
    logger = dbh.logger
    success = False
    try:
        # Get instance model for given cluster ID
        instance = InstanceModel(iid=clusterid, dbh=dbh)
        # Check if instance exists
        if instance.get_param("id") == None:
            err_msg = ("No instance with id %s found" % str(clusterid))
            raise Exception(err_msg)

        # Set IPv6 flag on salt profile
        lret = set_salt_profile_external_ipv6(dbh=dbh, instance=instance)
        # Check if salt profile update succeeded
        if lret == False:
            raise Exception("Failed to set sale profile's external IPv6 flag")

        if instance.get_param("cloud_provider") in [PROVIDER_AWS_DB_ENUM_VALUE]:
            logger.info("No Egress IPv6 assignment needed for AWS, continuing")
        else:
            # Handle the egress IPv6 for this cluster.
            lret = allocate_egress_ipv6_for_new_instance_impl(dbh=dbh,
                                                              instance_id=instance.get_param("id"),
                                                              edge_region_idx=instance.get_param("compute_region_idx"),
                                                              ha_peer=instance.get_param("ha_peer"),
                                                              is_passive=False)
            if lret == False:
                raise Exception("Failed to allocate egress IP for instance with cluster id %s" %
                                str(instance.get_param("id")))

        if instance.get_param("node_type") == NODE_TYPE_REMOTE_NET:
            # Get the instance ref for the peer.
            peer_instance = InstanceModel(iid=instance.get_param("ha_peer"), dbh=dbh)
            # Check if instance exists
            if peer_instance.get_param("id") == None:
                err_msg = ("No instance with id %s found" % str(peer_instance))
                raise Exception(err_msg)

            # Set IPv6 flag on salt profile
            lret = set_salt_profile_external_ipv6(dbh=dbh, instance=peer_instance)
            # Check if salt profile update succeeded
            if lret == False:
                raise Exception("Failed to set sale profile's external IPv6 flag")

            if instance.get_param("cloud_provider") in [PROVIDER_AWS_DB_ENUM_VALUE]:
                logger.info("No Egress IPv6 assignment needed for AWS, continuing")
            else:
                lret = allocate_egress_ipv6_for_new_instance_impl(dbh=dbh,
                                                                  instance_id=peer_instance.get_param("id"),
                                                                  edge_region_idx=peer_instance.get_param("compute_region_idx"),
                                                                  ha_peer=peer_instance.get_param("ha_peer"),
                                                                  is_passive=True)
                if lret == False:
                    raise Exception("Failed to allocate egress IP for passive instance with id %s" %
                                        str(peer_instance.get_param("id")))

        # Now trigger update for instance changes.
        success, trigger_update_seq_nr = trigger_update_for_instance_changes(dbh=dbh,
                                                            region_idx=instance.get_param("compute_region_idx"),
                                                            custid=instance.get_param("custid"),
                                                            cloud_provider=instance.get_param("cloud_provider"))
        if success == False:
            raise Exception("Failed to trigger update for instance changes")
        logger.info(f"Triggered update successfully for cluster id {clusterid} with seq # {trigger_update_seq_nr}")

        success = True
    # Catch any exceptions
    except Exception as E:
        logger.error(
            "Failed to set salt profile external IPv6 for instance %s: %s" % (clusterid, str(E.args)))
        logger.error(traceback.format_exc())
        logger.error(locals())
        success = False
    finally:
        return success

