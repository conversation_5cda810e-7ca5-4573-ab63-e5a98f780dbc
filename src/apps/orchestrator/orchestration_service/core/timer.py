import logging
import logging.handlers
import time
from libs.msg_defs.interface import *
from libs.msg_defs.handler import *
from libs.msg_defs.workflows import *
import libs.model.orchjobs as JOB
from libs.common.utils import prepare_job_status_msg

class TimerHandler(GenericHandler):
    status = "TIMER"
    def __init__(self, cfg):
        self.db_h = None
        self.qname = 'timer_q.fifo'
        # Init the logging queue
        super(<PERSON>r<PERSON><PERSON><PERSON>, self).init_generic_logger(qname=self.status.lower())
        self.sqs = SqsApi(cfg['region'], cfg['queues'], self.logger)
        self.valid_msgs = (TimerMsg,)
        self.ids = {}
        self.running = True
        #Init the failure queue.
        super(<PERSON>r<PERSON><PERSON><PERSON>, self).init_failure_queue('timer_failq')

    def process_msg(self, msg, workflow, jobid, result):
        job = JOB.OrchJobs(jobid = jobid, dbh = self.db_h)
        self.logger.debug("Timer process_message called")
        if not job.jobid:
            self.error = ("TimerHandler:process_msg:Unable to find job"
                " with id %d" % (jobid,))
            self.logger.error("TimerHandler:process_msg:Unable to find job"
                " with id %d" % (jobid,))
            return False
        cursor = self.db_h.get_cursor()
        try:
            param  = (msg.cb_q, msg.opaque, msg.interval)
            sql = ("insert into timer_jobs (`callback_q`, `callback_data`, `interval`)"
                " values (%s, %s, %s)") 
            cursor.execute(sql, param)
        except Exception as ex:
            self.error = ("TimerHandler:process_msg: Unable to insert job into"
                " timer table: %s" % (str(ex)))
            self.logger.error("TimerHandler:process_msg: Unable to insert job into"
                " timer table: %s" % (str(ex)))
            job.status_msg = prepare_job_status_msg("Failed to add timer into database, %s" % (str(ex)))
            job.save_job(self.db_h)
            self.db_h.cursorclose(cursor)
            return False
        job.status_msg = prepare_job_status_msg("Timer started for %s seconds %d" % (msg.cb_q, msg.interval))
        job.save_job(self.db_h)     
        self.db_h.cursorclose(cursor)
        return True
    # This is called every 10 seconds
    def poll_hook(self):
        cursor = self.db_h.get_cursor()
        processed = []
        self.logger.info ("Timer poll hook called")
        try:
            sql = ('select id, callback_q, callback_data from timer_jobs where' 
                    ' (expiry < now()) and expired = 0')
            cursor.execute(sql)
            res = cursor.fetchall()
            for row in res:
                id, callback_q, callback_data = row
                self.send_to_next_q(callback_q, [callback_data])
                processed.append(id)
            for id in processed:
                params = (id,)
                cursor.execute("delete from timer_jobs where id= %s", params)
        except Exception as ex:
            self.logger.error("TimerHandler:poll_hook():Unable to get timer "
                    "events: %s" % (str(ex),))
            return
        self.db_h.cursorclose(cursor)


