import json
import base64
import traceback
import ast
import os
import requests
import time
import re

from libs.msg_defs.interface import *
from libs.msg_defs.handler import *
import libs.model.custnodemodel as CSN
from libs.model.custnodemodel import CustNodeModel
import libs.model.instancemodel as INST
import libs.model.custmodel as CM
import libs.model.coloonboardingmastermodel as COLOM
from libs.model.custEpaasConfigModel import CustEpaasConfigModel
import libs.model.orchjobs as JOB
from libs.common.utils import prepare_job_status_msg, sns_sp_interconnect_firewall_rule_send_msg, \
    publish_avisar_event_with_tenant_region_nodetype_key, colo_100g_no_gre_feature_flag_enabled
from libs.cloud_providers.common.utils import update_cfgserv_cloud_objects_summary
from libs.cloud_providers.common.instance_trigger_update import trigger_update_for_instance_changes
from libs.common.shared.sys_utils import *
from libs.cloud_providers.gcp.instance_manager.gcp_colo_utils import check_gcp_cust_region_colo_cr_created
from libs.cloud_providers.common.gcp_alert_policy_helper import gcp_delete_alert_policy
from libs.apis.region_master_api import *
from libs.cfg import *
import libs.cloud_providers.common.ip_management.ipv4.ip_mgmt as IP_MGMT
from libs.apis.commit_job_status_api_orch import save_orch_success_state_to_commit_status_detail_table, \
    update_sub_status_for_commit_job, save_orch_failed_state_to_commit_status_detail_table, \
    save_bringup_status_to_commit_details_table, update_status_for_commit_job
from libs.logging_infra.orch_context import orch_store_in_trace_db
from libs.model.aggbandwidthmgmtmodel import AggBandwidthMgmtModel
from libs.model.instancemodel import update_change_to_dns_manager, \
    is_swg_proxy_deployment_needed, get_instance_ids_by_tenant_id_and_aggregate_region_name, \
    find_instances_by_custid_and_node_type
from orchestration_service.core.orchestrator_onramp_mgmt import (get_onramp_rn_count, allocate_onramp_rn_nodes,
                                                                 delete_child_onramp_rn)
from orchestration_service.core.orchestrator_nat_mgmt import (is_nat_gateway_nlb_needed,
                                                              delete_nat_instances_by_custid_and_region_id,
                                                              is_nat_gateway_ilb_needed)
from orchestration_service.core.orchestrator_nlb_mgmt import (delete_nlb_instance_by_custid_and_region_id,
                                                              delete_onramp_ilb_instance_by_custid_and_region_id)
from orchestration_service.core.orchestrator_central_cache import add_central_cache_tenant_region_mapping, \
    remove_central_cache_tenant_region_mapping
from orchestration_service.core.orchestrator_topology_mgmt import get_count_of_topology_instances_not_marked_for_deletion_in_region
from libs.cloud_providers.common.swgproxy_bringup import swgproxy_bringup
from libs.common.shared.okyo_fw_notify import notify_okyo_info
from libs.common.shared.py3_utils import b64encode
from libs.cloud_providers.common.utils import send_topology_update_msg_to_cfgservice
from libs.common.shared.grpc.proto.avisarpb.avisar_pb2_client import AvisarContext, \
    AvisarService, ORCHESTRATION_STATUS_FAILED, \
    ORCHESTRATION_STATUS_SUCCESS, \
    METRIC_SEVERITY_CRITICAL, \
    METRIC_SEVERITY_INFO, ORCHESTRATION_INSTANCE_CUST_TOPOLOGY_UNBIND_FAILED
from orchestration_service.core.orchestrator_inst_upgrade_mgmt import pinned_gp_gateway_instance_upgrade_orchestration, \
    delete_pinned_instance_entry_by_new_inst_id, is_current_upgrade_instance
from orchestration_service.core.orchestrator_ip_mgmt import allocate_new_egress_ip_nat_gateways, \
    allocate_new_egress_ip_gateways, \
    allocate_new_egress_ipv6_gateways, \
    allocate_new_egress_ip_non_gateways, \
    unbind_egress_ips_from_node_impl, \
    delete_gp_gateway_edge_location_references_impl, \
    allocate_new_egress_ipv6_firewalls, \
    perform_ip_reservation_for_compute_location, \
    release_cnat_public_ips
from orchestration_service.core.orchestrator_nat_mgmt import allocate_nat_gateways
from orchestration_service.core.orchestrator_nlb_mgmt import allocate_nlb, allocate_ilb, allocate_interconnect_onramp_ilb
from orchestration_service.core.orchestrator_ipv6_migration import migrate_cluster_to_ipv6

from libs.cloud_providers.common.upgrade_envoy_version_gcp import update_instance_template
from libs.cloud_providers.common.upgrade_ep_envoy_outside_panos import upgrade_ep_envoy_outside_panos
from libs.cloud_providers.common.ep_cnat_scaleout_helper import scale_ep_cnat
from libs.cloud_providers.common.masque_helper import is_masque_feature_enabled
from orchestration_service.core.orchestrator_commit_status_cfgservice import send_configservice_commit_status
from libs.cloud_providers.common.ep_cnat_scaleout_helper import is_ep_cnat_feature_enabled
from orchestration_service.core.orchestrator_integrity_checker import perform_integrity_checker_tasks
from libs.apis.region_master_api import gpcs_get_cloud_type_from_region_idx
from orchestration_service.core.orchestrator_priv_sase_node_update import handle_priv_sase_node_update_event
from orchestration_service.core.orchestrator_zti import handle_zti_location_add, handle_zti_location_delete
from libs.cloud_providers.common.adem_fw_notify import is_adem_notification_enabled, delete_notify_adem_service
from libs.common.sanitize_logger import SanitizeLogger



class OrchstrationHandler(GenericHandler):
    status = "ORCHESTRATION"
    running = True

    def __init__(self, cfg):
        self.error = ""
        unitTest = cfg.get('unittest', False)
        if unitTest:
            return
        self.db_h = None
        self.qname = 'orchestration_q.fifo'
        # Init the logging queue
        super(OrchstrationHandler, self).init_generic_logger_rastro(qname=self.status.lower())
        # Init the failure queue.
        super(OrchstrationHandler, self).init_failure_queue_rastro('orchestrator_failq')
        self.sqs = SqsApi(cfg['region'], cfg['queues'], self.logger)
        self.ids = {}
        self.name = "ORCHESTRATION"
        self.valid_msgs = (OrchestratorMsg, OrchestrationEventsMsg)
        self.logger.info("Child Constructor")
        self.err_msg = None
        self.return_err_msg = ""
        self.vm_type_map = dict()
        self.vm_type_map['48'] = "FIREWALL"
        self.vm_type_map['49'] = "GPGATEWAY"
        self.vm_type_map['50'] = "GPPORTAL"
        self.vm_type_map['51'] = "SERVICECONNECTION"
        self.vm_type_map['152'] = "CLEANPIPE"
        self.shard_id = int(os.environ["HOSTNAME"].split("orchestration-", 1)[1])
        self.avctx = AvisarContext(logger=self.logger,
                                   service_endpoint=cfg.get('avisar_service_endpoint'),
                                   forwarding_enabled=cfg.get('avisar_forwarding_enabled', False),
                                   sender="OrchestrationService",
                                   aws_env=cfg.get("aws_env"))
        # Init the Database.
        self.init_db(avctx=self.avctx)
        # Update the rastro trace DB table.
        if self.db_h:
            if self.update_trace_db() == False:
                raise Exception("Failed to update the rastro tarce Db, Cannot continue...")
        self.sanitize_logger = SanitizeLogger(self.logger)

    def update_trace_db(self):
        ret = False
        try:
            # Set the DB handler in the respective rastro context:-
            self.rastro_ctx.set_db_handler(self.db_h)
            # Store info about this logger in the trace DB.
            lret = orch_store_in_trace_db(self.db_h,
                                          self.rastro_ctx.get_trace_id(),
                                          tenant_id=999999999,
                                          workflow_type="Orch OrchestrationQ",
                                          workflow_id=int(time.time()))
            if lret == None:
                raise Exception("Failed to store info reg workflow type Orch OrchestrationQ")
            ret = True
        except Exception as E:
            self.logger.error(
                "Failed to update trace id in the master database. %s %s" % (str(E.args), str(traceback.format_exc())))
        finally:
            return ret

    def set_log_context(self, custnode, custid=None):
        if custid is not None:
            customer = CM.CustomerModel(custid=custid, dbh=self.db_h)
        else:
            customer = CM.CustomerModel(custid=custnode.custid, dbh=self.db_h)
        self.tenant_id = customer.get_param("acct_id")

    def bind_mp_to_dp_instances(self, inst_ids, inst_ids_dp):
        failed = 1
        try:
            # in 2.1 release, MP and DP are a static pair where mp1 is tied to dp1 
            #   and mp2 tied to dp2. Swap the ids for dp2 such that mp1_id always
            #   corresponds to DP's own MP. Mark for both mp and dp.
            if len(inst_ids) == 2 and len(inst_ids_dp) == 2:
                mp1 = INST.InstanceModel(inst_ids[0], dbh=self.db_h)
                mp2 = INST.InstanceModel(inst_ids[1], dbh=self.db_h)
                dp1 = INST.InstanceModel(inst_ids_dp[0], dbh=self.db_h)
                dp2 = INST.InstanceModel(inst_ids_dp[1], dbh=self.db_h)

                mp1.set_param("mp1_id", inst_ids[0])
                dp1.set_param("mp1_id", inst_ids[0])
                mp1.set_param("mpdp_clusterid", inst_ids[0])
                mp2.set_param("mpdp_clusterid", inst_ids[0])
                dp1.set_param("mpdp_clusterid", inst_ids[0])
                dp2.set_param("mpdp_clusterid", inst_ids[0])
                mp2.set_param("mp1_id", inst_ids[1])
                dp2.set_param("mp1_id", inst_ids[1])
                mp1.save()
                dp1.save()
                mp2.save()
                dp2.save()
                failed = 0
        except Exception as E:
            self.sanitize_logger.error("bind_mp_to_dp_instances: Failed with exception %s %s %s" %
                              (str(E.args), str(traceback.format_exc()), str(locals())))
        finally:
            return failed

    # Return DP instances corresponding to passed MP instances
    def get_dp_instances(self, mp_instances):
        dp_instances = []
        cursor = None
        sql = ""
        try:
            sql = "select id from instance_master where ((mp1_id = %s or " \
                  "mp1_id = %s) and slot_nr > %s)" % (mp_instances[0].get_param("id"),
                                                      mp_instances[1].get_param("id"),
                                                      INSTANCE_ROLE_MP)
            cursor = self.db_h.get_cursor()

            self.db_h.logger.info("get_dp_instances:Executing %s" % str(sql))
            cursor.execute(sql, None)
            res = cursor.fetchall()
            self.db_h.logger.info("get_dp_instances:Result: %s" % (str(res)))
        except Exception as ex:
            if cursor:
                self.db_h.cursorclose(cursor)
            self.db_h.logger.error(traceback.format_exc())
            self.db_h.logger.error("Failed to retrieve DP inst for MPs: %s %s" % (sql, str(ex)))
            return dp_instances

        if res is None or len(res) < 2:
            self.db_h.logger.error("Failed to retrieve DP inst for MPs: %s %s" %
                                   (mp_instances[0].get_param("id"),
                                    mp_instances[1].get_param("id")))
            return dp_instances
        for iid in res:
            dp_instances.append(INST.InstanceModel(iid=iid[0], dbh=self.db_h))

        return dp_instances

    def allocate_instance(self, node_id, vmtype, region_idx, version, custid,
                          custnode, transient = 0, is_dynamic = 0, 
                          is_clean_pipe = 0, panorama_job_id = None ,
                          is_okyo_instance_refresh = False, has_nat_gateway = False,
                          has_nlb = False):

        self.logger.info("orchestrator:allocate_instance(): is_clean_pipe:%d " % is_clean_pipe)
        instances = []
        dp_instances = []
        inst_ids_dp = []
        is_new = False
        res = None
        spn_name = custnode.spn_name
        is_mp_dp_seperation = False
        if vmtype == 'FIREWALL' and custnode.dp_gpcs_instance_size is not None:
            self.logger.info(
                "MP/DP seperation is enabled for custid %s and region_idx %s" % (str(custid), str(region_idx)))
            # Since MP DP seperation is not supported currently, commenting out.
            # is_mp_dp_seperation = True
        cloud_provider = gpcs_get_cloud_type_from_region_idx(self.db_h,
                                                             region_idx, custid=custid)
        self.logger.info(f"allocate_instance: Cloud provider for region {region_idx} is {cloud_provider}")

        # Find free instance. If not found allocate a new one.
        if custnode.inbound_access != 'dedicated' and cloud_provider not in [PROVIDER_VMWARE_ESXI]:
            if vmtype == "GPGATEWAY" and not is_dynamic and not custnode.is_device_tunnel_scale_scenario():
                res = self.db_h.get_free_gp_gateway_instance(node_id, vmtype, transient)
                if res is not None and res.ok and custnode.okyo_edge_site_id > 0 and not transient:
                    gp_inst = res.result[0]
                    # Refresh custnode again from db
                    custnode = CSN.CustNodeModel(custnode.id, dbh=self.db_h)
                    notify_okyo_info(custnode.name, custnode.okyo_data, gp_inst.get_param("id"),
                                     gp_inst.get_param("cloud_provider"), True,
                                     self.logger)
                elif res.result == -1:
                    self.logger.error("[allocate_instance] During get_free_gp_gateway_instance, exception occured vm_type: %s" % str(vmtype))
                    return (None, None, False)
            elif vmtype == "SWGPROXY" and not is_dynamic:
                res = self.db_h.get_free_swg_proxy_instance(node_id, vmtype, transient)
            elif custnode.license_type == 'FWAAS-AGGREGATE' and vmtype == 'FIREWALL' and custnode.transport_type != 'pa-connect':
                error_list = []
                res = self.db_h.get_allocated_spn(custid, custnode.id, custnode.spn_name, transient, error_list)
                if len(error_list):
                    lret, msg = error_list[0]
                    if lret < 0:
                        self.logger.error("Aggregate Bandwidth, Pre checks failed, cannot continue! %s" % str(msg))
                        return instances, dp_instances, is_new
            elif vmtype == "NAT":
                self.logger.info("NAT instance IP reuse is not supported. Will create a new instance!")
            elif vmtype == "NLB":
                self.logger.info("NLB instance IP reuse is not supported. Will create a new instance!")
            elif vmtype == "SERVICE_ILB":
                self.logger.info("SERVICE_ILB instance IP reuse is not supported. Will create a new instance!")
            elif vmtype == "REMOTE_NETWORK_ILB":
                self.logger.info("RN ILB instance IP reuse is not supported. Will create a new instance!")
            else:
                res = self.db_h.get_free_instance(custnode.id, vmtype, transient)

        self.logger.info(f"allocate_instance: Result {res} for region {region_idx} for cloud {cloud_provider}")
        if res is not None and res.ok:
            instances.append(res.result[0])
            if len(res.result) > 1:
                instances.append(res.result[1])
                # Populate corresponding DPs when present
                if is_mp_dp_seperation:
                    dp_instances = self.get_dp_instances(instances)
            self.logger.info(f"allocate_instance: Instances {instances} DP instances {dp_instances} for region {region_idx} for cloud {cloud_provider}")
            return instances, dp_instances, False
        # for now, we're choosing to not create new instances in refresh scenario's
        elif is_okyo_instance_refresh:
            custnode.set_okyo_inst_change_status(self.db_h, "FAIL")
            return (None, None, False)

        # get spn_name for sase_fabric tenant case
        # this is applicable to aws instances with site based license
        self.logger.info(f"Checking spn_name {custnode.spn_name} for region {region_idx} for cloud {cloud_provider}")
        if (custnode.node_type == NODE_TYPE_REMOTE_NET and (custnode.spn_name == "" or custnode.spn_name is None) and self.db_h.is_sase_fabric_enabled(custid)):
            compute_region_idx = gpcs_get_compute_region_idx_from_edge_region_idx(self.db_h, region_idx)
            compute_region_name = gpcs_get_edge_region_name_from_edge_region_idx(self.db_h, compute_region_idx)
            spn_name = self.get_next_service_index_by_cust_id_and_region(custid, compute_region_idx, compute_region_name)
            self.logger.info("RN HP instance without spn_name in cust_topology Allocate spn_name for new instance %s" % spn_name)

        # If not found create an instance
        if is_mp_dp_seperation == True:
            inst_ids, inst_ids_dp = self.db_h.get_new_instance_mp_dp_seperation(version, vmtype, region_idx,
                                        custnode, transient, custid,
                                        is_clean_pipe = is_clean_pipe,
                                        panorama_job_id = panorama_job_id,
                                        has_nat_gateway = has_nat_gateway,
                                        has_nlb = has_nlb)
        else:
            inst_ids = self.db_h.get_new_instance(version, vmtype, region_idx, custnode, transient, custid,
                           is_clean_pipe = is_clean_pipe,
                           panorama_job_id = panorama_job_id,
                           has_nat_gateway = has_nat_gateway,
                           has_nlb = has_nlb, spn_name = spn_name)
            
        if not inst_ids:
            self.error = self.db_h.error
            self.logger.error("Unable to get new instance for node type %s" % str(vmtype))
            return (None, None, False)

        is_new = True
        failed = 0
        for inst_id in inst_ids:
            inst = INST.InstanceModel(inst_id, dbh=self.db_h)
            instances.append(inst)

        if is_mp_dp_seperation and not inst_ids_dp:
            failed = 1
        else:
            for inst_id in inst_ids_dp:
                inst = INST.InstanceModel(inst_id, dbh=self.db_h)
                dp_instances.append(inst)

            # We have new or existing instance
            for instance in instances:
                lret = instance.bind(self.db_h, custid, vmtype, node_id,
                                     is_new, custnode, transient)
                if not lret:
                    failed = 1
                    break

            if is_mp_dp_seperation:
                # Bind MP to DP instances.
                failed = self.bind_mp_to_dp_instances(inst_ids, inst_ids_dp)

        if failed:
            self.error = self.db_h.error
            self.logger.error("orchestrator:allocate_instance():Unable to bind "
                              "instance with customer node")
            if is_new:
                for inst in (instances + dp_instances):
                    inst.delete(self.db_h)
            return None, None, False

        return instances, dp_instances, is_new

    def check_region_change(self, custnode, custid=0):
        """
        Given a custnode object, determine if there's compute region change
        for that site.

        'old_region' column in cust_topology is filled by 'process_onboarding'
        step function when location of an RN/SC is changed

        when is compute region NOT changed?
            1) old_region == region
            2) old_region != region but region_master.compute == instance.compute

        3) For other cases, compute region change has happened.

        return: True if compute region changed, False otherwise.
        """
        if custnode is False:
            self.logger.info("Empty custnode object. Cannot check region change.")
            return False

        instance = INST.InstanceModel(iid=custnode.instance1_id, dbh=self.db_h)
        cust_topo_compute_region_id = \
            gpcs_get_compute_region_idx_from_edge_region_idx(self.db_h,
                                                             custnode.region, custid=custid)

        self.logger.info("Edge: %s, Old Region: %s, Compute: %s, Compute of "
                         "Instance: %s" % (custnode.region, custnode.old_region,
                                           cust_topo_compute_region_id, instance.get_param("compute_region_idx")))

        # Case 1
        if custnode.old_region == custnode.region:
            self.logger.info("No change in region. Same edge location (%s)." % custnode.region)
            return False

        # Case 2
        if str(cust_topo_compute_region_id) == str(instance.get_param("compute_region_idx")):
            self.logger.info("No change in region. Instance and Region computes "
                             "match (%s)." % instance.get_param("compute_region_idx"))
            return False

        # Edge location has changed and Compute region has changed too.
        self.logger.info("Compute locations have CHANGED.")
        return True

    def delete_instance_by_instance_id_list(self, dp_inst_ids):
        success = False
        dbh = self.db_h
        logger = dbh.logger
        try:
            for inst_id in dp_inst_ids:
                im = INST.InstanceModel(inst_id, dbh)
                im.delete(dbh)
            success = True
        except Exception as E:
            self.sanitize_logger.error("Failed to delete instance/s. Exception: %s. traceback: %s locals: %s"
                              % (str(E.args), str(traceback.format_exc()), str(locals())))
        finally:
            return success

    def is_ipv6_enabled_for_instance(self, inst):
        status=False
        try:
            salt_profile =  json.loads(inst.get_param("salt_profile"))
            status = salt_profile.get('hasExternalIPv6', False)
        except Exception as E:
            status = False
            raise Exception("Failed to get salt_profile for an instance, Exception: %s, "
                            "traceback: %s, locals: %s" %
                            (str(E.args), str(traceback.format_exc()), locals()))
        finally:
            return status

    def allocate_new_egress_ip(self, inst, edge_region_idx, node_type):
        '''
        Allocate a new Egress IP to an instance. The allocated IP is regional to edge_region_idx
        '''
        success = False
        error = False
        is_ipv6_enabled = False
        try:
            if inst.get_param("cloud_provider") not in [ PROVIDER_GCP, PROVIDER_GCP_DB_ENUM_VALUE, PROVIDER_OCI, PROVIDER_OCI_DB_ENUM_VALUE ]:
                self.logger.info("Cloud provider %s does not support edge locations" %
                                 str(inst.get_param("cloud_provider")))
                success = True
                return

            # If its a secondary instance skip egress IP mgmt.
            if inst.get_param("id") != inst.get_param("clusterid"):
                self.logger.info("Egress IP rule management already taken care by primary instance %s" %
                                 str(inst.get_param("clusterid")))
                success = True
                return

            is_ipv6_enabled = self.is_ipv6_enabled_for_instance(inst)
            if is_ipv6_disabled_for_location(self.db_h, edge_region_idx, custid=inst.get_param("custid")):
                self.logger.info(f"Skipping IPv6 assignment for region id {edge_region_idx} since its disabled")
                is_ipv6_enabled = False

            if node_type == NODE_TYPE_REMOTE_NET:
                success = allocate_new_egress_ip_non_gateways(self.db_h, inst, edge_region_idx, node_type)
                if success == False:
                    raise Exception("Failed to allocate egress ip list for firewall %s and edge location %s"
                                    % (str(inst.get_param("id")),
                                       str(edge_region_idx)))

                if is_ipv6_enabled:
                    lret = allocate_new_egress_ipv6_firewalls(dbh=self.db_h,
                                                              inst=inst,
                                                              edge_location_id=edge_region_idx)
                    if lret == False:
                        raise Exception("Failed to allocate egress ipv6 list for firewall %s and edge location %s"
                                                                                        % (str(inst.get_param("id")),
                                                                                           str(edge_region_idx)))

            elif node_type == NODE_TYPE_GP_GATEWAY:
                success = allocate_new_egress_ip_gateways(self.db_h, inst, edge_region_idx, node_type)
                if success == False:
                    err_msg = "Failed to allocate egress IP for the instance id %s" % str(inst.get_param("id"))
                    self.logger.error(err_msg)
                    raise Exception(err_msg)

                if is_ipv6_enabled:
                    success = allocate_new_egress_ipv6_gateways(self.db_h, inst, edge_region_idx, NODE_TYPE_GP_GATEWAY)
                    if success == False:
                        err_msg = "Failed to allocate egress IP for the instance id %s" % str(inst.get_param("id"))
                        self.logger.error(err_msg)
                        raise Exception(err_msg)
                # No need to set the success value, since it will be set above.
            else:
                success = allocate_new_egress_ip_non_gateways(self.db_h, inst, edge_region_idx, node_type)
                if success == False:
                    raise Exception("Failed to allocate egress ip list for firewall %s and edge location %s"
                                    % (str(inst.get_param("id")),
                                       str(edge_region_idx)))

            success = True
        except Exception as E:
            self.sanitize_logger.error("Failed to delete instance/s. Exception: %s. traceback: %s locals: %s"
                              % (str(E.args), str(traceback.format_exc()), str(locals())))
            success = False
        finally:
            return success


    # Tejas P.
    #  P.S. Naming convention:
    #   cust_topology: region_name, region_idx is the edge-region_name, edge-region_idx
    #   instance_master: we have the following:
    #     compute_region_name, compute_region_idx : This the physical/virtual compute resource allocated for the edge
    #                                                region.
    #  Note: edge -> compute is a many to one relation.
    #    Example 1: Below the 2 edge regions map to the same compute region.
    #    edge-region-1 -> compute-region-3 i.e. us-west-1 -> us-west-3
    #    edge-region-2 -> compute-region-3 i.e. us-west-101 -> us-west-3
    #    Example 2: Below the 2 edge regions map to the different compute region each.
    #    edge-region-1 -> compute-region-3 i.e. us-west-1 -> us-west-3
    #    edge-region-2 -> compute-region-4 i.e. us-west-101 -> us-west-4
    #    Example 3: Edge and compute locations can also be the same.
    #    edge-region-1  -> compute-region-1 i.e. us-west-1 -> us-west-1

    def process_add_topology(self,
                             node,
                             job,
                             job_instances):
        '''

        This function adds the entry in the cust topology.

        custid - cust id of the customer.
        node - node object ref.
        panorama_job_id - Jobid of the panorama pushed job
        job_instances - Outparam (instances that we allocated for the job)
        :return: True on success, False on failure.

        '''
        ret = False
        instances = []
        job_tuple = None
        custid = self.avctx.cust_id
        panorama_job_id = self.avctx.panorama_job_id
        has_nat_gateway = False
        create_nat_gateway = False
        has_nlb = False
        try:
            custnode = CSN.CustNodeModel(node.nodeid, dbh=self.db_h)
            custnode_id = custnode.id
            tenant_id = self.tenant_id
            if panorama_job_id and custnode_id:
                job_tuple = (tenant_id, panorama_job_id, custnode_id)

            # Get the edge region idx from the cust topology region field.
            edge_region_idx = node.region
            self.logger.info("process_add_topology: Processing Node %s %s %s %s" % (node.nodeid, node.vmtype,
                                                                                    edge_region_idx, node.version))
            compute_region_idx = None
            compute_region_idx = gpcs_get_compute_region_idx_from_edge_region_idx(self.db_h, edge_region_idx,
                                                                                  custid=custid)

            cloud_provider = gpcs_get_cloud_type_from_region_idx(self.db_h, custnode.region, custid=custid)
            self.sanitize_logger.info(f"process_add_topology: compute_region_idx={compute_region_idx} for "
                             f"edge_region_idx={edge_region_idx} for node {node.nodeid} for cloud {cloud_provider}")
            if compute_region_idx == None:
                err_msg = "process_add_topology: Failed to get the compute region for edge region %s" % str(edge_region_idx)
                raise Exception(err_msg)

            if custnode.is_clean_pipe:
                if custnode.parent_id == custnode.id:
                    self.logger.info(
                        "process_msg(): ignoring parent entry topology_add is_clean_pipe: %d parent_id: %d ",
                        custnode.is_clean_pipe, custnode.parent_id)
                    ret = True
                    return
                else:
                    self.logger.info("process_msg(): topology_add is_clean_pipe: %d parent_id:%d",
                                     custnode.is_clean_pipe, custnode.parent_id)
            if not custnode.id:
                self.error = (
                        "process_add_topology: CreateInstance:Unable to find node in customer topology %s" %
                        (str(node.nodeid),))
                self.logger.error(self.error)
                raise Exception(self.error)

            if custnode.is_deleted == 1:
                self.error = ("Not creating node id %s as its scheduled for deletion" % str(custnode.name))
                raise Exception(self.error)

            if custnode.node_type == NODE_TYPE_REMOTE_NET and custnode.transport_type == 'pa-connect':
                if custnode.parent_id is None or custnode.parent_id == 0 or custnode.id == custnode.parent_id:
                    self.logger.info(f"Parent node for onramp RN:{custnode.name} ")
                    rn_count = get_onramp_rn_count(self.db_h, custnode)
                    rn_nodes_created = allocate_onramp_rn_nodes(self.db_h, custnode, rn_count)
                    self.logger.info(f"RN nodes needed: {rn_count}, nodes created: {rn_nodes_created}")
                    if rn_nodes_created == 0:
                        raise Exception("Failed to create child onramp RN nodes")

                    lret = allocate_interconnect_onramp_ilb(self.db_h, custid, compute_region_idx, custnode)
                    if lret < 0:
                        raise Exception("Failed to create ILB for onramp RN nodes")
                    return True
                elif custnode.parent_id == -1:
                    lret = allocate_interconnect_onramp_ilb(self.db_h, custid, compute_region_idx, custnode)
                    if lret < 0:
                        raise Exception("Failed to create ILB for onramp RN nodes")


            if custnode.node_type == NODE_TYPE_GP_GATEWAY:
                nat_gateways_created = False
                # Check if the NAT gateways and NLB need to be created,
                # for the (customer, region) combination, based on no.
                # of gateways (threshold reached).
                nat_gw_needed, nlb_needed = is_nat_gateway_nlb_needed(
                                                self.db_h,
                                                custid,
                                                compute_region_idx,
                                                edge_region_idx=edge_region_idx)
                self.logger.info(f"process_add_topology: nat_gw_needed={nat_gw_needed}, nlb_needed={nlb_needed} "
                                 f"for custid={custid}, compute_region_idx={compute_region_idx} for cust node region"
                                 f" {edge_region_idx}")
                if nat_gw_needed:
                    has_nat_gateway = False
                    lret = allocate_nat_gateways(self.db_h,
                                                 custid,
                                                 compute_region_idx, edge_region_idx=edge_region_idx,
                                                 cloud_provider=cloud_provider)
                    if lret < 0:
                        raise Exception("Failed to allocated NAT gateways. "
                                        "Cannot continue.")
                    if lret == 1:
                        has_nat_gateway = True
                    self.logger.info(f"process_add_topology: has_nat_gateway : {has_nat_gateway}")
                else:
                    self.logger.info("NAT gateways are not needed for "
                                     "custid %s and region is %s" %
                                     (str(custid), str(compute_region_idx)))

                if nlb_needed:
                    lret = allocate_nlb(self.db_h, custid, compute_region_idx)
                    if lret < 0:
                        raise Exception("Failed to allocate NLB for tenant "
                                        "%s in region %s. Cannot continue." %
                                        (str(custid), str(compute_region_idx)))
                    if lret == 1:
                        has_nlb = True
                    self.logger.info(f"process_add_topology: has_nlb : {has_nlb}")

                    is_ilb_needed = is_nat_gateway_ilb_needed(
                        self.db_h,
                        custid,
                        compute_region_idx,
                        edge_region_idx=edge_region_idx) or is_masque_feature_enabled(self.db_h,custid)
                    if is_ilb_needed:
                        # Currently the checks are we are re using all the checks for NLB for ILB also
                        # But only if 5G mu exists in a region then we will bring up Service ILB as well.
                        lret = allocate_ilb(self.db_h, custid, compute_region_idx)
                        if lret < 0:
                            raise Exception("Failed to allocate Service ILB for tenant "
                                            "%s in region %s. Cannot continue." %
                                            (str(custid), str(compute_region_idx)))
                        if lret == 1:
                            has_nlb = True
                        self.logger.info(f"process_add_topology: has_svc_ilb : {has_nlb}")
                else:
                    self.logger.info("NLB is not needed for custid %s in "
                                     "region %s" % (str(custid),
                                     str(compute_region_idx)))

            if custnode.node_type == NODE_TYPE_SERVICE_CONN and custnode.transport_type == 'colo-connect':
                colo_no_gre_flag = colo_100g_no_gre_feature_flag_enabled(self.tenant_id, cfg, self.logger)
                if colo_no_gre_flag:
                    self.logger.info("process_add_topology: colo-sc %s should be behind nlb" % (str(custnode.name)))
                    has_nlb = True

            if custnode.node_type in (NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY):

                proxy_bringup = swgproxy_bringup(self.db_h, node_type=custnode.node_type, alt_node_type=custnode.alt_node_type)
                self.logger.info("call add_salt_profile_for_ep_region with: custid: %s region: %s, nodeType: %s."
                                 % (str(custid), str(custnode.region), str(custnode.node_type)))
                result = proxy_bringup.ep_set_envoy_parameters(custid, custnode.region, node_type=custnode.node_type,
                                                               alt_node_type=custnode.alt_node_type)
                if not result["ok"]:
                    error_msg = f"Failed ep_set_envoy_parameters: {result.get('err_msg', 'Unknown error')}"
                    self.logger.error(error_msg)
                    raise Exception(error_msg)

                proxy_bringup.add_salt_profile_for_ep_region(custid, custnode.region, topology_wf=True,
                                                             node_type=custnode.node_type,
                                                             alt_node_type=custnode.alt_node_type)
                if custnode.node_type == NODE_TYPE_SWG_PROXY:
                    cloud_provider = gpcs_get_cloud_type_from_region_idx(self.db_h, custnode.region, custid=custid)
                    self.logger.info("Checking if cloud nat ip is enabled annd reserving ips if needed")
                    cloud_nat_params = proxy_bringup.gpcs_get_cnat_params(self, cfg, custid, custnode.region,
                                                                          cloud_provider, custnode.node_type)
                    is_ff_enabled = is_ep_cnat_feature_enabled(self.db_h, custid)
                    if cloud_nat_params and cloud_nat_params.get("cnat_enabled", None) == "1" and \
                            cloud_provider == PROVIDER_GCP and is_ff_enabled and custnode.is_dynamic_node == 1:
                        res = proxy_bringup.perform_ip_reservation_for_cnat_gcp(self.db_h, custid, custnode.region,
                                                                          cloud_nat_params)

            if custnode.num_allocs:
                # This is the case where orchestrator restarted while processing an instance and
                # it got the same message again from the sqs as it was not deleted.
                # Here either instances are created or the
                res, instances = custnode.get_retry_instance_list(dbh=self.db_h, ids=self.ids)
                if not res:
                    # Some problem send to failure queue.
                    self.error = ("get_retry_instance_list failed.")
                    raise Exception(self.error)
                if not len(instances):

                    self.error = (
                            "CreateInstance: Node already has as instance node name %s" % str(custnode.name))
                    self.logger.error(self.error)
                    raise Exception(self.error)
                else:
                    # In this case, we want to trigger an update for the compute region and not the edge-region since
                    # edge-region is a virtual region.

                    compute_region_idx = instances[0].get_param("compute_region_idx")
                    if compute_region_idx == 0:
                        self.logger.error("TOPOLOGY_ADD: Fatal ! Found transient instance list region id as 0.")
                        return
                    myres, _ = trigger_update_for_instance_changes(self.db_h, compute_region_idx, custid,
                                                                   node_type=custnode.node_type,
                                                                   job_tuple=job_tuple,
                                                                   avctx=self.avctx,
                                                                   alt_node_type=custnode.alt_node_type)

                    if myres:
                        # TODO: Tejas - Check with the swg team, why do we have this code here,
                        #  instance retry has nothing to do with SWG ?
                        swg_redep, regions = is_swg_proxy_deployment_needed(
                                                 self.db_h, custid,
                                                 custnode.node_type)
                        if swg_redep:
                            for ep_region in regions:
                                self.logger.info(f"Redeploy SWG Proxy for "
                                                 f"{custid} in {ep_region}")
                                myres, _ = trigger_update_for_instance_changes(
                                               self.db_h, ep_region,
                                               custid,
                                               node_type=NODE_TYPE_SWG_PROXY,
                                               job_tuple=job_tuple,
                                               avctx=self.avctx)
                                if myres != True:
                                    self.logger.error(f"Failed to trigger"
                                        f"update for EP redeployment for "
                                        f"{custid} in {ep_region}")
                                else:
                                    self.logger.info(f"Successfully "
                                        f"redeployed EP for {custid} in "
                                        f"{ep_region}")
                        else:
                            self.logger.info(f"No EP redeployment needed for "
                                             f"{custid}")

                    ret = myres
                    return

            # In the allocate instance, we use the region as the edge_region_idx, since at this point in time
            # we do not know the compute_region_idx that we would allocate.
            insts, dp_insts, is_new = self.allocate_instance(node.nodeid,
                                                             node.vmtype,
                                                             edge_region_idx,
                                                             node.version,
                                                             custid,
                                                             custnode,
                                                             is_dynamic=custnode.is_dynamic_node,
                                                             is_clean_pipe=custnode.is_clean_pipe,
                                                             panorama_job_id=panorama_job_id,
                                                             has_nat_gateway=has_nat_gateway,
                                                             has_nlb=has_nlb)
            self.logger.info(f"process_add_topology: Processing instances {insts} and dp instances {dp_insts} is_new "
                             f"{is_new} for edge region {edge_region_idx} for custid {custid} for node {node.vmtype}")

            # send to zti controller
            handle_zti_location_add(self.db_h,
                                            self.logger,
                                            custid,
                                            edge_region_idx,
                                            compute_region_idx,
                                            cloud_provider)

            # Refresh custnode again from db
            custnode = CSN.CustNodeModel(custnode.id, dbh=self.db_h)
            if insts == None:
                self.error = ("orchstrator.process_msg:Unable to get instance for node\n")
                self.error += str(self.db_h.error)
                self.logger.error("orchstrator.process_msg:Unable to get instance for node\n")
                raise Exception(self.error)

            if dp_insts is None:
                dp_insts = []

            found_pinned_instance = False
            for inst in (dp_insts + insts):

                is_pinned_instance = inst.get_param("is_pinned_instance")
                if is_pinned_instance:
                    found_pinned_instance = True
                    self.logger.info(f"process_add_topology: Found pinned instance {inst.get_param('name')}")

                if is_new:
                    # Append to list of instances to be started.
                    instances.append(inst)
                    msg = ("process_add_topology: Allocating new Instance %s for new site" % inst.get_param("name"))
                    job.status_msg = prepare_job_status_msg(msg)
                    self.logger.info(msg)
                    job.save_job(self.db_h)
                else:
                    msg = ("process_add_topology: Using existing Instance %s for new site" % inst.get_param("name"))
                    self.logger.info(msg)
                    job.status_msg = prepare_job_status_msg(msg)
                    job.save_job(self.db_h)

                    is_edge_location = edge_region_idx != inst.get_param("compute_region_idx")

                    self.logger.info(f"process_add_topology: Total instances {instances}")
                    if len(instances) == 0:
                        if is_edge_location and is_integ_or_dp(inst.get_param("slot_nr")):
                            self.logger.info(f"process_add_topology: Call allocate_new_egress_ip for edge location {edge_region_idx} for {inst}")
                            self.allocate_new_egress_ip(inst, edge_region_idx, custnode.node_type)
                        elif not is_edge_location:
                            lres = perform_ip_reservation_for_compute_location(self.db_h, inst)
                            if lres == False:
                                err_msg = f"Failed to perform IP reservation for compute location {edge_region_idx}"
                                raise Exception(err_msg)
                        instances.append(inst)
                    else:
                        instances.append(inst)

            if found_pinned_instance and custnode.node_type == NODE_TYPE_GP_GATEWAY:
                # Add central cache service tenant region mapping on creation of pinned GP gateways
                add_central_cache_tenant_region_mapping(self.db_h, custid, self.tenant_id, compute_region_idx)

            # Now add the instances that we allocated so that we can monitor the instance bringup etc
            if panorama_job_id != None and instances != None and len(instances):
                for inst in instances:
                    job_instances.append(inst.get_param("id"))

            if instances:
                # New instances are allocated in the 'compute_region_id', we need to deploy now.
                compute_region_idx = instances[0].get_param("compute_region_idx")
                res, _ = trigger_update_for_instance_changes(self.db_h, compute_region_idx, custid,
                                                             node_type=custnode.node_type,
                                                             job_tuple=job_tuple,
                                                             avctx=self.avctx,
                                                             alt_node_type=custnode.alt_node_type)
                if res != True:
                    err_msg = "Failed to trigger update for instance management."
                    raise Exception(err_msg)

                swg_redep, regions = is_swg_proxy_deployment_needed(self.db_h,
                                         custid, custnode.node_type)
                if swg_redep:
                    for ep_region in regions:
                        self.logger.info(f"Redeploy SWG Proxy for {custid} "
                                         f"in {ep_region}")
                        myres, _ = trigger_update_for_instance_changes(
                                       self.db_h, ep_region, custid,
                                       node_type=NODE_TYPE_SWG_PROXY,
                                       job_tuple=job_tuple,
                                       avctx=self.avctx,
                                       alt_node_type=custnode.alt_node_type)

                        if myres != True:
                            err_msg = ("Failed to trigger update for SWG "
                                       "Proxy redeployment during instance "
                                       "management.")
                            raise Exception(err_msg)
                        else:
                            self.logger.info(f"Successfully "
                                        f"redeployed EP for {custid} in "
                                        f"{ep_region}")
                else:
                    self.logger.info(f"No EP redeployment needed for {custid}")

                if self.should_trigger_sp_firewall_rule_operation(instances[0], custnode):
                    # In case of sp-interconnect the firewall rules are configured on the sharedVPC
                    # and not on the per-tenant VPC. So direct API calls has to be executed.

                    # SNS/SQS expect message in string format the dic object.
                    rastro_log_tag = b64encode(self.logger.update_logtag())
                    msg = f'{{ "tenant_id": {instances[0].get_param("acct_id")},' \
                          f' "panorama_job_id": "{panorama_job_id}",' \
                          f' "inst_id": {instances[0].get_param("id")},' \
                          f' "firewall_name": "firewall-rule-{instances[0].get_param("alias")}",' \
                          f' "log_tag":  "{rastro_log_tag}" }}'
                    sns_sp_interconnect_firewall_rule_send_msg(self.db_h, instances[0].get_param('acct_id'),
                                                               msg, instances[0].get_param("id"))

            ret = True
        except Exception as E:
            self.sanitize_logger.error("Failed with exception, %s locals %s traceback: %s" % (str(E.args), str(locals()),
                                                                                     str(traceback.format_exc())))
            self.return_err_msg = "Failed with exception, %s, traceback: %s" % (str(E.args),
                                                                                str(traceback.format_exc()))
            self.logger.error(self.return_err_msg)
            ret = False
        finally:
            return ret


    def process_update_envoy_version(self,
                                     custid,
                                     compute_region_id,
                                     cloud_provider, job, node_type):
        all_alt_node_types = [-1]
        if node_type == NODE_TYPE_BI_NH_PROXY:
            # In case of BINHP, upgrade should be triggered for all alt_node_type stacks
            all_alt_node_types = list(onramp_topology_id_to_name_dict.keys())
            # exclude UDA alt node type for RBI request
            all_alt_node_types.remove(NODE_TYPE_UDA)
        elif node_type == NODE_TYPE_UDA:
            # For UDA nodetype, gcp_envoy_upgrade_lambda sends UDA as nodetype
            # but orchestrated node is in fact BINHP. So adjust it here to use 173.177
            all_alt_node_types = [NODE_TYPE_UDA]
            node_type = NODE_TYPE_BI_NH_PROXY
        for alt_node_type in all_alt_node_types:
            ret = self.process_update_envoy_version_impl(custid,
                                                         compute_region_id,
                                                         cloud_provider, job, node_type, alt_node_type=alt_node_type)
        return ret


    def process_update_envoy_version_impl(self,
                                     custid,
                                     compute_region_id,
                                     cloud_provider, job, node_type, alt_node_type):
        try:
            if cloud_provider == PROVIDER_OCI:
                proxy_bringup = swgproxy_bringup(self.db_h, node_type=node_type, alt_node_type=alt_node_type)
                proxy_bringup.add_salt_profile_for_ep_region(custid, compute_region_id, topology_wf=False, node_type=node_type, alt_node_type=alt_node_type)
                #the ep-set-envoy param function updates cnat ipaddresses as well
                result = proxy_bringup.ep_set_envoy_parameters(custid, compute_region_id, node_type=node_type, alt_node_type=alt_node_type)

                if not result["ok"]:
                    error_msg = f"Failed ep_set_envoy_parameters: {result.get('err_msg', 'Unknown error')}"
                    self.logger.error(error_msg)
                    raise Exception(error_msg)

                trigger_update_for_instance_changes(self.db_h, int(compute_region_id), custid, node_type=node_type)
                ret = True
            else:
                new_template_name, ret = update_instance_template(self.db_h, custid, compute_region_id, cloud_provider, node_type, alt_node_type)
                if ret:
                    proxy_bringup = swgproxy_bringup(self.db_h, node_type=node_type, alt_node_type=alt_node_type)
                    self.logger.info("Adding salt_profile with new template")
                    proxy_bringup.add_salt_profile_for_ep_region(custid, compute_region_id, new_template_name, node_type=node_type, alt_node_type=alt_node_type)
                else:
                    self.logger.info(f"Not upgrading the envoy version for node {node_type}.{alt_node_type} for cust {custid} in {compute_region_id} region")
        except Exception as E:
            self.return_err_msg = ("Failed with exception, %s locals %s traceback: %s" % (str(E.args), str(locals()),
                                                                                          str(traceback.format_exc())))
            self.sanitize_logger.error(self.return_err_msg)
            ret = False
        finally:
            return ret

    def process_update_topology(self,
                                node,
                                job,
                                job_instances,
                                job_transient_instances):
        '''

        :param custid:
        :param node:
        :param panorama_job_id:
        :param job_instances:
        :param job_transient_instances:
        :return: ret = True on success, ret = False on failure.
        '''
        custid = self.avctx.cust_id
        panorama_job_id = self.avctx.panorama_job_id

        ret = False
        job_tuple = None
        try:
            is_okyo_instance_refresh = False
            is_agg_bandwidth = False
            is_spn_changed = False
            qos_changed = False
            compute_region_changed = False
            edge_location_changed = False
            only_trigger_egress_ip_change = False
            custnode = CSN.CustNodeModel(node.nodeid, dbh=self.db_h)
            custnode_id = custnode.id
            tenant_id = self.tenant_id
            if panorama_job_id and custnode_id:
                job_tuple = (tenant_id, panorama_job_id, custnode_id)

            self.logger.info("Processing Node %s %s %s %s" % (node.nodeid, node.vmtype,
                                                              node.region, node.version))
            if not custnode.id:
                self.error = ("CreateInstance:Unable to find node in customer topology %s" % (str(node.nodeid),))
                self.logger.error("CreateInstance:Unable to find node in customer topology %s" % (str(node.nodeid),))
                raise Exception(self.error)

            if custnode.node_type in (NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY):
                proxy_bringup = swgproxy_bringup(self.db_h, node_type=custnode.node_type, alt_node_type=custnode.alt_node_type)
                self.logger.info("call add_salt_profile_for_ep_region with: custid: %s region: %s, nodeType: %s."
                                 % (str(custid), str(custnode.region), str(custnode.node_type)))
                proxy_bringup.add_salt_profile_for_ep_region(custid, custnode.region, topology_wf=True, node_type=custnode.node_type, alt_node_type=custnode.alt_node_type)
                result = proxy_bringup.ep_set_envoy_parameters(custid, custnode.region, node_type=custnode.node_type, alt_node_type=custnode.alt_node_type)
                if not result["ok"]:
                    error_msg = f"Failed ep_set_envoy_parameters: {result.get('err_msg', 'Unknown error')}"
                    self.logger.error(error_msg)
                    raise Exception(error_msg)

            # TODO Check retry message
            edge_region_idx = custnode.region

            instances = custnode.get_instances(self.db_h)
            if not instances:
                # Something went wrong in previous attempt, we will spin up new instances.
                self.logger.error("TOPOLOGY_UPDATE:No Provisioned Instances"
                                  "found for the node %s" % str(custnode.name))

            # Check for any instance in transition table. may be duplicate message:
            transition_instances = custnode.get_instances(self.db_h, transient=1)
            if transition_instances:
                self.logger.info("Retrying for instances created in previous attempt")
                if not custnode.transition_instances(self.db_h, job):
                    self.error = ("Unable to transition to new instance")
                    raise Exception(self.error)
                else:
                    ret = True
                    return
            if (custnode.node_type == NODE_TYPE_REMOTE_NET and custnode.transport_type == "pa-connect" and
                    (custnode.parent_id is None or custnode.parent_id == 0)):
                ret = True
                self.logger.info(f"Ignoring Update request for RN Interconnect onramp node: {custnode.name}")
                return

            if instances:
                # Set the instances that we allocated/Reused for the job.
                # Append the instance1_id
                job_instances.append(instances[0])
                if len(instances) > 1:
                    # Append the instance2_id.
                    job_instances.append(instances[1])

                primary_instance = INST.InstanceModel(iid=instances[0], dbh=self.db_h)
                if not primary_instance.get_param("id"):
                    self.error = ("Instance %d bound to site %s not found" % (instances[0], str(custnode.name)))
                    self.logger.error("Instance %d bound to site %s not found" % (instances[0], str(custnode.name)))
                    raise Exception(self.error)

                primary_integ_or_dp_iids = custnode.get_integ_or_dp_instances(self.db_h)
                primary_integ_or_dp_instance = INST.InstanceModel(iid=primary_integ_or_dp_iids[0], dbh=self.db_h)
                if not primary_integ_or_dp_instance:
                    self.error = ("Legacy or DP Instance %d bound to site %s not found" % (
                        primary_integ_or_dp_iids[0], str(custnode.name)))
                    self.logger.error(self.error)
                    raise Exception(self.error)
                self.process_inbound_access_cfg_change(primary_integ_or_dp_instance, custnode)
                # Lets see the Database state also.
                sql = "select license_type, curr_qos from cust_topology where id = %s" % (str(node.nodeid))
                cursor = self.db_h.get_cursor()
                try:
                    self.db_h.logger.info("Executing %s" % str(sql))
                    cursor.execute(sql, None)
                    res = cursor.fetchall()
                    self.db_h.logger.info("Result: %s" % (str(res)))
                except Exception as ex:
                    self.db_h.cursorclose(cursor)
                    self.db_h.logger.info("Unable to execute %s for %s %s" % (sql, str(self.qname), str(ex)))

                # Applicable only to new lic checks.
                if custnode.license_type == "FWAAS-AGGREGATE":
                    # Set the agg bandwidth to True.
                    is_agg_bandwidth = True
                    # Since with Aggregate bandwidth we do not allow changing the already allocated bandwidth for SPN.
                    qos_changed = False
                    # Check for the SPN change.
                    if custnode.spn_name != custnode.old_spn_name and primary_instance.get_param("spn_name") != custnode.spn_name:
                        is_spn_changed = True
                # Applicable to old liscenses Note. code not changed.0
                else:
                    # Check if QOS is changed:
                    if custnode.qos != custnode.curr_qos:
                        qos_changed = True
                    self.logger.info(
                        "QOS Changed = %s old QOS: %s new QOS: %s" % (str(qos_changed), str(custnode.curr_qos),
                                                                      str(custnode.qos)))

                # Check if region has changed
                compute_region_changed = self.check_region_change(custnode, custid=custid)
                edge_location_changed = (custnode.region != custnode.old_region)
                self.logger.info("Compute region has changed = %s. Edge location has changed = %s" %
                                 (compute_region_changed, edge_location_changed))

                if not compute_region_changed and custnode.okyo_edge_site_id > 0:
                    is_okyo_instance_refresh = True
                    self.logger.info(
                        "okyo instance change triggered see if we can find a different instance in same compute location "
                        "for device = %s , data=%s" % (custnode.name, custnode.okyo_data))
                if not is_agg_bandwidth:
                    if not qos_changed and not compute_region_changed and not is_okyo_instance_refresh:
                        self.logger.info("Probably got duplicate message. No change in "
                                         "provisioned and required values for region (%s) and qos(%s)"
                                         % (str(custnode.region), str(custnode.qos)))

                    # Since the region is not changed, lets see if we can use same instance for new qos value.
                    if not compute_region_changed and not is_okyo_instance_refresh and primary_instance.update_qos(
                            custnode, self.db_h):
                        self.logger.info("Compute region not changed, updated the QOS")
                        if edge_location_changed:
                            self.logger.info("Change old_region from %s to %s for cust_topo id %s" %
                                             (custnode.old_region, custnode.region, custnode.id))
                            only_trigger_egress_ip_change = True
                        else:
                            ret = True
                            mark_bringup_success = True
                            return
                else:
                    if compute_region_changed or is_spn_changed:
                        agg_bw_model = AggBandwidthMgmtModel(dbh=self.db_h)
                        ret = agg_bw_model.is_agg_bw_spn_registered_for_customer(tenant_id,
                                                                                 custnode.spn_name,
                                                                                 region=None)
                        if ret == False:
                            self.logger.error("(-1)Fatal! SPN is not allocated! Cannot continue further...")
                            success = False
                            return
                        self.logger.info("Proceeding as usual for instance allocation.")

                    elif not edge_location_changed:
                        self.logger.info("No change was made to the site id %s, name %s! Hence not doing anything."
                                         % (str(custnode.id), str(custnode.name)))
                        ret = True
                        mark_bringup_success = True
                        return
                    else:
                        only_trigger_egress_ip_change = True

            # If there's only a edge location change, then we only need to potentially allocate
            # a new egress IP for the existing instance
            if only_trigger_egress_ip_change and custnode.node_type == NODE_TYPE_REMOTE_NET:
                inst = INST.InstanceModel(iid=custnode.instance1_id, dbh=self.db_h)
                self.allocate_new_egress_ip(inst, edge_region_idx, custnode.node_type)
                myres, _ = trigger_update_for_instance_changes(self.db_h, inst.get_param("compute_region_idx"), custid,
                                                               node_type=custnode.node_type,
                                                               avctx=self.avctx,
                                                               alt_node_type=custnode.alt_node_type)
                if myres is not True:
                    err_msg = "Failed to trigger update for instance changes."
                    raise Exception(err_msg)

                swg_redep, regions = is_swg_proxy_deployment_needed(
                                         self.db_h, custid,
                                         custnode.node_type)
                if swg_redep:
                    for region in regions:
                        self.logger.info(f"Redeploy SWG Proxy for {custid} "
                                         f"in {region}")
                        myres, _ = trigger_update_for_instance_changes(
                                       self.db_h,
                                       region,
                                       custid,
                                       node_type=NODE_TYPE_SWG_PROXY,
                                       avctx=self.avctx)

                        if myres is not True:
                            err_msg = ("Failed to trigger update for SWG "
                                       "Proxy redeployment during instance "
                                       "changes.")
                            raise Exception(err_msg)
                        else:
                            self.logger.info(f"Successfully redeployed EP "
                                             f"for {custid} in {region}")
                else:
                    self.logger.info(f"No EP redeployment needed for {custid}")

                custnode.set_old_region(self.db_h, custnode.region)
                ret = True
                mark_bringup_success = True
                return

            # Try allocating a transient instance. We will then defer the rest of the logic to bringup.
            insts, dp_insts, is_new = self.allocate_instance(node.nodeid,
                                                             node.vmtype,
                                                             edge_region_idx,
                                                             node.version,
                                                             custid,
                                                             custnode,
                                                             transient=1,
                                                             is_dynamic=custnode.is_dynamic_node,
                                                             is_clean_pipe=custnode.is_clean_pipe,
                                                             panorama_job_id=panorama_job_id,
                                                             is_okyo_instance_refresh=is_okyo_instance_refresh)

            if insts == None:
                self.error = ("orchstrator.process_msg:Unable to get New "
                              "instance for topology update")
                self.logger.error(self.error)
                raise Exception(self.error)

            if is_new:
                for inst in insts:
                    msg = ("Allocating new Instance %s for new site" % inst.get_param("name"))
                    job.status_msg = prepare_job_status_msg(msg)
                    self.logger.info(msg)
                    job.save_job(self.db_h)
                # We now need to trigger the cloud_provider specific instance creation logic for the compute instance.
                myres, _ = trigger_update_for_instance_changes(self.db_h, insts[0].get_param("compute_region_idx"), custid,
                                                               node_type=custnode.node_type,
                                                               job_tuple=job_tuple,
                                                               avctx=self.avctx,
                                                               alt_node_type=custnode.alt_node_type)
                if myres != True:
                    err_msg = "Failed to trigger update for instance changes."
                    raise Exception(err_msg)

                swg_redep, regions = is_swg_proxy_deployment_needed(
                                         self.db_h, custid,
                                         custnode.node_type)
                if swg_redep:
                    for ep_region in regions:
                        self.logger.info(f"Redeploy SWG Proxy for {custid} "
                                         f"in {ep_region}")
                        myres, _ = trigger_update_for_instance_changes(
                                       self.db_h,
                                       ep_region,
                                       custid,
                                       node_type=NODE_TYPE_SWG_PROXY,
                                       job_tuple=job_tuple,
                                       avctx=self.avctx)

                        if myres != True:
                            err_msg = ("Failed to trigger update for SWG "
                                       "Proxy redeployment during instance "
                                       "changes.")
                            raise Exception(err_msg)
                        else:
                            self.logger.info(f"Successfully redeployed EP "
                                             f"for {custid} in {ep_region}")
                else:
                    self.logger.info(f"No EP redeployment needed for {custid}")
            else:
                my_new_compute_region_idx = None
                for inst in (dp_insts + insts):
                    # update the binding as transient
                    msg = ("Using existing Instance %s for new site" % inst.get_param("name"))
                    self.logger.info(msg)
                    job.status_msg = prepare_job_status_msg(msg)
                    job.save_job(self.db_h)

                    #Set the new compute region.
                    if not my_new_compute_region_idx:
                        my_new_compute_region_idx = inst.get_param("compute_region_idx")

                    is_edge_location = edge_region_idx != my_new_compute_region_idx
                    if is_edge_location:
                        self.allocate_new_egress_ip(inst, edge_region_idx, custnode.node_type)

                # Do switch now.
                old_compute_region_idx = custnode.transition_instances(self.db_h, job)
                if old_compute_region_idx:
                    # New instances were created, so now lets trigger update to delete the old instances.
                    myret, _ = trigger_update_for_instance_changes(self.db_h, old_compute_region_idx, custid,
                                                                   node_type=custnode.node_type,
                                                                   avctx=self.avctx,
                                                                   alt_node_type=custnode.alt_node_type)
                    if myret != True:
                        ret = False
                        err_msg = "Failed to trigger update for instance changes."
                        self.logger.error(err_msg)
                        raise Exception(err_msg)

                    swg_redep, regions = is_swg_proxy_deployment_needed(
                                             self.db_h, custid,
                                             custnode.node_type)
                    if swg_redep:
                        for ep_region in regions:
                            self.logger.info(f"Redeploy SWG Proxy for "
                                             f"{custid} in {ep_region}")
                            myret, _ = trigger_update_for_instance_changes(
                                           self.db_h, ep_region,
                                           custid,
                                           node_type=NODE_TYPE_SWG_PROXY,
                                           avctx=self.avctx)

                            if myret != True:
                                ret = False
                                err_msg = ("Failed to trigger update for SWG "
                                           "Proxy redeployment during instance "
                                           "changes.")
                                self.logger.error(err_msg)
                                raise Exception(err_msg)
                            else:
                                self.logger.info(f"Successfully redeployed EP "
                                                 f"for {custid} in {ep_region}")
                    else:
                        self.logger.info(f"No EP redeployment needed for "
                                         f"{custid}")

                if my_new_compute_region_idx:
                    myret, _ = trigger_update_for_instance_changes(self.db_h, my_new_compute_region_idx, custid,
                                                                   node_type=custnode.node_type,
                                                                   avctx=self.avctx,
                                                                   alt_node_type=custnode.alt_node_type)
                    if myret != True:
                        ret = False
                        err_msg = "Failed to trigger update for instance changes."
                        self.logger.error(err_msg)
                        raise Exception(err_msg)

            if custnode.old_region != custnode.region:
                self.logger.info("Update old_region from %s to %s for cust_topo id %s" %
                                 (custnode.old_region, custnode.region, custnode.id))
                custnode.set_old_region(self.db_h, custnode.region)
            if custnode.old_spn_name != custnode.spn_name:
                self.logger.info("Update old_spn_name from %s to %s for cust_topo id %s" %
                                 (custnode.old_spn_name, custnode.spn_name, custnode.id))
                custnode.set_old_spn_name(self.db_h, custnode.spn_name)

            # At this point we need to set transient_instances since we will save them in the jobs table.
            if panorama_job_id != None and insts != None and len(insts):
                inst1_id = -1
                inst2_id = -1
                if len(insts) < 2:
                    job_transient_instances.append(insts[0].get_param("id"))
                elif len(insts) < 3:
                    job_transient_instances.append(insts[0].get_param("id"))
                    job_transient_instances.append(insts[1].get_param("id"))
            '''
                res = save_orch_success_state_to_commit_status_detail_table(self.db_h,
                                                                            self.tenant_id,
                                                                            panorama_job_id,
                                                                            custnode.id,
                                                                            inst1_id,
                                                                            inst2_id)
                if res != True:
                    err_msg = "Failed to update orchestration details_to_commit_status_table, Failing commit!"
                    self.logger.error(err_msg)
            '''

            # All good now.
            ret = True
        except Exception as E:
            self.sanitize_logger.error("Failed with exception, %s locals %s traceback: %s" % (str(E.args), str(locals()),
                                                                                     str(traceback.format_exc())))
            self.return_err_msg = ("Failed with exception, %s traceback: %s" % (str(E.args),
                                                                                str(traceback.format_exc())))
            self.logger.error(self.return_err_msg)
            ret = False
        finally:
            return ret

    class deleteInstanceDetails():
        def __init__(self):
            self.is_instance_deleted = False
            self.compute_region_idx = None
            self.compute_region_name = None
            self.is_pinned_instance = False
            self.is_dynamic_instance = False
            self.is_okyo = False
            self.mpdp_clusterid = None
            self.job_tuple = None
            self.job = None
            self.job_instances = None
            self.job_transient_instances = None
            self.node_type = None
            self.valid = False
            self.is_using_sp_interconnect = False
            self.is_dedicated_inbound_instance = 0
            self.instance1_alias = ""
            self.cloud_provider = None

        def set_is_instance_deleted(self, is_instance_deleted):
            self.is_instance_deleted = is_instance_deleted

        def set_compute_region_idx(self, compute_region_idx):
            self.compute_region_idx = compute_region_idx

        def set_compute_region_name(self, compute_region_name):
            self.compute_region_name = compute_region_name

        def set_is_pinned_instance(self, is_pinned_instance):
            self.is_pinned_instance = is_pinned_instance

        def set_is_dynamic_instance(self, is_dynamic_instance):
            self.is_dynamic_instance = is_dynamic_instance

        def set_mpdp_clusterid(self, mpdp_clusterid):
            self.mpdp_clusterid = mpdp_clusterid

        def set_is_okyo(self, is_okyo):
            self.is_okyo = is_okyo

        def set_job_tuple(self, job_tuple):
            self.job_tuple = job_tuple

        def set_job(self, job):
            self.job = job

        def set_job_instances(self, job_instances):
            self.job_instances = job_instances

        def set_job_transient_instances(self, job_transient_instances):
            self.job_transient_instances = job_transient_instances

        def set_node_type(self, node_type):
            self.node_type = node_type

        def set_valid(self, valid):
            self.valid = valid

        def set_is_using_sp_interconnect(self, is_using_sp_interconnect):
            self.is_using_sp_interconnect = is_using_sp_interconnect

        def set_instance1_alias(self, instance1_alias):
            self.instance1_alias = instance1_alias

        def set_is_dedicated_inbound_instance(self, is_dedicated_inbound_instance):
            self.is_dedicated_inbound_instance = is_dedicated_inbound_instance

        def set_cloud_provider(self, cloud_provider):
            self.cloud_provider = cloud_provider

    def process_delete_topology_handle_instances(self,
                                                 custnode,
                                                 deleteInstanceDetailsObj):
        success=False
        job = deleteInstanceDetailsObj.job
        is_instance_deleted = False
        compute_region_idx = None
        compute_region_name = None
        is_pinned_instance = False
        is_okyo = False
        mpdp_clusterid = None
        failed = False

        try:
            sql = ("SELECT instance1_id AS inst_id FROM cust_topology "
                   "WHERE id=%s "
                   "UNION "
                   "SELECT instance2_id AS inst_id FROM cust_topology "
                   "WHERE id=%s")
            params = (custnode.id, custnode.id)
            self.logger.info("SQL: %s" % (sql % params))

            cursor = self.db_h.get_cursor()
            try:
                cursor.execute(sql, params)
                ret = cursor.fetchall()
                self.db_h.cursorclose(cursor)
            except Exception as ex:
                self.error = ("Unable to find instance allocation entry for site %d: %s" % (custnode.id, str(ex)))
                self.logger.error("Unable to find instance allocation entry for site %d: %s" % (custnode.id, str(ex)))
                job.status_msg = prepare_job_status_msg("Unable to find instance allocation entry for site %d: %s" %
                                                        (custnode.id, str(ex)))
                job.save_job(self.db_h)
                self.db_h.cursorclose(cursor)
                raise Exception(job.status_msg)

            if not ret:
                self.error = ("Unable to find instance allocation entry for site %d" % (custnode.id))
                self.logger.error("Unable to find instance allocation entry for site %d" % (custnode.id))
                job.status_msg = prepare_job_status_msg("Unable to find instance allocation entry for site %d" %
                                                        (custnode.id))
                job.save_job(self.db_h)
                self.db_h.cursorclose(cursor)
                raise Exception(job.status_msg)

            self.logger.info("TOPOLOGY_DELETE: Found allocations %s" % str(ret))
            inst1_id = -1
            inst2_id = -1

            for rows in ret:
                instanceid = rows[0]
                instance = INST.InstanceModel(instanceid, self.db_h)
                if not instance.get_param("id"):
                    self.error += ("Unable to find instance with id %s" % str(instanceid))
                    self.logger.error("Unable to find instance with id %s" % str(instanceid))
                    # For GPAAS this is a valid case since we do not have HA.
                    self.logger.info("Continuing since no instance with instance id 0 exists")
                    continue
                else:
                    if inst1_id == -1:
                        inst1_id = instanceid
                        deleteInstanceDetailsObj.set_instance1_alias(instance.get_param("alias"))
                    else:
                        inst2_id = instanceid

                    # Both the instances should have same data so updating again is fine.
                    self.logger.info("Found instance with id %s, Trying to unbind." % str(instanceid))
                    deleteInstanceDetailsObj.set_compute_region_idx(instance.get_param("compute_region_idx"))
                    deleteInstanceDetailsObj.set_compute_region_name(instance.get_param("compute_region_name"))
                    deleteInstanceDetailsObj.set_is_pinned_instance(instance.get_param("is_pinned_instance"))
                    deleteInstanceDetailsObj.set_is_dynamic_instance(instance.get_param("is_dynamic_instance"))
                    deleteInstanceDetailsObj.set_mpdp_clusterid(instance.get_param("mpdp_clusterid"))
                    deleteInstanceDetailsObj.set_node_type(instance.get_param("node_type"))
                    deleteInstanceDetailsObj.set_is_using_sp_interconnect(instance.get_param("is_using_sp_interconnect"))
                    deleteInstanceDetailsObj.set_is_dedicated_inbound_instance(instance.get_param("is_dedicated_inbound_instance"))
                    deleteInstanceDetailsObj.set_cloud_provider(instance.get_param("cloud_provider"))
                    deleteInstanceDetailsObj.set_valid(True)
                    # TODO: Not sure if this code is needed any more.
                    if custnode.okyo_edge_site_id > 0 and instanceid > 0:
                        is_okyo = True
                        notify_okyo_info(custnode.name, custnode.okyo_data,
                                         instanceid, instance.get_param("cloud_provider"),
                                         False, self.logger)

                    # Unbind the instance now. It does an unbind as well as instance delete if num_allocs == 0 for that
                    # instance.

                    inst_id = instance.get_param("id")
                    vmid = instance.get_param("vmid")

                    ret = instance.unbind(custnode, self.db_h, job)
                    if not ret:
                        self.error += ("Unable to unbind instance %s "
                                       "with node %s" % (instance.get_param("name"),
                                                         custnode.name))
                        self.error += self.error
                        self.logger.error(self.error)
                        job.status_msg = prepare_job_status_msg(self.error)
                        job.save_job(self.db_h)
                        failed = 1

                        if self.avctx is not None:
                            try:
                                customer = CM.CustomerModel(custid=self.avctx.cust_id, dbh=self.db_h)
                                tenant_id = customer.get_param("acct_id")
                                region_id = instance.get_param("compute_region_idx")
                                node_type = instance.get_param("node_type")
                                metric_type = ORCHESTRATION_INSTANCE_CUST_TOPOLOGY_UNBIND_FAILED
                                self.avctx.set_ctx(tenant_id=tenant_id,
                                                   sup_tenant_id=customer.get_param("super_acct_id"),
                                                   tenant_name=customer.get_param("name"),
                                                   region_id=region_id,
                                                   region_name=instance.get_param("compute_region_name"),
                                                   trace_id=(
                                                       self.logger.get_enhanced_traceid() if not None else self.logger.uuid),
                                                   node_type=node_type,
                                                   cloud_provider=instance.get_param("cloud_provider"))
                                publish_avisar_event_with_tenant_region_nodetype_key(self.avctx, self.logger,
                                                                                     ORCHESTRATION_INSTANCE_CUST_TOPOLOGY_UNBIND_FAILED,
                                                                                     "instance unbind failed",
                                                                                     METRIC_SEVERITY_CRITICAL)
                            except Exception as e:
                                self.logger.error("Avisar Service publish event %s exception: %s" % (metric_type, str(e)))

                    if not instance.get_param("id"):
                        self.logger.info("Instance was cleaned up; "
                                         "set is_instance_deleted = 1")
                        is_instance_deleted = True

                        # ONLY If the instance is cleaned up then delete the pinned instance upgrade entry as well.
                        inst_node_type = instance.get_param("node_type")
                        from libs.model.pinnedInstanceUpgradeModel import PinnedInstanceUpgradeModel
                        if inst_node_type in [NODE_TYPE_GP_GATEWAY,
                                              NODE_TYPE_GP_PORTAL,
                                              NODE_TYPE_CLEAN_PIPE,
                                              NODE_TYPE_SWG_PROXY,
                                              NODE_TYPE_BI_NH_PROXY,
                                              NODE_TYPE_PROBE_VM]:
                            pinned_inst_ref = PinnedInstanceUpgradeModel(dbh=self.db_h)
                            success = pinned_inst_ref.delete_entry_by_current_instance_id(instanceid)
                            if success == False:
                                self.error += "Failed to delete the pinned instance upgrade entry for " \
                                              "current instance id %s " % str(instanceid)
                                job.status_msg = prepare_job_status_msg(self.error)
                                self.logger.error(self.error)
                                job.save_job(self.db_h)
                        # Clean up any stale entries in RDS that is referenced by this deleted instance
                        self.cleanup_instance_references(inst_id, vmid)

                        # send notification to ADEM service for this
                        if is_adem_notification_enabled(self.db_h, self.logger):
                            delete_notify_adem_service(inst_id, self.db_h, self.logger)
                    else:
                        # This is a valid case where same instance is used by many cust topology entries.
                        self.logger.info("Instance %s was not cleaned up" % str(instanceid))

            deleteInstanceDetailsObj.job_instances.append(inst1_id)
            deleteInstanceDetailsObj.job_instances.append(inst2_id)

            if not failed:
                success = True
            else:
                success = False
        except Exception as E:
            success = False
            is_instance_deleted = False
            self.sanitize_logger.error("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                       str(traceback.format_exc()),
                                                                                       str(locals())))
        finally:
            return success, is_instance_deleted

    def cleanup_instance_references(self, inst_id, vmid):
        sql = ("DELETE FROM xxx_cfgserv_firewall_summary WHERE firewall_id=%s")
        params = (inst_id,)
        cursor = self.db_h.get_cursor()
        try:
            cursor.execute(sql, params)
            self.db_h.cursorclose(cursor)
        except Exception as ex:
            self.logger.error(f"Failed to cleanup stale firewall_summary: {ex}")
        sql = ("DELETE FROM state_instance_ipsec_tunnel WHERE instance_id=%s")
        params = (vmid,)
        cursor = self.db_h.get_cursor()
        try:
            cursor.execute(sql, params)
            self.db_h.cursorclose(cursor)
        except Exception as ex:
            self.logger.error(f"Failed to cleanup stale state_instance_ipsec_tunnel: {ex}")
        sql = ("DELETE FROM state_instance_gpuser_log WHERE instance_id=%s")
        params = (vmid,)
        cursor = self.db_h.get_cursor()
        try:
            cursor.execute(sql, params)
            self.db_h.cursorclose(cursor)
        except Exception as ex:
            self.logger.error(f"Failed to cleanup stale state_instance_gpuser_log: {ex}")

    def process_delete_topology_handle_transient_instances(self, custnode, deleteInstanceDetailsObj):
        success = False
        transient_instance_deleted = False
        job = deleteInstanceDetailsObj.job
        failed=False

        try:
            self.logger.info("Getting info about transient instances for cust topology id %s" % str(custnode.id))
            sql = ("SELECT instance1_transient AS inst_id FROM cust_topology "
                   "WHERE id=%s "
                   "UNION "
                   "SELECT instance2_transient AS inst_id FROM cust_topology "
                   "WHERE id=%s")
            params = (custnode.id, custnode.id)
            self.logger.info("SQL: %s" % (sql % params))

            cursor = self.db_h.get_cursor()
            try:
                cursor.execute(sql, params)
                ret = cursor.fetchall()
                self.db_h.cursorclose(cursor)
            except Exception as ex:
                self.error = ("Unable to find transient instance allocation entry "
                              "for site %s: %s" % (str(custnode.id), str(ex)))
                self.logger.error("Unable to find transient instance allocation entry for "
                                  "site %s: %s" % (str(custnode.id), str(ex)))
                job.status_msg = prepare_job_status_msg("Unable to find transient instance allocation entry for "
                                                        "site %s: %s" %
                                                        (str(custnode.id), str(ex)))
                job.save_job(self.db_h)
                self.db_h.cursorclose(cursor)
                raise Exception(self.err_msg)

            instance1_transient_id = -1
            instance2_transient_id = -1

            for rows in ret:
                instance_transient_id = rows[0]
                if instance_transient_id == 0:
                    self.logger.info("Continuing since no transient id %s exists" % str(instance_transient_id))
                    continue
                else:
                    if instance1_transient_id < 0:
                        instance1_transient_id = instance_transient_id
                        deleteInstanceDetailsObj.job_transient_instances.append(instance1_transient_id)
                    else:
                        instance2_transient_id = instance_transient_id
                        deleteInstanceDetailsObj.job_transient_instances.append(instance2_transient_id)

                self.logger.info("Found instance with id %s, Trying to unbind." % str(instance_transient_id))
                instance_obj = INST.InstanceModel(instance_transient_id, self.db_h)
                if not instance_obj.get_param("id"):
                    self.error += ("Unable to find instance with id %s" % str(instance_transient_id))
                    self.logger.error("Unable to find instance with id %s" % str(instance_transient_id))
                    continue

                if deleteInstanceDetailsObj.valid != True:
                    deleteInstanceDetailsObj.set_compute_region_idx(instance_obj.get_param("compute_region_idx"))
                    deleteInstanceDetailsObj.set_compute_region_name(instance_obj.get_param("compute_region_name"))
                    deleteInstanceDetailsObj.set_is_pinned_instance(instance_obj.get_param("is_pinned_instance"))
                    deleteInstanceDetailsObj.set_mpdp_clusterid(instance_obj.get_param("mpdp_clusterid"))
                    deleteInstanceDetailsObj.set_node_type(instance_obj.get_param("node_type"))
                    deleteInstanceDetailsObj.set_is_dynamic_instance(instance_obj.get_param("is_dynamic_instance"))
                    deleteInstanceDetailsObj.set_cloud_provider(instance_obj.get_param("cloud_provider"))
                    deleteInstanceDetailsObj.set_valid(True)

                # Unbind the instance now.
                ret = instance_obj.unbind(custnode, self.db_h, job)
                if not ret:
                    self.error += ("Unable to unbind transient instance %s "
                                   "with node %s" % (instance_obj.get_param("name"),
                                                     custnode.name))
                    self.error += self.error
                    self.logger.error(self.error)
                    job.status_msg = prepare_job_status_msg(self.error)
                    job.save_job(self.db_h)
                    failed = 1

                if not instance_obj.get_param("id"):
                    self.logger.info("Instance %s was cleaned up" % str(instance_transient_id))
                    transient_instance_deleted = 1

                    inst_node_type = instance_obj.get_param("node_type")
                    if inst_node_type in [NODE_TYPE_GP_GATEWAY]:
                        # Perform cleanup related to 2 phase upgrade.
                        sql = ("UPDATE pool_allocation "
                               "SET transient_inst_id = 0, "
                               "is_in_use_by_transient_inst = 0 "
                               "WHERE transient_inst_id=%s")
                        params = (instance_transient_id,)
                        self.logger.info("SQL: %s" % (sql % params))
                        cursor = self.db_h.get_cursor()
                        try:
                            cursor.execute(sql, params)
                            self.db_h.cursorclose(cursor)
                        except Exception as ex:
                            err_msg = ("Unable to update the "
                                       "pool_allocation table to set "
                                       "transient_inst_id = 0 for entries "
                                       "where transient_inst_id = %s"
                                       % instance_transient_id)
                            self.error += err_msg
                            self.logger.error(err_msg)
                            self.db_h.cursorclose(cursor)
                            raise Exception(self.err_msg)
                    continue
                else:
                    self.logger.info("Instance %s was not cleaned up" % str(instance_transient_id))

            if failed:
                success=False
            else:
                success = True

        except Exception as E:
            success = False
            transient_instance_deleted = False
            self.sanitize_logger.error("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                       str(traceback.format_exc()),
                                                                                       str(locals())))
        finally:
            return success, transient_instance_deleted

    def process_delete_topology_handle_mp_dp_instances(self, custnode, deleteInstanceDetailsObj):
        success=False
        inst1_id, inst2_id = -1, -1
        mpdp_clusterid = deleteInstanceDetailsObj.mpdp_clusterid
        job_instances = deleteInstanceDetailsObj.job_instances
        try:
            if mpdp_clusterid in [0, None]:
                self.logger.info("MP/DP cluster id is none for node_type %s, continuing" %
                                 (str(deleteInstanceDetailsObj.node_type)))
                success = True
                return

            if len(job_instances) > 1:
                inst1_id, inst2_id = job_instances
            if len(job_instances) == 1:
                inst1_id = job_instances[0]

            # Let's also delete the DP instances if this is MP/DP seperation.
            dp_inst_ids = INST.get_linked_dp_instances_by_mp_instance_id(self.db_h,
                                                                         mpdp_clusterid)
            if dp_inst_ids == None:
                # Not bailing out here.
                self.logger.error("Failed to find instances linked to the DP.")
            elif len(dp_inst_ids) == 0:
                self.logger.info(
                    "Instance ids %s and %s do not have any linked DP instances!" % (str(inst1_id), str(inst2_id)))
            else:
                self.logger.info(
                    "Instance ids %s and %s have %s as linked DP instances! Attempting to delete those." %
                    (str(inst1_id), str(inst2_id), str(dp_inst_ids)))

                ret = self.delete_instance_by_instance_id_list(dp_inst_ids)
                if ret == False:
                    err_msg = ("Failed to delete instances from instance_master table.")
                    raise Exception(err_msg)

            success=True
        except Exception as E:
            success = False
            self.sanitize_logger.error("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                       str(traceback.format_exc()),
                                                                                       str(locals())))
        finally:
            return success


    def process_delete_topology_handle_autoscaled_instances(self,
                                                            custnode,
                                                            deleteInstanceDetailsObj):
        '''

        :param custnode:
        :param eleteInstanceDetailsObj:
        :return: success/Failure
        '''
        success = False
        all_instance_ids = []
        try:
            custid= custnode.custid
            tenant_id = self.tenant_id

            my_inst_model = INST.InstanceModel(dbh=self.db_h)

            all_instances = my_inst_model.get_all_instances_for_region_and_custid \
                (custid, deleteInstanceDetailsObj.compute_region_idx, [custnode.node_type])
            auto_scaled_instances = []
            pinned_instances = []

            for my_inst_id in all_instances:
                all_instance_ids.append(my_inst_id[0])
                my_inst = INST.InstanceModel(dbh=self.db_h, iid=my_inst_id[0])
                if custnode.alt_node_type != my_inst.get_param("alt_node_type"):
                    self.logger.info("Ignoring instance: %s with alt_node_type %s, curr: %s" % (pinned_instances, my_inst.get_param("alt_node_type"), custnode.alt_node_type))
                    continue
                if my_inst.get_param("id"):
                    if (my_inst.get_param("is_pinned_instance") and
                            not my_inst.get_param("is_dynamic_instance") and
                            not is_current_upgrade_instance(self.logger,
                                                            self.db_h, my_inst.get_param("id"))):
                        pinned_instances.append(my_inst_id[0])
                    if my_inst.get_param("is_dynamic_instance"):
                        auto_scaled_instances.append(my_inst_id[0])

            self.logger.info("Pinned instances: %s" % pinned_instances)
            self.logger.info("Auto scaled instances: %s" % auto_scaled_instances)
            if len(pinned_instances):
                self.logger.info("The compute region %s already has "
                                 "undeleted pinned instances, hence "
                                 "cannot delete the dynamic instances"
                                 % str(deleteInstanceDetailsObj.compute_region_idx))
                # TODO: Tejas: WTH. Raise an Alarm here. How would this happen ?
                success = False
                return
            else:
                if len(auto_scaled_instances):
                    # We need to get the node id's of the instances that are dynamic.
                    auto_scaled_cust_node_entries = []
                    for auto_scaled_inst_idx in auto_scaled_instances:
                        my_scaled_inst_ref = INST.InstanceModel(dbh=self.db_h, iid=auto_scaled_inst_idx)
                        if my_scaled_inst_ref.get_param("id"):
                            my_sites = my_scaled_inst_ref.get_sites(self.db_h)
                            for site in my_sites:
                                auto_scaled_cust_node_entries.append(site)
                    self.logger.info("Site values for auto scaled instances:%s" % auto_scaled_cust_node_entries)

                    for site_id in auto_scaled_cust_node_entries:
                        site_ref = CSN.CustNodeModel(iid=site_id, dbh=self.db_h)
                        if site_ref.id:
                            site_ref.is_deleted = 1
                            self.logger.info("Setting is_deleted to 1 for cust node %s in region %s" %
                                             (str(site_ref.id), str(deleteInstanceDetailsObj.compute_region_idx)))
                            site_ref.save(dbh=self.db_h)
                else:
                    self.logger.info("No auto scaled instances")

                if custnode.node_type in (NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY):
                    # Let's cleanup the added entries:
                    res = update_change_to_dns_manager(self.db_h, custid,
                                                       custnode.node_type,
                                                       deleteInstanceDetailsObj.compute_region_name,
                                                       deleteInstanceDetailsObj.compute_region_name,
                                                       "delete", '')
                    if res == False:
                        self.logger.error("Failed to add entry to the DNS manager for deletion \
                                 of cust id %s, compute_region %s" % (str(custid),
                                                                      str(deleteInstanceDetailsObj.compute_region_name)))
                    else:
                        self.logger.info("Added entry to dns update manager successfully for cust id %s, \
                                            compute_region %s" % (str(custid),
                                                                  str(deleteInstanceDetailsObj.compute_region_name)))
                    # Clean up NLB IPs
                    c = IP_MGMT.release_all_nlb_ips(self.db_h, tenant_id,
                                                    deleteInstanceDetailsObj.compute_region_idx, custnode.node_type, custnode.alt_node_type)
                    if c == -1:
                        self.logger.error(
                            "Failed to release nlb ip when deleting the last proxy from the region")
                    elif c == 0:
                        self.logger.info("No NLB IP released from public_ip_pool when deleting "
                                         "the last proxy from the region. This is OK for AWS region.")
                    else:
                        self.logger.info(
                            "%s NLB IP released when deleting the last proxy from the region" % str(c))

            success = True

        except Exception as E:
            success = False
            self.sanitize_logger.error("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                       str(traceback.format_exc()),
                                                                                       str(locals())))
        finally:
            return success, all_instance_ids


    def process_delete_topology_handle_edge_location_deletion(self,
                                                              custnode,
                                                              deleteInstanceDetailsObj, cloud_provider=PROVIDER_GCP):
        success=False
        custid=custnode.custid
        try:
            edge_loc_deleted_idx = custnode.region
            compute_idx = deleteInstanceDetailsObj.compute_region_idx
            if compute_idx in [None, 0]:
                err_msg = ("Found compute region idx as %s, we don not need to do further cleanup" % str(compute_idx))
                self.logger.error(err_msg)
                success=True
                return

            self.logger.info(f"process_delete_topology_handle_edge_location_deletion: Handle cust_topology deletion for edge region {edge_loc_deleted_idx} for cloud {cloud_provider} and custid {custid}")
            # For OCI; we need to also specifically delete the cust_topology and IM entry for the edge NATs
            # Update later to handle GCP Cloud NAT deletion as well. For GCP Cloud NAT also we will have a IM entry per NAT GW
            if cloud_provider in [PROVIDER_OCI]:
                self.logger.info(f"process_delete_topology_handle_edge_location_deletion: Delete NAT for edge region {edge_loc_deleted_idx} for cloud {cloud_provider} and custid {custid}")
                result = delete_nat_instances_by_custid_and_region_id(self.db_h, custid, edge_loc_deleted_idx)
                if result == False:
                    err_msg = "Failed to delete the NAT instances linked to custid %s and region is %s" % str(custid), str(edge_loc_deleted_idx)
                    self.logger.error(err_msg)

            ret_code = delete_gp_gateway_edge_location_references_impl(self.db_h,
                                                                       custid,
                                                                       edge_loc_deleted_idx,
                                                                       compute_idx,
                                                                       self.avctx,
                                                                       node_type=NODE_TYPE_GP_GATEWAY)
            if ret_code == False:
                self.logger.error("Failed to delete gp gateway edge location references for"
                                  " Edge location: %s in Compute location %s" % (str(edge_loc_deleted_idx),
                                                                                 str(compute_idx)))
                return

            # Also cleanup the NLB instance egress IP's if we have a NLB instance.
            result = unbind_egress_ips_from_node_impl(
                self.db_h, custid,
                compute_idx, edge_loc_deleted_idx,
                NODE_TYPE_NLB_INSTANCE)
            if result == False:
                err_msg = ("Failed to delete the NLB egress IP list "
                           "for Cust ID %s and region %s" %
                           (str(custid),
                            str(edge_loc_deleted_idx)))
                ret = False
                self.logger.error(err_msg)
                raise Exception(err_msg)

            # Also cleanup the NAT instance egress IP's if we have a NAT instance.
            result = unbind_egress_ips_from_node_impl(
                self.db_h, custid, compute_idx,
                edge_loc_deleted_idx,
                NODE_TYPE_NAT_INSTANCE)
            if result == False:
                err_msg = ("Failed to delete the NAT gateway egress IP list for Cust ID %s and region %s" %
                           (str(custid),
                            str(edge_loc_deleted_idx)))
                ret = False
                self.logger.error(err_msg)
                raise Exception(err_msg)

            # Trigger the changes for the instance, i.e.,
            # remove the forwarding rules for NAT/NLB.
            myres, _ = trigger_update_for_instance_changes(
                                   self.db_h, compute_idx,
                                   custid,
                                   node_type=NODE_TYPE_GP_GATEWAY,
                                   avctx=self.avctx)
            if myres == False:
                err_msg = ("Failed to trigger instance changes for tenant %s changes for compute idx %s" %
                           (str(custid),
                            str(compute_idx)))
                self.logger.error(err_msg)
                raise Exception(err_msg)

            success=True
        except Exception as E:
            success = False
            self.sanitize_logger.error("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                       str(traceback.format_exc()),
                                                                                       str(locals())))
        finally:
            return success


    def process_delete_topology_handle_nat_nlb_deletion(self,
                                                        custnode,
                                                        deleteInstanceDetailsObj):
        success=False
        custid=custnode.custid
        compute_region_idx=deleteInstanceDetailsObj.compute_region_idx

        try:
            #  FREE the NLB and NAT gateway since the pinned instance is deleted.
            if custnode.node_type == NODE_TYPE_GP_GATEWAY:
                self.logger.info(f"process_delete_topology_handle_nat_nlb_deletion: Handle NAT/NLB cleanup for custid {custid} and region {compute_region_idx}")
                result = unbind_egress_ips_from_node_impl(
                    self.db_h, custid,
                    compute_region_idx, None,
                    NODE_TYPE_NAT_INSTANCE, delete_all=True)
                if result == False:
                    err_msg = ("Failed to delete the NAT gateway egress IP list for Cust ID %s and region %s" %
                               (str(custid),
                                str(compute_region_idx)))
                    ret = False
                    self.logger.error(err_msg)
                    raise Exception(err_msg)

                # For OCI Cloud NAT we also need to delete the edge NAT region cust_topology entry
                cloud_provider = gpcs_get_cloud_type_from_region_idx(self.db_h, compute_region_idx, custid=custid)
                self.logger.info(f"process_delete_topology_handle_nat_nlb_deletion: Delete NAT for custid {custid} and compute region {compute_region_idx} for node region {custnode.region} for cloud {cloud_provider}")
                if str(compute_region_idx) != str(custnode.region) and \
                   (cloud_provider == PROVIDER_OCI or (cloud_provider == PROVIDER_GCP and self.db_h.is_gcp_cnat_supported(cloud_provider, custid, compute_region_idx))):
                    self.logger.info(f"process_delete_topology_handle_nat_nlb_deletion: Delete NAT for edge region {custnode.region} for cloud {cloud_provider} and custid {custid}")
                    result = delete_nat_instances_by_custid_and_region_id(self.db_h, custid, custnode.region)
                    if result == False:
                        err_msg = "Failed to delete the NAT instances linked to custid %s and region is %s" % str(custid), str(custnode.region)
                        self.logger.error(err_msg)
                # Send a delete for NAT instances.
                result = delete_nat_instances_by_custid_and_region_id(self.db_h, custid,
                                                                      compute_region_idx)
                if result == False:
                    err_msg = "Failed to delete the NAT instances linked to custid %s and region is %s" % \
                              (str(custid), str(compute_region_idx))
                    self.logger.error(err_msg)
                    raise Exception(err_msg)

                # Also cleanup the NLB instance egress IP's
                # if we have a NLB instance.
                result = unbind_egress_ips_from_node_impl(
                    self.db_h, custid,
                    compute_region_idx, None,
                    NODE_TYPE_NLB_INSTANCE, delete_all=True)
                if result == False:
                    err_msg = ("Failed to delete the NLB egress IP list "
                               "for Cust ID %s and region %s" %
                               (str(custid),
                                str(compute_region_idx)))
                    ret = False
                    self.logger.error(err_msg)
                    raise Exception(err_msg)

                self.logger.info(f"process_delete_topology_handle_nat_nlb_deletion: Delete NLB for custid {custid} and region {compute_region_idx}")
                nlb_instance_types = [NODE_TYPE_NLB_INSTANCE, NODE_TYPE_SERVICE_ILB]
                for lb in nlb_instance_types:
                    # Send a delete for LB instance.
                    self.logger.info(f"Sending delete call for: {lb} type")
                    result = delete_nlb_instance_by_custid_and_region_id(
                        self.db_h, custid, compute_region_idx, node_type=lb)
                    if not result:
                        err_msg = "Failed to delete the [%s] instance " \
                                  "linked to custid %s and region %s" % \
                                  (lb, str(custid),
                                   str(compute_region_idx))
                        self.logger.error(err_msg)
                        raise Exception(err_msg)

            success=True
        except Exception as E:
            success = False
            self.sanitize_logger.error("Failed with exception %s, traceback: %s, locals: %s" % (str(E.args),
                                                                                       str(traceback.format_exc()),
                                                                                       str(locals())))
        finally:
            self.logger.info(f"process_delete_topology_handle_nat_nlb_deletion: NAT/NLB deleted {success}")
            return success

    def process_delete_topology(self,
                                job,
                                job_instances,
                                job_transient_instances, cloud_provider=None):
        '''

        :param custid:
        :param node:
        :param panorama_job_id:
        :param job_instances:
        :param job_transient_instances:
        :return: ret = True on success, ret = False on failure.

        '''
        custid = self.avctx.cust_id
        node_id = self.avctx.cust_topology_id
        panorama_job_id = self.avctx.panorama_job_id
        cust_ep_region_to_delete = self.avctx.region_id
        ret = False
        job_tuple = None
        instance_ids = []
        deleteInstanceDetailsObj = self.deleteInstanceDetails()
        deleteInstanceDetailsObj.set_job_instances(job_instances)
        deleteInstanceDetailsObj.set_job_transient_instances(job_transient_instances)
        custnode = None

        try:
            custnode = CSN.CustNodeModel(iid=node_id, dbh=self.db_h)
            if not custnode.id:
                self.error = ("TOPOLOGY_DELETE: Node %s is not found" % custnode.name)
                self.logger.error("TOPOLOGY_DELETE: Node %s is not found" % custnode.name)
                job.status_msg = prepare_job_status_msg("TOPOLOGY_DELETE: Node %s is not found" % custnode.name)
                job.save_job(self.db_h)
                ret = True
                return

            custnode_id = custnode.id
            cust_ep_region_to_delete = custnode.region
            tenant_id = self.tenant_id
            if panorama_job_id and custnode_id:
                deleteInstanceDetailsObj.job_tuple = (tenant_id, panorama_job_id, custnode.id)

            self.logger.info("Initiating Delete for site %s" % str(custnode.name))
            if custnode.is_deleted != 1:
                self.error = ("Node %s is not marked for deletion" % custnode.name)
                self.logger.error("Node %s is not marked for deletion" % custnode.name)
                job.status_msg = prepare_job_status_msg("Node %s is not marked for deletion" % custnode.name)
                job.save_job(self.db_h)
                ret = True
                return

            # Call config service to delete the topology
            self.logger.info("Sending Delete message to Config Service for site %s" % str(custnode.name))
            payload = {"tenant_id": str(tenant_id),
                       "name": custnode.name,
                       "node_type": custnode.node_type,
                       "row_id": custnode.id}
            send_topology_update_msg_to_cfgservice(payload=payload, logger=self.logger)

            if custnode.is_clean_pipe:
                self.gpcs_cleanpipe_update_zone_usage(custnode.id)
                if custnode.parent_id == custnode.id:
                    self.gpcs_delete_state_clean_pipe_site_entry(custid, custnode.id)

            if custnode.node_type == NODE_TYPE_REMOTE_NET and custnode.transport_type == "pa-connect":
                if custnode.parent_id is None or custnode.parent_id == 0 or custnode.parent_id == custnode.id:
                    self.logger.info("Delete child onramp nodes and load balancer")
                    delete_child_onramp_rn(self.db_h, custnode.id, custnode.custid, custnode.region)
                    delete_onramp_ilb_instance_by_custid_and_region_id(self.db_h, custnode.id, custnode.custid, custnode.region)

            # Handle deletion of instance1_id and instance2_id
            success, \
            is_instance_deleted = self.process_delete_topology_handle_instances(custnode,
                                                                                deleteInstanceDetailsObj)
            if success == False or deleteInstanceDetailsObj == None:
                self.logger.error("Failed to delete instances")
                raise Exception("Failed to delete instances")

            # Handle deletion of instance1_transient and instance2_transient
            success, transient_instance_deleted = self.process_delete_topology_handle_transient_instances(
                                                                              custnode,
                                                                              deleteInstanceDetailsObj)

            if success == False:
                self.logger.error("Failed to delete the transient instances")
                raise Exception("Failed to delete the transient instances")

            custnode = CSN.CustNodeModel(iid=node_id, dbh=self.db_h)
            if not custnode.num_allocs:
                if not custnode.delete(self.db_h):
                    self.error += ("Unable to delete the customer site %s" % custnode.name)
                    self.logger.error("Unable to delete the customer site %s" % custnode.name)
                    job.status_msg = prepare_job_status_msg("Unable to delete the customer site %s" % custnode.name)
                    job.save_job(self.db_h)
                    raise Exception(self.error)
                else:
                    # Set the instance deletion as successful. Here we mark for bringup since the
                    # deletion case does not involve commit and other things.
                    if panorama_job_id:
                        save_bringup_status_to_commit_details_table(self.db_h, self.tenant_id,
                                                                    panorama_job_id, custnode.id, BRINGUP_SUCCESS)

            if is_instance_deleted == 1 or transient_instance_deleted == 1:
                # Let's also delete the DP instances if this is MP/DP separation.

                success = self.process_delete_topology_handle_mp_dp_instances(custnode, deleteInstanceDetailsObj)
                if success == False:
                    self.logger.error("Failed to delete tge mp dp separation instances, continuing!")

                # We need to delete the compute instance which is in region regardless of the node type.
                self.logger.info("TOPOLOGY_DELETE: Triggering instance change updates for region %s"
                                     % str(deleteInstanceDetailsObj.compute_region_idx))
                self.logger.info(f"process_delete_topology: TOPOLOGY_DELETE invoked for cloud {cloud_provider} for region {str(deleteInstanceDetailsObj.compute_region_idx)}")
                myret, _ = trigger_update_for_instance_changes(self.db_h,
                                                               deleteInstanceDetailsObj.compute_region_idx,
                                                               custnode.custid,
                                                               node_type=custnode.node_type,
                                                               job_tuple=deleteInstanceDetailsObj.job_tuple,
                                                               avctx=self.avctx,
                                                               alt_node_type=custnode.alt_node_type)
                if myret != True:
                    raise Exception("Failed to trigger update for instance changes.")

                swg_redep, regions = is_swg_proxy_deployment_needed(
                                         self.db_h, custid,
                                         custnode.node_type)
                if swg_redep:
                    for ep_region in regions:
                        self.logger.info(f"Redeploy SWG Proxy for {custid} "
                                         f"in {ep_region}")
                        myret, _ = trigger_update_for_instance_changes(
                                       self.db_h,
                                       ep_region,
                                       custid,
                                       node_type=NODE_TYPE_SWG_PROXY,
                                       avctx=self.avctx)

                        if myret != True:
                            err_msg = ("Failed to trigger update for SWG "
                                       "Proxy redeployment during instance "
                                       "changes.")
                            self.logger.error(err_msg)
                            raise Exception(err_msg)
                        else:
                            self.logger.info(f"Successfully redeployed EP "
                                             f"for {custid} in {ep_region}")
                else:
                    self.logger.info(f"No EP redeployment needed for {custid}")

                # We need to check if there are auto-scaled instances in this compute location, If there are then
                # they are also candidates for deletion.
                # If the deleted instance was a pinned instance then we try to get all the instances that
                # were auto-scaled.
                # We also want to handle cleanup for OCI cloud provider where we will not have pinned instance as all gateways
                # will always be behind the NLB
                self.logger.info(f"process_delete_topology: Delete Instance details object is_pinned_instance : {deleteInstanceDetailsObj.is_pinned_instance}, is_dynamic_instance: {deleteInstanceDetailsObj.is_dynamic_instance} for {custnode.node_type} for {deleteInstanceDetailsObj.compute_region_idx}")
                if deleteInstanceDetailsObj.is_pinned_instance and not deleteInstanceDetailsObj.is_dynamic_instance and \
                        (custnode.node_type in [NODE_TYPE_GP_GATEWAY, NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY]) \
                        and deleteInstanceDetailsObj.cloud_provider not in [PROVIDER_VMWARE_ESXI_DB_ENUM_VALUE]:

                    self.logger.info("process_delete_topology: Pinned instance entry found, proceeding to delete the Auto-scaled instances.")
                    self.logger.info(f"process_delete_topology: handle auto scale instances deletion for cloud {cloud_provider}")
                    success, instance_ids = self.process_delete_topology_handle_autoscaled_instances(custnode,
                                                                                       deleteInstanceDetailsObj)
                    if success == False:
                        self.logger.error("process_delete_topology: Failed to delete autoscaled instances")
                        raise Exception("process_delete_topology: Failed to delete auto scaled instances")

                    self.logger.info(f"process_delete_topology: handle NAT/NLB instances deletion for cloud {cloud_provider}")
                    # Before coming here, check if NAT/NLB are there or not.
                    # Also handle the NLB and NAT gateway cleanup.
                    success = self.process_delete_topology_handle_nat_nlb_deletion(custnode,
                                                                                   deleteInstanceDetailsObj)
                    if success == False:
                        self.logger.error("process_delete_topology: Failed to delete NAT/NLB instances")
                        raise Exception("process_delete_topology: Failed to delete NAT/NLB instances")

                    # Send delete to zti controller
                    handle_zti_location_delete(self.db_h,
                                                self.logger,
                                                custid,
                                                deleteInstanceDetailsObj.compute_region_idx,
                                                cloud_provider)

                if custnode.node_type in (NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY):
                    # delete cust_epaas_entry
                    self.delete_cust_epaas_entries(custid,
                                                   cust_ep_region_to_delete,
                                                   custnode, instance_ids)

                if custnode.node_type == NODE_TYPE_GP_GATEWAY:
                    # Determine if any GP Gateway instances for the tenant in the region are remaining (not marked for deletion)
                    status, count = \
                        get_count_of_topology_instances_not_marked_for_deletion_in_region(self.db_h,
                                                                                          custid,
                                                                                          deleteInstanceDetailsObj.compute_region_idx,
                                                                                          custnode.node_type)
                    if status == False:
                        self.logger.error(f"Failed to get count of topology instances that "
                                          f"are not marked for deletion for custid {custid} in "
                                          f"region {deleteInstanceDetailsObj.compute_region_idx} "
                                          f"for node type {custnode.node_type}")
                    elif count == 0:
                        # Delete the tenant region mapping for the central cache since all GP Gateways have been marked for deletion
                        remove_central_cache_tenant_region_mapping(self.db_h, custid, self.tenant_id, deleteInstanceDetailsObj.compute_region_idx)
                    else:
                        self.logger.error(f"Skip deletion of tenant region mapping since "
                                          f"there are still {count} GP Gateways for tenant {self.tenant_id} cust id {custid} "
                                          f"in region {deleteInstanceDetailsObj.compute_region_idx}")

                # For RN use case if the SPN are part of interconnect
                # then delete the firewall rules created in host project
                if custnode.node_type == NODE_TYPE_REMOTE_NET \
                        and deleteInstanceDetailsObj.is_using_sp_interconnect \
                        and custnode.inbound_access != 'disabled':
                    rastro_log_tag = b64encode(self.logger.update_logtag())
                    msg = f'{{ "tenant_id": {tenant_id},' \
                          f' "inst_id": "0",' \
                          f' "panorama_job_id": "{panorama_job_id}",' \
                          f' "firewall_name": "firewall-rule-{deleteInstanceDetailsObj.instance1_alias}",' \
                          f' "log_tag":  "{rastro_log_tag}" }}'
                    sns_sp_interconnect_firewall_rule_send_msg(self.db_h, tenant_id, msg)
            else:
                if custnode.node_type == NODE_TYPE_GP_GATEWAY and not deleteInstanceDetailsObj.is_okyo:
                    # This edge location now needs to be removed from the compute location.
                    # We will need to remove this location from the egress ip's in the compute idx as specified by the
                    # instance master table. We will also remove the route53 entries.
                    # TODO: ROHAN For OCI cloud; we also need to cleanup the NAT IM entry on edge location deletion event
                    cloud_provider = gpcs_get_cloud_type_from_region_idx(self.db_h, custnode.region, custid=custid)
                    self.logger.info(f"process_delete_topology: Handle topology delete for region {custnode.region} for custid {custid} with cloud_provider {cloud_provider}")
                    success = self.process_delete_topology_handle_edge_location_deletion(custnode,
                                                                                         deleteInstanceDetailsObj, cloud_provider=cloud_provider)

                    if success == False:
                        self.logger.error("Failed to cleanup the edge location info")
                        raise Exception("Failed to cleanup the edge location info")

            ret = True

        except Exception as E:
            self.return_err_msg = "Failed with exception, %s traceback: %s" % (str(E.args),
                                                                               str(traceback.format_exc()))
            self.logger.error(self.return_err_msg)
            ret = False
        finally:
            self.logger.info(f"Delete stale cust_epaas_entry if not yet deleted for custid {custid}")
            if ret is False and custnode and custnode.node_type in (NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY):
                # delete cust_epaas_entry
                is_delete_entry = self.delete_cust_epaas_entries(custid,
                                               custnode.region,
                                               custnode, instance_ids)
                if not is_delete_entry:
                    self.logger.info(f"Could not delete cust_epaas entriy for custid {custid} and region {custnode.region}")

            return ret
    
    def delete_cust_epaas_entries(self, custid, cust_ep_region_to_delete, custnode=None, instanceids_to_delete=None):
        if instanceids_to_delete is None:
            instanceids_to_delete = []
        self.logger.info("Entered delete_cust_epaas_entries block for custid %s" % custid)
        instanceids=[]
        success = False
        cursor = self.db_h.get_cursor()
        active_instances = False
        node_type = NODE_TYPE_SWG_PROXY
        alt_node_type = -1
        if custnode:
            custid = custnode.custid
            cust_ep_region_to_delete = custnode.region
            node_type = custnode.node_type
            alt_node_type = custnode.alt_node_type
        try:
            self.logger.info(
                "Deleting cust_epaas_entry for custid %s and region %s" % (custid, cust_ep_region_to_delete))
            self.logger.info(
                "Fetching instances for the custid %s and region %s" % (str(custid), str(cust_ep_region_to_delete)))
            sql = "select instance1_id AS instance_id from cust_topology where custid=%s and region=%s and " \
                  "is_deleted = 0 and node_type = %s and alt_node_type = %s UNION select instance2_id AS instance_id from cust_topology where " \
                  "custid=%s and region=%s and is_deleted = 0 and node_type = %s and alt_node_type = %s"
            params = (custid, cust_ep_region_to_delete, node_type, alt_node_type, custid, cust_ep_region_to_delete,
                      node_type, alt_node_type)
            cursor = self.db_h.get_cursor()
            try:
                cursor.execute(sql, params)
                ret = cursor.fetchall()
                for row in ret:
                    instanceids.append(row[0])
            except Exception as ex:
                self.logger.info("Unable to fetch instances for custid %s and region %s, failed with exception %s" % (
                    str(custid), str(cust_ep_region_to_delete), str(ex.args)))
            for instanceid in instanceids:
                try:
                    if instanceid in instanceids_to_delete:
                        continue
                    instance = INST.InstanceModel(instanceid, self.db_h)
                    if instance.get_param("id"):
                        if instance.get_param("mark_delete") == 0:
                            self.return_err_msg = "Instance_master still active hence cust_epaas_entry cannot be " \
                                                  "deleted"
                            self.logger.error(self.return_err_msg)
                            active_instances = True
                            break
                except Exception as e:
                    self.logger.info("Unable to fetch the instance for id %s and exception %s" %
                                     (instanceid, str(e.args)))
            if active_instances:
                raise Exception(
                    "Instances active for custid %s and region %s. Hence cust_epaas_entry can not be deleted"
                    % (str(custid), str(cust_ep_region_to_delete)))
            try:
                self.logger.info("Deleting alert policies if present")
                self.delete_alert_policy_for_deboarded_regions(node_type, custid, cust_ep_region_to_delete)
            except Exception as e:
                self.logger.error(f"error while trying to delete alert policies exception {str(e.args)}")
            sql = "delete from cust_epaas_config where custid=%s and " \
                  "compute_region_id=%s and node_type=%s and alt_node_type = %s"
            params = (custid, cust_ep_region_to_delete, node_type, alt_node_type)
            self.logger.info(sql % params)
            cursor.execute(sql, params)
            self.db_h.cursorclose(cursor)
            success = True
            self.logger.info("Successfully deleted cust_epaas_entry for custid %s region %s node_type %s and alt_node_type %s" % (
                custid, cust_ep_region_to_delete, node_type, alt_node_type))
            self.logger.info("Call function to release cnat_ips if present")
            release_cnat_public_ips(self.db_h, cust_ep_region_to_delete, custid)

        except Exception as ex:
            self.return_err_msg = "Error occurred while deleting cust_epaas_entry where custid = %s and region = " \
                                  "%s with error %s" % (custnode.custid, custnode.region, str(ex))
            self.logger.error(self.return_err_msg)
            self.db_h.cursorclose(cursor)
        finally:
            return success

    def handle_egress_ip_scale_event(self, instance_id, region_id, nr_ip_addresses):
        '''
            return: success as True if scale is done else False
        '''
        success = False
        if instance_id in [0, None] or region_id in [0, None] or nr_ip_addresses in [0, None]:
            self.sanitize_logger.error("One or more required parameters are empty or invalid. locals: %s" % str(locals()))
            return success
        try:
            # Check if the instance is present.
            inst_model = INST.InstanceModel(iid=instance_id, dbh=self.db_h)
            if inst_model.get_param("id") in [None, 0]:
                err_msg = "Failed to get the details about instance id %s" % str(inst_model.get_param("id"))
                self.logger.error(err_msg)
                raise Exception(err_msg)

            # Get the clean IP tag.
            customer_model = CM.CustomerModel(custid=inst_model.get_param("custid"), dbh=self.db_h)
            is_clean_ip_tag = customer_model.gcp_is_clean_ip_project()

            # Decrement nr_ip_addresses count by one if the region is a compute location since the instance would
            # already have one for it.
            if int(region_id) == inst_model.get_param("compute_region_idx") and int(nr_ip_addresses) > 0:
                nr_ip_addresses = int(nr_ip_addresses) - 1

            self.sanitize_logger.info("********** Success, Able to receive message *********** : %s" % str(locals()))

            reserve_pupi_ip = False
            sp_egress_type = "SP"
            if inst_model.get_param("is_using_sp_interconnect"):
                egress_res = self.db_h.get_sp_egress_type(inst_model.get_param("custid"),
                                                     get_cloud_native_location_name_from_region_id(self.db_h, inst_model.get_param(
                                                         "compute_region_idx")))
                if egress_res is not None and egress_res.ok and egress_res.result[0] is not None:
                    sp_egress_type = egress_res.result[0]

                if sp_egress_type not in ['PA', 'HYBRID']:
                    reserve_pupi_ip = True

            ret = allocate_new_egress_ip_nat_gateways(self.db_h,
                                                      inst_model,
                                                      edge_region_idx=region_id,
                                                      is_clean_ip_tag=is_clean_ip_tag,
                                                      num_of_ips_to_reserve=nr_ip_addresses,
                                                      num_of_ips_to_bind=nr_ip_addresses,
                                                      target_node_type=NODE_TYPE_GP_GATEWAY,
                                                      reserve_pupi_ip=reserve_pupi_ip)
            if not ret:
                self.logger.error("Failed to scale egress IP list for region %s, instance %s to nr_ip_addresses %s" %
                                  (str(region_id),
                                   str(inst_model.get_param("id")),
                                   str(nr_ip_addresses)))
                raise Exception("Failed to scale egress IP list")

            try:
                alt_node_type = inst_model.get_param("alt_node_type")
            except:
                alt_node_type = -1
            myres, trigger_update_nr = trigger_update_for_instance_changes(self.db_h,
                                                                           inst_model.get_param("compute_region_idx"),
                                                                           inst_model.get_param("custid"),
                                                                           node_type=inst_model.get_param("node_type"),
                                                                           alt_node_type=alt_node_type)
            if not myres:
                self.logger.error("Failed to trigger update for instance changes for cust id %s, compute region id %s."
                                  % (str(inst_model.get_param("custid")), str(inst_model.get_param("compute_region_idx"))))
                raise Exception("Failed to trigger update for instance changes")
            else:
                self.logger.info("Successfully triggered update for instance change for "
                                 "cust id %s, compute region %s, trigger id %s" % (str(inst_model.get_param("custid")),
                                                                                   str(inst_model.get_param("compute_region_idx")),
                                                                                   str(trigger_update_nr)))
            success = True

        except Exception as E:
            self.sanitize_logger.error("handle_egress_ip_scale_event Failed with exception %s, locals: %s" % (str(E.args),
                                                                                                     str(locals())))
            success = False
        finally:
            return success

    def delete_alert_policy_for_deboarded_regions(self, node_type, custid, region):
        success = True
        if node_type == NODE_TYPE_SWG_PROXY:
            try:
                customer_model = CM.CustomerModel(custid=custid, dbh=self.db_h)
                success = gcp_delete_alert_policy(cfg, self.db_h, custid, region,
                                        customer_model.get_param('project_id'))
            except Exception as e:
                self.logger.error(f"Exception while trying to delete alert policies , failed with exception {str(e.args)}")
                success = False
        else:
            self.logger.info("Alert policies are created/deleted only for swg nodes, skipping alert policy delete")
        return success

    def find_colo_components_need_trigger(self, colo_obj, regionid, custid, update_bgp_peer, create_vlan_att="false"):
        router_trigger_needed = False
        vlan_trigger_needed = False
        router_state = None
        vlan_state = None
        router_salt_profile = {}
        router_salt_profile_str = ""

        try:
            # Get state of Cloud Router and Vlan
            router_state = int(colo_obj.get_param('cr_state'))
            vlan_state = int(colo_obj.get_param('vlan_state'))

            # feedback messages
            if update_bgp_peer == "true":
                # message from post-processing of COLO CR or COLO VLAN
                #router_salt_profile['peer'] = colo_obj.get_param("name")
                #router_salt_profile_str = json.dumps(router_salt_profile)
                #colo_obj.set_param("salt_profile", router_salt_profile_str)
                router_trigger_needed = True
                vlan_trigger_needed = False
            elif create_vlan_att == "true":
                # message from post-processing of Colo CR to create VLAN
                router_trigger_needed = False
                vlan_trigger_needed = True
            else:
                # create router first and then trigger vlan creation
                if router_state == 0 and vlan_state == 0:
                    router_trigger_needed = True
                    vlan_trigger_needed = False
                else:
                    # This entry has CR created
                    # so this Compute Region has CR
                    router_trigger_needed = True
                    vlan_trigger_needed = False
        except Exception as E:
            self.sanitize_logger.error("find_colo_components_need_trigger Failed with exception %s, locals: %s"
                              % (str(E.args), str(locals())))
        finally:
            return router_trigger_needed, vlan_trigger_needed

    def handle_colo_vlan_onboard_event(self, colo_vlan_onb_id, region_id, custid, update_bgp_peer, create_vlan_att):
        '''
            return: success as True if scale is done else False
        '''
        success = False
        router_trigger_needed = False
        vlan_trigger_needed = False
        is_deleted = 0
        cloud_obj_summary_input = {
            "tenant_id": "0",
            "obj_id": "0",
            "obj_type": "Colo-Connect-Onboarding",
            "panorama_job_id": "0",
            "state": "Failure"}

        if colo_vlan_onb_id in [0, None] or region_id in [0, None] or custid in [0, None]:
            self.sanitize_logger.error("COLO_VLAN_ONBOARD_EVENT: One or more required parameters are empty or invalid. locals: %s" % str(locals()))
            return success
        try:
            # Check if the instance is present.
            colo_onb_model = COLOM.ColoOnboardingModel(id=colo_vlan_onb_id, dbh=self.db_h)
            if colo_onb_model.get_param("id") in [None, 0]:
                err_msg = "Failed to get the details about colo onboarding object id %s" % str(colo_onb_model.get_param("id"))
                self.logger.error(err_msg)
                raise Exception(err_msg)

            cloud_obj_summary_input = {
                "tenant_id": str(colo_onb_model.get_param("acct_id")),
                "obj_id": str(colo_onb_model.get_param("id")),
                "obj_type": "Colo-Connect-Onboarding",
                "panorama_job_id": str(colo_onb_model.get_param("panorama_job_id")),
                "state": "Failure"}

            # check if colo vlan deleted
            is_deleted = int(colo_onb_model.get_param("is_deleted"))

            # Check fields for cloud router
            if is_deleted == 0 and colo_onb_model.get_param("bgp") in [None, ""]:
                err_msg = "Failed to get the bgp details about colo onboarding object id %s" % str(colo_onb_model.get_param("id"))
                self.logger.error(err_msg)
                raise Exception(err_msg)

            self.sanitize_logger.info("********** Success, Able to receive message *********** : %s" % str(locals()))


            if is_deleted == 1:
                # colo vlan is marked deleted
                if update_bgp_peer == "true":
                    # update bgp peer true means vlan cleaned up
                    # clean peer from router or
                    # clean up router if no vlans
                    # remove entry from rds
                    router_trigger_needed = True
                    colo_onb_model.delete_entry()
                else:
                    vlan_trigger_needed = True
            else:
                # Logic to create salt profile for CLOUD Router
                # find which component need trigger
                router_trigger_needed, vlan_trigger_needed = self.find_colo_components_need_trigger(colo_obj=colo_onb_model,
                                                                                                    regionid=region_id,
                                                                                                    custid=custid,
                                                                                                    update_bgp_peer=update_bgp_peer,
                                                                                                    create_vlan_att=create_vlan_att)

            # trigger update component as needed
            if router_trigger_needed == True:
                myres, trigger_update_nr = trigger_update_for_instance_changes(self.db_h,
                                                                               colo_onb_model.get_param(
                                                                                   "compute_region_id"),
                                                                               colo_onb_model.get_param("custid"),
                                                                               node_type=NODE_TYPE_COLO_ROUTER,
                                                                               avctx=self.avctx)
                if not myres:
                    self.logger.error(
                        "Failed to trigger update on colo cloud router for colo onboarding object changes for cust id %s, compute region id %s."
                        % (str(colo_onb_model.get_param("custid")), str(colo_onb_model.get_param("compute_region_id"))))
                    raise Exception("Failed to trigger update for colo cloud router changes")

                else:
                    self.logger.info("Successfully triggered update for Colo Cloud Router change for "
                                     "cust id %s, compute region %s, trigger id %s" % (
                                     str(colo_onb_model.get_param("custid")),
                                     str(colo_onb_model.get_param("compute_region_id")),
                                     str(trigger_update_nr)))

            if vlan_trigger_needed == True:
                myres, trigger_update_nr = trigger_update_for_instance_changes(self.db_h,
                                                                               colo_onb_model.get_param("compute_region_id"),
                                                                               colo_onb_model.get_param("custid"),
                                                                               node_type=NODE_TYPE_SERVICE_CONN,
                                                                               avctx=self.avctx)
                if not myres:
                    self.logger.error("Failed to trigger update for colo onboarding object changes for cust id %s, compute region id %s."
                                      % (str(colo_onb_model.get_param("custid")), str(colo_onb_model.get_param("compute_region_id"))))
                    raise Exception("Failed to trigger update for instance changes")
                else:
                    self.logger.info("Successfully triggered update for Colo Onboarding change for "
                                     "cust id %s, compute region %s, trigger id %s" % (str(colo_onb_model.get_param("custid")),
                                                                                       str(colo_onb_model.get_param("compute_region_id")),
                                                                                       str(trigger_update_nr)))
            ret = update_sub_status_for_commit_job(dbh=self.db_h,
                                                        tenant_id=colo_onb_model.get_param("acct_id"),
                                                        job_id=colo_onb_model.get_param("panorama_job_id"),
                                                        sub_status="DEPLOYMENT_STARTED")
            if ret != True:
                err_msg = "Failed to update sub-status for colo onboarding id %s" % str(colo_vlan_onb_id)
                self.logger.error(err_msg)
                raise Exception(err_msg)
            success = True
        except Exception as E:
            self.sanitize_logger.error("handle_colo_vlan_onboard_event Failed with exception %s, locals: %s" % (str(E.args),
                                                                                                     str(locals())))
            ret = update_sub_status_for_commit_job(dbh=self.db_h,
                                                   tenant_id=colo_onb_model.get_param("acct_id"),
                                                   job_id=colo_onb_model.get_param("panorama_job_id"),
                                                   sub_status="DEPLOYMENT_FAILED")
            ret = update_status_for_commit_job(dbh=self.db_h,
                                                   tenant_id=colo_onb_model.get_param("acct_id"),
                                                   job_id=colo_onb_model.get_param("panorama_job_id"),
                                                   status="Failed")
            ret = update_cfgserv_cloud_objects_summary(obj_input=cloud_obj_summary_input, logger=self.logger)
            success = False
        finally:
            return success

    def handle_delete_instance_event(self, job_ref, instance_id, delete_cluster, is_transient):
        """
            return: success as True if scale is done else False
        """
        success = False
        if instance_id in [0, None] or delete_cluster in [None] or is_transient in [None]:
            self.sanitize_logger.error("One or more required parameters are empty or invalid. locals: %s" % str(locals()))
            return success
        try:
            self.sanitize_logger.info("********** Success, Able to receive message *********** : %s" % str(locals()))
            cluster_id = None
            # Check if the instance is present.
            inst_model = INST.InstanceModel(iid=instance_id, dbh=self.db_h)
            if inst_model.get_param("id") in [None, 0]:
                err_msg = "Failed to get the details about instance id %s" % str(inst_model.get_param("id"))
                self.logger.error(err_msg)
                raise Exception(err_msg)

            cluster_id = inst_model.get_param("clusterid")
            node_type = inst_model.get_param("node_type")
            try:
                alt_node_type = inst_model.get_param("alt_node_type")
            except:
                alt_node_type = -1
            custid = inst_model.get_param("custid")
            compute_region_id = inst_model.get_param("compute_region_idx")

            # Get the Sites corresponding to this instance.
            sites = inst_model.get_sites(dbh=self.db_h, transition=is_transient)
            self.logger.info("Sites are %s" % str(sites))
            self.logger.info("Transition sites are %s" % str(sites))

            # For each cust topology node id, lets unbind the instances from the cust topology node.
            for my_cust_topo_node_id in sites:
                my_cust_node_ref = CSN.CustNodeModel(iid=my_cust_topo_node_id, dbh=self.db_h)
                if my_cust_node_ref.id:
                    # Let's attempt to unbind this instance from node id.
                    lret = inst_model.unbind(my_cust_node_ref, dbh=self.db_h, job=job_ref)
                    if lret == False:
                        self.logger.error("Failed to unbind instance id %s from cust topology id %s" %
                                          (str(inst_model.get_param("id")), str(my_cust_node_ref.id)))
                else:
                    self.logger.info("Customer topology info related to id %s in cust_topology not found" %
                                     str(my_cust_topo_node_id))

            if delete_cluster and len(sites)  == 0:
                self.logger.info(f"Deleting stale instance {inst_model.get_param('id')} and "
                                 f"name {inst_model.get_param('name')}")
                inst_model.delete(dbh=self.db_h)

            '''
            # Time to delete the instance.
            self.logger.info("Attempting to delete the instance id %s" % str(inst_model.id))
            ret = inst_model.delete(dbh=self.db_h)
            if ret == False:
                err_msg = ("Error during instance deletion for id %s, name %s" %
                                  (str(inst_model.id), str(inst_model.name)))
                self.logger.error(err_msg)
                raise Exception(err_msg)
            '''

            myres, seq_nr = trigger_update_for_instance_changes(self.db_h,
                                                                compute_region_id,
                                                                custid,
                                                                node_type=node_type,
                                                                avctx=self.avctx,
                                                                alt_node_type=alt_node_type)
            if myres == False:
                self.logger.error("Failed to trigger instance changes for compute idx %s, "
                                  "custid %s and node_type %s" %
                                  (str(compute_region_id), str(custid), str(node_type)))
                raise Exception("Failed to trigger update for instance changes")
            else:
                self.logger.info("Successfully triggered instance change notification for "
                                 "compute idx %s, custid %s and node_type %s with seq # %s" %
                                 (str(compute_region_id), str(custid), str(node_type), str(seq_nr)))

            swg_redep, regions = is_swg_proxy_deployment_needed(
                                     self.db_h, custid,
                                     node_type)
            if swg_redep:
                for ep_region in regions:
                    self.logger.info(f"Redeploy SWG Proxy for {custid} "
                                     f"in {ep_region}")
                    myres, _ = trigger_update_for_instance_changes(
                                   self.db_h, ep_region, custid,
                                   node_type=NODE_TYPE_SWG_PROXY,
                                   avctx=self.avctx)

                    if myres == False:
                        self.logger.error(f"Failed to trigger instance "
                                      f"changes for SWG Proxy redeployment "
                                      f"in {ep_region} and custid {custid}")
                        raise Exception("Failed to trigger update for "
                                        "instance changes for SWG Proxy "
                                        "redeployment")
                    else:
                        self.logger.info(f"Successfully triggered instance "
                                     f"change notification for SWG Proxy "
                                     f"redeployment in {ep_region} and "
                                     f"custid {custid} with seq # {seq_nr}")
            else:
                self.logger.info(f"No EP redeployment needed for {custid}")

            myres = delete_pinned_instance_entry_by_new_inst_id(self.db_h, instance_id)
            if myres == False:
                self.logger.error("Failed to delete new_instance_id %s from pinned_instance_upgrade_table" %
                                  str(instance_id))

            success = True

        except Exception as E:
            self.sanitize_logger.error("handle_delete_instance_event Failed with exception %s, locals: %s, Traceback: %s" %
                              (str(E.args), str(locals()), str(traceback.format_exc())))
            success = False
        finally:
            return success

    def handle_tenant_ipv6_settings_event(self, job_ref, tenant_id, region_name, mode, node_type):
        success = False
        try:
            if tenant_id in [0, None] or region_name in ["", None] or mode in [None] or node_type in [0, None]:
                self.sanitize_logger.error("One or more required parameters are empty or invalid. locals: %s" % str(locals()))
                return success
            node_type = int(node_type)
            cust_model = CM.CustomerModel(acct_id=tenant_id, dbh=self.db_h)
            custid = cust_model.get_param("id")
            if custid == None:
                raise Exception("Failed to get the custid")

            if node_type == NODE_TYPE_GP_PORTAL and region_name == "all":
                lret, instance_id_list = find_instances_by_custid_and_node_type(self.db_h,
                                                                                custid,
                                                                                node_type)
            else:
                lret, instance_id_list = get_instance_ids_by_tenant_id_and_aggregate_region_name(self.db_h,
                                                                                         tenant_id,
                                                                                         region_name,
                                                                                         node_type)
            if lret == False or instance_id_list == None:
                err_msg = "Failed to get the vmid to IPv6 addr dict"
                raise Exception(err_msg)
            self.logger.info(f"get_instance_ids_by_tenant_id_and_aggregate_region_name {instance_id_list}")

            if mode == "disable":
                self.logger.info(f"Removing route53 entry for instance id list {instance_id_list}")
                self.logger.info("Removing route53 entry for Ipv6 addresses")

                for inst_id in instance_id_list:
                    inst_model = INST.InstanceModel(iid=inst_id, dbh=self.db_h)
                    if inst_model.get_param("id") == None:
                        err_msg = (f"Failed to find id {inst_id} from the database")
                        raise Exception(err_msg)

                    egress_ipv6_dict_str = inst_model.get_param("egress_ipv6_list")
                    egress_ipv6_dict = dict()
                    if inst_model.get_param("is_instance_behind_nlb"):
                        #For intances behind NLB, interface_ipv6_list will contain all ipv6 IPs for the NLB, including compute, so we need to pop compute
                        self.logger.info(f"Instance is behind nlb, using interface_ipv6_list instead of egress_ipv6_list")
                        #Keep the var name the same, to avoid duplicate code
                        egress_ipv6_dict_str = inst_model.get_param("interface_ipv6_list")
                        
                    if isinstance(egress_ipv6_dict_str, str) :
                        try:
                            egress_ipv6_dict = json.loads(egress_ipv6_dict_str)
                            if inst_model.get_param("is_instance_behind_nlb"):
                                compute_str = str(inst_model.get_param("compute_region_idx"))
                                self.logger.info(f"For instances behind NLB, popping compute region from interface_ipv6_dict before cleaning up the FQDN. Dict: "
                                                f"{egress_ipv6_dict}, compute is: {compute_str}")
                                if egress_ipv6_dict.get(compute_str):
                                    egress_ipv6_dict.pop(compute_str)

                            for key in egress_ipv6_dict:
                                egress_ipv6_dict[key] = egress_ipv6_dict[key].split('/')[0]

                        except Exception as E:
                            self.logger.error(f"Failed to decode json str {egress_ipv6_dict_str}, exception {E.args}")
                            continue

                        inst_model.cleanup_fqdn(self.db_h,
                                            selective_egress_ip_deletion=True,
                                            egress_ip_dict=egress_ipv6_dict,
                                            is_ipv6=True)

                    if inst_model.get_param("compute_region_idx") and inst_model.get_param("public_ipv6"):
                        # Handle the compute region idx differently.
                        inst_model.cleanup_fqdn(dbh=self.db_h, is_ipv6=True)

            success = True
        except Exception as E:
            self.sanitize_logger.error("handle_tenant_ipv6_settings_event Failed with exception %s, locals: %s, Traceback: %s" %
                              (str(E.args), str(locals()), str(traceback.format_exc())))
            success = False
        finally:
            return success

    def process_msg(self, msg, workflow, jobid, result):
        job_tuple = None
        # tenant_id=None
        status_update_done = False
        ret = False
        # These are all used for updating the job status.
        job_instances = []
        job_transient_instances = []
        mark_bringup_success = False
        self.err_msg = ""
        instance1_id = -1
        instance2_id = -1
        self.return_err_msg = ""

        try:
            # print "Processing message %s Id: %s\n" % (inmsg['Body'], inmsg['MessageId'])
            start_time = time.time()
            self.logger.info(f"Starting processing of new message at {start_time}")
            job = JOB.OrchJobs(jobid=jobid, dbh=self.db_h)
            self.avctx.panorama_job_id = None
            self.rastro_log_tag = None
            if not job.jobid:
                self.error = ("OnboardingHandler:process_msg:Unable to find job with id %d" % jobid)
                self.logger.error("OnboardingHandler:process_msg:Unable to find job with id %d" % jobid)
                return
            job.status = OrchstrationHandler.status
            job.status_msg = prepare_job_status_msg("Starting orchestration for the job")
            job.save_job(self.db_h)
            if hasattr(msg, 'panorama_job_id'):
                self.avctx.panorama_job_id = msg.panorama_job_id
                # panorama_job_id = msg.panorama_job_id
            if hasattr(msg, 'rastro_log_tag'):
                rastro_log_tag = msg.rastro_log_tag
                # Set the new rastro tag in the logger:
                try:
                    self.rastro_log_tag = json.loads(base64.b64decode(rastro_log_tag).decode('utf-8'))
                    self.logger.set_enhanced_log_tag(self.rastro_log_tag)
                except Exception as E:
                    self.logger.error("Failed to decode the rastro tag. Exception %s. Traceback %s."
                                      % (str(E.args), str(traceback.format_exc())))

            # TOPOLOGY_ADD WORKFLOW
            if workflow == 'TOPOLOGY_ADD':
                instances = []
                orchmsg = msg
                self.avctx.cust_id = orchmsg.custid
                for node in orchmsg.nodes:
                    custnode = CSN.CustNodeModel(node.nodeid, dbh=self.db_h)
                    custnode.set_avisar_ctx(self.db_h, avctx=self.avctx)
                    self.set_log_context(custnode)
                    ret = self.process_add_topology(
                        node,
                        job,
                        job_instances)
                    if not ret and custnode.is_dynamic_node and not custnode.is_deleted:
                        self.logger.info(f"Cleaning up dynamic cust_topology so that we can retrigger "
                                         f"the scale up")
                        custnode.mark_for_delete(self.db_h)
                    return

            elif workflow == "TOPOLOGY_UPDATE":
                node = msg.nodes[0]
                self.avctx.cust_id = msg.custid
                custnode = CSN.CustNodeModel(node.nodeid, dbh=self.db_h)
                self.set_log_context(custnode)
                custnode.set_avisar_ctx(self.db_h, avctx=self.avctx)
                ret = self.process_update_topology(node,
                                                   job,
                                                   job_instances,
                                                   job_transient_instances)
                return
            elif workflow == "UPGRADE_EP_OUTSIDE_PANOS":
                custid = msg.custid
                compute_region_id = msg.compute_region_id
                cloud_provider = msg.cloud_provider
                node_type = msg.node_type
                self.logger.info("Entered UPGRADE_EP_OUTSIDE_PANOS workflow for custid %s and region %s"
                                 % (str(custid), str(compute_region_id)))
                cust_ep_cfg = CustEpaasConfigModel(self.db_h, custid, compute_region_id, PROVIDER_GCP, node_type=node_type)
                if not cust_ep_cfg.get_entry():
                    self.logger.info(f"No cust_epaas_config entry found for custid {str(custid)} and region "
                                     f"{str(compute_region_id)}, exiting migration")
                    return False
                if cust_ep_cfg.get_param("migrate_ep_status") in ("IN_PROGRESS", "DONE_MIGRATION","UPGRADE_IN_PROGRESS"):
                    self.logger.info(f"Migration already in progress for custid {str(custid)} and region "
                                     f"{str(compute_region_id)}, exiting operation as its a duplicate request")
                    return False
                else:
                    self.logger.info(f"Setting migration status to UPGRADE_IN_PROGRESS for custid {str(custid)} and region "
                                     f"{str(compute_region_id)}")
                    cust_ep_cfg.set_param("migrate_ep_status", "UPGRADE_IN_PROGRESS")
                    cust_ep_cfg.save()
                self.set_log_context(None, custid)
                proxy_bringup = swgproxy_bringup(self.db_h, node_type)
                proxy_bringup.add_salt_profile_for_ep_region(custid, compute_region_id, node_type=node_type)
                sql = "SELECT cust_topology.id FROM cust_topology WHERE custid = %s AND region = %s and " \
                      "node_type = %s"
                params = (custid, compute_region_id, node_type)
                cursor = self.db_h.get_cursor()
                ret = None
                try:
                    cursor.execute(sql, params)
                    ret = cursor.fetchall()
                    self.db_h.cursorclose(cursor)
                except Exception as e:
                    self.logger.info("Failed to get cust_topology records for custid %s and region %s. Ex(%s)"
                                     % (str(custid), str(compute_region_id), str(e)))
                    cust_ep_cfg.set_param("migrate_ep_status", "FAILED")
                    cust_ep_cfg.save()
                    return
                if ret:
                    custmodel = CM.CustomerModel(custid=custid, dbh=self.db_h)
                    migrating_nodes={}
                    for custnodeId in ret:
                        nodemodel = CSN.CustNodeModel(iid=custnodeId[0], dbh=self.db_h)

                        old_instance1_id=nodemodel.instance1_id
                        my_version = custmodel.get_param("version")
                        ret = self.db_h.get_nodetype_sw_version_from_cust_version(
                        'SWGPROXY', my_version)
                        node_version = ret.result[0]
                        custnodes = [VmNodes(str(nodemodel.id), 'SWGPROXY', nodemodel.region,
                                node_version, logger=self.logger)]
                        for custnode in custnodes:
                            inst_ids = self.db_h.get_new_instance(custnode.version, custnode.vmtype, compute_region_id, nodemodel, 0, custid,
                                                                  is_clean_pipe=nodemodel.is_clean_pipe)
                            if not inst_ids:
                                self.error = self.db_h.error
                                self.logger.error("Unable to get new instance for node type %s" % str(custnode.vmtype))
                                ep_val = cust_ep_cfg.get_param("old_eproxy_outside_panos")
                                cust_ep_cfg.set_param("eproxy_outside_panos", ep_val)
                                cust_ep_cfg.set_param("migrate_ep_status", "FAILED")
                                cust_ep_cfg.save()
                                return (None, None, False)
                        self.logger.info("New instance created %s" % (str(nodemodel.instance1_id)))
                        migrating_nodes[nodemodel.id] = old_instance1_id
                    cust_ep_cfg.set_param("migrating_nodes", json.dumps(migrating_nodes))
                    cust_ep_cfg.save()
                ret = upgrade_ep_envoy_outside_panos(self.db_h,
                                                     custid,
                                                     compute_region_id,
                                                     cloud_provider,
                                                     node_type)
                return
            elif workflow == "TOPOLOGY_DELETE":
                self.avctx.cust_id = msg.custid
                cloud_provider = msg.cloud_provider
                if len(msg.nodes) < 1:
                    self.error = ("TOPOLOGY_DELETE: No nodes to delete")
                    self.logger.error("TOPOLOGY_DELETE: No nodes to delete")
                    job.status_msg = prepare_job_status_msg("TOPOLOGY_DELETE: No Nodes to delete")
                    job.save_job(self.db_h)
                    ret = True
                    return
                node = msg.nodes[0]
                custnode = CSN.CustNodeModel(iid=node, dbh=self.db_h)
                if not custnode.id:
                    self.error = ("TOPOLOGY_DELETE: Node %s is not found" % custnode.name)
                    self.logger.error("TOPOLOGY_DELETE: Node %s is not found" % custnode.name)
                    job.status_msg = prepare_job_status_msg("TOPOLOGY_DELETE: Node %s is not found" % custnode.name)
                    job.save_job(self.db_h)
                    ret = True
                    return

                self.set_log_context(custnode)
                custnode.set_avisar_ctx(self.db_h, avctx=self.avctx)
                ret = self.process_delete_topology(
                    job,
                    job_instances,
                    job_transient_instances, cloud_provider=cloud_provider)
                return

            elif workflow == "UPGRADE_ENVOY_VERSION":
                custid = msg.custid
                compute_region_id = msg.compute_region_id
                cloud_provider = msg.cloud_provider
                node_type = msg.node_type
                ret = self.process_update_envoy_version(custid,
                                                        compute_region_id,
                                                        cloud_provider, job, node_type)
                return

            elif workflow == "EP_CNAT_SCALEOUT":
                custid = msg.custid
                compute_region_id = msg.compute_region_id
                is_ff_enabled = is_ep_cnat_feature_enabled(self.db_h, custid)
                if not is_ff_enabled:
                    #TODO: Add avisar alert
                    err_msg = f"Cloud NAT feature flag requirement not met, CNAT will not autoscale fot custid {custid}"
                    self.logger.error(err_msg)
                    raise Exception(err_msg)
                success = scale_ep_cnat(self.db_h, custid, compute_region_id)
                return success

            elif workflow == "EGRESS_IP_SCALE_EVENT":
                if not hasattr(msg, 'EgressIPScaleEventMsg'):
                    self.return_err_msg = "Message does not have EgressIPScaleEventMsg attribute"
                    raise Exception(self.return_err_msg)
                else:
                    ret = self.handle_egress_ip_scale_event(msg.EgressIPScaleEventMsg.instance_id,
                                                            msg.EgressIPScaleEventMsg.region_id,
                                                            msg.EgressIPScaleEventMsg.nr_ip_addresses)
                    job.status_msg = prepare_job_status_msg("EGRESS_IP_SCALE_EVENT: Return value %s, TraceID: %s"
                                                            % (str(ret), str(self.logger.uuid)))
                    job.save_job(self.db_h)
                    return

            elif workflow == "MIGRATE_CLUSTER_TO_IPV6_EVENT":
                if not hasattr(msg, 'MigrateClusterToIPV6EventMsg'):
                    self.return_err_msg = "Message does not have MigrateClusterToIPV6EventMsg attribute"
                    raise Exception(self.return_err_msg)
                else:
                    ret = migrate_cluster_to_ipv6(self.db_h, msg.MigrateClusterToIPV6EventMsg.cluster_id)
                    job.status_msg = prepare_job_status_msg("MIGRATE_CLUSTER_TO_IPV6_EVENT: Return value %s, "
                                                            "TraceID: %s"
                                                            % (str(ret), str(self.logger.uuid)))
                    job.save_job(self.db_h)
                    return

            elif workflow == "COLO_VLAN_ONBOARD_EVENT":
                if not hasattr(msg, 'ColoVlanOnboardEventMsg'):
                    self.return_err_msg = "Message does not have ColoVlanOnboardEventMsg attribute"
                    raise Exception(self.return_err_msg)
                else:
                    ret = self.handle_colo_vlan_onboard_event(msg.ColoVlanOnboardEventMsg.colo_vlan_onb_id,
                                                            msg.ColoVlanOnboardEventMsg.region_id,
                                                            msg.ColoVlanOnboardEventMsg.custid,
                                                            msg.ColoVlanOnboardEventMsg.update_bgp_peer,
                                                            msg.ColoVlanOnboardEventMsg.create_vlan_att)
                    job.status_msg = prepare_job_status_msg("COLO_VLAN_ONBOARD_EVENT: Return value %s, TraceID: %s"
                                                            % (str(ret), str(self.logger.uuid)))
                    job.save_job(self.db_h)
                    return

            elif workflow == "PRIV_SASE_NODE_UPDATE_EVENT":
                if not hasattr(msg, 'PrivSaseNodeUpdateEventMsg'):
                    self.return_err_msg = "Message does not PrivSaseNodeUpdateEventMsg attribute"
                    raise Exception(self.return_err_msg)
                else:
                    ret = handle_priv_sase_node_update_event(self.db_h, sase_private_region_id=msg.PrivSaseNodeUpdateEventMsg.sase_private_region_id,
                                                             service_node_type= msg.PrivSaseNodeUpdateEventMsg.service_node_type,
                                                            custid= msg.PrivSaseNodeUpdateEventMsg.custid,
                                                            node_type= msg.PrivSaseNodeUpdateEventMsg.node_type)
                    job.status_msg = prepare_job_status_msg("PRIV_SASE_NODE_UPDATE_EVENT: Return value %s, TraceID: %s"
                                                            % (str(ret), str(self.logger.uuid)))
                    job.save_job(self.db_h)
                    return ret

            elif workflow == "DELETE_INSTANCE_EVENT":
                if not hasattr(msg, 'DeleteInstanceEventMsg'):
                    self.return_err_msg = "Message does not have DeleteInstanceEventMsg attribute"
                    raise Exception(self.return_err_msg)
                else:
                    ret = self.handle_delete_instance_event(job,
                                                            msg.DeleteInstanceEventMsg.instance_id,
                                                            msg.DeleteInstanceEventMsg.delete_cluster,
                                                            msg.DeleteInstanceEventMsg.is_transient)
                    job.status_msg = prepare_job_status_msg("DELETE_INSTANCE_EVENT: Return value %s, TraceID: %s"
                                                            % (str(ret), str(self.logger.uuid)))
                    job.save_job(self.db_h)
                    return

            elif workflow == "TENANT_IPV6_SETTINGS_EVENT":
                if not hasattr(msg, 'TenantIpv6SettingsEventMsg'):
                    self.return_err_msg = "Message does not have TenantIpv6SettingsEventMsg attribute"
                    raise Exception(self.return_err_msg)
                else:
                    ret = self.handle_tenant_ipv6_settings_event(job,
                                                                 msg.TenantIpv6SettingsEventMsg.tenant_id,
                                                                 msg.TenantIpv6SettingsEventMsg.region_name,
                                                                 msg.TenantIpv6SettingsEventMsg.mode,
                                                                 msg.TenantIpv6SettingsEventMsg.node_type)
                    job.status_msg = prepare_job_status_msg("TENANT_IPV6_SETTINGS_EVENT: Return value %s, TraceID: %s"
                                                            % (str(ret), str(self.logger.uuid)))
                    job.save_job(self.db_h)
                    return

            else:
                self.logger.error("%s: WORKFLOW %s is not supported\n" % (
                    self.qname, workflow))
                return

        except Exception as E:
            self.return_err_msg = "process_msg: Failed with exception %s, Traceback: %s" % (str(E.args),
                                                                                            str(traceback.format_exc()))
            self.logger.error(self.return_err_msg)
            ret = False
        finally:
            cfgservice_commit_status_payload = dict()
            cfgservice_commit_status_payload["panorama_job_id"] = self.avctx.panorama_job_id
            cfgservice_commit_status_payload["tenant_id"] = self.avctx.tenant_id
            cfgservice_commit_status_payload["cust_topology_id"] = self.avctx.cust_topology_id
            cfgservice_commit_status_payload["instance1_id"] = instance1_id
            cfgservice_commit_status_payload["instance2_id"] = instance2_id
            cfgservice_commit_status_payload["workflow_type"] = workflow
            cfgservice_commit_status_payload["log_id"] = self.logger.uuid
            cfgservice_commit_status_payload["workflow_status"] = ""
            cfgservice_commit_status_payload["workflow_status_details"] = ""
            cfgservice_commit_status_payload["orchestrator_status"] = ""
            cfgservice_commit_status_payload["orchestrator_status_details"] = ""
            cfgservice_commit_status_payload["node_type"] = msg.node_type
            if ret == True and self.avctx.panorama_job_id and self.avctx.tenant_id and self.avctx.cust_topology_id:
                if self.avctx.panorama_job_id and self.avctx.tenant_id and self.avctx.cust_topology_id:
                    instance1_id = -1
                    instance2_id = -1

                    if job_transient_instances and len(job_transient_instances) > 0:
                        instance1_id = job_transient_instances[0]
                        if len(job_transient_instances) > 1:
                            instance2_id = job_transient_instances[1]
                    elif job_instances and len(job_instances) > 0:
                        instance1_id = job_instances[0]
                        if len(job_instances) > 1:
                            instance2_id = job_instances[1]
                    else:
                        self.logger.info("No instances present.")

                    res = save_orch_success_state_to_commit_status_detail_table(self.db_h,
                                                                                self.avctx.tenant_id,
                                                                                self.avctx.panorama_job_id,
                                                                                self.avctx.cust_topology_id,
                                                                                instance1_id,
                                                                                instance2_id)

                    cfgservice_commit_status_payload["orchestrator_status"] = ORCHESTRATION_SUCCESS
                    cfgservice_commit_status_payload["instance1_id"] = instance1_id
                    cfgservice_commit_status_payload["instance2_id"] = instance2_id
                    send_configservice_commit_status(cfgservice_commit_status_payload, self.db_h)
                    if res is not True:
                        err_msg = "Failed to update orchestration details_to_commit_status_table, Failing commit!"
                        self.logger.error(err_msg)
                        # TODO: May be in future change from BRINGUP_FAILED to new RDS Save failure. BRINGUP_FAILED means bigger/broader issue.
                        cfgservice_commit_status_payload["workflow_status"] = BRINGUP_FAILED
                        cfgservice_commit_status_payload["workflow_status_details"] = "Exception: %s" % err_msg
                        send_configservice_commit_status(cfgservice_commit_status_payload, self.db_h)
                        raise Exception(err_msg)
                    if mark_bringup_success is True:
                        save_bringup_status_to_commit_details_table(self.db_h,
                                                                    self.avctx.tenant_id,
                                                                    self.avctx.panorama_job_id,
                                                                    self.avctx.cust_topology_id,
                                                                    BRINGUP_SUCCESS)
                        cfgservice_commit_status_payload["workflow_status"] = BRINGUP_SUCCESS
                        send_configservice_commit_status(cfgservice_commit_status_payload, self.db_h)

            elif ret is False and self.avctx.panorama_job_id and self.avctx.tenant_id and self.avctx.cust_topology_id:
                if self.avctx.panorama_job_id and self.avctx.tenant_id and self.avctx.cust_topology_id:
                    res = save_orch_failed_state_to_commit_status_detail_table(self.db_h,
                                                                               self.avctx.tenant_id,
                                                                               self.avctx.panorama_job_id,
                                                                               self.avctx.cust_topology_id)
                    cfgservice_commit_status_payload["orchestrator_status"] = ORCHESTRATION_FAILED
                    cfgservice_commit_status_payload["workflow_status"] = BRINGUP_FAILED
                    send_configservice_commit_status(cfgservice_commit_status_payload, self.db_h)
                    if res is not True:
                        err_msg = "Failed to update orchestration details_to_commit_status_table, Failing commit!"
                        self.logger.error(err_msg)
                        # TODO: May be in future change from BRINGUP_FAILED to new RDS Save failure. BRINGUP_FAILED means bigger/broader issue.
                        cfgservice_commit_status_payload["workflow_status"] = BRINGUP_FAILED
                        cfgservice_commit_status_payload["workflow_status_details"] = "Exception: %s" % err_msg
                        send_configservice_commit_status(cfgservice_commit_status_payload, self.db_h)
                        raise Exception(err_msg)
            else:
                self.logger.info("Not updating the commit job status table.")

            if ret is False:
                self.avctx.set_ctx(metric_type=ORCHESTRATION_STATUS_FAILED,
                                   metric_state=str(workflow) + " : " + str(self.return_err_msg),
                                   metric_severity=METRIC_SEVERITY_CRITICAL)
            else:
                self.avctx.set_ctx(metric_type=ORCHESTRATION_STATUS_SUCCESS,
                                   metric_state="Orchestration workflow %s is successful with instances (%s, %s)" %
                                                (str(workflow), str(instance1_id), str(instance2_id)),
                                   metric_severity=METRIC_SEVERITY_INFO)
            self.avctx.set_ctx(trace_id=self.logger.get_enhanced_traceid() if not None else self.logger.uuid)

            # Publish the message to the Service.
            resp = self.avctx.publish_event()
            self.logger.info("Avisar Service publish event response: %s" % str(resp))

            self.logger.reset_enhanced_log_tag()
            self.logger.info(f"Finished processing of message. Total time taken = {time.time() - start_time}")
            return ret

    def poll_two_hrs_task(self):
        try:
            if self.shard_id == 0:
                perform_integrity_checker_tasks(self)
        except Exception as E:
            self.logger.error(f"Failed with Exception in integrity checker!, Exception {E.args}")

    def poll_hook(self):
        # If 10 seconds is too aggressive, we might think of changing it to 30 seconds.
        pinned_gp_gateway_instance_upgrade_orchestration(self, db_h=self.db_h)

    # Update parent cust_topology entry on instance deletion to ensure
    # zone balancing on future bandwidth upscaling
    def gpcs_cleanpipe_update_zone_usage(self, cust_node_id):
        self.logger.info("Updating zone usage on instance deletion")
        sql = "SELECT cust_topology.parent_id, cust_topology.assigned_zone FROM cust_topology WHERE id='%s'"
        params = (cust_node_id,)
        cursor = self.db_h.get_cursor()
        ret = None
        try:
            cursor.execute(sql, params)
            ret = cursor.fetchall()
            self.db_h.cursorclose(cursor)
        except Exception as e:
            self.logger.info("Failed to get parent_id, assigned_zone. Ex(%s)" % str(e))
            return
        if ret is None:
            self.logger.info("Bad SQL Response")
            return
        (parent_id, assigned_zone,) = ret[0]
        if parent_id is None:
            return
        if parent_id == 0:
            return
        if assigned_zone is None:
            return
        # Get the zone_usage stats from the parent
        ret = None
        sql = "SELECT cust_topology.zone_usage FROM cust_topology WHERE cust_topology.id=%s"
        params = (str(parent_id),)
        cursor = self.db_h.get_cursor()
        try:
            cursor.execute(sql, params)
            ret = cursor.fetchall()
            self.db_h.cursorclose(cursor)
        except Exception as e:
            self.logger.info("Failed to get zone_usage from parent. Ex(%s)" % str(e))
            return
        if ret is None:
            self.logger.info("Bad SQL response")
            return
        if len(ret) == 0:
            return
        zone_usage = {}
        if not ret[0]:
            # This parent is not keeping track of children
            self.logger.info("No zone_usage stats being tracked by the parent")
            return
        (z_usage,) = ret[0]
        zone_usage = json.loads(z_usage)
        # Decrement zone_usage count on the assigned zone
        zone_usage[assigned_zone][1] = str(int(zone_usage[assigned_zone][1]) - 1)
        sql = "UPDATE cust_topology SET zone_usage=%s WHERE id=%s"
        params = (json.dumps(zone_usage), str(parent_id),)
        cursor = self.db_h.get_cursor()
        try:
            cursor.execute(sql, params)
            self.db_h.cursorclose(cursor)
        except Exception as e:
            self.logger.info("Failed to update zone_usage. Ex(%s)" % str(e))

    def gpcs_delete_state_clean_pipe_site_entry(self, custid, siteid):
        self.logger.info("deleting clean_pipe_site_entry: custid:%d siteid: %d " % (custid, siteid))
        sql = "delete from state_clean_pipe_site where custid=%s and site_id=%s"
        params = (custid, siteid,)
        cursor = self.db_h.get_cursor()
        try:
            cursor.execute(sql, params)
            self.db_h.cursorclose(cursor)
        except Exception as e:
            self.logger.info("Failed to delted clean_pipe_site_entry. Ex(%s)" % str(e))

    def process_inbound_access_cfg_change(self, primary_instance_model, custnode_model):
        siteid = custnode_model.id
        instance_id = primary_instance_model.get_param("id")
        acct_id = primary_instance_model.get_param("acct_id")
        ha_peer = primary_instance_model.get_param("ha_peer")
        self.logger.info("processing inbound access cfg change for site: %s" % (siteid))
        try:
            gcp_ip_mgmt = IP_MGMT.GCPIPHandler(
                self.db_h, str(instance_id),
                str(acct_id))
            gcp_ip_mgmt.init_inbound_access_ip_mapping(siteid, update_existing_mapping=True)
            gcp_ip_mgmt.copy_ip_from_active_instance(
                str(ha_peer))
            myres, _ = trigger_update_for_instance_changes(self.db_h,
                                                           primary_instance_model.get_param("compute_region_idx"),
                                                           primary_instance_model.get_param("custid"),
                                                           node_type=custnode_model.node_type,
                                                           avctx=self.avctx,
                                                           alt_node_type=custnode_model.alt_node_type)
            if myres == False:
                self.logger.error("Failed to trigger instance changes for compute idx %s" %
                                  (str(primary_instance_model.get_param("compute_region_idx"))))

            if self.should_trigger_sp_firewall_rule_operation(primary_instance_model, custnode_model):
                # In case of sp-interconnect the firewall rules are configured on the sharedVPC
                # and not on the service project VPC. So direct API calls has to be executed.
                rastro_log_tag = b64encode(self.logger.update_logtag())
                msg = f'{{ "tenant_id": {acct_id},' \
                      f' "panorama_job_id":"{self.avctx.panorama_job_id}",' \
                      f' "inst_id": {instance_id},' \
                      f' "firewall_name": "firewall-rule-{primary_instance_model.get_param("alias")}",' \
                      f' "log_tag":  "{rastro_log_tag}" }}'
                sns_sp_interconnect_firewall_rule_send_msg(self.db_h, acct_id,
                                                           msg, primary_instance_model.get_param("alias"))
        except Exception as e:
            self.logger.error("Failed to processing inbound access cfg change for site: %s"
                              " Exception: %s %s" % (siteid, e, str(traceback.format_exc())))
            return
        self.logger.info("Successfully processed inbound access cfg change for site: %s" % (siteid))
        return True

    def should_trigger_sp_firewall_rule_operation(self, inst_model, cust_node):
        """
        Determine if the firewall rule operations has to be created on the host project.
        Currently, only for firewalls in sp-interconnect regions and in inbound access RN the rules has to be created
        Only for cust_topology entries with inbound_access = disabled are Inbound access RN.
        @param inst_model: instance_master model object.
        @param cust_node: cust_topology object of the FW.
        @return: boolean
        """
        msg = f"Checking if firewall rule configuration is required for, " \
              f"is_using_sp_interconnect {inst_model.get_param('is_using_sp_interconnect')}, " \
              f"node_type: {inst_model.get_param('node_type')}, " \
              f"cust_node_model.inbound_access: {cust_node.inbound_access}"
        self.logger.info(msg)
        should_fire = False
        if inst_model.get_param("is_using_sp_interconnect") \
                and inst_model.get_param('node_type') == NODE_TYPE_REMOTE_NET \
                and cust_node.inbound_access != 'disabled':
            should_fire = True

        self.logger.info(f"Return should_trigger_sp_firewall_rule_operation: {should_fire}")
        return should_fire


    def get_current_service_index_by_cust_id_and_region(self, cust_id, compute_region_idx):
        """
        Retrieves the highest service index from SPN names matching the given customer ID 
        and compute region index.
        Returns 0 if no instances exist or no valid service index is found.
    
        Args:
            cust_id (int): Customer ID
            compute_region_idx (int): Compute region index
    
        Returns:
        
            int: Current highest service index or 0 if none found
    
        Raises:
            Exception: If database query fails or invalid parameters
        """

    
        if cust_id is None:
            self.logger.error(f"Invalid cust_id")
            return 0
    
        if compute_region_idx is None:
            self.logger.error(f"Invalid compute_region_idx: must be non-negative integer, got {compute_region_idx}")
            return 0
    
        try:
            sql = """
                SELECT spn_name 
                FROM instance_master 
                WHERE custid = %s 
                AND node_type = %s
                AND compute_region_idx = %s 
                AND spn_name IS NOT NULL 
                AND spn_name != ''
                AND id = clusterid
            """
            params = (cust_id, NODE_TYPE_REMOTE_NET, compute_region_idx)
        
            self.logger.info(f"Getting service index for cust_id: {cust_id}, region: {compute_region_idx}")
        
            cursor = self.db_h.get_cursor()
            cursor.execute(sql, params)
            result = cursor.fetchall()
            self.db_h.cursorclose(cursor)
            if not result:
                self.logger.info(f"Database query failed or empty: {result}")
                return 0

            self.logger.info(f"Database query response: {result}")

            max_service_index = 0
            found_any_instance = False

            # Regex pattern to match service index at the end of SPN name
            # Matches patterns like "us-east-service1", "us-west-service123", etc.

            service_index_pattern = re.compile(r'-service(\d+)$')
        
            for row in result:
                spn_name = row[0] if row and len(row) > 0 else ""
                trimmed_name = spn_name.strip() if spn_name else ""
            
                if not trimmed_name:
                    continue
            
                found_any_instance = True
            
                # Extract service index using regex
                match = service_index_pattern.search(trimmed_name)
                if match:
                    try:
                        service_index = int(match.group(1))
                        if service_index > max_service_index:
                            max_service_index = service_index
                    except ValueError:
                        self.logger.warn(f"Invalid service index in SPN name: {trimmed_name}")
                        continue
        
            # Return 1 if no instances exist or no valid service index found
            if not found_any_instance or max_service_index == 0:
                self.logger.info("No valid service index found, returning 1")
                return 0
        
            self.logger.info(f"Current max service index: {max_service_index}")
            return max_service_index
        
        except Exception as e:
            self.logger.error(f"Error getting current service index: {str(e)}")
            return 0

    def get_next_service_index_by_cust_id_and_region(self, cust_id, compute_region_idx, compute_region_name):
        """
        Returns the next available service index which is current max index + 1,
        or 1 if no instances exist, and forms the SPN name.
    
        Args:
            cust_id (int): Customer ID
            compute_region_idx (int): Compute region index
            compute_region_name (str): Compute region name
            logger: Logger instance (optional)
    
        Returns:
            str: SPN name formed as "compute_region_name-service{next_index}"
    
        Raises:
            Exception: If database query fails or invalid parameters
        """
        try:
            current_index = self.get_current_service_index_by_cust_id_and_region(
                cust_id, compute_region_idx)
            next_index = current_index + 1
            spn_name = f"{compute_region_name}-service{next_index}"
            self.logger.info(f"Next service index: {next_index}, SPN name: {spn_name}")
            return spn_name
        
        except Exception as e:
            self.logger.error(f"Error getting next service index: {str(e)}")
            return ""

def main():
    '''
    ret_code = delete_gp_gateway_edge_location_references_impl(db_h,
                                                               custid,
                                                               edge_loc_deleted_idx,
                                                               compute_idx,
                                                               None)
    if ret_code == False:
        self.logger.error("Failed to delete gp gateway edge location references for"
                           " Edge location: %s in Compute location %s" % (str(edge_loc_deleted_idx),
                                                                          str(compute_idx)))
    '''
    print("Creating the logger!!!")
    from libs.common.logger import logger
    from libs.db.dbhandle import DbHandle
    from libs.msg_defs.handler import GenericHandler
    from libs.cfg import cfg
    print("Creating the logger!!!")
    mylogger = logger("orchestrator_test.log", "/home/<USER>/orchestrator.tejas/")
    dbh = DbHandle(mylogger)
    orch_hndlr = OrchstrationHandler(cfg)
    orch_hndlr.db_h = dbh
    print((delete_gp_gateway_edge_location_references_impl(dbh, 1347, 224, 211, None)))
    print((delete_gp_gateway_edge_location_references_impl(dbh, 1347, 224, 211, None)))


"""
def nlb_ip_unit_test():
    from libs.common.logger import logger
    from libs.db.dbhandle import DbHandle
    mylogger = logger("nlb_ip_unit_test", "/var/log/pan/")
    dbh = DbHandle(mylogger)
    from libs.cloud_providers.common import ip_mgmt as IP_MGMT
    gcp_ip_mgmt = IP_MGMT.GCPIPHandler(dbh, cluster_id=str(10086), acct_id=str(570814374), node_id=12276)
    ip = gcp_ip_mgmt.get_nlb_ip()
    dbh.logger.info("nlb_ip: %s" % ip)
    c = IP_MGMT.release_all_nlb_ips(dbh, 570814374, 295)
    dbh.logger.info("%s nlb_ip relased" % c)
"""
