import ipaddress
import json
import traceback
import re
from typing import Dict, List, Tuple, Any, Optional, Set
from enum import Enum
from libs.common.shared.sys_utils import (NODE_TYPE_GP_GATEWAY, NODE_TYPE_SASE_PRIV_REGION_LB,
                                          NODE_TYPE_SASE_PRIV_REGION_AGENT, NODE_TYPE_REMOTE_NET,
                                          NODE_TYPE_GP_GATEWAY_NAME, NODE_TYPE_SASE_PRIV_REGION_LB_NAME,
                                          NODE_TYPE_SASE_PRIV_REGION_AGENT_NAME, NODE_TYPE_REMOTE_NET_NAME)
from libs.model.instancemodel import InstanceModel
from libs.model.privateRegionInstanceProfileModelOrch import privateRegionInstanceProfileModelOrch
from libs.cloud_providers.common.utils import send_sns_msg_for_spr_update
from orchestration_service.core.orchestrator_pvt_region_mgmt import update_private_instance_mgmt_entry

NODE_TYPE_NAME_MAPPING = {
    NODE_TYPE_GP_GATEWAY: NODE_TYPE_GP_GATEWAY_NAME,
    NODE_TYPE_SASE_PRIV_REGION_LB: NODE_TYPE_SASE_PRIV_REGION_LB_NAME,
    NODE_TYPE_SASE_PRIV_REGION_AGENT: NODE_TYPE_SASE_PRIV_REGION_AGENT_NAME,
    NODE_TYPE_REMOTE_NET: NODE_TYPE_REMOTE_NET_NAME
}


class ChangeType(Enum):
    """Enum to track different types of changes in instance configurations."""
    VIP_CHANGE = "vip_change"
    IP_CHANGE = "ip_change"
    GATEWAY_CHANGE = "gateway_change"
    NO_CHANGE = "no_change"
    MANAGEMENT_CHANGE = "management_change"
    MANAGEMENT_DNS_CHANGE = "management_dns_change"


class ProfileSyncManager:
    """
    Manager class to handle synchronization between instance profiles and instances.

    This class follows the initialization-is-configuration pattern where all required
    parameters are provided at initialization time.
    """

    def __init__(self, dbh, node_type, service_node_type, sase_private_region_id, custid):
        """
        Initialize the ProfileSyncManager with all required parameters.

        Args:
            dbh: Database handler with execute_query method and logger
            node_type: Node type ID (49 for mobile users gateway, 179 for SASE private region load balancer)
            service_node_type: Service node type ID
            sase_private_region_id: SASE private region ID
            custid: Customer ID
        """
        self.dbh = dbh
        self.logger = dbh.logger
        self.node_type = node_type
        self.service_node_type = service_node_type
        self.sase_private_region_id = sase_private_region_id
        self.custid = custid

        # Validate node type immediately during initialization
        if self.node_type not in [NODE_TYPE_GP_GATEWAY, NODE_TYPE_SASE_PRIV_REGION_LB, NODE_TYPE_REMOTE_NET,
                                  NODE_TYPE_SASE_PRIV_REGION_AGENT]:
            raise ValueError(f"Unsupported node_type: {self.node_type}. Supported types: 48, 49, 179, 180")

        # Initialize result structure for reuse
        self.result = self._create_empty_result()

    def _create_empty_result(self):
        """Create an empty result dictionary with the default structure."""
        return {
            "success": False,
            "changes": {
                "vip_changes": 0,
                "ip_changes": 0,
                "gateway_changes": 0,
                "management_changes": 0,
                "total_instances_updated": 0,
                "management_dns_changes": 0,
                "instances_with_changes": [],
                "failed_instances": []
            },
            "change_types": set(),
            "error": None,
            "partial_success": False,
            "node_type": self.node_type
        }

    def _parse_userdata_to_dict(self, userdata_str: str) -> Dict[str, str]:
        """
        Parse UserData string into a dictionary of key-value pairs.

        Args:
            userdata_str: Comma-separated string of key=value pairs

        Returns:
            Dictionary of key-value pairs
        """
        userdata_dict = {}
        if not userdata_str:
            return userdata_dict

        try:
            # Split by comma and parse each key=value pair
            pairs = userdata_str.split(', ')
            for pair in pairs:
                if '=' in pair:
                    key, value = pair.split('=', 1)  # Split only on first = to handle values with =
                    userdata_dict[key.strip()] = value.strip()
        except Exception as e:
            self.logger.error(f"Error parsing UserData string: {str(e)}\n{traceback.format_exc()}")

        return userdata_dict


    def _rebuild_userdata_string(self, userdata_dict: Dict[str, str]) -> str:
        """
        Rebuild UserData string from dictionary of key-value pairs.

        Args:
            userdata_dict: Dictionary of key-value pairs

        Returns:
            Comma-separated string of key=value pairs

        Raises:
            Exception: If there's an error rebuilding the string and userdata_dict is not empty
        """
        try:
            # Handle empty dictionary case - this is valid
            if not userdata_dict:
                return ""

            pairs = []
            for key, value in userdata_dict.items():
                # Validate key and value are strings
                if not isinstance(key, str) or not isinstance(value, str):
                    raise ValueError(
                        f"Invalid key-value pair: key='{key}' (type: {type(key)}), value='{value}' (type: {type(value)})")
                pairs.append(f"{key}={value}")

            result = ', '.join(pairs)
            self.logger.debug(f"Successfully rebuilt UserData string: {result}")
            return result

        except Exception as e:
            error_msg = f"Error rebuilding UserData string from dict {userdata_dict}: {str(e)}"
            self.logger.error(error_msg)
            # Re-raise the exception instead of returning empty string
            raise Exception(error_msg) from e

    def _cidr_to_ip_and_netmask(self, cidr_str: str) -> Tuple[str, str]:
        """
        Convert CIDR notation to IP address and netmask using ipaddress module.

        Args:
            cidr_str: IP address in CIDR notation (e.g., "************/24")
                     or plain IP address (e.g., "************")

        Returns:
            Tuple of (ip_address, netmask)
            - If input has no CIDR notation, returns (ip_address, "")
            - If input is invalid, returns (original_input, "")

        Examples:
            "************/24" -> ("************", "*************")
            "********/16" -> ("********", "***********")
            "************" -> ("************", "")
        """
        try:
            # Handle empty or None input
            if not cidr_str or not isinstance(cidr_str, str):
                self.logger.error(f"Invalid input: cidr_str must be a non-empty string, got: {cidr_str}")
                return str(cidr_str) if cidr_str else "", ""

            # Strip whitespace
            cidr_str = cidr_str.strip()

            # If no CIDR notation, return as-is with empty netmask
            if '/' not in cidr_str:
                # Validate it's still a valid IP address
                try:
                    ipaddress.IPv4Address(cidr_str)
                    return cidr_str, ""
                except ipaddress.AddressValueError:
                    self.logger.error(f"Invalid IP address format: {cidr_str}")
                    return cidr_str, ""

            # Parse CIDR notation using IPv4Interface
            interface = ipaddress.IPv4Interface(cidr_str)

            # Extract IP address and netmask as strings
            ip_address = str(interface.ip)
            netmask = str(interface.netmask)

            self.logger.info(f"Successfully converted CIDR {cidr_str} to IP: {ip_address}, Netmask: {netmask}")

            return ip_address, netmask

        except ipaddress.AddressValueError as e:
            # Specific error for invalid IP address format
            self.logger.error(f"Invalid IP address or CIDR format '{cidr_str}': {str(e)}")
            return cidr_str, ""

        except ipaddress.NetmaskValueError as e:
            # Specific error for invalid netmask/prefix
            self.logger.error(f"Invalid netmask or prefix length in '{cidr_str}': {str(e)}")
            return cidr_str, ""

        except ValueError as e:
            # General value error (catches other ipaddress-related errors)
            self.logger.error(f"Value error converting CIDR '{cidr_str}': {str(e)}")
            return cidr_str, ""

        except Exception as e:
            # Catch-all for any unexpected errors
            self.logger.error(f"Unexpected error converting CIDR '{cidr_str}' to IP and netmask: {str(e)}")
            return cidr_str, ""


    def _ip_and_netmask_to_cidr(self, ip_address: str, netmask: str) -> str:
        """
        Convert IP address and netmask to CIDR notation.

        Args:
            ip_address: IP address
            netmask: Netmask in dotted decimal notation

        Returns:
            IP address in CIDR notation
        """
        try:
            if not netmask:
                return ip_address

            # Convert netmask to prefix length
            mask_parts = netmask.split('.')
            if len(mask_parts) != 4:
                return ip_address

            mask_int = (int(mask_parts[0]) << 24) + (int(mask_parts[1]) << 16) + (int(mask_parts[2]) << 8) + int(
                mask_parts[3])
            prefix_len = bin(mask_int).count('1')

            return f"{ip_address}/{prefix_len}"
        except Exception as e:
            self.logger.error(f"Error converting IP {ip_address} and netmask {netmask} to CIDR: {str(e)}")
            return ip_address

    def _update_userdata_mgmt_settings(self, userdata_dict: Dict[str, str], network_config: Dict[str, Any]) -> bool:
        """
        Update management settings in UserData dictionary based on NetworkConfig.

        Args:
            userdata_dict: UserData dictionary to update
            network_config: NetworkConfig dictionary with current settings

        Returns:
            True if UserData was modified, False otherwise
        """
        userdata_changed = False

        try:
            # Get the allocation type from NetworkConfig
            alloc_type = network_config.get('mgmt.alloc_type')

            if not alloc_type:
                self.logger.error("Management allocation type not found in network configuration")
                return False

            # PRE-VALIDATION: If static allocation, validate all required fields first
            if alloc_type == 'static':
                mgmt_ip_cidr = network_config.get('mgmt.ip_address')
                if mgmt_ip_cidr:
                    ip, netmask = self._cidr_to_ip_and_netmask(mgmt_ip_cidr)

                    # Validate that IP parsing was successful
                    # _cidr_to_ip_and_netmask returns (original_input, "") on error
                    # So we need to check if we got a valid IP address back
                    if not ip or not self._is_valid_ip_address(ip):
                        self.logger.error(f"Failed to parse valid IP address from CIDR: {mgmt_ip_cidr}. "
                                          f"Parsed result: ip='{ip}', netmask='{netmask}'. "
                                          f"Aborting UserData update to avoid inconsistent state.")
                        return False

                    self.logger.info(
                        f"Successfully validated IP parsing: {mgmt_ip_cidr} -> ip='{ip}', netmask='{netmask}'")

            # Now proceed with updates since validation passed

            # Update allocation type
            current_alloc_type = userdata_dict.get('mgmt.alloc_type')
            if current_alloc_type != alloc_type:
                userdata_dict['mgmt.alloc_type'] = alloc_type
                userdata_changed = True
                self.logger.info(f"Updated mgmt.alloc_type: {current_alloc_type} -> {alloc_type}")

            if alloc_type == 'static':
                # Handle static configuration - add all mgmt settings

                # Update IP address and netmask (we already validated this above)
                mgmt_ip_cidr = network_config.get('mgmt.ip_address')
                if mgmt_ip_cidr:
                    ip, netmask = self._cidr_to_ip_and_netmask(mgmt_ip_cidr)
                    # We know ip is valid from pre-validation

                    current_ip = userdata_dict.get('mgmt.ip_address')
                    if current_ip != ip:
                        userdata_dict['mgmt.ip_address'] = ip
                        userdata_changed = True
                        self.logger.info(f"Updated mgmt.ip_address: {current_ip} -> {ip}")

                    # Only update netmask if it's not empty (CIDR notation was used)
                    if netmask:
                        current_netmask = userdata_dict.get('mgmt.netmask')
                        if current_netmask != netmask:
                            userdata_dict['mgmt.netmask'] = netmask
                            userdata_changed = True
                            self.logger.info(f"Updated mgmt.netmask: {current_netmask} -> {netmask}")
                    else:
                        # If no netmask returned, remove any existing netmask from UserData
                        # This handles the case where IP was provided without CIDR notation
                        if 'mgmt.netmask' in userdata_dict:
                            old_netmask = userdata_dict['mgmt.netmask']
                            del userdata_dict['mgmt.netmask']
                            userdata_changed = True
                            self.logger.info(
                                f"Removed mgmt.netmask (was: {old_netmask}) from UserData as IP address {mgmt_ip_cidr} has no CIDR notation")

                # Update gateway
                mgmt_gateway = network_config.get('mgmt.gateway')
                if mgmt_gateway:
                    current_gateway = userdata_dict.get('mgmt.gateway')
                    if current_gateway != mgmt_gateway:
                        userdata_dict['mgmt.gateway'] = mgmt_gateway
                        userdata_changed = True
                        self.logger.info(f"Updated mgmt.gateway: {current_gateway} -> {mgmt_gateway}")

                # Update DNS settings
                for dns_field in ['mgmt.dns.1', 'mgmt.dns.2']:
                    mgmt_dns = network_config.get(dns_field)
                    if mgmt_dns:
                        current_dns = userdata_dict.get(dns_field)
                        if current_dns != mgmt_dns:
                            userdata_dict[dns_field] = mgmt_dns
                            userdata_changed = True
                            self.logger.debug(f"Updated {dns_field}: {current_dns} -> {mgmt_dns}")

            elif alloc_type == 'dhcp':
                # Handle DHCP configuration - remove static settings
                static_keys_to_remove = ['mgmt.ip_address', 'mgmt.netmask', 'mgmt.gateway', 'mgmt.dns.1', 'mgmt.dns.2']

                for key in static_keys_to_remove:
                    if key in userdata_dict:
                        old_value = userdata_dict[key]
                        del userdata_dict[key]
                        userdata_changed = True
                        self.logger.debug(f"Removed {key} (was: {old_value}) for DHCP allocation")

            # Log summary of changes
            if userdata_changed:
                self.logger.info(f"UserData management settings updated successfully for allocation type: {alloc_type}")
            else:
                self.logger.debug(f"No changes needed for UserData management settings (allocation type: {alloc_type})")

        except Exception as e:
            self.logger.error(f"Error updating UserData mgmt settings: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False

        return userdata_changed

    def _is_valid_ip_address(self, ip_str: str) -> bool:
        """
        Validate if a string is a valid IPv4 address.

        Args:
            ip_str: String to validate as IP address

        Returns:
            True if valid IPv4 address, False otherwise
        """
        try:
            if not ip_str or not isinstance(ip_str, str):
                return False

            # Strip whitespace
            ip_str = ip_str.strip()

            # Use ipaddress module to validate
            ipaddress.IPv4Address(ip_str)
            return True

        except (ipaddress.AddressValueError, ValueError):
            return False
        except Exception as e:
            self.logger.debug(f"Unexpected error validating IP address '{ip_str}': {str(e)}")
            return False


    def sync_profile_to_instances(self):
        """
        Synchronize instance profile with corresponding instance masters.

        Returns:
            Dict containing status, changes made, and any errors
        """
        # Reset the result for this run
        self.result = self._create_empty_result()

        try:
            # Fetch the instance profile data
            profile = self._get_instance_profile()
            if not profile:
                self.result["error"] = (f"No profile found for node_type={self.node_type}, "
                                        f"service_node_type={self.service_node_type}, "
                                        f"sase_private_region_id={self.sase_private_region_id}, "
                                        f"custid={self.custid}")
                return self.result

            # Fetch all instances that match the profile criteria
            instances = self._get_instances()
            if not instances:
                self.result["error"] = (f"No instances found for node_type={self.node_type}, "
                                        f"compute_region_idx={self.sase_private_region_id}, "
                                        f"custid={self.custid}")
                return self.result

            # Process the synchronization
            changes, change_types = self._process_sync(profile, instances, self.node_type)

            # Copy the changes to the result
            self.result["changes"] = changes
            self.result["change_types"] = change_types

            # Determine success status based on failures
            if "global_error" in changes:
                self.result["error"] = changes["global_error"]
                # Even with a global error, we might have had partial success
                if changes["total_instances_updated"] > 0:
                    self.result["partial_success"] = True
                    self.result["success"] = False
            elif len(changes.get("failed_instances", [])) > 0:
                # Some instances failed, but not all
                if changes["total_instances_updated"] > 0:
                    self.result["partial_success"] = True
                    self.result["success"] = True  # Still consider it a success if some instances were updated
                    self.result["error"] = f"{len(changes['failed_instances'])} instances failed to sync"
                else:
                    # All instances failed
                    self.result["success"] = False
                    self.result["error"] = f"All {len(changes['failed_instances'])} instances failed to sync"
            else:
                # No failures
                self.result["success"] = True

            node_type_name = NODE_TYPE_NAME_MAPPING.get(self.node_type, f"Unknown Node Type ({self.node_type})")

            # Log appropriate message based on success status
            if self.result["success"] and not self.result["partial_success"]:
                self.logger.info(
                    f"Synchronization completed successfully for {node_type_name} (node_type={self.node_type}), "
                    f"service_node_type={self.service_node_type}, "
                    f"sase_private_region_id={self.sase_private_region_id}, "
                    f"custid={self.custid}. "
                    f"Changes: {changes['total_instances_updated']} instances updated.")
            elif self.result["partial_success"]:
                self.logger.warning(
                    f"Synchronization partially completed for {node_type_name} (node_type={self.node_type}), "
                    f"service_node_type={self.service_node_type}, "
                    f"sase_private_region_id={self.sase_private_region_id}, "
                    f"custid={self.custid}. "
                    f"Updated: {changes['total_instances_updated']}, "
                    f"Failed: {len(changes.get('failed_instances', []))}.")
            else:
                self.logger.error(f"Synchronization failed for {node_type_name} (node_type={self.node_type}), "
                                  f"service_node_type={self.service_node_type}, "
                                  f"sase_private_region_id={self.sase_private_region_id}, "
                                  f"custid={self.custid}. "
                                  f"Error: {self.result['error']}")

        except Exception as e:
            error_msg = f"Error in sync_profile_to_instances: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            self.result["error"] = error_msg

        return self.result

    def _get_instance_profile(self) -> Optional[Dict]:
        """
        Fetch instance profile from the database using PrivateRegionInstanceProfileModelOrch.

        Returns:
            Instance profile data as a dictionary or None if not found
        """
        try:
            # Create profile model instance with region-based lookup parameters
            priv_region_inst_prof_model = privateRegionInstanceProfileModelOrch(
                self.dbh,
                custid=self.custid,
                host_profile_id=None,  # We're using region-based lookup
                node_type=self.node_type,
                service_node_type=self.service_node_type,
                sase_private_region_id=self.sase_private_region_id
            )

            # Check if a valid entry was found
            if not priv_region_inst_prof_model.valid:
                err_msg = (f"Failed to find a valid entry {self.custid}/None/"
                           f"{self.node_type}/{self.service_node_type}/{self.sase_private_region_id} for the tuple provided")
                self.logger.error(err_msg)
                return None

            # Get the profile ID and instance details
            profile_id = priv_region_inst_prof_model.get_param("id")
            instance_details_json = priv_region_inst_prof_model.get_param("instance_details")

            # Parse the instance_details JSON
            instance_details = {}
            if instance_details_json:
                try:
                    # If it's already a dict, use it directly
                    if isinstance(instance_details_json, dict):
                        instance_details = instance_details_json
                    else:
                        # Otherwise, try to parse it as JSON
                        instance_details = json.loads(instance_details_json)
                except json.JSONDecodeError:
                    self.logger.error(f"Could not parse instance_details JSON for profile ID {profile_id}")
                    return None

            return {
                'id': profile_id,
                'instance_details': instance_details
            }

        except Exception as e:
            self.logger.error(f"Error fetching instance profile: {str(e)}")
            self.logger.error(traceback.format_exc())
            return None

    def _get_instances(self) -> List[Dict]:
        """
        Fetch instances from the database using InstanceModel.

        Returns:
            List of instance data dictionaries
        """
        try:
            # Query for instances matching our criteria directly with SQL
            # since InstanceModel may not have a class method for querying
            sql = """
                  SELECT id \
                  FROM instance_master
                  WHERE node_type = %s
                    AND compute_region_idx = %s
                    AND custid = %s \
                  """
            params = (self.node_type, self.sase_private_region_id, self.custid)

            cursor = None
            instance_ids = []
            failed_instances = []  # Track failed instances

            try:
                cursor = self.dbh.get_cursor()
                cursor.execute(sql, params)
                rows = cursor.fetchall()

                # Extract the IDs from the query results
                for row in rows:
                    instance_ids.append(row[0])
            except Exception as e:
                self.logger.error(f"Error querying instance IDs: {str(e)}")
                self.logger.error(traceback.format_exc())
                return []
            finally:
                if cursor:
                    self.dbh.cursorclose(cursor)

            if not instance_ids:
                return []

            instances = []
            for instance_id in instance_ids:
                try:
                    # Create instance model for each ID
                    instance_model = InstanceModel(instance_id, self.dbh)

                    # Get the alias for better error reporting
                    alias = instance_model.get_param("alias")

                    # Extract the needed fields
                    salt_profile_str = instance_model.get_param("salt_profile")

                    # Convert salt_profile from string to dict if needed
                    salt_profile = {}
                    if salt_profile_str:
                        if isinstance(salt_profile_str, dict):
                            salt_profile = salt_profile_str
                        else:
                            try:
                                salt_profile = json.loads(salt_profile_str)
                            except json.JSONDecodeError:
                                error_msg = f"Could not parse salt_profile for instance ID {instance_id} (alias: {alias})"
                                self.logger.error(error_msg)
                                failed_instances.append({"id": instance_id, "alias": alias, "error": error_msg})
                                continue  # Skip this instance and move to the next one

                    # Skip instances with empty salt_profile - this is critical data
                    if not salt_profile:
                        error_msg = f"Empty salt_profile for instance ID {instance_id} (alias: {alias})"
                        self.logger.error(error_msg)
                        failed_instances.append({"id": instance_id, "alias": alias, "error": error_msg})
                        continue

                    instance = {
                        'id': instance_id,
                        'alias': alias,
                        'lb_details': instance_model.get_param("lb_details"),
                        'pvt_ip': instance_model.get_param("pvt_ip"),
                        'salt_profile': salt_profile
                    }

                    instances.append(instance)
                except Exception as e:
                    error_msg = f"Error processing instance ID {instance_id}: {str(e)}"
                    self.logger.error(error_msg)
                    self.logger.error(traceback.format_exc())
                    failed_instances.append({"id": instance_id, "error": error_msg})

            # Log a summary of failed instances
            if failed_instances:
                self.logger.error(f"Failed to process {len(failed_instances)} out of {len(instance_ids)} instances")

            return instances
        except Exception as e:
            self.logger.error(f"Error fetching instances: {str(e)}")
            self.logger.error(traceback.format_exc())
            return []

    def _process_sync(self, profile: Dict, instances: List[Dict], node_type: int) -> Tuple[Dict[str, Any], Set[str]]:
        """
        Process synchronization between profile and instances.

        Args:
            profile: Instance profile data
            instances: List of instance data
            node_type: Node type ID

        Returns:
            Tuple of (changes_dict, change_types_set)
        """
        changes = {
            "vip_changes": 0,
            "ip_changes": 0,
            "gateway_changes": 0,
            "management_changes": 0,
            "management_dns_changes": 0,
            "total_instances_updated": 0,
            "instances_with_changes": [],
            "failed_instances": []  # Track instances that failed to sync
        }

        change_types = set()

        try:
            # Extract global VIP configuration
            global_config = profile.get('instance_details', {}).get('global', {})
            public_vip = global_config.get('public.vip_address')
            private_vip = global_config.get('private.vip_address')
            vip_string = f"{private_vip}:{public_vip}" if private_vip and public_vip else None

            # Map instances by name for easy lookup
            instance_configs = {}
            instance_list = profile.get('instance_details', {}).get('instances', [])

            for instance_data in instance_list:
                name = instance_data.get('instance_name')
                if name:
                    instance_configs[name] = instance_data

            # Get the appropriate network config key prefix based on node type
            network_config_prefix = "dataplane_outside"

            # Process each instance
            for instance in instances:
                try:
                    instance_id = instance['id']
                    instance_alias = instance['alias']

                    # Skip if we don't have this instance in the profile
                    if instance_alias not in instance_configs:
                        self.logger.error(f"Instance {instance_alias} (ID: {instance_id}) not found in profile")
                        continue

                    instance_config = instance_configs[instance_alias]

                    # Here is where we call _sync_instance
                    instance_changes = self._sync_instance(
                        instance_id,
                        instance,
                        instance_config,
                        vip_string,
                        public_vip,
                        private_vip,
                        network_config_prefix,
                        node_type
                    )

                    if instance_changes["change_detected"]:
                        changes["total_instances_updated"] += 1
                        changes["vip_changes"] += 1 if instance_changes["vip_change"] else 0
                        changes["ip_changes"] += 1 if instance_changes["ip_change"] else 0
                        changes["gateway_changes"] += 1 if instance_changes["gateway_change"] else 0
                        changes["management_changes"] += 1 if instance_changes["management_change"] else 0
                        changes["management_dns_changes"] += 1 if instance_changes["management_dns_change"] else 0

                        changes["instances_with_changes"].append({
                            "id": instance_id,
                            "alias": instance_alias,
                            "changes": instance_changes
                        })

                        # Update the change types set
                        if instance_changes["vip_change"]:
                            change_types.add(ChangeType.VIP_CHANGE.value)
                        if instance_changes["ip_change"]:
                            change_types.add(ChangeType.IP_CHANGE.value)
                        if instance_changes["gateway_change"]:
                            change_types.add(ChangeType.GATEWAY_CHANGE.value)
                        if instance_changes["management_change"]:
                            change_types.add(ChangeType.MANAGEMENT_CHANGE.value)
                        if instance_changes["management_dns_change"]:
                            change_types.add(ChangeType.MANAGEMENT_DNS_CHANGE.value)

                        if not (instance_changes["management_change"] or instance_changes["management_dns_change"]):

                            send_sns_msg_for_spr_update(instance_id, self.custid, self.logger)
                            update_private_instance_mgmt_entry(dbh=self.dbh,
                                                               inst_id=instance_id, svc_node_type=node_type)

                    # If there's an error in the instance sync, record it
                    if instance_changes.get("error"):
                        changes["failed_instances"].append({
                            "id": instance_id,
                            "alias": instance_alias,
                            "error": instance_changes["error"]
                        })
                        self.logger.error(f"Error syncing instance {instance_alias} (ID: {instance_id}): "
                                          f"{instance_changes['error']}")

                except Exception as e:
                    # Log the error but continue processing other instances
                    error_msg = f"Exception processing instance {instance.get('alias', 'unknown')} (ID: {instance.get('id', 'unknown')}): {str(e)}"
                    self.logger.error(error_msg)
                    self.logger.error(traceback.format_exc())

                    changes["failed_instances"].append({
                        "id": instance.get('id', 'unknown'),
                        "alias": instance.get('alias', 'unknown'),
                        "error": error_msg
                    })

        except Exception as e:
            # Log the error but return what we've processed so far
            error_msg = f"Exception in _process_sync: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            changes["global_error"] = error_msg

        # Include summary of failed instances in the logs
        if changes.get("failed_instances"):
            self.logger.warning(f"Failed to sync {len(changes['failed_instances'])} instances. "
                                f"See logs for details.")

        return changes, change_types

    def _sync_instance(self, instance_id: int, instance_data: Dict, instance_config: Dict,
                       vip_string: Optional[str], public_vip: Optional[str],
                       private_vip: Optional[str], network_config_prefix: str,
                       node_type: int) -> Dict[str, Any]:
        """
        Synchronize a single instance with its configuration using InstanceModel.

        Args:
            instance_id: Instance ID in the database
            instance_data: Instance data extracted from InstanceModel
            instance_config: Configuration from instance_details
            vip_string: Combined VIP string in format "{private_vip}:{public_vip}"
            public_vip: Public VIP address
            private_vip: Private VIP address
            network_config_prefix: Prefix for NetworkConfig keys (should be "dataplane_outside")
            node_type: Node type ID

        Returns:
            Dictionary with information about changes made
        """
        changes = {
            "change_detected": False,
            "vip_change": False,
            "ip_change": False,
            "gateway_change": False,
            "management_change": False,
            "management_dns_change": False,
            "userdata_change": False,  # New field to track UserData changes
            "model_updated": False,
            "error": None,
            "node_type": node_type
        }

        try:
            # Get the instance model
            instance_model = InstanceModel(instance_id, self.dbh)
            instance_alias = instance_data['alias']

            node_type_name = NODE_TYPE_NAME_MAPPING.get(self.node_type, f"Unknown Node Type ({self.node_type})")

            # Get the current salt profile
            current_salt_profile = instance_data.get('salt_profile')
            if not current_salt_profile:
                error_msg = f"Instance {instance_alias} (ID: {instance_id}) is missing salt_profile"
                self.logger.error(error_msg)
                changes["error"] = error_msg
                return changes

            if not isinstance(current_salt_profile, dict):
                error_msg = f"Instance {instance_alias} (ID: {instance_id}) has invalid salt_profile (not a dictionary)"
                self.logger.error(error_msg)
                changes["error"] = error_msg
                return changes

            # Create a deep copy of the salt profile to modify
            salt_profile = dict(current_salt_profile)

            # Check if NetworkConfig exists - this is required
            if 'NetworkConfig' not in salt_profile:
                error_msg = f"Instance {instance_alias} (ID: {instance_id}) is missing NetworkConfig in salt_profile"
                self.logger.error(error_msg)
                changes["error"] = error_msg
                return changes

            if not isinstance(salt_profile['NetworkConfig'], dict):
                error_msg = f"Instance {instance_alias} (ID: {instance_id}) has invalid NetworkConfig in salt_profile (not a dictionary)"
                self.logger.error(error_msg)
                changes["error"] = error_msg
                return changes

            network_config = salt_profile['NetworkConfig']
            salt_profile_changed = False

            # Check for VIP changes
            try:
                if vip_string and instance_data.get('lb_details') != vip_string:
                    self.logger.info(f"VIP change for {node_type_name} instance {instance_alias} (ID: {instance_id}): "
                                     f"{instance_data.get('lb_details')} -> {vip_string}")

                    # Update lb_details in instance model
                    instance_model.set_param("lb_details", vip_string)
                    changes["vip_change"] = True
                    changes["change_detected"] = True

                    # Update VIPs in network_config using correct keys
                    network_config[f'{network_config_prefix}.public.vip_address'] = public_vip
                    network_config[f'{network_config_prefix}.private.vip_address'] = private_vip
                    salt_profile_changed = True
            except Exception as e:
                error_msg = f"Error processing VIP change for instance {instance_alias} (ID: {instance_id}): {str(e)}"
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
                changes["error"] = error_msg
                return changes

            # Get interface configurations
            dp_interface = None
            mgmt_interface = None
            try:
                for interface in instance_config.get('interfaces', []):
                    if interface.get('type') == 'dataplane_outside_network':
                        dp_interface = interface
                    elif interface.get('type') == 'management_network':
                        mgmt_interface = interface

                if not dp_interface:
                    self.logger.error(
                        f"No dataplane_outside_network interface found for instance {instance_alias} (ID: {instance_id})")

                if not mgmt_interface:
                    self.logger.error(
                        f"No management_network interface found for instance {instance_alias} (ID: {instance_id})")
            except Exception as e:
                error_msg = f"Error finding interfaces for instance {instance_alias} (ID: {instance_id}): {str(e)}"
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
                changes["error"] = error_msg
                return changes

            # Check for dataplane IP changes
            try:
                if dp_interface and 'ip-address' in dp_interface:
                    profile_ip = dp_interface['ip-address']
                    current_ip = network_config.get(f'{network_config_prefix}.ip_address')

                    if profile_ip != current_ip:
                        self.logger.info(
                            f"Dataplane IP change for {node_type_name} instance {instance_alias} (ID: {instance_id}): "
                            f"{current_ip} -> {profile_ip}")

                        # Update IP in network_config
                        network_config[f'{network_config_prefix}.ip_address'] = profile_ip
                        salt_profile_changed = True

                        # Update pvt_ip (without subnet mask)
                        try:
                            ip_without_mask = profile_ip.split('/')[0]
                            instance_model.set_param("pvt_ip", ip_without_mask)
                        except Exception as e:
                            error_msg = f"Error parsing IP address {profile_ip} for instance {instance_alias} (ID: {instance_id}): {str(e)}"
                            self.logger.error(error_msg)
                            self.logger.error(traceback.format_exc())
                            changes["error"] = error_msg
                            return changes

                        changes["ip_change"] = True
                        changes["change_detected"] = True
            except Exception as e:
                error_msg = f"Error processing dataplane IP change for instance {instance_alias} (ID: {instance_id}): {str(e)}"
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
                changes["error"] = error_msg
                return changes

            # Check for dataplane gateway changes
            try:
                if dp_interface and 'default-gateway' in dp_interface:
                    profile_gw = dp_interface['default-gateway']
                    current_gw = network_config.get(f'{network_config_prefix}.gateway')

                    if profile_gw != current_gw:
                        self.logger.info(
                            f"Dataplane gateway change for {node_type_name} instance {instance_alias} (ID: {instance_id}): "
                            f"{current_gw} -> {profile_gw}")

                        # Update gateway in network_config
                        network_config[f'{network_config_prefix}.gateway'] = profile_gw
                        salt_profile_changed = True

                        changes["gateway_change"] = True
                        changes["change_detected"] = True
            except Exception as e:
                error_msg = f"Error processing dataplane gateway change for instance {instance_alias} (ID: {instance_id}): {str(e)}"
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
                changes["error"] = error_msg
                return changes

            # Check for management network changes using correct NetworkConfig keys
            try:
                if mgmt_interface:
                    profile_mgmt_alloc_type = mgmt_interface.get('ip-assignment-type')
                    current_mgmt_alloc_type = network_config.get('mgmt.alloc_type')

                    self.logger.info(
                        f"Management network check for {node_type_name} instance {instance_alias} (ID: {instance_id}): "
                        f"Profile allocation type: {profile_mgmt_alloc_type}, Current allocation type: {current_mgmt_alloc_type}")

                    # Case 1: Allocation type change (dhcp -> static OR static -> dhcp)
                    if profile_mgmt_alloc_type != current_mgmt_alloc_type:
                        self.logger.info(
                            f"Management allocation type change for {node_type_name} instance {instance_alias} (ID: {instance_id}): "
                            f"{current_mgmt_alloc_type} -> {profile_mgmt_alloc_type}")

                        # Update allocation type
                        network_config['mgmt.alloc_type'] = profile_mgmt_alloc_type
                        salt_profile_changed = True
                        changes["management_change"] = True
                        changes["change_detected"] = True

                    # Handle configuration based on the target allocation type
                    if profile_mgmt_alloc_type == 'static':
                        # Case 1a: dhcp -> static OR Case 3: static -> static (with possible IP/GW changes)

                        # Check and update management IP address
                        if 'ip-address' in mgmt_interface:
                            profile_mgmt_ip = mgmt_interface['ip-address']
                            current_mgmt_ip = network_config.get('mgmt.ip_address')

                            if profile_mgmt_ip != current_mgmt_ip:
                                self.logger.info(
                                    f"Management IP change for {node_type_name} instance {instance_alias} (ID: {instance_id}): "
                                    f"{current_mgmt_ip} -> {profile_mgmt_ip}")

                                network_config['mgmt.ip_address'] = profile_mgmt_ip
                                salt_profile_changed = True
                                changes["management_change"] = True
                                changes["change_detected"] = True
                        else:
                            # Profile specifies static but no IP address - this is an error
                            error_msg = f"Profile specifies static management allocation but no ip-address for instance {instance_alias} (ID: {instance_id})"
                            self.logger.error(error_msg)
                            changes["error"] = error_msg
                            return changes

                        try:
                            mgmt_ip_without_mask = profile_mgmt_ip.split('/')[0]
                            instance_model.set_param("mgt_ip", mgmt_ip_without_mask)
                            self.logger.info(
                                f"Updated mgt_ip in instance_master for {node_type_name} instance {instance_alias} (ID: {instance_id}): "
                                f"{mgmt_ip_without_mask}")
                        except Exception as e:
                            error_msg = f"Error parsing management IP address {profile_mgmt_ip} for instance {instance_alias} (ID: {instance_id}): {str(e)}"
                            self.logger.error(error_msg)
                            self.logger.error(traceback.format_exc())
                            changes["error"] = error_msg
                            return changes

                        # Check and update management gateway
                        if 'default-gateway' in mgmt_interface:
                            profile_mgmt_gw = mgmt_interface['default-gateway']
                            current_mgmt_gw = network_config.get('mgmt.gateway')

                            if profile_mgmt_gw != current_mgmt_gw:
                                self.logger.info(
                                    f"Management gateway change for {node_type_name} instance {instance_alias} (ID: {instance_id}): "
                                    f"{current_mgmt_gw} -> {profile_mgmt_gw}")

                                network_config['mgmt.gateway'] = profile_mgmt_gw
                                salt_profile_changed = True
                                changes["management_change"] = True
                                changes["change_detected"] = True
                        else:
                            # Profile specifies static but no gateway - this is an error
                            error_msg = f"Profile specifies static management allocation but no default-gateway for instance {instance_alias} (ID: {instance_id})"
                            self.logger.error(error_msg)
                            changes["error"] = error_msg
                            return changes

                        # Check and update DNS settings
                        dns_fields = ['dns.1', 'dns.2']
                        for dns_field in dns_fields:
                            profile_dns = mgmt_interface.get(dns_field)
                            current_dns = network_config.get(f'mgmt.{dns_field}')

                            if profile_dns is not None:
                                # Profile has DNS setting - check if it needs to be updated
                                if profile_dns != current_dns:
                                    self.logger.info(
                                        f"Management DNS change for {node_type_name} instance {instance_alias} (ID: {instance_id}): "
                                        f"mgmt.{dns_field}: {current_dns} -> {profile_dns}")

                                    network_config[f'mgmt.{dns_field}'] = profile_dns
                                    salt_profile_changed = True
                                    changes["management_dns_change"] = True
                                    changes["change_detected"] = True

                    elif profile_mgmt_alloc_type == 'dhcp':
                        # Case 1b: static -> dhcp
                        # Remove static configuration settings if they exist
                        static_keys_to_remove = ['mgmt.ip_address', 'mgmt.gateway', 'mgmt.dns.1', 'mgmt.dns.2']
                        for key in static_keys_to_remove:
                            if key in network_config:
                                self.logger.info(
                                    f"Removing static management setting {key} for {node_type_name} instance {instance_alias} (ID: {instance_id}) - switching to DHCP")
                                del network_config[key]
                                salt_profile_changed = True
                                changes["management_change"] = True
                                changes["change_detected"] = True

                        current_mgt_ip = instance_model.get_param("mgt_ip")
                        if current_mgt_ip and current_mgmt_alloc_type == 'static':
                            instance_model.set_param("mgt_ip", None)
                            self.logger.info(
                                f"Cleared mgt_ip in instance_master for {node_type_name} instance {instance_alias} (ID: {instance_id}) - switching to DHCP")

                        # Verify no static settings remain in DHCP mode
                        remaining_static_keys = [key for key in static_keys_to_remove if key in network_config]
                        if remaining_static_keys:
                            self.logger.error(
                                f"Static management settings still present after DHCP switch for instance {instance_alias} (ID: {instance_id}): {remaining_static_keys}")

                    else:
                        # Invalid allocation type
                        error_msg = f"Invalid ip-assignment-type '{profile_mgmt_alloc_type}' for management interface for instance {instance_alias} (ID: {instance_id}). Must be 'static' or 'dhcp'"
                        self.logger.error(error_msg)
                        changes["error"] = error_msg
                        return changes

                    # Validation: Ensure consistency after changes
                    final_alloc_type = network_config.get('mgmt.alloc_type')
                    if final_alloc_type == 'static':
                        # Verify static configuration is complete
                        if 'mgmt.ip_address' not in network_config or 'mgmt.gateway' not in network_config:
                            error_msg = f"Incomplete static management configuration for instance {instance_alias} (ID: {instance_id}). Missing IP or gateway"
                            self.logger.error(error_msg)
                            changes["error"] = error_msg
                            return changes
                    elif final_alloc_type == 'dhcp':
                        # Verify no static configuration remains
                        static_keys_present = [key for key in
                                               ['mgmt.ip_address', 'mgmt.gateway', 'mgmt.dns.1', 'mgmt.dns.2'] if
                                               key in network_config]
                        if static_keys_present:
                            self.logger.error(
                                f"Static management configuration present in DHCP mode for instance {instance_alias} (ID: {instance_id}): {static_keys_present}")

            except Exception as e:
                error_msg = f"Error processing management network changes for instance {instance_alias} (ID: {instance_id}): {str(e)}"
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
                changes["error"] = error_msg
                return changes

            # Synchronize UserData with NetworkConfig changes
            try:
                if salt_profile_changed and 'UserData' in salt_profile:
                    current_userdata = salt_profile.get('UserData', '')

                    # Parse current UserData into dictionary
                    userdata_dict = self._parse_userdata_to_dict(current_userdata)

                    # Update UserData mgmt settings based on NetworkConfig changes
                    userdata_mgmt_changed = self._update_userdata_mgmt_settings(userdata_dict, network_config)

                    if userdata_mgmt_changed:
                        # Rebuild UserData string and update salt_profile
                        new_userdata = self._rebuild_userdata_string(userdata_dict)
                        salt_profile['UserData'] = new_userdata
                        changes["userdata_change"] = True

                        self.logger.info(
                            f"UserData updated for {node_type_name} instance {instance_alias} (ID: {instance_id}) to sync with NetworkConfig")

                        # Log the specific changes for debugging
                        self.logger.debug(
                            f"UserData change details for instance {instance_alias} (ID: {instance_id}):\n"
                            f"  Original: {current_userdata[:200]}...\n"
                            f"  Updated:  {new_userdata[:200]}...")

            except Exception as e:
                error_msg = f"Error synchronizing UserData for instance {instance_alias} (ID: {instance_id}): {str(e)}"
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
                changes["error"] = error_msg
                return changes

            # Update salt_profile in instance model if changed
            try:
                if salt_profile_changed:
                    salt_profile_json = json.dumps(salt_profile)
                    instance_model.set_param("salt_profile", salt_profile_json)
            except Exception as e:
                error_msg = f"Error serializing salt profile for instance {instance_alias} (ID: {instance_id}): {str(e)}"
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
                changes["error"] = error_msg
                return changes

            # Save changes to the database if any were made
            if changes["change_detected"]:
                try:
                    self.logger.info(
                        f"Saving updates for {node_type_name} instance {instance_alias} (ID: {instance_id})")

                    # Save the instance model to persist changes
                    instance_model.save()
                    changes["model_updated"] = True

                    # Log summary of what was changed
                    change_summary = []
                    if changes["vip_change"]:
                        change_summary.append("VIP")
                    if changes["ip_change"]:
                        change_summary.append("IP")
                    if changes["gateway_change"]:
                        change_summary.append("Gateway")
                    if changes["management_change"]:
                        change_summary.append("Management")
                    if changes["management_dns_change"]:
                        change_summary.append("Management DNS")
                    if changes["userdata_change"]:
                        change_summary.append("UserData")

                    self.logger.info(
                        f"Update successful for {node_type_name} instance {instance_alias} (ID: {instance_id}). "
                        f"Changes: {', '.join(change_summary)}")

                except Exception as e:
                    error_msg = f"Error saving updates for {node_type_name} instance {instance_alias} (ID: {instance_id}): {str(e)}"
                    self.logger.error(error_msg)
                    self.logger.error(traceback.format_exc())
                    changes["error"] = error_msg
                    return changes

        except Exception as e:
            # Handle general exception
            error_msg = f"Unexpected error in _sync_instance for instance ID {instance_id}: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            changes["error"] = error_msg

        return changes
