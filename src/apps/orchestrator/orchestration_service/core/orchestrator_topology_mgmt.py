from libs.model.execute_orch_query import execute_orch_query

def get_count_of_topology_instances_not_marked_for_deletion_in_region(dbh, custid, region_id, node_type):
    """
    Counts all the topology instances of a specified node type for a customer in a region that are not marked for deletion.
    If successful, returns TRUE and the count.
    On failure, return False and 0.
    """

    logger = dbh.logger
    count = 0
    sql = ("SELECT count(*) FROM cust_topology t JOIN instance_master i ON i.id = t.instance1_id AND i.compute_region_idx = %s "
           "WHERE t.custid = %s AND t.node_type = %s AND t.is_deleted = 0 ")
    params = (region_id, custid, node_type, )
    logger.info(f"SQL: [{(sql % params)}]")
    ret, result = execute_orch_query(dbh, sql, params, "fetchone")
    logger.info(f"Return from RDS: {ret}: {result}")
    if ret is False or result is None or len(result) == 0:
        logger.info(f"Failed to get a count of the instances of type {node_type} for custid {custid} in region {region_id}: {ret}: {result}")
        return (False, count)
    count = result[0]
    logger.info(f"Successfully retrieved count ({count}) of instances of type {node_type} for custid {custid} in region {region_id} that are not marked for deletion")
    return (True, count)
