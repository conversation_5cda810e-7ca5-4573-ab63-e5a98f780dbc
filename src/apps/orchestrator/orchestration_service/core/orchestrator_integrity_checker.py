import traceback
import time

from libs.model.execute_orch_query import *
from libs.model.custnodemodel import CustNodeModel
from libs.model.instancemodel import InstanceModel
from libs.model.pinnedInstanceUpgradeModel import PinnedInstanceUpgradeModel

from libs.common.shared.sys_utils import (NODE_TYPE_REMOTE_NET, NODE_TYPE_SWG_PROXY, NODE_TYPE_GP_GATEWAY,\
                                         NODE_TYPE_GP_PORTAL, NODE_TYPE_SERVICE_CONN, NODE_TYPE_NAT_INSTANCE,
                                          NODE_TYPE_SERVICE_ILB)


'''
select id, name, is_deleted, update_time from cust_topology where is_deleted = 1 
'''
def check_and_delete_undeleted_cust_topology_entries(OrchstrationHandlerReference):
    """
    This function checks for entries in the 'cust_topology' table that are marked as deleted
    (is_deleted = 1) but have not been deleted from the database. It attempts to
    delete these entries if certain conditions are met.

    Parameters:
    OrchstrationHandlerReference: An object that provides access to the database handler (dbh),
    logger, and other context needed for the operation.

    Returns:
    success (bool): Indicates whether the operation was successful.
    """

    dbh = OrchstrationHandlerReference.db_h
    logger = dbh.logger
    polling_interval_hours = 2
    desired_completion_hours = 12
    max_entries_per_interval = 25
    avctx = OrchstrationHandlerReference.avctx
    has_error = False
    try:
        
        # Query to get the total number of entries
        count_sql = """
            SELECT COUNT(*) 
            FROM cust_topology 
            WHERE is_deleted = 1
            AND update_time < (NOW() - INTERVAL 6 HOUR)
        """
        params = None
        ret, count_res = execute_orch_query(dbh, count_sql, params, "fetchone")
        if ret == False or not count_res:
            error = "!!! Fatal! Failed to execute count query. Return status %s/%s !!!" % \
                    (str(ret), str(count_res))
            raise Exception(error)
        
        total_entries = count_res[0]
        logger.info(f"Total undeleted entries: {total_entries}")

        # If there are no entries, return early
        if total_entries == 0:
            logger.info("No undeleted entries found. Exiting function.")
            return

        # Calculate the limit based on the total entries and desired completion time
        limit = total_entries // (desired_completion_hours // polling_interval_hours)
        if limit > max_entries_per_interval:
            limit = max_entries_per_interval
        else:
            limit = total_entries

        logger.info(f"Calculated limit for this run: {limit}")
        #remove create_time and replace it with update_time for further limiting the possiblity of race conditions in multiple threads
        #and race condition with autoscale delete triggers, not checking for update_time results in failure to TF call 
        #alternatively we can check mark_delete in instance_master instead of update_time but that needs instance_id rather 
        #topology id, so going with update_time for now
        sql = f"""
            SELECT 
                id, 
                name, 
                is_deleted, 
                update_time 
            FROM 
                cust_topology 
            WHERE 
                is_deleted = 1
                AND update_time < (NOW() - INTERVAL 6 HOUR)
            LIMIT {limit}
        """
        params = None
        ret, res = execute_orch_query(dbh, sql, params, "fetchall")
        if ret == False:
            error = "!!! Fatal! Failed to execute query or no entries found. Return status %s/%s !!!" % \
                    (str(ret), str(res))
            raise Exception(error)
        logger.info("Result: %s" % str(res))
        for row in res:
            if isinstance(row, tuple):
                instance1_id_exists = False
                instance2_id_exists = False
                instance1_id_transient_exists = False
                instance2_id_transient_exists = False

                id, name, is_deleted, update_time = row
                logger.info("Found undeleted entry in cust topology %s, name %s and update time %s"  % \
                                                             (str(id),
                                                              str(name),
                                                              str(update_time)))
                # update the is_deleted column to 1
                cust_node_model = CustNodeModel(iid=id, dbh=dbh)
                if cust_node_model.id != id:
                    logger.error("check_and_delete_undeleted_cust_topology_entries: "
                                 "Failed to lookup cust topology entry with id = %s" % str(id))
                    continue

                if cust_node_model.instance1_id == 0 and cust_node_model.instance2_id == 0:
                    # delete the cust model entry directly.
                    logger.info("Deleting cust node model entry since there is no instance "
                                     "associated and is_deleted is set to 1")
                    if cust_node_model.delete(dbh) == False:
                        logger.error(f"Failed to delete cust node model entry id "
                                          f"{cust_node_model.id}")
                        continue

                if cust_node_model.instance1_id and cust_node_model.instance1_id > 0:
                    # Check if the instance master entry is present.
                    instance_model = InstanceModel(dbh=dbh,
                                                   iid=cust_node_model.instance1_id)
                    if instance_model.get_param("id") == None:
                        logger.error(f"No entry with id {cust_node_model.instance1_id} in instance master exists")
                        instance1_id_exists = False
                        cust_node_model.instance1_id = 0
                    else:
                        instance1_id_exists = True

                if cust_node_model.instance2_id and cust_node_model.instance2_id > 0:
                    # Check if the instance master entry is present.
                    instance_model = InstanceModel(dbh=dbh,
                                                   iid=cust_node_model.instance2_id)
                    if instance_model.get_param("id") == None:
                        logger.error(f"No entry with id {cust_node_model.instance2_id} in instance master exists")
                        instance2_id_exists = False
                        cust_node_model.instance2_id = 0
                    else:
                        instance2_id_exists = True

                if cust_node_model.instance1_transient and cust_node_model.instance1_transient > 0:
                    # Check if the instance master entry is present.
                    instance_model = InstanceModel(dbh=dbh,
                                                   iid=cust_node_model.instance1_transient)
                    if instance_model.get_param("id") == None:
                        logger.error(f"No entry with id {cust_node_model.instance1_transient} in "
                                     f"instance masrer exists")
                        instance1_id_transient_exists = False
                        cust_node_model.instance1_transient = 0
                    else:
                        instance1_id_transient_exists = True

                if cust_node_model.instance2_transient and cust_node_model.instance2_transient > 0:
                    # Check if the instance master entry is present.
                    instance_model = InstanceModel(dbh=dbh,
                                                   iid=cust_node_model.instance2_transient)
                    if instance_model.get_param("id") == None:
                        logger.error(f"No entry with id {cust_node_model.instance2_transient} in "
                                     f"instance master exists")
                        instance2_id_transient_exists = False
                        cust_node_model.instance2_transient = 0
                    else:
                        instance2_id_transient_exists = True

                # Depending on how many instances are existing, update the cust topology accordingly.
                num_allocs = (1 if instance1_id_exists else 0) + \
                             (1 if instance2_id_exists else 0) + \
                             (1 if instance1_id_transient_exists else 0) + \
                             (1 if instance2_id_transient_exists else 0)
                cust_node_model.num_allocs = num_allocs

                cust_node_model.is_deleted = 0
                logger.info(f"Setting is_deleted to 0 for id {cust_node_model.id}")
                result = cust_node_model.save(dbh, update_instance_params=True)
                if result == False:
                    logger.error("Failed to set is_deleted = 0 for cust topology with id = %s" % str(id))
                    has_error=True
                    continue
                else:
                    logger.info(f"Successfully set is_deleted=0 for cust topology with id {id}")

                cust_node_model.is_deleted = 1
                result = cust_node_model.save(dbh)
                if result == False:
                    logger.error("Failed to delete cust topology with id = %s" % str(id))
                    has_error=True
                    continue
                else:
                    logger.info(f"Successfully set is_deleted=1 for cust topology with id {id}")

    except Exception as E:
        logger.error(f"Exception {E.args} occurred while checking for undeleted cust topology entries:"
                     f" {traceback.format_exc()}")
        logger.error(f"locals: {locals()}")
        has_error = True
    finally:
        return False if has_error else True

'''
select id, name, mark_delete, node_type, create_time, update_time from instance_master where id not in
(select instance1_id AS inst_id from cust_topology UNION select instance2_id AS inst_id from cust_topology 
 UNION 
select instance1_transient AS inst_id from cust_topology 
 UNION 
select instance2_transient AS inst_id from cust_topology) 
and node_type in (48, 49, 50, 51, 153, 156) and is_sase_fabric_spn = 0 and mcw_enabled = 0
and create_time < (NOW() - INTERVAL 1 DAY);
'''

def get_undeleted_instances(OrchstrationHandlerReference):
    """
    This function identifies and deletes instances from the 'instance_master' table that are not
    referenced in the 'cust_topology' table and meet certain conditions. It ensures that instances
    which should be marked for deletion are actually removed from the database.

    Parameters:
    OrchstrationHandlerReference: An object that provides access to the database handler (dbh),
    logger, and other context needed for the operation.

    Returns:
    success (bool): Indicates whether the operation was successful.
    """
    dbh = OrchstrationHandlerReference.db_h
    logger = dbh.logger
    avctx = OrchstrationHandlerReference.avctx
    has_error = False
    polling_interval_hours = 2
    desired_completion_hours = 12
    max_entries_per_interval = 25
    try:
        # Configurable parameters
        node_types = (NODE_TYPE_REMOTE_NET, NODE_TYPE_SERVICE_CONN, NODE_TYPE_GP_GATEWAY, NODE_TYPE_GP_PORTAL,
                      NODE_TYPE_SWG_PROXY, NODE_TYPE_SERVICE_ILB)  # Tuple of node types to filter
        interval_days = 1  # Interval in days

        # Query to get the total number of entries
        count_sql = f"""
            SELECT COUNT(*)
            FROM instance_master im
            WHERE id NOT IN (
                SELECT instance1_id AS inst_id FROM cust_topology
                UNION
                SELECT instance2_id AS inst_id FROM cust_topology
                UNION
                SELECT instance1_transient AS inst_id FROM cust_topology
                UNION
                SELECT instance2_transient AS inst_id FROM cust_topology
            )
            AND node_type IN {node_types}
            AND is_sase_fabric_spn = 0
            AND mcw_enabled = 0
            AND create_time < (NOW() - INTERVAL {interval_days} DAY)
            AND custid NOT IN (
                SELECT cec.custid FROM cust_epaas_config cec WHERE 
                cec.migrate_ep_status IN ('UPGRADE_IN_PROGRESS', 'DONE_MIGRATION', 'IN_PROGRESS') 
                AND cec.node_type = im.node_type AND cec.compute_region_id = im.compute_region_idx
            )
        """
        params = None
        ret, count_res = execute_orch_query(dbh, count_sql, params, "fetchone")
        if ret == False or not count_res:
            error = "!!! Fatal! Failed to execute count query. Return status %s/%s !!!" % \
                    (str(ret), str(count_res))
            raise Exception(error)

        total_entries = count_res[0]
        logger.info(f"Total undeleted instances: {total_entries}")

        # If there are no entries, return early
        if total_entries == 0:
            logger.info("No undeleted instances found. Exiting function.")
            return

        # Calculate the limit based on the total entries and desired completion time
        limit = total_entries // (desired_completion_hours // polling_interval_hours)
        if limit > max_entries_per_interval:
            limit = max_entries_per_interval
        else:
            limit = total_entries

        # Constructing the SQL query
        sql = f"""
            SELECT 
                id, 
                name, 
                mark_delete, 
                node_type, 
                create_time, 
                update_time 
            FROM 
                instance_master im
            WHERE 
                id NOT IN (
                    SELECT instance1_id AS inst_id FROM cust_topology
                    UNION
                    SELECT instance2_id AS inst_id FROM cust_topology
                    UNION
                    SELECT instance1_transient AS inst_id FROM cust_topology
                    UNION
                    SELECT instance2_transient AS inst_id FROM cust_topology
                )
                AND node_type IN {node_types}
                AND is_sase_fabric_spn = 0
                AND mcw_enabled = 0
                AND create_time < (NOW() - INTERVAL {interval_days} DAY) 
                AND custid NOT IN (
                SELECT cec.custid FROM cust_epaas_config cec WHERE 
                cec.migrate_ep_status IN ('UPGRADE_IN_PROGRESS', 'DONE_MIGRATION', 'IN_PROGRESS') 
                AND cec.node_type = im.node_type AND cec.compute_region_id = im.compute_region_idx
                ) LIMIT {limit}
        """
        ret, res = execute_orch_query(dbh, sql, params, "fetchall")
        if ret == False:
            error = "!!! Fatal! Failed to execute query or no entries found. Return status %s/%s !!!" % \
                    (str(ret), str(res))
            raise Exception(error)
        logger.info("Result: %s" % str(res))
        for row in res:
            if isinstance(row, tuple):
                id, name, mark_delete, node_type, create_time, update_time = row
                logger.info(f"Row details : ID - {id}, "
                            f"Name - {name}, "
                            f"mark delete - {mark_delete}, "
                            f"node_type - {node_type}, "
                            f"create_time - {create_time}, "
                            f"update_time - {update_time}")
                logger.info("Found undeleted entry in instance_master with id %s, name %s and update time %s" % \
                            (str(id),
                             str(name),
                             str(update_time)))
                # update the is_deleted column to 1
                instance_master_model = InstanceModel(iid=id, dbh=dbh)
                logger.error(f"Instance master model details {instance_master_model.__str__()}")
                if instance_master_model.get_param("id") != id:
                    logger.error("Failed to lookup instance master entry with id = %s" % str(id))
                    has_error = True
                    continue
                # If this is a instance that is part of pinned instance upgrade table, Skip deletion since there
                # is a period of time when we don't have reference for old instance.
                pinned_instance_model = PinnedInstanceUpgradeModel(dbh, instance_id=id)
                if pinned_instance_model.get_param('new_instance_id') not in [None, "None", 0, ""] and \
                   pinned_instance_model.get_param("delete_old_done") not in [1, "1"]:
                    logger.info(f"Found an instance {pinned_instance_model.get_param('new_instance_id')} "
                                f"for existing id {id} in pinned instance upgrade table, skipping deletion!")
                    continue
                else:
                    logger.info(f"Did not find instance {id} in pinned instance upgrade table, continuing for deletion!")
                instance_master_model.delete(dbh)
    except Exception as E:
        logger.error(f"Exception {E.args} occurred while checking for undeleted cust topology entries:"
                     f" {traceback.format_exc()}")
        logger.error(f"locals: {locals()}")
        has_error = True
    finally:
        return False if has_error else True
    

'''
SELECT ct.id, ct.custid, ct.name, ct.instance1_id, ct.instance2_id, ct.update_time, ct.node_type
FROM cust_topology ct
WHERE instance1_id NOT IN (SELECT id FROM instance_master)
  AND instance2_id NOT IN (SELECT id FROM instance_master)
  AND instance1_transient NOT IN (SELECT id FROM instance_master)
  AND instance2_transient NOT IN (SELECT id FROM instance_master) and node_type  in (48, 49, 50, 51, 153, 156);
'''

def get_orphan_cust_topology_entries(OrchstrationHandlerReference):
    dbh = OrchstrationHandlerReference.db_h
    logger = dbh.logger
    avctx = OrchstrationHandlerReference.avctx
    has_error = False
    polling_interval_hours = 2
    desired_completion_hours = 12
    max_entries_per_interval = 25
    try:
        # Configurable parameters
        node_types = (NODE_TYPE_REMOTE_NET, NODE_TYPE_SERVICE_CONN, NODE_TYPE_GP_GATEWAY, NODE_TYPE_GP_PORTAL,
                      NODE_TYPE_SWG_PROXY, NODE_TYPE_NAT_INSTANCE, NODE_TYPE_SERVICE_ILB)  # Tuple of node types to filter
        interval_days = 1  # Interval in days

        # Query to get the total number of orphan entries
        count_sql = f"""
            SELECT COUNT(*)
            FROM cust_topology ct
            WHERE 
                instance1_id NOT IN (SELECT id FROM instance_master)
                AND instance2_id NOT IN (SELECT id FROM instance_master)
                AND instance1_transient NOT IN (SELECT id FROM instance_master)
                AND instance2_transient NOT IN (SELECT id FROM instance_master)
                AND node_type IN {node_types}
                AND NOT (ct.transport_type = 'pa-connect' AND ct.parent_id = 0)
                AND ct.create_time < (NOW() - INTERVAL {interval_days} DAY)
        """
        params = None
        ret, count_res = execute_orch_query(dbh, count_sql, params, "fetchone")
        if ret == False or not count_res:
            error = "!!! Fatal! Failed to execute count query. Return status %s/%s !!!" % \
                    (str(ret), str(count_res))
            raise Exception(error)

        total_entries = count_res[0]
        logger.info(f"Total orphan cust topology entries: {total_entries}")

        # If there are no entries, return early
        if total_entries == 0:
            logger.info("No orphan cust topology entries found. Exiting function.")
            return
        
        # Calculate the limit based on the total entries and desired completion time
        limit = total_entries // (desired_completion_hours // polling_interval_hours)
        logger.info(f"Calculated limit for this run: {limit}")

        # Calculate the limit based on the total entries and desired completion time
        limit = total_entries // (desired_completion_hours // polling_interval_hours)
        if limit > max_entries_per_interval:
            limit = max_entries_per_interval
        else:
            limit = total_entries

        # Constructing the SQL query
        sql = f"""
            SELECT 
                ct.id, 
                ct.custid, 
                ct.name, 
                ct.instance1_id, 
                ct.instance2_id, 
                ct.update_time, 
                ct.node_type
            FROM 
                cust_topology ct
            WHERE 
                instance1_id NOT IN (SELECT id FROM instance_master)
                AND instance2_id NOT IN (SELECT id FROM instance_master)
                AND instance1_transient NOT IN (SELECT id FROM instance_master)
                AND instance2_transient NOT IN (SELECT id FROM instance_master)
                AND node_type IN {node_types} 
                AND NOT (ct.transport_type = 'pa-connect' AND ct.parent_id = 0)
                AND ct.create_time < (NOW() - INTERVAL {interval_days} DAY) LIMIT {limit}
        """
        ret, res = execute_orch_query(dbh, sql, params, "fetchall")
        if ret == False:
            error = "!!! Fatal! Failed to execute query or no entries found. Return status %s/%s !!!" % \
                    (str(ret), str(res))
            raise Exception(error)
        logger.info("Result: %s" % str(res))
        for row in res:
            if isinstance(row, tuple):
                id, custid, name, instance1_id, instance2_id, update_time, node_type = row
                logger.info("get_orphan_cust_topology_entries :"
                            "Found undeleted entry in cust topology %s, name %s and update time %s" % \
                            (str(id),
                             str(name),
                             str(update_time)))
                logger.info("Deleting cust node model entry since there is no instance "
                                 "associated and is_deleted is set to 1")
                cust_node_model = CustNodeModel(iid=id, dbh=dbh)
                if cust_node_model.delete(dbh) == False:
                    logger.error(f"get_orphan_cust_topology_entries: "
                                      f"Failed to delete cust node model entry id {cust_node_model.id}")
                    has_error = True
                    # TODO: Send a notification to avisar ?
                    continue

    except Exception as E:
        logger.error(f"Exception {E.args} occurred while checking for undeleted cust topology entries:"
                     f" {traceback.format_exc()}")
        logger.error(f"locals: {locals()}")
        has_error = True
    finally:
        return False if has_error else True


def perform_integrity_checker_tasks(OrchstrationHandlerReference):
    """
    This function performs a series of integrity checks and cleanup tasks on the database.
    It sequentially calls functions to handle undeleted customer topology entries, undeleted
    instances, and orphan customer topology entries, logging the success or failure of each task.

    Parameters:
    OrchstrationHandlerReference: An object that provides access to the database handler (dbh),
    logger, and other context needed for the operation.
    """
    logger = None
    try:
        logger = OrchstrationHandlerReference.db_h.logger
        logger.info("Performing task for check_and_delete_undeleted_cust_topology_entries")
        success = check_and_delete_undeleted_cust_topology_entries(OrchstrationHandlerReference)
        if success == False:
            logger.error("check_and_delete_undeleted_cust_topology_entries: Failed to cleanup some undeleted entries")

        # Handling for get_undeleted_instances
        logger.info("Performing task for get_undeleted_instances")
        success = get_undeleted_instances(OrchstrationHandlerReference)
        if not success:
            logger.error("get_undeleted_instances: Failed to cleanup some undeleted instances")
        else:
            logger.info(f"Successfully processed entries in get_undeleted_instances")

        # Handling for get_orphan_cust_topology_entries
        logger.info("Performing task for get_orphan_cust_topology_entries")
        success = get_orphan_cust_topology_entries(OrchstrationHandlerReference)
        if not success:
            logger.error("get_orphan_cust_topology_entries: Failed to cleanup some orphan entries")
        else:
            logger.info(f"Successfully processed entries in get_orphan_cust_topology_entries")
    except Exception as E:
        if logger:
            logger.error(f"Failed with exception {E}, traceback : {traceback.format_exc()}, locals: {locals()}")

'''
    Future work:

    select ip, acct_id, cluster_id, IP_type from public_ip_pool where cluster_id > 0 and cluster_id not in 
    (select id from instance_master) LIMIT 10000;

     SELECT 
            id, 
                panorama_job_id, 
                rastro_log_tag
            FROM 
                cust_topology
            WHERE 
                instance1_id = 0
                AND instance2_id = 0
                AND instance1_transient = 0
                AND instance2_transient = 0
                AND node_type IN (48, 49, 50, 51, 153, 156)
'''

def main():
    from orchestration_service.core.orchestrator import OrchstrationHandler
    print("Creating the logger!!!")
    from libs.common.logger import logger
    from libs.db.dbhandle import DbHandle
    from libs.msg_defs.handler import GenericHandler
    from libs.cfg import cfg
    print("Creating the logger!!!")
    mylogger = logger("orchestrator_test.log", "/orch_aas/orchestration_service/core/")
    dbh = DbHandle(mylogger)
    orch_hndlr = OrchstrationHandler(cfg)
    orch_hndlr.db_h = dbh

    print(get_undeleted_instances(orch_hndlr))

#main()
