import json
import time
import re
import traceback
from libs.msg_defs.handler import *
from libs.msg_defs.interface import *
from libs.cfg import *
import libs.model.custmodel as CS
import libs.model.custnodemodel as CSN
import libs.model.instancemodel as INST
import libs.model.orchjobs as JOB
from libs.model.vpcmodel import VpcModel
from libs.model.execute_orch_query import *
from libs.apis.region_master_api import *
from libs.cloud_providers.common.instance_trigger_update import trigger_update_for_instance_changes
from libs.cloud_providers.common.instance_ami_util import get_arch_for_cloud_machine_type
from libs.common.utils import gcp_instance_has_nic_limit, get_service_subnet_details, aws_instance_has_nic_limit
from libs.common.shared.sys_utils import *
from libs.common.otp import generate_otp
from libs.common.utils import *
from libs.db.dbhandle import is_region_id_premium_zone
import base64
from orchestration_service.core.orchestrator_commit_status_cfgservice import send_configservice_commit_status

#mapping from node type to VM type
node_vm_type_map = { 48 : 'FIREWALL', 49 : 'GPGATEWAY', 50 : 'GPPORTAL', 51 : 'SERVICECONNECTION', 152 : 'CLEANPIPE'}

shared_vpc_prefix = 'sase-shared-dp-vpc'
shared_subnet_prefix = 'sase-shared-vpc'
shared_vpc_shard_id = '1'
shared_subnet_shard_id = '01'

def notify_instance_upgrade_to_configservice(panorama_job_id, tenant_id, cust_id, instance_id, node_type, logger_uuid, db_h):
    cfgservice_commit_status_payload = dict()
    cfgservice_commit_status_payload["panorama_job_id"] = panorama_job_id
    cfgservice_commit_status_payload["tenant_id"] = tenant_id
    cfgservice_commit_status_payload["cust_topology_id"] = cust_id
    cfgservice_commit_status_payload["instance1_id"] = instance_id
    cfgservice_commit_status_payload["instance2_id"] = ""
    cfgservice_commit_status_payload["workflow_type"] = "INSTANCE_UPGRADE"
    cfgservice_commit_status_payload["log_id"] = logger_uuid
    cfgservice_commit_status_payload["workflow_status"] = "SUCCESS"
    cfgservice_commit_status_payload["workflow_status_details"] = ""
    cfgservice_commit_status_payload["orchestrator_status"] = ""
    cfgservice_commit_status_payload["orchestrator_status_details"] = ""
    cfgservice_commit_status_payload["node_type"] = node_type
    send_configservice_commit_status(cfgservice_commit_status_payload, db_h)

class InstanceHandler(GenericHandler):
    status = "INSTANCE_Q"

    def __init__(self, cfg):
        self.qname = 'instance_q.fifo'
        self.db_h=None
        # Init the logging queue
        super(InstanceHandler, self).init_generic_logger_rastro(qname=self.status.lower())
        self.sqs = SqsApi(cfg['region'], cfg['queues'], self.logger)
        self.valid_msgs = (InstanceReplaceMsg,)
        self.ids = {}
        self.running = True
        self.avctx = AvisarContext(logger=self.logger,
                                   service_endpoint=cfg.get('avisar_service_endpoint'),
                                   forwarding_enabled=cfg.get('avisar_forwarding_enabled', False),
                                   sender="InstanceHandler",
                                   aws_env=cfg.get("aws_env"))

    def get_default_license_sku_type(self, node_type):
        if node_type == 48:
            sku = "FWAAS-300Mbps"
        elif node_type == 49 or node_type == 50:
            sku = "GPAAS-SMALL"
        elif node_type == 51:
            sku = "SERVICECONNECTION"
        elif node_type == 152:
            sku = "CPAAS-300Mbps"
        elif node_type == 154:
            sku = "MAAS"
        else:
            sku = "FWAAS-100Mbps"

        return sku

    def get_vpc_instance(self, dbh, region, node_type, custid, is_passive, is_premium_zone=0):
        vpc_instance = None
        try:
            ha_flag = is_passive if not is_premium_zone else 0
            vpc = VpcModel(region, dbh, ha_flag=ha_flag, custid=custid, is_premium_zone = is_premium_zone)
            if not vpc:
                error = ("Unable to get vpc for Node Type %s, Region id %s"
                        ", Customer id %s" % (node_type, region, custid))
                dbh.logger.error(error)
                raise Exception(error)

            vpc_instance = vpc
        except Exception as E:
            dbh.logger.error("get_vpc_instance: Failed to get the VPC instance %s. locals %s" %
                            (str(E.args), str(locals())))
            vpc_instance = None
        finally:
            return vpc_instance

    def is_spi_customer(self, customer):
        if (customer.get_param("root_tsg_id") is not None and str(customer.get_param("root_tsg_id")) != '0'):
            return True
        return False 

    def process_msg_replace(self, job, result, instance, command, retain_instance_info):
        # Get customer info and current ami id from customer's version
        self.error = ""
        custid = instance.get_param("custid")
        customer = CS.CustomerModel(custid = custid, dbh = self.db_h)
        if not customer.get_param("id"):
            self.error = ("InstanceHandler:process_msg_replace:Unable to find "
                "customer with Id %s" % str(custid))
            self.logger.error(self.error)
            job.status_msg = prepare_job_status_msg(self.error)
            job.save_job(self.db_h)
            return False
        self.logger.info("InstanceHandler:process_msg_replace: instance %s " \
                "instanceid %s " % (str(instance.get_param("name")), str(instance.get_param("id"))))

        # update salt profile with new AMI id:
        cft_params = instance.get_param("salt_profile")
        self.logger.info("Instance %s, old salt profile %s" % (str(instance.get_param("name")), str(instance.get_param("salt_profile"))))

        ret = self.db_h.get_ami_id_for_instance_version(instance)
        if not ret.ok:
            self.error = ("Unable to get AMI id and version for instance %s" %
                (str(instance.get_param("name"))))
            self.logger.error(self.error)
            job.status_msg = prepare_job_status_msg(self.error)
            job.save_job(self.db_h)
            self.error = self.db_h.error + self.error
            return False

        ami_ids = ret.result
        ami_id = ami_ids["x86-64"]["ami-id"]
        ami_id_aarch64 = None
        version = ami_ids["x86-64"]["version"]

        if "aarch64" in ami_ids:
            ami_id_aarch64 = ami_ids["aarch64"].get("ami-id")

        try:
            cft_dict = json.loads(cft_params)
            """
            # GCP salt profile initially doesn't have this field
            if cft_dict['PrimaryImage'] == ami_id:
                msg = ("Instance's Current and New ami_id (%s) is same. "
                       "CYR-4084: Upgrade the instance even ami is same."
                       % str(ami_id))
                self.logger.info(msg)
            """
            if cft_dict.get("arch-type") == 'aarch64':
                _ami_id = ami_id_aarch64.strip()
            else:
                _ami_id = ami_id.strip()
            self.logger.info(f"process_msg_replace: AMI ID for instance={str(instance.get_param('name'))}, new ami_id={_ami_id}")
            if instance.get_param("cloud_provider") == 'aws':
                cft_dict['PrimaryImage'] = _ami_id
            elif instance.get_param("cloud_provider") == 'gcp':
                cft_dict['ImageName'] = _ami_id
                # As of June 2019, GCP do not support change of instance image.
                # Thus, we crete new instance with new alias as workaround
                old_alias = str(instance.get_param("alias"))
                instance.build_alias()  # base name without counter
                if instance.get_param("alias") != old_alias:
                    # increase the counter
                    (_, _suffix) = old_alias.rsplit('-', 1)
                    _suffix = '-{:04d}'.format(int(_suffix)+1)
                    tmp_alias = instance.get_param("alias")
                    tmp_alias += _suffix
                    instance.set_param("alias", tmp_alias)
                else:
                    tmp_alias = instance.get_param("alias")
                    tmp_alias += "-0001"
                    instance.set_param("alias", tmp_alias)
                cft_dict['InstanceName'] = instance.get_param("alias")

                # New otp is needed for every new instance
                otp = generate_otp(self.logger, instance.get_param("name"), instance.get_param("id"))
                if otp is None:
                    self.logger.error("Failed to generate otp for instance %s" % (str(instance.get_param("id"))))
                    raise Exception(self.error)

                # replate old otp with new one
                cft_dict['UserData'] = re.sub(r'(?:(?<=[ ,])|(?<=^))cert_fetch_otp *= *[^ ^,]+',
                                              'cert_fetch_otp='+str(otp),
                                              cft_dict['UserData'])

                # instance.vmid = None
            elif instance.get_param("cloud_provider") == 'oci':
                cft_dict['ImageName'] = _ami_id
                self.logger.info(f"process_msg_replace: AMI ID for instance={str(instance.get_param('name'))}, new ami_id={cft_dict['ImageName']}")
            elif instance.get_param("cloud_provider") is None:
                self.error = ("cloud_provider not found in instance model")
                self.logger.error(self.error)
                job.status_msg = prepare_job_status_msg(self.error)
                job.save_job(self.db_h)
                return False
            else:
                self.error = ("Cloud provider %s not supported by INSTANCE_REPLACE" % str(instance.get_param("cloud_provider")))
                self.logger.error(self.error)
                job.status_msg = prepare_job_status_msg(self.error)
                job.save_job(self.db_h)
                return False
            inft_set = cft_dict['PrimaryIntfSet']
            if inft_set == 0:
                inft_set = 1
            else:
                inft_set = 0
            cft_dict['PrimaryIntfSet'] = inft_set
        except Exception as ex:
            self.error = ("Invalid instance cft params: %s" % str(ex))
            self.logger.error(self.error)
            job.status_msg = prepare_job_status_msg(self.error)
            job.save_job(self.db_h)
            return False

        dbh = self.db_h
        cursor = None
        inst_type = None
        region = instance.get_param("compute_region_idx")
        node_type = instance.get_param("node_type")

        if ("PrimaryCapacityType" in list(cft_dict.keys())):
            capacity_type = cft_dict["PrimaryCapacityType"]
        else:
            capacity_type = "default"

        market_type = cft_dict.get("PrimaryMarketType", "")
        dpdk_qcount = cft_dict.get("PrimaryDpdkQcount", 0)
        capacitytype_key = "PrimaryCapacityType" if (instance.get_param("id") ==
            instance.get_param("clusterid")) else "SecondaryCapacityType"


        if retain_instance_info == False:
            try:
                cur_inst_type = cft_dict["InstType"]
                is_colo_sc = True if 'colo_interface_support' in cft_dict.keys() \
                                     and cft_dict['colo_interface_support'] == True else False

                sku = self.get_default_license_sku_type(node_type)
                self.logger.info("Existing Instance custid: %s " \
                    "node_type: %s cloud_provider: %s region: %s " \
                    "InstType: %s sku: %s" % \
                    (str(instance.get_param("custid")), str(instance.get_param("node_type")),
                    instance.get_param("cloud_provider"), str(region), cur_inst_type, str(sku)))

                if is_colo_sc == True:
                    (market_type2, gpcs_instance_size2, dp_gpcs_instance_size, capacity_type2, dpdk_qcount2, ha_mode) = \
                        get_capacity_type(dbh, custid, region, NODE_TYPE_COLO_SC, version=version)
                    if (gpcs_instance_size2 is None):
                        gpcs_instance_size2, dpdk_qcount2, capacity_type2 = get_colo_sc_capacity_type(dbh=dbh)
                else:
                    (market_type2, gpcs_instance_size2, dp_gpcs_instance_size, capacity_type2, dpdk_qcount2, ha_mode) = \
                        get_capacity_type(dbh, custid, region, node_type, sku, version=version, is_ha_upgrade=True)

                if instance.get_param("cloud_provider") == PROVIDER_GCP and \
                        dpdk_qcount2 is not None and dpdk_qcount2 > 0:
                    dpdk_qcount = dpdk_qcount2
                    self.logger.info("cloud_provider: %s dpdk_qcount: %d" % \
                        (instance.get_param("cloud_provider"), dpdk_qcount))

                if (gpcs_instance_size2 != None):
                    if market_type2 is not None and market_type2 != '':
                        market_type = market_type2
                    dp_cloud_machine_type = None
                    if (dp_gpcs_instance_size is not None):
                        dp_cloud_machine_type = get_cloud_machine_type_with_region_idx(
                            dbh, region, dp_gpcs_instance_size, custid=custid)

                    cloud_machine_type = get_cloud_machine_type_with_region_idx(
                            dbh, region, gpcs_instance_size2, custid=custid)
                    if (capacity_type2 == None or not capacity_type2.strip()):
                        capacity_type = "default"
                    else:
                        capacity_type = capacity_type2

                    if instance.get_param("slot_nr") == 0:
                        if command == "replace_second":
                            (cloud_machine_type, capacity_type) = get_target_passive_machine_type(self.db_h,
                                                                                                  instance.get_param("id"),
                                                                                                  cloud_machine_type,
                                                                                                  capacity_type)
                        else:
                            (cloud_machine_type, capacity_type) = get_target_active_machine_type(self.db_h,
                                                                                                 instance.get_param("id"),
                                                                                                 cloud_machine_type,
                                                                                                 capacity_type)
                    dbh.logger.info("Upgrading: %s - after calling new API " \
                                     "custid: %s region: %s node_type: %s market_type = %s " \
                                     "capacity_type: %s " \
                                     "cloud machine_type: %s  gpcs_instance_size: %s " \
                                     "dp_cloud_machine_type: %s  dp_gpcs_instance_size: %s " \
                                     "market_type: %s" % \
                                     (instance.get_param("name"), str(custid), str(region),
                                      str(node_type), str(market_type), str(capacity_type),
                                      str(cloud_machine_type), gpcs_instance_size2,
                                      str(dp_cloud_machine_type), dp_gpcs_instance_size, market_type))

                    cloud_machine_type = get_target_override_for_version(dbh,
                                                                        instance.get_param("cloud_provider"),
                                                                        custid,
                                                                        node_type,
                                                                        cloud_machine_type,
                                                                        version,
                                                                        region,
                                                                        1)

                    if instance.get_param("slot_nr") == INSTANCE_ROLE_DP:
                        # Initialize DP specific size
                        cft_dict["InstType"] = dp_cloud_machine_type
                    else:
                        cft_dict["InstType"] = cloud_machine_type
                    cft_dict[capacitytype_key] = capacity_type
                    if market_type != "":
                        cft_dict["PrimaryMarketType"] = market_type
                        cft_dict["SecondaryMarketType"] = market_type
                    if dpdk_qcount > 0:
                        cft_dict["PrimaryDpdkQcount"] = dpdk_qcount
                        cft_dict["SecondaryDpdkQcount"] = dpdk_qcount

                    # update ami based on cloud_machine_type
                    arch_type=get_arch_for_cloud_machine_type(self.db_h, cloud_machine_type)
                    ami_id = ami_ids[arch_type]["ami-id"]
                    _ami_id = ami_id.strip()
                    if instance.get_param("cloud_provider") == 'aws':
                        cft_dict['PrimaryImage'] = _ami_id
                    elif instance.get_param("cloud_provider") == 'gcp':
                        cft_dict['ImageName'] = _ami_id

            except Exception as ex:
                self.logger.error("Upgrading Custom MachineType exception: %s" %
                    (str(ex,)))
                self.logger.error(
                    "Exception %s occured: %s %s\n" % (str(E.args), str(traceback.format_exc()), str(locals())))
                dbh.cursorclose(cursor)
        else:
            self.logger.info("retaining current instance info for instance %s" % (str(instance.get_param("id"))))

        try:
            if (self.db_h.get_service_nic_feature_flag(PROVIDER_GCP) ==  True and
                instance.get_param("cloud_provider") == 'gcp'  and
                instance.get_param("node_type") in self.db_h.get_service_nic_node_types(PROVIDER_GCP) and
                not gcp_instance_has_nic_limit(self.logger, cft_dict["InstType"]) and
                not self.is_spi_customer(customer)):
                userdata = cft_dict['UserData']
                if 'is_service_nic_supported' not in userdata:
                    self.logger.info("Adding service_nic to the instnace")
                    region = cft_dict['RegionName']
                    host_project = self.db_h.get_gcp_host_project()
                    if host_project is not  None:
                        vpc_prefix_from_gcp_env = self.db_h.get_shared_vpc_prefix_from_deployed_gcp_env()
                        cft_dict['serviceInterfaceName'] = 'nic-service'
                        if vpc_prefix_from_gcp_env == "":
                            cft_dict['serviceNetwork'] = shared_vpc_prefix + '-' + shared_vpc_shard_id
                        else:
                            cft_dict['serviceNetwork'] = shared_vpc_prefix + '-' + vpc_prefix_from_gcp_env + '-' + shared_vpc_shard_id
                        cft_dict['serviceSubnet'] = shared_subnet_prefix + '-' + region + '-' + shared_subnet_shard_id
                        cft_dict['serviceProjectOverride'] = host_project
                        cft_dict['serviceHasExternalIP'] = False
                        cft_dict['is_service_nic_supported'] = True
                        userdata += (",is_service_nic_supported=True")
                        cft_dict['UserData'] = userdata
                    else:
                        self.logger.error("Unable to fetch gcp host project")
                else:
                    self.logger.info("service_nic already present")
        except Exception as ex:
            self.logger.error("failed adding service nic with exception %s" %
                (str(ex,)))
            self.logger.error("%s Traceback: %s" % (self.error, str(traceback.format_exc())))
            dbh.cursorclose(cursor)
            return False

        try:
            if (self.db_h.get_service_nic_feature_flag(PROVIDER_AWS) ==  True and
                instance.get_param("cloud_provider") == 'aws'  and
                instance.get_param("node_type") in self.db_h.get_service_nic_node_types(PROVIDER_AWS) and
                not aws_instance_has_nic_limit(self.logger, cft_dict["InstType"]) and
                not self.is_spi_customer(customer)):
                userdata = cft_dict['UserData']
                if 'is_service_nic_supported' not in userdata:
                    self.logger.info("Adding service_nic to the instnace")
                    salt_profile_name = instance.get_param("salt_profile_name")
                    if 'passive' in salt_profile_name:
                        is_passive = True
                    else:
                        is_passive = False
                    is_premium_zone = is_region_id_premium_zone(self.db_h, region, custid=custid)
                    vpc_instance = self.get_vpc_instance(self.db_h, region, node_type, custid, is_passive, is_premium_zone)
                    is_service_subnet_present, service_params = get_service_subnet_details(dbh, vpc_instance)
                    if is_service_subnet_present : 
                        cft_dict['Eth3SecGrp'] = service_params[1]
                        if is_passive:
                            cft_dict['ServicePassiveSubnet'] = service_params[0]
                            cft_dict['PassiveServiceIntfSet'] = 1
                        else:
                            cft_dict['ServiceActiveSubnet'] = service_params[0]
                            cft_dict['ActiveServiceIntfSet'] = 1

                        cft_dict['is_service_nic_supported'] = True
                        userdata += (",is_service_nic_supported=True")
                        cft_dict['UserData'] = userdata
                    self.logger.info("instance service_nic is supported, but service subnet is not ready.")
                else:
                    self.logger.info("service_nic already present")
        except Exception as ex:
            self.logger.error("failed adding service nic with exception %s" %
                (str(ex,)))
            self.logger.error("%s Traceback: %s" % (self.error, str(traceback.format_exc())))
            dbh.cursorclose(cursor)
            return False

        self.logger.debug("Upgrading Custom Machine-Type Final: " \
            "custid: %s node_type: %s region: %s " \
            "cloud provider: %s " \
            "NEW inst_type: %s capacitytype_key: %s capacity_type: %s " \
            "dpdk_qcount: %d" % (str(instance.get_param("custid")), str(instance.get_param("node_type")),
            str(region), instance.get_param("cloud_provider"), cft_dict["InstType"],
            capacitytype_key, capacity_type, dpdk_qcount))

        #TBD
        #instance.get_instance_profile_fixup_for_haif(cft_dict, cft_dict["InstType"])

        # Recreate salt_profile
        cft_params = json.dumps(cft_dict)

        instance.set_param("salt_profile", cft_params)
        self.logger.info("Instance %s, new salt profile %s" % (str(instance.get_param("name")),
                                                               str(instance.get_param("salt_profile"))))
        instance.set_param("retries", 0)
        instance.set_param("timestamp", time.time())
        instance.set_param("state", -2)  # Replace
        instance.set_param("upgrade_creation", 1)  # Upgrade
        # CYR-10843. Do NOT update 'version' column to target AMI version.
        # If we do and if upgrade fails, it's not easy to
        # revert it. So, update AMI version on successful upgrade. This is
        # done in soft_upgrade_fw_verify lambda (upgrade.py file).
        # instance.version = version
        instance.set_param("tagging_status", "not_yet_tagged")

        # CYR-12797, CYR-13015 max-capacity still set to 300 after upgrade, should be 500
        #   for GCP use PROVIDER_GCP_MAX_FW_INSTANCE_CAPACITY,
        #   for AWS max-capacity will be picked up from machine_type_master

        (update_max_capacity, new_max_capacity) = self.db_h.get_updated_max_capacity_for_instance(custid,
                                                                                                  instance.get_param("max_capacity"))

        preupgrade_capacity = instance.get_param("max_capacity")
        self.logger.info("Instance upgrade %s: pre-upgrade capacity = %s, update-max-capacity = %s, new_max_capacity = %s"
                        % (str(instance.get_param("name")), str(preupgrade_capacity), str(update_max_capacity), str(new_max_capacity)))

        #CYR-14637 - Max capacity is increased to 500 before HA peer can be upgraded also
        update_peer_max_capacity = False
        peer = None
        if update_max_capacity and instance.get_param("ha_peer"):
            peer = INST.InstanceModel(iid=instance.get_param("ha_peer"), dbh=self.db_h)
            if not peer.get_param("id"):
                dbh.logger.error("Unable to find HA peer for instance %s" % instance.get_param("name"))
            else:
                peer_supported_max_capacity = peer.get_supported_max_capacity(db_h=dbh)
                if new_max_capacity > peer_supported_max_capacity:
                    # if peer version doesnt support the new capacity yet, defer to when both
                    # instances are upgraded to update max. In this case, current instance's
                    # max-capacity will get updated when peer is upgraded
                    self.logger.info("Defer update of max-capacity till peer %s at version %s is also upgraded"
                                     % (peer.get_param("name"), peer.get_param("version")))
                    update_max_capacity = False
                else:
                    # Flag to indicate both that a peer exists and that the peer max-capacity needs to be updated as well
                    self.logger.info("Update max-capacity of instance %s and of peer %s" % (instance.get_param("name"), peer.get_param("name")))
                    update_peer_max_capacity = True

        if update_max_capacity:
            try :
                if instance.get_param("cloud_provider") == 'gcp':
                    instance.set_param("max_capacity", new_max_capacity)
                    self.logger.info("Instance upgrade for GCP instance %s max-capacity updated to %s"
                                      % (str(instance.get_param("name")), str(instance.get_param("max_capacity"))))
                else: # cloud_provider is AWS. Other conditions already checked above

                    # For AWS, not all machine types may support the new updated max-capacity. Choose it from machine_type_master
                    inst_sites = instance.get_sites(dbh)
                    if len(inst_sites) == 0:
                        self.logger.error("Unable to find sites for AWS instance %s, setting instance capacity to max supported"
                                          % (str(instance.get_param("name"))))
                        if instance.get_param("max_capacity") <= PROVIDER_COMMON_OLD_MAX_CAPACITY:
                            # set to new max only if its previously set to old max i.e. 300
                            # otherwise, leave to current setting
                            instance.set_param("max_capacity", new_max_capacity)
                    else:
                        site0 = inst_sites[0]
                        cust_node_reference = CSN.CustNodeModel(iid=site0, dbh=self.db_h)
                        if cust_node_reference.id == None or cust_node_reference.id == 0:
                            self.error = ("No instance node reference %s found for updating max-capacity for AWS instance %s"
                                          % (str(site0), str(instance.get_param("name"))))
                            raise Exception(self.error)
                        machine_type = cust_node_reference.machine_type
                        node_vm_type = node_vm_type_map[node_type]
                        instance.set_param("max_capacity", self.db_h.get_capacity_for_instance(node_vm_type, machine_type, region))
                        if instance.get_param("max_capacity") == 0 or instance.get_param("max_capacity") == None:
                            instance.set_param("max_capacity", preupgrade_capacity)

                    self.logger.info ("Instance upgrade for AWS instance %s max-capacity updated to %s" % (str(instance.get_param("name")), str(instance.get_param("max_capacity"))))
            except Exception as E:
                self.logger.info("Instance upgrade: Error updating max-capacity of instance %s" % (str(instance.get_param("name"))))


        if instance.get_param("max_capacity") == 0:
            # in case there are errors updating max-capacity
            self.logger.info("Instance upgrade: Error in max-capacity updation, resetting to pre-upgrade capacity for %s"
                            % (str(instance.get_param("name"))))
            instance.set_param("max_capacity", preupgrade_capacity)

        if peer and update_peer_max_capacity:
            peer.set_param("max_capacity", instance.get_param("max_capacity"))
            peer.save()

        #In case of upgrade, insert an entry in xxx_cfgserv_firewall_summary for ugrade_commit_on_startup
        ret = instance.insert_last_successfull_job_info(self.db_h, instance.get_param("id"), customer=customer)
        if ret == False:
            self.logger.info("Unable to insert upgrade-commit-on-startup entry in the xxx_cfgserv_firewall_summary table")
        instance.save()

        #Send notification to configservice regarding instance upgrade and upgrade_commit_on_startup info
        notify_instance_upgrade_to_configservice(self.avctx.panorama_job_id, self.avctx.tenant_id, str(instance.get_param("custid")), instance.get_param("id"), instance.get_param("node_type"), self.logger.uuid, self.db_h)

        try:
            instance_check_again = INST.InstanceModel(iid=instance.get_param("id"), dbh=self.db_h)
            if instance_check_again:
                self.logger.info("Updated DB state for instance %s : %s" %(str(instance.get_param("id")), str(instance_check_again.__str__())))
        except Exception as E:
            self.logger.info("Unable to query instance id %s" %(str(instance.get_param("id"))))
        compute_region_idx = instance.get_param("compute_region_idx")
        cloud_provider = gpcs_get_cloud_type_from_region_idx(self.db_h, compute_region_idx, custid=custid)
        #cftmsg = CloudFormationMsg(orchhdr, custid, region)
        myres, trigger_update_seq_no = trigger_update_for_instance_changes(self.db_h, compute_region_idx, custid, cloud_provider)
        #jmsg = self.to_json(cftmsg)
        #result.append(jmsg)
        return myres, trigger_update_seq_no

    def process_msg_resize(self, job, result, instance, command, target_machine_type,
            target_capacity_type, upgrade_type, trace_id):
        success = "Failed"
        trigger_update_seq_no = 0
        try:
            # Update salt profile with target instance type
            cft_params = instance.get_param("salt_profile")

            self.logger.info("Instance %s, old salt profile %s" % (str(instance.get_param("name")),
                                                                   str(instance.get_param("salt_profile"))))
            try:
                cft_dict = json.loads(cft_params)
            except Exception as ex:
                self.error = ("Invalid instance cft params: %s" % str(ex))
                raise Exception(self.error)
            self.logger.info("Received iid: %s, Target MachineType : %s, "
                "Target Capacity Type: %s" % (instance.get_param("id"), target_machine_type, target_capacity_type))

            if target_machine_type is None or target_machine_type == cft_dict.get("InstType", None):
                if target_machine_type == None:
                    self.error = "Either vertical scale is disabled or no matching instance type found."
                    raise Exception(self.error)
                else:
                    success = "Continue"
                    self.error = "Target MachineType is same as current: %s" % target_machine_type
                    # Also mark this by setitng new_instance_vmid
                    if not instance.update_upgrade_table(self.db_h, 0, True, True):
                        raise Exception("Failed to update new vmid in the " \
                         "instance_upgrade for id: %s" % instance.get_param("id"))
                    if (early_process_peer_instance(self.db_h, instance, cft_dict, command, upgrade_type,
                        target_cpu, target_memory, target_session)):
                        self.logger.info("Early procesed the Peer and no upgrade neeeded "
                            "workflow is complete for instance_id  %s" % (instance.get_param("id")))
                        success = "Done"
                    else:
                        self.logger.info("Continue to process instance for id %s" % (instance.get_param("id")))
                    return
            else:
                # Not really an error!
                self.error = "Attempting to resize instance %s from %s to %s, %s" % \
                    (instance.get_param("id"), cft_dict.get("InstType"), target_machine_type,
                    target_capacity_type)
                cft_dict["InstType"] = target_machine_type

                capacitytype_key = "PrimaryCapacityType" if (instance.get_param("id") ==
                    instance.get_param("clusterid")) else "SecondaryCapacityType"
                cft_dict[capacitytype_key] = target_capacity_type

                self.logger.info("Resizing Machine-Type Final: "
                         "custid: %s node_type: %s region: %s "
                         "cloud provider: %s "
                         "NEW inst_type: %s capacitytype_key: %s "
                         "capacity_type: %s" %
                         (str(instance.get_param("custid")), str(instance.get_param("node_type")),
                         str(instance.get_param("compute_region_idx")), instance.get_param("cloud_provider"),
                         target_machine_type, capacitytype_key, target_capacity_type))

                try:
                    instance.get_instance_profile_fixup_for_haif(cft_dict, target_machine_type)
                except Exception as ex:
                    self.error = "Exception in get_instance_profile_fixup_for_haif: %s" % str(ex)
                    self.logger.error("%s Traceback: %s" % (self.error,
                        str(traceback.format_exc())))
                    raise Exception(self.error)

                # Recreate salt_profile
                cft_params = json.dumps(cft_dict)

                self.logger.debug("Instance %s, current salt profile %s" %
                    (str(instance.get_param("name")), str(instance.get_param("salt_profile"))))
                instance.set_param("salt_profile", cft_params)
                self.logger.info("Instance %s, new salt profile %s" %
                    (str(instance.get_param("name")), str(instance.get_param("salt_profile"))))
                instance.set_param("retries", 0) #TBD
                instance.set_param("timestamp", time.time())
                # -3: Indicates resize is being processed
                instance.set_param("state", -3)
                instance.set_param("upgrade_creation", 1)  # Upgrade
                instance.set_param("tagging_status", "not_yet_tagged")
                #No config commit needed

                instance.save()

                if (gcp_instance_has_nic_limit(logger=self.logger, size=instance.get_param("native_machine_type")) !=
                    gcp_instance_has_nic_limit(logger=self.logger, size=target_machine_type)):
                    try:
                        # Switch to legacy upgrade via replace
                        ret, trigger_update_seq_no = self.process_msg_replace(job, result, instance, None, True)
                        if not ret:
                            raise Exception(self.error)
                    except Exception as E:
                        self.logger.info("Unable to replace instance id %s, %s" %(str(instance.get_param("id")), str(E.args)))
                else:
                    try:
                        instance_check_again = INST.InstanceModel(iid=instance.get_param("id"), dbh=self.db_h)
                        if instance_check_again:
                            self.logger.info("Updated DB state for instance %s : %s" %(str(instance.get_param("id")), str(instance_check_again.__str__())))
                    except Exception as E:
                        self.logger.info("Unable to query instance id %s, %s" %(str(instance.get_param("id")), str(E.args)))
                    compute_region_idx = instance.get_param("compute_region_idx")
                    cloud_provider = gpcs_get_cloud_type_from_region_idx(self.db_h, compute_region_idx,
                                                                         custid=instance.get_param("custid"))
                    myres, trigger_update_seq_no = trigger_update_for_instance_changes(self.db_h,
                            compute_region_idx, instance.get_param("custid"), cloud_provider)

                    self.logger.info("Triggered update for instance changes: %s" % str(myres))
                    if myres < 0 or trigger_update_seq_no == None:
                        err_msg = ("Failed to trigger update for the entry. Cust ID: %s, \
                                      compute region: %s, cloud_provider: %s" %
                                      (str(compute_region_idx), str(instance.get_param("custid")), str(cloud_provider)))
                        raise Exception(err_msg)

                # Update the instance upgrade table now.
                ret = instance.update_upgrade_table_seq_nr(self.db_h, trigger_update_seq_no)
                if ret == False:
                    err_msg = ("Failed to update instance_upgrade_seq_nr %s for the entry. \
                               Cust ID: %s, compute region: %s, cloud_provider: %s"
                               % (str(trigger_update_seq_no),
                                  str(compute_region_idx),
                                  str(instance.get_param("custid")),
                                  str(cloud_provider)))
                    raise Exception(err_msg)

                customer = CS.CustomerModel(custid=instance.get_param("custid"), dbh=self.db_h)
                if not customer.get_param("id"):
                    self.error = ("InstanceHandler:process_msg_resize:Unable to "
                            "find customer with Id %s" % str(instance.get_param("custid")))
                    raise Exception(self.error)
            success = "Success"
        except Exception as E:
            job.status_msg = prepare_job_status_msg(self.error)
            job.save_job(self.db_h)
            self.logger.error(self.error)
            job.status_msg = prepare_job_status_msg(self.error)
            job.save_job(self.db_h)
            self.logger.error("Sending 'Failed' notification")
            self.logger.error("Exception %s Traceback: %s, locals: %s" %
                    (str(E), str(traceback.format_exc()), locals()))
        finally:
            return upgradesvc_notification_send("ORCHESTRATION", instance,
                success, self.db_h, trace_id, vmid=instance.get_param("vmid"), status_msg=self.error)

    def process_msg(self, msg, workflow, jobid, result):
        self.error = ""
        self.rastro_log_tag = None
        ret = False
        try:
            if msg.rastro_log_tag != None:
                try:
                    self.rastro_log_tag = json.loads(base64.b64decode(msg.rastro_log_tag).decode('utf-8'))
                    self.logger.set_enhanced_log_tag(self.rastro_log_tag)
                except Exception as E:
                    self.logger.info("Failed to do a base64 decode for %s" % str(msg.rastro_log_tag))

            job = JOB.OrchJobs(jobid=jobid, dbh=self.db_h)
            if not job.jobid:
                self.error = ("InstanceHandler:process_msg:Unable to find job with id %d" % jobid)
                self.logger.error("InstanceHandler:process_msg:Unable to find job with id %d" % jobid)
                ret = False
                return
            job.status = self.status
            job.status_msg = prepare_job_status_msg("Starting orchestration for the job")
            job.save_job(self.db_h)
            instance = INST.InstanceModel(iid=msg.instanceid, dbh=self.db_h)
            if not instance.get_param("id"):
                self.error = ("InstanceHandler:process_msg:Unable to find instance with Id %s" % str(msg.instanceid))
                self.logger.error(self.error)
                job.status_msg = prepare_job_status_msg(self.error)
                job.save_job(self.db_h)
                ret = False
                return
            self.logger.info("instance vmid is %s" % instance.get_param("vmid"))

            if workflow == "INSTANCE_REPLACE":
                # TODO: Check for duplicate message
                if instance.get_param("state") == -2: #or instance.state == -3:
                    self.logger.error("Probably got duplicate message for instance %s" % str(instance.get_param("name")))
                    ret = True
                    return
                # Check the type from instance_upgrade table
                sql = "select upgrade_type, instance_id, target_machine_type, " \
                      "target_capacity_type, trace_id, command, retain_instance_info " \
                      "from instance_upgrade where cluster_id=%s and workflow_status <> 'done'"
                ret, res = execute_orch_query(self.db_h, sql, (instance.get_param("clusterid"),), "fetchall")
                self.logger.info("Result: %s, %s" % (str(ret), str(res)))
                if ret:
                    upgrade_type = res[0][0]
                    if upgrade_type == "step-function":
                        # Legacy upgrade via replace
                        ret, _ = self.process_msg_replace(job, result, instance, res[0][5], res[0][6])
                        return

                    # Pick the matching row. upgrade_type must be same for
                    #   both the rows of a cluster_id anyway.
                    found = False
                    for row in res:
                        upgrade_type = row[0]
                        instance_id = row[1]
                        if upgrade_type in ["VERTICAL_SCALE_UP", "VERTICAL_SCALE_DOWN"] and instance_id == instance.get_param("id"):
                            found = True
                            target_machine_type = row[2]
                            target_capacity_type = row[3]
                            trace_id = row[4]
                            command = row[5]
                            # Vertical scale trigger
                            ret = self.process_msg_resize(job, result, instance, command, target_machine_type,
                                 target_capacity_type, upgrade_type, trace_id)
                            return
                    if not found:
                        self.logger.error("Failed to find vert scale entry in " \
                                          "instance_upgrade for instance_id %s" % instance.get_param("id"))
                        ret = False
                        return
                else:
                    self.logger.error("Failed to retrieve upgrade_type, target_cpu, target_memory")
                    ret = False
                    return
            ret = True
        except Exception as E:
            ret = False
            self.logger.error("Failed with Exception %s" % str(E))
            self.logger.error("Traceback %s, locals: %s" % (str(traceback.format_exc()), str(locals())))
        finally:
            return ret
