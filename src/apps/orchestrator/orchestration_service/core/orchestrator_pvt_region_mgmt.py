import traceback
import hashlib
import json

from libs.common.shared.sys_utils import NODE_TYPE_SASE_PRIV_REGION_LB, NODE_TYPE_GP_GATEWAY
from libs.model.privateInstanceMgmtModelOrch import privateRegionInstanceMgmtModelOrch
from libs.model.instancemodel import InstanceModel
from libs.model.custnodemodel import find_instance_entries_by_svctype_and_region


def compute_config_hash(config):
    # Extract and normalize fields in a fixed order
    fields = [
        config.get("mgmt.alloc_type", "dhcp"),
        config.get("mgmt.ip_address", ""),
        config.get("mgmt.default_gateway", ""),
        config.get("mgmt.dns.1", "*******"),
        config.get("mgmt.dns.2", "*******"),
    ]

    # Serialize in a stable format
    serialized = json.dumps(fields, separators=(',', ':'), ensure_ascii=False)

    # Compute SHA-256 hash
    return hashlib.sha256(serialized.encode('utf-8')).hexdigest()


def sase_pvt_region_get_commit_hash_for_mgmt_interface(logger, network_cfg):
    success = False
    computed_hash = None
    try:
        # Check the network config for mgmt info. Create a hash and store it in the priv_instance_management_table
        computed_hash = compute_config_hash(network_cfg)
        success = True
    except Exception as E:
        logger.error(f"sase_pvt_region_get_commit_hash_for_mgmt_interface:"
                     f" Failed with exception {E.args}, Traceback: {traceback.format_exc()}, "
                     f"locals : {locals()}")
    finally:
        return success, computed_hash


def setup_private_instance_mgmt_entry(dbh, instance_model, svc_node_type):
    success = False
    logger = dbh.logger
    try:
        # Check the network config for mgmt info. Create a hash and store it in the priv_instance_management_table
        # Populate the priv_instance_management_table with the relevant details.
        # Trigger update on the terraform if it's a new instance creation ??? Do we need it, once its deployed - rest of
        # the members will get the info.
        # if the mobile user gateway is added, trigger an update for the Load balancer to include the new gateway.

        # Get the required info from instance_mode
        inst_id = instance_model.get_param("id")
        acct_id = instance_model.get_param("acct_id")
        node_type = instance_model.get_param("node_type")
        salt_profile = instance_model.get_param("salt_profile")
        salt_profile_dict = json.loads(salt_profile)
        network_cfg = salt_profile_dict["NetworkConfig"]

        success, cfg_hash = sase_pvt_region_get_commit_hash_for_mgmt_interface(logger, network_cfg)
        if success == False or cfg_hash == None:
            raise Exception("Failed to compute commit hash!")

        priv_inst_profile = privateRegionInstanceMgmtModelOrch(dbh, inst_id, node_type)
        priv_inst_profile.set_param("node_type", node_type)
        # Service node type would be setup later in the post-processing step after the cust topo
        # and instance master entries are linked.
        priv_inst_profile.set_param("svc_node_type", svc_node_type)
        priv_inst_profile.set_param("trigger_setcfg", 10)
        if node_type in [ NODE_TYPE_GP_GATEWAY ]:
            priv_inst_profile.set_param("trigger_setcfg_done", 10)
        else:
            priv_inst_profile.set_param("trigger_setcfg_done", 0)
        priv_inst_profile.set_param("acct_id", acct_id)
        priv_inst_profile.set_param("commit_hash", cfg_hash)
        success = priv_inst_profile.save()
        if success == False:
            raise Exception("setup_private_instance_mgmt_entry - failed to save the priv_inst_profile")

        success = True
    except Exception as E:
        logger.error(f"setup_private_instance_mgmt_entry: "
                     f"Failed with exception {E.args}, Traceback: {traceback.format_exc()}, "
                     f"locals : {locals()}")
    finally:
        return success


def update_private_instance_mgmt_entry(dbh, inst_id, svc_node_type):
    # We are adding the trigger seq # and svc node type post processing once the cust topo and instance master
    # entries are linked.
    success = False
    logger = dbh.logger
    try:
        inst_model = InstanceModel(iid=inst_id, dbh=dbh)
        if inst_model.get_param("id") == None:
            logger.info(f"Unable to find instance with id {inst_id} in the db. Skipping private inst mgmt entry!")
            success = True
            return

        node_type = inst_model.get_param("node_type")
        if node_type in [0, None, "None"]:
            logger.info(f"Skipping instance entry trigger for instance {inst_id}")
            success = True
            return

        # Get the entry that we added from the database for priv inst mgmt.
        priv_inst_mgmt_model = privateRegionInstanceMgmtModelOrch(dbh, inst_id, node_type)
        if priv_inst_mgmt_model.valid != True:
            logger.info(f"Creating entry for inst id {inst_id} and node_type {node_type}")
            if setup_private_instance_mgmt_entry(dbh, inst_model, svc_node_type) == False:
                raise Exception(f"Failed to create a private region entry entry for {inst_id} "
                                f"and node_type {node_type}")
            else:
                logger.info(f"Successfully created entry for inst id {inst_id} and node_type {node_type}")
                success = True
                return

        priv_inst_mgmt_model.set_param("svc_node_type", svc_node_type)
        priv_inst_mgmt_model.set_param("trigger_setcfg", int(priv_inst_mgmt_model.get_param("trigger_setcfg")) + 10)
        if node_type == NODE_TYPE_GP_GATEWAY:
            # Set to same as trigger_setcfg + 10
            priv_inst_mgmt_model.set_param("trigger_setcfg_done",
                                           int(priv_inst_mgmt_model.get_param("trigger_setcfg")))

        # Now lets also populate the cfghash.
        salt_profile = inst_model.get_param("salt_profile")
        salt_profile_dict = json.loads(salt_profile)
        network_cfg = salt_profile_dict["NetworkConfig"]
        success, cfg_hash = sase_pvt_region_get_commit_hash_for_mgmt_interface(logger, network_cfg)
        if success == False or cfg_hash == None:
            raise Exception("Failed to compute commit hash!")
        logger.info(f"Setting the config-hash for inst id {inst_id} to {cfg_hash}")
        priv_inst_mgmt_model.set_param("commit_hash", cfg_hash)

        # Save the config to the database!
        if priv_inst_mgmt_model.save() == False:
            raise Exception("Failed to save priv instance mgmt model")

        success = True
    except Exception as E:
        logger.error(f"update_private_instance_mgmt_entry: "
                     f"Failed with exception {E.args}, Traceback: {traceback.format_exc()}, "
                     f"locals : {locals()}")
    finally:
        return success

def delete_private_instance_mgmt_entry(dbh, inst_id):
    # We are adding the trigger seq # and svc node type post processing once the cust topo and instance master
    # entries are linked.
    success = False
    logger = dbh.logger
    try:
        logger.info(f"delete_private_instance_mgmt_entry: Cleaning up entry related to inst id {inst_id} from "
                    f"priv_instance_management_table")
        inst_model = InstanceModel(iid=inst_id, dbh=dbh)
        node_type = inst_model.get_param("node_type")

        # Get the entry from the database for priv inst mgmt.
        priv_inst_mgmt_model = privateRegionInstanceMgmtModelOrch(dbh, inst_id, node_type)
        if priv_inst_mgmt_model.valid != True:
            raise Exception("Failed to get valid priv inst mgmt model!")

        if priv_inst_mgmt_model.delete() == False:
            raise Exception("Failed to save pric instance mgmt model")

        success = True
    except Exception as E:
        logger.error(f"delete_private_instance_mgmt_entry: "
                     f"Failed with exception {E.args}, Traceback: {traceback.format_exc()}, "
                     f"locals : {locals()}")
    finally:
        return success

# If there is a new MU added we will need to do a trigger update for the MU's as well.
def trigger_priv_inst_mgmt_lb_update(dbh, custid, region_id, svc_node_type):
    success = False
    logger = dbh.logger
    try:
        node_type = NODE_TYPE_SASE_PRIV_REGION_LB
        # Find instances of type LB where there is a match for compute region and svc node type.
        success, instances = find_instance_entries_by_svctype_and_region(dbh, custid, region_id, svc_node_type,
                                                                         node_type)
        if success == False:
            raise Exception("Failed to get details about the instances from the db")
        for instance_id in instances:
            # Trigger update for the lbs to re-discover!
            success = update_private_instance_mgmt_entry(dbh, instance_id, svc_node_type)
            if success == False:
                raise Exception("Failed to trigger cfg change for private instance mgmt entry!")

        success = True
    except Exception as E:
        logger.error(f"trigger_priv_inst_mgmt_lb_update: "
                     f"Failed with exception {E.args}, Traceback: {traceback.format_exc()}, "
                     f"locals : {locals()}")
    finally:
        return success

def trigger_priv_inst_mgmt_mu_update(dbh, custid, region_id, svc_node_type):
    success = False
    logger = dbh.logger
    try:
        node_type = NODE_TYPE_GP_GATEWAY
        # Find instances of type MU where there is a match for compute region and svc node type.
        success, instances = find_instance_entries_by_svctype_and_region(dbh, custid, region_id, svc_node_type,
                                                                         node_type)
        if success == False:
            raise Exception("Failed to get details about the instances from the db")
        for instance_id in instances:
            # Trigger update for the lbs to re-discover!
            success = update_private_instance_mgmt_entry(dbh, instance_id, svc_node_type)
            if success == False:
                raise Exception("Failed to trigger cfg change for private instance mgmt entry!")
        success = True
    except Exception as E:
        logger.error(f"trigger_priv_inst_mgmt_mu_update: "
                     f"Failed with exception {E.args}, Traceback: {traceback.format_exc()}, "
                     f"locals : {locals()}")
    finally:
        return success

def main():
    import libs.db.dbhandle as DB
    import threading
    import logging
    filename = "test.log"
    FORMAT = "[%(filename)s:%(lineno)s - %(funcName)20s() ] %(message)s"
    logging.basicConfig(format=FORMAT)
    handler = logging.handlers.RotatingFileHandler(filename, maxBytes=10485760, backupCount=3)
    logger = logging.getLogger(threading.currentThread().getName())
    logger.setLevel(logging.DEBUG)
    logger.addHandler(handler)
    dbh = DB.DbHandle(logger)

    instmodel = InstanceModel(dbh=dbh, iid=2274)
    '''
    1)
    #print(instmodel.__str__())
    salt_profile = instmodel.get_param("salt_profile")
    salt_profile_dict = json.loads(salt_profile)
    network_cfg = salt_profile_dict["NetworkConfig"]
    print(network_cfg)
    svc_node_type = 49
    print(setup_private_instance_mgmt_entry(dbh, instmodel, svc_node_type))
    '''

    # 2) update_private_instance_mgmt_entry
    # print(update_private_instance_mgmt_entry(dbh, 2274, 49))

    # 3) find_instance_entries_by_svctype_and_region()
    # print(find_instance_entries_by_svctype_and_region(dbh, 86, 5008, 49, 179))
    #print(trigger_priv_inst_mgmt_lb_update(dbh, 86, 5008, 49))


#if __name__ == '__main__':
#    main()
