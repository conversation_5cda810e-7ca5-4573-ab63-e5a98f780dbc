package otelemetry

import (
	"go.opentelemetry.io/otel/exporters/prometheus"
	"go.opentelemetry.io/otel/metric"
	metric_api "go.opentelemetry.io/otel/sdk/metric"
	"orchestrator/libs/go/utils"
	"orchestrator/zti_controller/infra/logger"
	"sync"
)

var (
	_meterOnce sync.Once
	_meter     metric.Meter
)

func Meter() metric.Meter {
	_meterOnce.Do(func() {
		eLog := utils.GetEventLogger(logger.GetGlobalLogger())
		eLog.LogInfo("initialze /metrics provider")
		exporter, err := prometheus.New()
		if err != nil {
			eLog.LogFatal("failed to initialize metrics provider: %v", err)
		}
		_meter = metric_api.NewMeterProvider(metric_api.WithReader(exporter)).Meter("zti_controller")
	})
	return _meter
}
