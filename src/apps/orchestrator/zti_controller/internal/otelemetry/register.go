package otelemetry

import (
	"go.opentelemetry.io/otel/metric"
	"orchestrator/libs/go/utils"
	"orchestrator/zti_controller/infra/logger"
)

type ZtiControllerMetrics struct {
	// metrics
	ZtiControllerSvcStart metric.Int64Counter

	// metrics
	TenantRegionOnboardingEventTrigger metric.Int64Counter
	TenantRegionOnboardingEventSuccess metric.Int64Counter
	TenantRegionOnboardingEventFailure metric.Int64Counter

	TenantRegionOffBoardingEventTrigger metric.Int64Counter
	TenantRegionOffBoardingEventSuccess metric.Int64Counter
	TenantRegionOffBoardingEventFailure metric.Int64Counter
}

var m ZtiControllerMetrics

func GetMetricsHandler() *ZtiControllerMetrics {
	return &m
}

func RegisterMetrics() error {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	if c, err := Meter().Int64Counter("zti_controller_svc_start", metric.WithDescription("zti controller service start")); err != nil {
		eLog.LogFatal("failed to create metrics counter zti controller svc: %v", err)
	} else {
		m.ZtiControllerSvcStart = c
	}

	if c, err := Meter().Int64Counter("tenant_region_onboarding_event_trigger", metric.WithDescription("tenant regional onboarding event")); err != nil {
		eLog.LogFatal("failed to create metrics counter zti controller svc: %v", err)
	} else {
		m.TenantRegionOnboardingEventTrigger = c
	}

	if c, err := Meter().Int64Counter("tenant_region_offboarding_event_trigger", metric.WithDescription("tenant regional offboarding event")); err != nil {
		eLog.LogFatal("failed to create metrics counter zti controller svc: %v", err)
	} else {
		m.TenantRegionOffBoardingEventTrigger = c
	}
	return nil
}
