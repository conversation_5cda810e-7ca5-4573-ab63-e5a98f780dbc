package orchestration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"go.panw.local/pangolin/clogger"
	"io"
	"net/http"
	"orchestrator/libs/go/dbaccess/models/cust_master"
	"orchestrator/libs/go/dbaccess/models/ingress_cluster_config"
	"orchestrator/libs/go/dbaccess/models/ingress_cust_config"
	"orchestrator/libs/go/dbaccess/models/ingress_master"
	"orchestrator/libs/go/dbaccess/models/instance_master"
	"orchestrator/libs/go/dbaccess/models/region_master"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/license"
	"orchestrator/libs/go/utils"
	"orchestrator/zti_controller/infra/db"
	"orchestrator/zti_controller/infra/httpclient"
	"orchestrator/zti_controller/infra/logger"
	"strconv"
)

type LocationReq struct {
	ComputeRegionId      int64  `json:"compute_region_id"`
	EdgeLocationRegionId int64  `json:"edge_location_region_id,omitempty"`
	UUID                 string `json:"uuid,omitempty"`
}

type UpgradeReq struct {
	CustId          string `json:"cust_id"`
	ComputeRegionId int64  `json:"compute_region_id"`
}

type EdgeLocationMapping []EdgeLocation
type EdgeLocation struct {
	GpaasDisplayName     string `json:"gpaas_display_name"`
	EdgeLocationRegionID int64  `json:"edge_location_region_id"`
}

func (om *OrchestrationMgr) validateOnboardingInputs(computeRegionId, edgeLocationRegionId, custId int64) error {
	if custId <= 0 {
		return fmt.Errorf("custID cannot be empty")
	}
	if computeRegionId <= 0 {
		return fmt.Errorf("computeRegionId must be positive, got: %d", computeRegionId)
	}
	if edgeLocationRegionId <= 0 {
		return fmt.Errorf("edgeLocationRegionId must be positive, got: %d", edgeLocationRegionId)
	}
	return nil
}

func (om *OrchestrationMgr) validateDeboardInputs(computeRegionId, custId int64) error {
	if custId <= 0 {
		return fmt.Errorf("custID cannot be empty")
	}
	if computeRegionId <= 0 {
		return fmt.Errorf("computeRegionId must be positive, got: %d", computeRegionId)
	}
	return nil
}

// generateEntradaId generates a standardized entrada ID from customer ID and compute region ID
func generateEntradaId(custId, computeRegionId int64) string {
	return "entrada-" + strconv.FormatInt(custId, 10) + "-" + strconv.FormatInt(computeRegionId, 10) + "-"
}

func (om *OrchestrationMgr) getIngressClusterCfgEntry(dbAccessor db.DbAccessor, computeRegionId int64, eLog *clogger.EventLogger) (*ingress_cluster_config.Row, error) {
	ingressClusterEntry, err := dbAccessor.GetIngressClusterConfigByRegionId(computeRegionId, eLog)
	if err != nil {
		eLog.LogError("Error GetIngressClusterConfigByRegionId: %v, computeRegionId=%v", err, computeRegionId)
		return nil, fmt.Errorf("failed to retrieve ingress cluster config: %v", err)
	}
	return ingressClusterEntry, nil
}

func (om *OrchestrationMgr) getRegionMasterEntry(dbAccessor db.DbAccessor, computeRegionId int64, eLog *clogger.EventLogger) (*region_master.Row, error) {
	// get region master entry
	regionMasterEntry, err := dbAccessor.GetRegionMasterById(computeRegionId, eLog)
	if err != nil {
		eLog.LogError("Error GetRegionMasterById: %v, computeRegionId=%v", err, computeRegionId)
		return nil, fmt.Errorf("failed to retrieve region master entry: %v", err)
	}
	return regionMasterEntry, nil
}

func (om *OrchestrationMgr) getIngressCustCfgEntry(dbAccessor db.DbAccessor, custId, regionID int64, eLog *clogger.EventLogger) (*ingress_cust_config.Row, error) {
	// Get ingress configuration for the customer
	ingressCustCfgEntry, err := dbAccessor.GetIngressCustConfigByID(custId, regionID, eLog)
	if err != nil {
		eLog.LogError("Error GetIngressCustConfigByID: %v, CustId=%v", err, custId)
		return nil, fmt.Errorf("failed to retrieve ingress cust config: %v", err)
	}
	eLog.LogInfo("IngressCustConfigEntry: %v", ingressCustCfgEntry)
	return ingressCustCfgEntry, nil
}

func (om *OrchestrationMgr) getCustMasterEntry(dbAccessor db.DbAccessor, custId int64, eLog *clogger.EventLogger) (*cust_master.Row, error) {
	// Get cust info
	CustMasterEntry, err := dbAccessor.GetCustMasterById(custId, eLog)
	if err != nil {
		eLog.LogError("Error GetCustMaster: %v, custId=%v", err, custId)
		return nil, fmt.Errorf("failed to retrieve customer master entry: %v", err)
	}

	return CustMasterEntry, nil
}

func (om *OrchestrationMgr) sendArgoRequest(err error, path string, req ArgoTriggerReq, eLog *clogger.EventLogger) error {
	// Marshal the triggerReq to JSON
	jsonData, err := json.Marshal(req)
	if err != nil {
		eLog.LogError("Error marshaling JSON triggerReq:", err)
		return fmt.Errorf("failed to marshal triggerReq: %v", err)
	}

	// Create HTTP request
	payload, err := http.NewRequest("POST", path, bytes.NewBuffer(jsonData))
	if err != nil {
		eLog.LogError("Failed to prepare event trigger request: %v", err)
		return fmt.Errorf("failed to prepare event trigger request: %v", err)
	}

	// Set the content type header
	payload.Header.Set("Content-Type", "application/json")

	//Use GetHttpClient() to get the HTTP client
	hc := httpclient.GetHttpClient()
	resp, err := hc.Do(payload)
	if err != nil {
		eLog.LogError("Failed to send event trigger request: %v, uri=%s", err, path)
		return fmt.Errorf("failed to send event trigger request: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	respBody, _ := io.ReadAll(resp.Body)

	// Check response status
	if c := resp.StatusCode; c < 200 || c >= 300 {
		eLog.LogError("Event trigger failed with status code %d: %s", c, string(respBody))
		return fmt.Errorf("event trigger failed with status code %d: %s", c, string(respBody))
	}

	eLog.LogInfo("Successfully sent JSON triggerReq to %s", path)
	eLog.LogInfo("Response: %s", string(respBody))
	return nil
}

func (om *OrchestrationMgr) getCdlRegion(tenantId int64) (string, error) {
	dbAccessor := db.GetDbAccessor()
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	dbCtx := &sql.DbContext{DbConn: dbAccessor.GetDbConn(), Logger: eLog}
	regionName, err := license.GetCDLRegionNameByTenantID(dbCtx, tenantId)
	if err != nil {
		eLog.LogError("Unable to get the CDL region for the tenant %d, err: %v", tenantId, err)
		return "", fmt.Errorf("failed to retrieve CDL region for tenant: %v", err)
	}
	eLog.LogInfo("CDL Region: %v", regionName)
	return regionName, nil
}

func (om *OrchestrationMgr) UpdateProvStatusHelper(custid, computeRegionId int64, ProvStatus string) error {
	dbAccessor := db.GetDbAccessor()
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	dbCtx := &sql.DbContext{DbConn: dbAccessor.GetDbConn(), Logger: eLog}

	//update Prov status
	var provStatus ingress_master.ProvisioningStatus
	provStatus.Status = ProvStatus
	status, err := json.Marshal(provStatus)
	if err != nil {
		eLog.LogError("Error marshaling JSON triggerReq: %v", err)
		return fmt.Errorf("failed to marshal triggerReq: %v", err)
	}
	statusString := string(status)
	err = ingress_master.UpdateProvStatus(dbCtx, custid, computeRegionId, &statusString)
	if err != nil {
		eLog.LogError("Error updating prov status: %v", err)
		return fmt.Errorf("failed to update prov status: %v", err)
	}
	return nil
}

func (om *OrchestrationMgr) UpdateIlbIPHelper(custid, computeRegionId int64, ilbIP string) error {
	dbAccessor := db.GetDbAccessor()
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	dbCtx := &sql.DbContext{DbConn: dbAccessor.GetDbConn(), Logger: eLog}
	err := ingress_master.UpdateIlbIP(dbCtx, custid, computeRegionId, ilbIP)
	if err != nil {
		eLog.LogError("Error updating ilb IP: %v", err)
		return fmt.Errorf("failed to update ilb IP: %v", err)
	}
	return nil
}

func (om *OrchestrationMgr) GetIlbIP(custid, computeRegionId, nodeType int64) (string, error) {
	dbAccessor := db.GetDbAccessor()
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	dbCtx := &sql.DbContext{DbConn: dbAccessor.GetDbConn(), Logger: eLog}
	entry, err := instance_master.GetRowByNodeTypeAndRegion(dbCtx, custid, computeRegionId, nodeType)
	if err != nil {
		eLog.LogError("Error getting instance master entry: %v", err)
		return "", fmt.Errorf("failed to get instance master entry: %v", err)
	}

	ip := entry.PublicIp.String()
	if ip == "" {
		return "", fmt.Errorf("failed to get instance master entry, ip is empty")
	}

	return entry.PublicIp.String(), nil
}
