package orchestration

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/lambda"
	"github.com/aws/aws-sdk-go-v2/service/lambda/types"
	"orchestrator/libs/go/utils"
	pconfig "orchestrator/zti_controller/infra/config"
	"orchestrator/zti_controller/infra/db"
	"orchestrator/zti_controller/infra/logger"
	"orchestrator/zti_controller/internal/common"
	"strconv"
)

const (
	lambda_notify_cfgservice = "notify_cfgservice"

	lamba_action_notify_fqdn_change = "notify_fqdn_change"

	op_add    = "Add"
	op_delete = "Delete"
	op_update = "Update"
)

// RegionalILB defines the structure for the nested "regional_ilb" JSON object.
type RegionalILB struct {
	RegionComputeID string `json:"region_compute_id"`
	Operation       string `json:"operation"`
	ILBIP           string `json:"ilb_ip"`
	Name            string `json:"name"`
}

// LambdaRequest defines the structure for the top-level JSON object sent to the Lambda.
type LambdaRequest struct {
	TenantID    string      `json:"tenant_id"`
	Action      string      `json:"action"`
	FQDN        string      `json:"fqdn"`
	RegionalILB RegionalILB `json:"regional_ilb"`
}

// InvokeLambdaHelper invokes an AWS Lambda function with a specific payload and returns the response
func InvokeLambdaHelper(ctx context.Context, lambdaFunctionName string, payload *LambdaRequest) ([]byte, error) {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	eLog.LogInfo("calling lambda function: %s with payload: %+v", lambdaFunctionName, payload)

	c := pconfig.GetConfig()
	awsRegion := c.Region

	// The Lambda service expects the payload as a JSON byte slice.
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		eLog.LogError("failed to marshal payload to JSON: %v", err)
		return nil, err
	}

	// This loads the default configuration from your environment (IAM role, ~/.aws/credentials, etc.)
	// It's the standard way to set up the SDK.
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion(awsRegion))
	if err != nil {
		eLog.LogError("unable to load SDK config, %v", err)
		return nil, err
	}

	// This client object is used to make API calls to the AWS Lambda service.
	lambdaClient := lambda.NewFromConfig(cfg)

	eLog.LogInfo("calling lambda function: %s with jsonPayload=%s", lambdaFunctionName, string(jsonPayload))

	// We are using the "RequestResponse" invocation type, which means the call is synchronous.
	// Our program will wait for the Lambda function to finish and return a response.
	result, err := lambdaClient.Invoke(ctx, &lambda.InvokeInput{
		FunctionName:   aws.String(lambdaFunctionName),
		InvocationType: types.InvocationTypeRequestResponse,
		Payload:        jsonPayload,
	})
	if err != nil {
		eLog.LogError("failed to invoke lambda function: %v", err)
		return nil, err
	}

	// Check if the Lambda function itself returned an error.
	if result.FunctionError != nil {
		eLog.LogError("lambda function returned an error: %s", *result.FunctionError)
		return nil, fmt.Errorf("lambda function error: %s", *result.FunctionError)
	}

	// The status code should be 200 for a successful invocation.
	if result.StatusCode != 200 {
		eLog.LogError("lambda invocation failed with status code: %d", result.StatusCode)
		return nil, fmt.Errorf("lambda invocation failed with status code: %d", result.StatusCode)
	}

	eLog.LogInfo("Successfully invoked lambda '%s'.\n", lambdaFunctionName)
	eLog.LogInfo("Lambda Response:\n%s\n", string(result.Payload))
	return result.Payload, nil
}

func (om *OrchestrationMgr) TriggerNotifyConfigService(ctx context.Context, payload LambdaRequest) ([]byte, error) {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	data, err := InvokeLambdaHelper(ctx, lambda_notify_cfgservice, &payload)
	if err != nil {
		return nil, err
	}

	eLog.LogInfo("Lambda Response:\n%s\n", string(data))
	return data, nil
}

func (om *OrchestrationMgr) TriggerNotifyConfigHelper(ctx context.Context, custId int64) error {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	cfg := pconfig.GetConfig()
	zone := cfg.ZtiController.R53Info.EntradaDomain

	// update DB
	dbAccessor := db.GetDbAccessor()

	// Get custMaster entry
	custMasterEntry, err := om.getCustMasterEntry(dbAccessor, custId, eLog)
	if err != nil {
		return err
	}

	rows, err := dbAccessor.GetIngressMasterEntryByCustId(custId, eLog)
	if err != nil {
		eLog.LogError("Failed to get ingress master entry: %v", err)
		return fmt.Errorf("failed to get ingress master entry: %v", err)
	}

	if len(rows) == 0 {
		eLog.LogInfo("No ingress master entry found")
	}

	for _, iEntry := range rows {
		eLog.LogInfo("Found ingress master entry: %v", iEntry)
		// notify config service
		req := LambdaRequest{
			TenantID: strconv.FormatInt(custMasterEntry.AcctID.Int64(), 10),
			Action:   lamba_action_notify_fqdn_change,
			FQDN:     common.GetGlobalFQDN(custMasterEntry.Name.String(), zone),
			RegionalILB: RegionalILB{
				RegionComputeID: iEntry.ComputeRegionID.String(),
				Operation:       op_add,
				ILBIP:           iEntry.ILBIP.String(),
				Name:            iEntry.NativeComputeRegionName.String(),
			},
		}

		data, err := om.TriggerNotifyConfigService(context.Background(), req)
		if err != nil {
			eLog.LogError("Failed to trigger NotifyConfigService: %v", err)
		} else {
			eLog.LogInfo("Lambda Response:\n%s\n", string(data))
		}
	}
	return nil
}
