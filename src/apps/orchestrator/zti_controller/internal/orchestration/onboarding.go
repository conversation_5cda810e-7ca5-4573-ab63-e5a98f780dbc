package orchestration

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
	"go.panw.local/pangolin/clogger"
	"orchestrator/libs/go/dbaccess/models/ingress_cust_config"
	"orchestrator/libs/go/dbaccess/models/ingress_master"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/utils"
	pconfig "orchestrator/zti_controller/infra/config"
	"orchestrator/zti_controller/infra/db"
	"orchestrator/zti_controller/infra/logger"
	"orchestrator/zti_controller/internal/argo"
	"orchestrator/zti_controller/internal/common"
	"orchestrator/zti_controller/internal/dns"
	"strconv"
	"time"
)

const (
	INGRESS_PROVISION_STARTED     = "INGRESS_PROVISION_STARTED"
	INGRESS_PROVISION_IN_PROGRESS = "INGRESS_PROVISION_IN_PROGRESS"
	INGRESS_PROVISION_FAILED      = "INGRESS_PROVISION_FAILED"
	INGRESS_PROVISION_COMPLETED   = "INGRESS_PROVISION_COMPLETED"

	NAMESPACE_SUFFIX     = "ns"
	APP_SUFFIX           = "app"
	REPLICA_COUNT        = 1
	DOCKER_REGISTRY_BASE = "us-docker.pkg.dev"
	ENTRADA_REPO         = "entrada"

	TenantID      = "tenant_id"
	TsgID         = "tsg_id"
	CustID        = "cust_id"
	RegionID      = "region_id"
	CloudProvider = "cloud-provider"
	EnvoyVersion  = "envoy_version"
)

type ArgoTriggerReq struct {
	ApplicationNamespace string `json:"applicationNamespace"`
	ApplicationName      string `json:"applicationName"`
	KubernetesClusterURL string `json:"kubernetesClusterURL"`
	Values               struct {
		ReplicaCount int `json:"replicaCount"`
		Envoyproxy   struct {
			Version    string `json:"version"`
			TenantId   string `json:"tid"`
			TsgID      string `json:"tsgid"`
			CDlRegion  string `json:"cdl_region"`
			RegionName string `json:"region_name"`
			RegionID   string `json:"region_id"`
		} `json:"envoyproxy"`
		Kro struct {
			Name        string `json:"name"`
			ProjectName string `json:"projectName"`
			Location    string `json:"location"`
			AddressType string `json:"addressType"`
		} `json:"kro"`
	} `json:"values"`
	Env      string `json:"env"`
	Region   string `json:"region"`
	Tenantid string `json:"tenantid"`
	Custid   string `json:"custid"`
}

func (om *OrchestrationMgr) TriggerLocationOnboarding(req LocationReq, custId int64, eLog *clogger.EventLogger) error {
	eLog.LogInfo("req=%s", req)

	dbAccessor := db.GetDbAccessor()
	cfg := pconfig.GetConfig()
	computeRegionId := req.ComputeRegionId
	edgeLocationRegionId := req.EdgeLocationRegionId

	// Input validation
	if err := om.validateOnboardingInputs(computeRegionId, edgeLocationRegionId, custId); err != nil {
		eLog.LogError("Input validation failed: %v", err)
		return err
	}

	// Get custMaster entry
	custMasterEntry, err := om.getCustMasterEntry(dbAccessor, custId, eLog)
	if err != nil {
		return err
	}

	// Get ingress cust cfg entry
	ingressCustCfgEntry, err := om.getIngressCustCfgEntry(dbAccessor, custId, computeRegionId, eLog)
	if err != nil {
		return err
	}

	// Get region Master entry
	regionMasterEntry, err := om.getRegionMasterEntry(dbAccessor, computeRegionId, eLog)
	if err != nil {
		return err
	}

	// Get ingress cluster entry
	ingressClusterEntry, err := om.getIngressClusterCfgEntry(dbAccessor, computeRegionId, eLog)
	if err != nil {
		return err
	}

	custName := custMasterEntry.Name
	nativeComputeRegionName := regionMasterEntry.NativeComputeRegionName.String()
	cloudProvider := regionMasterEntry.CloudProvider.String()
	clusterUrl := ingressClusterEntry.ClusterKubeURL
	if clusterUrl == "" {
		eLog.LogError("ClusterURL is empty for computeRegionId=%v", computeRegionId)
		return fmt.Errorf("cluster URL cannot be empty")
	}

	var proxySettings ingress_cust_config.ProxySettings
	if ingressCustCfgEntry.ProxySettings != nil && *ingressCustCfgEntry.ProxySettings != "" {
		err := json.Unmarshal([]byte(*ingressCustCfgEntry.ProxySettings), &proxySettings)
		if err != nil {
			eLog.LogError("Error Unmarshal ProxySettings: %v, custId=%v", err, custId)
			return fmt.Errorf("failed to unmarshal proxy settings: %v", err)
		}
	}

	entradaId := generateEntradaId(custId, computeRegionId)
	triggerReq := ArgoTriggerReq{}

	//TODO workaround - bringup entrada cluster
	ProjectId := cfg.ZtiController.DataplaneInfo.GcpProjectID
	ProjectId = "sase-entrada-gke-dev-01"

	cdlRegion, err := om.getCdlRegion(custMasterEntry.AcctID.Int64())
	if err != nil {
		eLog.LogError("Error getting cdlRegion: %v", err)
	}

	TenantIdStr := strconv.FormatInt(custMasterEntry.AcctID.Int64(), 10)

	attrs := attribute.NewSet(
		attribute.Key(TenantID).String(TenantIdStr),
		attribute.Key(TsgID).String(custMasterEntry.TenantServiceGroup.String()),
		attribute.Key(CustID).String(strconv.FormatInt(custId, 10)),
		attribute.Key(RegionID).String(strconv.FormatInt(computeRegionId, 10)),
		attribute.Key(CloudProvider).String("GCP"),
		attribute.Key(EnvoyVersion).String(proxySettings.EnvoyVersion),
	)
	om.metrics.TenantRegionOnboardingEventTrigger.Add(context.TODO(), 1, metric.WithAttributeSet(attrs))

	triggerReq.ApplicationNamespace = fmt.Sprintf("%s%s", entradaId, NAMESPACE_SUFFIX)
	triggerReq.ApplicationName = fmt.Sprintf("%s%s", entradaId, APP_SUFFIX)
	triggerReq.KubernetesClusterURL = string(clusterUrl)
	triggerReq.Values.ReplicaCount = REPLICA_COUNT
	triggerReq.Values.Envoyproxy.Version = fmt.Sprintf("%s/%s/%s/%s", DOCKER_REGISTRY_BASE, ProjectId, ENTRADA_REPO, proxySettings.EnvoyVersion)
	triggerReq.Values.Envoyproxy.TenantId = TenantIdStr
	triggerReq.Values.Envoyproxy.TsgID = custMasterEntry.TenantServiceGroup.String()
	triggerReq.Values.Envoyproxy.CDlRegion = cdlRegion

	triggerReq.Values.Envoyproxy.RegionName = nativeComputeRegionName
	triggerReq.Values.Envoyproxy.RegionID = strconv.FormatInt(computeRegionId, 10)

	triggerReq.Values.Kro.Name = entradaId + cfg.ZtiController.Kro.NameSuffix
	triggerReq.Values.Kro.ProjectName = ProjectId
	triggerReq.Values.Kro.Location = nativeComputeRegionName
	triggerReq.Values.Kro.AddressType = cfg.ZtiController.Kro.AddressType
	triggerReq.Env = strconv.FormatInt(custMasterEntry.AcctID.Int64(), 10)
	triggerReq.Region = nativeComputeRegionName
	triggerReq.Tenantid = TenantIdStr
	triggerReq.Custid = strconv.FormatInt(custMasterEntry.ID.Int64(), 10)

	eLog.LogInfo("sending triggerReq=%+v", triggerReq)
	eLog.LogInfo("sending triggerReq=%s", fmt.Sprintf("%s", triggerReq))

	url := cfg.ZtiController.ArgoServiceInfo.ArgoBaseUrl
	ApiPath := cfg.ZtiController.ArgoServiceInfo.ArgoApiPath

	path := ApiPath + url
	eLog.LogInfo("path=%s", path)
	err = om.sendArgoRequest(err, path, triggerReq, eLog)
	if err != nil {
		return err
	}

	ingressMasterEntry, err := om.GetOrCreateIngressMasterEntry(dbAccessor, eLog, custId, computeRegionId, nativeComputeRegionName, cloudProvider)
	if err != nil {
		return err
	}

	ingressMasterEntry.ProxySettings = ingressCustCfgEntry.ProxySettings
	var provStatus ingress_master.ProvisioningStatus
	provStatus.Status = INGRESS_PROVISION_STARTED
	status, err := json.Marshal(provStatus)
	if err != nil {
		return err
	}
	statusString := string(status)
	ingressMasterEntry.ProvisioningStatus = &statusString

	// TODO get the edge location gpass display name and not compute regin gpass display name
	eLocEntry := EdgeLocation{
		GpaasDisplayName:     regionMasterEntry.GpaasDisplayName.String(),
		EdgeLocationRegionID: edgeLocationRegionId,
	}
	err = om.updateEdgeLocationMapping(ingressMasterEntry, eLog, custId, edgeLocationRegionId, eLocEntry)
	if err != nil {
		return err
	}

	err = dbAccessor.SaveToIngressMaster(ingressMasterEntry, eLog)
	if err != nil {
		eLog.LogError("Failed to save to ingress master: %v", err)
	}

	//Trigger a IP address glean logic
	// TODO better logic
	go func() {
		var req argo.ArgoCallBackReq
		req.ArgoApplication = triggerReq.ApplicationName
		req.RegionId = computeRegionId
		req.RegionName = nativeComputeRegionName
		req.CustId = custId
		req.CustName = string(custName)
		i := 0
		for {
			globalFqdn, _, err := om.ArgoMgr.FetchAndUpdateExternalIP(req)
			if err == nil {
				tenantid := strconv.FormatInt(custMasterEntry.AcctID.Int64(), 10)
				err := om.PostProcessProvSuccess(req, eLog, tenantid, globalFqdn)
				if err != nil {
					eLog.LogError("Failed to post process prov success: %v", err)
				}
				break
			}
			if i >= 30 {
				err = om.UpdateProvStatusHelper(custId, computeRegionId, INGRESS_PROVISION_FAILED)
				if err != nil {
					eLog.LogError("Failed to update ProvStatus: %v", err)
				}
				eLog.LogError("Failed to update ProvStatus: %v", err)
				break
			}
			i++
			time.Sleep(10 * time.Second)
		}
	}()
	return nil
}

func (om *OrchestrationMgr) PostProcessProvSuccess(r argo.ArgoCallBackReq, eLog *clogger.EventLogger, tenantId string, globalFqdn string) error {
	dbAccessor := db.GetDbAccessor()

	iEntry, err := dbAccessor.GetIngressMasterEntryByCustIdAndRegionId(int64(r.CustId), r.RegionId, eLog)
	if err != nil {
		eLog.LogError("Failed to get ingress master entry")
		return fmt.Errorf("Failed to get ingress master entry: %v", err)
	}

	eLog.LogInfo("IngressMasterEntry: %v", iEntry)
	eLog.LogInfo("Successfully fetched and updated external IP for %s", iEntry.ExternalRegionalIP)

	ilbIP := iEntry.ILBIP.String()
	eLog.LogInfo("ILBIP: %v", ilbIP)

	// If ilb ip is not filled, Fetch and update
	if ilbIP == "" {
		// Update ILB if not updated.
		ip, err := om.GetIlbIP(r.CustId, r.RegionId, 191)
		if err != nil {
			eLog.LogError("Failed to get lb ip: %v", err)
		} else {
			err := om.UpdateIlbIPHelper(r.CustId, r.RegionId, ip)
			if err != nil {
				eLog.LogError("Failed to update lb ip: %v", err)
			}
			ilbIP = ip
			eLog.LogInfo("update ILB IP: %v", ilbIP)
		}
	}

	// notify config service
	req := LambdaRequest{
		TenantID: tenantId,
		Action:   lamba_action_notify_fqdn_change,
		FQDN:     globalFqdn,
		RegionalILB: RegionalILB{
			RegionComputeID: iEntry.ComputeRegionID.String(),
			Operation:       op_add,
			ILBIP:           ilbIP,
			Name:            iEntry.NativeComputeRegionName.String(),
		},
	}

	data, err := om.TriggerNotifyConfigService(context.Background(), req)
	if err != nil {
		eLog.LogError("Failed to trigger NotifyConfigService: %v", err)
		// TODO fix me
	} else {
		eLog.LogInfo("Lambda Response:\n%s\n", string(data))
	}

	err = om.UpdateProvStatusHelper(r.CustId, r.RegionId, INGRESS_PROVISION_COMPLETED)
	if err != nil {
		eLog.LogError("failed to update ProvStatus: %v", err)
		return fmt.Errorf("failed to update ProvStatus: %v", err)
	}
	eLog.LogInfo("Successfully updated ProvStatus")
	return nil
}

func (om *OrchestrationMgr) updateEdgeLocationMapping(ingressMasterEntry *ingress_master.Row, eLog *clogger.EventLogger, custId int64, edgeLocationRegionId int64, eLocEntry EdgeLocation) error {
	var eMap EdgeLocationMapping
	if ingressMasterEntry.EdgeLocationMapping != nil && *ingressMasterEntry.EdgeLocationMapping != "" {
		err := json.Unmarshal([]byte(*ingressMasterEntry.EdgeLocationMapping), &eMap)
		if err != nil {
			eLog.LogError("Error Unmarshal ProxySettings: %v, CustId=%v", err, custId)
			return fmt.Errorf("failed to unmarshal proxy settings: %v", err)
		}
	}

	found := false
	for _, e := range eMap {
		if e.EdgeLocationRegionID == edgeLocationRegionId {
			found = true
			return nil
		}
	}

	if !found {
		eMap = append(eMap, eLocEntry)
	}

	eMapTemp, err := json.Marshal(eMap)
	if err != nil {
		return err
	}
	eMapStr := string(eMapTemp)
	ingressMasterEntry.EdgeLocationMapping = &eMapStr
	return nil
}

func (om *OrchestrationMgr) GetOrCreateIngressMasterEntry(dbAccessor db.DbAccessor, eLog *clogger.EventLogger, custId int64, computeRegionId int64, n, c string) (*ingress_master.Row, error) {
	entry, err := dbAccessor.GetIngressMasterEntryByCustIdAndRegionId(custId, computeRegionId, eLog)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// if entry not found
			eLog.LogInfo("Not Found. Create an entry IngressMaster")
			ingressMasterEntry := &ingress_master.Row{}
			ingressMasterEntry.CustID = sql.Int64(custId)
			ingressMasterEntry.ComputeRegionID = sql.String(strconv.FormatInt(computeRegionId, 10))
			ingressMasterEntry.VNI = 0
			ingressMasterEntry.NativeComputeRegionName = sql.String(n)
			ingressMasterEntry.CloudProvider = sql.String(c)
			return ingressMasterEntry, nil
		}

		return nil, err
	}

	eLog.LogInfo("Found an IngressMasterEntry: %v)%v", entry, entry)
	return entry, nil
}

func (om *OrchestrationMgr) GetIngressMasterEntry(dbAccessor db.DbAccessor, eLog *clogger.EventLogger, custId int64, computeRegionId int64) (*ingress_master.Row, error) {
	entry, err := dbAccessor.GetIngressMasterEntryByCustIdAndRegionId(custId, computeRegionId, eLog)
	if err != nil {
		return nil, err
	}

	eLog.LogInfo("Found an IngressMasterEntry: %v)%v", entry, entry)
	return entry, nil
}

func (om *OrchestrationMgr) GetTenantLocations(custID string, m *logger.LogMeta) error {
	dbAccessor := db.GetDbAccessor()
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	cust, _ := strconv.Atoi(custID)
	rows, err := dbAccessor.GetIngressMasterEntryByCustId(int64(cust), eLog)
	if err != nil {
		eLog.LogError("Failed to get ingress master entry: %v", err)
		return fmt.Errorf("failed to get ingress master entry: %v", err)
	}

	if len(rows) == 0 {
		eLog.LogInfo("No ingress master entry found")
	}

	for _, row := range rows {
		eLog.LogInfo("Found ingress master entry: %v", row)
	}

	m.V = rows
	return nil
}

func (om *OrchestrationMgr) TriggerLocationDeboarding(regionId, custId int64) error {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	eLog.LogInfo(fmt.Sprintf("Triggering region Deboarding for Region %v, Customer %v", regionId, custId))

	// Input validation
	if err := om.validateDeboardInputs(regionId, custId); err != nil {
		eLog.LogError("Input validation failed: %v", err)
		return err
	}

	dbAccessor := db.GetDbAccessor()

	// Get ingress cluster entry
	ingressClusterEntry, err := om.getIngressClusterCfgEntry(dbAccessor, regionId, eLog)
	if err != nil {
		return err
	}

	regionMasterEntry, err := om.getRegionMasterEntry(dbAccessor, regionId, eLog)
	if err != nil {
		return err
	}

	// Get custMaster entry
	custMasterEntry, err := om.getCustMasterEntry(dbAccessor, custId, eLog)
	if err != nil {
		return err
	}

	clusterUrl := ingressClusterEntry.ClusterKubeURL
	if clusterUrl == "" {
		eLog.LogError("ClusterURL is empty for regionId=%v", regionId)
		return fmt.Errorf("cluster URL cannot be empty")
	}

	TenantIdStr := strconv.FormatInt(custMasterEntry.AcctID.Int64(), 10)
	attrs := attribute.NewSet(
		attribute.Key(TenantID).String(TenantIdStr),
		attribute.Key(TsgID).String(custMasterEntry.TenantServiceGroup.String()),
		attribute.Key(CustID).String(strconv.FormatInt(custId, 10)),
		attribute.Key(RegionID).String(strconv.FormatInt(regionId, 10)),
		attribute.Key(CloudProvider).String("GCP"),
	)
	om.metrics.TenantRegionOffBoardingEventTrigger.Add(context.TODO(), 1, metric.WithAttributeSet(attrs))

	entradaId := generateEntradaId(custId, regionId)
	cfg := pconfig.GetConfig()
	triggerReq := ArgoTriggerReq{}
	triggerReq.ApplicationNamespace = fmt.Sprintf("%s%s", entradaId, NAMESPACE_SUFFIX)
	triggerReq.ApplicationName = fmt.Sprintf("%s%s", entradaId, APP_SUFFIX)
	triggerReq.KubernetesClusterURL = string(clusterUrl)
	eLog.LogInfo("sending triggerReq=%+v", triggerReq)
	eLog.LogInfo("sending triggerReq=%s", fmt.Sprintf("%s", triggerReq))

	url := cfg.ZtiController.ArgoServiceInfo.ArgoDeleteUrl
	ApiPath := cfg.ZtiController.ArgoServiceInfo.ArgoDeleteApiPath
	path := ApiPath + url

	eLog.LogInfo("path=%s", path)
	err = om.sendArgoRequest(err, path, triggerReq, eLog)
	if err != nil {
		return err
	}

	ingressMasterEntry, err := dbAccessor.GetIngressMasterEntryByCustIdAndRegionId(custId, regionId, eLog)
	if err != nil {
		eLog.LogError("Failed to get ingress master entry")
		return fmt.Errorf("failed to get ingress master entry: %v", err)
	}
	eLog.LogInfo("IngressMasterEntry: %v", ingressMasterEntry)

	// update DB
	custPrefix, err := common.GetDnsCustPrefix(custId)
	if err != nil {
		eLog.LogError("Failed to get dns cust prefix")
		return fmt.Errorf("failed to get dns cust prefix: %v", err)
	}
	info := dns.RecordInfo{
		CustPrefix: custPrefix,
		LocInfo: dns.LocationInfo{
			Region:   regionMasterEntry.NativeComputeRegionName.String(),
			Ip:       ingressMasterEntry.ExternalRegionalIP.String(),
			RegionId: regionId,
		},
	}

	eLog.LogInfo("delete dns record info=%v", info)
	err = om.DnsMgr.DeleteDnsRecord(context.TODO(), info)
	if err != nil {
		eLog.LogError("Failed to delete DNS record", "err", err)
		return fmt.Errorf("failed to delete DNS record: %v", err)
	}

	// delete ingress master entry
	err = dbAccessor.DeleteIngressMasterEntry(ingressMasterEntry, eLog)
	if err != nil {
		eLog.LogError("Failed to delete ingress master entry", "err", err)
		return fmt.Errorf("failed to delete ingress master entry: %v", err)
	}
	return nil
}
