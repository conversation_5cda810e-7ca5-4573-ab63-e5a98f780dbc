package orchestration

import (
	"context"
	"github.com/gorilla/mux"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
	"orchestrator/libs/go/cogs"
	"orchestrator/libs/go/utils"
	pconfig "orchestrator/zti_controller/infra/config"
	"orchestrator/zti_controller/infra/logger"
	"orchestrator/zti_controller/internal/argo"
	"orchestrator/zti_controller/internal/dns"
	"orchestrator/zti_controller/internal/event"
	"orchestrator/zti_controller/internal/otelemetry"
	"os"
	"sync"
	"time"
)

const (
	OrchPollInterval       = 30 * time.Second
	ORCHESTRATION_MGR_NAME = "Orchestration Manager"
)

type OrchestrationMgr struct {
	name     string
	HostName string
	ID       event.ConfStateBits
	chConfig chan event.ConfigEvent
	DnsMgr   *dns.DnsManager
	ArgoMgr  *argo.ArgoMgr
	metrics  *otelemetry.ZtiControllerMetrics
	sync.RWMutex
}

func NewOrchestrationMgr(mtlsR *mux.Router, dns *dns.DnsManager, argoMgr *argo.ArgoMgr) *OrchestrationMgr {
	mgr := OrchestrationMgr{
		name:     ORCHESTRATION_MGR_NAME,
		ID:       event.OrchMgrBit,
		chConfig: make(chan event.ConfigEvent, 100),
		HostName: os.Getenv("HOSTNAME"),
		DnsMgr:   dns,
		ArgoMgr:  argoMgr,
	}
	//logger.Info("Register orchestration Mgr Events ..")
	//mgr.RegisterEvents()

	// MTLS endpoints require client MTLS certs (verified by Istio)
	mgr.setMtlsEndpoints(mtlsR)
	return &mgr
}

func (om *OrchestrationMgr) Setup() error {
	cfg := pconfig.GetConfig()
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	eLog.LogInfo("OrchestrationMgr setup started")
	eLog.LogInfo("Startup config cfg.ZtiController=%+v", cfg.ZtiController)

	m := cogs.NewMachine(
		cogs.WithLogger(logger.GetGlobalLogger()),
		cogs.WithState(
			cogs.NewState(
				"pod_monitor_events",
				"Monitor for pod events",
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithPubSubSubscriber(
					"host-gpcs-dev-01", "entrada-pod-monitor-dev")),
					cogs.WithHandler(om.PodMonitorHandler)),
			),
		),
	)
	m.Start()

	// Get metrics handler
	om.metrics = otelemetry.GetMetricsHandler()

	attrs := attribute.NewSet(
		attribute.Key("pod_name").String(om.HostName),
		attribute.Key("namespace").String(os.Getenv("POD_NAMESPACE")),
		attribute.Key("region").String(os.Getenv(cfg.Region)),
		attribute.Key("env").String(os.Getenv(cfg.AwsEnv)),
		attribute.Key("env_type").String(os.Getenv(cfg.AwsEnvType)),
	)
	om.metrics.ZtiControllerSvcStart.Add(context.TODO(), 1, metric.WithAttributeSet(attrs))
	return nil
}

func (om *OrchestrationMgr) RegisterEvents() {
	for _, evt := range []event.EventID{
		event.EventTenantNotification,
	} {
		event.ZTIEventBus.Handlers[evt] = append(event.ZTIEventBus.Handlers[evt], om)
	}
}

func (om *OrchestrationMgr) GetChannel() chan event.ConfigEvent {
	return om.chConfig
}

func (om *OrchestrationMgr) GetID() event.ConfStateBits {
	return om.ID
}

func (om *OrchestrationMgr) Run(chQuit chan struct{}) {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	go func() {
		tickerPoll := time.NewTimer(OrchPollInterval)
		for {
			select {
			case <-tickerPoll.C:
				eLog.LogInfo("OrchestrationMgr Poll Check")
			case msg := <-om.chConfig:
				ack := false
				//logger.Infof("%s: config event received: %+v", om.name, msg)
				switch msg.ID {
				default:
					// ignore other messages
				}
				if ack {
					event.ZTIEventBus.Ack(msg, om.ID)
				}
			case <-chQuit:
				return
			}
		}
	}()
}
