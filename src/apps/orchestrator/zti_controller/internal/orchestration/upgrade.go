package orchestration

import (
	"encoding/json"
	"fmt"
	"github.com/gorilla/mux"
	"net/http"
	"orchestrator/libs/go/utils"
	"orchestrator/zti_controller/infra/logger"
)

func (om *OrchestrationMgr) upgradeTenant(w http.ResponseWriter, r *http.Request) {
	m := &logger.LogMeta{
		Code: http.StatusOK,
		Text: fmt.Sprintf("Invoking API: %s", logger.Caller()),
	}
	defer logger.LogAndReply(w, r, m)

	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	decoder := json.NewDecoder(r.Body)
	var req UpgradeReq
	err := decoder.Decode(&req)
	if err != nil {
		m.Code = http.StatusBadRequest
		m.Err = err
		return
	}

	eLog.LogInfo("upgrade req=%+v", req)

	// TODO
	return
}

func (om *OrchestrationMgr) getUpgradeInfo(w http.ResponseWriter, r *http.Request) {
	m := &logger.LogMeta{
		Code:   http.StatusOK,
		Text:   fmt.Sprintf("Invoking API: %s", logger.Caller()),
		CustID: mux.Vars(r)["id"],
	}

	defer logger.LogAndReply(w, r, m)
	if m.CustID == "" {
		m.Code = http.StatusBadRequest
		m.Err = fmt.Errorf("missing tenant id")
		return
	}

	// TODO
	return
}
