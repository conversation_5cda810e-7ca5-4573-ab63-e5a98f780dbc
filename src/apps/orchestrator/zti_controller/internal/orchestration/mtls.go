package orchestration

import (
	"encoding/json"
	"fmt"
	"github.com/gorilla/mux"
	"net/http"
	"orchestrator/libs/go/utils"
	"orchestrator/zti_controller/infra/logger"
	"strconv"
)

func (om *OrchestrationMgr) setMtlsEndpoints(r *mux.Router) {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	eLog.LogInfo("setEndpoints Locations")
	r.HandleFunc("/cust/{id}/locations", om.enableTenantLocations).Methods(http.MethodPost)
	r.HandleFunc("/cust/{id}/locations", om.getTenantLocations).Methods(http.MethodGet)
	r.Handle<PERSON>unc("/cust/{id}/locations/{rid}", om.deleteTenantLocations).Methods(http.MethodDelete)

	eLog.LogInfo("setEndpoints CustConfig")
	r.<PERSON>le<PERSON>("/cust-config", om.updateIngressCustConfig).Methods(http.MethodPost)
	r.HandleFunc("/cust/{id}/cust-config", om.getIngressCustConfig).Methods(http.MethodGet)

	r.HandleFunc("/cust/{id}/trigger-config-notify", om.triggerNotifyConfig).Methods(http.MethodPost)

	r.HandleFunc("/upgrade", om.upgradeTenant).Methods(http.MethodPost)
	r.HandleFunc("/upgrade/{id}", om.getUpgradeInfo).Methods(http.MethodGet)
	eLog.LogInfo("setEndpoints exit")
}

func (om *OrchestrationMgr) getTenantLocations(w http.ResponseWriter, r *http.Request) {
	m := &logger.LogMeta{
		Code:   http.StatusOK,
		Text:   fmt.Sprintf("Invoking API: %s", logger.Caller()),
		CustID: mux.Vars(r)["id"],
	}

	defer logger.LogAndReply(w, r, m)
	if m.CustID == "" {
		m.Code = http.StatusBadRequest
		m.Err = fmt.Errorf("missing tenant id")
		return
	}

	err := om.GetTenantLocations(m.CustID, m)
	if err != nil {
		m.Code = http.StatusBadRequest
		m.Err = err
		return
	}
	return
}

// @Summary enable an TenantLocation for the tenant
// @ID add-locations
// @Accept  json
// @Produce  json
// @Param Tenant location body LocationReq true "LocationReq"
// @Success 200 {array} TenantLocation
// @Header 200 {string} Token "qwerty"
// @Failure 400
// @Failure 428
// @Failure 500 {object} string
// @Router /zti/iapi/v1/cust/{custid}/locations [post]
func (om *OrchestrationMgr) enableTenantLocations(w http.ResponseWriter, r *http.Request) {
	m := &logger.LogMeta{
		Code:   http.StatusOK,
		Text:   fmt.Sprintf("Invoking API: %s", logger.Caller()),
		CustID: mux.Vars(r)["id"],
	}
	defer logger.LogAndReply(w, r, m)

	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	decoder := json.NewDecoder(r.Body)
	var req LocationReq
	err := decoder.Decode(&req)
	if err != nil {
		m.Code = http.StatusBadRequest
		m.Err = err
		return
	}

	if m.CustID == "" {
		m.Code = http.StatusBadRequest
		m.Err = fmt.Errorf("missing tenant id")
		return
	}

	if req.UUID != "" {
		eLog.SetLoggerTag(req.UUID)
		eLog.LogInfo("Logger tag set for tenant %v with UUID %s", m.CustID, req.UUID)
	} else {
		eLog.LogInfo("No logger tag provided for tenant %v", m.CustID)
	}

	eLog.LogInfo("UUID Test for tenant %v", eLog.TraceID)

	eLog.LogInfo("req=%", req)

	custId, err := strconv.Atoi(m.CustID)
	if err != nil {
		eLog.LogError("Invalid custID format: %v, CustId=%s", err, m.CustID)
		m.Code = http.StatusBadRequest
		return
	}

	err = om.TriggerLocationOnboarding(req, int64(custId), eLog)
	if err != nil {
		m.Code = http.StatusBadRequest
		m.Err = err
		return
	}

	m.V = map[string]string{"msg": fmt.Sprintf("tenant orch triggered on region %+v", req.ComputeRegionId)}
	return
}

// TODO get the edge location details as well.
func (om *OrchestrationMgr) deleteTenantLocations(w http.ResponseWriter, r *http.Request) {
	m := &logger.LogMeta{
		Code:   http.StatusOK,
		Text:   fmt.Sprintf("Invoking API: %s", logger.Caller()),
		CustID: mux.Vars(r)["id"],
	}
	defer logger.LogAndReply(w, r, m)

	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	if m.CustID == "" {
		m.Code = http.StatusBadRequest
		m.Err = fmt.Errorf("missing tenant id")
		return
	}
	custId, err := strconv.Atoi(m.CustID)
	if err != nil {
		eLog.LogError("Invalid custID format: %v, CustId=%s", err, m.CustID)
		m.Code = http.StatusBadRequest
		return
	}

	ComputeRegionId := mux.Vars(r)["rid"]
	if ComputeRegionId == "" {
		m.Code = http.StatusBadRequest
		m.Err = fmt.Errorf("missing region id")
		return
	}
	regionId, err := strconv.Atoi(ComputeRegionId)
	if err != nil {
		eLog.LogError("Invalid custID format: %v, CustId=%s", err, m.CustID)
		m.Code = http.StatusBadRequest
		return
	}
	eLog.LogInfo("ComputeRegionId=%s", ComputeRegionId)

	err = om.TriggerLocationDeboarding(int64(regionId), int64(custId))
	if err != nil {
		m.Code = http.StatusBadRequest
		m.Err = err
		return
	}
}

// @Summary enable an TenantLocation for the tenant
// @ID add-locations
// @Accept  json
// @Produce  json
// @Param Tenant location body LocationReq true "LocationReq"
// @Success 200 {array} TenantLocation
// @Header 200 {string} Token "qwerty"
// @Failure 400
// @Failure 428
// @Failure 500 {object} string
// @Router /zti/iapi/v1/cust/{custid}/triggerNotifyConfig [post]
func (om *OrchestrationMgr) triggerNotifyConfig(w http.ResponseWriter, r *http.Request) {
	m := &logger.LogMeta{
		Code:   http.StatusOK,
		Text:   fmt.Sprintf("Invoking API: %s", logger.Caller()),
		CustID: mux.Vars(r)["id"],
	}
	defer logger.LogAndReply(w, r, m)

	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	if m.CustID == "" {
		m.Code = http.StatusBadRequest
		m.Err = fmt.Errorf("missing tenant id")
		return
	}

	custId, err := strconv.Atoi(m.CustID)
	if err != nil {
		eLog.LogError("Invalid custID format: %v, CustId=%s", err, m.CustID)
		m.Code = http.StatusBadRequest
		return
	}

	err = om.TriggerNotifyConfigHelper(r.Context(), int64(custId))
	if err != nil {
		m.Code = http.StatusBadRequest
		m.Err = err
		return
	}

	m.V = map[string]string{"msg": fmt.Sprintf("TriggerNotifyConfig %+v", m.CustID)}
	return
}
