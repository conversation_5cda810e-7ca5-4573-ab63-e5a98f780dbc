package service

import (
	"context"
	"fmt"
	"github.com/gorilla/mux"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/cors"
	"go.panw.local/pangolin/clogger"
	"net/http"
	"orchestrator/libs/go/utils"
	pconfig "orchestrator/zti_controller/infra/config"
	"orchestrator/zti_controller/infra/logger"
	"orchestrator/zti_controller/internal/argo"
	"orchestrator/zti_controller/internal/dns"
	"orchestrator/zti_controller/internal/event"
	"orchestrator/zti_controller/internal/healthz"
	"orchestrator/zti_controller/internal/orchestration"
	"orchestrator/zti_controller/internal/otelemetry"
)

type ServiceInfo struct {
	Version string `json:"version"`
	Build   string `json:"build"`
}

type ControllerService struct {
	name             string
	ID               event.ConfStateBits
	info             ServiceInfo
	Router           *mux.Router
	mtlsCl           *http.Client
	healthz          *healthz.Healthz
	gLogger          *clogger.Clogger
	orchestrationMgr *orchestration.OrchestrationMgr
	dnsMgr           *dns.DnsManager
	argoMgr          *argo.ArgoMgr
	clients          []event.EventHandler
	chConfig         chan event.ConfigEvent // config notification
}

func NewControllerService(ctx context.Context, g *clogger.Clogger) *ControllerService {
	s := &ControllerService{
		name:     "Controller service",
		gLogger:  g,
		chConfig: make(chan event.ConfigEvent, 500),
	}

	// Init Event bus
	//event.InitEventBus()

	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	cfg := healthz.HealthzConfig{}
	s.healthz = healthz.NewHealthzHandler(&cfg)
	s.Router = mux.NewRouter()
	s.Router.StrictSlash(true)
	mtlsRouter := s.Router.PathPrefix("/zti/iapi/v1").Subrouter()

	// register prometheus exporter
	s.Router.Handle("/metrics", promhttp.Handler())
	err := otelemetry.RegisterMetrics()
	if err != nil {
		eLog.LogFatal("Fatal Error %v metrics failed", err)
	}

	// init sub modules
	s.dnsMgr = dns.NewDnsMgr(mtlsRouter)
	if err := s.dnsMgr.Setup(); err != nil {
		s.gLogger.LogFatal("Failed to setup dnsMgr", "err", err)
	}
	eLog.LogInfo("Initializing DNS manager")

	s.argoMgr = argo.NewArgoMgr(mtlsRouter, s.dnsMgr)
	if err := s.argoMgr.Setup(); err != nil {
		s.gLogger.LogFatal("Failed to setup argoMgr", "err", err)
	}
	eLog.LogInfo("Initializing Argo Manager")

	s.orchestrationMgr = orchestration.NewOrchestrationMgr(mtlsRouter, s.dnsMgr, s.argoMgr)
	if err := s.orchestrationMgr.Setup(); err != nil {
		s.gLogger.LogFatal("Failed to setup orchestrationMgr", "err", err)
	}
	eLog.LogInfo("Initializing Orchestration Manager")

	s.healthz.Start()
	s.healthz.SetReady()
	return s
}

func (s *ControllerService) Name() string {
	return s.name
}

func (s *ControllerService) StartService(chQuit chan struct{}) {
	s.Run(chQuit)
}

func (s *ControllerService) Run(chQuit chan struct{}) {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	for _, client := range s.clients {
		client.Run(chQuit)
	}

	cfg := pconfig.GetConfig()
	h := cfg.ZtiController.Service.Host
	p := cfg.ZtiController.Service.Port
	host := fmt.Sprintf("%s:%d", h, p)

	copts := cors.Options{
		AllowedMethods: []string{"POST", "GET", "OPTIONS", "PUT", "DELETE"},
		AllowedHeaders: []string{"Content-Type", "Content-Length", "Accept-Encoding", "Authorization"},
	}
	//copts.AllowedOrigins = append(copts.AllowedOrigins, s.cfg.Service.Origins...)

	//s.gLogger.LogInfo("cors origins=%+v", s.cfg.Service.Origins)
	c := cors.New(copts)
	handler := c.Handler(s.Router)

	go func() {
		if err := http.ListenAndServe(host, handler); err != nil {
			s.gLogger.LogFatal("%s failed to run on %s, err %v!!!", s.name, host, err)
		}
	}()
	eLog.LogInfo("%s runs on %s", s.name, host)
}
