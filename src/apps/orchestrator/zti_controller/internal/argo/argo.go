package argo

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"github.com/gorilla/mux"
	"io"
	"net/http"
	"orchestrator/libs/go/dbaccess/models/ingress_master"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/utils"
	"orchestrator/zti_controller/infra/db"
	"orchestrator/zti_controller/infra/logger"
	"orchestrator/zti_controller/internal/common"
	"orchestrator/zti_controller/internal/dns"
	"orchestrator/zti_controller/internal/event"
	"os"
	"strconv"
	"strings"
	"sync"
)

// Configuration for the API calls
const (
	ARGO_MGR_NAME = "Argo Manager"

	// TODO move to config
	baseURL     = "https://pa-core-api-dev2-1.api.panclouddev.com"
	authURL     = "/api/v1/session"
	appURL      = "/api/v1/applications"
	resourceURL = "/resource-tree"
	username    = "admin"
)

type ArgoMgr struct {
	name     string
	HostName string
	ID       event.ConfStateBits
	chConfig chan event.ConfigEvent
	dnsMgr   *dns.DnsManager
	sync.RWMutex
}

func NewArgoMgr(mtlsR *mux.Router, dns *dns.DnsManager) *ArgoMgr {
	mgr := ArgoMgr{
		name:     ARGO_MGR_NAME,
		ID:       event.ArgoMgrBit,
		chConfig: make(chan event.ConfigEvent, 100),
		HostName: os.Getenv("HOSTNAME"),
		dnsMgr:   dns,
	}
	mgr.setMtlsEndpoints(mtlsR)
	return &mgr
}

func (am *ArgoMgr) Setup() error {
	return nil
}

//func (am *ArgoMgr) processArgoCallback(w http.ResponseWriter, r *http.Request) {
//	m := &logger.LogMeta{
//		Code: http.StatusOK,
//		Text: fmt.Sprintf("Invoking API: %s", logger.Caller()),
//	}
//	defer logger.LogAndReply(w, r, m)
//
//	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
//
//	decoder := json.NewDecoder(r.Body)
//	var req ArgoCallBackReq
//	err := decoder.Decode(&req)
//	if err != nil {
//		m.Code = http.StatusBadRequest
//		m.Err = err
//		return
//	}
//
//	eLog.LogInfo("req=%s", req)
//
//	ip, status, err := am.FetchAndUpdateExternalIP(req)
//	if err != nil {
//		m.Code = http.StatusBadRequest
//		m.Err = err
//		return
//	}
//	m.V = map[string]string{"ip": ip, "status": status}
//	return
//}

func (am *ArgoMgr) FetchAndUpdateExternalIP(req ArgoCallBackReq) (globalfqdn, status string, err error) {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	pwd, err := am.GetArgoCDAdminPassword(context.TODO(), "")
	if err != nil {
		eLog.LogError("Failed to get ArgoCD admin password")
		return "", "", err
	}

	regionalExtIP, healthStatus, err := fetchExternalIP(username, pwd, req.ArgoApplication)
	if err != nil {
		eLog.LogError("Failed to fetch external IP")
		return "", "", err
	}

	if !strings.EqualFold(healthStatus, "healthy") {
		eLog.LogError("Argo application health status is not healthy: %s", healthStatus)
		return "", healthStatus, fmt.Errorf("argo application health status is not healthy: %s", healthStatus)
	}

	// update DB
	custPrefix, err := common.GetDnsCustPrefix(req.CustId)
	if err != nil {
		eLog.LogError("Failed to get dns cust prefix")
		return "", "", err
	}

	info := dns.RecordInfo{
		CustPrefix: custPrefix,
		LocInfo: dns.LocationInfo{
			Region:   req.RegionName,
			Ip:       regionalExtIP,
			RegionId: req.RegionId,
		},
	}

	eLog.LogInfo("Creating FQDN Record info =%+v", info)
	fqdn, globalFqdn, err := am.dnsMgr.CreateOrUpdateDnsRecord(context.TODO(), info)
	if err != nil {
		eLog.LogError("Failed to update DNS record err=%+v", err)
		return "", "", err
	}

	eLog.LogInfo("Regional fqdn=%s Global Fqdn=%s", fqdn, globalFqdn)

	dbAccessor := db.GetDbAccessor()

	// Get ingress configuration for the customer
	ingressMasterEntry, err := dbAccessor.GetIngressMasterEntryByCustIdAndRegionId(int64(req.CustId), req.RegionId, eLog)
	if err != nil {
		eLog.LogError("Failed to get ingress master entry")
		return "", "", err
	}
	eLog.LogInfo("IngressMasterEntry: %v", ingressMasterEntry)

	r := &ingress_master.Row{}
	if ingressMasterEntry.RegionalFQDN == "" || string(ingressMasterEntry.RegionalFQDN) != fqdn {
		//update DNS entry
		r.CustID = sql.Int64(req.CustId)
		r.ComputeRegionID = sql.String(strconv.FormatInt(req.RegionId, 10))
		r.RegionalFQDN = sql.String(fqdn)
		r.ExternalRegionalIP = sql.String(regionalExtIP)
		err = dbAccessor.UpdateFQDNinIngressMaster(r, eLog)
		if err != nil {
			eLog.LogError("Failed to update ingress master entry")
			return "", "", err
		}
		eLog.LogInfo("Updated FQDN Ingress Master: %v", err)
	}

	return globalFqdn, "", nil
}

func fetchExternalIP(username, password, argoApp string) (string, string, error) {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	// --- Step 0: Create a custom HTTP client ---
	// This client is configured to ignore SSL certificate verification,
	// which is equivalent to curl's -k or --insecure flag.
	// WARNING: This is insecure and should only be used in development
	// or when you trust the endpoint.
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// --- Step 1: Get the authentication token ---
	eLog.LogInfo("Attempting to get authentication token...")
	token, err := getAuthToken(client, username, password)
	if err != nil {
		eLog.LogError("Error getting auth token: %v", err)
		return "", "", err
	}
	eLog.LogInfo("Successfully retrieved token")
	//eLog.LogInfo("Successfully retrieved token: %s...", token[:20]) // Print first 20 chars for verification

	// --- Step 3: Use the token to get and parse the resource data ---
	eLog.LogInfo("Fetching resource data with the token...")
	resourceData, err := getResourceData(client, token, argoApp)
	if err != nil {
		eLog.LogError("Error getting resource data: %v", err)
		return "", "", err
	}
	eLog.LogInfo("Successfully fetched and parsed resource data")
	//eLog.LogInfo("Successfully fetched and parsed resource data. resourceData: %+v", resourceData)

	// --- Step 4: Process the data to find and display healthy services ---
	return processResponse(resourceData)
}

// getAuthToken handles the authentication and token retrieval.
func getAuthToken(client *http.Client, username, password string) (string, error) {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	// Create the request body
	loginData := LoginRequest{
		Username: username,
		Password: password,
	}
	requestBody, err := json.Marshal(loginData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal login data: %w", err)
	}

	// Create the POST request
	req, err := http.NewRequest("POST", baseURL+authURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// Execute the request
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	// Read and check the response
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("authentication failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Unmarshal the response to get the token
	var loginResponse LoginResponse
	if err := json.Unmarshal(bodyBytes, &loginResponse); err != nil {
		return "", fmt.Errorf("failed to unmarshal token response: %w", err)
	}

	if loginResponse.Token == "" {
		return "", fmt.Errorf("token was not found in the response")
	}

	eLog.LogInfo("Successfully authenticated with token: %s", loginResponse.Token)
	return loginResponse.Token, nil
}

// Response is the top-level structure of the JSON object.
type Response struct {
	Nodes []Node `json:"nodes"`
}

// Health represents the health status of a node.
type Health struct {
	Status string `json:"status"`
}

// Node represents an object within the "nodes" array.
// The Health field has been added to this struct.
type Node struct {
	Kind           string         `json:"kind"`
	Name           string         `json:"name"`
	NetworkingInfo NetworkingInfo `json:"networkingInfo"`
	Health         Health         `json:"health"`
}

// NetworkingInfo contains network-related details, including ingress points.
type NetworkingInfo struct {
	Ingress []Ingress `json:"ingress"`
}

// Ingress represents an ingress point with an IP address.
type Ingress struct {
	IP string `json:"ip"`
}

func getResourceData(client *http.Client, token, argoApp string) (*Response, error) {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	// Create the GET request for the resource
	url := baseURL + appURL + "/" + argoApp + resourceURL
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create resource request: %w", err)
	}

	// Set the Authorization header with the Bearer token
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))

	// Execute the request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute resource request: %w", err)
	}
	defer resp.Body.Close()

	// Read and check the response status
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read resource response body: %w", err)
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("resource request failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Unmarshal the JSON response into our Go structs.
	var response Response
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal resource JSON: %w", err)
	}

	eLog.LogInfo("Successfully fetched and parsed resource data.")
	return &response, nil
}

// iterates through the parsed data and prints the desired information.
func processResponse(response *Response) (string, string, error) {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	eLog.LogInfo("--- Ingress IPs for Healthy Services ---")

	foundServices := false
	// Iterate over each node in the response.
	for _, node := range response.Nodes {
		eLog.LogInfo("node: %+v", node)
		// We are checking for two conditions:
		// 1. The node kind must be "Service".
		// 2. The service's health status must be "Healthy".
		if node.Kind == "Service" && node.Health.Status == "Healthy" {
			if len(node.NetworkingInfo.Ingress) > 0 {
				foundServices = true
				// Iterate over each ingress point for the service.
				for _, ingress := range node.NetworkingInfo.Ingress {
					// Print the service name and its ingress IP.
					eLog.LogInfo("- Service '%s': %s", node.Name, ingress.IP)
					return ingress.IP, node.Health.Status, nil
				}
			}
		}
	}

	if !foundServices {
		eLog.LogInfo("No healthy services with ingress IPs were found.")
	}
	return "", "", fmt.Errorf("no healthy services with ingress IPs were found")
}
