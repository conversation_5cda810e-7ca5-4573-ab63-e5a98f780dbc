package dns

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/route53"
	r53types "github.com/aws/aws-sdk-go-v2/service/route53/types"
	"orchestrator/libs/go/dbaccess/models/region_master"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/utils"
	"orchestrator/zti_controller/infra/db"
	"orchestrator/zti_controller/infra/logger"
	"strings"
	"time"
)

const (
	maxR53Records = 300
	defaultWait   = 10 * time.Second
	maxRetries    = 10
	maxThrottles  = 4
)

type Coordinates struct {
	Latitude  string
	Longitude string
}

type LatLongCoordinates struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// nonRetryableError is a wrapper to indicate an error should not be retried.
type nonRetryableError struct{ error }

func (e nonRetryableError) Unwrap() error { return e.error }

// GetGCPCoords dynamically "fetches" coordinates from the internal map.
func GetGCPCoords(region string) (Coordinates, error) {
	dbAccessor := db.GetDbAccessor()
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	dbCtx := &sql.DbContext{DbConn: dbAccessor.GetDbConn(), Logger: eLog}

	entry, err := region_master.GetRowByNativeRegionName(dbCtx, "gcp", region)
	if err != nil {
		return Coordinates{}, err
	}

	eLog.LogInfo("Entry =%+v", entry)

	var latLongInfo LatLongCoordinates
	if entry.Metadata != "" {
		err := json.Unmarshal([]byte(entry.Metadata), &latLongInfo)
		if err != nil {
			eLog.LogError("Error unmarshalling distance mapping coordinates", err)
			return Coordinates{}, err
		}
	}

	c := Coordinates{
		Latitude:  fmt.Sprintf("%.2f", latLongInfo.Latitude),
		Longitude: fmt.Sprintf("%.2f", latLongInfo.Longitude),
	}
	eLog.LogInfo("Region: %s GetGCPCoords c: %+v", region, c)
	return c, nil
}

// isTokenError checks for common AWS token-related errors.
func isTokenError(err error) bool {
	if err == nil {
		return false
	}
	msg := strings.ToLower(err.Error())
	return strings.Contains(msg, "expiredtoken") || strings.Contains(msg, "invalidclienttokenid")
}

func getR53AWSPartition() string {
	return "aws"
}

// retryWithBackoff implements a retry mechanism with exponential backoff.
func retryWithBackoff(maxRetries int, initialDelay time.Duration, fn func() error) error {
	var err error
	delay := initialDelay
	for i := 0; i < maxRetries; i++ {
		err = fn()
		if err == nil {
			return nil // Success
		}

		var nre nonRetryableError
		if errors.As(err, &nre) {
			return nre.Unwrap() // Do not retry
		}

		if i < maxRetries-1 {
			time.Sleep(delay)
			delay *= 2 // Exponential backoff
		}
	}
	return err // Return the last error
}

// changeResourceRecordSets applies a batch of changes to a hosted zone.
func (dm *DnsManager) ChangeResourceRecordSets(ctx context.Context, changeBatch *r53types.ChangeBatch) error {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	eLog.LogInfo("Applying ChangeBatch request: %+v", changeBatch)
	input := &route53.ChangeResourceRecordSetsInput{
		HostedZoneId: aws.String(dm.r53ZoneID),
		ChangeBatch:  changeBatch,
	}

	err := retryWithBackoff(maxRetries, defaultWait, func() error {
		if dm.r53Client == nil {
			if err := dm.r53ClientConstruct(ctx); err != nil {
				return fmt.Errorf("failed to construct R53 client: %w", err)
			}
		}
		_, err := dm.r53Client.ChangeResourceRecordSets(ctx, input)
		if err != nil {
			eLog.LogError("Error changing resource record sets: %v", err)
			if isTokenError(err) {
				eLog.LogError("Token expired, re-initializing R53 client.")
				dm.r53ClientConstruct(ctx) // Ignoring error for simplicity
				return err                 // Retryable
			}
			var invalidChangeBatch *r53types.InvalidChangeBatch
			if errors.As(err, &invalidChangeBatch) {
				eLog.LogError("InvalidChangeBatch error: %v", err)
				return nonRetryableError{err}
			}
			return err
		}
		return nil
	})

	if err != nil {
		eLog.LogError("Error changing resource record sets: %v", err)
		return err
	}

	eLog.LogInfo("Successfully changed resource record sets.")
	return nil
}

// DeleteRecordSetByName finds and deletes all record sets matching a specific name.
// It adopts the robust error handling and method-based style of your example.
func (dm *DnsManager) DeleteRecordSetByName(ctx context.Context, recordName string) error {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	eLog.LogInfo("Attempting to find record set '%s' in hosted zone '%s'\n", recordName, dm.r53ZoneID)

	// Step 1: Find the exact record(s) to delete using a paginator.
	paginator := route53.NewListResourceRecordSetsPaginator(dm.r53Client, &route53.ListResourceRecordSetsInput{
		HostedZoneId:    aws.String(dm.r53ZoneID),
		StartRecordName: aws.String(recordName),
	})

	var recordsToDelete []r53types.ResourceRecordSet
	for paginator.HasMorePages() {
		page, err := paginator.NextPage(ctx)
		if err != nil {
			// This part of the logic is less likely to have token errors, but we wrap for consistency.
			return fmt.Errorf("failed to list resource record sets: %w", err)
		}
		for _, record := range page.ResourceRecordSets {
			if aws.ToString(record.Name) == recordName {
				recordsToDelete = append(recordsToDelete, record)
			}
		}
	}

	if len(recordsToDelete) == 0 {
		eLog.LogInfo("No record sets found with the name '%s', operation successful.", recordName)
		return nil // Not an error if the record doesn't exist.
	}
	eLog.LogInfo("Found %d record set(s) to delete.\n", len(recordsToDelete))

	// Step 2: Build the ChangeBatch with the records we found.
	var changes []r53types.Change
	for _, record := range recordsToDelete {
		changes = append(changes, r53types.Change{
			Action:            r53types.ChangeActionDelete,
			ResourceRecordSet: &record, // Use the exact record data found.
		})
	}
	changeBatch := &r53types.ChangeBatch{
		Comment: aws.String(fmt.Sprintf("Programmatically deleting record set %s", recordName)),
		Changes: changes,
	}

	// Step 3: Execute the delete request with retry logic.
	eLog.LogInfo("Applying ChangeBatch request for deletion: %+v", changeBatch)
	input := &route53.ChangeResourceRecordSetsInput{
		HostedZoneId: aws.String(dm.r53ZoneID),
		ChangeBatch:  changeBatch,
	}

	// This retry logic mirrors the structure of your provided example.
	err := retryWithBackoff(3, 2*time.Second, func() error {
		if dm.r53Client == nil {
			if err := dm.r53ClientConstruct(ctx); err != nil {
				return fmt.Errorf("failed to construct R53 client: %w", err)
			}
		}
		_, err := dm.r53Client.ChangeResourceRecordSets(ctx, input)
		if err != nil {
			eLog.LogInfo("Error changing resource record sets on attempt: %v", err)
			if isTokenError(err) {
				eLog.LogInfo("Token expired, re-initializing R53 client.")
				_ = dm.r53ClientConstruct(ctx) // Re-initialize client
				return err                     // Return original error to trigger retry
			}
			var invalidChangeBatch *r53types.InvalidChangeBatch
			if errors.As(err, &invalidChangeBatch) {
				eLog.LogInfo("Non-retryable InvalidChangeBatch error: %v", err)
				return nonRetryableError{err} // Stop retrying for this error
			}
			return err // Return other errors to trigger retry
		}
		return nil // Success, stop retrying
	})

	if err != nil {
		eLog.LogInfo("Failed to delete record sets after retries: %v", err)
		return err
	}

	eLog.LogInfo("Successfully deleted resource record sets.")
	return nil
}
