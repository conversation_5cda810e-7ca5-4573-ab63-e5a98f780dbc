package dns

import (
	"encoding/json"
	"fmt"
	"github.com/gorilla/mux"
	"net/http"
	"orchestrator/libs/go/utils"
	"orchestrator/zti_controller/infra/logger"
)

type FqdnReq struct {
	Fqdn string `json:"fqdn"`
}

func (dm *DnsManager) setMtlsEndpoints(r *mux.Router) {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	eLog.LogInfo("setEndpoints callback")

	// debug endpoint
	r.Handle<PERSON>unc("/delete-fqdn", dm.deleteFqdnEntry).Methods(http.MethodDelete)
	eLog.LogInfo("setEndpoints exit")
}

func (dm *DnsManager) deleteFqdnEntry(w http.ResponseWriter, r *http.Request) {
	m := &logger.LogMeta{
		Code: http.StatusOK,
		Text: fmt.Sprintf("Invoking API: %s", logger.Caller()),
	}
	defer logger.LogAndReply(w, r, m)

	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	decoder := json.NewDecoder(r.Body)
	var req FqdnReq
	err := decoder.Decode(&req)
	if err != nil {
		m.Code = http.StatusBadRequest
		m.Err = err
		return
	}

	eLog.LogInfo("deleteFqdnEntry req=%+v", req)

	err = dm.DeleteRecordSetByName(r.Context(), req.Fqdn)
	if err != nil {
		m.Code = http.StatusBadRequest
		m.Err = err
		return
	}
}
