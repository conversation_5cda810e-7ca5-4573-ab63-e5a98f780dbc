package dns

import (
	"context"
	"errors"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials/stscreds"
	"github.com/aws/aws-sdk-go-v2/service/route53"
	r53types "github.com/aws/aws-sdk-go-v2/service/route53/types"
	"github.com/aws/aws-sdk-go-v2/service/sts"
	"github.com/dgraph-io/ristretto"
	"github.com/gorilla/mux"
	"orchestrator/libs/go/utils"
	pconfig "orchestrator/zti_controller/infra/config"
	"orchestrator/zti_controller/infra/logger"
	"orchestrator/zti_controller/internal/common"
	"orchestrator/zti_controller/internal/event"
	"os"
	"strings"
	"sync"
)

const (
	DNS_MGR_NAME = "DNS Manager"
)

// TODO health check for DNS entries

// RecordInfo holds the data needed to create a global DNS record.
type LocationInfo struct {
	Region   string
	Ip       string
	RegionId int64
}

type RecordInfo struct {
	CustPrefix string
	LocInfo    LocationInfo
}

type CustInfo struct {
	Region       string
	CustomerName string
}

type DnsManager struct {
	name     string
	HostName string
	ID       event.ConfStateBits
	chConfig chan event.ConfigEvent
	cache    *ristretto.Cache
	sync.RWMutex

	r53ZoneName string
	r53Account  string
	account     string
	r53RoleARN  string
	r53ZoneID   string
	stsClient   *sts.Client
	r53Client   *route53.Client
}

func NewDnsMgr(mtlsR *mux.Router) *DnsManager {
	mgr := DnsManager{
		name:     DNS_MGR_NAME,
		ID:       event.OrchMgrBit,
		chConfig: make(chan event.ConfigEvent, 100),
		HostName: os.Getenv("HOSTNAME"),
	}

	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	eLog.LogInfo("Initializing DNS manager")

	mgr.setMtlsEndpoints(mtlsR)
	return &mgr
}

func (dm *DnsManager) Setup() error {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	cfg := pconfig.GetConfig()

	// Remove the 'a' prefix from the string.
	r53Account := strings.TrimPrefix(cfg.ZtiController.R53Info.Route53acct, "a")
	dm.r53Account = r53Account
	dm.r53ZoneName = cfg.ZtiController.R53Info.EntradaDomain

	ctx := context.Background()
	awsCfg, err := config.LoadDefaultConfig(context.Background())
	if err != nil {
		return fmt.Errorf("failed to load AWS config: %w", err)
	}

	dm.stsClient = sts.NewFromConfig(awsCfg)
	callerID, err := dm.stsClient.GetCallerIdentity(ctx, &sts.GetCallerIdentityInput{})
	if err != nil {
		return fmt.Errorf("failed to get caller identity: %w", err)
	}
	arnParts := strings.Split(aws.ToString(callerID.Arn), ":")
	if len(arnParts) < 5 {
		return fmt.Errorf("invalid ARN format: %s", aws.ToString(callerID.Arn))
	}
	dm.account = arnParts[4]

	awsR53Partition := getR53AWSPartition()
	dm.r53RoleARN = fmt.Sprintf("arn:%s:iam::%s:role/Route53_Policy_Export", awsR53Partition, dm.r53Account)
	if err := dm.r53ClientConstruct(ctx); err != nil {
		return err
	}

	eLog.LogInfo("Setting up r53 client")
	zoneID, err := dm.r53GetZoneID(ctx)
	if err != nil {
		return err
	}
	dm.r53ZoneID = zoneID
	eLog.LogInfo("ZoneId=%s", zoneID)
	return nil
}

// Creates the Route 53 client, assuming a role if necessary.
func (dm *DnsManager) r53ClientConstruct(ctx context.Context) error {
	awsCfg, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		return fmt.Errorf("failed to load AWS config: %w", err)
	}

	var r53Client *route53.Client
	creds := stscreds.NewAssumeRoleProvider(dm.stsClient, dm.r53RoleARN)
	awsCfg.Credentials = aws.NewCredentialsCache(creds)
	r53Client = route53.NewFromConfig(awsCfg)
	dm.r53Client = r53Client
	return nil
}

// r53GetZoneID finds the hosted zone ID for the configured domain.
func (dm *DnsManager) r53GetZoneID(ctx context.Context) (string, error) {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	dnsName := dm.r53ZoneName + "."
	var response *route53.ListHostedZonesByNameOutput
	var err error

	err = retryWithBackoff(maxRetries, defaultWait, func() error {
		response, err = dm.r53Client.ListHostedZonesByName(ctx, &route53.ListHostedZonesByNameInput{
			DNSName: aws.String(dnsName),
		})
		if err != nil {
			eLog.LogError("Failed to list hosted zones", "err", err)
			return err
		}
		return nil
	})

	for _, z := range response.HostedZones {
		if aws.ToString(z.Name) == dnsName {
			eLog.LogInfo("Zone ID for domain %s is: %s", dnsName, aws.ToString(z.Id))
			// The ID is in the format /hostedzone/ZONEID
			return strings.Replace(aws.ToString(z.Id), "/hostedzone/", "", 1), nil
		}
	}

	return "", fmt.Errorf("gpcs domain %s is not registered", dm.r53ZoneName)
}

// builds the FQDN and calls the context to update Route 53.
func (dm *DnsManager) CreateOrUpdateRegionalDNSRecord(ctx context.Context, info RecordInfo) (string, error) {
	region := info.LocInfo.Region
	ip := info.LocInfo.Ip
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	eLog.LogInfo("Updating DNS record from NLB in region: %s, with IP address: %s", region, ip)
	fqdn, err := common.GetRegionalFQDN(info.LocInfo.RegionId, region, info.CustPrefix, dm.r53ZoneName)
	if err != nil {
		return "", err
	}
	eLog.LogInfo("Entrada FQDN: %s; IP: %s", fqdn, ip)
	return dm.UpdateNodeFQDN(ctx, fqdn, ip, "")
}

// UpdateNodeFQDN creates or updates a weighted 'A' record for a node.
func (dm *DnsManager) UpdateNodeFQDN(ctx context.Context, nodeFQDN, publicIP, healthCheckID string) (string, error) {
	setID := fmt.Sprintf("%s-%s", nodeFQDN[:min(4, len(nodeFQDN))], publicIP)
	ttl := int64(60)
	weight := int64(200)

	rrs := &r53types.ResourceRecordSet{
		Name:          aws.String(nodeFQDN),
		Type:          r53types.RRTypeA,
		SetIdentifier: aws.String(setID),
		Weight:        aws.Int64(weight),
		TTL:           aws.Int64(ttl),
		ResourceRecords: []r53types.ResourceRecord{
			{Value: aws.String(publicIP)},
		},
	}
	if healthCheckID != "" {
		rrs.HealthCheckId = aws.String(healthCheckID)
	}

	changeBatch := &r53types.ChangeBatch{
		Changes: []r53types.Change{
			{
				Action:            r53types.ChangeActionUpsert,
				ResourceRecordSet: rrs,
			},
		},
	}

	err := dm.ChangeResourceRecordSets(ctx, changeBatch)
	if err != nil {
		return "", fmt.Errorf("%w: %v", "Update FQDN error", err)
	}
	return nodeFQDN, nil
}

// deletes a weighted 'A' record for a node.
// To delete a record, all parameters (Name, Type, SetIdentifier, etc.) must match the existing record.
func (dm *DnsManager) DeleteRegionalDNSRecord(ctx context.Context, info RecordInfo) error {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	eLog.LogInfo("Deleting DNS record info: %v", info)

	ip := info.LocInfo.Ip
	region := info.LocInfo.Region
	fqdn, err := common.GetRegionalFQDN(info.LocInfo.RegionId, region, info.CustPrefix, dm.r53ZoneName)
	if err != nil {
		eLog.LogError("Failed to get regional FQDN for deletion: %v", err)
		return err
	}

	setID := fmt.Sprintf("%s-%s", fqdn[:min(4, len(fqdn))], ip)
	ttl := int64(60)
	weight := int64(200)

	// Construct the exact record set that needs to be deleted.
	rrs := &r53types.ResourceRecordSet{
		Name:          aws.String(fqdn),
		Type:          r53types.RRTypeA,
		SetIdentifier: aws.String(setID),
		Weight:        aws.Int64(weight),
		TTL:           aws.Int64(ttl),
		ResourceRecords: []r53types.ResourceRecord{
			{Value: aws.String(ip)},
		},
	}

	changeBatch := &r53types.ChangeBatch{
		Changes: []r53types.Change{
			{
				Action:            r53types.ChangeActionDelete,
				ResourceRecordSet: rrs,
			},
		},
	}

	err = dm.ChangeResourceRecordSets(ctx, changeBatch)
	if err != nil {
		return fmt.Errorf("%w: %v", errors.New("delete FQDN error"), err)
	}
	return nil
}

// CreateOrUpdateGlobalDNSRecord creates or updates a global A record with geolocation routing.
func (dm *DnsManager) CreateOrUpdateGlobalDNSRecord(ctx context.Context, info RecordInfo) (string, error) {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	fqdn := common.GetGlobalFQDN(info.CustPrefix, dm.r53ZoneName)
	eLog.LogInfo("Updating global DNS record for %s", fqdn)

	var changes []r53types.Change
	ttl := int64(300)

	// Get the coordinates for the specified GCP region.
	coords, err := GetGCPCoords(info.LocInfo.Region)
	if err != nil {
		eLog.LogError("Failed to get gcp coords from region", "error", err)
		return "", fmt.Errorf("could not retrieve coordinates for region %s: %w", info.LocInfo.Region, err)
	}

	//updatedCustomerName := common.GetUpdatedCustomerName(info.CustomerName)
	changes = append(changes, r53types.Change{
		Action: r53types.ChangeActionUpsert,
		ResourceRecordSet: &r53types.ResourceRecordSet{
			Name:            aws.String(fqdn),
			Type:            r53types.RRTypeA,
			SetIdentifier:   aws.String(fmt.Sprintf("%s-%s", info.CustPrefix, info.LocInfo.Region)), // Unique ID for this geo-location
			TTL:             aws.Int64(ttl),
			ResourceRecords: []r53types.ResourceRecord{{Value: aws.String(info.LocInfo.Ip)}},
			GeoProximityLocation: &r53types.GeoProximityLocation{
				Coordinates: &r53types.Coordinates{
					Latitude:  aws.String(coords.Latitude),
					Longitude: aws.String(coords.Longitude),
				},
			},
		},
	})

	//  Apply all changes in a single batch.
	changeBatch := &r53types.ChangeBatch{
		Comment: aws.String(fmt.Sprintf("UPSERT global geolocation A records for %s", fqdn)),
		Changes: changes,
	}

	err = dm.ChangeResourceRecordSets(ctx, changeBatch)
	if err != nil {
		return "", fmt.Errorf("%w: %v", errors.New("Update Global FQDN error"), err)
	}

	eLog.LogInfo("Successfully updated global DNS record: %s", fqdn)
	return fqdn, nil
}

// DeleteGlobalDNSRecord deletes a specific geolocation A record from a set.
// The info.LocInfo slice should contain the single region and IP to be deleted.
func (dm *DnsManager) DeleteGlobalDNSRecord(ctx context.Context, info RecordInfo) error {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	fqdn := common.GetGlobalFQDN(info.CustPrefix, dm.r53ZoneName)
	ttl := int64(300)

	eLog.LogInfo("Attempting to delete DNS record for region", "fqdn", fqdn, "region", info.LocInfo.Region)

	// Get the coordinates for the specified GCP region.
	coords, err := GetGCPCoords(info.LocInfo.Region)
	if err != nil {
		eLog.LogError("Failed to get gcp coords from region", "error", err)
		return err
	}

	recordSetToDelete := &r53types.ResourceRecordSet{
		Name:            aws.String(fqdn),
		Type:            r53types.RRTypeA,
		SetIdentifier:   aws.String(fmt.Sprintf("%s-%s", info.CustPrefix, info.LocInfo.Region)),
		TTL:             aws.Int64(ttl),
		ResourceRecords: []r53types.ResourceRecord{{Value: aws.String(info.LocInfo.Ip)}},
		GeoProximityLocation: &r53types.GeoProximityLocation{
			Coordinates: &r53types.Coordinates{
				Latitude:  aws.String(coords.Latitude),
				Longitude: aws.String(coords.Longitude),
			},
		},
	}

	changeBatch := &r53types.ChangeBatch{
		Comment: aws.String(fmt.Sprintf("DELETE geolocation A record for %s in region %s", fqdn, info.LocInfo.Region)),
		Changes: []r53types.Change{
			{
				Action:            r53types.ChangeActionDelete,
				ResourceRecordSet: recordSetToDelete,
			},
		},
	}

	err = dm.ChangeResourceRecordSets(ctx, changeBatch)
	if err != nil {
		return fmt.Errorf("%w: %v", errors.New("delete Global FQDN region error"), err)
	}

	eLog.LogInfo("Successfully deleted DNS record for region", "fqdn", fqdn, "region", info.LocInfo.Region)
	return nil
}

func (dm *DnsManager) CreateOrUpdateDnsRecord(ctx context.Context, info RecordInfo) (string, string, error) {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	regionalRecord, err := dm.CreateOrUpdateRegionalDNSRecord(ctx, info)
	if err != nil {
		eLog.LogError("Failed to create or update Regional DNS record", "err", err)
		return "", "", err
	}
	eLog.LogInfo("Successfully created or updated Regional DNS regionalRecord: %s", regionalRecord)

	globalRecord, err := dm.CreateOrUpdateGlobalDNSRecord(ctx, info)
	if err != nil {
		eLog.LogError("Failed to create or update GlobalDNS record", "err", err)
		return "", "", err
	}

	eLog.LogInfo("Successfully created or updated Global DNS regionalRecord: %s", globalRecord)
	return regionalRecord, globalRecord, nil
}

func (dm *DnsManager) DeleteDnsRecord(ctx context.Context, info RecordInfo) error {
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())
	err := dm.DeleteRegionalDNSRecord(ctx, info)
	if err != nil {
		eLog.LogError("Failed to delete Regional DNS record", "err", err)
		return err
	}
	eLog.LogInfo("Successfully deleted Regional DNS record: %s", info.CustPrefix)

	err = dm.DeleteGlobalDNSRecord(ctx, info)
	if err != nil {
		eLog.LogError("Failed to delete GlobalDNS record", "err", err)
		return err
	}
	eLog.LogInfo("Successfully deleted GlobalDNS record: %s", info.CustPrefix)

	return nil
}
