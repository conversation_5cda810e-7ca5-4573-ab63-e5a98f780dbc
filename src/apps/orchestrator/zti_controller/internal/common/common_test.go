package common

import (
	"testing"
)

func TestExtractPrefixBeforeGP(t *testing.T) {
	tests := []struct {
		name           string
		input          string
		expectedResult string
		expectError    bool
		errorMessage   string
	}{
		// Original test cases
		{
			name:           "Valid input with .gp suffix",
			input:          "customer123.gp",
			expectedResult: "customer123",
			expectError:    false,
		},
		{
			name:           "Valid input with .gp in middle",
			input:          "tenant456.gp.example.com",
			expectedResult: "tenant456",
			expectError:    false,
		},
		{
			name:           "Input without .gp substring",
			input:          "customer123.com",
			expectedResult: "customer123.com",
			expectError:    true,
			errorMessage:   "cust prefix not found in gpaas table",
		},
		{
			name:           "Empty string input",
			input:          "",
			expectedResult: "",
			expectError:    true,
			errorMessage:   "cust prefix not found in gpaas table",
		},
		// Real-world test cases
		{
			name:           "Credit union with long numeric suffix",
			input:          "creditunionoftexas-126658002.gp.panclouddev.com",
			expectedResult: "creditunionoftexas-126658002",
			expectError:    false,
		},
		{
			name:           "PEM file without .gp (should return error)",
			input:          "portalwildcardcert.pem",
			expectedResult: "portalwildcardcert.pem",
			expectError:    true,
			errorMessage:   "cust prefix not found in gpaas table",
		},
		{
			name:           "Short customer name - eho",
			input:          "eho.gp.panclouddev.com",
			expectedResult: "eho",
			expectError:    false,
		},
		{
			name:           "Development customer with numeric suffix",
			input:          "dev-2-1-cust-31.gp.panclouddev.com",
			expectedResult: "dev-2-1-cust-31",
			expectError:    false,
		},
		{
			name:           "Entrada customer with long ID",
			input:          "entrada5-1207410090.gp.panclouddev.com",
			expectedResult: "entrada5-1207410090",
			expectError:    false,
		},
		{
			name:           "Entrada8 customer with ID",
			input:          "entrada8-1163688089.gp.panclouddev.com",
			expectedResult: "entrada8-1163688089",
			expectError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := extractPrefixBeforeGP(tt.input)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
					return
				}
				if tt.errorMessage != "" && err.Error() != tt.errorMessage {
					t.Errorf("Expected error message '%s', got '%s'", tt.errorMessage, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
					return
				}
			}

			if result != tt.expectedResult {
				t.Errorf("Expected result '%s', got '%s'", tt.expectedResult, result)
			}
		})
	}
}
