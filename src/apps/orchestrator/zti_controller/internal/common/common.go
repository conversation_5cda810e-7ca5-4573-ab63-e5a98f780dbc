package common

import (
	"fmt"
	"orchestrator/libs/go/dbaccess/models/gpaas_table"
	"orchestrator/libs/go/dbaccess/models/region_master"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/utils"
	"orchestrator/zti_controller/infra/db"
	"orchestrator/zti_controller/infra/logger"
	"strings"
)

const (
	API    = "api"
	GLOBAL = "global"
	GW     = "gw"
)

func GetGlobalFQDN(custPrefix, zone string) string {
	return fmt.Sprintf("%s-%s-%s.%s.%s", custPrefix, GLOBAL, GW, API, zone)
}

func GetRegionalFQDN(regionId int64, region, custPrefix, zone string) (string, error) {
	fqdnLocationName, err := GetFqdnLocationName(regionId)
	if err != nil {
		return "", fmt.Errorf("failed to get gqdn location name: %v", err)
	}

	return fmt.Sprintf("%s-%s-%s.%s.%s", custPrefix, fqdnLocationName, GW, API, zone), nil
}

func GetFqdnLocationName(regionId int64) (string, error) {
	dbAccessor := db.GetDbAccessor()
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	dbCtx := &sql.DbContext{DbConn: dbAccessor.GetDbConn(), Logger: eLog}
	entry, err := region_master.GetRowBySameEdgeLocAndComputeRegion(dbCtx, regionId)
	if err != nil {
		eLog.LogError("Failed to fetch region master entry")
		return "", fmt.Errorf("failed to fetch region master entry")
	}
	locationName := entry.FqdnLocationName.String()
	eLog.LogInfo("Location name: %s", locationName)

	return locationName, nil
}

func GetDnsCustPrefix(custId int64) (string, error) {
	dbAccessor := db.GetDbAccessor()
	eLog := utils.GetEventLogger(logger.GetGlobalLogger())

	dbCtx := &sql.DbContext{DbConn: dbAccessor.GetDbConn(), Logger: eLog}

	row, err := gpaas_table.GetByCustId(dbCtx, custId)
	if err != nil {
		eLog.LogError("Failed to fetch external IP")
		return "", err
	}

	custPrefix, err := extractPrefixBeforeGP(row.Name.String())
	if err != nil {
		eLog.LogError("cust prefix not found in gpaas table")
		return "", err
	}
	return custPrefix, nil
}

func extractPrefixBeforeGP(input string) (string, error) {
	// strings.Index returns the starting index of the first instance of a substring.
	// If the substring is not found, it returns -1.
	index := strings.Index(input, ".gp")

	// Check if ".gp" was found in the string.
	if index != -1 {
		// If found, slice the string from the beginning (index 0)
		// up to the index where ".gp" starts.
		return input[:index], nil
	}

	// If ".gp" was not found, return the original string.
	return input, fmt.Errorf("cust prefix not found in gpaas table")
}
