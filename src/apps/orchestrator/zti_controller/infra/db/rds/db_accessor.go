package rds

import (
	"orchestrator/libs/go/config"
	"orchestrator/libs/go/dbaccess/models/colo_connect_link_master"
	"orchestrator/libs/go/dbaccess/models/colo_onboarding_master"
	"orchestrator/libs/go/dbaccess/models/cust_epaas_config"
	"orchestrator/libs/go/dbaccess/models/cust_master"
	"orchestrator/libs/go/dbaccess/models/eproxy_mapping"
	"orchestrator/libs/go/dbaccess/models/explicit_proxy_tenant_info"
	"orchestrator/libs/go/dbaccess/models/gcp_projects_pool"
	"orchestrator/libs/go/dbaccess/models/ingress_cluster_config"
	"orchestrator/libs/go/dbaccess/models/ingress_cust_config"
	"orchestrator/libs/go/dbaccess/models/ingress_master"
	"orchestrator/libs/go/dbaccess/models/ingress_pod_master"
	"orchestrator/libs/go/dbaccess/models/instance_master"
	"orchestrator/libs/go/dbaccess/models/instance_upgrade"
	"orchestrator/libs/go/dbaccess/models/network_load_balancer_config"
	"orchestrator/libs/go/dbaccess/models/oci_shared_network_info"
	"orchestrator/libs/go/dbaccess/models/oci_tenant_network_info"
	"orchestrator/libs/go/dbaccess/models/orch_cfg"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_gcp_table"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_table"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_tf_import_table"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_tf_table"
	"orchestrator/libs/go/dbaccess/models/orch_project_management_tf_table"
	"orchestrator/libs/go/dbaccess/models/provision_api_status"
	"orchestrator/libs/go/dbaccess/models/region_master"
	"orchestrator/libs/go/dbaccess/models/traffic_mirroring_cfg"
	"orchestrator/libs/go/dbaccess/sql"
	"strconv"

	"go.panw.local/pangolin/clogger"
)

func NewDBAccessor(cfg *config.Config, log *clogger.Clogger) (*RdsAccessor, error) {
	var err error
	decryptedPassword, err := cfg.GetDBPassword()
	if err != nil {
		return nil, err
	}
	dbConn, err := sql.SetupClientConnSQL(sql.ClientConnInput{
		Host:       cfg.DBHost,
		User:       cfg.DBUser,
		Password:   decryptedPassword,
		Database:   cfg.DBName,
		MaxWorkers: 5,
	})
	if err != nil {
		return nil, err
	}

	acc := &RdsAccessor{
		dbConn: dbConn,
	}
	log.LogInfo("Successfully initialized RDS accessor")
	return acc, nil
}

type RdsAccessor struct {
	dbConn *sql.DbConn
}

func (rds *RdsAccessor) GetDbConn() *sql.DbConn {
	return rds.dbConn
}

func (rds *RdsAccessor) GetCustMasterById(id int64, eLog *clogger.EventLogger) (*cust_master.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return cust_master.GetRowByID(dbCtx, id)
}

func (rds *RdsAccessor) GetCustMasterByAcctId(acctId int64, eLog *clogger.EventLogger) (*cust_master.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return cust_master.GetRowByAcctID(dbCtx, acctId)
}

func (rds *RdsAccessor) GetInstMasterById(id int64, eLog *clogger.EventLogger) (*instance_master.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return instance_master.GetRowByID(dbCtx, id)
}

func (rds *RdsAccessor) GetInstMasterByCustomerRegion(custId, regionId, serviceTypeId int64, eLog *clogger.EventLogger) ([]*instance_master.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return instance_master.GetRowsByCustomerRegion(dbCtx, custId, regionId, serviceTypeId)
}

func (rds *RdsAccessor) GetInstMgmtByUniqKeys(custId, regionId int64, cloudProvider string, serviceTypeId int64, eLog *clogger.EventLogger) (*orch_instance_management_table.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return orch_instance_management_table.GetRowByUniqueKeys(dbCtx, custId, regionId, cloudProvider, serviceTypeId)
}

func (rds *RdsAccessor) GetInstMgmtByEIDX(eidx int64, eLog *clogger.EventLogger) (*orch_instance_management_table.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return orch_instance_management_table.GetRowByEIDX(dbCtx, eidx)
}

func (rds *RdsAccessor) GetInstMgmtByEIDXList(eidxList []int64, eLog *clogger.EventLogger) ([]*orch_instance_management_table.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return orch_instance_management_table.GetRowsByEIDXList(dbCtx, eidxList)
}

func (rds *RdsAccessor) GetInstMgmtByGcpProvisionType(gcpProvisionType string, eLog *clogger.EventLogger) ([]*orch_instance_management_table.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return orch_instance_management_table.GetRowsByGcpProvisionType(dbCtx, gcpProvisionType)
}

func (rds *RdsAccessor) CreateInstMgmt(row *orch_instance_management_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.Save(dbCtx)
}

func (rds *RdsAccessor) UpdateInstMgmtGcpProvisionType(row *orch_instance_management_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.SaveGcpProvisionType(dbCtx)
}

func (rds *RdsAccessor) UpdateInstMgmtWorkspaceName(row *orch_instance_management_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.SaveWorkspaceName(dbCtx)
}

func (rds *RdsAccessor) UpdateInstMgmtPostTerraformMigration(row *orch_instance_management_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.SavePostTerraformMigration(dbCtx)
}

func (rds *RdsAccessor) GetInstMgmtTfByInstMgmtId(instMgmtId int64, eLog *clogger.EventLogger) (*orch_instance_management_tf_table.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return orch_instance_management_tf_table.GetRowByInstMgmtID(dbCtx, instMgmtId)
}

func (rds *RdsAccessor) GetInstMgmtTfStage2(eLog *clogger.EventLogger) ([]*orch_instance_management_tf_table.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return orch_instance_management_tf_table.GetUpdatePendingRows(dbCtx)
}

func (rds *RdsAccessor) GetInstMgmtTfStage3(eLog *clogger.EventLogger) ([]*orch_instance_management_tf_table.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return orch_instance_management_tf_table.GetProcessPendingRows(dbCtx)
}

func (rds *RdsAccessor) GetInstMgmtTfStage4(eLog *clogger.EventLogger) ([]*orch_instance_management_tf_table.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return orch_instance_management_tf_table.GetPostProcessPendingRows(dbCtx)
}

func (rds *RdsAccessor) GetInstMgmtTfStage5(eLog *clogger.EventLogger) ([]*orch_instance_management_tf_table.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return orch_instance_management_tf_table.GetServiceMigrationPendingRows(dbCtx)
}

func (rds *RdsAccessor) GetInstMgmtTfImportByInstMgmtId(instMgmtId int64, eLog *clogger.EventLogger) (*orch_instance_management_tf_import_table.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return orch_instance_management_tf_import_table.GetRowByInstMgmtID(dbCtx, instMgmtId)
}

func (rds *RdsAccessor) GetInstMgmtTfImportByKey(provisionType string, custId, regionId, serviceId int64,
	cloudProvider string, eLog *clogger.EventLogger) (*orch_instance_management_tf_import_table.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return orch_instance_management_tf_import_table.GetRowByKey(dbCtx, provisionType, custId, regionId, serviceId,
		cloudProvider)
}

func (rds *RdsAccessor) CreateInstMgmtTfImport(row *orch_instance_management_tf_import_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.Save(dbCtx)
}

func (rds *RdsAccessor) UpdateInstMgmtTfImportRequestStates(row *orch_instance_management_tf_import_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.SaveRequestStates(dbCtx)
}

func (rds *RdsAccessor) UpdateInstMgmtTfImportRunStates(row *orch_instance_management_tf_import_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.SaveRunStates(dbCtx)
}

func (rds *RdsAccessor) GetNetworkLoadBalancerConfigByCustomerRegionElseDefault(custId int64, regionId int64, npgaProtocolEnabled bool, eLog *clogger.EventLogger, cloudProvider string) (*network_load_balancer_config.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return network_load_balancer_config.GetRowByCustomerRegionElseDefault(dbCtx, custId, regionId, npgaProtocolEnabled, cloudProvider)
}

func (rds *RdsAccessor) CreateInstMgmtTf(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.Save(dbCtx)
}

func (rds *RdsAccessor) UpdateInstMgmtTf(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.SaveAll(dbCtx)
}

func (rds *RdsAccessor) UpdateInstMgmtTfTriggerUpdate(instMgmtId, triggerUpdate int64, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	row, err := orch_instance_management_tf_table.GetRowByInstMgmtID(dbCtx, instMgmtId)
	if err != nil {
		return err
	}
	row.TriggerUpdate = sql.Int64(triggerUpdate)
	return row.SaveTriggerUpdate(dbCtx)
}

func (rds *RdsAccessor) UpdateInstMgmtTfAlerts(instMgmtId int64, alerts string, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	row, err := orch_instance_management_tf_table.GetRowByInstMgmtID(dbCtx, instMgmtId)
	if err != nil {
		return err
	}
	row.Alerts = sql.String(alerts)
	return row.SaveAlerts(dbCtx)
}

func (rds *RdsAccessor) UpdateInstMgmtTfMaxAttempted(instMgmtId, maxAttempted int64, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	row, err := orch_instance_management_tf_table.GetRowByInstMgmtID(dbCtx, instMgmtId)
	if err != nil {
		return err
	}
	row.MaxAttempted = sql.Int64(maxAttempted)
	return row.SaveMaxAttempted(dbCtx)
}

func (rds *RdsAccessor) UpdateInstMgmtTfStage1(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.SaveStage1(dbCtx)
}

func (rds *RdsAccessor) UpdateInstMgmtTfStage2(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.SaveStage2(dbCtx)
}

func (rds *RdsAccessor) UpdateInstMgmtTfStage3(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.SaveStage3(dbCtx)
}

func (rds *RdsAccessor) UpdateInstMgmtTfStage4(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.SaveStage4(dbCtx)
}

func (rds *RdsAccessor) UpdateInstMgmtTfStage5(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.SaveStage5(dbCtx)
}
func (rds *RdsAccessor) GetOrchCfgByName(name string, eLog *clogger.EventLogger) (*orch_cfg.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return orch_cfg.GetRowByName(dbCtx, name)
}

func (rds *RdsAccessor) GetInstMgmtGcpByInstMgmtId(instMgmtId int64, eLog *clogger.EventLogger) (*orch_instance_management_gcp_table.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return orch_instance_management_gcp_table.GetRowByInstMgmtID(dbCtx, instMgmtId)
}

func (rds *RdsAccessor) UpdateInstMgmtGcpTriggerUpdateBy10(row *orch_instance_management_gcp_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.IncrementTriggerUpdateBy10(dbCtx)
}

func (rds *RdsAccessor) GetPrjMgmtTfByKey(projectId, cloudProvider, ociTenancyId string, eLog *clogger.EventLogger) (*orch_project_management_tf_table.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return orch_project_management_tf_table.GetByKey(dbCtx, projectId, cloudProvider, ociTenancyId)
}

func (rds *RdsAccessor) CreatePrjMgmtTf(row *orch_project_management_tf_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.Save(dbCtx)
}

func (rds *RdsAccessor) UpdatePrjMgmtTf(row *orch_project_management_tf_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.SaveAll(dbCtx)
}

func (rds *RdsAccessor) DeletePrjMgmtTf(row *orch_project_management_tf_table.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.Delete(dbCtx)
}

func (rds *RdsAccessor) GetRegionMasterById(id int64, eLog *clogger.EventLogger) (*region_master.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return region_master.GetRowByID(dbCtx, id)
}

func (rds *RdsAccessor) GetRegionMasterByNativeRegionName(cloudProvider, nativeRegionName string, eLog *clogger.EventLogger) (*region_master.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return region_master.GetRowByNativeRegionName(dbCtx, cloudProvider, nativeRegionName)
}

func (rds *RdsAccessor) GetColoOnboardingMasterByKey(custId, computeRegionId int64, eLog *clogger.EventLogger) ([]*colo_onboarding_master.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return colo_onboarding_master.GetRowsByCustIDAndComputeRegionID(dbCtx, custId, computeRegionId)
}

func (rds *RdsAccessor) GetColoConnLinkMasterByIds(ids []int64, eLog *clogger.EventLogger) ([]*colo_connect_link_master.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return colo_connect_link_master.GetRowsByIDs(dbCtx, ids)
}

func (rds *RdsAccessor) GetTrafficMirroringCfgByKey(tenantId, computeRegionId int64, cloudProvider string, eLog *clogger.EventLogger) (*traffic_mirroring_cfg.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return traffic_mirroring_cfg.GetRowByKey(dbCtx, tenantId, computeRegionId, cloudProvider)
}

func (rds *RdsAccessor) GetRowForRegion(tenancyId string, regionName string, networkType string, shardId int64, eLog *clogger.EventLogger) (*oci_shared_network_info.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return oci_shared_network_info.GetRowForRegion(dbCtx, tenancyId, regionName, networkType, shardId)
}

func (rds *RdsAccessor) SaveTenantCompartment(row *oci_tenant_network_info.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.InsertTenantCompartment(dbCtx)
}

func (rds *RdsAccessor) SaveTenantNetwork(row *oci_tenant_network_info.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.ReplaceTenantNetwork(dbCtx)
}

func (rds *RdsAccessor) UpdateTenantNetwork(row *oci_tenant_network_info.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.UpdateTenantNetwork(dbCtx)
}

func (rds *RdsAccessor) DeleteTenantNetwork(row *oci_tenant_network_info.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.DeleteTenantNetwork(dbCtx)
}

func (rds *RdsAccessor) GetRowForTenantCompartment(acctID int64, eLog *clogger.EventLogger) (*oci_tenant_network_info.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return oci_tenant_network_info.GetRowForTenantCompartment(dbCtx, acctID)
}

func (rds *RdsAccessor) GetRowForTenantNetwork(acctID int64, regionName string, eLog *clogger.EventLogger) (*oci_tenant_network_info.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return oci_tenant_network_info.GetRowForTenantNetwork(dbCtx, acctID, regionName)
}

func (rds *RdsAccessor) InsertProvisionAPIStatus(row *provision_api_status.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.Insert(dbCtx)
}

func (rds *RdsAccessor) SetProvisionAPIStatus(eLog *clogger.EventLogger, fields map[string]interface{}) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	for k, v := range fields {
		if err := provision_api_status.SetRowByUUID(dbCtx, eLog.TraceID, k, v); err != nil {
			return err
		}
	}
	return nil
}

func (rds *RdsAccessor) GetInProgressProvisionEntries(eLog *clogger.EventLogger) ([]*provision_api_status.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return provision_api_status.GetUnfinishedRows(dbCtx)
}

func (rds *RdsAccessor) GetProvisionAPIStatusByUUID(eLog *clogger.EventLogger, uuid string) (*provision_api_status.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return provision_api_status.GetRowByUUID(dbCtx, uuid)
}

func (rds *RdsAccessor) GetGcpProjectsPoolByName(name string, eLog *clogger.EventLogger) (*gcp_projects_pool.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return gcp_projects_pool.GetByName(dbCtx, name)
}

func (rds *RdsAccessor) GetCustEpaasConfigByCustIdAndComputeRegionIdAndNodeType(custId int64, computeRegionId int64, nodeType int64, eLog *clogger.EventLogger) (*cust_epaas_config.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return cust_epaas_config.GetRowByCustIDAndComputeRegionIdAndNodeType(dbCtx, custId, nodeType, computeRegionId)
}

func (rds *RdsAccessor) GetEproxyMappingByZoneAndName(zone int64, name string, eLog *clogger.EventLogger) (*eproxy_mapping.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return eproxy_mapping.GetRowByZoneAndName(dbCtx, zone, name)
}

func (rds *RdsAccessor) GetExplicitProxyTenantConfigRowByTenantId(tenantId int64, eLog *clogger.EventLogger) (*explicit_proxy_tenant_info.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return explicit_proxy_tenant_info.GetRowByTenantId(dbCtx, tenantId)
}

func (rds *RdsAccessor) GetInstanceUpgradePendingRowsByCustIDAndRegion(custId, regionId int64, eLog *clogger.EventLogger) ([]*instance_upgrade.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return instance_upgrade.GetPendingRowsByCustIDAndRegion(dbCtx, strconv.FormatInt(custId, 10), strconv.FormatInt(regionId, 10))
}

func (rds *RdsAccessor) DataDumpSupported(eLog *clogger.EventLogger) bool {
	return false
}

func (rds *RdsAccessor) DataDump(eLog *clogger.EventLogger) bool {
	eLog.LogDebug("RdsAccess does not support DataDump")
	return true
}

func (rds *RdsAccessor) GetIngressCustConfigByID(id int64, regionId int64, eLog *clogger.EventLogger) (*ingress_cust_config.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return ingress_cust_config.GetByCustIDAndRegionID(dbCtx, id, regionId)
}

func (rds *RdsAccessor) GetAllIngressCustConfigByID(id int64, eLog *clogger.EventLogger) ([]*ingress_cust_config.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return ingress_cust_config.GetAllByID(dbCtx, id)
}

func (rds *RdsAccessor) SaveToIngressMaster(row *ingress_master.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.Upsert(dbCtx)
}

func (rds *RdsAccessor) UpdateFQDNinIngressMaster(row *ingress_master.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.UpdateFQDN(dbCtx)
}

func (rds *RdsAccessor) GetIngressMasterEntryByCustId(custId int64, eLog *clogger.EventLogger) ([]*ingress_master.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return ingress_master.GetAllRows(dbCtx, custId)
}

func (rds *RdsAccessor) GetIngressMasterEntryByCustIdAndRegionId(custId, regionId int64, eLog *clogger.EventLogger) (*ingress_master.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return ingress_master.GetRowForRegionId(dbCtx, custId, regionId)
}

func (rds *RdsAccessor) SaveToIngressCustConfig(row *ingress_cust_config.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.Save(dbCtx)
}

func (rds *RdsAccessor) CreateOrUpdateToIngressCustConfig(row *ingress_cust_config.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.Upsert(dbCtx)
}

func (rds *RdsAccessor) GetIngressCustConfigByCustId(custId int64, eLog *clogger.EventLogger) (*ingress_cust_config.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return ingress_cust_config.GetByCustID(dbCtx, custId)
}

func (rds *RdsAccessor) GetIngressClusterConfigByRegionId(regionId int64, eLog *clogger.EventLogger) (*ingress_cluster_config.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return ingress_cluster_config.GetByRegionId(dbCtx, regionId)
}

func (rds *RdsAccessor) DeleteIngressMasterEntry(row *ingress_master.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.Delete(dbCtx)
}

func (rds *RdsAccessor) SaveToIngressPodMaster(row *ingress_pod_master.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.Upsert(dbCtx)
}

func (rds *RdsAccessor) GetIngressPodMasterEntryByCustId(custId int64, eLog *clogger.EventLogger) ([]*ingress_pod_master.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return ingress_pod_master.GetAllRows(dbCtx, custId)
}

func (rds *RdsAccessor) GetIngressPodMasterEntryByCustIdAndRegionId(custId, regionId int64, eLog *clogger.EventLogger) (*ingress_pod_master.Row, error) {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return ingress_pod_master.GetRowForRegionId(dbCtx, custId, regionId)
}

func (rds *RdsAccessor) DeleteIngressPodMasterEntry(row *ingress_pod_master.Row, eLog *clogger.EventLogger) error {
	dbCtx := &sql.DbContext{DbConn: rds.dbConn, Logger: eLog}
	return row.Delete(dbCtx)
}
