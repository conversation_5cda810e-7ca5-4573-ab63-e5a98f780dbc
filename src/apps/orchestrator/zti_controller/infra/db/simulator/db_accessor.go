package simulator

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"orchestrator/libs/go/dbaccess/models/colo_connect_link_master"
	"orchestrator/libs/go/dbaccess/models/colo_onboarding_master"
	"orchestrator/libs/go/dbaccess/models/cust_epaas_config"
	"orchestrator/libs/go/dbaccess/models/cust_master"
	"orchestrator/libs/go/dbaccess/models/eproxy_mapping"
	"orchestrator/libs/go/dbaccess/models/explicit_proxy_tenant_info"
	"orchestrator/libs/go/dbaccess/models/gcp_projects_pool"
	"orchestrator/libs/go/dbaccess/models/ingress_cluster_config"
	"orchestrator/libs/go/dbaccess/models/ingress_cust_config"
	"orchestrator/libs/go/dbaccess/models/ingress_master"
	"orchestrator/libs/go/dbaccess/models/ingress_pod_master"
	"orchestrator/libs/go/dbaccess/models/instance_master"
	"orchestrator/libs/go/dbaccess/models/instance_upgrade"
	"orchestrator/libs/go/dbaccess/models/network_load_balancer_config"
	"orchestrator/libs/go/dbaccess/models/oci_shared_network_info"
	"orchestrator/libs/go/dbaccess/models/oci_tenant_network_info"
	"orchestrator/libs/go/dbaccess/models/orch_cfg"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_gcp_table"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_table"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_tf_import_table"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_tf_table"
	"orchestrator/libs/go/dbaccess/models/orch_project_management_tf_table"
	"orchestrator/libs/go/dbaccess/models/provision_api_status"
	"orchestrator/libs/go/dbaccess/models/region_master"
	"orchestrator/libs/go/dbaccess/models/traffic_mirroring_cfg"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/terraform"
	"os"
	"path/filepath"
	"reflect"
	"strconv"
	"sync"
	"time"

	"go.panw.local/pangolin/clogger"
)

func NewDBAccessor(log *clogger.Clogger) (*SimDbAccessor, error) {
	var err error
	dataFilepath := "./devenv/db_simulator"
	acc := &SimDbAccessor{
		custMasterTablePath:              filepath.Join(dataFilepath, "cust_master.json"),
		instMasterTablePath:              filepath.Join(dataFilepath, "instance_master.json"),
		instMgmtTablePath:                filepath.Join(dataFilepath, "instance_mgmt.json"),
		instMgmtTfTablePath:              filepath.Join(dataFilepath, "instance_mgmt_tf.json"),
		instMgmtTfImportTablePath:        filepath.Join(dataFilepath, "instance_mgmt_tf_import.json"),
		orchCfgTablePath:                 filepath.Join(dataFilepath, "orch_cfg.json"),
		nlbCfgTablePath:                  filepath.Join(dataFilepath, "nlb_cfg.json"),
		instMgmtGcpTablePath:             filepath.Join(dataFilepath, "instance_mgmt_gcp.json"),
		prjMgmtTfTablePath:               filepath.Join(dataFilepath, "project_mgmt_tf.json"),
		regionMasterTablePath:            filepath.Join(dataFilepath, "region_master.json"),
		coloOnboardingMasterTablePath:    filepath.Join(dataFilepath, "colo_onboarding_master.json"),
		coloConnectLinkMasterTablePath:   filepath.Join(dataFilepath, "colo_connect_link_master.json"),
		trafficMirroringCfgTablePath:     filepath.Join(dataFilepath, "traffic_mirroring_cfg.json"),
		provisionAPIStatusTablePath:      filepath.Join(dataFilepath, "provision_api_status.json"),
		ingressCustConfigTablePath:       filepath.Join(dataFilepath, "ingress_cust_config.json"),
		ingressClusterConfigTablePath:    filepath.Join(dataFilepath, "ingress_cluster_config.json"),
		ingressMasterTablePath:           filepath.Join(dataFilepath, "ingress_master.json"),
		ingressPodMasterTablePath:        filepath.Join(dataFilepath, "ingress_pod_master.json"),
		ociTenantNetworkInfoTablePath:    filepath.Join(dataFilepath, "oci_tenant_network_info.json"),
		ociSharedNetworkInfoTablePath:    filepath.Join(dataFilepath, "oci_shared_network_info.json"),
		gcpProjectsPoolTablePath:         filepath.Join(dataFilepath, "gcp_projects_pool.json"),
		custEpaasConfigEntryTablePath:    filepath.Join(dataFilepath, "cust_epaas_config_entry.json"),
		explicitProxyTenantInfoTablePath: filepath.Join(dataFilepath, "explicit_proxy_tenant_info.json"),
		eproxyMappingTablePath:           filepath.Join(dataFilepath, "eproxy_mapping.json"),
		instanceUpgradeTablePath:         filepath.Join(dataFilepath, "instance_upgrade.json"),
		instMgmtTableEIDX:                1000}

	tables := map[string]struct {
		table interface{}
		eidx  *sql.Int64
	}{
		acc.custMasterTablePath:              {&acc.custMasterTable, nil},
		acc.instMasterTablePath:              {&acc.instMasterTable, nil},
		acc.instMgmtTablePath:                {&acc.instMgmtTable, &acc.instMgmtTableEIDX},
		acc.instMgmtTfTablePath:              {&acc.instMgmtTfTable, nil},
		acc.instMgmtTfImportTablePath:        {&acc.instMgmtTfImportTable, nil},
		acc.orchCfgTablePath:                 {&acc.orchCfgTable, nil},
		acc.nlbCfgTablePath:                  {&acc.nlbCfgTable, nil},
		acc.instMgmtGcpTablePath:             {&acc.instMgmtGcpTable, nil},
		acc.prjMgmtTfTablePath:               {&acc.prjMgmtTfTable, nil},
		acc.regionMasterTablePath:            {&acc.regionMasterTable, nil},
		acc.coloOnboardingMasterTablePath:    {&acc.coloOnboardingMasterTable, nil},
		acc.coloConnectLinkMasterTablePath:   {&acc.coloConnectLinkMasterTable, nil},
		acc.trafficMirroringCfgTablePath:     {&acc.trafficMirroringCfgTable, nil},
		acc.provisionAPIStatusTablePath:      {&acc.provisionAPIStatusTable, nil},
		acc.ingressCustConfigTablePath:       {&acc.ingressCustConfigTable, nil},
		acc.ingressClusterConfigTablePath:    {&acc.ingressClusterConfigTable, nil},
		acc.ingressMasterTablePath:           {&acc.ingressMasterTable, nil},
		acc.ociTenantNetworkInfoTablePath:    {&acc.ociTenantNetworkInfoTable, nil},
		acc.ociSharedNetworkInfoTablePath:    {&acc.ociSharedNetworkInfoTable, nil},
		acc.gcpProjectsPoolTablePath:         {&acc.gcpProjectsPoolTable, nil},
		acc.custEpaasConfigEntryTablePath:    {&acc.custEpaasConfigEntryTable, nil},
		acc.explicitProxyTenantInfoTablePath: {&acc.explicitProxyTenantInfoTable, nil},
		acc.eproxyMappingTablePath:           {&acc.eproxyMappingTablePath, nil},
		acc.instanceUpgradeTablePath:         {&acc.instanceUpgradeTable, nil},
	}
	load := func(tableName string, table interface{}, eidx *sql.Int64) error {
		data, err := ioutil.ReadFile(tableName)
		if err != nil {
			if _, ok := err.(*os.PathError); ok {
				log.LogInfo("Skip loading %v, file does not exist", tableName)
				return nil
			} else {
				log.LogError("Failed to load %v: %v\n", tableName, err)
				return err
			}
		} else if len(data) == 0 {
			log.LogInfo("Skip loading %v, file is empty", tableName)
			return nil
		}

		log.LogDebug("Loading %v", tableName)
		err = unmarshalJsonBySqlTag(log, data, table)
		if err != nil {
			log.LogError("Failed to unmarshal %v: %v\n", tableName, err)
			return err
		}

		if eidx != nil {
			highestIdx := int64(0)
			sliceVal := reflect.ValueOf(table).Elem()
			for i := 0; i < sliceVal.Len(); i++ {
				rowVal := sliceVal.Index(i).Elem()
				if eidxVal := rowVal.FieldByName("EIDX"); eidxVal.IsZero() {
					errStr := fmt.Sprintf("Failed to find \"EIDX\" column in %v", tableName)
					log.LogError(errStr)
					return fmt.Errorf(errStr)
				} else if idx := eidxVal.Int(); idx > highestIdx {
					highestIdx = idx
				}
			}
			*eidx = sql.Int64(highestIdx) + 1
		}
		log.LogInfo("Successfully load file %v", tableName)
		return nil
	}

	for n, s := range tables {
		if n != "" {
			if err = load(n, s.table, s.eidx); err != nil {
				return nil, err
			}
		}
	}
	log.LogInfo("Successfully initialized Simulated DB accessor")
	return acc, nil
}

type SimDbAccessor struct {
	sync.RWMutex

	custMasterTablePath string
	custMasterTable     []*cust_master.Row

	instMasterTablePath string
	instMasterTable     []*instance_master.Row

	instMgmtTablePath string
	instMgmtTable     []*orch_instance_management_table.Row
	instMgmtTableEIDX sql.Int64

	instMgmtTfTablePath string
	instMgmtTfTable     []*orch_instance_management_tf_table.Row

	instMgmtTfImportTablePath string
	instMgmtTfImportTable     []*orch_instance_management_tf_import_table.Row

	orchCfgTablePath string
	orchCfgTable     []*orch_cfg.Row

	nlbCfgTablePath string
	nlbCfgTable     []*network_load_balancer_config.Row

	instMgmtGcpTablePath string
	instMgmtGcpTable     []*orch_instance_management_gcp_table.Row

	prjMgmtTfTablePath string
	prjMgmtTfTable     []*orch_project_management_tf_table.Row

	regionMasterTablePath string
	regionMasterTable     []*region_master.Row

	coloOnboardingMasterTablePath string
	coloOnboardingMasterTable     []*colo_onboarding_master.Row

	coloConnectLinkMasterTablePath string
	coloConnectLinkMasterTable     []*colo_connect_link_master.Row

	trafficMirroringCfgTablePath string
	trafficMirroringCfgTable     []*traffic_mirroring_cfg.Row

	provisionAPIStatusTablePath string
	provisionAPIStatusTable     []*provision_api_status.Row

	ingressCustConfigTablePath string
	ingressCustConfigTable     []*ingress_cust_config.Row

	ingressClusterConfigTablePath string
	ingressClusterConfigTable     []*ingress_cluster_config.Row

	ingressMasterTablePath string
	ingressMasterTable     []*ingress_master.Row

	ingressPodMasterTablePath string
	ingressPodMasterTable     []*ingress_pod_master.Row

	ociTenantNetworkInfoTablePath string
	ociTenantNetworkInfoTable     []*oci_tenant_network_info.Row

	ociSharedNetworkInfoTablePath string
	ociSharedNetworkInfoTable     []*oci_shared_network_info.Row

	gcpProjectsPoolTablePath string
	gcpProjectsPoolTable     []*gcp_projects_pool.Row

	custEpaasConfigEntryTablePath string
	custEpaasConfigEntryTable     []*cust_epaas_config.Row

	explicitProxyTenantInfoTablePath string
	explicitProxyTenantInfoTable     []*explicit_proxy_tenant_info.Row

	eproxyMappingTablePath string
	eproxyMappingTable     []*eproxy_mapping.Row

	instanceUpgradeTablePath string
	instanceUpgradeTable     []*instance_upgrade.Row
}

func (acc *SimDbAccessor) GetCustMasterById(id int64, eLog *clogger.EventLogger) (*cust_master.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.custMasterTable {
		if r.ID.Int64() == id {
			cpy := *r
			return &cpy, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetCustMasterByAcctId(acctId int64, eLog *clogger.EventLogger) (*cust_master.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.custMasterTable {
		if r.AcctID.Int64() == acctId {
			cpy := *r
			return &cpy, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetInstMasterById(id int64, eLog *clogger.EventLogger) (*instance_master.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.instMasterTable {
		if r.ID.Int64() == id {
			cpy := *r
			return &cpy, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetInstMasterByCustomerRegion(custId, regionId, serviceTypeId int64, eLog *clogger.EventLogger) ([]*instance_master.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	var output []*instance_master.Row
	for _, r := range acc.instMasterTable {
		if r.CustID.Int64() == custId && r.ComputeRegionIdx.Int64() == regionId {
			if serviceTypeId != 0 {
				if serviceTypeId == r.NodeType.Int64() {
					cpy := *r
					output = append(output, &cpy)
				}
			} else {
				switch int(r.NodeType.Int64()) {
				case terraform.NodeTypeRemoteNetwork, terraform.NodeTypeServiceConnection:
					if r.HAPeer.Int64() != 0 {
						cpy := *r
						output = append(output, &cpy)
					}
				case terraform.NodeTypeGPGateway, terraform.NodeTypeGPPortal, terraform.NodeTypeNAT, terraform.NodeTypeNLB:
					cpy := *r
					output = append(output, &cpy)
				}
			}
		}
	}
	return output, nil
}

func (acc *SimDbAccessor) GetInstMgmtByUniqKeys(custId, regionId int64, cloudProvider string, serviceTypeId int64, eLog *clogger.EventLogger) (*orch_instance_management_table.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.instMgmtTable {
		if r.CustID.Int64() == custId && r.Region.Int64() == regionId &&
			r.CloudProvider.String() == cloudProvider && r.NodeType.Int64() == serviceTypeId {
			cpy := *r
			return &cpy, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetInstMgmtByEIDX(eidx int64, eLog *clogger.EventLogger) (*orch_instance_management_table.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.instMgmtTable {
		if r.EIDX.Int64() == eidx {
			cpy := *r
			return &cpy, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetInstMgmtByEIDXList(eidxList []int64, eLog *clogger.EventLogger) ([]*orch_instance_management_table.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	eidxMap := make(map[int64]bool)
	for _, eidx := range eidxList {
		eidxMap[eidx] = true
	}
	var rows []*orch_instance_management_table.Row
	for _, r := range acc.instMgmtTable {
		if eidxMap[r.EIDX.Int64()] {
			cpy := *r
			rows = append(rows, &cpy)
		}
	}
	return rows, nil
}

func (acc *SimDbAccessor) GetInstMgmtByGcpProvisionType(gcpProvisionType string, eLog *clogger.EventLogger) ([]*orch_instance_management_table.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	var rows []*orch_instance_management_table.Row
	for _, r := range acc.instMgmtTable {
		if r.GcpProvisionType.String() == gcpProvisionType {
			cpy := *r
			rows = append(rows, &cpy)
		}
	}
	return rows, nil
}

func (acc *SimDbAccessor) CreateInstMgmt(row *orch_instance_management_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	cpy := *row
	cpy.EIDX = acc.instMgmtTableEIDX
	acc.instMgmtTableEIDX++
	acc.instMgmtTable = append(acc.instMgmtTable, &cpy)
	return nil
}

func (acc *SimDbAccessor) UpdateInstMgmtGcpProvisionType(row *orch_instance_management_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	acc.Unlock()

	for _, r := range acc.instMgmtTable {
		if r.EIDX == row.EIDX {
			r.GcpProvisionType = row.GcpProvisionType
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) UpdateInstMgmtWorkspaceName(row *orch_instance_management_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	acc.Unlock()

	for _, r := range acc.instMgmtTable {
		if r.EIDX == row.EIDX {
			r.WorkspaceName = row.WorkspaceName
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) UpdateInstMgmtPostTerraformMigration(row *orch_instance_management_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	acc.Unlock()

	for _, r := range acc.instMgmtTable {
		if r.EIDX == row.EIDX {
			r.WorkspaceName = row.WorkspaceName
			r.GcpProvisionType = row.GcpProvisionType
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) GetInstMgmtTfByInstMgmtId(instMgmtId int64, eLog *clogger.EventLogger) (*orch_instance_management_tf_table.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.instMgmtTfTable {
		if r.InstMgmtID.Int64() == instMgmtId {
			cpy := *r
			return &cpy, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) CreateInstMgmtTf(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	cpy := *row
	acc.instMgmtTfTable = append(acc.instMgmtTfTable, &cpy)
	return nil
}

func (acc *SimDbAccessor) UpdateInstMgmtTf(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.instMgmtTfTable {
		if r.InstMgmtID == row.InstMgmtID {
			r.Metadata = row.Metadata
			r.TriggerUpdate = row.TriggerUpdate
			r.FinishedUpdate = row.FinishedUpdate
			r.StartedProcessing = row.StartedProcessing
			r.CurrRunStatus = row.CurrRunStatus
			r.PrevRunStatus = row.PrevRunStatus
			r.RunStatusChangeTimestamp = row.RunStatusChangeTimestamp
			r.FinishedProcessing = row.FinishedProcessing
			r.StartedPostProcessing = row.StartedPostProcessing
			r.FinishedPostProcessing = row.FinishedPostProcessing
			r.RunId = row.RunId
			r.RunType = row.RunType
			r.RunUrl = row.RunUrl
			r.PostProcessingStatus = row.PostProcessingStatus
			r.MaxAttempted = row.MaxAttempted
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) GetInstMgmtTfStage2(eLog *clogger.EventLogger) ([]*orch_instance_management_tf_table.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	var output []*orch_instance_management_tf_table.Row
	for _, r := range acc.instMgmtTfTable {
		if r.TriggerUpdate > r.FinishedUpdate && r.TriggerUpdate != r.MaxAttempted {
			cpy := *r
			output = append(output, &cpy)
		}
	}
	return output, nil
}

func (acc *SimDbAccessor) GetInstMgmtTfStage3(eLog *clogger.EventLogger) ([]*orch_instance_management_tf_table.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	var output []*orch_instance_management_tf_table.Row
	for _, r := range acc.instMgmtTfTable {
		if r.FinishedUpdate > r.FinishedProcessing && r.TriggerUpdate != r.MaxAttempted {
			cpy := *r
			output = append(output, &cpy)
		}
	}
	return output, nil
}

func (acc *SimDbAccessor) GetInstMgmtTfStage4(eLog *clogger.EventLogger) ([]*orch_instance_management_tf_table.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	var output []*orch_instance_management_tf_table.Row
	for _, r := range acc.instMgmtTfTable {
		if r.FinishedProcessing > r.FinishedPostProcessing && r.TriggerUpdate != r.MaxAttempted {
			cpy := *r
			output = append(output, &cpy)
		}
	}
	return output, nil
}

func (acc *SimDbAccessor) GetInstMgmtTfStage5(eLog *clogger.EventLogger) ([]*orch_instance_management_tf_table.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	var output []*orch_instance_management_tf_table.Row
	for _, r := range acc.instMgmtTfTable {
		if r.StartedServiceMigration != 0 && r.FinishedPostProcessing > r.FinishedServiceMigration && r.TriggerUpdate != r.MaxAttempted {
			cpy := *r
			output = append(output, &cpy)
		}
	}
	return output, nil
}

func (acc *SimDbAccessor) GetInstMgmtTfImportByInstMgmtId(instMgmtId int64, eLog *clogger.EventLogger) (*orch_instance_management_tf_import_table.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.instMgmtTfImportTable {
		if r.InstMgmtID.Int64() == instMgmtId {
			cpy := *r
			return &cpy, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetInstMgmtTfImportByKey(provisionType string, custId, regionId, serviceId int64,
	cloudProvider string, eLog *clogger.EventLogger) (*orch_instance_management_tf_import_table.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.instMgmtTfImportTable {
		if r.ProvisionType.String() == provisionType && r.CustID.Int64() == custId &&
			r.Region.Int64() == regionId && r.ServiceTypeID.Int64() == serviceId {
			cpy := *r
			return &cpy, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) CreateInstMgmtTfImport(row *orch_instance_management_tf_import_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	cpy := *row
	acc.instMgmtTfImportTable = append(acc.instMgmtTfImportTable, &cpy)
	return nil
}

func (acc *SimDbAccessor) UpdateInstMgmtTfImportRequestStates(row *orch_instance_management_tf_import_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.instMgmtTfImportTable {
		if r.InstMgmtID == row.InstMgmtID {
			r.TriggerUpdateLatest = row.TriggerUpdateLatest
			r.ImportCount = row.ImportCount
			r.ImportStart = row.ImportStart
			r.MigrationCount = row.MigrationCount
			r.MigrationStart = row.MigrationStart
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) UpdateInstMgmtTfImportRunStates(row *orch_instance_management_tf_import_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.instMgmtTfImportTable {
		if r.InstMgmtID == row.InstMgmtID {
			r.RunId = row.RunId
			r.RunType = row.RunType
			r.RunUrl = row.RunUrl
			r.RunStatus = row.RunStatus
			r.PlanStatus = row.PlanStatus
			r.HasChanges = row.HasChanges
			r.ResourceAdditions = row.ResourceAdditions
			r.ResourceChanges = row.ResourceChanges
			r.ResourceDestructions = row.ResourceDestructions
			r.PlanLogUrl = row.PlanLogUrl
			r.ExecutionPlan = row.ExecutionPlan
			r.TriggerUpdateImported = row.TriggerUpdateImported
			r.ImportDone = row.ImportDone
			r.TriggerUpdateMigrated = row.TriggerUpdateMigrated
			r.ResourceMigrationCount = row.ResourceMigrationCount
			r.MigrationDone = row.MigrationDone
			r.Zones = row.Zones
			r.AutoMigration = row.AutoMigration
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) GetNetworkLoadBalancerConfigByCustomerRegionElseDefault(custId int64, regionId int64, ngpaProtocolEnabled bool, eLog *clogger.EventLogger, cloudProvider string) (*network_load_balancer_config.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.nlbCfgTable {
		if r.CustID.Int64() == custId && r.RegionID.Int64() == regionId {
			cpy := *r
			return &cpy, nil
		}
	}

	var sessionAffinity int64
	if ngpaProtocolEnabled {
		// CYR-44445 Enable 5 tuple session affinity for all NGPA Greenfield tenants
		sessionAffinity = network_load_balancer_config.DEFAULT_NGPA_PROTO_SESSION_AFFINITY
	} else {
		sessionAffinity = network_load_balancer_config.DEFAULT_SESSION_AFFINITY
	}
	return &network_load_balancer_config.Row{
		sql.Int64(custId),
		sql.Int64(regionId),
		network_load_balancer_config.DEFAULT_FW_RULE_PROTOCOL,
		network_load_balancer_config.DEFAULT_BACKEND_SERV_PROTOCOL,
		sql.Int64(sessionAffinity),
		network_load_balancer_config.DEFAULT_HC_PROTOCOL,
		network_load_balancer_config.DEFAULT_HC_PORT,
		network_load_balancer_config.DEFAULT_HC_INTERVAL,
		network_load_balancer_config.DEFAULT_HC_TIMEOUT,
		network_load_balancer_config.DEFAULT_HC_UNHEALTHY_THRESHOLD,
		network_load_balancer_config.DEFAULT_HC_HEALTHY_THRESHOLD,
		network_load_balancer_config.DEFAULT_CONN_PERS_ON_UNHEALTHY_BACKENDS,
		network_load_balancer_config.DEFAULT_IS_STRONG_SESSION_AFFINITY_SUPPORTED,
		network_load_balancer_config.DEFAULT_NGPA_IDLE_TIMEOUT_SEC,
		"EXTERNAL",
		network_load_balancer_config.DEFAULT_LOAD_BALANCER_LISTENER_TYPE,
		network_load_balancer_config.DEFAULT_DUAL_STACK_SUPPORTED,
		sql.String(cloudProvider),
		network_load_balancer_config.DEFAULT_ENABLE_SRC_DEST_IP_PRESERVATION,
	}, nil
}

func (acc *SimDbAccessor) UpdateInstMgmtTfTriggerUpdate(instMgmtId int64, triggerUpdate int64, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.instMgmtTfTable {
		if r.InstMgmtID == sql.Int64(instMgmtId) {
			r.TriggerUpdate = sql.Int64(triggerUpdate)
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) UpdateInstMgmtTfAlerts(instMgmtId int64, alerts string, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.instMgmtTfTable {
		if r.InstMgmtID == sql.Int64(instMgmtId) {
			r.Alerts = sql.String(alerts)
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) UpdateInstMgmtTfMaxAttempted(instMgmtId int64, maxAttempted int64, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.instMgmtTfTable {
		if r.InstMgmtID == sql.Int64(instMgmtId) {
			r.MaxAttempted = sql.Int64(maxAttempted)
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) UpdateInstMgmtTfStage1(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.instMgmtTfTable {
		if r.InstMgmtID == row.InstMgmtID {
			r.Metadata = row.Metadata
			r.TriggerUpdate = row.TriggerUpdate
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) UpdateInstMgmtTfStage2(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.instMgmtTfTable {
		if r.InstMgmtID == row.InstMgmtID {
			r.Metadata = row.Metadata
			r.FinishedUpdate = row.FinishedUpdate
			r.StartedProcessing = row.StartedProcessing
			r.PrevRunStatus = row.PrevRunStatus
			r.CurrRunStatus = row.CurrRunStatus
			r.RunStatusChangeTimestamp = row.RunStatusChangeTimestamp
			r.RunId = row.RunId
			r.RunType = row.RunType
			r.RunUrl = row.RunUrl
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) UpdateInstMgmtTfStage3(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.instMgmtTfTable {
		if r.InstMgmtID == row.InstMgmtID {
			r.Metadata = row.Metadata
			r.FinishedProcessing = row.FinishedProcessing
			r.PrevRunStatus = row.PrevRunStatus
			r.CurrRunStatus = row.CurrRunStatus
			r.RunStatusChangeTimestamp = row.RunStatusChangeTimestamp
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) UpdateInstMgmtTfStage4(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.instMgmtTfTable {
		if r.InstMgmtID == row.InstMgmtID {
			r.Metadata = row.Metadata
			r.StartedPostProcessing = row.StartedPostProcessing
			r.FinishedPostProcessing = row.FinishedPostProcessing
			r.PostProcessingStatus = row.PostProcessingStatus
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) UpdateInstMgmtTfStage5(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.instMgmtTfTable {
		if r.InstMgmtID == row.InstMgmtID {
			r.Metadata = row.Metadata
			r.DeploymentID = row.DeploymentID
			r.StartedServiceMigration = row.StartedServiceMigration
			r.FinishedServiceMigration = row.FinishedServiceMigration
			r.ServiceMigrationStatus = row.ServiceMigrationStatus
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) GetOrchCfgByName(name string, eLog *clogger.EventLogger) (*orch_cfg.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.orchCfgTable {
		if r.Name.String() == name {
			cpy := *r
			return &cpy, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetInstMgmtGcpByInstMgmtId(instMgmtId int64, eLog *clogger.EventLogger) (*orch_instance_management_gcp_table.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.instMgmtGcpTable {
		if r.InstMgmtID.Int64() == instMgmtId {
			cpy := *r
			return &cpy, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) UpdateInstMgmtGcpTriggerUpdateBy10(row *orch_instance_management_gcp_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.instMgmtGcpTable {
		if r.InstMgmtID.Int64() == row.InstMgmtID.Int64() {
			r.TriggerUpdate = sql.Int64(r.TriggerUpdate.Int64() + 10)
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) GetPrjMgmtTfByKey(projectId, cloudProvider, ociTenancyId string, eLog *clogger.EventLogger) (*orch_project_management_tf_table.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.prjMgmtTfTable {
		if r.ProjectID.String() == projectId && r.CloudProvider.String() == cloudProvider && r.OciTenancyId.String() == ociTenancyId {
			cpy := *r
			return &cpy, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) CreatePrjMgmtTf(row *orch_project_management_tf_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	cpy := *row
	acc.prjMgmtTfTable = append(acc.prjMgmtTfTable, &cpy)
	return nil
}

func (acc *SimDbAccessor) UpdatePrjMgmtTf(row *orch_project_management_tf_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.prjMgmtTfTable {
		if r.ProjectID == row.ProjectID && r.CloudProvider == row.CloudProvider {
			r.ProjectID = row.ProjectID
			r.ProjectName = row.ProjectName
			r.CustomerName = row.CustomerName
			r.CloudProvider = row.CloudProvider
			r.OciTenancyId = row.OciTenancyId
			r.RegionName = row.RegionName
			r.OciParentCompId = row.OciParentCompId
			r.DebugID = row.DebugID
			r.WorkspaceName = row.WorkspaceName
			r.TriggerUpdate = row.TriggerUpdate
			r.ImportCount = row.ImportCount
			r.MigrationCount = row.MigrationCount
			r.ImportTriggerUpdate = row.ImportTriggerUpdate
			r.MigrationTriggerUpdate = row.MigrationTriggerUpdate
			r.FinishedUpdate = row.FinishedUpdate
			r.StartedProcessing = row.StartedProcessing
			r.CurrRunStatus = row.CurrRunStatus
			r.PrevRunStatus = row.PrevRunStatus
			r.RunStatusChangeTimestamp = row.RunStatusChangeTimestamp
			r.RunId = row.RunId
			r.RunType = row.RunType
			r.RunUrl = row.RunUrl
			r.ResourceCount = row.ResourceCount
			r.ResourceAdditions = row.ResourceAdditions
			r.ResourceChanges = row.ResourceChanges
			r.ResourceDestructions = row.ResourceDestructions
			r.ResourceImports = row.ResourceImports
			r.ImportResult = row.ImportResult
			r.FinishedProcessing = row.FinishedProcessing
			r.StartedPostProcessing = row.StartedPostProcessing
			r.Output = row.Output
			r.FinishedPostProcessing = row.FinishedPostProcessing
			r.DeployedByProvSvc = row.DeployedByProvSvc
			r.ErrorReason = row.ErrorReason
			return nil
		}
	}
	return sql.ErrNoRows
}

func (acc *SimDbAccessor) DeletePrjMgmtTf(row *orch_project_management_tf_table.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for i, r := range acc.prjMgmtTfTable {
		if r.ProjectID == row.ProjectID && r.CloudProvider == row.CloudProvider {
			// Remove the element at index i from acc.prjMgmtTfTable
			acc.prjMgmtTfTable = append(acc.prjMgmtTfTable[:i], acc.prjMgmtTfTable[i+1:]...)
		}
	}
	return nil
}

func (acc *SimDbAccessor) GetRegionMasterById(id int64, eLog *clogger.EventLogger) (*region_master.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.regionMasterTable {
		if r.EdgeLocationRegionId.Int64() == id {
			cpy := *r
			return &cpy, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetRegionMasterByNativeRegionName(cloudProvider, nativeRegionName string, eLog *clogger.EventLogger) (*region_master.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.regionMasterTable {
		if r.CloudProvider.String() == cloudProvider && r.NativeComputeRegionName.String() == nativeRegionName {
			cpy := *r
			return &cpy, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetColoOnboardingMasterByKey(custId, computeRegionId int64, eLog *clogger.EventLogger) ([]*colo_onboarding_master.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	var output []*colo_onboarding_master.Row
	for _, r := range acc.coloOnboardingMasterTable {
		if r.CustID.Int64() == custId && r.ComputeRegionID.Int64() == computeRegionId {
			cpy := *r
			output = append(output, &cpy)
		}
	}
	return output, nil
}

func (acc *SimDbAccessor) GetColoConnLinkMasterByIds(ids []int64, eLog *clogger.EventLogger) ([]*colo_connect_link_master.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	idMap := make(map[int64]bool)
	for _, id := range ids {
		idMap[id] = true
	}
	var output []*colo_connect_link_master.Row
	for _, r := range acc.coloConnectLinkMasterTable {
		if idMap[r.ID.Int64()] {
			cpy := *r
			output = append(output, &cpy)
		}
	}
	return output, nil
}

func (acc *SimDbAccessor) GetTrafficMirroringCfgByKey(tenantId, computeRegionId int64, cloudProvider string, eLog *clogger.EventLogger) (*traffic_mirroring_cfg.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.trafficMirroringCfgTable {
		if r.TenantID.Int64() == tenantId && r.ComputeRegionIdx.Int64() == computeRegionId &&
			r.CloudProvider.String() == cloudProvider {
			cpy := *r
			return &cpy, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) InsertProvisionAPIStatus(row *provision_api_status.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	acc.provisionAPIStatusTable = append(acc.provisionAPIStatusTable, row)
	return nil
}

func (acc *SimDbAccessor) SetProvisionAPIStatus(eLog *clogger.EventLogger, fields map[string]interface{}) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.provisionAPIStatusTable {
		if r.UUID.String() != eLog.TraceID {
			continue
		}
		for k, v := range fields {
			switch k {
			case "input_yaml":
				r.InputYAML = sql.String(v.(string))
			case "run_id":
				r.RunID = sql.String(v.(string))
			case "status":
				r.Status = sql.String(v.(string))
			case "generate_config_status":
				r.GenerateConfigStatus = sql.Int64(v.(int))
			case "apply_config_status":
				r.ApplyConfigStatus = sql.Int64(v.(int))
			case "post_process_status":
				r.PostProcessStatus = sql.Int64(v.(int))
			case "send_callback_status":
				r.SendCallbackStatus = sql.Int64(v.(int))
			case "error_reason":
				r.ErrorReason = sql.String(v.(string))
			}
		}
	}
	return nil
}

func (acc *SimDbAccessor) GetInProgressProvisionEntries(eLog *clogger.EventLogger) ([]*provision_api_status.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	var ret []*provision_api_status.Row
	for _, r := range acc.provisionAPIStatusTable {
		if r.GenerateConfigStatus.Int64()+r.ApplyConfigStatus.Int64()+r.PostProcessStatus.Int64()+r.SendCallbackStatus.Int64() < 4 {
			ret = append(ret, r)
		}
	}
	return ret, nil
}

func (acc *SimDbAccessor) GetProvisionAPIStatusByUUID(eLog *clogger.EventLogger, uuid string) (*provision_api_status.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.provisionAPIStatusTable {
		if r.UUID.String() == uuid {
			return r, nil
		}
	}
	return nil, fmt.Errorf("row not found")
}

func (acc *SimDbAccessor) GetRowForRegion(tenancyId string, regionName string, networkType string, shardId int64, eLog *clogger.EventLogger) (*oci_shared_network_info.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.ociSharedNetworkInfoTable {
		if r.TenancyId.String() == tenancyId && r.RegionName.String() == regionName &&
			r.NetworkType.String() == networkType && r.ShardId.Int64() == shardId {
			return r, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) SaveTenantCompartment(row *oci_tenant_network_info.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	acc.ociTenantNetworkInfoTable = append(acc.ociTenantNetworkInfoTable, row)
	return nil
}

func (acc *SimDbAccessor) SaveTenantNetwork(row *oci_tenant_network_info.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	acc.ociTenantNetworkInfoTable = append(acc.ociTenantNetworkInfoTable, row)
	return nil
}

func (acc *SimDbAccessor) UpdateTenantNetwork(row *oci_tenant_network_info.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for i, r := range acc.ociTenantNetworkInfoTable {
		if r.AcctId.Int64() == row.AcctId.Int64() {
			// Remove the element at index i from acc.ociTenantNetworkInfoTable
			acc.ociTenantNetworkInfoTable = append(acc.ociTenantNetworkInfoTable[:i], acc.ociTenantNetworkInfoTable[i+1:]...)
		}
	}

	// now append the updated row to the table
	acc.ociTenantNetworkInfoTable = append(acc.ociTenantNetworkInfoTable, row)

	return nil
}

func (acc *SimDbAccessor) DeleteTenantNetwork(row *oci_tenant_network_info.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for i, r := range acc.ociTenantNetworkInfoTable {
		if r.CompartmentId.String() == row.CompartmentId.String() && r.RegionName.String() == row.RegionName.String() {
			acc.ociTenantNetworkInfoTable = append(acc.ociTenantNetworkInfoTable[:i], acc.ociTenantNetworkInfoTable[i+1:]...)
		}
	}

	return nil
}

func (acc *SimDbAccessor) GetRowForTenantCompartment(acctID int64, eLog *clogger.EventLogger) (*oci_tenant_network_info.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.ociTenantNetworkInfoTable {
		if r.AcctId.Int64() == acctID {
			return r, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetRowForTenantNetwork(acctID int64, regionName string, eLog *clogger.EventLogger) (*oci_tenant_network_info.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.ociTenantNetworkInfoTable {
		if r.AcctId.Int64() == acctID && r.RegionName.String() == regionName {
			return r, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetGcpProjectsPoolByName(name string, eLog *clogger.EventLogger) (*gcp_projects_pool.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.gcpProjectsPoolTable {
		if r.Name.String() == name {
			return r, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetCustEpaasConfigByCustIdAndComputeRegionIdAndNodeType(custId int64, computeRegionId int64, nodeType int64, eLog *clogger.EventLogger) (*cust_epaas_config.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.custEpaasConfigEntryTable {
		if r.CustID.Int64() == custId && r.ComputeRegionId.Int64() == computeRegionId && r.NodeType.Int64() == nodeType {
			return r, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetEproxyMappingByZoneAndName(zone int64, name string, eLog *clogger.EventLogger) (*eproxy_mapping.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.eproxyMappingTable {
		if r.Zone.Int64() == zone && r.Name.String() == name {
			return r, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetExplicitProxyTenantConfigRowByTenantId(tenantId int64, eLog *clogger.EventLogger) (*explicit_proxy_tenant_info.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.explicitProxyTenantInfoTable {
		if r.TenantId.Int64() == tenantId {
			return r, nil
		}
	}
	return nil, sql.ErrNoRows
}

func (acc *SimDbAccessor) GetInstanceUpgradePendingRowsByCustIDAndRegion(custId, regionId int64, eLog *clogger.EventLogger) ([]*instance_upgrade.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	var output []*instance_upgrade.Row
	for _, r := range acc.instanceUpgradeTable {
		createTime, err := time.Parse("2006-01-02 15:04:05", r.CreateTime.String())
		if err != nil {
			eLog.LogError("Error parsing CreateTime: %v", err)
			return nil, err
		}
		twoWeeks := 2 * 7 * 24 * time.Hour
		if r.CustID.String() == strconv.FormatInt(custId, 10) && r.Region.String() == strconv.FormatInt(regionId, 10) && createTime.After(time.Now().Add(-2*twoWeeks)) {
			output = append(output, r)
		}
	}
	return output, nil
}

func (acc *SimDbAccessor) DataDumpSupported(eLog *clogger.EventLogger) bool {
	return true
}

func (acc *SimDbAccessor) GetDbConn() *sql.DbConn {
	return nil
}

func (acc *SimDbAccessor) DataDump(eLog *clogger.EventLogger) bool {
	acc.Lock()
	defer acc.Unlock()

	// TODO: For now skipping readonly tables below, revisit if necessary:
	//  - custMasterTable
	//  - instMasterTable
	//  - orchCfgTable
	tables := map[string]interface{}{
		acc.instMgmtTablePath:   acc.instMgmtTable,
		acc.instMgmtTfTablePath: acc.instMgmtTfTable,
	}
	dump := func(tableName string, table interface{}) error {
		perm := os.FileMode(0644) // default file mode
		statinfo, err := os.Stat(tableName)
		if err == nil {
			perm = statinfo.Mode()
		} else if _, ok := err.(*os.PathError); !ok {
			eLog.LogError("Error retrieving info on file %v: %v", tableName, err)
			return err
		}
		f, err := os.OpenFile(tableName, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, perm)
		if err != nil {
			eLog.LogError("Failed to open file %v: %v", tableName, err)
			return err
		}
		bytes, err := json.MarshalIndent(table, "", "\t")
		if err != nil {
			eLog.LogError("Failed to JSON marshal data for file %v: %v", tableName, err)
			return err
		}
		nb, err := f.Write(bytes)
		if err != nil {
			eLog.LogError("Failed to output file %v: %v number of bytes written=%v", tableName, err, nb)
			return err
		}
		err = f.Sync()
		if err != nil {
			eLog.LogError("Failed to sync file %v: %v", tableName, err)
			return err
		}
		return nil
	}

	success := true
	for n, t := range tables {
		if n != "" {
			if err := dump(n, t); err == nil {
				eLog.LogInfo("Successfully output to file %v", n)
			} else {
				success = false
			}
		}
	}
	return success
}

// unmarshalJsonBySqlTag unmarshals JSON data into a struct or slice of structs,
// matching JSON keys with struct fields based on SQL tag names.
//
// Parameters:
//   - log: A pointer to a clogger.Clogger for logging.
//   - jsonData: The JSON data to unmarshal.
//   - holder: A pointer to the struct or slice of structs to unmarshal into.
//
// The function works as follows:
//  1. It verifies that the holder is a non-nil pointer to a struct or slice of structs.
//  2. It creates a map of SQL tag names to field names for the target struct.
//  3. It unmarshals the JSON data into a map or slice of maps.
//  4. It reformats the map(s) to use struct field names as keys instead of SQL tag names.
//  5. It marshals the reformatted map(s) back to JSON.
//  6. Finally, it unmarshals the reformatted JSON into the holder.
//
// This approach allows for proper matching between JSON keys and struct fields
// when the struct fields use SQL tags instead of JSON tags.
//
// Returns an error if any step in the process fails.
func unmarshalJsonBySqlTag(log *clogger.Clogger, jsonData []byte, holder interface{}) error {
	pVal := reflect.ValueOf(holder)
	if pVal.Kind() != reflect.Pointer || pVal.IsNil() {
		return fmt.Errorf("input object must be a non-nil pointer")
	}

	pVal = pVal.Elem()
	typ := pVal.Type()
	if typ.Kind() == reflect.Interface {
		pVal = pVal.Elem()
		typ = pVal.Type()
	}

	isSlice := typ.Kind() == reflect.Slice
	if isSlice {
		typ = typ.Elem()
		if typ.Kind() == reflect.Pointer {
			typ = typ.Elem()
		}
	}
	if typ.Kind() != reflect.Struct {
		return fmt.Errorf("input object must be a pointer to a struct, a struct slice, or a struct pointer slice")
	}

	// Create a map of SQL tag names to field names
	fieldNames := make(map[string]string)
	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		if sqlTag := field.Tag.Get("sql"); sqlTag != "" {
			fieldNames[sqlTag] = field.Name
		}
	}

	reformatKey := func(srcMap map[string]interface{}) map[string]interface{} {
		newMap := make(map[string]interface{})
		for k, v := range srcMap {
			if fieldName, exist := fieldNames[k]; exist {
				newMap[fieldName] = v
			} else {
				// log.LogDebug("Skipping key: %v (no matching SQL tag)", k)
			}
		}
		return newMap
	}

	var data []byte
	var err error

	if isSlice {
		var mapSlice []map[string]interface{}
		if err = json.Unmarshal(jsonData, &mapSlice); err != nil {
			return fmt.Errorf("failed to unmarshal JSON array to maps: %w", err)
		}
		newMapSlice := make([]map[string]interface{}, len(mapSlice))
		for i, m := range mapSlice {
			newMapSlice[i] = reformatKey(m)
		}
		if data, err = json.Marshal(newMapSlice); err != nil {
			return fmt.Errorf("failed to marshal maps to JSON array: %w", err)
		}
	} else {
		var singleMap map[string]interface{}
		if err = json.Unmarshal(jsonData, &singleMap); err != nil {
			return fmt.Errorf("failed to unmarshal JSON to map: %w", err)
		}
		newMap := reformatKey(singleMap)
		if data, err = json.Marshal(newMap); err != nil {
			return fmt.Errorf("failed to marshal map to JSON: %w", err)
		}
	}

	return json.Unmarshal(data, holder)
}

func (acc *SimDbAccessor) GetIngressCustConfigByID(id, RegionId int64, eLog *clogger.EventLogger) (*ingress_cust_config.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	custzero := &ingress_cust_config.Row{}
	regionIdStr := strconv.FormatInt(RegionId, 10)
	for _, r := range acc.ingressCustConfigTable {
		if r.CustID.Int64() == id && r.ComputeRegionID.String() == regionIdStr {
			return r, nil
		}

		if r.CustID.Int64() == 0 {
			custzero = r
		}
	}
	if custzero != nil {
		return custzero, nil
	}
	return nil, fmt.Errorf("row not found")
}

func (acc *SimDbAccessor) GetAllIngressCustConfigByID(id int64, eLog *clogger.EventLogger) ([]*ingress_cust_config.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	var output []*ingress_cust_config.Row
	for _, r := range acc.ingressCustConfigTable {
		if r.CustID.Int64() == id {
			output = append(output, r)
		}

		if r.CustID.Int64() == 0 {
			output = append(output, r)
		}
	}
	return output, nil
}

func (acc *SimDbAccessor) SaveToIngressMaster(row *ingress_master.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	acc.ingressMasterTable = append(acc.ingressMasterTable, row)
	return nil
}

func (acc *SimDbAccessor) UpdateFQDNinIngressMaster(row *ingress_master.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for _, r := range acc.ingressMasterTable {
		if r.CustID.Int64() == int64(row.CustID) && r.ComputeRegionID.String() == row.ComputeRegionID.String() {
			r.RegionalFQDN = row.RegionalFQDN
		}
	}
	return nil
}

func (acc *SimDbAccessor) GetIngressMasterEntryByCustIdAndRegionId(custId, regionId int64, eLog *clogger.EventLogger) (*ingress_master.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	regionIdstr := strconv.FormatInt(regionId, 10)
	for _, r := range acc.ingressMasterTable {
		if r.CustID.Int64() == custId && r.ComputeRegionID.String() == regionIdstr {
			return r, nil
		}
	}
	return nil, fmt.Errorf("row not found for custId %d and regionId %s", custId, regionId)
}

func (acc *SimDbAccessor) GetIngressMasterEntryByCustId(custId int64, eLog *clogger.EventLogger) ([]*ingress_master.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	var output []*ingress_master.Row
	for _, r := range acc.ingressMasterTable {
		if r.CustID.Int64() == custId {
			output = append(output, r)
		}
	}
	return output, nil
}

func (acc *SimDbAccessor) SaveToIngressCustConfig(row *ingress_cust_config.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	acc.ingressCustConfigTable = append(acc.ingressCustConfigTable, row)
	return nil
}
func (acc *SimDbAccessor) CreateOrUpdateToIngressCustConfig(row *ingress_cust_config.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	// Check if a row with the same CustID already exists
	for i, existingRow := range acc.ingressCustConfigTable {
		if existingRow.CustID == row.CustID {
			// Update existing row
			acc.ingressCustConfigTable[i] = row
			return nil
		}
	}

	// If no existing row found, create new one
	acc.ingressCustConfigTable = append(acc.ingressCustConfigTable, row)
	return nil
}
func (acc *SimDbAccessor) GetIngressCustConfigByCustId(custId int64, eLog *clogger.EventLogger) (*ingress_cust_config.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.ingressCustConfigTable {
		if r.CustID.Int64() == custId {
			return r, nil
		}
	}
	return nil, fmt.Errorf("row not found for customer ID: %d", custId)
}

func (acc *SimDbAccessor) GetIngressClusterConfigByRegionId(regionId int64, eLog *clogger.EventLogger) (*ingress_cluster_config.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	for _, r := range acc.ingressClusterConfigTable {
		if r.RegionID.Int64() == regionId {
			return r, nil
		}
	}
	return nil, fmt.Errorf("row not found for regionId: %d", regionId)
}

func (acc *SimDbAccessor) DeleteIngressMasterEntry(row *ingress_master.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for i, r := range acc.ingressMasterTable {
		if r.CustID == row.CustID && r.ComputeRegionID == row.ComputeRegionID {
			acc.ingressMasterTable = append(acc.ingressMasterTable[:i], acc.ingressMasterTable[i+1:]...)
		}
	}
	return nil
}

func (acc *SimDbAccessor) SaveToIngressPodMaster(row *ingress_pod_master.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	acc.ingressPodMasterTable = append(acc.ingressPodMasterTable, row)
	return nil
}

func (acc *SimDbAccessor) GetIngressPodMasterEntryByCustIdAndRegionId(custId, regionId int64, eLog *clogger.EventLogger) (*ingress_pod_master.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	regionIdstr := strconv.FormatInt(regionId, 10)
	for _, r := range acc.ingressPodMasterTable {
		if r.CustID.Int64() == custId && r.ComputeRegionID.String() == regionIdstr {
			return r, nil
		}
	}
	return nil, fmt.Errorf("row not found for custId %d and regionId %s", custId, regionId)
}

func (acc *SimDbAccessor) GetIngressPodMasterEntryByCustId(custId int64, eLog *clogger.EventLogger) ([]*ingress_pod_master.Row, error) {
	acc.RLock()
	defer acc.RUnlock()

	var output []*ingress_pod_master.Row
	for _, r := range acc.ingressPodMasterTable {
		if r.CustID.Int64() == custId {
			output = append(output, r)
		}
	}
	return output, nil
}

func (acc *SimDbAccessor) DeleteIngressPodMasterEntry(row *ingress_pod_master.Row, eLog *clogger.EventLogger) error {
	acc.Lock()
	defer acc.Unlock()

	for i, r := range acc.ingressPodMasterTable {
		if r.CustID == row.CustID && r.ComputeRegionID == row.ComputeRegionID {
			acc.ingressPodMasterTable = append(acc.ingressPodMasterTable[:i], acc.ingressPodMasterTable[i+1:]...)
		}
	}
	return nil
}
