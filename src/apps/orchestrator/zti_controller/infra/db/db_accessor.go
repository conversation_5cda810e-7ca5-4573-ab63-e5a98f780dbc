package db

import (
	"orchestrator/libs/go/dbaccess/models/colo_connect_link_master"
	"orchestrator/libs/go/dbaccess/models/colo_onboarding_master"
	"orchestrator/libs/go/dbaccess/models/cust_epaas_config"
	"orchestrator/libs/go/dbaccess/models/cust_master"
	"orchestrator/libs/go/dbaccess/models/eproxy_mapping"
	"orchestrator/libs/go/dbaccess/models/explicit_proxy_tenant_info"
	"orchestrator/libs/go/dbaccess/models/gcp_projects_pool"
	"orchestrator/libs/go/dbaccess/models/ingress_cluster_config"
	"orchestrator/libs/go/dbaccess/models/ingress_cust_config"
	"orchestrator/libs/go/dbaccess/models/ingress_master"
	"orchestrator/libs/go/dbaccess/models/ingress_pod_master"
	"orchestrator/libs/go/dbaccess/models/instance_master"
	"orchestrator/libs/go/dbaccess/models/instance_upgrade"
	"orchestrator/libs/go/dbaccess/models/network_load_balancer_config"
	"orchestrator/libs/go/dbaccess/models/oci_shared_network_info"
	"orchestrator/libs/go/dbaccess/models/oci_tenant_network_info"
	"orchestrator/libs/go/dbaccess/models/orch_cfg"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_gcp_table"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_table"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_tf_import_table"
	"orchestrator/libs/go/dbaccess/models/orch_instance_management_tf_table"
	"orchestrator/libs/go/dbaccess/models/orch_project_management_tf_table"
	"orchestrator/libs/go/dbaccess/models/provision_api_status"
	"orchestrator/libs/go/dbaccess/models/region_master"
	"orchestrator/libs/go/dbaccess/models/traffic_mirroring_cfg"
	"orchestrator/libs/go/dbaccess/sql"
	pconfig "orchestrator/zti_controller/infra/config"
	"orchestrator/zti_controller/infra/db/rds"
	"orchestrator/zti_controller/infra/db/simulator"
	"orchestrator/zti_controller/infra/logger"

	"go.panw.local/pangolin/clogger"
)

type DbAccessor interface {
	// cust_master
	GetCustMasterById(id int64, eLog *clogger.EventLogger) (*cust_master.Row, error)
	GetCustMasterByAcctId(acctId int64, eLog *clogger.EventLogger) (*cust_master.Row, error)

	// instance_master
	GetInstMasterById(id int64, eLog *clogger.EventLogger) (*instance_master.Row, error)
	GetInstMasterByCustomerRegion(custId, regionId, serviceTypeId int64, eLog *clogger.EventLogger) ([]*instance_master.Row, error)

	// orch_instance_management_table
	GetInstMgmtByUniqKeys(custId, regionId int64, cloudProvider string, serviceTypeId int64, eLog *clogger.EventLogger) (*orch_instance_management_table.Row, error)
	GetInstMgmtByEIDX(eidx int64, eLog *clogger.EventLogger) (*orch_instance_management_table.Row, error)
	GetInstMgmtByEIDXList(eidxList []int64, eLog *clogger.EventLogger) ([]*orch_instance_management_table.Row, error)
	GetInstMgmtByGcpProvisionType(gcpProvisionType string, eLog *clogger.EventLogger) ([]*orch_instance_management_table.Row, error)
	CreateInstMgmt(row *orch_instance_management_table.Row, eLog *clogger.EventLogger) error
	UpdateInstMgmtGcpProvisionType(row *orch_instance_management_table.Row, eLog *clogger.EventLogger) error
	UpdateInstMgmtWorkspaceName(row *orch_instance_management_table.Row, eLog *clogger.EventLogger) error
	UpdateInstMgmtPostTerraformMigration(row *orch_instance_management_table.Row, eLog *clogger.EventLogger) error

	// inst_mgmt_tf
	GetInstMgmtTfByInstMgmtId(instMgmtId int64, eLog *clogger.EventLogger) (*orch_instance_management_tf_table.Row, error)
	GetInstMgmtTfStage2(eLog *clogger.EventLogger) ([]*orch_instance_management_tf_table.Row, error)
	GetInstMgmtTfStage3(eLog *clogger.EventLogger) ([]*orch_instance_management_tf_table.Row, error)
	GetInstMgmtTfStage4(eLog *clogger.EventLogger) ([]*orch_instance_management_tf_table.Row, error)
	GetInstMgmtTfStage5(eLog *clogger.EventLogger) ([]*orch_instance_management_tf_table.Row, error)
	CreateInstMgmtTf(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error
	UpdateInstMgmtTf(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error
	UpdateInstMgmtTfTriggerUpdate(instMgmtId, triggerUpdate int64, eLog *clogger.EventLogger) error
	UpdateInstMgmtTfAlerts(instMgmtId int64, alerts string, eLog *clogger.EventLogger) error
	UpdateInstMgmtTfMaxAttempted(instMgmtId, maxAttempted int64, eLog *clogger.EventLogger) error
	UpdateInstMgmtTfStage1(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error
	UpdateInstMgmtTfStage2(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error
	UpdateInstMgmtTfStage3(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error
	UpdateInstMgmtTfStage4(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error
	UpdateInstMgmtTfStage5(row *orch_instance_management_tf_table.Row, eLog *clogger.EventLogger) error

	// orch_cfg
	GetOrchCfgByName(name string, eLog *clogger.EventLogger) (*orch_cfg.Row, error)

	// network_load_balancer_config
	GetNetworkLoadBalancerConfigByCustomerRegionElseDefault(custId, regionId int64, npgaProtocolEnabled bool, eLog *clogger.EventLogger, cloudProvider string) (*network_load_balancer_config.Row, error)

	// orch_instance_management_tf_import_table
	GetInstMgmtTfImportByInstMgmtId(instMgmtId int64, eLog *clogger.EventLogger) (*orch_instance_management_tf_import_table.Row, error)
	GetInstMgmtTfImportByKey(provisionType string, custId, regionId, serviceId int64, cloudProvider string, eLog *clogger.EventLogger) (*orch_instance_management_tf_import_table.Row, error)
	CreateInstMgmtTfImport(row *orch_instance_management_tf_import_table.Row, eLog *clogger.EventLogger) error
	UpdateInstMgmtTfImportRequestStates(row *orch_instance_management_tf_import_table.Row, eLog *clogger.EventLogger) error
	UpdateInstMgmtTfImportRunStates(row *orch_instance_management_tf_import_table.Row, eLog *clogger.EventLogger) error

	// orch_instance_management_gcp_table
	GetInstMgmtGcpByInstMgmtId(instMgmtId int64, eLog *clogger.EventLogger) (*orch_instance_management_gcp_table.Row, error)
	UpdateInstMgmtGcpTriggerUpdateBy10(row *orch_instance_management_gcp_table.Row, eLog *clogger.EventLogger) error

	// orch_project_management_tf_table
	GetPrjMgmtTfByKey(projectID, cloudProvider, ociTenancyID string, eLog *clogger.EventLogger) (*orch_project_management_tf_table.Row, error)
	CreatePrjMgmtTf(row *orch_project_management_tf_table.Row, eLog *clogger.EventLogger) error
	UpdatePrjMgmtTf(row *orch_project_management_tf_table.Row, eLog *clogger.EventLogger) error
	DeletePrjMgmtTf(row *orch_project_management_tf_table.Row, eLog *clogger.EventLogger) error

	// region_master
	GetRegionMasterById(id int64, eLog *clogger.EventLogger) (*region_master.Row, error)
	GetRegionMasterByNativeRegionName(cloudProvider, nativeRegionName string, eLog *clogger.EventLogger) (*region_master.Row, error)

	// oci shared network info
	GetRowForRegion(tenancyId string, regionName string, networkType string, shardId int64, eLog *clogger.EventLogger) (*oci_shared_network_info.Row, error)

	// oci tenant compartment and network info
	SaveTenantCompartment(row *oci_tenant_network_info.Row, eLog *clogger.EventLogger) error
	GetRowForTenantCompartment(acctID int64, eLog *clogger.EventLogger) (*oci_tenant_network_info.Row, error)
	SaveTenantNetwork(row *oci_tenant_network_info.Row, eLog *clogger.EventLogger) error
	UpdateTenantNetwork(row *oci_tenant_network_info.Row, eLog *clogger.EventLogger) error
	DeleteTenantNetwork(row *oci_tenant_network_info.Row, eLog *clogger.EventLogger) error
	GetRowForTenantNetwork(acctID int64, regionName string, eLog *clogger.EventLogger) (*oci_tenant_network_info.Row, error)

	// colo_onboarding_master
	GetColoOnboardingMasterByKey(custId, computeRegionId int64, eLog *clogger.EventLogger) ([]*colo_onboarding_master.Row, error)

	// colo_connect_link_master
	GetColoConnLinkMasterByIds(ids []int64, eLog *clogger.EventLogger) ([]*colo_connect_link_master.Row, error)

	// traffic_mirroring_cfg
	GetTrafficMirroringCfgByKey(tenantId, computeRegionId int64, cloudProvider string, eLog *clogger.EventLogger) (*traffic_mirroring_cfg.Row, error)

	// provision_api_status
	InsertProvisionAPIStatus(*provision_api_status.Row, *clogger.EventLogger) error
	SetProvisionAPIStatus(*clogger.EventLogger, map[string]interface{}) error
	GetInProgressProvisionEntries(*clogger.EventLogger) ([]*provision_api_status.Row, error)
	GetProvisionAPIStatusByUUID(*clogger.EventLogger, string) (*provision_api_status.Row, error)

	// gcp_projects_pool
	GetGcpProjectsPoolByName(name string, eLog *clogger.EventLogger) (*gcp_projects_pool.Row, error)

	// cust epaas config
	GetCustEpaasConfigByCustIdAndComputeRegionIdAndNodeType(custId int64, computeRegionId int64, node_type int64, eLog *clogger.EventLogger) (*cust_epaas_config.Row, error)

	// eproxy mapping
	GetEproxyMappingByZoneAndName(zone int64, name string, eLog *clogger.EventLogger) (*eproxy_mapping.Row, error)

	// explicit proxy tenant info
	GetExplicitProxyTenantConfigRowByTenantId(tenantId int64, eLog *clogger.EventLogger) (*explicit_proxy_tenant_info.Row, error)

	// instance_upgrade
	GetInstanceUpgradePendingRowsByCustIDAndRegion(custId, regionId int64, eLog *clogger.EventLogger) ([]*instance_upgrade.Row, error)

	// Ingress_cust_config
	GetIngressCustConfigByID(custId, RegionId int64, eLog *clogger.EventLogger) (*ingress_cust_config.Row, error)
	GetAllIngressCustConfigByID(custId int64, eLog *clogger.EventLogger) ([]*ingress_cust_config.Row, error)
	SaveToIngressCustConfig(row *ingress_cust_config.Row, eLog *clogger.EventLogger) error
	CreateOrUpdateToIngressCustConfig(row *ingress_cust_config.Row, eLog *clogger.EventLogger) error
	GetIngressCustConfigByCustId(custId int64, eLog *clogger.EventLogger) (*ingress_cust_config.Row, error)

	//ingress master entry
	SaveToIngressMaster(*ingress_master.Row, *clogger.EventLogger) error
	UpdateFQDNinIngressMaster(*ingress_master.Row, *clogger.EventLogger) error
	GetIngressMasterEntryByCustId(custId int64, eLog *clogger.EventLogger) ([]*ingress_master.Row, error)
	GetIngressMasterEntryByCustIdAndRegionId(custId, regionId int64, eLog *clogger.EventLogger) (*ingress_master.Row, error)
	DeleteIngressMasterEntry(row *ingress_master.Row, eLog *clogger.EventLogger) error

	// Cluster config
	GetIngressClusterConfigByRegionId(regionId int64, eLog *clogger.EventLogger) (*ingress_cluster_config.Row, error)

	// ingress pod master
	SaveToIngressPodMaster(*ingress_pod_master.Row, *clogger.EventLogger) error
	GetIngressPodMasterEntryByCustId(custId int64, eLog *clogger.EventLogger) ([]*ingress_pod_master.Row, error)
	GetIngressPodMasterEntryByCustIdAndRegionId(custId, regionId int64, eLog *clogger.EventLogger) (*ingress_pod_master.Row, error)
	DeleteIngressPodMasterEntry(row *ingress_pod_master.Row, eLog *clogger.EventLogger) error

	// generic helpers
	DataDumpSupported(eLog *clogger.EventLogger) bool
	DataDump(eLog *clogger.EventLogger) bool
	GetDbConn() *sql.DbConn
}

var dbAccessor DbAccessor

func Initialize() error {
	var err error
	cfg := pconfig.GetConfig()
	log := logger.GetGlobalLogger()

	simulation := cfg.ZtiController.DbSimulation
	if simulation {
		dbAccessor, err = simulator.NewDBAccessor(log)
	} else {
		dbAccessor, err = rds.NewDBAccessor(cfg, log)
	}
	if err != nil {
		log.LogError("Failed to create DBAccessor: %v, simulation=%v", err, simulation)
	}
	return err
}

// SetDbAccessor force sets the dbAccessor singleton. Once it is set all subsequent GetDbAccessor() call returns
// the same DbAccessor passed-in at the last invocation of this method. This is only used during testing
func SetDbAccessor(dba DbAccessor) {
	dbAccessor = dba
}

func GetDbAccessor() DbAccessor {
	return dbAccessor
}
