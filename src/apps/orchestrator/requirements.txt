#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile --pip-args='--no-build-isolation' requirements.in
#
annotated-types==0.7.0
    # via pydantic
anyio==4.10.0
    # via starlette
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
boto3==1.21.9
    # via
    #   -r requirements.in
    #   moto
botocore==1.24.46
    # via
    #   boto3
    #   moto
    #   s3transfer
cachetools==5.5.2
    # via google-auth
certifi==2025.8.3
    # via
    #   oci
    #   requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.2
    # via requests
circuitbreaker==2.1.3
    # via oci
click==8.2.1
    # via uvicorn
cryptography==42.0.2
    # via
    #   -r requirements.in
    #   jwcrypto
    #   moto
    #   oci
    #   pyopenssl
decorator==5.2.1
    # via retry
deprecated==1.2.18
    # via google-cloud-spanner
dnspython==2.7.0
    # via pymongo
fastapi==0.109.2
    # via -r requirements.in
geographiclib==2.0
    # via geopy
geopy==2.4.1
    # via -r requirements.in
google-api-core[grpc]==2.25.1
    # via
    #   google-api-python-client
    #   google-cloud-bigquery
    #   google-cloud-core
    #   google-cloud-monitoring
    #   google-cloud-pubsub
    #   google-cloud-resource-manager
    #   google-cloud-secret-manager
    #   google-cloud-spanner
google-api-python-client==2.116.0
    # via -r requirements.in
google-auth==2.27.0
    # via
    #   -r requirements.in
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-cloud-bigquery
    #   google-cloud-core
    #   google-cloud-monitoring
    #   google-cloud-resource-manager
google-auth-httplib2==0.2.0
    # via google-api-python-client
google-cloud-bigquery==3.35.1
    # via -r requirements.in
google-cloud-core==2.4.3
    # via
    #   google-cloud-bigquery
    #   google-cloud-spanner
google-cloud-monitoring==2.21.0
    # via -r requirements.in
google-cloud-pubsub==2.19.1
    # via -r requirements.in
google-cloud-resource-manager==1.12.5
    # via -r requirements.in
google-cloud-secret-manager==2.18.0
    # via -r requirements.in
google-cloud-spanner==3.42.0
    # via -r requirements.in
google-crc32c==1.7.1
    # via google-resumable-media
google-resumable-media==2.7.2
    # via google-cloud-bigquery
googleapis-common-protos[grpc]==1.70.0
    # via
    #   google-api-core
    #   grpc-google-iam-v1
    #   grpcio-status
grpc-google-iam-v1==0.14.2
    # via
    #   google-cloud-pubsub
    #   google-cloud-resource-manager
    #   google-cloud-secret-manager
    #   google-cloud-spanner
grpc-interceptor==0.15.4
    # via google-cloud-spanner
grpcio==1.74.0
    # via
    #   google-api-core
    #   google-cloud-pubsub
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpc-interceptor
    #   grpcio-status
grpcio-status==1.62.3
    # via
    #   google-api-core
    #   google-cloud-pubsub
h11==0.16.0
    # via uvicorn
httplib2==0.22.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
idna==3.10
    # via
    #   anyio
    #   requests
influxdb==5.3.1
    # via -r requirements.in
jinja2==3.1.6
    # via moto
jmespath==0.10.0
    # via
    #   boto3
    #   botocore
jsonpickle==2.1.0
    # via -r requirements.in
jsonschema==4.23.0
    # via -r requirements.in
jsonschema-specifications==2025.4.1
    # via jsonschema
jwcrypto==1.5.6
    # via -r requirements.in
markupsafe==3.0.2
    # via
    #   jinja2
    #   moto
    #   werkzeug
moto==3.0.5
    # via -r requirements.in
msgpack==1.1.1
    # via influxdb
mysql-connector-python==8.4.0
    # via -r requirements.in
netaddr==0.8.0
    # via -r requirements.in
ns1-python==0.17.1
    # via -r requirements.in
oci==2.139.0
    # via -r requirements.in
packaging==25.0
    # via
    #   -r requirements.in
    #   google-cloud-bigquery
pem==21.2.0
    # via -r requirements.in
proto-plus==1.26.1
    # via
    #   google-api-core
    #   google-cloud-monitoring
    #   google-cloud-pubsub
    #   google-cloud-resource-manager
    #   google-cloud-secret-manager
    #   google-cloud-spanner
protobuf==4.25.8
    # via
    #   google-api-core
    #   google-cloud-monitoring
    #   google-cloud-pubsub
    #   google-cloud-resource-manager
    #   google-cloud-secret-manager
    #   google-cloud-spanner
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
    #   proto-plus
py==1.11.0
    # via retry
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pycryptodome==3.20.0
    # via -r requirements.in
pydantic==2.11.7
    # via fastapi
pydantic-core==2.33.2
    # via pydantic
pyjwt==2.4.0
    # via -r requirements.in
pymongo==4.13.2
    # via -r requirements.in
pymysql==1.1.1
    # via -r requirements.in
pyopenssl==24.0.0
    # via
    #   -r requirements.in
    #   oci
pyparsing==3.2.3
    # via httplib2
python-dateutil==2.9.0.post0
    # via
    #   botocore
    #   google-cloud-bigquery
    #   influxdb
    #   moto
    #   oci
pytz==2025.2
    # via
    #   influxdb
    #   moto
    #   oci
pyyaml==6.0.2
    # via
    #   -r requirements.in
    #   responses
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.4
    # via
    #   google-api-core
    #   google-cloud-bigquery
    #   influxdb
    #   moto
    #   responses
responses==0.25.7
    # via moto
retry==0.9.2
    # via -r requirements.in
rpds-py==0.26.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via google-auth
s3transfer==0.5.2
    # via boto3
six==1.17.0
    # via
    #   influxdb
    #   python-dateutil
sniffio==1.3.1
    # via anyio
sqlparse==0.5.3
    # via google-cloud-spanner
starlette==0.36.3
    # via fastapi
typing-extensions==4.14.1
    # via
    #   anyio
    #   fastapi
    #   jwcrypto
    #   pydantic
    #   pydantic-core
    #   referencing
    #   typing-inspection
typing-inspection==0.4.1
    # via pydantic
uritemplate==4.2.0
    # via google-api-python-client
urllib3==1.26.20
    # via
    #   botocore
    #   requests
    #   responses
uvicorn==0.18.3
    # via -r requirements.in
werkzeug==3.1.3
    # via moto
wrapt==1.17.2
    # via deprecated
xmltodict==0.13.0
    # via
    #   -r requirements.in
    #   moto
