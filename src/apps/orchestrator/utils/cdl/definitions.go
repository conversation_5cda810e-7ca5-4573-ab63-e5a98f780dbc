package cdl

const (
	CDL_REGION_UNKNOWN_IDX   int = -1
	CDL_REGION_AMERICAS_IDX      = 1
	CDL_REGION_EUROPE_IDX        = 2
	CDL_REGION_UK_IDX            = 3
	CDL_REGION_FEDRAMP_IDX       = 4
	CDL_REGION_CANADA_IDX        = 5
	CDL_REGION_JAPAN_IDX         = 6
	CDL_REGION_AUSTRALIA_IDX     = 7
	CDL_REGION_GERMANY_IDX       = 8
	CDL_REGION_INDIA_IDX         = 9
	CDL_REGION_SINGAPORE_IDX     = 10
)

const (
	CDL_REGION_UNKNOWN   string = "unknown"
	CDL_REGION_AMERICAS         = "americas"
	CDL_REGION_EUROPE           = "europe"
	CDL_REGION_UK               = "uk"
	CDL_REGION_FEDRAMP          = "gov"
	CDL_REGION_CANADA           = "ca"
	CDL_REGION_JAPAN            = "jp"
	CDL_REGION_AUSTRALIA        = "au"
	CDL_REGION_GERMANY          = "de"
	CDL_REGION_INDIA            = "in"
	CDL_REGION_SINGAPORE        = "sg"
)

var (
	AppAssociationRegionStringLookupTable = map[string]int{
		CDL_REGION_UNKNOWN:   CDL_REGION_UNKNOWN_IDX,
		CDL_REGION_AMERICAS:  CDL_REGION_AMERICAS_IDX,
		CDL_REGION_EUROPE:    CDL_REGION_EUROPE_IDX,
		CDL_REGION_UK:        CDL_REGION_UK_IDX,
		CDL_REGION_FEDRAMP:   CDL_REGION_FEDRAMP_IDX,
		CDL_REGION_CANADA:    CDL_REGION_CANADA_IDX,
		CDL_REGION_JAPAN:     CDL_REGION_JAPAN_IDX,
		CDL_REGION_AUSTRALIA: CDL_REGION_AUSTRALIA_IDX,
		CDL_REGION_GERMANY:   CDL_REGION_GERMANY_IDX,
		CDL_REGION_INDIA:     CDL_REGION_INDIA_IDX,
		CDL_REGION_SINGAPORE: CDL_REGION_SINGAPORE_IDX,
	}

	AppAssociationRegionIntLookupTable = map[int]string{
		CDL_REGION_UNKNOWN_IDX:   CDL_REGION_UNKNOWN,
		CDL_REGION_AMERICAS_IDX:  CDL_REGION_AMERICAS,
		CDL_REGION_EUROPE_IDX:    CDL_REGION_EUROPE,
		CDL_REGION_UK_IDX:        CDL_REGION_UK,
		CDL_REGION_FEDRAMP_IDX:   CDL_REGION_FEDRAMP,
		CDL_REGION_CANADA_IDX:    CDL_REGION_CANADA,
		CDL_REGION_JAPAN_IDX:     CDL_REGION_JAPAN,
		CDL_REGION_AUSTRALIA_IDX: CDL_REGION_AUSTRALIA,
		CDL_REGION_GERMANY_IDX:   CDL_REGION_GERMANY,
		CDL_REGION_INDIA_IDX:     CDL_REGION_INDIA,
		CDL_REGION_SINGAPORE_IDX: CDL_REGION_SINGAPORE,
	}
)
