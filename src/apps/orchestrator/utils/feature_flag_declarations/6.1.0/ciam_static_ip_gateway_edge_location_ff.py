import logging
import sys
import os
import libs.feature_flags.feature_flags_exceptions as ffe
from libs.feature_flags.feature_flags import FeatureFlag
sys.path.append(os.getcwd())

def main():
    '''ciam_static_ip_gateway_edge_location_ff for GPCS rel versions 6.1.0'''
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    f_handler = logging.FileHandler('/var/log/pan/ciam_static_ip_gateway_edge_location_ff.log')
    f_handler.setLevel(logging.INFO)
    f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    f_handler.setFormatter(f_format)
    logger.addHandler(f_handler)
    app_names = ['plugin', 'saas_infra']
    try:
        for app in app_names:
            ciam_ff = FeatureFlag(name='ciam_static_ip_gateway_edge_location_ff',
                                        app=app,
                                        rel_version='6.1.0',
                                        feature='ciam_static_ip_gateway_edge_location',
                                        flag_type='sre',
                                        logger=logger)

            # default value 0: disabled
            ciam_ff.add_dev_sre_default_flag_value(0)
            ciam_ff.add_ff_err_msg("CIAM Static IP Gateway Edge Location feature disabled by default")

            ciam_ff.save()
    except (ffe.MissingRequiredAttributeException, ffe.BadValueException, ffe.InvalidOptionException, ffe.FeatureFlagSaveDBException) as e:
        logger.error(f"Exception: {str(e)}")
        raise

main()