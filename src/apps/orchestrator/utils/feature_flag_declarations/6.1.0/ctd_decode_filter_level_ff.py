import logging
import sys
import os
import libs.feature_flags.feature_flags_exceptions as ffe
from libs.feature_flags.feature_flags import FeatureFlag

sys.path.append(os.getcwd())

VERSION = '6.1.0'
FF_NAME = 'ctd_decode_filter_level_ff_sre'

def main():
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    f_handler = logging.FileHandler(f'/var/log/pan/{FF_NAME}.log')
    f_handler.setLevel(logging.INFO)
    f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    f_handler.setFormatter(f_format)
    logger.addHandler(f_handler)

    try:
        ctd_decode_filter_level_ff = FeatureFlag(name=FF_NAME,
                                app='saas_agent',
                                rel_version=VERSION,
                                feature=FF_NAME,
                                flag_type='sre',
                                logger=logger)
        
        # Set default value to False
        ctd_decode_filter_level_ff.add_dev_sre_default_flag_value(0)
        ctd_decode_filter_level_ff.add_ff_err_msg("ctd decode filter level change feature is disabled for this tenant")
        ctd_decode_filter_level_ff.save()
    except (ffe.MissingRequiredAttributeException, ffe.BadValueException, 
            ffe.InvalidOptionException, ffe.FeatureFlagSaveDBException) as e:
        logger.error(f"Exception: {str(e)}")
        raise

main()

