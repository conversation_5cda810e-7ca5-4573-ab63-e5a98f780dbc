"""
SASE Private Region Feature Flag Declaration

This module defines feature flags for SASE Private Region functionality in GPCS release 6.1.0.
Creates both compatibility and dev SRE feature flags to manage feature rollouts and testing.
"""

import logging
import sys
import os
import libs.feature_flags.feature_flags_exceptions as ffe
from libs.feature_flags.feature_flags import FeatureFlag
sys.path.append(os.getcwd())

def main():
    '''Add sase_pvt_region_feature_flag for GPCS rel versions 6.1.0'''
    
    # Setup logging configuration
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    f_handler = logging.FileHandler('/var/log/pan/sase_pvt_region_ff.log')
    f_handler.setLevel(logging.INFO)
    f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    f_handler.setFormatter(f_format)
    logger.addHandler(f_handler)
    
    try:
        # Create SASE Private Region compatibility feature flag
        # This flag ensures feature is only enabled when all required component versions are met
        logger.info("Creating SASE Private Region compatibility feature flag")
        sase_pvt_ff = FeatureFlag(name='sase_pvt_region_feature_flag',
                                     app='plugin',
                                     rel_version='6.1.0',
                                     feature='sase_pvt_region_feature_flag',
                                     flag_type='compatibility',
                                     logger=logger)

        # Define minimum required versions for SASE Private Region compatibility
        # AMI version dependency - requires at least 11.2.8
        sase_pvt_ff.add_ami_version_dep("11.2.8")
        # Panorama plugin dependency - requires at least 6.1.0
        sase_pvt_ff.add_panorama_plugin_dep("panorama_plugin", "6.1.0")
        # SaaS agent dependency - requires at least 6.1.0
        sase_pvt_ff.add_fw_app_version_dep("saas_agent", "6.1.0")
        # Error message displayed when compatibility requirements are not met
        sase_pvt_ff.add_ff_err_msg("SASE Pvt Region requires at least 6.1.0 panorama plugin, 6.1.0 saas agent, and 11.2.8 ami")
        # Save compatibility flag to database
        sase_pvt_ff.save()
        logger.info("Successfully created SASE Private Region compatibility feature flag")
        
        # Create SASE Private Region dev SRE feature flag
        # This flag allows dev/SRE teams to control feature enablement for testing and operations
        logger.info("Creating SASE Private Region dev SRE feature flag")
        sase_pvt_dev_sre_ff = FeatureFlag(name='sase_pvt_region_dev_sre_flag',
                                         app='plugin',
                                         rel_version='6.1.0', 
                                         feature='sase_pvt_region_dev_sre',
                                         flag_type='dev_sre',
                                         logger=logger)
        
        # default value -> 0
        sase_pvt_dev_sre_ff.add_dev_sre_default_flag_value(0)
        # Dev SRE flags typically don't have version dependencies
        # They are manually controlled by development/SRE teams for operational purposes
        sase_pvt_dev_sre_ff.add_ff_err_msg("SASE Private Region dev SRE flag is disabled. Contact SRE team to enable for testing/operational purposes.")
        # Save dev SRE flag to database
        sase_pvt_dev_sre_ff.save()
        logger.info("Successfully created SASE Private Region dev SRE feature flag")
        
    except (ffe.MissingRequiredAttributeException, ffe.BadValueException, ffe.InvalidOptionException, ffe.FeatureFlagSaveDBException) as e:
        logger.error(f"Exception: {str(e)}")
        raise

# Execute main function when script is run
main()
