import logging
import sys
import os
import libs.feature_flags.feature_flags_exceptions as ffe
from libs.feature_flags.feature_flags import FeatureFlag
sys.path.append(os.getcwd())

def main():
    '''Add sase_pvt_region_feature_flag for GPCS rel versions 6.1.0'''
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    f_handler = logging.FileHandler('/var/log/pan/sase_pvt_region_ff.log')
    f_handler.setLevel(logging.INFO)
    f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    f_handler.setFormatter(f_format)
    logger.addHandler(f_handler)
    try:
        sase_pvt_ff = FeatureFlag(name='sase_pvt_region_feature_flag',
                                     app='plugin',
                                     rel_version='6.1.0',
                                     feature='sase_pvt_region_feature_flag',
                                     flag_type='compatibility',
                                     logger=logger)

        # Enabled only with the following added dependencies
        sase_pvt_ff.add_ami_version_dep("11.2.8")
        sase_pvt_ff.add_panorama_plugin_dep("panorama_plugin", "6.1.0")
        sase_pvt_ff.add_fw_app_version_dep("saas_agent", "6.1.0")
        sase_pvt_ff.add_ff_err_msg("SASE Pvt Region requires at least 6.1.0 panorama plugin, 6.1.0 saas agent, and 11.2.8 ami")

        sase_pvt_ff.save()
    except (ffe.MissingRequiredAttributeException, ffe.BadValueException, ffe.InvalidOptionException, ffe.FeatureFlagSaveDBException) as e:
        logger.error(f"Exception: {str(e)}")
        raise

main()
