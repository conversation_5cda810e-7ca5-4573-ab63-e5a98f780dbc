import logging
import sys
import os
import json
import libs.feature_flags.feature_flags_exceptions as ffe
from libs.feature_flags.feature_flags import FeatureFlag
sys.path.append(os.getcwd())

FF_NAME = 'saml_auth_response_customization'
FF_SRE_NAME = 'saml_auth_response_customization_sre'
FEATURE_NAME = 'saml_auth_response_customization'
REL_VERSION = '6.1.0'
SAAS_AGENT_VERSION = '5.2.0'
PANORAMA_PLUGIN_VERSION = '5.2.0'
AMI_REL_VERSION = "10.2.4"
ERR_MSG = f"saml_auth_response_customization requires at least ami {AMI_REL_VERSION}, panorama plugin {PANORAMA_PLUGIN_VERSION}, saas-agent {SAAS_AGENT_VERSION}"

def saml_auth_response_customization_ff_add_dep(ff_obj):
    # Enabled only with the following added dependencies
    ff_obj.add_ami_version_dep(AMI_REL_VERSION)
    ff_obj.add_fw_app_version_dep("saas_agent", SAAS_AGENT_VERSION)
    ff_obj.add_panorama_plugin_dep("panorama_plugin", PANORAMA_PLUGIN_VERSION)
    ff_obj.add_ff_err_msg(ERR_MSG)
    ff_obj.save()

def main():
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    f_handler = logging.FileHandler('/var/log/pan/saml_auth_response_customization_feature_flag.log')
    f_handler.setLevel(logging.INFO)
    f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - "
                                 "%(message)s")
    f_handler.setFormatter(f_format)
    logger.addHandler(f_handler)


    try:
        apps = ['plugin', 'saas_agent']
        for app_name in apps:
            saml_auth_response_customization_ff_plugin = FeatureFlag(name=FF_NAME,
                                            app=app_name,
                                            rel_version=REL_VERSION,
                                            feature=FEATURE_NAME,
                                            flag_type='compatibility',
                                            logger=logger)

            saml_auth_response_customization_ff_add_dep(saml_auth_response_customization_ff_plugin)

            saml_auth_response_customization_ff_plugin_sre = FeatureFlag(name=FF_SRE_NAME,
                                            app=app_name,
                                            rel_version=REL_VERSION,
                                            feature=FEATURE_NAME,
                                            flag_type='sre',
                                            logger=logger)

            # default value 1: enabled
            saml_auth_response_customization_ff_plugin_sre.add_dev_sre_default_flag_value(0)
            saml_auth_response_customization_ff_plugin_sre.add_ff_err_msg(
                "saml_auth_response_customization SRE is disabled for this tenant")
            saml_auth_response_customization_ff_plugin_sre.save()

    except (ffe.MissingRequiredAttributeException, ffe.BadValueException, ffe.InvalidOptionException, ffe.FeatureFlagSaveDBException) as e:
        logger.error(f"Exception: {str(e)}")
        raise
    except Exception as e:
        logger.error("Exception: %s" % str(e))
        raise


main()