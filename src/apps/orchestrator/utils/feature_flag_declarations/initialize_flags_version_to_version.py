#!/usr/bin/python3
import logging
import os
import sys
import initialize_flags

def main():
    log_filename = "/var/log/pan/feature_flags.log"
    handler = logging.handlers.RotatingFileHandler(log_filename,
                                                   maxBytes=10485760,
                                                   backupCount=1)
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    logformat = logging.Formatter('%(asctime)s %(name)s %(levelname)-8s: '
                                  '%(message)s')
    handler.setFormatter(logformat)
    logger.addHandler(handler)
    logger.info("--- initialize_flags_from_to ---")
    # Print the script name and command-line arguments
    logger.info("script name:", sys.argv[0])
    logger.info(f"command-line arguments:{sys.argv[1:]}")

    # Check if the required number of arguments is passed
    if len(sys.argv) < 2:
        logger.error("Error: Missing required arguments.")
        logger.error("Usage: initialize_flags_version_to_version.py from_version to_version")
    else:
        from_version = sys.argv[1]
        from_version_details = from_version.split('.')
        if len(from_version_details) == 4:
            from_version_details.pop()
        if len(from_version_details) == 3:
            from_version = '.'.join(from_version_details)
        else:
            raise Exception("Version not in known format")

    current_ff_version_file = os.getcwd() + "/utils/feature_flag_declarations/version"
    current_ff_release_version = ""
    with open(current_ff_version_file, 'r') as file:
        current_ff_release_version = file.read().strip()

    if current_ff_release_version == "" or current_ff_release_version is None:
        logger.error("Failed to find release_version in version file")
        raise Exception("Failed to find release_version in version file")
    logger.info(f"ff release_version version:{current_ff_release_version}")
    current_ff_release_version_details = current_ff_release_version.split('.')
    if len(current_ff_release_version_details) == 4:
        current_ff_release_version_details.pop()
    if len(current_ff_release_version_details) == 3:
        current_ff_release_version = '.'.join(current_ff_release_version_details)
    else:
        raise Exception("current_ff_release_version Version not in known format")

    if from_version == current_ff_release_version:
        logger.info(f" from_version == to_version :{from_version}")

    initialize_flags.iterative_feature_flag_initialization(logger, from_version, current_ff_release_version)

    logger.info(f"finished initializing feature flags from: {from_version} to {current_ff_release_version}")

if __name__ == "__main__":
    main()
