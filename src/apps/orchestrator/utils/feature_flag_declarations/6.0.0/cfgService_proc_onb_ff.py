import logging
import sys
import os
from libs.feature_flags.feature_flags import FeatureFlag
import libs.feature_flags.feature_flags_exceptions as ffe


def main():
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    f_handler = logging.FileHandler('/var/log/pan/cfgservice_proc_onb_ff.log')
    f_handler.setLevel(logging.INFO)
    f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s -"
                                 "%(message)s")
    f_handler.setFormatter(f_format)
    logger.addHandler(f_handler)
    deployment_type = dict()
    deployment_type["fedramp-high"] = {"value": 0}
    deployment_type["fedramp-mod"] = {"value": 0}
    deployment_type["fedramp-il5"] = {"value": 0}
    deployment_type["china"] = {"value": 0}

    try:
        feature_arguments = {"green_field_only": True}
        cfgService_ff = FeatureFlag(name='cfgservice_proc_onb',
                                    app='saas_infra', rel_version='6.0.0',
                                    feature='cfgservice_proc_onb',
                                    flag_type='dev', logger=logger)
        cfgService_ff.add_deployment_dep(deployment_type)
        cfgService_ff.add_dev_sre_default_flag_value(1)
        cfgService_ff.add_ff_err_msg("cfgService process onboardings is disabled for this tenant")
        cfgService_ff.add_feature_arguments(feature_arguments)
        cfgService_ff.save()
        cfgService_ff_svc = FeatureFlag(name='cfgservice_proc_onb',
                                        app='service', rel_version='6.0.0',
                                        feature='cfgservice_proc_onb',
                                        flag_type='dev', logger=logger)
        cfgService_ff_svc.add_deployment_dep(deployment_type)
        cfgService_ff_svc.add_dev_sre_default_flag_value(1)
        cfgService_ff_svc.add_feature_arguments(feature_arguments)
        cfgService_ff_svc.add_ff_err_msg("cfgService process onboardings disabled for this "
                                         "tenant")
        cfgService_ff_svc.save()
    except (ffe.MissingRequiredAttributeException, ffe.BadValueException, ffe.InvalidOptionException, ffe.FeatureFlagSaveDBException) as e:
        logger.error(f"Exception: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Generic Exception: {str(e)}")
        raise


main()
