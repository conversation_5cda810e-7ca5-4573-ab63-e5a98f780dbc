#!/usr/bin/python3

import subprocess
import os
import sys
import logging
import logging.handlers
import boto3
import json


from libs.common.shared.utils import get_aws_partition, check_result
from libs.model.orchcfgmodel_v2 import OrchCfgModel_v2 as OrchCfgModel
from libs.db.dbhandle import DbHandle
from libs.common.shared import dbconn


sys.path.append(os.getcwd())
try:
    from libs.common.shared import dbconn
except Exception as e:
    raise Exception("Failed to import required libs: %s" % str(e))

if os.getenv("AWS_REGION") == None:
    raise Exception("AWS region env var is not set")

def init_flags(logger, ff_release_version=None):
    if ff_release_version is None:
        query = ("SELECT value FROM orch_cfg WHERE name='version'")
        result = dbconn.find_one(query, logger=logger,component="ff")
        if result is None or len(result) == 0 or result[0] is None or \
                len(result[0]) == 0:
            raise Exception("Failed to get current infra version from orch_cfg")
        curr_version = result[0][0]
        logger.info("Current version is %s" % str(curr_version))
        # remove commit number
        curr_version = curr_version.split('-')[0]
        curr_version_details = curr_version.split('.')
        if len(curr_version_details) == 4:
            curr_version_details.pop()
        if len(curr_version_details) == 3:
            version_folder = '.'.join(curr_version_details)
        else:
            raise Exception("Version not in known format")
    else:
        curr_version_details = ff_release_version.split('.')
        if len(curr_version_details) == 4:
            curr_version_details.pop()
        if len(curr_version_details) == 3:
            version_folder = '.'.join(curr_version_details)
        else:
            raise Exception("Version not in known format")
    
    version_folder = version_folder.strip()
    ff_path = os.getcwd() + "/utils/feature_flag_declarations/" + \
            str(version_folder) 
    logger.info("path: %s" % str(ff_path))
    if not os.path.exists(ff_path):
        logger.info("path: %s missing so skip checking for feature flags" %
                   str(ff_path))
        return
    dir_list = os.listdir(ff_path)
    fail_list = list()
    pass_list = list()
    for f in dir_list:
        full_path = os.path.join(ff_path, f)
        if os.path.isdir(full_path):
            logger.info("Skip: %s" % full_path)
            continue
        if f == "__init__.py":
            logger.info("Skip: %s" % full_path)
            continue
        if f[0] == ".":
            logger.info("Skip: %s" % full_path)
            continue
        logger.info("Running: %s" % str(full_path))
        out = subprocess.check_output(["/usr/local/bin/python3", full_path]).decode('ascii')
        logger.info("out: %s" % str(out))
        if "Traceback" in out:
            fail_list.append(f)
            logger.error("%s failed." % str(f))
        else:
            pass_list.append(f)
    if len(fail_list) > 0:
        logger.error("Failure in initializing feature flags")
        raise Exception("Failed to initialize feature flags")
    logger.info("Successfully initialized feature flags")

    #invoke ff declaration lambda here async

    dbh = DbHandle(logger)
    global_cfg = OrchCfgModel(dbh)
    cfg = global_cfg.fields
    AWS_PARTITION = get_aws_partition()

    try:
        upgradeFeatureFlags_lambda = "arn:" + AWS_PARTITION + ":lambda:" + cfg.get('region') + \
                             ":" + cfg['acct_id'] + ":function:upgradeFeatureFlags"
        logger.info("lambda arn is: %s" % upgradeFeatureFlags_lambda)
        lambda_client = boto3.client('lambda', region_name=cfg['region'])
        input_params = {
            "ff_release_version": ff_release_version
        }
        response = lambda_client.invoke_async(InvokeArgs=\
                                              json.dumps(input_params),
                                              FunctionName=upgradeFeatureFlags_lambda
                                              )

        logger.info("Response of upgradeFeatureFlags lambda call  : %s" %
                    str(response))

    except Exception as e:
        logger.error("Exception: %s" % str(e))
        raise

def iterative_feature_flag_initialization(logger, ff_start_release_version, ff_end_release_version):
    # ff initialization ff_start_release_version to ff_end_release_version and create the right entries
    ff_directory = os.getcwd() + "/utils/feature_flag_declarations/"
    # List all entries in the directory
    entries = os.listdir(ff_directory)
    ff_release_versions = [entry for entry in entries if os.path.isdir(os.path.join(ff_directory, entry))]
    sorted_ff_release_versions = sorted(ff_release_versions)
    for ff_release_version in sorted_ff_release_versions:
        # loop through the directories and invoke ff initialization
        if ff_release_version < ff_start_release_version:
            logger.info(f"skip processing {ff_release_version}")
            continue
        if ff_release_version > ff_end_release_version:
            logger.info(f"end processing {ff_release_version}")
            break
        else:
            logger.info(f"process {ff_release_version}")
            init_flags(logger, ff_release_version)
            logger.info(f"finished processing {ff_release_version}")
    logger.info(f"Finished initializing feature flags ff_start_release_version:{ff_start_release_version},  "
                f"ff_end_release_version:{ff_end_release_version}")

    return

def main():
    log_filename = "/var/log/pan/feature_flags.log"
    handler = logging.handlers.RotatingFileHandler(log_filename,
                                                   maxBytes=10485760,
                                                   backupCount=1)
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    logformat = logging.Formatter('%(asctime)s %(name)s %(levelname)-8s: '
                                  '%(message)s')
    handler.setFormatter(logformat)
    logger.addHandler(handler)
    logger.info("--- Start ---")
    # read the version file
    # Open the file in read mode ('r')
    current_ff_version_file = os.getcwd() + "/utils/feature_flag_declarations/version"
    current_ff_release_version = ""
    with open(current_ff_version_file, 'r') as file:
        current_ff_release_version = file.read()
    if current_ff_release_version == "" or current_ff_release_version is None:
        logger.error("Failed to find release_version in version file")
        raise Exception("Failed to find release_version in version file")
    logger.info(f"ff release_version version:{current_ff_release_version}")

    current_ff_release_version_details = current_ff_release_version.split('.')
    if len(current_ff_release_version_details) == 4:
        current_ff_release_version_details.pop()
    if len(current_ff_release_version_details) == 3:
        current_ff_release_version = '.'.join(current_ff_release_version_details)
    else:
        raise Exception("current_ff_release_version Version not in known format")

    # check if this is a brand new environment
    # check ff declaration table and absence signifies that this is a brand new env w.r.t. feature flags
    query = (f"SELECT COUNT(*) FROM feature_flags")
    result = dbconn.find_one(query, logger=logger, component="ff")
    if not check_result(result, logger):
        raise Exception("Failed to query feature_flag")
    flag_count = result[0][0]
    logger.info(f"flag count :{flag_count} in feature_flags table")
    if flag_count == 0:
        logger.info("Failed to find feature_flag entries in db, that signifies that this is a brand new env ")
        if current_ff_release_version == "":
            raise Exception("Failed to find release_version in version file ")
        logger.info(f"first time env bring up release_version version is {current_ff_release_version}")

        start_ff_release_version = "3.2.0"
        logger.info(f"new environment invoke ff declaration for files from  release_version "
                    f"start:{start_ff_release_version} end:{current_ff_release_version}")
        iterative_feature_flag_initialization(logger, start_ff_release_version, current_ff_release_version)
        logger.info(f"new environment finished ff declaration for files from release_version"
                    f"start:{start_ff_release_version} end:{current_ff_release_version}")
    else:
        logger.info("found feature_flag entries in db, that signifies that this is a an existing env ")
        # legacy workflow
        init_flags(logger, current_ff_release_version)


if __name__ == "__main__":
    main()
