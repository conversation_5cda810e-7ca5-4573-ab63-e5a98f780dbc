import argparse
import re
import json
from threading import RLock
import boto3
import sys
import logging
import traceback
from concurrent.futures import ThreadP<PERSON>Executor, as_completed
from ipaddress import IPv4Network
from google.cloud.compute_v1 import Instance, NetworksClient, InstancesClient, SubnetworksClient, FirewallsClient
from googleapiclient.discovery import build
from migration_worker import MigrationConfig
from libs.cloud_providers.common.instance_trigger_update import trigger_update_for_instance_changes
from libs.db.dbhandle import DbHandle
from libs.common.shared.rastro.rastro_logging_wrapper import RastroLoggingInfra
from libs.common.shared.gcp_utils import ProxyStates
from libs.model.custmodel import CustomerModel
from migrate_utils import classify_cluster_instances, migrate_instance_to_network, peer_networks, unpeer_networks, get_instance_network_interface, get_network_subnet_map, publish_network_sns, get_cluster_status, trigger_failover, remove_tag, is_host_project
from migrationhelper.trolle_gcp_utils import extract_project_from_uri
import random
from time import sleep
sys.path.append("/home/<USER>/orchestrator")
from libs.common.shared import dbconn
from libs.common.shared.gcp_utils import gcp_authenticate
from libs.cfg import cfg

def get_project_id(id) -> None:
  sql=f"""
  SELECT project_id FROM cust_master WHERE id = '{id}'
  """
  result=dbconn.execute_lambda_query(sql, None)
  if not result['ok']:
    raise RuntimeError(f"Error occurred getting project for customer {id}")
  return result['result'][0][0]

def get_tenant_management_vpc(networks_client, project_id) -> None:
  networks=networks_client.list(project=project_id)
  for network in networks:
    if network.name.lower().split('/')[-1].startswith("gpcs-vpc-mgmt"):
      return network
  return None

def check_terraform_region_status(dbh, customer_id, region_id=None):
    """
    Check if customer has regions with Terraform provisioning enabled for the specified node types.
    Returns: (has_tf_regions, tf_regions_list, dm_regions_list)
    """
    try:
        # Build the base query
        if region_id is None:
            # Check all regions for this customer
            sql = f"""
            SELECT DISTINCT region, gcp_provision_type
            FROM orch_instance_management_table
            WHERE custid = {customer_id} AND cloud_provider = 'gcp'
            """
        else:
            # Check specific region
            sql = f"""
            SELECT DISTINCT region, gcp_provision_type
            FROM orch_instance_management_table
            WHERE custid = {customer_id} AND cloud_provider = 'gcp' AND region = {region_id}
            """
        result = dbconn.execute_lambda_query(sql, None)

        if not result['ok']:
            dbh.logger.warning(f"Could not query region provisioning types for customer {customer_id}, result {result}")
            return False, [], []

        tf_regions = []
        dm_regions = []

        for row in result['result']:
            region, provision_type = row
            if provision_type in ['terraform_provision', 'infrastructure_manager', 'terraform_migration']:
                tf_regions.append(region)
            else:
                dm_regions.append(region)

        # Remove duplicates and sort
        tf_regions = sorted(list(set(tf_regions)))
        dm_regions = sorted(list(set(dm_regions)))

        has_tf_regions = len(tf_regions) > 0
        return has_tf_regions, tf_regions, dm_regions

    except Exception as e:
        dbh.logger.error(f"Error checking Terraform region status: {str(e)}")
        return False, [], []

class MigrationProject:
  def __init__(
      self,
      project_id: str,
      customer_id,
      region_id: str,
      logger,
      dm_client,
      instances_client: InstancesClient,
      networks_client: NetworksClient,
      subnetworks_client: SubnetworksClient,
      firewalls_client: FirewallsClient,
      migration_config: MigrationConfig,
      executor: ThreadPoolExecutor,
      node_types: list,
      ignore_ha_state: bool=False,
      dry_run: bool=False,
      revert: bool=False,
      migrate_tf_regions: bool=False
  ) -> None:
    self.logger=logger
    self.executor=executor
    self.dry_run=dry_run
    self.dbh = DbHandle(logger)
    self.revert=revert
    self.migrate_tf_regions=migrate_tf_regions  # Store the parameter
    self.customer = CustomerModel(dbh=self.dbh, custid=customer_id)

    if not self.revert:
      self.project_id = get_project_id(customer_id)
    else:
      self.project_id = project_id

    self.region_id=region_id
    self.ignore_ha_state = ignore_ha_state
    self.node_types = node_types

    # Pass node_types to the safety check
    self._perform_terraform_safety_check()

    self.logger.info(f"Initializing migration manager for customer {customer_id}, project {self.project_id}")
    self.dm = dm_client
    self.instances_client=instances_client
    self.networks_client=networks_client
    self.subnetworks_client=subnetworks_client
    self.firewalls_client=firewalls_client
    self.aws_account_id=boto3.client('sts').get_caller_identity().get('Account')
    self.instances_by_name={} #NOTE: Assumes globally unique instance names (true for firewall instances)
    self.transition_vpc=None
    self.legacy_management_vpc=None
    self.migration_config=migration_config
    self.target_vpc=self.migration_config.target_vpc
    self.fw_instance_records={}
    self.firewall_cluster_instances=None
    self.fw_db_cache={}
    self.get_project_acct_id()
    self.update_customer()
    self.refresh_rds_state()
    self.logger.debug(f"""
                      Firewall records from panw instance_master table:
                      {self.fw_rds_cache}
                      """)
    self.refresh_networks()
    if not self.ignore_ha_state:
      self.logger.debug(f"""
                        Transition VPC:
                        {self.transition_vpc.name}

                      Legacy Mgmt VPC:
                      {self.legacy_management_vpc.name}

                      New Mgmt VPC:
                      {self.target_vpc.name}
                      """)
    if self.ignore_ha_state:
      self.fetch_panw_fw_instances()
      self.logger.debug(f"""
                        Firewall instances:
                        { {k: [v for v in a.keys()] for k, a in self.cluster_instance_records.items()} }
                        """)
    else:
      self.refresh_panw_fw_instances()
      self.refresh_ddb_cluster_status()
      self.logger.debug(f"""
                        Firewall instances:
                        { {k: [v for v in a.keys()] for k, a in self.cluster_instance_records.items()} }
                        """)
      self.logger.debug(f"""
                        DDB reported status:
                        {self.clusters_ddb_cache}
                        """)
      self.classify_clusters()
      self.logger.debug(f"""
                        Detected cluster states:
                        { {k: {ak:av for ak, av in a.items() if ak not in ['instance_a', 'instance_b']} for k, a in self.cluster_classifications.items() } }
                        """)

    self.logger.info(f"Finished initializing migration manager for project {self.project_id}")

  def _perform_terraform_safety_check(self):
    """
    Perform safety check for Terraform regions before proceeding with migration.
    Now node-type aware when node_types are specified.
    """
    try:
      has_tf_regions, tf_regions, dm_regions = check_terraform_region_status(
          self.dbh,
          self.customer.get_param('id'),
          self.region_id
      )

      if has_tf_regions:
        if not self.migrate_tf_regions:
          # Format regions for user-friendly error message
          tf_regions_str = ', '.join(str(r) for r in tf_regions)
          dm_regions_str = ', '.join(str(r) for r in dm_regions) if dm_regions else "None"
          node_types_str = ', '.join(str(nt) for nt in self.node_types) if self.node_types else "All"

          error_msg = f"""
TERRAFORM REGION SAFETY CHECK FAILED

Customer {self.customer.get_param('id')} has hybrid regions that are DM and TF enabled for the specified node types:

  Node Types:         {node_types_str}
  Terraform Regions:  {tf_regions_str}
  DM Regions:         {dm_regions_str}

This migration script was originally designed for Deployment Manager environments.
Running on Terraform-managed regions can cause dual provisioning conflicts.

SAFETY REQUIREMENT: Use --migrate-tf-regions (-mtr) if you want to migrate TF enabled regions.

Example:
  python migration_project.py -c {self.customer.get_param('id')} --migrate-tf-regions

WARNING: This bypasses safety checks. Ensure you understand the implications
of running migration scripts on Terraform-managed infrastructure.

For more information, see incident CYR-55339 and BHP Group RCA (July 2025).
          """

          self.logger.error(error_msg)
          raise RuntimeError("Migration aborted due to Terraform region safety check. Use --migrate-tf-regions (-mtr) to proceed.")

        else:
          # User explicitly enabled TF migration - log warning and proceed
          tf_regions_str = ', '.join(str(r) for r in tf_regions)
          node_types_str = ', '.join(str(nt) for nt in self.node_types) if self.node_types else "All"
          warning_msg = f"""
TERRAFORM REGION MIGRATION ENABLED

Proceeding with migration on Terraform-managed regions: {tf_regions_str}
For node types: {node_types_str}

CAUTION: You have bypassed safety checks with --migrate-tf-regions flag.
Monitor for dual provisioning conflicts and state synchronization issues.

This script will attempt to coordinate with existing Terraform state.
          """
          self.logger.warning(warning_msg)

      else:
        # No TF regions detected for the specified node types - safe to proceed
        node_types_str = ', '.join(str(nt) for nt in self.node_types) if self.node_types else "All"
        self.logger.info(f"Terraform safety check passed: No Terraform-managed regions detected for node types: {node_types_str}")

    except Exception as e:
      self.logger.error(f"Terraform safety check failed with exception: {str(e)}")
      if "Migration aborted" in str(e):
        raise  # Re-raise our safety check exception
      else:
        # For other exceptions, warn but don't block (backward compatibility)
        self.logger.warning(f"Could not perform Terraform safety check, proceeding with caution: {str(e)}")

  def get_project_panw_id(self) -> None:
    sql=f"""
    SELECT id FROM cust_master WHERE project_id = '{self.project_id}'
    """
    result=dbconn.execute_lambda_query(sql, None)
    if not result['ok']:
      raise RuntimeError(f"Error occurred getting firewall state for project {self.project_id}")
    return result['result'][0][0]

  def refresh_ddb_cluster_status(self) -> None:
    cluster_states={}
    client = boto3.client('lambda', region_name=cfg['region'])
    futures={self.executor.submit(get_cluster_status, cid, self.aws_account_id, client): cid for cid in self.clusters_rds_cache.keys()}
    for fut in as_completed(futures):
      cluster=futures[fut]
      state=fut.result()
      cluster_states[cluster]=state
    self.clusters_ddb_cache=cluster_states

  def classify_clusters(self):
    cluster_classifications={}
    for cluster_id, cluster_state in self.clusters_ddb_cache.items():
      cluster_classifications[cluster_id] = classify_cluster_instances(cluster_id, cluster_state, {k:self.instances_by_name[v] for k, v in self.vmid_directory.items()})
    self.cluster_classifications=cluster_classifications

  def get_project_acct_id(self) -> None:
    sql=f"""
    SELECT acct_id FROM cust_master WHERE project_id = '{self.project_id}'
    """
    result=dbconn.execute_lambda_query(sql, None)
    if not result['ok']:
      raise RuntimeError(f"Error occurred getting acct id for customer {self.customer.get_param('id')}")
    self.acct_id=result['result'][0][0]

  def trigger_update_for_all_regions(self):
    sql = f"select distinct(region) from orch_instance_management_table where custid={self.customer.get_param('id')} and cloud_provider='gcp'"
    success = False
    try:
        self.logger.info(f"Executing {str(sql)}")
        result=dbconn.execute_lambda_query(sql, None)
        if result['ok']:
          for row in result['result']:
            myres, _ = trigger_update_for_instance_changes(self.dbh, row[0], self.customer.get_param('id'), avctx=None)
            self.logger.debug(f"Trigger update result {myres}")
    except Exception as E:
        self.logger.error(f"set_salt_profile_by_instance_id: Failed with exception {str(E.args)}, Traceback: {str(traceback.format_exc())}, locals: {str(locals())}")
    finally:
        return success


  def query_fw_master_db(self, node_types, fields=['vmid', 'clusterid','name', 'ha_state', 'node_type', 'vm_status', 'slot_nr', 'mgt_ip', 'id', 'salt_profile']):
    self.logger.info(f"is host project {is_host_project(self.migration_config.target_vpc_project)} target project {self.migration_config.target_vpc_project}")
    if is_host_project(self.migration_config.target_vpc_project):
      if self.customer.get_param("swgp_pvm_state") == int(ProxyStates.PROXY_UNINITIALIZED) and ('153' in node_types):
        node_types.remove('153')
      if self.customer.get_param("rn_proxy_state") == int(ProxyStates.PROXY_UNINITIALIZED) and ('48' in node_types):
        node_types.remove('48')
      if self.customer.get_param("sc_proxy_state") == int(ProxyStates.PROXY_UNINITIALIZED) and ('51' in node_types):
        node_types.remove('51')
      if self.customer.get_param("global_proxy_state") == int(ProxyStates.PROXY_UNINITIALIZED):
        if ('49' in node_types):
          node_types.remove('49')
        if ('50' in node_types):
          node_types.remove('50')
        if ('154' in node_types):
          node_types.remove('154')
    else:
      if self.customer.get_param("swgp_pvm_state") != int(ProxyStates.PROXY_UNINITIALIZED) and ('153' in node_types):
        node_types.remove('153')
      if self.customer.get_param("rn_proxy_state") != int(ProxyStates.PROXY_UNINITIALIZED) and ('48' in node_types):
        node_types.remove('48')
      if self.customer.get_param("sc_proxy_state") != int(ProxyStates.PROXY_UNINITIALIZED) and ('51' in node_types):
        node_types.remove('51')
      if self.customer.get_param("global_proxy_state") != int(ProxyStates.PROXY_UNINITIALIZED):
        if ('49' in node_types):
          node_types.remove('49')
        if ('50' in node_types):
          node_types.remove('50')
        if ('154' in node_types):
          node_types.remove('154')

    if node_types == []:
      self.logger.info(f"No node types to migrate, exiting...")
      return []

    self.logger.info(f"Migrating Node Types {', '.join(node_types)}")
    if self.region_id is None:
      sql=f"""
      SELECT {', '.join(fields)}
      FROM instance_master
      WHERE
      custid = '{self.customer.get_param("id")}'
      AND node_type in ({', '.join(node_types)})
      """
    else:
      sql=f"""
      SELECT {', '.join(fields)}
      FROM instance_master
      WHERE
      custid = '{self.customer.get_param("id")}'
      AND node_type in ({', '.join(node_types)})
      AND compute_region_idx = '{self.region_id}'
      """
    result=dbconn.execute_lambda_query(sql, None)
    if not result['ok']:
      raise RuntimeError(f"Error occurred getting firewall state for project {self.project_id}")
    rows=[]
    for row in result['result']:
      rows.append({k:v for k,v in zip(fields, row)})
    return rows

  def publish_network_sns(self):
    self.logger.debug("Calling publish_network_sns...")
    sql=f'CALL publish_network_sns("instance_master","Update", 0, {self.acct_id});'
    result=dbconn.execute_lambda_query(sql, None)
    if not result['ok']:
      raise RuntimeError(f"Error occurred in publish_network_sns")

  def refresh_rds_state(self) -> None:
    """Query the database to get a list of firewall instance pairs and their current status (active/passive/not ready/transitioning/etc) for a given gcp project id."""
    rows=self.query_fw_master_db(node_types=self.node_types, fields=['vmid', 'clusterid','name', 'ha_state', 'node_type', 'vm_status', 'slot_nr', 'mgt_ip', 'id', 'salt_profile'])
    records={}
    clusters={}
    for row in rows:
      if row['name'] and row['clusterid'] and row['vmid']:
        records[row['name'].lower().replace('_','-')]=row
        cluster=clusters.get(row['clusterid'], {})
        cluster[row['name']]=row
        clusters[row['clusterid']]=cluster
    self.fw_rds_cache=records
    self.clusters_rds_cache=clusters

  def get_panw_fw_instances(self):
    """Returns a dict of instances by panw name based on metadata field"""
    agg_list=self.instances_client.aggregated_list(project=self.project_id)
    fw_instance_records={}
    for _, result in agg_list:
      if result.instances:
        for instance in result.instances:
          metadata_dict={item.key: item.value for item in instance.metadata.items}
          if metadata_dict.get('instance_name') is not None:
            fw_instance_records[metadata_dict['instance_name'].lower().replace('_','-')]=instance
    return fw_instance_records

  def fetch_panw_fw_instances(self) -> None:
    instances = {k.lower().replace('_','-'): v for k,v in self.get_panw_fw_instances().items()}
    cluster_instance_records={}
    for name, record in self.fw_rds_cache.items():
      if name in instances.keys():
        cluster=cluster_instance_records.get(record['clusterid'], {})
        instance=cluster.get(record['vmid'], {})
        instance['instance'] = instances[name]
        instance['salt_profile'] = record['salt_profile']
        cluster[record['vmid']]=instance
        cluster_instance_records[record['clusterid']]=cluster
    self.cluster_instance_records=cluster_instance_records
    #self.logger.debug(f"cluster instance records {cluster_instance_records}")

  def refresh_panw_fw_instances(self) -> None:
    instances = {k.lower().replace('_','-'): v for k,v in self.get_panw_fw_instances().items()}
    cluster_instance_records={}
    vmid_directory={}
    instances_by_name={}
    for name, record in self.fw_rds_cache.items():
      if name in instances.keys():
        cluster=cluster_instance_records.get(record['clusterid'], {})
        cluster[name]=instances[name]
        cluster_instance_records[record['clusterid']]=cluster
        vmid_directory[record['vmid']]=name
        instances_by_name[name]=instances[name]
    self.cluster_instance_records=cluster_instance_records
    self.vmid_directory=vmid_directory
    self.instances_by_name=instances_by_name
  
  def refresh_networks(self) -> None:
    networks=self.networks_client.list(project=self.project_id)
    transition_vpc=None
    transition_vpc_subnets=None
    legacy_management_vpc=None
    legacy_management_vpc_subnets=None
    for network in networks:
      if self.is_legacy_management_vpc(network.name):
        legacy_management_vpc=network
        legacy_management_vpc_subnets=get_network_subnet_map(self.project_id, network.name, self.subnetworks_client)
      elif self.is_transition_vpc(network.name):
        transition_vpc=network
        transition_vpc_subnets=get_network_subnet_map(self.project_id, network.name, self.subnetworks_client)
    target_vpc_mgmt_subnets=get_network_subnet_map(
        extract_project_from_uri(self.target_vpc.self_link),
        self.target_vpc.name,
        self.subnetworks_client)
    self.transition_vpc=transition_vpc
    self.transition_vpc_subnets=transition_vpc_subnets
    self.legacy_management_vpc=legacy_management_vpc
    self.legacy_management_vpc_subnets=legacy_management_vpc_subnets
    self.target_vpc_mgmt_subnets=target_vpc_mgmt_subnets

  def wait_until_active(self, instance, cluster_id, vm_id):
    while instance.status != "RUNNING":
      self.logger.debug(f"{instance.name} in status {instance.status}, waiting...")
      sleep(5)
      instance=self.instances_client.get(project=self.project_id, zone=instance.zone, instance=instance.name)
    self.logger.debug(f"{instance.name} running, checking dynamo db for active status...")
    boto_client = boto3.client('lambda', region_name=cfg['region'])

    # This field is weird - it appears to be a string representation of a python
    # dict, which is NOT in a JSON compatible format on account of containing
    # single quotes. Could use eval or literal_eval but those both have some
    # problems. Thus the ugly hack:
    status_result_string=self.get_cluster_status(cluster_id, client=boto_client).get("status_result", "{'result': 'no_status'}")
    status_result=json.loads(status_result_string.replace("\'", "\""))
    # End ugly hack

    while status_result.get(vm_id, 'missing') != 'active':
      self.logger.debug(f"{instance.name} in ha status {status_result.get(vm_id, 'missing')}, waiting...")
      jitter=random.uniform(-1.0,1.0)
      sleep(5.0+jitter)
      status_result_string=self.get_cluster_status(cluster_id, client=boto_client).get("status_result", "{'result': 'no_status'}")
      status_result=json.loads(status_result_string.replace("\'", "\""))
    return

  def wait_until_passive(self, instance: Instance, cluster_id, vm_id):
    while instance.status != "RUNNING":
      self.logger.debug(f"{instance.name} in status {instance.status}, waiting...")
      sleep(5)
      instance=self.instances_client.get(project=self.project_id, zone=str(instance.zone).split('/')[-1], instance=instance.name)
    self.logger.debug(f"{instance.name} running, checking dynamo db for passive status...")
    boto_client = boto3.client('lambda', region_name=cfg['region'])

    # This field is weird - it appears to be a string representation of a python
    # dict, which is NOT in a JSON compatible format on account of containing
    # single quotes. Could use eval or literal_eval but those both have some
    # problems. Thus the ugly hack:
    status_result_string=self.get_cluster_status(cluster_id, client=boto_client).get("status_result", "{'result': 'no_status'}")
    status_result=json.loads(status_result_string.replace("\'", "\""))
    # End ugly hack

    while status_result.get(vm_id, 'missing') != 'passive':
      self.logger.debug(f"{instance.name} in ha status {status_result.get(vm_id, 'missing')}, waiting...")
      jitter=random.uniform(-1.0,1.0)
      sleep(5.0+jitter)
      status_result_string=self.get_cluster_status(cluster_id, client=boto_client).get("status_result", "{'result': 'no_status'}")
      status_result=json.loads(status_result_string.replace("\'", "\""))
    return

  def get_cluster_status(self, clusterid, client=None) -> dict:
    if client == None:
      client = boto3.client('lambda', region_name=cfg['region'])
    payload = { "cluster_id": clusterid }
    ha_status_arn = f"arn:aws:lambda:{cfg['region']}:{self.aws_account_id}:function:check_ha_status_dynamodb"
    try:
        response = client.invoke(
            FunctionName=ha_status_arn,
            InvocationType='RequestResponse',
            LogType='None',
            Payload=json.dumps(payload),
        )
        response = json.loads(response['Payload'].read())
        self.logger.info("Result from check_ha_status_dynamodb:%s" % response)
        if response['result'] == 'failed':
            return { "result": "failed", 
                     "error_msg": response['error_msg']
                   }
        self.logger.debug(f"""
                          Got response from Dynamo DB:
                          {response}
                          """)
        return response
    except Exception as e:
        reason = "Exception: %s" % (str(e))
        self.logger.error(reason)
        return { "result": "failed", "error_msg": reason }

  def get_cluster_ha_states(self, clusterid, client=None):
    vmid_peer=None
    peer_state='unknown'
    vmid_ha=None
    ha_state='unknown'
    try:
      status=self.get_cluster_status(clusterid, client)
      vmid_ha=status.get('vmid_ha')
      vmid_peer=status.get('vmid_peer')
      status_result_string=status.get("status_result", "{'result': 'no_status'}")
      status_result=json.loads(status_result_string.replace("\'", "\""))
      
      ha_states_set=set()
      ha_states_set.add(str(status_result.get(vmid_ha)).lower())
      ha_states_set.add(str(status.get('ha_status')).lower())
      ha_states_set.add(str(status.get('ha_local_state')).lower())
      ha_states_set.add('active' if (status.get('active_vmid') == vmid_ha) else None)
      ha_states_set.add('passive' if (status.get('passive_vmid') == vmid_ha) else None)
      ha_states_set.discard(None)
      ha_states_set.discard('')
      ha_states_set.discard(None)
      ha_states=[str(i).lower() for i in ha_states_set if str(i)]
      if len(ha_states) > 1:
        ha_state='conflicting'
        self.logger.error(f'Conflict in ha firewall, states reported: {ha_states_set}')
      elif len(ha_states) == 1:
        ha_state=ha_states[0]

      peer_states_set=set()
      peer_states_set.add(str(status_result.get(vmid_peer)).lower())
      peer_states_set.add(str(status.get('peer_status')).lower())
      peer_states_set.add(str(status.get('peer_local_state')).lower())
      peer_states_set.add('active' if (status.get('active_vmid') == vmid_peer) else None)
      peer_states_set.add('passive' if (status.get('passive_vmid') == vmid_peer) else None)
      peer_states_set.discard(None)
      peer_states_set.discard('')
      peer_states=[str(i).lower() for i in peer_states_set if str(i)]
      if len(peer_states) > 1:
        peer_state='conflicting'
        self.logger.error(f'Conflict in peer firewall, states reported: {peer_states_set}')
      elif len(peer_states) == 1:
        peer_state=peer_states[0]
    except Exception:
      logging.exception(f'Exception while getting cluster ha states for cluster {clusterid}')
    result={
        'peer_vmid': vmid_peer,
        'peer_state': peer_state,
        'ha_vmid': vmid_ha,
        'ha_state': ha_state
    }
    return result
      
  def is_legacy_management_vpc(self, network_name: str) -> bool:
    if network_name.lower().split('/')[-1].startswith("gpcs-vpc-mgmt"):
      return True
    return False

  def is_transition_vpc(self, network_name: str) -> bool:
    #if network_name.lower().split('/')[-1].startswith("transition-vpc"):
    if re.search('vpc-transition', network_name.lower()):
      return True
    return False

  def update_instance_mgt_ip(self, vmid, mgt_ip, cx_id, salt_profile_str=None):
    update_salt_profile=f", salt_profile='{salt_profile_str}'" if salt_profile_str else f""
    sql=f"""
    UPDATE instance_master
    SET mgt_ip = "{mgt_ip}"
    {update_salt_profile}
    WHERE
    vmid = "{vmid}"
    LIMIT 1;
    """
    self.logger.debug(f"RDS update query {sql}")
    if self.dry_run == False:
      result=dbconn.execute_lambda_query(sql, None)
      if not result['ok']:
        error = f"Error occurred in updating management IP for vmid {vmid} sql {sql} reason {result}!"
        raise RuntimeError(error)
    return

  def update_instance_mgt_ip_records(self, executor: ThreadPoolExecutor):
    self.refresh_panw_fw_instances()
    futures={}
    for vmid, instance in self.vmid_directory.items():
      iface=get_instance_network_interface(self.instances_by_name[instance], 'nic1')
      future=executor.submit(self.update_instance_mgt_ip, vmid, iface.network_i_p, self.customer.get_param("id"))
      futures[future]=vmid
    failures_encountered=[]
    for fut in as_completed(futures):
      try:
        fut.result()
        self.logger.debug(f"Management IP record updated for vmid {futures[fut]}")
      except Exception as err:
        logging.error(f"Error while updating management IP for vmid {futures[fut]}: {err}")
        failures_encountered.append(err)
    publish_network_sns(self.customer.get_param("id"))
    if failures_encountered:
      raise RuntimeError(f"Errors while updating management IP's: {failures_encountered}")

  def refresh_status(self):
    self.logger.debug("Refreshing status")
    if self.ignore_ha_state == False:
      self.refresh_panw_fw_instances()
      self.refresh_ddb_cluster_status()
      self.classify_clusters()
    else:
      self.fetch_panw_fw_instances()

  def phase_1_prechecks(self):
    try:
      assert self.transition_vpc is not None, "Transition VPC not found!"
      assert self.legacy_management_vpc is not None, "Legacy management VPC not found!"
      assert not len({k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_2']}), "Clusters in phase 2 detected!"
    except Exception:
      self.logger.error(f"Project {self.project_id} not ready for phase 1, aborting!")
      raise

  def run_phase_1(self, executor: ThreadPoolExecutor):
    abort=False
    self.refresh_status()
    self.logger.info(f"Beginning phase 1 for project {self.project_id}...")
    self.phase_1_prechecks()

    clusters_to_migrate = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_0', 'phase_1']}
    self.logger.info(f"Detected {len(clusters_to_migrate)} phase 1 migration candidates: {clusters_to_migrate.keys()}") # Includes already migrated
    try:
      unpeer_networks(self.transition_vpc, self.target_vpc, self.networks_client)
    except Exception:
      pass

    peer_networks(self.transition_vpc, self.legacy_management_vpc, self.networks_client)
    self.logger.info(f"[{self.project_id}] Peering created, migrating firewall instances to transition VPC...")

    # select instances that need to have network iface updated
    clusters_phase_1 = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_1']}
    clusters_phase_0 = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_0']}
    self.logger.info(f"Found {len(clusters_phase_0)} requiring network interface updates, proceeding to update...")
    # Move Passive instances to Transition Network
    subnet_map=get_network_subnet_map(self.project_id, str(self.transition_vpc.name), self.subnetworks_client)
    futures={}
    for cluster_id, status in clusters_phase_0.items():
      if status['status_b'] not in ['active']:
        instance_id = status['instance_b']
        vmid = status['vmid_b']
        vmname = status['instance_b'].name
      else:
        instance_id = status['instance_a']
        vmid = status['vmid_a']
        vmname = status['instance_a'].name
      kwargs={
          "logger": self.logger,
          "instance": instance_id,
          "network": str(self.transition_vpc.name),
          "network_project":  self.project_id,
          "subnet_map": subnet_map,
          "subnetworks_client": self.subnetworks_client,
          "instances_client": self.instances_client,
          "iface_name": 'nic1',
          "add_tag": True,
      }
      self.logger.debug(f"Migrating vmid {vmid}: {vmname} for cluster {cluster_id} to transition vpc...")
      futures[executor.submit(migrate_instance_to_network, **kwargs)] = vmid
    mgt_ip_futures={}
    for fut in as_completed(futures):
      try:
        fut.result()
        self.logger.debug(f"VMID {futures[fut]} network interface updated, updating RDB record...")
        iface = get_instance_network_interface(fut.result(), 'nic1')
        mgt_ip_futures[executor.submit(self.update_instance_mgt_ip, futures[fut], iface.network_i_p, self.customer.get_param("id"))]=futures[fut]
      except Exception:
        self.logger.exception(f"VMID {futures[fut]} failed to migrate!")
        abort=True

    for fut in as_completed(mgt_ip_futures):
      fut.result()
      self.logger.debug(f"VMID {mgt_ip_futures[fut]} master ip record updated successfully")

    self.logger.info(f"Phase 1 network interface updates complete, calling publish_network_sns()...")
    self.publish_network_sns()

    if abort:
      raise RuntimeError("Errors encountered while updating instance network interfaces, aborting!")


    self.logger.info(f"Refreshing firewalls directory...")
    self.refresh_status()

    # self.logger.info(f"Waiting for firewalls to report 'initial' status")
    # initialized_clusters=[k for k,v in clusters_phase_1.items()]
    # clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_1'] and k not in initialized_clusters}
    # self.logger.debug(f"States are: { {k: (v['vmid_a'], v['status_a'], v['vmid_b'], v['status_b']) for k, v in clusters_to_fail_over.items()} }")
    # while not all([i['status_a'] in ['active'] and i['status_b'] in ['initial'] for i in clusters_to_fail_over.values()]):
    #   if not all([i['status_a'] in ['active', None] for i in clusters_to_fail_over.values()]):
    #     raise RuntimeError("Prior active reporting unexpected status, aborting!")
    #   for cluster in [k for k, v in clusters_to_fail_over.items() if v['status_a'] in ['initial']]:
    #     initialized_clusters.append(cluster)
    #   self.logger.debug(f"States are: { {k: (v['vmid_a'], v['status_a'], v['vmid_b'], v['status_b']) for k, v in clusters_to_fail_over.items()} }")
    #   self.logger.info(f"Waiting for firewalls to register 'initial' with DDB...")
    #   sleep(30)
    #   self.logger.info(f"Refreshing firewalls directory...")
    #   self.refresh_status()
    #   clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_1'] and k not in initialized_clusters}

    # self.logger.info(f"Waiting for firewalls to finish initializing...")
    # sleep(30)
    # self.refresh_status()
    clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_1']}
    self.logger.debug(f"States are: { {k: (v['vmid_a'], v['status_a'], v.get('instance_a_transition'), v['vmid_b'], v['status_b'], v.get('instance_b_transition')) for k, v in clusters_to_fail_over.items()} }")
    while not all([i['status_a'] in ['active', 'passive'] and i['status_b'] in ['active', 'passive'] for i in clusters_to_fail_over.values()]):
      # if not all([i['status_a'] in ['active', None] for i in clusters_to_fail_over.values()]):
      #   raise RuntimeError("Prior active reporting unexpected status, aborting!")
      self.logger.debug(f"States are: { {k: (v['vmid_a'], v['status_a'], v.get('instance_a_transition'), v['vmid_b'], v['status_b'], v.get('instance_b_transition')) for k, v in clusters_to_fail_over.items()} }")
      self.logger.info(f"Waiting for firewalls to register with DDB...")
      sleep(30)
      self.logger.info(f"Refreshing firewalls directory...")
      self.refresh_status()
      clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_1']}

    self.logger.info("All clusters reporting ready, proceeding to failover...")
    clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_1']}
    futures={}
    # select active instances in tenant mgmt vpc that need to be failed over to instances in transition vpc
    for k, v in clusters_to_fail_over.items():
      if (v.get('instance_a_transition') is None and v['status_a'] in ['active']):
        vmid = v['vmid_a']
      elif (v.get('instance_b_transition') is None and v['status_b'] in ['active']):
        vmid = v['vmid_b']
      else:
        self.logger.info(f"No switchover needed, skip switchover, state: { {k: (v['vmid_a'], v['status_a'], v.get('instance_a_transition'), v.get('instance_a_shared'), v['vmid_b'], v['status_b'], v.get('instance_b_transition'), v.get('instance_b_shared')) for k, v in clusters_to_fail_over.items()} }")
        continue
      futures[executor.submit(trigger_failover, vmid, self.aws_account_id)]=k

    for fut in as_completed(futures):
      try:
        fut.result()
        self.logger.info(f"Cluster {futures[fut]} failed over successfully")
      except Exception:
        self.logger.exception(f"Cluster {futures[fut]} failover unsuccessful!")
        abort=True

    if abort:
      raise RuntimeError(f"Failures encountered while failing over to transition vpc, aborting!")

    sleep(30)

    self.logger.info(f"Refreshing firewalls directory...")
    self.refresh_status()

    clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_1']}
    self.logger.debug(f"States are: { {k: (v['vmid_a'], v['status_a'], v.get('instance_a_transition'), v['vmid_b'], v['status_b'], v.get('instance_b_transition')) for k, v in clusters_to_fail_over.items()} }")
    while not all([i['status_a'] in ['active', 'passive'] and i['status_b'] in ['active', 'passive'] for i in clusters_to_fail_over.values()]):
      self.logger.debug(f"States are: { {k: (v['vmid_a'], v['status_a'], v['vmid_b'], v['status_b']) for k, v in clusters_to_fail_over.items()} }")
      self.logger.info(f"Waiting for firewalls to complete failover...")
      sleep(30)
      self.logger.info(f"Refreshing firewalls directory...")
      self.refresh_status()
      clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_1']}

    self.logger.info("All clusters failed over to transition vpc, phase 1 complete.")

  def phase_2_prechecks(self):
    try:
      assert self.transition_vpc is not None, "Transition VPC not found!"
      assert self.target_vpc is not None, "Target management VPC not found!"
    except Exception:
      self.logger.error(f"Project {self.project_id} not ready for phase 2, aborting!")
      raise

  def run_phase_2(self, executor: ThreadPoolExecutor, peering_lock):
    self.logger.info(f"Beginning phase 2 for project {self.project_id}...")
    self.phase_2_prechecks()
    try:
      unpeer_networks(self.transition_vpc, self.legacy_management_vpc, self.networks_client)
    except Exception:
      pass
    self.logger.info(f"[{self.project_id}] Transition/legacy peering deleted, migrating firewall instances to transition VPC...")

    abort=False

    clusters_phase_2 = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_2']}
    clusters_phase_1 = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_1']}
    self.logger.info(f"Found {len(clusters_phase_1)} requiring network interface updates, proceeding to update...")
    # Move instance A's to new network
    subnet_map=get_network_subnet_map(self.migration_config.target_vpc_project, str(self.target_vpc.name), self.subnetworks_client)
    # self.logger.debug(f"New management subnet map: {subnet_map}")
    futures={}
    for cluster_id, status in clusters_phase_1.items():
      if status.get('instance_a_transition') is None:
        instance_id = status['instance_a']
        vmid = status['vmid_a']
        vmname = status['instance_a'].name
      elif status.get('instance_b_transition') is None:
        instance_id = status['instance_b']
        vmid = status['vmid_b']
        vmname = status['instance_b'].name
      else:
        self.logger.exception(f"Project {self.project_id} not ready for phase 2, aborting!")
        break
      kwargs={
          "logger": self.logger,
          "instance": instance_id,
          "network": str(self.target_vpc.name),
          "network_project":  self.project_id,
          "subnet_map": subnet_map,
          "subnetworks_client": self.subnetworks_client,
          "instances_client": self.instances_client,
          "iface_name": 'nic1'
      }
      self.logger.debug(f"Migrating vmid {vmid}: {vmname} for cluster {cluster_id} to new_mgmt vpc...")
      futures[executor.submit(migrate_instance_to_network, **kwargs)] = vmid
    mgt_ip_futures={}
    for fut in as_completed(futures):
      try:
        fut.result()
        self.logger.debug(f"VMID {futures[fut]} network interface updated, updating RDB record...")
        iface = get_instance_network_interface(fut.result(), 'nic1')
        mgt_ip_futures[executor.submit(self.update_instance_mgt_ip, futures[fut], iface.network_i_p, self.customer.get_param("id"))]=futures[fut]
      except Exception:
        self.logger.exception(f"VMID {futures[fut]} failed to migrate!")
        abort=True

    for fut in as_completed(mgt_ip_futures):
      fut.result()
      self.logger.debug(f"VMID {mgt_ip_futures[fut]} master ip record updated successfully")

    if abort:
      raise RuntimeError("Errors encountered while updating instance network interfaces, aborting!")

    sleep(30)

    self.logger.info(f"All passives successfully moved to new management VPC for project {self.project_id}")
    self.logger.info(f"Entering hot migration period for phase 2 on project {self.project_id}, acquiring lock...")
    peering_lock.acquire()
    self.logger.info(f"Peering lock acquired for project {self.project_id}")
    peer_networks(self.transition_vpc, self.target_vpc, self.networks_client)
    self.publish_network_sns()
    self.logger.info(f"Called publish_network_sns, waiting for firewalls to register...")
    self.refresh_status()
    initialized_clusters=[k for k,v in clusters_phase_2.items()]
    clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_2'] and k not in initialized_clusters}
    # while not all([i['status_b'] in ['active'] and i['status_a'] in ['initial'] for i in clusters_to_fail_over.values()]):
    #   # if not all([i['status_b'] in ['active', None] for i in clusters_to_fail_over.values()]):
    #   #   raise RuntimeError("Prior active reporting unexpected status, aborting!")
    #   for cluster in [k for k, v in clusters_to_fail_over.items() if v['status_a'] in ['initial']]:
    #     initialized_clusters.append(cluster)
    #   self.logger.debug(f"States are: { {k: (v['vmid_a'], v['status_a'], v.get('instance_a_transition'), v.get('instance_a_shared'), v['vmid_b'], v['status_b'], v.get('instance_b_transition'), v.get('instance_b_shared')) for k, v in clusters_to_fail_over.items()} }")
    #   self.logger.info(f"Waiting for firewalls to register 'initial' with DDB...")
    #   sleep(30)
    #   self.logger.info(f"Refreshing firewalls directory...")
    #   self.refresh_status()
    #   clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_2'] and k not in initialized_clusters}

    self.logger.info(f"Firewalls registered, waiting to register passive...")
    sleep(30)
    self.refresh_status()
    clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_2']}
    self.logger.debug(f"States are: { {k: (v['vmid_a'], v['status_a'], v.get('instance_a_transition'), v.get('instance_a_shared'), v['vmid_b'], v['status_b'], v.get('instance_b_transition'), v.get('instance_b_shared')) for k, v in clusters_to_fail_over.items()} }")
    while not all([i['status_a'] in ['active', 'passive'] and i['status_b'] in ['active', 'passive'] for i in clusters_to_fail_over.values()]):
      self.logger.debug(f"States are: { {k: (v['vmid_a'], v['status_a'], v.get('instance_a_transition'), v.get('instance_a_shared'), v['vmid_b'], v['status_b'], v.get('instance_b_transition'), v.get('instance_b_shared')) for k, v in clusters_to_fail_over.items()} }")
      self.logger.info(f"Waiting for firewalls to register 'passive' with DDB...")
      sleep(30)
      self.logger.info(f"Refreshing firewalls directory...")
      self.refresh_status()
      clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_2']}


    self.logger.info("All clusters reporting ready, proceeding to failover...")

    # # select active instances in transition vpc that need to be failed over to instances in target vpc
    # clusters_to_fail_over = {k:v for k,v in clusters_to_fail_over.items() if (v.get('instance_a_transition') and v['status_a'] in ['active']) or
    #                          (v.get('instance_b_transition') and v['status_b'] in ['active'])}

    clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_2']}
    futures={}
    for k, v in clusters_to_fail_over.items():
      if (v.get('instance_a_transition') and v['status_a'] in ['active']):
        vmid = v['vmid_a']
      elif (v.get('instance_b_transition') and v['status_b'] in ['active']):
        vmid = v['vmid_b']
      else:
        self.logger.info(f"No switchover needed, skip switchover, state: { {k: (v['vmid_a'], v['status_a'], v.get('instance_a_transition'), v.get('instance_a_shared'), v['vmid_b'], v['status_b'], v.get('instance_b_transition'), v.get('instance_b_shared')) for k, v in clusters_to_fail_over.items()} }")
        continue
      futures[executor.submit(trigger_failover, vmid, self.aws_account_id)]=k

    for fut in as_completed(futures):
      try:
        fut.result()
        self.logger.info(f"Cluster {futures[fut]} failed over successfully")
      except Exception:
        self.logger.exception(f"Cluster {futures[fut]} failover unsuccessful!")
        abort=True

    if abort:
      raise RuntimeError(f"Failures encountered while failing over to transition vpc, aborting!")

    self.logger.info(f"Refreshing firewalls directory...")
    self.refresh_status()

    clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_2']}
    self.logger.debug(f"States are: { {k: (v['vmid_a'], v['status_a'], v.get('instance_a_transition'), v.get('instance_a_shared'), v['vmid_b'], v['status_b'], v.get('instance_b_transition'), v.get('instance_b_shared')) for k, v in clusters_to_fail_over.items()} }")
    while not all([i['status_a'] in ['active', 'passive'] and i['status_b'] in ['passive', 'active'] for i in clusters_to_fail_over.values()]):
      self.logger.debug(f"States are: { {k: (v['vmid_a'], v['status_a'], v.get('instance_a_transition'), v.get('instance_a_shared'), v['vmid_b'], v['status_b'], v.get('instance_b_transition'), v.get('instance_b_shared')) for k, v in clusters_to_fail_over.items()} }")
      self.logger.info(f"Waiting for firewalls to complete failover...")
      sleep(30)
      self.logger.info(f"Refreshing firewalls directory...")
      self.refresh_status()
      clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_2']}

    self.logger.info(f"Failover to new management vpc complete")
    self.logger.info(f"Phase 2 completed successfully for project {self.project_id}")

  def phase_3_prechecks(self):
    try:
      assert self.transition_vpc is not None, "Transition VPC not found!"
      assert self.target_vpc is not None, "Target management VPC not found!"
      # assert peering_lock._is_owned(), "Peering limit lock is unlocked! Something very strange has happened!"
    except Exception:
      self.logger.error(f"Project {self.project_id} not ready for phase 3, aborting!")
      raise

  def run_phase_3(self, executor: ThreadPoolExecutor, peering_lock):
    self.logger.info(f"Beginning phase 3 for project {self.project_id}...")
    self.refresh_status()
    self.phase_3_prechecks()
    abort=False

    peering_lock.acquire()
    try:
      unpeer_networks(self.transition_vpc, self.target_vpc, self.networks_client)
    except Exception: # TODO: fix - risky
      pass
    self.logger.info(f"Transition/management peering deleted, releasing lock.")
    peering_lock.release()

    clusters_phase_2 = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_2']}
    clusters_phase_3 = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_3']}
    self.logger.info(f"Found {len(clusters_phase_2)} requiring network interface updates, proceeding to update...")
    # Move instance A's to new network
    subnet_map=get_network_subnet_map(self.migration_config.target_vpc_project, str(self.target_vpc.name), self.subnetworks_client)
    futures={}
    for cluster_id, status in clusters_phase_2.items():
      if status.get('instance_a_transition'):
        instance_id = status['instance_a']
        vmid = status['vmid_a']
        vmname = status['instance_a'].name
      elif status.get('instance_b_transition'):
        instance_id = status['instance_b']
        vmid = status['vmid_b']
        vmname = status['instance_b'].name
      else:
        self.logger.exception(f"Project {self.project_id} not ready for phase 3!")
        break
      kwargs={
          "logger": self.logger,
          "instance": instance_id,
          "network": str(self.target_vpc.name),
          "network_project":  self.project_id,
          "subnet_map": subnet_map,
          "subnetworks_client": self.subnetworks_client,
          "instances_client": self.instances_client,
          "iface_name": 'nic1',
          "remove_tag": True,
      }
      self.logger.debug(f"Migrating vmid {vmid}: {vmname} for cluster {cluster_id} to new_mgmt vpc...")
      futures[executor.submit(migrate_instance_to_network, **kwargs)] = vmid
    mgt_ip_futures={}
    tag_futures={}
    for cluster_id, status in clusters_phase_3.items():
      if status.get('instance_a_transition'):
        instance_id = status['instance_a']
        vmname = status['instance_a'].name
      elif status.get('instance_b_transition'):
        instance_id = status['instance_b']
        vmname = status['instance_b'].name
      else:
        break
      tag_futures[executor.submit(remove_tag, instance_id, self.instances_client)]=vmname
    for fut in as_completed(futures):
      try:
        fut.result()
        self.logger.debug(f"VMID {futures[fut]} network interface updated, updating RDB record...")
        iface = get_instance_network_interface(fut.result(), 'nic1')
        mgt_ip_futures[executor.submit(self.update_instance_mgt_ip, futures[fut], iface.network_i_p, self.customer.get_param("id"))]=futures[fut]
      except Exception:
        self.logger.exception(f"VMID {futures[fut]} failed to migrate!")
        abort=True

    for fut in as_completed(tag_futures):
      try:
        fut.result()
        self.logger.debug(f"Instance {tag_futures[fut]} network tags updated")
      except Exception:
        self.logger.exception(f"Instance {tag_futures[fut]} network tags failed to update!")

    for fut in as_completed(mgt_ip_futures):
      fut.result()
      self.logger.debug(f"VMID {mgt_ip_futures[fut]} master ip record updated successfully")

    if abort:
      raise RuntimeError("Errors encountered while updating instance network interfaces, aborting!")

    self.publish_network_sns()
    sleep(30)
    self.refresh_status()

    clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_3']}
    self.logger.debug(f"States are: { {k: (v['vmid_a'], v['status_a'], v.get('instance_a_transition'), v.get('instance_a_shared'), v['vmid_b'], v['status_b'], v.get('instance_b_transition'), v.get('instance_b_shared')) for k, v in clusters_to_fail_over.items()} }")
    while not all([i['status_a'] in ['active', 'passive'] and i['status_b'] in ['active', 'passive'] and i['status_a'] != i['status_b'] for i in clusters_to_fail_over.values()]):
      self.logger.debug(f"States are: { {k: (v['vmid_a'], v['status_a'], v['vmid_b'], v['status_b']) for k, v in clusters_to_fail_over.items()} }")
      self.logger.info(f"Waiting for firewalls to register with DDB...")
      sleep(30)
      self.logger.info(f"Refreshing firewalls directory...")
      self.refresh_status()
      clusters_to_fail_over = {k:v for k,v in self.cluster_classifications.items() if v['phase'] in ['phase_3']}

    self.logger.info(f"Phase 3 completed successfully for project {self.project_id}")

  def trigger_dm_resync(self):
    response = None
    # request = self.dm.deployments().delete(project=self.project_id, deployment="us-east-2", deletePolicy="ABANDON")
    # response = request.execute()
    # self.logger.debug(f"DM resync response {response}")
    if not self.region_id:
      self.trigger_update_for_all_regions()
    else:
      myres, _ = trigger_update_for_instance_changes(self.dbh, self.region_id, self.customer.get_param("id"), avctx=None)
      self.logger.debug(f"Trigger update result {myres}")

  def update_customer(self):
    if self.dry_run:
      return
    if self.revert:
      self.customer.enable_proxy_vms()
    else:
      self.customer.disable_proxy_vms()
    self.customer.save()


  def run_migrate(self, executor: ThreadPoolExecutor):
    self.logger.info(f"Beginning migration for project {self.project_id}, region {self.region_id}...")
    #try:
    #unpeer_networks(self.transition_vpc, self.legacy_management_vpc, self.networks_client)
    #except Exception:
    #  pass
    #self.logger.info(f"[{self.project_id}] Transition/legacy peering deleted, migrating firewall instances to transition VPC...")

    abort=False

    self.logger.info(f"Found {len(self.cluster_instance_records)} clusters requiring network interface updates, proceeding to update...")
    # Move instance A's to new network
    subnet_map=get_network_subnet_map(self.migration_config.target_vpc_project, str(self.target_vpc.name), self.subnetworks_client)
    # self.logger.debug(f"New management subnet map: {subnet_map}")
    futures={}
    #k: [v for v in a.keys()] for k, a in self.cluster_instance_records.items()
    for cluster_id, record in self.cluster_instance_records.items():
      for vmid, record in record.items():
        instance = record['instance']
        vmname = record['instance'].name
        salt_profile_str = record['salt_profile']
        self.logger.debug(f"Salt Profile {salt_profile_str}")
        kwargs={
          "logger": self.logger,
          "instance": instance,
          "network": str(self.target_vpc.name),
          "network_project":  self.migration_config.target_vpc_project,
          "subnet_map": subnet_map,
          "subnetworks_client": self.subnetworks_client,
          "instances_client": self.instances_client,
          "iface_name": 'nic1',
          "salt_profile_str": salt_profile_str,
          "dry_run": self.dry_run,
        }
        self.logger.debug(f"Migrating vmid {vmid}: {vmname} for cluster {cluster_id} to {str(self.target_vpc.name)} vpc...")
        futures[executor.submit(migrate_instance_to_network, **kwargs)] = vmid
    instance_update_futures={}
    salt_profile_futures={}
    for fut in as_completed(futures):
      try:
        res = fut.result()
        if res[0] == None:
          continue
        self.logger.debug(f"VMID {futures[fut]} network interface updated, updating RDB record...")
        iface = get_instance_network_interface(res[0], 'nic1')
        instance_update_futures[executor.submit(self.update_instance_mgt_ip, futures[fut], iface.network_i_p, self.customer.get_param("id"), res[1])]=futures[fut]
      except Exception:
        self.logger.exception(f"Failed to migrate instance, VMID {futures[fut]}")
        abort=True

    for fut in as_completed(instance_update_futures):
      res = fut.result()
      self.logger.debug(f"VMID {instance_update_futures[fut]} instance parameters updated successfully result {res}")

    self.logger.info(f"Trigger DM resync...")
    if self.dry_run == False:
      self.trigger_dm_resync()

    if abort:
      raise RuntimeError("Errors encountered while updating instance network interfaces, aborting!")

    self.logger.info(f"All instances successfully moved to new management VPC for project {self.project_id}")
    self.logger.info(f"Failover to new management vpc complete")
    self.logger.info(f"Migration completed successfully for project {self.project_id}, region {self.region_id}")

  def set_proxy_states_to_no_proxy(self):
    """
    Set proxy states to NO_PROXY based on node types being migrated.
    This maps node types to their corresponding proxy state fields in cust_master.
    """
    if not self.node_types:
      self.logger.info("No node types specified, skipping proxy state updates")
      return

    # Node type to proxy state field mapping based on the codebase context
    node_type_to_proxy_field = {
      '49': 'global_proxy_state',  # Global proxy (firewall)
      '50': 'global_proxy_state',  # Global proxy (firewall)
      '154': 'global_proxy_state', # Global proxy
      '48': 'rn_proxy_state',      # RN proxy
      '51': 'sc_proxy_state',      # SC proxy
      '153': 'swgp_pvm_state',     # SWG proxy PVM
    }

    proxy_fields_to_update = set()

    # Collect unique proxy fields that need to be updated
    for node_type in self.node_types:
      if node_type in node_type_to_proxy_field:
        proxy_fields_to_update.add(node_type_to_proxy_field[node_type])
        self.logger.info(f"Node type {node_type} mapped to proxy field: {node_type_to_proxy_field[node_type]}")
      else:
        self.logger.warning(f"Node type {node_type} does not have a known proxy state mapping")

    if not proxy_fields_to_update:
      self.logger.warning("No proxy state fields identified for update based on provided node types")
      return

    # Update each identified proxy field to NO_PROXY
    for proxy_field in proxy_fields_to_update:
      try:
        self.logger.info(f"Setting {proxy_field} to NO_PROXY (0) for customer {self.customer.get_param('id')}")
        if self.dry_run:
          self.logger.info(f"[DRY RUN] Would set {proxy_field} = {int(ProxyStates.NO_PROXY)} for customer {self.customer.get_param('id')}")
          continue

        # Update the customer model
        self.customer.set_param(proxy_field, int(ProxyStates.NO_PROXY))
        self.logger.info(f"Successfully set {proxy_field} to NO_PROXY for customer {self.customer.get_param('id')}")

      except Exception as e:
        self.logger.error(f"Failed to set {proxy_field} to NO_PROXY for customer {self.customer.get_param('id')}: {str(e)}")
        raise

    # Save all changes at once
    if not self.dry_run:
      try:
        self.customer.save()
        self.logger.info(f"Successfully saved proxy state changes for customer {self.customer.get_param('id')}")
      except Exception as e:
        self.logger.error(f"Failed to save proxy state changes for customer {self.customer.get_param('id')}: {str(e)}")
        raise
    else:
      self.logger.info("[DRY RUN] Would save proxy state changes to database")

    # Log final state for verification
    for proxy_field in proxy_fields_to_update:
      current_value = self.customer.get_param(proxy_field)
      self.logger.info(f"Final state - {proxy_field}: {current_value} (expected: {int(ProxyStates.NO_PROXY)})")


def main(args):
  logger = RastroLoggingInfra(name="cust_migration", fileBasedLogging=True, log_root_dir='/home/<USER>/orchestrator/utils/gcp_cust_provisioning/cust_migration_no_proxy_arch/', log_tag = None)
  stream_handler = logging.StreamHandler()
  stream_handler.setLevel(logging.INFO)
  formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
  stream_handler.setFormatter(formatter)
  logger.logger.addHandler(stream_handler)
  script_name = sys.argv[0]
  arguments = sys.argv[1:]
  command = " ".join([script_name] + arguments)
  logger.info(f"Migration script invoked using command {command}")

  credentials=gcp_authenticate(cfg, logger)
  networks_client=NetworksClient(credentials=credentials)
  subnetworks_client=SubnetworksClient(credentials=credentials)
  instances_client=InstancesClient(credentials=credentials)
  firewalls_client=FirewallsClient(credentials=credentials)
  dm_client=build('deploymentmanager', 'v2', cache_discovery=False, credentials=credentials)
  cidr = None
  if args.cidr:
    cidr=IPv4Network(args.cidr)
  if args.revert:
    project_id = get_project_id(args.custid)
    target_vpc = get_tenant_management_vpc(networks_client, project_id)
  else:
    project_id = args.target_vpc_project
    target_vpc=networks_client.get(project=args.target_vpc_project, network=args.target_vpc)
  migration_config=MigrationConfig(
      target_vpc=target_vpc,
      global_cidr_range=cidr,
      target_vpc_project=project_id,
  )
  with ThreadPoolExecutor(max_workers=args.workers) as executor:
    migration_project=MigrationProject(
        project_id=project_id,
        customer_id=args.custid,
        region_id=args.region,
        logger=logger,
        dm_client=dm_client,
        instances_client=instances_client,
        networks_client=networks_client,
        subnetworks_client=subnetworks_client,
        firewalls_client=firewalls_client,
        migration_config=migration_config,
        executor=executor,
        node_types = args.node_types,
        ignore_ha_state=args.ignore_ha_state,
        dry_run=args.dry_run,
        revert=args.revert,
        migrate_tf_regions=args.migrate_tf_regions,
    )
    #migration_project.update_instance_mgt_ip_records(executor)
    #migration_project.publish_network_sns()
    lock=RLock()
    if args.ignore_ha_state:
      migration_project.run_migrate(executor)
    if args.phase1:
      migration_project.run_phase_1(executor)
    if args.phase2:
      migration_project.run_phase_2(executor, lock)
    if args.phase3:
      migration_project.run_phase_3(executor, lock)
    if args.restore_proxy_vms:
      migration_project.customer.enable_proxy_vms()
      migration_project.customer.save()
    if args.set_no_proxy_state:
      migration_project.set_proxy_states_to_no_proxy()

if __name__ == "__main__":
  parser=argparse.ArgumentParser()
  parser.add_argument("-c", "--custid", help="GCP Cust ID for project to migrate.", required=True)
  parser.add_argument("-r", "--region", help="GCP Region ID for project to migrate.", required=False)
  parser.add_argument("-n", "--cidr", help="cidr range to use for the transition VPC subnets.", required=False)
  parser.add_argument("-tp", "--target-vpc-project", help="project ID for the target VPC project of the management VPC.",
                      required=False, default="host-gpcs-dev-01")
  parser.add_argument("-tv", "--target-vpc", help="Name of the target management VPC", required=False, default="shared-mgmt-vpc")
  parser.add_argument("-w", "--workers", help="Number of thread pool workers to use. Defaults to 1 for testing purposes", default=1, type=int)
  parser.add_argument("-d", "--dry-run", help="Make no project changes, only print operations that would be run", action="store_true", default=False)
  parser.add_argument("-p1", "--phase1", help="Run the first phase", action="store_true", default=False)
  parser.add_argument("-p2", "--phase2", help="Run the second phase", action="store_true", default=False)
  parser.add_argument("-p3", "--phase3", help="Run the third phase", action="store_true", default=False)
  parser.add_argument("-ihs", "--ignore-ha-state", help="Run the migration without ha state sync", action="store_true", default=True)
  parser.add_argument("-lt", "--local-test", help="Local testing mode - bypass PANW auth flow", action="store_true", default=False)
  parser.add_argument("-nt", "--node-types", nargs='+', help="list of node types to migrate", default=['48', '49', '50', '51', '154'])
  parser.add_argument("-rt", "--revert", help="Revert migration back to tenant management vpc", action="store_true", default=False)
  parser.add_argument("-rpvms", "--restore-proxy-vms", help="Enable proxy VM states at the end of script completion", action="store_true", default=False)

  # NEW TERRAFORM SAFETY PARAMETER
  parser.add_argument("-mtr", "--migrate-tf-regions",
                      help="Allow migration on Terraform-managed regions. CAUTION: This bypasses safety checks for dual provisioning conflicts. Use only if you understand the implications.",
                      action="store_true",
                      default=False)
  # PROXY STATE PARAMETER
  parser.add_argument("-snps", "--set-no-proxy-state",
                      help="Set proxy states to NO_PROXY for the specified node types. Affects rn_proxy_state, global_proxy_state, sc_proxy_state, and swgp_pvm_state fields based on node type mapping.",
                      action="store_true",
                      default=False)
  args=parser.parse_args()
  main(args)
