// package metrics_server

package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net"
	"orchestrator/avisar_service/libs/alert_manager"
	"orchestrator/libs/go/aws/partition"
	"orchestrator/utils/cdl"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"syscall"
	"time"

	"orchestrator/avisar_service/app/iot_cert_cleanup"
	"orchestrator/avisar_service/app/key_rotation"
	"orchestrator/avisar_service/app/ngpa_auto_migration"
	"orchestrator/avisar_service/app/orch_db_cleanup"
	"orchestrator/avisar_service/app/rpc_handler"
	"orchestrator/avisar_service/app/scale_event"
	"orchestrator/avisar_service/libs/utils"
	"orchestrator/libs/common/shared/grpc/proto/avisarpb"
	"orchestrator/libs/go/aws/region"
	aws_utils "orchestrator/libs/go/aws/utils"
	"orchestrator/libs/go/cogs"
	"orchestrator/libs/go/config"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/upgrade_service/db"
	"orchestrator/upgrade_service/definitions"

	"github.com/spf13/viper"
	"google.golang.org/grpc"

	"go.panw.local/pangolin/clogger"
)

type eventProxyCfg struct {
	debug                       bool
	serverIP                    string
	serverPort                  string
	timeout                     string
	marsEventsTopicName         string
	notificationEventsTopicName string
	GCPProjectID                string
	credsFilePath               string
}

type iotCertCleanupListenerCfg struct {
	listenerSQSQueueName    string
	listnerSQSFailQueueName string
	awsAccountID            string
	cronPollFrequency       uint32
}

type verticalScaleEventCfg struct {
	vertScaleTopicName          string
	GCPProjectID                string
	senderGCPProjectID          string
	marsEventsTopicName         string
	notificationEventsTopicName string
}

var (
	cfg      *config.Config
	gLogger  *clogger.Clogger
	localRun bool

	basePath = flag.String("base-path", "/orch_aas/libs", "Base path to orchestrator")
	logMode  = flag.String("log-mode", "stdout", "Log to stdout or file")
	runType  = flag.String("run-type", "kubernetes", "Default to the kubernetes run type")
	logFile  = flag.String("log-file", "/var/log/pan/avisar_service.log", "Log file to log to")
)

func setupLogger(cfg *config.Config) {
	deployedEnv := ""
	if cfg != nil {
		deployedEnv = cfg.UpgradeService.DeployedEnv
	}
	if deployedEnv == "dev" {
		gLogger = clogger.NewConsoleLogger()
	} else {
		gLogger = clogger.NewLogger(os.Stdout)
	}
	gLogger.AddContext(clogger.RastroCtx)
}

func setupFileLogger(cfg *config.Config, filePath string) {
	writer := &clogger.RotateWriter{
		FileName:     filePath,
		MaxSize:      50 * 1024 * 1024, // 50MB
		MaxRetained:  10,               // Keep 10 archived logs
		NeedCompress: true,             // Compress the archived logs
	}
	gLogger = clogger.NewLogger(writer)
	gLogger.AddContext(clogger.RastroCtx)
}

func (cfg eventProxyCfg) String() string {
	return "Debug: " + strconv.FormatBool(cfg.debug) + "\n" +
		"IP address: " + cfg.serverIP + "\n" +
		"Port: " + cfg.serverPort + "\n" +
		"Timeout: " + cfg.timeout + "\n"
}

/* Let's read in the local configuration file: */

func isLocalRunEnabled() bool {
	/* Setup the paths: */
	viper.AddConfigPath(".")
	viper.AddConfigPath("avisar_service")
	viper.SetConfigName("config")
	err := viper.ReadInConfig()
	if err != nil {
		log.Printf("Viper could not read the config file, error %+v", err)
		return false
	}

	lRun := viper.Get("run.local")
	if lRun == true {
		log.Printf("Local run is enabled")
	} else {
		log.Printf("Local run is disabled")
	}
	return lRun.(bool)
}

func readEventProxyCfg(logger *clogger.Clogger) *eventProxyCfg {
	var myEventProxyCfg eventProxyCfg

	/* Setup the paths: */
	viper.AddConfigPath("avisar_service")
	viper.AddConfigPath(".")
	viper.SetConfigName("config")
	err := viper.ReadInConfig()
	if err != nil {
		log.Printf("Viper could not read the config file, error %+v", err)
		return nil
	}

	debug := viper.Get("logging.debug")
	if debug == nil {
		myEventProxyCfg.debug = false
	} else {
		myEventProxyCfg.debug = debug.(bool)
	}

	serverIP := viper.Get("publish.alerts.listen.ip")
	if serverIP == nil {
		myEventProxyCfg.serverIP = "0.0.0.0"
	} else {
		myEventProxyCfg.serverIP = serverIP.(string)
	}

	serverPort := viper.Get("publish.alerts.listen.port")
	if serverPort == nil {
		myEventProxyCfg.serverPort = "50051"
	} else {
		myEventProxyCfg.serverPort = serverPort.(string)
	}

	gcpProjectID := viper.Get("publish.alerts.gcpProjectID")
	if gcpProjectID == nil {
		myEventProxyCfg.GCPProjectID = ""
	} else {
		myEventProxyCfg.GCPProjectID = gcpProjectID.(string)
		logger.LogInfo("Critical events ProjectID to use for critical events is %s",
			myEventProxyCfg.GCPProjectID)
	}

	marsEventsTopicName := viper.Get("publish.alerts.marsEventsTopicName")
	if marsEventsTopicName == nil {
		myEventProxyCfg.marsEventsTopicName = ""
	} else {
		myEventProxyCfg.marsEventsTopicName = marsEventsTopicName.(string)
		logger.LogInfo("Topic name to use for critical events is: %s",
			myEventProxyCfg.marsEventsTopicName)
	}

	notificationEventsTopicName := viper.Get("publish.alerts.notificationEventsTopicName")
	if notificationEventsTopicName == nil {
		myEventProxyCfg.notificationEventsTopicName = ""
	} else {
		myEventProxyCfg.notificationEventsTopicName = notificationEventsTopicName.(string)
		logger.LogInfo("Topic name to use for all events: %s", myEventProxyCfg.notificationEventsTopicName)
	}

	credsFilePath := viper.Get("publish.alerts.credsFilePath")
	if credsFilePath == nil {
		myEventProxyCfg.credsFilePath = ""
	} else {
		myEventProxyCfg.credsFilePath = credsFilePath.(string)
		logger.LogInfo("Credentials file path to use for critical events is: %s",
			myEventProxyCfg.credsFilePath)
	}
	return &myEventProxyCfg
}

func readIoTCertCleanupListenerCfg(logger *clogger.Clogger) *iotCertCleanupListenerCfg {
	var myIoTCertCleanupListenerCfg iotCertCleanupListenerCfg

	/* Setup the paths: */
	viper.AddConfigPath("avisar_service")
	viper.AddConfigPath(".")
	viper.SetConfigName("config")
	err := viper.ReadInConfig()
	if err != nil {
		log.Printf("Viper could not read the local config file, error %+v", err)
		return nil
	}

	awsAccountID := viper.Get("iotCertCleanup.events.AWSAccountID")
	if awsAccountID == nil {
		myIoTCertCleanupListenerCfg.awsAccountID = ""
	} else {
		myIoTCertCleanupListenerCfg.awsAccountID = awsAccountID.(string)
		logger.LogInfo("AWS account ID to use for IoT cert clean up listener is %s",
			myIoTCertCleanupListenerCfg.awsAccountID)
	}

	listenerSQSQueueName := viper.Get("iotCertCleanup.events.listenerSQSQueueName")
	if listenerSQSQueueName == nil {
		myIoTCertCleanupListenerCfg.listenerSQSQueueName = ""
	} else {
		myIoTCertCleanupListenerCfg.listenerSQSQueueName = listenerSQSQueueName.(string)
		logger.LogInfo("SQS Queue name to use for IoT cert clean up is: %s",
			myIoTCertCleanupListenerCfg.listenerSQSQueueName)
	}

	// TODO: Read failq name from config once PASRE is fixed.
	myIoTCertCleanupListenerCfg.listnerSQSFailQueueName = "msgbus-resource-cleanup-failq.fifo"

	cronPollFrequency := viper.Get("iotCertCleanup.events.cronPollFrequency")
	if cronPollFrequency == nil {
		myIoTCertCleanupListenerCfg.cronPollFrequency = 300
	} else {
		i, err := strconv.ParseInt(cronPollFrequency.(string), 10, 32)
		if err != nil {
			logger.LogError("Failed to parse the cron Poll frequency")
			i = 300
		}

		myIoTCertCleanupListenerCfg.cronPollFrequency = uint32(i)
		logger.LogInfo("Cron Poll frequency to use for iotCertCleanup: %d",
			myIoTCertCleanupListenerCfg.cronPollFrequency)
	}
	return &myIoTCertCleanupListenerCfg
}

func readVerticalScaleEventCfg(logger *clogger.Clogger) *verticalScaleEventCfg {
	var myVerticalScaleEventCfg verticalScaleEventCfg

	/* Setup the paths: */
	viper.AddConfigPath("avisar_service")
	viper.AddConfigPath(".")
	viper.SetConfigName("config")
	err := viper.ReadInConfig()
	if err != nil {
		log.Printf("Viper could not read the local config file, error %+v", err)
		return nil
	}

	GCPProjectID := viper.Get("verticalScale.events.gcpProjectID")
	if GCPProjectID == nil {
		myVerticalScaleEventCfg.GCPProjectID = ""
	} else {
		myVerticalScaleEventCfg.GCPProjectID = GCPProjectID.(string)
		logger.LogInfo("GCP Project ID to use for Vertical Scale Event listener is %s",
			myVerticalScaleEventCfg.GCPProjectID)
	}

	senderGCPProjectID := viper.Get("publish.alerts.gcpProjectID")
	if GCPProjectID == nil {
		myVerticalScaleEventCfg.senderGCPProjectID = ""
	} else {
		myVerticalScaleEventCfg.senderGCPProjectID = senderGCPProjectID.(string)
		logger.LogInfo("GCP Project ID to use for All Events notification is %s",
			myVerticalScaleEventCfg.senderGCPProjectID)
	}

	vertScaleTopicName := viper.Get("verticalScale.events.vertScaleTopicName")
	if vertScaleTopicName == nil {
		myVerticalScaleEventCfg.vertScaleTopicName = ""
	} else {
		myVerticalScaleEventCfg.vertScaleTopicName = vertScaleTopicName.(string)
		logger.LogInfo("GCP PubSub Topic Name to use for Vertical Scale Events is: %s",
			myVerticalScaleEventCfg.vertScaleTopicName)
	}

	marsEventsTopicName := viper.Get("publish.alerts.marsEventsTopicName")
	if marsEventsTopicName == nil {
		myVerticalScaleEventCfg.marsEventsTopicName = ""
	} else {
		myVerticalScaleEventCfg.marsEventsTopicName = marsEventsTopicName.(string)
		logger.LogInfo("Topic name to use for critical events is: %s",
			myVerticalScaleEventCfg.marsEventsTopicName)
	}

	notificationEventsTopicName := viper.Get("publish.alerts.notificationEventsTopicName")
	if notificationEventsTopicName == nil {
		myVerticalScaleEventCfg.notificationEventsTopicName = ""
	} else {
		myVerticalScaleEventCfg.notificationEventsTopicName = notificationEventsTopicName.(string)
		logger.LogInfo("Topic name to use for all events: %s", myVerticalScaleEventCfg.notificationEventsTopicName)
	}

	return &myVerticalScaleEventCfg
}

func createPubSubClientMarsEvents(myServer *rpc_handler.Server,
	VertScaleEventCtxt *scale_event.EventsCtxt,
	cdlRegionIDX int,
	gcpProjectID string,
	topicName string) {
	if len(gcpProjectID) == 0 {
		fmt.Printf("Skipping creation of PubSub client in %d since no project ID is given\n", cdlRegionIDX)
		return
	}
	var myEventPubSubInfo rpc_handler.EventPubSubInfo
	ctx := context.Background()

	myEventPubSubInfo.PubSubClient, _, myEventPubSubInfo.PublishingTopic =
		cogs.CreateGCPPubSubClient(&ctx, gcpProjectID, topicName)

	// TODO: Remove it
	fmt.Printf("PubSubClient: %+v, PublishingTopic: %+v", myEventPubSubInfo.PubSubClient, myEventPubSubInfo.PublishingTopic)
	/* caller will provide the context struct to hold clients */
	if myServer != nil {
		myServer.MyMarsEvents.MarsEventDetailsMap[cdlRegionIDX] = &myEventPubSubInfo
	}
	if VertScaleEventCtxt != nil {
		VertScaleEventCtxt.MyMarsEvents.MarsEventDetailsMap[cdlRegionIDX] = &myEventPubSubInfo
	}
}

func createPubSubClientAllEvents(myServer *rpc_handler.Server,
	VertScaleEventCtxt *scale_event.EventsCtxt,
	gcpProjectID string,
	topicName string) {
	var myEventPubSubInfo rpc_handler.EventPubSubInfo
	ctx := context.Background()

	myEventPubSubInfo.PubSubClient, _, myEventPubSubInfo.PublishingTopic =
		cogs.CreateGCPPubSubClient(&ctx, gcpProjectID, topicName)

	/* caller will provide the context struct to hold clients */
	if myServer != nil {
		myServer.MyAllEvents.AllEventsDetails = myEventPubSubInfo
	}

	if VertScaleEventCtxt != nil {
		VertScaleEventCtxt.MyAllEvents.AllEventsDetails = myEventPubSubInfo
	}
}

func startGRPCService(logger *clogger.Clogger,
	myEventProxyCfg *eventProxyCfg) {
	logger.LogInfo("Starting GRPC service...")
	// If the function exits we would close the wait group.
	var myServer rpc_handler.Server
	var serverAddress string
	// Create and/or set subscriptions for the MARS Alerting service.

	if !localRun {
		/*
			# Setup of the APP CREDS would be done by init binary.
			fileLocation := cfg.AvisarService.CredsFilePath
			os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", fileLocation)
			logger.LogInfo("Default file location for credentials: %s", fileLocation)
		*/

		if cfg.AvisarService.MarsEvents.ForwardingEnabled == false &&
			cfg.AvisarService.AllEvents.ForwardingEnabled == false {
			logger.LogInfo("Forwarding to event Pipeline is disabled. Cannot continue!")
			return
		}

		if cfg.AvisarService.MarsEvents.ForwardingEnabled == false {
			logger.LogInfo("Forwarding is not enabled for MarsEvents")
		} else {
			myServer.MyMarsEvents.ForwardingEnabled = cfg.AvisarService.MarsEvents.ForwardingEnabled
			myServer.MyMarsEvents.MarsEventDetailsMap = make(map[int]*rpc_handler.EventPubSubInfo)
			createPubSubClientMarsEvents(&myServer, nil, cdl.CDL_REGION_UNKNOWN_IDX,
				cfg.AvisarService.MarsEvents.Americas.GcpProjectID,
				cfg.AvisarService.MarsEvents.Americas.MarsEventsTopicName)
			createPubSubClientMarsEvents(&myServer, nil, cdl.CDL_REGION_AMERICAS_IDX,
				cfg.AvisarService.MarsEvents.Americas.GcpProjectID,
				cfg.AvisarService.MarsEvents.Americas.MarsEventsTopicName)
			createPubSubClientMarsEvents(&myServer, nil, cdl.CDL_REGION_EUROPE_IDX,
				cfg.AvisarService.MarsEvents.Europe.GcpProjectID,
				cfg.AvisarService.MarsEvents.Europe.MarsEventsTopicName)
			createPubSubClientMarsEvents(&myServer, nil, cdl.CDL_REGION_UK_IDX,
				cfg.AvisarService.MarsEvents.Unitedkingdom.GcpProjectID,
				cfg.AvisarService.MarsEvents.Unitedkingdom.MarsEventsTopicName)
			createPubSubClientMarsEvents(&myServer, nil, cdl.CDL_REGION_FEDRAMP_IDX,
				cfg.AvisarService.MarsEvents.Fedramp.GcpProjectID,
				cfg.AvisarService.MarsEvents.Fedramp.MarsEventsTopicName)
			createPubSubClientMarsEvents(&myServer, nil, cdl.CDL_REGION_CANADA_IDX,
				cfg.AvisarService.MarsEvents.Canada.GcpProjectID,
				cfg.AvisarService.MarsEvents.Canada.MarsEventsTopicName)
			createPubSubClientMarsEvents(&myServer, nil, cdl.CDL_REGION_JAPAN_IDX,
				cfg.AvisarService.MarsEvents.Japan.GcpProjectID,
				cfg.AvisarService.MarsEvents.Japan.MarsEventsTopicName)
			createPubSubClientMarsEvents(&myServer, nil, cdl.CDL_REGION_AUSTRALIA_IDX,
				cfg.AvisarService.MarsEvents.Australia.GcpProjectID,
				cfg.AvisarService.MarsEvents.Australia.MarsEventsTopicName)
			createPubSubClientMarsEvents(&myServer, nil, cdl.CDL_REGION_GERMANY_IDX,
				cfg.AvisarService.MarsEvents.Germany.GcpProjectID,
				cfg.AvisarService.MarsEvents.Germany.MarsEventsTopicName)
			createPubSubClientMarsEvents(&myServer, nil, cdl.CDL_REGION_INDIA_IDX,
				cfg.AvisarService.MarsEvents.India.GcpProjectID,
				cfg.AvisarService.MarsEvents.India.MarsEventsTopicName)
			createPubSubClientMarsEvents(&myServer, nil, cdl.CDL_REGION_SINGAPORE_IDX,
				cfg.AvisarService.MarsEvents.Singapore.GcpProjectID,
				cfg.AvisarService.MarsEvents.Singapore.MarsEventsTopicName)
		}

		if cfg.AvisarService.MarsEvents.ForwardingEnabled == false {
			logger.LogInfo("Forwarding is not enabled for All Events")
		} else {
			myServer.MyAllEvents.ForwardingEnabled = cfg.AvisarService.AllEvents.ForwardingEnabled
			createPubSubClientAllEvents(&myServer, nil, cfg.AvisarService.AllEvents.GcpProjectID,
				cfg.AvisarService.AllEvents.MarsEventsTopicName)
		}
	} else {
		logger.LogInfo("This is local run.")

		// Set the Env variable for Google App Credentials
		fileLocation := myEventProxyCfg.credsFilePath
		os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", fileLocation)
		logger.LogInfo("Default file location for credentials: %s", fileLocation)
		myServer.MyAllEvents.ForwardingEnabled = true
		myServer.MyMarsEvents.ForwardingEnabled = false
		createPubSubClientAllEvents(&myServer, nil, myEventProxyCfg.GCPProjectID,
			myEventProxyCfg.notificationEventsTopicName)
	}
	eLog := utils.GetEventLogger(logger)
	myServer.DbCtx = &sql.DbContext{Logger: eLog, DbConn: db.GetDBConn()}

	/*
		marsPubSubClient, _, marsPublishingTopic := cogs.CreateGCPPubSubClient(&ctx, gcpProjectID, marsAlertsTopic)
		logger.LogInfo("Mars events Pub Sub Client %+v", marsPubSubClient)
		logger.LogInfo("publishingTopic %+v", marsPublishingTopic)

		notificationEventsPubSubClient, _, allEventsPublishingTopic := cogs.CreateGCPPubSubClient(&ctx, gcpProjectID,
			notificationAlertsTopic)
		logger.LogInfo("All events Pub SubClient %+v", notificationEventsPubSubClient)
		logger.LogInfo("publishingTopic %+v", allEventsPublishingTopic)
	*/
	// TODO Remove it.
	fmt.Printf("Server details: %+v", myServer)

	if localRun && myEventProxyCfg != nil {
		serverAddress = strings.Join([]string{fmt.Sprintf("%v", myEventProxyCfg.serverIP),
			fmt.Sprintf("%v", myEventProxyCfg.serverPort)},
			":")
	} else {
		serverAddress = "0.0.0.0:50051"
	}

	// Format: 0.0.0.0:50051
	lis, err := net.Listen("tcp", serverAddress)
	if err != nil {
		log.Fatalf("Failed to listen: %v", err)
	}

	s := grpc.NewServer()
	avisarpb.RegisterAvisarServiceServer(s, &myServer)

	if err := s.Serve(lis); err != nil {
		log.Fatalf("Failed to serve %v", err)
	}
}

func startIOTCleanupCogsWheel(myIoTCertCleanupListenerCfg *iotCertCleanupListenerCfg) cogs.Machine {
	if cfg.AvisarService.IotCertCleanup.ReceivingEnabled == false {
		gLogger.LogInfo("Receiving to event Pipeline is disabled. Cannot continue!")
		return nil
	}
	gLogger.LogInfo("Starting the IOT cleanup cogs wheel")
	// Do not need Cron for now. Will enable if required.
	// var myCronPollFrequency uint32
	var myListenerSQSQueueName string
	var myListenerSQSFailQueueName string
	var myAWSAccountID string

	if isLocalRunEnabled() == true {
		// myCronPollFrequency = myIoTCertCleanupListenerCfg.cronPollFrequency
		myListenerSQSQueueName = myIoTCertCleanupListenerCfg.listenerSQSQueueName
		myListenerSQSFailQueueName = myIoTCertCleanupListenerCfg.listnerSQSFailQueueName
		myAWSAccountID = myIoTCertCleanupListenerCfg.awsAccountID
	} else {
		// myCronPollFrequency = cfg.AvisarService.IotCertCleanup.CronPollFrequency
		myListenerSQSQueueName = cfg.AvisarService.IotCertCleanup.ListenerSQSQueueName
		myAWSAccountID = cfg.AvisarService.IotCertCleanup.AwsAccountID
		// TODO: Fix this once PASRE ticket is fixed for FailQueueName.
		myListenerSQSFailQueueName = "msgbus-resource-cleanup-failq.fifo"
	}

	m := cogs.NewMachine(
		cogs.WithLogger(gLogger),
		cogs.WithSQLConn(db.GetDBConn()),
		cogs.WithState(
			cogs.NewState(
				"iot_cert_cleanup",
				"Monitor for notifications related to IoT cert cleanup",
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithSQS(myListenerSQSQueueName)),
					cogs.WithHandler(iot_cert_cleanup.Handler)),
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithSQS(myListenerSQSFailQueueName)),
					cogs.WithHandler(iot_cert_cleanup.Handler)),
				// cogs.WithCronJob(iot_cert_cleanup.IoTCertCleanupCron, cogs.CronPeriod{Seconds: myCronPollFrequency}),
				cogs.WithPublisher(cogs.WithSNS(myAWSAccountID)),
			),
		),
	)
	gLogger.LogInfo("Starting the default cogs machine")
	m.Start()
	return m
}

func startDBCleanupCogsWheel() cogs.Machine {
	gLogger.LogInfo("Starting the DB cleanup cogs wheel")
	m := cogs.NewMachine(
		cogs.WithLogger(gLogger),
		cogs.WithSQLConn(db.GetDBConn()),
		cogs.WithState(
			cogs.NewState(
				"orch_db_cleanup",
				"Cleanup Database state",
				cogs.WithCronJob(orch_db_cleanup.CronHandler, cogs.CronPeriod{
					Hours:   23,
					Minutes: 59,
					Seconds: 59,
				}),
			),
		),
	)
	gLogger.LogInfo("Starting the default cogs machine")
	m.Start()
	return m
}

func startVertScaleCogsWheel(myVerticalScaleEventCfg *verticalScaleEventCfg) cogs.Machine {

	var (
		myVerticalScaleTopicName string
		listenerGCPProjectId     string
		senderGCPProjectId       string
		pubsubSubscriptionName   string
	)

	if cfg.AvisarService.VerticalScaleEvent.ForwardingEnabled == false {
		gLogger.LogInfo("Forwarding for Vertical Scale Events to event Pipeline is disabled. Cannot continue!")
		return nil
	}
	gLogger.LogInfo("Starting the wheel cogs wheel for Vertical Scale Events")

	if isLocalRunEnabled() == true {
		myVerticalScaleTopicName = myVerticalScaleEventCfg.vertScaleTopicName
		pubsubSubscriptionName = myVerticalScaleEventCfg.vertScaleTopicName
		/* TODO Check GCP project id from where msg is received */
		listenerGCPProjectId = myVerticalScaleEventCfg.GCPProjectID
		senderGCPProjectId = myVerticalScaleEventCfg.senderGCPProjectID
		scale_event.VertScaleEventCtxt.MyAllEvents.ForwardingEnabled = true
		scale_event.VertScaleEventCtxt.MyMarsEvents.ForwardingEnabled = false
		createPubSubClientAllEvents(nil, &scale_event.VertScaleEventCtxt, senderGCPProjectId,
			myVerticalScaleEventCfg.notificationEventsTopicName)
	} else {
		myVerticalScaleTopicName = cfg.AvisarService.VerticalScaleEvent.VertScaleTopicName
		pubsubSubscriptionName = fmt.Sprintf("%s-%s-sub", myVerticalScaleTopicName,
			strings.TrimPrefix(cfg.UpgradeService.VertScalePubSubTopic, "event_vscale_topic_"))
		listenerGCPProjectId = cfg.AvisarService.VerticalScaleEvent.GcpProjectID
		gLogger.LogInfo("Using Project %+v and topic name %+v !", listenerGCPProjectId, myVerticalScaleTopicName)
		/* pubsub client ctxt to forward messages */
		if cfg.AvisarService.MarsEvents.ForwardingEnabled == false &&
			cfg.AvisarService.AllEvents.ForwardingEnabled == false {
			gLogger.LogInfo("Forwarding to event Pipeline is disabled. Cannot continue!")
			return nil
		}

		if cfg.AvisarService.MarsEvents.ForwardingEnabled == false {
			gLogger.LogInfo("Forwarding is not enabled for MarsEvents")
		} else {
			gLogger.LogInfo("Vertical Scale Event: Creating Events Pipeline Client Context for MarsEvents")
			scale_event.VertScaleEventCtxt.MyMarsEvents.ForwardingEnabled = cfg.AvisarService.MarsEvents.ForwardingEnabled
			scale_event.VertScaleEventCtxt.MyMarsEvents.MarsEventDetailsMap = make(map[int]*rpc_handler.EventPubSubInfo)
			createPubSubClientMarsEvents(nil, &scale_event.VertScaleEventCtxt, cdl.CDL_REGION_UNKNOWN_IDX,
				cfg.AvisarService.MarsEvents.Americas.GcpProjectID,
				cfg.AvisarService.MarsEvents.Americas.MarsEventsTopicName)
			createPubSubClientMarsEvents(nil, &scale_event.VertScaleEventCtxt, cdl.CDL_REGION_AMERICAS_IDX,
				cfg.AvisarService.MarsEvents.Americas.GcpProjectID,
				cfg.AvisarService.MarsEvents.Americas.MarsEventsTopicName)
			createPubSubClientMarsEvents(nil, &scale_event.VertScaleEventCtxt, cdl.CDL_REGION_EUROPE_IDX,
				cfg.AvisarService.MarsEvents.Europe.GcpProjectID,
				cfg.AvisarService.MarsEvents.Europe.MarsEventsTopicName)
			createPubSubClientMarsEvents(nil, &scale_event.VertScaleEventCtxt, cdl.CDL_REGION_UK_IDX,
				cfg.AvisarService.MarsEvents.Unitedkingdom.GcpProjectID,
				cfg.AvisarService.MarsEvents.Unitedkingdom.MarsEventsTopicName)
			createPubSubClientMarsEvents(nil, &scale_event.VertScaleEventCtxt, cdl.CDL_REGION_FEDRAMP_IDX,
				cfg.AvisarService.MarsEvents.Fedramp.GcpProjectID,
				cfg.AvisarService.MarsEvents.Fedramp.MarsEventsTopicName)
			createPubSubClientMarsEvents(nil, &scale_event.VertScaleEventCtxt, cdl.CDL_REGION_CANADA_IDX,
				cfg.AvisarService.MarsEvents.Canada.GcpProjectID,
				cfg.AvisarService.MarsEvents.Canada.MarsEventsTopicName)
			createPubSubClientMarsEvents(nil, &scale_event.VertScaleEventCtxt, cdl.CDL_REGION_JAPAN_IDX,
				cfg.AvisarService.MarsEvents.Japan.GcpProjectID,
				cfg.AvisarService.MarsEvents.Japan.MarsEventsTopicName)
			createPubSubClientMarsEvents(nil, &scale_event.VertScaleEventCtxt, cdl.CDL_REGION_AUSTRALIA_IDX,
				cfg.AvisarService.MarsEvents.Australia.GcpProjectID,
				cfg.AvisarService.MarsEvents.Australia.MarsEventsTopicName)
			createPubSubClientMarsEvents(nil, &scale_event.VertScaleEventCtxt, cdl.CDL_REGION_GERMANY_IDX,
				cfg.AvisarService.MarsEvents.Germany.GcpProjectID,
				cfg.AvisarService.MarsEvents.Germany.MarsEventsTopicName)
			createPubSubClientMarsEvents(nil, &scale_event.VertScaleEventCtxt, cdl.CDL_REGION_INDIA_IDX,
				cfg.AvisarService.MarsEvents.India.GcpProjectID,
				cfg.AvisarService.MarsEvents.India.MarsEventsTopicName)
			createPubSubClientMarsEvents(nil, &scale_event.VertScaleEventCtxt, cdl.CDL_REGION_SINGAPORE_IDX,
				cfg.AvisarService.MarsEvents.Singapore.GcpProjectID,
				cfg.AvisarService.MarsEvents.Singapore.MarsEventsTopicName)
		}

		if cfg.AvisarService.AllEvents.ForwardingEnabled == false {
			gLogger.LogInfo("Forwarding is not enabled for All Events")
		} else {
			gLogger.LogInfo("Vertical Scale Event: Creating Events Pipeline Client Context for AllEvents")
			scale_event.VertScaleEventCtxt.MyAllEvents.ForwardingEnabled = cfg.AvisarService.AllEvents.ForwardingEnabled
			createPubSubClientAllEvents(nil, &scale_event.VertScaleEventCtxt, cfg.AvisarService.AllEvents.GcpProjectID,
				cfg.AvisarService.AllEvents.MarsEventsTopicName)
		}
	}

	m := cogs.NewMachine(
		cogs.WithLogger(gLogger),
		cogs.WithSQLConn(db.GetDBConn()),
		cogs.WithState(
			cogs.NewState(
				"scale_event",
				"Monitor for notifications related to Vertical and Auto Scale Events",
				cogs.WithSubscription(cogs.WithSubscriberConfig(cogs.WithPubSubSubscriber(
					listenerGCPProjectId, pubsubSubscriptionName)),
					cogs.WithHandler(scale_event.Handler)),
			),
		),
	)
	gLogger.LogInfo("Starting the cogs service for the Vertical Scale Event")
	m.Start()
	return m
}

func startKeyRotationCogsWheel() cogs.Machine {
	if cfg.AvisarService.KeyRotation.ServiceEnabled == false {
		gLogger.LogInfo("Key Rotation disabled. Cannot continue!")
		return nil
	}
	gLogger.LogInfo("Creating the new cogs wheel machine for key rotation service")

	m := cogs.NewMachine(
		cogs.WithLogger(gLogger),
		cogs.WithSQLConn(db.GetDBConn()),
		cogs.WithState(
			cogs.NewState(
				"key_rotation",
				"Key rotation",
				cogs.WithCronJob(key_rotation.CronHandler, cogs.CronPeriod{
					Hours:   23,
					Minutes: 59,
					Seconds: 59,
				}),
			),
		),
	)
	gLogger.LogInfo("Starting the cogs wheel machine for key rotation")
	m.Start()
	return m
}

func startEPKeyRotationCogsWheel() cogs.Machine {
	if cfg.AvisarService.EPKeyRotation.ServiceEnabled == false {
		gLogger.LogInfo("EP key Rotation disabled. Cannot continue!")
		return nil
	}
	gLogger.LogInfo("Creating the new cogs wheel machine for EP key rotation service")

	m := cogs.NewMachine(
		cogs.WithLogger(gLogger),
		cogs.WithSQLConn(db.GetDBConn()),
		cogs.WithState(
			cogs.NewState(
				"ep_key_rotation",
				"EP key rotation",
				cogs.WithCronJob(key_rotation.EPCronHandler, cogs.CronPeriod{
					Hours:   23,
					Minutes: 59,
					Seconds: 59,
				}),
			),
		),
	)
	gLogger.LogInfo("Starting the cogs wheel machine for EP key rotation")
	m.Start()
	return m
}

func startNGPAAutoMigrationCogsWheel() cogs.Machine {
	if cfg.AvisarService.NGPAAutoMigration.ServiceEnabled == false {
		gLogger.LogInfo("NGPA Auto Migration disabled. Cannot continue!")
		return nil
	}
	gLogger.LogInfo("Creating the new cogs wheel machine for NGPA Auto Migration service")

	m := cogs.NewMachine(
		cogs.WithLogger(gLogger),
		cogs.WithSQLConn(db.GetDBConn()),
		cogs.WithState(
			cogs.NewState(
				"ngpa_auto_migration",
				"NGPA Auto migration",
				cogs.WithCronJob(ngpa_auto_migration.CronHandler, cogs.CronPeriod{
					Hours:   0,
					Minutes: 59,
					Seconds: 59,
				}),
			),
		),
	)
	gLogger.LogInfo("Starting the cogs wheel machine for NGPA Auto Migration")
	m.Start()
	return m
}

func init() {
	localRun = isLocalRunEnabled()
	var err error

	// First parse the supplied flags.
	flag.Parse()

	log.Printf("base path is %s", *basePath)

	if localRun == true {
		setupLogger(cfg)
	} else {
		cfg, err = config.LoadConfig(fmt.Sprintf("%s/cfg.yaml", *basePath))
		if err != nil || cfg == nil {
			log.Fatalf("Failed to read cfg.yaml : %v\n", err)
		}
		log.Printf("cfg file details read: Output: %+v", cfg)
		setupLogger(cfg)
		region.Set(cfg.Region)
		if cfg.AwsPartition != "" {
			partition.Set(cfg.AwsPartition)
		}
		utilsContext := aws_utils.CreateContext()
		setupDBConnection()

		if *runType == definitions.RunTypeVM {
			utilsContext.ReadFromS3ToFile(gLogger, cfg.KeysBucketName,
				fmt.Sprintf("gcp/%s.json.enc", cfg.GCPOrchProjectID),
				fmt.Sprintf("%s/google_application_credentials.json", *basePath),
				true)
			os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", fmt.Sprintf(
				"%s/google_application_credentials.json", *basePath))
		} else {
			os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", fmt.Sprintf(
				"%s/google_application_credentials.json", *basePath))
		}
	}

	if *logMode != "stdout" {
		setupFileLogger(cfg, *logFile)
	} else {
		setupLogger(cfg)
	}
}

func setupDBConnection() {
	for {
		// Initialize the DB - Keep retrying if there is no connectivity
		if err := db.Initialize(cfg, gLogger); err != nil {
			gLogger.LogError("Failed to setup DB connection : %v", err)
			time.Sleep(5 * time.Second)
		} else {
			gLogger.LogInfo("DB Connection setup to RDS")
			break
		}
	}
}

func main() {
	gLogger.LogInfo("Stating the avisar_service...")
	utilsContext := aws_utils.GetContext()
	gLogger.LogInfo("Reading the Event Proxy configuration now.")
	myEventProxyCfg := readEventProxyCfg(gLogger)
	if myEventProxyCfg == nil {
		gLogger.LogError("Failed to read the local configuration or local read is disabled!")
	} else {
		gLogger.LogInfo("Local config: %s", myEventProxyCfg)
	}

	myIoTCertCleanupListenerCfg := readIoTCertCleanupListenerCfg(gLogger)
	if myIoTCertCleanupListenerCfg == nil {
		gLogger.LogError("Fatal! Failed to read the local configuration or local read is disabled")
	} else {
		gLogger.LogInfo("Local config: %+v", myIoTCertCleanupListenerCfg)
	}

	myVerticalScaleEventCfg := readVerticalScaleEventCfg(gLogger)
	if myVerticalScaleEventCfg == nil {
		gLogger.LogError("Fatal! Failed to read the local configuration or local read is disabled")
	} else {
		gLogger.LogInfo("Local config: %s", myVerticalScaleEventCfg)
	}

	alert_manager.Initialize(gLogger)

	go startGRPCService(gLogger, myEventProxyCfg)

	// DR checks. Only run if we're deployed in the Active Region
	curOrchState := aws_utils.OrchStateInit
	prevOrchState := aws_utils.OrchStateInit
	var (
		iotCleanupMachine        cogs.Machine
		vertScaleMachine         cogs.Machine
		keyRotationMachine       cogs.Machine
		dbCleanupMachine         cogs.Machine
		ngpaAutoMigrationMachine cogs.Machine
		epKeyRotationMachine     cogs.Machine
	)
	sigChan := make(chan os.Signal, 2)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)
	for {
		select {
		case sig := <-sigChan:
			switch sig {
			case os.Interrupt:
				// Handle SIGINT
				fallthrough
			case syscall.SIGTERM:
				gLogger.LogInfo("Got %s. Attempting Graceful Termination", sig.String())
				if iotCleanupMachine != nil {
					iotCleanupMachine.Stop()
					iotCleanupMachine.Wait()
					gLogger.LogInfo("Terminated iotCleanupMachine")
				}
				if vertScaleMachine != nil {
					vertScaleMachine.Stop()
					vertScaleMachine.Wait()
					gLogger.LogInfo("Terminated vertScaleMachine")
				}
				if keyRotationMachine != nil {
					keyRotationMachine.Stop()
					keyRotationMachine.Wait()
					gLogger.LogInfo("Terminated keyRotationMachine")
				}
				if dbCleanupMachine != nil {
					dbCleanupMachine.Stop()
					dbCleanupMachine.Wait()
					gLogger.LogInfo("Terminated dbCleanupMachine")
				}
				if ngpaAutoMigrationMachine != nil {
					ngpaAutoMigrationMachine.Stop()
					ngpaAutoMigrationMachine.Wait()
					gLogger.LogInfo("Terminated ngpaAutoMigrationMachine")
				}
				if epKeyRotationMachine != nil {
					epKeyRotationMachine.Stop()
					epKeyRotationMachine.Wait()
					gLogger.LogInfo("Terminated epKeyRotationMachine")
				}
				gLogger.LogInfo("Finished stopping all cogs machines. Exiting now...")
				os.Exit(1)
			default:
				gLogger.LogInfo("Unhandled Signal: %s", sig.String())
			}
		case <-time.After(10 * time.Second):
			curOrchState = utilsContext.GetOrchCurrentState(gLogger)
			if prevOrchState != curOrchState {
				if curOrchState == aws_utils.OrchStateActive {
					gLogger.LogInfo("Orchestrator is Active. Starting service...")
					iotCleanupMachine = startIOTCleanupCogsWheel(myIoTCertCleanupListenerCfg)
					vertScaleMachine = startVertScaleCogsWheel(myVerticalScaleEventCfg)
					keyRotationMachine = startKeyRotationCogsWheel()
					epKeyRotationMachine = startEPKeyRotationCogsWheel()
					dbCleanupMachine = startDBCleanupCogsWheel()
					ngpaAutoMigrationMachine = startNGPAAutoMigrationCogsWheel()
				} else {
					gLogger.LogInfo("Orchestrator has moved to Pasive or Init. Stopping service...")
					if iotCleanupMachine != nil {
						iotCleanupMachine.Stop()
						iotCleanupMachine.Wait()
					}
					if vertScaleMachine != nil {
						vertScaleMachine.Stop()
						vertScaleMachine.Wait()
					}
					if keyRotationMachine != nil {
						keyRotationMachine.Stop()
						keyRotationMachine.Wait()
					}
					if dbCleanupMachine != nil {
						dbCleanupMachine.Stop()
						dbCleanupMachine.Wait()
					}
					if ngpaAutoMigrationMachine != nil {
						ngpaAutoMigrationMachine.Stop()
						ngpaAutoMigrationMachine.Wait()
					}
					if epKeyRotationMachine != nil {
						epKeyRotationMachine.Stop()
						epKeyRotationMachine.Wait()
					}
				}
			}
			prevOrchState = curOrchState
		}
	}
}
