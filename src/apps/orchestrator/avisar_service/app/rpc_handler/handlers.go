package rpc_handler

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"orchestrator/avisar_service/app/common"
	"orchestrator/avisar_service/libs/alert_manager"
	"orchestrator/libs/go/license"
	"orchestrator/utils/cdl"
	"strconv"
	"strings"
	"time"

	"orchestrator/avisar_service/app/event_publisher"
	_ "orchestrator/avisar_service/app/event_publisher"
	"orchestrator/libs/common/shared/grpc/proto/avisarpb"
	"orchestrator/libs/go/dbaccess/sql"

	"cloud.google.com/go/pubsub"
	"go.panw.local/pangolin/clogger"
)

type EventPubSubInfo struct {
	PubSubClient    *pubsub.Client
	PublishingTopic *pubsub.Topic
}
type MarsEvents struct {
	ForwardingEnabled   bool
	MarsEventDetailsMap map[int]*EventPubSubInfo
}

type AllEvents struct {
	ForwardingEnabled bool
	AllEventsDetails  EventPubSubInfo
}

type Server struct {
	MyMarsEvents MarsEvents
	MyAllEvents  AllEvents
	// Logger       *clogger.Clogger
	DbCtx *sql.DbContext
}

var (
	severityLookupTable = map[string]int{
		"emergency": 0,
		"alert":     1,
		"critical":  2,
		"error":     3,
		"warning":   4,
		"notice":    5,
		"info":      6,
		"debug":     7,
	}
)

/*
	This routine creates a Notification message for both Orchestrator Pipeline and Mars Alert pipeline.
	Note:
		In params:
			*clogger.CLogger : Reference to clogger for logging.
			*avisarpb.Event : Reference to avisarpb event.
		Out params:
			NotificationAlertMessageString : Message to be sent to prisma_access_notification_sink topic.
		    AlertMessageString : Message to be sent to prisma_sase_alerts pipeline.
            attributes : Attributes to send as part of pub/sub message.
	        error : Populated in case of an error.
*/

func CreateAlertMessageJson(Logger *clogger.EventLogger, req *avisarpb.Event, alertID string,
	recordCritical func(definition *common.AlertDefinition, str string) error) (*string, *string, map[string]string, error) {
	dt := time.Now()
	utcTimeStr := (dt.Format("2006-01-02 15:04:05.000000 UTC"))
	var sendCriticalAlertMsgToMars = false
	var AlertMessageString string

	MyAlertResourceData := &common.AlertResourceData{
		MetricType:        req.GetMetricType(),
		MetricState:       req.GetMetricState(),
		Severity:          req.GetSeverity(),
		Sender:            req.GetSender(),
		TraceID:           req.GetTraceId(),
		TenantID:          req.GetTenantId(),
		CustID:            req.GetCustId(),
		Timestamp:         &utcTimeStr,
		NodeType:          req.GetNodeType(),
		IsAutoscale:       req.GetIsAutoscale(),
		JobID:             req.GetJobId(),
		IsUpgrade:         req.GetIsUpgrade(),
		CloudProvider:     req.GetCloudProvider(),
		RegionID:          req.GetRegionId(),
		RegionName:        req.GetRegionName(),
		StackName:         req.GetStackName(),
		StackStatus:       req.GetStackStatus(),
		StackStatusReason: req.GetStackStatusReason(),
		CustTopologyID:    req.GetCustTopologyId(),
		GcpProjectID:      req.GetGcpProjectId(),
		StackDebugID:      req.GetStackDebugId(),
		ShardID:           req.GetShardId(),
	}

	myAlertMessage := &common.AlertDefinition{TenantID: req.GetSupTenantId(),
		SubTenantID:      req.GetTenantId(),
		AlertID:          alertID,
		State:            string(common.AlertStateRaised),
		Message:          req.GetMetricState(),
		AlertCodeMessage: req.GetMetricState(),
		Code:             req.GetMetricType(),
		Category:         "PrismaAccessControlPlane",
		SubCategory:      "PRISMA",
		Severity:         req.GetSeverity(),
		SeverityID:       severityLookupTable[strings.ToLower(req.GetSeverity())],
		FirstEventTime:   &utcTimeStr,
		RaisedTime:       &utcTimeStr,
		ClearedTime:      nil,
		UpdatedTime:      &utcTimeStr,
		Domain:           "Internal",
		AwsEnv:           req.GetAwsEnv(),
		TenantName:       req.GetTenantName(),
	}

	if myAlertMessage.SeverityID <= 2 {
		sendCriticalAlertMsgToMars = true
	}

	if sendCriticalAlertMsgToMars {
		AlertResourceDataBytes, err := json.Marshal(MyAlertResourceData)
		if err != nil {
			Logger.LogError("%v", err)
			return nil, nil, nil, err
		}
		AlertResourceDataString := string(AlertResourceDataBytes)
		myAlertMessage.MyAlertResourceData = AlertResourceDataString
		cachedAWSEnv := myAlertMessage.AwsEnv
		myAlertMessage.AwsEnv = ""
		AlertMessageBytes, err := json.Marshal(myAlertMessage)
		if err != nil {
			Logger.LogError("%v", err)
			return nil, nil, nil, err
		}
		myAlertMessage.AwsEnv = cachedAWSEnv

		AlertMessageString = string(AlertMessageBytes)
		Logger.LogInfo("Sending Message %s to PubSub", AlertMessageString)

		if recordCritical != nil {
			// Note recordCritical saves AwsEnv even though it is stripped out in the published msg.
			err = recordCritical(myAlertMessage, AlertMessageString)
			if err != nil {
				Logger.LogError("Failed to record critical alert: %v", err)
				return nil, nil, nil, err
			}
		}
	}

	// MyAlertResourceData (to timeseries_prisma_access_notifications_sink_topic)
	myAlertMessage.MyAlertResourceData = MyAlertResourceData
	reqBodyBytes := new(bytes.Buffer)
	json.NewEncoder(reqBodyBytes).Encode(myAlertMessage)
	NotificationAlertMessageString := string(reqBodyBytes.Bytes())
	Logger.LogInfo("NotificationAlertMessageString is %s", NotificationAlertMessageString)

	attributes := make(map[string]string)
	attributes["code"] = myAlertMessage.Code
	attributes["domain"] = "ORCHESTRATOR"
	attributes["tenant_id"] = myAlertMessage.TenantID
	attributes["sub_tenant_id"] = myAlertMessage.SubTenantID

	return &NotificationAlertMessageString, &AlertMessageString, attributes, nil
}

func logReceivedEvent(logger *clogger.EventLogger, req *avisarpb.Event) {
	logger.LogInfo("Logging Event from the client:\n"+
		"Metric Type: %v\n"+
		"Sender: %v\n"+
		"TraceID : %v\n"+
		"TenantID : %v\n"+
		"CustID: %v\n"+
		"isAutoscale: %v\n"+
		"isUpgrade: %v\n"+
		"Timestamp: %v\n"+
		"Severity: %v\n"+
		"NodeType:  %v\n"+
		"CloudProvider: %v\n"+
		"GetRegionId: %v\n"+
		"GetRegionName: %v\n"+
		"GetStackName: %v\n"+
		"GetStackStatus: %v\n"+
		"GetStackDebugId: %v\n"+
		"GetGcpProjectId: %v\n"+
		"GetMetricState: %v\n"+
		"GetCustTopologyId: %v\n"+
		"JobID: %v\n"+
		"AwsEnv: %v\n"+
		"TenantName: %v\n"+
		"ShardID: %v\n",
		req.GetMetricType(), req.GetSender(), req.GetTraceId(), req.GetTenantId(), req.GetCustId(),
		req.GetIsAutoscale(), req.GetIsUpgrade(), req.GetTimestamp(), req.GetSeverity(), req.GetNodeType(),
		req.GetCloudProvider(), req.GetRegionId(), req.GetRegionName(), req.GetStackName(), req.GetStackStatus(),
		req.GetStackDebugId(), req.GetGcpProjectId(), req.GetMetricState(), req.GetCustTopologyId(), req.GetJobId(),
		req.GetAwsEnv(), req.GetTenantName(), req.GetShardId())
}

/*
The SendEvent routine receives the grpc callback when the client sends us a request.
*/
func (s *Server) SendEvent(ctx context.Context, req *avisarpb.Event) (*avisarpb.EventNotificationResponse, error) {
	Logger := s.DbCtx.Logger
	var NotificationAlertMessageString *string
	var AlertMessageString *string
	success := true
	var hasError bool

	Logger.LogInfo("Received Event from the client.")
	logReceivedEvent(Logger, req)

	alertID, groupID, err := alert_manager.AlertIDFromEvent(req)
	if err != nil {
		Logger.LogError("Failed to generate AlertID from Event: %v", err)
		resp := avisarpb.EventNotificationResponse{
			ResponseCode:    500,
			ResponseMessage: "Failed to Generate AlertID from request payload.",
		}
		return &resp, nil
	}

	record := func(alert *common.AlertDefinition, alertStr string) error {
		return alert_manager.RecordAlert(s.DbCtx, groupID, req.Sender, req.GetModule(), alert, alertStr)
	}
	NotificationAlertMessageString, AlertMessageString, attributes, err := CreateAlertMessageJson(Logger, req, alertID, record)
	if err != nil {
		Logger.LogError("Failed to create Alert Message Json")
		resp := avisarpb.EventNotificationResponse{
			ResponseCode:    500,
			ResponseMessage: "Failed to Create Alert message to send to Pub/Sub.",
		}
		return &resp, nil
	}

	if AlertMessageString != nil && *AlertMessageString != "" {
		Logger.LogInfo("This message needs to be forwarded to MARS alert service.")
		tenantID := attributes["tenant_id"]
		tenantIDInt, err := strconv.ParseInt(tenantID, 0, 64)
		if err != nil {
			Logger.LogError("Failed to get the tenantID, err: %v", err)
			hasError = true
		}
		if !hasError {
			regionIdx, err := license.GetCDLRegionByTenantID(s.DbCtx, tenantIDInt)
			if err != nil {
				Logger.LogError("Unable to get the CDL region for the tenant %d, err: %v", tenantIDInt, err)
				success = false
			} else {
				Logger.LogInfo("Sending Alert Message %v to GCP Pub/Sub with Attributes %v to region %s",
					*AlertMessageString, attributes,
					cdl.AppAssociationRegionIntLookupTable[regionIdx])

				errCode := event_publisher.PublishEventToPipeline(Logger,
					s.MyMarsEvents.MarsEventDetailsMap[regionIdx].PublishingTopic,
					*AlertMessageString, attributes)
				if errCode < 0 {
					success = false
				}
			}
		}
	}

	Logger.LogInfo("Sending Notification Message %v to GCP Pub/Sub with Attributes %v",
		*NotificationAlertMessageString, attributes)
	errCode := event_publisher.PublishEventToAllEventsPipeline(Logger,
		s.MyAllEvents.AllEventsDetails.PublishingTopic,
		*NotificationAlertMessageString,
		attributes)
	if errCode < 0 {
		success = false
	}

	var resp avisarpb.EventNotificationResponse
	if success == false {
		resp = avisarpb.EventNotificationResponse{
			ResponseCode:    500,
			ResponseMessage: "Failed to send Alert message to Pub/Sub.",
		}
	} else {
		resp = avisarpb.EventNotificationResponse{
			ResponseCode:    200,
			ResponseMessage: "Success",
		}
	}

	// Clear alerts
	if success {
		clearedAlerts := alert_manager.ClearAlerts(s.DbCtx, req.GetMetricType(), groupID, &alert_manager.ClearConditionParam{
			Action: req.Action,
		}, req.Module, req.Sender, Logger)

		for _, clearedAlert := range clearedAlerts {
			// Create attributes for the event publisher
			clearedAttributes := make(map[string]string)
			clearedAttributes["code"] = clearedAlert.Code
			clearedAttributes["domain"] = "ORCHESTRATOR"
			clearedAttributes["tenant_id"] = clearedAlert.TenantID
			clearedAttributes["sub_tenant_id"] = clearedAlert.SubTenantID

			// Convert the alert to JSON
			clearedAlertBytes, err := json.Marshal(clearedAlert)
			if err != nil {
				Logger.LogError("Failed to marshal cleared alert: %v", err)
				continue
			}

			clearedAlertString := string(clearedAlertBytes)

			// Send to MARS events publisher if enabled
			if s.MyMarsEvents.ForwardingEnabled {
				tenantIDInt, err := strconv.ParseInt(clearedAlert.TenantID, 0, 64)
				if err != nil {
					Logger.LogError("Failed to parse tenantID for cleared alert: %v", err)
				} else {
					regionIdx, err := license.GetCDLRegionByTenantID(s.DbCtx, tenantIDInt)
					if err != nil {
						Logger.LogError("Unable to get the CDL region for the tenant %d, err: %v", tenantIDInt, err)
					} else if publisher, exists := s.MyMarsEvents.MarsEventDetailsMap[regionIdx]; exists && publisher != nil {
						Logger.LogInfo("Sending cleared alert message to MARS alert service for region %s",
							cdl.AppAssociationRegionIntLookupTable[regionIdx])

						errCode := event_publisher.PublishEventToPipeline(Logger,
							publisher.PublishingTopic,
							clearedAlertString, clearedAttributes)
						if errCode < 0 {
							Logger.LogError("Failed to publish cleared alert to MARS events")
						}
					}
				}
			}

			// Send to All Events publisher if enabled
			if s.MyAllEvents.ForwardingEnabled {
				Logger.LogInfo("Sending cleared alert message to All Events pipeline")
				errCode := event_publisher.PublishEventToAllEventsPipeline(Logger,
					s.MyAllEvents.AllEventsDetails.PublishingTopic,
					clearedAlertString, clearedAttributes)
				if errCode < 0 {
					Logger.LogError("Failed to publish cleared alert to All Events pipeline")
				}
			}
		}
	}

	return &resp, nil
}

func (*Server) IncrementCounter(ctx context.Context, req *avisarpb.Counter) (*avisarpb.CounterResponse, error) {
	fmt.Printf("CounterValue %v", req.Counter)

	resp := avisarpb.CounterResponse{
		ResponseCode:    200,
		ResponseMessage: "Success",
	}
	return &resp, nil
}
