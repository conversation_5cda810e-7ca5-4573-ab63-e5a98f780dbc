package scale_event

import (
	"bytes"
	"encoding/json"
	"orchestrator/avisar_service/app/common"
	"orchestrator/avisar_service/app/event_publisher"
	"orchestrator/libs/go/dbaccess/sql"
	"orchestrator/libs/go/license"
	"orchestrator/utils/cdl"
	"strconv"

	"go.panw.local/pangolin/clogger"

	"orchestrator/avisar_service/app/rpc_handler"

	"orchestrator/libs/go/cogs"
	"orchestrator/libs/go/utils"
)

type EventsCtxt struct {
	MyMarsEvents rpc_handler.MarsEvents
	MyAllEvents  rpc_handler.AllEvents
}

var (
	VertScaleEventCtxt EventsCtxt
	DbCtx              *sql.DbContext
)

func convertToAIOpsAlertType(mt string) string {
	ret := mt
	switch mt {
	case "SCHEDULED_SCALE_UP":
		fallthrough
	case "SCALE_UP":
		ret = "AL_AUTOSCALE_SCALE_UP_FAILURE"
	case "SCALE_DOWN":
		ret = "AL_AUTOSCALE_SCALE_DOWN_FAILURE"
	}
	return ret
}

func createMessage(eLog *clogger.EventLogger,
	alertMsg common.AlertDefinition) (*string, *string, map[string]string, error) {
	var (
		sendCriticalAlertMsgToMars bool
		AlertMessageString         string
	)

	if alertMsg.SeverityID <= 3 {
		sendCriticalAlertMsgToMars = true
	}

	reqBodyBytes := new(bytes.Buffer)
	json.NewEncoder(reqBodyBytes).Encode(alertMsg)
	NotificationAlertMessageString := string(reqBodyBytes.Bytes())
	eLog.LogInfo("Vertical Scale Event: NotificationAlertMessageString is %s", NotificationAlertMessageString)

	/* Form message for MARS events */
	/* Only send Error, Critical, Alert and Emergency messages to MARS for AIOps consumption */
	if sendCriticalAlertMsgToMars {
		// Convert to AIOps alert code - only sent to AIOPs. Our notification code remains unchanged
		// Also convert the JSON struct to a string since AIOps does not denormalize about our resource_data
		alertMsg.Code = convertToAIOpsAlertType(alertMsg.Code)
		resourceData, _ := json.Marshal(alertMsg.MyAlertResourceData)
		alertMsg.MyAlertResourceData = string(resourceData)
		AlertMessageBytes, err := json.Marshal(alertMsg)
		if err != nil {
			eLog.LogError("%v", err)
			return nil, nil, nil, err
		}

		AlertMessageString = string(AlertMessageBytes)
		eLog.LogInfo("Message created for MARS %s to be sent on PubSub", AlertMessageString)
	}

	attributes := make(map[string]string)
	attributes["code"] = alertMsg.Code
	attributes["domain"] = "ORCHESTRATOR"
	attributes["tenant_id"] = alertMsg.TenantID
	attributes["sub_tenant_id"] = alertMsg.SubTenantID

	return &NotificationAlertMessageString, &AlertMessageString, attributes, nil
}

func Handler(ctx *cogs.MessageContext) cogs.PublishMessage {
	/* TODO check if needed logger event */
	eLog := utils.GetEventLogger(ctx.Logger)
	eLog.NewLoggerEvent()

	var (
		alertMsg                       common.AlertDefinition
		NotificationAlertMessageString *string
		AlertMessageString             *string
		hasError                       bool
		emptyResp                      cogs.PublishMessage
	)
	success := true
	DbCtx = &sql.DbContext{
		DbConn: ctx.DBConn,
		Logger: eLog,
	}

	eLog.LogDebug("Scale Event handler received raw message : %s", string(ctx.Payload))
	if err := json.Unmarshal(ctx.Payload, &alertMsg); err != nil {
		eLog.LogError("Failed to unmarshal: %v", err)
		return emptyResp
	}
	eLog.LogDebug("Unmarshaled to : %+v", alertMsg)
	NotificationAlertMessageString, AlertMessageString, attributes, err := createMessage(eLog, alertMsg)
	if err != nil {
		eLog.LogError("Failed to create Alert Message Json")
		return emptyResp
	}

	if AlertMessageString != nil && *AlertMessageString != "" {
		eLog.LogInfo("There is a message to be forwarded to MARS alert service.")
		tenantID := attributes["tenant_id"]
		tenantIDInt, err := strconv.ParseInt(tenantID, 0, 64)
		if err != nil {
			eLog.LogError("Failed to get the tenantID to forward message to MARS, err: %v", err)
			hasError = true
		}
		if !hasError {
			var regionIdx int
			var err error
			if tenantIDInt == 0 {
				regionIdx = cdl.CDL_REGION_AMERICAS_IDX
			} else {
				regionIdx, err = license.GetCDLRegionByTenantID(DbCtx, tenantIDInt)
				if err != nil {
					eLog.LogError("Unable to get the CDL region for the tenant %d, err: %v", tenantIDInt, err)
					success = false
				}
			}
			if err == nil {
				eLog.LogInfo("Sending Alert Message %v to GCP Pub/Sub with Attributes %v to region %s",
					*AlertMessageString, attributes,
					cdl.AppAssociationRegionIntLookupTable[regionIdx])

				errCode := event_publisher.PublishEventToPipeline(eLog,
					VertScaleEventCtxt.MyMarsEvents.MarsEventDetailsMap[regionIdx].PublishingTopic,
					*AlertMessageString, attributes)
				if errCode < 0 {
					success = false
				}
			}
		}
	}

	/* send all notifications */
	eLog.LogInfo("Sending Notification Message %v to GCP Pub/Sub for All Events with Attributes %v",
		*NotificationAlertMessageString, attributes)
	errCode := event_publisher.PublishEventToAllEventsPipeline(eLog,
		VertScaleEventCtxt.MyAllEvents.AllEventsDetails.PublishingTopic,
		*NotificationAlertMessageString,
		attributes)
	if errCode < 0 {
		success = false
	}

	if !success {
		return emptyResp
	}
	return emptyResp
}
