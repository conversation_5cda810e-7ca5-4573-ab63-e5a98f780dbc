from libs.msg_defs.interface import *
from libs.msg_defs.handler import *
from libs.comm.sqsapi import SqsApi
from libs.cloud_providers.aws.instance_manager.aws_bringup import Bringup<PERSON><PERSON>Handler
from libs.cloud_providers.gcp.instance_manager.gcp_bringup import Bringup<PERSON><PERSON><PERSON>andler
from libs.cloud_providers.azr.instance_manager.azr_bringup import BringupAZRHandler
from libs.cloud_providers.common.upgrade_envoy_version_gcp import process_completed_upgrade_envoy_stack
from libs.cloud_providers.common.upgrade_envoy_version_gcp import process_update_eproxy_data_for_cust_epaas_records
from libs.cloud_providers.common.upgrade_ep_envoy_outside_panos import process_inprogress_upgrade_ep_outside_panos_stack
from libs.cloud_providers.common.upgrade_ep_envoy_outside_panos import process_completed_upgrade_ep_outside_panos_stack
from libs.cloud_providers.common.pinnedInstanceUpgradeBringup import pinned_instance_upgarde_bringup_processing
from libs.cloud_providers.aws.instance_manager.update_eproxy_managed_vms import process_update_eproxy_data_for_cust_epaas_records_aws
from libs.cfg import *
from libs.common.shared.sys_utils import DEPLOYMENT_ENV_FEDRAMP_IL5, DEPLOYMENT_ENV_FEDRAMP_HIGH
from libs.common.logger import logger, CtxLogAdapter
from libs.logging_infra.orch_context import orch_store_in_trace_db
from libs.logging_infra.orch_context import OrchestratorContext
from libs.common.shared.utils import is_china_env
import time
import traceback
import sys
EPROXY_TIME_INTERVAL = 120

class BringupHandler(GenericHandler):
    status = "BRINGUP"

    def init_aws_bringup_logger(self):
        myname = "bringup_aws"
        try:
            if self.logger:
                self.logger.info("Creating the handler logging file.")
        except Exception as E:
            pass

        rastro_ctx = OrchestratorContext(name=myname,
                                         log_root_dir=cfg['log-root-dir'])
        self.aws_logger_rastro_ctx = rastro_ctx
        self.aws_logger = rastro_ctx.logger
        self.aws_logger.setLevel(logging.INFO)
        return True

    def init_upgrade_ep_bringup_logger(self):
        myname = "bringup_upgrade_ep"
        try:
            if self.logger:
                self.logger.info("Creating the handler logging file.")
        except Exception as E:
            pass

        rastro_ctx = OrchestratorContext(name=myname,
                                         log_root_dir=cfg['log-root-dir'])
        self.upgrade_ep_logger_rastro_ctx = rastro_ctx
        self.upgrade_ep_logger = rastro_ctx.logger
        self.upgrade_ep_logger.setLevel(logging.INFO)
        return True

    def init_eproxy_data_update_bringup_logger(self):
        myname = "eproxy_data_update_logger"
        try:
            if self.logger:
                self.logger.info("Creating the handler logging file.")
        except Exception as E:
            pass

        rastro_ctx = OrchestratorContext(name=myname,
                                         log_root_dir=cfg['log-root-dir'])
        self.eproxy_data_update_rastro_ctx = rastro_ctx
        self.eproxy_data_update_logger = rastro_ctx.logger
        self.eproxy_data_update_logger.setLevel(logging.INFO)
        return True

    def init_gcp_bringup_logger(self):
        myname = "bringup_gcp"
        try:
            if self.logger:
                self.logger.info("Creating the handler logging file.")
        except Exception as E:
            pass

        rastro_ctx = OrchestratorContext(name=myname,
                log_root_dir=cfg['log-root-dir'])
        self.gcp_logger_rastro_ctx = rastro_ctx
        self.gcp_logger = rastro_ctx.logger
        self.gcp_logger.setLevel(logging.INFO)
        return True

    def init_azr_bringup_logger(self):
        myname = "bringup_azr"
        try:
            if self.logger:
                self.logger.info("Creating the handler logging file.")
        except Exception as E:
            pass

        rastro_ctx = OrchestratorContext(name=myname,
                log_root_dir=cfg['log-root-dir'])
        self.azr_logger_rastro_ctx = rastro_ctx
        self.azr_logger = rastro_ctx.logger
        self.azr_logger.setLevel(logging.DEBUG)
        return True

    def init_osp_bringup_logger(self):
        myname = "bringup_openstack"
        try:
            if self.logger:
                self.logger.info("Creating the handler logging file.")
        except Exception as E:
            pass

        rastro_ctx = OrchestratorContext(name=myname,
                log_root_dir=cfg['log-root-dir'])
        self.osp_logger_rastro_ctx = rastro_ctx
        self.osp_logger = rastro_ctx.logger
        self.osp_logger.setLevel(logging.DEBUG)
        return True

    def init_instance_upgrade_logger(self):
        myname = "bringup_instance_upgrade"
        try:
            if self.logger:
                self.logger.info("Creating the handler logging file.")
        except Exception as E:
            pass

        rastro_ctx = OrchestratorContext(name=myname,
                log_root_dir=cfg['log-root-dir'])
        self.instance_upgrade_logger_rastro_ctx = rastro_ctx
        self.instance_upgrade_logger = rastro_ctx.logger
        self.instance_upgrade_logger.setLevel(logging.INFO)
        return True

    def init_cust_epaas_logger(self):
        myname = "bringup_instance_cust_epaas"
        try:
            if self.logger:
                self.logger.info("Creating the handler logging file.")
        except Exception as E:
            pass

        rastro_ctx = OrchestratorContext(name=myname,
                log_root_dir=cfg['log-root-dir'])
        self.cust_epaas_logger_rastro_ctx = rastro_ctx
        self.cust_epaas_logger = rastro_ctx.logger
        self.cust_epaas_logger.setLevel(logging.INFO)
        return True

    def set_logger_as_aws_bringup_logger(self):
        self.aws_logger.debug("Setting the logger %s to aws_logger %s " % (str(self.logger), str(self.aws_logger)))
        self.logger = self.aws_logger
        self.db_h.logger = self.aws_logger

    def set_logger_as_upgrade_ep_bringup_logger(self):
        self.upgrade_ep_logger.debug("Setting the logger %s to upgrade_ep_logger %s " % (str(self.logger), str(self.upgrade_ep_logger)))
        self.logger = self.upgrade_ep_logger
        self.db_h.logger = self.upgrade_ep_logger

    def set_logger_as_eproxy_data_update_logger(self):
        self.eproxy_data_update_logger.debug("Setting the logger %s to eproxy_data_update_logger %s " % (str(self.logger), str(self.eproxy_data_update_logger)))
        self.logger = self.eproxy_data_update_logger
        self.db_h.logger = self.eproxy_data_update_logger

    def set_logger_as_cust_epaas_envoy_logger(self):
        self.cust_epaas_logger.debug("Setting the logger %s to cust_epaas_logger %s " % (str(self.logger),
                                                                                         str(self.cust_epaas_logger)))
        self.logger = self.cust_epaas_logger
        self.db_h.logger = self.cust_epaas_logger

    def set_logger_as_gcp_bringup_logger(self):
        self.gcp_logger.debug("Setting the logger to gcp_logger")
        self.logger = self.gcp_logger
        self.db_h.logger = self.gcp_logger

    def set_logger_as_azr_bringup_logger(self):
        self.azr_logger.info("Setting the logger to azr_logger")
        self.logger = self.azr_logger
        self.db_h.logger = self.azr_logger

    def set_logger_as_osp_bringup_logger(self):
        self.osp_logger.info("Setting the logger to osp_logger")
        self.logger = self.osp_logger
        self.db_h.logger = self.osp_logger

    def set_logger_as_instance_upgrade_logger(self):
        self.instance_upgrade_logger.debug("Setting the logger to upgrade_logger")
        self.logger = self.instance_upgrade_logger
        self.db_h.logger = self.instance_upgrade_logger

    def set_default_logger(self):
        self.logger = self.default_logger
        self.db_h.logger = self.default_logger

    def update_trace_db(self):
        ret = False
        try:
            self.init_db()
            # Set the DB handler in the respective rastro context:-
            self.default_logger_rastro_ctx.set_db_handler(self.db_h)
            # Store info about this logger in the trace DH.
            lret = orch_store_in_trace_db(self.db_h,
                    self.default_logger_rastro_ctx.get_trace_id(),
                    tenant_id=999999999,
                    workflow_type="Orch Generic Bringup",
                    workflow_id=int(time.time()))
            if lret == None:
                raise Exception("Failed to store info reg workflow type Orch Generic Bringup")

            self.gcp_logger_rastro_ctx.set_db_handler(self.db_h)
            lret = orch_store_in_trace_db(self.db_h,
                    self.gcp_logger_rastro_ctx.get_trace_id(),
                    tenant_id=999999999,
                    workflow_type="Orch GCP Bringup",
                    workflow_id=int(time.time()))
            if lret == None:
                raise Exception("Failed to store info reg workflow type Orch GCP Bringup")

            self.aws_logger_rastro_ctx.set_db_handler(self.db_h)
            lret = orch_store_in_trace_db(self.db_h,
                    self.aws_logger_rastro_ctx.get_trace_id(),
                    tenant_id=999999999,
                    workflow_type="Orch AWS Bringup",
                    workflow_id=int(time.time()))
            if lret == None:
                raise Exception("Failed to store info reg Orch AWS Bringup")

            self.upgrade_ep_logger_rastro_ctx.set_db_handler(self.db_h)
            lret = orch_store_in_trace_db(self.db_h,
                                          self.upgrade_ep_logger_rastro_ctx.get_trace_id(),
                                          tenant_id=999999999,
                                          workflow_type="Orch upgrade ep Bringup",
                                          workflow_id=int(time.time()))
            if lret == None:
                raise Exception("Failed to store info reg Orch Upgrade ep Bringup")

            self.eproxy_data_update_rastro_ctx.set_db_handler(self.db_h)
            lret = orch_store_in_trace_db(self.db_h,
                                          self.eproxy_data_update_rastro_ctx.get_trace_id(),
                                          tenant_id=999999999,
                                          workflow_type="Orch update eproxy data ",
                                          workflow_id=int(time.time()))
            if lret == None:
                raise Exception("Failed to store info reg Orch update eproxy data")

            self.azr_logger_rastro_ctx.set_db_handler(self.db_h)
            lret = orch_store_in_trace_db(self.db_h,
                    self.azr_logger_rastro_ctx.get_trace_id(),
                    tenant_id=999999999,
                    workflow_type="Orch AZR Bringup",
                    workflow_id=int(time.time()))
            if lret == None:
                raise Exception("Failed to store info reg workflow type Orch AZR Bringup")

            self.instance_upgrade_logger_rastro_ctx.set_db_handler(self.db_h)
            lret = orch_store_in_trace_db(self.db_h,
                    self.instance_upgrade_logger_rastro_ctx.get_trace_id(),
                    tenant_id=999999999,
                    workflow_type="Orch Instance Upgrade Bringup",
                    workflow_id=int(time.time()))
            if lret == None:                       
                raise Exception("Failed to store info reg Orch Instance Upgrade Bringup")

            self.cust_epaas_logger_rastro_ctx.set_db_handler(self.db_h)
            lret = orch_store_in_trace_db(self.db_h,
                                          self.cust_epaas_logger_rastro_ctx.get_trace_id(),
                                          tenant_id=999999999,
                                          workflow_type="Cust epaas check",
                                          workflow_id=int(time.time()))
            if lret == None:
                raise Exception("Failed to store info reg Cust epaas check")

            ret = True
        except Exception as E:
            self.default_logger.error("Failed to update trace id in the master database. %s %s" % (str(E.args), str(traceback.format_exc())))
        finally:
            return ret


    def __init__(self, cfg, shard_cfg):
        self.db_h = None 
        self.qname = 'bringup'
        # The below function will populate self.logger
        super(BringupHandler, self).init_generic_logger_rastro(qname=self.qname.lower())
        # Init the failure queue.
        super(BringupHandler, self).init_failure_queue_rastro('bringup_failq')

        # All the logger variables below
        self.default_logger = self.logger
        self.default_logger.setLevel(logging.INFO)
        self.default_logger_rastro_ctx = self.rastro_ctx
        self.gcp_logger_rastro_ctx=None
        self.gcp_logger=None
        self.aws_logger_rastro_ctx=None
        self.aws_logger=None
        self.upgrade_ep_logger_rastro_ctx = None
        self.upgrade_ep_logger = None
        self.eproxy_data_update_rastro_ctx = None
        self.eproxy_data_update_logger = None
        self.cust_epaas_logger_rastro_ctx = None
        self.cust_epaas_logger = None
        self.azr_logger_rastro_ctx=None
        self.azr_logger=None
        self.osp_logger_rastro_ctx=None
        self.osp_logger=None

        self.instance_upgrade_logger_rastro_ctx=None
        self.instance_upgrade_logger = None

        self.sqs = SqsApi(cfg['region'], cfg['queues'], self.logger)
        self.valid_msgs = (CloudFormationMsg,)
        self.ids = {}
        self.path = ''
        self.poll_hook_path = ''
        self.running = True
        self.timeout = 1800
        self.error = ""

        # Init the loggers.
        self.init_aws_bringup_logger()
        self.init_gcp_bringup_logger()
        self.init_azr_bringup_logger()
        self.init_osp_bringup_logger()
        self.init_instance_upgrade_logger()
        self.init_cust_epaas_logger()
        self.init_upgrade_ep_bringup_logger()
        self.init_eproxy_data_update_bringup_logger()

        # Delete it.
        self.aws_logger.info("AWS logger")
        self.gcp_logger.info("GCP logger")
        self.azr_logger.info("AZR logger")
        # self.osp_logger.info("OPENSTACK logger")
        self.instance_upgrade_logger.info("Instance Upgarde logger")
        self.cust_epaas_logger.info("Cust_epaas_logger")
        self.upgrade_ep_logger.info("Upgrade_ep_logger")
        self.eproxy_data_update_logger.info("Eproxy data update logger")

        # Initialize the DB.
        self.init_db()

        # Update the Trace DB.
        if self.update_trace_db() == False:
            raise Exception("Failed to update the rastro trace DB. Cannot continue")

        self.avctx = AvisarContext(logger=self.logger,
                                   service_endpoint=cfg.get('avisar_service_endpoint'),
                                   forwarding_enabled=cfg.get('avisar_forwarding_enabled', False),
                                   sender="InstanceMgmtService",
                                   aws_env=cfg.get("aws_env"))
        # Shard Information
        # Each pod only polls on tenant/regions within its shard
        self.shard_id = shard_cfg["shard_id"]
        self.replica_size = shard_cfg["replica_size"]


    def handle(self):
        sleep_time = 30
        process_aws_stack_ts = 0
        process_completed_aws_stack_ts = 0
        process_gcp_stack_ts = 0
        process_completed_gcp_stack_ts = 0
        process_azr_stack_ts = 0
        process_osp_stack_ts = 0

        process_completed_azr_stack_ts = 0
        process_completed_osp_stack_ts = 0
        process_cust_epaas_stack_ts = 0
        process_completed_cust_epaas_stack_ts = 0
        process_upgrade_ep_stack_ts = 0
        process_completed_upgrade_epstack_ts = 0
        process_eproxy_update_data_ts = 0
        process_completed_eproxy_update_data_ts = 0
        process_eproxy_update_data_ts_aws = 0
        process_completed_eproxy_update_data_ts_aws = 0

        while self.running:
            try:
                # For AWS
                aws_start_time = time.time()
                self.default_logger.debug("Processing instance bringup for AWS!")
                self.set_logger_as_aws_bringup_logger()
                self.avctx.logger = self.logger
                buaws = BringupAWSHandler(self.db_h, self.avctx, self.shard_id, self.replica_size)
                buaws.process_aws_stack()
                process_aws_stack_ts = int(time.time())
                if process_aws_stack_ts - process_completed_aws_stack_ts >= 60:
                    buaws.process_completed_aws_stack()
                    process_completed_aws_stack_ts = int(time.time())
                self.default_logger.info(f"AWS Bringup Processing took "
                        f"{time.time() - aws_start_time} seconds")

                if cfg.get('aws_env_type') != DEPLOYMENT_ENV_FEDRAMP_IL5 and not is_china_env():
                    # For GCP
                    gcp_start_time = time.time()
                    self.default_logger.debug("Processing instance bringup for GCP!")
                    self.set_logger_as_gcp_bringup_logger()
                    self.avctx.logger = self.logger
                    bugcp = BringupGCPHandler(self.db_h, self.avctx, self.shard_id, self.replica_size)
                    bugcp.process_gcp_stack()
                    process_gcp_stack_ts = int(time.time())
                    if process_gcp_stack_ts - process_completed_gcp_stack_ts >= 60:
                        bugcp.process_completed_gcp_stack()
                        process_completed_gcp_stack_ts = int(time.time())
                    self.default_logger.info(f"GCP Bringup Processing took "\
                            f"{time.time() - gcp_start_time} seconds")
                    # For AZR
                    azr_start_time = time.time()
                    self.default_logger.info("Processing instance bringup for AZR!")
                    self.set_logger_as_azr_bringup_logger()
                    self.avctx.logger = self.logger
                    buazr = BringupAZRHandler(self.db_h, self.avctx, self.shard_id, self.replica_size)
                    process_azr_stack_ts = int(time.time())
                    if process_azr_stack_ts - process_completed_azr_stack_ts >= 60:
                        buazr.process_completed_azr_workspace()
                        process_completed_azr_stack_ts = int(time.time())
                    self.default_logger.info(f"AZR Bringup Processing took "
                            f"{time.time() - azr_start_time} seconds")

                    # For OPENSTACK
                    osp_start_time = time.time()
                    self.default_logger.info("Processing instance bringup for OPENSTACK!")
                    self.set_logger_as_osp_bringup_logger()
                    self.avctx.logger = self.logger
                    # buosp = BringupOSPHandler(self.db_h, self.avctx, self.shard_id, self.replica_size)
                    # self.default_logger.info("TODO Processing instance bringup for OPENSTACK!")
                    process_osp_stack_ts = int(time.time())
                    if process_osp_stack_ts - process_completed_osp_stack_ts >= 60:
                        # buosp.process_completed_osp_workspace()
                        # self.default_logger.info("TODO Processing instance bringup for OPENSTACK!")
                        process_completed_osp_stack_ts = int(time.time())
                    self.default_logger.info(f"OSP Bringup Processing took "
                            f"{time.time() - azr_start_time} seconds")

                upgrade_start_time = time.time()
                self.default_logger.debug("Processing instance upgrade!")
                # Process Pinned Instance Upgrade Stages 3-5
                self.default_logger.info("Processing instance bringup for Upgrade!")
                self.set_logger_as_instance_upgrade_logger()
                bup_pinned_inst_upgrade = pinned_instance_upgarde_bringup_processing(dbh=self.db_h,
                        shard_id=self.shard_id,
                        replica_size=self.replica_size)
                bup_pinned_inst_upgrade.process_instances_bringup()
                bup_pinned_inst_upgrade.process_instance_transition()
                bup_pinned_inst_upgrade.process_old_instance_delete()
                self.default_logger.debug("Processing instance upgrade success check and template deletion!")
                self.default_logger.info(f"Bringup Processing for PinnedInstUpgrade took " \
                        f"{time.time() - upgrade_start_time} seconds")
                # Process upgrade for EPaaS
                if cfg.get('aws_env_type') not in (DEPLOYMENT_ENV_FEDRAMP_IL5, DEPLOYMENT_ENV_FEDRAMP_HIGH):
                    epaas_start_time = time.time()
                    #TODO tenant id is currently missing, and aws swg is per tenant ff 
                    #so by default cannot track swg timestamp bringup
                    if is_china_env():
                        self.set_logger_as_eproxy_data_update_logger()
                        process_eproxy_update_data_ts_aws = int(time.time())
                        if process_eproxy_update_data_ts_aws - process_completed_eproxy_update_data_ts_aws >= EPROXY_TIME_INTERVAL:
                            process_update_eproxy_data_for_cust_epaas_records_aws(self.db_h, self.shard_id, self.replica_size)
                            process_completed_eproxy_update_data_ts_aws = int(time.time())
                    else:
                        self.set_logger_as_cust_epaas_envoy_logger()
                        process_cust_epaas_stack_ts = int(time.time())
                        if process_cust_epaas_stack_ts - process_completed_cust_epaas_stack_ts >= 240:
                            process_completed_upgrade_envoy_stack(self.db_h, self.shard_id, self.replica_size)
                            process_completed_cust_epaas_stack_ts = int(time.time())
                        self.set_logger_as_eproxy_data_update_logger()
                        # process_eproxy_update_data_ts = int(time.time())
                        # if process_eproxy_update_data_ts - process_completed_eproxy_update_data_ts >= EPROXY_TIME_INTERVAL:
                        #     process_update_eproxy_data_for_cust_epaas_records(self.db_h, self.shard_id, self.replica_size)
                        #     process_completed_eproxy_update_data_ts = int(time.time())
                        self.set_logger_as_upgrade_ep_bringup_logger()
                        process_upgrade_ep_stack_ts = int(time.time())
                        #Changing wait time from 10 min to 1 min to migrated upgraded entries
                        if process_upgrade_ep_stack_ts - process_completed_upgrade_epstack_ts >= 60:
                            process_inprogress_upgrade_ep_outside_panos_stack(self.db_h, self.shard_id, self.replica_size)
                            process_completed_upgrade_ep_outside_panos_stack(self.db_h, self.shard_id, self.replica_size)
                            process_completed_upgrade_epstack_ts = int(time.time())

                    self.default_logger.info(f"Bringup Processing for EPaaS took " \
                            f"{time.time() - epaas_start_time} seconds")


                # Sleep for a while.
            except Exception as ex:
                if self.default_logger:
                    self.default_logger.error("handle():Possible Bug in handle function %s"
                            % str(ex))
                    self.default_logger.error(traceback.format_exc())
            finally:
                time.sleep(sleep_time)
'''
def main():
    from libs.msg_defs.handler import GenericHandler
    print(cfg)
    buph = BringupHandler(cfg)
    print(buph)
main()
'''
