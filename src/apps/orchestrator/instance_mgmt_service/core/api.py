import contextlib
import logging
import logging.handlers
from fastapi import FastAPI, Request, Response
import time
import uvicorn
from threading import Thread, Condition
from libs.msg_defs.handler import *

from .api_defs.service_status import get_service_status
from .api_defs.post_process import post_process, PostProcessModel, DeploymentPostProcessModel
from .api_defs.post_process import deployment_post_process

app = FastAPI()

api_handler_dbh = None

class Server(uvicorn.Server):
    def install_signal_handlers(self):
        '''
        Handled by the main inst-mgmt thread
        '''
        pass

    @contextlib.contextmanager
    def run_in_thread(self):
        thread = Thread(target=self.run)
        thread.start()
        try:
            while not self.started:
                time.sleep(1e-3)
            yield
        finally:
            self.should_exit = True
            thread.join()

class APIHandler(GenericHandler):
    '''
    APIHand<PERSON> is the handler wrapping the Fast API based server implementation
    on the inst-mgmt service
    '''
    def __init__(self, cfg:dict, shard_cfg:dict, dbh):
        global api_handler_dbh
        api_handler_dbh = dbh
        self.db_h = dbh
        super(APIH<PERSON><PERSON>, self).init_generic_logger_rastro(qname="fastapi")
        self.logger.setLevel(logging.INFO)
        self.default_logger = self.logger
        self.default_logger_rastro_ctx = self.rastro_ctx

        self.running = True
        self.avctx = AvisarContext(logger=self.logger,
                service_endpoint=cfg.get('avisar_service_endpoint'),
                forwarding_enabled=cfg.get('avisar_forwarding_enabled', False),
                sender="InstanceMgmtService_API",
                aws_env=cfg.get("aws_env"))

    def start_fastapi_server(self, cond:Condition):
        '''
        Start the FastAPI Server using uvicorn
        '''
        cond.acquire()
        config = uvicorn.Config("instance_mgmt_service.core.api:app",
                host='0.0.0.0',
                port=8080,
                reload=True,
                debug=True,
                workers=3)
        server = Server(config=config)
        with server.run_in_thread():
            cond.wait()
            cond.release()
        self.logger.info("Exited the Fast API Server")

    def handle(self, cond:Condition):
        '''
        Thread Handler forked from the main inst-mgmt thread.
        We start the Fast API Server from here, and wait for notification on the 
        Condition to close the Fast API Server
        '''
        fast_api_t = Thread(target=self.start_fastapi_server, args=(cond,))
        fast_api_t.start()
        while self.running:
            # The server is asynchronous. Just waiting here till the main thread
            # tells us to cleanup and exit
            time.sleep(5)
        # Notify the primary fast api thread to clean up
        cond.acquire()
        cond.notify_all()
        cond.release()
        fast_api_t.join()
        self.logger.info("Cleaned up the Fast API Server")

#################################################################
#                         API Routing                           #
#################################################################
@app.get("/api/instance_management/status")
async def fast_api_get_status(request: Request, response: Response):
    return get_service_status(api_handler_dbh, request, response)

@app.post("/api/instance_management/post_process/{inst_mgmt_id}")
async def fast_api_post_process(request: Request, inst_mgmt_id:int, body: PostProcessModel, response: Response):
    return post_process(api_handler_dbh, request, inst_mgmt_id, body, response)

@app.put("/api/instance_management/deployment_post_process/{inst_mgmt_id}")
async def fast_api_deployment_post_process(request: Request, inst_mgmt_id:int, body: DeploymentPostProcessModel, response: Response):
    return deployment_post_process(api_handler_dbh, request, inst_mgmt_id, body, response)