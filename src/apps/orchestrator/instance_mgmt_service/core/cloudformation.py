import logging
import logging.handlers
from libs.msg_defs.interface import *
from libs.msg_defs.handler import *
from libs.cfg import *
from libs.common.shared.sys_utils import DEPLOYMENT_ENV_FEDRAMP_IL5
from libs.cloud_providers.aws.instance_manager.aws_cloudformation import <PERSON><PERSON><PERSON><PERSON>lerAWS
from libs.cloud_providers.gcp.instance_manager.gcp_deploymentmgr import DeploymentMgrHandlerGCP
from libs.logging_infra.orch_context import orch_store_in_trace_db
from libs.logging_infra.orch_context import OrchestratorContext
from libs.common.shared.grpc.proto.avisarpb.avisar_pb2_client import AvisarContext
from libs.common.shared.utils import is_china_env
import os

SIM_START = 2
SIM_RUNNING = 3


class CloudformationHandler(GenericHandler):

    def __init__(self, cfg, shard_cfg):
        self.db_h = None
        self.qname = 'cloudformation_q.fifo'
        self.status = "CLOUDFORMATION"
        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).init_generic_logger_rastro(qname=self.status.lower())
        super(CloudformationHandler, self).init_failure_queue_rastro(qname=self.status.lower() + "_failq")
        self.logger.setLevel(logging.INFO)
        self.default_logger = self.logger
        self.default_logger_rastro_ctx = self.rastro_ctx
        self.sqs = SqsApi(cfg['region'], cfg['queues'], self.logger)
        self.valid_msgs = (CloudFormationMsg,)
        self.ids = {}
        self.running = True
        self.path = ''

        self.init_aws_cloudformation_logger()
        self.init_gcp_deployment_manager_logger()

        # Create an avisar context.
        self.avctx = AvisarContext(logger=self.logger,
                                   service_endpoint=cfg.get('avisar_service_endpoint'),
                                   forwarding_enabled=cfg.get('avisar_forwarding_enabled', False),
                                   sender="InstanceMgmtService",
                                   aws_env=cfg.get("aws_env"))
        # Shard Information
        # Each pod only polls on tenant/regions within its shard
        self.shard_id = shard_cfg.get("shard_id", None)
        self.replica_size = shard_cfg.get("replica_size", None)

    def init_aws_cloudformation_logger(self):
        myname = "cloudformation_aws"
        try:
            if self.logger:
                self.logger.info("Creating the handler logging file.")
        except Exception as E:
            pass

        rastro_ctx = OrchestratorContext(name=myname, log_root_dir=cfg['log-root-dir'])
        self.aws_logger_rastro_ctx = rastro_ctx
        self.aws_logger = rastro_ctx.logger
        self.aws_logger.setLevel(logging.INFO)
        return True

    def init_gcp_deployment_manager_logger(self):
        myname = "deployment_manager_gcp"
        try:
            if self.logger:
                self.logger.info("Creating the handler logging file.")
        except Exception as E:
            pass
        rastro_ctx = OrchestratorContext(name=myname, log_root_dir=cfg['log-root-dir'])
        self.gcp_logger_rastro_ctx = rastro_ctx
        self.gcp_logger = rastro_ctx.logger
        self.gcp_logger.setLevel(logging.INFO)
        return True

    def set_logger_as_aws_cloudformation_logger(self):
        self.logger = self.aws_logger
        self.db_h.logger = self.aws_logger

    def set_logger_as_gcp_deployment_manager_logger(self):
        self.logger = self.gcp_logger
        self.db_h.logger = self.gcp_logger

    def update_trace_db(self):
        ret = False
        try:
            # self.init_db()
            # Set the DB handler in the respective rastro context:-
            self.default_logger_rastro_ctx.set_db_handler(self.db_h)
            # Store info about this logger in the trace DH.
            lret = orch_store_in_trace_db(self.db_h,
                                          self.default_logger_rastro_ctx.get_trace_id(),
                                          tenant_id=999999999,
                                          workflow_type="Orch Generic Cloudformation",
                                          workflow_id=int(time.time()))
            if lret == None:
                raise Exception("Failed to store info reg workflow type Orch Generic Bringup")

            self.gcp_logger_rastro_ctx.set_db_handler(self.db_h)
            lret = orch_store_in_trace_db(self.db_h,
                                          self.gcp_logger_rastro_ctx.get_trace_id(),
                                          tenant_id=999999999,
                                          workflow_type="Orch GCP DeploymentManger",
                                          workflow_id=int(time.time()))
            if lret == None:
                raise Exception("Failed to store info reg workflow type Orch GCP Bringup")

            self.aws_logger_rastro_ctx.set_db_handler(self.db_h)
            lret = orch_store_in_trace_db(self.db_h,
                                          self.aws_logger_rastro_ctx.get_trace_id(),
                                          tenant_id=999999999,
                                          workflow_type="Orch AWS Cloudformation",
                                          workflow_id=int(time.time()))
            if lret == None:
                raise Exception("Failed to store info reg Orch AWS Bringup")

            ret = True
        except Exception as E:
            self.default_logger.error(
                "Failed to update trace id in the master database. %s %s" % (str(E.args), str(traceback.format_exc())))
        finally:
            return ret

    def handle(self):
        sleep_time = 30
        if not self.update_trace_db():
            raise Exception("Failed to update the rastro trace DB. Cannot continue")

        while self.running:
            try:
                # For AWS
                self.set_logger_as_aws_cloudformation_logger()
                self.logger.debug("Processing instance deployment for AWS!")
                self.avctx.logger = self.logger
                cfaws = CloudformationHandlerAWS(self.db_h, self.avctx, self.shard_id, self.replica_size)
                cfaws.process_aws_instance_deployment()

                if cfg.get('aws_env_type') != DEPLOYMENT_ENV_FEDRAMP_IL5 and not is_china_env():
                    # For GCP
                    self.set_logger_as_gcp_deployment_manager_logger()
                    self.logger.debug("Processing instance deployment for GCP!")
                    self.avctx.logger = self.logger
                    dm_gcp = DeploymentMgrHandlerGCP(self.db_h, self.avctx, self.shard_id, self.replica_size)
                    dm_gcp.process_gcp_instance_deployment()

                # Now sleep for a while.
                time.sleep(sleep_time)
            except Exception as ex:
                if self.default_logger is not None:
                    self.default_logger.error("handle():Possible Bug in handle function %s"
                                              % str(ex))
                    self.default_logger.error(traceback.format_exc())
