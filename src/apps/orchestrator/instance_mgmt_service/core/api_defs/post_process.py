from fastapi import HTTPEx<PERSON>, Request, Response
from pydantic import BaseModel, Field
from typing import List, Union

from libs.cloud_providers.common.probevm_bringup import probevm_bringup
from libs.cloud_providers.gcp.instance_manager.gcp_bringup import process_completed_nlb, \
    process_completed_colo_vlan_gcp_stack, calc_ipv6_interface_list, process_completed_ilb, \
    process_completed_onramp_ilb, process_completed_colo_ilbs

from libs.common.shared.sys_utils import *
from libs.cfg import cfg
from libs.cloud_providers.common.instance_bringup import instance_bringup_processing
from libs.cloud_providers.gcp.instance_manager.gcp_sase_access_utils import process_region_update_for_sase_access
from libs.cloud_providers.common.ep_cnat_scaleout_helper import send_notification_to_saas_agent
import libs.db.dbhandle as DB
from libs.common.shared.rastro.rastro_logging_wrapper import RastroLoggingInfra
from libs.model.custmodel import CustomerModel
from orchestration_service.core.orchestrator_nlb_mgmt import get_nlb_ip_address_map, update_nlb_egress_ip_list
import utils.Exceptions as customException

class PostProcessModel(BaseModel):
    '''
    JSON schema for the POST API call to trigger post processing
    The cluster info includes all possible fields that we might be interrested in reading out of
    the TF state output
    '''
    tenant_id: int = Field(description="Tenant ID (Mandatory)")
    region_id: Union[int, None] = Field(default=0, description="Compute Region ID")
    workspace_name: Union[str, None] = Field(default="", description="Workspace name")
    trigger_update: Union[int, None] = Field(default=0, description="TriggerUpdate value")
    cloud_provider: str = Field(
        description="Cloud Provider where the resources being post-processed are deployed in (Mandatory)")
    service_type_id: int = Field(description="Service type ID of the resources (Mandatory)")
    oci_info: Union[dict, None] = Field(default=None, description="Parsed OCI output state")
    cluster_info: Union[List[dict], None] = Field(default=None, description="Parsed TF instance output state")
    private_peering_info: Union[List[dict], None] = Field(default=None, description="Parsed TF private peering (interconnect attachment) output state")
    elb_info: Union[List[dict], None] = Field(default=None, description="Parsed TF ELB output state")
    ilb_info: Union[List[dict], None] = Field(default=None, description="Parsed TF ILB output state")

class DeploymentOutputModel(BaseModel):
    instances: List[dict] = Field(description="List of Instances")

class DeploymentPostProcessModel(BaseModel):
    '''
    JSON schema for the POST API call to Deployment post processing
    '''
    uuid: str = Field(description="uuid")
    status: str = Field(description="status")
    output: DeploymentOutputModel = Field(description="deployment output")

def validate_body(body: PostProcessModel):
    '''
    Returns only if the JSON payload is acceptable
    '''
    if body.cloud_provider not in ["aws", "azr", "gcp", "openstack", "oci"]:
        raise HTTPException(status_code=400, detail="unsupported cloud provider '{}'".format(body.cloud_provider))
    if body.cloud_provider == "oci":
        oci_info = body.oci_info
        if oci_info is None or len(oci_info) == 0:
            return
    else:
        cluster_info = body.cluster_info
        if cluster_info is None:
            return
        if len(cluster_info) == 0:
            return
        for ci in cluster_info:
            if not ci.get('primary_vm_id', None):
                raise HTTPException(status_code=400, detail="primary_vm_id not provided")
            if not ci.get('primary_id', None):
                raise HTTPException(status_code=400, detail="primary_id not provided")


def get_post_processor(dbh, cloud_provider) -> instance_bringup_processing:
    post_processor = instance_bringup_processing(dbh)
    if cloud_provider == "aws":
        post_processor._cloud_provider = PROVIDER_AWS
    elif cloud_provider == "azr":
        post_processor._cloud_provider = PROVIDER_AZR
    elif cloud_provider == "openstack":
        post_processor._cloud_provider = PROVIDER_OPENSTACK
    elif cloud_provider == "oci":
        post_processor._cloud_provider = PROVIDER_OCI
    else:
        post_processor._cloud_provider = PROVIDER_GCP

    return post_processor


def setup_oci_post_processor(dbh, logger, custid, region_id, cloud_provider, trigger_update, cluster_info: dict) -> instance_bringup_processing:
    '''
    Setup the Post Processing class
    '''
    logger.info(f"setup_oci_post_processor: cluster_info {cluster_info}")
    post_processor = get_post_processor(dbh, cloud_provider)
    post_processor.finished_processing_seq_nr = trigger_update
    post_processor._primary_id = cluster_info['primary_id']
    post_processor._cust_id = custid
    post_processor._primary_mgt_ip = cluster_info.get('primary_mgmt_ip', '')
    post_processor._primary_public_ip = cluster_info.get('public_ip', '')
    post_processor._reserved_public_ip_id = cluster_info.get('reserved_public_ip_id', '')
    # For NLB this will get set to secondary NLB IPv6 address which we will also set as
    # public_ipv6 in instance_bringup.py later
    post_processor._primary_public_ipv6 = cluster_info.get('eip_v6', '')
    post_processor._primary_public_ipv6_prefix_len = 128
    post_processor._primary_egress_ipv6_list = cluster_info.get('egress_ip_v6_list', {})
    post_processor._primary_pvt_ip = cluster_info.get('primary_pvt_ip', '')
    post_processor._primary_pvt_ip_id = cluster_info.get('primary_pvt_ip_id', '')
    post_processor._primary_vmid = cluster_info['primary_vm_id']
    post_processor._primary_machine_type = cluster_info.get('primary_machine_type', '')
    post_processor._primary_ingress_ip_mappings = cluster_info.get('ingress_ip_mappings', {})
    post_processor._primary_egress_ip_mappings = cluster_info.get('egress_ip_mappings', {})
    post_processor._primary_pripub_ip_mappings = cluster_info.get('pripub_ip_mappings', {})
    post_processor._primary_interface_ip_list = cluster_info.get('interface_ip_list', {})
    post_processor._primary_interface_ipv6_list = cluster_info.get('interface_ipv6_list', {})
    post_processor._primary_nlb_domain_details = cluster_info.get('lb_details', '')
    post_processor._primary_nlb_domain_details_v6 = cluster_info.get('lb_details_v6', '')
    post_processor._extrn_ip = cluster_info.get('extrn_ip', '')
    post_processor._reserved_public_ip_ids = cluster_info.get('reserved_public_ip_ids', {})
    post_processor._primary_pvt_ip_ids = cluster_info.get('primary_pvt_ip_ids', {})

    # Add NLB node interface_ipv6_list for adding security policy source IPv6 address on OCI firewalls
    post_processor._nlb_interface_ipv6_list = cluster_info.get('nlb_intf_ipv6_list', {})

    if cluster_info.get('secondary_id', None):
        post_processor._secondary_id = cluster_info['secondary_id']
        post_processor._secondary_mgt_ip = cluster_info.get('secondary_mgmt_ip', '')
        post_processor._secondary_public_ip = cluster_info.get('public_ip', '')
        post_processor._secondary_pvt_ip = cluster_info.get('secondary_pvt_ip', '')
        post_processor._secondary_vmid = cluster_info.get('secondary_vm_id', '')
        post_processor._secondary_machine_type = cluster_info.get('secondary_machine_type')

    logger.info(f"setup_oci_post_processor: Setting lb_details to {post_processor._primary_nlb_domain_details}")
    return post_processor


def setup_post_processor(dbh, logger, custid, region_id, cloud_provider, trigger_update, processed_nlb_dict, processed_ilb_dict, cluster_info: dict) -> instance_bringup_processing:
    '''
    Setup the Post Processing class
    '''
    post_processor = get_post_processor(dbh, cloud_provider)
    #These will be used in PR with interface_ipv6_list
    nlb_egress_ipv6_list_with_compute = processed_nlb_dict.get("nlb_egress_ipv6_list_with_compute")
    nlb_egress_ipv6_list = processed_nlb_dict.get("nlb_egress_ipv6_list")
    nlb_ipv6_address = processed_nlb_dict.get("nlb_ipv6_address")
    nlb_ip_address = processed_nlb_dict.get("nlb_ip_address")
    svc_ilb_ip_address = processed_nlb_dict.get("nlb_ip_address")
    post_processor.finished_processing_seq_nr = trigger_update
    post_processor._primary_id = cluster_info['primary_id']
    post_processor._cust_id = custid
    post_processor._primary_mgt_ip = cluster_info.get('primary_mgmt_ip', '')
    post_processor._primary_public_ip = cluster_info.get('public_ip', '')
    post_processor._primary_public_ip_dp2 = cluster_info.get('public_ip_dp2', '')
    post_processor._primary_public_ipv6 = cluster_info.get('eip_v6', '')
    post_processor._primary_public_ipv6_prefix_len = cluster_info.get('eip_v6_prefix_len', '')
    post_processor._primary_public_ipv6_dp2 = cluster_info.get('eip_v6_dp2', '')
    post_processor._primary_public_ipv6_prefix_len_dp2 = cluster_info.get('eip_v6_prefix_len_dp2', '')
    post_processor._primary_egress_ipv6_list = cluster_info.get('egress_ip_v6_list', {})
    post_processor._primary_pvt_ip = cluster_info.get('primary_pvt_ip', '')
    post_processor._alias_ip_ranges = cluster_info.get('alias_ip_ranges', [])
    post_processor._primary_vmid = cluster_info['primary_vm_id']
    post_processor._primary_machine_type = cluster_info.get('primary_machine_type', '')
    post_processor._primary_nlb_domain_details = nlb_ip_address
    post_processor._primary_nlb_public_ipv6 = nlb_ipv6_address
    post_processor._primary_sase_svc_ilb_v4_address = processed_ilb_dict.get("sase_svc_ilb_ipv4_address", None)
    post_processor._primary_rn_int_onramp_ilb_v4_address = processed_ilb_dict.get("rn_ilb_ipv4_address", None)
    post_processor._primary_rn_int_onramp_ilb_v6_address = processed_ilb_dict.get("rn_ilb_ipv6_address", None)
    post_processor._region_colo_ilbs_dict = processed_ilb_dict.get("region_colo_ilbs_dict", {})
    post_processor._primary_interface_ip_list = get_nlb_ip_address_map(dbh, custid, region_id)
    post_processor._primary_ingress_ip_mappings = cluster_info.get('ingress_ip_mappings', {})
    post_processor._primary_egress_ip_mappings = cluster_info.get('egress_ip_mappings', {})
    post_processor._primary_pripub_ip_mappings = cluster_info.get('pripub_ip_mappings', {})
    post_processor._primary_colo_interface_ip_address = cluster_info.get('primary_colo_interface_ip_address', '')
    try:
        post_processor._primary_interface_ipv6_list = calc_ipv6_interface_list(dbh,
                                                    post_processor._primary_id,
                                                    post_processor._primary_public_ipv6,
                                                    post_processor._primary_egress_ipv6_list,
                                                    nlb_egress_ipv6_list_with_compute,
                                                    nlb_ipv6_address,
                                                    dbh.logger)
    except customException.InstanceNotExistsException as E:
        logger.error(f"Error calculating the interface_ipv6_list during post processing for custid {custid}, instance {post_processor._primary_id}: {E.args}")

    # Will the NLB IP information be part of cluster_info? Or maybe a single object outside the list since the NLB
    # IP is shared by all GPGWs
    if cluster_info.get('secondary_id', None):
        post_processor._secondary_id = cluster_info['secondary_id']
        post_processor._secondary_mgt_ip = cluster_info.get('secondary_mgmt_ip', '')
        post_processor._secondary_public_ip = cluster_info.get('public_ip', '')
        post_processor._secondary_pvt_ip = cluster_info.get('secondary_pvt_ip', '')
        post_processor._secondary_vmid = cluster_info.get('secondary_vm_id', '')
        post_processor._secondary_machine_type = cluster_info.get('secondary_machine_type')
        post_processor._secondary_colo_interface_ip_address = cluster_info.get('secondary_colo_interface_ip_address', '')

    return post_processor


def process_probe_vm_instance(dbh, cluster_info: dict):
    probevm_inst_info = {}
    probevm_inst_info['PrimaryId'] = cluster_info['primary_id']
    probevm_inst_info['PrimaryVmId'] = cluster_info['primary_vm_id']
    probevm_inst_info['PrimaryMgtIp'] = cluster_info.get('primary_mgmt_ip', '')
    probevm_inst_info['PrimaryPvtIp'] = cluster_info.get('primary_pvt_ip', '')
    probevm_inst_info['EIP'] = cluster_info.get('public_ip', '')
    probevm_inst_info['PrimaryMachineType'] = cluster_info.get('primary_machine_type', '')
    # Assign reserved public IP ID for the probe VM processing
    probevm_inst_info['ReservedPublicIPId'] = cluster_info.get('reserved_public_ip_id', '')
    probevm_inst_info['PrivateIPId'] = cluster_info.get('primary_pvt_ip_id', '')

    post_processor = probevm_bringup(dbh)
    post_processor.process_instance(probevm_inst_info)


def process_oci_stack_output(dbh, logger, custid, service_type_id, region_id,
                             cloud_provider, trigger_update, oci_info, inst_mgmt_id):
    cluster_ids_seen = set()
    if oci_info is None or len(oci_info) == 0:
        logger.info(f"process_oci_stack_output: oci_info {oci_info} likely a stack destroy operation. Skipping OCI output processing.")
        post_processor = get_post_processor(dbh, cloud_provider)
        logger.info(f"process_oci_stack_output: Call process_instance_release_pending with inst_mgmt_id {inst_mgmt_id} and region_id {region_id}")
        post_processor.process_instance_release_pending(cluster_ids_seen, inst_mgmt_id, region_id)
        return
    nlb_details = oci_info.get("nlb_details", {})
    cluster_details = oci_info.get("cluster_details", {})
    nat_details = oci_info.get("nat_details", {})
    ep_details = oci_info.get("ep_details", {})
    nlb_info = {}
    logger.info("Beginning process_oci_stack_output")
    post_processor = None
    for nlb_id, nlb_info in nlb_details.items():
        logger.info(f"process_oci_stack_output: Processing NLB info {nlb_info} for NLB {nlb_id}")
        ci = {}
        ci['primary_id'] = nlb_id
        ci['primary_vm_id'] = nlb_info.get('nlb_id')
        ci['region_id_ingress_ip_mappings'] = nlb_info.get('region_id_ingress_ip_mappings', {})
        logger.info(f"process_oci_stack_output: Region ID ingress IP mappings {ci['region_id_ingress_ip_mappings']} for custid {custid} region {region_id}")

        # Update NLB public_ip with compute region ID public IP to handle IIR disabled cases where NLB could carry multiple ingress IPs
        ci['public_ip'] = ci['region_id_ingress_ip_mappings'].get(str(region_id), '')
        logger.info(f"process_oci_stack_output: Updating NLB public ip to {ci['public_ip']} for custid {custid} region {region_id}")
        ingress_ip_mappings = nlb_info.get('ingress_ip_mappings')
        if ingress_ip_mappings and isinstance(ingress_ip_mappings, dict):
            ci['ingress_ip_mappings'] = ingress_ip_mappings
            logger.info(f"process_oci_stack_output: Updated Ingress IP mappings {ci['ingress_ip_mappings']} for custid {custid} region {region_id}")
        ci['reserved_public_ip_id'] = nlb_info.get('reserved_public_ip_id', '')
        ci['primary_pvt_ip'] = nlb_info.get('private_ip_address')
        ci['primary_pvt_ip_id'] = nlb_info.get('private_ip_id')
        ci['primary_pvt_ip_ids'] = nlb_info.get('private_ip_ids', {})
        ci['reserved_public_ip_ids'] = nlb_info.get('reserved_public_ip_ids', {})
        logger.info(f"process_oci_stack_output: Processing NLB with secondary IPv6 address {nlb_info.get('nlb_vnic_secondary_ipv6', '')} for custid {custid} region {region_id}")
        ci['eip_v6'] = nlb_info.get('nlb_vnic_secondary_ipv6', '')
        ci['nlb_primary_v6'] = nlb_info.get('nlb_vnic_primary_ipv6', '')
        ci['nlb_intf_ipv6_list'] = {str(region_id): ci['nlb_primary_v6']} if ci['nlb_primary_v6'] else {}
        ci['interface_ipv6_list'] = {str(region_id): ci['nlb_primary_v6']} if ci['nlb_primary_v6'] else {}
        logger.info(f"process_oci_stack_output: Processing NLB with primary IPv6 address {ci['nlb_primary_v6']} for custid {custid} region {region_id} NLB Interface IPv6 list {ci['nlb_intf_ipv6_list']}")
        logger.info(f"process_oci_stack_output: Processing NLB with interface_ipv6_list {ci['interface_ipv6_list']} for custid {custid} region {region_id}")
        try:
            ret = update_nlb_egress_ip_list(dbh, custid, region_id, nlb_info.get('region_id_ingress_ip_mappings', {}))
            logger.info(f"process_oci_stack_output: Updated NLB egress IP list result: {ret}")
        except Exception as ex:
            logger.error(f"process_oci_stack_output: Failed Updating NLB egress IP list result with exception {ex}")
        post_processor = setup_oci_post_processor(dbh, logger, custid, region_id,
                                                  cloud_provider, trigger_update, ci)
        post_processor.process_instance()
        cluster_ids_seen.add(nlb_id)

    logger.info("Completed post-process for OCI NLBs")

    for nat_id, nat_info in nat_details.items():
        ci = {}
        ci['primary_id'] = nat_id
        ci['primary_vm_id'] = nat_info.get('nat_id')
        ci['pripub_ip_mappings'] = nat_info.get('pripub_ip_mappings')
        post_processor = setup_oci_post_processor(dbh, logger, custid, region_id,
                                                  cloud_provider, trigger_update, ci)
        post_processor.process_instance()
        cluster_ids_seen.add(nat_id)
        
    logger.info("Completed post-process for OCI NAT GWs")        

    interface_ip_list = get_nlb_ip_address_map(dbh, custid, region_id)
    # TNRC processing nlb IP map to get ipv6 list
    
    logger.info(f"process_oci_stack_output: IIL -> {interface_ip_list} for {custid}, {region_id}")
    lb_details = ''
    lb_details_v6 = ''
    # For IIR disabled cases where multiple edge regions are onboarded
    # interface_ip_list will have compute as well as edge region ID entries
    # lb_details should only contain compute region's public IP
    # Example for compute region 220 and edge regions 301 and 302 with IIR disabled
    # interface_ip_list looks like -> {'220': '********', '301': '********', '302': '********'}
    # In this case, lb_details should be set to '********' as the compute region's public IP
    for loc_id, public_ip in interface_ip_list.items():
        logger.info(f"process_oci_stack_output: loc_id  {loc_id}, public_ip {public_ip}, region_id {region_id} for {interface_ip_list}")
        if str(loc_id) == str(region_id):
            lb_details = public_ip
            break
    # assuming there is only one NLB per tenant, region
    # We are setting lb_details_v6 here with the NLB's secondary IPv6 address
    lb_details_v6 = nlb_info.get('nlb_vnic_secondary_ipv6', '')
    logger.info(f"process_oci_stack_output: lb_details {lb_details}, lb_details_v6 {lb_details_v6} for {custid}, {region_id}")

    for cluster_id, cluster_info in cluster_details.items():
        ci = {}
        if cluster_id:
            cluster_ids_seen.add(str(cluster_id))
        for vm_id, vm_info in cluster_info.items():
            logger.info(f"process_oci_stack_output: Processing VM info {vm_info} for cluster {cluster_id}")
            if vm_info.get('is_secondary', False) == True:
                ci['secondary_id'] = vm_id
                ci['secondary_vm_id'] = vm_info.get('vm_id')
                ci['secondary_mgmt_ip'] = vm_info.get('shared_mgmt_private_ip')
                ci['secondary_pvt_ip'] = vm_info.get('dp_private_ip')
                ci['secondary_machine_type'] = vm_info.get('machine_type')
                continue
            else:
                ci['primary_id'] = vm_id
                ci['primary_vm_id'] = vm_info.get('vm_id')
                ci['primary_mgmt_ip'] = vm_info.get('shared_mgmt_private_ip')
                ci['primary_pvt_ip'] = vm_info.get('dp_private_ip')
                ci['primary_pvt_ip_id'] = vm_info.get('dp_private_ip_id')
                ci['primary_machine_type'] = vm_info.get('machine_type')
            ci['public_ip'] = vm_info.get('dp_public_ip')
            ci['reserved_public_ip_id'] = vm_info.get('reserved_public_ip_id')
            logger.info(f"process_oci_stack_output: Processing cluster {cluster_id}, VM {vm_id} with secondary IPv6 address {vm_info.get('dp_publicv6_ip_secondary', '')}")
            ci['eip_v6'] = vm_info.get('dp_publicv6_ip_secondary', '')
            ci['egress_ip_mappings'] = vm_info.get('egress_ip_mappings')
            ci['interface_ip_list'] = interface_ip_list
            if lb_details:
                ci['lb_details'] = lb_details
                if lb_details_v6:
                    ci['lb_details_v6'] = lb_details_v6
                    # Set interface IPv6 list with load balancer IPv6 secondary address
                    ci['interface_ipv6_list'] = {str(region_id): lb_details_v6} if lb_details_v6 else {}
                    logger.info(f"process_oci_stack_output: Interface IPv6 list set to {ci.get('interface_ipv6_list')} for {custid}, {region_id}")
                    logger.info(f"process_oci_stack_output: LB details IPv6 set to {ci.get('lb_details_v6')} for {custid}, {region_id}")

            if service_type_id == NODE_TYPE_SWG_PROXY:
                ci['lb_details'] = vm_info.get('lb_ip')# lb_ip is only available for swg instances and can be set for lb_details
                ci['extrn_ip'] = vm_info.get('extrn_ip')# extrn_ip is only available for swg instances 
    
        if service_type_id == NODE_TYPE_PROBE_VM:
            logger.info(f"process_oci_stack_output: Reserved public IP ID for Probe VM is {ci.get('reserved_public_ip_id', None)} for {custid}, {region_id}")
            logger.info(f"process_oci_stack_output: Private IP ID for Probe VM is {ci.get('primary_pvt_ip_id', None)} for {custid}, {region_id}")
            process_probe_vm_instance(dbh, ci)
        else:
            post_processor = setup_oci_post_processor(dbh, logger, custid, region_id,
                                                      cloud_provider, trigger_update, ci)
            post_processor.process_instance()

    if ep_details:
        logger.info(f"Processing ilb ip that needs to be set for SWGPROXY nodes.")
        post_processor.process_instance_ep_ilb_ip(ilb_ip_address=ep_details["ilb_ip_address"], 
                                                  region_id=region_id, cust_id=custid)
        #trigger saas-agent notification but since we do not have account id here, pass None
        send_notification_to_saas_agent(dbh, custid, None, region_id, PROVIDER_OCI, NODE_TYPE_SWG_PROXY)
    
    logger.info(f"Processing any IPs that need to be fixed up for NGPA MUs, cluster_ids_seen: {cluster_ids_seen}")
    if not post_processor:
        #In the case where no body.cluster_info is given, post_processor can be None
        post_processor = get_post_processor(dbh, cloud_provider)

    post_processor.process_instance_release_pending(cluster_ids_seen, inst_mgmt_id, region_id)
    logger.info("Completed post-process for OCI VM instances")


def post_process(dbh, request: Request, inst_mgmt_id: int, body: PostProcessModel, response: Response):
    '''
    Trigger Post Processing for a particular inst_mgmt_id and cloud_provider
    Sample Body:
    {
        "tenant_id": 1234,
        "region_id": 201,
        "cloud_provider": "gcp",
        "cluster_info": [
            {
                "cluster_id": 100,
                "primary_id": 100,
                "primary_vm_id": "foobar",
                "primary_mgmt_ip": "x.x.x.x",
                "primary_pvt_ip": "x.x.x.x",
                "alias_ip_ranges": [],
                "secondary_id": 101,
                "secondary_vm_id": "foobaz",
                "secondary_mgmt_ip": "x.x.x.x",
                "secondary_pvt_ip": "x.x.x.x",
                "public_ip": "x.x.x.x"
            },
            ...
        ]
    }
    '''
    rastro_trace_id = request.headers.get("X-Trace-Id")
    logger = RastroLoggingInfra(name="post_process_api")
    logger.uuid = rastro_trace_id
    dbh.logger = logger
    validate_body(body)
    custmodel = None
    custid = None
    post_processor = None
    if custmodel is None:
        custmodel = CustomerModel(dbh=dbh, acct_id=body.tenant_id)
    custid = custmodel.get_param("id")
    logger.info("Starting post process")
    if body.cloud_provider == "oci":
        process_oci_stack_output(dbh, logger, custid, body.service_type_id,
                                 body.region_id,
                                 body.cloud_provider, body.trigger_update,
                                 body.oci_info, inst_mgmt_id)
    else:
        logger.info(f"body.cluster_info: {body.cluster_info}")
        logger.info(f"body.elb_info: {body.elb_info}")
        logger.info(f"body.ilb_info: {body.ilb_info}")
        processed_nlb_dict = {"nlb_ip_address": None,
                              "nlb_ipv6_address": None,
                              "nlb_egress_ipv6_list_with_compute": {},
                              "nlb_egress_ipv6_list": {}}

        processed_ilb_dict = {"sase_svc_ilb_ipv4_address": None,
                              "rn_ilb_ipv4_address": None,
                              "rn_ilb_ipv6_address": None,
                              "region_colo_ilbs_dict": None}
        if body.elb_info:
            processed_nlb_dict = process_completed_nlb(dbh, custid, body.region_id, all_inst_output=body.elb_info)
        else:
            processed_nlb_dict = process_completed_nlb(dbh, custid, body.region_id)
        logger.info("Completed nlb post process")

        # We need to process the ILB object object to fetch ipv4 address.
        if body.ilb_info:
            processed_ilb_dict["sase_svc_ilb_ipv4_address"] = process_completed_ilb(dbh, custid, body.region_id, all_inst_output=body.ilb_info)
            processed_ilb_dict["rn_ilb_ipv4_address"], processed_ilb_dict["rn_ilb_ipv6_address"] = process_completed_onramp_ilb(dbh, custid, body.region_id, all_inst_output=body.ilb_info)
            processed_ilb_dict["region_colo_ilbs_dict"] = process_completed_colo_ilbs(dbh, custid, body.region_id, all_ilb_output=body.ilb_info)

        deployed_instance_ids = []
        # Filter for Service Type Ids - Will include RN/SC/MU SPNs
        sase_access_desired_service_type_ids = [0]
        cluster_ids_seen = set()
        if body.cluster_info is not None:
            for ci in body.cluster_info:
                if body.service_type_id == NODE_TYPE_PROBE_VM:
                    process_probe_vm_instance(dbh, ci)
                else:
                    post_processor = setup_post_processor(dbh, logger, custid, body.region_id,
                                                          body.cloud_provider, body.trigger_update,
                                                          processed_nlb_dict, processed_ilb_dict, ci)
                    post_processor.process_instance()
                # Processing Specific to SASE Access / Single IP / RN HP
                
                if custmodel.get_param("sase_fabric_support") == 1:
                    if body.cloud_provider == "gcp" and body.service_type_id in sase_access_desired_service_type_ids:
                        primary_id = ci.get('primary_id')
                        secondary_id = ci.get('secondary_id')
                        deployed_instance_ids.append(primary_id)
                        deployed_instance_ids.append(secondary_id)
                        
                cluster_id = ci.get('cluster_id')
                if cluster_id:
                    cluster_ids_seen.add(str(cluster_id))

        logger.info(f"Processing any IPs that need to be fixed up for NGPA MUs, cluster_ids_seen: {cluster_ids_seen}")
        
        if not post_processor:
            #In the case where no body.cluster_info is given, post_processor can be None
            post_processor = get_post_processor(dbh, body.cloud_provider)
            
        post_processor.process_instance_release_pending(cluster_ids_seen, inst_mgmt_id, body.region_id)

        if custmodel.get_param("sase_fabric_support") == 1:
            process_region_update_for_sase_access(dbh,
                                                  custmodel,
                                                  body.region_id,
                                                  deployed_instance_ids,
                                                  body.trigger_update)
            logger.info("Completed post-process for SASE Access")

        logger.info("Completed instance post process")
        custid = custmodel.get_param("id")
        project_id = custmodel.get_param("project_id")
        all_colo_att_output = []
        if body.private_peering_info is not None:
            all_colo_att_output = body.private_peering_info
        process_completed_colo_vlan_gcp_stack(dbh, custid, body.region_id, project_id, body.workspace_name, body.trigger_update, all_colo_att_output)
        logger.info("Completed colo post process")
    response.headers["X-Trace-Id"] = rastro_trace_id
    return {"status": "ok"}

def validate_deployment_body(body: DeploymentPostProcessModel):
    '''
    Returns only if the JSON payload is acceptable
    '''
    if not body.status in ('ok'):
        raise HTTPException(status_code=400, detail="status is not ok")
    output = body.output
    if not output:
        raise HTTPException(status_code=400, detail="No Deployment output")

    for inst in output.instances:
        if not inst.get('name'):
            raise HTTPException(status_code=400, detail="vm_name not provided")
        if not inst.get('vm_id', None):
            raise HTTPException(status_code=400, detail="vm_id not provided")
        if not inst.get('interfaces', None):
            raise HTTPException(status_code=400, detail="interfaces not provided")

def deployment_setup_post_processor(dbh, logger, cloud_provider, instance) -> instance_bringup_processing:
    post_processor = get_post_processor(dbh, cloud_provider)

    post_processor._primary_id = int(instance.get('name').split('_')[1])
    for intf in instance.get('interfaces'):
        intf_name = intf.get('name')
        if intf_name == 'nic-mgmt':
            post_processor._primary_mgt_ip = intf.get('private_ip', '')
        elif intf_name == 'nic-dp':
            post_processor._primary_pvt_ip = intf.get('private_ip', '')
            post_processor._primary_public_ip = intf.get('public_ip', '')

    post_processor._primary_vmid = instance.get('vm_id')
    return post_processor

def deployment_post_process(dbh, request: Request, inst_mgmt_id: int,
                            body: DeploymentPostProcessModel, response: Response):
    """ Sample payload from provision-service
     {
        "uuid":"hhhhhhhh-hhhh-hhhh-hhhh-hhhhhhhhhhhh",
        "status":"ok",
        "output": {
            "instances": [
                {
                    "name":"xxxx_1234_us-west-123_xxxxxxxxxxx-123456789",
                    "vm_id":"xxx",
                    "interfaces":[
                        {
                            "name":"nic-mgmt",
                            "private_ip":"100.64.x.x"
                        },
                        {
                            "name":"nic-dp",
                            "public_ip":"x.x.x.x",
                            "private_ip":"100.70.x.x"
                        }
                    ]
                }
            ]
        }
    }
    """
    logger = RastroLoggingInfra(name="deployment_post_process_api")
    logger.uuid = body.uuid
    dbh.logger = logger
    validate_deployment_body(body)

    logger.info("Deployment response body: %s" % str(body))
    for inst in body.output.instances:
        #FIXME: Cloud provider is not present in response yet.
        post_processor = deployment_setup_post_processor(dbh, logger, PROVIDER_OPENSTACK_DB_ENUM_VALUE, inst)
        post_processor.process_instance()

    return {"status": "ok"}
