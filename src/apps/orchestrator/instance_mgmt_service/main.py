#!/usr/bin/python3
import logging
import logging.handlers
import threading
from libs.comm.sqsapi import SqsApi
from libs.common.shared.orch_utils import OrchState
from instance_mgmt_service.core.cloudformation import CloudformationHandler
from instance_mgmt_service.core.bringup import BringupHandler
from instance_mgmt_service.core.api import APIHandler
from libs.cfg import *
import sys
import signal
import time
import traceback

inst_mgmt = None
running = True
import subprocess
import boto3
import requests
from base64 import b64decode
import libs.db.dbhandle as DB
import os
import yaml
from yaml.loader import SafeLoader

class ThreadHandle():
    handle = None
    t_handle = None

    def __init__(self):
        pass

class InstMgmtModel():
    def __init__(self, dbh=None):
        self.sqs = None
        self.dbh = dbh
        # Inst-Mgmt sharding
        # It is only used for the Handlers involved with polling. It allows each pod to only poll
        # on a subset of all the entries.
        self.shard_cfg = dict()
        if cfg['deployment_mode'] == 'vm':
            self.shard_cfg["shard_id"] = 0
        else:
            self.shard_cfg["shard_id"] = int(os.environ["HOSTNAME"].split("inst-mgmt-", 1)[1])
        self.shard_cfg["replica_size"] = int(os.environ.get("REPLICA_SIZE", 1))

        # Conditional Notification to shut down the Fast API Server
        self.cond = threading.Condition()

        self.cft_q = ThreadHandle()  # Cloud Formation thread Q
        self.brup_q = ThreadHandle()  # Bringup Q
        self.api_t = ThreadHandle()  # API Handler

        self.brup_q.handle = BringupHandler(cfg, self.shard_cfg)
        self.cft_q.handle = CloudformationHandler(cfg, self.shard_cfg)
        self.api_t.handle = APIHandler(cfg, self.shard_cfg, self.dbh)

        self.cft_q.t_handle = threading.Thread(name="CloudformationHandler",
                                               target=thread_handler, args=(self.cft_q.handle,), daemon=True)
        self.brup_q.t_handle = threading.Thread(name="BringupHandler",
                                                target=thread_handler, args=(self.brup_q.handle,), daemon=True)
        self.api_t.t_handle = threading.Thread(name="APIHandler",
                                               target=self.api_t.handle.handle, args=(self.cond,), daemon=True)

        self.threads = {
            'cft_thread': self.cft_q.t_handle,
            'bringup_thread': self.brup_q.t_handle,
            'api_thread': self.api_t.t_handle,
        }

    def start_threads(self):
        self.brup_q.t_handle.start()
        self.cft_q.t_handle.start()
        self.api_t.t_handle.start()

    def stop_threads(self):
        self.cft_q.handle.running = False
        self.brup_q.handle.running = False
        self.api_t.handle.running = False

    def copy_orchcfg_to_db(self):
        '''
        Only doing this for the first inst-mgmt pod to upgrade
        '''
        if self.shard_cfg["shard_id"] != 0:
            return
        logger = self.dbh.logger
        cursor = self.dbh.get_cursor(self.dbh)

        withheld_keys = [
        'dbpassword',
        'aws_access_key_id',
        'aws_secret_access_key'
        ]
        for key, value in list(cfg.items()):
            if key in withheld_keys:
                logger.info(f"Do not insert {key} in to orch_cfg table")
                continue

            if isinstance(value, (str, dict, bool, list)):
                logger.info("Inserting orch_cfg setting into DB: %s" % key)
                params = (key, str(value), str(value))
                sql = ("insert into orch_cfg (name, value) values (%s, %s) on duplicate key update value=%s")
                try:
                    cursor.execute(sql, params)
                except Exception as ex:
                    logger.error("CustomerModel:save(): Update query failed: %s" % (str(ex),))

        self.dbh.cursorclose(cursor)


def thread_handler(thread_h):
    try:
        print(("Starting sqs queue thread handler for %s " % str(thread_h.qname)))
        thread_h.init_db()
        thread_h.handle()
    except Exception as E:
        print("Fatal! Exiting all the threads and re-strating process due to exception %s, traceback: %s" %
              (str(E.args), str(traceback.format_exc())))
        sys.exit(-1)


def signal_handler(signal, frame):
    global inst_mgmt
    global running
    print("Exiting.. Ctrl+c")
    for th in threading.enumerate():
        print(th)
        traceback.print_stack(sys._current_frames()[th.ident])
        print()
    if inst_mgmt:
        inst_mgmt.stop_threads()
    inst_mgmt = None
    running = False


def read_aws_credentials():
    '''
    Read the AWS credentials and save it to our cfg dict
    '''
    if os.environ.get("AWS_ENV", "") == "":
        cfg['deployment_mode'] = 'vm'
    else:
        cfg['deployment_mode'] = 'kubernetes'
        with open("/orch_aas/libs/cfg.yaml") as f:
            data = yaml.load(f, Loader=SafeLoader)
            cfg['aws_access_key_id'] = data['aws_access_key_id']
            cfg['aws_secret_access_key'] = data['aws_secret_access_key']


def get_aws_region(logger):
    # Check if AWS_REGION environment variable is set
    if not os.environ.get('AWS_REGION'):
        try:
            # Try to fetch the region from EC2 metadata
            aws_region = requests.get('http://***************/latest/meta-data/placement/region',
                                      timeout=2).text.strip()

            # Set the AWS_REGION environment variable
            os.environ['AWS_REGION'] = aws_region
            logger.info(f"AWS_REGION is set to {aws_region}")
        except requests.RequestException:
            logger.error("Error: Unable to determine AWS region.")
            sys.exit(-1)
        except Exception as E:
            logger.error(f"Except: {E.args}")
            sys.exit(-1)
    else:
        logger.info(f"AWS_REGION is already set to {os.environ['AWS_REGION']}")


# Function to check if any thread is not alive
def any_thread_not_alive(threads):
    for thread_name, thread in threads.items():
        if not thread.is_alive():
            print(f"Thread {thread_name} is not alive!")
            return True
        else:
            print(f"Thread {thread_name} is alive!")
    return False

def main():
    global inst_mgmt
    global running
    log_root_dir = "/var/log/pan/"
    read_aws_credentials()
    threads = {}
    try:
        if 'log-root-dir' in cfg:
            log_root_dir = cfg['log-root-dir']
        else:
            cfg['log-root-dir'] = log_root_dir

        cmd = []
        cmd.append("mkdir")
        cmd.append("-p")
        cmd.append(log_root_dir)
        output = subprocess.check_output(cmd)
        print(("%s: %s" % (log_root_dir, str(output))))
    except Exception as ex:
        print(("Failed to start Orchestrator: %s" % str(ex)))
        return

    filename = log_root_dir + "/main.log"
    handler = logging.handlers.RotatingFileHandler(filename, maxBytes=10485760, backupCount=1)
    logger = logging.getLogger("main")
    logger.setLevel(logging.DEBUG)
    logger.addHandler(handler)
    logger.info("Starting....")
    logger.info(str(cfg))
    get_aws_region(logger)
    signal.signal(signal.SIGINT, signal_handler)
    orch_state = OrchState(logger)
    prev_orch_state = orch_state.NOT_INITIALIZED

    while (running):
        cur_orch_state = orch_state.get_orch_current_state()
        if prev_orch_state != cur_orch_state:
            logger.info("Orchestrator state changed from %s to %s" % (prev_orch_state, cur_orch_state))
            if cur_orch_state == orch_state.ACTIVE:
                try:
                    dbh = DB.DbHandle(logger)
                    inst_mgmt = InstMgmtModel(dbh)
                    threads = inst_mgmt.threads
                    # inst_mgmt.copy_orchcfg_to_db()
                    boto3.setup_default_session(region_name=cfg['region'])
                    if cfg.get('dbpassword_decrypted', False) == False:
                        ENCRYPTED = cfg['dbpassword']
                        password = boto3.client('kms', cfg['region']).decrypt(CiphertextBlob=b64decode(ENCRYPTED))[
                            'Plaintext']
                        cfg['dbpassword'] = password.decode("utf-8")
                        cfg['dbpassword_decrypted'] = True
                except Exception as ex:
                    logger.error("Unable to decrypt database password: %s" % str(ex))
                    return

                inst_mgmt.start_threads()
            else:
                if inst_mgmt:
                    inst_mgmt.stop_threads()
                    inst_mgmt = None
        prev_orch_state = cur_orch_state
        logger.debug("Back in main(inst_mgmt_service), current orch_state %s" % (cur_orch_state))

        # Periodically check if threads are alive
        if running == True and cur_orch_state == orch_state.ACTIVE and any_thread_not_alive(threads):
            print("At least one thread that should be running is not running. Exiting...")
            sys.exit(-1)

        time.sleep(5)


if __name__ == "__main__":
    main()
