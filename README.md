- Create your feature/bugfix branch. Build pipeline will run as part of each check-in. If the build fails please check the build log for any UT failure. You can run build/UT locally as well by following the build step above.

- Once you are ready to create a MR, submit the MR using the below comment format using #env at the end. The MR will not be approved if scripts didnt run with the latest code.
git commit -m "<bug-id> #comment <comment>"
eg)
git commit -m "CYR-00000 #comment Sample Comment "
Orchestration component MR's get deployed on dev7 and automation tests are run against dev7

- Check pipeline status for successful run. 
- If any failure in build/UT troubleshoot the issue or engage component owner
- If the coverage step fails, 
    - ensure the code added has proper UT coverage. 
    - rebase the PR in case there are more UTs added in dev and feature branch is missing those.
- If any issue with Terraform deployment please engage component owner to check.
- If any issue with automation or post-deployment step, please engage QA team.
- Please ensure clean pipeline run for the MR approval

 <!---Protected_by_PANW_Code_Armor_2024 - Y3ByfC9wcmlzbWEtYWNjZXNzL3Nhc2UtY29zbW9zLWdyb3VwL3Nhc2UtY29udHJvbGxlci1ncm91cC9vcmNoZXN0cmF0b3J8MTMzMTR8ZGV2L2RldmVsb3A= ---> 

## Build in local and upload aws ecr
##################
prerequisite : 
##################
1. ECR_REPO name prefix should be "orch/".
1. configure dest env where you want push credentials in local as below.
1. for non-prod env always push image to qa ecr repo as all non-prod eks refers image from qa ecr repo.



- echo [profile qa] >> ~/.aws/config
- echo 'region = us-west-2' >> ~/.aws/config
- echo 'output = json' >>  ~/.aws/config
- echo [qa] >> ~/.aws/credentials
- echo aws_access_key_id = ${QA_AWS_ACCESS_KEY_ID} >> ~/.aws/credentials
- echo aws_secret_access_key = ${QA_AWS_SECRET_ACCESS_KEY} >>  ~/.aws/credentials
- cat ~/.aws/config
- cat ~/.aws/credentials



########
steps
########

- M-C02FRHZMMD6M:docker rbhavsar$ export PAN_BUILD_DIR=/Users/<USER>/orchestrator
- M-C02FRHZMMD6M:docker rbhavsar$ export ECR_REPO=orch/feature/cyr-43243-local
- M-C02FRHZMMD6M:docker rbhavsar$ export COMMON_LIB_PATH=/Users/<USER>/saas-infra/src/apps/shared
- M-C02FRHZMMD6M:docker rbhavsar$ pwd
- /Users/<USER>/orchestrator/docker
- M-C02FRHZMMD6M:docker rbhavsar$ make local_build upload


## Run UT in local machine
* export PAN_BUILD_DIR=/Users/<USER>/orchestrator
* export SAAS_INFRA_BUILD_DIR=/Users/<USER>/saas-infra
* cd /Users/<USER>/orchestrator/tests/orchestrator
* python3 -m venv venv
* source venv/bin/activate
* make local_ut
