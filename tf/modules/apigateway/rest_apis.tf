################################################################################
# VPC Link and NLB Data
################################################################################
data "aws_lb" "pim-nlb" {
  name = "pim-service-nlb"
}

data "aws_lambda_function" "pyauth" {
  function_name = "pyauth"
}

resource "aws_api_gateway_vpc_link" "pim-eks-vpc-link" {
  name        = "pim-eks-vpc-link"
  description = "VPC Link for WebSocket APIs"
  target_arns = [data.aws_lb.pim-nlb.arn]

  tags = {
    Name        = "pim-eks-vpc-link"
    Environment = "Production"
  }
}

################################################################################
# WebSocket API Configuration
################################################################################
resource "aws_apigatewayv2_api" "pim_websocket_api" {
  name                       = "pim-websocket-api"
  protocol_type              = "WEBSOCKET"
  route_selection_expression = "$request.body.default"
}

################################################################################
# Lambda Authorizer Configuration
################################################################################
resource "aws_lambda_permission" "pyauth_permission" {
  statement_id  = "AllowExecutionFromAPIGateway-${aws_apigatewayv2_api.pim_websocket_api.id}"
  action        = "lambda:InvokeFunction"
  function_name = data.aws_lambda_function.pyauth.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.pim_websocket_api.execution_arn}/*"
}

resource "aws_apigatewayv2_authorizer" "pyauth" {
  api_id           = aws_apigatewayv2_api.pim_websocket_api.id
  authorizer_type  = "REQUEST"
  authorizer_uri   = data.aws_lambda_function.pyauth.invoke_arn
  identity_sources = ["route.request.header.Authorization"]
  name             = "pyauth"
}

################################################################################
# Integrations (VPC Proxy Integration: False)
################################################################################
resource "aws_apigatewayv2_integration" "connect_integration" {
  api_id             = aws_apigatewayv2_api.pim_websocket_api.id
  integration_type   = "HTTP"
  connection_type    = "VPC_LINK"
  connection_id      = aws_api_gateway_vpc_link.pim-eks-vpc-link.id
  integration_uri    = "http://${data.aws_lb.pim-nlb.dns_name}:8080/pim-service/connect"
  integration_method = "ANY"
  timeout_milliseconds = 29000
  content_handling_strategy = "CONVERT_TO_TEXT"
  request_parameters = {
    "integration.request.header.X-Amzn-ConnectionId" = "context.connectionId",
    "integration.request.header.Client-ID": "route.request.header.Client-ID"
  }
}

resource "aws_apigatewayv2_integration" "disconnect_integration" {
  api_id             = aws_apigatewayv2_api.pim_websocket_api.id
  integration_type   = "HTTP"
  connection_type    = "VPC_LINK"
  connection_id      = aws_api_gateway_vpc_link.pim-eks-vpc-link.id
  integration_uri    = "http://${data.aws_lb.pim-nlb.dns_name}:8080/pim-service/disconnect"
  integration_method = "ANY"
  timeout_milliseconds = 29000

  request_parameters = {
    "integration.request.header.X-Amzn-ConnectionId" = "context.connectionId"
  }
}

resource "aws_apigatewayv2_integration" "default_integration" {
  api_id             = aws_apigatewayv2_api.pim_websocket_api.id
  integration_type   = "HTTP"
  connection_type    = "VPC_LINK"
  connection_id      = aws_api_gateway_vpc_link.pim-eks-vpc-link.id
  integration_uri    = "http://${data.aws_lb.pim-nlb.dns_name}:8080/pim-service/default"
  integration_method = "ANY"
  timeout_milliseconds = 29000
  content_handling_strategy = "CONVERT_TO_TEXT"
  request_parameters = {
    "integration.request.header.X-Amzn-ConnectionId" = "context.connectionId"
  }
}

################################################################################
# Routes with Authorization for $connect
################################################################################
resource "aws_apigatewayv2_route" "connect_route" {
  api_id             = aws_apigatewayv2_api.pim_websocket_api.id
  route_key          = "$connect"
  authorization_type = "CUSTOM"
  authorizer_id      = aws_apigatewayv2_authorizer.pyauth.id
  target             = "integrations/${aws_apigatewayv2_integration.connect_integration.id}"
  request_parameter {
    request_parameter_key = "route.request.header.Client-ID"
    required = false
  }
}

################################################################################
# Integration Response for $connect
################################################################################
resource "aws_apigatewayv2_integration_response" "connect_response" {
  api_id                   = aws_apigatewayv2_api.pim_websocket_api.id
  integration_id           = aws_apigatewayv2_integration.connect_integration.id
  integration_response_key = "$default"

  response_templates = {
    "application/json" = "{}"
  }
}

################################################################################
# Route Response for $connect Route (Enable Two-Way Communication)
################################################################################
resource "aws_apigatewayv2_route_response" "connect_route_response" {
  api_id             = aws_apigatewayv2_api.pim_websocket_api.id
  route_id           = aws_apigatewayv2_route.connect_route.id
  route_response_key = "$connect"
}

################################################################################
# Routes with Authorization for $disconnect
################################################################################

resource "aws_apigatewayv2_route" "disconnect_route" {
  api_id    = aws_apigatewayv2_api.pim_websocket_api.id
  route_key = "$disconnect"
  target    = "integrations/${aws_apigatewayv2_integration.disconnect_integration.id}"
}

################################################################################
# Routes with Authorization for $default
################################################################################

resource "aws_apigatewayv2_route" "default_route" {
  api_id    = aws_apigatewayv2_api.pim_websocket_api.id
  route_key = "$default"
  target    = "integrations/${aws_apigatewayv2_integration.default_integration.id}"
}

################################################################################
# Stage with auto_deploy enabled and throttling configuration
################################################################################
resource "aws_apigatewayv2_stage" "production" {
  api_id      = aws_apigatewayv2_api.pim_websocket_api.id
  name        = "production"
  auto_deploy = true
  
  default_route_settings {
    throttling_rate_limit  = 10000
    throttling_burst_limit = 10000
  }
}

################################################################################
# Integration Response for $default
################################################################################
resource "aws_apigatewayv2_integration_response" "default_response" {
  api_id                   = aws_apigatewayv2_api.pim_websocket_api.id
  integration_id           = aws_apigatewayv2_integration.default_integration.id
  integration_response_key = "$default"

  response_templates = {
    "application/json" = "{ \"body\": $input.path('$.body') }"
  }
}

################################################################################
# Route Response for $default Route (Enable Two-Way Communication)
################################################################################
resource "aws_apigatewayv2_route_response" "default_route_response" {
  api_id             = aws_apigatewayv2_api.pim_websocket_api.id
  route_id           = aws_apigatewayv2_route.default_route.id
  route_response_key = "$default"
}

################################################################################
# IAM Role and Policies for Orchestrator
################################################################################
data "aws_iam_role" "orchestrator" {
  name = "orchestrator"
}

resource "aws_iam_role_policy" "allow_websocket_connection" {
  name   = "AllowWebSocketConnectionToClientFromServer"
  role   = data.aws_iam_role.orchestrator.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "execute-api:ManageConnections"
        Resource = "arn:${var.aws_partition}:execute-api:*:${var.account_id}:*/*"
      }
    ]
  })
}

resource "aws_iam_role_policy" "allow_apigateway_gets" {
  name   = "AllAPIGatewayGets"
  role   = data.aws_iam_role.orchestrator.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "apigateway:GET"
        Resource = "arn:${var.aws_partition}:apigateway:*::/apis"
      }
    ]
  })
}
