{
    "meta": {
        "branch": "dev/develop",
        "aws_access_key_id": "aws_access_key_id_cyr_{{aws_underscore_env}}",
        "aws_secret_access_key": "aws_secret_access_key_cyr_{{aws_underscore_env}}",
        "svc_acct": "GPCS_MONIT_CRED_DEV_KMS",
        "aws_env": "{{aws_env}}",
        "account_id": "{{acct_id}}",
        "regions": {{regions_list}},
        "active_region": ["{{active_region}}"],
        "swg_account_id": "************",
        "eks_cluster_name": "orch_eks_{{active_region}}_{{aws_env}}",
        "r53_account": "{{route53acct}}",
        "environment_type": "dev",
        "environment_mode": "commercial",
        "deployment_stack": "terragrunt"
    }
}