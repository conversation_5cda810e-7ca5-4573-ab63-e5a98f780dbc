{
    "meta": {
        "branch": "dev/develop",
        "aws_access_key_id": "aws_access_key_id_cyr_{{aws_underscore_env}}",
        "aws_secret_access_key": "aws_secret_access_key_cyr_{{aws_underscore_env}}",
        "svc_acct": "CYR_FH_MAUI_PUBLISHER_KMS",
        "aws_env": "{{aws_env}}",
        "eks_cluster_name": "orch_eks_{{active_region}}_{{aws_env}}",
        "account_id": "{{acct_id}}",
        "regions": {{regions_list}},
        "active_region": ["{{active_region}}"],
        "r53_account": "{{route53acct}}",
        "environment_type": "qa",
        "environment_mode": "fed-high",
        "deployment_stack": "terragrunt"
    }
}