# CODEOWNERS for different sections of the repository

# Catch all rule for all - placed at the top
* @ashih @tepatel @kashankar @athirumalaia @yuhzhang @rparulekar @acalderon @yazaria @fstanley

# [Feature Flag Declaration Owners]
# Assigning ownership for all files under `feature_flag_declarations` to specific users
src/apps/orchestrator/utils/feature_flag_declarations @bsingh @kjoshi
src/apps/orchestrator/utils/feature_flag_declarations/ @bsingh @kjoshi
tests/orchestrator/feature_flag @bsingh @kjoshi
tests/orchestrator/feature_flag/ @bsingh @kjoshi
src/apps/orchestrator/libs/route53 @shbhosale @mjain3 @arajurwar
src/apps/orchestrator/libs/route53/ @shbhosale @mjain3 @arajurwar
tests/orchestrator/libs/route53 @shbhosale @mjain3 @arajurwar
tests/orchestrator/libs/route53/ @shbhosale @mjain3 @arajurwar

#Entrada project
src/apps/orchestrator/zti_controller @fstanley @athirumalaia @ashih
src/apps/orchestrator/zti_controller/ @fstanley @athirumalaia @ashih
src/apps/orchestrator/orchestration_service/core/orchestrator_zti.py @fstanley

# [OCI Cloud owners]
# Assigning ownership for all files under `oci` to specific users
tests/orchestrator/libs/cloud_providers/oci @kashankar @rparulekar @vmudalagiri @yuhzhang @acalderon
tests/orchestrator/libs/cloud_providers/oci/ @kashankar @rparulekar @vmudalagiri @yuhzhang @acalderon
src/apps/orchestrator/provision_service/terraform/oci @kashankar @rparulekar @vmudalagiri @yuhzhang @acalderon
src/apps/orchestrator/provision_service/terraform/oci/ @kashankar @rparulekar @vmudalagiri @yuhzhang @acalderon
src/apps/orchestrator/provision_service/resources/templates/oci @kashankar @rparulekar @vmudalagiri @yuhzhang @acalderon
src/apps/orchestrator/provision_service/resources/templates/oci/ @kashankar @rparulekar @vmudalagiri @yuhzhang @acalderon
src/apps/orchestrator/provision_service/api/deployment/resource/oci @kashankar @rparulekar @vmudalagiri @yuhzhang @acalderon
src/apps/orchestrator/provision_service/api/deployment/resource/oci/ @kashankar @rparulekar @vmudalagiri @yuhzhang @acalderon
src/apps/orchestrator/libs/cloud_providers/oci @kashankar @rparulekar @vmudalagiri @yuhzhang @acalderon
src/apps/orchestrator/libs/cloud_providers/oci/ @kashankar @rparulekar @vmudalagiri @yuhzhang @acalderon
src/apps/orchestrator/libs/go/oci @kashankar @rparulekar @vmudalagiri @yuhzhang @acalderon
src/apps/orchestrator/libs/go/oci/ @kashankar @rparulekar @vmudalagiri @yuhzhang @acalderon 

# RN native-ip
src/apps/orchestrator/orchestration_service/core/orchestrator_onramp_mgmt.py @athangudu @raavula @ytwig
tests/orchestrator/orchestration_service/core/test_orchestrator_onramp_mgmt.py @athangudu @raavula @ytwig