include:
  - project: 'build-tools/ci-templates'
    ref: master
    file: 'checkmarx_validation.yaml'

  - project: 'build-tools/ci-templates'
    ref: master
    file: 'blackduck_validation.yaml'

variables:
  SERVICE_ARTIFACTS_S3_BUCKET_US_WEST_2: cust-provisioning
  GENERIC_LAMBDA_S3_BUCKET_NAME: pantest-lambdas

stages:
  - infosec_validation
  - build_pipeline
  - deploy_pipeline

checkmarx_validation:
  extends: ".checkmarx_validation"
  stage: infosec_validation
  variables:
    ProjectName: "CxServer\\SP\\Company\\prismaaccess\\cosmos\\$CI_PROJECT_NAME"
  only:
    refs:
      - develop
  allow_failure: true

blackduck_validation:
  extends: ".common_blackduck_validation"
  stage: infosec_validation
  before_script:
    - gradle wrapper
  variables:
    SECURE_CICD_BD_IMAGE: "docker-io.art.code.pan.run/gradle:6.7-jdk15"
    SECURE_CICD_BD_VERSION_NAME: "latest"
    SECURE_CICD_PRODUCT_GROUP_NAME: "prismaaccess"
    SECURE_CICD_PRODUCT_TEAM_NAME: "cosmos"
    SECURE_CICD_GROUP_NUMBER: "4469"
    SECURE_CICD_PIPELINE_NAME: "$CI_PROJECT_NAME"
    SECURE_CICD_CI_OWNER: "engprod"
    SECURE_CICD_CI_ENV: "gcp"
    SECURE_CICD_DEPLOY_ENV: "prod"
    SECURE_CICD_BD_TOKEN: "${BD_TOKEN}"
  only:
    refs:
      - develop
  allow_failure: true

build_child_pipeline:
  stage: build_pipeline
  image: docker.art.code.pan.run/build-tools--image-alpine:3.15.0.ep3
  rules:
    - if: $CI_COMMIT_BRANCH == "schadha-dev" || $CI_COMMIT_BRANCH == "develop" || $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "release"
      when: always
  script:
    - apk add -U jsonnet
    - ENV_VARS=$(env | sed 's/^\(\w*\)=.*$/--ext-str \1/g')
    - jsonnet --ext-str CI_COMMIT_BRANCH=$CI_COMMIT_BRANCH --ext-str DEPLOY_ENV=$DEPLOY_ENV gitlab-ci.jsonnet  > generated-config.yml
  artifacts:
    paths:
      - generated-config.yml

trigger_jobs:
  stage: deploy_pipeline
  rules:
    - if: $CI_COMMIT_BRANCH == "schadha-dev" || $CI_COMMIT_BRANCH == "develop" || $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "release"
      when: always
  needs:
    - build_child_pipeline
  trigger:
    include:
      - artifact: generated-config.yml
        job: build_child_pipeline
    strategy: depend