acct_id: {{ cyrus.acct_id }}
aws_partition: {{ cyrus.aws_format }}
acs_gcp_project_id: {{ cyrus.acs_gcp_project_id }}
acs_spanner_env_suffix: {{ cyrus.acs_spanner_env_suffix }}
pa_proxy_config_discovery_fqdn: {{ cyrus.pa_proxy_config_discovery_fqdn }}
pa_proxy_gatekeeper_fqdn: {{ cyrus.pa_proxy_gatekeeper_fqdn }}
pa_telegraph_fqdn: {{ cyrus.pa_telegraph_fqdn }}
pa_cdl_proxy_fqdn: {{ cyrus.pa_cdl_proxy_fqdn }}
eproxy_image_project: {{ cyrus.eproxy_image_project }}
uda_base_zone: {{ cyrus.uda_base_zone }}
rbi_base_zone: {{ cyrus.rbi_base_zone }}
rbi_infra_edl: {{ cyrus.rbi_infra_edl }}
uda_provider_bce: {{ cyrus.uda_provider_bce.jwt_url }}
uda_provider_rbi: {{ cyrus.uda_provider_rbi.jwt_url }}
sasedp_infra_project: {{ cyrus.sasedp_infra_project }}
nhp-proxyprotocol-version: {{ cyrus.nhp_proxyprotocol_version }}
traffic_mirroring_host_project_id: {{ cyrus.traffic_mirroring_host_project_id }}
envoy_ssh_key: {{ cyrus.envoy_ssh_key }}
envoy_traffic_port: 8080
envoy_hc_port: 8080
envoy_inital_cnt: 1
envoy_min_cnt: 1
envoy_max_cnt: 10
envoy_disc_size_gb: 40
envoy_autosclale_cpu_target: 0.70
upstream-proxyprotocol-version: v1
cnat_min_count: 1
cnat_max_count: 16
cnat_mon_enabled: 1
cnat_vm_min_port: 512
cnat_vm_max_port: 32768
auth_proxy_image_version: pa-proxyvm-6-0-0-8-20250313200929-0d438fcf
aws_eproxy_image_version: PA-ProxyVM-6.0.0-473-20250203015803-945eee6
auth_proxy_project_id: {{ cyrus.auth_proxy_project_id }}
auth_proxy_host_project_id: {{ cyrus.auth_proxy_host_project_id}}
auth_proxy_parent_id: {{ cyrus.auth_proxy_parent_id }}
auth_proxy_subnets: {"us-east4": {"primary": "***********/21", "secondary_ranges": [{"in_use": 0, "subnet": "10.0.0.0/24"}, {"in_use": 0, "subnet": "********/24"}, {"in_use": 0, "subnet": "********/24"}, {"in_use": 0, "subnet": "********/24"}, {"in_use": 0, "subnet": "********/24"}, {"in_use": 0, "subnet": "********/24"}, {"in_use": 0, "subnet": "********/24"}, {"in_use": 0, "subnet": "********/24"}, {"in_use": 0, "subnet": "********/24"}, {"in_use": 0, "subnet": "********/24"}, {"in_use": 0, "subnet": "*********/24"}, {"in_use": 0, "subnet": "*********/24"}, {"in_use": 0, "subnet": "*********/24"}, {"in_use": 0, "subnet": "*********/24"}, {"in_use": 0, "subnet": "*********/24"}, {"in_use": 0, "subnet": "*********/24"}, {"in_use": 0, "subnet": "*********/24"}, {"in_use": 0, "subnet": "*********/24"}, {"in_use": 0, "subnet": "*********/24"}, {"in_use": 0, "subnet": "*********/24"}, {"in_use": 0, "subnet": "10.0.20.0/24"}, {"in_use": 0, "subnet": "10.0.21.0/24"}, {"in_use": 0, "subnet": "10.0.22.0/24"}, {"in_use": 0, "subnet": "10.0.23.0/24"}, {"in_use": 0, "subnet": "10.0.24.0/24"}, {"in_use": 0, "subnet": "10.0.25.0/24"}, {"in_use": 0, "subnet": "10.0.26.0/24"}, {"in_use": 0, "subnet": "10.0.27.0/24"}, {"in_use": 0, "subnet": "10.0.28.0/24"}, {"in_use": 0, "subnet": "10.0.29.0/24"}]}, "us-west1": {"primary": "100.72.248.0/21", "secondary_ranges": [{"in_use": 0, "subnet": "10.0.150.0/24"}, {"in_use": 0, "subnet": "10.0.151.0/24"}, {"in_use": 0, "subnet": "10.0.152.0/24"}, {"in_use": 0, "subnet": "10.0.153.0/24"}, {"in_use": 0, "subnet": "10.0.154.0/24"}, {"in_use": 0, "subnet": "10.0.155.0/24"}, {"in_use": 0, "subnet": "10.0.156.0/24"}, {"in_use": 0, "subnet": "10.0.157.0/24"}, {"in_use": 0, "subnet": "10.0.158.0/24"}, {"in_use": 0, "subnet": "10.0.159.0/24"}, {"in_use": 0, "subnet": "10.0.160.0/24"}, {"in_use": 0, "subnet": "10.0.161.0/24"}, {"in_use": 0, "subnet": "10.0.162.0/24"}, {"in_use": 0, "subnet": "10.0.163.0/24"}, {"in_use": 0, "subnet": "10.0.164.0/24"}, {"in_use": 0, "subnet": "10.0.165.0/24"}, {"in_use": 0, "subnet": "10.0.166.0/24"}, {"in_use": 0, "subnet": "10.0.167.0/24"}, {"in_use": 0, "subnet": "10.0.168.0/24"}, {"in_use": 0, "subnet": "10.0.169.0/24"}, {"in_use": 0, "subnet": "10.0.170.0/24"}, {"in_use": 0, "subnet": "10.0.171.0/24"}, {"in_use": 0, "subnet": "10.0.172.0/24"}, {"in_use": 0, "subnet": "10.0.173.0/24"}, {"in_use": 0, "subnet": "10.0.174.0/24"}, {"in_use": 0, "subnet": "10.0.175.0/24"}, {"in_use": 0, "subnet": "10.0.176.0/24"}, {"in_use": 0, "subnet": "10.0.177.0/24"}, {"in_use": 0, "subnet": "10.0.178.0/24"}, {"in_use": 0, "subnet": "10.0.179.0/24"}]}, "europe-west2": {"primary": "100.72.40.0/21", "secondary_ranges": [{"in_use": 0, "subnet": "10.1.44.0/24"}, {"in_use": 0, "subnet": "10.1.45.0/24"}, {"in_use": 0, "subnet": "10.1.46.0/24"}, {"in_use": 0, "subnet": "10.1.47.0/24"}, {"in_use": 0, "subnet": "10.1.48.0/24"}, {"in_use": 0, "subnet": "10.1.49.0/24"}, {"in_use": 0, "subnet": "10.1.50.0/24"}, {"in_use": 0, "subnet": "10.1.51.0/24"}, {"in_use": 0, "subnet": "10.1.52.0/24"}, {"in_use": 0, "subnet": "10.1.53.0/24"}, {"in_use": 0, "subnet": "10.1.54.0/24"}, {"in_use": 0, "subnet": "10.1.55.0/24"}, {"in_use": 0, "subnet": "10.1.56.0/24"}, {"in_use": 0, "subnet": "10.1.57.0/24"}, {"in_use": 0, "subnet": "10.1.58.0/24"}, {"in_use": 0, "subnet": "10.1.59.0/24"}, {"in_use": 0, "subnet": "10.1.60.0/24"}, {"in_use": 0, "subnet": "10.1.61.0/24"}, {"in_use": 0, "subnet": "10.1.62.0/24"}, {"in_use": 0, "subnet": "10.1.63.0/24"}, {"in_use": 0, "subnet": "10.1.64.0/24"}, {"in_use": 0, "subnet": "10.1.65.0/24"}, {"in_use": 0, "subnet": "10.1.66.0/24"}, {"in_use": 0, "subnet": "10.1.67.0/24"}, {"in_use": 0, "subnet": "10.1.68.0/24"}, {"in_use": 0, "subnet": "10.1.69.0/24"}, {"in_use": 0, "subnet": "10.1.70.0/24"}, {"in_use": 0, "subnet": "10.1.71.0/24"}, {"in_use": 0, "subnet": "10.1.72.0/24"}, {"in_use": 0, "subnet": "10.1.73.0/24"}]}, "europe-west3": {"primary": "100.72.48.0/21", "secondary_ranges": [{"in_use": 0, "subnet": "10.1.194.0/24"}, {"in_use": 0, "subnet": "10.1.195.0/24"}, {"in_use": 0, "subnet": "10.1.196.0/24"}, {"in_use": 0, "subnet": "10.1.197.0/24"}, {"in_use": 0, "subnet": "10.1.198.0/24"}, {"in_use": 0, "subnet": "10.1.199.0/24"}, {"in_use": 0, "subnet": "10.1.200.0/24"}, {"in_use": 0, "subnet": "10.1.201.0/24"}, {"in_use": 0, "subnet": "10.1.202.0/24"}, {"in_use": 0, "subnet": "10.1.203.0/24"}, {"in_use": 0, "subnet": "10.1.204.0/24"}, {"in_use": 0, "subnet": "10.1.205.0/24"}, {"in_use": 0, "subnet": "10.1.206.0/24"}, {"in_use": 0, "subnet": "10.1.207.0/24"}, {"in_use": 0, "subnet": "10.1.208.0/24"}, {"in_use": 0, "subnet": "10.1.209.0/24"}, {"in_use": 0, "subnet": "10.1.210.0/24"}, {"in_use": 0, "subnet": "10.1.211.0/24"}, {"in_use": 0, "subnet": "10.1.212.0/24"}, {"in_use": 0, "subnet": "10.1.213.0/24"}, {"in_use": 0, "subnet": "10.1.214.0/24"}, {"in_use": 0, "subnet": "10.1.215.0/24"}, {"in_use": 0, "subnet": "10.1.216.0/24"}, {"in_use": 0, "subnet": "10.1.217.0/24"}, {"in_use": 0, "subnet": "10.1.218.0/24"}, {"in_use": 0, "subnet": "10.1.219.0/24"}, {"in_use": 0, "subnet": "10.1.220.0/24"}, {"in_use": 0, "subnet": "10.1.221.0/24"}, {"in_use": 0, "subnet": "10.1.222.0/24"}, {"in_use": 0, "subnet": "10.1.223.0/24"}]}, "asia-south1": {"primary": "100.72.24.0/21", "secondary_ranges": [{"in_use": 0, "subnet": "10.2.88.0/24"}, {"in_use": 0, "subnet": "10.2.89.0/24"}, {"in_use": 0, "subnet": "10.2.90.0/24"}, {"in_use": 0, "subnet": "10.2.91.0/24"}, {"in_use": 0, "subnet": "10.2.92.0/24"}, {"in_use": 0, "subnet": "10.2.93.0/24"}, {"in_use": 0, "subnet": "10.2.94.0/24"}, {"in_use": 0, "subnet": "10.2.95.0/24"}, {"in_use": 0, "subnet": "10.2.96.0/24"}, {"in_use": 0, "subnet": "10.2.97.0/24"}, {"in_use": 0, "subnet": "10.2.98.0/24"}, {"in_use": 0, "subnet": "10.2.99.0/24"}, {"in_use": 0, "subnet": "10.2.100.0/24"}, {"in_use": 0, "subnet": "10.2.101.0/24"}, {"in_use": 0, "subnet": "10.2.102.0/24"}, {"in_use": 0, "subnet": "10.2.103.0/24"}, {"in_use": 0, "subnet": "10.2.104.0/24"}, {"in_use": 0, "subnet": "10.2.105.0/24"}, {"in_use": 0, "subnet": "**********/24"}, {"in_use": 0, "subnet": "**********/24"}, {"in_use": 0, "subnet": "**********/24"}, {"in_use": 0, "subnet": "**********/24"}, {"in_use": 0, "subnet": "**********/24"}, {"in_use": 0, "subnet": "**********/24"}, {"in_use": 0, "subnet": "**********/24"}, {"in_use": 0, "subnet": "**********/24"}, {"in_use": 0, "subnet": "**********/24"}, {"in_use": 0, "subnet": "**********/24"}, {"in_use": 0, "subnet": "**********/24"}, {"in_use": 0, "subnet": "**********/24"}]}}
ipCidrRange: /31
cnat_ntuple_hash: 2
cnat_exclsv_enabled: 0
cnat_mql_query: "    fetch gce_instance    | metric 'compute.googleapis.com/nat/port_usage'    | filter (resource.zone =~ '{}.*')    | group_by 1m, [value_port_usage_mean: max(value.port_usage)]    | every 1m    | condition val() > {}    "
cnat_alert_email: {{ cyrus.cnat_alert_email }}
cnat_reset_window: 2
cnat_scaleout_factor: 0.6
cnat_threshold_value: 0
cnat_severity: CRITICAL
ep_extrn_sec_range_limit: 8
acs_spanner_instance_id: {{ cyrus.acs_spanner_instance_id }}
aws_env: {{ cyrus.aws_env }}
bucket-name: {{ cyrus.bucket_name }}
cs_endpoint_api: {{ cyrus.cs_endpoint_api }}
valid_file_types: png,sh,gds,tmp,job,mp3,Multi-Level-Encoding,deflate64-zip,msoffice,vb,aip-encrypted-xlsx,eps,tiff,mp4,mht,bat,svg,wmf,ogg,avi-xvid,exr,encrypted-rar,flash,hdf,vba,vbs,pkg,tbz2,mkv,dsn,iwork-numbers,rtf,apk,xll,bzip2,renamed-zip,flv,com,pl,docx,cpp-src,shk,ai,vhdl,tar,encrypted-office2007,macapp,csv,cpp-hdr,aip-encrypted-docx,rpf,asm,iso,webp,psd,cab,access-shortcut,xpm,wmv,wsh,crl,microsoft-shell,sgi,png-upload,mdt,pdf,shb,bmp-upload,encrypted-ppt,prg,rm,tif,iqy,onenote,pptx,dpx,dex,zip,PE,php,mdw,csharp,hta,msc,ops,r,split-rar,avi-divx,mdz,split-cab,asp,vsdm,stp,scr,mda,emf,jsp,pbix,encrypted-pdf,unknown-binary,vhd,webm,mpeg-ts,whl,encrypted-7z,pgp,docm,scf,edif,ppt,chm,mdi,doc,verilog,encrypted-zip,xls,elf,reg,jar,aspx,adp,rvt,lib,gif,c_cpp-src,ace,dwg,pem,lnk,ocx,ruby,txt-upload,encrypted-xls,lzh,pst,sys,mdb,rar,xlsx,softimg,hwpx,iwork-pages,isp,gzip,inf,url,tdb,catpart,any,arj,kdb,hlp,mpeg,mif,ins,woff,vmdk,dwf,bas,msi,jpeg,wsf,encrypted-doc,powershell,cdr,pbm,matlab/obj-c,ma,encrypted-pptx,pcl,ost,vsd,shs,java-src,msp,encrypted-docx,bmp,iff,mach-o,nupkg,7z,ade,c_cpp-hdr,oab,rpm,vbe,iwork-keynote,der,cmd,mpkg,class,cin,zcompressed,py,rla,mb,gif-upload,avi,cpl,dxf,vsdx,its,deb,aip-encrypted-pptx,conda,ichitaro,encrypted-xlsx,torrent,dll,jse,js,exe,vxd,hwp,crx,possible-activex-cab,gadget,pif,mov,jpeg-upload,ico,wri,slk,dmg
cft-bucket-name: {{ cyrus.cft_bucket_name }}
cleanpipe_default_cust_site_cfg:
  sessionAffinity: CLIENT_IP
  bwPerFirewall: 1000
  defaultAdvRouteEnable: True
cloud-xform-bucket: pan-cloud-xform
ca_mgr_info: {{ cyrus.ca_mgr_info }}
pa_proxy_services_ca_key_id: it_ca_cert
it_ca_cert: {{ cyrus.it_ca_cert }}
dbhost: {{ cyrus.dbhost }}
dbhost_ro: {{ cyrus.dbhost_ro }}
dbname: fwaas_1
dbpassword: {{ cyrus.dbpass }}
dbuser: orchestrator
dem_config:
  dem_support_account_id: {{ cyrus.dem_config }}
epaas_sinefa_acct_id: {{ cyrus.epaas_sinefa_acct_id }}
epaas_sinefa_api_gw_fqdn: na3-app.sinefa.com
epaas_sinefa_api_key: {{cyrus.epaas_sinefa_api_key }}
eproxy_outside_panos: {{cyrus.eproxy_outside_panos}}
failure_q:
  attribute:
    ReceiveMessageWaitTimeSeconds: 20
    VisibilityTimeout: 200
  name: failure_q.fifo
fw_ssh_key: {{ cyrus.fw_ssh_key }}
spr_fw_ssh_key: {{ cyrus.spr_fw_ssh_key }}
fwtemplate_no_ha: https://s3-{{ cyrus.aws_region }}.amazonaws.com/{{ cyrus.cft_bucket_name }}/fwtemplate_no_ha.yaml
fwtemplate_sp_no_ha: https://s3-{{ cyrus.aws_region }}.amazonaws.com/{{ cyrus.cft_bucket_name }}/fwtemplate_sp_no_ha.yaml
fwtemplate_fedramp_no_ha: https://s3-{{ cyrus.aws_region }}.amazonaws.com/{{ cyrus.cft_bucket_name }}/fwtemplate_no_ha.yaml
fwtemplate_lz_no_ha: https://s3-{{ cyrus.aws_region }}.amazonaws.com/{{ cyrus.cft_bucket_name }}/fwtemplate_local_zones_no_ha.yaml
fwtemplate_fedramp: https://{{ cyrus.cft_bucket_name }}.s3.amazonaws.com/fwtemplate_fedramp.yaml
fwtemplate: https://{{ cyrus.cft_bucket_name }}.s3.amazonaws.com/fwtemplate.yaml
fwtemplate_mp_instance: https://{{ cyrus.cft_bucket_name }}.s3.amazonaws.com/fwtemplate_mp_instance.yaml
fwtemplate_spot: https://{{ cyrus.cft_bucket_name }}.s3.amazonaws.com/fwtemplate_spot.yaml
gcp_env: {{ cyrus.gcp_orch_project_id_pattern }}
gcp_heartbeat_subnets: **********/16, ***********/22
gcp_host_proj_list: {{ cyrus.gcp_host_proj_list }}
gcp_host_svc_limit: {{ cyrus.gcp_host_svc_limit }}
gcp_img_host: {{ cyrus.gcp_img_host }}
gcp_orch_billing_id: {{ cyrus.gcp_orch_billing_id }}
gcp_orch_folder_id: {{ cyrus.gcp_orch_folder_id }}
gcp_orch_proj_list: {{ cyrus.gcp_orch_proj_list }}
gcp_orch_project_deploy_limit: {{ cyrus.gcp_orch_project_deploy_limit }}
gcp_orch_project_id: orchestrator-gpcs-{{ cyrus.gcp_orch_project_id_pattern }}
gcp_pan_tech_support: techsupport-{{ cyrus.gcp_orch_project_id_pattern }}
gcp_panrepo_bucket: {{ cyrus.gcp_panrepo_bucket }}
gcp_panrepo_role: {{ cyrus.gcp_panrepo_role }}
gcp_proxy_info:
- image: centos-7-v20190326
  imageProj: centos-cloud
  machineType: {{ cyrus.gcp_proxy_machineType }}
  region: us-west1
  ssh-keys: {{ cyrus.gcp_proxy_sshkey }}
  subnet: shared-us-west1-01
  vpc: shared
- image: centos-7-v20190326
  imageProj: centos-cloud
  machineType: {{ cyrus.gcp_proxy_machineType }}
  region: europe-west2
  ssh-keys: {{ cyrus.gcp_proxy_sshkey }}
  subnet: shared-europe-west2-01
  vpc: shared
gcp_syslog_server: {{ cyrus.gcp_syslog_server }}
gcp_tech_support_role: {{ tech_support_role }}
gcp_mgmt_shared_vpc: shared-mgmt-vpc
oci_panrepo_bucket: {{ cyrus.oci_panrepo_bucket }}
oci_tenancy_id: {{ cyrus.oci_tenancy_id }}
oci_pa_tenants_compartment_id: {{ cyrus.oci_pa_tenants_compartment_id }}
gp-gw-domain: {{ cyrus.gp_gw_domain }}
gp-gw-domain-prisma: {{ cyrus.gp_gw_domain_prisma }}
gptemplate: https://{{ cyrus.cft_bucket_name }}.s3.amazonaws.com/gptemplate.yaml
gptemplate_sp: https://{{ cyrus.cft_bucket_name }}.s3.amazonaws.com/gptemplate_sp.yaml
gptemplate_spot: https://{{ cyrus.cft_bucket_name }}.s3.amazonaws.com/gptemplate_spot.yaml
fwtemplate_lz: https://{{ cyrus.cft_bucket_name }}.s3.amazonaws.com/fwtemplate_local_zones.yaml
gptemplate_lz: https://s3-us-west-2.amazonaws.com/{{ cyrus.cft_bucket_name }}/gptemplate_local_zones.yaml
ipsync_host_name: {{ cyrus.ipsync_host_name }}
ipsync_host_name_grpc: {{ cyrus.ipsync_host_name_grpc }}
keys-bucket-name: pan-keys-{{ cyrus.aws_region }}-{{ cyrus.aws_env }}
logging_fqdn: {{ cyrus.logging_fqdn }}
mapbox-api-key: cGsuZXlKMUlqb2ljM0pwYm1GMGFHZDFkSFJwY0dGdWR5SXNJbUVpT2lKamF6a3hkM296YnpZd01tWTVNMlpzWm14MVlXVnZkbVp3SW4wLnNadjNtU3l2Tlo3VEF4T056OGdCQkE=
mdns_ns1_api_key: {{ cyrus.mdns_ns1_api_key }}
mdns_ns1_monitoring_enabled: {{ cyrus.mdns_ns1_monitoring_enabled }}
mdns_ns1_monitoring_regions: dal sin sjc lga ams syd nrt lhr gru
mdns_ns1_monitored_svc_url: http://e2edpc.swg.prismaaccess.com:8888/e2edpc
mdns_ns1_datasource_id: {{ cyrus.mdns_ns1_datasource_id }}
mdns_ns1_notifn_list_id: {{ cyrus.mdns_ns1_notifn_list_id }}
mdns_r53_ep_zone_id: {{cyrus.mdns_r53_ep_zone_id}}
mdns_r53_monitoring_freq: {{cyrus.mdns_r53_monitoring_freq}}
mdns_r53_monitoring_failure_threshold: {{cyrus.mdns_r53_monitoring_failure_threshold}}
mdns_r53_monitoring_regions: us-east-1 us-west-1 us-west-2 eu-west-1 ap-southeast-1 ap-southeast-2 ap-northeast-1 sa-east-1
mdns_r53_monitoring_enabled: {{cyrus.mdns_r53_monitoring_enabled}}
mdns_r53_tcp_monitoring_enabled: {{cyrus.mdns_r53_tcp_monitoring_enabled}}
mdns_r53_monitored_svc_url: {{cyrus.mdns_r53_monitored_svc_url}}
mdns_r53_monitoring_notifn_sns_topic: {{cyrus.mdns_r53_monitoring_notifn_sns_topic}}
nattemplate: https://{{ cyrus.cft_bucket_name }}.s3.amazonaws.com/nattemplate.yaml
notification_q:
  attribute:
    ReceiveMessageWaitTimeSeconds: 20
  name: orchestrator_ntfy_q
orchestrator_role: {{ orchestrator_role }}
pac_files_bucket_name: pac-files-{{ cyrus.aws_region }}-{{ cyrus.aws_env }}
pan_tech_support: pan-technical-support-{{ cyrus.aws_region }}-{{ cyrus.aws_env }}
panrepo_bucket_name: panrepo-{{ cyrus.aws_region }}-{{ cyrus.aws_env }}
allowed-command-bucket-name: pan-allowed-command-{{ cyrus.aws_env }}
queues:
- attribute:
    FifoQueue: True
    ReceiveMessageWaitTimeSeconds: 20
    VisibilityTimeout: 300
  name: onboarding_q.fifo
- attribute:
    FifoQueue: True
    ReceiveMessageWaitTimeSeconds: 20
    VisibilityTimeout: 300
  name: orchestration_q.fifo
- attribute:
    FifoQueue: True
    ReceiveMessageWaitTimeSeconds: 20
    VisibilityTimeout: 300
  name: healthmonitor_q.fifo
- attribute:
    FifoQueue: True
    ReceiveMessageWaitTimeSeconds: 20
    VisibilityTimeout: 300
  name: configuration_q.fifo
- attribute:
    FifoQueue: True
    ReceiveMessageWaitTimeSeconds: 20
    VisibilityTimeout: 300
  name: customer_q.fifo
- attribute:
    FifoQueue: True
    ReceiveMessageWaitTimeSeconds: 20
    VisibilityTimeout: 300
  name: timer_q.fifo
- attribute:
    FifoQueue: True
    ReceiveMessageWaitTimeSeconds: 20
    VisibilityTimeout: 200
  name: cloudformation_q.fifo
- attribute:
    FifoQueue: True
    ReceiveMessageWaitTimeSeconds: 20
    VisibilityTimeout: 300
  name: bringup_q.fifo
- attribute:
    FifoQueue: True
    ReceiveMessageWaitTimeSeconds: 20
    VisibilityTimeout: 300
  name: licensing_q.fifo
- attribute:
    FifoQueue: True
    ReceiveMessageWaitTimeSeconds: 20
    VisibilityTimeout: 200
  name: instance_q.fifo
- attribute:
    FifoQueue: True
    ReceiveMessageWaitTimeSeconds: 20
  name: failure_q.fifo
- attribute:
    FifoQueue: True
    ReceiveMessageWaitTimeSeconds: 20
    VisibilityTimeout: 300
  name: done_q.fifo
region: {{ cyrus.aws_region }}
route53acct: {{ cyrus.route53acct }}
route53region: us-west-2
saas_gpcs_api_endpoint: {{ cyrus.saas_gpcs_api_endpoint }}/api
salt-id: 0x0001
sns_base_arn: 'arn:{{ cyrus.aws_format }}:sns:{{ cyrus.aws_region }}:{{ cyrus.acct_id }}:'
sqs-key: sqs
sqs_base_arn: 'arn:{{ cyrus.aws_format }}:sqs:{{ cyrus.aws_region }}:{{ cyrus.acct_id }}:'
swg_proxy_base_zone: {{ cyrus.swg_proxy_base_zone }}
swg_auth_proxy_base_zone: {{ cyrus.swg_auth_proxy_base_zone }}
eproxytemplate: https://{{ cyrus.cft_bucket_name }}.s3.amazonaws.com/eproxytemplate.yaml
swgtemplate: https://{{ cyrus.cft_bucket_name }}.s3.amazonaws.com/swgtemplate.yaml
swgtemplate_spot: https://{{ cyrus.cft_bucket_name }}.s3.amazonaws.com/swgtemplate_spot.yaml
techdump-key: techdump
topics:
- arn: arn:{{ cyrus.aws_format }}:sns:{{ cyrus.aws_region }}:{{ cyrus.acct_id }}:Customer_topology_change
  name: cust_topology
  next_q: onboarding_q.fifo
- arn: arn:{{ cyrus.aws_format }}:sns:{{ cyrus.aws_region }}:{{ cyrus.acct_id }}:LicenseChange
  name: license_change
  next_q: None
- arn: arn:{{ cyrus.aws_format }}:sns:{{ cyrus.aws_region }}:{{ cyrus.acct_id }}:InstanceReplaceTopic
  name: instance_replace
  next_q: onboarding_q.fifo
- arn: arn:{{ cyrus.aws_format }}:sns:{{ cyrus.aws_region }}:{{ cyrus.acct_id }}:Cust_epaas_change
  name: cust_epaas
  next_q: onboarding_q.fifo
- arn: arn:{{ cyrus.aws_format }}:sns:{{ cyrus.aws_region }}:{{ cyrus.acct_id }}:ep_cloud_nat_scaleout
  name: ep_cnat_scaleout
  next_q: onboarding_q.fifo
- arn: arn:{{ cyrus.aws_format }}:sns:{{ cyrus.aws_region }}:{{ cyrus.acct_id }}:egress_ip_scale_event
  name: egress_ip_scale
  next_q: onboarding_q.fifo
- arn: arn:{{ cyrus.aws_format }}:sns:{{ cyrus.aws_region }}:{{ cyrus.acct_id }}:delete_instance
  name: delete_instance
  next_q: onboarding_q.fifo
- arn: arn:{{ cyrus.aws_format }}:sns:{{ cyrus.aws_region }}:{{ cyrus.acct_id }}:migrate_cluster_to_ipv6
  name: migrate_cluster_to_ipv6
- arn: arn:{{ cyrus.aws_format }}:sns:{{ cyrus.aws_region }}:{{ cyrus.acct_id }}:tenant_ipv6_settings
  name: tenant_ipv6_settings
  next_q: onboarding_q.fifo
- arn: arn:{{ cyrus.aws_format }}:sns:{{ cyrus.aws_region }}:{{ cyrus.acct_id }}:Colo_Onboarding_Topic
  name: colo_vlan_onboard
  next_q: onboarding_q.fifo
- arn: arn:{{ cyrus.aws_format }}:sns:{{ cyrus.aws_region }}:{{ cyrus.acct_id }}:priv_sase_node_update
  name: priv_sase_node_update
  next_q: onboarding_q.fifo
trace_id: orch-{{ app_env }}
upgrade_service:
  log_level: DEBUG
  deployed_env: {{ parent_env }}
  states:
    - name: api
      concurrency: 1
    - name: instance_checker
      concurrency: 1
      poll_interval:
        minutes: 1
    - name: orch_client
      concurrency: 1
      poll_interval:
        minutes: 1
        # Also supports seconds and hours
    - name: ha_sync_mon
      concurrency: 1
      poll_interval:
        minutes: 1
    - name: failover_mon
      concurrency: 1
      poll_interval:
        minutes: 1
    - name: reporting
      concurrency: 5
      poll_interval:
        minutes: 1
  gcp_project_id: {{ cyrus.gcp_host_proj_list.split(',')[0] }}
  google_application_credentials: /home/<USER>/.config/gcloud/application_default_credentials.json
  vertical_scale_pubsub_topic: event_vscale_topic_{{ cyrus.saas_gpcs_api_endpoint }}
  autoscale_pubsub_topic: event_autoscale_topic_{{ cyrus.saas_gpcs_api_endpoint }}
  epcnatscale_pubsub_topic: event_epcnatscale_topic_{{ cyrus.saas_gpcs_api_endpoint }}
  csp_to_pa_alert_pubsub_topic: csp_to_pa_alert_topic_{{ cyrus.saas_gpcs_api_endpoint }}
  epcnatscale_latency: 8
  csp_to_pa_alert_topic_num_shards: 3
terraform_service:
  log_level: DEBUG
  deployed_env: {{ cyrus.gcp_orch_project_id_pattern }}
  states:
    - name: generate_config
      concurrency: 1
      poll_interval:
        hours: 0
        minutes: 1
        seconds: 0
    - name: check_status
      concurrency: 1
      poll_interval:
        hours: 0
        minutes: 0
        seconds: 30
  port: 8443
  azr_tenant_id: 66b66353-3b76-4e41-9dc3-fee328bd400e
  azr_client_id: {{ cyrus.azr_service_account_id }}
  azr_client_secret: {{ cyrus.azr_service_account_secret }}
  organisation: karthik-PAN
  token: {{ cyrus.azr_tf_token }}
azr_mgmt_info:
  resource_group: host-pa-{{ cyrus.gcp_orch_project_id_pattern }}-01-rg
  vnet: mgmt-vnet
  subnet: mgmt-subnet
  syslog_pls_resource_id: /subscriptions/{{ cyrus.azr_host_subscription_id }}/resourceGroups/host-pa-{{ cyrus.gcp_orch_project_id_pattern }}-01-rg/providers/Microsoft.Network/privateLinkServices/sre_mgmt_syslog_pls
  proxy_ssh_keys: {{ cyrus.gcp_proxy_sshkey }}
azr_host_subscription_id: {{ cyrus.azr_host_subscription_id }}
azr_image_subscription_id: {{ cyrus.azr_image_subscription_id }}
azr_image_gallery_name: imagegallerytest
azr_image_resource_group: eho-group
azr_billing_account: ********
azr_enrollment_account: 292909
azr_mgmt_group: PRISMAACCESS-{{ cyrus.gcp_orch_project_id_pattern|upper }}
avisar_service:
  mars_events:
    forwarding_enabled: {{ cyrus.forwarding_enabled_mars }}
    americas:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id }}
    europe:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_eu }}
    uk:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_uk }}
    ca:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_ca }}
    jp:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_japan }}
    au:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_au }}
    de:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_germany }}
    in:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_in }}
    sg:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_singapore }}
    ch:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_ch }}
    fr:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_fr }}
    qa:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_qa }}
    tw:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_tw }}
    kr:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_kr }}
    cn:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_cn }}
    ae:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_ae }}
    il:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_il }}
    id:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_id }}
    sa:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_sa }}
    pl:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_pl }}
    it:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_it }}
    es:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_es }}
    za:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_za }}
    unitedkingdom:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_uk }}
    canada:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_ca }}
    japan:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_japan }}
    australia:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_au }}
    germany:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_germany }}
    india:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_in }}
    singapore:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_singapore }}
    switzerland:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_ch }}
    france:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_fr }}
    qatar:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_qa }}
    taiwan:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_tw }}
    korea:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_kr }}
    china:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_cn }}
    uae:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_ae }}
    israel:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_il }}
    indonesia:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_id }}
    saudiarabia:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_sa }}
    poland:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_pl }}
    italy:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_it }}
    spain:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_es }}
    southafrica:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_za }}
    fedramp:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_fedramp }}
    gov:
      mars_events_topic_name: prisma_sase_alerts
      gcp_project_id: {{ cyrus.gcp_project_id_fedmod }}
  all_events:
    forwarding_enabled: {{ cyrus.forwarding_enabled_all_events }}
    mars_events_topic_name: timeseries_prisma_access_notifications_sink_topic
    gcp_project_id: {{ cyrus.gcp_project_id }}
  vertical_scale_event:
    forwarding_enabled: {{ cyrus.forwarding_enabled_all_events }}
    vertical_scale_topic_name: event_avisar_topic
    gcp_project_id: {{ cyrus.gcp_host_proj_list.split(",")[0] }}
  iot_cert_cleanup:
    receiving_enabled: {{ cyrus.receiving_enabled}}
    listener_sqs_queue_name: msgbus-resource-cleanup.fifo
    listener_sqs_failq_queue_name: msgbus-resource-cleanup-failq.fifo
    aws_account_id: {{ cyrus.acct_id }}
    cron_poll_frequency: 300
  config_commit_alert:
    forwarding_enabled: {{ cyrus.forwarding_enabled_config_commit }}
  key_rotation:
    service_enabled: {{ cyrus.avisar_key_rotation }}
  ngpa_auto_migration:
    service_enabled: {{ cyrus.avisar_ngpa_auto_migration }}
  ipam_events:
    forwarding_enabled: {{ cyrus.forwarding_enabled_all_events }}
    ipam_topic_name: event_avisar_topic
    gcp_project_id: {{ cyrus.gcp_host_proj_list.split(",")[0] }}
avisar_forwarding_enabled: {{ cyrus.avisar_forwarding_enabled }}
avisar_service_endpoint: {{ avisar_service_endpoint }}
aws_env_type: {{ cyrus.aws_env_type }}
r53_access_key_id: {{ cyrus.r53_access_key_id }}
r53_secret_access_key: {{ cyrus.r53_secret_access_key }}
fedramp_dp_acct_id: {{ cyrus.fedramp_dp_acct_id }}
mdns_ns1_tcp_monitoring_enabled: {{ cyrus.mdns_ns1_tcp_monitoring_enabled }}
mdns_ns1_monitoring_freq: {{ cyrus.mdns_ns1_monitoring_freq }}
mdns_ns1_monitoring_connect_timeout: {{ cyrus.mdns_ns1_monitoring_connect_timeout }}
mdns_ns1_monitoring_time_to_connect: {{ cyrus.mdns_ns1_monitoring_time_to_connect }}
mdns_ns1_monitoring_http_idle_timeout: {{ cyrus.mdns_ns1_monitoring_http_idle_timeout }}
mdns_ns1_monitoring_notifn_delay: {{ cyrus.mdns_ns1_monitoring_notifn_delay }}
ep_migration_wait_time: 3600
av_buckets: {{ cyrus.av_buckets }}
av_rpm_buckets: {{ cyrus.av_rpm_buckets }}
content_rpm_buckets: {{ cyrus.content_rpm_buckets }}
provision_url: 'https://{{ cyrus.provision_url }}/orchestrator/provisioning'
openstack_orch_project_id: '{{ cyrus.openstack_orch_project_id }}'
osp_pan_tech_support: '{{ cyrus.osp_pan_tech_support }}'
provision_service:
  log_level: DEBUG
  deployed_env: test
  tf_server: {{ cyrus.tf_server }}
  tf_token: {{ cyrus.tf_token }}
  tf_org: {{ cyrus.tf_org }}
  tf_prj: {{ cyrus.tf_prj }}
  tf_insecure_skip_verify: {{ cyrus.tf_insecure_skip_verify }}
  google_provider_version: {{ cyrus.google_provider_version }}
  openstack:
    deploy_certificate: "{{ cyrus.deploy_certificate }}"
    deploy_credential: "{{ cyrus.deploy_credential }}"
    auth_url: "{{ cyrus.auth_url }}"
    agent_pool_id: "{{ cyrus.agent_pool_id }}"
    execution_mode: "{{ cyrus.execution_mode }}"
  gcp:
    deploy_credential: /orch_aas/libs/google_application_credentials.json
    terraform_version: "{{ cyrus.tf_ver_gcp }}"
    terraform_migration_config:
      auto_migration: {{ cyrus.tf_auto_migration_gcp }}
    new_project_gcp_provision_type: "{{ cyrus.gcp_provision_type_new_project }}"
    new_network_gcp_provision_type: "{{ cyrus.gcp_provision_type_new_network }}"
    usage_query_support: {{ cyrus.usage_query_support_gcp }}
  oci:
    deploy_credential: /orch_aas/libs/oci_credentials.cfg
    terraform_version: "{{ cyrus.tf_ver_oci }}"
  db_simulation: False
  s3_upload: True
  post_processing_url: http://inst-mgmt:8080/api/instance_management/post_process
  api:
    ca_cert_path: {{ cyrus.ca_cert_path }}
    ca_key_path: {{ cyrus.ca_key_path }}
    task_attempt_max: {{ cyrus.prov_svc_task_attempt_max }}
orchestration_service:
  state_import_url: http://provision-service:8080/api/import
  trigger_update_url: http://provision-service:8080/api/instances/triggerUpdate
  new_instance_gcp_provision_type: {{ gcp_provision_type }}
  trigger_update_retry: 3
epm_domain: "{{ cyrus.epm_domain }}"
zti_controller:
  log_level: DEBUG
  deployed_env: {{ parent_env }}
  service:
    host: "0.0.0.0"
    port: 8080
  db_simulation: false
  argo_service_info:
    argo_base_url: "/entrada-create"
    argo_api_path: "http://webhook-entrada-create-eventsource-svc.argocd.svc.cluster.local:10001"
    argo_delete_url: "/entrada-delete"
    argo_delete_api_path: "http://webhook-entrada-delete-eventsource-svc.argocd.svc.cluster.local:10001"
  r53_info:
    route53_acct: "{{ cyrus.route53acct }}"
    entrada_domain: "panclouddev.com"
  dataplane_info:
    gcp_project_id: "sase-entrada-gke-dev-01"
  kro:
    address_type: "EXTERNAL"
    name_suffix: "entrada-ext-ip"