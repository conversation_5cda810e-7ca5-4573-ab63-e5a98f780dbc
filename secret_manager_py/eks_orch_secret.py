from operator import mod
import sys, yaml
import boto3
from jinja2 import Environment, FileSystemLoader, loaders
import os

which_env = sys.argv[1]
region = sys.argv[2]
if which_env == "dev":
    aws_env = "dev1"
else:
    aws_env = which_env

#CYR-50393
aws_env = aws_env.replace('-', '_')
env_access_key =  os.environ.get(sys.argv[3])
env_secret_key =  os.environ.get(sys.argv[4])

#### Check the orchestration mode ####
modeCheckFile  = 'k8s/helmcharts/saasinfra/values_image_cyr-'+which_env+'.yaml'
with open(r''+modeCheckFile) as file:
    modeData = yaml.full_load(file)
    try:
        modeValue = modeData['saasinfra']['deployment_mode']
    except:
        modeValue = "No Mode Set"

if modeValue == "vm":
    env_mode = "vm"
elif modeValue == "eks":
    env_mode = "eks"
else:
    env_mode = modeValue
    
print (env_mode)

file_loader = FileSystemLoader('secret_manager_py/templates')
env = Environment(loader = file_loader,trim_blocks=True,lstrip_blocks=True)
session = boto3.Session(profile_name="default")

def render_template(jinjaFile):
    renderTemplate=env.get_template(jinjaFile)
    if env_mode == "No Mode Set":
        outputValues = renderTemplate.render(access_key=env_access_key, secret_key=env_secret_key)
    else:
        #print(outputValues)
        outputValues = renderTemplate.render(access_key=env_access_key, secret_key=env_secret_key, deployment_mode=env_mode)
    return outputValues

#### Create eks_orch secrets in AWS Secret Manager ####
def create_secrets(secretName):
        print ("\n######### Creating eks-orch secret #########")
        if env_mode == "No Mode Set":
            try:
                secretClient = session.client('secretsmanager', region_name=region)
                response = secretClient.create_secret(Name='eks-orch',SecretString=''+render_template(secretName+"-withoutMode"))
                print ("Secret " +secretName +" created successfully !!!!! ")
            except Exception as e:
                print(f"Secret {secretName} is already present in secret Manager, hence updating the exsisting secret")
                try:
                    update_secrets(secretName)
                except Exception as e:
                    print(f"Error updating secrets: {e}")
                    sys.exit(1)
        else:
            print("EKS Mode is Set to " + env_mode)
            try:
                secretClient = session.client('secretsmanager', region_name=region)
                response = secretClient.create_secret(Name='eks-orch',SecretString=''+render_template(secretName+"-Mode"))
                print ("Secret " +secretName +" created successfully !!!!! ")
            except Exception as e:
                print("Secret is already present in secret Manager, hence updating the exsisting secret")
                try:
                    update_secrets(secretName)
                except Exception as e:
                    print(f"Error updating secrets: {e}")
                    sys.exit(1)

#### update exsisting secrets in AWS Secret Manager ####
def update_secrets(secretName):
        print ("\n######### Updating secret " +secretName +" #########")
        if env_mode == "No Mode Set":
            try:
                secretClient = session.client('secretsmanager', region_name=region)
                kwargs = {'SecretId': secretName}
                kwargs["SecretString"] = render_template(secretName+"-withoutMode")
                response = secretClient.update_secret(**kwargs)
                print("Secret " +secretName +" updated successfully !!!!! ")
            except Exception as e:
                print(e)
                sys.exit(1)
        else:
            try:
                secretClient = session.client('secretsmanager', region_name=region)
                kwargs = {'SecretId': secretName}
                kwargs["SecretString"] = render_template(secretName+"-Mode")
                response = secretClient.update_secret(**kwargs)
                print ("Secret " +secretName +" updated successfully !!!!! ")
            except Exception as e:
                print(e)
                sys.exit(1)

try:
    create_secrets("eks-orch")
except Exception as e:
    print(f"Fatal error: {e}")
    sys.exit(1)
