import sys, yaml
import boto3
from jinja2 import Environment, FileSystemLoader, loaders
import traceback
from botocore.exceptions import ClientError
file_loader = FileSystemLoader('secret_manager_py/templates')
env = Environment(loader = file_loader,trim_blocks=True,lstrip_blocks=True)
which_env = sys.argv[1]
environ_region = which_env
region = sys.argv[2]
if region == 'us-east-1':
    which_env = "{}-{}".format(sys.argv[1], region)
env_type = sys.argv[3]
deployment_stack = sys.argv[4]
print(f"env_type = {env_type}, deployment_stack = {deployment_stack}")
parent_env = env_type
print(f"parent_env = {parent_env}")

session = boto3.Session(profile_name="default")

#### Check the orchestration mode ####
modeCheckFile = 'k8s/helmcharts/saasinfra/values_image_cyr-'+environ_region+'.yaml'
try:
    with open(r''+modeCheckFile) as file:
        modeData = yaml.full_load(file)
        try:
            modeValue = modeData['saasinfra']['deployment_mode']
        except:
            modeValue = "No Mode Set"
except Exception as e:
    print(f"Failed to open mode check file: {e}")
    sys.exit(1)

if modeValue == "vm":
    env_mode = "vm"
    avisar_service_endpoint = "0.0.0.0:50051"
elif modeValue == "eks":
    env_mode = "eks"
    avisar_service_endpoint = "avisar:50051"
else:
    env_mode = modeValue
print(env_mode)

if deployment_stack == 'terragrunt':
    orchestrator_role = "orchestrator-{}".format(region)
    tech_support_role = "roles/storage.admin"
else:
    orchestrator_role = "orchestrator"
    tech_support_role = "organizations/992524860932/roles/GPCSObjectWriter"

#CYR-55060
gcp_provision_type = "terraform_provision"

#### Render values in cfg.py and cfg.yaml from the data yaml file ####
def render_template(jinjaFile):
    env_data_path = f'secret_manager_py/envs/{environ_region}_env_data.yaml'
    try:
        with open(env_data_path, 'r') as file:
            envData = yaml.safe_load(file)
    except Exception as e:
        print(f"Failed to open or parse env data file: {e}")
        sys.exit(1)

    renderTemplate = env.get_template(jinjaFile)
    print(f"renderTemplate = {renderTemplate}")
    print("Found env data yaml...")
    print(f"which_env = {which_env} and parent_env = {parent_env}")
    try:
        for i in envData:
            outputValues = renderTemplate.render(cyrus=envData['cyrus'][which_env], app_env=which_env,
                                             parent_env=parent_env, avisar_service_endpoint=avisar_service_endpoint,
                                             orchestrator_role=orchestrator_role, tech_support_role=tech_support_role,
                                             gcp_provision_type=gcp_provision_type)
        return outputValues
    except Exception as e:
        print(f"Template rendering failed: {e}")
        print(traceback.format_exc())
        sys.exit(1)

#### Create secrets in AWS Secret Manager ####
def create_secrets(secretName):
    print(f"######### Creating secret {secretName} #########")
    try:
        secretClient = session.client('secretsmanager', region_name=region)
        response = secretClient.create_secret(Name=''+secretName,SecretString=''+render_template(secretName))
        print(f"Secret {secretName} created successfully!")
    except ClientError as e:
        if e.response['Error']['Code'] == 'ResourceExistsException':
            print(f"Handled Exception - Secret {secretName} already exists. Updating the existing secret.")
            update_secrets(secretName)
        else:
            print(f"AWS ClientError: {e}")
            sys.exit(1)
    except Exception as ex:
        print(f"Unexpected error: {ex}")
        print(traceback.format_exc())
        # Try update but still exit with error if that fails
        try:
            update_secrets(secretName)
        except:
            sys.exit(1)

#### update existing secrets in AWS Secret Manager ####
def update_secrets(secretName):
    print(f"######### Updating secret {secretName} #########")
    try:
        secretClient = session.client('secretsmanager', region_name=region)
        kwargs = {'SecretId': secretName}
        kwargs["SecretString"] = render_template(secretName)
        response = secretClient.update_secret(**kwargs)
        print(f"Secret {secretName} updated successfully!")
    except Exception as e:
        print(f"Error updating secret: {e}")
        print(traceback.format_exc())
        sys.exit(1)

try:
    create_secrets("cfg.py")
    create_secrets("cfg.yaml")
    print("All secrets created/updated successfully.")
except Exception as e:
    print(f"Failed to create/update secrets: {e}")
    print(traceback.format_exc())
    sys.exit(1)
