cyrus:
  dev15:
    acct_id: 412374631581
    dbhost: fwaasdb-dev15.panclouddev.com
    dbhost_ro: fwaasdb-dev15-ro.panclouddev.com
    dbpass: AQICAHjZYrK3oYeb/F4uQsG4miHmKMs0RM5UQd2HxT+qMq2ijQEdtg6MA5JM3NyVbGZodbV5AAAAaDBmBgkqhkiG9w0BBwagWTBXAgEAMFIGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQM1qRG4X8t7p1lbL7PAgEQgCUcKxcv3umpSsbceycEK3Voui4VIPOZ6ehDvf9NUg7qDsCKxfD/
    gp_gw_domain: panclouddev.com
    cs_endpoint_api: sasedp.panclouddev.com
    gp_gw_domain_prisma: prismaaccesstest.com
    logging_fqdn: api.lcaas.qa.ap.paloaltonetworks.com
    route53acct: a447084568087
    bucket_name: pan-content-dev15
    cft_bucket_name: pan-cft-dev15
    xform_bucket_name: pan-cloud-xform
    saas_gpcs_api_endpoint: dev15.panclouddev.com
    fwaas_version: GPCS-1.0.74
    pa_cdl_proxy_fqdn: dev.cdls.swg.panclouddev.com
    pa_proxy_config_discovery_fqdn: dev.cfgds.swg.panclouddev.com
    pa_proxy_gatekeeper_fqdn: dev.gks.swg.panclouddev.com
    auth_proxy_project_id: pa-authproxy-dev-01
    auth_proxy_host_project_id: host-gpcs-authproxy-dev-01
    auth_proxy_parent_id: 9000000000
    pa_telegraph_fqdn: dev.tgs.swg.prismaaccess.com
    eproxy_image_project: image-gpcs-nonprod-01
    eproxy_outside_panos: 0
    ca_mgr_info: qa:086110123105:orch_secret_manager
    it_ca_cert: 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
    mdns_ns1_api_key: AQICAHjZYrK3oYeb/F4uQsG4miHmKMs0RM5UQd2HxT+qMq2ijQF+qtIbtGiD96MN7P7Fty3tAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQM7q9T4Sdk+Z4/UTLlAgEQgC8YNZPVX8JyRm4StDP3H9SaZsETdMw2Qy6c1U44rtUQqI//QLfDrQoQf6G9vfeX2A==
    mdns_ns1_datasource_id: 0bbc0afe3fe6ee91558f00213ca6cc8d
    mdns_ns1_notifn_list_id: 6306b8d6fa1af7007e566286
    mdns_ns1_monitoring_enabled: 0
    swg_proxy_base_zone: proxy.panclouddev.com
    swg_auth_proxy_base_zone: authproxy.panclouddev.com
    gcp_orch_project_id_pattern: dev
    gcp_orch_folder_id: 614125472925
    gcp_orch_billing_id: 0191EA-C0E00D-70713E
    gcp_orch_project_deploy_limit: 1000
    gcp_host_svc_limit: 2500
    gcp_img_host: image-gpcs-nonprod-01
    gcp_host_proj_list: host-gpcs-dev-01,host-gpcs-dev-02
    gcp_orch_proj_list: orchestrator-gpcs-dev,orchestrator-gpcs-dev-01,orchestrator-gpcs-dev-02
    aws_env: dev15
    gcp_proxy_machineType: e2-micro
    gcp_syslog_server: **************
    gcp_proxy_sshkey: gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8
    fw_ssh_key: gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8
    spr_fw_ssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDKGNN4kmq9HzHWNr56eFdxSpN3AISJzF9FpmI+xlpCMHWg57Xk68gxgCkjxgsIn6NqgGsZFjD1AO7B3uW/Hp2KTpr2qx73z5k0TyoT8bW3xCFsX4eTL4BnxuBqLCrT80aDQUreS9yt5PXmcZt9VEulJ7o6j2VaOvh6ea/suAfuU6gU7Oa/ZkHIyxBUvVZGUcheWn6GGIHszE97abKZ5WYESTu59P5skHox2BDwv2JfJBPJ1xRLMeP40BVlE8udmnVTTa33J+jQ1f3XIPecShSCwzFZ+uBKnLldO0SUaAEmaFykiASZ5wpDuC5KeDvE6tKtAxx23LdeX0crJrm3uGbXjtf+KLq5rHfJ7LdTgI3Y7DmkctHjdlZAqrTeMDA9LXYhxZZdncnEH82BSxF9qFpSI2bVVcZqPysSpUaW0hnSLH3/xKrgK3T7MIsNCdHJlvOs59J1wnXU4VP2T37LI9tMiMiTInEbTyQpe+4EEzfAzcBkZMkjqwN6WPDMzUt+1Lc1UUk+Ibp4bkcytBnAtT/algdmWVno+k49mi3rSVN4mpRm8qoWI+ljF5XWyLQbkL9/HRMoy60cClI/sqNOFc89B/qjZhCuNdls1ZhGFQAppq34icxoFQQi/GLEWy+SC6zXpP9oymXxxz8SNywxEOyWmCfBlDBzxaEI57LADJQmkw== sase-pvt-region@SCMAC140691
    gcp_panrepo_bucket: panrepo-dev
    gcp_panrepo_role: organizations/992524860932/roles/GPCSObjectReader
    oci_panrepo_bucket: panrepo-dev
    oci_tenancy_id: ocid1.tenancy.oc1..aaaaaaaa6i3rfx2bj2u63lrfi57s423rrn7iw6ak3ppa5ehaxnh4tznc3muq
    oci_pa_tenants_compartment_id: ocid1.compartment.oc1..aaaaaaaaxoigp7gscqgq2sgkaqqizwgzwenxcnf7iu3vxlznlteclmngjkea
    dem_config: 31145
    acs_gcp_project_id: pa-acs-infra-dev-01
    acs_spanner_instance_id: acs-non-prod-spanner-instance
    acs_spanner_env_suffix: dev
    epaas_sinefa_acct_id: 28576
    epaas_sinefa_api_key: AQICAHjZYrK3oYeb/F4uQsG4miHmKMs0RM5UQd2HxT+qMq2ijQHtsmR2IdOB96uKNIYaioxBAAAAgzCBgAYJKoZIhvcNAQcGoHMwcQIBADBsBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDHqPcs7/lJWKEXW6vwIBEIA/8xFELkuozbgQkBr+gAklkeVB40ZQpGGI7Up/0sOYbx09LtkL8BLkBLanKnnMu42Y/U3atEUwkocozUBiDavK
    ipsync_host_name: global-dev.ipsync.panclouddev.com
    ipsync_host_name_grpc: dev.grpc-ipsync.swg.panclouddev.com
    azr_service_account_id: b9fca69b-5714-42d9-a854-9488b4b319af
    azr_service_account_secret: AQICAHjZYrK3oYeb/F4uQsG4miHmKMs0RM5UQd2HxT+qMq2ijQGMz4uSUq1PveOhZrenLCbcAAAAgDB+BgkqhkiG9w0BBwagcTBvAgEAMGoGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMgmI6l94FSqr+DGz7AgEQgD2o6RAQQZwmox+EL56wPlUex1qohepuDDZkjZJFyUGvy/ZC5tEVcfL8d9PQhn5EBDi81XL/3ybz2zdgVrFq
    azr_tf_token: AQICAHjZYrK3oYeb/F4uQsG4miHmKMs0RM5UQd2HxT+qMq2ijQFva5eftLDOYPjcXShSze2oAAAAvDCBuQYJKoZIhvcNAQcGoIGrMIGoAgEAMIGiBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDIVvyDF8ucP2E9/cFAIBEIB1pEjIYfTn9tGRQZhNSOUEP3nUiUfUuHH3xJ+MPKEaIrLVtQMGN5fDbWwJZmdNlcv2cqnZkIXdZuH0sFt97xIYAh3CL1ZQTvFFerxBgkiVObLQ8R0r769Yh45OSiUNCJacvt43GAUj5AW/utqxVg+L7gD97a9K
    azr_host_subscription_id: c6fe33ae-5cac-4587-a217-70fcfc8a653e
    azr_image_subscription_id: 2a8831de-b127-4420-824d-ca2077ad4558
    forwarding_enabled_mars: false
    forwarding_enabled_all_events: false
    forwarding_enabled_config_commit: true
    gcp_project_id: pa-sase-insights-dev-02
    gcp_project_id_eu: pa-sase-insights-dev-02
    gcp_project_id_uk: pa-sase-insights-dev-02
    gcp_project_id_fedramp: pa-sase-insights-dev-02
    gcp_project_id_ca: pa-sase-insights-dev-02
    gcp_project_id_japan: pa-sase-insights-dev-02
    gcp_project_id_au: pa-sase-insights-dev-02
    gcp_project_id_germany: pa-sase-insights-dev-02
    gcp_project_id_in: pa-sase-insights-dev-02
    gcp_project_id_singapore: pa-sase-insights-dev-02
    gcp_project_id_ch: pa-sase-insights-dev-02
    gcp_project_id_fr: pa-sase-insights-dev-02
    gcp_project_id_qa: pa-sase-insights-dev-02
    gcp_project_id_tw: pa-sase-insights-dev-02
    gcp_project_id_kr: pa-sase-insights-dev-02
    gcp_project_id_cn: pa-sase-insights-dev-02
    gcp_project_id_ae: pa-sase-insights-dev-02
    gcp_project_id_il: pa-sase-insights-dev-02
    gcp_project_id_id: pa-sase-insights-dev-02
    gcp_project_id_sa: pa-sase-insights-dev-02
    gcp_project_id_pl: pa-sase-insights-dev-02
    gcp_project_id_it: pa-sase-insights-dev-02
    gcp_project_id_es: pa-sase-insights-dev-02
    gcp_project_id_za: pa-sase-insights-dev-02
    gcp_project_id_fedmod: pa-sase-insights-dev-02
    receiving_enabled: false
    avisar_forwarding_enabled: true
    avisar_key_rotation: true
    avisar_ngpa_auto_migration: true
    aws_format: aws
    aws_region: us-west-2
    aws_env_type: commercial
    fedramp_dp_acct_id: ""
    r53_access_key_id: ""
    r53_secret_access_key: ""
    mdns_ns1_tcp_monitoring_enabled: 0
    mdns_ns1_monitoring_freq: 30
    mdns_ns1_monitoring_connect_timeout: 14
    mdns_ns1_monitoring_time_to_connect: 7
    mdns_ns1_monitoring_http_idle_timeout: 3
    mdns_ns1_monitoring_notifn_delay: 15
    mdns_r53_ep_zone_id: ""
    mdns_r53_monitoring_freq: 30
    mdns_r53_monitoring_failure_threshold: 3
    mdns_r53_monitoring_enabled: 0
    mdns_r53_monitored_svc_url: http://e2edpc.swg.prismaaccess.com:8888/e2edpc
    mdns_r53_monitoring_notifn_sns_topic: ""
    mdns_r53_tcp_monitoring_enabled: 0
    sasedp_infra_project: sasedp-infra-dev-01
    uda_provider_rbi:
      jwt_url: https://portal-dev.rbi.panclouddev.com/default/auth/jwks
    uda_provider_bce:
      jwt_url: https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>
    uda_base_zone: uda.panclouddev.com
    rbi_base_zone: rbi.panclouddev.com
    rbi_infra_edl: https://cdn-dev.rbi.panclouddev.com/publicedls/rdi.edl
    nhp_proxyprotocol_version: v2
    openstack_orch_project_id: cust-non-prod
    auth_url: https://non-prod.pa-colo.core.pan.run:5000
    agent_pool_id: apool-tr2T6y98xVKwJBje
    deploy_certificate: /orch_aas/libs/openstack_certificate.crt
    deploy_credential: /orch_aas/libs/openstack_credentials.json
    execution_mode: agent
    av_buckets:
      gcp:
      - project: host-gpcs-dev-01
        bucket_name: av-repo-dev
      aws:
      - region: us-west-2
        bucket_name: av-repo-us-west-2-dev15
      - region: us-east-1
        bucket_name: av-repo-us-east-1-dev15
      oci:
      - region: us-phoenix-1
        bucket_name: av-repo-dev
    content_rpm_buckets:
      gcp:
      - project: host-gpcs-dev-01
        bucket_name: content-rpm-repo-dev
      aws:
      - region: us-west-2
        bucket_name: content-rpm-repo-us-west-2-dev15
      - region: us-east-1
        bucket_name: content-rpm-repo-us-east-1-dev15
      oci:
      - region: us-phoenix-1
        bucket_name: content-rpm-repo-dev
    av_rpm_buckets:
      gcp:
      - project: host-gpcs-dev-01
        bucket_name: av-rpm-repo-dev
      aws:
      - region: us-west-2
        bucket_name: av-rpm-repo-us-west-2-dev15
      - region: us-east-1
        bucket_name: av-rpm-repo-us-east-1-dev15
      oci:
      - region: us-phoenix-1
        bucket_name: av-rpm-repo-dev
    tf_token: ""
    tf_prj: dev15
    tf_org: PANW-SASE-NonProd
    tf_server: https://app.terraform.io/
    tf_ver_gcp: "1.5.7"
    tf_auto_migration_gcp: false
    tf_ver_oci: "1.5.x"
    osp_pan_tech_support: techsupport-dev-openstack
    provision_url: dev15.panclouddev.com
    gcp_provision_type_new_project: deployment_manager
    gcp_provision_type_new_network: deployment_manager
    usage_query_support_gcp: false
    ca_cert_path: lambda_certs/client_cert_fetch_devel.pem
    ca_key_path: lambda_certs/client_cert_fetch_devel.key
    prov_svc_task_attempt_max: 3
    epm_domain: epm.panclouddev.com
    envoy_ssh_key: admin:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r admin@SJCMACJ15HHTD8
    traffic_mirroring_host_project_id: tm-shared-project
    cnat_enabled: 0
    cnat_min_count: 1
    cnat_max_count: 16
    cnat_mon_enabled: 1
    cnat_vm_min_port: 512
    cnat_vm_max_port: 32768
    cnat_ntuple_hash: 2
    cnat_exclsv_enabled: 0
    cnat_alert_email: <EMAIL>
