cyrus:
  prod7:
    acct_id: 690159712765
    dbhost: fwaasdb-prod7.gpcloudservice.com
    dbhost_ro: fwaasdb-prod7-ro.gpcloudservice.com
    dbpass: AQICAHjpv2As00Svit/xougkwpUMqJkQCt//krxpuHoUlHnLRAGK/0jWFTpEYYGxPfWtOzPmAAAAaDBmBgkqhkiG9w0BBwagWTBXAgEAMFIGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQM35h38Fl+OgKjg7jwAgEQgCVB22MBXkBSwpQHcdoGE2lFO1w0z1iNe1wjTiLI2FQtknqwteZw
    gp_gw_domain: gpcloudservice.com
    gp_gw_domain_prisma: prismaaccess.com
    logging_fqdn: lic.lc.prod.us.cs.paloaltonetworks.com
    route53acct: a631680999526
    bucket_name: pan-content-us-west-2-prod7
    cft_bucket_name: pan-cft-us-west-2-prod7
    xform_bucket_name: pan-cloud-xform
    saas_gpcs_api_endpoint: prod7.gpcloudservice.com
    fwaas_version: GPCS-1.0.74
    pa_cdl_proxy_fqdn: prod.cdls.swg.prismaaccess.com
    pa_proxy_config_discovery_fqdn: prod.cfgds.swg.prismaaccess.com
    auth_proxy_project_id: pa-authproxy-prod-01
    cs_endpoint_api: prismaaccess.com
    auth_proxy_host_project_id: host-gpcs-authproxy-prod-01
    auth_proxy_parent_id: 97000000000
    pa_proxy_gatekeeper_fqdn: prod.gks.swg.prismaaccess.com
    pa_telegraph_fqdn: prod.tgs.swg.prismaaccess.com
    eproxy_image_project: image-gpcs-prod-01
    eproxy_outside_panos: 1
    ca_mgr_info: prod:631680999526:orch_secret_manager
    it_ca_cert: 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
    mdns_ns1_api_key: AQICAHjpv2As00Svit/xougkwpUMqJkQCt//krxpuHoUlHnLRAHxl6X2e24x3h2MTm2LMODCAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMS/kKG+/7pxXQKsQIAgEQgC87PUuHjoFoSzpvMPYobm4WPk7lKkI6eLYODrBv+Gv71Xb4iIuoOg1n8TKkZJOh/Q==
    mdns_ns1_datasource_id: d8e52934aaacc31fbeb749904e118279
    mdns_ns1_notifn_list_id: 63094a46d1e4b3108ebc2af0
    mdns_ns1_monitoring_enabled: 1
    swg_proxy_base_zone: proxy.prismaaccess.com
    swg_auth_proxy_base_zone: authproxy.prismaaccess.com
    gcp_orch_project_id_pattern: prod
    gcp_orch_folder_id: 144971859984
    gcp_orch_billing_id: 0191EA-C0E00D-70713E
    gcp_orch_project_deploy_limit: 1000
    gcp_host_svc_limit: 2500
    gcp_img_host: image-gpcs-prod-01
    gcp_host_proj_list: host-gpcs-prod-01,host-gpcs-prod-02,host-gpcs-prod-03
    gcp_orch_proj_list: orchestrator-gpcs-prod,orchestrator-gpcs-prod-02,orchestrator-gpcs-prod-03,orchestrator-gpcs-prod-04
    aws_env: prod7
    gcp_proxy_machineType: e2-standard-2
    gcp_syslog_server: **************
    gcp_proxy_sshkey: gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDklWxFsQWW0wqBa4gnNpumT6O+c55ynw9vJmzGsDdEyjD1vSITFgwIOFDVanOEMLHxdgyRXRqjC769yQ0eG3XVuoRuj5qd2bhoLfYUGU9I1vQ0OsIsLYZEtOZCCcDqDn4GRGYJSd77YkK3rlHeal+CCTrpqyfYjQXeaM9zCZj01AzitgFaDnVJlzQW4MxB/+ivjGgRWRtsM6S1+LoXfvjJE3NstQM7bor9TClYVG3ooPfq2SgwSDgr6FSzvRd9I/pedN9DSFCvqOTDBZjSI514b8n7jx/Qf+N0/Ww+k8muKoEqA2B+mDQvvdpxuVLFK17GxoamzhzoJxb+/Sorp46P gce-user
    fw_ssh_key: gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDklWxFsQWW0wqBa4gnNpumT6O+c55ynw9vJmzGsDdEyjD1vSITFgwIOFDVanOEMLHxdgyRXRqjC769yQ0eG3XVuoRuj5qd2bhoLfYUGU9I1vQ0OsIsLYZEtOZCCcDqDn4GRGYJSd77YkK3rlHeal+CCTrpqyfYjQXeaM9zCZj01AzitgFaDnVJlzQW4MxB/+ivjGgRWRtsM6S1+LoXfvjJE3NstQM7bor9TClYVG3ooPfq2SgwSDgr6FSzvRd9I/pedN9DSFCvqOTDBZjSI514b8n7jx/Qf+N0/Ww+k8muKoEqA2B+mDQvvdpxuVLFK17GxoamzhzoJxb+/Sorp46P gce-user
    spr_fw_ssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCvJfwbfoVbxVS3IWZ0jXxpINSK/6dkfEahIh4z4F8EvDwQyN/ct9r4JDagxrTlsuFds4SHDZYGLSwL0+dAoOvcWl7ZxDSr5DbpZWb6iGDNKFhkdOGIvWXVgQyHP2ScWIjo+I1jV8e9h4OL6KuqtQAlHC59WAc8tNQ4B8+0GaMzEZt0odhaXuCj/TmoJmqWv0Hy03bnDYjc/kER2TeGW+NtwS2++2GeStyWvXsi7cxkLO5RFBbfRBx9z+UA/HxJP52J5HBFLqGeB/MneMbIy35qrnOhz1b59+BGP18IB64vd5dQo+r6BGuCNntEFB+1TE0ystmKfp8PMwHY9MXgfqHuZv4/TzZC08a/S5CtiBuE/FIH7T+Zni7oEK9q8rtlzm5Yy82hpcbNXTDMjVdEqvm6OqBqylpYTtWf1nLj/tMi2RGkfb3PLt8cmkjGbJnesvF94Er1YepBtRimuHddg2/rJD/AuHL2Bo2C0YkW6IqKC2gSx0+FWC31r7tVOdP5KY/M7GDCziauACeJ1rUieUqtYbqqhQC6LWUHSaCe170J4D5q6Sa4steSbSRkTMqQk08DrMhgoOIfUzh0sCEdIv7/cIPYoxMY5rgf0MLzgboP2JvMy9BG0yhhy4NhrWWvC3ua8J6CLdxMb1PCDSi+c6J2H/HMLaCkvyZOGNmuPcBIPQ== sase-pvt-region@SCMAC250593
    gcp_panrepo_bucket: panrepo-prod
    gcp_panrepo_role: organizations/992524860932/roles/GPCSObjectReader
    oci_panrepo_bucket: panrepo-prod
    oci_tenancy_id: ocid1.tenancy.oc1..aaaaaaaag7m24rusohtrjl7uooulfoeah66eiwdy53gis76kc6wywxp362fq
    oci_pa_tenants_compartment_id: ocid1.compartment.oc1..aaaaaaaaxxynuujkk25652dtwvi6jfthnjqptbtsa5r3f42d6tdm22spcxha
    dem_config: 327027
    acs_gcp_project_id: pa-acs-infra-prod-01
    acs_spanner_instance_id: acs-prod-spanner-instance
    acs_spanner_env_suffix: prod
    epaas_sinefa_acct_id: 28574
    epaas_sinefa_api_key: AQICAHjpv2As00Svit/xougkwpUMqJkQCt//krxpuHoUlHnLRAHtjJE1sMDn99+dsycEdCaSAAAAgzCBgAYJKoZIhvcNAQcGoHMwcQIBADBsBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDBNI3KPNvDmSUnlO/AIBEIA/jd+SAYb4XaCkiG54ZZxPDgr74S3Jg0/CQh+blnQGdeN73cq3lZ3m4GEsO6nLp+NOFunfLoVX3hd5zPgrtHX/
    ipsync_host_name: global.ipsync.prismaaccess.com
    ipsync_host_name_grpc: prod.grpc-ipsync.swg.prismaaccess.com
    azr_service_account_id: b9fca69b-5714-42d9-a854-9488b4b319af
    azr_service_account_secret: AQICAHjpv2As00Svit/xougkwpUMqJkQCt//krxpuHoUlHnLRAE2vZd7E1hjiOPsgjy2cc8JAAAAhDCBgQYJKoZIhvcNAQcGoHQwcgIBADBtBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDL4oSCaq/my2wyg7pQIBEIBAy12MbgfBMohp5AB8jCqlgcYpX+eZOv4BDsGEsAfNbQSs8WyqMr+I9o6VQ8Q9CYh/mbo0jbpxiA25E1ZLH0z+YA==
    azr_tf_token: AQICAHjpv2As00Svit/xougkwpUMqJkQCt//krxpuHoUlHnLRAH2AN3A917ZXBi1kw74OtjhAAAAvDCBuQYJKoZIhvcNAQcGoIGrMIGoAgEAMIGiBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDPMIF55UfqirHSs9ewIBEIB1HE3XdFTeb6hMeSXgxQ4yQmoBMwVluYdXslpK+tAbSmDjYfUmADP/KuWoFV20ZqWY3webn5IiXQ0srKKoqdv13aAcNnzAFvhvfOW5qUS0O76cdfHqpiwq5zPl2FlR6X7/6OflsC7oOAU82f/ZBKg+5tn3skwH
    azr_host_subscription_id: fd0d8c05-8c47-4a33-bfac-44f62d3f7945
    azr_image_subscription_id: 2a8831de-b127-4420-824d-ca2077ad4558
    forwarding_enabled_mars: true
    forwarding_enabled_all_events: true
    forwarding_enabled_config_commit: true
    gcp_project_id: pa-sase-insights-prod-01
    gcp_project_id_eu: pa-sase-insights-eu-prod-01
    gcp_project_id_uk: pa-sase-insights-uk-prod-01
    gcp_project_id_fedramp: pa-sase-insights-prod-01
    gcp_project_id_ca: pa-sase-insights-ca-prod-01
    gcp_project_id_japan: pa-sase-insights-jp-prod-01
    gcp_project_id_au: pa-sase-insights-au-prod-01
    gcp_project_id_germany: pa-sase-insights-de-prod-01
    gcp_project_id_in: pa-sase-insights-in-prod-01
    gcp_project_id_singapore: pa-sase-insights-sg-prod-01
    gcp_project_id_ch: pa-sase-insights-ch-prod-01
    gcp_project_id_fr: pa-sase-insights-fr-prod-01
    gcp_project_id_qa: pa-sase-insights-qa-prod-01
    gcp_project_id_tw: pa-sase-insights-tw-prod-01
    gcp_project_id_kr: pa-sase-insights-kr-prod-01
    gcp_project_id_cn: pa-sase-insights-cn-prod-01
    gcp_project_id_ae: pa-sase-insights-ae-prod-01
    gcp_project_id_il: pa-sase-insights-il-prod-01
    gcp_project_id_id: pa-sase-insights-id-prod-01
    gcp_project_id_sa: pa-sase-insights-sa-prod-01
    gcp_project_id_pl: pa-sase-insights-pl-prod-01
    gcp_project_id_it: pa-sase-insights-it-prod-01
    gcp_project_id_es: pa-sase-insights-es-prod-01
    gcp_project_id_es: pa-sase-insights-za-prod-01
    gcp_project_id_fedmod: pa-sase-insights-prod-01
    receiving_enabled: false
    avisar_forwarding_enabled: true
    avisar_key_rotation: true
    avisar_ngpa_auto_migration: true
    aws_format: aws
    aws_region: us-west-2
    aws_env_type: commercial
    fedramp_dp_acct_id: ""
    r53_access_key_id: ""
    r53_secret_access_key: ""
    mdns_ns1_tcp_monitoring_enabled: 1
    mdns_ns1_monitoring_freq: 30
    mdns_ns1_monitoring_connect_timeout: 14
    mdns_ns1_monitoring_time_to_connect: 7
    mdns_ns1_monitoring_http_idle_timeout: 3
    mdns_ns1_monitoring_notifn_delay: 15
    mdns_r53_ep_zone_id: ""
    mdns_r53_monitoring_freq: 30
    mdns_r53_monitoring_failure_threshold: 3
    mdns_r53_monitoring_enabled: 0
    mdns_r53_monitored_svc_url: http://e2edpc.swg.prismaaccess.com:8888/e2edpc
    mdns_r53_monitoring_notifn_sns_topic: ""
    mdns_r53_tcp_monitoring_enabled: 0
    sasedp_infra_project: sasedp-infra-prod-01
    uda_provider_rbi:
      jwt_url: https://portal-prod.rbi.io/default/auth/jwks
    uda_provider_bce:
      jwt_url: https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>
    uda_base_zone: uda.prismaaccess.com
    rbi_base_zone: rbi.io
    rbi_infra_edl: https://cdn-dev.rbi.panclouddev.com/publicedls/rpi.edl
    nhp_proxyprotocol_version: v2
    openstack_orch_project_id: cust-prod
    auth_url: ""
    agent_pool_id: ""
    deploy_certificate: ""
    deploy_credential: ""
    execution_mode: ""
    av_buckets:
      gcp:
      - project: host-gpcs-prod-01
        bucket_name: av-repo-prod
      aws:
      - region: us-west-2
        bucket_name: av-repo-us-west-2-prod7
      - region: us-east-1
        bucket_name: av-repo-us-east-1-prod7
      oci:
      - region: us-phoenix-1
        bucket_name: av-repo-prod
    content_rpm_buckets:
      gcp:
      - project: host-gpcs-prod-01
        bucket_name: content-rpm-repo-prod
      aws:
      - region: us-west-2
        bucket_name: content-rpm-repo-us-west-2-prod7
      - region: us-east-1
        bucket_name: content-rpm-repo-us-east-1-prod7
      oci:
      - region: us-phoenix-1
        bucket_name: content-rpm-repo-prod
    av_rpm_buckets:
      gcp:
      - project: host-gpcs-prod-01
        bucket_name: av-rpm-repo-prod
      aws:
      - region: us-west-2
        bucket_name: av-rpm-repo-us-west-2-prod7
      - region: us-east-1
        bucket_name: av-rpm-repo-us-east-1-prod7
      oci:
      - region: us-phoenix-1
        bucket_name: av-rpm-repo-prod
    tf_token: ""
    tf_prj: prod7
    tf_org: PANW-SASE-Prod
    tf_server: https://app.terraform.io/
    tf_ver_gcp: "1.5.7"
    tf_auto_migration_gcp: false
    tf_ver_oci: "1.5.x"
    osp_pan_tech_support: techsupport-dev-openstack
    provision_url: ""
    gcp_provision_type_new_project: deployment_manager
    gcp_provision_type_new_network: deployment_manager
    usage_query_support_gcp: false
    ca_cert_path: lambda_certs/client_cert_fetch_prod.pem
    ca_key_path: lambda_certs/client_cert_fetch_prod.key
    prov_svc_task_attempt_max: 3
    epm_domain: epm.gpcloudservice.com
    cnat_alert_email: <EMAIL>
    envoy_ssh_key: admin:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDklWxFsQWW0wqBa4gnNpumT6O+c55ynw9vJmzGsDdEyjD1vSITFgwIOFDVanOEMLHxdgyRXRqjC769yQ0eG3XVuoRuj5qd2bhoLfYUGU9I1vQ0OsIsLYZEtOZCCcDqDn4GRGYJSd77YkK3rlHeal+CCTrpqyfYjQXeaM9zCZj01AzitgFaDnVJlzQW4MxB/+ivjGgRWRtsM6S1+LoXfvjJE3NstQM7bor9TClYVG3ooPfq2SgwSDgr6FSzvRd9I/pedN9DSFCvqOTDBZjSI514b8n7jx/Qf+N0/Ww+k8muKoEqA2B+mDQvvdpxuVLFK17GxoamzhzoJxb+/Sorp46P admin
    traffic_mirroring_host_project_id: tm-shared-gpcs-prod
  prod7-us-east-1:
    acct_id: 690159712765
    dbhost: fwaasdb-prod7.gpcloudservice.com
    dbhost_ro: fwaasdb-prod7-ro.gpcloudservice.com
    dbpass: AQICAHjpv2As00Svit/xougkwpUMqJkQCt//krxpuHoUlHnLRAGK/0jWFTpEYYGxPfWtOzPmAAAAaDBmBgkqhkiG9w0BBwagWTBXAgEAMFIGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQM35h38Fl+OgKjg7jwAgEQgCVB22MBXkBSwpQHcdoGE2lFO1w0z1iNe1wjTiLI2FQtknqwteZw
    gp_gw_domain: gpcloudservice.com
    gp_gw_domain_prisma: prismaaccess.com
    logging_fqdn: lic.lc.prod.us.cs.paloaltonetworks.com
    route53acct: a631680999526
    bucket_name: pan-content-us-east-1-prod7
    cft_bucket_name: pan-cft-us-east-1-prod7
    xform_bucket_name: pan-cloud-xform
    saas_gpcs_api_endpoint: prod7.gpcloudservice.com
    fwaas_version: GPCS-1.0.74
    pa_cdl_proxy_fqdn: prod.cdls.swg.prismaaccess.com
    pa_proxy_config_discovery_fqdn: prod.cfgds.swg.prismaaccess.com
    auth_proxy_project_id: pa-authproxy-prod-01
    auth_proxy_host_project_id: host-gpcs-authproxy-prod-01
    auth_proxy_parent_id: 97000000000
    pa_proxy_gatekeeper_fqdn: prod.gks.swg.prismaaccess.com
    pa_telegraph_fqdn: prod.tgs.swg.prismaaccess.com
    eproxy_image_project: image-gpcs-prod-01
    ca_mgr_info: prod:631680999526:orch_secret_manager
    it_ca_cert: 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
    mdns_ns1_api_key: AQICAHjpv2As00Svit/xougkwpUMqJkQCt//krxpuHoUlHnLRAHxl6X2e24x3h2MTm2LMODCAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMS/kKG+/7pxXQKsQIAgEQgC87PUuHjoFoSzpvMPYobm4WPk7lKkI6eLYODrBv+Gv71Xb4iIuoOg1n8TKkZJOh/Q==
    mdns_ns1_datasource_id: d8e52934aaacc31fbeb749904e118279
    mdns_ns1_notifn_list_id: 63094a46d1e4b3108ebc2af0
    mdns_ns1_monitoring_enabled: 1
    swg_proxy_base_zone: proxy.prismaaccess.com
    swg_auth_proxy_base_zone: authproxy.prismaaccess.com
    gcp_orch_project_id_pattern: prod
    gcp_orch_folder_id: 144971859984
    gcp_orch_billing_id: 0191EA-C0E00D-70713E
    gcp_orch_project_deploy_limit: 1000
    gcp_host_svc_limit: 2500
    gcp_img_host: image-gpcs-prod-01
    gcp_host_proj_list: host-gpcs-prod-01,host-gpcs-prod-02,host-gpcs-prod-03
    gcp_orch_proj_list: orchestrator-gpcs-prod,orchestrator-gpcs-prod-02,orchestrator-gpcs-prod-03,orchestrator-gpcs-prod-04
    aws_env: prod7
    gcp_proxy_machineType: e2-standard-2
    gcp_syslog_server: **************
    gcp_proxy_sshkey: gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDklWxFsQWW0wqBa4gnNpumT6O+c55ynw9vJmzGsDdEyjD1vSITFgwIOFDVanOEMLHxdgyRXRqjC769yQ0eG3XVuoRuj5qd2bhoLfYUGU9I1vQ0OsIsLYZEtOZCCcDqDn4GRGYJSd77YkK3rlHeal+CCTrpqyfYjQXeaM9zCZj01AzitgFaDnVJlzQW4MxB/+ivjGgRWRtsM6S1+LoXfvjJE3NstQM7bor9TClYVG3ooPfq2SgwSDgr6FSzvRd9I/pedN9DSFCvqOTDBZjSI514b8n7jx/Qf+N0/Ww+k8muKoEqA2B+mDQvvdpxuVLFK17GxoamzhzoJxb+/Sorp46P gce-user
    fw_ssh_key: gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDklWxFsQWW0wqBa4gnNpumT6O+c55ynw9vJmzGsDdEyjD1vSITFgwIOFDVanOEMLHxdgyRXRqjC769yQ0eG3XVuoRuj5qd2bhoLfYUGU9I1vQ0OsIsLYZEtOZCCcDqDn4GRGYJSd77YkK3rlHeal+CCTrpqyfYjQXeaM9zCZj01AzitgFaDnVJlzQW4MxB/+ivjGgRWRtsM6S1+LoXfvjJE3NstQM7bor9TClYVG3ooPfq2SgwSDgr6FSzvRd9I/pedN9DSFCvqOTDBZjSI514b8n7jx/Qf+N0/Ww+k8muKoEqA2B+mDQvvdpxuVLFK17GxoamzhzoJxb+/Sorp46P gce-user
    spr_fw_ssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCvJfwbfoVbxVS3IWZ0jXxpINSK/6dkfEahIh4z4F8EvDwQyN/ct9r4JDagxrTlsuFds4SHDZYGLSwL0+dAoOvcWl7ZxDSr5DbpZWb6iGDNKFhkdOGIvWXVgQyHP2ScWIjo+I1jV8e9h4OL6KuqtQAlHC59WAc8tNQ4B8+0GaMzEZt0odhaXuCj/TmoJmqWv0Hy03bnDYjc/kER2TeGW+NtwS2++2GeStyWvXsi7cxkLO5RFBbfRBx9z+UA/HxJP52J5HBFLqGeB/MneMbIy35qrnOhz1b59+BGP18IB64vd5dQo+r6BGuCNntEFB+1TE0ystmKfp8PMwHY9MXgfqHuZv4/TzZC08a/S5CtiBuE/FIH7T+Zni7oEK9q8rtlzm5Yy82hpcbNXTDMjVdEqvm6OqBqylpYTtWf1nLj/tMi2RGkfb3PLt8cmkjGbJnesvF94Er1YepBtRimuHddg2/rJD/AuHL2Bo2C0YkW6IqKC2gSx0+FWC31r7tVOdP5KY/M7GDCziauACeJ1rUieUqtYbqqhQC6LWUHSaCe170J4D5q6Sa4steSbSRkTMqQk08DrMhgoOIfUzh0sCEdIv7/cIPYoxMY5rgf0MLzgboP2JvMy9BG0yhhy4NhrWWvC3ua8J6CLdxMb1PCDSi+c6J2H/HMLaCkvyZOGNmuPcBIPQ== sase-pvt-region@SCMAC250593
    gcp_panrepo_bucket: panrepo-prod
    gcp_panrepo_role: organizations/992524860932/roles/GPCSObjectReader
    oci_panrepo_bucket: panrepo-prod
    oci_tenancy_id: ocid1.tenancy.oc1..aaaaaaaag7m24rusohtrjl7uooulfoeah66eiwdy53gis76kc6wywxp362fq
    oci_pa_tenants_compartment_id: ocid1.compartment.oc1..aaaaaaaaxxynuujkk25652dtwvi6jfthnjqptbtsa5r3f42d6tdm22spcxha
    dem_config: 327027
    acs_gcp_project_id: pa-acs-infra-prod-01
    acs_spanner_instance_id: acs-prod-spanner-instance
    acs_spanner_env_suffix: prod
    epaas_sinefa_acct_id: 28574
    epaas_sinefa_api_key: AQICAHjpv2As00Svit/xougkwpUMqJkQCt//krxpuHoUlHnLRAHtjJE1sMDn99+dsycEdCaSAAAAgzCBgAYJKoZIhvcNAQcGoHMwcQIBADBsBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDBNI3KPNvDmSUnlO/AIBEIA/jd+SAYb4XaCkiG54ZZxPDgr74S3Jg0/CQh+blnQGdeN73cq3lZ3m4GEsO6nLp+NOFunfLoVX3hd5zPgrtHX/
    ipsync_host_name: global.ipsync.prismaaccess.com
    ipsync_host_name_grpc: prod.grpc-ipsync.swg.prismaaccess.com
    azr_service_account_id: b9fca69b-5714-42d9-a854-9488b4b319af
    azr_service_account_secret: AQICAHjpv2As00Svit/xougkwpUMqJkQCt//krxpuHoUlHnLRAE2vZd7E1hjiOPsgjy2cc8JAAAAhDCBgQYJKoZIhvcNAQcGoHQwcgIBADBtBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDL4oSCaq/my2wyg7pQIBEIBAy12MbgfBMohp5AB8jCqlgcYpX+eZOv4BDsGEsAfNbQSs8WyqMr+I9o6VQ8Q9CYh/mbo0jbpxiA25E1ZLH0z+YA==
    azr_tf_token: AQICAHjpv2As00Svit/xougkwpUMqJkQCt//krxpuHoUlHnLRAH2AN3A917ZXBi1kw74OtjhAAAAvDCBuQYJKoZIhvcNAQcGoIGrMIGoAgEAMIGiBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDPMIF55UfqirHSs9ewIBEIB1HE3XdFTeb6hMeSXgxQ4yQmoBMwVluYdXslpK+tAbSmDjYfUmADP/KuWoFV20ZqWY3webn5IiXQ0srKKoqdv13aAcNnzAFvhvfOW5qUS0O76cdfHqpiwq5zPl2FlR6X7/6OflsC7oOAU82f/ZBKg+5tn3skwH
    azr_host_subscription_id: fd0d8c05-8c47-4a33-bfac-44f62d3f7945
    azr_image_subscription_id: 2a8831de-b127-4420-824d-ca2077ad4558
    forwarding_enabled_mars: true
    forwarding_enabled_all_events: true
    forwarding_enabled_config_commit: true
    gcp_project_id: pa-sase-insights-prod-01
    gcp_project_id_eu: pa-sase-insights-eu-prod-01
    gcp_project_id_uk: pa-sase-insights-uk-prod-01
    gcp_project_id_fedramp: pa-sase-insights-prod-01
    gcp_project_id_ca: pa-sase-insights-ca-prod-01
    gcp_project_id_japan: pa-sase-insights-jp-prod-01
    gcp_project_id_au: pa-sase-insights-au-prod-01
    gcp_project_id_germany: pa-sase-insights-de-prod-01
    gcp_project_id_in: pa-sase-insights-in-prod-01
    gcp_project_id_singapore: pa-sase-insights-sg-prod-01
    gcp_project_id_ch: pa-sase-insights-ch-prod-01
    gcp_project_id_fr: pa-sase-insights-fr-prod-01
    gcp_project_id_qa: pa-sase-insights-qa-prod-01
    gcp_project_id_tw: pa-sase-insights-tw-prod-01
    gcp_project_id_kr: pa-sase-insights-kr-prod-01
    gcp_project_id_cn: pa-sase-insights-cn-prod-01
    gcp_project_id_ae: pa-sase-insights-ae-prod-01
    gcp_project_id_il: pa-sase-insights-il-prod-01
    gcp_project_id_id: pa-sase-insights-id-prod-01
    gcp_project_id_sa: pa-sase-insights-sa-prod-01
    gcp_project_id_pl: pa-sase-insights-pl-prod-01
    gcp_project_id_it: pa-sase-insights-it-prod-01
    gcp_project_id_es: pa-sase-insights-es-prod-01
    gcp_project_id_es: pa-sase-insights-za-prod-01
    gcp_project_id_fedmod: pa-sase-insights-prod-01
    receiving_enabled: false
    avisar_forwarding_enabled: true
    avisar_key_rotation: true
    avisar_ngpa_auto_migration: true
    aws_format: aws
    aws_region: us-east-1
    aws_env_type: commercial
    fedramp_dp_acct_id: ""
    r53_access_key_id: ""
    r53_secret_access_key: ""
    mdns_ns1_tcp_monitoring_enabled: 1
    mdns_ns1_monitoring_freq: 30
    mdns_ns1_monitoring_connect_timeout: 14
    mdns_ns1_monitoring_time_to_connect: 7
    mdns_ns1_monitoring_http_idle_timeout: 3
    mdns_ns1_monitoring_notifn_delay: 15
    mdns_r53_ep_zone_id: ""
    mdns_r53_monitoring_freq: 30
    mdns_r53_monitoring_failure_threshold: 3
    mdns_r53_monitoring_enabled: 0
    mdns_r53_monitored_svc_url: http://e2edpc.swg.prismaaccess.com:8888/e2edpc
    mdns_r53_monitoring_notifn_sns_topic: ""
    mdns_r53_tcp_monitoring_enabled: 0
    uda_base_zone: uda.prismaaccess.com
    rbi_base_zone: rbi.io
    rbi_infra_edl: https://cdn-dev.rbi.panclouddev.com/publicedls/rpi.edl
    nhp-proxyprotocol-version: v2
    openstack_orch_project_id: cust-prod
    auth_url: ""
    agent_pool_id: ""
    deploy_certificate: ""
    deploy_credential: ""
    execution_mode: ""
    av_buckets:
      gcp:
      - project: host-gpcs-prod-01
        bucket_name: av-repo-prod
      aws:
      - region: us-west-2
        bucket_name: av-repo-us-west-2-prod7
      - region: us-east-1
        bucket_name: av-repo-us-east-1-prod7
      oci:
      - region: us-phoenix-1
        bucket_name: av-repo-prod
    content_rpm_buckets:
      gcp:
      - project: host-gpcs-prod-01
        bucket_name: content-rpm-repo-prod
      aws:
      - region: us-west-2
        bucket_name: content-rpm-repo-us-west-2-prod7
      - region: us-east-1
        bucket_name: content-rpm-repo-us-east-1-prod7
      oci:
      - region: us-phoenix-1
        bucket_name: content-rpm-repo-prod
    av_rpm_buckets:
      gcp:
      - project: host-gpcs-prod-01
        bucket_name: av-rpm-repo-prod
      aws:
      - region: us-west-2
        bucket_name: av-rpm-repo-us-west-2-prod7
      - region: us-east-1
        bucket_name: av-rpm-repo-us-east-1-prod7
      oci:
      - region: us-phoenix-1
        bucket_name: av-rpm-repo-prod
    tf_token: ""
    tf_prj: prod7
    tf_org: PANW-SASE-Prod
    tf_server: https://app.terraform.io/
    tf_ver_gcp: "1.5.7"
    tf_auto_migration_gcp: false
    tf_ver_oci: "1.5.x"
    osp_pan_tech_support: techsupport-dev-openstack
    provision_url: ""
    gcp_provision_type_new_project: deployment_manager
    gcp_provision_type_new_network: deployment_manager
    usage_query_support_gcp: false
    ca_cert_path: lambda_certs/client_cert_fetch_prod.pem
    ca_key_path: lambda_certs/client_cert_fetch_prod.key
    prov_svc_task_attempt_max: 3
    epm_domain: epm.gpcloudservice.com
    envoy_ssh_key: admin:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDklWxFsQWW0wqBa4gnNpumT6O+c55ynw9vJmzGsDdEyjD1vSITFgwIOFDVanOEMLHxdgyRXRqjC769yQ0eG3XVuoRuj5qd2bhoLfYUGU9I1vQ0OsIsLYZEtOZCCcDqDn4GRGYJSd77YkK3rlHeal+CCTrpqyfYjQXeaM9zCZj01AzitgFaDnVJlzQW4MxB/+ivjGgRWRtsM6S1+LoXfvjJE3NstQM7bor9TClYVG3ooPfq2SgwSDgr6FSzvRd9I/pedN9DSFCvqOTDBZjSI514b8n7jx/Qf+N0/Ww+k8muKoEqA2B+mDQvvdpxuVLFK17GxoamzhzoJxb+/Sorp46P admin
    traffic_mirroring_host_project_id: tm-shared-gpcs-prod
    cnat_alert_email: <EMAIL>
    cnat_enabled: 0
    cnat_min_count: 1
    cnat_max_count: 16
    cnat_mon_enabled: 1
    cnat_vm_min_port: 512
    cnat_vm_max_port: 32768
    cnat_ntuple_hash: 2
    cnat_exclsv_enabled: 0
