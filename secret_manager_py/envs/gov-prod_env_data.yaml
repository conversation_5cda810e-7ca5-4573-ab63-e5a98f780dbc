cyrus:
  gov-prod:
    acct_id: ************
    dbhost: fwaasdb-gov-prod.fed.prismaaccess.com
    dbhost_ro: fwaasdb-gov-prod-ro.fed.prismaaccess.com
    dbpass: AQICAHj1T/bG0yLNYh0iQn0MsUJ2mV5nlb2DzDLGyO6/BRO5aAFUBz0NAHnKZlj9N2wJAUS4AAAAaDBmBgkqhkiG9w0BBwagWTBXAgEAMFIGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMxkTKlObdeSbAvK0uAgEQgCVrx3hGkS3jmzfCvWKUy0ultLxMoxV8w9q9XjRg1LLkWR1JTIZk
    gp_gw_domain: fed.prismaaccess.com
    gp_gw_domain_prisma: fed.prismaaccess.com
    cs_endpoint_api: prismaaccess.com
    logging_fqdn: lic.lc.prod.us.cs.paloaltonetworks.com
    route53acct: a************
    bucket_name: pan-content-us-west-2-gov-prod
    cft_bucket_name: pan-cft-us-west-2-gov-prod
    xform_bucket_name: pan-cloud-xform
    saas_gpcs_api_endpoint: api.fed.prismaaccess.com
    fwaas_version: GPCS-1.0.74
    pa_cdl_proxy_fqdn: prod.cdls.swg.fed.prismaaccess.com
    pa_proxy_config_discovery_fqdn: prod.cfgds.swg.fed.prismaaccess.com
    pa_proxy_gatekeeper_fqdn: prod.gks.swg.fed.prismaaccess.com
    pa_telegraph_fqdn: prod.tgs.swg.fed.prismaaccess.com
    eproxy_image_project: gov-image-gpcs-prod-01
    eproxy_outside_panos: 1
    ca_mgr_info: "prod:631680999526:orch_secret_manager"
    it_ca_cert: 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
    mdns_ns1_api_key: AQICAHj1T/bG0yLNYh0iQn0MsUJ2mV5nlb2DzDLGyO6/BRO5aAGguXrGgV222bRUkRTaEJzuAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMyUkI2i+U4oSawTCXAgEQgC9dvt8Su7l87T0RXJN4Fj1GFtVUdghvAxWu7zNFelUIz/Xz8yTj0RwYZBvdK6ejJA==
    mdns_ns1_datasource_id: d8e52934aaacc31fbeb749904e118279
    mdns_ns1_notifn_list_id: 63094a46d1e4b3108ebc2af0
    mdns_ns1_monitoring_enabled: 1
    swg_proxy_base_zone: proxy.fed.prismaaccess.com
    gcp_orch_project_id_pattern: gov-prod
    gcp_orch_folder_id: 1062482435126
    gcp_orch_billing_id: 0191EA-C0E00D-70713E
    gcp_orch_project_deploy_limit: 1000
    gcp_host_svc_limit: 2500
    gcp_img_host: gov-image-gpcs-prod-01
    gcp_host_proj_list: gov-host-gpcs-prod-01
    gcp_orch_proj_list: gov-orchestrator-gpcs-prod
    agent_pool_id: ""
    aws_env: gov-prod
    gcp_proxy_machineType: n1-standard-2
    gcp_syslog_server: ************
    gcp_proxy_sshkey: gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQD3MGS6oZezshEgLfs6EGH36dGkfvnT6o+H42v1/fwBwZ7Ht52F0OClWM0fdYeeSegXdKVyZDa7yu7uG+GZTUjqO+PnXzKdOsshuEAAz2iE6Sbq/wkNh9XiA5Tt9DCtCjw57yJmtBymcT6xzMz/C05tdUQQf2dz0OS6lqZ8rtJcOn8tBvOD5fCvtMlTgnYEkPJ5RSfQVMOwx+XOAdcos2kinbh1UC8HkJpTVs0mf/Qg1VzymOtKXWNKPKksD1MasEe83zj4RWD973tDSIWloEcAWe4HAbEU2B3U1N20mIJdpgI3GeJYfVpMW8kgwzBr261FzES3XbkTHcG+vt0OcBEv gce-user
    fw_ssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQD3MGS6oZezshEgLfs6EGH36dGkfvnT6o+H42v1/fwBwZ7Ht52F0OClWM0fdYeeSegXdKVyZDa7yu7uG+GZTUjqO+PnXzKdOsshuEAAz2iE6Sbq/wkNh9XiA5Tt9DCtCjw57yJmtBymcT6xzMz/C05tdUQQf2dz0OS6lqZ8rtJcOn8tBvOD5fCvtMlTgnYEkPJ5RSfQVMOwx+XOAdcos2kinbh1UC8HkJpTVs0mf/Qg1VzymOtKXWNKPKksD1MasEe83zj4RWD973tDSIWloEcAWe4HAbEU2B3U1N20mIJdpgI3GeJYfVpMW8kgwzBr261FzES3XbkTHcG+vt0OcBEv gce-user@gpcs-sre-prod-jbox-01
    spr_fw_ssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCvJfwbfoVbxVS3IWZ0jXxpINSK/6dkfEahIh4z4F8EvDwQyN/ct9r4JDagxrTlsuFds4SHDZYGLSwL0+dAoOvcWl7ZxDSr5DbpZWb6iGDNKFhkdOGIvWXVgQyHP2ScWIjo+I1jV8e9h4OL6KuqtQAlHC59WAc8tNQ4B8+0GaMzEZt0odhaXuCj/TmoJmqWv0Hy03bnDYjc/kER2TeGW+NtwS2++2GeStyWvXsi7cxkLO5RFBbfRBx9z+UA/HxJP52J5HBFLqGeB/MneMbIy35qrnOhz1b59+BGP18IB64vd5dQo+r6BGuCNntEFB+1TE0ystmKfp8PMwHY9MXgfqHuZv4/TzZC08a/S5CtiBuE/FIH7T+Zni7oEK9q8rtlzm5Yy82hpcbNXTDMjVdEqvm6OqBqylpYTtWf1nLj/tMi2RGkfb3PLt8cmkjGbJnesvF94Er1YepBtRimuHddg2/rJD/AuHL2Bo2C0YkW6IqKC2gSx0+FWC31r7tVOdP5KY/M7GDCziauACeJ1rUieUqtYbqqhQC6LWUHSaCe170J4D5q6Sa4steSbSRkTMqQk08DrMhgoOIfUzh0sCEdIv7/cIPYoxMY5rgf0MLzgboP2JvMy9BG0yhhy4NhrWWvC3ua8J6CLdxMb1PCDSi+c6J2H/HMLaCkvyZOGNmuPcBIPQ== sase-pvt-region@SCMAC250593
    gcp_panrepo_bucket: panrepo-gov-prod
    gcp_panrepo_role: roles/storage.admin
    gcp_tech_support_role: roles/storage.admin
    dem_config: 1758126
    acs_gcp_project_id: sasedp-infra-fedmod-prod-01
    acs_spanner_instance_id: acs-prod-spanner-instance
    acs_spanner_env_suffix: prod
    epaas_sinefa_acct_id: 28574
    epaas_sinefa_api_key: AQICAHj1T/bG0yLNYh0iQn0MsUJ2mV5nlb2DzDLGyO6/BRO5aAHYsTd7axs00sqE5Ukfl/EnAAAAgzCBgAYJKoZIhvcNAQcGoHMwcQIBADBsBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDLFS5j1m/L+Pf5vy0QIBEIA//FtONJeym+q1tSaf6yLVABLkZPnpu53izx1NNF/8WY8BMnS2cjJtUkVGQhym5oLPZsxZn/IdIr1QhYG5i1FZ
    ipsync_host_name: global.ipsync.swg.fed.prismaaccess.com
    ipsync_host_name_grpc: prod.grpc-ipsync.swg.fed.prismaaccess.com
    azr_service_account_id: b9fca69b-5714-42d9-a854-9488b4b319af
    azr_service_account_secret: AQICAHj1T/bG0yLNYh0iQn0MsUJ2mV5nlb2DzDLGyO6/BRO5aAEyd5twer3jEs9V4NAjXR3RAAAAhDCBgQYJKoZIhvcNAQcGoHQwcgIBADBtBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDLEXvH4dS1hpvrWrbgIBEIBAdmSD2GzA8QLC0EgDCUAb6O+RaUODq3S9EBpPaQFQ004ngKnNy4ozVwmqdLaIeT1JixAOyani3lgV97TSCtJz3Q==
    azr_tf_token: AQICAHj1T/bG0yLNYh0iQn0MsUJ2mV5nlb2DzDLGyO6/BRO5aAEfcyavYBCIkKvxnqvNtWMCAAAAvDCBuQYJKoZIhvcNAQcGoIGrMIGoAgEAMIGiBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDOB6jpnJd9CMCS4nlAIBEIB1kKzRNGj0dci0crMsepxFISA6YGVK5xKuoU+ivLwGAerC2u2ih26Jo4zPRlDl1S2afbSbqeQMChsJ9/sjOW8R2RaIibyYg5SQqkExF2H3pd8Vi9m1jlBtBDg9Kr6e7U5MlqTDfzpFLUVfMkd5xIUqBeys98i6
    azr_host_subscription_id: fd0d8c05-8c47-4a33-bfac-44f62d3f7945
    azr_image_subscription_id: 2a8831de-b127-4420-824d-ca2077ad4558
    forwarding_enabled_mars: true
    forwarding_enabled_all_events: true
    forwarding_enabled_config_commit: true
    gcp_project_id: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_eu: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_uk: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_fedramp: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_ca: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_japan: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_au: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_germany: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_in: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_singapore: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_ch: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_fr: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_qa: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_tw: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_kr: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_cn: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_ae: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_il: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_id: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_sa: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_pl: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_it: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_es: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_za: pa-sase-cosmos-fed-mod-prod-1
    gcp_project_id_fedmod: pa-sase-cosmos-fed-mod-prod-1
    receiving_enabled: false
    avisar_forwarding_enabled: true
    avisar_key_rotation: true
    avisar_ngpa_auto_migration: true
    aws_format: aws
    aws_region: us-west-2
    aws_env_type: fedramp-mod
    fedramp_dp_acct_id: ""
    r53_access_key_id: ""
    r53_secret_access_key: ""
    mdns_ns1_tcp_monitoring_enabled: 1
    mdns_ns1_monitoring_freq: 30
    mdns_ns1_monitoring_connect_timeout: 14
    mdns_ns1_monitoring_time_to_connect: 7
    mdns_ns1_monitoring_http_idle_timeout: 3
    mdns_ns1_monitoring_notifn_delay: 15
    mdns_r53_ep_zone_id: "Z030598917PZF0WBL0LUV"
    mdns_r53_monitoring_freq: 30
    mdns_r53_monitoring_failure_threshold: 3
    mdns_r53_monitoring_enabled: 1
    mdns_r53_tcp_monitoring_enabled: 1
    mdns_r53_monitored_svc_url: http://e2edpc.swg.fed.prismaaccess.com:8888/e2edpc
    mdns_r53_monitoring_notifn_sns_topic: "ep-fedramp-hc-alarm"
    mdns_r53_monitoring_regions: "us-east-1 us-west-1 us-west-2 eu-west-1 ap-southeast-1 ap-southeast-2 ap-northeast-1 sa-east-1"
    uda_base_zone: uda.panclouddev.com
    rbi_base_zone: rbi.panclouddev.com
    nhp_proxyprotocol_version: v2
    av_buckets:
      gcp:
        - project: gov-host-gpcs-prod-01
          bucket_name: av-repo-gov-prod
      aws:
        - region: us-west-2
          bucket_name: av-repo-us-west-2-gov-prod
        - region: us-east-1
          bucket_name: av-repo-us-east-1-gov-prod
    content_rpm_buckets:
      gcp:
        - project: gov-host-gpcs-prod-01
          bucket_name: content-rpm-repo-gov-prod
      aws:
        - region: us-west-2
          bucket_name: content-rpm-repo-us-west-2-gov-prod
        - region: us-east-1
          bucket_name: content-rpm-repo-us-east-1-gov-prod
    av_rpm_buckets:
      gcp:
        - project: gov-host-gpcs-prod-01
          bucket_name: av-rpm-repo-gov-prod
      aws:
        - region: us-west-2
          bucket_name: av-rpm-repo-us-west-2-gov-prod
        - region: us-east-1
          bucket_name: av-rpm-repo-us-east-1-gov-prod
    tf_token: ""
    tf_prj: gov-prod
    tf_org: PANW-SASE-Prod
    tf_server: https://app.terraform.io/
    provision_url: https://api.fed.prismaaccess.com/orchestrator/provisioning
    osp_pan_tech_support: ""
    openstack_orch_project_id: ""
    auth_url: 'https://prod.pa-colo.core.pan.run:5000'
    ca_cert_path: lambda_certs/client_cert_fetch_prod.pem
    ca_key_path: lambda_certs/client_cert_fetch_prod.key
    deploy_certificate: ''
    deploy_credential: ''
    epm_domain: epm.gpcloudservice.com
    execution_mode: ''
    prov_svc_task_attempt_max: 3
    pa_proxy_sinkhole_fqdn: all.sink.swg.fed.prismaaccess.com
    rbi_infra_edl: 'https://cdn-dev.rbi.panclouddev.com/publicedls/rpi.edl'
    sasedp_infra_project: sasedp-infra-prod-01
    tf_auto_migration_gcp: false
    traffic_mirroring_host_project_id: tm-shared-fm-prod
    envoy_ssh_key: admin:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQD3MGS6oZezshEgLfs6EGH36dGkfvnT6o+H42v1/fwBwZ7Ht52F0OClWM0fdYeeSegXdKVyZDa7yu7uG+GZTUjqO+PnXzKdOsshuEAAz2iE6Sbq/wkNh9XiA5Tt9DCtCjw57yJmtBymcT6xzMz/C05tdUQQf2dz0OS6lqZ8rtJcOn8tBvOD5fCvtMlTgnYEkPJ5RSfQVMOwx+XOAdcos2kinbh1UC8HkJpTVs0mf/Qg1VzymOtKXWNKPKksD1MasEe83zj4RWD973tDSIWloEcAWe4HAbEU2B3U1N20mIJdpgI3GeJYfVpMW8kgwzBr261FzES3XbkTHcG+vt0OcBEv admin
    uda_provider_bce:
      jwt_url: https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>
    uda_provider_rbi:
      jwt_url: https://portal-prod.rbi.io/default/auth/jwks
    usage_query_support_gcp: false
    cnat_alert_email: <EMAIL> # needs to be updated
  gov-prod-us-east-1:
      acct_id: ************
      dbhost: fwaasdb-gov-prod.fed.prismaaccess.com
      dbhost_ro: fwaasdb-gov-prod-ro.fed.prismaaccess.com
      dbpass: AQICAHj1T/bG0yLNYh0iQn0MsUJ2mV5nlb2DzDLGyO6/BRO5aAFUBz0NAHnKZlj9N2wJAUS4AAAAaDBmBgkqhkiG9w0BBwagWTBXAgEAMFIGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMxkTKlObdeSbAvK0uAgEQgCVrx3hGkS3jmzfCvWKUy0ultLxMoxV8w9q9XjRg1LLkWR1JTIZk
      gp_gw_domain: fed.prismaaccess.com
      gp_gw_domain_prisma: fed.prismaaccess.com
      logging_fqdn: lic.lc.prod.us.cs.paloaltonetworks.com
      route53acct: a************
      bucket_name: pan-content-us-east-1-gov-prod
      cft_bucket_name: pan-cft-us-east-1-gov-prod
      xform_bucket_name: pan-cloud-xform
      saas_gpcs_api_endpoint: api.fed.prismaaccess.com
      fwaas_version: GPCS-1.0.74
      pa_cdl_proxy_fqdn: prod.cdls.swg.fed.prismaaccess.com
      pa_proxy_config_discovery_fqdn: prod.cfgds.swg.fed.prismaaccess.com
      pa_proxy_gatekeeper_fqdn: prod.gks.swg.fed.prismaaccess.com
      pa_telegraph_fqdn: prod.tgs.swg.fed.prismaaccess.com
      eproxy_image_project: gov-image-gpcs-prod-01
      eproxy_outside_panos: 1
      agent_pool_id: ""
      ca_mgr_info: "prod:631680999526:orch_secret_manager"
      it_ca_cert: 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
      mdns_ns1_api_key: AQICAHj1T/bG0yLNYh0iQn0MsUJ2mV5nlb2DzDLGyO6/BRO5aAGguXrGgV222bRUkRTaEJzuAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMyUkI2i+U4oSawTCXAgEQgC9dvt8Su7l87T0RXJN4Fj1GFtVUdghvAxWu7zNFelUIz/Xz8yTj0RwYZBvdK6ejJA==
      mdns_ns1_datasource_id: d8e52934aaacc31fbeb749904e118279
      mdns_ns1_notifn_list_id: 63094a46d1e4b3108ebc2af0
      mdns_ns1_monitoring_enabled: 1
      swg_proxy_base_zone: proxy.fed.prismaaccess.com
      gcp_orch_project_id_pattern: gov-prod
      gcp_orch_folder_id: 1062482435126
      gcp_orch_billing_id: 0191EA-C0E00D-70713E
      gcp_orch_project_deploy_limit: 1000
      gcp_host_svc_limit: 2500
      gcp_img_host: gov-image-gpcs-prod-01
      gcp_host_proj_list: gov-host-gpcs-prod-01
      gcp_orch_proj_list: gov-orchestrator-gpcs-prod
      aws_env: gov-prod
      gcp_proxy_machineType: n1-standard-2
      gcp_syslog_server: ************
      gcp_proxy_sshkey: gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQD3MGS6oZezshEgLfs6EGH36dGkfvnT6o+H42v1/fwBwZ7Ht52F0OClWM0fdYeeSegXdKVyZDa7yu7uG+GZTUjqO+PnXzKdOsshuEAAz2iE6Sbq/wkNh9XiA5Tt9DCtCjw57yJmtBymcT6xzMz/C05tdUQQf2dz0OS6lqZ8rtJcOn8tBvOD5fCvtMlTgnYEkPJ5RSfQVMOwx+XOAdcos2kinbh1UC8HkJpTVs0mf/Qg1VzymOtKXWNKPKksD1MasEe83zj4RWD973tDSIWloEcAWe4HAbEU2B3U1N20mIJdpgI3GeJYfVpMW8kgwzBr261FzES3XbkTHcG+vt0OcBEv gce-user
      fw_ssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQD3MGS6oZezshEgLfs6EGH36dGkfvnT6o+H42v1/fwBwZ7Ht52F0OClWM0fdYeeSegXdKVyZDa7yu7uG+GZTUjqO+PnXzKdOsshuEAAz2iE6Sbq/wkNh9XiA5Tt9DCtCjw57yJmtBymcT6xzMz/C05tdUQQf2dz0OS6lqZ8rtJcOn8tBvOD5fCvtMlTgnYEkPJ5RSfQVMOwx+XOAdcos2kinbh1UC8HkJpTVs0mf/Qg1VzymOtKXWNKPKksD1MasEe83zj4RWD973tDSIWloEcAWe4HAbEU2B3U1N20mIJdpgI3GeJYfVpMW8kgwzBr261FzES3XbkTHcG+vt0OcBEv gce-user@gpcs-sre-prod-jbox-01
      spr_fw_ssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCvJfwbfoVbxVS3IWZ0jXxpINSK/6dkfEahIh4z4F8EvDwQyN/ct9r4JDagxrTlsuFds4SHDZYGLSwL0+dAoOvcWl7ZxDSr5DbpZWb6iGDNKFhkdOGIvWXVgQyHP2ScWIjo+I1jV8e9h4OL6KuqtQAlHC59WAc8tNQ4B8+0GaMzEZt0odhaXuCj/TmoJmqWv0Hy03bnDYjc/kER2TeGW+NtwS2++2GeStyWvXsi7cxkLO5RFBbfRBx9z+UA/HxJP52J5HBFLqGeB/MneMbIy35qrnOhz1b59+BGP18IB64vd5dQo+r6BGuCNntEFB+1TE0ystmKfp8PMwHY9MXgfqHuZv4/TzZC08a/S5CtiBuE/FIH7T+Zni7oEK9q8rtlzm5Yy82hpcbNXTDMjVdEqvm6OqBqylpYTtWf1nLj/tMi2RGkfb3PLt8cmkjGbJnesvF94Er1YepBtRimuHddg2/rJD/AuHL2Bo2C0YkW6IqKC2gSx0+FWC31r7tVOdP5KY/M7GDCziauACeJ1rUieUqtYbqqhQC6LWUHSaCe170J4D5q6Sa4steSbSRkTMqQk08DrMhgoOIfUzh0sCEdIv7/cIPYoxMY5rgf0MLzgboP2JvMy9BG0yhhy4NhrWWvC3ua8J6CLdxMb1PCDSi+c6J2H/HMLaCkvyZOGNmuPcBIPQ== sase-pvt-region@SCMAC250593
      gcp_panrepo_bucket: panrepo-gov-prod
      gcp_panrepo_role: roles/storage.admin
      gcp_tech_support_role: roles/storage.admin
      dem_config: 1758126
      acs_gcp_project_id: sasedp-infra-fedmod-prod-01
      acs_spanner_instance_id: acs-prod-spanner-instance
      acs_spanner_env_suffix: prod
      epaas_sinefa_acct_id: 28574
      epaas_sinefa_api_key: AQICAHj1T/bG0yLNYh0iQn0MsUJ2mV5nlb2DzDLGyO6/BRO5aAHYsTd7axs00sqE5Ukfl/EnAAAAgzCBgAYJKoZIhvcNAQcGoHMwcQIBADBsBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDLFS5j1m/L+Pf5vy0QIBEIA//FtONJeym+q1tSaf6yLVABLkZPnpu53izx1NNF/8WY8BMnS2cjJtUkVGQhym5oLPZsxZn/IdIr1QhYG5i1FZ
      ipsync_host_name: global.ipsync.swg.fed.prismaaccess.com
      ipsync_host_name_grpc: prod.grpc-ipsync.swg.fed.prismaaccess.com
      azr_service_account_id: b9fca69b-5714-42d9-a854-9488b4b319af
      azr_service_account_secret: AQICAHj1T/bG0yLNYh0iQn0MsUJ2mV5nlb2DzDLGyO6/BRO5aAEyd5twer3jEs9V4NAjXR3RAAAAhDCBgQYJKoZIhvcNAQcGoHQwcgIBADBtBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDLEXvH4dS1hpvrWrbgIBEIBAdmSD2GzA8QLC0EgDCUAb6O+RaUODq3S9EBpPaQFQ004ngKnNy4ozVwmqdLaIeT1JixAOyani3lgV97TSCtJz3Q==
      azr_tf_token: AQICAHj1T/bG0yLNYh0iQn0MsUJ2mV5nlb2DzDLGyO6/BRO5aAEfcyavYBCIkKvxnqvNtWMCAAAAvDCBuQYJKoZIhvcNAQcGoIGrMIGoAgEAMIGiBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDOB6jpnJd9CMCS4nlAIBEIB1kKzRNGj0dci0crMsepxFISA6YGVK5xKuoU+ivLwGAerC2u2ih26Jo4zPRlDl1S2afbSbqeQMChsJ9/sjOW8R2RaIibyYg5SQqkExF2H3pd8Vi9m1jlBtBDg9Kr6e7U5MlqTDfzpFLUVfMkd5xIUqBeys98i6
      azr_host_subscription_id: fd0d8c05-8c47-4a33-bfac-44f62d3f7945
      azr_image_subscription_id: 2a8831de-b127-4420-824d-ca2077ad4558
      forwarding_enabled_mars: true
      forwarding_enabled_all_events: true
      forwarding_enabled_config_commit: true
      gcp_project_id: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_eu: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_uk: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_fedramp: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_ca: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_japan: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_au: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_germany: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_in: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_singapore: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_ch: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_fr: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_qa: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_tw: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_kr: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_cn: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_ae: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_il: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_id: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_sa: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_pl: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_it: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_es: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_za: pa-sase-cosmos-fed-mod-prod-1
      gcp_project_id_fedmod: pa-sase-cosmos-fed-mod-prod-1
      receiving_enabled: false
      avisar_forwarding_enabled: true
      avisar_key_rotation: true
      avisar_ngpa_auto_migration: true
      aws_format: aws
      aws_region: us-east-1
      aws_env_type: fedramp-mod
      fedramp_dp_acct_id: ""
      r53_access_key_id: ""
      r53_secret_access_key: ""
      mdns_ns1_tcp_monitoring_enabled: 1
      mdns_ns1_monitoring_freq: 30
      mdns_ns1_monitoring_connect_timeout: 14
      mdns_ns1_monitoring_time_to_connect: 7
      mdns_ns1_monitoring_http_idle_timeout: 3
      mdns_ns1_monitoring_notifn_delay: 15
      mdns_r53_ep_zone_id: "Z030598917PZF0WBL0LUV"
      mdns_r53_monitoring_freq: 30
      mdns_r53_monitoring_failure_threshold: 3
      mdns_r53_monitoring_enabled: 1
      mdns_r53_tcp_monitoring_enabled: 1
      mdns_r53_monitored_svc_url: http://e2edpc.swg.fed.prismaaccess.com:8888/e2edpc
      mdns_r53_monitoring_notifn_sns_topic: "ep-fedramp-hc-alarm"
      mdns_r53_monitoring_regions: "us-east-1 us-west-1 us-west-2 eu-west-1 ap-southeast-1 ap-southeast-2 ap-northeast-1 sa-east-1"
      uda_base_zone: uda.panclouddev.com
      rbi_base_zone: rbi.panclouddev.com
      nhp_proxyprotocol_version: v2
      av_buckets:
        gcp:
          - project: gov-host-gpcs-prod-01
            bucket_name: av-repo-gov-prod
        aws:
          - region: us-west-2
            bucket_name: av-repo-us-west-2-gov-prod
          - region: us-east-1
            bucket_name: av-repo-us-east-1-gov-prod
      content_rpm_buckets:
        gcp:
          - project: gov-host-gpcs-prod-01
            bucket_name: content-rpm-repo-gov-prod
        aws:
          - region: us-west-2
            bucket_name: content-rpm-repo-us-west-2-gov-prod
          - region: us-east-1
            bucket_name: content-rpm-repo-us-east-1-gov-prod
      av_rpm_buckets:
        gcp:
          - project: gov-host-gpcs-prod-01
            bucket_name: av-rpm-repo-gov-prod
        aws:
          - region: us-west-2
            bucket_name: av-rpm-repo-us-west-2-gov-prod
          - region: us-east-1
            bucket_name: av-rpm-repo-us-east-1-gov-prod
      tf_token: ""
      tf_prj: gov-prod
      tf_org: PANW-SASE-Prod
      google_provider_version: "5.42.0"
      tf_server: https://app.terraform.io/
      provision_url: https://api.fed.prismaaccess.com/orchestrator/provisioning
      osp_pan_tech_support: ""
      openstack_orch_project_id: ""
      auth_url: 'https://prod.pa-colo.core.pan.run:5000'
      ca_cert_path: lambda_certs/client_cert_fetch_prod.pem
      ca_key_path: lambda_certs/client_cert_fetch_prod.key
      deploy_certificate: ''
      deploy_credential: ''
      epm_domain: epm.gpcloudservice.com
      execution_mode: ''
      prov_svc_task_attempt_max: 3
      pa_proxy_sinkhole_fqdn: all.sink.swg.fed.prismaaccess.com
      rbi_infra_edl: 'https://cdn-dev.rbi.panclouddev.com/publicedls/rpi.edl'
      sasedp_infra_project: sasedp-infra-prod-01
      tf_auto_migration_gcp: false
      traffic_mirroring_host_project_id: tm-shared-fm-prod
      envoy_ssh_key: admin:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQD3MGS6oZezshEgLfs6EGH36dGkfvnT6o+H42v1/fwBwZ7Ht52F0OClWM0fdYeeSegXdKVyZDa7yu7uG+GZTUjqO+PnXzKdOsshuEAAz2iE6Sbq/wkNh9XiA5Tt9DCtCjw57yJmtBymcT6xzMz/C05tdUQQf2dz0OS6lqZ8rtJcOn8tBvOD5fCvtMlTgnYEkPJ5RSfQVMOwx+XOAdcos2kinbh1UC8HkJpTVs0mf/Qg1VzymOtKXWNKPKksD1MasEe83zj4RWD973tDSIWloEcAWe4HAbEU2B3U1N20mIJdpgI3GeJYfVpMW8kgwzBr261FzES3XbkTHcG+vt0OcBEv admin
      uda_provider_bce:
        jwt_url: https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>
      uda_provider_rbi:
        jwt_url: https://portal-prod.rbi.io/default/auth/jwks
      usage_query_support_gcp: false
      cnat_alert_email: <EMAIL> # needs to be updated