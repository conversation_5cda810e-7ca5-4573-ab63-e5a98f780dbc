cyrus:
  fm-molokai:
    acct_id: 024848480221
    dbhost: fwaasdb.fm-molokai.prismaaccess.com
    dbhost_ro: fwaasdb-ro.fm-molokai.prismaaccess.com
    dbpass: AQICAHgsg2BJ0qDZG8H1f38CKp/cQtBkc9WsJVjqMMzA20MXaAHq+eALKTnF9XC2KJ0fRKCMAAAAdDByBgkqhkiG9w0BBwagZTBjAgEAMF4GCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMNmIUpbbL3A6bsDhCAgEQgDGIo93YcdoRDjIEpT0CUmVVS8Z+8PIBOb8YdSuQvLe356s1cDrICt7Iwis+WWBN3hr/
    gp_gw_domain: fm-molokai.prismaaccess.com
    cs_endpoint_api: sasedp.panclouddev.com
    gp_gw_domain_prisma: fm-molokai.prismaaccess.com
    logging_fqdn: lic.lc.prod.us.cs.paloaltonetworks.com
    route53acct: a631680999526
    bucket_name: pan-content-us-west-2-fm-molokai
    cft_bucket_name: pan-cft-us-west-2-fm-molokai
    xform_bucket_name: cloud-xform-fm-molokai
    saas_gpcs_api_endpoint: api.fm-molokai.prismaaccess.com
    fwaas_version: GPCS-1.0.74
    swg_proxy_base_zone: proxy.fm-molokai.prismaaccess.com
    pa_cdl_proxy_fqdn:  cdls.swg.fm-molokai.prismaaccess.com
    pa_proxy_config_discovery_fqdn: cfgds.swg.fm-molokai.prismaaccess.com
    pa_proxy_gatekeeper_fqdn: gks.swg.fm-molokai.prismaaccess.com
    pa_proxy_sinkhole_fqdn: all.sink.swg.fm-molokai.prismaaccess.com
    ipsync_host_name: ipsync.fm-molokai.prismaaccess.com
    ipsync_host_name_grpc: grpc-ipsync.fm-molokai.prismaaccess.com
    acs_gcp_project_id: sasedp-infra-fedmod-maui-01 
    acs_spanner_instance_id: acs-non-prod-spanner-instance
    acs_spanner_env_suffix: molokai
    eproxy_image_project: image-gpcs-prod-01
    eproxy_outside_panos: 1
    auth_proxy_project_id: pa-authproxy-prod-01
    auth_proxy_host_project_id: host-gpcs-authproxy-prod-01
    auth_proxy_parent_id: 9010000000
    ca_mgr_info: "" 
    it_ca_cert: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUVzVENDQXhtZ0F3SUJBZ0lRSGRoNVpNN0hZN0ZBRFVsRUozRFRVVEFOQmdrcWhraUc5dzBCQVF3RkFEQmMKTVFzd0NRWURWUVFHRXdKVlV6RWdNQjRHQTFVRUNoTVhVR0ZzYnlCQmJIUnZJRTVsZEhkdmNtdHpJRWx1WXk0eApLekFwQmdOVkJBTVRJbEJoYkc4Z1FXeDBieUJPWlhSM2IzSnJjeUJKYm1NdUxWSnZiM1F0UTBFZ1J6RXdIaGNOCk1UY3dOVEkwTWpFME5UVXdXaGNOTWpjd05USTBNakUxTkRReVdqQmNNUXN3Q1FZRFZRUUdFd0pWVXpFZ01CNEcKQTFVRUNoTVhVR0ZzYnlCQmJIUnZJRTVsZEhkdmNtdHpJRWx1WXk0eEt6QXBCZ05WQkFNVElsQmhiRzhnUVd4MApieUJPWlhSM2IzSnJjeUJKYm1NdUxWSnZiM1F0UTBFZ1J6RXdnZ0dpTUEwR0NTcUdTSWIzRFFFQkFRVUFBNElCCmp3QXdnZ0dLQW9JQmdRREN1R01WQStOSEZ6Z2ROanhBbVhwVGtDOVg3NWcrT1E4Vmw5c2tZbjNHQ1N6MGdjN1AKelR0UzFERkIrMzVzMHhBQWg3TjQrTlZGS0hJamRuOXNURUV3NGthMmp1bnFBQ2JqcTBhU3FSWWZpQUo4aXdBQgpiZWJsdGxmL2ZCUXF6OEJUQUQrYXFxN1lQUkh0QkJMdTZnUFJLYzZVZSs0UHRMNkx6cUI3L0liVk0zcENrcnpiCmh3eGhGY255YTBmSFpnVEJDVFVKbjExK0xlN2lhUXpqSTd4NEpSYlQvaVBQY1h0VDVtdGNEYmVJaCtNYllSbE8KMWFHVkNaNWlldEpTNjQ0N0dtYTJzbW1aOU84WFppbjJCRGt0S3FiUGRGdGlYRXM4ZVlmOVNGU29CNUdnUnZqSgo3WExEWDdqZGgvYW8wZTErZU5pTXUxSkZrNGR0RmdlRTQ1NTFqYmZMVjQyUk1HRjJ0VGNNQ1ZFODdvdGNxTWtBClZkN1RxOUg2LzhHVXN5UkpIZC96Ylo5V3NjdkR0NHhXd1U2YmcrMjF2N3R6RWUxa3poTFdWWjAvVktHNnNKVDQKVHdqanphMVN0ak14QU1KdTNwbUsvSUJPVTRQdXFpTXo3alQzZzJlRDFZTlljdFVJaEI1WWhOK2xBMlI0SU5FaApLdFFUbk9zWm84d29VVWNDQXdFQUFhTnZNRzB3RGdZRFZSMFBBUUgvQkFRREFnRUdNQThHQTFVZEV3RUIvd1FGCk1BTUJBZjh3SFFZRFZSME9CQllFRk05ZDk4bS80c0x5ZXptMlRoTjRabmltd3V2ek1CQUdDU3NHQVFRQmdqY1YKQVFRREFnRUFNQmtHQTFVZElBUVNNQkF3RGdZTUt3WUJCQUdCeG5VREFRRUNNQTBHQ1NxR1NJYjNEUUVCREFVQQpBNElCZ1FCUFQzekpaOVFIbHBRdHB0Ylppczk5ZXRGSzJ5c0paNVJQbkg3bXlnWDVoTkt0TW00U0hpU2pCeCtjCjFLRzNvNkd5cEt2Z2xBblRpWmRjRm9JRkwyNEIyaDgxaFlsS25nTHhzaE5VRC9EM05yRmp5TE9FQjd2Q2hVdGYKeC9WVENSSWhLR1hlLzRkYVBhR1JXTmFQNkN0YTlwL2xEaXBSd2FHUDdpRGllamtWRGhCWUdNaHNTeGduVTF2cApQakNGa2RxS2dvMTQ1S1NORW5VMkwzL3FCOStWNG00R1B6M3k2OTV5RVB1bEVxSVFmWkxVTnpLOEZ1bzBWMXhGCnBGclV1SkQ0SmZUZE9NZDNrMzlXWlkxU1ltazBDTHQ5YnVGUXNrZjREYldab2FWYTU5K3RwMGcyalZnRFBaclIKY1hUdE5DODJEMGR6VzFhOVU3RFNYOGptOTZJUUp3SWw0aHhqSHFPV1JSY1RRUDdwTlp6MjAvdlhLOTcvWERGcAo0UE0ybUwvakVQNzc5YkJxUFJ0TEIxVlBUMmtzazF6UkVlVnhoanduRVhOYk5uM2xVaHVLRXZhQTIxc3lxL1d6CjJMZU04QlRrd2IvTUdIc21iUFBRUzBUTWpuMG9CNnBVcTJuMDdUU05tSFl0SHNmLzNQRVkvTUp5T1N5bWxVSGwKYVNtVHJmST0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQotLS0tLUJFR0lOIENFUlRJRklDQVRFLS0tLS0KTUlJRmxEQ0NBL3lnQXdJQkFnSVRjd0FBQUF2eGkyNmNGd1AxbUFBQUFBQUFDekFOQmdrcWhraUc5dzBCQVF3RgpBREJjTVFzd0NRWURWUVFHRXdKVlV6RWdNQjRHQTFVRUNoTVhVR0ZzYnlCQmJIUnZJRTVsZEhkdmNtdHpJRWx1Cll5NHhLekFwQmdOVkJBTVRJbEJoYkc4Z1FXeDBieUJPWlhSM2IzSnJjeUJKYm1NdUxWSnZiM1F0UTBFZ1J6RXcKSGhjTk1UY3dOekV6TURNMU5ETXlXaGNOTWpjd05USTBNakUxTkRReVdqQnNNUXN3Q1FZRFZRUUdFd0pWVXpFZwpNQjRHQTFVRUNoTVhVR0ZzYnlCQmJIUnZJRTVsZEhkdmNtdHpJRWx1WXk0eE96QTVCZ05WQkFNVE1sQmhiRzhnClFXeDBieUJPWlhSM2IzSnJjeTFUU2tNdFUyVnlkbWxqWlhNdFNXNTBaWEp0WldScFlYUmxMVU5CSUVjeE1JSUIKb2pBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVk4QU1JSUJpZ0tDQVlFQTA0MC8xckhwd09JZG9qTmNEZk1ZeVJsbApLcWtRdDVLb3F6K0RxVzU1Vy9EVUgxamlzMS9VKy9RK3ZVMlByaWZUaHFlWUxLU2hrdktudzR6TlgzMjVJWVZWClFvU3M1aXpXNlVFbTZRR3BaaW9NNUl4M0x1ZWIrdGM1VnFOR2ZOVTEyRkFzSVZDeUpsZDc2NGI0aVRrZVduVXcKVWtDY0xtQng1TzdrRWVFRXlNbC9OOGZpTkM2MjRxMTFiSHJ5d0xodzMzMjlSeXFrMi9rdlluNTRxY0QyY01yUwpHODMrODh5dStPOU1QT0Q1SlFSSjREeVVQblJXZ1Vrclpkd3gzTjAxdXJBR3JMbmk5cjVwYXBFSm9tUnM1cGlnCkd6YlNFbVJ2THRHWFYvSzh0V3BScWpuSitTdzl4aUQ0cmdhMlRaL2RvY2VqOHk2b3pUeGJpTUVQVkN5NktUd0wKN2VrNmg4U3FhRGxFblc4R3JSSUV6dG9lU1NFZ1VkRStDNTUzVHI5TDRKU1N3L1FncVMwaGhiK1dIMlVlT0M4UApHZld1SS8xa1AwNzgrenV6ZzRyZzBMejdSNFhYZWdZcmdTcFFIaUJmWDVsRWwzN3kybHV6L3ZxNWhscnQxWDFiCmN3ZDBXazl0VVhYMEI0T1p3TWZ0T1JWN0JvY1FEQk1EbXBETGQ1WWJBZ01CQUFHamdnRTlNSUlCT1RBT0JnTlYKSFE4QkFmOEVCQU1DQVFZd0VBWUpLd1lCQkFHQ054VUJCQU1DQVFBd0hRWURWUjBPQkJZRUZOd0o1UlpSNm0yMwo4L1JqTXdLOGxNeHhvRHhXTUJvR0ExVWRJQVFUTUJFd0R3WU5Ld1lCQkFHQnhuVURBUUVDQWpBWkJna3JCZ0VFCkFZSTNGQUlFREI0S0FGTUFkUUJpQUVNQVFUQVBCZ05WSFJNQkFmOEVCVEFEQVFIL01COEdBMVVkSXdRWU1CYUEKRk05ZDk4bS80c0x5ZXptMlRoTjRabmltd3V2ek1FQUdBMVVkSHdRNU1EY3dOYUF6b0RHR0wyaDBkSEE2THk5agpjbXd1Y0dGc2IyRnNkRzl1WlhSM2IzSnJjeTVqYjIwdmNHRnVMWEp2YjNRdFkyRXVZM0pzTUVzR0NDc0dBUVVGCkJ3RUJCRDh3UFRBN0JnZ3JCZ0VGQlFjd0FvWXZhSFIwY0RvdkwyTnliQzV3WVd4dllXeDBiMjVsZEhkdmNtdHoKTG1OdmJTOXdZVzR0Y205dmRDMWpZUzVqY25Rd0RRWUpLb1pJaHZjTkFRRU1CUUFEZ2dHQkFBWmFQc3d4a28zcApKbFdRam9iWUMzZ25ReklSbTVSdlhRNlU3ZXlRZzArTnZ3bUhMQ2RrY3hOZVd4ejZ2NWtLNFdKM1VBb3l1RU1hCkZaUWRkZk5IWittZmgyaDVCWDdsbXRneVB1VHpuSFVJTlVZVFBOOHZ1NDJPUFZGSVlGclRFN3VLK2RzVnJNbWMKdWk1dEtmVk9wQk1oeU85VXYyK2tPb3BVY2N6TDZ6bUI1WmFTUXVTSkV3dDRiZzZjTm9FMk9BL1NFaU9oK3lFWQpSQVBNdHA2U3gvMGFaMmo2RlJzNGo0bk9HZ0VGMDdTM1A4SkpmYldUVFozKzF4ZTNOdnR3dlIwVWJ0RjJLMVVPCmFFek42dzV0dmNLZGhxY2N0VG9waXdPY2JENEZZTFRlSTliMXkxVFV3SzRpbVNJUVlwajJ6TTFEMUh2WmU4RkQKc2pZQnNFczdFa1VRUDloSFh2c3kxaFV2RFBTU2tYUE5VcVRJQkVaVlN0VmZBK25XaGY0Vi9hczdxdjV2dFVabAoweG9RYTVDc0R2ZkdzOHZ2L2ptNUNJcUlQZUJFUHBIRW9ydFBhVHhKZU1KNXVMRENnVHhlOFovVHZ6WGprcTFEClUyeGVzNUc0a2hPOG9JblFZVHFUSTdqcXNNbytkRVc5MDlpSzlVcWJUaFMyYlRmb0IzcFBHQT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0KLS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUZoRENDQSt5Z0F3SUJBZ0lJZFo1SUpFQmhSVUl3RFFZSktvWklodmNOQVFFTUJRQXdiREVMTUFrR0ExVUUKQmhNQ1ZWTXhJREFlQmdOVkJBb1RGMUJoYkc4Z1FXeDBieUJPWlhSM2IzSnJjeUJKYm1NdU1Uc3dPUVlEVlFRRApFekpRWVd4dklFRnNkRzhnVG1WMGQyOXlhM010VTBwRExWTmxjblpwWTJWekxVbHVkR1Z5YldWa2FXRjBaUzFEClFTQkhNVEFlRncweU5EQXlNRFl5TVRFd05EQmFGdzB5TnpBMU1qUXlNVFUwTkRKYU1GVXhKREFpQmdOVkJBTU0KRzBSQlRDMVRaWEoyYVdObGN5MUpjM04xYVc1bkxVTkJNaTFITlRFZ01CNEdBMVVFQ2d3WFVHRnNieTFCYkhSdgpMVTVsZEhkdmNtdHpMVWx1WXk0eEN6QUpCZ05WQkFZVEFsVlRNSUlCb2pBTkJna3Foa2lHOXcwQkFRRUZBQU9DCkFZOEFNSUlCaWdLQ0FZRUExeEU4eEJvdk9lcXlRMi9RM2NrOG5rQ2hMYkEvclBtRUZoVUZUdHcwZ3FWcGMvSnUKWHdGL2o2ZEtxcGttaXRUU2QwdERLamNSS1pabFA3SFloOHBmU2RyYlR1WVdGWlUxZEtxZVozaW1oMTY4ZnNxbwppWHF0UjM5TWZqakIyR1p4dk9PWmJTcWJienAyVUdlbm5XOCs2UFFmYVpaNzR3NE91WWhzZkJDZzV3SEYwU3NlCjJQYjREOCtnN01lTnkwaFNrK0ZGdnZnaGZ5ZkJJaVVsc2w5Wk93OExVekR0VGJsSC9iTktsdWI1MXpWaGtPejEKOGx1dU5USkwyaWxxR3NaUTR4SDdQNzBhZkpvaEx3eUJLblZvQUdmeDlwVXpzVnhYUE1sUzJXOEc0SnVBWEM5VwpEemlHclJ3bzRaQ0ttMXdLa0MxajczVktqV0dvZld2L1FDcUxyaWhyWmR4ckYzd0ZGVDVrTkxQWmJGRUVRVHc0CjROdlRMMUZ3WGcyWVA0eEZXRWFYKzAvdTRva1pFSzA3MjhodGhZWllOVldXbG01L29MbFVVNTVDblBMUlBwemwKY0t2dEtaT0N2YTdERStLNzdhcVNheUJVejE2TjV2Wk1WRm1oZHNqZ09Ga2c4OFpnK1JmRUZaT2FlWHVyM0lxNQpoK1NpMFBORFViaEFkbFJMQWdNQkFBR2pnZ0UvTUlJQk96QVNCZ05WSFJNQkFmOEVDREFHQVFIL0FnRUFNQjhHCkExVWRJd1FZTUJhQUZOd0o1UlpSNm0yMzgvUmpNd0s4bE14eG9EeFdNR0VHQ0NzR0FRVUZCd0VCQkZVd1V6QlIKQmdnckJnRUZCUWN3QW9aRmFIUjBjSE02THk5amNtd3VjR0ZzYjJGc2RHOXVaWFIzYjNKcmN5NWpiMjB2Y0dGdQpMWE5xWXkxelpYSjJhV05sY3kxcGJuUmxjbTFsWkdsaGRHVXRZMkV1WTNKME1Cc0dBMVVkSUFRVU1CSXdFQVlPCkt3WUJCQUdCeG5VREJnRUNCUVV3VlFZRFZSMGZCRTR3VERCS29FaWdSb1pFYUhSMGNEb3ZMMk55YkM1d1lXeHYKWVd4MGIyNWxkSGR2Y210ekxtTnZiUzl3WVc0dGMycGpMWE5sY25acFkyVnpMV2x1ZEdWeWJXVmthV0YwWlMxagpZUzVqY213d0hRWURWUjBPQkJZRUZGV0UvMUxUc00xaGNIUFAyU3JjdWFrZnQ3NWlNQTRHQTFVZER3RUIvd1FFCkF3SUJoakFOQmdrcWhraUc5dzBCQVF3RkFBT0NBWUVBR3RJTnpBTHhNQ2tNVTFsNVJVYWRKNXJQUk1va3A5QmYKeCthbEdEcUdaSWpoM3N4VmszYyszcVppTm4wSDNIdGcxdVE1V0t3UDR0a24wYytqeUNtaENOMHBja3BtK1BYVgpLeEJjZEtmVWt6OTdnYTFoT09nVnltL0tOR3BUN0tNTGdDOC8vdFcvYzJ1WmM0VHM2MVh2Yi9vR0hpdGVaejIyCnRjQlZReEZEQWdjaTlnbGhMbXNwemtZL28reXhENGMrUG12M2k1anE5SlFEaWhlVWJMc2tadGlydWx0VFY4cDcKQU01ejFvczBCZU9jdmN6TE9hMjR0eXJkT2JWeXJkM1dndzlPK0VTZVk3a3ZsQStQMElUMXR3YjZQbW0veVlHbApRbHJzNkI1WUVkQUthV1I1WDBnOUtKWThtTUtZdEtiSHhjVXFqWmF5b1VSZEkrNUxMWTdrcXFkQWx5MWRQTm04Cnh4WC9rY2NEQ0JMRG56Zk9ReVN1RnMxb1kvNW1IQ3ZSQjRROVNvRlo3Tzk0NktId1BPWkw0OFdWRWN1TVI1R2oKNjVCMWZlNUNrRUt1R0owOGlyMkNtQzI2R0JPZkYxN0tuM2JseS85anBwOUJWZWYwRVF1ZnBmS0dPQlE2QkJsQQp6aklmZjk4ZjNYVHdMek5WMUF0Z2xSRnlXOEhONzBqcgotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCi0tLS0tQkVHSU4gQ0VSVElGSUNBVEUtLS0tLQpNSUlGaERDQ0EreWdBd0lCQWdJSVQ2T1VUTDNCUFpvd0RRWUpLb1pJaHZjTkFRRU1CUUF3YkRFTE1Ba0dBMVVFCkJoTUNWVk14SURBZUJnTlZCQW9URjFCaGJHOGdRV3gwYnlCT1pYUjNiM0pyY3lCSmJtTXVNVHN3T1FZRFZRUUQKRXpKUVlXeHZJRUZzZEc4Z1RtVjBkMjl5YTNNdFUwcERMVk5sY25acFkyVnpMVWx1ZEdWeWJXVmthV0YwWlMxRApRU0JITVRBZUZ3MHlOREF5TURZeU1URXhNalZhRncweU56QTFNalF5TVRVME5ESmFNRlV4SkRBaUJnTlZCQU1NCkcxTktReTFUWlhKMmFXTmxjeTFKYzNOMWFXNW5MVU5CTWkxSE5URWdNQjRHQTFVRUNnd1hVR0ZzYnkxQmJIUnYKTFU1bGRIZHZjbXR6TFVsdVl5NHhDekFKQmdOVkJBWVRBbFZUTUlJQm9qQU5CZ2txaGtpRzl3MEJBUUVGQUFPQwpBWThBTUlJQmlnS0NBWUVBemZCR2ZkazEyM09KcXMxUkVjVVJqcHNKL3VWNU9SejNFTFNmTy8rLy9GamtiSm9mCko5YUJ5QVc4Z0p0dmZHUk5uUlVESTZGeXNqUk9JUUFseUV0QURGMHNZOVdPU3RueXBLemJGd01hYld3aTNjWWcKaDQ4LysrYUNDOGpqSmFRN1ZaRnZnSFN2SXdoUW1kb1FuSU9VUHYrOGViY3k5ekFEaWFxRGU2N2JlWWVzeUExWApQSnBHcE1PQTZvUUp2cTAxWHcrTjFrcmtrYXI2bVVRMGsrdnQxWDg1ZjJ2Z21WVmk1QW5MSkk5Rm5XY01JbVQxCjFySXlhU3JOUjlVRURJdEludDB0SUxYNVREVFFZZUFyL3JINURKUGpSUzBpMVVFeEFNVzlyS1JnSW9LR3pHancKRHRScS9ZSTBjamVPeTFhOUt0VllSQXB0MWlKRWtDTlM0V2diU2w1UmJtK3ZzZjgwcmtLVXNQMi9ERjlZSHdxdgpIbEM3ZnJFUWVPTjZIR05wZytBaUdTTndZMmprU1hQMjhtaUVMOFBPT2pSU2xTWlBYZkJDaWhudTFiaURCOXRVCllUalZJS3Y1YkxoT0hVUGphZXN4VG10SFdUTHdhVjdkT2JBY2pwL3M4MUM4ekt4NmUwVTVQcUxKdXREODRKLzgKVGdTaWxsSnFpMnNTQ2NqUkFnTUJBQUdqZ2dFL01JSUJPekFTQmdOVkhSTUJBZjhFQ0RBR0FRSC9BZ0VBTUI4RwpBMVVkSXdRWU1CYUFGTndKNVJaUjZtMjM4L1JqTXdLOGxNeHhvRHhXTUdFR0NDc0dBUVVGQndFQkJGVXdVekJSCkJnZ3JCZ0VGQlFjd0FvWkZhSFIwY0hNNkx5OWpjbXd1Y0dGc2IyRnNkRzl1WlhSM2IzSnJjeTVqYjIwdmNHRnUKTFhOcVl5MXpaWEoyYVdObGN5MXBiblJsY20xbFpHbGhkR1V0WTJFdVkzSjBNQnNHQTFVZElBUVVNQkl3RUFZTwpLd1lCQkFHQnhuVURBUUVDQlFVd1ZRWURWUjBmQkU0d1REQktvRWlnUm9aRWFIUjBjRG92TDJOeWJDNXdZV3h2CllXeDBiMjVsZEhkdmNtdHpMbU52YlM5d1lXNHRjMnBqTFhObGNuWnBZMlZ6TFdsdWRHVnliV1ZrYVdGMFpTMWoKWVM1amNtd3dIUVlEVlIwT0JCWUVGQ1lkWUtYNkJNdXRFSGVFT1JlVURiVk1jbmJSTUE0R0ExVWREd0VCL3dRRQpBd0lCaGpBTkJna3Foa2lHOXcwQkFRd0ZBQU9DQVlFQVFBcURxcG05SkxneDdCNEluL3dDdjZzelR6VEhOSlAvCnFHTlkzOFB3S1Bsb0R5Rkljc3NWMFdRRVVaQng1Lzg0cFhiOHpjZlZjcm5xK0Y2ZWgxd09wWVdpeUdmbXVjN3oKeE0reUJrTUxUTENuRmIrNHNWWG12TkN3REh2Mzg2d2xFdWJtdkhXL09Tb1htcVpWZnYzR2NmL3plMUVuK3c3bgpSQnkxTGt6aE82dWxNZ0d5c2F2RlFwK1ZWb0UwcXNDTitpNEd2RFNMMzV2aWR0blZBTDlybXM5ZE5JNUtHN2xBCmVyZ25TUG90RVZ0RXB6KzBJalpmSVJwak5rL1BqZmFvKzhIdVZMVENlZytPWWFHcGlYbEE0MGtPVTYwcGpOaS8KK0l5djV2TTVrQXFxTGlZWHdkZjF3eGd1WHlyc0FZbGVzVW1GN0cvYW5LeTR2eEMwSVNuT05Db003ZWJTVSs1TApoSXFVREJsSjZVM3NIMHJ0NXRrOFpxY2tnSHVlT0VrbW54R1RFa0VRaHdWY3VESTl5NitCQnFCaTdIZTgyd1laCmphUTMxMTI0d0RnMXZTVTI1MGgxRVVKbDdCbXJzUXJJTzFYdXBuOFAzYXBCdjVaS2NpSHhEbkRwN05iYXhtdlIKR3U1SEhDb2RiYnh0d1pGWElTVGVUOHJJWTZ6QmEvc1AKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQ==
    mdns_ns1_api_key: AQICAHh9FMCI4nqR4EaDdVk/A+lg0C4wwTN7AQko2oLVQQqK8wEn3qNmYHFHM42Ce2gcu+QPAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMIKAo/G+x9gT3FA9VAgEQgC+yrAG941bFnUE7OQSOCKNks4gwBG+/Jf+8785XRnbgLbRUoIqOIUvHlxKoAtkLbw==
    mdns_ns1_datasource_id: d8e52934aaacc31fbeb749904e118279
    mdns_ns1_notifn_list_id: 63094a46d1e4b3108ebc2af0
    mdns_ns1_monitoring_enabled: 1
    gcp_orch_project_id_pattern: fm-molokai
    gcp_orch_folder_id: 905396310507
    gcp_orch_billing_id: 0191EA-C0E00D-70713E
    gcp_orch_project_deploy_limit: 1000
    gcp_host_svc_limit: 2500
    gcp_img_host: gov-image-gpcs-prod-01
    gcp_host_proj_list: pa-host-fm-molokai-1
    gcp_orch_proj_list: pa-orch-fm-molokai-1
    aws_env: fm-molokai
    gcp_proxy_machineType: e2-standard-2
    gcp_syslog_server: **************
    gcp_proxy_sshkey: gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDklWxFsQWW0wqBa4gnNpumT6O+c55ynw9vJmzGsDdEyjD1vSITFgwIOFDVanOEMLHxdgyRXRqjC769yQ0eG3XVuoRuj5qd2bhoLfYUGU9I1vQ0OsIsLYZEtOZCCcDqDn4GRGYJSd77YkK3rlHeal+CCTrpqyfYjQXeaM9zCZj01AzitgFaDnVJlzQW4MxB/+ivjGgRWRtsM6S1+LoXfvjJE3NstQM7bor9TClYVG3ooPfq2SgwSDgr6FSzvRd9I/pedN9DSFCvqOTDBZjSI514b8n7jx/Qf+N0/Ww+k8muKoEqA2B+mDQvvdpxuVLFK17GxoamzhzoJxb+/Sorp46P gce-user
    fw_ssh_key: gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCwx4R7wepo+eAs4e5XCqPTonErX9jOYgYqwyCjjas/eqDZbX2I+E3ds2GeL3mPtq8XWNGcS1m6N7RkUXTCq6VYej0E0SD3EFShkV8egiSYy6FEJ4O1GqFkL0rUYptn53xOqTtoEVGEH+RQ+rL8RXrvph7qo9EeCQEyc6eWyAobgjG04adxBsqk88Jjixh7VA8shutM/OL8TRUrbyrRt9zUEZk3dfmrcyQY9WAtbwOndmAlVkWeK/owxHV8D1W8BQDXsBDDmOLJ9ttj5QODSCtGq+GzMdM3iCm5/nGOBsZ3DVmorsZycqMGP/JdnBVJlNkez+t40KQqA3ZR5Aq1Xadp gce-user
    spr_fw_ssh_key: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCvJfwbfoVbxVS3IWZ0jXxpINSK/6dkfEahIh4z4F8EvDwQyN/ct9r4JDagxrTlsuFds4SHDZYGLSwL0+dAoOvcWl7ZxDSr5DbpZWb6iGDNKFhkdOGIvWXVgQyHP2ScWIjo+I1jV8e9h4OL6KuqtQAlHC59WAc8tNQ4B8+0GaMzEZt0odhaXuCj/TmoJmqWv0Hy03bnDYjc/kER2TeGW+NtwS2++2GeStyWvXsi7cxkLO5RFBbfRBx9z+UA/HxJP52J5HBFLqGeB/MneMbIy35qrnOhz1b59+BGP18IB64vd5dQo+r6BGuCNntEFB+1TE0ystmKfp8PMwHY9MXgfqHuZv4/TzZC08a/S5CtiBuE/FIH7T+Zni7oEK9q8rtlzm5Yy82hpcbNXTDMjVdEqvm6OqBqylpYTtWf1nLj/tMi2RGkfb3PLt8cmkjGbJnesvF94Er1YepBtRimuHddg2/rJD/AuHL2Bo2C0YkW6IqKC2gSx0+FWC31r7tVOdP5KY/M7GDCziauACeJ1rUieUqtYbqqhQC6LWUHSaCe170J4D5q6Sa4steSbSRkTMqQk08DrMhgoOIfUzh0sCEdIv7/cIPYoxMY5rgf0MLzgboP2JvMy9BG0yhhy4NhrWWvC3ua8J6CLdxMb1PCDSi+c6J2H/HMLaCkvyZOGNmuPcBIPQ== sase-pvt-region@SCMAC250593
    gcp_panrepo_bucket: panrepo-fm-molokai
    gcp_panrepo_role: roles/storage.admin
    dem_config: 327027
    epaas_sinefa_acct_id: 28574
    epaas_sinefa_api_key: AQICAHh9FMCI4nqR4EaDdVk/A+lg0C4wwTN7AQko2oLVQQqK8wF8Y+0LADH/mr2Jgph02v5YAAAAgzCBgAYJKoZIhvcNAQcGoHMwcQIBADBsBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDObqDheQEOO2EEsMtwIBEIA/Sb5oQgGm1c1Vj9pKYJug/CxvMma3PMIbDm+3VmYaPp1pHiw/JHtKbaj9kqhDg+01xxvFimGFSaBC5qEWD6Ek
    azr_service_account_id: b9fca69b-5714-42d9-a854-9488b4b319af
    azr_service_account_secret: AQICAHh9FMCI4nqR4EaDdVk/A+lg0C4wwTN7AQko2oLVQQqK8wHwKO1cXgV1LEQ+1P03HHlAAAAAhDCBgQYJKoZIhvcNAQcGoHQwcgIBADBtBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDL3yztlnXbUkuLq1QgIBEIBA531hXH+ozco4CCBRz6BXXoSFgTVveQziBQrWUJXJ10tNBh9Zlj7KEuGvc8Rc1h8Ut926pwappqg0F5SFGrYJfg==
    azr_tf_token: AQICAHh9FMCI4nqR4EaDdVk/A+lg0C4wwTN7AQko2oLVQQqK8wFfuSDmNCLteEDeH5Hxg80UAAAAvDCBuQYJKoZIhvcNAQcGoIGrMIGoAgEAMIGiBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDDwvvdxylq482ZlvpQIBEIB1Vb5Am2IJChVn+9XErMun+l8ZcUgnxJTfsKbcK3hZ5R5YAOwFUvd3bVuUwjX63mw/JNOuJd7GHxQ3wtMLwjoIp+drKF1dnAMw83wONrZUT6hI55+4adUsFKPKyEABBoykAxI3lYxJwiqplDHUcwmEtKCZFhHc
    azr_host_subscription_id: fd0d8c05-8c47-4a33-bfac-44f62d3f7945
    azr_image_subscription_id: 2a8831de-b127-4420-824d-ca2077ad4558
    #MARS CONFIG START
    forwarding_enabled_mars: true
    forwarding_enabled_all_events: true
    forwarding_enabled_config_commit: true
    gcp_project_id: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_eu: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_uk: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_fedramp: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_ca: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_japan: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_au: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_germany: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_in: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_singapore: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_ch: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_fr: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_qa: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_tw: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_kr: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_cn: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_ae: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_il: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_id: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_sa: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_pl: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_it: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_es: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_za: pa-sase-cosmos-fed-mod-stg-1
    gcp_project_id_fedmod: pa-sase-cosmos-fed-mod-stg-1
    receiving_enabled: false
    avisar_forwarding_enabled: true
    avisar_key_rotation: true
    avisar_ngpa_auto_migration: true
    #MARS CONFIG END
    aws_format: aws
    aws_region: us-west-2
    aws_env_type: fedramp-mod
    fedramp_dp_acct_id: ""
    r53_access_key_id: ""
    r53_secret_access_key: ""
    mdns_ns1_tcp_monitoring_enabled: 1
    mdns_ns1_monitoring_freq: 30
    mdns_ns1_monitoring_connect_timeout: 14
    mdns_ns1_monitoring_time_to_connect: 7
    mdns_ns1_monitoring_http_idle_timeout: 3
    mdns_ns1_monitoring_notifn_delay: 15
    mdns_r53_ep_zone_id: ""
    mdns_r53_monitoring_freq: 30
    mdns_r53_monitoring_failure_threshold: 3
    mdns_r53_monitoring_enabled: 1
    mdns_r53_tcp_monitoring_enabled: 1
    mdns_r53_monitored_svc_url: http://e2edpc.swg.prismaaccess.com:8888/e2edpc
    mdns_r53_monitoring_notifn_sns_topic: "ep-fedramp-hc-alarm"
    sasedp_infra_project: sasedp-infra-prod-01
    uda_provider_rbi:
      jwt_url: https://portal-prod.rbi.io/default/auth/jwks
    uda_provider_bce:
      jwt_url: https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>
    uda_base_zone: uda.prismaaccess.com
    rbi_base_zone: rbi.io
    rbi_infra_edl: https://cdn-dev.rbi.panclouddev.com/publicedls/rpi.edl
    nhp_proxyprotocol_version: v2
    openstack_orch_project_id: cust-prod
    auth_url: https://prod.pa-colo.core.pan.run:5000
    agent_pool_id: ""
    deploy_certificate: ""
    deploy_credential: ""
    execution_mode: ""
    av_buckets:
      gcp:
      - project: pa-host-fm-molokai-1
        bucket_name: av-repo-fm-molokai
      aws:
      - region: us-west-2
        bucket_name: av-repo-us-west-2-fm-molokai
      - region: us-east-1
        bucket_name: av-repo-us-east-1-fm-molokai
    content_rpm_buckets:
      gcp:
      - project: pa-host-fm-molokai-1
        bucket_name: content-rpm-repo-fm-molokai
      aws:
      - region: us-west-2
        bucket_name: content-rpm-repo-us-west-2-fm-molokai
      - region: us-east-1
        bucket_name: content-rpm-repo-us-east-1-fm-molokai
    av_rpm_buckets:
      gcp:
      - project: pa-host-fm-molokai-1
        bucket_name: av-rpm-repo-fm-molokai
      aws:
      - region: us-west-2
        bucket_name: av-rpm-repo-us-west-2-fm-molokai
      - region: us-east-1
        bucket_name: av-rpm-repo-us-east-1-fm-molokai
    tf_token: ""
    tf_prj: ""
    tf_org: PANW-SASE-Prod
    google_provider_version: "5.42.0"
    tf_server: https://app.terraform.io/
    osp_pan_tech_support: techsupport-dev-openstack
    provision_url: api.fm-molokai.prismaaccess.com
    tf_auto_migration_gcp: false
    usage_query_support_gcp: false
    ca_cert_path: lambda_certs/client_cert_fetch_prod.pem
    ca_key_path: lambda_certs/client_cert_fetch_prod.key
    prov_svc_task_attempt_max: 3
    epm_domain: epm.gpcloudservice.com
    traffic_mirroring_host_project_id: tm-shared-fm-molokai
    cnat_alert_email: <EMAIL>
