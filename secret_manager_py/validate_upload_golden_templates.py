# __author__ = '<PERSON> Bhavsar'
import os
import sys, yaml
import boto3
import json
from jinja2 import Environment, FileSystemLoader, loaders
import traceback
from botocore.exceptions import ClientError
import os
from collections import defaultdict

qa_access_key = os.environ.get('aws_access_key_id_cyr_qa')
qa_secret_key = os.environ.get('aws_secret_access_key_cyr_qa')

aws_env = sys.argv[1]
current_region = sys.argv[2]
env_type = sys.argv[3]
env_mode = sys.argv[4]
validate_env_data_against_golden_template_var = sys.argv[5]
passive_region = 'us-east-1'
print(f"aws_env = {aws_env}, current_region = {current_region}, env_type = {env_type}, env_mode = {env_mode}, validate_env_data_against_golden_template_var = {validate_env_data_against_golden_template_var} ")
validate_env_data_against_golden_template_var = str(validate_env_data_against_golden_template_var)
print(f"TYPE - {type(validate_env_data_against_golden_template_var)}")

def load_yaml_file(file_path,environment):
    """ Load the YAML content from a file, replacing placeholders with constant values. """
    with open(file_path, 'r') as file:
        content = file.read()
        # Replace placeholders with a constant value 'CONSTANT'
        content = content.replace('{{aws_env}}', environment).replace('{{passive_region}}',passive_region)
        content = content.replace('{{', '').replace('}}', '')
        return yaml.safe_load(content)

def verify_keys(template, real,path='',original_env_data_yaml_path='',golden_env_data_yaml_path=''):
    """ Recursively verify that all keys in real data are in the template. """
    for key in real:
        if key not in template:
            raise ValueError(f"key '{key}' found at path '{path}' in {original_env_data_yaml_path}, which is not present in golden template in {golden_env_data_yaml_path}")
        if isinstance(real[key], dict) and isinstance(template[key], dict):
            verify_keys(template[key], real[key], path=f"{path}.{key}" if path else key,original_env_data_yaml_path=original_env_data_yaml_path,golden_env_data_yaml_path=golden_env_data_yaml_path)


def validate_env_data_against_golden_template(file_path_with_golden_file_path):
    for environment,data in file_path_with_golden_file_path.items():
        print(f"check for environment = {environment}")
        original_env_data_yaml_path = data[0]
        golden_env_data_yaml_path = data[1]
        print(f"original_env_data_yaml_path = {original_env_data_yaml_path}")
        print(f"golden_env_data_yaml_path = {golden_env_data_yaml_path}")

        template_data = load_yaml_file(golden_env_data_yaml_path, environment)
        original_data = load_yaml_file(original_env_data_yaml_path, environment)

        # Verify the keys in the real data against the template
        try:
            verify_keys(template_data['cyrus'], original_data['cyrus'],original_env_data_yaml_path=original_env_data_yaml_path,golden_env_data_yaml_path=golden_env_data_yaml_path)
            print(f"All keys in the real data file = {original_env_data_yaml_path} are valid according to the template = {golden_env_data_yaml_path} for environment = {environment}")
        except ValueError as e:
            print(str(e))
            sys.exit(1)

def ensure_bucket_exists(s3_client, bucket_name):
    try:
        s3_client.head_bucket(Bucket=bucket_name)
    except ClientError as e:
        error_code = int(e.response['Error']['Code'])
        if error_code == 404:
            region = s3_client.meta.region_name
            s3_client.create_bucket(
                Bucket=bucket_name,
                CreateBucketConfiguration={'LocationConstraint': region}
            )
            print(f"Bucket '{bucket_name}' created successfully in region {region}.")
        else:
            raise

def upload_golden_templates_to_s3(local_dir, bucket_name):
    s3 = boto3.client('s3',
                      aws_access_key_id=qa_access_key,
                      aws_secret_access_key=qa_secret_key)

    ensure_bucket_exists(s3, bucket_name)

    for root, dirs, files in os.walk(local_dir):
        for file in files:
            local_path = os.path.join(root, file)
            s3_path = os.path.relpath(local_path, local_dir)

            print(f"Uploading {local_path} to {s3_path}")
            s3.upload_file(local_path, bucket_name, s3_path)

    print("Upload completed successfully!")


def set_bucket_policy(bucket_name):
    s3 = boto3.client('s3',
                      aws_access_key_id=qa_access_key,
                      aws_secret_access_key=qa_secret_key
                      )

    bucket_policy = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Deny",
                "Principal": "*",
                "Action": [
                    "s3:DeleteObject",
                    "s3:DeleteObjectVersion"
                ],
                "Resource": f"arn:aws:s3:::{bucket_name}/*"
            }
        ]
    }

    policy_string = json.dumps(bucket_policy)

    s3.put_bucket_policy(Bucket=bucket_name, Policy=policy_string)
    print(f"Policy successfully applied to bucket: {bucket_name}")

def find_env_files_map(directory):
    env_map = defaultdict(list)

    for filename in os.listdir(directory):
        if filename.endswith('_env_data.yaml'):
            for env_type in ['dev', 'qa', 'prod']:
                if filename.startswith(env_type):
                    env_name = filename.split('_')[0]
                    env_map[env_type].append(env_name)
                    break

    return dict(env_map)

if validate_env_data_against_golden_template_var == 'True':
    if current_region == 'us-west-2' and env_mode == 'commercial':
        if aws_env == 'dev7':
            original_map = find_env_files_map('secret_manager_py/envs')
            print(f"original_map = {original_map}")
            file_path_with_golden_file_path = {}
            for env_type, aws_envs in original_map.items():
                for aws_environment in aws_envs:
                    file_path_with_golden_file_path[aws_environment] = [
                        f'secret_manager_py/envs/{aws_environment}_env_data.yaml',
                        f'pan-cicd-golden-templates/{env_type}/{env_mode}/orch_env_data_yaml/{env_type}_env_data.yaml'
                    ]
            #{'prod6': ['secret_manager_py/envs/prod6_env_data.yaml', 'pan-cicd-golden-templates/prod/commercial/orch_env_data_yaml/prod_env_data.yaml'], 'prod2': ['secret_manager_py/envs/prod2_env_data.yaml', 'pan-cicd-golden-templates/prod/commercial/orch_env_data_yaml/prod_env_data.yaml']}
            print(f"file_path_with_golden_file_path = {file_path_with_golden_file_path}")
            validate_env_data_against_golden_template(file_path_with_golden_file_path)
        else:
            file_path_with_golden_file_path = {
                aws_env: [f'secret_manager_py/envs/{aws_env}_env_data.yaml',
                          f'pan-cicd-golden-templates/{env_type}/{env_mode}/orch_env_data_yaml/{env_type}_env_data.yaml']
            }
            validate_env_data_against_golden_template(file_path_with_golden_file_path)
    else:
        print("no need to execute validate_upload_golden_template for else condition.")


    if current_region == 'us-west-2' and env_type == 'qa' and env_mode == 'commercial':
        print(f"upload golden templates for env_type = qa")
        local_dir = 'pan-cicd-golden-templates'
        bucket_name = 'pan-cicd-golden-templates'
        try:
            upload_golden_templates_to_s3(local_dir, bucket_name)
            set_bucket_policy(bucket_name) #don't allow anyone to delete folder/files inside bucket.
        except Exception as ex:
            print(f"exception while (upload_golden_templates_to_s3) ex = {ex}")
            print(traceback.format_exc())
            sys.exit(1)  # Added this line to ensure CI fails on upload error
else:
    print("skip validation of env_data against golden template")