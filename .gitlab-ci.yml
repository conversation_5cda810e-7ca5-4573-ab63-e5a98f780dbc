include:
  - project: secure-cicd/secure-iac-steps
    ref: master
    file: secureiac.yaml
  - project: build-tools/ci-templates
    ref: master
    file: checkmarx_validation.yaml
  - project: build-tools/ci-templates
    ref: master
    file: blackduck_validation.yaml
  - project: engineering-productivity/ci-templates
    ref: master
    file: output/ai-review/base.yaml
  - project: engineering-productivity/ci-templates
    ref: master
    file: output/ai-build-failure-triage/ai-build-failure-triage.yaml
  - project: prisma-access/sase-cosmos-group/sase-controller-group/pa-infra-group/pa-infra-components-terraform
    ref: dev/develop
    file: cicd-templates/update-badge.yml
  - project: prisma-access/sase-cosmos-group/sase-controller-group/pa-infra-group/pa-infra-components-terraform
    ref: dev/develop
    file: cicd-templates/merge-request.yml


#variables: # test for dev16
#  SERVICE_ARTIFACTS_S3_BUCKET_US_WEST_2: pan-customer-provisioning-service
#  GENERIC_LAMBDA_S3_BUCKET_NAME: pantest-lambdas


variables:
  SERVICE_VERSION:
    value: ""
    description: "Service src zip (eg: *******-5.e4b38022d5.zip) - Default is latest from selected branch"
  COMMON_LAYER_ZIP:
    value: ""
    description: "Common saas-infra code zip (eg: common-layer-*******-119.pan_6859974e3d.zip)"
  DEPLOY_ENV:
    value: ""
    description: "Environments to deploy into (eg: dev9,dev16)"
  FORCE_DEPLOY_SAME_COMMIT_HASH:
    value: "False"
    options:
      - "True"
      - "False"
    description: "Values: True/False . Force and deploy even if the existing commit hash matches with new build. "
  CREATE_SECRETS_ONLY:
    value: "False"
    options:
      - "True"
      - "False"
    description: "Values: True/False . if we want to just deploy secrets the specify True."
  VALIDATE_ENV_DATA_AGAINST_GOLDEN_TEMPLATE:
    value: "True"
    options:
      - "True"
      - "False"
    description: "Values: True/False . validate aws_env_data.yaml against specific environment_type golden template."


stages:
  - secure_iac
  - checkmarx_validation
  - blackduck_validation
  - ai-review
  - configure_pipeline
  - run_pipeline
  - update_badge

secure_iac:
  extends: ".common_secureiac_validation"
  stage: secure_iac
  only:
    refs:
      - dev/develop
      - release/*
      - hotfix/*

checkmarx_validation:
  extends: ".checkmarx_validation"
  variables:
    ProjectName: "CxServer\\SP\\Company\\prismaaccess\\$CI_PROJECT_NAME"
    CX_BASE_BRANCH: "dev/develop"
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev/develop" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^release\/.+/)'
      changes:
        - src/**/*
      when: always
    - when: never
  allow_failure: false

blackduck_validation:
  extends: ".common_blackduck_validation"
  stage: blackduck_validation
  before_script:
    - pip3 install -r requirements.txt
  variables:
    SECURE_CICD_BD_IMAGE: "docker-infosec.art.code.pan.run/secure-cicd--prodsectools--blackduck_omnibus_debian--master:latest"
    SECURE_CICD_BD_VERSION_NAME: "latest"
    SECURE_CICD_PRODUCT_GROUP_NAME: "prismaaccess"
    SECURE_CICD_PRODUCT_TEAM_NAME: "cosmos"
    SECURE_CICD_GROUP_NUMBER: "4469"
    SECURE_CICD_PIPELINE_NAME: "$CI_PROJECT_NAME"
    SECURE_CICD_CI_OWNER: "engprod"
    SECURE_CICD_CI_ENV: "gcp"
    SECURE_CICD_DEPLOY_ENV: "prod"
    SECURE_CICD_BD_TOKEN: "${BD_TOKEN}"
    DETECT_PIP_REQUIREMENTS_PATH: "requirements.txt"
  only:
    refs:
      - dev/develop
      - release/*
      - hotfix/*
  allow_failure: true

ai-review-stage:
  extends: .ai-review
  stage: ai-review
  rules:
    - if: '$CI_COMMIT_REF_NAME == "dev/develop"'
    - if: '$CI_MERGE_REQUEST_ID'
  allow_failure: true

configure_pipeline:
  extends: ".configure_pipeline"

run_pipeline:
  extends: ".run_pipeline"

#below workaround for: https://gitlab.com/gitlab-org/gitlab/-/issues/280818
update_badge:
  extends: ".update-badge"
