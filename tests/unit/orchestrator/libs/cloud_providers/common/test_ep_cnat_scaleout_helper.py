import unittest
from unittest.mock import MagicMock, patch

from src.apps.orchestrator.libs.cloud_providers.common.ep_cnat_scaleout_helper import release_redundant_ips_for_tenant_region
from src.apps.orchestrator.libs.common.constants import NODE_TYPE_SWG_PROXY


class TestReleaseRedundantIps(unittest.TestCase):

    def setUp(self):
        self.dbh = MagicMock()
        self.dbh.logger = MagicMock()
        self.cust_id = "test_cust"
        self.region_id = "test_region"
        self.cloud_provider = "aws"

    @patch('src.apps.orchestrator.libs.cloud_providers.common.ep_cnat_scaleout_helper.CustEpaasConfigModel')
    @patch('src.apps.orchestrator.libs.cloud_providers.common.ep_cnat_scaleout_helper.release_cnat_ip_from_public_ip_pool')
    def test_release_redundant_ips_success(self, mock_release_ip, mock_config_model):
        # Setup
        mock_config = MagicMock()
        mock_config.get_entry.return_value = True
        mock_config.get_param.side_effect = lambda param: {
            'cnat_public_ips': '*******, *******, *******',
            'cnat_min_count': 1
        }.get(param)
        mock_config_model.return_value = mock_config

        # Execute
        result, ip_list = release_redundant_ips_for_tenant_region(
            self.cust_id, self.region_id, self.cloud_provider, self.dbh)

        # Assert
        self.assertTrue(result)
        self.assertEqual(ip_list, ['*******'])
        mock_config_model.assert_called_once_with(
            dbh=self.dbh,
            custid=self.cust_id,
            compute_region_id=self.region_id,
            cloud_provider=self.cloud_provider,
            node_type=NODE_TYPE_SWG_PROXY
        )
        mock_config.set_param.assert_called_once_with('cnat_public_ips', '*******')
        mock_config.save.assert_called_once()
        mock_release_ip.assert_called_once_with(self.cust_id, ['*******', '*******'], self.dbh.logger)

    @patch('src.apps.orchestrator.libs.cloud_providers.common.ep_cnat_scaleout_helper.CustEpaasConfigModel')
    @patch('src.apps.orchestrator.libs.cloud_providers.common.ep_cnat_scaleout_helper.release_cnat_ip_from_public_ip_pool')
    def test_no_redundant_ips(self, mock_release_ip, mock_config_model):
        # Setup
        mock_config = MagicMock()
        mock_config.get_entry.return_value = True
        mock_config.get_param.side_effect = lambda param: {
            'cnat_public_ips': '*******, *******',
            'cnat_min_count': 2
        }.get(param)
        mock_config_model.return_value = mock_config

        # Execute
        result, ip_list = release_redundant_ips_for_tenant_region(
            self.cust_id, self.region_id, self.cloud_provider, self.dbh)

        # Assert
        self.assertTrue(result)
        self.assertEqual(ip_list, ['*******', '*******'])
        mock_release_ip.assert_not_called()
        mock_config.set_param.assert_not_called()
        mock_config.save.assert_not_called()

