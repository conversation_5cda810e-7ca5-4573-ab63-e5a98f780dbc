import unittest
from unittest.mock import MagicMock, patch
import json
import sys
import os
import mock_imports_orchestrator

# Mock the dependencies before importing the module
mock_cfg = MagicMock()
mock_cfg.get = MagicMock(return_value="mock_value")

# Apply mocks to modules
with patch.dict('sys.modules', {
    'libs.cfg': MagicMock(),
    'libs.common.shared.grpc.proto.avisarpb.avisar_pb2_client': MagicMock(),
    'libs.msg_defs.interface': MagicMock(),
    'libs.msg_defs.workflows': MagicMock()
}):
    # Mock the cfg module
    sys.modules['libs.cfg'].cfg = mock_cfg

    # Create mock classes for imports
    mock_OrchHeader = MagicMock()
    mock_NtfyOnboardingMsg = MagicMock()
    mock_PrivSaseNodeUpdateEventMsg = MagicMock()

    # Assign mocks to the module
    sys.modules['libs.msg_defs.interface'].OrchHeader = mock_OrchHeader
    sys.modules['libs.msg_defs.interface'].NtfyOnboardingMsg = mock_NtfyOnboardingMsg
    sys.modules['libs.msg_defs.interface'].PrivSaseNodeUpdateEventMsg = mock_PrivSaseNodeUpdateEventMsg

    # Now import our module
    from src.apps.orchestrator.notification_service.core.notificationhdlr import PrivSaseNodeUpdateEventTopic


class TestPrivSaseNodeUpdateEventTopic(unittest.TestCase):
    def setUp(self):
        # Create mock objects for dependencies
        self.mock_logger = MagicMock()
        self.mock_dbh = MagicMock()
        self.mock_sqs = MagicMock()
        self.mock_sqs.sqs = MagicMock()
        self.mock_sqs.queues = []

        # Mock the queue returned by get_queue_by_name
        mock_queue = MagicMock()
        self.mock_sqs.sqs.get_queue_by_name = MagicMock(return_value=mock_queue)

        # Create test instance
        self.topic = "test_topic"
        self.next_q = "test_queue"

        # We need to patch AvisarContext since it's used in the constructor
        with patch('libs.common.shared.grpc.proto.avisarpb.avisar_pb2_client.AvisarContext', MagicMock()):
            self.test_instance = PrivSaseNodeUpdateEventTopic(
                self.topic,
                self.mock_dbh,
                self.mock_sqs,
                self.next_q,
                self.mock_logger
            )

        # Set up common test parameters
        self.action = "priv_sase_node_update_event"
        self.sase_private_region_id = 123
        self.custid = 456
        self.node_type = 789
        self.service_node_type = 101

        # Mock job object
        self.mock_job = MagicMock()
        self.mock_job.job_type = "test_job_type"
        self.mock_job.jobid = 12345
        self.mock_job.status = "INIT"

    def test_priv_sase_node_update_event_action_handler_success(self):
        """Test successful execution of the action handler"""
        # Reset mock classes to ensure clean state
        mock_OrchHeader.reset_mock()
        mock_NtfyOnboardingMsg.reset_mock()
        mock_PrivSaseNodeUpdateEventMsg.reset_mock()

        # Set up specific mock instances
        mock_orch_header_instance = MagicMock()
        mock_ntfy_msg_instance = MagicMock()
        mock_priv_sase_instance = MagicMock()

        # Configure return values
        mock_OrchHeader.return_value = mock_orch_header_instance
        mock_NtfyOnboardingMsg.return_value = mock_ntfy_msg_instance
        mock_PrivSaseNodeUpdateEventMsg.return_value = mock_priv_sase_instance

        # Mock the get_new_job method to return a successful job
        self.test_instance.priv_sase_node_update_event_get_new_job = MagicMock(return_value=self.mock_job)

        # Mock to_json to return a predictable value
        self.test_instance.to_json = MagicMock(return_value='{"mocked_json": true}')

        # Mock get_e2e_rastro_tag
        self.test_instance.get_e2e_rastro_tag = MagicMock(return_value={"trace_id": "test-trace"})

        # Call the method
        result = self.test_instance.priv_sase_node_update_event_action_handler(
            self.action,
            self.sase_private_region_id,
            self.custid,
            self.node_type,
            self.service_node_type
        )

        # Verify the method was called with correct arguments
        self.test_instance.priv_sase_node_update_event_get_new_job.assert_called_once_with(
            self.action,
            self.sase_private_region_id,
            self.custid,
            self.node_type,
            self.service_node_type
        )

        # Verify OrchHeader was created with the correct job info
        mock_OrchHeader.assert_called_once_with(self.mock_job.job_type, self.mock_job.jobid)

        # Verify PrivSaseNodeUpdateEventMsg was instantiated
        mock_PrivSaseNodeUpdateEventMsg.assert_called_once()

        # Verify metadata was set correctly
        mock_priv_sase_instance.set_PrivSaseNodeUpdateEventMsg_metadata.assert_called_once_with(
            sase_private_region_id=self.sase_private_region_id,
            service_node_type=self.service_node_type,
            custid=self.custid,
            node_type=self.node_type
        )

        # Verify NtfyOnboardingMsg was created
        mock_NtfyOnboardingMsg.assert_called_once()

        # Verify to_json was called
        self.test_instance.to_json.assert_called_once()

        # Verify send_msg was called with correct arguments
        self.mock_sqs.send_msg.assert_called_once_with(self.next_q, '{"mocked_json": true}')

    def test_priv_sase_node_update_event_action_handler_job_failure(self):
        """Test handler behavior when job creation fails"""
        # Mock the get_new_job method to return None (job creation failure)
        self.test_instance.priv_sase_node_update_event_get_new_job = MagicMock(return_value=None)

        # Call the method
        result = self.test_instance.priv_sase_node_update_event_action_handler(
            self.action,
            self.sase_private_region_id,
            self.custid,
            self.node_type,
            self.service_node_type
        )

        # Verify the method was called with correct arguments
        self.test_instance.priv_sase_node_update_event_get_new_job.assert_called_once_with(
            self.action,
            self.sase_private_region_id,
            self.custid,
            self.node_type,
            self.service_node_type
        )

        # Function should return False when job creation fails
        self.assertFalse(result)

    def test_priv_sase_node_update_event_action_handler_wrong_action(self):
        """Test handler behavior with incorrect action parameter"""
        # Mock get_new_job to ensure it's not called
        self.test_instance.priv_sase_node_update_event_get_new_job = MagicMock()

        # Call the method with an incorrect action
        result = self.test_instance.priv_sase_node_update_event_action_handler(
            "wrong_action",
            self.sase_private_region_id,
            self.custid,
            self.node_type,
            self.service_node_type
        )

        # Verify get_new_job was not called
        self.test_instance.priv_sase_node_update_event_get_new_job.assert_not_called()

        # Function should complete without errors but do nothing and return None
        self.assertIsNone(result)

    def test_case_insensitive_action(self):
        """Test that the action check is case-insensitive"""
        # Reset mocks
        mock_OrchHeader.reset_mock()
        mock_NtfyOnboardingMsg.reset_mock()
        mock_PrivSaseNodeUpdateEventMsg.reset_mock()

        # Set up mock instances
        mock_orch_header_instance = MagicMock()
        mock_ntfy_msg_instance = MagicMock()
        mock_priv_sase_instance = MagicMock()

        # Configure return values
        mock_OrchHeader.return_value = mock_orch_header_instance
        mock_NtfyOnboardingMsg.return_value = mock_ntfy_msg_instance
        mock_PrivSaseNodeUpdateEventMsg.return_value = mock_priv_sase_instance

        # Setup additional mocks
        self.test_instance.priv_sase_node_update_event_get_new_job = MagicMock(return_value=self.mock_job)
        self.test_instance.to_json = MagicMock(return_value='{"mocked_json": true}')
        self.test_instance.get_e2e_rastro_tag = MagicMock(return_value=None)

        # Call with mixed case action
        self.test_instance.priv_sase_node_update_event_action_handler(
            "PriV_SasE_NodE_UpdatE_Event",
            self.sase_private_region_id,
            self.custid,
            self.node_type,
            self.service_node_type
        )

        # Verify get_new_job was called (meaning action check passed)
        self.test_instance.priv_sase_node_update_event_get_new_job.assert_called_once()

        # Verify send_msg was called (completed successfully)
        self.mock_sqs.send_msg.assert_called_once()

    def test_set_metadata_called_correctly(self):
        """Test that the set_PrivSaseNodeUpdateEventMsg_metadata method is called with correct parameters"""
        # Reset mocks
        mock_OrchHeader.reset_mock()
        mock_NtfyOnboardingMsg.reset_mock()
        mock_PrivSaseNodeUpdateEventMsg.reset_mock()

        # Set up mock instances
        mock_orch_header_instance = MagicMock()
        mock_ntfy_msg_instance = MagicMock()
        mock_priv_sase_instance = MagicMock()

        # Configure return values
        mock_OrchHeader.return_value = mock_orch_header_instance
        mock_NtfyOnboardingMsg.return_value = mock_ntfy_msg_instance
        mock_PrivSaseNodeUpdateEventMsg.return_value = mock_priv_sase_instance

        # Setup additional mocks
        self.test_instance.priv_sase_node_update_event_get_new_job = MagicMock(return_value=self.mock_job)
        self.test_instance.to_json = MagicMock(return_value='{"mocked_json": true}')
        self.test_instance.get_e2e_rastro_tag = MagicMock(return_value=None)

        # Call the method
        self.test_instance.priv_sase_node_update_event_action_handler(
            self.action,
            self.sase_private_region_id,
            self.custid,
            self.node_type,
            self.service_node_type
        )

        # Verify set_PrivSaseNodeUpdateEventMsg_metadata was called with correct parameters
        mock_priv_sase_instance.set_PrivSaseNodeUpdateEventMsg_metadata.assert_called_once_with(
            sase_private_region_id=self.sase_private_region_id,
            service_node_type=self.service_node_type,
            custid=self.custid,
            node_type=self.node_type
        )

