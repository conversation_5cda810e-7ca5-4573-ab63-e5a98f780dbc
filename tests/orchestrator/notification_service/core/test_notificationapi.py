from unittest.mock import patch, MagicMock as mock
import pytest
from mock_imports_orchestrator import *
from notification_service.core.notificationapi import NotificationApi


class TestNotificationApi:
    @patch('notification_service.core.notificationapi.OrchestratorContext')
    @patch('notification_service.core.notificationapi.SqsApi')
    @patch('boto3.client')
    def test_init_success(self, mock_boto3, mock_sqs, mock_context):
        mock_ctx = mock_context.return_value
        mock_sqs.return_value.get_queue_arn.return_value = 'mock_q_arn'

        mock_sns = mock_boto3.return_value
        mock_sns.subscribe.return_value = {'SubscriptionArn': 'arn:aws:sns:us-east-1:123456789012:some-topic'}

        cfg = {
            'region': 'us-east-1',
            'log-root-dir' : "var/log/pan",
            'failure_q' : "failq.fifo",
            'notification_q': {'name': 'notif-queue'},
            'topics': [{'arn': 'arn:aws:sns:us-east-1:123456789012:some-topic', 'name' : "some-topic",
                        'next_q': "next-queue"}]
        }

        api = NotificationApi(cfg)
        assert api.topics[0]['arn'] == 'arn:aws:sns:us-east-1:123456789012:some-topic'

    @patch('notification_service.core.notificationapi.OrchestratorContext')
    @patch('notification_service.core.notificationapi.SqsApi')
    @patch('boto3.client')
    def test_init_exception(self, mock_boto3, mock_sqs, mock_context):
        mock_sqs.return_value.get_queue_arn.return_value = 'mock_q_arn'
        mock_exit = MagicMock()
        mock_sns = mock_boto3.return_value
        mock_sns.subscribe.side_effect = Exception('Failed to subscribe')

        cfg = {
            'region': 'us-east-1',
            'log-root-dir': "var/log/pan",
            'failure_q': "failq.fifo",
            'notification_q': {'name': 'notif-queue'},
        }

        with pytest.raises(SystemExit):
            NotificationApi(cfg)