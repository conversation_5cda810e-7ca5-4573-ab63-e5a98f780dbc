import unittest
import logging
import pytest
import traceback
import sys
from unittest import TestCase
from unittest.mock import patch
from unittest.mock import MagicMock as Mock
import mock_imports_orchestrator
import libs.model.instancemodel
import utils.Exceptions as customException
from libs.model.orchcfgmodel_v2 import OrchCfgModel_v2 as OrchCfgModel
from libs.cloud_providers.gcp.instance_manager.gcp_bringup import BringupGCPHandler, process_completed_nlb, reset_global_ip_in_public_ip_pool, calc_ipv6_interface_list
import libs.cloud_providers.gcp.instance_manager.gcp_bringup as gcp_bringup
import libs.model.instancemodel as instancemodel
from libs.common.shared.gcp_utils import gcp_authenticate
from libs.cloud_providers.aws.instance_manager.aws_bringup import get_aws_il5_sc_instance_vips, process_aws_il5_sc_instance_vips
import datetime

#vmId:  i-08d294bf4543be895
resp_ha_active_vm = {'Reservations': [{'Groups': [],
                   'Instances': [{'AmiLaunchIndex': 0,
                                  'Architecture': 'x86_64',
                                  'BlockDeviceMappings': [{'DeviceName': '/dev/xvda',
                                                           'Ebs': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 52, tzinfo=None),
                                                                   'DeleteOnTermination': True,
                                                                   'Status': 'attached',
                                                                   'VolumeId': 'vol-07ac60c5fc66d6ceb'}}],
                                  'CapacityReservationSpecification': {'CapacityReservationPreference': 'open'},
                                  'ClientToken': '',
                                  'CpuOptions': {'CoreCount': 4,
                                                 'ThreadsPerCore': 2},
                                  'EbsOptimized': False,
                                  'EnaSupport': True,
                                  'EnclaveOptions': {'Enabled': False},
                                  'HibernationOptions': {'Configured': False},
                                  'Hypervisor': 'xen',
                                  'IamInstanceProfile': {'Arn': 'arn:aws-us-gov:iam::044372533743:instance-profile/Customers/480518630_role',
                                                         'Id': 'AIPAQUVGPEHXRVQQGESI4'},
                                  'ImageId': 'ami-0f394d3c8149d8c2d',
                                  'InstanceId': 'i-08d294bf4543be895',
                                  'InstanceType': 'm5.2xlarge',
                                  'KeyName': 'orchestrator',
                                  'LaunchTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                  'MaintenanceOptions': {'AutoRecovery': 'default'},
                                  'MetadataOptions': {'HttpEndpoint': 'enabled',
                                                      'HttpProtocolIpv6': 'disabled',
                                                      'HttpPutResponseHopLimit': 1,
                                                      'HttpTokens': 'optional',
                                                      'InstanceMetadataTags': 'disabled',
                                                      'State': 'applied'},
                                  'Monitoring': {'State': 'disabled'},
                                  'NetworkInterfaces': [{'Attachment': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-016212fa5f6efd140',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 1,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-0159b6874e591095a',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-dp'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:9c:47:9f:1a:b1',
                                                         'NetworkInterfaceId': 'eni-0ccaf4556c8cf7f9c',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-100-80-48-110.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '*************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-100-80-48-110.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '*************'},
                                                                                {'Primary': False,
                                                                                 'PrivateDnsName': 'ip-100-80-48-63.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-04fab066e68dcb9d6',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'},
                                                        {'Attachment': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-0080b24c40f7004cf',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 0,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-0770325498151e55a',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-mgmt'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:1e:2a:72:bd:39',
                                                         'NetworkInterfaceId': 'eni-011f1382b15b5128e',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-100-80-52-94.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-100-80-52-94.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-08c17afb8516f55be',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'},
                                                        {'Attachment': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-043cd116387d8e768',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 3,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-027e74a0c30daf106',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-vgw'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:c1:c3:44:41:85',
                                                         'NetworkInterfaceId': 'eni-0413514734a9e1c4b',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-140-48-1-169.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-140-48-1-169.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '************'},
                                                                                {'Primary': False,
                                                                                 'PrivateDnsName': 'ip-140-48-1-174.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-04270186ee712ef00',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'},
                                                        {'Attachment': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-0c756f9b34427fc9f',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 2,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-0159b6874e591095a',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-dp'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:63:4b:37:48:75',
                                                         'NetworkInterfaceId': 'eni-007f5103dc8054831',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-100-80-50-154.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '*************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-100-80-50-154.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '*************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-058efbebb5315102d',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'}],
                                  'Placement': {'AvailabilityZone': 'us-gov-east-1a',
                                                'GroupName': '',
                                                'Tenancy': 'dedicated'},
                                  'PlatformDetails': 'Linux/UNIX',
                                  'PrivateDnsName': 'ip-100-80-52-94.us-gov-east-1.compute.internal',
                                  'PrivateDnsNameOptions': {'EnableResourceNameDnsAAAARecord': False,
                                                            'EnableResourceNameDnsARecord': False,
                                                            'HostnameType': 'ip-name'},
                                  'PrivateIpAddress': '************',
                                  'ProductCodes': [],
                                  'PublicDnsName': '',
                                  'RootDeviceName': '/dev/xvda',
                                  'RootDeviceType': 'ebs',
                                  'SecurityGroups': [{'GroupId': 'sg-0770325498151e55a',
                                                      'GroupName': 'adesa-480518630-108-sg-panos-mgmt'}],
                                  'SourceDestCheck': True,
                                  'State': {'Code': 16, 'Name': 'running'},
                                  'StateTransitionReason': '',
                                  'SubnetId': 'subnet-08c17afb8516f55be',
                                  'Tags': [{'Key': 'instancetype',
                                            'Value': 'sfw'},
                                           {'Key': 'resource_domain',
                                            'Value': 'prisma_access_core'},
                                           {'Key': 'Name',
                                            'Value': 'SFW_1484_us-gov-east-1_adesa-480518630'},
                                           {'Key': 'instancesize',
                                            'Value': 'gpcs-2xlarge'},
                                           {'Key': 'licensetype',
                                            'Value': 'internal'},
                                           {'Key': 'Customer',
                                            'Value': '480518630'},
                                           {'Key': 'aws:cloudformation:stack-name',
                                            'Value': 'cust34-FIREWALL1483-1CZS5E2YLGR7E'},
                                           {'Key': 'aws:cloudformation:stack-id',
                                            'Value': 'arn:aws-us-gov:cloudformation:us-gov-east-1:044372533743:stack/cust34-FIREWALL1483-1CZS5E2YLGR7E/c4ddd7d0-0988-11ef-a655-0e995af59d6b'},
                                           {'Key': 'computeregion',
                                            'Value': 'us-gov-east-1'},
                                           {'Key': 'nativemachinetype',
                                            'Value': 'm5.2xlarge'},
                                           {'Key': 'timestamp',
                                            'Value': '**********'},
                                           {'Key': 'supertenantid',
                                            'Value': '480518630'},
                                           {'Key': 'capacity_type',
                                            'Value': 'PA-CAP550'},
                                           {'Key': 'cloudprovider',
                                            'Value': 'aws'},
                                           {'Key': 'tenantid',
                                            'Value': '480518630'},
                                           {'Key': 'theater',
                                            'Value': 'americas'},
                                           {'Key': 'tenantname',
                                            'Value': 'adesa-480518630'},
                                           {'Key': 'aws:cloudformation:logical-id',
                                            'Value': 'EC2Instance2'},
                                           {'Key': 'environment',
                                            'Value': 'il5dev'}],
                                  'UsageOperation': 'RunInstances',
                                  'UsageOperationUpdateTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                  'VirtualizationType': 'hvm',
                                  'VpcId': 'vpc-08d72d68fe6ecf1af'}],
                   'OwnerId': '044372533743',
                   'RequesterId': '806090990039',
                   'ReservationId': 'r-034b6b396f2c3a898'}],
 'ResponseMetadata': {'HTTPHeaders': {'cache-control': 'no-cache, no-store',
                                      'content-type': 'text/xml;charset=UTF-8',
                                      'date': 'Thu, 09 May 2024 04:55:49 GMT',
                                      'server': 'AmazonEC2',
                                      'strict-transport-security': 'max-age=31536000; '
                                                                   'includeSubDomains',
                                      'transfer-encoding': 'chunked',
                                      'vary': 'accept-encoding',
                                      'x-amzn-requestid': '83366bdf-73f8-453f-be64-c291071939e2'},
                      'HTTPStatusCode': 200,
                      'RequestId': '83366bdf-73f8-453f-be64-c291071939e2',
                      'RetryAttempts': 0}}
# vmId:  i-0bb43cf742f2f11e3
resp_ha_passive_vm = {'Reservations': [{'Groups': [],
                   'Instances': [{'AmiLaunchIndex': 0,
                                  'Architecture': 'x86_64',
                                  'BlockDeviceMappings': [{'DeviceName': '/dev/xvda',
                                                           'Ebs': {'AttachTime': datetime.datetime(2024, 5, 8, 23, 46, 54, tzinfo=None),
                                                                   'DeleteOnTermination': True,
                                                                   'Status': 'attached',
                                                                   'VolumeId': 'vol-0384d4db33cca1c3d'}}],
                                  'CapacityReservationSpecification': {'CapacityReservationPreference': 'open'},
                                  'ClientToken': '',
                                  'CpuOptions': {'CoreCount': 4,
                                                 'ThreadsPerCore': 2},
                                  'EbsOptimized': False,
                                  'EnaSupport': True,
                                  'EnclaveOptions': {'Enabled': False},
                                  'HibernationOptions': {'Configured': False},
                                  'Hypervisor': 'xen',
                                  'IamInstanceProfile': {'Arn': 'arn:aws-us-gov:iam::044372533743:instance-profile/Customers/480518630_role',
                                                         'Id': 'AIPAQUVGPEHXRVQQGESI4'},
                                  'ImageId': 'ami-0856cdcf8be3d413f',
                                  'InstanceId': 'i-0bb43cf742f2f11e3',
                                  'InstanceType': 'm5.2xlarge',
                                  'KeyName': 'orchestrator',
                                  'LaunchTime': datetime.datetime(2024, 5, 8, 23, 46, 53, tzinfo=None),
                                  'MaintenanceOptions': {'AutoRecovery': 'default'},
                                  'MetadataOptions': {'HttpEndpoint': 'enabled',
                                                      'HttpProtocolIpv6': 'disabled',
                                                      'HttpPutResponseHopLimit': 1,
                                                      'HttpTokens': 'optional',
                                                      'InstanceMetadataTags': 'disabled',
                                                      'State': 'applied'},
                                  'Monitoring': {'State': 'disabled'},
                                  'NetworkInterfaces': [{'Attachment': {'AttachTime': datetime.datetime(2024, 5, 8, 23, 46, 53, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-0d27fe16894d4f04c',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 3,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-027e74a0c30daf106',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-vgw'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:ea:0a:24:ba:8d',
                                                         'NetworkInterfaceId': 'eni-011d0e3b4780715a7',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-140-48-1-178.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-140-48-1-178.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-04270186ee712ef00',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'},
                                                        {'Attachment': {'AttachTime': datetime.datetime(2024, 5, 8, 23, 46, 53, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-0bafce26f621698c9',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 0,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-0770325498151e55a',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-mgmt'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:b2:12:40:d0:79',
                                                         'NetworkInterfaceId': 'eni-00a30534a0d62c5b9',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-100-80-52-224.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '*************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-100-80-52-224.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '*************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-08c17afb8516f55be',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'},
                                                        {'Attachment': {'AttachTime': datetime.datetime(2024, 5, 8, 23, 46, 53, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-07f47c64a5fba19ce',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 2,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-0159b6874e591095a',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-dp'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:68:52:b6:b2:59',
                                                         'NetworkInterfaceId': 'eni-0cae15602b9a128c7',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-100-80-50-96.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-100-80-50-96.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-058efbebb5315102d',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'},
                                                        {'Association': {'IpOwnerId': '044372533743',
                                                                         'PublicDnsName': 'ec2-18-254-232-10.us-gov-east-1.compute.amazonaws.com',
                                                                         'PublicIp': '*************'},
                                                         'Attachment': {'AttachTime': datetime.datetime(2024, 5, 8, 23, 46, 53, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-07c558d04032d75a4',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 1,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-0159b6874e591095a',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-dp'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:f2:71:b3:3b:91',
                                                         'NetworkInterfaceId': 'eni-0a56d3db15946b20c',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-100-80-48-206.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '*************',
                                                         'PrivateIpAddresses': [{'Association': {'IpOwnerId': '044372533743',
                                                                                                 'PublicDnsName': 'ec2-18-254-232-10.us-gov-east-1.compute.amazonaws.com',
                                                                                                 'PublicIp': '*************'},
                                                                                 'Primary': True,
                                                                                 'PrivateDnsName': 'ip-100-80-48-206.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '*************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-04fab066e68dcb9d6',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'}],
                                  'Placement': {'AvailabilityZone': 'us-gov-east-1a',
                                                'GroupName': '',
                                                'Tenancy': 'dedicated'},
                                  'PlatformDetails': 'Linux/UNIX',
                                  'PrivateDnsName': 'ip-100-80-52-224.us-gov-east-1.compute.internal',
                                  'PrivateDnsNameOptions': {'EnableResourceNameDnsAAAARecord': False,
                                                            'EnableResourceNameDnsARecord': False,
                                                            'HostnameType': 'ip-name'},
                                  'PrivateIpAddress': '*************',
                                  'ProductCodes': [],
                                  'PublicDnsName': '',
                                  'RootDeviceName': '/dev/xvda',
                                  'RootDeviceType': 'ebs',
                                  'SecurityGroups': [{'GroupId': 'sg-0770325498151e55a',
                                                      'GroupName': 'adesa-480518630-108-sg-panos-mgmt'}],
                                  'SourceDestCheck': True,
                                  'State': {'Code': 16, 'Name': 'running'},
                                  'StateTransitionReason': '',
                                  'SubnetId': 'subnet-08c17afb8516f55be',
                                  'Tags': [{'Key': 'Name',
                                            'Value': 'SFW_1483_us-gov-east-1_adesa-480518630'},
                                           {'Key': 'aws:cloudformation:logical-id',
                                            'Value': 'EC2Instance1'},
                                           {'Key': 'licensetype',
                                            'Value': 'internal'},
                                           {'Key': 'timestamp',
                                            'Value': '**********'},
                                           {'Key': 'aws:cloudformation:stack-name',
                                            'Value': 'cust34-FIREWALL1483-1CZS5E2YLGR7E'},
                                           {'Key': 'tenantid',
                                            'Value': '480518630'},
                                           {'Key': 'instancetype',
                                            'Value': 'sfw'},
                                           {'Key': 'cloudprovider',
                                            'Value': 'aws'},
                                           {'Key': 'theater',
                                            'Value': 'americas'},
                                           {'Key': 'aws:cloudformation:stack-id',
                                            'Value': 'arn:aws-us-gov:cloudformation:us-gov-east-1:044372533743:stack/cust34-FIREWALL1483-1CZS5E2YLGR7E/c4ddd7d0-0988-11ef-a655-0e995af59d6b'},
                                           {'Key': 'nativemachinetype',
                                            'Value': 'm5.2xlarge'},
                                           {'Key': 'Customer',
                                            'Value': '480518630'},
                                           {'Key': 'supertenantid',
                                            'Value': '480518630'},
                                           {'Key': 'capacity_type',
                                            'Value': 'PA-CAP550'},
                                           {'Key': 'computeregion',
                                            'Value': 'us-gov-east-1'},
                                           {'Key': 'instancesize',
                                            'Value': 'gpcs-2xlarge'},
                                           {'Key': 'environment',
                                            'Value': 'il5dev'},
                                           {'Key': 'resource_domain',
                                            'Value': 'prisma_access_core'},
                                           {'Key': 'tenantname',
                                            'Value': 'adesa-480518630'}],
                                  'UsageOperation': 'RunInstances',
                                  'UsageOperationUpdateTime': datetime.datetime(2024, 5, 8, 23, 46, 53, tzinfo=None),
                                  'VirtualizationType': 'hvm',
                                  'VpcId': 'vpc-08d72d68fe6ecf1af'}],
                   'OwnerId': '044372533743',
                   'RequesterId': '806090990039',
                   'ReservationId': 'r-0bf5de47df33f5a61'}],
 'ResponseMetadata': {'HTTPHeaders': {'cache-control': 'no-cache, no-store',
                                      'content-type': 'text/xml;charset=UTF-8',
                                      'date': 'Thu, 09 May 2024 04:58:19 GMT',
                                      'server': 'AmazonEC2',
                                      'strict-transport-security': 'max-age=31536000; '
                                                                   'includeSubDomains',
                                      'transfer-encoding': 'chunked',
                                      'vary': 'accept-encoding',
                                      'x-amzn-requestid': '787d39ee-a1c5-430e-81dd-e96c50a74be7'},
                      'HTTPStatusCode': 200,
                      'RequestId': '787d39ee-a1c5-430e-81dd-e96c50a74be7',
                      'RetryAttempts': 0}}

negative_vgw_vip_resp = {'Reservations': [{'Groups': [],
                   'Instances': [{'AmiLaunchIndex': 0,
                                  'Architecture': 'x86_64',
                                  'BlockDeviceMappings': [{'DeviceName': '/dev/xvda',
                                                           'Ebs': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 52, tzinfo=None),
                                                                   'DeleteOnTermination': True,
                                                                   'Status': 'attached',
                                                                   'VolumeId': 'vol-07ac60c5fc66d6ceb'}}],
                                  'CapacityReservationSpecification': {'CapacityReservationPreference': 'open'},
                                  'ClientToken': '',
                                  'CpuOptions': {'CoreCount': 4,
                                                 'ThreadsPerCore': 2},
                                  'EbsOptimized': False,
                                  'EnaSupport': True,
                                  'EnclaveOptions': {'Enabled': False},
                                  'HibernationOptions': {'Configured': False},
                                  'Hypervisor': 'xen',
                                  'IamInstanceProfile': {'Arn': 'arn:aws-us-gov:iam::044372533743:instance-profile/Customers/480518630_role',
                                                         'Id': 'AIPAQUVGPEHXRVQQGESI4'},
                                  'ImageId': 'ami-0f394d3c8149d8c2d',
                                  'InstanceId': 'i-08d294bf4543be895',
                                  'InstanceType': 'm5.2xlarge',
                                  'KeyName': 'orchestrator',
                                  'LaunchTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                  'MaintenanceOptions': {'AutoRecovery': 'default'},
                                  'MetadataOptions': {'HttpEndpoint': 'enabled',
                                                      'HttpProtocolIpv6': 'disabled',
                                                      'HttpPutResponseHopLimit': 1,
                                                      'HttpTokens': 'optional',
                                                      'InstanceMetadataTags': 'disabled',
                                                      'State': 'applied'},
                                  'Monitoring': {'State': 'disabled'},
                                  'NetworkInterfaces': [{'Attachment': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-016212fa5f6efd140',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 1,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-0159b6874e591095a',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-dp'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:9c:47:9f:1a:b1',
                                                         'NetworkInterfaceId': 'eni-0ccaf4556c8cf7f9c',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-100-80-48-110.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '*************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-100-80-48-110.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '*************'},
                                                                                {'Primary': False,
                                                                                 'PrivateDnsName': 'ip-100-80-48-63.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-04fab066e68dcb9d6',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'},
                                                        {'Attachment': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-0080b24c40f7004cf',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 0,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-0770325498151e55a',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-mgmt'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:1e:2a:72:bd:39',
                                                         'NetworkInterfaceId': 'eni-011f1382b15b5128e',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-100-80-52-94.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-100-80-52-94.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-08c17afb8516f55be',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'},
                                                        {'Attachment': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-043cd116387d8e768',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 3,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-027e74a0c30daf106',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-vgw'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:c1:c3:44:41:85',
                                                         'NetworkInterfaceId': 'eni-0413514734a9e1c4b',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-140-48-1-169.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-140-48-1-169.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '************'},
                                                                                {'Primary': True,
                                                                                 'PrivateDnsName': 'ip-140-48-1-174.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-04270186ee712ef00',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'},
                                                        {'Attachment': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-0c756f9b34427fc9f',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 2,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-0159b6874e591095a',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-dp'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:63:4b:37:48:75',
                                                         'NetworkInterfaceId': 'eni-007f5103dc8054831',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-100-80-50-154.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '*************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-100-80-50-154.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '*************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-058efbebb5315102d',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'}],
                                  'Placement': {'AvailabilityZone': 'us-gov-east-1a',
                                                'GroupName': '',
                                                'Tenancy': 'dedicated'},
                                  'PlatformDetails': 'Linux/UNIX',
                                  'PrivateDnsName': 'ip-100-80-52-94.us-gov-east-1.compute.internal',
                                  'PrivateDnsNameOptions': {'EnableResourceNameDnsAAAARecord': False,
                                                            'EnableResourceNameDnsARecord': False,
                                                            'HostnameType': 'ip-name'},
                                  'PrivateIpAddress': '************',
                                  'ProductCodes': [],
                                  'PublicDnsName': '',
                                  'RootDeviceName': '/dev/xvda',
                                  'RootDeviceType': 'ebs',
                                  'SecurityGroups': [{'GroupId': 'sg-0770325498151e55a',
                                                      'GroupName': 'adesa-480518630-108-sg-panos-mgmt'}],
                                  'SourceDestCheck': True,
                                  'State': {'Code': 16, 'Name': 'running'},
                                  'StateTransitionReason': '',
                                  'SubnetId': 'subnet-08c17afb8516f55be',
                                  'Tags': [{'Key': 'instancetype',
                                            'Value': 'sfw'},
                                           {'Key': 'resource_domain',
                                            'Value': 'prisma_access_core'},
                                           {'Key': 'Name',
                                            'Value': 'SFW_1484_us-gov-east-1_adesa-480518630'},
                                           {'Key': 'instancesize',
                                            'Value': 'gpcs-2xlarge'},
                                           {'Key': 'licensetype',
                                            'Value': 'internal'},
                                           {'Key': 'Customer',
                                            'Value': '480518630'},
                                           {'Key': 'aws:cloudformation:stack-name',
                                            'Value': 'cust34-FIREWALL1483-1CZS5E2YLGR7E'},
                                           {'Key': 'aws:cloudformation:stack-id',
                                            'Value': 'arn:aws-us-gov:cloudformation:us-gov-east-1:044372533743:stack/cust34-FIREWALL1483-1CZS5E2YLGR7E/c4ddd7d0-0988-11ef-a655-0e995af59d6b'},
                                           {'Key': 'computeregion',
                                            'Value': 'us-gov-east-1'},
                                           {'Key': 'nativemachinetype',
                                            'Value': 'm5.2xlarge'},
                                           {'Key': 'timestamp',
                                            'Value': '**********'},
                                           {'Key': 'supertenantid',
                                            'Value': '480518630'},
                                           {'Key': 'capacity_type',
                                            'Value': 'PA-CAP550'},
                                           {'Key': 'cloudprovider',
                                            'Value': 'aws'},
                                           {'Key': 'tenantid',
                                            'Value': '480518630'},
                                           {'Key': 'theater',
                                            'Value': 'americas'},
                                           {'Key': 'tenantname',
                                            'Value': 'adesa-480518630'},
                                           {'Key': 'aws:cloudformation:logical-id',
                                            'Value': 'EC2Instance2'},
                                           {'Key': 'environment',
                                            'Value': 'il5dev'}],
                                  'UsageOperation': 'RunInstances',
                                  'UsageOperationUpdateTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                  'VirtualizationType': 'hvm',
                                  'VpcId': 'vpc-08d72d68fe6ecf1af'}],
                   'OwnerId': '044372533743',
                   'RequesterId': '806090990039',
                   'ReservationId': 'r-034b6b396f2c3a898'}],
 'ResponseMetadata': {'HTTPHeaders': {'cache-control': 'no-cache, no-store',
                                      'content-type': 'text/xml;charset=UTF-8',
                                      'date': 'Thu, 09 May 2024 04:55:49 GMT',
                                      'server': 'AmazonEC2',
                                      'strict-transport-security': 'max-age=31536000; '
                                                                   'includeSubDomains',
                                      'transfer-encoding': 'chunked',
                                      'vary': 'accept-encoding',
                                      'x-amzn-requestid': '83366bdf-73f8-453f-be64-c291071939e2'},
                      'HTTPStatusCode': 200,
                      'RequestId': '83366bdf-73f8-453f-be64-c291071939e2',
                      'RetryAttempts': 0}}
# This response has both DP vip but with  Primary as True it should have been secondary
negative_dp_vip_resp = {'Reservations': [{'Groups': [],
                   'Instances': [{'AmiLaunchIndex': 0,
                                  'Architecture': 'x86_64',
                                  'BlockDeviceMappings': [{'DeviceName': '/dev/xvda',
                                                           'Ebs': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 52, tzinfo=None),
                                                                   'DeleteOnTermination': True,
                                                                   'Status': 'attached',
                                                                   'VolumeId': 'vol-07ac60c5fc66d6ceb'}}],
                                  'CapacityReservationSpecification': {'CapacityReservationPreference': 'open'},
                                  'ClientToken': '',
                                  'CpuOptions': {'CoreCount': 4,
                                                 'ThreadsPerCore': 2},
                                  'EbsOptimized': False,
                                  'EnaSupport': True,
                                  'EnclaveOptions': {'Enabled': False},
                                  'HibernationOptions': {'Configured': False},
                                  'Hypervisor': 'xen',
                                  'IamInstanceProfile': {'Arn': 'arn:aws-us-gov:iam::044372533743:instance-profile/Customers/480518630_role',
                                                         'Id': 'AIPAQUVGPEHXRVQQGESI4'},
                                  'ImageId': 'ami-0f394d3c8149d8c2d',
                                  'InstanceId': 'i-08d294bf4543be895',
                                  'InstanceType': 'm5.2xlarge',
                                  'KeyName': 'orchestrator',
                                  'LaunchTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                  'MaintenanceOptions': {'AutoRecovery': 'default'},
                                  'MetadataOptions': {'HttpEndpoint': 'enabled',
                                                      'HttpProtocolIpv6': 'disabled',
                                                      'HttpPutResponseHopLimit': 1,
                                                      'HttpTokens': 'optional',
                                                      'InstanceMetadataTags': 'disabled',
                                                      'State': 'applied'},
                                  'Monitoring': {'State': 'disabled'},
                                  'NetworkInterfaces': [{'Attachment': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-016212fa5f6efd140',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 1,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-0159b6874e591095a',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-dp'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:9c:47:9f:1a:b1',
                                                         'NetworkInterfaceId': 'eni-0ccaf4556c8cf7f9c',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-100-80-48-110.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '*************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-100-80-48-110.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '*************'},
                                                                                {'Primary': True,
                                                                                 'PrivateDnsName': 'ip-100-80-48-63.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-04fab066e68dcb9d6',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'},
                                                        {'Attachment': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-0080b24c40f7004cf',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 0,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-0770325498151e55a',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-mgmt'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:1e:2a:72:bd:39',
                                                         'NetworkInterfaceId': 'eni-011f1382b15b5128e',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-100-80-52-94.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-100-80-52-94.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-08c17afb8516f55be',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'},
                                                        {'Attachment': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-043cd116387d8e768',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 3,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-027e74a0c30daf106',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-vgw'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:c1:c3:44:41:85',
                                                         'NetworkInterfaceId': 'eni-0413514734a9e1c4b',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-140-48-1-169.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-140-48-1-169.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '************'},
                                                                                {'Primary': False,
                                                                                 'PrivateDnsName': 'ip-140-48-1-174.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-04270186ee712ef00',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'},
                                                        {'Attachment': {'AttachTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                                                        'AttachmentId': 'eni-attach-0c756f9b34427fc9f',
                                                                        'DeleteOnTermination': False,
                                                                        'DeviceIndex': 2,
                                                                        'NetworkCardIndex': 0,
                                                                        'Status': 'attached'},
                                                         'Description': '',
                                                         'Groups': [{'GroupId': 'sg-0159b6874e591095a',
                                                                     'GroupName': 'adesa-480518630-108-sg-panos-dp'}],
                                                         'InterfaceType': 'interface',
                                                         'Ipv6Addresses': [],
                                                         'MacAddress': '06:63:4b:37:48:75',
                                                         'NetworkInterfaceId': 'eni-007f5103dc8054831',
                                                         'OwnerId': '044372533743',
                                                         'PrivateDnsName': 'ip-100-80-50-154.us-gov-east-1.compute.internal',
                                                         'PrivateIpAddress': '*************',
                                                         'PrivateIpAddresses': [{'Primary': True,
                                                                                 'PrivateDnsName': 'ip-100-80-50-154.us-gov-east-1.compute.internal',
                                                                                 'PrivateIpAddress': '*************'}],
                                                         'SourceDestCheck': True,
                                                         'Status': 'in-use',
                                                         'SubnetId': 'subnet-058efbebb5315102d',
                                                         'VpcId': 'vpc-08d72d68fe6ecf1af'}],
                                  'Placement': {'AvailabilityZone': 'us-gov-east-1a',
                                                'GroupName': '',
                                                'Tenancy': 'dedicated'},
                                  'PlatformDetails': 'Linux/UNIX',
                                  'PrivateDnsName': 'ip-100-80-52-94.us-gov-east-1.compute.internal',
                                  'PrivateDnsNameOptions': {'EnableResourceNameDnsAAAARecord': False,
                                                            'EnableResourceNameDnsARecord': False,
                                                            'HostnameType': 'ip-name'},
                                  'PrivateIpAddress': '************',
                                  'ProductCodes': [],
                                  'PublicDnsName': '',
                                  'RootDeviceName': '/dev/xvda',
                                  'RootDeviceType': 'ebs',
                                  'SecurityGroups': [{'GroupId': 'sg-0770325498151e55a',
                                                      'GroupName': 'adesa-480518630-108-sg-panos-mgmt'}],
                                  'SourceDestCheck': True,
                                  'State': {'Code': 16, 'Name': 'running'},
                                  'StateTransitionReason': '',
                                  'SubnetId': 'subnet-08c17afb8516f55be',
                                  'Tags': [{'Key': 'instancetype',
                                            'Value': 'sfw'},
                                           {'Key': 'resource_domain',
                                            'Value': 'prisma_access_core'},
                                           {'Key': 'Name',
                                            'Value': 'SFW_1484_us-gov-east-1_adesa-480518630'},
                                           {'Key': 'instancesize',
                                            'Value': 'gpcs-2xlarge'},
                                           {'Key': 'licensetype',
                                            'Value': 'internal'},
                                           {'Key': 'Customer',
                                            'Value': '480518630'},
                                           {'Key': 'aws:cloudformation:stack-name',
                                            'Value': 'cust34-FIREWALL1483-1CZS5E2YLGR7E'},
                                           {'Key': 'aws:cloudformation:stack-id',
                                            'Value': 'arn:aws-us-gov:cloudformation:us-gov-east-1:044372533743:stack/cust34-FIREWALL1483-1CZS5E2YLGR7E/c4ddd7d0-0988-11ef-a655-0e995af59d6b'},
                                           {'Key': 'computeregion',
                                            'Value': 'us-gov-east-1'},
                                           {'Key': 'nativemachinetype',
                                            'Value': 'm5.2xlarge'},
                                           {'Key': 'timestamp',
                                            'Value': '**********'},
                                           {'Key': 'supertenantid',
                                            'Value': '480518630'},
                                           {'Key': 'capacity_type',
                                            'Value': 'PA-CAP550'},
                                           {'Key': 'cloudprovider',
                                            'Value': 'aws'},
                                           {'Key': 'tenantid',
                                            'Value': '480518630'},
                                           {'Key': 'theater',
                                            'Value': 'americas'},
                                           {'Key': 'tenantname',
                                            'Value': 'adesa-480518630'},
                                           {'Key': 'aws:cloudformation:logical-id',
                                            'Value': 'EC2Instance2'},
                                           {'Key': 'environment',
                                            'Value': 'il5dev'}],
                                  'UsageOperation': 'RunInstances',
                                  'UsageOperationUpdateTime': datetime.datetime(2024, 5, 3, 20, 7, 51, tzinfo=None),
                                  'VirtualizationType': 'hvm',
                                  'VpcId': 'vpc-08d72d68fe6ecf1af'}],
                   'OwnerId': '044372533743',
                   'RequesterId': '806090990039',
                   'ReservationId': 'r-034b6b396f2c3a898'}],
 'ResponseMetadata': {'HTTPHeaders': {'cache-control': 'no-cache, no-store',
                                      'content-type': 'text/xml;charset=UTF-8',
                                      'date': 'Thu, 09 May 2024 04:55:49 GMT',
                                      'server': 'AmazonEC2',
                                      'strict-transport-security': 'max-age=31536000; '
                                                                   'includeSubDomains',
                                      'transfer-encoding': 'chunked',
                                      'vary': 'accept-encoding',
                                      'x-amzn-requestid': '83366bdf-73f8-453f-be64-c291071939e2'},
                      'HTTPStatusCode': 200,
                      'RequestId': '83366bdf-73f8-453f-be64-c291071939e2',
                      'RetryAttempts': 0}}
#http://bhfsteve.blogspot.com/2012/06/patching-tip-using-mocks-in-python-unit.html
class TestIL5SCVips():

    @patch('libs.cloud_providers.aws.instance_manager.aws_bringup.get_aws_govcloud_dp_acct_boto3_client_for_service')
    def test_il5_sc_cluster_vips(self,  mock_get_aws_govcloud_dp_acct_boto3_client_for_service):
        log = logging.getLogger()

        print(mock_get_aws_govcloud_dp_acct_boto3_client_for_service)

        mock_get_aws_govcloud_dp_acct_boto3_client_for_service.return_value.describe_instances.return_value = resp_ha_active_vm
        primary_instance_vips = get_aws_il5_sc_instance_vips(log, "region", "1234")
        print("primary_instance_vips: ", primary_instance_vips)
        expected_res = [{'DeviceIndex': 1, 'SecondaryPrivateIpAddress': '************'}, {'DeviceIndex': 3, 'SecondaryPrivateIpAddress': '************'}]
        assert (primary_instance_vips == expected_res)

        mock_get_aws_govcloud_dp_acct_boto3_client_for_service.return_value.describe_instances.return_value = resp_ha_passive_vm
        secondary_instance_vips = get_aws_il5_sc_instance_vips(log, "region", "1234")
        print("secondary_instance_vips:", secondary_instance_vips)
        expected_res = []
        assert (secondary_instance_vips == expected_res)
        cft_dp_vip = '************'
        cft_vgw_vip = '************'
        dp_vip, vgw_vip = process_aws_il5_sc_instance_vips(log, primary_instance_vips,
                                               secondary_instance_vips, cft_dp_vip,
                                               cft_vgw_vip)
        e_dp_vip = '************'
        e_vgw_vip = '************'
        print("vgw_vip:", vgw_vip, 'dp_vip:', dp_vip)
        assert(dp_vip == e_dp_vip and vgw_vip == e_vgw_vip)


    @patch('libs.cloud_providers.aws.instance_manager.aws_bringup.get_aws_govcloud_dp_acct_boto3_client_for_service')
    def test_il5_sc_cluster_cft_vips(self, mock_get_aws_govcloud_dp_acct_boto3_client_for_service):
        """
        log = logging.getLogger()
        res = get_aws_il5_sc_instance_vips(log, "region", "1234")
        print(res)
        expected_res = [{'DeviceIndex': 3, 'SecondaryPrivateIpAddress': '************'}]

        assert (res == expected_res)
        """
        log = logging.getLogger()

        print(mock_get_aws_govcloud_dp_acct_boto3_client_for_service)

        mock_get_aws_govcloud_dp_acct_boto3_client_for_service.return_value.describe_instances.return_value = resp_ha_passive_vm
        primary_instance_vips = get_aws_il5_sc_instance_vips(log, "region", "1234")
        print("primary_instance_vips: ", primary_instance_vips)
        expected_res = []
        assert (primary_instance_vips == expected_res)

        mock_get_aws_govcloud_dp_acct_boto3_client_for_service.return_value.describe_instances.return_value = resp_ha_passive_vm
        secondary_instance_vips = get_aws_il5_sc_instance_vips(log, "region", "1234")
        print("secondary_instance_vips:", secondary_instance_vips)
        expected_res = []
        assert (secondary_instance_vips == expected_res)
        cft_dp_vip = '************'
        cft_vgw_vip = '************'
        dp_vip, vgw_vip = process_aws_il5_sc_instance_vips(log, primary_instance_vips,
                                                           secondary_instance_vips, cft_dp_vip,
                                                           cft_vgw_vip)
        e_dp_vip = '************'
        e_vgw_vip = '************'
        print("vgw_vip:", vgw_vip, 'dp_vip:', dp_vip)
        assert (dp_vip == e_dp_vip and vgw_vip == e_vgw_vip)

    @patch('libs.cloud_providers.aws.instance_manager.aws_bringup.get_aws_govcloud_dp_acct_boto3_client_for_service')
    def test_il5_sc_cluster_passive_vips(self,  mock_get_aws_govcloud_dp_acct_boto3_client_for_service):
        log = logging.getLogger()

        print(mock_get_aws_govcloud_dp_acct_boto3_client_for_service)

        mock_get_aws_govcloud_dp_acct_boto3_client_for_service.return_value.describe_instances.return_value = resp_ha_passive_vm
        primary_instance_vips = get_aws_il5_sc_instance_vips(log, "region", "1234")
        print("primary_instance_vips: ", primary_instance_vips)
        expected_res = []

        assert (primary_instance_vips == expected_res)

        mock_get_aws_govcloud_dp_acct_boto3_client_for_service.return_value.describe_instances.return_value = resp_ha_active_vm
        secondary_instance_vips = get_aws_il5_sc_instance_vips(log, "region", "1234")
        print("secondary_instance_vips:", secondary_instance_vips)
        expected_res = [{'DeviceIndex': 1, 'SecondaryPrivateIpAddress': '************'},
                        {'DeviceIndex': 3, 'SecondaryPrivateIpAddress': '************'}]

        assert (secondary_instance_vips == expected_res)

        cft_dp_vip = '************'
        cft_vgw_vip = '************'
        dp_vip, vgw_vip = process_aws_il5_sc_instance_vips(log, primary_instance_vips,
                                               secondary_instance_vips, cft_dp_vip,
                                               cft_vgw_vip)
        e_dp_vip = '************'
        e_vgw_vip = '************'
        print("vgw_vip:", vgw_vip, 'dp_vip:', dp_vip)
        assert(dp_vip == e_dp_vip and vgw_vip == e_vgw_vip)


    @patch('libs.cloud_providers.aws.instance_manager.aws_bringup.get_aws_govcloud_dp_acct_boto3_client_for_service')
    def test_il5_sc_cluster_vgw_vips(self, mock_get_aws_govcloud_dp_acct_boto3_client_for_service):
        log = logging.getLogger()

        print(mock_get_aws_govcloud_dp_acct_boto3_client_for_service)

        mock_get_aws_govcloud_dp_acct_boto3_client_for_service.return_value.describe_instances.return_value = negative_vgw_vip_resp
        primary_instance_vips = get_aws_il5_sc_instance_vips(log, "region", "1234")
        print("primary_instance_vips: ", primary_instance_vips)
        expected_res = [{'DeviceIndex': 1, 'SecondaryPrivateIpAddress': '************'}]

        assert (primary_instance_vips == expected_res)

        mock_get_aws_govcloud_dp_acct_boto3_client_for_service.return_value.describe_instances.return_value = negative_dp_vip_resp
        secondary_instance_vips = get_aws_il5_sc_instance_vips(log, "region", "1234")
        print("secondary_instance_vips:", secondary_instance_vips)
        expected_res = [{'DeviceIndex': 3, 'SecondaryPrivateIpAddress': '************'}]

        assert (secondary_instance_vips == expected_res)

        cft_dp_vip = '************'
        cft_vgw_vip = '************'
        dp_vip, vgw_vip = process_aws_il5_sc_instance_vips(log, primary_instance_vips,
                                                           secondary_instance_vips, cft_dp_vip,
                                                           cft_vgw_vip)
        e_dp_vip = '************'
        e_vgw_vip = ''
        print("vgw_vip:", vgw_vip, 'dp_vip:', dp_vip)
        assert (dp_vip == e_dp_vip and vgw_vip == e_vgw_vip)

    @patch('libs.cloud_providers.aws.instance_manager.aws_bringup.get_aws_govcloud_dp_acct_boto3_client_for_service')
    def test_il5_sc_cluster_passive_vgw_vips(self, mock_get_aws_govcloud_dp_acct_boto3_client_for_service):
        print(mock_get_aws_govcloud_dp_acct_boto3_client_for_service)
        mock_get_aws_govcloud_dp_acct_boto3_client_for_service.return_value.describe_instances.return_value = resp_ha_passive_vm

        log = logging.getLogger()
        res = get_aws_il5_sc_instance_vips(log, "region", "1234")
        print(res)
        expected_res = []

        assert (res == expected_res)
