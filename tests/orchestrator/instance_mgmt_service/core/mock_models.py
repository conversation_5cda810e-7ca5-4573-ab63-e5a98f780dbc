class MockCustomerModel:
    def __init__(self, dbh=None, acct_id=None):
        self.fields = ["id",
                       "name",
                       "acct_id",
                       "project_id",
                       "super_acct_id",
                       "support_acct_id",
                       ]
        self.values = [None] * len(self.fields)

        self.id = 123
        self.name = "testCust"
        self.acct_id = acct_id
        self.project_id = "testPrj"
        self.dbh = dbh

    def get_param(self, field):
        myidx = self.fields.index(field)
        return self.values[myidx]
