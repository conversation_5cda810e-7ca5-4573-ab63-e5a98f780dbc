import logging
import pytest
from unittest.mock import patch
import utils.Exceptions as customException
import instance_mgmt_service.core.api_defs.post_process as post_process

class Mock_conn():
    def __init__(self):
        pass

    def close(self):
        pass

    def commit(self):
        pass

class Mock_cursor():
    def __init__(self, testSuiteIdx):
        self.sql = None
        self.params = None
        self.testSuiteIdx = testSuiteIdx

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        return [[""]]

    def fetchone(self):
        return ["1"]

    def lastrowid(self):
        pass

class Mock_DBHandler():
    def __init__(self, logger, testSuiteIdx):
        self.logger = logger
        self.conn = Mock_conn()
        self.testSuiteIdx = testSuiteIdx

    def conn(self):
        return self.conn

    def get_cursor(self):
        return  Mock_cursor(self.testSuiteIdx)

    def cursorclose(self, cursor):
        return

class TestPostProcess():
    @patch('instance_mgmt_service.core.api_defs.post_process.get_nlb_ip_address_map', return_value={"215" : "************"})
    @patch('instance_mgmt_service.core.api_defs.post_process.calc_ipv6_interface_list', return_value={"215" : "2606:f4c0:26cc:11b:8000:34:0:0/96"})
    def test_setup_post_processor(self, mock_calc, mock_get_nlb_ip_address_map, caplog):
        caplog.set_level(logging.INFO)
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger,testSuiteIdx=0)
        custid = 95
        region_id = 215
        cloud_provider = "gcp"
        trigger_update_seq_nr = 1234
        processed_nlb_dict = {"nlb_ip_address": "************",
                          "nlb_ipv6_address": "2601:f4c0:26cc:11b:8111:34:0:0/96",
                          "nlb_egress_ipv6_list_with_compute": {},
                          "nlb_egress_ipv6_list": {}}
        cluster_info = {"cluster_id": 100,
                "primary_id": 100,
                "primary_vm_id": "foobar",
                "primary_mgmt_ip": "************",
                "primary_pvt_ip": "************",
                "secondary_id": 101,
                "secondary_vm_id": "foobaz",
                "secondary_mgmt_ip": "************",
                "secondary_pvt_ip": "************",
                "public_ip": "************"}

        post_processor = post_process.setup_post_processor(dbh, test_logger, custid, 
                                                           region_id, cloud_provider, 
                                                           trigger_update_seq_nr,
                                                           processed_nlb_dict, cluster_info)
        assert post_processor._primary_id == 100
        post_processor._primary_nlb_domain_details == "************"
        post_processor._primary_nlb_public_ipv6 == "2601:f4c0:26cc:11b:8111:34:0:0/96"

    @patch('instance_mgmt_service.core.api_defs.post_process.get_nlb_ip_address_map', return_value={"215" : "************"})
    @patch('instance_mgmt_service.core.api_defs.post_process.calc_ipv6_interface_list', side_effect = customException.InstanceNotExistsException("test"))
    def test_setup_post_processor_exception(self, mock_calc, mock_get_nlb_ip_address_map, caplog):
        test_logger=logging.getLogger('testlogger')
        caplog.set_level(logging.INFO)
        dbh=Mock_DBHandler(logger=test_logger,testSuiteIdx=0)
        custid = 95
        region_id = 215
        cloud_provider = "gcp"
        trigger_update_seq_nr = 1234
        processed_nlb_dict = {"nlb_ip_address": None,
                          "nlb_ipv6_address": None,
                          "nlb_egress_ipv6_list_with_compute": {},
                          "nlb_egress_ipv6_list": {}}
        cluster_info = {"cluster_id": 100,
                "primary_id": 100,
                "primary_vm_id": "foobar",
                "primary_mgmt_ip": "************",
                "primary_pvt_ip": "************",
                "secondary_id": 101,
                "secondary_vm_id": "foobaz",
                "secondary_mgmt_ip": "************",
                "secondary_pvt_ip": "************",
                "public_ip": "************"}

        post_processor = post_process.setup_post_processor(dbh, test_logger, custid, 
                                                           region_id, cloud_provider, 
                                                           trigger_update_seq_nr,
                                                           processed_nlb_dict, cluster_info)
        assert post_processor._primary_id == 100
        assert "Error calculating the interface_ipv6_list during post processing for custid" in caplog.text

    @patch('instance_mgmt_service.core.api_defs.post_process.CustomerModel')
    @patch('instance_mgmt_service.core.api_defs.post_process.RastroLoggingInfra')
    @patch('instance_mgmt_service.core.api_defs.post_process.validate_body')
    @patch('instance_mgmt_service.core.api_defs.post_process.process_completed_nlb')
    @patch('instance_mgmt_service.core.api_defs.post_process.get_post_processor')
    def test_post_process_no_cluster_info(self, mock_get_post, mock_process_nlb, mock_validate, mock_rastro, mock_customer, mock_dbh, mock_request, mock_response):
        mock_customer.return_value.get_param.side_effect = [1234, 0, "project-123"]
        mock_process_nlb.return_value = {"nlb_ip_address": "*******"}
        body = post_process.PostProcessModel(
            tenant_id=1234,
            region_id=201,
            cloud_provider="gcp",
            service_type_id=1,
            trigger_update=1,
            cluster_info=None,
            workspace_name="test-workspace"
        )
        
        result = post_process.post_process(mock_dbh, mock_request, 1, body, mock_response)
        
        assert result == {"status": "ok"}
        mock_get_post.assert_called_once()
        mock_get_post.return_value.process_instance_release_pending.assert_called_once()
        assert mock_response.headers["X-Trace-Id"] == "test-trace-id"
