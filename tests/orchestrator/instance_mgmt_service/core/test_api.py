from fastapi.testclient import TestClient
from unittest.mock import patch, ANY

import instance_mgmt_service
from mock_models import *

import pytest
import mock_imports_orchestrator
import utils.Exceptions as customException

from instance_mgmt_service.core.api import app

client = TestClient(app)


post_process_api_test_cases =  [
        # Invalid Method on API
        ("put", {}, 405),
        # Missing payload parameter to trigger the HTTPException
        ("post",
            {
                "region_id": 201,
                "cloud_provider": "gcp",
                "service_type_id": 0,
                "cluster_info": [
                    {
                        "cluster_id": 100,
                        "primary_id": 100,
                        "primary_vm_id": "foobar",
                        "primary_mgmt_ip": "x.x.x.x",
                        "primary_pvt_ip": "x.x.x.x",
                        "secondary_id": 101,
                        "secondary_vm_id": "foobaz",
                        "secondary_mgmt_ip": "x.x.x.x",
                        "secondary_pvt_ip": "x.x.x.x",
                        "public_ip": "x.x.x.x"
                        }
                    ]
                },
            422),
        # Valid API call
        ("post",
            {
                "tenant_id": 123,
                "region_id": 201,
                "cloud_provider": "gcp",
                "service_type_id": 0,
                "cluster_info": [
                    {
                        "cluster_id": 100,
                        "primary_id": 100,
                        "primary_vm_id": "foobar",
                        "primary_mgmt_ip": "**********",
                        "primary_pvt_ip": "**********",
                        "secondary_id": 101,
                        "secondary_vm_id": "foobaz",
                        "secondary_mgmt_ip": "**********",
                        "secondary_pvt_ip": "**********",
                        "public_ip": "***********"
                        }
                    ]
                },
            200),
        ("post",
            {
                "tenant_id": 123,
                "cloud_provider": "gcp",
                "service_type_id": 0,
                "cluster_info": [
                    {
                        "cluster_id": 100,
                        "primary_id": 100,
                        "primary_vm_id": "foobar",
                        "primary_mgmt_ip": "**********",
                        "primary_pvt_ip": "**********",
                        "secondary_id": 101,
                        "secondary_vm_id": "foobaz",
                        "secondary_mgmt_ip": "**********",
                        "secondary_pvt_ip": "**********",
                        "public_ip": "***********"
                    }
                ],
                "private_peering_info": [
                    {
                        "name": "colo-int-foobar",
                        "self_link": "https://www.googleapis.com/compute/v1/projects/cust-XXX/regions/ABC/interconnectAttachments/colo-int-foobar",
                        "state": "ACTIVE",
                        "region": "us-west2",
                        "router": "https://www.googleapis.com/compute/v1/projects/cust-XXX/regions/ABC/interconnectAttachments/colo-int-rtr",
                        "pairing_key": "1234567/ABC/1"
                    }
                ],
                "elb_info": [
                    {
                        "name":             	"nlb-16940-us-west-201-labreabakery-1083089583",
                        "type":             	"Elb",
                        "egress_ip_v6_list": 	{"201" :"2606:f4c0:270c:11b:8000:1a:0:0/96"}
                    }
                ]
            },
        200)
        ]

deployment_post_process_api_test_cases =  [
        # Invalid Method on API
        ("post", {}, 405),
        # Missing payload parameter to trigger the HTTPException
        ("put",
         {
            "uuid":"11111111-1111-1111-1111-111111111111",
            "status":"ok",
            "output": {
                "instances": [
                    {
                        "name":"GPGW_1234_us-west-123_renault-123456789",
                        "interfaces":[
                            {
                                "name":"nic-mgmt",
                                "private_ip":"**********"
                            },
                            {
                                "name":"nic-dp",
                                "public_ip":"********",
                                "private_ip":"**********"
                            }
                        ]
                    }
                ]
            }
         },
         400),
        # Valid API call
        ("put",
         {
            "uuid":"11111111-1111-1111-1111-111111111111",
            "status":"ok",
            "output": {
                "instances": [
                    {
                        "name":"GPGW_1234_us-west-123_xxxxxxxxxxx-123456789",
                        "vm_id":"122222222",
                        "interfaces":[
                            {
                                "name":"nic-mgmt",
                                "private_ip":"**********"
                            },
                            {
                                "name":"nic-dp",
                                "public_ip":"********",
                                "private_ip":"**********"
                            }
                        ]
                    }
                ]
            }
         },
         200)
    ]


class TestAPI():
    def test_fast_api_get_status(self):
        response = client.get("/api/instance_management/status")
        assert response.status_code == 200

    @pytest.mark.parametrize("method,payload,expected_status_code", post_process_api_test_cases)
    @patch('instance_mgmt_service.core.api_defs.post_process.calc_ipv6_interface_list')
    @patch('instance_mgmt_service.core.api_defs.post_process.get_nlb_ip_address_map')
    @patch('instance_mgmt_service.core.api_defs.post_process.process_completed_nlb')
    @patch('instance_mgmt_service.core.api_defs.post_process.CustomerModel')
    @patch('instance_mgmt_service.core.api.api_handler_dbh')
    def test_fast_api_post_process(self, mock_dbh, mock_cust_model, mock_process_completed_nlb, mock_get_nlb_ip_address_map,
                                   mock_calc_ipv6_interface_list, method, payload, expected_status_code):
        '''
        Test the post_process API handler served by the inst-mgmt service.
        Implicitly also tests the invocation of the post_process implementation
        '''
        mock_cust_model.return_value = MockCustomerModel()
        mock_process_completed_nlb.return_value = {}
        mock_get_nlb_ip_address_map.return_value = {}
        mock_calc_ipv6_interface_list.return_value = {}
        assert mock_dbh is instance_mgmt_service.core.api.api_handler_dbh
        assert mock_cust_model is instance_mgmt_service.core.api_defs.post_process.CustomerModel
        assert mock_process_completed_nlb is instance_mgmt_service.core.api_defs.post_process.process_completed_nlb
        assert mock_get_nlb_ip_address_map is instance_mgmt_service.core.api_defs.post_process.get_nlb_ip_address_map

        uri = "/api/instance_management/post_process/1234"
        if method == "put":
            response = client.put(uri)
        elif method == "post":
            response = client.post(uri,
                            headers={"X-Trace-Id": "2b96d99d-0797-4099-b722-c1cefdd55272"},
                            json=payload)
        else:
            # Unsupported method
            assert False == True
        assert response.status_code == expected_status_code

    @pytest.mark.parametrize("method,payload,expected_status_code",
                             deployment_post_process_api_test_cases)
    @patch('instance_mgmt_service.core.api.api_handler_dbh')
    def test_deployment_fast_api_post_process(self, mock_dbh, method, payload, expected_status_code):
        '''
        Test the post_process API handler served by the inst-mgmt service.
        Implicitly also tests the invocation of the post_process implementation
        '''
        uri = "/api/instance_management/deployment_post_process/1234"
        if method == "post":
            response = client.post(uri)
        elif method == "put":
            response = client.put(uri,
                                  json=payload)
        else:
            # Unsupported method
            assert False == True
        assert response.status_code == expected_status_code
