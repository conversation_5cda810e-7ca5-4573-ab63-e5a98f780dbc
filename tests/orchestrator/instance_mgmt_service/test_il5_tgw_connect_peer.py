import unittest
import logging
import pytest
import traceback
import sys
from unittest import TestCase
from unittest.mock import patch
from unittest.mock import MagicMock as Mock
import mock_imports_orchestrator
import libs.model.instancemodel
import utils.Exceptions as customException
from libs.model.orchcfgmodel_v2 import OrchCfgModel_v2 as OrchCfgModel
from libs.cloud_providers.gcp.instance_manager.gcp_bringup import BringupGCPHandler, process_completed_nlb, reset_global_ip_in_public_ip_pool, calc_ipv6_interface_list
import libs.cloud_providers.gcp.instance_manager.gcp_bringup as gcp_bringup
import libs.model.instancemodel as instancemodel
from libs.common.shared.gcp_utils import gcp_authenticate
from libs.cloud_providers.aws.instance_manager.aws_bringup import get_aws_il5_sc_instance_vips, process_aws_il5_sc_instance_vips
from libs.cloud_providers.aws.instance_manager.aws_tgw import il5_tgw_event_add_sc_instance, il5_tgw_event_delete_sc_instance

import datetime

tgw_created = {}
tgw_create_error = {}
vpc_attach_connect = {'attach_id': 'tgw-attach-096725798e7be1c6b', 'connect_id': 'tgw-attach-0f6cfc5fb7df083d5'}
peer_connect_ip_list_matching = {'peerList':
                            [{'State': 'available',
                              'TgwAddress': '*************',
                              'PeerAddress': '************',
                              'InsideCidrBlocks': ['*************/29'],
                              'TgwConnectPeerId': 'tgw-connect-peer-05820356df1c60ac4',
                              'PeerBGPAsnId': 64512, 'TGWBGPAsnId': 64512}]}
peer_connect_ip_list = {'peerList':
                            [{'State': 'available',
                              'TgwAddress': '*************',
                              'PeerAddress': '************',
                              'InsideCidrBlocks': ['*************/29'],
                              'TgwConnectPeerId': 'tgw-connect-peer-05820356df1c60ac4',
                              'PeerBGPAsnId': 64512, 'TGWBGPAsnId': 64512}]}
# test IL5 TransitGateway Connect Peer
class TestIL5TgwConPeer():
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.is_env_fedramp_il5')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_tgw_list_connect_peer_ips')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_find_tgw_connect_attachment')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_tgw_get_vpcId_using_instanceId')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.get_aws_datapath_tgwId')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.get_aws_govcloud_dp_acct_boto3_client_for_service')
    def test_il5_create_tgw_connect_peer_matching(self, mock_dp_boto3_client, mock_get_tgwId,
                                         mock_vpcId_using_instanceId, mock_tgw_connect_attachment,
                                         mock_peer_ips, mock_is_il5):
        logger = logging.getLogger()

        mock_dp_boto3_client.return_value.describe_instances.return_value = None
        mock_dp_boto3_client.return_value.describe_transit_gateway_vpc_attachments.return_value = None
        mock_dp_boto3_client.return_value.describe_transit_gateway_connects = None

        # mock_dp_acct_boto3_client.return_value.describe_instances
        tgwId = 'tgw-0bf7794d385c95b05'
        mock_get_tgwId.return_value = tgwId

        vpcId = 'vpc-0d0274e0fcdf48767'
        mock_vpcId_using_instanceId.return_value = vpcId

        mock_tgw_connect_attachment.return_value = vpc_attach_connect

        mock_peer_ips.return_value = peer_connect_ip_list_matching

        mock_is_il5.return_value = True
        regionName = 'us-gov-west-1'
        vmId = 'vmid-1'
        # ###############
        # matching VIP
        # ###############
        vgwVip = '************'
        vpcId = None
        tgwId = None
        asnId = 64512
        nameTag = "ry-test"
        res = il5_tgw_event_add_sc_instance(logger, regionName, vmId, vgwVip, vpcId, tgwId, asnId, nameTag)
        logger.info(str(res))
        assert (res == peer_connect_ip_list_matching['peerList'][0])

    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.is_env_fedramp_il5')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_tgw_create_connect_peer')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_tgw_list_connect_peer_ips')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_find_tgw_connect_attachment')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_tgw_get_vpcId_using_instanceId')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.get_aws_datapath_tgwId')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.get_aws_govcloud_dp_acct_boto3_client_for_service')
    def test_il5_create_tgw_connect_peer(self, mock_dp_boto3_client, mock_get_tgwId,
                                         mock_vpcId_using_instanceId, mock_tgw_connect_attachment,
                                         mock_peer_ips, mock_create_connect_peer, mock_is_il5):

        logger = logging.getLogger()

        mock_dp_boto3_client.return_value.describe_instances.return_value = None
        mock_dp_boto3_client.return_value.describe_transit_gateway_vpc_attachments.return_value = None
        mock_dp_boto3_client.return_value.describe_transit_gateway_connects = None

        # mock_dp_acct_boto3_client.return_value.describe_instances
        tgwId = 'tgw-0bf7794d385c95b05'
        mock_get_tgwId.return_value = tgwId

        vpcId = 'vpc-0d0274e0fcdf48767'
        mock_vpcId_using_instanceId.return_value = vpcId

        mock_tgw_connect_attachment.return_value = vpc_attach_connect

        mock_peer_ips.return_value = peer_connect_ip_list

        mock_create_connect_peer.return_value = peer_connect_ip_list['peerList'][0]
        mock_is_il5.return_value = True
        regionName = 'us-gov-west-1'
        vmId = 'vmid-1'
        vgwVip = '************'
        vpcId = None
        tgwId = None
        asnId = 64512
        nameTag = "ry-test"
        res = il5_tgw_event_add_sc_instance(logger, regionName, vmId, vgwVip, vpcId, tgwId, asnId, nameTag)
        assert (res == peer_connect_ip_list['peerList'][0])

    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.is_env_fedramp_il5')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_tgw_create_connect_peer')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_tgw_list_connect_peer_ips')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_find_tgw_connect_attachment')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_tgw_get_vpcId_using_instanceId')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.get_aws_datapath_tgwId')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.get_aws_govcloud_dp_acct_boto3_client_for_service')
    def test_il5_delete_tgw_connect_peer(self, mock_dp_boto3_client, mock_get_tgwId,
                                                  mock_vpcId_using_instanceId, mock_tgw_connect_attachment,
                                                  mock_peer_ips, mock_delete_connect_peer, mock_is_il5):
        logger = logging.getLogger()
        logger = logging.getLogger()

        mock_dp_boto3_client.return_value.describe_instances.return_value = None
        mock_dp_boto3_client.return_value.describe_transit_gateway_vpc_attachments.return_value = None
        mock_dp_boto3_client.return_value.describe_transit_gateway_connects = None

        # mock_dp_acct_boto3_client.return_value.describe_instances
        tgwId = 'tgw-0bf7794d385c95b05'
        mock_get_tgwId.return_value = tgwId

        vpcId = 'vpc-0d0274e0fcdf48767'
        mock_vpcId_using_instanceId.return_value = vpcId

        mock_tgw_connect_attachment.return_value = vpc_attach_connect

        mock_peer_ips.return_value = peer_connect_ip_list

        mock_delete_connect_peer.return_value = peer_connect_ip_list['peerList'][0]
        mock_is_il5.return_value = True

        regionName = 'us-gov-west-1'
        vmId = 'vmid-1'
        vgwVip = '************'
        vpcId = None
        tgwId = None
        res = il5_tgw_event_delete_sc_instance(logger, regionName, vmId, vgwVip, vpcId, tgwId)
        assert (res == peer_connect_ip_list['peerList'][0])
        return

    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.is_env_fedramp_il5')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_tgw_create_connect_peer')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_tgw_list_connect_peer_ips')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_find_tgw_connect_attachment')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.il5_tgw_get_vpcId_using_instanceId')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.get_aws_datapath_tgwId')
    @patch('libs.cloud_providers.aws.instance_manager.aws_tgw.get_aws_govcloud_dp_acct_boto3_client_for_service')
    def test_il5_delete_tgw_connect_peer_mismatch(self, mock_dp_boto3_client, mock_get_tgwId,
                                         mock_vpcId_using_instanceId, mock_tgw_connect_attachment,
                                         mock_peer_ips, mock_delete_connect_peer, mock_is_il5):
        logger = logging.getLogger()
        logger = logging.getLogger()

        mock_dp_boto3_client.return_value.describe_instances.return_value = None
        mock_dp_boto3_client.return_value.describe_transit_gateway_vpc_attachments.return_value = None
        mock_dp_boto3_client.return_value.describe_transit_gateway_connects = None

        # mock_dp_acct_boto3_client.return_value.describe_instances
        tgwId = 'tgw-0bf7794d385c95b05'
        mock_get_tgwId.return_value = tgwId

        vpcId = 'vpc-0d0274e0fcdf48767'
        mock_vpcId_using_instanceId.return_value = vpcId

        mock_tgw_connect_attachment.return_value = vpc_attach_connect

        mock_peer_ips.return_value = peer_connect_ip_list

        mock_delete_connect_peer.return_value = peer_connect_ip_list['peerList'][0]
        mock_is_il5.return_value = True

        regionName = 'us-gov-west-1'
        vmId = 'vmid-1'
        vgwVip = '************'
        vpcId = None
        tgwId = None
        res = il5_tgw_event_delete_sc_instance(logger, regionName, vmId, vgwVip, vpcId, tgwId)
        assert (res == {})
        return



