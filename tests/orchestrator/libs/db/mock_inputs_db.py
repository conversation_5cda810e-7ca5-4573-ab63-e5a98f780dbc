class Mock_cursor_update_status_msg_for_cust_topology_node():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        return True

    def close(self):
        pass

class Mock_cursor_success_get_central_cache_service_endpoint():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        return ["http://gpcs_region_mapping/url"]

    def close(self):
        pass

class Mock_cursor_success_get_central_cache_service_endpoint_failed():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        return None

    def close(self):
        pass

class Mock_cursor_success_empty_string_gpcs_tenant_region_mapping_query():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        if 'gpcs_tenant_region_mapping' in self.sql:
            return [""]
        else:
            return ["http://gpcs_region_mapping/url"]

    def close(self):
        pass

class Mock_cursor_success_gpcs_region_mapping_query():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        if 'gpcs_tenant_region_mapping' in self.sql:
            return []
        else:
            return ["http://gpcs_region_mapping/url"]

    def close(self):
        pass


class Mock_cursor_success_gpcs_tenant_region_mapping_query():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        return ["http://gpcs_region_mapping/url"]

    def close(self):
        pass

class Mock_cursor_success_empty_string_gpcs_tenant_region_mapping_query():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        if 'gpcs_tenant_region_mapping' in self.sql:
            return [""]
        else:
            return ["http://gpcs_region_mapping/url"]

    def close(self):
        pass

class Mock_cursor_success_gpcs_region_mapping_query():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        if 'gpcs_tenant_region_mapping' in self.sql:
            return []
        else:
            return ["http://gpcs_region_mapping/url"]

    def close(self):
        pass

class Mock_cursor_failed_gpcs_region_mapping_query():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        if 'gpcs_tenant_region_mapping' in self.sql:
            return []
        elif 'gpcs_region_mapping' in self.sql:
            return [""]
        return ["http://services_endpoint_mapping/url"]

    def close(self):
        pass

class Mock_cursor_failed_services_endpoint_mapping_query():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        if 'gpcs_tenant_region_mapping' in self.sql:
            return []
        elif 'gpcs_region_mapping' in self.sql:
            return [""]
        return [""]

    def close(self):
        pass

class Mock_cursor_success_get_ciam_service_endpoint():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        return ["http://services_endpoint_mapping/url"]

    def close(self):
        pass

class Mock_cursor_success_get_ciam_service_endpoint_failed():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        return None

    def close(self):
        pass

class Mock_cursor_exception():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        raise ("NO URL FOUND IN RDS")

    def close(self):
        pass
