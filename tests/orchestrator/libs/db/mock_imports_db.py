from unittest.mock import MagicMock
import sys
import os
import pytest
import json, time
from base64 import b64decode

sys.modules['sqlite3'] = MagicMock()
sys.modules['jsonpickle'] = MagicMock()
sys.modules["mysql"] = MagicMock()
sys.modules["mysql.connector"] = MagicMock()
sys.modules['libs.model.orchjobs'] = MagicMock()
sys.modules['hashlib'] = MagicMock()
sys.modules['boto3'] = MagicMock()
sys.modules['boto3.client'] = MagicMock()
sys.modules['boto3.dynamodb'] = MagicMock()
sys.modules['boto3.dynamodb.conditions'] = MagicMock()
sys.modules['traceback'] = MagicMock()
sys.modules['google'] = MagicMock()
sys.modules['google.oauth2'] = MagicMock()
sys.modules['googleapiclient'] = MagicMock()
sys.modules['ipam_plugin'] = MagicMock()
sys.modules['ipam_factory'] = MagicMock()
sys.modules['ngpa_extern'] = MagicMock()
sys.modules['ngpa_extern.external_utils'] = MagicMock()
