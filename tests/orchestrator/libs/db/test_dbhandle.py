import json
from unittest.mock import MagicMock, patch
import pytest
import logging
import mock_imports_db
import mock_inputs_db
from mock_models import *
from libs.db.dbhandle import Db<PERSON>and<PERSON>, DbResult
from libs.model.instancemodel import InstanceModel
from libs.common.shared.sys_utils import *
from libs.model.custmodel import CustomerModel


class TestDbHandle:
    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_success_gpcs_tenant_region_mapping_query)
    def test_get_central_cache_service_endpoint_from_gpcs_tenant_region_mapping_rds_table(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the central cache endpoint mapping is present in gpcs_tenant_region_mapping
            table in RDS.
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.get_central_cache_service_endpoint('qa', '115', '215')
        assert "Got central_cache_service_endpoint from gpcs_tenant_region_mapping: http://gpcs_region_mapping/url" in caplog.text
        assert "Returning central_cache_service_endpoint: http://gpcs_region_mapping/url" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_success_empty_string_gpcs_tenant_region_mapping_query)
    def test_get_central_cache_service_endpoint_from_gpcs_tenant_region_mapping_rds_table_empty_string(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the central cache endpoint mapping is present in gpcs_tenant_region_mapping
            table in RDS.
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.get_central_cache_service_endpoint('qa', '115', '215')
        assert "Got central_cache_service_endpoint from gpcs_tenant_region_mapping: " in caplog.text
        assert "Returning central_cache_service_endpoint: " in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_success_gpcs_region_mapping_query)
    def test_get_central_cache_service_endpoint_from_gpcs_region_mapping_rds_table(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the central cache endpoint mapping is present in gpcs_region_mapping
            table in RDS.
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.get_central_cache_service_endpoint('qa', '115', '215')
        assert "Got central_cache_service_endpoint from gpcs_region_mapping: http://gpcs_region_mapping/url" in caplog.text
        assert "Returning central_cache_service_endpoint: http://gpcs_region_mapping/url" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_failed_gpcs_region_mapping_query)
    def test_get_central_cache_service_endpoint_from_services_endpoint_mapping_rds_table(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the central cache endpoint mapping is not present in gpcs_region_mapping
            table in RDS. In this case we will fall back to using the mapping from services_endpoint_mapping RDS table
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        answer = dbh.get_central_cache_service_endpoint('qa', '115', '215')
        assert answer == "http://services_endpoint_mapping/url"
        assert "Failed to get central_cache_service_endpoint from gpcs_region_mapping table in RDS" in caplog.text
        assert "Falling back to central_cache_service_endpoint from services_endpoint_mapping table in RDS" in caplog.text
        assert "Returning central_cache_service_endpoint: http://services_endpoint_mapping/url" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_exception)
    def test_get_central_cache_service_endpoint_failure(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the get_central_cache_service_endpoint call hits an exception
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        answer = dbh.get_central_cache_service_endpoint('qa', '115', '215')
        assert answer == ""
        assert "Failed to get central_cache_service_endpoint from gpcs_region_mapping table in RDS" not in caplog.text
        assert "Falling back to central_cache_service_endpoint from services_endpoint_mapping table in RDS" not in caplog.text
        assert "Returning central_cache_service_endpoint: http://services_endpoint_mapping/url" not in caplog.text
        assert "get_central_cache_service_endpoint caught exception" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_failed_services_endpoint_mapping_query)
    def test_get_central_cache_service_endpoint_from_services_endpoint_mapping_rds_table_failure(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the central cache endpoint mapping is not present in services_endpoint_mapping RDS table
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        answer = dbh.get_central_cache_service_endpoint('qa', '115', '215')
        assert answer == ""
        assert "Failed to get central_cache_service_endpoint from services_endpoint_mapping table in RDS" in caplog.text
        assert "get_central_cache_service_endpoint caught exception" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_success_gpcs_tenant_region_mapping_query)
    def test_get_central_cache_service_backup_endpoint_from_gpcs_tenant_region_mapping_rds_table(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the central cache backup endpoint mapping is present in gpcs_tenant_region_mapping
            table in RDS.
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        answer = dbh.get_central_cache_service_backup_endpoint('qa', '115', '215')
        assert answer == "http://gpcs_region_mapping/url"

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_success_empty_string_gpcs_tenant_region_mapping_query)
    def test_get_central_cache_service_backup_endpoint_from_gpcs_tenant_region_mapping_rds_table_empty_string(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the central cache backup endpoint mapping is present in gpcs_tenant_region_mapping
            table in RDS but it is an empty string.  The endpoint from the gpcs_region_mapping should be used instead.
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        answer = dbh.get_central_cache_service_backup_endpoint('qa', '115', '215')
        assert answer == "http://gpcs_region_mapping/url"

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_success_gpcs_region_mapping_query)
    def test_get_central_cache_service_backup_endpoint_from_gpcs_region_mapping_rds_table(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the central cache backup endpoint mapping is present in gpcs_region_mapping
            table in RDS.
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        answer = dbh.get_central_cache_service_backup_endpoint('qa', '115', '215')
        assert answer == "http://gpcs_region_mapping/url"

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_exception)
    def test_get_central_cache_service_backup_endpoint_failure(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the get_central_cache_service_backup_endpoint call hits an exception
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        answer = dbh.get_central_cache_service_backup_endpoint('qa', '115', '215')
        assert answer == ''
        assert "get_central_cache_service_backup_endpoint" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_failed_gpcs_region_mapping_query)
    def test_get_central_cache_service_backup_endpoint_from_services_endpoint_mapping_rds_table(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the central cache backup endpoint mapping is not present in gpcs_region_mapping
            table in RDS. In this case we will fall back to using the mapping from services_endpoint_mapping RDS table
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        answer = dbh.get_central_cache_service_backup_endpoint('qa', '115', '215')
        assert answer == 'http://services_endpoint_mapping/url'
        assert "Returning central_cache_service_backup_endpoint" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_failed_services_endpoint_mapping_query)
    def test_get_central_cache_service_backup_endpoint_from_services_endpoint_mapping_rds_table_failure(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the central cache backup endpoint mapping is not present in services_endpoint_mapping RDS table
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        answer = dbh.get_central_cache_service_backup_endpoint('qa', '115', '215')
        assert answer == ""
        assert "Failed to get central_cache_service_backup_endpoint from gpcs_region_mapping" in caplog.text
        assert "get_central_cache_service_backup_endpoint caught exception" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=Exception('test'))
    def test_get_central_cache_service_endpoint_exception(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where an exception is raised for get_central_cache_service_endpoint
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.get_central_cache_service_endpoint('qa', '115', '215')
        assert "get_central_cache_service_endpoint caught exception: " in caplog.text
        assert "Returning central_cache_service_endpoint: " in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_success_get_central_cache_service_endpoint)
    def test_get_central_cache_service_backup_endpoint(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the central cache endpoint mapping is present in RDS
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.get_central_cache_service_backup_endpoint('qa', '115', '215')
        assert "Got central_cache_service_backup_endpoint from gpcs_tenant_region_mapping: http://gpcs_region_mapping/url" in caplog.text
        assert "Returning central_cache_service_backup_endpoint: http://gpcs_region_mapping/url" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_success_get_central_cache_service_endpoint_failed)
    def test_get_central_cache_service_backup_endpoint_failed(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the central cache endpoint mapping is not present in RDS
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.get_central_cache_service_backup_endpoint('qa', '115', '215')
        assert "Failed to get central_cache_service_backup_endpoint from services_endpoint_mapping" in caplog.text
        assert "Returning central_cache_service_backup_endpoint: " in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=Exception('test'))
    def test_get_central_cache_service_backup_endpoint_exception(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where an exception is raised for get_central_cache_service_backup_endpoint
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.get_central_cache_service_backup_endpoint('qa', '115', '215')
        assert "get_central_cache_service_backup_endpoint caught exception: " in caplog.text
        assert "Returning central_cache_service_backup_endpoint: " in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_success_get_ciam_service_endpoint)
    def test_get_ciam_service_endpoint(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the ciam endpoint mapping is present in RDS
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.get_ciam_service_endpoint('qa')
        assert "Got ciam_service_endpoint from RDS: http://services_endpoint_mapping/url" in caplog.text
        assert "Returning ciam_service_endpoint: http://services_endpoint_mapping/url" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_success_get_ciam_service_endpoint_failed)
    def test_get_ciam_service_endpoint_failed(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where the ciam endpoint mapping is not present in RDS
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.get_ciam_service_endpoint('qa')
        assert "Failed to get ciam_service_endpoint from RDS" in caplog.text
        assert "Returning ciam_service_endpoint: " in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=Exception('test'))
    def test_get_ciam_service_endpoint_exception(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where an exception is raised for get_ciam_service_endpoint
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.get_ciam_service_endpoint('qa')
        assert "get_ciam_service_endpoint caught exception: " in caplog.text
        assert "Returning ciam_service_endpoint: " in caplog.text

    @patch('libs.model.instancemodel._mapping_for_firewall_summary_table')
    @patch('libs.db.dbhandle.DbHandle.cursorclose')
    @patch('libs.db.dbhandle.CustomerModel')
    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_insert_job_in_firewall_summary_send_to_lambda(self,mock_cursor, mock_custmodel, mock_cursorclose, mock_test_mapping_for_firewall_summary_table,caplog):
        '''
            This UT will test the scenario where the lambda_params are successfully sent to async_call_firewall_summary_notify_lambda
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)

        dbh.conn = MagicMock()

        instance = InstanceModel(iid=11111, dbh=dbh)
        instance.set_param("custid",20)
        instance.set_param("slot_nr", 1)
        instance.set_param("acct_id", 101)
        instance.set_param("id", 10)
        instance.set_param("node_type", 48)


        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchall = mock_cursor.return_value.fetchall
        mock_cursor_fetchall.return_value = [[121]]

        mock_cursor_execute = mock_cursor.return_value.execute
        mock_cursor_execute.return_value = MagicMock()

        mock_custmodel.return_value = MagicMock()
        mock_custmodel_super_acct_id = mock_custmodel.return_value.super_acct_id
        mock_custmodel_super_acct_id.return_value = 901

        mock_cursorclose.return_value = MagicMock()


        INSTANCE_ROLE_MP = 1

        dbh.insert_job_in_firewall_summary(instance, False, 167, 48)
        assert "Sending the params to async_call_firewall_summary_notify_lambda" in caplog.text

    @patch('libs.db.dbhandle.config_commit_al_notify')
    @patch('libs.model.instancemodel._mapping_for_firewall_summary_table')
    @patch('libs.db.dbhandle.DbHandle.cursorclose')
    @patch('libs.db.dbhandle.CustomerModel')
    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_insert_job_in_firewall_summary_exception(self,mock_cursor, mock_custmodel, mock_cursorclose, mock_test_mapping_for_firewall_summary_table, mock_config_commit_al_notify, caplog):
        '''
            This UT will test the scenario where there is an exception while sending the params to async_call_firewall_summary_notify_lambda
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)

        dbh.conn = MagicMock()

        instance = InstanceModel(iid=11111, dbh=dbh)
        instance.set_param("custid",20)
        instance.set_param("slot_nr", 1)
        instance.set_param("acct_id", 101)
        instance.set_param("id", 10)
        instance.set_param("node_type", 48)


        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchall = mock_cursor.return_value.fetchall
        mock_cursor_fetchall.return_value = [[121]]

        mock_cursor_execute = mock_cursor.return_value.execute
        mock_cursor_execute.return_value = MagicMock()

        mock_custmodel.return_value = MagicMock()
        mock_custmodel_super_acct_id = mock_custmodel.return_value.super_acct_id
        mock_custmodel_super_acct_id.return_value = 901

        mock_cursorclose.return_value = MagicMock()
        INSTANCE_ROLE_MP = 1

        mock_config_commit_al_notify.return_value = MagicMock()
        mock_async = mock_config_commit_al_notify.async_call_firewall_summary_notify_lambda
        mock_async.side_effect = Exception("test error")

        dbh.insert_job_in_firewall_summary(instance, False, 167, 48)
        assert "Exception: Failed to send new job message to alert engine for new onboarding. Err: test error" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_update_status_msg_for_cust_topology_node)
    def test_update_status_msg_for_cust_topology_node(self, mock_cursor, caplog):
        logger = logging.getLogger()
        cust_topology_node_id = 11565
        status_msg = "Successfully created required instances"
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)

        ret = dbh.update_status_msg_for_cust_topology_node(cust_topology_node_id, status_msg)
        assert ret == True

    @patch('libs.db.dbhandle.get_salt_profile_for_instance_entry')
    @patch('libs.db.dbhandle.get_salt_profile_name_for_instance_entry')
    @patch('libs.db.dbhandle.get_portal_base')
    @patch('libs.db.dbhandle.generate_otp')
    @patch('libs.db.dbhandle.InstanceModel')
    @patch('libs.db.dbhandle.DbHandle.validate_set_inst_using_sp_interconnect')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_sec_key_name')
    @patch('libs.db.dbhandle.DbHandle.get_updated_max_capacity_for_instance')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_id_for_instance')
    @patch('libs.db.dbhandle.CustomerModel')
    @patch('libs.db.dbhandle.gpcs_get_compute_region_idx_from_edge_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_cloud_type_from_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_edge_region_name_from_edge_region_idx')
    @patch('libs.db.dbhandle.DbHandle.get_nodeTypeInstance')
    def test_get_new_instance_int_ipv6_for_instances_behind_nlb(self, mock_get_nodeTypeInstance, mock_gpcs_get_edge_region_name_from_edge_region_idx, mock_gpcs_get_cloud_type_from_region_idx, mock_gpcs_get_compute_region_idx_from_edge_region_idx, mock_CustomerModel, mock_get_vpc_id_for_instance, mock_get_updated_max_capacity_for_instance, mock_get_vpc_sec_key_name, mock_validate_set_inst_using_sp_interconnect, mock_InstanceModel, mock_generate_otp, mock_get_portal_base, mock_get_salt_profile_name_for_instance_entry, mock_get_salt_profile_for_instance_entry, caplog):
        '''
           UT to ensure that for instances behind the NLB we will enable the IPv6 support flag in instance_entry_dict
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.conn = MagicMock()
        dbh.capacity_type = ""
        dbh.cloud_machine_type = ""
        dbh.market_type = ""
        dbh.dpdk_qcount = 0
        mock_get_nodeTypeInstance.return_value = MockNodeTypeModel(node_type=49)
        mock_gpcs_get_edge_region_name_from_edge_region_idx.return_value = 'usa'
        mock_gpcs_get_cloud_type_from_region_idx.return_value = 'gcp'
        mock_gpcs_get_compute_region_idx_from_edge_region_idx.return_value = 220
        mock_CustomerModel.return_value = MockCustomerModel()
        mock_CustomerModel.set_param("external_ipv6_support", True)
        mock_get_vpc_id_for_instance.return_value = 75
        mock_get_updated_max_capacity_for_instance.return_value = (500, 1000)
        mock_get_vpc_sec_key_name.return_value = "test"
        mock_validate_set_inst_using_sp_interconnect.return_value = MagicMock()
        mock_InstanceModel.return_value = MockInstanceModel()
        mock_generate_otp.return_value = 150
        mock_get_portal_base.return_value = "test"
        mock_get_salt_profile_name_for_instance_entry.return_value = "test-entry"
        mock_get_salt_profile_for_instance_entry.return_value = {"name": "test"}
        ret = dbh.get_new_instance_int('4.2.0', "GPGATEWAY", 'test', 48, 'test', is_instance_behind_nlb=True)
        assert "External IPv6 support is not enabled." not in caplog.text
            
    @patch('libs.db.dbhandle.get_salt_profile_for_instance_entry')
    @patch('libs.db.dbhandle.get_salt_profile_name_for_instance_entry')
    @patch('libs.db.dbhandle.get_portal_base')
    @patch('libs.db.dbhandle.generate_otp')
    @patch('libs.db.dbhandle.InstanceModel')
    @patch('libs.db.dbhandle.DbHandle.validate_set_inst_using_sp_interconnect')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_sec_key_name')
    @patch('libs.db.dbhandle.DbHandle.get_updated_max_capacity_for_instance')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_id_for_instance')
    @patch('libs.db.dbhandle.CustomerModel')
    @patch('libs.db.dbhandle.gpcs_get_compute_region_idx_from_edge_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_cloud_type_from_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_edge_region_name_from_edge_region_idx')
    @patch('libs.db.dbhandle.DbHandle.get_nodeTypeInstance')
    def test_get_new_instance_int_ipv6_for_nlb_instance(self, mock_get_nodeTypeInstance, mock_gpcs_get_edge_region_name_from_edge_region_idx, mock_gpcs_get_cloud_type_from_region_idx, mock_gpcs_get_compute_region_idx_from_edge_region_idx, mock_CustomerModel, mock_get_vpc_id_for_instance, mock_get_updated_max_capacity_for_instance, mock_get_vpc_sec_key_name, mock_validate_set_inst_using_sp_interconnect, mock_InstanceModel, mock_generate_otp, mock_get_portal_base, mock_get_salt_profile_name_for_instance_entry, mock_get_salt_profile_for_instance_entry, caplog):
        '''
           UT to ensure that for instances he NLB instance itself will enable the IPv6 support flag in instance_entry_dict
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.conn = MagicMock()
        dbh.capacity_type = ""
        dbh.cloud_machine_type = ""
        dbh.market_type = ""
        dbh.dpdk_qcount = 0
        mock_get_nodeTypeInstance.return_value = MockNodeTypeModel()
        mock_gpcs_get_edge_region_name_from_edge_region_idx.return_value = 'usa'
        mock_gpcs_get_cloud_type_from_region_idx.return_value = 'gcp'
        mock_gpcs_get_compute_region_idx_from_edge_region_idx.return_value = 220
        mock_CustomerModel.return_value = MockCustomerModel()
        mock_CustomerModel.set_param("external_ipv6_support", True)
        mock_get_vpc_id_for_instance.return_value = 75
        mock_get_updated_max_capacity_for_instance.return_value = (500, 1000)
        mock_get_vpc_sec_key_name.return_value = "test"
        mock_validate_set_inst_using_sp_interconnect.return_value = MagicMock()
        mock_InstanceModel.return_value = MockInstanceModel()
        mock_generate_otp.return_value = 150
        mock_get_portal_base.return_value = "test"
        mock_get_salt_profile_name_for_instance_entry.return_value = "test-entry"
        mock_get_salt_profile_for_instance_entry.return_value = {"name": "test"}
        ret = dbh.get_new_instance_int('4.2.0', "NLB", 'test', 48, 'test')
        assert "External IPv6 support is not enabled." not in caplog.text


    @patch('libs.db.dbhandle.get_salt_profile_for_instance_entry')
    @patch('libs.db.dbhandle.get_salt_profile_name_for_instance_entry')
    @patch('libs.db.dbhandle.get_portal_base')
    @patch('libs.db.dbhandle.generate_otp')
    @patch('libs.db.dbhandle.InstanceModel')
    @patch('libs.db.dbhandle.DbHandle.validate_set_inst_using_sp_interconnect')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_sec_key_name')
    @patch('libs.db.dbhandle.DbHandle.get_updated_max_capacity_for_instance')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_id_for_instance')
    @patch('libs.db.dbhandle.CustomerModel')
    @patch('libs.db.dbhandle.gpcs_get_compute_region_idx_from_edge_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_cloud_type_from_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_edge_region_name_from_edge_region_idx')
    @patch('libs.db.dbhandle.DbHandle.get_nodeTypeInstance')
    @patch('libs.db.dbhandle.is_dhcp_pool_allocation_enabled')
    def test_get_new_instance_int_dhcp_allocation_enabled(self, mock_dhcp_enabled_check, mock_get_nodeTypeInstance, mock_gpcs_get_edge_region_name_from_edge_region_idx, mock_gpcs_get_cloud_type_from_region_idx, mock_gpcs_get_compute_region_idx_from_edge_region_idx, mock_CustomerModel, mock_get_vpc_id_for_instance, mock_get_updated_max_capacity_for_instance, mock_get_vpc_sec_key_name, mock_validate_set_inst_using_sp_interconnect, mock_InstanceModel, mock_generate_otp, mock_get_portal_base, mock_get_salt_profile_name_for_instance_entry, mock_get_salt_profile_for_instance_entry, caplog):
        '''
           UT to ensure that for instances of tenants with DHCP enabled,
           we do not perform pool allocation enablement.
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.conn = MagicMock()
        dbh.capacity_type = ""
        dbh.cloud_machine_type = ""
        dbh.market_type = ""
        dbh.dpdk_qcount = 0
        mock_get_nodeTypeInstance.return_value = MockNodeTypeModel()
        mock_gpcs_get_edge_region_name_from_edge_region_idx.return_value = 'usa'
        mock_gpcs_get_cloud_type_from_region_idx.return_value = 'gcp'
        mock_gpcs_get_compute_region_idx_from_edge_region_idx.return_value = 220
        mock_CustomerModel.return_value = MockCustomerModel()
        mock_CustomerModel.set_param("external_ipv6_support", True)
        mock_get_vpc_id_for_instance.return_value = 75
        mock_get_updated_max_capacity_for_instance.return_value = (500, 1000)
        mock_get_vpc_sec_key_name.return_value = "test"
        mock_validate_set_inst_using_sp_interconnect.return_value = MagicMock()
        mock_InstanceModel.return_value = MockInstanceModel()
        mock_generate_otp.return_value = 150
        mock_get_portal_base.return_value = "test"
        mock_get_salt_profile_name_for_instance_entry.return_value = "test-entry"
        mock_get_salt_profile_for_instance_entry.return_value = {"name": "test"}
        mock_dhcp_enabled_check.return_value = 1
        ret = dbh.get_new_instance_int('4.2.0', "GPGATEWAY", 'test', 48, 'test')
        assert "Two phase upgrade invocation; no DHCP Allocation enabled for tenant; invoke update_pool_allocation_table" not in caplog.text


    @patch('libs.db.dbhandle.get_salt_profile_for_instance_entry')
    @patch('libs.db.dbhandle.get_salt_profile_name_for_instance_entry')
    @patch('libs.db.dbhandle.get_portal_base')
    @patch('libs.db.dbhandle.generate_otp')
    @patch('libs.db.dbhandle.InstanceModel')
    @patch('libs.db.dbhandle.DbHandle.validate_set_inst_using_sp_interconnect')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_sec_key_name')
    @patch('libs.db.dbhandle.DbHandle.get_updated_max_capacity_for_instance')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_id_for_instance')
    @patch('libs.db.dbhandle.CustomerModel')
    @patch('libs.db.dbhandle.gpcs_get_compute_region_idx_from_edge_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_cloud_type_from_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_edge_region_name_from_edge_region_idx')
    @patch('libs.db.dbhandle.DbHandle.get_nodeTypeInstance')
    @patch('libs.db.dbhandle.is_dhcp_pool_allocation_enabled')
    @patch('libs.db.dbhandle.is_no_passive_instances_enabled')
    def test_get_new_instance_int_dhcp_allocation_enabled_v2(self, mock_passive, mock_dhcp_enabled_check, mock_get_nodeTypeInstance, mock_gpcs_get_edge_region_name_from_edge_region_idx, mock_gpcs_get_cloud_type_from_region_idx, mock_gpcs_get_compute_region_idx_from_edge_region_idx, mock_CustomerModel, mock_get_vpc_id_for_instance, mock_get_updated_max_capacity_for_instance, mock_get_vpc_sec_key_name, mock_validate_set_inst_using_sp_interconnect, mock_InstanceModel, mock_generate_otp, mock_get_portal_base, mock_get_salt_profile_name_for_instance_entry, mock_get_salt_profile_for_instance_entry, caplog):
        '''
           UT to ensure that for instances of tenants with DHCP enabled,
           we do not perform pool allocation enablement.
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.conn = MagicMock()
        dbh.capacity_type = ""
        dbh.cloud_machine_type = ""
        dbh.market_type = ""
        dbh.dpdk_qcount = 0
        mock_passive.return_value = False
        mock_get_nodeTypeInstance.return_value = MockNodeTypeModel()
        mock_gpcs_get_edge_region_name_from_edge_region_idx.return_value = 'usa'
        mock_gpcs_get_cloud_type_from_region_idx.return_value = 'gcp'
        mock_gpcs_get_compute_region_idx_from_edge_region_idx.return_value = 220
        mock_CustomerModel.return_value = MockCustomerModel()
        mock_CustomerModel.set_param("external_ipv6_support", True)
        mock_get_vpc_id_for_instance.return_value = 75
        mock_get_updated_max_capacity_for_instance.return_value = (500, 1000)
        mock_get_vpc_sec_key_name.return_value = "test"
        mock_validate_set_inst_using_sp_interconnect.return_value = MagicMock()
        mock_InstanceModel.return_value = MockInstanceModel()
        mock_generate_otp.return_value = 150
        mock_get_portal_base.return_value = "test"
        mock_get_salt_profile_name_for_instance_entry.return_value = "test-entry"
        mock_get_salt_profile_for_instance_entry.return_value = {"name": "test"}
        mock_dhcp_enabled_check.return_value = 0
        ret = dbh.get_new_instance_int('4.2.0', "GPGATEWAY", 'test', 48, 'test', upgrade_creation=1, is_two_phase_upgrade_call=True)
        assert "Two phase upgrade invocation; no DHCP Allocation enabled for tenant; invoke update_pool_allocation_table" in caplog.text


    @patch('libs.db.dbhandle.get_salt_profile_for_instance_entry')
    @patch('libs.db.dbhandle.get_salt_profile_name_for_instance_entry')
    @patch('libs.db.dbhandle.get_portal_base')
    @patch('libs.db.dbhandle.generate_otp')
    @patch('libs.db.dbhandle.InstanceModel')
    @patch('libs.db.dbhandle.DbHandle.validate_set_inst_using_sp_interconnect')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_sec_key_name')
    @patch('libs.db.dbhandle.DbHandle.get_updated_max_capacity_for_instance')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_id_for_instance')
    @patch('libs.db.dbhandle.CustomerModel')
    @patch('libs.db.dbhandle.gpcs_get_compute_region_idx_from_edge_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_cloud_type_from_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_edge_region_name_from_edge_region_idx')
    @patch('libs.db.dbhandle.DbHandle.get_nodeTypeInstance')
    @patch('libs.db.dbhandle.is_no_passive_instances_enabled')
    def test_get_new_instance_int_pbf_disabled_instance_behind_nlb_nat(self, mock_passive,  mock_get_nodeTypeInstance, mock_gpcs_get_edge_region_name_from_edge_region_idx, mock_gpcs_get_cloud_type_from_region_idx, mock_gpcs_get_compute_region_idx_from_edge_region_idx, mock_CustomerModel, mock_get_vpc_id_for_instance, mock_get_updated_max_capacity_for_instance, mock_get_vpc_sec_key_name, mock_validate_set_inst_using_sp_interconnect, mock_InstanceModel, mock_generate_otp, mock_get_portal_base, mock_get_salt_profile_name_for_instance_entry, mock_get_salt_profile_for_instance_entry, caplog):
        '''
           UT to ensure that for instances behind the NLB / NAT will not set the PBF flag
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.conn = MagicMock()
        dbh.capacity_type = ""
        dbh.cloud_machine_type = ""
        dbh.market_type = ""
        dbh.dpdk_qcount = 0
        mock_passive.return_value = False
        mock_get_nodeTypeInstance.return_value = MockNodeTypeModel()
        mock_gpcs_get_edge_region_name_from_edge_region_idx.return_value = 'usa'
        mock_gpcs_get_cloud_type_from_region_idx.return_value = 'gcp'
        mock_gpcs_get_compute_region_idx_from_edge_region_idx.return_value = 220
        mock_CustomerModel.return_value = MockCustomerModel()
        mock_CustomerModel.set_param("external_ipv6_support", True)
        mock_get_vpc_id_for_instance.return_value = 75
        mock_get_updated_max_capacity_for_instance.return_value = (500, 1000)
        mock_get_vpc_sec_key_name.return_value = "test"
        mock_validate_set_inst_using_sp_interconnect.return_value = MagicMock()
        mock_InstanceModel.return_value = MockInstanceModel()
        mock_generate_otp.return_value = 150
        mock_get_portal_base.return_value = "test"
        mock_get_salt_profile_name_for_instance_entry.return_value = "test-entry"
        mock_get_salt_profile_for_instance_entry.return_value = {"name": "test"}
        ret = dbh.get_new_instance_int('4.2.0', "GPGATEWAY", 'test', 48, 'test', is_instance_behind_nlb=True)
        assert "Set use_PBF as False for the instance" in caplog.text

    @patch('libs.db.dbhandle.get_salt_profile_for_instance_entry')
    @patch('libs.db.dbhandle.get_salt_profile_name_for_instance_entry')
    @patch('libs.db.dbhandle.get_portal_base')
    @patch('libs.db.dbhandle.generate_otp')
    @patch('libs.db.dbhandle.InstanceModel')
    @patch('libs.db.dbhandle.DbHandle.validate_set_inst_using_sp_interconnect')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_sec_key_name')
    @patch('libs.db.dbhandle.DbHandle.get_updated_max_capacity_for_instance')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_id_for_instance')
    @patch('libs.db.dbhandle.CustomerModel')
    @patch('libs.db.dbhandle.gpcs_get_compute_region_idx_from_edge_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_cloud_type_from_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_edge_region_name_from_edge_region_idx')
    @patch('libs.db.dbhandle.DbHandle.get_nodeTypeInstance')
    @patch('libs.db.dbhandle.is_dhcp_pool_allocation_enabled')
    @patch('libs.db.dbhandle.is_no_passive_instances_enabled')
    def test_get_new_instance_int_nlb_should_not_get_loopback(self, mock_passive, mock_dhcp_enabled_check, mock_get_nodeTypeInstance, mock_gpcs_get_edge_region_name_from_edge_region_idx, mock_gpcs_get_cloud_type_from_region_idx, mock_gpcs_get_compute_region_idx_from_edge_region_idx, mock_CustomerModel, mock_get_vpc_id_for_instance, mock_get_updated_max_capacity_for_instance, mock_get_vpc_sec_key_name, mock_validate_set_inst_using_sp_interconnect, mock_InstanceModel, mock_generate_otp, mock_get_portal_base, mock_get_salt_profile_name_for_instance_entry, mock_get_salt_profile_for_instance_entry, caplog):
        '''
           UT to ensure that for instances of tenants with DHCP enabled,
           we do not perform pool allocation enablement.
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.conn = MagicMock()
        dbh.capacity_type = ""
        dbh.cloud_machine_type = ""
        dbh.market_type = ""
        dbh.dpdk_qcount = 0
        mock_passive.return_value = False
        mock_get_nodeTypeInstance.return_value = MockNodeTypeModel()
        mock_gpcs_get_edge_region_name_from_edge_region_idx.return_value = 'usa'
        mock_gpcs_get_cloud_type_from_region_idx.return_value = 'gcp'
        mock_gpcs_get_compute_region_idx_from_edge_region_idx.return_value = 220
        mock_CustomerModel.return_value = MockCustomerModel()
        mock_CustomerModel.set_param("external_ipv6_support", True)
        mock_get_vpc_id_for_instance.return_value = 75
        mock_get_updated_max_capacity_for_instance.return_value = (500, 1000)
        mock_get_vpc_sec_key_name.return_value = "test"
        mock_validate_set_inst_using_sp_interconnect.return_value = MagicMock()
        mock_InstanceModel.return_value = MockInstanceModel()
        mock_generate_otp.return_value = 150
        mock_get_portal_base.return_value = "test"
        mock_get_salt_profile_name_for_instance_entry.return_value = "test-entry"
        mock_get_salt_profile_for_instance_entry.return_value = {"name": "test"}
        mock_dhcp_enabled_check.return_value = 0
        ret = dbh.get_new_instance_int('10.2.4', "NLB", 'test', 151, 'test')
        assert not mock_dhcp_enabled_check.called

    @patch('libs.db.dbhandle.get_cloud_native_location_name_from_region_id')
    @patch('libs.db.dbhandle.swgproxy_bringup')
    @patch('libs.db.dbhandle.DbHandle.get_sp_egress_type')
    @patch('libs.db.dbhandle.DbHandle.get_enable_5g')
    @patch('libs.db.dbhandle.DbHandle.get_interconnect_vpc_details')
    @patch('libs.db.dbhandle.DbHandle.get_sp_tgw_state')
    def test_fetch_interconnect_details(self, mock_get_sp_tgw_state, mock_get_interconnect_vpc_details,
                                        mock_get_enable_5g, mock_get_sp_egress_type, mock_swgproxy_bringup,
                                        mock_get_cloud_native_location_name_from_region_id, caplog):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.conn = MagicMock()

        # Create mock objects
        mock_instance = MockInstanceModel()
        mock_customer = MockCustomerModel()
        region = 226

        # Test case 1: Backbone selection disabled
        mock_customer.set_param("backbone_selection", 0)
        mock_instance.set_param("cloud_provider", PROVIDER_GCP_DB_ENUM_VALUE)
        result = dbh.fetch_interconnect_details(mock_instance, mock_customer, region)
        assert result["interconnect_exists_in_region"] == False
        assert result["is_using_sp_interconnect"] == False
        assert result["is_5g_enabled"] == True
        assert result["sp_egress_type"] == "SP"

        # Test case 2: Unsupported cloud provider
        mock_customer.set_param("backbone_selection", 1)
        mock_instance.set_param("cloud_provider", PROVIDER_AZR_DB_ENUM_VALUE)
        result = dbh.fetch_interconnect_details(mock_instance, mock_customer, region)
        assert result["interconnect_exists_in_region"] == False
        assert result["is_using_sp_interconnect"] == False

        # Setup for remaining tests
        mock_instance.set_param("cloud_provider", PROVIDER_GCP_DB_ENUM_VALUE)
        mock_get_cloud_native_location_name_from_region_id.return_value = "us-central1"
        mock_get_interconnect_vpc_details.return_value = DbResult(True, ["vpc-123"])
        mock_get_sp_tgw_state.return_value = DbResult(True, ["tgw-123"])

        # Test case 3: Region in excluded list. Ignoring the value in excluded_backbone_regions
        mock_customer.set_param("excluded_backbone_regions", '{"excluded_backbone_regions": ["us-central1"]}')
        result = dbh.fetch_interconnect_details(mock_instance, mock_customer, region)
        assert result["interconnect_exists_in_region"] == True

        # Test case 4: Valid region with interconnect
        mock_customer.set_param("excluded_backbone_regions", '{"excluded_backbone_regions": ["us-west1"]}')
        mock_get_sp_egress_type.return_value = DbResult(True, ["SP"])
        result = dbh.fetch_interconnect_details(mock_instance, mock_customer, region)
        assert result["interconnect_exists_in_region"] == True
        assert result["sp_egress_type"] == "SP"

        # Test case 5: GP Gateway with 5G enabled
        mock_instance.set_param("node_type", NODE_TYPE_GP_GATEWAY)
        mock_get_enable_5g.return_value = DbResult(True, [1])
        result = dbh.fetch_interconnect_details(mock_instance, mock_customer, region)
        assert result["is_5g_enabled"] == True
        assert result["is_using_sp_interconnect"] == True

        # Test case 6: SWG Proxy with eproxy outside panos
        mock_instance.set_param("node_type", NODE_TYPE_SWG_PROXY)
        mock_swgproxy_bringup_instance = mock_swgproxy_bringup.return_value
        mock_swgproxy_bringup_instance.gpcs_envoy_outside_panos_val.return_value = True
        result = dbh.fetch_interconnect_details(mock_instance, mock_customer, region)
        assert result["is_using_sp_interconnect"] == True

        # Test case 7: NAT instance with PA egress type
        mock_instance.set_param("node_type", NODE_TYPE_NAT_INSTANCE)
        mock_get_sp_egress_type.return_value = DbResult(True, ["PA"])
        result = dbh.fetch_interconnect_details(mock_instance, mock_customer, region)
        assert result["sp_egress_type"] == "PA"
        assert result["is_using_sp_interconnect"] == True

        # Test case 8: Exception handling
        mock_get_interconnect_vpc_details.side_effect = Exception("Test error")
        result = dbh.fetch_interconnect_details(mock_instance, mock_customer, region)
        assert "Failed to fetch the interconnect details" in caplog.text
        assert result["error_msg"] == ""

    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_enable_5g(self, mock_get_cursor, caplog):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.conn = MagicMock()

        # Mock the get_cloud_native_location_name_from_region_id method
        dbh.get_cloud_native_location_name_from_region_id = MagicMock(return_value="us-central1")

        # Test case where 5G is enabled
        mock_get_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_get_cursor.return_value.fetchone
        mock_cursor_fetchone.return_value = (1,)

        ret = dbh.get_enable_5g(123, "us-central1")
        assert ret.ok == True
        assert ret.result == [1]
        assert "Checking get enable_5g for cust_id: 123 and cloud_native_compute_region: us-central1" in caplog.text

        # Test case where 5G is disabled
        mock_cursor_fetchone.return_value = (0,)
        ret = dbh.get_enable_5g(456, "us-west1")
        assert ret.ok == True
        assert ret.result == [0]
        assert "Checking get enable_5g for cust_id: 456 and cloud_native_compute_region: us-central1" in caplog.text

        # Test case for exception handling
        mock_cursor_fetchone.side_effect = Exception("Database error")
        ret = dbh.get_enable_5g(789, "eu-west1")
        assert ret.ok == False
        assert ret.result == []
        assert "Failed to get enable_5g, Exception: Database error" in caplog.text

        # Test case for None result
        mock_cursor_fetchone.return_value = None
        ret = dbh.get_enable_5g(101, "ap-southeast1")
        assert ret.ok == False
        assert ret.result == []
        assert "enable_5g: 101 not found in interconnect_vpc_master table." in caplog.text

    @patch('libs.db.dbhandle.get_cloud_native_location_name_from_region_id')
    @patch('libs.db.dbhandle.DbHandle.get_sp_tgw_state')
    def test_is_instance_using_sp_interconnect_aws(self, mock_get_sp_tgw_state,
                                                   mock_get_cloud_native_location_name_from_region_id, caplog):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.conn = MagicMock()

        # Mock the Instance model object
        mock_InstanceModel = MockInstanceModel()
        mock_InstanceModel.set_param("cloud_provider", PROVIDER_AWS_DB_ENUM_VALUE)
        mock_InstanceModel.set_param("node_type", NODE_TYPE_GP_GATEWAY)
        mock_InstanceModel.set_param("compute_region_idx", 216)

        mock_get_sp_tgw_state.return_value = DbResult(True, ['tgw_created'])

        # Without backbone selection check if proper error is thrown.
        mock_CustomerModel = MockCustomerModel()
        mock_CustomerModel.set_param("backbone_selection", 0)
        ret = dbh.fetch_interconnect_details(mock_InstanceModel, mock_CustomerModel, 226)
        assert "has chosen not to use backbone" in caplog.text
        assert ret["is_using_sp_interconnect"] == False
        assert ret["interconnect_exists_in_region"] == False

        # Mock Backbone Selection
        mock_CustomerModel.set_param("backbone_selection", 1)
        mock_CustomerModel.set_param("excluded_backbone_regions",
                                     '{"excluded_backbone_regions": ["asia-east1", "asia-east2", "asia-northwest1", "asia-northwest2", "asia-south1", "asia-south2", "asia-southeast1", "asia-southeast2"]}')
        # Mock the region from compute_region call
        mock_get_cloud_native_location_name_from_region_id.return_value = "asia-east1"

        # In exclude region list
        ret = dbh.fetch_interconnect_details(mock_InstanceModel, mock_CustomerModel, 226)
        assert 'in the exclude list' in caplog.text
        assert ret["is_using_sp_interconnect"] == False
        assert ret["interconnect_exists_in_region"] == False

        mock_get_cloud_native_location_name_from_region_id.return_value = "eu-west2"
        ret = dbh.fetch_interconnect_details(mock_InstanceModel, mock_CustomerModel, 226)
        assert 'Location not in excluded_clouds_list' in caplog.text
        assert ret["is_using_sp_interconnect"] == True
        assert ret["interconnect_exists_in_region"] == True

        # Test with null excluded_backbone_regions
        mock_CustomerModel.set_param("excluded_backbone_regions", None)
        ret = dbh.fetch_interconnect_details(mock_InstanceModel, mock_CustomerModel, 226)
        assert ret["is_using_sp_interconnect"] == True
        assert ret["interconnect_exists_in_region"] == True

        # Test with empty excluded regions list
        mock_CustomerModel.set_param("excluded_backbone_regions", '{"excluded_backbone_regions": []}')
        ret = dbh.fetch_interconnect_details(mock_InstanceModel, mock_CustomerModel, 226)
        assert ret["is_using_sp_interconnect"] == True
        assert ret["interconnect_exists_in_region"] == True

        # Test different node types
        for node_type in [NODE_TYPE_GP_GATEWAY, NODE_TYPE_SERVICE_CONN, NODE_TYPE_REMOTE_NET]:
            mock_InstanceModel.set_param("node_type", node_type)
            ret = dbh.fetch_interconnect_details(mock_InstanceModel, mock_CustomerModel, 226)
            assert ret["is_using_sp_interconnect"] == True
            assert ret["interconnect_exists_in_region"] == True

        # Test unsupported node types for AWS
        for node_type in [NODE_TYPE_SWG_PROXY, NODE_TYPE_BI_NH_PROXY, NODE_TYPE_PROBE_VM]:
            mock_InstanceModel.set_param("node_type", node_type)
            ret = dbh.fetch_interconnect_details(mock_InstanceModel, mock_CustomerModel, 226)
            assert ret["is_using_sp_interconnect"] == False


    @patch('libs.db.dbhandle.get_salt_profile_for_instance_entry')
    @patch('libs.db.dbhandle.get_salt_profile_name_for_instance_entry')
    @patch('libs.db.dbhandle.get_portal_base')
    @patch('libs.db.dbhandle.generate_otp')
    @patch('libs.db.dbhandle.InstanceModel')
    @patch('libs.db.dbhandle.DbHandle.validate_set_inst_using_sp_interconnect')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_sec_key_name')
    @patch('libs.db.dbhandle.DbHandle.get_updated_max_capacity_for_instance')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_id_for_instance')
    @patch('libs.db.dbhandle.CustomerModel')
    @patch('libs.db.dbhandle.gpcs_get_compute_region_idx_from_edge_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_cloud_type_from_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_edge_region_name_from_edge_region_idx')
    @patch('libs.db.dbhandle.DbHandle.get_nodeTypeInstance')
    @patch('libs.db.dbhandle.is_no_passive_instances_enabled')
    def test_get_new_instance_int_set_nat_inst_plugin(self, mock_passive, mock_get_nodeTypeInstance, mock_gpcs_get_edge_region_name_from_edge_region_idx, mock_gpcs_get_cloud_type_from_region_idx, mock_gpcs_get_compute_region_idx_from_edge_region_idx, mock_CustomerModel, mock_get_vpc_id_for_instance, mock_get_updated_max_capacity_for_instance, mock_get_vpc_sec_key_name, mock_validate_set_inst_using_sp_interconnect, mock_InstanceModel, mock_generate_otp, mock_get_portal_base, mock_get_salt_profile_name_for_instance_entry, mock_get_salt_profile_for_instance_entry, caplog):
        '''
           UT to test NAT instance plugin version setup
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.conn = MagicMock()
        dbh.capacity_type = ""
        dbh.cloud_machine_type = ""
        dbh.market_type = ""
        dbh.dpdk_qcount = 0
        mock_passive.return_value = False
        mock_get_nodeTypeInstance.return_value = MockNodeTypeModel(156, 'test')
        mock_gpcs_get_edge_region_name_from_edge_region_idx.return_value = 'usa'
        mock_gpcs_get_cloud_type_from_region_idx.return_value = 'gcp'
        mock_gpcs_get_compute_region_idx_from_edge_region_idx.return_value = 220
        mock_CustomerModel.return_value = MockCustomerModel()
        mock_CustomerModel.set_param("external_ipv6_support", True)
        mock_get_vpc_id_for_instance.return_value = 75
        mock_get_updated_max_capacity_for_instance.return_value = (500, 1000)
        mock_get_vpc_sec_key_name.return_value = "test"
        mock_validate_set_inst_using_sp_interconnect.return_value = MagicMock()
        mock_InstanceModel.return_value = MockInstanceModel(dbh=dbh)
        mock_generate_otp.return_value = 150
        mock_get_portal_base.return_value = "test"
        mock_get_salt_profile_name_for_instance_entry.return_value = "test-entry"
        mock_get_salt_profile_for_instance_entry.return_value = {"name": "test"}
        dbh.get_new_instance_int('4.2.0', "NAT", 'test', 156, 'test', is_instance_behind_nlb=False)
        assert "\'collectd\': \'4.1.0-1\'" in caplog.text
        assert "\'sasemsgd\': \'3.2.1-2\'" in caplog.text
        assert "\'pacached\': \'4.2.0-1\'" in caplog.text
        
    @patch('libs.db.dbhandle.get_salt_profile_for_instance_entry')
    @patch('libs.db.dbhandle.get_salt_profile_name_for_instance_entry')
    @patch('libs.db.dbhandle.get_portal_base')
    @patch('libs.db.dbhandle.generate_otp')
    @patch('libs.db.dbhandle.InstanceModel')
    @patch('libs.db.dbhandle.DbHandle.validate_set_inst_using_sp_interconnect')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_sec_key_name')
    @patch('libs.db.dbhandle.DbHandle.get_updated_max_capacity_for_instance')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_id_for_instance')
    @patch('libs.db.dbhandle.CustomerModel')
    @patch('libs.db.dbhandle.gpcs_get_compute_region_idx_from_edge_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_cloud_type_from_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_edge_region_name_from_edge_region_idx')
    @patch('libs.db.dbhandle.DbHandle.get_nodeTypeInstance')
    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    @patch('libs.db.dbhandle.mu_sc_mapping_feature_flag_enabled')
    @patch('libs.db.dbhandle.is_no_passive_instances_enabled')
    def test_get_new_instance_int_get_sc_map_id_first(self, mock_passive, ff_mock, mock_cursor, mock_get_nodeTypeInstance, mock_gpcs_get_edge_region_name_from_edge_region_idx, mock_gpcs_get_cloud_type_from_region_idx, mock_gpcs_get_compute_region_idx_from_edge_region_idx, mock_CustomerModel, mock_get_vpc_id_for_instance, mock_get_updated_max_capacity_for_instance, mock_get_vpc_sec_key_name, mock_validate_set_inst_using_sp_interconnect, mock_InstanceModel, mock_generate_otp, mock_get_portal_base, mock_get_salt_profile_name_for_instance_entry, mock_get_salt_profile_for_instance_entry, caplog):
        '''
           UT to ensure that for instances behind the NLB we will enable the IPv6 support flag in instance_entry_dict
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.conn = MagicMock()
        dbh.capacity_type = ""
        dbh.cloud_machine_type = ""
        dbh.market_type = ""
        dbh.dpdk_qcount = 0
        dbh.cpu_platform = "test_platform"
        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchall = mock_cursor.return_value.fetchall
        mock_cursor_fetchall.return_value = [(1, 1), (2, 2)]
        
        mock_passive.return_value = False
        mock_get_nodeTypeInstance.return_value = MockNodeTypeModel(49)
        mock_gpcs_get_edge_region_name_from_edge_region_idx.return_value = 'usa'
        mock_gpcs_get_cloud_type_from_region_idx.return_value = 'gcp'
        mock_gpcs_get_compute_region_idx_from_edge_region_idx.return_value = 220
        mock_CustomerModel.return_value = MockCustomerModel()
        mock_CustomerModel.set_param("external_ipv6_support", True)
        mock_get_vpc_id_for_instance.return_value = 75
        mock_get_updated_max_capacity_for_instance.return_value = (500, 1000)
        mock_get_vpc_sec_key_name.return_value = "test"
        mock_validate_set_inst_using_sp_interconnect.return_value = MagicMock()
        mock_InstanceModel.return_value = MockInstanceModel()
        mock_generate_otp.return_value = 150
        mock_get_portal_base.return_value = "test"
        mock_get_salt_profile_name_for_instance_entry.return_value = "test-entry"
        mock_get_salt_profile_for_instance_entry.return_value = {"name": "test"}
        ret = dbh.get_new_instance_int('4.2.0', "GPGATEWAY", 'test', 48, 'test', is_instance_behind_nlb=True)
        assert "next_sc_map_id: 3" in caplog.text
        
    @patch('libs.db.dbhandle.get_salt_profile_for_instance_entry')
    @patch('libs.db.dbhandle.get_salt_profile_name_for_instance_entry')
    @patch('libs.db.dbhandle.get_portal_base')
    @patch('libs.db.dbhandle.generate_otp')
    @patch('libs.db.dbhandle.InstanceModel')
    @patch('libs.db.dbhandle.DbHandle.validate_set_inst_using_sp_interconnect')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_sec_key_name')
    @patch('libs.db.dbhandle.DbHandle.get_updated_max_capacity_for_instance')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_id_for_instance')
    @patch('libs.db.dbhandle.CustomerModel')
    @patch('libs.db.dbhandle.gpcs_get_compute_region_idx_from_edge_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_cloud_type_from_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_edge_region_name_from_edge_region_idx')
    @patch('libs.db.dbhandle.DbHandle.get_nodeTypeInstance')
    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    @patch('libs.db.dbhandle.mu_sc_mapping_feature_flag_enabled')
    @patch('libs.db.dbhandle.is_no_passive_instances_enabled')
    def test_get_new_instance_int_get_sc_map_id_no_mappings(self, mock_passive, ff_mock, mock_cursor, mock_get_nodeTypeInstance, mock_gpcs_get_edge_region_name_from_edge_region_idx, mock_gpcs_get_cloud_type_from_region_idx, mock_gpcs_get_compute_region_idx_from_edge_region_idx, mock_CustomerModel, mock_get_vpc_id_for_instance, mock_get_updated_max_capacity_for_instance, mock_get_vpc_sec_key_name, mock_validate_set_inst_using_sp_interconnect, mock_InstanceModel, mock_generate_otp, mock_get_portal_base, mock_get_salt_profile_name_for_instance_entry, mock_get_salt_profile_for_instance_entry, caplog):
        '''
           UT to ensure that for instances behind the NLB we will enable the IPv6 support flag in instance_entry_dict
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.conn = MagicMock()
        dbh.capacity_type = ""
        dbh.cloud_machine_type = ""
        dbh.market_type = ""
        dbh.dpdk_qcount = 0
        dbh.cpu_platform = "test_platform"
        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchall = mock_cursor.return_value.fetchall
        mock_cursor_fetchall.return_value = [(1, 0), (2, 0), (3, 0)]
        
        mock_passive.return_value = False
        mock_get_nodeTypeInstance.return_value = MockNodeTypeModel(49)
        mock_gpcs_get_edge_region_name_from_edge_region_idx.return_value = 'usa'
        mock_gpcs_get_cloud_type_from_region_idx.return_value = 'gcp'
        mock_gpcs_get_compute_region_idx_from_edge_region_idx.return_value = 220
        mock_CustomerModel.return_value = MockCustomerModel()
        mock_CustomerModel.set_param("external_ipv6_support", True)
        mock_get_vpc_id_for_instance.return_value = 75
        mock_get_updated_max_capacity_for_instance.return_value = (500, 1000)
        mock_get_vpc_sec_key_name.return_value = "test"
        mock_validate_set_inst_using_sp_interconnect.return_value = MagicMock()
        mock_InstanceModel.return_value = MockInstanceModel()
        mock_generate_otp.return_value = 150
        mock_get_portal_base.return_value = "test"
        mock_get_salt_profile_name_for_instance_entry.return_value = "test-entry"
        mock_get_salt_profile_for_instance_entry.return_value = {"name": "test"}
        ret = dbh.get_new_instance_int('4.2.0', "GPGATEWAY", 'test', 48, 'test', is_instance_behind_nlb=True)
        assert "next_sc_map_id: 1" in caplog.text
        
    @patch('libs.db.dbhandle.get_salt_profile_for_instance_entry')
    @patch('libs.db.dbhandle.get_salt_profile_name_for_instance_entry')
    @patch('libs.db.dbhandle.get_portal_base')
    @patch('libs.db.dbhandle.generate_otp')
    @patch('libs.db.dbhandle.InstanceModel')
    @patch('libs.db.dbhandle.DbHandle.validate_set_inst_using_sp_interconnect')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_sec_key_name')
    @patch('libs.db.dbhandle.DbHandle.get_updated_max_capacity_for_instance')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_id_for_instance')
    @patch('libs.db.dbhandle.CustomerModel')
    @patch('libs.db.dbhandle.gpcs_get_compute_region_idx_from_edge_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_cloud_type_from_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_edge_region_name_from_edge_region_idx')
    @patch('libs.db.dbhandle.DbHandle.get_nodeTypeInstance')
    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    @patch('libs.db.dbhandle.mu_sc_mapping_feature_flag_enabled')
    @patch('libs.db.dbhandle.is_no_passive_instances_enabled')
    def test_get_new_instance_int_get_sc_map_id_no_instances(self, mock_passive, ff_mock, mock_cursor, mock_get_nodeTypeInstance, mock_gpcs_get_edge_region_name_from_edge_region_idx, mock_gpcs_get_cloud_type_from_region_idx, mock_gpcs_get_compute_region_idx_from_edge_region_idx, mock_CustomerModel, mock_get_vpc_id_for_instance, mock_get_updated_max_capacity_for_instance, mock_get_vpc_sec_key_name, mock_validate_set_inst_using_sp_interconnect, mock_InstanceModel, mock_generate_otp, mock_get_portal_base, mock_get_salt_profile_name_for_instance_entry, mock_get_salt_profile_for_instance_entry, caplog):
        '''
           UT to ensure that for instances behind the NLB we will enable the IPv6 support flag in instance_entry_dict
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.conn = MagicMock()
        dbh.capacity_type = ""
        dbh.cloud_machine_type = ""
        dbh.market_type = ""
        dbh.dpdk_qcount = 0
        dbh.cpu_platform = "test_platform"
        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchall = mock_cursor.return_value.fetchall
        mock_cursor_fetchall.return_value = []
        
        mock_passive.return_value = False
        mock_get_nodeTypeInstance.return_value = MockNodeTypeModel(49)
        mock_gpcs_get_edge_region_name_from_edge_region_idx.return_value = 'usa'
        mock_gpcs_get_cloud_type_from_region_idx.return_value = 'gcp'
        mock_gpcs_get_compute_region_idx_from_edge_region_idx.return_value = 220
        mock_CustomerModel.return_value = MockCustomerModel()
        mock_CustomerModel.set_param("external_ipv6_support", True)
        mock_get_vpc_id_for_instance.return_value = 75
        mock_get_updated_max_capacity_for_instance.return_value = (500, 1000)
        mock_get_vpc_sec_key_name.return_value = "test"
        mock_validate_set_inst_using_sp_interconnect.return_value = MagicMock()
        mock_InstanceModel.return_value = MockInstanceModel()
        mock_generate_otp.return_value = 150
        mock_get_portal_base.return_value = "test"
        mock_get_salt_profile_name_for_instance_entry.return_value = "test-entry"
        mock_get_salt_profile_for_instance_entry.return_value = {"name": "test"}
        ret = dbh.get_new_instance_int('4.2.0', "GPGATEWAY", 'test', 48, 'test', is_instance_behind_nlb=True)
        assert "next_sc_map_id: 1" in caplog.text
    
    @patch('libs.db.dbhandle.get_salt_profile_for_instance_entry')
    @patch('libs.db.dbhandle.get_salt_profile_name_for_instance_entry')
    @patch('libs.db.dbhandle.get_portal_base')
    @patch('libs.db.dbhandle.generate_otp')
    @patch('libs.db.dbhandle.InstanceModel')
    @patch('libs.db.dbhandle.DbHandle.validate_set_inst_using_sp_interconnect')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_sec_key_name')
    @patch('libs.db.dbhandle.DbHandle.get_updated_max_capacity_for_instance')
    @patch('libs.db.dbhandle.DbHandle.get_vpc_id_for_instance')
    @patch('libs.db.dbhandle.CustomerModel')
    @patch('libs.db.dbhandle.gpcs_get_compute_region_idx_from_edge_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_cloud_type_from_region_idx')
    @patch('libs.db.dbhandle.gpcs_get_edge_region_name_from_edge_region_idx')
    @patch('libs.db.dbhandle.DbHandle.get_nodeTypeInstance')
    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    @patch('libs.db.dbhandle.mu_sc_mapping_feature_flag_enabled')
    @patch('libs.db.dbhandle.is_no_passive_instances_enabled')
    @patch('libs.db.dbhandle.DbHandle.get_rnsc_fqdn_ff')
    @patch('libs.db.dbhandle.plugin_version_supports_fqdn')
    @patch('libs.db.dbhandle.saas_agent_version_supports_fqdn')
    @patch('libs.db.dbhandle.is_ingress_ip_reduction_enabled')
    @patch('libs.db.dbhandle.DbHandle.get_agentless_uda_ff')
    def test_get_new_instance_int_rnsc_fqdn(self, mock_get_agentless_uda, mock_ingress_ip, mock_saas_agent_fqdn, mock_plugin_fqdn, mock_rnsc_fqdn_ff, mock_passive, ff_mock, mock_cursor, mock_get_nodeTypeInstance, mock_gpcs_get_edge_region_name_from_edge_region_idx, mock_gpcs_get_cloud_type_from_region_idx, mock_gpcs_get_compute_region_idx_from_edge_region_idx, mock_CustomerModel, mock_get_vpc_id_for_instance, mock_get_updated_max_capacity_for_instance, mock_get_vpc_sec_key_name, mock_validate_set_inst_using_sp_interconnect, mock_InstanceModel, mock_generate_otp, mock_get_portal_base, mock_get_salt_profile_name_for_instance_entry, mock_get_salt_profile_for_instance_entry, caplog):
        '''
           UT to ensure that for instances behind the NLB we will enable the IPv6 support flag in instance_entry_dict
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        dbh.conn = MagicMock()
        dbh.capacity_type = ""
        dbh.cloud_machine_type = ""
        dbh.market_type = ""
        dbh.dpdk_qcount = 0
        dbh.cpu_platform = "test_platform"
        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchall = mock_cursor.return_value.fetchall
        mock_cursor_fetchall.return_value = []
        
        mock_passive.return_value = False
        mock_get_nodeTypeInstance.return_value = MockNodeTypeModel(node_type=48, ha_reqd=True)
        mock_gpcs_get_edge_region_name_from_edge_region_idx.return_value = 'usa'
        mock_gpcs_get_cloud_type_from_region_idx.return_value = 'gcp'
        mock_gpcs_get_compute_region_idx_from_edge_region_idx.return_value = 220
        mock_CustomerModel.return_value = MockCustomerModel()
        mock_CustomerModel.set_param("external_ipv6_support", True)

        mock_get_vpc_id_for_instance.return_value = 75
        mock_get_updated_max_capacity_for_instance.return_value = (500, 1000)
        mock_get_vpc_sec_key_name.return_value = "test"
        mock_validate_set_inst_using_sp_interconnect.return_value = MagicMock()
        mock_InstanceModel.return_value = MockInstanceModel()
        mock_generate_otp.return_value = 150
        mock_get_portal_base.return_value = "test"
        mock_get_salt_profile_name_for_instance_entry.return_value = "test-entry"
        mock_get_salt_profile_for_instance_entry.return_value = {"name": "test"}
        ret = dbh.get_new_instance_int('5.1.0', "SFW", 'test', 48, 'test', is_instance_behind_nlb=True)
        mock_saas_agent_fqdn.assert_called_once()
        mock_plugin_fqdn.assert_called_once()
        mock_rnsc_fqdn_ff.assert_called_once()
    
    @pytest.mark.parametrize("mock_infra_version, expected_return", (
        ("5.0.0", False),
        ("5.1.0", True),
        ("5.2.0", True)))
    def test_get_rnsc_fqdn_ff(self, mock_infra_version, expected_return):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        dbh.conn = MagicMock()
        
        def get_user_flags(tenant_id, app_name, major_version):
            assert major_version == mock_infra_version
            assert app_name == "saas_infra"
            return {"rnsc_fqdn_feature_flag": {"value": 1 if expected_return else 0}}
        
        dbh.extract_infra_version = MagicMock(return_value=mock_infra_version)
        dbh.get_user_flags = MagicMock()
        dbh.get_user_flags.side_effect = get_user_flags
        
        ret = dbh.get_rnsc_fqdn_ff("1234")
        assert ret == expected_return        

    @pytest.mark.parametrize("test_input, expected_output", [(('',''), None), (('tgw_12345', 'tgw_created'), 'tgw_12345')])
    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_sp_tgw_state(self, mock_cursor, test_input, expected_output):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        dbh.conn = MagicMock()

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_cursor.return_value.fetchone
        mock_cursor_fetchone.return_value = test_input
        res = dbh.get_sp_tgw_state(1, 'us-west-1')
        assert res is not None
        assert res.result[0] == expected_output

    @pytest.mark.parametrize("test_input, expected_output", [('1.1.1.21', 'id_123456'), ('1.1.2.23', None)])
    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_sp_subnet_details(self, mock_cursor, test_input, expected_output):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        dbh.conn = MagicMock()

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchall = mock_cursor.return_value.fetchall
        mock_cursor_fetchall.return_value = [('id_123456', '1.1.1.0/24')]
        res = dbh.get_sp_subnet_details(1, 'us-west-1', test_input)
        assert res is not None
        assert res.result[0] == expected_output

    @patch('libs.db.dbhandle.InstanceModel')
    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_tenant_region_next_sc_map_id_no_mappings(self, mock_InstanceModel, mock_cursor, caplog):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        dbh.conn = MagicMock()
        
        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchall = mock_cursor.return_value.fetchall
        mock_cursor_fetchall.return_value = [(1, 0), (2, 0), (4, 0)]
        
        map_id = dbh.get_tenant_region_next_sc_map_id(1, 2)
        print(map_id)
        print(caplog.text)
        assert map_id == 1
    
    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_tenant_region_next_sc_map_id_no_instances(self, mock_cursor, caplog):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        dbh.conn = MagicMock()
        
        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchall = mock_cursor.return_value.fetchall
        mock_cursor_fetchall.return_value = []
        
        map_id = dbh.get_tenant_region_next_sc_map_id(1, 2)
        print(map_id)
        assert map_id == 1
        
    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_tenant_region_next_sc_map_id_some_mappings(self, mock_cursor, caplog):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        dbh.conn = MagicMock()
        
        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchall = mock_cursor.return_value.fetchall
        mock_cursor_fetchall.return_value = [(1, 1), (2, 2), (4, 3)]
        
        map_id = dbh.get_tenant_region_next_sc_map_id(1, 2)
        print(map_id)
        assert map_id == 4
        
    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_tenant_region_next_sc_map_id_more_mappings_out_of_order(self, mock_cursor, caplog):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        dbh.conn = MagicMock()
        
        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchall = mock_cursor.return_value.fetchall
        mock_cursor_fetchall.return_value = [(5, 1), (6, 3), (2, 2), (6, 4), (7, 5)]
        
        map_id = dbh.get_tenant_region_next_sc_map_id(1, 2)
        print(map_id)
        assert map_id == 6
    
    
    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_tenant_region_next_sc_map_id_more_mappings_has_hole(self, mock_cursor, caplog):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        dbh.conn = MagicMock()
        
        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchall = mock_cursor.return_value.fetchall
        mock_cursor_fetchall.return_value = [(5, 1), (6, 3), (1, 4), (7, 5)]
        
        map_id = dbh.get_tenant_region_next_sc_map_id(1, 2)
        print(map_id)
        assert map_id == 2
        
    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_tenant_region_next_sc_map_id_more_mappings_two_holes(self, mock_cursor, caplog):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        dbh.conn = MagicMock()
        
        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchall = mock_cursor.return_value.fetchall
        mock_cursor_fetchall.return_value = [(6, 3), (2, 2), (7, 5), (8, 6), (9, 7), (10, 8)]
        
        map_id = dbh.get_tenant_region_next_sc_map_id(1, 2)
        print(map_id)
        assert map_id == 1
        
    @pytest.mark.parametrize("dbResult, retVal", [([[1]],True),([[0]],False)])
    @patch('libs.db.dbhandle.DbHandle.extract_infra_version')
    @patch('libs.db.dbhandle.dbconn.find_one')
    def test_get_service_nic_feature_flag(self, mock_find_one, mock_find_version ,dbResult, retVal):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        mock_find_version.return_value = '5.2.0'
        mock_find_one.return_value = (dbResult)
        assert dbh.get_service_nic_feature_flag(1) == retVal

    @pytest.mark.parametrize("dbResult, retVal", [([[1]],True),([[0]],False)])
    @patch('libs.db.dbhandle.dbconn.find_one')
    def test_get_is_dp_nic_on_shared_vpc_feature_flag(self, mock_find_one, dbResult, retVal):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        mock_find_one.return_value = (dbResult)
        assert dbh.get_is_dp_nic_on_shared_vpc_feature_flag() == retVal

    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_sp_egress_type_success(self, mock_cursor, caplog):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_cursor.return_value.fetchone
        mock_cursor_fetchone.return_value = ['egress_type_1']

        result = dbh.get_sp_egress_type(1, 'us-west1')
        assert result.ok == True
        assert result.result == ['egress_type_1']
        assert "get_sp_egress_type for cust_id: 1 and cloud_native_compute_region: us-west1" in caplog.text
        assert "egress_details: ['egress_type_1']" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_sp_egress_type_none_result(self, mock_cursor, caplog):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_cursor.return_value.fetchone
        mock_cursor_fetchone.return_value = None

        result = dbh.get_sp_egress_type(2, 'us-east1')
        assert result.ok == True
        assert result.result == [None]
        assert "get_sp_egress_type for cust_id: 2 and cloud_native_compute_region: us-east1" in caplog.text
        assert "egress_details: None" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_sp_egress_type_empty_string(self, mock_cursor, caplog):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_cursor.return_value.fetchone
        mock_cursor_fetchone.return_value = ['']

        result = dbh.get_sp_egress_type(3, 'eu-west1')
        assert result.ok == True
        assert result.result == [None]
        assert "get_sp_egress_type for cust_id: 3 and cloud_native_compute_region: eu-west1" in caplog.text
        assert "egress_details: ['']" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_sp_egress_type_exception(self, mock_cursor, caplog):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.ERROR)

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_cursor.return_value.fetchone
        mock_cursor_fetchone.side_effect = Exception("Database error")

        result = dbh.get_sp_egress_type(4, 'ap-southeast1')
        assert result.ok == False
        assert result.result == []
        assert "Failed to get egress type, Exception: Database error" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_sp_egress_type_multiple_calls(self, mock_cursor, caplog):
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_cursor.return_value.fetchone
        mock_cursor_fetchone.side_effect = [['egress_type_1'], None, ['egress_type_2']]

        result1 = dbh.get_sp_egress_type(5, 'us-central1')
        result2 = dbh.get_sp_egress_type(6, 'eu-central1')
        result3 = dbh.get_sp_egress_type(7, 'ap-east1')

        assert result1.ok == True and result1.result == ['egress_type_1']
        assert result2.ok == True and result2.result == [None]
        assert result3.ok == True and result3.result == ['egress_type_2']
        assert "get_sp_egress_type for cust_id: 5 and cloud_native_compute_region: us-central1" in caplog.text
        assert "get_sp_egress_type for cust_id: 6 and cloud_native_compute_region: eu-central1" in caplog.text
        assert "get_sp_egress_type for cust_id: 7 and cloud_native_compute_region: ap-east1" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_tenant_interconnect_vpc_details_success(self, mock_cursor, caplog):
        '''
            This UT will test the scenario where the tenant interconnect VPC details are successfully retrieved
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_cursor.return_value.fetchone
        mock_cursor_fetchone.return_value = ('vpc-123', 'subnet-456', 1000)

        result = dbh.get_tenant_interconnect_vpc_details(100, 'us-central1', 'interconnect-test')

        assert result.ok == True
        assert result.result == ['vpc-123', 'subnet-456', 1000]
        assert "get_tenant_interconnect_vpc_details for cust_id: 100" in caplog.text
        assert "cloud_native_compute_region: us-central1" in caplog.text
        assert "interconnect_name: interconnect-test" in caplog.text
        assert "get_tenant_interconnect_vpc_details output: ('vpc-123', 'subnet-456', 1000)" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_tenant_interconnect_vpc_details_none_result(self, mock_cursor, caplog):
        '''
            This UT will test the scenario where no VPC details are found in the database
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_cursor.return_value.fetchone
        mock_cursor_fetchone.return_value = None

        result = dbh.get_tenant_interconnect_vpc_details(200, 'us-west1', 'interconnect-nonexistent')

        assert result.ok == True
        assert result.result == [None, None]
        assert "vpc_details are not found in tenant_interconnect_vpc_master table for custid: 200" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_tenant_interconnect_vpc_details_empty_vpc_name(self, mock_cursor, caplog):
        '''
            This UT will test the scenario where VPC name is empty string
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_cursor.return_value.fetchone
        mock_cursor_fetchone.return_value = ('', 'subnet-456', 1000)

        result = dbh.get_tenant_interconnect_vpc_details(300, 'eu-west1', 'interconnect-empty-vpc')

        assert result.ok == True
        assert result.result == [None, None]
        assert "vpc_details are not found in tenant_interconnect_vpc_master table for custid: 300" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_tenant_interconnect_vpc_details_empty_subnet_name(self, mock_cursor, caplog):
        '''
            This UT will test the scenario where subnet name is empty string
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_cursor.return_value.fetchone
        mock_cursor_fetchone.return_value = ('vpc-123', '', 1000)

        result = dbh.get_tenant_interconnect_vpc_details(400, 'ap-southeast1', 'interconnect-empty-subnet')

        assert result.ok == True
        assert result.result == [None, None]
        assert "vpc_details are not found in tenant_interconnect_vpc_master table for custid: 400" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_tenant_interconnect_vpc_details_exception(self, mock_cursor, caplog):
        '''
            This UT will test the scenario where an exception is raised during database query
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.ERROR)

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_cursor.return_value.fetchone
        mock_cursor_fetchone.side_effect = Exception("Database connection error")

        result = dbh.get_tenant_interconnect_vpc_details(500, 'us-east1', 'interconnect-error')

        assert result.ok == False
        assert result.result == []
        assert "Failed to get onramp vpc details, Exception: Database connection error" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_tenant_interconnect_vpc_details_zero_bandwidth(self, mock_cursor, caplog):
        '''
            This UT will test the scenario where bandwidth is zero but VPC and subnet are valid
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_cursor.return_value.fetchone
        mock_cursor_fetchone.return_value = ('vpc-789', 'subnet-101', 0)

        result = dbh.get_tenant_interconnect_vpc_details(600, 'eu-central1', 'interconnect-zero-bw')

        assert result.ok == True
        assert result.result == ['vpc-789', 'subnet-101', 0]
        assert "get_tenant_interconnect_vpc_details output: ('vpc-789', 'subnet-101', 0)" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_tenant_interconnect_vpc_details_high_bandwidth(self, mock_cursor, caplog):
        '''
            This UT will test the scenario with high bandwidth value
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_cursor.return_value.fetchone
        mock_cursor_fetchone.return_value = ('vpc-high-bw', 'subnet-high-bw', 50000)

        result = dbh.get_tenant_interconnect_vpc_details(700, 'asia-southeast1', 'interconnect-high-bw')

        assert result.ok == True
        assert result.result == ['vpc-high-bw', 'subnet-high-bw', 50000]
        assert "get_tenant_interconnect_vpc_details output: ('vpc-high-bw', 'subnet-high-bw', 50000)" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor')
    def test_get_tenant_interconnect_vpc_details_special_characters(self, mock_cursor, caplog):
        '''
            This UT will test the scenario with special characters in interconnect name
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)

        mock_cursor.return_value = MagicMock()
        mock_cursor_fetchone = mock_cursor.return_value.fetchone
        mock_cursor_fetchone.return_value = ('vpc-special', 'subnet-special', 2000)

        result = dbh.get_tenant_interconnect_vpc_details(800, 'us-west2', 'interconnect-test_123')

        assert result.ok == True
        assert result.result == ['vpc-special', 'subnet-special', 2000]
        assert "interconnect_name: interconnect-test_123" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_success_gp_deployed_regions)
    def test_get_gp_deployed_regions_for_cust_dbh_success(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where deployed regions are successfully retrieved for a customer
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        
        result = dbh.get_gp_deployed_regions_for_cust_dbh(48, 123, 220)
        
        assert result is not None
        assert len(result) > 0
        assert "Fetching deployed regions for custid: 123, node_type: 48, compute_region_idx: 220" in caplog.text
        assert "get_gp_deployed_regions_for_cust_dbh, db result:" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_empty_result)
    def test_get_gp_deployed_regions_for_cust_dbh_empty_result(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where no deployed regions are found for a customer
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        
        result = dbh.get_gp_deployed_regions_for_cust_dbh(49, 456, 225)
        
        assert result is None
        assert "Not able to get_gp_deployed_regions_for_cust, ret:" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_multiple_regions_different_compute)
    def test_get_gp_deployed_regions_for_cust_dbh_multiple_regions_filter(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where multiple regions exist but only matching compute_region_idx are returned
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        
        result = dbh.get_gp_deployed_regions_for_cust_dbh(48, 789, 220)
        
        assert result is not None
        assert isinstance(result, list)
        assert "Fetching deployed regions for custid: 789, node_type: 48, compute_region_idx: 220" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_single_matching_region)
    def test_get_gp_deployed_regions_for_cust_dbh_single_match(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where exactly one region matches the compute_region_idx
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        
        result = dbh.get_gp_deployed_regions_for_cust_dbh(50, 101, 215)
        
        assert result is not None
        assert len(result) == 1
        assert "get_gp_deployed_regions_for_cust_dbh, db result:" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_no_matching_compute_region)
    def test_get_gp_deployed_regions_for_cust_dbh_no_compute_match(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where regions exist but none match the compute_region_idx
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        
        result = dbh.get_gp_deployed_regions_for_cust_dbh(48, 202, 999)
        
        assert result is not None
        assert len(result) == 0
        assert "get_gp_deployed_regions_for_cust_dbh, db result:" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=Exception('Database connection error'))
    def test_get_gp_deployed_regions_for_cust_dbh_exception(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where an exception occurs during database query execution
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.ERROR)
        
        result = dbh.get_gp_deployed_regions_for_cust_dbh(48, 303, 230)
        
        assert result is not None
        assert len(result) == 0
        assert "Failed to execute SQL:" in caplog.text
        assert "Database connection error" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_string_compute_region_idx)
    def test_get_gp_deployed_regions_for_cust_dbh_string_compute_region(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where compute_region_idx comparison works with string values
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        
        result = dbh.get_gp_deployed_regions_for_cust_dbh(48, 404, "220")
        
        assert result is not None
        assert "Fetching deployed regions for custid: 404, node_type: 48, compute_region_idx: 220" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_mixed_dynamic_instances)
    def test_get_gp_deployed_regions_for_cust_dbh_filters_dynamic_instances(self, mocked_get_cursor, caplog):
        '''
            This UT will test that the query properly filters out dynamic instances (is_dynamic_instance=0)
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        
        result = dbh.get_gp_deployed_regions_for_cust_dbh(51, 505, 240)
        
        assert result is not None
        assert "get_gp_deployed_regions_for_cust_dbh, db result:" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_large_dataset)
    def test_get_gp_deployed_regions_for_cust_dbh_large_result_set(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where a large number of regions are returned
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        
        result = dbh.get_gp_deployed_regions_for_cust_dbh(52, 606, 250)
        
        assert result is not None
        assert isinstance(result, list)
        assert "Fetching deployed regions for custid: 606, node_type: 52, compute_region_idx: 250" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_duplicate_regions)
    def test_get_gp_deployed_regions_for_cust_dbh_duplicate_handling(self, mocked_get_cursor, caplog):
        '''
            This UT will test how the method handles potential duplicate regions in the result
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        
        result = dbh.get_gp_deployed_regions_for_cust_dbh(48, 707, 260)
        
        assert result is not None
        assert isinstance(result, list)
        assert "get_gp_deployed_regions_for_cust_dbh, db result:" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_zero_values)
    def test_get_gp_deployed_regions_for_cust_dbh_zero_parameters(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where zero values are passed as parameters
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        
        result = dbh.get_gp_deployed_regions_for_cust_dbh(0, 0, 0)
        
        assert "Fetching deployed regions for custid: 0, node_type: 0, compute_region_idx: 0" in caplog.text

    @patch('libs.db.dbhandle.DbHandle.get_cursor', side_effect=mock_inputs_db.Mock_cursor_negative_values)
    def test_get_gp_deployed_regions_for_cust_dbh_negative_parameters(self, mocked_get_cursor, caplog):
        '''
            This UT will test the scenario where negative values are passed as parameters
        '''
        logger = logging.getLogger()
        dbh = DbHandle(logger=logger)
        caplog.set_level(logging.INFO)
        
        result = dbh.get_gp_deployed_regions_for_cust_dbh(-1, -1, -1)
        
        assert "Fetching deployed regions for custid: -1, node_type: -1, compute_region_idx: -1" in caplog.text
