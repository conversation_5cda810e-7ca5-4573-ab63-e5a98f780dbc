import logging
import json

logger = logging.getLogger()

cfg = {
    "envoy_image_version": "pa-proxyvm-3-2-1-1-20221101203853",
    "project_id": "cust-gpcs-mwernosr5hj1l6v29jv3",
    "eproxy_image_project": "image-gpcs-nonprod-01",
    "gcp_img_host": "image-gpcs-nonprod-01",
    "envoy_inital_cnt": "1",
    "envoy_min_cnt": "1",
    "envoy_max_cnt": "10",
    "envoy_traffic_port": "8080",
    "envoy_hc_port": "8887",
    "envoy_disc_size_gb": "40",
    "envoy_autosclale_cpu_target": "0.5",
    "envoy_ssh_key": "admin:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r admin@SJCMACJ15HHTD8",
    "envoy_proxy_secret_id": "customer**********",
    "pa_proxy_config_discovery_fqdn": "dev.cfgds.swg.panclouddev.com",
    "pa_telegraph_fqdn": "dev.tgs.swg.panclouddev.com",
    "pa_proxy_services_ca_key_id": "it_ca_cert",
    "upstream-proxyprotocol-version": "v1",
    "pa_proxy_gatekeeper_fqdn": "dev.gks.swg.panclouddev.com",
    "pa_cdl_proxy_fqdn": "dev.cdls.swg.panclouddev.com"
}


class MockNodeTypeModel:
    def __init__(self, node_type=151, machine_type='test', ha_reqd=False):
        self.node_id = node_type
        self.username = 'test'
        self.ha_reqd = ha_reqd


class MockCustomerModel:

    def __init__(self, dbh=None, custid=None, super_acct_id=None, acct_id=None, logger=None):
        self.fields = ["id",
                       "name",
                       "acct_id",
                       "super_acct_id",
                       "support_acct_id",
                       "tenant_service_group",
                       "sase_fabric_support",
                       "sase_fabric_sdwan_info",
                       "sase_fabric_shard_id",
                       "group_support_acct_id",
                       "version",
                       "is_readonly",
                       "sim_flag",
                       "contentver",
                       "avver",
                       "cont_freeze",
                       "primary_region",
                       "service_subnet",
                       "service_subnet_v6",
                       "colo_connect_subnet",
                       "pre_prod_tenant_type",
                       "subnet_start",
                       "subnet_end",
                       "iam_role",
                       "autofocus",
                       "af_expiry",
                       "logging_fqdn",
                       "gpname",
                       "gpaas_expired",
                       "fwaas_expired",
                       "colo_expired",
                       "cloud_xform_ver",
                       "self_bgp_as",
                       "saas_agent_version",
                       "stable_saas_agent_version",
                       "disable_ospf",
                       "is_deleted",
                       "project_id",
                       "subscription_id",
                       "trace_id",
                       "host_project_id",
                       "orch_project_id",
                       "is_cleanpipe",
                       "cpaas_expired",
                       "svc_acct",
                       "internal_svc_acct",
                       "proxy_vms",
                       "proxy_svc_acct",
                       "overlapped_subnets",
                       "sc_aggregate",
                       "default_route",
                       "default_route_rn",
                       "default_route_mobile",
                       "pbf_config",
                       "pa_features",
                       "enable_gp_autoscale",
                       "gp_enable_vertical_scaling",
                       "gp_enable_horizontal_scaling",
                       "enable_gp_user_count_autoscale",
                       "gp_use_pa_capacity_user_pct",
                       "gp_use_allow_list",
                       "gp_use_allow_list_auto_scale_only",
                       "gp_auto_reserve_ip",
                       "gp_use_subnet_allow_list",
                       "use_auto_updated_schedule",
                       "enable_cpu_model_triggered_scale",
                       "gp_enable_cpu_model_dcpu_2std",
                       "gp_enable_cpu_model_dcpu_1std",
                       "gp_enable_cpu_model_dcpu_avg",
                       "gp_enable_cpu_model_dcpu_avg_1std",
                       "gp_enable_cpu_model_dcpu_avg_2std",
                       "enable_scheduled_scale",
                       "max_num_gw",
                       "gp_scaleup_timeout",
                       "enable_swg_autoscale",
                       "swg_use_allow_list",
                       "swg_auto_reserve_ip",
                       "swg_use_subnet_allow_list",
                       "enable_swg_dcpu_autoscale",
                       "enable_swg_mcpu_autoscale",
                       "enable_swg_memory_autoscale",
                       "enable_swg_scheduled_autoscale",
                       "max_num_swg",
                       "eproxy_image_version",
                       "swg_scaleup_timeout",
                       "create_time",
                       "update_time",
                       "excluded_clouds",
                       "wifclient_version",
                       "dlp_server_fqdn",
                       "collectd_version",
                       "dirsyncd_version",
                       "panorama_version",
                       "config_override",
                       "dns_sec_redirect",
                       "url",
                       "gpfqdn",
                       "saasauthorizer_version",
                       "traffic_steering",
                       "collectd_cfg",
                       "use_latest_distance_mapping_coordinates",
                       "panorama_plugin_version",
                       "cmd_executor_version",
                       "egress_ip_api_usage",
                       "mask_ip_status",
                       "egress_ip_validity_period",
                       "epfqdn",
                       "probe_enable",
                       "psk_secret",
                       "contact_label",
                       "ipsyncd_version",
                       "use_legacy_role",
                       "okyo_enable",
                       "use_v2_dp_subnet",
                       "asymmetric_ha_mode",
                       "rn_vertscale",
                       "sc_vertscale",
                       "is_next_gen_pa",
                       "l3fwdrules_mu",
                       "l3fwdrules_rn",
                       "global_proxy_state",
                       "rn_proxy_state",
                       "sc_proxy_state",
                       "fwdrulesall",
                       "okyo_nat_config",
                       "sasemsgd_version",
                       "mtdc_version",
                       "multi_portal_multi_auth",
                       "is_nlb_supported",
                       "is_commit_validate_supported",
                       "is_central_cache_supported",
                       "mtdc_telemetry_cmd_list",
                       "healthd_version",
                       "pacached_version",
                       "is_ngpa_protocol_enabled",
                       "external_ipv6_support",
                       "is_weighted_load_balancing",
                       "r53_ttl",
                       "excluded_backbone_regions",
                       "backbone_selection",
                       "root_tsg_id",
                       "binhp_eproxy_image_version",
                       "uda_nhproxy_image_version",
                       "mars_agent_profiles_version",
                       "ddns_version",
                       "zdns_fwdd_version"
                       ]
        self.values = [None] * len(self.fields)
        self.set_param("id", 115)
        self.set_param("acct_id", 2000)
        # self.set_param("dbh", dbh)
        self.set_param("acct_id", "**********")
        self.set_param("eproxy_image_version", "pa-proxyvm-3-2-1-1-20221101203853")
        self.set_param("uda_nhproxy_image_version", "")
        self.set_param("binhp_eproxy_image_version", "pa-nhproxyvm-4-2-0-0-e489ba293e-20230708163007")
        self.set_param("project_id", "cust-gpcs-mwernosr5hj1l6v29jv3")
        self.set_param("svc_acct", "<EMAIL>")
        self.set_param("super_acct_id", "**********")
        self.set_param("logging_fqdn", "test.com")
        self.set_param("dlp_server_fqdn", "test1.com")
        self.set_param("contentver", "1.0")
        self.set_param("saas_agent_version", "4.2.0")
        self.set_param("wifclient_version", "4.2.0")
        self.set_param("dirsyncd_version", "4.2.0")
        self.set_param("collectd_version", "4.2.0")
        self.set_param("collectd_cfg", {})
        self.set_param("saasauthorizer_version", "4.2.0")
        self.set_param("cmd_executor_version", "4.2.0")
        self.set_param("ipsyncd_version", "4.2.0")
        self.set_param("healthd_version", "4.2.0")
        self.set_param("sasemsgd_version", "4.2.0")
        self.set_param("mtdc_version", "4.2.0")
        self.set_param("name", "att")
        self.set_param("iam_role", "admin")
        self.set_param("is_nlb_supported", True)
        self.set_param("is_ngpa_protocol_enabled", True)
        self.set_param("is_central_cache_supported", True)
        self.logger = logger

    def get_param(self, field):
        myidx = self.fields.index(field)
        return self.values[myidx]

    def set_param(self, field, value):
        if field in self.fields:
            myidx = self.fields.index(field)
            self.values[myidx] = value
        else:
            raise Exception(f"field {field} does not exist")

    def save(self):
        return True
    
    def get_plugin_dict_for_nat_instance(self, region_id):
        return {'collectd': '4.1.0-1',
                'sasemsgd': '3.2.1-2',
                'pacached': '4.2.0-1'
        }


class MockOrchCfgModel():
    def __init__(self, dbh=None):
        self.dbh = dbh
        self.valid = True
        self.fields = cfg


class MockInstanceModel():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0
        self.logger = None
        if dbh:
            self.logger = dbh.logger
        self.fields = ["id",
                       "vmid",
                       "name",
                       "alias",
                       "node_type",
                       "vpc_id",
                       "custid",
                       "acct_id",
                       "state",
                       "vm_status",
                       "mark_delete",
                       "cloudwatch_ns",
                       "curr_tenants",
                       "max_capacity",
                       "mgt_ip",
                       "pvt_ip",
                       "extrn_ip",
                       "pvt_ip2",
                       "public_ip",
                       "vgw_vip",
                       "sase_fabric_ip",
                       "clusterid",
                       "ha_peer",
                       "ha_state",
                       "ha2_ip",
                       "username",
                       "key_name",
                       "loopback_ip",
                       "loopback_ip_offset",
                       "timestamp",
                       "qname",
                       "retries",
                       "tunnelsubnet",
                       "salt_profile_name",
                       "salt_profile",
                       "logging_fqdn",
                       "dlp_server_fqdn",
                       "version",
                       "content_version",
                       "content_uri",
                       "target_saas_agent_version",
                       "default_or_stable_saas_agent_version",
                       "target_wifclient_version",
                       "target_dirsyncd_version",
                       "target_collectd_version",
                       "collectd_cfg",
                       "target_saasauthorizer_version",
                       "target_sasemsgd_version",
                       "target_mtdc_version",
                       "compute_region_idx",
                       "compute_region_name",
                       "node_fqdn",
                       "iot_certid",
                       "iot_certarn",
                       "healthcheck_id",
                       "cloud_provider",
                       "egress_ip_list",
                       "inbound_access_ip_list",
                       "is_pinned_instance",
                       "is_dynamic_instance",
                       "upgrade_creation",
                       "tagging_status",
                       "is_dedicated_inbound_instance",
                       "spn_name",
                       "target_cmd_executor_version",
                       "lb_details",
                       "mp1_id",
                       "mpdp_clusterid",
                       "slot_nr",
                       "target_ipsyncd_version",
                       "target_healthd_version",
                       "use_PBF",
                       "native_machine_type",
                       "gw_capabilities",
                       "has_nat_instance",
                       "is_pa_connector_managed",
                       "is_instance_behind_nlb",
                       "interface_ip_list",
                       "upgrade_status",
                       "ibgp_med_value",
                       "is_two_phase_upgrade_supported",
                       "public_ipv6",
                       "is_key_rotation_supported",
                       "delete_deployment",
                       "is_using_sp_interconnect",
                       "is_sase_fabric_spn",
                       "egress_ipv6_list",
                       "egress_ipv6_list_subnet",
                       "target_collectd_version",
                       "target_sasemsgd_version",
                       "target_pacached_version",
                       "no_passive_instance"]
        self.values = [None] * len(self.fields)

    def query_all_stage4_entries(self):
        return True, [[0]]

    def set_param(self, field, value):
        if field in self.fields:
            myidx = self.fields.index(field)
            self.values[myidx] = value
        else:
            return 1

    def save(self):
        if self.logger:
            self.logger.info(f"save instance: {zip(self.fields, self.values)}")
        return True

    def save_stage4(self):
        return 1

    def delete(self, dbh):
        return

    def get_sites(self, dbh, transition):
        return [111]

    def bind(dbh, custid, node_type, node_id, is_new, custnode):
        return True

    def get_param(self, field):
        myidx = self.fields.index(field)
        return self.values[myidx]

    def build_name(self):
        return

    def build_alias(self):
        return
