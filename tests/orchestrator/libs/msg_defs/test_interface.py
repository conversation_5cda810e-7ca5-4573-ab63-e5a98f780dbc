import unittest
import logging
from unittest.mock import patch, MagicMock
from libs.msg_defs.interface import (
    VmNodes, OrchHeader, NtfyOnboardingMsg, DeploymentNode, OrchestrationEventsMsg,
    OrchestratorMsg, EgressIPScaleEventMsg, MigrateClusterToIPV6EventMsg,
    TenantIpv6SettingsEventMsg, ColoVlanOnboardEventMsg, DeleteInstanceEventMsg,
    CustomerChangeMsg, BringupMsg, InstanceReplaceMsg, CloudFormationMsg,
    TimerMsg, ErrorMsg, PrivSaseNodeUpdateEventMsg, vmtypes, lic_types
)
from libs.common.shared.py3_utils import convert_str_to_bytes, b64encode

class TestVmNodes(unittest.TestCase):
    def setUp(self):
        self.logger = logging.getLogger('test_logger')

    def test_valid_vmtype(self):
        node = VmNodes('node123', 'FIREWALL', 'us-west', '1.0', self.logger)
        self.assertEqual(node.nodeid, 'node123')
        self.assertEqual(node.vmtype, 'FIREWALL')
        self.assertEqual(node.region, 'us-west')
        self.assertEqual(node.version, '1.0')

    def test_invalid_vmtype(self):
        with patch.object(self.logger, 'error') as mock_error:
            node = VmNodes('node123', 'INVALID_TYPE', 'us-west', '1.0', self.logger)
            mock_error.assert_called_once_with("Error: Type of vm INVALID_TYPE is not supported")
            self.assertIsNone(node.nodeid)
            self.assertIsNone(node.vmtype)

    def test_no_logger_with_invalid_type(self):
        node = VmNodes('node123', 'INVALID_TYPE', 'us-west', '1.0')
        # Should not raise an exception even with invalid type when no logger is provided
        self.assertEqual(node.nodeid, 'node123')
        self.assertEqual(node.vmtype, 'INVALID_TYPE')
        self.assertEqual(node.region, 'us-west')
        self.assertEqual(node.version, '1.0')

class TestOrchHeader(unittest.TestCase):
    def test_init(self):
        header = OrchHeader('test_workflow', 12345)
        self.assertEqual(header.workflow, 'test_workflow')
        self.assertEqual(header.jobid, 12345)

    def test_default_jobid(self):
        header = OrchHeader('test_workflow')
        self.assertEqual(header.workflow, 'test_workflow')
        self.assertEqual(header.jobid, 0)

class TestNtfyOnboardingMsg(unittest.TestCase):
    def test_init_without_log_tag(self):
        header = OrchHeader('test_workflow', 12345)
        msg = NtfyOnboardingMsg(header, 'test_topic', 'create', 'id123', 'pan_job_123',
                              None, 'aws', 'us-west-2', 'FIREWALL')
        self.assertEqual(msg.hdr, header)
        self.assertEqual(msg.topic, 'test_topic')
        self.assertEqual(msg.operation, 'create')
        self.assertEqual(msg.id, 'id123')
        self.assertEqual(msg.panorama_job_id, 'pan_job_123')
        self.assertEqual(msg.cloud_provider, 'aws')
        self.assertEqual(msg.compute_region_id, 'us-west-2')
        self.assertEqual(msg.node_type, 'FIREWALL')
        self.assertIsNone(msg.rastro_log_tag)

    def test_init_with_log_tag(self):
        header = OrchHeader('test_workflow')
        log_tag = "test_log_tag"
        msg = NtfyOnboardingMsg(header, 'test_topic', 'delete', 'id456', 'pan_job_456',
                              log_tag, 'azure', 'eastus', 'GPGATEWAY')
        self.assertEqual(msg.hdr, header)
        self.assertEqual(msg.topic, 'test_topic')
        self.assertEqual(msg.operation, 'delete')
        self.assertEqual(msg.id, 'id456')
        self.assertEqual(msg.panorama_job_id, 'pan_job_456')
        self.assertEqual(msg.cloud_provider, 'azure')
        self.assertEqual(msg.compute_region_id, 'eastus')
        self.assertEqual(msg.node_type, 'GPGATEWAY')
        self.assertEqual(msg.rastro_log_tag, b64encode(log_tag))

    def test_event_message_fields_are_none_by_default(self):
        header = OrchHeader('test_workflow')
        msg = NtfyOnboardingMsg(header, 'test_topic', 'update', 'id789')
        self.assertIsNone(msg.EgressIPScaleEventMsg)
        self.assertIsNone(msg.ColoVlanOnboardEventMsg)
        self.assertIsNone(msg.PrivSaseNodeUpdateEventMsg)
        self.assertIsNone(msg.DeleteInstanceEventMsg)
        self.assertIsNone(msg.MigrateClusterToIPV6EventMsg)
        self.assertIsNone(msg.TenantIpv6SettingsEventMsg)

class TestDeploymentNode(unittest.TestCase):
    def test_init(self):
        node = DeploymentNode('FIREWALL', 'VM-50', '10.0.0', 'high', 't2.large',
                             'small', 'on-demand', True, 'cloud', 'active-passive', True)
        self.assertEqual(node.node_type, 'FIREWALL')
        self.assertEqual(node.machine_type, 'VM-50')
        self.assertEqual(node.version, '10.0.0')
        self.assertEqual(node.capacity_type, 'high')
        self.assertEqual(node.cloud_machine_type, 't2.large')
        self.assertEqual(node.pa_instance_size, 'small')
        self.assertEqual(node.market_type, 'on-demand')
        self.assertTrue(node.mp_dp_split)
        self.assertEqual(node.deployment_type, 'cloud')
        self.assertEqual(node.ha_type, 'active-passive')
        self.assertTrue(node.is_sase)

class TestOrchestrationEventsMsg(unittest.TestCase):
    def test_init_without_log_tag(self):
        header = OrchHeader('test_workflow', 12345)
        msg = OrchestrationEventsMsg(header, 'test_topic', 'CREATE', 'test_event', 'tenant123', 'pan_job_123')
        self.assertEqual(msg.hdr, header)
        self.assertEqual(msg.topic, 'test_topic')
        self.assertEqual(msg.event_type, 'CREATE')
        self.assertEqual(msg.event, 'test_event')
        self.assertEqual(msg.tenant_id, 'tenant123')
        self.assertEqual(msg.panorama_job_id, 'pan_job_123')
        self.assertIsNone(msg.custid)
        self.assertEqual(msg.deployment_nodes, [])
        self.assertIsNone(msg.rastro_log_tag)

    def test_init_with_log_tag(self):
        header = OrchHeader('test_workflow')
        log_tag = "test_log_tag"
        msg = OrchestrationEventsMsg(header, 'test_topic', 'UPDATE', 'test_event', 'tenant456', 'pan_job_456', log_tag)
        self.assertEqual(msg.hdr, header)
        self.assertEqual(msg.topic, 'test_topic')
        self.assertEqual(msg.event_type, 'UPDATE')
        self.assertEqual(msg.event, 'test_event')
        self.assertEqual(msg.tenant_id, 'tenant456')
        self.assertEqual(msg.panorama_job_id, 'pan_job_456')
        self.assertIsNone(msg.custid)
        self.assertEqual(msg.deployment_nodes, [])
        self.assertEqual(msg.rastro_log_tag, b64encode(log_tag))

class TestOrchestratorMsg(unittest.TestCase):
    def test_init_without_log_tag(self):
        header = OrchHeader('test_workflow', 12345)
        nodes = [VmNodes('node1', 'FIREWALL', 'us-west', '1.0')]
        msg = OrchestratorMsg(header, 'cust123', nodes, 'pan_job_123', None, 'aws', 'us-west-2', 'FIREWALL')
        self.assertEqual(msg.hdr, header)
        self.assertEqual(msg.custid, 'cust123')
        self.assertEqual(msg.nodes, nodes)
        self.assertEqual(msg.panorama_job_id, 'pan_job_123')
        self.assertEqual(msg.cloud_provider, 'aws')
        self.assertEqual(msg.compute_region_id, 'us-west-2')
        self.assertEqual(msg.node_type, 'FIREWALL')
        self.assertIsNone(msg.rastro_log_tag)

    def test_init_with_log_tag(self):
        header = OrchHeader('test_workflow')
        nodes = [VmNodes('node2', 'GPGATEWAY', 'us-east', '2.0')]
        log_tag = "test_log_tag"
        msg = OrchestratorMsg(header, 'cust456', nodes, 'pan_job_456', log_tag, 'azure', 'eastus', 'GPGATEWAY')
        self.assertEqual(msg.hdr, header)
        self.assertEqual(msg.custid, 'cust456')
        self.assertEqual(msg.nodes, nodes)
        self.assertEqual(msg.panorama_job_id, 'pan_job_456')
        self.assertEqual(msg.cloud_provider, 'azure')
        self.assertEqual(msg.compute_region_id, 'eastus')
        self.assertEqual(msg.node_type, 'GPGATEWAY')
        self.assertEqual(msg.rastro_log_tag, b64encode(log_tag))

    def test_event_message_fields_are_none_by_default(self):
        header = OrchHeader('test_workflow')
        nodes = []
        msg = OrchestratorMsg(header, 'cust789', nodes)
        self.assertIsNone(msg.EgressIPScaleEventMsg)
        self.assertIsNone(msg.ColoVlanOnboardEventMsg)
        self.assertIsNone(msg.DeleteInstanceEventMsg)
        self.assertIsNone(msg.MigrateClusterToIPV6EventMsg)
        self.assertIsNone(msg.TenantIpv6SettingsEventMsg)
        self.assertIsNone(msg.PrivSaseNodeUpdateEventMsg)

class TestEgressIPScaleEventMsg(unittest.TestCase):
    def test_init(self):
        msg = EgressIPScaleEventMsg()
        self.assertFalse(msg.valid)
        self.assertIsNone(msg.instance_id)
        self.assertIsNone(msg.region_id)
        self.assertIsNone(msg.nr_ip_addresses)

    def test_set_metadata(self):
        msg = EgressIPScaleEventMsg()
        msg.set_EgressIPScaleEvent_metadata('instance123', 'us-west-2', 5)
        self.assertTrue(msg.valid)
        self.assertEqual(msg.instance_id, 'instance123')
        self.assertEqual(msg.region_id, 'us-west-2')
        self.assertEqual(msg.nr_ip_addresses, 5)

class TestMigrateClusterToIPV6EventMsg(unittest.TestCase):
    def test_init(self):
        msg = MigrateClusterToIPV6EventMsg()
        self.assertFalse(msg.valid)
        self.assertIsNone(msg.cluster_id)

    def test_set_metadata(self):
        msg = MigrateClusterToIPV6EventMsg()
        msg.set_MigrateClusterToIPV6_metadata('cluster123')
        self.assertTrue(msg.valid)
        self.assertEqual(msg.cluster_id, 'cluster123')

class TestTenantIpv6SettingsEventMsg(unittest.TestCase):
    def test_init(self):
        msg = TenantIpv6SettingsEventMsg()
        self.assertFalse(msg.valid)
        self.assertIsNone(msg.tenant_id)
        self.assertIsNone(msg.region_name)
        self.assertIsNone(msg.mode)
        self.assertIsNone(msg.node_type)

    def test_set_metadata(self):
        msg = TenantIpv6SettingsEventMsg()
        msg.set_TenantIpv6SettingsEventMsg_metadata('tenant123', 'us-west-2', 'dual-stack', 'FIREWALL')
        self.assertTrue(msg.valid)
        self.assertEqual(msg.tenant_id, 'tenant123')
        self.assertEqual(msg.region_name, 'us-west-2')
        self.assertEqual(msg.mode, 'dual-stack')


class TestPrivSaseNodeUpdateEventMsgMetadata:
    def test_set_metadata_valid_inputs(self):

        msg = PrivSaseNodeUpdateEventMsg()
        assert not msg.valid

        msg.set_PrivSaseNodeUpdateEventMsg_metadata(
            sase_private_region_id="region-1",
            custid="customer-123",
            node_type="gateway",
            service_node_type="edge"
        )

        assert msg.valid
        assert msg.sase_private_region_id == "region-1"
        assert msg.custid == "customer-123"
        assert msg.node_type == "gateway"
        assert msg.service_node_type == "edge"

    def test_set_metadata_empty_inputs(self):
        from src.apps.orchestrator.libs.msg_defs.interface import PrivSaseNodeUpdateEventMsg

        msg = PrivSaseNodeUpdateEventMsg()
        msg.set_PrivSaseNodeUpdateEventMsg_metadata("", "", "", "")

        assert msg.valid
        assert msg.sase_private_region_id == ""
        assert msg.custid == ""
        assert msg.node_type == ""
        assert msg.service_node_type == ""

    def test_set_metadata_numeric_inputs(self):
        from src.apps.orchestrator.libs.msg_defs.interface import PrivSaseNodeUpdateEventMsg

        msg = PrivSaseNodeUpdateEventMsg()
        msg.set_PrivSaseNodeUpdateEventMsg_metadata(
            sase_private_region_id=123,
            custid=456,
            node_type=789,
            service_node_type=101112
        )

        assert msg.valid
        assert msg.sase_private_region_id == 123
        assert msg.custid == 456
        assert msg.node_type == 789
        assert msg.service_node_type == 101112

    def test_set_metadata_none_inputs(self):
        from src.apps.orchestrator.libs.msg_defs.interface import PrivSaseNodeUpdateEventMsg

        msg = PrivSaseNodeUpdateEventMsg()
        msg.set_PrivSaseNodeUpdateEventMsg_metadata(None, None, None, None)

        assert msg.valid
        assert msg.sase_private_region_id is None
        assert msg.custid is None
        assert msg.node_type is None
class TestColoVlanOnboardEventMsg(unittest.TestCase):
    def test_init(self):
        msg = ColoVlanOnboardEventMsg()
        self.assertFalse(msg.valid)
        self.assertIsNone(msg.colo_vlan_onb_id)
        self.assertIsNone(msg.region_id)
        self.assertIsNone(msg.custid)
        self.assertIsNone(msg.update_bgp_peer)

    def test_set_metadata_with_required_params(self):
        msg = ColoVlanOnboardEventMsg()
        msg.set_ColoVlanOnboardingEvent_metadata('vlan123', 'us-east-1')
        self.assertTrue(msg.valid)
        self.assertEqual(msg.colo_vlan_onb_id, 'vlan123')
        self.assertEqual(msg.region_id, 'us-east-1')
        self.assertEqual(msg.custid, 0)
        self.assertEqual(msg.update_bgp_peer, "false")
        self.assertEqual(msg.create_vlan_att, "false")

    def test_set_metadata_with_all_params(self):
        msg = ColoVlanOnboardEventMsg()
        msg.set_ColoVlanOnboardingEvent_metadata('vlan456', 'eu-west-1', 12345, "true", "true")
        self.assertTrue(msg.valid)
        self.assertEqual(msg.colo_vlan_onb_id, 'vlan456')
        self.assertEqual(msg.region_id, 'eu-west-1')
        self.assertEqual(msg.custid, 12345)
        self.assertEqual(msg.update_bgp_peer, "true")
        self.assertEqual(msg.create_vlan_att, "true")

    def test_set_metadata_with_custom_custid(self):
        msg = ColoVlanOnboardEventMsg()
        msg.set_ColoVlanOnboardingEvent_metadata('vlan789', 'ap-south-1', 98765)
        self.assertTrue(msg.valid)
        self.assertEqual(msg.colo_vlan_onb_id, 'vlan789')
        self.assertEqual(msg.region_id, 'ap-south-1')
        self.assertEqual(msg.custid, 98765)
        self.assertEqual(msg.update_bgp_peer, "false")
        self.assertEqual(msg.create_vlan_att, "false")

    def test_set_metadata_with_empty_values(self):
        msg = ColoVlanOnboardEventMsg()
        msg.set_ColoVlanOnboardingEvent_metadata('', '', 0, '', '')
        self.assertTrue(msg.valid)
        self.assertEqual(msg.colo_vlan_onb_id, '')
        self.assertEqual(msg.region_id, '')
        self.assertEqual(msg.custid, 0)
        self.assertEqual(msg.update_bgp_peer, '')
        self.assertEqual(msg.create_vlan_att, '')
