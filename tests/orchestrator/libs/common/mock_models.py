import logging
import mock_import_utils
from unittest.mock import patch, MagicMock, Mock

logger = logging.getLogger()

class DB:
    def __init__(self):
        self.logger = logger

class CustomerModel:
    def __init__(self, custid, dbh):
        self.dbh = dbh
        self.fields = ["super_acct_id", "acct_id", "id", "gpname", "probe_enable", "name",
                       "psk_secret", "is_readonly", "url", "gpfqdn", "eproxy_image_version", "project_id",
                       "asymmetric_ha_mode", "binhp_eproxy_image_version", "uda_nhproxy_image_version"]
        self.values = [1011453056, 1011453056, custid, "test", 0, "test_customer", "dummy", 0, "dummyURL", "dummyFQDN",
                       None, "dummy", "dummy-proj", 1, None, None]
        self.logger = logger

    def get_param(self, field):
        print(self.fields)
        myidx = self.fields.index(field)
        return self.values[myidx]

    def set_param(self, field, value):
        if field in self.fields:
            myidx = self.fields.index(field)
            self.values[myidx] = value
        else:
            raise Exception(f"field {field} does not exist")

    def __str__(self):
        return "dummy"
    def save(self):
        return

class cftDict:
    def __init__(self):
        pass

    def get(self, param1, param2):
        return 'e2-standard-4'


class InstanceModel:

    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        pass

    def get_param(self, key):
        if key == "custid":
            return 1
        if key == "id":
            return self.id
        if key == "node_type":
            return 48
        if key == "cloud_provide":
            return "aws"
        if key == "compute_region_idx":
            return 200
        if key == "slot_nr":
            return 0
        if key == "native_machine_type":
            return "e2-standard-4"
        if key == "clusterid":
            return self.id   
        if key == "ha_peer":
            return (self.id + 1)
        if key == "salt_profile":
            mock_json_loads =  MagicMock()
            return mock_json_loads
        return None            

    def update_upgrade_table(self, dbh, Failed, reuse_vmid, use_instance_id):
        return True
