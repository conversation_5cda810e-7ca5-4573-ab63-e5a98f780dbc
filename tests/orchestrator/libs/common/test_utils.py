import pytest
import mock_import_utils
from unittest.mock import patch, MagicMock
from libs.common.shared.sys_utils import NODE_TYPE_REMOTE_NET, NODE_TYPE_SERVICE_CONN
from libs.common import utils
from libs.model.instancemodel import InstanceModel
import mock_models
from libs.cfg import *
import logging

sql_cpu_2std = (("e2-standard-4", "PA-CAP530", 4, 16), \
              ("e2-highmem-2", "PA-CAP410", 2,16), \
              ("e2-standard-4", "PA-CAP532", 4,16), \
              ("e2-custom-4-24576", "PA-CAP551",4,24), \
                ("e2-highmem-4", "PA-CAP550", 4, 32), \
                ("e2-custom-8-24576", "PA-CAP551", 8, 24), \
                ("e2-standard-8", "PA-CAP550", 8, 32), \
                ("e2-standard-16", "PA-CAP700", 16, 64))

sql_cpu_4std = (("e2-custom-8-24576", "PA-CAP551", 8, 24), \
                ("e2-standard-8", "PA-CAP550", 8, 32), \
                ("e2-standard-16", "PA-CAP700", 16, 64))

sql_cpu_8std = (("e2-standard-16", "PA-CAP700", 16, 64), )

sql_mem_2std = (("e2-standard-4", "PA-CAP530", 4, 16), \
              ("e2-standard-4", "PA-CAP532", 4,16), \
              ("e2-custom-4-24576", "PA-CAP551",4,24), \
                ("e2-highmem-4", "PA-CAP550", 4, 32), \
                ("e2-custom-8-24576", "PA-CAP551", 8, 24), \
                ("e2-standard-8", "PA-CAP550", 8, 32), \
                ("e2-standard-16", "PA-CAP700", 16, 64))

sql_mem_4std = (("e2-custom-4-24576", "PA-CAP551",4,24), \
                ("e2-highmem-4", "PA-CAP550", 4, 32), \
                ("e2-custom-8-24576", "PA-CAP551", 8, 24), \
                ("e2-standard-8", "PA-CAP550", 8, 32), \
                ("e2-standard-16", "PA-CAP700", 16, 64))

sql_mem_8std = (("e2-standard-16", "PA-CAP700", 16, 64), )

upgrade_type = ([["VERTICAL_SCALE_UP"]])

class Mock_cursor():
    def __init__(self):
        self.sql = None
        self.params = None
        self.returnval = None

    def __init__(self, ret):
        self.sql = None
        self.params = None
        self.returnval = ret

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        return ("true", 2, 16, 4, 64)

    def statement(self):
        return {"test"}

    def fetchall(self):
        return (self.returnval)

class Mock_DBHandler():
    def __init__(self, logger, returndb):
        self.logger = logger
        self.returndb = returndb

    def get_cursor(self):
        return Mock_cursor(self.returndb)

    def cursorclose(self, cursor):
        return

class TestOrchestratorUtils:

    test_get_panos_ami_ver_bld_inputs = [
        ("PANOS-10.2.4-515.saas",[10,2,4],"",515,""),
        ("PA-VM-SaaS-10.2.4-515.saas",[10,2,4],"",515,""),
        ("PA-VM-SaaS-10.2.4-c515.saas",[10,2,4],"c",515,""),
        ("PANOS-10.2.4-c515.saas",[10,2,4],"c",515,""),
        ("PA-VM-SaaS-10.2.5.saas",[10,2,5],"",0,""),
        ("PANOS-10.2.5.saas",[10,2,5],"",0,"")
    ]
    @pytest.mark.parametrize("version, retVer, ret_bld_alpha, ret_bld_numeric, expected_output", test_get_panos_ami_ver_bld_inputs)
    def test_get_panos_ami_ver_bld (self, version, retVer, ret_bld_alpha, ret_bld_numeric, expected_output, caplog):
        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger, None)
        caplog.set_level(logging.DEBUG)

        ret = utils.get_panos_ami_ver_bld(version, dbh)
        print(caplog.text)
        assert expected_output in caplog.text
        assert ret == (retVer, ret_bld_alpha, ret_bld_numeric)

    test_check_panos_version_inputs = [
        ("PANOS-9.2.4-c538.saas","PANOS-9.2.4-c70.saas",False,""),
        ("PANOS-9.2.4-c538.saas","PANOS-10.2.4-c70.saas",True,""),
        ("PANOS-9.2.4-c538.saas","PANOS-9.10.4-c70.saas",True,""),
        ("PANOS-9.2.4-c538.saas","PANOS-9.2.10-c70.saas",True,""),
        ("PANOS-9.2.4-c538.saas","PANOS-9.2.4-d70.saas",True,""),
        ("PANOS-9.2.4-c538.saas","PANOS-9.2.4-c700.saas",True,""),
        ("PANOS-9.2.4-c538.saas","PANOS-10.2.4-c70.saas",True,""),
        ("PANOS-10.2.4-c70.saas","PANOS-9.2.4-c538.saas",False,""),
        ("PANOS-10.2.4-c538.saas","PANOS-10.2.4-c538.saas",True,""),
        ("PANOS-10.2.4-c538.saas","PANOS-10.2.4-c70.saas",False,""),
        ("PANOS-10.2.4-c70.saas","PANOS-10.2.4-c538.saas",True,""),
    ]
    @pytest.mark.parametrize("min_version, version, passed, expected_output", test_check_panos_version_inputs)
    def test_check_panos_version (self, min_version, version, passed, expected_output, caplog):
        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger, sql_cpu_2std)
        caplog.set_level(logging.DEBUG)

        ret = utils.check_panos_version(min_version, version, dbh)
        print(caplog.text)
        assert expected_output in caplog.text
        assert ret == passed

    test_get_target_override_for_version_inputs = [
        ("aws", 2345, 49, "t3.xlarge", "PA-VM-10.2.8-c233.saas", "10", 0, "t3g.xlarge", ""),
        ("aws", 2345, 49, "t3a.xlarge", "PA-VM-10.2.8-c233.saas", "10", 0, "m5.xlarge", ""),
        ("aws", 2345, 49, "m5.xlarge", "PA-VM-10.2.8-c233.saas", "10", 0, "m6g.xlarge", ""),
        ("aws", 2345, 49, "m5a.xlarge", "PA-VM-10.2.8-c233saas", "10", 0, "m5a.xlarge", ""),
        ("gcp", 2345, 49, "e2-standard-4", "PA-VM-10.2.8-c233.saas", "10", 0, "e2-standard-4", ""),
        ("aws", 2345, 49, "t3.xlarge", "PA-VM-10.2.8.saas", "10", 0, "t3g.xlarge", ""),
        ("aws", 2345, 50, "m5.xlarge", "PA-VM-10.2.3.saas", "10", 0, "m5.xlarge", ""),
        ("aws", 2345, 50, "m5.xlarge", "PA-VM-10.2.8.saas", "10", 0, "m6g.xlarge", ""),
        ("aws", 2345, 50, "m6i.xlarge", "PA-VM-10.2.2.saas", "10", 0, "m6g.xlarge", ""),
        ("aws", 2345, 50, "m6i.xlarge", "PA-VM-10.2.2-c01.saas", "10", 0, "m6g.xlarge", ""),
        ("aws", 2345, 163, "m6i.xlarge", "PA-VM-10.2.2-c01.saas", "10", 0, "m6i.xlarge", ""),
        ("aws", 2345, 50, "m6i.xlarge", "PA-VM-10.2.2-c01.saas", "10", 1, "m6g.xlarge", ""),
        ("aws", 2345, 163, "m6i.xlarge", "PA-VM-10.2.2-c01.saas", "10", 1, "m6i.xlarge", ""),
    ]
    @patch("libs.common.utils.get_all_rows", return_value=([
        (0, 49, 10, 'aws', 'PA-VM-SaaS-10.2.3-c513.saas', r'{"m5\\." : "m6g.", "m6i\\." : "m6g.", "r5\\." : "r6g.", "r6i\\." : "r6g.", "t3\\.":"t4g.", "t3a\\.":"m5."}'), 
        (2345, 49, 10, 'aws', 'PA-VM-SaaS-10.2.3-c513.saas', r'{"m5\\." : "m6g.", "m6i\\." : "m6g.", "r5\\." : "r6g.", "r6i\\." : "r6g.", "t3\\.":"t3g.", "t3a\\.":"m5."}'),
        (2345, 49, 10, 'aws', 'PA-VM-SaaS-10.2.2.saas', r'{"m6i\\." : "m6g.", "r5\\." : "r6g.", "r6i\\." : "r6g.", "t3\\.":"t3g.", "t3a\\.":"m5."}')
        ]))
    @pytest.mark.parametrize("cloud_provider, custid, node_type, cloud_machine_type, version, compute_region_id, is_ha_upgrade, retVal, \
                             expected_output", test_get_target_override_for_version_inputs)
    def test_get_target_override_for_version (self, dbh, cloud_provider, custid, node_type, cloud_machine_type, version, 
                    compute_region_id, is_ha_upgrade, retVal, expected_output, caplog):
        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger, sql_cpu_2std)
        caplog.set_level(logging.DEBUG)

        ret = utils.get_target_override_for_version(dbh, cloud_provider, custid, node_type, cloud_machine_type, version, compute_region_id, is_ha_upgrade)
        assert expected_output in caplog.text
        assert ret == retVal

    test_get_size_captype_override_inputs = [
        # cloud_provider, custid, compute_region_id, node_type, version, is_ha_upgrade, license_type, native_premium_zone_name,
        #   ret_gpcs_instance_size, ret_capacity_type, ret_dp_gpcs_instance_size, ret_market_type,
        #   ret_dpdk_qcount, ret_ha_mode, expected_output", test_get_size_captype_override_inputs
        ("gcp", 44, 217, 51, "PA-VM-10.2.8-c230.saas", 0, "", "", "n2-highmem-2", "PA-CAP410", "", "", 0, "", ""),
        ("gcp", 44, 10, 51, "PA-VM-10.2.3.saas", 0, "", "", None, None, None, None, None, None, ""),
        ("gcp", 44, 10, 51, "PA-VM-10.2.6.saas", 0, "", "", None, None, None, None, None, None, ""),
        ("gcp", 44, 10, 51, "PA-VM-10.2.9.saas", 0, "", "", "n2-highmem-2", "PA-CAP410", "", "", 0, "", ""),
        ("gcp", 44, 10, 51, "PA-VM-10.2.9-c01.saas", 0, "", "", "n2-highmem-2", "PA-CAP410", "", "", 0, "", "")
    ]
    # cm_internal, lcategory, aws_env_property; 
    @patch("libs.common.utils.get_one_row", return_value=(1,"EVAL","QA"))
    @patch("libs.common.utils.get_all_rows", return_value=([
        #[CLOUD_PROVIDER, COMPUTE_REGION_ID, CUSTID, NODE_TYPE, MIN_VERSION, LICENCE, INTERNAL, GPCS_INSTANCE_SIZE, CAPACITY_TYPE, DP_GPCS_INSTANCE_SIZE, MARKET_TYPE, DPDK_QCOUNT, HA_MODE, ID]
        ("gcp", 0, 0, 51, 'PA-VM-SaaS-10.2.8-c200.saas', "EVAL", 1, "n2-highmem-2", "PA-CAP410", "", "", 0, "", 1),
        ]))
    #@patch("libs.common.utils.check_panos_version", return_value=False)
    @pytest.mark.parametrize("cloud_provider, custid, compute_region_id, node_type, version, is_ha_upgrade, " \
                            "license_type, native_premium_zone_name, " \
                            "ret_gpcs_instance_size, ret_capacity_type, ret_dp_gpcs_instance_size, ret_market_type, " \
                            "ret_dpdk_qcount, ret_ha_mode, expected_output", test_get_size_captype_override_inputs)
    def test_get_size_captype_override(self, mock_get_one_row, mock_get_all_rows, 
                                    cloud_provider, custid, compute_region_id, node_type, version, is_ha_upgrade, license_type, 
                                    native_premium_zone_name, 
                                    ret_gpcs_instance_size, ret_capacity_type, ret_dp_gpcs_instance_size, ret_market_type, ret_dpdk_qcount, 
                                    ret_ha_mode, expected_output, caplog):
        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger, sql_cpu_2std)
        caplog.set_level(logging.DEBUG)

        # Call the function with test parameters
        gpcs_instance_size, capacity_type, dp_gpcs_instance_size, market_type, dpdk_qcount, ha_mode, id = \
            utils.get_size_captype_override(dbh, 
                                            cloud_provider, 
                                            custid, 
                                            compute_region_id, 
                                            node_type, 
                                            version,
                                            is_ha_upgrade,
                                            license_type,
                                            native_premium_zone_name)
        # Assert that the function returned the expected results
        assert expected_output in caplog.text
        assert (gpcs_instance_size, capacity_type, dp_gpcs_instance_size, market_type, dpdk_qcount, ha_mode) == \
            (ret_gpcs_instance_size, ret_capacity_type, ret_dp_gpcs_instance_size, ret_market_type, ret_dpdk_qcount, ret_ha_mode)

    def test_is_sase_sc_enabled(self):
        res = utils.is_sase_sc_enabled()
        assert res == False

    @patch("libs.common.utils.is_sase_sc_enabled", return_value=True)
    def test_get_sase_fabric_node_type_mapping_sase_sc_enabled(self, is_sase_sc_enabled):
        mock_dbh = MagicMock()
        res = utils.get_sase_fabric_node_type_mapping(mock_dbh, NODE_TYPE_SERVICE_CONN)
        assert res == "SC_SPN"

    @patch("libs.common.utils.is_sase_sc_enabled", return_value=False)
    def test_get_sase_fabric_node_type_mapping_sase_sc_disabled(self, is_sase_sc_enabled):
        mock_dbh = MagicMock()
        res = utils.get_sase_fabric_node_type_mapping(mock_dbh, NODE_TYPE_SERVICE_CONN)
        assert res == None

    def test_utils_select_target_machine_type(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger, sql_cpu_2std)
        instModel = InstanceModel(iid=0, dbh=dbh)
        caplog.set_level(logging.DEBUG)
        instModel.get_param = MagicMock()
        instModel.get_param.return_value = 0
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True


        dbh = Mock_DBHandler(logger, sql_cpu_2std)
        #e2-highmem-2 scale up cpu
        #target_cpu=2, target_memory=16, target_sessions=2000000, cur_cpu=2, cur_memory=16, capacity_type="PA-CAP410"
        (target_machine_type, target_capacity_type) = utils.select_target_machine_type(dbh, instModel, "VERTICAL_SCALE_UP", 2, 16, 2000000, 2, 16,  "PA-CAP410")
        assert (target_machine_type, target_capacity_type) == ("e2-highmem-2", "PA-CAP410")

        dbh = Mock_DBHandler(logger, sql_cpu_2std)
        #e2-standard-2 scale up cpu
        #target_cpu=4, target_memory=8, target_sessions=256000, cur_cpu=2, cur_memory=8, capacity_type="PA-CAP310"
        (target_machine_type, target_capacity_type) = utils.select_target_machine_type(dbh, instModel, "VERTICAL_SCALE_UP", 4, 8, 256000, 2, 8,  "PA-CAP310")
        assert (target_machine_type, target_capacity_type) == ("e2-standard-4", "PA-CAP530")

        dbh = Mock_DBHandler(logger, sql_cpu_4std)
        #e2-standard-4 scale up cpu
        #target_cpu=6, target_memory=16, target_sessions=1000000, cur_cpu=4, cur_memory=16, capacity_type="PA-CAP530"
        (target_machine_type, target_capacity_type) = utils.select_target_machine_type(dbh, instModel, "VERTICAL_SCALE_UP", 6, 16, 1000000, 4, 16,  "PA-CAP530")
        assert (target_machine_type, target_capacity_type) == ("e2-custom-8-24576", "PA-CAP551")

        dbh = Mock_DBHandler(logger, sql_cpu_8std)
        #e2-standard-8 scale up cpu
        #target_cpu=10, target_memory=32, target_sessions=2000000, cur_cpu=8, cur_memory=32, capacity_type="PA-CAP550"
        (target_machine_type, target_capacity_type) = utils.select_target_machine_type(dbh, instModel, "VERTICAL_SCALE_UP", 10, 32, 2000000, 8, 32,  "PA-CAP550")
        assert (target_machine_type, target_capacity_type) == ("e2-standard-16", "PA-CAP700")

        #scale up memory
        dbh = Mock_DBHandler(logger, sql_mem_2std)
        #e2-standard-2 scale up mem
        #target_cpu=2, target_memory=10, target_sessions=256000, cur_cpu=2, cur_memory==8, capacity_type="PA-CAP310"
        (target_machine_type, target_capacity_type) = utils.select_target_machine_type(dbh, instModel, "VERTICAL_SCALE_UP", 2, 10, 256000, 2, 8,  "PA-CAP310")
        assert (target_machine_type, target_capacity_type) == ("e2-standard-4", "PA-CAP530")

        dbh = Mock_DBHandler(logger, sql_mem_4std)
        #e2-standard-4 scale up mem
        #target_cpu=4, target_memory=18, target_sessions=1000000, cur_cpu=4, cur_memory=16, capacity_type="PA-CAP530"
        (target_machine_type, target_capacity_type) = utils.select_target_machine_type(dbh, instModel, "VERTICAL_SCALE_UP", 4, 18, 1000000, 4, 16,  "PA-CAP530")
        assert (target_machine_type, target_capacity_type) == ("e2-highmem-4", "PA-CAP550")

        dbh = Mock_DBHandler(logger, sql_mem_8std)
        #e2-standard-8 scale up mem
        #target_cpu=8, target_memory=34, target_sessions=2000000, cur_cpu=8, cur_memory=32, capacity_type="PA-CAP550"
        (target_machine_type, target_capacity_type) = utils.select_target_machine_type(dbh, instModel, "VERTICAL_SCALE_UP", 8, 34, 2000000, 8, 32,  "PA-CAP550")
        assert (target_machine_type, target_capacity_type) == ("e2-standard-16", "PA-CAP700")

         
        dbh = Mock_DBHandler(logger, sql_cpu_4std)
        #e2-standard-16 scale down cpu
        #target_cpu=8 (16/2), target_memory=0, target_sessions=0, cur_cpu=16, cur_memory=64, capacity_type="PA-CAP700"
        (target_machine_type, target_capacity_type) = utils.select_target_machine_type(dbh, instModel, "VERTICAL_SCALE_DOWN", 8, 0, 0, 16, 64,  "PA-CAP700")
        assert (target_machine_type, target_capacity_type) == ("e2-standard-8", "PA-CAP550")

    def test_get_colo_sc_capacity_type_expected_values(self):
        # input
        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger, sql_cpu_2std)

        # Call API
        instance_size, dpdk_qcount, capacity_type = utils.get_colo_sc_capacity_type(dbh)

        # Assert
        assert instance_size == "gpcs-m5a-8xlarge-n2"
        assert dpdk_qcount == 0
        assert capacity_type == "PA-CAP700"

    def test_get_colo_sc_capacity_type_handle_none(self):
        # Arrange
        dbh = None  # Replace with appropriate test value

        # Act
        instance_size, dpdk_qcount, capacity_type = utils.get_colo_sc_capacity_type(dbh)

        # Assert
        assert instance_size == None
        assert dpdk_qcount == None
        assert capacity_type == None

    gcp_replace_case_inputs = [
        ('e2-standard-2', 'e2-standard-4', True),
        ('e2-standard-4', 'e2-standard-2', True),
        ('e2-standard-8', 'e2-standard-4', False),
        ('e2-standard-4', 'e2-standard-8', False)
    ]

    @pytest.mark.parametrize("cur_cap_type, target_cap_type, retVal", gcp_replace_case_inputs)
    def test_utils_is_vert_scale_replace_case(self, cur_cap_type, target_cap_type, retVal, caplog):
        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger, upgrade_type)
        caplog.set_level(logging.DEBUG)
        instance = mock_models.InstanceModel(1, mock_models.DB())
        ret = InstanceModel.is_vert_scale_replace_case(
            instance, dbh, cur_cap_type, target_cap_type)
        assert ret == retVal

    get_machine_family_inputs = [ 
        ('e2-highmem-4', 'gcp', 'e2', ''), 
        ('m5.4xlarge', 'aws', 'm5', ''), 
        ('VM.Standard.E5.Flex-4-16', 'oci', 'VM.Standard.E5.Flex', '')
    ]
    @pytest.mark.parametrize("machine_type, cloud_provider, retVal, expected_output", get_machine_family_inputs)
    def test_utils_get_machine_family(self, machine_type, cloud_provider, retVal, expected_output, caplog):
        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger, None)
        caplog.set_level(logging.DEBUG)

        ret = utils.get_machine_family(dbh, machine_type, cloud_provider)

        print(caplog.text)
        assert expected_output in caplog.text
        assert retVal == ret

    machine_type_info_inputs = [
        ((4,15), 'c4d-standard-4', 'gcp'), ((4,7), 'c4d-highcpu-4', 'gcp'), ((4,31), 'c4d-highmem-4', 'gcp'),
        ((4,15), 'c4-standard-4', 'gcp'),  ((4,8), 'c4-highcpu-4', 'gcp'), ((4,31), 'c4-highmem-4', 'gcp'),
        ((4,16), 'c4a-standard-4', 'gcp'), ((4,8), 'c4a-highcpu-4', 'gcp'), ((4,32), 'c4a-highmem-4', 'gcp'),
        ((4,16), 'n4-standard-4', 'gcp'),  ((4,8), 'n4-highcpu-4', 'gcp'), ((4,32), 'n4-highmem-4', 'gcp'), ((4,10), 'n4-custom-4-10240', 'gcp'),
        ((4,16), 'c3d-standard-4', 'gcp'), ((4,8), 'c3d-highcpu-4', 'gcp'), ((4,32), 'c3d-highmem-4', 'gcp'),
        ((4,16), 'c3-standard-4', 'gcp'),  ((4,8), 'c3-highcpu-4', 'gcp'), ((4,32), 'c3-highmem-4', 'gcp'),
        ((4,16), 'n2d-standard-4', 'gcp'), ((4,4), 'n2d-highcpu-4', 'gcp'), ((4,32), 'n2d-highmem-4', 'gcp'), ((4,10), 'n2d-custom-4-10240', 'gcp'),
        ((4,16), 'n2-standard-4', 'gcp'),  ((4,4), 'n2-highcpu-4', 'gcp'), ((4,32), 'n2-highmem-4', 'gcp'), ((4,10), 'n2-custom-4-10240', 'gcp'),
        ((4,16), 'e2-standard-4', 'gcp'),  ((4,4), 'e2-highcpu-4', 'gcp'), ((4,32), 'e2-highmem-4', 'gcp'), ((4,10), 'e2-custom-4-10240', 'gcp'),
        ((4,16), 't2d-standard-4', 'gcp'),

        ((2,8), 'm5.large', 'aws'), ((4,16), 'm5a.xlarge', 'aws'), ((8,32), 'm6i.2xlarge', 'aws'), ((16,64), 'm6g.4xlarge', 'aws'), 
        ((2,8), 'm6a.large', 'aws'), ((4,16), 'm7i.xlarge', 'aws'), ((8,32), 'm7g.2xlarge', 'aws'),
        ((2,16), 'r5.large', 'aws'), ((4,32), 'r5a.xlarge', 'aws'), ((8,64), 'r6i.2xlarge', 'aws'), ((16,128), 'r6g.4xlarge', 'aws'), 
        ((2,16), 'r6a.large', 'aws'), ((4,32), 'r7i.xlarge', 'aws'), ((8,64), 'r7g.2xlarge', 'aws'), ((16,128), 'r8g.4xlarge', 'aws'),
        ((2,4), 'c5.large', 'aws'), ((4,8), 'c5a.xlarge', 'aws'), ((8,16), 'c6i.2xlarge', 'aws'), ((16,32), 'c6g.4xlarge', 'aws'), 
        ((2,4), 'c6a.large', 'aws'), ((4,8), 'c7i.xlarge', 'aws'), ((8,16), 'c7g.2xlarge', 'aws'), ((16,32), 'c8g.4xlarge', 'aws'),
        ((2,8), 't3.large', 'aws'), ((4,16), 't3a.xlarge', 'aws'), ((8,32), 't4g.2xlarge', 'aws'),

        ((4,16), 'VM.Standard.E5.Flex-4-16', 'oci'), ((12,56), 'VM.Standard.E5.Flex-12-56', 'oci'), ((4,16), 'VM.Standard.E5.Flex-4-16-b2', 'oci')
    ]
    @pytest.mark.parametrize("ret_vcpu_ram, machine_type, cloud_provider", machine_type_info_inputs)
    @patch("libs.common.utils.get_one_row", return_value=None)
    def test_utils_get_machine_type_info(self, mock_get_one_row, ret_vcpu_ram, machine_type, cloud_provider, caplog):
        logger = logging.getLogger('testlogger')
        caplog.set_level(logging.DEBUG)
        mock_dbh = MagicMock()
        instance = mock_models.InstanceModel(1, mock_models.DB())
        (vcpu, ram) = utils.get_machine_type_info(mock_dbh, machine_type, cloud_provider)
        assert (vcpu, ram) == ret_vcpu_ram

    machine_type_info_inputs = [
        (ValueError, 'invalid', 'gcp')
    ]
    @pytest.mark.parametrize("exception, machine_type, cloud_provider", machine_type_info_inputs)
    @patch("libs.common.utils.get_one_row", return_value=None)
    def test_exception_utils_get_machine_type_info(self, mock_get_one_row, exception, machine_type, cloud_provider, caplog):
        logger = logging.getLogger('testlogger')
        caplog.set_level(logging.DEBUG)
        mock_dbh = MagicMock()
        instance = mock_models.InstanceModel(1, mock_models.DB())
        with pytest.raises(exception):
            (vcpu, ram) = utils.get_machine_type_info(mock_dbh, machine_type, cloud_provider)

    @patch("libs.common.utils.get_machine_type_info", return_value=(4, 16))
    def test_utils_set_current_vs_params(self, mock_gcv, caplog):
        logger = logging.getLogger('testlogger')
        caplog.set_level(logging.DEBUG)
        mock_dbh = MagicMock()
        instance = mock_models.InstanceModel(1, mock_models.DB())
        cft_dict = MagicMock()
        cft_dict.get("PrimaryCapacityType").return_value='PA-CAP210'
        (cur_cpu, cur_mem, cur_sessions) = utils.set_current_vs_params(mock_dbh, instance, cft_dict)
        assert cur_cpu == 4
        assert cur_mem == 16
        assert cur_sessions == 0

    def test_utils_get_mp_dp_resources(self, caplog):
        mock_dbh = MagicMock()
        (cur_dp_cpu, cur_mp_mem, mt_dp_cpu, mt_cur_ram) = \
            utils.get_mp_dp_resources(mock_dbh, 4,16,2,8,'PA-CAP530','PA-CAP310')
        assert cur_dp_cpu == 1
        assert cur_mp_mem == 3.5
        assert mt_dp_cpu == 2
        assert mt_cur_ram == 8

    target_values = [
        # VS Input values are represented as (cpuflag, memflag, sessflag)
        #(retVal(set_current_vs_params: cpu, mem ,sessions),  upgrade_type, cpuflag, memflag, sessflag, targetcpu, targetmem, targetsess)
        ((2,8,256000), "VERTICAL_SCALE_UP",1,1,1,4,10,256002),
        ((4,16,600000), "VERTICAL_SCALE_UP",1,1,1,6,18,600002),
        ((8,24,1200000), "VERTICAL_SCALE_UP",1,1,1,10,26,1200002),
        ((8,32,2000000), "VERTICAL_SCALE_DOWN",1,0,0,4,0,0)
    ]

    @pytest.mark.parametrize("retVal, upgrade_type, cpuflag, memflag, sessflag, tcpu, tmem, tsess", target_values)
    @patch("libs.common.utils.set_current_vs_params")
    def test_utils_set_altered_scaled_values(self, mock_set_current_vs_params, retVal,  upgrade_type, \
    cpuflag, memflag, sessflag, tcpu, tmem, tsess, caplog):
        mock_dbh = MagicMock()
        instance = mock_models.InstanceModel(1, mock_models.DB())
        cft_dict = MagicMock()
        mock_set_current_vs_params.return_value = retVal
        (target_cpu, target_memory, target_sessions) = utils.set_altered_scaled_values(mock_dbh, instance, cft_dict, \
            upgrade_type, cpuflag, memflag, sessflag)
        assert target_cpu == tcpu
        assert target_memory == tmem
        assert target_sessions == tsess


    parameters_get_target_machine_type =[
        ("VERTICAL_SCALE_UP",  8, 34, 2000000, 'e2-standard-4', 'PA-CAP550', (True,2,4,8,32)),
        ("VERTICAL_SCALE_UP",  8, 34, 2000000, None, None, (False,2,4,8,32)),
        ("VERTICAL_SCALE_DOWN",  8, 34, 2000000, 'e2-standard-4', 'PA-CAP550', (True,8,16,16,32))
    ]
    @pytest.mark.parametrize("upgrade_type, tcpu, tmem, tsess, tmachine, tcapcity, gor",parameters_get_target_machine_type)
    @patch("libs.common.utils.set_altered_scaled_values", return_value=(4,32,1000000))
    @patch("libs.common.utils.get_one_row")
    @patch("libs.common.utils.set_current_vs_params", return_value = (2,8,256000))
    @patch("libs.common.utils.get_machine_type_info", return_value=((4,32)))
    @patch("libs.common.utils.select_target_machine_type", return_value=(('e2-standard-4','PA-CAP550')))
    def test_utils_get_target_machine_type(self, mock_stmot, mock_gcv, mock_scvs, mock_gor, mock_sasc, \
        upgrade_type, tcpu, tmem, tsess, tmachine, tcapcity, gor):
        mock_dbh = MagicMock()
        instance = mock_models.InstanceModel(1, mock_models.DB())
        cft_dict = MagicMock()
        cft_dict.get("PrimaryCapacityType").return_value='PA-CAP550'
        mock_gor.return_value = gor
        (target_machine_type, target_capacity_type) = utils.get_target_machine_type(mock_dbh, instance, cft_dict, upgrade_type, tcpu, tmem, tsess)
        assert target_machine_type == tmachine
        assert target_capacity_type == tcapcity

    @patch("libs.common.utils.get_one_row", return_value=('gcp', 200, ""))
    @patch("libs.common.utils.get_size_captype_override", return_value=('gpcs-xlarge', 'PA-CAP530',"","",0,'ACTIVE_PASSIVE',1))
    def test_utils_get_capacity_type(self, mock_gofc, mock_gor, caplog):
        mock_dbh = MagicMock()
        (market_type, gpcs_instance_size, dp_gpcs_instance_size, capacity_type, dpdk_qcount, ha_mode) = utils.get_capacity_type(mock_dbh, 0,10,48)
        assert gpcs_instance_size == 'gpcs-xlarge'
        assert capacity_type == 'PA-CAP530'

    parameters_get_target_passive_machine_type =[
        ('e2-standard-4','PA-CAP550',1,'e2-standard-4','PA-CAP550'),
        ('e2-standard-16','PA-CAP700',1,'e2-standard-4','PA-CAP550')
    ]

    #TODO : Check why thos one failed
    '''
    @pytest.mark.parametrize("newinsttype, newcaptype, instance_id, cur_mtype, cur_cap", parameters_get_target_passive_machine_type)
    @patch("libs.common.utils.get_one_row")
    @patch("libs.model.custmodel.CustomerModel", return_value=mock_models.CustomerModel(1, mock_models.DB()))
    @patch("libs.model.instancemodel.InstanceModel", return_value=mock_models.InstanceModel(1, mock_models.DB()))
    def test_utils_get_target_active_passive_machine_type(self, mock_IM, mock_CM, mock_gor, newinsttype, newcaptype, instance_id, cur_mtype, cur_cap):
        mock_dbh = MagicMock()
        mock_gor.return_value = (newinsttype, newcaptype)
        (current_inst_type, current_capacity_type) = utils.get_target_passive_machine_type(mock_dbh, instance_id, cur_mtype, cur_cap)
        assert current_inst_type == newinsttype
        assert current_capacity_type == newcaptype
        (current_inst_type, current_capacity_type) = utils.get_target_active_machine_type(mock_dbh, instance_id, cur_mtype, cur_cap)
        assert current_inst_type == newinsttype
        assert current_capacity_type == newcaptype
    '''

    parameters_early_process_peer_instance =[
        ('e2-standard-4','PA-CAP550','e2-standard-4','PA-CAP550',1,'e2-standard-4','PA-CAP550', True),
        ('e2-standard-4','PA-CAP550','e2-standard-16','PA-CAP700',1,'e2-standard-4','PA-CAP550', False)
    ]
    @pytest.mark.parametrize("target_machine_type, target_capacity_type, newinsttype, newcaptype, instance_id, cur_mtype, cur_cap, retVal", \
        parameters_early_process_peer_instance)
    @patch("libs.common.utils.get_target_machine_type")
    @patch("libs.common.utils.get_target_passive_machine_type")
    @patch('json.loads', return_value=MagicMock())
    @patch("libs.model.instancemodel.InstanceModel", return_value=mock_models.InstanceModel(1, mock_models.DB()))
    def test_utils_early_process_peer_instance(self, mock_IM, mock_json, mock_gtpmt, mock_gtmt, target_machine_type, target_capacity_type, \
        newinsttype, newcaptype, instance_id, cur_mtype, cur_cap, retVal):
        mock_dbh = MagicMock()
        mock_gtmt.return_value = (target_machine_type, target_capacity_type)
        mock_gtpmt.return_value = (newinsttype, newcaptype)
        cft_dict = MagicMock()
        mock_json.return_value = mock_models.cftDict()
        ret = utils.early_process_peer_instance(mock_dbh, mock_IM, cft_dict, "resize_first", "VERTICAL_SCALE_UP", 2,16,1000000)
        assert ret == retVal

    @patch('libs.common.utils.boto3.client')
    def test_mu_sc_mapping_feature_flag_enabled_wrong_lambda_output(self, mock_response, caplog):
        caplog.set_level(logging.INFO)
        mock_payload = MagicMock()
        mock_payload.read.return_value = ''' {
            "test": 1
        }
        '''
        mocked = MagicMock()
        mocked.invoke.return_value= {"Payload" : mock_payload}
        mock_response.return_value = mocked
                
        logger = logging.getLogger()
        cfg = {'region': "us-west1", "acct_id": "testacct_id"}
        result = utils.mu_sc_mapping_feature_flag_enabled(4994, cfg, logger)
        assert result == False
        
    @patch('libs.common.utils.boto3.client')
    def test_mu_sc_mapping_feature_flag_enabled_does_not_exist(self, mock_response, caplog):
        caplog.set_level(logging.INFO)
        mock_payload = MagicMock()
        mock_payload.read.return_value = ''' {
        "2089364995": {
            "frr_feature_flag": {
            "value": 0,
            "feature": "frr_feature_flag",
            "type": "sre",
            "feature_arguments": "",
            "err_msg": "FRR is disabled for this tenant"
            },
            "managed_cloud_wan": {
            "value": 0,
            "feature": "managed_cloud_wan",
            "type": "sre",
            "feature_arguments": "",
            "err_msg": "Managed Cloud WAN feature is disabled for this tenant"
            },
            "mu_30_days_count_ff": {
            "value": 0,
            "feature": "mu_30_days_count_ff",
            "type": "sre",
            "feature_arguments": "",
            "err_msg": ""
            }
          }
        }
        '''
        mocked = MagicMock()
        mocked.invoke.return_value= {"Payload" : mock_payload}
        mock_response.return_value = mocked
                
        logger = logging.getLogger()
        cfg = {'region': "us-west1", "acct_id": "testacct_id"}
        result = utils.mu_sc_mapping_feature_flag_enabled(2089364995, cfg, logger)
        
        assert result == False
        
    @patch('libs.common.utils.boto3.client')
    def test_mu_sc_mapping_feature_flag_enabled_enabled(self, mock_response, caplog):
        caplog.set_level(logging.INFO)
        mock_payload = MagicMock()
        mock_payload.read.return_value = ''' {
        "2089364995": {
            "frr_feature_flag": {
            "value": 0,
            "feature": "frr_feature_flag",
            "type": "sre",
            "feature_arguments": "",
            "err_msg": "FRR is disabled for this tenant"
            },
            "mu_sc_mapping_feature_flag": {
            "value": 1,
            "feature": "managed_cloud_wan",
            "type": "sre",
            "feature_arguments": "",
            "err_msg": "mu_sc_mapping is disabled for this tenant"
            },
            "mu_30_days_count_ff": {
            "value": 0,
            "feature": "mu_30_days_count_ff",
            "type": "sre",
            "feature_arguments": "",
            "err_msg": ""
            }
          }
        }
        '''
        mocked = MagicMock()
        mocked.invoke.return_value= {"Payload" : mock_payload}
        mock_response.return_value = mocked
        logger = logging.getLogger()
        cfg = {'region': "us-west1", "acct_id": "testacct_id"}
        result = utils.mu_sc_mapping_feature_flag_enabled(2089364995, cfg, logger)
        
        assert result == True
        
    @patch('libs.common.utils.boto3.client')
    def test_mu_sc_mapping_feature_flag_enabled_disabled(self, mock_response, caplog):
        caplog.set_level(logging.INFO)
        mock_payload = MagicMock()
        mock_payload.read.return_value = ''' {
        "2089364995": {
            "frr_feature_flag": {
            "value": 0,
            "feature": "frr_feature_flag",
            "type": "sre",
            "feature_arguments": "",
            "err_msg": "FRR is disabled for this tenant"
            },
            "mu_sc_mapping_feature_flag": {
            "value": 0,
            "feature": "managed_cloud_wan",
            "type": "sre",
            "feature_arguments": "",
            "err_msg": "mu_sc_mapping is disabled for this tenant"
            },
            "mu_30_days_count_ff": {
            "value": 0,
            "feature": "mu_30_days_count_ff",
            "type": "sre",
            "feature_arguments": "",
            "err_msg": ""
            }
          }
        }
        '''
        mocked = MagicMock()
        mocked.invoke.return_value= {"Payload" : mock_payload}
        mock_response.return_value = mocked
        logger = logging.getLogger()
        cfg = {'region': "us-west1", "acct_id": "testacct_id"}
        result = utils.mu_sc_mapping_feature_flag_enabled(2089364995, cfg, logger)
        
        assert result == False

    @patch('libs.common.utils.boto3.client')
    def test_colo_100g_no_gre_feature_flag_enabled(self, mock_response, caplog):
        caplog.set_level(logging.INFO)
        mock_payload = MagicMock()
        mock_payload.read.return_value = ''' {
        "2089364995": {
            "frr_feature_flag": {
            "value": 0,
            "feature": "frr_feature_flag",
            "type": "sre",
            "feature_arguments": "",
            "err_msg": "FRR is disabled for this tenant"
            },
            "colo_100g_no_gre_dep": {
            "type": "compatibility",
            "value": 1,
            "value_override": -1,
            "feature": "colo_100g_no_gre",
            "feature_arguments": "",
            "err_msg": "Colo 100G No GRE feature requires atleast 11.2.8 dataplane, 6.1.0 Saas Agent and 6.1.0 Panorama Plugin"
            },
            "colo_100g_no_gre_sre": {
            "type": "sre",
            "feature": "colo_100g_no_gre",
            "feature_arguments": "",
            "err_msg": "Colo 100G No GRE SRE disable by default",
            "value": 1,
            "value_override": -1
            }
          }
        }
        '''
        mocked = MagicMock()
        mocked.invoke.return_value= {"Payload" : mock_payload}
        mock_response.return_value = mocked
        logger = logging.getLogger()
        cfg = {'region': "us-west1", "acct_id": "testacct_id"}
        result = utils.colo_100g_no_gre_feature_flag_enabled(2089364995, cfg, logger)

        assert result == True
    @pytest.mark.parametrize("service_subnet, sec_profile_service, expected_is_present, expected_params", [
        ("subnet-123", "sg-456", True, ("subnet-123", "sg-456")),
        ("None", "None", False, None),
        ("Invalid-subnet", "Invalid-sg", False, None),
        ("", "", False, None),
    ])
    def test_get_service_subnet_details(self, service_subnet, sec_profile_service, expected_is_present, expected_params, caplog):
        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger, None)
        caplog.set_level(logging.DEBUG)

        mock_vpc_instance = MagicMock()
        mock_vpc_instance.service_subnet = service_subnet
        mock_vpc_instance.sec_profile_service = sec_profile_service

        is_present, params = utils.get_service_subnet_details(dbh, mock_vpc_instance)

        assert is_present == expected_is_present
        assert params == expected_params

    def test_get_service_subnet_details_exception(self, caplog):
        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger, None)
        caplog.set_level(logging.ERROR)

        mock_vpc_instance = MagicMock()
        mock_vpc_instance.service_subnet = MagicMock(side_effect=Exception("Test exception"))

        is_present, params = utils.get_service_subnet_details(dbh, mock_vpc_instance)

        assert is_present == False
        assert params == None
        assert "Failed to get service_params" in caplog.text


    @patch('libs.common.utils.get_one_row')
    @patch('shared.loki_capability_utils.LokiFeatureCapabilityUtil')
    def test_get_user_license_cap_no_super_acct_id_found(self, mock_loki_util_class, mock_get_one_row, caplog):
        """Test when super_acct_id query returns None or empty result"""
        logger = logging.getLogger('testlogger')
        mock_dbh = MagicMock()
        mock_dbh.logger = logger
        caplog.set_level(logging.ERROR)

        mock_get_one_row.return_value = None

        result = utils.get_user_license_cap(mock_dbh, 12345)

        assert result is None
        assert "Error querying super account id for cust_id 12345" in caplog.text

    @patch('libs.common.utils.get_one_row')
    @patch('shared.loki_capability_utils.LokiFeatureCapabilityUtil')
    def test_get_user_license_cap_empty_super_acct_id_result(self, mock_loki_util_class, mock_get_one_row, caplog):
        """Test when super_acct_id query returns empty tuple"""
        logger = logging.getLogger('testlogger')
        mock_dbh = MagicMock()
        mock_dbh.logger = logger
        caplog.set_level(logging.ERROR)

        mock_get_one_row.return_value = ()

        result = utils.get_user_license_cap(mock_dbh, 12345)

        assert result is None
        assert "Error querying super account id for cust_id 12345" in caplog.text

    @patch('libs.common.utils.get_one_row')
    @patch('shared.loki_capability_utils.LokiFeatureCapabilityUtil')
    def test_get_user_license_cap_wrong_length_result(self, mock_loki_util_class, mock_get_one_row, caplog):
        """Test when super_acct_id query returns result with wrong length"""
        logger = logging.getLogger('testlogger')
        mock_dbh = MagicMock()
        mock_dbh.logger = logger
        caplog.set_level(logging.ERROR)

        mock_get_one_row.return_value = (123, 456)  # Length 2 instead of 1

        result = utils.get_user_license_cap(mock_dbh, 12345)

        assert result is None
        assert "Error querying super account id for cust_id 12345" in caplog.text

    @patch('libs.common.utils.get_one_row')
    @patch('shared.loki_capability_utils.LokiFeatureCapabilityUtil')
    def test_get_user_license_cap_loki_not_enabled_success(self, mock_loki_util_class, mock_get_one_row, caplog):
        """Test successful path when Loki is not enabled"""
        logger = logging.getLogger('testlogger')
        mock_dbh = MagicMock()
        mock_dbh.logger = logger
        caplog.set_level(logging.DEBUG)

        # Mock LokiFeatureCapabilityUtil
        mock_loki_util = MagicMock()
        mock_loki_util.is_loki_tenant.return_value = False
        mock_loki_util_class.return_value = mock_loki_util

        mock_get_one_row.side_effect = [
            (67890,),  # First call for super_acct_id
            (1500,)    # Second call for license capabilities
        ]

        result = utils.get_user_license_cap(mock_dbh, 12345)

        assert result == 1500
        assert "Loki is not enabled" in caplog.text
        assert "custid: 12345 num users: 1500" in caplog.text

    @patch('libs.common.utils.get_one_row')
    @patch('shared.loki_capability_utils.LokiFeatureCapabilityUtil')
    def test_get_user_license_cap_loki_not_enabled_exception(self, mock_loki_util_class, mock_get_one_row, caplog):
        """Test exception handling when Loki is not enabled but query fails"""
        logger = logging.getLogger('testlogger')
        mock_dbh = MagicMock()
        mock_dbh.logger = logger
        caplog.set_level(logging.ERROR)

        # Mock LokiFeatureCapabilityUtil
        mock_loki_util = MagicMock()
        mock_loki_util.is_loki_tenant.return_value = False
        mock_loki_util_class.return_value = mock_loki_util

        mock_get_one_row.side_effect = [
            (67890,),  # First call for super_acct_id
            Exception("Database error")  # Second call raises exception
        ]

        result = utils.get_user_license_cap(mock_dbh, 12345)

        assert result is None
        assert "get_user_license_cap failed to retrieve from tenant_app_associations" in caplog.text

    @patch('libs.common.utils.get_one_row')
    @patch('shared.loki_capability_utils.LokiFeatureCapabilityUtil')
    def test_get_user_license_cap_loki_not_enabled_null_result(self, mock_loki_util_class, mock_get_one_row, caplog):
        """Test when Loki is not enabled and query returns None"""
        logger = logging.getLogger('testlogger')
        mock_dbh = MagicMock()
        mock_dbh.logger = logger
        caplog.set_level(logging.DEBUG)

        # Mock LokiFeatureCapabilityUtil
        mock_loki_util = MagicMock()
        mock_loki_util.is_loki_tenant.return_value = False
        mock_loki_util_class.return_value = mock_loki_util

        mock_get_one_row.side_effect = [
            (67890,),  # First call for super_acct_id
            None       # Second call returns None
        ]

        result = utils.get_user_license_cap(mock_dbh, 12345)

        assert result is None
        assert "custid: 12345 num users: None" in caplog.text

    @patch('libs.common.utils.get_one_row')
    @patch('shared.loki_capability_utils.LokiFeatureCapabilityUtil')
    @patch.object(utils, 'LokiFeatureCapabilityUtil')
    def test_get_user_license_cap_loki_enabled_success(self, mock_loki_util_class, mock_loki_util_class2, mock_get_one_row, caplog):
        """Test successful path when Loki is enabled"""
        logger = logging.getLogger('testlogger')
        mock_dbh = MagicMock()
        mock_dbh.logger = logger
        caplog.set_level(logging.DEBUG)

        mock_get_one_row.return_value = (67890,)

        # Configure the mock instance methods
        mock_loki_instance = MagicMock()
        mock_loki_instance.is_loki_tenant.return_value = True
        mock_loki_instance.get_mobile_users_num_users.return_value = 2500

        mock_loki_util_class.return_value = mock_loki_instance

        result = utils.get_user_license_cap(mock_dbh, 12345)

        assert result == 2500
        assert "Loki is enabled" in caplog.text
        assert "custid: 12345 num users: 2500" in caplog.text
        mock_loki_util_class.assert_called_once_with(67890, logger)

    @patch('libs.common.utils.get_one_row')
    @patch('shared.loki_capability_utils.LokiFeatureCapabilityUtil')
    @patch.object(utils, 'LokiFeatureCapabilityUtil')
    def test_get_user_license_cap_loki_enabled_none_result(self, mock_loki_util_class, mock_loki_util_class2, mock_get_one_row, caplog):
        """Test when Loki is enabled but returns None"""
        logger = logging.getLogger('testlogger')
        mock_dbh = MagicMock()
        mock_dbh.logger = logger
        caplog.set_level(logging.DEBUG)

        mock_get_one_row.return_value = (67890,)

        # Configure the mock instance methods
        mock_loki_instance = MagicMock()
        mock_loki_instance.is_loki_tenant.return_value = True
        mock_loki_instance.get_mobile_users_num_users.return_value = None

        mock_loki_util_class.return_value = mock_loki_instance

        result = utils.get_user_license_cap(mock_dbh, 12345)

        assert result is None
        assert "Loki is enabled" in caplog.text
        mock_loki_util_class.assert_called_once_with(67890, logger)

    @patch('libs.common.utils.get_one_row')
    @patch('shared.loki_capability_utils.LokiFeatureCapabilityUtil')
    def test_get_user_license_cap_rds_query_returns_empty_string(self, mock_loki_util_class, mock_get_one_row, caplog):
        """Test when RDS query returns empty string instead of number"""
        logger = logging.getLogger('testlogger')
        mock_dbh = MagicMock()
        mock_dbh.logger = logger
        caplog.set_level(logging.ERROR)

        # Mock LokiFeatureCapabilityUtil
        mock_loki_util = MagicMock()
        mock_loki_util.is_loki_tenant.return_value = False
        mock_loki_util_class.return_value = mock_loki_util

        mock_get_one_row.side_effect = [
            (67890,),  # First call for super_acct_id
            ("",)      # Second call returns empty string
        ]

        result = utils.get_user_license_cap(mock_dbh, 12345)

        assert result is None
        assert "get_user_license_cap failed to retrieve from tenant_app_associations" in caplog.text

    @patch('libs.common.utils.get_one_row')
    @patch('shared.loki_capability_utils.LokiFeatureCapabilityUtil')
    def test_get_user_license_cap_rds_query_returns_non_numeric(self, mock_loki_util_class, mock_get_one_row, caplog):
        """Test when RDS query returns non-numeric value"""
        logger = logging.getLogger('testlogger')
        mock_dbh = MagicMock()
        mock_dbh.logger = logger
        caplog.set_level(logging.ERROR)

        # Mock LokiFeatureCapabilityUtil
        mock_loki_util = MagicMock()
        mock_loki_util.is_loki_tenant.return_value = False
        mock_loki_util_class.return_value = mock_loki_util

        mock_get_one_row.side_effect = [
            (67890,),        # First call for super_acct_id
            ("invalid",)     # Second call returns non-numeric string
        ]

        result = utils.get_user_license_cap(mock_dbh, 12345)

        assert result is None
        assert "get_user_license_cap failed to retrieve from tenant_app_associations" in caplog.text

    @patch('libs.common.utils.get_one_row')
    @patch('shared.loki_capability_utils.LokiFeatureCapabilityUtil')
    def test_get_user_license_cap_zero_value(self, mock_loki_util_class, mock_get_one_row, caplog):
        """Test when valid zero value is returned"""
        logger = logging.getLogger('testlogger')
        mock_dbh = MagicMock()
        mock_dbh.logger = logger
        caplog.set_level(logging.DEBUG)

        # Mock LokiFeatureCapabilityUtil
        mock_loki_util = MagicMock()
        mock_loki_util.is_loki_tenant.return_value = False
        mock_loki_util_class.return_value = mock_loki_util

        mock_get_one_row.side_effect = [
            (67890,),  # First call for super_acct_id
            (0,)       # Second call returns 0
        ]

        result = utils.get_user_license_cap(mock_dbh, 12345)

        assert result == 0
        assert "custid: 12345 num users: 0" in caplog.text

    @patch('libs.common.utils.get_one_row')
    def test_get_user_license_cap_super_acct_query_exception(self, mock_get_one_row, caplog):
        """Test exception during super_acct_id query"""
        logger = logging.getLogger('testlogger')
        mock_dbh = MagicMock()
        mock_dbh.logger = logger
        caplog.set_level(logging.ERROR)

        mock_get_one_row.side_effect = Exception("Database connection error")

        result = utils.get_user_license_cap(mock_dbh, 12345)

        assert result is None