import pytest
from unittest.mock import Mock, patch
from src.apps.orchestrator.libs.common.sanitize_logger import SanitizeLogger


class TestSanitizeLoggerInfoMethod:
    """Test class specifically for testing the info method with real scrubadub OTP detection."""

    @pytest.fixture
    def sanitize_logger_with_mock_underlying(self):
        """Create a SanitizeLogger instance with mocked underlying logger but real scrubadub."""
        # Create a mock underlying logger that we can inspect
        mock_underlying_logger = Mock()

        # Create the SanitizeLogger instance with the mock logger (this will use real scrubadub)
        sanitize_logger = SanitizeLogger(mock_underlying_logger)

        return sanitize_logger, mock_underlying_logger

    def test_info_method_with_otp_detection(self, sanitize_logger_with_mock_underlying):
        """Test that the info method properly sanitizes OTP values using real scrubadub."""
        sanitize_logger, mock_underlying_logger = sanitize_logger_with_mock_underlying

        # Use the same payload from test_sanitize_otp
        test_message = "Processing user data"
        otp_payload = "`KeyName`: `orchestrator`, `UserData`: `instance_name=GPPT_620395_eu-west-1_sasequickpov-**********,saas_gpcs_api_endpoint=prod6.gpcloudservice.com/api, cert_fetch_otp=1b122222-8baa-4111-852a-ec6cb31d7b33,cloud_provider=aws,custid=**********,lambdaprefix=sasequickpov-**********,custid_int=7967,gp_domain=gpcloudservice.com,route53acct=a631680999526,bucket-name=pan-content-us-west-2-prod6,panrepo_bucket_name=panrepo-us-west-2-prod6,sase_fabric=False,super_custid=**********,commit_validate=False,is_using_sp_interconnect=False,ciam_service_endpoint=ciam-prod.sasedp.prismaaccess.com,region=eu-west-1`" #gitleaks:allow

        kwargs_with_otp = {
            "user_data": otp_payload, #gitleaks:allow
            "operation": "cert_fetch_otp=abcd-1234-efgh-5678" #gitleaks:allow
        }

        # Call the info method
        sanitize_logger.info(test_message, otp_payload, **kwargs_with_otp)

        # Verify that the underlying logger's info method was called
        mock_underlying_logger.info.assert_called_once()

        # Get the arguments passed to the underlying logger
        call_args, call_kwargs = mock_underlying_logger.info.call_args

        # Verify the message was passed through unchanged
        assert call_args[0] == test_message

        # Verify that the OTP was sanitized in positional arguments
        sanitized_payload = call_args[1]
        assert "cert_fetch_otp={{OTP}}" in sanitized_payload #gitleaks:allow
        assert "cert_fetch_otp=1b135a75-8bdd-4007-8888-111111111" not in sanitized_payload #gitleaks:allow

        # Verify that the OTP was sanitized in keyword arguments
        assert "cert_fetch_otp={{OTP}}" in call_kwargs["user_data"] #gitleaks:allow
        assert "cert_fetch_otp=1b135a75-1111-2345-1234-ec6cb31d7b33" not in call_kwargs["user_data"] #gitleaks:allow
        assert call_kwargs["operation"] == "cert_fetch_otp={{OTP}}" #gitleaks:allow
        assert "cert_fetch_otp=abcd-1234-efgh-5678" not in call_kwargs["operation"] #gitleaks:allow

    def test_info_method_with_multiple_otp_patterns(self, sanitize_logger_with_mock_underlying):
        """Test that the info method handles multiple OTP patterns correctly."""
        sanitize_logger, mock_underlying_logger = sanitize_logger_with_mock_underlying

        test_message = "Multiple OTP test"
        args_with_multiple_otps = (
            "First OTP: cert_fetch_otp=abc123-def456-ghi789", #gitleaks:allow
            "Second OTP: cert_fetch_otp=111-222-333-444", #gitleaks:allow
            "Third OTP: cert_fetch_otp=zzz999yyy888xxx777" #gitleaks:allow
        )

        # Call the info method
        sanitize_logger.info(test_message, *args_with_multiple_otps)

        # Verify that the underlying logger's info method was called
        mock_underlying_logger.info.assert_called_once()

        # Get the arguments passed to the underlying logger
        call_args, call_kwargs = mock_underlying_logger.info.call_args

        # Verify all OTPs were sanitized
        for arg in call_args[1:]:  # Skip the message
            assert "cert_fetch_otp={{OTP}}" in arg #gitleaks:allow
            assert "abc123-def456-ghi789" not in arg #gitleaks:allow
            assert "111-222-333-444" not in arg #gitleaks:allow
            assert "zzz999yyy888xxx777" not in arg #gitleaks:allow

    def test_info_method_with_non_otp_data(self, sanitize_logger_with_mock_underlying):
        """Test that the info method doesn't affect non-OTP sensitive data."""
        sanitize_logger, mock_underlying_logger = sanitize_logger_with_mock_underlying

        test_message = "Regular logging test"
        regular_data = (
            "Normal log message",
            "No sensitive data here",
            "Just regular text"
        )
        kwargs_regular = {
            "status": "success",
            "count": 42,
            "server": "web-01"
        }

        # Call the info method
        sanitize_logger.info(test_message, *regular_data, **kwargs_regular)

        # Verify that the underlying logger's info method was called
        mock_underlying_logger.info.assert_called_once()

        # Get the arguments passed to the underlying logger
        call_args, call_kwargs = mock_underlying_logger.info.call_args

        # Verify the message was passed through unchanged
        assert call_args[0] == test_message

        # Verify regular data was passed through (scrubadub might still process it but shouldn't change it significantly)
        for i, expected in enumerate(regular_data):
            # The data should be similar (scrubadub may process it but not change non-sensitive content)
            assert expected in call_args[1]

    def test_info_method_with_mixed_sensitive_and_otp_data(self, sanitize_logger_with_mock_underlying):
        """Test that the info method handles both OTP and other sensitive data."""
        sanitize_logger, mock_underlying_logger = sanitize_logger_with_mock_underlying

        test_message = "Mixed data test"
        mixed_data = (
            "User <EMAIL> needs cert_fetch_otp=abc-123-def-456", #gitleaks:allow
            "Config: cert_fetch_otp=xyz789, email=<EMAIL>" #gitleaks:allow
        )

        # Call the info method
        sanitize_logger.info(test_message, *mixed_data)

        # Verify that the underlying logger's info method was called
        mock_underlying_logger.info.assert_called_once()

        # Get the arguments passed to the underlying logger
        call_args, call_kwargs = mock_underlying_logger.info.call_args

        # Verify OTPs were sanitized
        for arg in call_args[1:]:
            assert "cert_fetch_otp={{OTP}}" in arg #gitleaks:allow
            assert "cert_fetch_otp=abc-123-def-456" not in arg #gitleaks:allow
            assert "cert_fetch_otp=xyz789" not in arg #gitleaks:allow

    def test_info_method_preserves_other_logger_functionality(self, sanitize_logger_with_mock_underlying):
        """Test that the info method preserves other logging functionality like trace IDs."""
        sanitize_logger, mock_underlying_logger = sanitize_logger_with_mock_underlying

        # Test that other logger methods still work
        test_trace_id = "test-trace-123" #gitleaks:allow
        sanitize_logger.set_trace_id(test_trace_id)
        mock_underlying_logger.set_trace_id.assert_called_once_with(test_trace_id)

        # Test info with OTP
        test_message = "Trace ID test"
        otp_data = "Processing cert_fetch_otp=trace-test-otp-123" #gitleaks:allow

        sanitize_logger.info(test_message, otp_data)

        # Verify info was called and OTP was sanitized
        mock_underlying_logger.info.assert_called_once()
        call_args, call_kwargs = mock_underlying_logger.info.call_args

        assert call_args[0] == test_message
        assert "cert_fetch_otp={{OTP}}" in call_args[1] #gitleaks:allow
        assert "cert_fetch_otp=trace-test-otp-123" not in call_args[1] #gitleaks:allow

class TestSanitizeLoggerIntegration:
    """Integration tests for SanitizeLogger using real scrubadub package."""

    @pytest.fixture
    def sanitize_logger(self):
        """Create a SanitizeLogger instance for integration testing."""
        with patch('src.apps.orchestrator.libs.common.sanitize_logger.logger') as mock_logger:
            mock_logger.return_value = type('MockLogger', (), {
                'set_trace_id': lambda self, x: None,
                'set_default_trace_id': lambda self, x: None,
                'reset_trace_id': lambda self: None,
                'info': lambda self, *args, **kwargs: None,
                'error': lambda self, *args, **kwargs: None,
                'debug': lambda self, *args, **kwargs: None,
                'warning': lambda self, *args, **kwargs: None,
                'critical': lambda self, *args, **kwargs: None,
                'exception': lambda self, *args, **kwargs: None,
                'setLevel': lambda self, level: None,
            })()
            return SanitizeLogger("test_qname", "/tmp/test_logs")



    def test_sanitize_email_addresses(self, sanitize_logger):
        """Test sanitization of email addresses."""
        args = (
            "Contact <EMAIL> for support", #gitleaks:allow
            "User email: <EMAIL>", #gitleaks:allow
            "<NAME_EMAIL>" #gitleaks:allow
        )
        kwargs = {
            "primary_email": "<EMAIL>", #gitleaks:allow
            "backup_email": "<EMAIL>" #gitleaks:allow
        }

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        print("Email Test:")
        print(f"Original args: {args}")
        print(f"Sanitized args: {sanitized_args}")
        print(f"Original kwargs: {kwargs}")
        print(f"Sanitized kwargs: {sanitized_kwargs}")
        print("-" * 50)

    def test_sanitize_otp(self, sanitize_logger):
        """Test sanitization of passwords and secrets."""
        args = (
            "`KeyName`: `orchestrator`, `UserData`: `instance_name=GPPT_620395_eu-west-1_sasequickpov-**********,saas_gpcs_api_endpoint=prod6.gpcloudservice.com/api, cert_fetch_otp=1b135a75-8bdd-4007-852a-ec6cb31d7b33,cloud_provider=aws,custid=**********,lambdaprefix=sasequickpov-**********,custid_int=7967,gp_domain=gpcloudservice.com,route53acct=a631680999526,bucket-name=pan-content-us-west-2-prod6,panrepo_bucket_name=panrepo-us-west-2-prod6,sase_fabric=False,super_custid=**********,commit_validate=False,is_using_sp_interconnect=False,ciam_service_endpoint=ciam-prod.sasedp.prismaaccess.com,region=eu-west-1`",
        )
        kwargs = {
        }

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        print("OTP Test:")
        print(f"Original args: {args}")
        print(f"Sanitized args: {sanitized_args}")
        print(f"Original kwargs: {kwargs}")
        print(f"Sanitized kwargs: {sanitized_kwargs}")
        print("-" * 50)


    def test_sanitize_passwords_and_secrets(self, sanitize_logger):
        """Test sanitization of passwords and secrets."""
        args = (
            "password=secret123!", #gitleaks:allow
            "Login with username:admin password:mysecret", #gitleaks:allow
            "Database connection: user=admin;pwd=StrongPassword123" #gitleaks:allow
        )
        kwargs = {
            "user_password": "P@ssw0rd123!", #gitleaks:allow
            "db_secret": "very_secret_key_2023", #gitleaks:allow
            "encryption_key": "AES256_KEY_abcdef1234567890" #gitleaks:allow
        }

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        print("Passwords/Secrets Test:")
        print(f"Original args: {args}")
        print(f"Sanitized args: {sanitized_args}")
        print(f"Original kwargs: {kwargs}")
        print(f"Sanitized kwargs: {sanitized_kwargs}")
        print("-" * 50)

    def test_sanitize_urls_with_credentials(self, sanitize_logger):
        """Test sanitization of URLs containing credentials."""
        args = (
            "Database URL: postgresql://user:password@localhost:5432/mydb", #gitleaks:allow
            "Redis: redis://admin:<EMAIL>:6379", #gitleaks:allow
            "HTTP basic auth: https://user123:<EMAIL>/data" #gitleaks:allow
        )
        kwargs = {
            "mongo_url": "mongodb://dbuser:<EMAIL>/database", #gitleaks:allow
            "ftp_url": "ftp://ftpuser:<EMAIL>/uploads" #gitleaks:allow
        }

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        print("URLs with Credentials Test:")
        print(f"Original args: {args}")
        print(f"Sanitized args: {sanitized_args}")
        print(f"Original kwargs: {kwargs}")
        print(f"Sanitized kwargs: {sanitized_kwargs}")
        print("-" * 50)

    def test_sanitize_personal_information(self, sanitize_logger):
        """Test sanitization of personal information."""
        args = (
            "Patient name: John Smith, DOB: 01/15/1985",
            "Address: 123 Main St, Anytown, CA 90210",
            "Driver license: ********"
        )
        kwargs = {
            "full_name": "Alice Johnson",
            "address": "456 Oak Avenue, Springfield, IL 62701",
            "license_plate": "ABC-1234"
        }

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        print("Personal Information Test:")
        print(f"Original args: {args}")
        print(f"Sanitized args: {sanitized_args}")
        print(f"Original kwargs: {kwargs}")
        print(f"Sanitized kwargs: {sanitized_kwargs}")
        print("-" * 50)

    def test_sanitize_mixed_sensitive_data(self, sanitize_logger):
        """Test sanitization of mixed sensitive data types."""
        args = (
            "User <EMAIL> has CC **************** and phone (*************",
            "Config: api_key=sk_test_123, db_url=***************************** ssn=***********"
        )
        kwargs = {
            "user_profile": {
                "email": "<EMAIL>",
                "phone": "************",
                "card": "****************"
            },
            "connection_string": "Server=localhost;Database=app;User=sa;Password=**********;",
            "log_message": "Failed login <NAME_EMAIL> from IP *************"
        }

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        print("Mixed Sensitive Data Test:")
        print(f"Original args: {args}")
        print(f"Sanitized args: {sanitized_args}")
        print(f"Original kwargs: {kwargs}")
        print(f"Sanitized kwargs: {sanitized_kwargs}")
        print("-" * 50)

    def test_sanitize_non_sensitive_data(self, sanitize_logger):
        """Test sanitization of non-sensitive data (should remain unchanged)."""
        args = (
            "Processing order #12345",
            "Server status: OK",
            "Total records: 1000"
        )
        kwargs = {
            "status": "completed",
            "count": 42,
            "message": "Operation successful"
        }

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        print("Non-Sensitive Data Test:")
        print(f"Original args: {args}")
        print(f"Sanitized args: {sanitized_args}")
        print(f"Original kwargs: {kwargs}")
        print(f"Sanitized kwargs: {sanitized_kwargs}")
        print("-" * 50)

class TestSanitize:

    @pytest.fixture
    def sanitize_logger(self):
        """Create a SanitizeLogger instance for testing."""
        with patch('src.apps.orchestrator.libs.common.sanitize_logger.logger') as mock_logger:
            mock_logger.return_value = Mock()
            logger_instance = SanitizeLogger("test_qname", "/tmp/test_logs")
            return logger_instance


    def test_sanitize_with_positional_args(self, sanitize_logger):
        """Test sanitizing positional arguments."""
        # Mock the scrubber.clean method
        sanitize_logger.scrubber.clean = Mock(side_effect=lambda x: f"SANITIZED_{x}")

        args = ("password123", "secret_key", 42)
        kwargs = {}

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        assert len(sanitized_args) == 3
        assert sanitized_args[0] == "SANITIZED_password123"
        assert sanitized_args[1] == "SANITIZED_secret_key"
        assert sanitized_args[2] == "SANITIZED_42"
        assert sanitized_kwargs == {}

    def test_sanitize_with_keyword_args(self, sanitize_logger):
        """Test sanitizing keyword arguments."""
        sanitize_logger.scrubber.clean = Mock(side_effect=lambda x: f"SANITIZED_{x}")

        args = ()
        kwargs = {
            "username": "admin",
            "password": "secret123",
            "port": 8080,
            "enabled": True
        }

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        assert sanitized_args == ()
        assert len(sanitized_kwargs) == 4
        assert sanitized_kwargs["username"] == "SANITIZED_admin"
        assert sanitized_kwargs["password"] == "SANITIZED_secret123"
        assert sanitized_kwargs["port"] == "SANITIZED_8080"
        assert sanitized_kwargs["enabled"] == "SANITIZED_True"

    def test_sanitize_with_mixed_args(self, sanitize_logger):
        """Test sanitizing both positional and keyword arguments."""
        sanitize_logger.scrubber.clean = Mock(side_effect=lambda x: f"CLEAN_{x}")

        args = ("user123", "temp_password")
        kwargs = {
            "api_key": "abc123def456",
            "timeout": 30
        }

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        assert len(sanitized_args) == 2
        assert sanitized_args[0] == "CLEAN_user123"
        assert sanitized_args[1] == "CLEAN_temp_password"

        assert len(sanitized_kwargs) == 2
        assert sanitized_kwargs["api_key"] == "CLEAN_abc123def456"
        assert sanitized_kwargs["timeout"] == "CLEAN_30"

    def test_sanitize_with_empty_args(self, sanitize_logger):
        """Test sanitizing with no arguments."""
        sanitize_logger.scrubber.clean = Mock()

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize()

        assert sanitized_args == ()
        assert sanitized_kwargs == {}
        sanitize_logger.scrubber.clean.assert_not_called()

    def test_sanitize_with_none_values(self, sanitize_logger):
        """Test sanitizing with None values."""
        sanitize_logger.scrubber.clean = Mock(side_effect=lambda x: f"SANITIZED_{x}")

        args = (None,)
        kwargs = {"value": None}

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        assert sanitized_args[0] == "SANITIZED_None"
        assert sanitized_kwargs["value"] == "SANITIZED_None"

    def test_sanitize_with_complex_objects(self, sanitize_logger):
        """Test sanitizing with complex objects (lists, dicts)."""
        sanitize_logger.scrubber.clean = Mock(side_effect=lambda x: f"SANITIZED_{x}")

        test_list = ["item1", "item2"]
        test_dict = {"key": "value"}

        args = (test_list, test_dict)
        kwargs = {}

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        assert sanitized_args[0] == "SANITIZED_['item1', 'item2']"
        assert sanitized_args[1] == "SANITIZED_{'key': 'value'}"

    def test_sanitize_calls_scrubber_clean(self, sanitize_logger):
        """Test that sanitize method calls scrubber.clean for each argument."""
        sanitize_logger.scrubber.clean = Mock(return_value="CLEANED")

        args = ("arg1", "arg2")
        kwargs = {"key1": "value1", "key2": "value2"}

        sanitize_logger.sanitize(*args, **kwargs)

        # Verify scrubber.clean was called for each argument
        assert sanitize_logger.scrubber.clean.call_count == 4

        # Verify the calls were made with string versions of the arguments
        call_args = [call[0][0] for call in sanitize_logger.scrubber.clean.call_args_list]
        assert "arg1" in call_args
        assert "arg2" in call_args
        assert "value1" in call_args
        assert "value2" in call_args

    def test_sanitize_return_types(self, sanitize_logger):
        """Test that sanitize returns correct types."""
        sanitize_logger.scrubber.clean = Mock(return_value="CLEANED")

        args = ("test",)
        kwargs = {"key": "value"}

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        assert isinstance(sanitized_args, tuple)
        assert isinstance(sanitized_kwargs, dict)

    @patch('scrubadub.Scrubber')
    def test_sanitize_with_real_scrubber(self, mock_scrubber_class, sanitize_logger):
        """Test sanitize function with a more realistic scrubber scenario."""
        # Create a mock scrubber instance
        mock_scrubber = Mock()
        mock_scrubber.clean.side_effect = lambda x: x.replace("password", "{{PASSWORD}}")
        mock_scrubber_class.return_value = mock_scrubber

        # Replace the scrubber in our logger instance
        sanitize_logger.scrubber = mock_scrubber

        args = ("Login with password: secret123",)
        kwargs = {"auth": "username:password"}

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        assert sanitized_args[0] == "Login with {{PASSWORD}}: secret123"
        assert sanitized_kwargs["auth"] == "username:{{PASSWORD}}"

    def test_sanitize_preserves_argument_order(self, sanitize_logger):
        """Test that argument order is preserved after sanitization."""
        sanitize_logger.scrubber.clean = Mock(side_effect=lambda x: f"CLEAN_{x}")

        args = ("first", "second", "third")
        kwargs = {}

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        assert sanitized_args[0] == "CLEAN_first"
        assert sanitized_args[1] == "CLEAN_second"
        assert sanitized_args[2] == "CLEAN_third"

    def test_sanitize_with_special_characters(self, sanitize_logger):
        """Test sanitizing strings with special characters."""
        sanitize_logger.scrubber.clean = Mock(side_effect=lambda x: x.upper())

        args = ("<EMAIL>", "spëcial-chars_123")
        kwargs = {"url": "https://example.com/path?param=value"}

        sanitized_args, sanitized_kwargs = sanitize_logger.sanitize(*args, **kwargs)

        assert sanitized_args[0] == "<EMAIL>"
        assert sanitized_args[1] == "SPËCIAL-CHARS_123"
        assert sanitized_kwargs["url"] == "HTTPS://EXAMPLE.COM/PATH?PARAM=VALUE"