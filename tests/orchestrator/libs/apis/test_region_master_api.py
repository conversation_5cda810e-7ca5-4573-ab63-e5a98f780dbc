import unittest
from unittest.mock import patch, MagicMock

from mock_imports_orchestrator import *

from libs.apis.region_master_api import is_ipv6_disabled_for_location

class Logger():
    def __init__(self):
        pass

    def info(self, msg):
        print(msg)

    def error(self, msg):
        print(msg)

    def warn(self, msg):
        print(msg)

class TestIsIpv6Disabled(unittest.TestCase):
    @patch('libs.apis.region_master_api.is_ipv6_disabled_for_location_impl')
    @patch('libs.apis.region_master_api.RegionMasterModel')
    def test_is_ipv6_disabled(self, mock_rmm, mock_is_supported):
        # Arrange
        dbh = MagicMock()
        dbh.logger = Logger()
        region_idx = 1
        mock_rmm.return_value.valid_tuple = True
        mock_is_supported.return_value = False

        # Act
        result = is_ipv6_disabled_for_location(dbh, region_idx)

        # Assert
        self.assertFalse(result)
        mock_rmm.assert_called_once_with(dbh, edge_location_region_id=region_idx)

    def test_is_ipv6_disabled_exception(self):
        # Arrange
        dbh = None
        region_idx = None

        # Act
        result = is_ipv6_disabled_for_location(dbh, region_idx)

        # Assert
        self.assertTrue(result)