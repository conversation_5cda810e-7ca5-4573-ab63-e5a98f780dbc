from unittest.mock import patch, MagicMock
import logging
import copy
from unittest import TestCase
from libs.cfg import *
from libs.common.shared.sys_utils import *
import mock_imports_utils
from libs.cloud_providers.common.instance_param_mgmt import get_salt_profile_for_instance_entry
from libs.common.shared.sys_utils import (
    PROVIDER_OPENSTACK,               	# 4
    PROVIDER_OPENSTACK_DB_ENUM_VALUE 	# 'openstack'
)

'''
PYTHONPATH=~/saas-infra/src/apps/orchestrator/
pytest -s test_osp_instance_param_mgmt.py
'''

logger = logging.getLogger()

CUSTOMER_ID = 100
TENANT_ID = 567890

GW_INSTANCE_ID = 4900
GW_TEST_INSTANCE_ENTRY = {
        "instance_id": GW_INSTANCE_ID,
        "sase_fabric": 0,
        "commit_validate": 0,
        "region": "us-west-1",
        "edge_region_idx": "1000",
        "gpcs_instance_size": "gpcs-xlarge",
        "cloud_machine_type": "",
        "instance_role": "MU",
        "version": "panos-saas-image-1.0",
        "instance_name": "openstack-instance",
        "saas_gpcs_api_endpoint": "https://api.example.com",
        "cert_fetch_otp": "abcdef",
        "cloud_provider": PROVIDER_OPENSTACK_DB_ENUM_VALUE,
        "super_custid": TENANT_ID,
        "cust_id": CUSTOMER_ID,
        "acct_id": TENANT_ID,
        "serial_no": "Dummy_serial_no",
        "instance_alias": "fw-4900-us-west-1000-renault3402-567890",
        "custnode_id": "54321",
        "node_type_id":  49,
        "parent_id": "98765",
        "capacity_type": "PA-CAP530",
        "avail_domain": "example.com",
        "sessionAffinity": "random",
        "gp_gw_domain": "gw.example.com",
        "commit_validate": True,
        "is_nlb_supported": True,
        "is_ngpa_protocol_enabled": True,
        "is_central_cache_supported": False,
        "is_instance_behind_nlb": False,
        "central_cache_service_endpoint": "https://cache.example.com",
        "central_cache_service_backup_endpoint": "https://backup-cache.example.com",
        "ciam_service_endpoint": "https://ciam.example.com",
        "is_using_sp_interconnect": False
}

RN_INSTANCE_ID1 = 4801
RN_INSTANCE_ID2 = 4802
RN_TEST_INSTANCE_ENTRY = {
        "instance_id": RN_INSTANCE_ID1,
        "sase_fabric": 0,
        "commit_validate": 0,
        "region": "us-west-1",
        "edge_region_idx": "1000",
        "gpcs_instance_size": "gpcs-xlarge",
        "cloud_machine_type": "",
        "instance_role": "RN",
        "version": "panos-saas-image-1.0",
        "instance_name": "openstack-instance",
        "saas_gpcs_api_endpoint": "https://api.example.com",
        "cert_fetch_otp": "abcdef",
        "cloud_provider": PROVIDER_OPENSTACK_DB_ENUM_VALUE,
        "super_custid": TENANT_ID,
        "cust_id": CUSTOMER_ID,
        "acct_id": TENANT_ID,
        "serial_no": "Dummy_serial_no",
        "instance_alias": "fw-4801-us-west-1000-renault3402-567890",
        "custnode_id": "54321",
        "node_type_id":  48,
        "parent_id": "98765",
        "capacity_type": "PA-CAP530",
        "avail_domain": "example.com",
        "sessionAffinity": "random",
        "gp_gw_domain": "gw.example.com",
        "commit_validate": True,
        "is_nlb_supported": True,
        "is_ngpa_protocol_enabled": True,
        "is_central_cache_supported": False,
        "is_instance_behind_nlb": False,
        "central_cache_service_endpoint": "https://cache.example.com",
        "central_cache_service_backup_endpoint": "https://backup-cache.example.com",
        "ciam_service_endpoint": "https://ciam.example.com",
        "is_using_sp_interconnect": False
}

SC_INSTANCE_ID1 = 5101
SC_INSTANCE_ID2 = 5102
SC_TEST_INSTANCE_ENTRY = {
        "instance_id": SC_INSTANCE_ID1,
        "sase_fabric": 0,
        "commit_validate": 0,
        "region": "us-west-1",
        "edge_region_idx": "1000",
        "gpcs_instance_size": "gpcs-xlarge",
        "cloud_machine_type": "",
        "instance_role": "RN",
        "version": "panos-saas-image-1.0",
        "instance_name": "openstack-instance",
        "saas_gpcs_api_endpoint": "https://api.example.com",
        "cert_fetch_otp": "abcdef",
        "cloud_provider": PROVIDER_OPENSTACK_DB_ENUM_VALUE,
        "super_custid": TENANT_ID,
        "cust_id": CUSTOMER_ID,
        "acct_id": TENANT_ID,
        "serial_no": "Dummy_serial_no",
        "instance_alias": "fw-5101-us-west-1000-renault3402-567890",
        "custnode_id": "54321",
        "node_type_id":  48,
        "parent_id": "98765",
        "capacity_type": "PA-CAP530",
        "avail_domain": "example.com",
        "sessionAffinity": "random",
        "gp_gw_domain": "gw.example.com",
        "commit_validate": True,
        "is_nlb_supported": True,
        "is_ngpa_protocol_enabled": True,
        "is_central_cache_supported": False,
        "is_instance_behind_nlb": False,
        "central_cache_service_endpoint": "https://cache.example.com",
        "central_cache_service_backup_endpoint": "https://backup-cache.example.com",
        "ciam_service_endpoint": "https://ciam.example.com",
        "is_using_sp_interconnect": False
}

class Mock_cursor():
    def __init__(self, testSuiteIdx):
        self.sql = None
        self.params = None
        self.testSuiteIdx = testSuiteIdx

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params != None:
            return None
        raise Exception("No matching dict found to lookup for this test suite.")

    def fetchone(self):
        return {"name" : "Openstack"}


class Mock_DBHandler():
    def __init__(self, logger):
        self.logger = logger

    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return


class Mock_CustomerModel():
    def __init__(self, custid, dbh):
        self.dbh = dbh
        self.fields = ["super_acct_id", "acct_id", "id", "gpname", "probe_enable", "name",
                       "psk_secret", "is_readonly", "url", "gpfqdn", "eproxy_image_version", "project_id",
                       "asymmetric_ha_mode", "fwdrulesall",
                       "is_next_gen_pa", "svc_acct", "binhp_eproxy_image_version", "uda_nhproxy_image_version"]
        self.values = [TENANT_ID, TENANT_ID, CUSTOMER_ID, "test", 0, "test_customer", "dummy", 0, "dummyURL", "dummyFQDN",
                       None, "dummy", "dummy-proj", 1, "",
                       0, 0, None, None]

    def get_param(self, field):
        print(self.fields)
        myidx = self.fields.index(field)
        return self.values[myidx]

    def set_param(self, field, value):
        if field in self.fields:
            myidx = self.fields.index(field)
            self.values[myidx] = value
        else:
            raise Exception(f"field {field} does not exist")


class Mock_InstanceModel:
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh

        if iid == RN_INSTANCE_ID1 or RN_INSTANCE_ID2:
            node_type = 48
        elif iid == GW_INSTANCE_ID:
            node_type = 49
        elif iid == SC_INSTANCE_ID1 or SC_INSTANCE_ID2:
            node_type = 51
        else:
            node_type = 0

        name = "FW_DUMMY_VM-%s-TYPE-%d" % (iid, node_type)

        self.fields = ["id", "name", "custid", "compute_region_name", "compute_region_idx", "egress_ip_list", "slot_nr",
                       "vmid", "mgt_ip", "pvt_ip", "public_ip", "node_type",
                       "state", "gw_capabilities", "sase_fabric_ip", "native_machine_type"]
        self.values = [iid, name, CUSTOMER_ID, "us-west-1000", 1000, "", 0,
                       "", "", "", "", node_type, 0, 'None', 'None', '']

    def get_param(self, field):
        print(self.fields)
        myidx = self.fields.index(field)

        val = self.values[myidx]
        if val == 'None':
            return None

        return val

    def set_param(self, field, value):
        if field in self.fields:
            myidx = self.fields.index(field)
            self.values[myidx] = value
        else:
            raise Exception(f"field {field} does not exist")

    def requested_native_machine_type(self):
        return 0

    def __str__(self):
        return "dummy"


class TestOspInstanceParamMgmt(TestCase):

    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.CustomerModel', side_effect=Mock_CustomerModel)
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.InstanceModel', side_effect=Mock_InstanceModel)
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.gpcs_get_compute_region_name_from_edge_region_name', return_value="us-west-1")
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.get_cloud_native_location_name_from_region_id', return_value="us-west-1")
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.get_cloud_instance_types_for_region_idx', return_value='{"gpcs-xlarge": "panos-4cpu-16core"}')
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.osp_instance_info.osp_get_image_id', return_value="image-id-abcdef")
    def test_get_gcp_salt_profile_for_instance_entry(self, patch_CustomerModel, patch_InstanceModel, patch_compute_region_name, patch_region_name, patch_instance_types, patch_image_id):
        instance_entry_dict = copy.deepcopy(GW_TEST_INSTANCE_ENTRY)

        dbh = Mock_DBHandler(logger)
        cloud_provider = PROVIDER_OPENSTACK
        is_passive = False
        is_clean_pipe = False
        is_premium_zone = False
        node_type = 49 
        
        ret = get_salt_profile_for_instance_entry(dbh, instance_entry_dict, cloud_provider, is_passive, is_clean_pipe, is_premium_zone, node_type)
        print(f"openstack instance salt-profile: {ret}")
 
        assert ret['ImageName'] is not None
        assert ret['ImageId'] is not None
        assert ret['PrimaryCapacityType'] is not None
        assert ret['MgmtNetwork'] is not None
        assert ret['DPNetwork'] is not None

    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.CustomerModel', side_effect=Mock_CustomerModel)
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.InstanceModel', side_effect=Mock_InstanceModel)
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.gpcs_get_compute_region_name_from_edge_region_name', return_value="us-west-1")
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.get_cloud_native_location_name_from_region_id', return_value="us-west-1")
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.get_cloud_instance_types_for_region_idx', return_value='{"gpcs-xlarge": "panos-4cpu-16core"}')
    def test_get_gcp_salt_profile_for_instance_entry_neg(self, patch_CustomerModel, patch_InstanceModel, patch_compute_region_name, patch_region_name, patch_instance_types):
        instance_entry_dict = copy.deepcopy(GW_TEST_INSTANCE_ENTRY)

        dbh = Mock_DBHandler(logger)
        cloud_provider = PROVIDER_OPENSTACK
        is_passive = False
        is_clean_pipe = False
        is_premium_zone = False
        node_type = 49 
        
        ret = get_salt_profile_for_instance_entry(dbh, instance_entry_dict, cloud_provider, is_passive, is_clean_pipe, is_premium_zone, node_type)
        print(f"openstack instance salt-profile: {ret}")
 
        assert ret['ImageName'] is not None
        assert ret['PrimaryCapacityType'] is not None
        assert ret['MgmtNetwork'] is not None
        assert ret['DPNetwork'] is not None
        assert ret['ImageId'] is None

    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.CustomerModel', side_effect=Mock_CustomerModel)
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.InstanceModel', side_effect=Mock_InstanceModel)
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.gpcs_get_compute_region_name_from_edge_region_name', return_value="us-west-1")
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.get_cloud_native_location_name_from_region_id', return_value="us-west-1")
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.get_cloud_instance_types_for_region_idx', return_value='{"gpcs-xlarge": "panos-4cpu-16core"}')
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.osp_instance_info.osp_get_image_id', return_value="image-id-abcdef")
    def test_get_osp_salt_profile_for_rn_active_instance_entry(self, patch_CustomerModel, patch_InstanceModel, patch_compute_region_name, patch_region_name, patch_instance_types, patch_image_id):
        instance_entry_dict = copy.deepcopy(RN_TEST_INSTANCE_ENTRY)

        dbh = Mock_DBHandler(logger)
        cloud_provider = PROVIDER_OPENSTACK
        is_passive = False
        is_clean_pipe = False
        is_premium_zone = False
        node_type = 48

        ret = get_salt_profile_for_instance_entry(dbh, instance_entry_dict, cloud_provider, is_passive, is_clean_pipe, is_premium_zone, node_type)
        print(f"openstack instance salt-profile: {ret}")

        assert ret['ImageName']
        assert ret['ImageId']
        assert ret['PrimaryCapacityType']
        assert ret['static_ip']
        assert ret['MgmtNetwork']
        assert ret['DPNetwork']
        assert ret['HANetwork']

    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.CustomerModel', side_effect=Mock_CustomerModel)
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.InstanceModel', side_effect=Mock_InstanceModel)
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.gpcs_get_compute_region_name_from_edge_region_name', return_value="us-west-1")
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.get_cloud_native_location_name_from_region_id', return_value="us-west-1")
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.get_cloud_instance_types_for_region_idx', return_value='{"gpcs-xlarge": "panos-4cpu-16core"}')
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.osp_instance_info.osp_get_image_id', return_value="image-id-abcdef")
    def test_get_osp_salt_profile_for_rn_passive_instance_entry(self, patch_CustomerModel, patch_InstanceModel, patch_compute_region_name, patch_region_name, patch_instance_types, patch_image_id):
        instance_entry_dict = copy.deepcopy(RN_TEST_INSTANCE_ENTRY)

        dbh = Mock_DBHandler(logger)
        cloud_provider = PROVIDER_OPENSTACK
        is_passive = True
        is_clean_pipe = False
        is_premium_zone = False
        node_type = 48

        ret = get_salt_profile_for_instance_entry(dbh, instance_entry_dict, cloud_provider, is_passive, is_clean_pipe, is_premium_zone, node_type)
        print(f"openstack instance salt-profile: {ret}")

        assert ret['ImageName']
        assert ret['ImageId']
        assert ret['PrimaryCapacityType']
        assert not ret['static_ip']
        assert ret['MgmtNetwork']
        assert ret['DPNetwork']
        assert ret['HANetwork']

    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.CustomerModel', side_effect=Mock_CustomerModel)
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.InstanceModel', side_effect=Mock_InstanceModel)
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.gpcs_get_compute_region_name_from_edge_region_name', return_value="us-west-1")
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.get_cloud_native_location_name_from_region_id', return_value="us-west-1")
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.get_cloud_instance_types_for_region_idx', return_value='{"gpcs-xlarge": "panos-4cpu-16core"}')
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.osp_instance_info.osp_get_image_id', return_value="image-id-abcdef")
    def test_get_osp_salt_profile_for_sc_active_instance_entry(self, patch_CustomerModel, patch_InstanceModel, patch_compute_region_name, patch_region_name, patch_instance_types, patch_image_id):
        instance_entry_dict = copy.deepcopy(SC_TEST_INSTANCE_ENTRY)

        dbh = Mock_DBHandler(logger)
        cloud_provider = PROVIDER_OPENSTACK
        is_passive = False
        is_clean_pipe = False
        is_premium_zone = False
        node_type = 51

        ret = get_salt_profile_for_instance_entry(dbh, instance_entry_dict, cloud_provider, is_passive, is_clean_pipe, is_premium_zone, node_type)
        print(f"openstack instance salt-profile: {ret}")

        assert ret['ImageName']
        assert ret['ImageId']
        assert ret['PrimaryCapacityType']
        assert ret['static_ip']
        assert ret['MgmtNetwork']
        assert ret['DPNetwork']
        assert ret['HANetwork']

    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.CustomerModel', side_effect=Mock_CustomerModel)
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.InstanceModel', side_effect=Mock_InstanceModel)
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.gpcs_get_compute_region_name_from_edge_region_name', return_value="us-west-1")
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.get_cloud_native_location_name_from_region_id', return_value="us-west-1")
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.get_cloud_instance_types_for_region_idx', return_value='{"gpcs-xlarge": "panos-4cpu-16core"}')
    @patch('libs.cloud_providers.osp.instance_manager.osp_instance_info.osp_instance_info.osp_get_image_id', return_value="image-id-abcdef")
    def test_get_osp_salt_profile_for_sc_passive_instance_entry(self, patch_CustomerModel, patch_InstanceModel, patch_compute_region_name, patch_region_name, patch_instance_types, patch_image_id):
        instance_entry_dict = copy.deepcopy(SC_TEST_INSTANCE_ENTRY)

        dbh = Mock_DBHandler(logger)
        cloud_provider = PROVIDER_OPENSTACK
        is_passive = True
        is_clean_pipe = False
        is_premium_zone = False
        node_type = 51

        ret = get_salt_profile_for_instance_entry(dbh, instance_entry_dict, cloud_provider, is_passive, is_clean_pipe, is_premium_zone, node_type)
        print(f"openstack instance salt-profile: {ret}")

        assert ret['ImageName']
        assert ret['ImageId']
        assert ret['PrimaryCapacityType']
        assert not ret['static_ip']
        assert ret['MgmtNetwork']
        assert ret['DPNetwork']
        assert ret['HANetwork']
