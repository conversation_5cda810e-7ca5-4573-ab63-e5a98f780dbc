import logging
import json
logger = logging.getLogger()

class CustEpaasConfigModel:
    def __init__(self, dbh, custid, compute_region_id, cloud_provider, node_type=153, alt_node_type=-1):
        self.dbh = dbh
        self.custid = custid
        self.compute_region_id = compute_region_id
        self.cloud_provider = cloud_provider
        self.salt_profile = json.dumps({'template_name': "test_template", "envoy_image_version": "test_envoy"})
        self.upgrade_status = None
        self.target_envoy_image_version = "image-1234"
        self.current_envoy_image_version = None
        self.ep_min_count = 2
        self.ep_max_count = 5
        self.ep_autosclale_cpu_target = 0.7
        self.enable_tls_term_on_ep = 1
        self.migrate_ep_status = 'DELETE_STAGE2'
        self.node_type = node_type
        self.alt_node_type = -1
        self.enable_global_nlb = 0
        self.service_timeout = 0
        self.cnat_enabled = "0"
        self.cnat_public_ips = None
        self.cnat_min_count = 1
        self.cnat_max_count = 16
        self.cnat_vm_min_port = 32
        self.cnat_vm_max_port = 32768
        self.cnat_exclsv_enabled = 0
        self.cnat_ntuple_hash = '0'
        self.cnat_mon_enabled = '0'
        self.ipCidrRange = '/32'
        self.is_inst_autoscaled=1
        self.cnat_autoscaled_status = 'TRIGGERED_CNAT_LAMBDA'
        if compute_region_id == 222:
            self.enable_tls_term_on_ep = 0
            self.service_timeout = 300

    def get_entry(self):
        if self.custid == 1212:
            return False
        return True

    def get_param(self, val):
        if val == "salt_profile":
            return self.salt_profile
        elif val == "ep_min_count":
            return self.ep_min_count
        elif val == "ep_max_count":
            return self.ep_max_count
        elif val == "ep_autosclale_cpu_target":
            return self.ep_autosclale_cpu_target
        elif val == "enable_tls_term_on_ep":
            return self.enable_tls_term_on_ep
        elif val == "custid":
            return self.custid
        elif val == "target_envoy_image_version":
            return self.target_envoy_image_version
        elif val == "current_envoy_image_version":
            return self.current_envoy_image_version
        elif val == "enable_global_nlb":
            return self.enable_global_nlb
        elif val == "node_type":
            return self.node_type
        elif val == "service_timeout":
            return self.service_timeout
        elif val == "cnat_enabled":
            return self.cnat_enabled
        elif val == "cnat_public_ips":
            return self.cnat_public_ips
        elif val == "cnat_min_count":
            return self.cnat_min_count
        elif val == "cnat_max_count":
            return self.cnat_max_count
        elif val == "cnat_vm_min_port":
            return self.cnat_vm_min_port
        elif val == "cnat_vm_max_port":
            return self.cnat_vm_max_port
        elif val == "cnat_exclsv_enabled":
            return self.cnat_exclsv_enabled
        elif val == "cnat_ntuple_hash":
            return self.cnat_ntuple_hash
        elif val == "ipCidrRange":
            return self.ipCidrRange
        elif val == "cnat_autoscaled_status":
            return self.cnat_autoscaled_status
        elif val == "is_inst_autoscaled":
            return self.is_inst_autoscaled
        elif val == "cnat_mon_enabled":
            return self.cnat_mon_enabled
        else:
            return None

    def set_param(self, var, value):
        if var == 'migrate_ep_status':
            self.migrate_ep_status = value
        elif var == 'enable_global_nlb':
            self.enable_global_nlb = value
        elif var == "ep_min_count":
            self.ep_min_count = value
        elif var == "current_envoy_image_version":
            self.current_envoy_image_version = value
        elif var == "ep_max_count":
            self.ep_max_count = value
        elif var == "ep_autosclale_cpu_target":
            self.ep_autosclale_cpu_target = value
        elif var == "service_timeout":
            self.service_timeout = value
        elif var == "cnat_enabled":
            self.cnat_enabled = value
        elif var == "cnat_public_ips":
            self.cnat_public_ips = value
        elif var == "cnat_min_count":
            self.cnat_min_count = value
        elif var == "cnat_max_count":
            self.cnat_max_count = value
        elif var == "cnat_vm_min_port":
            self.cnat_vm_min_port = value
        elif var == "cnat_vm_max_port":
            self.cnat_vm_max_port = value
        elif var == "cnat_exclsv_enabled":
            self.cnat_exclsv_enabled = value
        elif var == "cnat_ntuple_hash":
            self.cnat_ntuple_hash = value
        elif var == "ipCidrRange":
            self.ipCidrRange = value
        elif var == "cnat_autoscaled_status":
            self.cnat_autoscaled_status = value
        elif var == "is_inst_autoscaled":
            self.is_inst_autoscaled = value
        elif var == "cnat_mon_enabled":
            self.cnat_mon_enabled = value
        else:
            self.salt_profile = value

    def save_salt_profile(self):
        pass

    def query_entries_in_progress(self):
        return True, [[153, -1, 16, 200, 'gcp', {}, "IN_PROGRESS", "test-log-a", "2023-09-28 16:27:51"]]

    def save(self):
        pass
