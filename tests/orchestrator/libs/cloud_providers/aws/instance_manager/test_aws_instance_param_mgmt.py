import sys
import os
import pytest
import logging
import unittest
from unittest.mock import patch, MagicMock

import mock_imports_aws_instance_param_mgmt,mock_imports_eproxy_managed_vms
from libs.cfg import *
from libs.cloud_providers.aws.instance_manager import aws_instance_param_mgmt
from libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt import get_aws_salt_profile_for_instance_entry,\
    get_saas_agent_api_endpoint, get_zone_for_vpc, get_aws_user_data_for_salt_profile, \
    eval_use_different_az
from libs.common.shared.sys_utils import *
logger = logging.getLogger()

class Mock_cursor():
    def __init__(self, testSuiteIdx):
        self.sql = None
        self.params = None
        self.testSuiteIdx = testSuiteIdx

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params != None:
            return None
        raise Exception("No matching dict found to lookup for this test suite.")

    def fetchone(self):
        return {"name" : "Tejas"}

class Mock_DBHandler():
    def __init__(self, logger):
        self.logger = logger

    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return

class Logger():
    def __init__(self):
        return
    def info(self, msg):
        print(msg)
    def error(self, msg):
        print(msg)

class TestAwsInstanceParamMgmt:

    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.is_aws_fedramp_env_govcloud', return_value=False)
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_vpc_instance')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_nodeTypeInstance')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_instance_key_name', return_value="aws-key")
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_ami_for_instance_entry')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_intf_subnet_id')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_sec_profile')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_arch_for_cloud_machine_type')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_user_data_for_salt_profile', return_value="userdata")
    def test_get_aws_salt_profile_for_instance_entry(self, mock_userdata, mock_arch, mock_sec, mock_subnet_id, mock_ami_ids, mock_keyname, mock_node_type, mock_vpc_inst, mock_gov_cloud):
        instance_entry_dict = {}
        instance_entry_dict['instance_id'] = 4466
        instance_entry_dict['node_type_id'] = 48
        instance_entry_dict['node_type'] = "REMOTENETWORK"
        instance_entry_dict['slot_nr'] = 1
        instance_entry_dict['iam_role'] = "dummy-role"
        instance_entry_dict['gw_interface_support'] = False
        instance_entry_dict['is_sase_fabric_spn'] = 0
        instance_entry_dict['commit_validate'] = 0
        instance_entry_dict['region'] = "us-west-1"
        instance_entry_dict['gpcs_instance_size'] = "gpcs-4xlarge"
        instance_entry_dict['cloud_machine_type'] = "m5.large"
        instance_entry_dict['instance_role'] = "MP"
        instance_entry_dict['version'] = "1.0"
        instance_entry_dict['instance_name'] = "my-instance"
        instance_entry_dict['saas_gpcs_api_endpoint'] = "https://api.example.com"
        instance_entry_dict['cert_fetch_otp'] = "abcdef"
        instance_entry_dict['cloud_provider'] = "gcp"
        instance_entry_dict['super_custid'] = "12345"
        instance_entry_dict['cust_id'] = 232
        instance_entry_dict['acct_id'] = "105082324"
        instance_entry_dict['serial_no'] = "Dummy_serial_no"
        instance_entry_dict['instance_alias'] = "fw-4466-us-east-2-logis2704-1050823"
        instance_entry_dict['custnode_id'] = "54321"
        instance_entry_dict['parent_id'] = "98765"
        instance_entry_dict['capacity_type'] = "PA-CAP530"
        instance_entry_dict['avail_domain'] = "example.com"
        instance_entry_dict['sessionAffinity'] = "random"
        instance_entry_dict['gp_gw_domain'] = "gw.example.com"
        instance_entry_dict['commit_validate'] = True
        instance_entry_dict['frr-enabled'] = 0
        instance_entry_dict['is_nlb_supported'] = True
        instance_entry_dict['is_ngpa_protocol_enabled'] = True
        instance_entry_dict['is_central_cache_supported'] = False
        instance_entry_dict['is_instance_behind_nlb'] = False
        instance_entry_dict['central_cache_service_endpoint'] = "https://cache.example.com"
        instance_entry_dict['central_cache_service_backup_endpoint'] = "https://backup-cache.example.com"
        instance_entry_dict['ciam_service_endpoint'] = "https://ciam.example.com"
        instance_entry_dict['is_using_sp_interconnect'] = False

        mock_ami_ids.return_value = {"x86-64": "ami-123",
                                    "aarch64": "ami-456"}
        mock_subnet_id.return_value = ["subnet-0fb71970c01256a43"]
        mock_sec.return_value = ["sg-029bb534b14e6aabf"]
        mock_arch.return_value="x86-64"

        ret = get_aws_salt_profile_for_instance_entry(Mock_DBHandler(logger), instance_entry_dict, False, False)
        assert ret["PrimaryImage"] == "ami-123"

        instance_entry_dict['cloud_machine_type'] = "m6g.large"
        mock_arch.return_value = "aarch64"
        ret = get_aws_salt_profile_for_instance_entry(Mock_DBHandler(logger), instance_entry_dict, False, False)
        assert ret["PrimaryImage"] == "ami-456"

    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.is_aws_fedramp_env_govcloud',
           return_value=False)
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_vpc_instance')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_nodeTypeInstance')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_instance_key_name',
           return_value="aws-key")
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_ami_for_instance_entry')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_intf_subnet_id')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_sec_profile')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_arch_for_cloud_machine_type')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_user_data_for_salt_profile',
           return_value="userdata")
    @patch('libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.AWSIPHandler')
    def test_get_aws_salt_profile_for_instance_entry_sp(self, mock_aws_ip_handler, mock_userdata,
                                                     mock_arch, mock_sec, mock_subnet_id, mock_ami_ids, mock_keyname,
                                                     mock_node_type, mock_vpc_inst, mock_gov_cloud):
        instance_entry_dict = {}
        instance_entry_dict['instance_id'] = 4466
        instance_entry_dict['node_type_id'] = 48
        instance_entry_dict['node_type'] = "REMOTENETWORK"
        instance_entry_dict['slot_nr'] = 1
        instance_entry_dict['iam_role'] = "dummy-role"
        instance_entry_dict['gw_interface_support'] = False
        instance_entry_dict['is_sase_fabric_spn'] = 0
        instance_entry_dict['commit_validate'] = 0
        instance_entry_dict['region'] = "us-west-1"
        instance_entry_dict['gpcs_instance_size'] = "gpcs-4xlarge"
        instance_entry_dict['cloud_machine_type'] = "m5.large"
        instance_entry_dict['instance_role'] = "MP"
        instance_entry_dict['version'] = "1.0"
        instance_entry_dict['instance_name'] = "my-instance"
        instance_entry_dict['saas_gpcs_api_endpoint'] = "https://api.example.com"
        instance_entry_dict['cert_fetch_otp'] = "abcdef"
        instance_entry_dict['cloud_provider'] = "gcp"
        instance_entry_dict['super_custid'] = "12345"
        instance_entry_dict['cust_id'] = 232
        instance_entry_dict['acct_id'] = "105082324"
        instance_entry_dict['serial_no'] = "Dummy_serial_no"
        instance_entry_dict['instance_alias'] = "fw-4466-us-east-2-logis2704-1050823"
        instance_entry_dict['custnode_id'] = "54321"
        instance_entry_dict['parent_id'] = "98765"
        instance_entry_dict['capacity_type'] = "PA-CAP530"
        instance_entry_dict['avail_domain'] = "example.com"
        instance_entry_dict['sessionAffinity'] = "random"
        instance_entry_dict['gp_gw_domain'] = "gw.example.com"
        instance_entry_dict['commit_validate'] = True
        instance_entry_dict['frr-enabled'] = 0
        instance_entry_dict['is_nlb_supported'] = True
        instance_entry_dict['is_ngpa_protocol_enabled'] = True
        instance_entry_dict['is_central_cache_supported'] = False
        instance_entry_dict['is_instance_behind_nlb'] = False
        instance_entry_dict['central_cache_service_endpoint'] = "https://cache.example.com"
        instance_entry_dict['central_cache_service_backup_endpoint'] = "https://backup-cache.example.com"
        instance_entry_dict['ciam_service_endpoint'] = "https://ciam.example.com"
        instance_entry_dict['is_using_sp_interconnect'] = True

        mock_aws_ip_handler.return_value = MagicMock()
        aws_ip_mgmt = mock_aws_ip_handler.return_value.allocate_public_ip_for_sp_tenant
        aws_ip_mgmt.return_value = '*******'

        mock_ami_ids.return_value = {"x86-64": "ami-123",
                                     "aarch64": "ami-456"}
        mock_subnet_id.return_value = ["subnet-0fb71970c01256a43"]
        mock_sec.return_value = ["sg-029bb534b14e6aabf"]
        mock_arch.return_value = "x86-64"

        ret = get_aws_salt_profile_for_instance_entry(Mock_DBHandler(logger), instance_entry_dict, False, False)
        assert ret["is_using_sp_interconnect"] == True

    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.is_aws_fedramp_env_govcloud', return_value=False)
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_vpc_instance')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_nodeTypeInstance')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_instance_key_name', return_value="aws-key")
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_ami_for_instance_entry')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_intf_subnet_id')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_sec_profile')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_arch_for_cloud_machine_type')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_user_data_for_salt_profile', return_value="userdata")
    def test_get_aws_salt_profile_for_no_passive_instance_entry(self, mock_userdata, mock_arch, mock_sec, mock_subnet_id, mock_ami_ids, mock_keyname, mock_node_type, mock_vpc_inst, mock_gov_cloud):
        instance_entry_dict = {}
        instance_entry_dict['instance_id'] = 4466
        instance_entry_dict['node_type_id'] = 48
        instance_entry_dict['node_type'] = "REMOTENETWORK"
        instance_entry_dict['slot_nr'] = 0
        instance_entry_dict['iam_role'] = "dummy-role"
        instance_entry_dict['gw_interface_support'] = False
        instance_entry_dict['is_sase_fabric_spn'] = 0
        instance_entry_dict['commit_validate'] = 0
        instance_entry_dict['region'] = "us-west-1"
        instance_entry_dict['gpcs_instance_size'] = "gpcs-4xlarge"
        instance_entry_dict['cloud_machine_type'] = "m5.large"
        instance_entry_dict['instance_role'] = "MP"
        instance_entry_dict['version'] = "1.0"
        instance_entry_dict['instance_name'] = "my-instance"
        instance_entry_dict['saas_gpcs_api_endpoint'] = "https://api.example.com"
        instance_entry_dict['cert_fetch_otp'] = "abcdef"
        instance_entry_dict['cloud_provider'] = "gcp"
        instance_entry_dict['super_custid'] = "12345"
        instance_entry_dict['cust_id'] = 232
        instance_entry_dict['acct_id'] = "105082324"
        instance_entry_dict['serial_no'] = "Dummy_serial_no"
        instance_entry_dict['instance_alias'] = "fw-4466-us-east-2-logis2704-1050823"
        instance_entry_dict['custnode_id'] = "54321"
        instance_entry_dict['parent_id'] = "98765"
        instance_entry_dict['no_passive_instance'] = True
        instance_entry_dict['capacity_type'] = "PA-CAP530"
        instance_entry_dict['avail_domain'] = "example.com"
        instance_entry_dict['sessionAffinity'] = "random"
        instance_entry_dict['gp_gw_domain'] = "gw.example.com"
        instance_entry_dict['commit_validate'] = True
        instance_entry_dict['frr-enabled'] = 0
        instance_entry_dict['is_nlb_supported'] = True
        instance_entry_dict['is_ngpa_protocol_enabled'] = True
        instance_entry_dict['is_central_cache_supported'] = False
        instance_entry_dict['is_instance_behind_nlb'] = False
        instance_entry_dict['central_cache_service_endpoint'] = "https://cache.example.com"
        instance_entry_dict['central_cache_service_backup_endpoint'] = "https://backup-cache.example.com"
        instance_entry_dict['ciam_service_endpoint'] = "https://ciam.example.com"

        mock_ami_ids.return_value = {"x86-64": "ami-123",
                                    "aarch64": "ami-456"}
        mock_subnet_id.return_value = ["subnet-0fb71970c01256a43", "subnet-0fb71970c01256a2a", "subnet-0fb71970c01256a3a"]
        mock_sec.return_value = ["sg-029bb534b14e6aabf", "sg-029bb534b14e6a2ed", "sg-029bb534b14e6a3ae"]
        mock_arch.return_value="x86-64"

        ret = get_aws_salt_profile_for_instance_entry(Mock_DBHandler(logger), instance_entry_dict, False, False)
        assert ret["PrimaryImage"] == "ami-123"

        instance_entry_dict['cloud_machine_type'] = "m6g.large"
        mock_arch.return_value = "aarch64"
        ret = get_aws_salt_profile_for_instance_entry(Mock_DBHandler(logger), instance_entry_dict, False, False)
        print(ret)
        assert "HAActiveSubnet" not in ret

   
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_service_subnet_details')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.is_aws_fedramp_env_govcloud',
           return_value=False)
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_vpc_instance')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_nodeTypeInstance')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_instance_key_name',
           return_value="aws-key")
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_ami_for_instance_entry')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_intf_subnet_id')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_sec_profile')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_arch_for_cloud_machine_type')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_user_data_for_salt_profile',
           return_value="userdata")
    @patch('libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.AWSIPHandler')
    def test_get_aws_salt_profile_for_instance_entry_service_subnet_RN(self, mock_aws_ip_handler, mock_userdata,
                                                     mock_arch, mock_sec, mock_subnet_id, mock_ami_ids, mock_keyname,
                                                     mock_node_type, mock_vpc_inst, mock_gov_cloud, mock_service_subnet_details):
        instance_entry_dict = {}
        instance_entry_dict['instance_id'] = 4466
        instance_entry_dict['node_type_id'] = 48
        instance_entry_dict['node_type'] = "REMOTENETWORK"
        instance_entry_dict['slot_nr'] = 0
        instance_entry_dict['iam_role'] = "dummy-role"
        instance_entry_dict['gw_interface_support'] = False
        instance_entry_dict['is_sase_fabric_spn'] = 0
        instance_entry_dict['commit_validate'] = 0
        instance_entry_dict['region'] = "us-west-1"
        instance_entry_dict['gpcs_instance_size'] = "gpcs-4xlarge"
        instance_entry_dict['cloud_machine_type'] = "m5.large"
        instance_entry_dict['instance_role'] = "MP"
        instance_entry_dict['version'] = "1.0"
        instance_entry_dict['instance_name'] = "my-instance"
        instance_entry_dict['saas_gpcs_api_endpoint'] = "https://api.example.com"
        instance_entry_dict['cert_fetch_otp'] = "abcdef"
        instance_entry_dict['cloud_provider'] = "gcp"
        instance_entry_dict['super_custid'] = "12345"
        instance_entry_dict['cust_id'] = 232
        instance_entry_dict['acct_id'] = "105082324"
        instance_entry_dict['serial_no'] = "Dummy_serial_no"
        instance_entry_dict['instance_alias'] = "fw-4466-us-east-2-logis2704-1050823"
        instance_entry_dict['custnode_id'] = "54321"
        instance_entry_dict['parent_id'] = "98765"
        instance_entry_dict['capacity_type'] = "PA-CAP530"
        instance_entry_dict['avail_domain'] = "example.com"
        instance_entry_dict['sessionAffinity'] = "random"
        instance_entry_dict['gp_gw_domain'] = "gw.example.com"
        instance_entry_dict['commit_validate'] = True
        instance_entry_dict['frr-enabled'] = 0
        instance_entry_dict['is_nlb_supported'] = True
        instance_entry_dict['is_ngpa_protocol_enabled'] = True
        instance_entry_dict['is_central_cache_supported'] = False
        instance_entry_dict['is_instance_behind_nlb'] = False
        instance_entry_dict['central_cache_service_endpoint'] = "https://cache.example.com"
        instance_entry_dict['central_cache_service_backup_endpoint'] = "https://backup-cache.example.com"
        instance_entry_dict['ciam_service_endpoint'] = "https://ciam.example.com"
        instance_entry_dict['is_service_nic_supported'] = True

        mock_aws_ip_handler.return_value = MagicMock()
        aws_ip_mgmt = mock_aws_ip_handler.return_value.allocate_public_ip_for_sp_tenant
        aws_ip_mgmt.return_value = '*******'

        mock_ami_ids.return_value = {"x86-64": "ami-123",
                                     "aarch64": "ami-456"}
        mock_subnet_id.return_value = ["subnet-0fb71970c01256a43", "subnet-0fb71970c01256a43"]
        mock_sec.return_value = ["sg-029bb534b14e6aabf", "sg-029bb534b14e6aabf"]
        mock_arch.return_value = "x86-64"
        mock_service_subnet_details.return_value = {True, ("subnet-0fb71970c01256a44","sg-029bb534b14e6aabe")}
        ret = get_aws_salt_profile_for_instance_entry(Mock_DBHandler(logger), instance_entry_dict, False, False)
        print(f"{ret}")
        assert ret["is_service_nic_supported"] == True
        assert ret["ServiceActiveSubnet"] == "subnet-0fb71970c01256a44"
        assert ret['Eth3SecGrp'] == "sg-029bb534b14e6aabe"
        
        
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_service_subnet_details')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.is_aws_fedramp_env_govcloud',
           return_value=False)
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_vpc_instance')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_nodeTypeInstance')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_instance_key_name',
           return_value="aws-key")
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_ami_for_instance_entry')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_intf_subnet_id')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_sec_profile')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_arch_for_cloud_machine_type')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_user_data_for_salt_profile',
           return_value="userdata")
    @patch('libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.AWSIPHandler')
    def test_get_aws_salt_profile_for_instance_entry_service_subnet_GPGW(self, mock_aws_ip_handler, mock_userdata,
                                                     mock_arch, mock_sec, mock_subnet_id, mock_ami_ids, mock_keyname,
                                                     mock_node_type, mock_vpc_inst, mock_gov_cloud, mock_service_subnet_details):
        instance_entry_dict = {}
        instance_entry_dict['instance_id'] = 4466
        instance_entry_dict['node_type_id'] = 49
        instance_entry_dict['node_type'] = "GPGW"
        instance_entry_dict['slot_nr'] = 0
        instance_entry_dict['iam_role'] = "dummy-role"
        instance_entry_dict['gw_interface_support'] = False
        instance_entry_dict['is_sase_fabric_spn'] = 0
        instance_entry_dict['commit_validate'] = 0
        instance_entry_dict['region'] = "us-west-1"
        instance_entry_dict['gpcs_instance_size'] = "gpcs-4xlarge"
        instance_entry_dict['cloud_machine_type'] = "m5.large"
        instance_entry_dict['instance_role'] = "MP"
        instance_entry_dict['version'] = "1.0"
        instance_entry_dict['instance_name'] = "my-instance"
        instance_entry_dict['saas_gpcs_api_endpoint'] = "https://api.example.com"
        instance_entry_dict['cert_fetch_otp'] = "abcdef"
        instance_entry_dict['cloud_provider'] = "gcp"
        instance_entry_dict['super_custid'] = "12345"
        instance_entry_dict['cust_id'] = 232
        instance_entry_dict['acct_id'] = "105082324"
        instance_entry_dict['serial_no'] = "Dummy_serial_no"
        instance_entry_dict['instance_alias'] = "fw-4466-us-east-2-logis2704-1050823"
        instance_entry_dict['custnode_id'] = "54321"
        instance_entry_dict['parent_id'] = "98765"
        instance_entry_dict['capacity_type'] = "PA-CAP530"
        instance_entry_dict['avail_domain'] = "example.com"
        instance_entry_dict['sessionAffinity'] = "random"
        instance_entry_dict['gp_gw_domain'] = "gw.example.com"
        instance_entry_dict['commit_validate'] = True
        instance_entry_dict['frr-enabled'] = 0
        instance_entry_dict['is_nlb_supported'] = True
        instance_entry_dict['is_ngpa_protocol_enabled'] = True
        instance_entry_dict['is_central_cache_supported'] = False
        instance_entry_dict['is_instance_behind_nlb'] = False
        instance_entry_dict['central_cache_service_endpoint'] = "https://cache.example.com"
        instance_entry_dict['central_cache_service_backup_endpoint'] = "https://backup-cache.example.com"
        instance_entry_dict['ciam_service_endpoint'] = "https://ciam.example.com"
        instance_entry_dict['is_service_nic_supported'] = True

        mock_aws_ip_handler.return_value = MagicMock()
        aws_ip_mgmt = mock_aws_ip_handler.return_value.allocate_public_ip_for_sp_tenant
        aws_ip_mgmt.return_value = '*******'

        mock_ami_ids.return_value = {"x86-64": "ami-123",
                                     "aarch64": "ami-456"}
        mock_subnet_id.return_value = ["subnet-0fb71970c01256a43", "subnet-0fb71970c01256a43"]
        mock_sec.return_value = ["sg-029bb534b14e6aabf", "sg-029bb534b14e6aabf"]
        mock_arch.return_value = "x86-64"
        mock_service_subnet_details.return_value = {True, ("subnet-0fb71970c01256a44","sg-029bb534b14e6aabe")}
        ret = get_aws_salt_profile_for_instance_entry(Mock_DBHandler(logger), instance_entry_dict, False, False)
        print(f"{ret}")
        assert ret["is_service_nic_supported"] == True
        assert ret["ServiceSubnet"] == "subnet-0fb71970c01256a44"
        assert ret['Eth3SecGrp'] == "sg-029bb534b14e6aabe"

class TestGetSaasAgentApiEndpoint():
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.cfg', {"saas_gpcs_api_endpoint": "https://example.com"})
    def test_api_endpoint_found(self):
        dbh = MagicMock()
        result = get_saas_agent_api_endpoint(dbh)
        assert result == "https://example.com"

    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.cfg', {})
    def test_api_endpoint_not_found(self):
        dbh = MagicMock()
        result = get_saas_agent_api_endpoint(dbh)
        assert result == ""
        dbh.logger.error.assert_called()


class TestGetZoneForVpc(unittest.TestCase):
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_cloud_native_location_name_from_region_id')
    @patch('boto3.client')
    def test_get_zone_for_vpc_success(self, mock_client,
                                      mock_get_cloud_native_location_name_from_region_id):
        mock_dbh = MagicMock()
        mock_dbh.logger = Logger()
        mock_get_cloud_native_location_name_from_region_id = MagicMock()

        mock_vpc_instance = MagicMock()
        mock_vpc_instance.dp_subnet1 = 'subnet-abc123'

        mock_response = {'Subnets': [{'AvailabilityZone': 'us-east-1a'}]}
        mock_client.return_value.describe_subnets.return_value = mock_response
        aws_instance_param_mgmt.g_subnet_cache = {}
        result = get_zone_for_vpc(mock_dbh, mock_vpc_instance)

        self.assertEqual(result, 'a')
        mock_client.return_value.describe_subnets.assert_called_with(
            Filters=[{'Name': 'subnet-id', 'Values': ['subnet-abc123']}]
        )

    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_cloud_native_location_name_from_region_id')
    @patch('boto3.client')
    def test_get_zone_for_vpc_no_subnets(self, mock_client, mock_get_cloud_native_location_name_from_region_id):
        mock_dbh = MagicMock()
        mock_dbh.logger = Logger()
        mock_get_cloud_native_location_name_from_region_id = MagicMock()

        mock_vpc_instance = MagicMock()
        mock_vpc_instance.dp_subnet1 = 'subnet-abc123'

        mock_response = {}
        mock_client.return_value.describe_subnets.return_value = mock_response
        aws_instance_param_mgmt.g_subnet_cache = {}
        result = get_zone_for_vpc(mock_dbh, mock_vpc_instance)

        self.assertIsNone(result)

    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_cloud_native_location_name_from_region_id')
    @patch('boto3.client')
    def test_get_zone_for_vpc_exception(self, mock_client, mock_get_cloud_native_location_name_from_region_id):
        def function_that_raises_exception():
            raise ValueError("An error occurred")

        mock_dbh = MagicMock()
        mock_dbh.logger = Logger()
        mock_get_cloud_native_location_name_from_region_id = MagicMock()

        mock_vpc_instance = MagicMock()
        mock_vpc_instance.dp_subnet1 = 'subnet-abc123'
        aws_instance_param_mgmt.g_subnet_cache={}
        mock_client.return_value.describe_subnets.side_effect = function_that_raises_exception

        result = get_zone_for_vpc(mock_dbh, mock_vpc_instance)

        self.assertIsNone(result)

    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_cloud_native_location_name_from_region_id')
    @patch('boto3.client')
    def test_get_zone_for_vpc_cache_success(self, mock_client,
                                            mock_get_cloud_native_location_name_from_region_id):
        mock_dbh = MagicMock()
        mock_dbh.logger = Logger()
        mock_get_cloud_native_location_name_from_region_id = MagicMock()
        mock_vpc_instance = MagicMock()
        mock_vpc_instance.dp_subnet1 = 'subnet-abc123'
        aws_instance_param_mgmt.g_subnet_cache["subnet-abc123"] = 'us-east-1a'

        result = get_zone_for_vpc(mock_dbh, mock_vpc_instance)
        self.assertEqual(result, 'a')

    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.is_aws_fedramp_env_govcloud',
           return_value=False)
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_vpc_instance')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_nodeTypeInstance')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_instance_key_name',
           return_value="aws-key")
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_ami_for_instance_entry')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_intf_subnet_id')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_sec_profile')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_arch_for_cloud_machine_type')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_aws_user_data_for_salt_profile',
           return_value="userdata")
    @patch('libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.AWSIPHandler')
    def test_get_aws_salt_profile_for_instance_entry_ep_china(self, mock_aws_ip_handler, mock_userdata,
                                                     mock_arch, mock_sec, mock_subnet_id, mock_ami_ids, mock_keyname,
                                                     mock_node_type, mock_vpc_inst, mock_gov_cloud):
        instance_entry_dict = {}
        instance_entry_dict['instance_id'] = 4466
        instance_entry_dict['node_type_id'] = 153
        instance_entry_dict['node_type'] = "SWGPROXY"
        instance_entry_dict['slot_nr'] = 1
        instance_entry_dict['iam_role'] = "dummy-role"
        instance_entry_dict['gw_interface_support'] = False
        instance_entry_dict['is_sase_fabric_spn'] = 0
        instance_entry_dict['commit_validate'] = 0
        instance_entry_dict['region'] = "us-west-1"
        instance_entry_dict['gpcs_instance_size'] = "gpcs-4xlarge"
        instance_entry_dict['cloud_machine_type'] = "m5.large"
        instance_entry_dict['instance_role'] = "MP"
        instance_entry_dict['version'] = "1.0"
        instance_entry_dict['instance_name'] = "my-instance"
        instance_entry_dict['saas_gpcs_api_endpoint'] = "https://api.example.com"
        instance_entry_dict['cert_fetch_otp'] = "abcdef"
        instance_entry_dict['cloud_provider'] = "gcp"
        instance_entry_dict['super_custid'] = "12345"
        instance_entry_dict['cust_id'] = 232
        instance_entry_dict['acct_id'] = "105082324"
        instance_entry_dict['serial_no'] = "Dummy_serial_no"
        instance_entry_dict['instance_alias'] = "fw-4466-us-east-2-logis2704-1050823"
        instance_entry_dict['custnode_id'] = "54321"
        instance_entry_dict['parent_id'] = "98765"
        instance_entry_dict['capacity_type'] = "PA-CAP530"
        instance_entry_dict['avail_domain'] = "example.com"
        instance_entry_dict['sessionAffinity'] = "random"
        instance_entry_dict['gp_gw_domain'] = "gw.example.com"
        instance_entry_dict['commit_validate'] = True
        instance_entry_dict['frr-enabled'] = 0
        instance_entry_dict['is_nlb_supported'] = True
        instance_entry_dict['is_ngpa_protocol_enabled'] = True
        instance_entry_dict['is_central_cache_supported'] = False
        instance_entry_dict['is_instance_behind_nlb'] = False
        instance_entry_dict['central_cache_service_endpoint'] = "https://cache.example.com"
        instance_entry_dict['central_cache_service_backup_endpoint'] = "https://backup-cache.example.com"
        instance_entry_dict['ciam_service_endpoint'] = "https://ciam.example.com"
        instance_entry_dict['proxy-protocol-enabled'] = '1'
        instance_entry_dict['customer_name'] = 'test'
        instance_entry_dict['route53_acct'] = 'test'
        instance_entry_dict['is_sase_fabric_spn'] = 'test'
        instance_entry_dict['is_using_sp_interconnect'] = 'False'
        instance_entry_dict['region_name'] = 'us-west2'
        instance_entry_dict['bucket_name'] = 'test'
        instance_entry_dict['panrepo_bucket_name'] = 'test'

        expected_return_value="instance_name=my-instance,saas_gpcs_api_endpoint=https://api.example.com, " \
                              "cert_fetch_otp=abcdef,cloud_provider=gcp,custid=105082324,lambdaprefix=test,custid_int=232," \
                              "gp_domain=gw.example.com,route53acct=test,bucket-name=test,panrepo_bucket_name=test," \
                              "sase_fabric=test,super_custid=12345,commit_validate=True,is_using_sp_interconnect=False," \
                              "ciam_service_endpoint=https://ciam.example.com,region=us-west2,proxy-protocol-enabled='1'," \
                              "instance-role=MP,slot-nr=1"

        mock_aws_ip_handler.return_value = MagicMock()
        aws_ip_mgmt = mock_aws_ip_handler.return_value.allocate_public_ip_for_sp_tenant
        aws_ip_mgmt.return_value = '*******'

        mock_ami_ids.return_value = {"x86-64": "ami-123",
                                     "aarch64": "ami-456"}
        mock_subnet_id.return_value = ["subnet-0fb71970c01256a43"]
        mock_sec.return_value = ["sg-029bb534b14e6aabf"]
        mock_arch.return_value = "x86-64"

        ret = get_aws_user_data_for_salt_profile(Mock_DBHandler(logger), instance_entry_dict)
        self.assertEqual(ret, expected_return_value)

    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.VpcModel')
    def test_eval_use_different_az(self, mock_VpcModel):
        mock_dbh = MagicMock()
        mock_dbh.get_cursor.return_value = MagicMock()
        mock_cursor = mock_dbh.get_cursor.return_value
        mock_cursor.fetchall.return_value = [('{"DPActiveSubnet": "subnet-1"}',),]

        mock_vpc_pri = MagicMock()
        mock_vpc_pri.dp_subnet1 = 'subnet-1'
        mock_VpcModel.return_value = mock_vpc_pri

        mock_vpc_sec = MagicMock()
        mock_vpc_sec.dp_subnet1 = 'subnet-2'
        mock_VpcModel.side_effect = [mock_vpc_pri, mock_vpc_sec]

        result = eval_use_different_az(mock_dbh, 1, 'mock-region', 49, 123)
        self.assertTrue(result)

        mock_cursor = mock_dbh.get_cursor.return_value
        mock_cursor.fetchall.return_value = [('{"DPActiveSubnet": "subnet-1"}',), ('{"DPActiveSubnet": "subnet-2"}',)]

        result = eval_use_different_az(mock_dbh, 1, 'mock-region', 49, 123)
        self.assertFalse(result)

