import pytest
import unittest
from unittest.mock import MagicMock, patch
from libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils import get_lb_template
from libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils import aws_instance_mgmt_utils
from libs.common.shared.sys_utils import NODE_TYPE_SWG_PROXY, NODE_TYPE_GP_GATEWAY
import logging


class TestGetLbTemplate(unittest.TestCase):

    def setUp(self):
        self.logger = MagicMock()
        self.template = ""
        self.lb_temp_info = MagicMock()
        self.lb_temp_info.cust_id = "123"
        self.lb_temp_info.protocol = "TCP"
        self.lb_temp_info.fw_port = "443"
        self.lb_temp_info.lb_port = "443"
        self.lb_temp_info.instance_tag_names = ["Instance1", "Instance2"]
        self.lb_temp_info.target_group_vpc_id = "vpc-12345"
        self.lb_temp_info.lb_name = "TestLB"
        self.lb_temp_info.lb_subnets = ["subnet-1", "subnet-2"]
        self.cert_url_agent_proxy = "arn:aws:acm:region:account:certificate/12345"
        self.ulb_temp_info = MagicMock()
        self.ulb_temp_info.lb_port = '6081'
        self.ulb_temp_info.fw_port = '6081'
        self.ulb_temp_info.port_health_check = '80'
        self.ulb_temp_info.protocol = 'UDP'
        self.ulb_temp_info.health_check_protocol = 'TCP'
        self.ulb_temp_info.health_check_interval = 5
        self.ulb_temp_info.health_check_timeout = 5
        self.ulb_temp_info.health_check_count_threshold = 2
        self.ulb_temp_info.health_check_enabled = True
        self.ulb_temp_info.lb_name = "SWGUDPLoadBalancer"
        self.ulb_temp_info.lb_subnets = ["subnet-1"]
        self.ulb_temp_info.instance_tag_names = ["Instance1", "Instance2"]
        self.ulb_temp_info.target_group_vpc_id = "vpc-12345"

    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.is_china_env')
    def test_get_lb_template_swg_proxy(self, mock_is_china_env):
        mock_is_china_env.return_value = False
        result = get_lb_template(self.logger, self.template, self.lb_temp_info, self.cert_url_agent_proxy, NODE_TYPE_SWG_PROXY, self.ulb_temp_info)
        self.assertIsNotNone(result)
        self.assertIn("EC2TargetGroup123", result)
        self.assertIn("LoadBalancer", result)
        self.assertIn("LoadBalancerListener", result)
        self.assertNotIn("SwgUdpLoadBalancerListener", result)
        self.assertNotIn("load_balancing.cross_zone.enabled", result)
        self.assertNotIn("EC2TargetGroupSWGUDP", result)

    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.is_china_env')
    def test_get_lb_template_gp_gateway(self, mock_is_china_env):
        mock_is_china_env.return_value = False
        self.lb_temp_info.target_groups = [{'protocol': 'UDP', 'port': '4501'}, {'protocol': 'TCP', 'port': '443'}]
        self.lb_temp_info.listeners = [{'protocol': 'UDP', 'port': '4501'}, {'protocol': 'TCP', 'port': '443'}]
        result = get_lb_template(self.logger, self.template, self.lb_temp_info, self.cert_url_agent_proxy, NODE_TYPE_GP_GATEWAY, self.ulb_temp_info)
        self.assertIsNotNone(result)
        self.assertIn("EC2TargetGroup123UDP4501", result)
        self.assertIn("EC2TargetGroup123TCP443", result)
        self.assertIn("LoadBalancer", result)
        self.assertIn("LoadBalancerListenerUDP4501", result)
        self.assertIn("LoadBalancerListenerTCP443", result)
        self.assertNotIn("SwgUdpLoadBalancerListener", result)
        self.assertNotIn("load_balancing.cross_zone.enabled", result)
        self.assertNotIn("EC2TargetGroupSWGUDP", result)

    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.is_china_env')
    def test_get_lb_template_china_env(self, mock_is_china_env):
        mock_is_china_env.return_value = True
        self.lb_temp_info.fw_port_kerberos = "88"
        self.lb_temp_info.fw_port_health_check = "8080"
        self.lb_temp_info.lb_port_agent = "10443"
        self.lb_temp_info.agent_protocol = "TLS"
        self.lb_temp_info.lb_port_saml = "443"
        self.lb_temp_info.lb_port_rn_saml = "444"
        self.lb_temp_info.lb_port_kerberos = "88"
        self.lb_temp_info.lb_port_rn_kerberos = "89"
        self.lb_temp_info.lb_port_health_check = "8080"
        result = get_lb_template(self.logger, self.template, self.lb_temp_info, self.cert_url_agent_proxy, NODE_TYPE_SWG_PROXY, self.ulb_temp_info)
        self.assertIsNotNone(result)
        self.assertIn("EC2TargetGroupSAML123", result)
        self.assertIn("EC2TargetGroupKerberos123", result)
        self.assertIn("EC2TargetGroupHealthCheck123", result)
        self.assertIn("LoadBalancerListenerAgent", result)
        self.assertIn("LoadBalancerListenerSAML", result)
        self.assertIn("LoadBalancerListenerRNSAML", result)
        self.assertIn("LoadBalancerListenerKerberos", result)
        self.assertIn("LoadBalancerListenerRNKerberos", result)
        self.assertIn("LoadBalancerListenerHealthCheck", result)
        self.assertIn("SwgUdpLoadBalancerListener", result)
        self.assertIn("load_balancing.cross_zone.enabled", result)
        self.assertIn("EC2TargetGroupSWGUDP", result)

    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.is_china_env')
    def test_get_lb_template_unsupported_node_type(self, mock_is_china_env):
        mock_is_china_env.return_value = False
        result = get_lb_template(self.logger, self.template, self.lb_temp_info, self.cert_url_agent_proxy, 999)
        assert result == None

class MockPaginator:
    def __init__(self):
        pass
    
    def paginate(self, CertificateStatuses):
        if CertificateStatuses != ['ISSUED']:
            return {}
        return [{
            "CertificateSummaryList": [
                {
                    "CertificateArn": "arn:aws:acm:region:account:certificate/123",
                    "DomainName": "*.proxy.panclouddev.com"
                },
                {
                    "CertificateArn": "arn:aws:acm:region:account:certificate/aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee",
                    "DomainName": "www.example.net"
                }
            ]
        }]
    
class TestGetCertArn:
    
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.get_cloud_native_location_name_from_region_id', return_value='ireland')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.boto3.client')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.is_china_env', return_value=False)
    def test_get_cert_arn_commercial(self, mock_china, mock_boto3, mock_get_native_region, caplog):
        
        caplog.set_level(logging.INFO)
        logger = logging.getLogger()
        region = '10'
        tenant_id = "123"
        dbh = MagicMock()
        dbh.logger = logger
        config = {"swg_proxy_base_zone" : "proxy.panclouddev.com"}
        aws_mgmt = aws_instance_mgmt_utils(dbh)
        
        mocked = MagicMock()
        mocked.get_paginator.return_value = MockPaginator()
        mock_boto3.return_value = mocked
        
        expected = "arn:aws:acm:region:account:certificate/123"
        actual = aws_mgmt.get_cert_arn(region, config)
        assert expected == actual
    
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.get_cloud_native_location_name_from_region_id', return_value='ireland')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.aws_instance_mgmt_utils.upload_ssl_cert', return_value="arn:aws:acm:region:account:certificate/123")
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.boto3.client')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.is_china_env', return_value=False)
    def test_get_cert_arn_commercial_upload(self, mock_china, mock_boto3, mock_upload_ssl, mock_get_native_region, caplog):
        
        caplog.set_level(logging.INFO)
        logger = logging.getLogger()
        region = '10'
        tenant_id = "123"
        dbh = MagicMock()
        dbh.logger = logger
        config = {"swg_proxy_base_zone" : "*.nonexitent.panclouddev.com"}
        aws_mgmt = aws_instance_mgmt_utils(dbh)
        
        mocked = MagicMock()
        mocked.get_paginator.return_value = MockPaginator()
        mock_boto3.return_value = mocked
        
        expected = "arn:aws:acm:region:account:certificate/123"
        actual = aws_mgmt.get_cert_arn(region, config)
        assert "certificate arn not found for region" in caplog.text
        assert "returning aws certificate arn arn:aws:acm:region:account:certificate/123" in caplog.text
        assert expected == actual 
    
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.get_cloud_native_location_name_from_region_id', return_value='cn-northwest-1')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.is_china_env', return_value=True)
    def test_get_cert_arn_china_northwest(self, mock_china, mock_get_native_region, caplog):
        
        caplog.set_level(logging.INFO)
        logger = logging.getLogger()
        region = '100'
        tenant_id = "123"
        dbh = MagicMock()
        dbh.logger = logger
        config = {
            "aws_swg_agent_proxy_cert_arn_cn_northwest":"arn:aws:acm:region:account:certificate/aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee"
        }
        aws_mgmt = aws_instance_mgmt_utils(dbh)
        
        expected = "arn:aws:acm:region:account:certificate/aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee"
        actual = aws_mgmt.get_cert_arn(region, config)
        assert "finding certificate arn for region cn-northwest-1" in caplog.text
        assert expected == actual 
    
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.get_cloud_native_location_name_from_region_id', return_value='cn-north')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.is_china_env', return_value=True)
    def test_get_cert_arn_china_north(self, mock_china, mock_get_native_region, caplog):
        
        caplog.set_level(logging.INFO)
        logger = logging.getLogger()
        region = ''
        tenant_id = "123"
        dbh = MagicMock()
        dbh.logger = logger
        config = {
            "aws_swg_agent_proxy_cert_arn_cn_north":"arn:aws:acm:region:account:certificate/aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee"
        }
        aws_mgmt = aws_instance_mgmt_utils(dbh)
        
        expected = "arn:aws:acm:region:account:certificate/aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee"
        actual = aws_mgmt.get_cert_arn(region, config)
        assert expected == actual
    
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.aws_instance_mgmt_utils.separate_cert_and_cert_chain', return_value=(b"leaf", b"chain"))
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.boto3.client')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.aws_instance_mgmt_utils.get_gcp_secret', return_value='test'.encode('UTF-8'))
    def test_upload_ssl_cert(self, mock_secret, mock_boto3, mock_separate, caplog):
        
        caplog.set_level(logging.INFO)
        logger = logging.getLogger()
        dbh = MagicMock()
        dbh.logger = logger
        config = {
            "aws_env":"dev"
        }
        aws_mgmt = aws_instance_mgmt_utils(dbh)
        mock_boto3.import_certificate.return_value = {'CertificateArn':"arn:aws:acm:region:account:certificate/aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee"}
        
        expected = "arn:aws:acm:region:account:certificate/aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee"
        actual = aws_mgmt.upload_ssl_cert(config, mock_boto3)
        assert expected == actual
    
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.secretmanager.SecretManagerServiceClient')
    @patch('libs.cloud_providers.aws.instance_manager.aws_instance_mgmt_utils.gcp_authenticate')
    def test_get_gcp_secret(self, mock_gcp, mock_sm, caplog):
        caplog.set_level(logging.INFO)
        logger = logging.getLogger()
        dbh = MagicMock()
        dbh.logger = logger
        config = {
            "acs_gcp_project_id":"test-project-id",
        }
        aws_mgmt = aws_instance_mgmt_utils(dbh)
        
        secret = MagicMock()
        secret.payload.data = b"test"
        client = MagicMock()
        client.access_secret_version.return_value = secret
        mock_sm.return_value = client
        
        actual = aws_mgmt.get_gcp_secret(config, 'fake-secret')
        expected = 'test'.encode('UTF-8')
        assert actual == expected 
    
        