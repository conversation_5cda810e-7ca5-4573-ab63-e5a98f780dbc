from unittest import TestCase
from unittest.mock import patch, MagicMock
import mock_imports_eproxy_managed_vms
from mock_cust_epaas_config import CustEpaasConfigModel
from libs.cloud_providers.aws.instance_manager.update_eproxy_managed_vms import update_swg_eproxy_instance_details_for_aws, update_swg_eproxy_upgrade_stack_for_aws
import logging
import boto3

logger = logging.getLogger()

class DB:
    def __init__(self):
        self.logger = logger

cust_ep_cfg = CustEpaasConfigModel(DB(), 24, 212, "gcp", node_type=153, alt_node_type=-1)

class TestEnvoydetailsforNewarchitecture(TestCase):

    @patch("boto3.client", return_value = MagicMock())
    @patch("libs.cloud_providers.aws.instance_manager.update_eproxy_managed_vms.update_swg_eproxy_managed_vms_aws",
           return_value=MagicMock())
    @patch("libs.cloud_providers.aws.instance_manager.update_eproxy_managed_vms.CustEpaasConfigModel",
           return_value=MagicMock())
    def test_update_swg_eproxy_instance_details(self, m1, m2, m3):
        # Mock the return values of client.describe_load_balancers
        mock_client_instance = m3.return_value
        mock_client_instance.describe_load_balancers.return_value = {
            'LoadBalancers': [
                {'LoadBalancerArn': 'arn:aws:elasticloadbalancing:region:account-id:loadbalancer/test-name'}]
        }

        # Mock the return values of ec2_client.describe_instances
        mock_ec2_client_instance = m3.return_value
        mock_ec2_client_instance.describe_instances.return_value = {
            'Reservations': [
                {
                    'Instances': [
                        {'InstanceId': 'instance-id'}
                    ]
                }
            ]
        }

        res = update_swg_eproxy_instance_details_for_aws(2, logger, 124, 123456, 'aws',DB(), 153, -1, "1.1.1.1")
        self.assertTrue(res)

    @patch('libs.cloud_providers.aws.instance_manager.update_eproxy_managed_vms.swgproxy_bringup')
    @patch("libs.cloud_providers.aws.instance_manager.update_eproxy_managed_vms.update_swg_eproxy_managed_vms_aws",
           return_value=MagicMock())
    @patch("libs.cloud_providers.aws.instance_manager.update_eproxy_managed_vms.CustEpaasConfigModel",
           return_value=cust_ep_cfg)
    def test_update_swg_eproxy_upgrade_stack_for_aws(self, m1, m2, m3):
        # Mock the return values of client.describe_load_balancers
        mock_swg_proxy_bringup = m3.return_value
        mock_swg_proxy_bringup.add_salt_profile_for_ep_region.return_value = True

        res = update_swg_eproxy_upgrade_stack_for_aws(2, logger, 124, 123456, 'aws',DB(), 153, -1)
        self.assertTrue(res)