class Compute:
    def __init__(self):
        pass

    def instances(self):
        return Instancedetails()

    def regionInstanceGroups(self):
        return InstanceList()

class InstanceList:
    def __init__(self):
        pass

    def listInstances(self, project, region, instanceGroup):
        self.project = project
        self.region = region
        self.instance_group = instanceGroup
        return Request()

    def listInstances_next(self, previous_request, previous_response):
        return None


class Request:
    def execute(self):
        return eproxy_managed_vms_response


class Request1:
    def execute(self):
        return instance_details_a


class Request2:
    def execute(self):
        return instance_details_b


class Instancedetails:
    def get(self, project, zone, instance):
        if instance == "gpcs-proxy-instance-asia-northeast1-*********-4pdd":
            return Request1()
        return Request2()

eproxy_managed_vms_response = {
  "kind": "compute#regionInstanceGroupsListInstances",
  "items": [
    {
      "instance": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/zones/asia-northeast1-b/instances/gpcs-proxy-instance-asia-northeast1-*********-4pdd",
      "status": "RUNNING",
      "namedPorts": [
        {
          "name": "http-8080",
          "port": 8080
        },
        {
          "name": "http-8081",
          "port": 8081
        },
        {
          "name": "http-8888",
          "port": 8888
        }
      ]
    },
    {
      "instance": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/zones/asia-northeast1-a/instances/gpcs-proxy-instance-asia-northeast1-*********-k8mm",
      "status": "RUNNING",
      "namedPorts": [
        {
          "name": "http-8080",
          "port": 8080
        },
        {
          "name": "http-8081",
          "port": 8081
        },
        {
          "name": "http-8888",
          "port": 8888
        }
      ]
    }
  ]
}

instance_details_a = {
  "kind": "compute#instance",
  "id": "4587774083188800978",
  "creationTimestamp": "2023-06-27T17:47:58.270-07:00",
  "name": "gpcs-proxy-instance-asia-northeast1-*********-4pdd",
  "tags": {
    "items": [
      "route-to-ilb-asia-northeast1-*********",
      "swg-proxy"
    ],
    "fingerprint": "IkHgmBaljUA="
  },
  "machineType": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/zones/asia-northeast1-b/machineTypes/e2-standard-4",
  "status": "RUNNING",
  "zone": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/zones/asia-northeast1-b",
  "canIpForward": True,
  "networkInterfaces": [
    {
      "kind": "compute#networkInterface",
      "network": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/global/networks/gpcs-vpc-dp-*********",
      "subnetwork": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/regions/asia-northeast1/subnetworks/subnet-dp-asia-northeast1-*********",
      "networkIP": "************",
      "name": "nic0",
      "fingerprint": "1PwWISf5DRw=",
      "stackType": "IPV4_ONLY"
    },
    {
      "kind": "compute#networkInterface",
      "network": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/global/networks/gpcs-vpc-mgmt-*********",
      "subnetwork": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/regions/asia-northeast1/subnetworks/subnet-mgmt-asia-northeast1-*********",
      "networkIP": "***********",
      "name": "nic1",
      "fingerprint": "MtYKrYy4bC8=",
      "stackType": "IPV4_ONLY"
    }
  ],
  "disks": [
    {
      "kind": "compute#attachedDisk",
      "type": "PERSISTENT",
      "mode": "READ_WRITE",
      "source": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/zones/asia-northeast1-b/disks/gpcs-proxy-instance-asia-northeast1-*********-4pdd",
      "deviceName": "boot",
      "index": 0,
      "boot": True,
      "autoDelete": True,
      "licenses": [
        "https://www.googleapis.com/compute/v1/projects/centos-cloud/global/licenses/centos-stream"
      ],
      "interface": "SCSI",
      "guestOsFeatures": [
        {
          "type": "UEFI_COMPATIBLE"
        },
        {
          "type": "VIRTIO_SCSI_MULTIQUEUE"
        },
        {
          "type": "SEV_CAPABLE"
        },
        {
          "type": "GVNIC"
        }
      ],
      "diskSizeGb": "40",
      "architecture": "X86_64"
    }
  ],
  "metadata": {
    "kind": "compute#metadata",
    "fingerprint": "7j05yXEEbWU=",
    "items": [
      {
        "key": "instance-template",
        "value": "projects/57530550063/global/instanceTemplates/gpcs-proxy-it-agp-asia-northeast1-*********"
      },
      {
        "key": "created-by",
        "value": "projects/57530550063/regions/asia-northeast1/instanceGroupManagers/gpcs-proxy-mig-asia-northeast1-*********-1"
      },
      {
        "key": "ssh-keys",
        "value": "admin:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r admin@SJCMACJ15HHTD8"
      },
      {
        "key": "pacfgds",
        "value": "dev.cfgds.swg.panclouddev.com"
      },
      {
        "key": "nodetype",
        "value": "153"
      },
      {
        "key": "mgmt-interface-swap",
        "value": "enable"
      },
      {
        "key": "dnsproxy",
        "value": "172.16.255.254"
      },
      {
        "key": "pagks",
        "value": "dev.gks.swg.panclouddev.com"
      },
      {
        "key": "cloud_provider",
        "value": "2"
      },
      {
        "key": "svc_acct",
        "value": "<EMAIL>"
      },
      {
        "key": "mtls_ca",
        "value": "it_ca_cert"
      },
      {
        "key": "mtls_certpkey",
        "value": "customer*********"
      },
      {
        "key": "upstream-proxyprotocol",
        "value": "v1"
      },
      {
        "key": "custsuperid",
        "value": "*********"
      },
      {
        "key": "patgs",
        "value": "dev.tgs.swg.panclouddev.com"
      },
      {
        "key": "custid",
        "value": "*********"
      },
      {
        "key": "custid_int",
        "value": "16"
      },
      {
        "key": "pacdls",
        "value": "dev.cdls.swg.panclouddev.com"
      },
      {
        "key": "secret_mgr_project",
        "value": "image-gpcs-nonprod-01"
      }
    ]
  },
  "serviceAccounts": [
    {
      "email": "<EMAIL>",
      "scopes": [
        "https://www.googleapis.com/auth/cloud-platform"
      ]
    }
  ],
  "selfLink": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/zones/asia-northeast1-b/instances/gpcs-proxy-instance-asia-northeast1-*********-4pdd",
  "scheduling": {
    "onHostMaintenance": "MIGRATE",
    "automaticRestart": True,
    "preemptible": False,
    "provisioningModel": "STANDARD"
  },
  "cpuPlatform": "Intel Broadwell",
  "labelFingerprint": "42WmSpB8rSM=",
  "startRestricted": False,
  "deletionProtection": False,
  "shieldedInstanceConfig": {
    "enableSecureBoot": False,
    "enableVtpm": True,
    "enableIntegrityMonitoring": True
  },
  "shieldedInstanceIntegrityPolicy": {
    "updateAutoLearnPolicy": True
  },
  "fingerprint": "dyhkNOFY4Vw=",
  "lastStartTimestamp": "2023-06-27T17:48:15.695-07:00"
}

instance_details_b = {
  "kind": "compute#instance",
  "id": "4349919833718815123",
  "creationTimestamp": "2023-06-20T08:36:29.617-07:00",
  "name": "gpcs-proxy-instance-asia-northeast1-*********-k8mm",
  "tags": {
    "items": [
      "route-to-ilb-asia-northeast1-*********",
      "swg-proxy"
    ],
    "fingerprint": "IkHgmBaljUA="
  },
  "machineType": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/zones/asia-northeast1-a/machineTypes/e2-standard-4",
  "status": "RUNNING",
  "zone": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/zones/asia-northeast1-a",
  "canIpForward": True,
  "networkInterfaces": [
    {
      "kind": "compute#networkInterface",
      "network": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/global/networks/gpcs-vpc-dp-*********",
      "subnetwork": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/regions/asia-northeast1/subnetworks/subnet-dp-asia-northeast1-*********",
      "networkIP": "************",
      "name": "nic0",
      "fingerprint": "lCFq1CMqg00=",
      "stackType": "IPV4_ONLY"
    },
    {
      "kind": "compute#networkInterface",
      "network": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/global/networks/gpcs-vpc-mgmt-*********",
      "subnetwork": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/regions/asia-northeast1/subnetworks/subnet-mgmt-asia-northeast1-*********",
      "networkIP": "*************",
      "name": "nic1",
      "fingerprint": "mYA-amLLUdw=",
      "stackType": "IPV4_ONLY"
    }
  ],
  "disks": [
    {
      "kind": "compute#attachedDisk",
      "type": "PERSISTENT",
      "mode": "READ_WRITE",
      "source": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/zones/asia-northeast1-a/disks/gpcs-proxy-instance-asia-northeast1-*********-k8mm",
      "deviceName": "boot",
      "index": 0,
      "boot": True,
      "autoDelete": True,
      "licenses": [
        "https://www.googleapis.com/compute/v1/projects/centos-cloud/global/licenses/centos-stream"
      ],
      "interface": "SCSI",
      "guestOsFeatures": [
        {
          "type": "UEFI_COMPATIBLE"
        },
        {
          "type": "VIRTIO_SCSI_MULTIQUEUE"
        },
        {
          "type": "SEV_CAPABLE"
        },
        {
          "type": "GVNIC"
        }
      ],
      "diskSizeGb": "40",
      "architecture": "X86_64"
    }
  ],
  "metadata": {
    "kind": "compute#metadata",
    "fingerprint": "7j05yXEEbWU=",
    "items": [
      {
        "key": "instance-template",
        "value": "projects/57530550063/global/instanceTemplates/gpcs-proxy-it-agp-asia-northeast1-*********"
      },
      {
        "key": "created-by",
        "value": "projects/57530550063/regions/asia-northeast1/instanceGroupManagers/gpcs-proxy-mig-asia-northeast1-*********-1"
      },
      {
        "key": "ssh-keys",
        "value": "admin:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r admin@SJCMACJ15HHTD8"
      },
      {
        "key": "pacfgds",
        "value": "dev.cfgds.swg.panclouddev.com"
      },
      {
        "key": "nodetype",
        "value": "153"
      },
      {
        "key": "mgmt-interface-swap",
        "value": "enable"
      },
      {
        "key": "dnsproxy",
        "value": "172.16.255.254"
      },
      {
        "key": "pagks",
        "value": "dev.gks.swg.panclouddev.com"
      },
      {
        "key": "cloud_provider",
        "value": "2"
      },
      {
        "key": "svc_acct",
        "value": "<EMAIL>"
      },
      {
        "key": "mtls_ca",
        "value": "it_ca_cert"
      },
      {
        "key": "mtls_certpkey",
        "value": "customer*********"
      },
      {
        "key": "upstream-proxyprotocol",
        "value": "v1"
      },
      {
        "key": "custsuperid",
        "value": "*********"
      },
      {
        "key": "patgs",
        "value": "dev.tgs.swg.panclouddev.com"
      },
      {
        "key": "custid",
        "value": "*********"
      },
      {
        "key": "custid_int",
        "value": "16"
      },
      {
        "key": "pacdls",
        "value": "dev.cdls.swg.panclouddev.com"
      },
      {
        "key": "secret_mgr_project",
        "value": "image-gpcs-nonprod-01"
      }
    ]
  },
  "serviceAccounts": [
    {
      "email": "<EMAIL>",
      "scopes": [
        "https://www.googleapis.com/auth/cloud-platform"
      ]
    }
  ],
  "selfLink": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/zones/asia-northeast1-a/instances/gpcs-proxy-instance-asia-northeast1-*********-k8mm",
  "scheduling": {
    "onHostMaintenance": "MIGRATE",
    "automaticRestart": True,
    "preemptible": False,
    "provisioningModel": "STANDARD"
  },
  "cpuPlatform": "AMD Rome",
  "labelFingerprint": "42WmSpB8rSM=",
  "startRestricted": False,
  "deletionProtection": False,
  "shieldedInstanceConfig": {
    "enableSecureBoot": False,
    "enableVtpm": True,
    "enableIntegrityMonitoring": True
  },
  "shieldedInstanceIntegrityPolicy": {
    "updateAutoLearnPolicy": True
  },
  "fingerprint": "wUqqJET0Y-U=",
  "lastStartTimestamp": "2023-06-20T08:36:36.372-07:00"
}
