from unittest import TestCase
from unittest.mock import patch, MagicMock
import mock_imports_eproxy_managed_vms
from mock_input_for_eproxy_managed_vms import Compute
from libs.cloud_providers.gcp.instance_manager.update_eproxy_managed_vms import update_swg_eproxy_instance_details
import logging

logger = logging.getLogger()

class DB:
    def __init__(self):
        self.logger = logger


class TestEnvoydetailsforNewarchitecture(TestCase):

    @patch("libs.cloud_providers.gcp.instance_manager.update_eproxy_managed_vms.get_compute", return_value = Compute())
    @patch("libs.cloud_providers.gcp.instance_manager.update_eproxy_managed_vms.get_is_agent_proxy_enabled",
           return_value=1)
    @patch("libs.cloud_providers.gcp.instance_manager.update_eproxy_managed_vms.get_eproxy_orch_state",
           return_value={})
    @patch("libs.cloud_providers.gcp.instance_manager.update_eproxy_managed_vms.update_swg_eproxy_managed_vms",
           return_value=MagicMock())
    @patch("libs.cloud_providers.gcp.instance_manager.update_eproxy_managed_vms.CustEpaasConfigModel",
           return_value=MagicMock())
    def test_update_swg_eproxy_instance_details(self, m1, m2, m3, m4, m5):
        res = update_swg_eproxy_instance_details("cust-gpcs-1iao398bdo27zwbyv04k", 222, {}, logger, 124, 123456, 'gcp',
                                           DB(), 153)
        self.assertTrue(res)

