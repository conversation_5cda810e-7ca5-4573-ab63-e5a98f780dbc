import mock_imports_gcp_instance_mgmt_utils
from mock_inputs_gcp_instance_mgmt_utils import swgproxy_bringup, param_dict, DB
from mock_inputs_gcp_instance_mgmt_utils import Open, param_dict_swg_agent
from libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils import gcp_instance_mgmt_utils
from unittest import TestCase
from unittest.mock import patch, MagicMock
import logging

logger = logging.getLogger()

file = Open()

def get_ssl_cert(cfg, project_id, ssl_cert_name):
    return ssl_cert_name


@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.swgproxy_bringup",
       return_value=swgproxy_bringup(DB()))
@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_proxy_prefix",
       return_value='gpcs-proxy-')
@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.is_cnat_enabled",
       return_value='True')
class TestGenerateTemplateForExplicitProxyMigrationFailureHandling(TestCase):

    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.open", return_value=file)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_instance_params",
           return_value=param_dict)
    def test_generate_explicit_prxoy_template_for_migration_failure(self, mock_param_dict, mock_proxy_bringup, mock_file,m5):
        """
        Test to verify that the new arch template gets generated successfully including is_old_arch data containing
        arch details of instances
        """
        gcp_utils = gcp_instance_mgmt_utils(DB())
        success, response = gcp_utils.generate_proxy_template(cfg={'envoy_hc_port': 80}, custid=287,
                                                              region=123,
                                                              custname="unittest", reg_name="test_region",
                                                              upgrade_ep_envoy_outside_panos=True)

        self.assertIn("is_old_arch", file.content)
        self.assertIn("sessionAffinity", file.content)
        self.assertIn("connectionTrackingPolicy", file.content)
        self.assertIn("CLIENT_IP_PORT_PROTO", file.content)
        self.assertIn("enableStrongAffinity", file.content)
        self.assertIn("gpcs-proxy-hc0-nlb-us-west2-826841054-80", file.content)
        self.assertEqual(success, True)

    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.open", return_value=file)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_instance_params",
           return_value=param_dict)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.gcpVpcModel",
           return_value=MagicMock(get_param=MagicMock(return_value="10.0.0.0/24")))
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.CustEpaasConfigModel",
           return_value=MagicMock(get_param=MagicMock(return_value=1)))
    def test_generate_explicit_prxoy_template_for_cnat_fields(self, mock_param_dict, mock_proxy_bringup, mock_file, m5, m6, m7, m8):
        """
        Test to verify that the new arch template gets generated successfully including is_old_arch data containing
        arch details of instances
        """
        gcp_utils = gcp_instance_mgmt_utils(DB())
        success, response = gcp_utils.generate_proxy_template(cfg={'envoy_hc_port': 80}, custid=287,
                                                              region=1278,
                                                              custname="unittest", reg_name="test_region",
                                                              upgrade_ep_envoy_outside_panos=True)

        self.assertIn("nats:", file.content)
        self.assertIn("natIps:\n          - $(ref.nat-ip", file.content)
        self.assertIn("sourceSubnetworkIpRangesToNat: 'LIST_OF_SUBNETWORKS'", file.content)
        self.assertIn("natIpAllocateOption: MANUAL_ONLY", file.content)
        self.assertIn("aliasIpRanges", file.content)
        self.assertEqual(success, True)


    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.open", return_value=file)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_instance_params",
           return_value=param_dict)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.is_cnat_enabled",
           return_value='True')
    def test_generate_explicit_prxoy_template_for_global_nlb_without_agp(self, mock_param_dict, mock_proxy_bringup,
                                                                    mock_file,m5, m6):
        """
        If agent proxy not enabled but its a new onboard then we bringup 3 Lbs and exclude the SSL LB
        """
        gcp_utils = gcp_instance_mgmt_utils(DB())
        success, response = gcp_utils.generate_proxy_template(cfg={'envoy_hc_port': 80}, custid=287,
                                                              region=1212,
                                                              custname="unittest", reg_name="test_region",
                                                              upgrade_ep_envoy_outside_panos=True)

        self.assertNotIn("gpcs-proxy-ssl-cert", file.content)
        self.assertIn("gpcs-proxy-mig-us-west2-826841054-1", file.content)
        self.assertIn("create_fw_ip_if_needed: True", file.content)
        self.assertIn("timeoutSec:", file.content)
        self.assertEqual(success, True)


@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_instance_params",
       return_value=param_dict_swg_agent[0])
@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.swgproxy_bringup",
       return_value=swgproxy_bringup(DB()))
@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.gcp_instance_mgmt_utils.get_ssl_cert",
       side_effect=get_ssl_cert)
@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_proxy_prefix",
        return_value='gpcs-proxy-')
@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.is_cnat_enabled",
       return_value='True')
class TestGenerateTemplateForAgentProxySSLCert(TestCase):

    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.open", return_value=file)
    def test_generate_explicit_prxoy_template_for_migration_failure_ssl(self, mock_param_dict, mock_proxy_bringup, mock_ssl,
                                                                    mock_file,m5, m6):
        """
        Test to verify that the ssl cert name is dynamic ie includes primary instance_id
        """
        gcp_utils = gcp_instance_mgmt_utils(DB())
        success, response = gcp_utils.generate_proxy_template(cfg={'envoy_hc_port': 80}, custid=287,
                                                              region=123,
                                                              custname="unittest", reg_name="test_region",
                                                              upgrade_ep_envoy_outside_panos=True)
        print(type(file.content))
        self.assertNotIn("gpcs-proxy-ssl-cert-us-west2-826841054-1-12345678912345678912345", file.content)
        self.assertIn("gpcs-proxy-ssl-cert-us-west2-826841054-1-1234567891234567891234", file.content)



@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.swgproxy_bringup",
       return_value=swgproxy_bringup(DB()))
@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_proxy_prefix",
       return_value='gpcs-proxy-')
class TestGenerateTemplateForExplicitProxyMultiZoneInstances(TestCase):

    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.open", return_value=file)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_instance_params",
           return_value=param_dict_swg_agent)
    def test_generate_explicit_proxy_template_for_multizone_instances(self, mock_param_dict, mock_proxy_bringup,
                                                                      mock_file, m5):
        """
        Test to verify that the new arch template gets generated successfully including is_old_arch data containing
        arch details of instances
        """
        gcp_utils = gcp_instance_mgmt_utils(DB())
        success, response = gcp_utils.generate_proxy_template(cfg={'envoy_hc_port': 80}, custid=287,
                                                              region=123,
                                                              custname="unittest", reg_name="test_region",
                                                              upgrade_ep_envoy_outside_panos=True)

        self.assertIn("gpcs-proxy-unmig-us-west1-a-826841054", file.content)
        self.assertIn("gpcs-proxy-unmig-us-west1-b-826841054", file.content)
        self.assertEqual(success, True)

#
#
