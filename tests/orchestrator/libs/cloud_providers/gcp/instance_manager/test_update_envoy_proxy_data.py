import pytest
from unittest.mock import Mock, patch
from libs.cloud_providers.gcp.instance_manager.update_envoy_proxy_data import trigger_envoy_update

def test_trigger_envoy_update_invalid_config():
    dbh = Mock()
    dbh.logger = Mock()
    cfg = Mock()
    cust_ep_cfg = Mock()
    cust_ep_cfg.get_entry.return_value = False

    result = trigger_envoy_update(dbh, cfg, cust_ep_cfg, 123, 'project-id', 'region-id', 'region-name')

    assert result == False
    dbh.logger.info.assert_called_with("Invalid cust_epaas_config skipping update")

def test_trigger_envoy_update_no_template_name():
    dbh = Mock()
    dbh.logger = Mock()
    cfg = Mock()
    cust_ep_cfg = Mock()
    cust_ep_cfg.get_entry.return_value = True
    cust_ep_cfg.get_param.return_value = '{"key": "value"}'

    result = trigger_envoy_update(dbh, cfg, cust_ep_cfg, 123, 'project-id', 'region-id', 'region-name')

    assert result == False
    dbh.logger.info.assert_called_with("No template name found in salt profile")

@patch('libs.cloud_providers.gcp.instance_manager.update_envoy_proxy_data.get_compute')
def test_trigger_envoy_update_success(mock_get_compute):
    dbh = Mock()
    dbh.logger = Mock()
    cfg = Mock()
    cust_ep_cfg = Mock()
    cust_ep_cfg.get_entry.return_value = True
    cust_ep_cfg.get_param.side_effect = ['{"template_name": "test-template"}', 'test-mig']

    mock_compute = Mock()
    mock_get_compute.return_value = mock_compute
    mock_request = Mock()
    mock_compute.regionInstanceGroupManagers().patch.return_value = mock_request
    mock_request.execute.return_value = {'status': 'success'}

    result = trigger_envoy_update(dbh, cfg, cust_ep_cfg, 123, 'project-id', 'region-id', 'region-name')

    assert result == True
    mock_compute.regionInstanceGroupManagers().patch.assert_called_once()
    dbh.logger.info.assert_called_with("Successfully updated instance template for custid: 123 region:region-id")

@patch('libs.cloud_providers.gcp.instance_manager.update_envoy_proxy_data.get_compute')
def test_trigger_envoy_update_exception(mock_get_compute):
    dbh = Mock()
    dbh.logger = Mock()
    cfg = Mock()
    cust_ep_cfg = Mock()
    cust_ep_cfg.get_entry.return_value = True
    cust_ep_cfg.get_param.side_effect = ['{"template_name": "test-template"}', 'test-mig']

    mock_compute = Mock()
    mock_get_compute.return_value = mock_compute
    mock_compute.regionInstanceGroupManagers().patch.side_effect = Exception("Test exception")

    result = trigger_envoy_update(dbh, cfg, cust_ep_cfg, 123, 'project-id', 'region-id', 'region-name')

    assert result == False
    dbh.logger.error.assert_called_once()
    assert "Test exception" in dbh.logger.error.call_args[0][0]
