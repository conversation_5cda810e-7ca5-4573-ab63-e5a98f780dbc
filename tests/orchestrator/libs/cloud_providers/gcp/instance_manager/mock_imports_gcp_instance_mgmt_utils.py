import sys
from unittest.mock import <PERSON>Mock
from mock_inputs_gcp_instance_mgmt_utils import Secret
sys.modules["libs.cfg"] = MagicMock()
sys.modules["libs.common.utils"] = MagicMock()
sys.modules["libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt"] = MagicMock()
sys.modules["libs.common.shared.gcp_utils"] = MagicMock(get_dynamic_instances_count=MagicMock(return_value=1))
sys.modules["libs.cloud_providers.common.utils"] = MagicMock()
sys.modules["libs.common.shared.sys_utils"] = MagicMock()
sys.modules["libs.model.instancemodel"] = MagicMock()
sys.modules["libs.model.custEpaasConfigModel"] = MagicMock()
sys.modules["libs.model.gcpvpcmodel"] = MagicMock()
sys.modules["libs.cloud_providers.common.swgproxy_bringup"] = MagicMock()
sys.modules['yaml'] = MagicMock()
sys.modules['yaml.loader'] = MagicMock()
sys.modules['googleapiclient'] = MagicMock()
sys.modules['googleapiclient.discovery'] = MagicMock()
sys.modules['google.cloud'] = MagicMock(secretmanager=Secret())
sys.modules['libs.apis.region_master_api'] = MagicMock()
sys.modules['libs.cloud_providers.gcp.instance_manager.gcp_colo_utils'] = \
    MagicMock(generate_colo_template=MagicMock(return_value="Template gen successful"))
sys.modules['orchestration_service.core.orchestrator_nlb_mgmt'] = MagicMock()
sys.modules['libs.cloud_providers.common.ep_cnat_scaleout_helper'] = MagicMock(is_ep_cnat_feature_enabled=True)

