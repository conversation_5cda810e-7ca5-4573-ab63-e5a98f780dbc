from unittest.mock import patch, MagicMock
import sys
import os
import pytest
import logging
import mock_imports_gcp_instance_mgmt_utils
from mock_inputs_gcp_instance_mgmt_utils import DB
from libs.model.custmodel import *
from libs.model.custnodemodel import *
from libs.model.instancemodel import InstanceModel
from libs.cloud_providers.gcp.instance_manager.gcp_instance_info import gcp_instance_info
logger = logging.getLogger()

class Mock_cursor():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.sql in mocked_db_return:
            answer = mocked_db_return.get(self.sql, None)
            return answer
        else:
            raise Exception("No matching dict found to lookup for this test suite.")

    def fetchone(self):
        answer = ''
        if 'inst_mgmt_id' in self.sql:
            json_str = r'{"template-url": "http://dummy.com"}'
            answer = [50, json_str, 1, 0, 1, "ERROR", 1, 1, 0, 1, 1]
        elif 'edge_location_region_name' in self.sql:
            answer = ["dummy"] * 24
            answer[8] = "gcp"
        elif 'network_load_balancer_config' in self.sql:
            answer = [1]
        elif 'eidx, region, custid, stack_name, cloud_provider, debug, debug_id, node_type' in self.sql:
            answer = ["100"] * 8
        elif 'UPDATE orch_instance_management_gcp_table SET inst_mgmt_id=' in self.sql:
            answer = ["1"]
        return answer

    def lastrowid(self):
        pass

class Mock_DBHandler():
    def __init__(self, logger):
        self.logger = logger

    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return
class mocked_CustomerModel():
    def __init__(self, custid, dbh):
        self.id =  custid
        self.dbh = dbh
        self.fwdrulesall = ""
        self.name = "dummy"
        self.acct_id = 0
        

def mock_is_sase_fabric():
    return True

class TestGcpInstanceInfo:
    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_gcp_instance_info(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:

            dbh = Mock_DBHandler(logger)
            instModel = InstanceModel(iid=11111, dbh=dbh)
            instModel.get_param = MagicMock()
            instModel.get_param.return_value = 0
            libs.cloud_providers.gcp.instance_manager.gcp_instance_info.PROVIDER_GCP = "GCP"
            libs.cloud_providers.gcp.instance_manager.gcp_instance_info.INSTANCE_ROLE_LEGACY = 0
            event = {}
            event['dbh'] = MagicMock()
            event['inst_id'] = 0
            event['region'] = "us-west-1"
            event['isPassive'] = False
            event['cloudtype'] = "GCP"
            event['gpcs_instance_size'] = "small"
            event['version'] = "1.0"
            event['instance_name'] = "my-instance"
            event['saas_gpcs_api_endpoint'] = "https://api.example.com"
            event['cert_fetch_otp'] = "abcdef"
            event['cloud_provider'] = "gcp"
            event['super_custid'] = "12345"
            event['custnode_id'] = "54321"
            event['parent_id'] = "98765"
            event['avail_domain'] = "example.com"
            event['sessionAffinity'] = "random"
            event['gp_gw_domain'] = "gw.example.com"
            event['commit_validate'] = True
            event['is_nlb_supported'] = True
            event['is_ngpa_protocol_enabled'] = True
            event['is_central_cache_supported'] = False
            event['is_instance_behind_nlb'] = False
            event['central_cache_service_endpoint'] = "https://cache.example.com"
            event['central_cache_service_backup_endpoint'] = "https://backup-cache.example.com"
            event['ciam_service_endpoint'] = "https://ciam.example.com"
            event['frr-enabled'] = 0
            event['ep-geneve-enabled'] = '1'

            ret = gcp_instance_info(event)
            ret.customer = mocked_CustomerModel(0,dbh)
            ret.zones_list = [0,1]
            ret.region_name = "dummy"
            ret.node_type = 49
            assert ret.central_cache_service_backup_endpoint == "https://backup-cache.example.com"
            assert ret.ciam_service_endpoint == "https://ciam.example.com"
            assert ret.get_user_data() == 'instance_name=my-instance,saas_gpcs_api_endpoint=https://api.example.com,cert_fetch_otp=abcdef,cloud_provider=gcp,custid=0,lambdaprefix=dummy,custid_int=0,gp_domain=gw.example.com,route53acct=,bucket-name=,panrepo_bucket_name=,aws-access-key-id=,aws-secret-access-key=,zone=0,zone-ha=1,region=None,edge-compute-region=dummy,super_custid=12345,ssh-keys=,sase_fabric=True,commit_validate=True,is_nlb_supported=True,is_ngpa_protocol_enabled=True,is_central_cache_supported=False,is_instance_behind_nlb=0,central_cache_service_endpoint=https://cache.example.com,central_cache_service_backup_endpoint=https://backup-cache.example.com,ciam_service_endpoint=https://ciam.example.com,frr-enabled=0,ep-geneve-enabled=1'

class TestGcpInstanceInfoInterfaceProcessing:
    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_interface_processing_with_valid_result(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            instModel = InstanceModel(iid=11111, dbh=dbh)
            instModel.get_param = MagicMock()
            instModel.get_param.return_value = 0

            event = self._create_base_event()

            mock_res = MagicMock()
            mock_res.ok = True
            mock_res.result = ["network-value", "subnet-value"]

            with patch.object(gcp_instance_info, '_get_network_details', return_value=mock_res):
                ret = gcp_instance_info(event)
                interface = {}

                if mock_res is not None and mock_res.ok and mock_res.result[0] is not None:
                    interface['network'] = mock_res.result[0]
                    interface['subnet'] = mock_res.result[1]

                assert interface['network'] == "network-value"
                assert interface['subnet'] == "subnet-value"

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_interface_processing_with_none_result(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            instModel = InstanceModel(iid=11111, dbh=dbh)
            instModel.get_param = MagicMock()
            instModel.get_param.return_value = 0

            event = self._create_base_event()

            mock_res = None

            with patch.object(gcp_instance_info, '_get_network_details', return_value=mock_res):
                ret = gcp_instance_info(event)
                interface = {}

                if mock_res is not None and mock_res.ok and mock_res.result[0] is not None:
                    interface['network'] = mock_res.result[0]
                    interface['subnet'] = mock_res.result[1]

                assert 'network' not in interface
                assert 'subnet' not in interface

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_interface_processing_with_false_ok_status(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            instModel = InstanceModel(iid=11111, dbh=dbh)
            instModel.get_param = MagicMock()
            instModel.get_param.return_value = 0

            event = self._create_base_event()

            mock_res = MagicMock()
            mock_res.ok = False
            mock_res.result = ["network-value", "subnet-value"]

            with patch.object(gcp_instance_info, '_get_network_details', return_value=mock_res):
                ret = gcp_instance_info(event)
                interface = {}

                if mock_res is not None and mock_res.ok and mock_res.result[0] is not None:
                    interface['network'] = mock_res.result[0]
                    interface['subnet'] = mock_res.result[1]

                assert 'network' not in interface
                assert 'subnet' not in interface

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_interface_processing_with_none_first_result_element(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            instModel = InstanceModel(iid=11111, dbh=dbh)
            instModel.get_param = MagicMock()
            instModel.get_param.return_value = 0

            event = self._create_base_event()

            mock_res = MagicMock()
            mock_res.ok = True
            mock_res.result = [None, "subnet-value"]

            with patch.object(gcp_instance_info, '_get_network_details', return_value=mock_res):
                ret = gcp_instance_info(event)
                interface = {}

                if mock_res is not None and mock_res.ok and mock_res.result[0] is not None:
                    interface['network'] = mock_res.result[0]
                    interface['subnet'] = mock_res.result[1]

                assert 'network' not in interface
                assert 'subnet' not in interface

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_interface_processing_with_empty_result_list(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            instModel = InstanceModel(iid=11111, dbh=dbh)
            instModel.get_param = MagicMock()
            instModel.get_param.return_value = 0

            event = self._create_base_event()

            mock_res = MagicMock()
            mock_res.ok = True
            mock_res.result = []

            with patch.object(gcp_instance_info, '_get_network_details', return_value=mock_res):
                ret = gcp_instance_info(event)
                interface = {}

                try:
                    if mock_res is not None and mock_res.ok and mock_res.result[0] is not None:
                        interface['network'] = mock_res.result[0]
                        interface['subnet'] = mock_res.result[1]
                except IndexError:
                    pass

                assert 'network' not in interface
                assert 'subnet' not in interface

    def _create_base_event(self):
        event = {}
        event['dbh'] = MagicMock()
        event['inst_id'] = 0
        event['region'] = "us-west-1"
        event['isPassive'] = False
        event['cloudtype'] = "GCP"
        event['gpcs_instance_size'] = "small"
        event['version'] = "1.0"
        event['instance_name'] = "my-instance"
        event['saas_gpcs_api_endpoint'] = "https://api.example.com"
        event['cert_fetch_otp'] = "abcdef"
        event['cloud_provider'] = "gcp"
        event['super_custid'] = "12345"
        event['custnode_id'] = "54321"
        event['parent_id'] = "98765"
        event['avail_domain'] = "example.com"
        event['sessionAffinity'] = "random"
        event['gp_gw_domain'] = "gw.example.com"
        event['commit_validate'] = True
        event['is_nlb_supported'] = True
        event['is_ngpa_protocol_enabled'] = True
        event['is_central_cache_supported'] = False
        event['is_instance_behind_nlb'] = False
        event['central_cache_service_endpoint'] = "https://cache.example.com"
        event['central_cache_service_backup_endpoint'] = "https://backup-cache.example.com"
        event['ciam_service_endpoint'] = "https://ciam.example.com"
        event['frr-enabled'] = 0
        event['ep-geneve-enabled'] = '1'
        return event

class TestGcpInstanceInfoAdditional:
    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_gcp_instance_is_proxy_type_swg_proxy(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            ret.customer = mocked_CustomerModel(0, dbh)
            ret.node_type = 55  # NODE_TYPE_SWG_PROXY
            assert ret.gcp_instance_is_proxy_type() == True

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_gcp_instance_is_proxy_type_bi_nh_proxy(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            ret.customer = mocked_CustomerModel(0, dbh)
            ret.node_type = 56  # NODE_TYPE_BI_NH_PROXY
            assert ret.gcp_instance_is_proxy_type() == True

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_gcp_instance_is_proxy_type_false(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            ret.customer = mocked_CustomerModel(0, dbh)
            ret.node_type = 1  # Some other node type
            assert ret.gcp_instance_is_proxy_type() == False

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_gcp_is_colo_interface_needed_true(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            ret.customer = mocked_CustomerModel(0, dbh)
            ret.node_type = 52  # NODE_TYPE_SERVICE_CONN
            ret.colo_interface_needed = True
            assert ret.gcp_is_colo_interface_needed() == True

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_gcp_is_colo_interface_needed_false_wrong_node_type(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            ret.customer = mocked_CustomerModel(0, dbh)
            ret.node_type = 1  # Not NODE_TYPE_SERVICE_CONN
            ret.colo_interface_needed = True
            assert ret.gcp_is_colo_interface_needed() == False

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_gcp_is_colo_interface_needed_false_colo_not_needed(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            ret.customer = mocked_CustomerModel(0, dbh)
            ret.node_type = 52  # NODE_TYPE_SERVICE_CONN
            ret.colo_interface_needed = False
            assert ret.gcp_is_colo_interface_needed() == False

    @patch("json.loads", return_value={"tenant_id": "123"})
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.is_sase_sc_enabled', return_value=True)
    def test_get_sase_fabric_node_types_with_sc_enabled(self, mock_sc_enabled, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            ret.customer = mocked_CustomerModel(0, dbh)
            sase_types = ret.get_sase_fabric_node_types()
            assert 51 in sase_types  # NODE_TYPE_REMOTE_NET
            assert 52 in sase_types  # NODE_TYPE_SERVICE_CONN

    @patch("json.loads", return_value={"tenant_id": "123"})
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.is_sase_sc_enabled', return_value=False)
    def test_get_sase_fabric_node_types_without_sc_enabled(self, mock_sc_enabled, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            ret.customer = mocked_CustomerModel(0, dbh)
            sase_types = ret.get_sase_fabric_node_types()
            assert 51 in sase_types  # NODE_TYPE_REMOTE_NET
            assert 52 not in sase_types  # NODE_TYPE_SERVICE_CONN

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_is_proxy_vm_disabled_remote_net_no_proxy(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            mock_customer = mocked_CustomerModel(0, dbh)
            mock_customer.get_param = MagicMock(return_value=1)  # ProxyStates.NO_PROXY
            ret.customer = mock_customer
            ret.node_type = 51  # NODE_TYPE_REMOTE_NET
            assert ret.is_proxy_vm_disabled() == True

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_is_proxy_vm_disabled_service_conn_with_proxy(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            mock_customer = mocked_CustomerModel(0, dbh)
            mock_customer.get_param = MagicMock(return_value=0)  # Not NO_PROXY
            ret.customer = mock_customer
            ret.node_type = 52  # NODE_TYPE_SERVICE_CONN
            assert ret.is_proxy_vm_disabled() == False

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_is_proxy_vm_disabled_swg_proxy_disabled(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            mock_customer = mocked_CustomerModel(0, dbh)
            mock_customer.get_param = MagicMock(return_value=1)  # ProxyStates.NO_PROXY
            ret.customer = mock_customer
            ret.node_type = 55  # NODE_TYPE_SWG_PROXY
            assert ret.is_proxy_vm_disabled() == True

    @patch("json.loads", return_value={"tenant_id": "123"})
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_has_nic_limit', return_value=False)
    def test_is_dual_egress_nic_allowed_pa_nat_instance(self, mock_nic_limit, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            ret.customer = mocked_CustomerModel(0, dbh)
            ret.sp_egress_type = 'PA'
            ret.node_type = 43  # NODE_TYPE_NAT_INSTANCE
            ret.cloud_machine_type = 'n1-standard-1'
            assert ret.is_dual_egress_nic_allowed() == True

    @patch("json.loads", return_value={"tenant_id": "123"})
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_has_nic_limit', return_value=False)
    def test_is_dual_egress_nic_allowed_hybrid_gp_gateway(self, mock_nic_limit, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            ret.customer = mocked_CustomerModel(0, dbh)
            ret.sp_egress_type = 'HYBRID'
            ret.node_type = 49  # NODE_TYPE_GP_GATEWAY
            ret.cloud_machine_type = 'n1-standard-1'
            assert ret.is_dual_egress_nic_allowed() == True

    @patch("json.loads", return_value={"tenant_id": "123"})
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_has_nic_limit', return_value=True)
    def test_is_dual_egress_nic_allowed_false_nic_limit(self, mock_nic_limit, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            ret.customer = mocked_CustomerModel(0, dbh)
            ret.sp_egress_type = 'PA'
            ret.node_type = 43  # NODE_TYPE_NAT_INSTANCE
            ret.cloud_machine_type = 'f1-micro'
            assert ret.is_dual_egress_nic_allowed() == False

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_is_dual_egress_nic_allowed_false_wrong_egress_type(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            ret.customer = mocked_CustomerModel(0, dbh)
            ret.sp_egress_type = 'OTHER'
            ret.node_type = 43  # NODE_TYPE_NAT_INSTANCE
            assert ret.is_dual_egress_nic_allowed() == False

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_is_dual_egress_nic_allowed_false_wrong_node_type(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            dbh = Mock_DBHandler(logger)
            event = self._create_base_event()
            ret = gcp_instance_info(event)
            ret.customer = mocked_CustomerModel(0, dbh)
            ret.sp_egress_type = 'PA'
            ret.node_type = 1  # Some other node type
            assert ret.is_dual_egress_nic_allowed() == False

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_gcp_instance_info_handler(self, mock_json):
        with patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_info.gcp_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:
            event = self._create_base_event()
            result = gcp_instance_info_handler(event)
            assert result is not None

    def _create_base_event(self):
        event = {}
        event['dbh'] = MagicMock()
        event['inst_id'] = 0


