from unittest.mock import patch, MagicMock
import logging
from unittest import TestCase
from mock_inputs_gcp_instance_mgmt_utils import Customer, InstanceModel, DB
import mock_imports_gcp_instance_param_mgmt
from mock_models import *
from libs.cfg import *
import libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt as gcp_param_mgmt

logger = logging.getLogger()

class Mock_cursor():
    def __init__(self, testSuiteIdx):
        self.sql = None
        self.params = None
        self.testSuiteIdx = testSuiteIdx

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params != None:
            return None
        raise Exception("No matching dict found to lookup for this test suite.")

    def fetchone(self):
        return {"name" : "Tejas"}

class Mock_DBHandler():
    def __init__(self, logger):
        self.logger = logger

    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return


class Mock_InstanceModel:
    def __init__(self, dbh):
        self.dbh = dbh
        self.schema_dict = {}
        self.schema_dict['custid'] = '123'
        self.schema_dict['compute_region_idx'] = '123'
        self.schema_dict['node_type'] = gcp_param_mgmt.NODE_TYPE_NAT_INSTANCE

    def get_param(self, field):
        return self.schema_dict[field]

class TestGCPInstanceParamMGMT1:
    def test_get_gcp_salt_profile_for_instance_entry(self, caplog):
        caplog.set_level(logging.INFO)
        instance_entry_dict = {}
        instance_entry_dict['instance_id'] = "123456"
        instance_entry_dict['region'] = "us-west-1"
        instance_entry_dict['gpcs_instance_size'] = "small"
        instance_entry_dict['version'] = "1.0"
        instance_entry_dict['instance_name'] = "my-instance"
        instance_entry_dict['saas_gpcs_api_endpoint'] = "https://api.example.com"
        instance_entry_dict['cert_fetch_otp'] = "abcdef"
        instance_entry_dict['cloud_provider'] = "gcp"
        instance_entry_dict['super_custid'] = "12345"
        instance_entry_dict['custnode_id'] = "54321"
        instance_entry_dict['parent_id'] = "98765"
        instance_entry_dict['avail_domain'] = "example.com"
        instance_entry_dict['sessionAffinity'] = "random"
        instance_entry_dict['gp_gw_domain'] = "gw.example.com"
        instance_entry_dict['commit_validate'] = True
        instance_entry_dict['is_nlb_supported'] = True
        instance_entry_dict['is_ngpa_protocol_enabled'] = True
        instance_entry_dict['is_central_cache_supported'] = False
        instance_entry_dict['is_instance_behind_nlb'] = False
        instance_entry_dict['central_cache_service_endpoint'] = "https://cache.example.com"
        instance_entry_dict['central_cache_service_backup_endpoint'] = "https://backup-cache.example.com"
        instance_entry_dict['ciam_service_endpoint'] = "https://ciam.example.com"
        instance_entry_dict['frr-enabled'] = 0
        instance_entry_dict['perform_ingress_ip_reduction'] = True
        ret = gcp_instance_param_mgmt(Mock_DBHandler(logger), instance_entry_dict, True)
        assert ret is not None
        instance_entry_dict['is_using_sp_interconnect'] = False
        instance_entry_dict["node_type_id"] = 49
        ret = gcp_param_mgmt.get_gcp_salt_profile_for_instance_entry(Mock_DBHandler(logger), instance_entry_dict, True)
        #assert ret is not None

    @patch(
        "libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.find_instances_by_custid_and_node_type_and_region_id",
        return_value=(True, [(1, "inst1")]))
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.find_standalone_instances_by_custid_and_node_type_and_region_id", return_value=(True, [(1, "inst1"), (2, "inst2")]))
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.find_nr_nat_instances_by_custid_and_region_id",
           return_value=(1, 2, 2))
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.find_instances_by_custid_and_region_id",
           return_value=(1, 2))
    def test_get_num_of_ips_by_instance_type(self, mock_find_instances_by_custid_and_region_id,
                                             mock_find_nr_nat_instances_by_custid_and_region_id, mock_find_standalone_instances_by_custid_and_node_type, 
                                             mock_find_instances_by_custid_and_node_type, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        inst_model = Mock_InstanceModel(dbh=dbh)
        nr_ips = gcp_param_mgmt.get_num_of_ips_by_instance_type(dbh, inst_model, 0)
        assert nr_ips == 6

        nr_ips = gcp_param_mgmt.get_num_of_ips_by_instance_type(dbh, inst_model, 1)
        assert nr_ips == 5

        nr_ips = gcp_param_mgmt.get_num_of_ips_by_instance_type(dbh, inst_model, 1, 1)
        assert nr_ips == 6

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.update_instance_behind_nlb')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.InstanceModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.gcp_is_clean_ip_project')
    def test_allocate_gp_gateways_egress_ip_for_new_instance_behind_nlb_nat_impl(self, mock_gcp_is_clean_ip_project, mock_IPManagementModel, mock_InstanceModel, mock_update_instance_behind_nlb, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        mock_gcp_is_clean_ip_project.return_value = False
        mock_IPManagementModel.return_value = MockIPManagementModel(dbh)
        mock_InstanceModel.return_value = MockInstanceModel(dbh=dbh)
        mock_update_instance_behind_nlb.return_value = MagicMock()
        instance_entry_dict = {"perform_ingress_ip_reduction": True, 'instance_id': "1000", "acct_id": "20000", "has_nat_gateway": True}
        ret = gcp_param_mgmt.allocate_gp_gateways_egress_ip_for_new_instance_behind_nlb_nat_impl(dbh, instance_entry_dict)
        assert "Set egress_ip_list value to ASSIGNED_TO_NAT for GW instance [GPGW_31090435_us-southeast_reservebankofaustralia-**********]" in caplog.text
        assert ret is True

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.ipv4_mgmt.GCPIPHandler')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.InstanceModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    def test_allocate_gp_gateways_egress_ip_for_new_instance_impl_iip_enabled(self, mock_IPManagementModel, mock_InstanceModel, mock_GCPIPHandler, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        mock_IPManagementModel.return_value = MockIPManagementModel(dbh)
        mock_InstanceModel.return_value = MockInstanceModelSAGWIIPEnabled(dbh=dbh)
        mock_GCPIPHandler.return_value = MockGCPIPHandler(dbh=dbh, instance_id="1000", acct_id="20000", node_id="115")
        instance_entry_dict = {"perform_ingress_ip_reduction": True, 'instance_id': "1000", "acct_id": "20000", "has_nat_gateway": False, "edge_region_idx": "220", "custnode_id": "115"}
        ret = gcp_param_mgmt.allocate_gp_gateways_egress_ip_for_new_instance_impl(dbh, instance_entry_dict)
        assert "Skip updating interface_ip_list with egress_ip_list as IIR feature is enabled" in caplog.text
        assert ret is True

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.ipv4_mgmt.GCPIPHandler')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.InstanceModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    def test_allocate_gp_gateways_egress_ip_for_new_instance_impl_iip_disabled(self, mock_IPManagementModel, mock_InstanceModel, mock_GCPIPHandler, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        mock_IPManagementModel.return_value = MockIPManagementModel(dbh)
        mock_InstanceModel.return_value = MockInstanceModelSAGWIIPEnabled(dbh=dbh)
        mock_GCPIPHandler.return_value = MockGCPIPHandler(dbh=dbh, instance_id="1000", acct_id="20000", node_id="115")
        instance_entry_dict = {"perform_ingress_ip_reduction": False, 'instance_id': "1000", "acct_id": "20000", "has_nat_gateway": False, "edge_region_idx": "220", "custnode_id": "115"}
        ret = gcp_param_mgmt.allocate_gp_gateways_egress_ip_for_new_instance_impl(dbh, instance_entry_dict)
        assert "Updating interface_ip_list with egress_ip_list list as IIR feature is disabled" in caplog.text
        assert ret is True

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.ipv4_mgmt.GCPIPHandler')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.InstanceModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    def test_allocate_gp_gateways_egress_ip_for_new_instance_impl_iip_disabled_ipv6_failed(self, mock_IPManagementModel, mock_InstanceModel, mock_GCPIPHandler, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        mock_IPManagementModel.return_value = MockIPManagementModel(dbh)
        mock_InstanceModel.return_value = MockInstanceModelSAGWIIPEnabledIpv6Failed(dbh=dbh)
        mock_GCPIPHandler.return_value = MockGCPIPHandler(dbh=dbh, instance_id="1000", acct_id="20000", node_id="115")
        instance_entry_dict = {"perform_ingress_ip_reduction": False, 'instance_id': "1000", "acct_id": "20000", "has_nat_gateway": False, "edge_region_idx": "220", "custnode_id": "115"}
        ret = gcp_param_mgmt.allocate_gp_gateways_egress_ip_for_new_instance_impl(dbh, instance_entry_dict)
        assert ret == False
        assert "Failed to update interface ipv6 list with egress ipv6 for" in caplog.text
        assert "Failed with exception" in caplog.text

class TestGcpInstanceParamMgmt(TestCase):

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.gcp_instance_info_handler')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_cloud_native_location_name_from_region_id', return_value="us-central1")
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_arch_for_cloud_machine_type')
    def test_get_gcp_salt_profile_for_instance_entry(self, mock_arch, mock_region_name, mock_gcp_instance_info):
        instance_entry_dict = {}
        instance_entry_dict['instance_id'] = 4466
        instance_entry_dict['sase_fabric'] = 0
        instance_entry_dict['commit_validate'] = 0
        instance_entry_dict['region'] = "us-west-1"
        instance_entry_dict['edge_region_idx'] = "220"
        instance_entry_dict['gpcs_instance_size'] = "gpcs-4xlarge"
        instance_entry_dict['cloud_machine_type'] = "n1-standard-16"
        instance_entry_dict['instance_role'] = "MP"
        instance_entry_dict['version'] = "1.0"
        instance_entry_dict['instance_name'] = "my-instance"
        instance_entry_dict['saas_gpcs_api_endpoint'] = "https://api.example.com"
        instance_entry_dict['cert_fetch_otp'] = "abcdef"
        instance_entry_dict['cloud_provider'] = "gcp"
        instance_entry_dict['super_custid'] = "12345"
        instance_entry_dict['cust_id'] = 232
        instance_entry_dict['acct_id'] = "105082324"
        instance_entry_dict['serial_no'] = "Dummy_serial_no"
        instance_entry_dict['instance_alias'] = "fw-4466-us-east-2-logis2704-1050823"
        instance_entry_dict['custnode_id'] = "54321"
        instance_entry_dict["node_type_id"] = 49
        instance_entry_dict['parent_id'] = "98765"
        instance_entry_dict['capacity_type'] = "PA-CAP530"
        instance_entry_dict['avail_domain'] = "example.com"
        instance_entry_dict['sessionAffinity'] = "random"
        instance_entry_dict['gp_gw_domain'] = "gw.example.com"
        instance_entry_dict['commit_validate'] = True
        instance_entry_dict['is_nlb_supported'] = True
        instance_entry_dict['is_ngpa_protocol_enabled'] = True
        instance_entry_dict['is_central_cache_supported'] = False
        instance_entry_dict['is_instance_behind_nlb'] = False
        instance_entry_dict['central_cache_service_endpoint'] = "https://cache.example.com"
        instance_entry_dict['central_cache_service_backup_endpoint'] = "https://backup-cache.example.com"
        instance_entry_dict['ciam_service_endpoint'] = "https://ciam.example.com"
        instance_entry_dict['is_using_sp_interconnect'] = False
        instance_entry_dict['frr-enabled'] = 0
        instance_entry_dict['perform_ingress_ip_reduction'] = True

        inst_prof = {}
        inst_prof["zone"] = "us-central1-a"
        inst_prof['userdata']= "instance_name=FW_4466_us-east-2_logis2704-105082324"
        inst_prof["image_project"] = "image-gpcs-nonprod-01"
        inst_prof['ami_ids'] = {"x86-64": "pa-vm-saas-gcp-10-2-4-c630-saas",
                                    "aarch64": "pa-vmarm-saas-gcp-10-2-4-c630-saas"}
        inst_prof['svc_acct'] = "<EMAIL>"
        inst_prof['clean_ip_tag'] = False
        inst_prof["static_ip"] = ''
        inst_prof['MgmtSubnet'] = 'shared-mgmt-us-central1-01'
        inst_prof['DPSubnet'] = 'dp-us-central1-01'
        inst_prof['HASubnet'] = 'ha-us-central1-01'
        inst_prof['MgmtNetwork'] = "shared-mgmt-vpc"
        inst_prof['DPNetwork'] = "dp-vpc"
        inst_prof['HANetwork'] = "ha-vpc"
        inst_prof['MgmtInterfaceName'] = "nic-mgmt"
        inst_prof['DPInterfaceName'] = "nic-dp"
        inst_prof['HAInterfaceName'] = "nic-ha"
        inst_prof['MgmtHasExternalIP'] = False
        inst_prof['DPHasExternalIP'] = True
        inst_prof['HAHasExternalIP'] = False
        inst_prof['is_using_sp_interconnect'] = False

        mock_gcp_instance_info.return_value=inst_prof
        mock_arch.return_value={"n1-standard-16" : "x86-64"}

        ret = gcp_param_mgmt.get_gcp_salt_profile_for_instance_entry(Mock_DBHandler(logger), instance_entry_dict, False)
        assert ret["ImageName"] == "pa-vm-saas-gcp-10-2-4-c630-saas"

        instance_entry_dict['cloud_machine_type'] = "t2a-standard-8"
        mock_arch.return_value = {"t2a-standard-8": "aarch64"}
        ret = gcp_param_mgmt.get_gcp_salt_profile_for_instance_entry(Mock_DBHandler(logger), instance_entry_dict, False)
        assert ret["ImageName"] == "pa-vmarm-saas-gcp-10-2-4-c630-saas"

        ret = get_gcp_salt_profile_for_instance_entry(Mock_DBHandler(logger), instance_entry_dict, False)
        assert ret["perform_ingress_ip_reduction"] == True

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.ipv4_mgmt',
           return_value=MagicMock(GCPIPHandler=MagicMock(get_nlb_ip=None, allocate_public_ip_for_customer=None)))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.InstanceModel',
           return_value=InstanceModel(123456, "test_instName"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.gcp_is_clean_ip_project',
           return_value=False)
    @patch(
        'libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_cloud_native_location_name_from_region_id',
        return_value="us-central1")
    def test_get_gcp_salt_profile_for_instance_entry_no_nlb_ip(self, mock4, mock1, mock2, mock3):
        '''
         verifying that the salt_profile does not get populated if nlb ip not present
        '''
        instance_entry_dict = {}
        instance_entry_dict['instance_id'] = "123456"
        instance_entry_dict['region'] = "us-west-1"
        instance_entry_dict['gpcs_instance_size'] = "small"
        instance_entry_dict['version'] = "1.0"
        instance_entry_dict['instance_name'] = "my-instance"
        instance_entry_dict['saas_gpcs_api_endpoint'] = "https://api.example.com"
        instance_entry_dict['cert_fetch_otp'] = "abcdef"
        instance_entry_dict['cloud_provider'] = "gcp"
        instance_entry_dict['super_custid'] = "12345"
        instance_entry_dict['custnode_id'] = "54321"
        instance_entry_dict['parent_id'] = "98765"
        instance_entry_dict['edge_region_idx'] = "220"
        instance_entry_dict['avail_domain'] = "example.com"
        instance_entry_dict['sessionAffinity'] = "random"
        instance_entry_dict['gp_gw_domain'] = "gw.example.com"
        instance_entry_dict['commit_validate'] = True
        instance_entry_dict['is_nlb_supported'] = True
        instance_entry_dict['is_ngpa_protocol_enabled'] = True
        instance_entry_dict['is_central_cache_supported'] = False
        instance_entry_dict['is_instance_behind_nlb'] = False
        instance_entry_dict['central_cache_service_endpoint'] = "https://cache.example.com"
        instance_entry_dict['central_cache_service_backup_endpoint'] = "https://backup-cache.example.com"
        instance_entry_dict['ciam_service_endpoint'] = "https://ciam.example.com"
        instance_entry_dict['frr-enabled'] = 0
        ret = gcp_param_mgmt.get_gcp_salt_profile_for_instance_entry(dbh=DB(),
                                                      instance_entry_dict=instance_entry_dict,
                                                      is_passive=True, is_clean_pipe=0, node_type='SWGPROXY')
        assert ret is None

class TestAllocateNatNlbEdgeLocationEgressIp:
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_num_of_ips_by_instance_type')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_nat_dummy_upgrade_ongoing_in_region')
    def test_allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl_nat_instance(self, mock_is_nat_dummy_upgrade, mock_get_num_of_ips, mock_IPManagementModel, mock_CustomerModel, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        my_inst = MockInstanceModel(dbh=dbh)
        egress_ip_list_loc = [220]
        instance_entry_dict = {"perform_ingress_ip_reduction": False}
        is_clean_ip_project = False

        mock_CustomerModel.return_value.get_auto_scale_options.return_value = (None, None, None, True)
        mock_get_num_of_ips.return_value = 2
        mock_IPManagementModel.return_value.reserve_ips_for_cust_region.return_value = {'ok': True, 'ip_list': [['***********', 'reserved']]}
        mock_IPManagementModel.return_value.bind_allow_listed_ips_to_instance.return_value = {"ok": True}
        mock_is_nat_dummy_upgrade.return_value = False

        result = gcp_param_mgmt.allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl(dbh, my_inst, egress_ip_list_loc, instance_entry_dict, is_clean_ip_project)

        assert result == True
        assert "Successfully bound edge IP address for instance" in caplog.text

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_num_of_ips_by_instance_type')
    def test_allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl_nlb_instance(self, mock_get_num_of_ips, mock_IPManagementModel, mock_CustomerModel, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        my_inst = MockInstanceModel(dbh=dbh, node_type=gcp_param_mgmt.NODE_TYPE_NLB_INSTANCE)
        egress_ip_list_loc = [220]
        instance_entry_dict = {"perform_ingress_ip_reduction": True}
        is_clean_ip_project = False

        result = gcp_param_mgmt.allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl(dbh, my_inst, egress_ip_list_loc, instance_entry_dict, is_clean_ip_project)

        assert result == True
        assert "No egress IPs are allocated to NLB if ingress_ip_reduction is enabled" in caplog.text

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_num_of_ips_by_instance_type')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_nat_dummy_upgrade_ongoing_in_region')
    def test_allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl_ip_reservation_failure(self, mock_is_nat_dummy_upgrade, mock_get_num_of_ips, mock_IPManagementModel, mock_CustomerModel, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        my_inst = MockInstanceModel(dbh=dbh)
        egress_ip_list_loc = [220]
        instance_entry_dict = {"perform_ingress_ip_reduction": False}
        is_clean_ip_project = False

        mock_CustomerModel.return_value.get_auto_scale_options.return_value = (None, None, None, False)
        mock_get_num_of_ips.return_value = 2
        mock_IPManagementModel.return_value.reserve_ips_for_cust_region.return_value = {'ok': False}
        mock_is_nat_dummy_upgrade.return_value = False

        result = gcp_param_mgmt.allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl(dbh, my_inst, egress_ip_list_loc, instance_entry_dict, is_clean_ip_project)

        assert result == False
        assert "Failed to reserver IP address for custid" in caplog.text

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_num_of_ips_by_instance_type')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_nat_dummy_upgrade_ongoing_in_region')
    def test_allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl_bind_ip_failure(self, mock_is_nat_dummy_upgrade, mock_get_num_of_ips, mock_IPManagementModel, mock_CustomerModel, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        my_inst = MockInstanceModel(dbh=dbh)
        egress_ip_list_loc = [220]
        instance_entry_dict = {"perform_ingress_ip_reduction": False}
        is_clean_ip_project = False

        mock_CustomerModel.return_value.get_auto_scale_options.return_value = (None, None, None, False)
        mock_get_num_of_ips.return_value = 2
        mock_IPManagementModel.return_value.reserve_ips_for_cust_region.return_value = {'ok': True, 'ip_list': [['***********', 'reserved']]}
        mock_IPManagementModel.return_value.bind_reserved_ips_to_instance.return_value = {"ok": False}
        mock_is_nat_dummy_upgrade.return_value = False

        result = gcp_param_mgmt.allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl(dbh, my_inst, egress_ip_list_loc, instance_entry_dict, is_clean_ip_project)

        assert result == False
        assert "Failed to bind reserved IP addresses to instance" in caplog.text


    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_num_of_ips_by_instance_type')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_nat_dummy_upgrade_ongoing_in_region')
    def test_allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl_nat_dummy_upgrade(self, mock_is_nat_dummy_upgrade, mock_get_num_of_ips, mock_IPManagementModel, mock_CustomerModel, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        my_inst = MockInstanceModel(dbh=dbh)
        egress_ip_list_loc = [220]
        instance_entry_dict = {"perform_ingress_ip_reduction": False}
        is_clean_ip_project = False

        mock_CustomerModel.return_value.get_auto_scale_options.return_value = (None, None, None, True)
        mock_get_num_of_ips.return_value = 2
        mock_IPManagementModel.return_value.reserve_ips_for_cust_region.return_value = {'ok': True, 'ip_list': [['***********', 'reserved']]}
        mock_IPManagementModel.return_value.bind_reserved_ips_to_instance.return_value = {"ok": True}
        mock_is_nat_dummy_upgrade.return_value = True

        result = gcp_param_mgmt.allocate_nat_nlb_edge_location_egress_ip_for_new_instance_impl(dbh, my_inst, egress_ip_list_loc, instance_entry_dict, is_clean_ip_project)

        assert result == True
        assert mock_IPManagementModel.return_value.bind_reserved_ips_to_instance.called
        assert not mock_IPManagementModel.return_value.bind_allow_listed_ips_to_instance.called


class TestGCPInstanceParamMGMTForColoSC:
    def test_get_gcp_salt_profile_for_colo_instance_entry(self, caplog):
        caplog.set_level(logging.INFO)
        instance_entry_dict = {}
        instance_entry_dict['instance_id'] = "123456"
        instance_entry_dict['region'] = "us-west-1"
        instance_entry_dict['gpcs_instance_size'] = "small"
        instance_entry_dict['version'] = "1.0"
        instance_entry_dict['instance_name'] = "my-instance"
        instance_entry_dict[
            'saas_gpcs_api_endpoint'] = "https://api.example.com"
        instance_entry_dict['cert_fetch_otp'] = "abcdef"
        instance_entry_dict['cloud_provider'] = "gcp"
        instance_entry_dict['super_custid'] = "12345"
        instance_entry_dict['custnode_id'] = "54321"
        instance_entry_dict['parent_id'] = "98765"
        instance_entry_dict['avail_domain'] = "example.com"
        instance_entry_dict['commit_validate'] = True
        instance_entry_dict['is_nlb_supported'] = True
        instance_entry_dict['is_central_cache_supported'] = False
        instance_entry_dict['is_instance_behind_nlb'] = True
        instance_entry_dict[
            'central_cache_service_endpoint'] = "https://cache.example.com"
        instance_entry_dict[
            'central_cache_service_backup_endpoint'] = "https://backup-cache.example.com"
        instance_entry_dict[
            'ciam_service_endpoint'] = "https://ciam.example.com"
        instance_entry_dict['frr-enabled'] = 0
        instance_entry_dict['colo_ilb_name'] = 'ilb-colo-sc1'
        instance_entry_dict['colo_interface_support'] = True

        ret = gcp_instance_param_mgmt(Mock_DBHandler(logger),
                                      instance_entry_dict, True)
        assert ret is not None
        instance_entry_dict['is_using_sp_interconnect'] = False
        instance_entry_dict["node_type_id"] = 51
        ret = gcp_param_mgmt.get_gcp_salt_profile_for_instance_entry(
            Mock_DBHandler(logger), instance_entry_dict, True)
        assert ret["colo_ilb_name"] == "ilb-colo-sc1"
