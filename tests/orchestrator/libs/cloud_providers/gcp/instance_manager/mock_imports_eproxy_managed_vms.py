import sys
from unittest.mock import MagicMock
sys.modules['googleapiclient.discovery'] = MagicMock()
sys.modules['libs.db.dbhandle'] = MagicMock()
sys.modules['libs.common.shared.gcp_utils'] = MagicMock()
sys.modules['libs.common.shared.rastro.rastro_logging_wrapper'] = MagicMock()
sys.modules['libs.model.custEpaasConfigModel'] = MagicMock()
sys.modules['libs.model.custmodel'] = MagicMock()
sys.modules['libs.model.orchcfgmodel'] = MagicMock()
sys.modules['libs.model.orchcfgmodel_v2'] = MagicMock()
sys.modules['libs.model.explicitProxyTenantInfoModel'] = MagicMock()
sys.modules['libs.model.regionmastermodel'] = MagicMock(get_compute_region_native_name="us-west1")
sys.modules['traceback'] = MagicMock()
sys.modules['argparse'] = MagicMock()