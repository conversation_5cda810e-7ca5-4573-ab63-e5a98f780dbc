import mock_imports_gcp_instance_mgmt_utils
import mock_imports_utils
from mock_inputs_gcp_instance_mgmt_utils import swgproxy_bringup, param_dict, DB, \
    nlb_param_dict, param_dict_nlb_node, param_dict_swg_agent, Service, gw_behind_nlb_param_dict_v6, no_passive_instance_dict, with_passive_instance_dict
from mock_inputs_gcp_instance_mgmt_utils import paths
import os
from libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils import gcp_instance_mgmt_utils
from common.shared.sys_utils import NODE_TYPE_BI_NH_PROXY, NODE_TYPE_SWG_PROXY, NODE_TYPE_UDA
import filecmp
from unittest import TestCase
from unittest.mock import patch, MagicMock
import logging

logger = logging.getLogger()
from pathlib import Path

class TestCreateInstanceTemplateLabels(TestCase):

    def test_create_instance_template_labels_rbi(self):
        gcp_utils = gcp_instance_mgmt_utils(DB())
        label_template = gcp_utils.create_instance_template_labels(cfg={'aws_env': "dev20"}, custname="tenant-name",
                                                                    node_type=NODE_TYPE_BI_NH_PROXY, alt_node_type=NODE_TYPE_SWG_PROXY, 
                                                                    params={'MachineType': "machine-type", 'ContactLabel': "userid"},
                                                                    metadata_dict={'custid': "123", 'custsuperid': "123",
                                                                    'compute_region': "us-east-1", 'cloud_provider': "2"},
                                                                    spacing="  ")
        expected_labels = '''  labels:
    - labelName: instancetype
      labelValue: binh-envoy
    - labelName: instancesize
      labelValue: machine-type
    - labelName: tenantname
      labelValue: tenant-name
    - labelName: environment
      labelValue: dev20
    - labelName: tenantid
      labelValue: 123
    - labelName: supertenantid
      labelValue: 123
    - labelName: computeregion
      labelValue: us-east-1
    - labelName: cloudprovider
      labelValue: gcp
    - labelName: resource_product
      labelValue: rbi
    - labelName: contactlabel
      labelValue: userid
    - labelName: licensetype
      labelValue: internal
'''
        self.assertEqual(label_template, expected_labels)

    def test_create_instance_template_labels_bce(self):
        gcp_utils = gcp_instance_mgmt_utils(DB())
        label_template = gcp_utils.create_instance_template_labels(cfg={'aws_env': "dev20"}, custname="tenant-name",
                                                                    node_type=NODE_TYPE_BI_NH_PROXY, alt_node_type=NODE_TYPE_UDA, 
                                                                    params={'MachineType': "machine-type", 'ContactLabel': "userid"},
                                                                    metadata_dict={'custid': "123", 'custsuperid': "123",
                                                                    'compute_region': "us-east-1", 'cloud_provider': "2"},
                                                                    spacing="  ")
        bce_label = "labelName: resource_product\n      labelValue: bce"
        self.assertEqual(bce_label in label_template, True)

    def test_create_instance_template_labels_email_format_contactlabel(self):
        gcp_utils = gcp_instance_mgmt_utils(DB())
        label_template = gcp_utils.create_instance_template_labels(cfg={'aws_env': "dev20"}, custname="tenant-name",
                                                                    node_type=NODE_TYPE_BI_NH_PROXY, alt_node_type=NODE_TYPE_UDA, 
                                                                    params={'MachineType': "machine-type", 'ContactLabel': "<EMAIL>"},
                                                                    metadata_dict={'custid': "123", 'custsuperid': "123",
                                                                    'compute_region': "us-east-1", 'cloud_provider': "2"},
                                                                    spacing="  ")
        contact_label = "labelName: contactlabel\n      labelValue: userid"
        self.assertEqual(contact_label in label_template, True)

    def test_create_instance_template_labels_invalid_email_format_contactlabel(self):
        gcp_utils = gcp_instance_mgmt_utils(DB())
        label_template = gcp_utils.create_instance_template_labels(cfg={'aws_env': "dev20"}, custname="tenant-name",
                                                                    node_type=NODE_TYPE_BI_NH_PROXY, alt_node_type=NODE_TYPE_UDA, 
                                                                    params={'MachineType': "machine-type", 'ContactLabel': "userid@email"},
                                                                    metadata_dict={'custid': "123", 'custsuperid': "123",
                                                                    'compute_region': "us-east-1", 'cloud_provider': "2"},
                                                                    spacing="  ")
        contact_label = "labelName: contactlabel"
        self.assertEqual(contact_label in label_template, False)

        label_template = gcp_utils.create_instance_template_labels(cfg={'aws_env': "dev20"}, custname="tenant-name",
                                                                    node_type=NODE_TYPE_BI_NH_PROXY, alt_node_type=NODE_TYPE_UDA, 
                                                                    params={'MachineType': "machine-type", 'ContactLabel': "user@<EMAIL>"},
                                                                    metadata_dict={'custid': "123", 'custsuperid': "123",
                                                                    'compute_region': "us-east-1", 'cloud_provider': "2"},
                                                                    spacing="  ")
        contact_label = "labelName: contactlabel"
        self.assertEqual(contact_label in label_template, False)

    def test_create_instance_template_labels_invalid_contactlabel(self):
        gcp_utils = gcp_instance_mgmt_utils(DB())
        label_template = gcp_utils.create_instance_template_labels(cfg={'aws_env': "dev20"}, custname="tenant-name",
                                                                    node_type=NODE_TYPE_BI_NH_PROXY, alt_node_type=NODE_TYPE_UDA, 
                                                                    params={'MachineType': "machine-type", 'ContactLabel': "user.id"},
                                                                    metadata_dict={'custid': "123", 'custsuperid': "123",
                                                                    'compute_region': "us-east-1", 'cloud_provider': "2"},
                                                                    spacing="  ")
        contact_label = "labelName: contactlabel"
        self.assertEqual(contact_label in label_template, False)


@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_instance_params",
       return_value=param_dict)
@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.swgproxy_bringup",
       return_value=swgproxy_bringup(DB()))
class TestGenerateTemplateForExplicitProxyMigration(TestCase):

    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.open", return_value=open(paths[0], "w+"))
    def test_generate_explicit_proxy_template_for_migration(self, mock_param_dict, mock_proxy_bringup, mock_file):
        """
        Test to verify that the new arch template gets generated successfully for non NULL  migrate_ep_status
        """
        gcp_utils = gcp_instance_mgmt_utils(DB())
        success, response = gcp_utils.generate_proxy_template(cfg={'envoy_hc_port': 80}, custid=287,
                                                              region=123,
                                                              custname="unittest", reg_name="test_region")

        self.assertEqual(success, True)

    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.open", return_value=open(paths[1], "w+"))
    def test_generate_explicit_proxy_template_no_migration(self, mock_param_dict, mock_proxy_bringup, mock_file):
        """
        Test to verify that the new arch template gets generated successfully for NULL migrate_ep_status
        """
        gcp_utils = gcp_instance_mgmt_utils(DB())
        success, response = gcp_utils.generate_proxy_template(cfg={'envoy_hc_port': 80}, custid=287,
                                                              region=456,
                                                              custname="unittest", reg_name="test_region")

        self.assertEqual(success, True)

    def tearDown(self):
        for path in paths[0:2]:
            try:
                os.remove(path)
            except Exception as e:
                pass


@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_instance_params",
       return_value=param_dict_nlb_node)
@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_nlb_params",
       return_value=nlb_param_dict)
class TestGenerateTemplateNLB(TestCase):
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.open", return_value=open(paths[2], "w+"))
    def test_generate_template_configure_gcp_nlb_template(self, mock_param_dict_nlb_node, mock_nlb_param_dict, mock_file):
        """
        Test to verify that the new NLB template gets generated successfully with NLB configuration
        """
        mock_param_dict_nlb_node['egress_ipv6_list_subnet'] = 123
        gcp_utils = gcp_instance_mgmt_utils(DB())
        success, response = gcp_utils.generate_template(cfg={'health_check_port': 8000}, custid=287, region=789, custname="unittest", reg_name="test_region")

        self.assertEqual(success, True)

    def tearDown(self):
        try:
            os.remove(paths[2])
        except Exception as e:
            pass

@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_instance_params",
       return_value=gw_behind_nlb_param_dict_v6)
@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_nlb_params",
       return_value=nlb_param_dict)
class TestGenerateTemplateNLBV6(TestCase):
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.open", return_value=open(paths[2], "w+"))
    def test_generate_template_configure_gcp_nlb_template(self, mock_nlb_param_dict, mock_gw_behind_nlb_param_dict_v6, mock_file):
        """
        Test to verify that the new NLB template gets generated successfully with NLB configuration
        """
        gcp_utils = gcp_instance_mgmt_utils(DB())
        success, response = gcp_utils.generate_template(cfg={'health_check_port': 8000}, custid=287, region=789, custname="unittest", reg_name="test_region")

        self.assertEqual(success, True)

    def tearDown(self):
        try:
            os.remove(paths[2])
        except Exception as e:
            pass
        
class TestGetIngressReducedEgressIpv6ListForNlb(TestCase):
    def test_get_ingress_reduced_egress_ipv6_list_for_nlb(self):
        gcp_utils = gcp_instance_mgmt_utils(DB())
        res = gcp_utils.get_ingress_reduced_egress_ipv6_list_for_nlb('{"201": "subnet-dp-us-west2-**********", "203": "subnet-dp-ipv6-us-west-1-201-**********"}', "201")
        assert res == '{"201": "subnet-dp-us-west2-**********"}'

@patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.swgproxy_bringup",
       return_value=swgproxy_bringup(DB()))
class TestGenerateTemplateForExplicitProxyWithAgent(TestCase):

    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_instance_params",
           return_value=param_dict_swg_agent[0])
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.open", return_value=open(paths[3], "w+"))
    def test_generate_explicit_proxy_template_for_agent_proxy(self, mock_param_dict, mock_proxy_bringup, mock_file):
        """
        Test to verify that the new arch template gets generated successfully for enable_tls_term_on_ep = 1
        """
        gcp_utils = gcp_instance_mgmt_utils(DB())
        success, response = gcp_utils.generate_proxy_template(cfg={'envoy_hc_port': 80, 'sslcert_version': '1'},
                                                              custid=287,
                                                              region=123,
                                                              custname="unittest", reg_name="test_region")

        self.assertEqual(success, True)


    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_instance_params",
           return_value=param_dict_swg_agent[0])
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.open", return_value=open(paths[4], "w+"))
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.gcp_instance_mgmt_utils.get_ssl_cert",
           return_value='gpcs-proxy-ssl-cert-test_region-**********-1')
    def test_generate_ep_template_with_new_ssl_cert_name(self, mock_param_dict, mock_proxy_bringup, mock_file,
                                                         mock_url):
        """
        Test to verify that the new arch template gets generated successfully for new ssl cert name
        """
        gcp_utils = gcp_instance_mgmt_utils(DB())
        success, response = gcp_utils.generate_proxy_template(cfg={'envoy_hc_port': 80, 'sslcert_version': '1'},
                                                              custid=287,
                                                              region=789,
                                                              custname="unittest", reg_name="test_region")

        self.assertEqual(success, True)


    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_instance_params",
           return_value=param_dict_swg_agent[1])
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_rn_egress_ip_list", return_value=["***********"])
    def test_generate_explicit_proxy_template_for_agent_proxy(self, mock_param_dict, mock_proxy_bringup, mock_rn_ip_list):
        """
        Test to verify that the new arch template gets generated successfully for enable_tls_term_on_ep = 1
        and enable_rn_to_ep_ctx_pass = 0
        """
        try:
            os.makedirs("cft")
        except Exception as e:
            pass
        gcp_utils = gcp_instance_mgmt_utils(DB())
        success, response = gcp_utils.generate_proxy_template(cfg={'envoy_hc_port': 80, 'sslcert_version': '1'},
                                                              custid=287,
                                                              region=321,
                                                              custname="unittest", reg_name="test_region")

        self.assertEqual(success, True)

        with open(response, 'r') as f:
            tmpl_file = f.readlines()
            found = "    nlbPort: '8082'\n" in tmpl_file
            self.assertEqual(found, True)
            found = "      portName: 'http-8080'\n" in tmpl_file
            self.assertEqual(found, True)
            found = "    nlbPort: '8083'\n" in tmpl_file
            self.assertEqual(found, True)
            found = "      portName: 'http-8081'\n" in tmpl_file
            self.assertEqual(found, True)
            found = "    create_fw_ip_if_needed: False\n" in tmpl_file
            self.assertEqual(found, True)

        try:
            os.remove(response)
            os.rmdir("cft")
        except Exception as e:
            pass


    def tearDown(self):
        for path in paths[3:5]:
            try:
                os.remove(path)
            except Exception as e:
                pass


class TestSSLCertcreationAndAccess(TestCase):

    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.gcp_authenticate", return_value=None)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.build", return_value=Service())
    def test_get_ssl_cert(self, mock_param_dict, mock_proxy_bringup):
        """
        Test function to create sslcertificate
        """
        gcp_utils = gcp_instance_mgmt_utils(DB())
        response = gcp_utils.get_ssl_cert(cfg={'envoy_hc_port': 80,'acs_gcp_project_id': 'test-asc-project'},
                                          project_id= "test-project-for-ssl",
                                          name="ssl-cert-1")

        self.assertEqual(response, "responseurl")

    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.gcp_authenticate", return_value=None)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.secretmanager.SecretManagerServiceClient",
           return_value=Service())
    def test_get_gcp_secret(self, mock_auth, mock_s):
        """
        Test function to access gcp secret
        """
        gcp_utils = gcp_instance_mgmt_utils(DB())
        response = gcp_utils.get_gcp_secret(cfg={'envoy_hc_port': 80, 'acs_gcp_project_id': 'test-asc-project'},
                                            secretname="secretname")

        self.assertEqual(response, "secretvalue")

    def tearDown(self):
        try:
            os.remove(paths[2])
        except Exception as e:
            pass


class TestGenerateTemplateNoPassive(TestCase):
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_instance_params",
           return_value=no_passive_instance_dict)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_nlb_params",
           return_value=nlb_param_dict)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.is_no_passive_instances_enabled",
           return_value=True)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.open", return_value=open(paths[2], "w+"))
    def test_generate_template_configure_gcp_no_passive_template(self, mock_instance_check, mock_param_dict_nlb_node, mock_nlb_param_dict, mock_file):
        """
        Test to verify that the new NLB template gets generated successfully with NLB configuration
        """
        mock_param_dict_nlb_node['egress_ipv6_list_subnet'] = 123
        gcp_utils = gcp_instance_mgmt_utils(DB())
        success, response = gcp_utils.generate_template(cfg={'health_check_port': 8000}, custid=287, region=789, custname="unittest", reg_name="test_region")
        self.assertEqual(success, True)

    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_instance_params",
           return_value=with_passive_instance_dict)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_nlb_params",
           return_value=nlb_param_dict)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.is_no_passive_instances_enabled",
           return_value=False)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.open", return_value=open(paths[2], "w+"))
    def test_generate_template_configure_gcp_with_passive_template(self, mock_file, mock_instance_check, mock_nlb_param_dict, mock_param_dict_nlb_node):
        """
        Test to verify that the new NLB template gets generated successfully with NLB configuration
        """
        mock_param_dict_nlb_node['egress_ipv6_list_subnet'] = 123
        gcp_utils = gcp_instance_mgmt_utils(DB())
        success, response = gcp_utils.generate_template(cfg={'health_check_port': 8000}, custid=287, region=789, custname="unittest", reg_name="test_region")
        self.assertEqual(success, True)

    def tearDown(self):
        try:
            os.remove(paths[2])
        except Exception as e:
            pass


class TestTemplateGenerationWithCNAT(TestCase):

    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_gcp_cust_region_instance_params",
           return_value=param_dict)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.is_cnat_enabled",
           return_value=True)
    @patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.get_dynamic_instances_count",
           return_value=2)
    def test_generate_template_with_cnat_enabled(self, mock_is_cnat_enabled, mock_param_dict, dynamic_count):
        """Test generation of template with CNAT enabled"""
        cnat_param_dict = {
            "cnat_enabled": "1",
            "cnat_min_count": 2,
            "cnat_max_count": 2,
            "cnat_vm_min_port": 1024,
            "cnat_vm_max_port": 65535,
            "cnat_public_ips": "********, ********, ********"
        }

        ip_list = ["********", "********"]

        gcp_utils = gcp_instance_mgmt_utils(DB())

        # Mock the __write_file_to_disk method to avoid writing to disk
        with patch.object(gcp_utils, '_gcp_instance_mgmt_utils__write_file_to_disk'):
            # Mock the swgproxy_bringup object
            mock_bringup = MagicMock(gpcs_get_cnat_params=MagicMock(return_value=cnat_param_dict), )
            with patch("libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils.swgproxy_bringup",
                      return_value=mock_bringup):
                success, response = gcp_utils.generate_proxy_template(
                    cfg={'health_check_port': 8000},
                    custid=287,
                    region=123,
                    custname="unittest",
                    reg_name="test_region",
                )
                self.assertEqual(success, True)
                self.assertTrue(response.startswith("cft/"))

