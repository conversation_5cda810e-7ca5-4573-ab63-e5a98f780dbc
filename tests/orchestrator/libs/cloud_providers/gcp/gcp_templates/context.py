class NLBtemplateContext:
    def __init__(self, is_regional):
        self.env = {"name": "gpcs-proxy-nlb-us-central1-**********",
                    "type": "network_load_balancer.py",
                    "project": "testproject123"}
        self.properties = {
            "nlbPort": '8080',
            "nlbPorts": ['8081', '8888'],
            "upgrade": True,
            "clean_ip_tag": False,
            "is_regional": is_regional,
            "region": "us-central1",
            "public_ip": "************",
            "protocol": "TCP",
            "network": "gpcs-vpc-dp-**********",
            "subnetwork": "subnet-dp-us-central1-**********",
            "backendService": {
                "backends": [{"group": "groupbslink123"}],
                "protocol": "TCP",
                "healthCheck": "grouphclink123",
                "connectionDraining": {
                    "drainingTimeoutSec": 30
                }
            }
        }

class NLBtemplateContextV6:
    def __init__(self):
        self.env = {"name": "nlb-16588-us-west-2-labreabakery-**********",
                    "type": "network_load_balancer.py",
                    "project": "testproject123"}
        self.properties = {
            "allPorts": True,
            "clean_ip_tag": False,
            "region": "us-west1",
            "public_ip": "*************",
            "protocol": "L3_DEFAULT",
            "has_external_ipv6": True,
            "network": "gpcs-vpc-dp-**********",
            "subnetwork": "subnet-dp-us-west1-**********",
            "egress_ipv6_list_subnet": {"200": "subnet-dp-us-west1-**********"},
            "backendService": {
                "backends": [{"group": "groupbslink123"}],
                "protocol": "UNSPECIFIED",
                "healthCheck": "grouphclink123",
                "sessionAffinity": "CLIENT_IP_PORT_PROTO",
                "connectionDraining": {
                    "drainingTimeoutSec": 30
                },
                "connectionTrackingPolicy": {
                   "enableStrongAffinity": True,
                   "idleTimeoutSec": 600,
                   "connectionPersistenceOnUnhealthyBackends": "ALWAYS_PERSIST"
                }
            }
        }
nlb_mock_responseV6 = {
   "resources":[
      {
         "name":"nlb-16588-us-west-2-labreabakery-10830895-bs",
         "type":"proxy_backend_service.py",
         "properties":{
            "loadBalancingScheme":"EXTERNAL",
            "has_external_ipv6": True,
            "region":"us-west1",
            "network":"projects/testproject123/global/networks/gpcs-vpc-dp-**********",
            "subnetwork":"https://www.googleapis.com/compute/v1/projects/testproject123/regions/us-west1/subnetworks/subnet-dp-us-west1-**********",
            "backends":[
               {
                  "group":"groupbslink123"
               }
            ],
            "protocol":"UNSPECIFIED",
            "connectionDraining":{
               "drainingTimeoutSec":30
            },
            "connectionTrackingPolicy": {
               "enableStrongAffinity": True,
               "idleTimeoutSec": 600,
               "connectionPersistenceOnUnhealthyBackends": "ALWAYS_PERSIST"
            },
            "healthCheck":"grouphclink123"
         }
      },
      {
         "name":"nlb-16588-us-west-2-labreabakery-10830895-fw-v6-200",
         "type":"proxy_forwarding_rule.py",
         "properties":{
            "loadBalancingScheme":"EXTERNAL",
            "IPProtocol":"L3_DEFAULT",
            "region":"us-west1",
            "backendService":"$(ref.nlb-16588-us-west-2-labreabakery-10830895-bs.selfLink)",
            "allPorts":True,
            "has_external_ipv6": True,
            "ipVersion":"IPV6",
            "network":"projects/testproject123/global/networks/gpcs-vpc-dp-**********",
            "subnetwork":"https://www.googleapis.com/compute/v1/projects/testproject123/regions/us-west1/subnetworks/subnet-dp-us-west1-**********"
         }
      }
   ],
   "outputs":[
      {
         "name":"backendServiceName",
         "value":"nlb-16588-us-west-2-labreabakery-10830895-bs"
      },
      {
         "name":"backendServiceSelfLink",
         "value":"$(ref.nlb-16588-us-west-2-labreabakery-10830895-bs.selfLink)"
      },
      {
         "name":"forwardingRuleName",
         "value":"nlb-16588-us-west-2-labreabakery-10830895-fw-v6-200"
      },
      {
         "name":"forwardingRuleSelfLink",
         "value":"$(ref.nlb-16588-us-west-2-labreabakery-10830895-fw-v6-200.selfLink)"
      },
      {
         "name":"region",
         "value":"us-west1"
      }
   ]
}

nlb_mock_response = {
   "resources":[
      {
         "name":"gpcs-proxy-nlb-us-central1-**********-fw-0",
         "type":"proxy_forwarding_rule.py",
         "properties":{
            "loadBalancingScheme":"EXTERNAL",
            "IPProtocol":"TCP",
            "region":"us-central1",
            "backendService":"$(ref.gpcs-proxy-nlb-us-central1-**********-bs.selfLink)",
            "allPorts":False,
            "portRange":"8080",
            "IPAddress":"************",
            "network":"projects/testproject123/global/networks/gpcs-vpc-dp-**********",
            "subnetwork":"https://www.googleapis.com/compute/v1/projects/testproject123/regions/us-central1/subnetworks/subnet-dp-us-central1-**********"
         }
      },
      {
         "name":"gpcs-proxy-nlb-us-central1-**********-bs",
         "type":"proxy_backend_service.py",
         "properties":{
            "loadBalancingScheme":"EXTERNAL",
            "region":"us-central1",
            "network":"projects/testproject123/global/networks/gpcs-vpc-dp-**********",
            "subnetwork":"https://www.googleapis.com/compute/v1/projects/testproject123/regions/us-central1/subnetworks/subnet-dp-us-central1-**********",
            "backends":[
               {
                  "group":"groupbslink123"
               }
            ],
            "protocol":"TCP",
            "connectionDraining":{
               "drainingTimeoutSec":30
            },
            "healthCheck":"grouphclink123"
         }
      },
      {
         "name":"gpcs-proxy-nlb-us-central1-**********-fw-1",
         "type":"proxy_forwarding_rule.py",
         "properties":{
            "loadBalancingScheme":"EXTERNAL",
            "IPProtocol":"TCP",
            "region":"us-central1",
            "backendService":"$(ref.gpcs-proxy-nlb-us-central1-**********-bs.selfLink)",
            "allPorts":False,
            "ports":['8081', '8888'],
            "IPAddress":"************",
            "network":"projects/testproject123/global/networks/gpcs-vpc-dp-**********",
            "subnetwork":"https://www.googleapis.com/compute/v1/projects/testproject123/regions/us-central1/subnetworks/subnet-dp-us-central1-**********"
         }
      }
   ],
   "outputs":[
      {
         "name":"forwardingRuleName",
         "value":"gpcs-proxy-nlb-us-central1-**********-fw-0"
      },
      {
         "name":"forwardingRuleSelfLink",
         "value":"$(ref.gpcs-proxy-nlb-us-central1-**********-fw-0.selfLink)"
      },
      {
         "name":"IPAddress",
         "value":"$(ref.gpcs-proxy-nlb-us-central1-**********-fw-0.IPAddress)"
      },
      {
         "name":"region",
         "value":"us-central1"
      },
      {
         "name":"backendServiceName",
         "value":"gpcs-proxy-nlb-us-central1-**********-bs"
      },
      {
         "name":"backendServiceSelfLink",
         "value":"$(ref.gpcs-proxy-nlb-us-central1-**********-bs.selfLink)"
      },
      {
         "name":"forwardingRuleName",
         "value":"gpcs-proxy-nlb-us-central1-**********-fw-1"
      },
      {
         "name":"forwardingRuleSelfLink",
         "value":"$(ref.gpcs-proxy-nlb-us-central1-**********-fw-1.selfLink)"
      },
      {
         "name":"IPAddress",
         "value":"$(ref.gpcs-proxy-nlb-us-central1-**********-fw-1.IPAddress)"
      },
      {
         "name":"region",
         "value":"us-central1"
      }
   ]
}


class UmigContext:
    def __init__(self):
        self.env = {"name": "gpcs-proxy-umig-asia-northeast1-member",
                    "type": "unmanaged-instance group-member.py",
                    "project": "testproject123"}
        self.properties = {
           "arch": [False],
           "zone": "asia-northeast1-a",
           "instanceGroup": "gpcs-proxy-unmig-asia-northeast1-a-826841054",
           "instances": ['swgproxy-2821-ap-northeast-1-300is']
        }


umig_member_mock_response = {
   "resources":[
      {
         "name":"swgproxy-2821-ap-northeast-1-300is-member",
         "action":"gcp-types/compute-v1:compute.instanceGroups.addInstances",
         "properties":{
            "zone":"asia-northeast1-a",
            "instanceGroup":"gpcs-proxy-unmig-asia-northeast1-a-826841054",
            "instances":[
               {
                  "instance":"$(ref.swgproxy-2821-ap-northeast-1-300is.selfLink)"
               }
            ]
         }
      }
   ],
   "outputs":[

   ]
}

class FwtemplateContext:
    def __init__(self):
        self.env = {"name": "gpcs-proxy-nlb-us-central1-**********",
                    "type": "network_load_balancer.py",
                    "project": "testproject123"}
        self.properties = {
                "name": ['swgproxy-2167408-us-east-2-clearsha', 'swgproxy-2168970-us-east-2-clearsha'],
                "InstName": ['SWGPROXY_2167408_us-east-2_clearshark-**********',
                           'SWGPROXY_2168970_us-east-2_clearshark-**********'],
                "PrimaryId": ['2167408', '2168970'],
                "count": 2,
                "imageProj": "image-gpcs-nonprod-01",
                "image": ['pa-vm-saas-gcp-10-2-4-c18-sase', 'pa-vm-saas-gcp-10-2-4-c21-sase'],
                "serviceAccount": "customer @ cust - gpcs - gfea44mfathucqndlu1o.iam.gserviceaccount.com",
                "clean_ip_tag": False,
                "egress_ip_list": {},
                "inbound_access_ip_list": None,
                "machineType": ['e2-standard-4', 'e2-standard-4'],
                "region": "us-central1",
                "zone": ['us-central1-a', 'us-central1-a'],
                "networkInterfaces":[
                                        {
                                            "name": "CLIENT_IP",
                                            "network": "gpcs - vpc - dp - **********",
                                            "subnet": "subnet - dp - us - central1 - **********",
                                            "static_ip": None,
                                            "has_external_ip": False
                                        },
                                        {
                                            "name": "nic-mgmt",
                                            "network": "gpcs - vpc - mgmt - **********",
                                            "subnet": "subnet - mgmt - us - central1 - **********",
                                            "static_ip": None,
                                            "has_external_ip": False
                                        },
                                        {
                                            "name": "nic-extrn",
                                            "network": "gpcs - vpc - extrn - **********",
                                            "subnet": "subnet - extrn - us - central1 - **********",
                                            "static_ip": ['*************', '*************'],
                                            "aliasIpRanges": [[
                                                                 {
                                                                     "ipCidrRange": '/32',
                                                                     "subnetworkRangeName":"secip1"
                                                                 }
                                                             ],
                                                            [
                                                                {
                                                                    "ipCidrRange": '/32',
                                                                    "subnetworkRangeName": "secip1"
                                                                }
                                                            ]
                                                            ],
                                            "has_external_ip": True
                                        }
                                    ],
                "firewallRule" : [
                    {
                        "name": "fw - rule - gpcs - dp - us - central1 - ********** - rn - to - ep",
                        "sourceRanges": ["************** / 32"],
                        "destinationRanges": ["************ / 32"],
                        "allowed": [
                            {"IPProtocol": "TCP",
                             "ports": ['8082','8083']
                             }
                            ]
                    }
                ],
                "metadata": [[{'key': 'instance_name', 'value': 'SWGPROXY_2167408_us-east-2_clearshark-**********'},
                            {'key': 'saas_gpcs_api_endpoint', 'value': 'qa3.panclouddev.com/api'},
                            {'key': 'cert_fetch_otp', 'value': 'ea66ab3e-39c0-4817-9660-287b847d59df'},
                            {'key': 'cloud_provider', 'value': 'gcp'}, {'key': 'custid', 'value': '**********'},
                            {'key': 'lambdaprefix', 'value': 'clearshark-**********'},
                            {'key': 'custid_int', 'value': '1190'}, {'key': 'gp_domain', 'value': 'panclouddev.com'},
                            {'key': 'route53acct', 'value': 'a447084568087'},
                            {'key': 'bucket-name', 'value': 'pan-content-qa3'},
                            {'key': 'panrepo_bucket_name', 'value': 'panrepo-us-west-2-qa3'},
                            {'key': 'aws-access-key-id', 'value': ''}, {'key': 'aws-secret-access-key', 'value': ''},
                            {'key': 'zone', 'value': 'us-central1-a'}, {'key': 'zone-ha', 'value': 'us-central1-b'},
                            {'key': 'region', 'value': 'us-central1'},
                            {'key': 'edge-compute-region', 'value': 'us-east-2'},
                            {'key': 'super_custid', 'value': '**********'}, {'key': 'ssh-keys',
                                                                             'value': 'gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8'},
                            {'key': 'is_sase_fabric_spn', 'value': 'False'}, {'key': 'commit_validate', 'value': 'False'},
                            {'key': 'is_nlb_supported', 'value': '0'},
                            {'key': 'is_ngpa_protocol_enabled', 'value': '0'},
                            {'key': 'is_central_cache_supported', 'value': '0'},
                            {'key': 'is_instance_behind_nlb', 'value': '0'}, {'key': 'central_cache_service_endpoint',
                                                                              'value': 'pa-service-api-us-qa01.tools.panclouddev.com'},
                            {'key': 'central_cache_service_backup_endpoint',
                             'value': 'pa-service-api-eu-qa03.tools.panclouddev.com'},
                            {'key': 'ciam_service_endpoint',
                             'value': 'ciam-qa.sasedp.panclouddev.comp'},
                            {'key': 'mgmt-interface-swap', 'value': 'enable'},
                            {'key': 'proxy-protocol-enabled', 'value': '1'}],
                           [{'key': 'instance_name', 'value': 'SWGPROXY_2168970_us-east-2_clearshark-**********'},
                            {'key': 'saas_gpcs_api_endpoint', 'value': 'qa3.panclouddev.com/api'},
                            {'key': 'cert_fetch_otp', 'value': 'f09766e2-23bc-4064-92c1-d437305b5093'},
                            {'key': 'cloud_provider', 'value': 'gcp'}, {'key': 'custid', 'value': '**********'},
                            {'key': 'lambdaprefix', 'value': 'clearshark-**********'},
                            {'key': 'custid_int', 'value': '1190'}, {'key': 'gp_domain', 'value': 'panclouddev.com'},
                            {'key': 'route53acct', 'value': 'a447084568087'},
                            {'key': 'bucket-name', 'value': 'pan-content-us-west-2-qa3'},
                            {'key': 'panrepo_bucket_name', 'value': 'panrepo-us-west-2-qa3'},
                            {'key': 'aws-access-key-id', 'value': ''}, {'key': 'aws-secret-access-key', 'value': ''},
                            {'key': 'zone', 'value': 'us-central1-a'}, {'key': 'zone-ha', 'value': 'us-central1-b'},
                            {'key': 'region', 'value': 'us-central1'},
                            {'key': 'edge-compute-region', 'value': 'us-east-2'},
                            {'key': 'super_custid', 'value': '**********'}, {'key': 'ssh-keys',
                                                                             'value': 'gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8'},
                            {'key': 'is_sase_fabric_spn', 'value': 'False'}, {'key': 'commit_validate', 'value': 'False'},
                            {'key': 'is_nlb_supported', 'value': '0'},
                            {'key': 'is_ngpa_protocol_enabled', 'value': '0'},
                            {'key': 'is_central_cache_supported', 'value': '0'},
                            {'key': 'is_instance_behind_nlb', 'value': '0'}, {'key': 'central_cache_service_endpoint',
                                                                              'value': 'pa-service-api-us-qa01.tools.panclouddev.com'},
                            {'key': 'central_cache_service_backup_endpoint',
                             'value': 'pa-service-api-eu-qa03.tools.panclouddev.com'},
                            {'key': 'ciam_service_endpoint',
                             'value': 'ciam-qa.sasedp.panclouddev.comp'},
                            {'key': 'is_using_sp_interconnect', 'value': 'False'},
                            {'key': 'mgmt-interface-swap', 'value': 'enable'},
                            {'key': 'proxy-protocol-enabled', 'value': '1'}]],
                "labels":[{
                    "labelName": "capacity_type",
                    "labelValue": "pa - cap421x"
                }]

            }

mock_fwtemplate_response={'resources': [{'type': 'compute.v1.instance', 'name': 'swgproxy-2167408-us-east-2-clearsha', 'properties': {'zone': 'us-central1-a', 'machineType': 'https://www.googleapis.com/compute/v1/projects/testproject123/zones/us-central1-a/machineTypes/e2-standard-4', 'disks': [{'deviceName': 'boot', 'type': 'PERSISTENT', 'boot': True, 'autoDelete': True, 'initializeParams': {'diskName': 'swgproxy-2167408-us-east-2-clearsha-disk', 'sourceImage': 'https://www.googleapis.com/compute/v1/projects/image-gpcs-nonprod-01/global/images/pa-vm-saas-gcp-10-2-4-c18-sase'}}], 'canIpForward': True, 'networkInterfaces': [{'network': 'https://www.googleapis.com/compute/v1/projects/testproject123/global/networks/gpcs - vpc - dp - **********', 'subnetwork': 'https://www.googleapis.com/compute/v1/projects/testproject123/regions/us-central1/subnetworks/subnet - dp - us - central1 - **********'}, {'network': 'https://www.googleapis.com/compute/v1/projects/testproject123/global/networks/gpcs - vpc - mgmt - **********', 'subnetwork': 'https://www.googleapis.com/compute/v1/projects/testproject123/regions/us-central1/subnetworks/subnet - mgmt - us - central1 - **********'}, {'network': 'https://www.googleapis.com/compute/v1/projects/testproject123/global/networks/gpcs - vpc - extrn - **********', 'subnetwork': 'https://www.googleapis.com/compute/v1/projects/testproject123/regions/us-central1/subnetworks/subnet - extrn - us - central1 - **********', 'accessConfigs': [{'name': 'external-nat', 'type': 'ONE_TO_ONE_NAT', 'natIP': '*************'}], 'aliasIpRanges': [{'ipCidrRange': '/32', 'subnetworkRangeName': 'secip1'}]}], 'metadata': {'items': [{'key': 'instance_name', 'value': 'SWGPROXY_2167408_us-east-2_clearshark-**********'}, {'key': 'saas_gpcs_api_endpoint', 'value': 'qa3.panclouddev.com/api'}, {'key': 'cert_fetch_otp', 'value': 'ea66ab3e-39c0-4817-9660-287b847d59df'}, {'key': 'cloud_provider', 'value': 'gcp'}, {'key': 'custid', 'value': '**********'}, {'key': 'lambdaprefix', 'value': 'clearshark-**********'}, {'key': 'custid_int', 'value': '1190'}, {'key': 'gp_domain', 'value': 'panclouddev.com'}, {'key': 'route53acct', 'value': 'a447084568087'}, {'key': 'bucket-name', 'value': 'pan-content-qa3'}, {'key': 'panrepo_bucket_name', 'value': 'panrepo-us-west-2-qa3'}, {'key': 'aws-access-key-id', 'value': ''}, {'key': 'aws-secret-access-key', 'value': ''}, {'key': 'zone', 'value': 'us-central1-a'}, {'key': 'zone-ha', 'value': 'us-central1-b'}, {'key': 'region', 'value': 'us-central1'}, {'key': 'edge-compute-region', 'value': 'us-east-2'}, {'key': 'super_custid', 'value': '**********'}, {'key': 'ssh-keys', 'value': 'gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8'}, {'key': 'is_sase_fabric_spn', 'value': 'False'}, {'key': 'commit_validate', 'value': 'False'}, {'key': 'is_nlb_supported', 'value': '0'}, {'key': 'is_ngpa_protocol_enabled', 'value': '0'}, {'key': 'is_central_cache_supported', 'value': '0'}, {'key': 'is_instance_behind_nlb', 'value': '0'}, {'key': 'central_cache_service_endpoint', 'value': 'pa-service-api-us-qa01.tools.panclouddev.com'}, {'key': 'central_cache_service_backup_endpoint', 'value': 'pa-service-api-eu-qa03.tools.panclouddev.com'}, {'key': 'ciam_service_endpoint', 'value': 'ciam-qa.sasedp.panclouddev.comp'}, {'key': 'mgmt-interface-swap', 'value': 'enable'}, {'key': 'proxy-protocol-enabled', 'value': '1'}]}, 'tags': {'items': ['fw-2167408', 'swg-proxy', 'swg-fw']}, 'serviceAccounts': [{'email': 'customer @ cust - gpcs - gfea44mfathucqndlu1o.iam.gserviceaccount.com', 'scopes': ['https://www.googleapis.com/auth/cloud-platform']}], 'labels': {'capacity_type': 'pa - cap421x'}}}, {'type': 'compute.v1.address', 'name': 'swgproxy-2167408-us-east-2-clearsha-address', 'properties': {'region': 'us-central1'}}, {'name': 'fw - rule - gpcs - dp - us - central1 - ********** - rn - to - ep-0', 'type': 'compute.v1.firewall', 'properties': {'network': 'https://www.googleapis.com/compute/v1/projects/testproject123/global/networks/gpcs - vpc - dp - **********', 'sourceRanges': ['************** / 32'], 'destinationRanges': ['************ / 32'], 'allowed': [{'IPProtocol': 'TCP', 'ports': ['8082', '8083']}], 'direction': 'INGRESS'}}, {'type': 'compute.v1.instance', 'name': 'swgproxy-2168970-us-east-2-clearsha', 'properties': {'zone': 'us-central1-a', 'machineType': 'https://www.googleapis.com/compute/v1/projects/testproject123/zones/us-central1-a/machineTypes/e2-standard-4', 'disks': [{'deviceName': 'boot', 'type': 'PERSISTENT', 'boot': True, 'autoDelete': True, 'initializeParams': {'diskName': 'swgproxy-2168970-us-east-2-clearsha-disk', 'sourceImage': 'https://www.googleapis.com/compute/v1/projects/image-gpcs-nonprod-01/global/images/pa-vm-saas-gcp-10-2-4-c21-sase'}}], 'canIpForward': True, 'networkInterfaces': [{'network': 'https://www.googleapis.com/compute/v1/projects/testproject123/global/networks/gpcs - vpc - dp - **********', 'subnetwork': 'https://www.googleapis.com/compute/v1/projects/testproject123/regions/us-central1/subnetworks/subnet - dp - us - central1 - **********'}, {'network': 'https://www.googleapis.com/compute/v1/projects/testproject123/global/networks/gpcs - vpc - mgmt - **********', 'subnetwork': 'https://www.googleapis.com/compute/v1/projects/testproject123/regions/us-central1/subnetworks/subnet - mgmt - us - central1 - **********'}, {'network': 'https://www.googleapis.com/compute/v1/projects/testproject123/global/networks/gpcs - vpc - extrn - **********', 'subnetwork': 'https://www.googleapis.com/compute/v1/projects/testproject123/regions/us-central1/subnetworks/subnet - extrn - us - central1 - **********', 'accessConfigs': [{'name': 'external-nat', 'type': 'ONE_TO_ONE_NAT', 'natIP': '*************'}], 'aliasIpRanges': [{'ipCidrRange': '/32', 'subnetworkRangeName': 'secip1'}]}], 'metadata': {'items': [{'key': 'instance_name', 'value': 'SWGPROXY_2168970_us-east-2_clearshark-**********'}, {'key': 'saas_gpcs_api_endpoint', 'value': 'qa3.panclouddev.com/api'}, {'key': 'cert_fetch_otp', 'value': 'f09766e2-23bc-4064-92c1-d437305b5093'}, {'key': 'cloud_provider', 'value': 'gcp'}, {'key': 'custid', 'value': '**********'}, {'key': 'lambdaprefix', 'value': 'clearshark-**********'}, {'key': 'custid_int', 'value': '1190'}, {'key': 'gp_domain', 'value': 'panclouddev.com'}, {'key': 'route53acct', 'value': 'a447084568087'}, {'key': 'bucket-name', 'value': 'pan-content-us-west-2-qa3'}, {'key': 'panrepo_bucket_name', 'value': 'panrepo-us-west-2-qa3'}, {'key': 'aws-access-key-id', 'value': ''}, {'key': 'aws-secret-access-key', 'value': ''}, {'key': 'zone', 'value': 'us-central1-a'}, {'key': 'zone-ha', 'value': 'us-central1-b'}, {'key': 'region', 'value': 'us-central1'}, {'key': 'edge-compute-region', 'value': 'us-east-2'}, {'key': 'super_custid', 'value': '**********'}, {'key': 'ssh-keys', 'value': 'gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8'}, {'key': 'is_sase_fabric_spn', 'value': 'False'}, {'key': 'commit_validate', 'value': 'False'}, {'key': 'is_nlb_supported', 'value': '0'}, {'key': 'is_ngpa_protocol_enabled', 'value': '0'}, {'key': 'is_central_cache_supported', 'value': '0'}, {'key': 'is_instance_behind_nlb', 'value': '0'}, {'key': 'central_cache_service_endpoint', 'value': 'pa-service-api-us-qa01.tools.panclouddev.com'}, {'key': 'central_cache_service_backup_endpoint', 'value': 'pa-service-api-eu-qa03.tools.panclouddev.com'}, {'key': 'ciam_service_endpoint', 'value': 'ciam-qa.sasedp.panclouddev.comp'}, {'key': 'is_using_sp_interconnect', 'value': 'False'}, {'key': 'mgmt-interface-swap', 'value': 'enable'}, {'key': 'proxy-protocol-enabled', 'value': '1'}]}, 'tags': {'items': ['fw-2168970', 'swg-proxy', 'swg-fw']}, 'serviceAccounts': [{'email': 'customer @ cust - gpcs - gfea44mfathucqndlu1o.iam.gserviceaccount.com', 'scopes': ['https://www.googleapis.com/auth/cloud-platform']}], 'labels': {'capacity_type': 'pa - cap421x'}}}, {'type': 'compute.v1.address', 'name': 'swgproxy-2168970-us-east-2-clearsha-address', 'properties': {'region': 'us-central1'}}, {'name': 'fw - rule - gpcs - dp - us - central1 - ********** - rn - to - ep-1', 'type': 'compute.v1.firewall', 'properties': {'network': 'https://www.googleapis.com/compute/v1/projects/testproject123/global/networks/gpcs - vpc - dp - **********', 'sourceRanges': ['************** / 32'], 'destinationRanges': ['************ / 32'], 'allowed': [{'IPProtocol': 'TCP', 'ports': ['8082', '8083']}], 'direction': 'INGRESS'}}], 'outputs': [{'name': 'PrimaryId', 'value': '2167408'}, {'name': 'PrimaryVmId', 'value': '$(ref.swgproxy-2167408-us-east-2-clearsha.id)'}, {'name': 'PrimaryMgtIp', 'value': '$(ref.swgproxy-2167408-us-east-2-clearsha.networkInterfaces[1].networkIP)'}, {'name': 'PrimaryPvtIp', 'value': '$(ref.swgproxy-2167408-us-east-2-clearsha.networkInterfaces[0].networkIP)'}, {'name': 'PrimaryMachineType', 'value': 'e2-standard-4'}, {'name': 'PrimaryExtrnIp', 'value': '$(ref.swgproxy-2167408-us-east-2-clearsha.networkInterfaces[2].networkIP)'}, {'name': 'EIP', 'value': '*************'}, {'name': 'PrimaryId', 'value': '2168970'}, {'name': 'PrimaryVmId', 'value': '$(ref.swgproxy-2168970-us-east-2-clearsha.id)'}, {'name': 'PrimaryMgtIp', 'value': '$(ref.swgproxy-2168970-us-east-2-clearsha.networkInterfaces[1].networkIP)'}, {'name': 'PrimaryPvtIp', 'value': '$(ref.swgproxy-2168970-us-east-2-clearsha.networkInterfaces[0].networkIP)'}, {'name': 'PrimaryMachineType', 'value': 'e2-standard-4'}, {'name': 'PrimaryExtrnIp', 'value': '$(ref.swgproxy-2168970-us-east-2-clearsha.networkInterfaces[2].networkIP)'}, {'name': 'EIP', 'value': '*************'}]}
