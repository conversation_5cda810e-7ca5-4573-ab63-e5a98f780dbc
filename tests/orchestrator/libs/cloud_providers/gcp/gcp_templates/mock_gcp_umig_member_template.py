

def get_ins_link(context, index):
    output_obj = {}
    if context.properties.get('arch', None) and context.properties['arch'][index]:
        member_url = ''.join(
      ['https://www.googleapis.com/compute/v1/projects/',
       context.env['project'], '/zones/', context.properties['zone'], '/instances/',
       context.properties['instances'][index]])
        output_obj['instance'] = member_url
    else:
        output_obj['instance'] = '$(ref.{}.selfLink)'.format(context.properties['instances'][index])
    return output_obj

def get_ins_obj(context, index):
    output_obj = get_ins_link(context, index)
    return output_obj

def generate_instance_resource(context, index):
    insts = []
    insts.append(get_ins_obj(context, index))

    umig = {
        'name': context.properties['instances'][index]+'-member',
        'action': 'gcp-types/compute-v1:compute.instanceGroups.addInstances',
        'properties':
            {
                'zone':
                    context.properties['zone'],
                'instanceGroup':
                    context.properties['instanceGroup'],
                'instances':
                    insts
            }
    }
    return umig;

def generate_config(context):
    resources = []
    for index in range(len(context.properties['instances'])):
        resources.append(generate_instance_resource(context, index))

    return {
        'resources':
            resources,
        'outputs':
            [
            ]
    }

