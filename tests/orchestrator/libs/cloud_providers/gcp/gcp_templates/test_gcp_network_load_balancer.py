import os
from unittest import TestCase
from pathlib import Path
from unittest.mock import patch, MagicMock
from libs.cloud_providers.gcp.gcp_templates.gcp_network_load_balancer_template import proxy_nlb_tpl
from libs.cloud_providers.gcp.gcp_templates.gcp_unmanaged_instance_group_member_template import umig_member_tpl

#create temp file
path = Path(__file__).resolve().parent
path1 = f"{path}/mock_gcp_template.py"
nlb_template = open(path1, "w+")
nlb_template.write(proxy_nlb_tpl)
import mock_gcp_template
import mock_gcp_umig_member_template

from context import NLBtemplateContext, nlb_mock_response, umig_member_mock_response, UmigContext, NLBtemplateContextV6, nlb_mock_responseV6

class TestGCPNLBTemplate(TestCase):

    def test_gcp_network_load_balancer_template(self):
        """
         Test that the nlb template gets generated successfully
        """

        response = mock_gcp_template.generate_config(NLBtemplateContext(True))
        self.assertEqual(response, nlb_mock_response)

    def test_gcp_network_load_balancer_template_v6(self):
        """
         Test that the nlb template gets generated successfully
        """

        response = mock_gcp_template.generate_config(NLBtemplateContextV6(True))
        self.assertEqual(response, nlb_mock_responseV6)

    def test_gcp_network_load_balancer_template_for_agent_proxy(self):
        """
         Test that the nlb template gets generated successfully
        """
        response = mock_gcp_template.generate_config(NLBtemplateContext(False))
        self.assertIn('is_regional', response['resources'][0]['properties'])
        self.assertIn('is_regional', response['resources'][1]['properties'])


class Testunmigmembertemplate(TestCase):

    def test_gcp_umig_member_template(self):
        """
         Test that the umig member template gets generated successfully
        """
        self.maxDiff = None
        response = mock_gcp_umig_member_template.generate_config(UmigContext())
        self.assertEqual(response, umig_member_mock_response)


# remove temp file
os.remove(path1)
