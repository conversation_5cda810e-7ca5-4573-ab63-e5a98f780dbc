import os
from unittest import TestCase
from pathlib import Path
from unittest.mock import patch, MagicMock
from context import FwtemplateContext, mock_fwtemplate_response
from libs.cloud_providers.gcp.gcp_templates.gcp_swgproxy_fw_template import GenerateConfig


class TestGenerateConfig(TestCase):

    def test_generate_config_for_fw_template(self):
        response = GenerateConfig(FwtemplateContext())
        print(response)
        self.assertEqual(response, mock_fwtemplate_response)


