import os
import sys
from unittest.mock import MagicMock
from mock_region_model import RegionMasterModel
sys.modules["libs.cfg"] = MagicMock()
sys.modules["libs.common.shared.sys_utils"] = MagicMock(PROVIDER_GCP=2, SERVICE_TYPE_SWG_NLB_INGRESS_IP=155)
sys.modules["libs.model.RegionMasterModel"] = MagicMock()
sys.modules["libs.model.custmodel"] = MagicMock()
sys.modules["libs.cloud_providers.common.swgproxy_bringup"] = MagicMock()
sys.modules["libs.model.explicitProxyTenantInfoModel"] = MagicMock()
sys.modules["libs.model.instancemanagement_gcp_model"] = MagicMock()
sys.modules["libs.model.instancemanagementmodel"] = MagicMock()
sys.modules["libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils"] = MagicMock()
sys.modules["libs.cloud_providers.gcp.instance_manager.gcp_bringup"] = MagicMock()
sys.modules["libs.model.custEpaasConfigModel"] = MagicMock()
sys.modules["libs.model.custnodemodel"] = MagicMock()
sys.modules["libs.model.instancemodel"] = MagicMock()
sys.modules["libs.model.orchcfgmodel"] = MagicMock()
sys.modules["libs.model.orchcfgmodel_v2"] = MagicMock()
sys.modules["libs.apis.region_master_api"] = MagicMock()
sys.modules["libs.common.shared.gcp_utils"] = MagicMock()
sys.modules["libs.common.shared.regionmastermodel"] = MagicMock()
sys.modules["libs.apis.region_master_api"] = MagicMock(gpcs_get_edge_region_name_from_edge_region_idx=MagicMock(return_value="us-west2"))
sys.modules["googleapiclient.discovery"] = MagicMock()
sys.modules["libs.cloud_providers.gcp.instance_manager.update_envoy_proxy_data"] = MagicMock()
sys.modules["libs.cloud_providers.gcp.instance_manager.update_eproxy_managed_vms"] = MagicMock()
sys.modules["libs.model.regionmastermodel"] = MagicMock(RegionMasterModel = RegionMasterModel)
