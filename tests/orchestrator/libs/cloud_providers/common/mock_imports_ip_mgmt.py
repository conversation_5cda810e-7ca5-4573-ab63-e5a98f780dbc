import sys
from unittest.mock import MagicMock
import mock_models
#sys.modules["libs.cfg"] = MagicMock()
#sys.modules["libs.common.shared.sys_utils"] = MagicMock(PROVIDER_UNDECIDED=0)
sys.modules["libs.model.RegionMasterModel"] = mock_models
sys.modules["libs.model.custmodel"] = mock_models
sys.modules["libs.model.custEpaasConfigModel"] = mock_models
sys.modules["libs.model.explicitProxyTenantInfoModel"] = mock_models
sys.modules["libs.apis.region_master_api"] = MagicMock()
sys.modules["mysql"] = MagicMock()
sys.modules["mysql.connector"] = MagicMock()
