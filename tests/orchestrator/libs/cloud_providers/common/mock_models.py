import logging
import json
import time
logger = logging.getLogger()
from mock_responses import cfg

class CustEpaasConfigModel:
    def __init__(self, dbh, custid, compute_region_id, cloud_provider, node_type=153, alt_node_type=-1):
        self.dbh = dbh
        self.custid = custid
        self.compute_region_id = compute_region_id
        self.cloud_provider = cloud_provider
        self.salt_profile = json.dumps({'template_name': "test_template", "envoy_image_version": "test_envoy"})
        self.upgrade_status = None
        self.target_envoy_image_version = "image-1234"
        self.current_envoy_image_version = None
        self.ep_min_count = 2
        self.ep_max_count = 5
        self.ep_autosclale_cpu_target = 0.7
        self.enable_tls_term_on_ep = 1
        self.migrate_ep_status = 'DELETE_STAGE2'
        self.node_type = node_type
        self.alt_node_type = -1
        self.enable_global_nlb = 0
        self.service_timeout = 0
        self.cnat_enabled = "0"
        self.cnat_public_ips = None
        self.cnat_min_count = 1
        self.cnat_max_count = 16
        self.cnat_vm_min_port = 32
        self.cnat_vm_max_port = 32768
        self.cnat_exclsv_enabled = 0
        self.cnat_ntuple_hash = '0'
        self.cnat_mon_enabled = '0'
        self.ipCidrRange = '/32'
        self.is_inst_autoscaled=1
        self.cnat_autoscaled_status = 'TRIGGERED_CNAT_LAMBDA'
        if compute_region_id == 222:
            self.enable_tls_term_on_ep = 0
            self.service_timeout = 300
        if compute_region_id == 289:
            self.ep_min_count = 0
            self.ep_max_count = 0
            self.ep_autosclale_cpu_target = 0

    def get_entry(self):
        if self.custid == 1212:
            return False
        return True

    def get_param(self, val):
        if val == "salt_profile":
            return self.salt_profile
        elif val == "ep_min_count":
            return self.ep_min_count
        elif val == "ep_max_count":
            return self.ep_max_count
        elif val == "ep_autosclale_cpu_target":
            return self.ep_autosclale_cpu_target
        elif val == "enable_tls_term_on_ep":
            return self.enable_tls_term_on_ep
        elif val == "custid":
            return self.custid
        elif val == "target_envoy_image_version":
            return self.target_envoy_image_version
        elif val == "current_envoy_image_version":
            return self.current_envoy_image_version
        elif val == "enable_global_nlb":
            return self.enable_global_nlb
        elif val == "node_type":
            return self.node_type
        elif val == "service_timeout":
            return self.service_timeout
        elif val == "cnat_enabled":
            return self.cnat_enabled
        elif val == "cnat_public_ips":
            return self.cnat_public_ips
        elif val == "cnat_min_count":
            return self.cnat_min_count
        elif val == "cnat_max_count":
            return self.cnat_max_count
        elif val == "cnat_vm_min_port":
            return self.cnat_vm_min_port
        elif val == "cnat_vm_max_port":
            return self.cnat_vm_max_port
        elif val == "cnat_exclsv_enabled":
            return self.cnat_exclsv_enabled
        elif val == "cnat_ntuple_hash":
            return self.cnat_ntuple_hash
        elif val == "ipCidrRange":
            return self.ipCidrRange
        elif val == "cnat_autoscaled_status":
            return self.cnat_autoscaled_status
        elif val == "is_inst_autoscaled":
            return self.is_inst_autoscaled
        elif val == "cnat_mon_enabled":
            return self.cnat_mon_enabled
        else:
            return None

    def set_param(self, var, value):
        if var == 'migrate_ep_status':
            self.migrate_ep_status = value
        elif var == 'enable_global_nlb':
            self.enable_global_nlb = value
        elif var == "ep_min_count":
            self.ep_min_count = value
        elif var == "current_envoy_image_version":
            self.current_envoy_image_version = value
        elif var == "ep_max_count":
            self.ep_max_count = value
        elif var == "ep_autosclale_cpu_target":
            self.ep_autosclale_cpu_target = value
        elif var == "service_timeout":
            self.service_timeout = value
        elif var == "cnat_enabled":
            self.cnat_enabled = value
        elif var == "cnat_public_ips":
            self.cnat_public_ips = value
        elif var == "cnat_min_count":
            self.cnat_min_count = value
        elif var == "cnat_max_count":
            self.cnat_max_count = value
        elif var == "cnat_vm_min_port":
            self.cnat_vm_min_port = value
        elif var == "cnat_vm_max_port":
            self.cnat_vm_max_port = value
        elif var == "cnat_exclsv_enabled":
            self.cnat_exclsv_enabled = value
        elif var == "cnat_ntuple_hash":
            self.cnat_ntuple_hash = value
        elif var == "ipCidrRange":
            self.ipCidrRange = value
        elif var == "cnat_autoscaled_status":
            self.cnat_autoscaled_status = value
        elif var == "is_inst_autoscaled":
            self.is_inst_autoscaled = value
        elif var == "cnat_mon_enabled":
            self.cnat_mon_enabled = value
        else:
            self.salt_profile = value

    def save_salt_profile(self):
        pass

    def query_entries_in_progress(self):
        return True, [[153, -1, 16, 200, 'gcp', {}, "IN_PROGRESS", "test-log-a", "2023-09-28 16:27:51"]]

    def save(self):
        pass

class DB:
    def __init__(self):
        self.logger = logger

class ExplicitProxyTenantInfoModel:
    def __init__(self, dbh, tenant_id, enable_tls_term_on_ep=0):
        self.dbh = dbh
        self.tenant_id = tenant_id
        self.envoy_proxy_secret_id = "customer1011453056"
        self.enable_tls_term_on_ep = enable_tls_term_on_ep

    def get_param(self, field):
        if field == "envoy_proxy_secret_id":
            return self.envoy_proxy_secret_id
        if field == "enable_tls_term_on_ep":
            return self.enable_tls_term_on_ep

    def get_entry(self):
        return True


class CustomerModel:
    def __init__(self, custid=None, dbh=None, acct_id=None):
        self.id = custid
        self.acct_id = acct_id
        self.dbh = dbh
        self.acct_id = "1011453056"
        self.eproxy_image_version = "pa-proxyvm-3-2-1-1-20221101203853"
        self.binhp_eproxy_image_version = "pa-nhproxyvm-4-2-0-0-e489ba293e-20230708163007"
        self.uda_nhproxy_image_version = "pa-nhproxyvm-4-2-0-0-f490ba908e-20230808203007"
        self.project_id = "cust-gpcs-mwernosr5hj1l6v29jv3"
        self.svc_acct = "<EMAIL>"
        self.super_acct_id = "1011453056"
        self.support_acct_id = "123456"
        self.key_val = {"id" : custid, "acct_id": acct_id, "eproxy_image_version" : "pa-proxyvm-3-2-1-1-20221101203853",
                        "project_id" : "cust-gpcs-mwernosr5hj1l6v29jv3",
                        "svc_acct" : "<EMAIL>",
                        "super_acct_id" : "1011453056", "subnet_end": "172.16.255.254",
                        "tenant_service_group": "11439239183", "gpname": "test.gpname", "support_acct_id": "123456"}

    def get_param(self, key):
        return self.key_val.get(key, None)

    def set_param(self, key, val):
        self.key_val[key] = val
        return self.key_val[key]



class OrchCfgModel():
    def __init__(self, dbh=None):
        self.dbh = dbh
        self.valid = True
        self.fields = cfg

class MockPinnedInstanceUpgradeModel():
    def __init__(self, dbh, instance_id=None, shard_id=None, replica_size=None):
        self.dbh = dbh
        self.instance_id = 1
        self.shard_id = 1
        self.replica_size = 1
        self.isvalid = True

    def query_all_stage4_entries(self):
        return True, [[0]]

    def set_param(self,input1, input2):
        return

    def save(self):
        return

    def save_stage1(self):
        return 1
    def save_stage2(self):
        return 1
    def save_stage3(self):
        return 1
    def save_stage4(self):
        return 1
    def save_stage5(self):
        return 1

    def get_param(self, input):
        if input=="delete_after":
            return 0
        elif input=="two_phase_upgrade_invocation":
            return 1
        else:
            return 1

class MockPinnedInstanceUpgradeModelNoBypass():
    def __init__(self, dbh, instance_id=None, shard_id=None, replica_size=None):
        self.dbh = dbh
        self.instance_id = 1
        self.shard_id = 1
        self.replica_size = 1
        self.isvalid = True
    def query_all_stage4_entries(self):
        return 2, [[0],[1]]
    def set_param(self,input1, input2):
        return
    def save(self):
        return
    def save_stage4(self):
        return 1
    def get_param(self, input):
        if input=="delete_after":
            return 0
        elif input=="bypass_commit_check":
            return 0
        elif input=="two_phase_upgrade_invocation":
            return 1
        else:
            return 1

class MockPinnedInstanceUpgradeModelFail():
    def __init__(self, dbh, instance_id=None, shard_id=None, replica_size=None):
        self.dbh = dbh
        self.instance_id = 1
        self.shard_id = 1
        self.replica_size = 1
        self.isvalid = True
    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def save(self):
        return
    def save_stage4(self):
        return 0
    def get_param(self, input):
        if input=="delete_after":
            return 0
        else:
            return 1

class MockInstanceModel():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0
    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def save(self):
        return
    def save_stage4(self):
        return 1
    def delete(self, dbh):
        return
    def get_sites(self,dbh,transition):
        return [111]
    def bind(dbh, custid, node_type, node_id, is_new, custnode):
        return True
    def get_param(self, input):
        return 1

class MockInstanceModelFail():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0
    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def save(self):
        return
    def save_stage4(self):
        return 0
    def delete(self, dbh):
        return
    def get_sites(self,dbh,transition):
        return [111]
    def bind(dbh, custid, node_type, node_id, is_new, custnode):
        return True
    def get_param(self, input):
        return 1
    def update_column_upgrade_status(self,dbh,input):
        return 1

class MockInstanceModelNoID():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0
    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def save(self):
        return
    def delete(self, dbh):
        return
    def get_sites(self,dbh,transition):
        return [111]
    def bind(dbh, custid, node_type, node_id, is_new, custnode):
        return True
    def save_stage4(self):
        return 0
    def get_param(self, input):
        if input=='id':
            return
        return 1
    def update_column_upgrade_status(self,dbh,input):
        return 1

class MockInstanceModelStop():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0
    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def save(self):
        return
    def save_stage4(self):
        return 1
    def get_param(self, input):
        if input=="vm_status":
            return 0
        return 1
    def update_column_upgrade_status(self,dbh,input):
        return 1

class MockInstanceModelNoBypass():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0
    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def save(self):
        return
    def save_stage4(self):
        return 1
    def check_commit_all(self):
        return 1, ""
    def get_param(self, input):
        if input=="bypass_commit_check":
            return 0
        return 1
    def update_column_upgrade_status(self,dbh,input):
        return 1

class MockInstanceModelCommitFails():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0
    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def save(self):
        return
    def save_stage4(self):
        return 1
    def check_commit_all(self):
        return 2, "fail_msg"
    def get_param(self, input):
        return 1
    def update_column_upgrade_status(self,dbh,input):
        return 1

class MockPinnedInstanceUpgradeModelTwoPhaseUpgrade():
    def __init__(self, dbh, instance_id=None, shard_id=None, replica_size=None):
        self.dbh = dbh
        self.instance_id = 1
        self.shard_id = 1
        self.replica_size = 1
        self.isvalid = True

    def query_all_stage4_entries(self):
        return True, [[0]]

    def set_param(self,input1, input2):
        return

    def save(self):
        return

    def save_stage4(self):
        return 1

    def save_stage6(self):
        return 1
    
    def update_all_cust_region_entries(self, param, val, exclude_self=False):
        return 1
    
    def get_param(self, input):
        if input == "delete_after":
            return 100
        elif input == "two_phase_upgrade_invocation":
            return 1
        elif input == 'upgrade_status':
            return 'COMPLETED-PRE-UPGRADE'
        elif input == 'delete_old_time':
            return int(time.time()) - 30
        elif input == 'nat_dummy_upgrade_max_time':
            return int(time.time()) - 20
        elif input == 'nat_dummy_upgrade_done_or_not_needed':
            return 0
        elif input == 'instance_region_id':
            return 214
        elif input == 'preserve_nat_ip':
            return 1
        else:
            return 1

    def query_all_stage5_entries(self):
        return (1, [[115]])

    def query_all_stage6_entries(self):
        return (1, [[115, 116]])
    
    def save_stage5(self):
        return 1

class MockPinnedInstanceUpgradeModelTwoPhaseUpgradeStuckInR53():
    def __init__(self, dbh, instance_id=None, shard_id=None, replica_size=None):
        self.dbh = dbh
        self.instance_id = 1
        self.shard_id = 1
        self.replica_size = 1
        self.two_phase_counter = 1
        self.delete_after = 100
        self.isvalid = True

    def query_all_stage4_entries(self):
        return True, [[0]]

    def set_param(self,input1, input2):
        if input1 == "two_phase_upgrade_invocation":
            self.two_phase_counter = input2
        elif input1 == "delete_after":
            self.delete_after = input2
        return

    def save(self):
        return
    def save_stage1(self):
        return 1
    def save_stage4(self):
        return 1

    def get_param(self, input):
        if input == "delete_after":
            return self.delete_after
        elif input == "two_phase_upgrade_invocation":
            return self.two_phase_counter
        elif input == 'upgrade_status':
            return 'READY_TO_UPGRADE'
        else:
            return 1

    def query_all_stage5_entries(self):
        return (1, [[115]])

    def save_stage5(self):
        return 1
    def save_r53_retry(self):
        return 1

class MockPinnedInstanceUpgradeModelTwoPhaseUpgradeR53RetryLimitReached():
    def __init__(self, dbh, instance_id=None, shard_id=None, replica_size=None):
        #This two_phase_counter starts at 11, so skips the retry steps,
        #since retry scenario is covered by model above
        self.dbh = dbh
        self.instance_id = 1
        self.shard_id = 1
        self.replica_size = 1
        self.two_phase_counter = 11
        self.delete_after = 100
        self.isvalid = True

    def query_all_stage4_entries(self):
        return True, [[0]]

    def set_param(self,input1, input2):
        if input1 == "two_phase_upgrade_invocation":
            self.two_phase_counter = input2
        elif input1 == "delete_after":
            self.delete_after = input2
        return

    def save(self):
        return
    def save_stage1(self):
        return 1
    def save_stage4(self):
        return 1

    def get_param(self, input):
        if input == "delete_after":
            return self.delete_after
        elif input == "two_phase_upgrade_invocation":
            return self.two_phase_counter
        elif input == 'upgrade_status':
            return 'READY_TO_UPGRADE'
        else:
            return 1

    def query_all_stage5_entries(self):
        return (1, [[115]])

    def save_stage5(self):
        return 1
    def save_r53_retry(self):
        return 1
   

class MockInstanceModelTwoPhaseUpgradePreUpgradeSuccess():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0

    def query_all_stage4_entries(self):
        return True, [[0]]

    def set_param(self,input1, input2):
        return

    def save(self):
        return

    def save_stage4(self):
        return 1

    def delete(self, dbh):
        return

    def get_sites(self,dbh,transition):
        return [111]

    def bind(dbh, custid, node_type, node_id, is_new, custnode):
        return True

    def get_param(self, key):
        if key == 'new_instance_id':
            return '118'
        elif key == 'id':
            return '118'
        elif key == 'upgrade_status':
            return 'COMPLETED-PRE-UPGRADE'

class MockInstanceModelTwoPhaseUpgradePreUpgradeFailure():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0

    def query_all_stage4_entries(self):
        return True, [[0]]

    def set_param(self,input1, input2):
        return

    def save(self):
        return

    def save_stage4(self):
        return 1

    def delete(self, dbh):
        return

    def get_sites(self,dbh,transition):
        return [111]

    def bind(dbh, custid, node_type, node_id, is_new, custnode):
        return True

    def get_param(self, key):
        if key == 'new_instance_id':
            return '118'
        elif key == 'id':
            return '118'
        elif key == 'upgrade_status':
            return 'READY-TO-UPGRADE'

class MockInstanceModelTwoPhaseUpgradeCompleted():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0

    def query_all_stage4_entries(self):
        return True, [[0]]

    def set_param(self,input1, input2):
        return

    def save(self):
        return

    def save_stage4(self):
        return 1

    def delete(self, dbh):
        return

    def get_sites(self,dbh,transition):
        return [111]

    def bind(dbh, custid, node_type, node_id, is_new, custnode):
        return True

    def save_stage5(self):
        return True

    def get_param(self, key):
        if key == 'new_instance_id':
            return '118'
        elif key == 'id':
            return '118'
        elif key == 'upgrade_status':
            return 'COMPLETED-UPGRADE'

class RegionMasterModel():
    def __init__(self, dbh, cloud_provider, edge_location_region_id):
        self.dbh = dbh
        self.edge_location_region_id = edge_location_region_id
        self.cloud_provider = cloud_provider

    def get_param(self, field):
        if field == "cloud_provider":
            return self.cloud_provider

    def get_cloud_type(self):
        if self.cloud_provider == 'gcp':
            return 2
        return 1

    def get_compute_region_native_name(self):
        if self.edge_location_region_id == 222:
            return 'singapore-g'
        return 'us-west-2'


class MockInstanceModelforDNS:
    def __init__(self, iid, dbh, is_dynamic_instance):
        self.iid = iid
        self.id = iid
        self.dbh = dbh
        self.dictval = {"id": iid, "node_type": 153, "is_dynamic_instance": is_dynamic_instance}
        self.dns_insert = 0

    def set_param(self, key, value):
        self.dictval[key] = value

    def get_param(self, key):
        return self.dictval.get(key, None)

    def process_dns_change_for_swg_proxy(self,action):
        self.dns_insert += 1
        return True

    def save(self):
        pass

class OrchCfgModel_v2():
    def __init__(self, dbh=None):
        self.dbh = dbh
        self.valid = True
        self.fields = cfg


class Boto3():
    def client(self, lambda_name, region_name):
        return Client()

class Client():
    def invoke(self, FunctionName, InvocationType, LogType, Payload):
        return cnat_resp

    def get_caller_identity(self):
        return {'Account': *********}


class BotoOutput:
    def read(self):
        return '''{
              "12345": {
                "ep_cnat_feature_flag": {
                  "type": "compatibility",
                  "value": 1,
                  "value_override": -1,
                  "feature": "ep_cnat_feature_flag",
                  "feature_arguments": "",
                  "err_msg": "Explicit Proxy Cloud NAT requires at least 5.1.0 Saas Agent, EProxy Outside Panos feature, 5.1.0 proxy ami, 10.2.4 ami"
                }
              }
            }'''

cnat_resp = {"Payload": BotoOutput()}

