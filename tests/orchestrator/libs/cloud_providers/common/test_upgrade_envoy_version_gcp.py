import sys
from unittest import TestCase
from unittest.mock import patch, MagicMock
import logging
logger = logging.getLogger()
import mock_imports_upgrade_ep_os
from mock_models import CustEpaasConfigModel, ExplicitProxyTenantInfoModel
from mock_region_model import RegionMasterModel, DB, Service
from libs.cloud_providers.common.upgrade_envoy_version_gcp import update_envoy_details_for_cust_epaas, \
    update_instance_template, create_new_template, process_completed_upgrade_envoy_stack, trigger_deployment_update
from mock_customer_model import CustomerModelGlobal
from mock_instance_model import InstanceManagement_GCP_Model, InstanceManagementModel
import json

inst_mgmt_gcp_rec = InstanceManagement_GCP_Model(DB(), 1234)
inst_mgmt_rec = InstanceManagementModel(DB(), 1234)

old_template = {
  "kind": "compute#instanceTemplate",
  "id": "9021955373948972797",
  "creationTimestamp": "2023-09-19T14:18:10.255-07:00",
  "name": "gpcs-proxy-it-us-west2-*********",
  "description": "",
  "properties": {
    "tags": {
      "items": [
        "swg-proxy",
        "route-to-ilb-us-west2-*********"
      ]
    },
    "machineType": "e2-standard-4",
    "canIpForward": True,
    "networkInterfaces": [
      {
        "kind": "compute#networkInterface",
        "network": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/global/networks/gpcs-vpc-dp-*********",
        "subnetwork": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/regions/us-west2/subnetworks/subnet-dp-us-west2-*********",
        "name": "nic0"
      },
      {
        "kind": "compute#networkInterface",
        "network": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/global/networks/gpcs-vpc-mgmt-*********",
        "subnetwork": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/regions/us-west2/subnetworks/subnet-mgmt-us-west2-*********",
        "name": "nic1"
      }
    ],
    "disks": [
      {
        "kind": "compute#attachedDisk",
        "type": "PERSISTENT",
        "mode": "READ_WRITE",
        "deviceName": "boot",
        "index": 0,
        "boot": True,
        "initializeParams": {
          "sourceImage": "https://www.googleapis.com/compute/v1/projects/image-gpcs-nonprod-01/global/images/pa-proxyvm-4-0-0-12-20230419215055",
          "diskSizeGb": "40"
        },
        "autoDelete": True
      }
    ],
    "metadata": {
      "kind": "compute#metadata",
      "fingerprint": "lBOcx7wPWKA=",
      "items": [
        {
          "key": "ssh-keys",
          "value": "admin:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r admin@SJCMACJ15HHTD8"
        },
        {
          "key": "mtls_certpkey",
          "value": "customer*********"
        },
        {
          "key": "secret_mgr_project",
          "value": "image-gpcs-nonprod-01"
        },
        {
          "key": "cloud_provider",
          "value": "2"
        },
        {
          "key": "upstream-proxyprotocol",
          "value": "v1"
        },
        {
          "key": "mgmt-interface-swap",
          "value": "enable"
        },
        {
          "key": "custid_int",
          "value": "16"
        },
        {
          "key": "custid",
          "value": "*********"
        },
        {
          "key": "custsuperid",
          "value": "*********"
        },
        {
          "key": "svc_acct",
          "value": "<EMAIL>"
        },
        {
          "key": "pacfgds",
          "value": "dev.cfgds.swg.panclouddev.com"
        },
        {
          "key": "pagks",
          "value": "dev.gks.swg.panclouddev.com"
        },
        {
          "key": "pacdls",
          "value": "dev.cdls.swg.panclouddev.com"
        },
        {
          "key": "patgs",
          "value": "dev.tgs.swg.panclouddev.com"
        },
        {
          "key": "mtls_ca",
          "value": "it_ca_cert"
        },
        {
          "key": "nodetype",
          "value": "153"
        },
        {
          "key": "dnsproxy",
          "value": "172.16.255.254"
        },
        {
          "key": "compute_region",
          "value": "us-west-201"
        },
        {
          "key": "tsgid",
          "value": ""
        }
      ]
    },
    "serviceAccounts": [
      {
        "email": "<EMAIL>",
        "scopes": [
          "https://www.googleapis.com/auth/cloud-platform"
        ]
      }
    ]
  },
  "selfLink": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/global/instanceTemplates/gpcs-proxy-it-us-west2-*********"
}


def get_ep_val():
    return {"envoy_image_version": "pa-proxyvm-3-2-1-1-**************",
            "project_id": "cust-gpcs-mwernosr5hj1l6v29jv3", "envoy_image_project": "image-gpcs-nonprod-01",
            "image_project": "image-gpcs-nonprod-01", "envoy_inital_cnt": "1", "envoy_min_cnt": "1",
            "envoy_max_cnt": "10", "envoy_traffic_port": "8080", "envoy_hc_port": "8887",
            "envoy_disc_size_gb": "40", "envoy_autosclale_cpu_target": "0.5", "migrate_ep_status": "COMPLETE",
            "user_data": "{\"ssh-keys\": \"admin:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r admin@SJCMACJ15HHTD8\", \"mtls_certpkey\": \"customer**********\", \"secret_mgr_project\": \"image-gpcs-nonprod-01\", \"cloud_provider\": 2, \"upstream-proxyprotocol\": \"v1\", \"mgmt-interface-swap\": \"enable\", \"custid_int\": 24, \"custid\": \"**********\", \"custsuperid\": \"**********\", \"svc_acct\": \"<EMAIL>\", \"pacfgds\": \"dev.cfgds.swg.panclouddev.com\", \"pagks\": \"dev.gks.swg.panclouddev.com\", \"pacdls\": \"dev.cdls.swg.panclouddev.com\", \"patgs\": \"dev.tgs.swg.panclouddev.com\", \"mtls_ca\": \"it_ca_cert\"}"
            }

class Eproxyperiodicupdate(TestCase):

    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.get_swg_proxy_ilb_ip_address',
           return_value=MagicMock())
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.update_swg_eproxy_instance_details',
           return_value=MagicMock())
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.CustEpaasConfigModel',
           return_value=CustEpaasConfigModel(DB(), 123, 220, 2))
    def test_update_envoy_details_for_cust_epaas(self, mock_cfg, mock_rmm, mock_cm):
        response = update_envoy_details_for_cust_epaas(DB(), 123, "test_project", 456, 220, DB(), {"sample": 123})
        self.assertTrue(response)

    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.get_global_cfg', return_value={})
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.RegionMasterModel',
           return_value=RegionMasterModel(200, DB()))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.CustomerModel',
           return_value=CustomerModelGlobal(custid=1, super_acct_id=None, acct_id=123456, logger=logger))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.CustEpaasConfigModel',
           return_value=CustEpaasConfigModel(dbh=DB(), custid=123, compute_region_id=220, cloud_provider='gcp'))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.ExplicitProxyTenantInfoModel',
           return_value=ExplicitProxyTenantInfoModel(dbh=DB(), tenant_id=1234))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.get_compute', return_value=Service())
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.swgproxy_bringup',
           return_value=MagicMock(add_salt_profile_for_ep_region=get_ep_val()))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.delete_template',return_value=MagicMock())
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.create_new_template', return_value=(True, old_template))
    def test_update_instance_template(self, mock_cfg, mock_rmm, mock_cm, mock_cep, mock_epti, mock_service, mock_swg,
                                      mock_dt, mock_ct):
        '''
        test gcp envoy upgrade
        '''
        self.maxDiff = None
        response = update_instance_template(DB(), 123, 220, 'gcp', 153, -1)
        self.assertEqual(response, ("gpcs-proxy-it-agp-us-west1-123456-v1", True))

    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.get_global_cfg', return_value={})
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.RegionMasterModel',
           return_value=RegionMasterModel(200, DB()))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.CustomerModel',
           return_value=CustomerModelGlobal(custid=1, super_acct_id=None, acct_id=123456, logger=logger))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.CustEpaasConfigModel',
           return_value=CustEpaasConfigModel(dbh=DB(), custid=123, compute_region_id=220, cloud_provider='gcp'))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.ExplicitProxyTenantInfoModel',
           return_value=ExplicitProxyTenantInfoModel(dbh=DB(), tenant_id=1234))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.get_compute', return_value=Service())
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.swgproxy_bringup',
           return_value=MagicMock(add_salt_profile_for_ep_region=MagicMock(get_ep_val()),
                                  get_gcp_cust_region_instance_params=MagicMock(return_value=[{"MachineType" : "e2-std-16"}])))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.delete_template', return_value=MagicMock())
    def test_create_instance_template(self, mock_cfg, mock_rmm, mock_cm, mock_cep, mock_epti, mock_service, mock_swg,
                                      mock_dt):
        '''
        test gcp envoy upgrade
        '''
        self.maxDiff = None
        userdata = {"certpkey": "customwer1234", "tsgid": 1234}
        userdatastr = json.dumps(userdata)
        ep_salt_profile = {"user_data" : userdatastr}
        ep_salt_profile = json.dumps(ep_salt_profile)
        response = create_new_template(DB(), 153, -1, 24, 214, old_template, "test124",
                         "template-123", ep_salt_profile)
        self.assertEqual(response[1]["properties"]["metadata"]["items"],
                         [{'key': 'certpkey','value': 'customwer1234'},{'key': 'tsgid', 'value': '1234'},
                          {'key': 'machineType', 'value': 'e2-std-16'}])



    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.get_swg_proxy_ilb_ip_address',
           return_value=MagicMock())
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.update_swg_eproxy_instance_details',
           return_value=MagicMock())
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.CustEpaasConfigModel',
           return_value=CustEpaasConfigModel(DB(), 123, 220, 2))
    def test_update_envoy_details_for_cust_epaas(self, mock_cfg, mock_rmm, mock_cm):
        response = update_envoy_details_for_cust_epaas(DB(), 123, "test_project", 456, 220, DB(), {"sample": 123})
        self.assertTrue(response)

    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.get_global_cfg', return_value={})
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.RegionMasterModel',
           return_value=RegionMasterModel(200, DB()))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.CustomerModel',
           return_value=CustomerModelGlobal(custid=1, super_acct_id=None, acct_id=123456, logger=logger))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.CustEpaasConfigModel',
           return_value=None)
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.ExplicitProxyTenantInfoModel',
           return_value=ExplicitProxyTenantInfoModel(dbh=DB(), tenant_id=1234))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.get_compute', return_value=Service())
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.swgproxy_bringup',
           return_value=MagicMock(add_salt_profile_for_ep_region=get_ep_val()))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.delete_template',return_value=MagicMock())
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.create_new_template', return_value=MagicMock())
    def test_update_instance_template_no_exist(self, mock_cfg, mock_rmm, mock_cm, mock_cep, mock_epti, mock_service, mock_swg,
                                      mock_dt, mock_ct):
        '''
        test gcp envoy upgrade
        '''
        self.maxDiff = None
        response = update_instance_template(DB(), 123, 220, 'gcp', 173, 49)
        self.assertEqual(response, (None, False))



    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.get_global_cfg', return_value={})
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.RegionMasterModel',
           return_value=RegionMasterModel(200, DB()))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.CustomerModel',
           return_value=CustomerModelGlobal(custid=1, super_acct_id=None, acct_id=123456, logger=logger))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.CustEpaasConfigModel',
           return_value=CustEpaasConfigModel(dbh=DB(), custid=123, compute_region_id=220, cloud_provider='gcp'))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.ExplicitProxyTenantInfoModel',
           return_value=ExplicitProxyTenantInfoModel(dbh=DB(), tenant_id=1234))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.get_compute', return_value=Service())
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.swgproxy_bringup',
           return_value=MagicMock(add_salt_profile_for_ep_region=get_ep_val()))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.delete_template', return_value=MagicMock())
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.create_new_template', return_value=(True, old_template))
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.check_gcp_mig_status', return_value=True)
    def test_process_upgrade_instance_template(self, mock_cfg, mock_rmm, mock_cm, mock_cep, mock_epti, mock_service, mock_swg,
                                      mock_dt, mock_ct, mock_pfx):
        '''
        test gcp envoy upgrade
        '''
        self.maxDiff = None
        dbh = DB()
        dbh.logger = MagicMock()
        response = process_completed_upgrade_envoy_stack(dbh, None, None)
        self.assertEqual(response, {16: [200]})

    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.InstanceManagement_GCP_Model',
           return_value=inst_mgmt_gcp_rec)
    @patch('libs.cloud_providers.common.upgrade_envoy_version_gcp.InstanceManagementModel',
           return_value=inst_mgmt_rec)
    def test_trigger_deployment_update_template(self, mock_inst_gcp, mock_inst):
        '''
        test gcp envoy upgrade
        '''
        self.maxDiff = None
        success = trigger_deployment_update(DB(), 24, 220, 153, 'gcp')
        self.assertEqual(success, True)
        self.assertEqual(inst_mgmt_gcp_rec.get_param("trigger_update"), 10)






