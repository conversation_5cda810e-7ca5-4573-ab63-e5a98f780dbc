import sys
import pytest
from unittest import TestCase
from unittest.mock import patch, MagicMock
import logging
logger = logging.getLogger()
import mock_imports_upgrade_ep_os
from mock_region_model import RegionMasterModel, DB, Service, mock_hc_response
from mock_customer_model import CustomerModelGlobal
from mock_models import CustEpaasConfigModel
from mock_instance_model import InstanceManagement_GCP_Model, InstanceModel, InstanceManagementModel
from libs.cloud_providers.common.upgrade_ep_envoy_outside_panos import revert_to_old_arch, get_wait_time,\
    delete_stage2_resources, call_dns_update, upgrade_ep_envoy_outside_panos, delete_stage3_resources
from libs.cloud_providers.common.delete_gcp_resources import delete_mig_group, delete_healthcheck

cust_ep_cfg1 = CustEpaasConfigModel(DB(), 123, 220, 'GCP', node_type=153, alt_node_type=-1)
cust_ep_cfg2 = CustEpaasConfigModel(DB(), 123, 220, 'GCP', node_type=153, alt_node_type=-1)

class UpgradeEpTests(TestCase):

    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    @patch('libs.cloud_providers.common.delete_gcp_resources.get_global_cfg', return_value={'envoy_hc_port' : 80})
    @patch('libs.cloud_providers.common.delete_gcp_resources.RegionMasterModel',
           return_value=RegionMasterModel(200, DB()))
    @patch('libs.cloud_providers.common.delete_gcp_resources.CustomerModel',
           return_value=CustomerModelGlobal(custid=1, super_acct_id=None, acct_id=123456, logger=logger))
    @patch('libs.cloud_providers.common.delete_gcp_resources.get_compute', return_value=Service())
    def test_delete_healthcheck_success(self, mock_cfg, mock_rmm, mock_cm, mock_service):
        '''
        test delete healthcheck successfully
        '''
        self.maxDiff = None
        success, response = delete_healthcheck(DB(), 200, 123456, 1)
        self.assertEqual(response, mock_hc_response)


    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.get_global_cfg', return_value=MagicMock())
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.CustNodeModel',
           return_value=MagicMock(instance1_id=456))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.get_cust_topology_record',
           return_value=[[127]])
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.INST',
           return_value=InstanceModel(123, DB()))
    def test_revert_to_old_arch(self, mock_cfg, mock_rmm, mock_cm, mock_im):
        '''
            Test reverting to old arch
        '''
        response = revert_to_old_arch(DB(), MagicMock(), 123, 220, '{"127": 456}', InstanceManagement_GCP_Model(DB(), 121))
        self.assertEqual(response, True)

    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.get_global_cfg', return_value=MagicMock())
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.RegionMasterModel',
           return_value=RegionMasterModel(201, DB()))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.CustomerModel',
           return_value=CustomerModelGlobal(custid=1, super_acct_id=None, acct_id=123456, logger=logger))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.get_compute', return_value=MagicMock())

    def test_delete_mig_resource(self, mock_cfg, mock_rmm, mock_cm, mock_service):
        '''
            test handling delete load balancer incase if failure
        '''
        response = delete_mig_group(logger, MagicMock(), "test123", "us-west1", 1234)
        self.assertEqual(response, True)

    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.get_global_cfg', return_value={})
    def test_get_wait_time_default(self, mock_cfg):
        '''
            test getting  wait time from cfg when value is not present in cfg
        '''
        response = get_wait_time(DB())
        self.assertEqual(response, 3600)

    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.get_global_cfg',
           return_value={"ep_migration_wait_time": "1200"})
    def test_get_wait_time_from_cfg(self, mock_cfg):
        '''
            test getting  wait time from cfg when value is present in cfg
        '''
        response = get_wait_time(DB())
        self.assertEqual(response, 1200)

    @patch('libs.cloud_providers.common.delete_gcp_resources.get_global_cfg', return_value=MagicMock())
    @patch('libs.cloud_providers.common.delete_gcp_resources.RegionMasterModel',
           return_value=RegionMasterModel(201, DB()))
    @patch('libs.cloud_providers.common.delete_gcp_resources.CustomerModel',
           return_value=CustomerModelGlobal(custid=1, super_acct_id=None, acct_id=123456, logger=logger))
    @patch('libs.cloud_providers.common.delete_gcp_resources.get_compute', return_value=Service())
    def test_delete_healthcheck_exception(self, mock_cfg, mock_rmm, mock_cm, mock_service):
        '''
            test handling delete healthcheck incase if failure
        '''
        success, response = delete_healthcheck(DB(), 123456, 201, 0)
        self.assertEqual(response, None)

    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.CustEpaasConfigModel',
           return_value=CustEpaasConfigModel(DB(), 123456, 200, 'gcp'))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.RegionMasterModel',
           return_value=RegionMasterModel(200, DB()))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.CustomerModel',
           return_value=CustomerModelGlobal(custid=1, super_acct_id=None, acct_id=123456, logger=logger))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.get_compute', return_value=Service())
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.delete_backend_service',
           return_value=True)
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.delete_mig_group', return_value=True)
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.release_all_ip_to_customer_ip_pool',
           return_value=1)
    def test_delete_stage2_resources(self, mock_cfg, mock_rmm, mock_cm, mock_service, mock_f1,
                                                        mock_f2, mock_f3):
        '''
           successfully release old nlb ip
        '''
        self._caplog.set_level(logging.INFO)
        stage2_records = [[153, -1, 123456, 200, 'gcp', {}, 'DELETE_STAGE2', {245 : 112}, '1BCDEF', '04-04-23 10:10:10']]
        success = delete_stage2_resources(stage2_records, DB(), Service())
        messages = [i.message for i in self._caplog.records]
        self.assertIn("Successfully deleted backend service and mig group for custid 123456 and region 200", messages)
        self.assertEqual(success, None)

    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.CustEpaasConfigModel',
           return_value=CustEpaasConfigModel(DB(), 123456, 200, 'gcp'))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.RegionMasterModel',
           return_value=RegionMasterModel(200, DB()))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.CustomerModel',
           return_value=CustomerModelGlobal(custid=1, super_acct_id=None, acct_id=123456, logger=logger))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.get_compute', return_value=Service())
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.delete_healthcheck',
           return_value=True)
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.delete_template', return_value=False)
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.call_dns_update', return_value=True)
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.release_all_ip_to_customer_ip_pool',
           return_value=1)
    def test_delete_stage3_resources_release_ip(self, mock_cfg, mock_rmm, mock_cm, mock_service, mock_f1,
                                                      mock_f2, mock_f3, mock_f4):
        '''
            releasing old nlb ip
        '''
        self._caplog.set_level(logging.INFO)
        stage3_records = [[153, -1, 123456, 200, 'gcp', {}, 'DELETE_STAGE3', {245: 112}, '1BCDEF', '04-04-23 10:10:10']]
        success = delete_stage3_resources(stage3_records, DB(), Service())
        messages = [i.message for i in self._caplog.records]
        self.assertIn("Calling dns update function to update NS1 with new nlb ip", messages)
        self.assertIn("Successfully released old nlb ip for custid 123456 and region 200", messages)
        self.assertEqual(success, None)

    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.INST', return_value=InstanceModel(123, DB()))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.get_primary_instance', return_value=123)
    def test_call_dns_update(self, mock_instance, mock_id):
        '''
           insert dns entry in dns_updates_manager
        '''
        self._caplog.set_level(logging.ERROR)
        success = call_dns_update(DB(), 11223344, 120, 153)
        messages = [i.message for i in self._caplog.records]
        self.assertEqual(success, True)

    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.get_global_cfg', return_value=MagicMock(eproxy_image_version="orch_cfg_ep_version"))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.CustomerModel',
           return_value=CustomerModelGlobal(custid=123, super_acct_id=21234, acct_id=21234))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.InstanceManagement_GCP_Model',
           return_value=InstanceManagement_GCP_Model(DB(), 121))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.instmgmt',
           return_value=InstanceManagementModel(DB(), 1234))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.CustEpaasConfigModel',
           return_value=cust_ep_cfg1)
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.swgproxy_bringup',
           return_value=MagicMock())
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.gcp_instance_mgmt_utils',
           return_value=MagicMock())
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.BringupGCPHandler',
           return_value=MagicMock(build_swg_proxy_stack_request=MagicMock(return_value={})))
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.get_stack_fingerprint',
           return_value="test_print")
    @patch('libs.cloud_providers.common.upgrade_ep_envoy_outside_panos.NODE_TYPE_SWG_PROXY',
           return_value=153)
    def test_upgrade_ep_os_envoy_version_from_cust_master(self, mock_cfg, mock_cm, mock_igm, mock_im, mock_cep,
                                                          mock_swg, mock_gcp, mock_bgcp, mock_gsf, mnode):
        '''
            Test reverting to old arch
        '''
        response = upgrade_ep_envoy_outside_panos(DB(), 123, 220, 'gcp', 153)
        self.assertEqual(response, True)
        self.assertEqual(cust_ep_cfg1.get_param("current_envoy_image_version"), "customer_ep_version")




