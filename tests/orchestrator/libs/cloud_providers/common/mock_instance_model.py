import logging
from utils.Exceptions import InvalidInstanceIdException, InvalidInterfaceIpv6ListException, InvalidInterfaceIpv6ListJsonException, DbUpdateException

logger = logging.getLogger()
from libs.common.shared.sys_utils import NODE_TYPE_GP_GATEWAY, NODE_TYPE_NLB_INSTANCE

class InstanceModel:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        pass

    def get_param(self, key):
        if key == "salt_profile":
            return {
                "Zone": "us-west1-a",
                "InstanceName": "swgproxy-456-fw",
                "AcctId": 1011453056
            }

    def cleanup(self):
        pass

    def unbind(self):
        pass

    def process_dns_change_for_swg_proxy(self, action):
        if action == "insert":
            return True
        return False


class InstanceModelEgressIPListTest:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        pass

    def set_param(self, var, value):
        return

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "state":
            return 1
        elif key == "public_ip":
            return "10.0.0.0"
        elif key == "is_instance_behind_nlb":
            return 1
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        return None
    
    def update_column_interface_ip_list(self, interface_ip_list):
        return
    
    def update_column_interface_ipv6_list(self, interface_ipv6_list):
        return
    
    def requested_native_machine_type(self):
        return 0
    
    def save(self):
        return True

class InstanceModelEgressIPListTestNull:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        pass

    def set_param(self, var, value):
        return

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "state":
            return 1
        elif key == "public_ip":
            return "10.0.0.0"
        elif key == "is_instance_behind_nlb":
            return 1
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "interface_ip_list":
            return "None"
        return None
    
    def update_column_interface_ip_list(self, interface_ip_list):
        return

    def update_column_interface_ipv6_list(self, interface_ipv6_list):
        return
    
    def requested_native_machine_type(self):
        return 0
    
    def save(self):
        return True

class InstanceModelEgressIPListTestNone:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        pass

    def set_param(self, var, value):
        return

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "state":
            return 1
        elif key == "public_ip":
            return "10.0.0.0"
        elif key == "is_instance_behind_nlb":
            return 1
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "interface_ip_list":
            return None
        return None
    
    def update_column_interface_ip_list(self, interface_ip_list):
        return

    def update_column_interface_ipv6_list(self, interface_ipv6_list):
        return

    def requested_native_machine_type(self):
        return 0
    
    def save(self):
        return True
class InstanceModelEgressIPListTestEmptyDict:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        pass

    def set_param(self, var, value):
        return

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "state":
            return 1
        elif key == "public_ip":
            return "10.0.0.0"
        elif key == "is_instance_behind_nlb":
            return 1
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "interface_ip_list":
            return '{}'
        return None
    
    def update_column_interface_ip_list(self, interface_ip_list):
        return
    
    def update_column_interface_ipv6_list(self, interface_ipv6_list):
        return
    
    def requested_native_machine_type(self):
        return 0
    
    def save(self):
        return True

class InstanceModelInterfaceIpv6ListEmpty:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        pass

    def update_column_interface_ip_list(self, interface_ip_list):
        return
    
    def update_column_interface_ipv6_list(self, interface_ipv6_list):
        return
    
    def set_param(self, var, value):
        return
    
    def requested_native_machine_type(self, cft_dict=None):
        return
    
    def save(self):
        return True

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "state":
            return 1
        elif key == "public_ip":
            return "10.0.0.0"
        elif key == "public_ipv6":
            return "2ff0:1000:ab::2"
        elif key == "is_instance_behind_nlb":
            return 1
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "interface_ip_list":
            return '{}'
        elif key == "interface_ipv6_list":
            return '{}'
        return None
    
    def requested_native_machine_type(self):
        return 0
    
class InstanceModelInterfaceIpv6ListNotEmpty:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id
        self.interface_ipv6_list = '{"277": "2ff0:1500:ab::2"}'

    def get_entry(self):
        pass

    def update_column_interface_ip_list(self, interface_ip_list):
        return
    
    def update_column_interface_ipv6_list(self, interface_ipv6_list):
        self.interface_ipv6_list = interface_ipv6_list
        return
    
    def set_param(self, var, value):
        if var == "interface_ipv6_list":
            self.interface_ipv6_list = value
        return
    
    def requested_native_machine_type(self, cft_dict=None):
        return
    
    def save(self):
        return True

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "state":
            return 1
        elif key == "public_ip":
            return "10.0.0.0"
        elif key == "public_ipv6":
            return "2ff0:1000:ab::2"
        elif key == "is_instance_behind_nlb":
            return 1
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "interface_ip_list":
            return '{}'
        elif key == "interface_ipv6_list":
            return self.interface_ipv6_list
        return None
    
    def requested_native_machine_type(self):
        return 0

class InstanceModelUpgradeInstance:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id
        self.interface_ipv6_list = '{"277": "2ff0:1500:ab::2"}'

    def get_entry(self):
        pass

    def update_column_interface_ip_list(self, interface_ip_list):
        return
    
    def update_column_interface_ipv6_list(self, interface_ipv6_list):
        self.interface_ipv6_list = interface_ipv6_list
        return
    
    def set_param(self, var, value):
        if var == "interface_ipv6_list":
            self.interface_ipv6_list = value
        return
    
    def requested_native_machine_type(self, cft_dict=None):
        return
    
    def save(self):
        return True

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "state":
            return 1
        elif key == "public_ip":
            return "10.0.0.0"
        elif key == "public_ipv6":
            return "2ff0:1000:ab::2"
        elif key == "is_instance_behind_nlb":
            return 1
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "interface_ip_list":
            return '{}'
        elif key == "interface_ipv6_list":
            return self.interface_ipv6_list
        elif key == "upgrade_status":
            return "PRE-UPGRADE"
        return None
    
    def requested_native_machine_type(self):
        return 0
    
class InstanceModelUpdateInterfaceIpv6ListInstanceException:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id
        self.interface_ipv6_list = '{"277": "2ff0:1500:ab::2"}'

    def get_entry(self):
        pass

    def update_column_interface_ip_list(self, interface_ip_list):
        return
    
    def update_column_interface_ipv6_list(self, interface_ipv6_list):
        self.interface_ipv6_list = interface_ipv6_list
        raise InvalidInstanceIdException("test")
        return
    
    def set_param(self, var, value):
        if var == "interface_ipv6_list":
            self.interface_ipv6_list = value
        return
    
    def requested_native_machine_type(self, cft_dict=None):
        return
    
    def save(self):
        return True

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "state":
            return 1
        elif key == "public_ip":
            return "10.0.0.0"
        elif key == "public_ipv6":
            return "2ff0:1000:ab::2"
        elif key == "is_instance_behind_nlb":
            return 1
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "interface_ip_list":
            return '{}'
        elif key == "interface_ipv6_list":
            return self.interface_ipv6_list
        return None
    
    def requested_native_machine_type(self):
        return 0

class InstanceModelUpdateInterfaceIpv6ListInvalidException:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id
        self.interface_ipv6_list = '{"277": "2ff0:1500:ab::2"}'

    def get_entry(self):
        pass

    def update_column_interface_ip_list(self, interface_ip_list):
        return
    
    def update_column_interface_ipv6_list(self, interface_ipv6_list):
        self.interface_ipv6_list = interface_ipv6_list
        raise InvalidInterfaceIpv6ListException("test")
        return
    
    def set_param(self, var, value):
        if var == "interface_ipv6_list":
            self.interface_ipv6_list = value
        return
    
    def requested_native_machine_type(self, cft_dict=None):
        return
    
    def save(self):
        return True

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "state":
            return 1
        elif key == "public_ip":
            return "10.0.0.0"
        elif key == "public_ipv6":
            return "2ff0:1000:ab::2"
        elif key == "is_instance_behind_nlb":
            return 1
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "interface_ip_list":
            return '{}'
        elif key == "interface_ipv6_list":
            return self.interface_ipv6_list
        return None
    
    def requested_native_machine_type(self):
        return 0

class InstanceModelUpdateInterfaceIpv6ListInvalidJsonException:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id
        self.interface_ipv6_list = '{"277": "2ff0:1500:ab::2"}'

    def get_entry(self):
        pass

    def update_column_interface_ip_list(self, interface_ip_list):
        return
    
    def update_column_interface_ipv6_list(self, interface_ipv6_list):
        self.interface_ipv6_list = interface_ipv6_list
        raise InvalidInterfaceIpv6ListJsonException("test")
        return
    
    def set_param(self, var, value):
        if var == "interface_ipv6_list":
            self.interface_ipv6_list = value
        return
    
    def requested_native_machine_type(self, cft_dict=None):
        return
    
    def save(self):
        return True

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "state":
            return 1
        elif key == "public_ip":
            return "10.0.0.0"
        elif key == "public_ipv6":
            return "2ff0:1000:ab::2"
        elif key == "is_instance_behind_nlb":
            return 1
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "interface_ip_list":
            return '{}'
        elif key == "interface_ipv6_list":
            return self.interface_ipv6_list
        return None
    
    def requested_native_machine_type(self):
        return 0

class InstanceModelDbUpdateException:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id
        self.interface_ipv6_list = '{"277": "2ff0:1500:ab::2"}'

    def get_entry(self):
        pass

    def update_column_interface_ip_list(self, interface_ip_list):
        return
    
    def update_column_interface_ipv6_list(self, interface_ipv6_list):
        self.interface_ipv6_list = interface_ipv6_list
        raise DbUpdateException("test")
        return
    
    def set_param(self, var, value):
        if var == "interface_ipv6_list":
            self.interface_ipv6_list = value
        return
    
    def requested_native_machine_type(self, cft_dict=None):
        return
    
    def save(self):
        return True

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "state":
            return 1
        elif key == "public_ip":
            return "10.0.0.0"
        elif key == "public_ipv6":
            return "2ff0:1000:ab::2"
        elif key == "is_instance_behind_nlb":
            return 1
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "interface_ip_list":
            return '{}'
        elif key == "interface_ipv6_list":
            return self.interface_ipv6_list
        return None
    
    def requested_native_machine_type(self):
        return 0

class InstanceManagement_GCP_Model:
    def __init__(self, dbh, inst_mgmt_id):
        self.dbh = dbh
        self.inst_mgmt_id = inst_mgmt_id
        self.fields = {"trigger_update" : 0, "finished_update": 0,
                       "metadata": "{\"template-url\": \"cft/templatepath\"}"}

    def get_param(self, key):
        return self.fields.get(key, None)

    def set_param(self, key, value):
        self.fields[key] = value

    def save_stage1(self):
        pass

    def get_entry(self):
        return True

    def entry_exits(self):
        return True

    def query_select_entry_for_region(self, cloud_provider, custid, compute_region_id, node_type):
        return True, [1234]

class InstanceManagementModel:
    def __init__(self, dbh, inst_mgmt_id):
        self.dbh = dbh
        self.inst_mgmt_id = inst_mgmt_id
        self.fields = {"trigger_update" : 0, "finished_update": 0, "stack_name": "test-stack-01"}

    def get_param(self, key):
        return self.fields.get(key, None)

    def set_param(self, key, value):
        self.fields[key] = value

    def save_stage1(self):
        pass

    def get_entry(self):
        return True

    def get_alt_node_type(self):
        return -1

class InstanceModelReservedPublicIp:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        pass

    def set_param(self, var, value):
        return

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "state":
            return 1
        elif key == "compute_region_idx":
            return "703"
        elif key == "node_type":
            return NODE_TYPE_NLB_INSTANCE
        elif key == "public_ip":
            return "********"
        return None
