import sys
from unittest.mock import MagicMock

import mock_models
sys.modules["libs.cfg"] = MagicMock()
sys.modules["libs.common.shared.sys_utils"] = MagicMock(PROVIDER_UNDECIDED=0, NODE_TYPE_SWG_PROXY=153,
                                                        NODE_TYPE_BI_NH_PROXY=173, NODE_TYPE_UDA=174, PROVIDER_AWS=1)
sys.modules["libs.common.shared.utils"] = MagicMock(get_cdl_region_info=MagicMock(return_value="americas"))
sys.modules["libs.model.RegionMasterModel"] = MagicMock()
sys.modules["libs.model.custmodel"] = MagicMock()
sys.modules["libs.model.explicitProxyTenantInfoModel"] = mock_models
sys.modules["libs.model.instancemanagement_gcp_model"] = MagicMock()
sys.modules["libs.model.instancemanagementmodel"] = MagicMock()
sys.modules["libs.cloud_providers.gcp.instance_manager.gcp_instance_mgmt_utils"] = MagicMock()
sys.modules["libs.cloud_providers.gcp.instance_manager.gcp_bringup"] = MagicMock()
sys.modules["libs.model.custEpaasConfigModel"] = MagicMock()
sys.modules["libs.model.custnodemodel"] = MagicMock()
sys.modules["libs.model.instancemodel"] = MagicMock()
sys.modules["libs.cloud_providers.common.upgrade_envoy_version_gcp"] = MagicMock()
sys.modules["libs.model.orchcfgmodel"] = mock_models
sys.modules["libs.model.orchcfgmodel_v2"] = mock_models
sys.modules["libs.apis.region_master_api"] = MagicMock()
