import logging
logger = logging.getLogger()
class RegionMasterModel():

    def __init__(self, edge_location_region_id, dbh):
        self.dbh = DB()
        self.edge_location_region_id = edge_location_region_id



    def get_compute_region_native_name(self):
        if self.edge_location_region_id == 200:
            return "us-west1"
        return "us-west2"

    def get_entry(self):
        pass

class DB:
    def __init__(self):
        self.logger = logger

    def get_cursor(self):
        return Cursor()

    def cursorclose(self, var):
        pass

class Cursor():
    def execute(self, sql, params):
        return True


class Service:
    def __init__(self):
        pass

    def regionHealthChecks(self):
        return Operations()

    def instanceGroups(self):
        return instanceGroups()

    def instanceTemplates(self):
        return instanceTemplates()

    def regionInstanceGroupManagers(self):
        return regionInstanceGroupManagers()

    def sslCertificates(self):
        return sslCertificates()

    def targetSslProxies(self):
        return targetSslProxies()

    def services(self):
        return ApiRequest()

class Operations:
    def __init__(self):
        pass

    def delete(self, project, region, healthCheck):
        return Requests(region)

class Requests:
    def __init__(self, region):
        self.region = region

    def execute(self):
        if self.region == "us-west1":
            return mock_hc_response
        else:
            raise Exception(f"Error deleting health check for region {self.region}")


class instanceGroups:
    def __init__(self):
        pass

    def removeInstances(self, project, zone, instance_group, body):
        return RequestsInst(project, zone, instance_group, body)


class RequestsInst:
    def __init__(self, project, zone, instance_group, body):
        self.project = project
        self.zone = zone
        self.instance_group = instance_group
        self.body = body

    def execute(self):
        return mock_ri_response

class sslCertificates:
    def __init__(self):
        pass

    def list(self, project):
        return sslcertExecute("list", project)

    def delete(self, project, sslCertificate):
        return sslcertExecute("delete", project, sslCertificate)

class targetSslProxies:
    def __init__(self):
        pass

    def list(self, project):
        return target_proxy_execute(project)


class sslcertExecute:
    def __init__(self, operation, project, sslcert=None):
        self.project = project
        self.operation = operation
        self.sslcert = sslcert

    def execute(self):
        if self.operation == "list":
            return mock_ssl_response
        return "done"

class target_proxy_execute:
    def __init__(self, project):
        self.project = project

    def execute(self):
        return mock_tsp_response

class instanceTemplates:
    def __init__(self):
        pass

    def get(self, project, instanceTemplate):
        return instanceTemplates_request()

    def insert(self, project, body):
        return instanceTemplates_request()

class instanceTemplates_request:
    def __init__(self):
        pass

    def execute(self):
        return mock_template_response

class regionInstanceGroupManagers:
    def __init__(self):
        pass

    def patch(self, project, region, instanceGroupManager, body):
        return Instance_group_request()

class Instance_group_request:
    def __init__(self):
        pass

    def execute(self):
        return {}


class ApiRequest:
    def __init__(self):
        pass

    def get(self, name):
        return ServiceRequest(1)

    def enable(self, name):
        return ServiceRequest(2)


class ServiceRequest:
    def __init__(self, id):
        self.id = id

    def execute(self):
        if self.id == 1:
            return {'state': 'ENABLED'}
        if self.id == 2:
            return {'name': 'operation123'}





mock_ssl_response = {
  "kind": "compute#sslCertificateList",
  "id": "projects/testproject/global/sslCertificates",
  "items": [
    {
      "kind": "compute#sslCertificate",
      "id": "1362139290394068485",
      "creationTimestamp": "2023-03-31T12:47:54.368-07:00",
      "name": "gpcs-proxy-ssl-cert-asia-northeast1-*********-1",
      "selfLink": "https://www.googleapis.com/compute/v1/projects/test_project/global/sslCertificates/gpcs-proxy-ssl-cert-asia-northeast1-*********-1",
      "certificate": "testcert",
      "selfManaged": {
        "certificate": "testcert"
      },
      "type": "SELF_MANAGED",
      "subjectAlternativeNames": [
        "*.proxy.panclouddev.com",
        "proxy.panclouddev.com"
      ],
      "expireTime": "2024-04-16T15:40:48.000-07:00"
    },
    {
      "kind": "compute#sslCertificate",
      "id": "2460771377587666442",
      "creationTimestamp": "2023-03-27T13:47:33.971-07:00",
      "name": "gpcs-proxy-ssl-cert-asia-northeast1-*********-5",
      "selfLink": "https://www.googleapis.com/compute/v1/projects/test_project/global/sslCertificates/gpcs-proxy-ssl-cert-asia-northeast1-*********-5",
      "certificate": "testcert",
      "selfManaged": {
        "certificate": "testcert"
      },
      "type": "SELF_MANAGED",
      "subjectAlternativeNames": [
        "*.proxy.panclouddev.com",
        "proxy.panclouddev.com"
      ],
      "expireTime": "2024-04-16T15:40:48.000-07:00"
    }
  ],
  "selfLink": "https://www.googleapis.com/compute/v1/projects/testproject/global/sslCertificates"
}


mock_tsp_response = {
  "kind": "compute#targetSslProxyList",
  "id": "projects/testproject/global/targetSslProxies",
  "items": [
    {
      "kind": "compute#targetSslProxy",
      "id": "2770165587342507942",
      "creationTimestamp": "2023-03-27T13:49:13.177-07:00",
      "name": "gpcs-proxy-nlb1-asia-northeast1-*********-fw-0-target",
      "selfLink": "https://www.googleapis.com/compute/v1/projects/testproject/global/targetSslProxies/gpcs-proxy-nlb1-asia-northeast1-*********-fw-0-target",
      "service": "https://www.googleapis.com/compute/v1/projects/testproject/global/backendServices/gpcs-proxy-nlb1-asia-northeast1-*********-bs",
      "sslCertificates": [
        "https://www.googleapis.com/compute/v1/projects/testproject/global/sslCertificates/gpcs-proxy-ssl-cert-asia-northeast1-*********-1"
      ],
      "proxyHeader": "PROXY_V1"
    }
  ],
  "selfLink": "https://www.googleapis.com/compute/v1/projects/testproject/global/targetSslProxies"
}




mock_hc_response = {
  "kind": "compute#operation",
  "id": "5652076715878723898",
  "name": "operation-1670942677222-5efb6a8b74613-ed5d9bcb-94fc69d6",
  "operationType": "delete",
  "targetLink": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-2a4u4pc0equ1pqo1mwxx/regions/us-east1/healthChecks/gpcs-proxy-hc1-nlb-us-east1-298318906",
  "targetId": "47531933792841424",
  "status": "RUNNING",
  "user": "<EMAIL>",
  "progress": 0,
  "insertTime": "2022-12-13T06:44:37.832-08:00",
  "startTime": "2022-12-13T06:44:37.844-08:00",
  "selfLink": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-2a4u4pc0equ1pqo1mwxx/regions/us-east1/operations/operation-1670942677222-5efb6a8b74613-ed5d9bcb-94fc69d6",
  "region": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-2a4u4pc0equ1pqo1mwxx/regions/us-east1"
}

mock_ri_response ={
  "kind": "compute#operation",
  "id": "1263467828475147341",
  "name": "operation-1674087073505-5f292c556df80-6c9e5400-bfb51b7b",
  "zone": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/zones/southamerica-east1-a",
  "operationType": "compute.instanceGroups.removeInstances",
  "targetLink": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/zones/southamerica-east1-a/instanceGroups/gpcs-proxy-unmig-southamerica-east1-a-*********",
  "targetId": "2823305478277094145",
  "status": "RUNNING",
  "user": "<EMAIL>",
  "progress": 0,
  "insertTime": "2023-01-18T16:11:14.082-08:00",
  "startTime": "2023-01-18T16:11:14.105-08:00",
  "selfLink": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/zones/southamerica-east1-a/operations/operation-1674087073505-5f292c556df80-6c9e5400-bfb51b7b"
}

mock_template_response = {
  "kind": "compute#instanceTemplate",
  "id": "9033277118177015516",
  "creationTimestamp": "2023-04-12T17:09:23.609-07:00",
  "name": "gpcs-proxy-it-southamerica-east1-*********",
  "description": "",
  "properties": {
    "tags": {
      "items": [
        "swg-proxy",
        "route-to-ilb-southamerica-east1-*********"
      ]
    },
    "machineType": "e2-standard-4",
    "canIpForward": True,
    "networkInterfaces": [
      {
        "kind": "compute#networkInterface",
        "network": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/global/networks/gpcs-vpc-dp-*********",
        "subnetwork": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/regions/southamerica-east1/subnetworks/subnet-dp-southamerica-east1-*********",
        "name": "nic0"
      },
      {
        "kind": "compute#networkInterface",
        "network": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/global/networks/gpcs-vpc-mgmt-*********",
        "subnetwork": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/regions/southamerica-east1/subnetworks/subnet-mgmt-southamerica-east1-*********",
        "name": "nic1"
      }
    ],
    "disks": [
      {
        "kind": "compute#attachedDisk",
        "type": "PERSISTENT",
        "mode": "READ_WRITE",
        "deviceName": "boot",
        "index": 0,
        "boot": True,
        "initializeParams": {
          "sourceImage": "https://www.googleapis.com/compute/v1/projects/image-gpcs-nonprod-01/global/images/pa-proxyvm-4-0-0-8-20230320180944",
          "diskSizeGb": "40"
        },
        "autoDelete": True
      }
    ],
    "metadata": {
      "kind": "compute#metadata",
      "fingerprint": "_tLrNmZJHRU=",
      "items": [
        {
          "key": "ssh-keys",
          "value": "admin:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r admin@SJCMACJ15HHTD8"
        },
        {
          "key": "mtls_certpkey",
          "value": "customer*********"
        },
        {
          "key": "secret_mgr_project",
          "value": "image-gpcs-nonprod-01"
        },
        {
          "key": "cloud_provider",
          "value": "2"
        },
        {
          "key": "upstream-proxyprotocol",
          "value": "v1"
        },
        {
          "key": "mgmt-interface-swap",
          "value": "enable"
        },
        {
          "key": "custid_int",
          "value": "16"
        },
        {
          "key": "custid",
          "value": "*********"
        },
        {
          "key": "custsuperid",
          "value": "*********"
        },
        {
          "key": "svc_acct",
          "value": "<EMAIL>"
        },
        {
          "key": "pacfgds",
          "value": "dev.cfgds.swg.panclouddev.com"
        },
        {
          "key": "pagks",
          "value": "dev.gks.swg.panclouddev.com"
        },
        {
          "key": "pacdls",
          "value": "dev.cdls.swg.panclouddev.com"
        },
        {
          "key": "patgs",
          "value": "dev.tgs.swg.panclouddev.com"
        },
        {
          "key": "mtls_ca",
          "value": "it_ca_cert"
        },
        {
          "key": "nodetype",
          "value": "153"
        }
      ]
    },
    "serviceAccounts": [
      {
        "email": "<EMAIL>",
        "scopes": [
          "https://www.googleapis.com/auth/cloud-platform"
        ]
      }
    ]
  },
  "selfLink": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/global/instanceTemplates/gpcs-proxy-it-southamerica-east1-*********",
  "targetLink": "https://www.googleapis.com/compute/v1/projects/cust-gpcs-1iao398bdo27zwbyv04k/global/instanceTemplates/gpcs-proxy-it-southamerica-east1-*********"

}
