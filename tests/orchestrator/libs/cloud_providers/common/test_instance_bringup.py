import sys
import pytest
from unittest.mock import patch, MagicMock
import logging
import mock_imports_orchestrator
from mock_region_model import RegionMasterModel, DB, Service, mock_hc_response
import mock_instance_model as mock_model
from libs.cloud_providers.common.instance_bringup import instance_bringup_processing
from libs.common.shared.sys_utils import PROVIDER_GCP, PROVIDER_OCI
from libs.common.shared.sys_utils import NODE_TYPE_GP_GATEWAY
from libs.cloud_providers.common.exceptions import UnableToAllocatedReservedPublicIpException

class logger():
    def info(self, *args):
        pass

    def error(self, *args):
        pass

    def warn(self, *args):
        pass

class Mock_conn():
    def __init__(self):
        pass

    def close(self):
        pass

class Mock_cursor():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params:
            query = self.sql%self.params
        else:
            query = self.sql
        pass

    def fetchone(self):
        pass

class Mock_DBHandler():
    def __init__(self):
        self.logger = logging.getLogger('testlogger')
        self.conn = Mock_conn()

    def conn(self):
        return self.conn

    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return

class TestInstanceBringupProcessing:
    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelEgressIPListTest(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_populate_interface_ip_list_not_set(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_nlb_domain_details = "test-lb"
        ibp_obj._primary_interface_ip_list = "********"
        ibp_obj.process_instance_primary()
        assert "Updating primary interface IP list ******** for the instance" in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelEgressIPListTestNull(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_populate_interface_ip_list_is_null(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_nlb_domain_details = "test-lb"
        ibp_obj._primary_interface_ip_list = "********"
        ibp_obj._primary_public_ip_id = None
        ibp_obj.process_instance_primary()
        assert "Updating primary interface IP list ******** for the instance" in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelEgressIPListTestNone(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_populate_interface_ip_list_is_none(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_nlb_domain_details = "test-lb"
        ibp_obj._primary_interface_ip_list = "********"
        ibp_obj._primary_public_ip_id = None
        ibp_obj.process_instance_primary()
        assert "Updating primary interface IP list ******** for the instance" in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelEgressIPListTestEmptyDict(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_populate_interface_ip_list_is_empty_dict_json_string(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_nlb_domain_details = "test-lb"
        ibp_obj._primary_interface_ip_list = "********"
        ibp_obj._primary_public_ip_id = None
        ibp_obj.process_instance_primary()
        assert "Updating primary interface IP list ******** for the instance" in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelEgressIPListTest(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_populate_interface_ip_list_not_set(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_nlb_domain_details = "test-lb"
        ibp_obj._primary_interface_ip_list = "********"
        ibp_obj._primary_nlb_public_ipv6 = "2606:f4c0:26cc:11b:8000:34:0:0/96"
        ibp_obj._primary_public_ip_id = None
        ibp_obj.process_instance_primary()
        assert "set lb_details_v6 field in" in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelInterfaceIpv6ListEmpty(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_populate_interface_ipv6_list_is_empty_dict_json_string(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_nlb_domain_details = "test-lb"
        ibp_obj._primary_interface_ip_list = "********"
        ibp_obj._primary_interface_ipv6_list = '{"277": "2ff0:1500:ab::2"}'
        ibp_obj._primary_public_ip_id = None
        success = ibp_obj.process_instance_primary()
        assert success == True
        assert "Updating primary interface IP list ******** for the instance" in caplog.text
        assert 'Updating primary interface IPv6 list for instance 115 to {"277": "2ff0:1500:ab::2"}' in caplog.text
        assert "set interface_ipv6_list field" in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelInterfaceIpv6ListNotEmpty(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_populate_interface_ipv6_list_not_empty(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_nlb_domain_details = "test-lb"
        ibp_obj._primary_interface_ip_list = "********"
        ibp_obj._primary_interface_ipv6_list = '{"300": "2ff1:1500:ab::2"}'
        ibp_obj._primary_public_ip_id = None
        success = ibp_obj.process_instance_primary()
        assert success == True
        assert "Updating primary interface IP list ******** for the instance" in caplog.text
        assert 'Updating primary interface IPv6 list for instance 115 to {"300": "2ff1:1500:ab::2"}' in caplog.text
        assert "set interface_ipv6_list field" in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.send_sns_msg_for_r53_update')
    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelUpgradeInstance(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_upgrade_instance_no_r53(self, patched_Instancemodel, send_sns, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_nlb_domain_details = "test-lb"
        ibp_obj._primary_interface_ip_list = "********"
        ibp_obj._primary_interface_ipv6_list = '{"300": "2ff1:1500:ab::2"}'
        ibp_obj._primary_public_ip_id = None
        ibp_obj._primary_nlb_public_ipv6="v6::v6"

        success = ibp_obj.process_instance_primary()
        assert success == True
        assert send_sns.assert_not_called()

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelUpdateInterfaceIpv6ListInstanceException(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_update_column_interface_ipv6_list_instance_exception(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_nlb_domain_details = "test-lb"
        ibp_obj._primary_interface_ip_list = "********"
        ibp_obj._primary_interface_ipv6_list = '{"300": "2ff1:1500:ab::2"}'
        ibp_obj._primary_public_ip_id = None
        success = ibp_obj.process_instance_primary()
        assert success == False
        assert 'Updating primary interface IPv6 list for instance 115 to {"300": "2ff1:1500:ab::2"}' in caplog.text
        assert "!!! Fatal !!!! process_primary_instance failed since instance cannot be found:" in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelUpdateInterfaceIpv6ListInvalidException(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_update_column_interface_ipv6_list_invalid_exception(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_nlb_domain_details = "test-lb"
        ibp_obj._primary_interface_ip_list = "********"
        ibp_obj._primary_interface_ipv6_list = '{"300": "2ff1:1500:ab::2"}'
        ibp_obj._primary_public_ip_id = None
        success = ibp_obj.process_instance_primary()
        assert success == False
        assert 'Updating primary interface IPv6 list for instance 115 to {"300": "2ff1:1500:ab::2"}' in caplog.text
        assert "!!! Fatal !!!! process_primary_instance failed since interface_ipv6_list is invalid:" in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelUpdateInterfaceIpv6ListInvalidJsonException(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_update_column_interface_ipv6_list_invalid_json_exception(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_nlb_domain_details = "test-lb"
        ibp_obj._primary_interface_ip_list = "********"
        ibp_obj._primary_interface_ipv6_list = '{"300": "2ff1:1500:ab::2"}'
        ibp_obj._primary_public_ip_id = None
        success = ibp_obj.process_instance_primary()
        assert success == False
        assert 'Updating primary interface IPv6 list for instance 115 to {"300": "2ff1:1500:ab::2"}' in caplog.text
        assert "!!! Fatal !!!! process_primary_instance failed since interface_ipv6_list cannot be converted to JSON:" in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelDbUpdateException(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_update_column_interface_ipv6_list_invalid_json_exception(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_nlb_domain_details = "test-lb"
        ibp_obj._primary_interface_ip_list = "********"
        ibp_obj._primary_interface_ipv6_list = '{"300": "2ff1:1500:ab::2"}'
        ibp_obj._primary_public_ip_id = None
        success = ibp_obj.process_instance_primary()
        assert success == False
        assert 'Updating primary interface IPv6 list for instance 115 to {"300": "2ff1:1500:ab::2"}' in caplog.text
        assert "!!! Fatal !!!! process_primary_instance failed since interface_ipv6_list cannot be updated in RDS:" in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelReservedPublicIp(id=456, dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.instance_bringup.get_cloud_native_location_name_from_region_id')
    def test_process_instance_primary_update_oci_reserved_public_ip_success(
        self,
        mock_get_cloud_native_location_name_from_region_id,
        mockInstance,
        caplog,
    ):
        caplog.set_level(logging.INFO)
        mock_get_cloud_native_location_name_from_region_id.return_value="ap-hyderabad-1"
        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_OCI
        ibp_obj._reserved_public_ip_id = "ocid.353aaa.555"
        ibp_obj._primary_pvt_ip_id = "ocid.56756756.333"
        ibp_obj.logger = logging.getLogger("test")
        success = ibp_obj.process_instance_primary()
        assert "The region_id is 703" in caplog.text
        assert "The native_compute_region_name of the region_id 703 is ap-hyderabad-1" in caplog.text
        assert "The reserved_public_ip_id of the reserved public ip ******** is ocid.353aaa.555" in caplog.text
        assert "The primary_pvt_ip_id is ocid.56756756.333"  in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelReservedPublicIp(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_update_oci_reserved_public_ip_oci_virtual_network_client_not_initialized(
        self,
        mockInstance,
        caplog,
    ):
        caplog.set_level(logging.INFO)
        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_OCI
        ibp_obj.logger = logging.getLogger("test")
        ibp_obj.oci_sdk_utility = MagicMock()
        ibp_obj._reserved_public_ip_id = "ocid.353aaa.555"
        success = ibp_obj.process_instance_primary()
        assert "There is no need to replace the public ipaddress with the reserved public ip as the self.oci_sdk_utility is already initialized" in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelReservedPublicIp(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_update_oci_reserved_public_ip_failure(
        self,
        mockInstance,
        caplog,
    ):
        caplog.set_level(logging.INFO)
        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_OCI
        ibp_obj.logger = logging.getLogger("test")
        ibp_obj._reserved_public_ip_id = None
        success = ibp_obj.process_instance_primary()
        assert "No need to replace the public ipaddress with the reserved public address as the condition was not met. The cloud provider is 5, reserved public ip ocid is None,node_type is 161" in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelReservedPublicIp(id=456, dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.instance_bringup.get_cloud_native_location_name_from_region_id')
    def test_process_instance_primary_update_oci_reserved_public_ip_exception(
        self,
        mock_get_cloud_native_location_name_from_region_id,
        mockInstance,
        caplog,
    ):
        caplog.set_level(logging.INFO)
        mock_get_cloud_native_location_name_from_region_id.side_effect = Exception("tst error")
        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_OCI
        ibp_obj._reserved_public_ip_id = "ocid.353aaa.555"
        ibp_obj._primary_pvt_ip_id = "ocid.56756756.333"
        ibp_obj._public_ip = "********"
        ibp_obj.logger = logging.getLogger("test")
        success = ibp_obj.process_instance_primary()
        assert "Exception occured when trying to update the reserved ip ocid ocid.353aaa.555 for the node type 161. Reason: tst error" in caplog.text
        assert "!!! Fatal !!!! process_primary_instance failed"  in caplog.text
        assert success == False

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelColoSC(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_colo_sc_pvt_ip2(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._alias_ip_ranges = [{'ip_cidr_range': '**********/32'}]
        success = ibp_obj.process_instance_primary()

        assert success == True
        assert "Setting pvt_ip2 for primary instance id 456 as **********" in caplog.text
        patched_Instancemodel.return_value.set_param.assert_called_with("pvt_ip2", "**********")

class TestProcessInstanceNgpaIpFixer:
    @patch('libs.cloud_providers.common.instance_bringup.create_ipam_utils')
    @patch('libs.cloud_providers.common.instance_bringup.is_ipam_service_enabled')
    def test_process_instance_release_pending_ipam_enabled(self, mock_is_ipam_enabled, mock_ipam_utils, caplog):
        caplog.set_level(logging.INFO)
        mock_is_ipam_enabled.return_value = IPAMService.ENABLED
        mock_ipam_instance = MagicMock()
        mock_ipam_utils.return_value = mock_ipam_instance

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj.dbh = Mock_DBHandler()
        ibp_obj.logger = logger()

        ibp_obj.process_instance_release_pending([1, 2, 3], 123, 456)

        assert "Processing release pending for deployment 123" in caplog.text
        mock_ipam_instance.ipam_update_address.assert_called_once_with(
            match_eidx=123,
            match_not_cluster_id=[1, 2, 3],
            match_status="release_pending",
            match_account_id=0,
            set_status="reserved",
            set_cluster_id=0,
            set_eidx=0,
            call_context="orch:process_instance_release_pending"
        )

    @patch('libs.cloud_providers.common.instance_bringup.is_ipam_service_enabled')
    def test_process_instance_release_pending_ipam_disabled(self, mock_is_ipam_enabled, caplog):
        caplog.set_level(logging.INFO)
        mock_is_ipam_enabled.return_value = IPAMService.DISABLED

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj.dbh = Mock_DBHandler()
        ibp_obj.logger = logger()

        ibp_obj.process_instance_release_pending([1, 2, 3], 123, 456)

        assert "Processing release pending for deployment 123" in caplog.text
        assert "UPDATE public_ip_pool SET cluster_id = 0" in caplog.text

class TestInstanceBringupLbDetails:
    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelEgressIPListTest(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_set_lb_details_with_rn_int_onramp_ilb_v4_address(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_rn_int_onramp_ilb_v4_address = "*************"
        ibp_obj._primary_public_ip_id = None
        success = ibp_obj.process_instance_primary()

        assert success == True
        patched_Instancemodel.return_value.set_param.assert_any_call("lb_details", "*************")

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelEgressIPListTest(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_set_lb_details_with_empty_rn_int_onramp_ilb_v4_address(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_rn_int_onramp_ilb_v4_address = ""
        ibp_obj._primary_public_ip_id = None
        success = ibp_obj.process_instance_primary()

        assert success == True
        patched_Instancemodel.return_value.set_param.assert_any_call("lb_details", "")

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelEgressIPListTest(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_set_lb_details_with_none_rn_int_onramp_ilb_v4_address(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_rn_int_onramp_ilb_v4_address = None
        ibp_obj._primary_public_ip_id = None
        success = ibp_obj.process_instance_primary()

        assert success == True
        patched_Instancemodel.return_value.set_param.assert_any_call("lb_details", None)

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelEgressIPListTest(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_set_lb_details_with_special_characters(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_rn_int_onramp_ilb_v4_address = "********"
        ibp_obj._primary_public_ip_id = None
        success = ibp_obj.process_instance_primary()

        assert success == True
        patched_Instancemodel.return_value.set_param.assert_any_call("lb_details", "********")

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelEgressIPListTest(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_set_lb_details_with_oci_provider(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_OCI
        ibp_obj._primary_rn_int_onramp_ilb_v4_address = "**********"
        ibp_obj._primary_public_ip_id = None
        success = ibp_obj.process_instance_primary()

        assert success == True
        patched_Instancemodel.return_value.set_param.assert_any_call("lb_details", "**********")

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelDbUpdateException(id=456, dbh=Mock_DBHandler()))
    def test_process_instance_primary_set_lb_details_db_exception(self, patched_Instancemodel, caplog):
        caplog.set_level(logging.INFO)

        ibp_obj = instance_bringup_processing(DB())
        ibp_obj._primary_id = 115
        ibp_obj._cloud_provider = PROVIDER_GCP
        ibp_obj._primary_rn_int_onramp_ilb_v4_address = "*************"
        ibp_obj._primary_public_ip_id = None
        success = ibp_obj.process_instance_primary()

        assert success == False

