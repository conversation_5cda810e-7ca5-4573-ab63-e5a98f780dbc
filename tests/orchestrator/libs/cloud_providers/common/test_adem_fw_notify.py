import pytest
from unittest.mock import patch, MagicMock
import logging
import mock_instance_model as mock_model
from mock_region_model import DB
import mock_imports_orchestrator
from libs.cloud_providers.common.adem_fw_notify import is_adem_notification_enabled, send_pubsub_to_adem_service, notify_adem_service, delete_notify_adem_service



class TestAdemFwNotify:

    @patch('libs.cloud_providers.common.adem_fw_notify.execute_orch_query')
    def test_is_adem_notification_enabled(self, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        mock_dbh = MagicMock()
        logger = logging.getLogger('testlogger')
        mock_execute_query.return_value = (True, (['enabled']))
        ret = is_adem_notification_enabled(mock_dbh, logger)
        assert ret == True
        assert "ADEM PROBE V2 feature is enabled" in caplog.text

    @patch('libs.cloud_providers.common.adem_fw_notify.execute_orch_query')
    def test_is_adem_notification_disabled(self, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        mock_dbh = MagicMock()
        logger = logging.getLogger('testlogger')
        mock_execute_query.return_value = (True, (['disabled']))
        ret = is_adem_notification_enabled(mock_dbh, logger)
        assert ret == True
        assert "ADEM PROBE V2 Feature is Disabled. Returning" in caplog.text

    @patch('libs.cloud_providers.common.adem_fw_notify.gcp_authenticate', return_value='')
    @patch("libs.cloud_providers.common.adem_fw_notify.gcpPubSub")
    def test_send_pubsub_to_adem_service(self, mock_gcpPubSub, mock_gcp_auth, caplog):
        mock_gcpPubSub.return_value.PubSubClient = MagicMock()
        mock_gcpPubSub.PubSubClient.return_value.set_topic_path.return_value = None
        mock_gcpPubSub.PubSubClient.return_value.is_topic_valid.return_value = True
        mock_gcpPubSub.PubSubClient.return_value.publish.return_value = True

        caplog.set_level(logging.INFO)
        mock_dbh = MagicMock()
        logger = logging.getLogger('testlogger')

        # instance_details = {`instance_id`: 324, `cloud_provider`: `gcp`, `name`: `GPGW_324_france-south_espo-440856540`, `custid`: 17, `tenant_id`: 440856540, `vmid`: `5779478493871966716`, `public_ip`: `************`, `vm_status`: 0, `node_type`: 49, `compute_region_id`: 309, `compute_region_name`: `france-south`, `lb_details`: None, `interface_details`: None, `is_instance_behind_nlb`: 0, `workflow`: `INSTANCE_CREATE`, `serial_number`: `7D32D1C19267B96`, `super_tenant_id`: 440856540}
        instance_details = {"instance_id": 324, "cloud_provider": "gcp"}
        send_pubsub_to_adem_service(mock_dbh, instance_details, logger)
        assert "Successfully published payload" in caplog.text

    @patch('libs.cloud_providers.common.adem_fw_notify.execute_orch_query', return_value=(True, ([440856540])))
    @patch('libs.cloud_providers.common.adem_fw_notify.execute_orch_query', return_value=(True, (["7B3E7CC398F64FF"])))
    def test_notify_adem_service(self, mock_tenant, mock_serial_num, caplog):
        mock_dbh = MagicMock()
        caplog.set_level(logging.INFO)
        logger = logging.getLogger('testlogger')
        notify_adem_service(MagicMock(), mock_dbh, logger)
        assert "Sending ADD notification:" in caplog.text

    def test_delete_notify_adem_service(self, caplog):
        mock_dbh = MagicMock()
        caplog.set_level(logging.INFO)
        logger = logging.getLogger('testlogger')
        delete_notify_adem_service(324, mock_dbh, logger)
        assert "Sending DELETE notification" in caplog.text

