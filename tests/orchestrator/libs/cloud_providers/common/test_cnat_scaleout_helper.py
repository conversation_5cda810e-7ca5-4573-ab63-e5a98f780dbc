import mock_import_cnat_scaleout_helper
from mock_models import CustEpaasConfigModel, CustomerModel, DB, cnat_resp
from unittest.mock import patch, MagicMock
from unittest import TestCase
from libs.cloud_providers.common.ep_cnat_scaleout_helper import scale_ep_cnat, send_notification_to_saas_agent, \
    is_ep_cnat_feature_enabled, update_alias_ips_for_vm_instances, release_cnat_ip_from_public_ip_pool

import logging


cust_ep_cfg1 = CustEpaasConfigModel(DB(), 24, 212, "gcp", node_type=153, alt_node_type=-1)
cust_ep_cfg2 = CustEpaasConfigModel(DB(), 24, 200, "gcp", node_type=153, alt_node_type=-1)
customer = CustomerModel(custid=24, dbh=DB(), acct_id=12345)

network_list = {"1234": {"instance1_id": "1234", "aliasIpRanges": "*******/31"}, "5678": {"instance1_id": "5678",
                                                                                          "aliasIpRanges": "*******/31"}}


class TestEPCNATScaleoutHelper(TestCase):

    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.CustEpaasConfigModel', return_value=cust_ep_cfg2)
    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.CustomerModel', return_value=customer)
    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.get_last_scaleout_timestamp',
           return_value=None)
    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.update_last_scaleout_timestamp',
           return_value=MagicMock(return_value=None))
    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.invoke_lambda_for_cnat_update',
           return_value=MagicMock(return_value=0))
    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.get_wait_time',return_value=MagicMock(return_value=600))
    def test_scale_ep_cnat(self, m1, m2, m3, m4, m5, m6):
        response = scale_ep_cnat(DB(), 24, 212)
        self.assertEqual(response, True)

    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.CustEpaasConfigModel', return_value=cust_ep_cfg2)
    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.invoke_lambda_for_cnat_update', return_value=0)
    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.get_account_id', return_value=12345)
    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.is_ep_cnat_feature_enabled', return_value=True)
    def test_invoke_lambda(self, m1, m2, m3,m4):
        dbh = DB()
        dbh.logger = MagicMock()
        self.assertEqual(cust_ep_cfg2.get_param("is_inst_autoscaled"), 1)
        response = send_notification_to_saas_agent(dbh, 24, 1234, 212, "gcp", 153)
        self.assertEqual(response, True)
        self.assertEqual(cust_ep_cfg2.get_param("is_inst_autoscaled"), 0)

    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.get_current_release_version', return_value='5.2.0')
    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.CustomerModel', return_value=customer)
    def test_get_ep_cnat_feature_flag(self, m1, m2):
        dbh = DB()
        response = is_ep_cnat_feature_enabled(dbh, 24)
        self.assertEqual(response, True)

    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.get_network_list_for_ip_range_update',
           return_value=network_list)
    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.get_compute', return_value=MagicMock())
    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.get_network_list_for_ip_range_update',
           return_value=MagicMock())
    def test_update_alias_ips_for_vm_instances(self, m1, m2, m3):
        dbh = DB()
        success, res = update_alias_ips_for_vm_instances([1234, 5678, 9912], {}, 267, "test_project", 11223344, 220, 0, "/31",
                                      "0", dbh.logger)
        self.assertEqual(res, network_list)

    @patch('libs.cloud_providers.common.ep_cnat_scaleout_helper.dbconn',
           return_value=MagicMock())
    def test_release_cnat_ip_from_public_ip_pool(self, m1):
        dbh = DB()
        res = release_cnat_ip_from_public_ip_pool(24, ['*******', '*******'], dbh.logger)
        self.assertEqual(res, True)







