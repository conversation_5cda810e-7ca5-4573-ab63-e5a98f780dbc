from unittest.mock import patch, MagicMock
from unittest import TestCase
import mock_imports_test_gcp_alert_policy_helper
from mock_inputs_for_alert_policy import get_client, Monitoring_v3, response, cnat_policy_params, alert_policy_json, resourcemanager_v3, pubsub_v1
from libs.cloud_providers.common.gcp_alert_policy_helper import add_service_id_to_topic, gcp_get_alert_policy, gcp_update_alert_policy, \
    enable_api, is_gcp_api_enabled
from mock_region_model import DB, Service
import logging
logger = logging.getLogger()


class TestAlertPolicyHelper(TestCase):

    @patch('libs.cloud_providers.common.gcp_alert_policy_helper.monitoring_v3', return_value=Monitoring_v3)
    @patch('libs.cloud_providers.common.gcp_alert_policy_helper.get_client', return_value=get_client())
    def test_gcp_get_alert_policy(self, mock1, mock2):
        cfg = {}
        project_id = "test-project-01"
        policy_name = "test-policy-01"
        res = gcp_get_alert_policy(cfg, project_id, policy_name, logger)
        self.assertEqual(res, response)


    @patch('libs.cloud_providers.common.gcp_alert_policy_helper.get_metrics_for_alert_policies_from_epti',
           return_value=(cnat_policy_params))
    @patch('libs.cloud_providers.common.gcp_alert_policy_helper.get_policy_id_for_alert_policies',
           return_value=alert_policy_json)
    @patch('libs.cloud_providers.common.gcp_alert_policy_helper.get_cloud_native_location_name_from_region_id',
           return_value=MagicMock(return_value="us-west1"))
    @patch('libs.cloud_providers.common.gcp_alert_policy_helper.gcp_get_alert_policy',
           return_value=MagicMock(return_value=response))
    @patch('libs.cloud_providers.common.gcp_alert_policy_helper.monitoring_v3', return_value=Monitoring_v3)
    @patch('libs.cloud_providers.common.gcp_alert_policy_helper.get_client', return_value=get_client())
    def test_update_alert_policy(self, mk1, mk2, mk3, mk4, mk5, mk6):
        cfg ={}
        project_id = "cust-gpcs-6wv7gdioliz0anj9jt1y"
        res = gcp_update_alert_policy(DB(), 24, **********, cfg, project_id, 201, logger)
        self.assertEqual(res, response)

    @patch('libs.cloud_providers.common.gcp_alert_policy_helper.get_service',
           return_value=Service())
    @patch('libs.cloud_providers.common.gcp_alert_policy_helper.check_if_api_is_enabled',
           return_value=True)
    def test_enable_api(self, mk1, mk2):
        cfg = {}
        project_id = "cust-gpcs-6wv7gdioliz0anj9jt1y"
        res = enable_api(project_id, cfg, logger, "monitoring.googleapis.com")
        self.assertEqual(res, True)

    @patch('libs.cloud_providers.common.gcp_alert_policy_helper.resourcemanager_v3', return_value=resourcemanager_v3)
    @patch('libs.cloud_providers.common.gcp_alert_policy_helper.pubsub_v1', return_value=pubsub_v1)
    def test_enable_api(self, mk1, mk2):
        cfg = {}
        project_id = "cust-gpcs-6wv7gdioliz0anj9jt1y"
        success, response = add_service_id_to_topic(project_id, "testpath123", cfg, 24, logger)
        self.assertEqual(success, True)
        self.assertEqual(response, True)






