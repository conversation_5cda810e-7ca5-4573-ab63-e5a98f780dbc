import sys
from unittest.mock import MagicMock

sys.modules["libs.common.shared.gcp_utils"] = MagicMock()
sys.modules["google.cloud"] = MagicMock()
sys.modules["libs.model.orchcfgmodel"] = MagicMock()
sys.modules["libs.common.shared.model"] = MagicMock()
sys.modules["libs.apis.region_master_api"] = MagicMock(get_cloud_native_location_name_from_region_id=MagicMock(return_value="us-west2"))
sys.modules["googleapiclient.discovery"] = MagicMock()