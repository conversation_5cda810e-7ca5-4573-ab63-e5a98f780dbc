import sys
import pytest
from unittest import TestCase
from unittest.mock import patch, MagicMock
from libs.cloud_providers.common.instance_ami_util import compare_ami_version, get_ami_version_major_build_number
import logging
logger = logging.getLogger()

class TestInstanceAMIUtil:

    def test_compare_ami_version_smaller(self, caplog):
        caplog.set_level(logging.INFO)
        response = compare_ami_version('10.2.4', '10.2.6')
        assert response == 1


    def test_compare_ami_version_greater(self, caplog):
        caplog.set_level(logging.INFO)
        response = compare_ami_version('10.2.4', '10.2.3')
        assert response == -1

    def test_compare_ami_version_equal(self, caplog):
        caplog.set_level(logging.INFO)
        response = compare_ami_version('10.2.4', '10.2.4')
        assert response == 0


    def test_get_ami_version_major_build_number_valid(self, caplog):
        caplog.set_level(logging.INFO)
        response = get_ami_version_major_build_number('PA-VM-SaaS-10.2.4-h21.saas')
        assert response == '10.2.4'


    def test_get_ami_version_major_build_number_invalid(self, caplog):
        caplog.set_level(logging.INFO)
        response = get_ami_version_major_build_number('PA-VM-SaaS-h21.saas')
        assert response == None






