import pytest
from unittest.mock import patch, MagicMock
import logging
import mock_instance_model as mock_model
from mock_region_model import DB
import mock_imports_orchestrator
from libs.cloud_providers.common.probevm_bringup import probevm_bringup
from libs.cloud_providers.common.exceptions import UnableToAllocatedReservedPublicIpException

class Mock_conn():
    def __init__(self):
        pass

    def close(self):
        pass

class Mock_DBHandler():
    def __init__(self):
        self.logger = logging.getLogger('testlogger')
        self.conn = Mock_conn()
        
    def conn(self):
        return self.conn
        
    def get_cursor(self):
        return Mock_cursor()
    
    def cursorclose(self, cursor):
        return

class TestProbeVmBringUp:

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelReservedPublicIp(id=456, dbh=<PERSON><PERSON>_<PERSON>H<PERSON><PERSON>()))
    def test_update_probevm_public_ip_success(
        self,
        mock_instance,
        caplog,
    ):
        caplog.set_level(logging.INFO)
        pvmobj = probevm_bringup(DB())
        mock_probevm_inst_info = {"ReservedPublicIPId": "ocid.353aaa.555", "PrivateIPId": "ocid.456haa.575"}
        pvmobj.update_probevm_public_ip(mock_probevm_inst_info, mock_instance)
        assert "update_probevm_public_ip: The region_id is" in caplog.text
        assert "The reserved_public_ip_id is ocid.353aaa.555" in caplog.text
        assert "update_probevm_public_ip: The primary_pvt_ip_id is ocid.456haa.575" in caplog.text

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', return_value=mock_model.InstanceModelReservedPublicIp(id=456, dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.probevm_bringup.get_cloud_native_location_name_from_region_id')
    def test_update_probevm_public_ip_exception(
        self,
        mock_get_cloud_native_location_name_from_region_id,
        mock_instance,
        caplog,
    ):
        caplog.set_level(logging.INFO)
        mock_get_cloud_native_location_name_from_region_id.side_effect = Exception("tst error")
        pvmobj = probevm_bringup(DB())
        mock_probevm_inst_info = {"ReservedPublicIPId": "ocid.353aaa.555", "PrivateIPId": "ocid.456haa.575"}
        with pytest.raises(UnableToAllocatedReservedPublicIpException):
            pvmobj.update_probevm_public_ip(mock_probevm_inst_info, mock_instance)
        assert "update_probevm_public_ip: Exception occured when trying to update the reserved ip ocid ocid.353aaa.555 for Probe VM. Reason: tst error" in caplog.text
