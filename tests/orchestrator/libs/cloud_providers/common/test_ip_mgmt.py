import logging
import os
os.environ['AWS_REGION'] = "mock-aws-region"
from unittest.mock import patch, MagicMock
from mock_models import DB, RegionMasterModel, \
    ExplicitProxyTenantInfoModel, CustEpaasConfigModel
from libs.cloud_providers.common.ip_management.ipv4.ip_mgmt import \
    release_all_ip_to_customer_ip_pool, \
    get_service_id_for_nlb, \
    is_enable_tls_term_on_ep_for_cust, \
    release_all_nlb_ips, set_ip_status_active

from libs.cloud_providers.common.ip_management.ipv4.ip_mgmt import IPHandler

region_aws = RegionMasterModel(DB(), 'aws', 200)
region_gcp = RegionMasterModel(DB(), 'gcp', 222)

ep_tenant_info1 = ExplicitProxyTenantInfoModel(DB(), 24252627, 0)
ep_tenant_info2 = ExplicitProxyTenantInfoModel(DB(), 21212121, 1)
cust_ep_cfg = CustEpaasConfigModel(DB(), 123, 222, 2)


class Mock_cursor():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        return {"name": "test_instance_model"}


class Mock_DBHandler():
    def __init__(self, logger):
        self.logger = logger

    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return


class TestIpMgmt:

    @patch("libs.model.regionmastermodel.RegionMasterModel", return_value=region_gcp)
    @patch("libs.cloud_providers.common.ip_mgmt.dbconn.execute_lambda_query", return_value={'ok': 1, 'result': [[1]]})
    def test_release_all_ip_to_customer_ip_pool_pass1(self, mock_rmm, mock_elq, caplog):
        caplog.set_level(logging.INFO)

        res = release_all_ip_to_customer_ip_pool(DB(), 24252627, 'gcp', 'singapore-g', 155, None)
        assert (res == 1)

        assert "1 IPs released from the region." in caplog.text

    @patch("libs.model.regionmastermodel.RegionMasterModel", return_value=region_gcp)
    @patch("libs.cloud_providers.common.ip_mgmt.dbconn.execute_lambda_query", return_value={'ok': 1, 'result': [[0]]})
    def test_release_all_ip_to_customer_ip_pool_pass2(self, mock_rmm, mock_elq, caplog):
        caplog.set_level(logging.INFO)

        res = release_all_ip_to_customer_ip_pool(DB(), 24252627, 'gcp', 'singapore-g', 155, None)
        assert (res == 0)

        assert "0 IPs released from the region." in caplog.text

    @patch("libs.model.regionmastermodel.RegionMasterModel", return_value=region_gcp)
    @patch("libs.cloud_providers.common.ip_mgmt.dbconn.execute_lambda_query", return_value={'ok': 0})
    def test_release_all_ip_to_customer_ip_pool_fail(self, mock_rmm, mock_elq, caplog):
        caplog.set_level(logging.INFO)

        res = release_all_ip_to_customer_ip_pool(DB(), 24252627, 'gcp', 'singapore-g', 155, None)
        assert (res == -1)

        assert "Failed to release IPs!" in caplog.text

    @patch("libs.model.regionmastermodel.RegionMasterModel", return_value=region_gcp)
    def test_get_service_id_for_nlb(self, mock_rmm, caplog):
        caplog.set_level(logging.INFO)

        execute_orch_query = MagicMock()
        execute_orch_query.return_value = True, "singapore-g"
        res = get_service_id_for_nlb(DB(), 21212121, 222)
        assert (res == "gpcs-proxy-nlb-singapore-g-21212121")

        assert "Returning service id: gpcs-proxy-nlb-singapore-g-21212121" in caplog.text

    @patch("libs.cloud_providers.common.ip_mgmt.ExplicitProxyTenantInfoModel", return_value=ep_tenant_info2)
    def test_is_enable_tls_term_on_ep_for_cust(self, mock_epti, caplog):
        caplog.set_level(logging.INFO)

        res = is_enable_tls_term_on_ep_for_cust(DB(), 21212121, 220)
        assert (res == 1)

        assert "enable_tls_term_on_ep for acct_id 21212121 is 1" in caplog.text

    @patch("libs.cloud_providers.common.ip_mgmt.ExplicitProxyTenantInfoModel", return_value=ep_tenant_info2)
    @patch("libs.model.regionmastermodel.RegionMasterModel", return_value=region_gcp)
    @patch("libs.cloud_providers.common.ip_mgmt.dbconn.execute_lambda_query", return_value={'ok': 1, 'result': [[1]]})
    def test_release_all_nlb_ips_for_tls_term_tenant_pass(self, mock_epti, mock_rmm, mock_elq, caplog):
        caplog.set_level(logging.DEBUG)

        res = release_all_nlb_ips(DB(), 21212121, 222, 153)
        assert (res == 1)

        assert "1 nlb IPs released to customer pool." in caplog.text

    @patch("libs.cloud_providers.common.ip_mgmt.ExplicitProxyTenantInfoModel", return_value=ep_tenant_info1)
    @patch("libs.model.regionmastermodel.RegionMasterModel", return_value=region_gcp)
    @patch("libs.cloud_providers.common.ip_mgmt.dbconn.execute_lambda_query", return_value={'ok': 0})
    def test_release_all_nlb_ips_for_tls_term_tenant_fail(self, mock_epti, mock_rmm, mock_elq, caplog):
        caplog.set_level(logging.DEBUG)

        res = release_all_nlb_ips(DB(), 21212121, 222, 153)
        assert (res == -1)

        assert "Failed to release_all_nlb_ips" in caplog.text

    @patch("libs.cloud_providers.common.ip_mgmt.ExplicitProxyTenantInfoModel", return_value=ep_tenant_info2)
    @patch("libs.model.regionmastermodel.RegionMasterModel", return_value=region_aws)
    @patch("libs.cloud_providers.common.ip_mgmt.dbconn.execute_lambda_query", return_value={'ok': 0})
    def test_release_all_nlb_ips_for_aws_tenant(self, mock_epti, mock_rmm, mock_elq, caplog):
        caplog.set_level(logging.DEBUG)

        res = release_all_nlb_ips(DB(), 21212121, 200, 153)
        assert (res == 0)

        assert "Only GCP need to manage NLB IP" in caplog.text

    def test_find_nlb_settings_by_custid_and_region_id(self):
        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger)
        ip_mgmt = IPHandler(dbh, 0, 0)
        ret = ip_mgmt.find_nlb_settings_by_custid_and_region_id(0, 0)
        assert (ret['success'] == True)  # add assertion here


    @patch("libs.cloud_providers.common.ip_mgmt.ExplicitProxyTenantInfoModel", return_value=ep_tenant_info1)
    @patch("libs.model.regionmastermodel.RegionMasterModel", return_value=region_gcp)
    @patch("libs.cloud_providers.common.ip_mgmt.dbconn.execute_lambda_query", return_value={'ok': 1, 'result': [[1]]})
    def test_release_all_nlb_ips_for_tls_term_tenant_pass_mig(self, mock_epti, mock_rmm, mock_elq, caplog):
        '''
        check global ips are released when enable_tls_term_on_ep is enabled at cust_epaas_config level
        '''
        caplog.set_level(logging.DEBUG)

        res = release_all_nlb_ips(DB(), 21212121, 220, 153)
        assert (res == 1)

        assert "1 nlb IPs released to customer pool." in caplog.text

    @patch("libs.cloud_providers.common.ip_mgmt.dbconn.execute_lambda_query", return_value={'ok': 1, 'result': []})
    def test_set_ip_status_active(self, mock_rmm, caplog):
        caplog.set_level(logging.INFO)

        execute_orch_query = MagicMock()
        logger = DB().logger
        execute_orch_query.return_value = True, "singapore-g"
        res = set_ip_status_active('*******', logger)
        assert (res is True)

        assert "Successfully set status as active for the nlb ip {ip}" in caplog.text

    def test_reserve_double_if_necessary(self):
        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger)
        ip_mgmt = IPHandler(dbh, 0, 0)
        ip_mgmt._get_non_upgrade_inst_count = MagicMock()
        ip_mgmt._get_non_upgrade_inst_count.return_value = 1
        ip_mgmt._get_allocated_ip_count_in_city_country_in_location = MagicMock()
        ip_mgmt._get_allocated_ip_count_in_city_country_in_location.return_value = {('Dublin', 'Ireland'): 2}
        ip_mgmt.find_nlb_settings_by_custid_and_region_id = MagicMock()
        ret = ip_mgmt.reserve_double_if_necessary(1226571487, 0, 10, 49, [('Dublin', 'Ireland')],
                                                  'aws', 'mock_location', 1, 9386)
        assert (ret is True)

    def test_reserve_double_if_necessary_sp(self):
        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger)
        ip_mgmt = IPHandler(dbh, 0, 0)
        ip_mgmt._get_non_upgrade_inst_count = MagicMock()
        ip_mgmt._get_non_upgrade_inst_count.return_value = 1
        ip_mgmt._get_allocated_ip_count_in_city_country_in_location = MagicMock()
        ip_mgmt._get_allocated_ip_count_in_city_country_in_location.return_value = {('Dublin', 'Ireland'): 2}
        ip_mgmt.find_nlb_settings_by_custid_and_region_id = MagicMock()
        ret = ip_mgmt.reserve_double_if_necessary(1226571487, 0, 10, 49, [('Dublin', 'Ireland')],
                                                  'aws', 'mock_location', 1, 9386, 1)
        assert (ret is True)