import sys
import json
import pytest
from unittest import TestCase
from unittest.mock import patch, MagicMock
import logging
logger = logging.getLogger()
import mock_imports_swgproxy_bringup
from mock_responses import cfg, salt_profile
from mock_models import CustEpaasConfigModel, DB, CustomerModel, \
    ExplicitProxyTenantInfoModel, MockInstanceModelforDNS,OrchCfgModel
from libs.cloud_providers.common.swgproxy_bringup import swgproxy_bringup


cust_ep_cfg = CustEpaasConfigModel(DB(), 24, 220, 2)
cust_ep_cfg3 = CustEpaasConfigModel(DB(), 1212, 220, 2)
cust_ep_cfg4 = CustEpaasConfigModel(DB(), 12, 222, 2)
cust_ep_cfg5 = CustEpaasConfigModel(DB(), 24, 289, 2)
ep_tenant_info = ExplicitProxyTenantInfoModel(DB(), 24252627)
instmodel_1 = MockInstanceModelforDNS(123, DB(), 0)
instmodel_2 = MockInstanceModelforDNS(456, DB(), 1)

class SwgProxyTests(TestCase):

    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_compute_region_name_from_edge_region_idx',
           return_value='us-west1')
    def test_add_salt_profile_for_ep_region_cust_epaas_populated(self, mock_cust_ep, mock_cp, mock_cs, mock_region):
        """
            envoy autoscale parameters should be picked from cust_epaas_config table incase its already populated
        """
        self.maxDiff = None

        cust_ep_cfg.ep_min_count = 2
        cust_ep_cfg.ep_max_count = 5
        cust_ep_cfg.ep_autosclale_cpu_target = 0.6
        proxy = swgproxy_bringup(DB())
        proxy.add_salt_profile_for_ep_region(24, 220)
        sp = json.loads(cust_ep_cfg.salt_profile)
        self.assertEqual(sp["envoy_min_cnt"], "2")
        self.assertEqual(sp["envoy_max_cnt"], "5")
        self.assertEqual(sp["envoy_autosclale_cpu_target"], "0.6")

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_compute_region_name_from_edge_region_idx',
           return_value='us-west1')
    @patch('libs.cloud_providers.common.swgproxy_bringup.is_ep_geneve_enabled', return_value=1)
    def test_add_salt_profile_for_ep_region_cust_epaas_ep_null(self, mock_geneve, mock_cust_ep, mock_cp, mock_cs, mock_region):
        """
            envoy autoscale parameters should be picked from orch_cfg table incase cust_epaas envoy params are null
        """
        self.maxDiff = None
        cust_ep_cfg.ep_min_count = None
        cust_ep_cfg.ep_max_count = None
        cust_ep_cfg.ep_autosclale_cpu_target = None
        cust_ep_cfg.enable_envoy_geneve = "1"
        proxy = swgproxy_bringup(DB())
        proxy.add_salt_profile_for_ep_region(24, 220)
        sp = json.loads(cust_ep_cfg.salt_profile)
        self.assertEqual(sp["envoy_min_cnt"], cfg["envoy_min_cnt"])
        self.assertEqual(sp["envoy_max_cnt"], cfg["envoy_max_cnt"])
        self.assertEqual(sp["envoy_autosclale_cpu_target"], cfg["envoy_autosclale_cpu_target"])
        self.assertEqual(sp["ep-geneve-enabled"], "true")

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_compute_region_name_from_edge_region_idx',
           return_value='us-west1')
    def test_add_salt_profile_for_ep_region_when_envoy_version_present(self, mock_cust_ep, mock_cp, mock_cs, mock_region):
        """
            envoy autoscale parameters should be picked from orch_cfg table incase cust_epaas envoy params are null
        """
        self.maxDiff = None
        proxy = swgproxy_bringup(DB())
        res=proxy.add_salt_profile_for_ep_region(24, 220)
        self.assertEqual(res, None)


    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_compute_region_name_from_edge_region_idx',
           return_value='us-west1')
    def test_add_salt_profile_for_ep_region_cust_epaas_ep_nodetype(self, mock_cust_ep, mock_cp, mock_cs, mock_region):
        """
            verify nodetype swgproxy is populated in the userdata
        """
        self.maxDiff = None
        proxy = swgproxy_bringup(DB())
        proxy.add_salt_profile_for_ep_region(24, 220)
        sp = json.loads(cust_ep_cfg.salt_profile)
        userdata = json.loads(sp["user_data"])
        self.assertEqual(userdata.get("pahcs", None), "all.sink.swg.panclouddev.com")
        self.assertEqual(userdata.get("pashs", None), "sink.swg.panclouddev.com")
        self.assertEqual(userdata.get("cdl_region", None), "americas")

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg)
    @patch('libs.cloud_providers.common.swgproxy_bringup.ExplicitProxyTenantInfoModel', return_value=ep_tenant_info)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_compute_region_name_from_edge_region_idx',
           return_value='us-west1')
    def test_add_salt_profile_for_ep_region_listener_proxy_protocol(self, mock_cust_ep, mock_ep_tenant, mock_cp,
                                                                    mock_cs, mock_region):
        """
            envoy autoscale parameters should be picked from orch_cfg table incase cust_epaas envoy params are null
        """
        self.maxDiff = None
        ep_tenant_info.enable_tls_term_on_ep = 1
        proxy = swgproxy_bringup(DB())
        proxy.add_salt_profile_for_ep_region(24, 220)
        sp = json.loads(cust_ep_cfg.salt_profile)
        userdata = json.loads(sp["user_data"])
        self.assertEqual(userdata.get("listener_proxy_protocol", None), 1)

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg)
    @patch('libs.cloud_providers.common.swgproxy_bringup.ExplicitProxyTenantInfoModel', return_value=ep_tenant_info)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_compute_region_name_from_edge_region_idx',
           return_value='us-west1')
    def test_add_salt_profile_for_ep_region_listener_proxy_protocol_without_agp(self, mock_cust_ep, mock_ep_tenant, mock_cp,
                                                                    mock_cs, mock_region):
        """
            envoy autoscale parameters should be picked from orch_cfg table incase cust_epaas envoy params are null
        """
        self.maxDiff = None
        ep_tenant_info.enable_global_nlb = 1
        proxy = swgproxy_bringup(DB())
        proxy.add_salt_profile_for_ep_region(24, 220)
        sp = json.loads(cust_ep_cfg.salt_profile)
        userdata = json.loads(sp["user_data"])
        self.assertEqual(userdata.get("listener_proxy_protocol", None), 1)

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg)
    @patch('libs.cloud_providers.common.swgproxy_bringup.ExplicitProxyTenantInfoModel', return_value=ep_tenant_info)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_compute_region_name_from_edge_region_idx',
           return_value='us-west1')
    def test_get_salt_profile_without_agp(self, mock_cust_ep, mock_ep_tenant, mock_cp,
                                                                    mock_cs, mock_region):
        """
            envoy autoscale parameters should be picked from orch_cfg table incase cust_epaas envoy params are null
        """
        self.maxDiff = None
        cust_ep_cfg.enable_tls_term_on_ep = None
        ep_tenant_info.enable_tls_term_on_ep = 0
        proxy = swgproxy_bringup(DB())
        sp = proxy.add_salt_profile_for_ep_region(24, 200)
        salt_profile = proxy.ep_cust_region_salt_profile(24, 200)
        sp = json.loads(cust_ep_cfg.salt_profile)
        userdata = json.loads(sp["user_data"])
        self.assertEqual(userdata.get("listener_proxy_protocol", None), None)
        self.assertEqual(userdata.get("compute_region", None), 'us-west1')
        self.assertEqual(salt_profile.get("enable_tls_term_on_ep"), None)

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg)
    @patch('libs.cloud_providers.common.swgproxy_bringup.ExplicitProxyTenantInfoModel', return_value=ep_tenant_info)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_compute_region_name_from_edge_region_idx',
           return_value='us-west1')
    @patch('libs.cloud_providers.common.swgproxy_bringup.get_cs_region',
           return_value='"us.cs-dev.sasedp.panclouddev.com')
    def test_get_salt_profile_with_agp(self, mock_cust_ep, mock_ep_tenant, mock_cp,
                              mock_cs, mock_region):
        """
            envoy autoscale parameters should be picked from orch_cfg table incase cust_epaas envoy params are null
        """
        self.maxDiff = None
        cust_ep_cfg.enable_tls_term_on_ep = 1
        ep_tenant_info.enable_tls_term_on_ep = 0
        proxy = swgproxy_bringup(DB())
        sp = proxy.add_salt_profile_for_ep_region(24, 200)
        salt_profile = proxy.ep_cust_region_salt_profile(24, 200)
        sp = json.loads(cust_ep_cfg.salt_profile)
        userdata = json.loads(sp["user_data"])
        self.assertEqual(userdata.get("listener_proxy_protocol", None), 1)
        self.assertEqual(userdata.get("compute_region", None), 'us-west1')
        self.assertEqual(salt_profile.get("enable_tls_term_on_ep"), 1)
        self.assertEqual(salt_profile.get("enable_global_nlb"), 0)
        self.assertEqual(salt_profile.get("csp_id"), "123456")
        self.assertEqual(salt_profile.get("pa_cs"), "us.cs-dev.sasedp.panclouddev.com")

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg5)
    @patch('libs.cloud_providers.common.swgproxy_bringup.ExplicitProxyTenantInfoModel', return_value=ep_tenant_info)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_compute_region_name_from_edge_region_idx',
           return_value='us-west1')
    @patch('libs.cloud_providers.common.swgproxy_bringup.get_cs_region',
           return_value='"us.cs-dev.sasedp.panclouddev.com')
    def test_get_salt_profile_with_zero_envoy(self, mock_cust_ep, mock_ep_tenant, mock_cp,
                                       mock_cs, mock_region):
        """
            envoy autoscale parameters should be picked from orch_cfg table incase cust_epaas envoy params are null
        """
        self.maxDiff = None
        cust_ep_cfg.enable_tls_term_on_ep = 1
        ep_tenant_info.enable_tls_term_on_ep = 0
        proxy = swgproxy_bringup(DB())
        sp = proxy.add_salt_profile_for_ep_region(24, 289)
        salt_profile = proxy.ep_cust_region_salt_profile(24, 289)
        self.assertEqual(salt_profile.get("envoy_min_cnt"), 1)
        self.assertEqual(salt_profile.get("envoy_max_cnt"), 10)

    @patch('libs.cloud_providers.common.swgproxy_bringup.InstanceModel', return_value=instmodel_1)
    def test_process_instance(self,mock_cs):
        """
            dns entry should get insterted as it is the original instance
        """
        self.maxDiff = None
        proxy = swgproxy_bringup(DB())
        proxy_instance_info = {"PrimaryId": 123, "PrimaryVmId": 12345,
                               "PrimaryMgtIp": "*******", "PrimaryPvtIp": "*******",
                               "PrimaryExtrnIp": "*******", "EIP": "********",
                               "lb_details": "***********", "LoopBackIp": "***********",
                               "PrimaryMachineType": "e2-standard-4", "state": 1}
        res = proxy.process_instance(proxy_instance_info)
        self.assertEqual(res, True)
        self.assertEqual(instmodel_1.dns_insert, 1)

    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_envoy_outside_panos_val')
    @patch('libs.cloud_providers.common.swgproxy_bringup.InstanceModel', return_value=instmodel_1)
    def test_process_instance_loopback_ip_old_arch(self,mock_cs, mock_gpcs_envoy_outside_panos_val, caplog):
        """
            loopback ip should be inserted
        """
        self.maxDiff = None
        caplog.set_level(logging.INFO)
        proxy = swgproxy_bringup(DB())
        proxy_instance_info = {"PrimaryId": 123, "PrimaryVmId": 12345,
                               "PrimaryMgtIp": "*******", "PrimaryPvtIp": "*******",
                               "PrimaryExtrnIp": "*******", "EIP": "********",
                               "lb_details": "***********", "LoopBackIp": "***********",
                               "PrimaryMachineType": "e2-standard-4", "state": 1}
        mock_gpcs_envoy_outside_panos_val.return_value = False
        res = proxy.process_instance(proxy_instance_info)
        self.assertEqual(res, True)
        assert "old arch setting loopback_ip" in caplog.text

    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_envoy_outside_panos_val')
    @patch('libs.cloud_providers.common.swgproxy_bringup.InstanceModel', return_value=instmodel_1)
    def test_process_instance_loopback_ip_new_arch(self,mock_cs, mock_gpcs_envoy_outside_panos_val, caplog):
        """
            loopback ip should not be inserted
        """
        self.maxDiff = None
        caplog.set_level(logging.INFO)
        proxy = swgproxy_bringup(DB())
        proxy_instance_info = {"PrimaryId": 123, "PrimaryVmId": 12345,
                               "PrimaryMgtIp": "*******", "PrimaryPvtIp": "*******",
                               "PrimaryExtrnIp": "*******", "EIP": "********",
                               "lb_details": "***********", "LoopBackIp": "***********",
                               "PrimaryMachineType": "e2-standard-4", "state": 1}
        mock_gpcs_envoy_outside_panos_val.return_value = True
        res = proxy.process_instance(proxy_instance_info)
        self.assertEqual(res, True)
        assert "new arch skip setting loopback_ip" in caplog.text
          
    @patch('libs.cloud_providers.common.swgproxy_bringup.InstanceModel', return_value=instmodel_2)
    def test_process_instance_autoscaled(self, mock_cs):
        """
            dns entry should not get insterted as it is not the original instance
        """
        self.maxDiff = None
        self._caplog.set_level(logging.INFO)
        proxy = swgproxy_bringup(DB())
        proxy_instance_info = {"PrimaryId": 123, "PrimaryVmId": 12345,
                               "PrimaryMgtIp": "*******", "PrimaryPvtIp": "*******",
                               "PrimaryExtrnIp": "*******", "EIP": "********",
                               "lb_details": "***********", "LoopBackIp": "***********",
                               "PrimaryMachineType": "e2-standard-4", "state": 1}
        res = proxy.process_instance(proxy_instance_info)
        self.assertEqual(res, True)
        self.assertEqual(instmodel_2.dns_insert, 0)
        messages = [i.message for i in self._caplog.records]
        self.assertIn("Not calling dns insert function as its not a primary instance id: "
                      "456, node_type: 153, is_dynamic_node: 1, lb_details ***********", messages)


    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg)
    @patch('libs.cloud_providers.common.swgproxy_bringup.ExplicitProxyTenantInfoModel', return_value=ep_tenant_info)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_compute_region_name_from_edge_region_idx',
           return_value='us-west1')
    def test_get_salt_profile_metadata(self, mock_cust_ep, mock_ep_tenant, mock_cp,
                              mock_cs, mock_region):
        self.maxDiff = None
        proxy = swgproxy_bringup(DB())
        sp = proxy.add_salt_profile_for_ep_region(24, 200)
        salt_profile = proxy.ep_cust_region_salt_profile(24, 200)
        sp = json.loads(cust_ep_cfg.salt_profile)
        userdata = json.loads(sp["user_data"])
        self.assertEqual(userdata.get("dnsproxy", None), '172.16.255.254')
        self.assertEqual(userdata.get("tsgid", None), '11439239183')

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg3)
    @patch('libs.cloud_providers.common.swgproxy_bringup.ExplicitProxyTenantInfoModel', return_value=ep_tenant_info)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx',
           return_value='gcp')
    def test_envoy_params(self, mock_cust_ep, mock_ep_tenant, mock_cp,
                                       mock_cs, mock_region):
        self.maxDiff = None
        proxy = swgproxy_bringup(DB())
        proxy.ep_set_envoy_parameters(1212, 200, 153, -1)
        self.assertEqual(cust_ep_cfg3.enable_global_nlb, 1)
        self.assertEqual(cust_ep_cfg3.ep_min_count, 2)
        self.assertEqual(cust_ep_cfg3.ep_max_count, 5)
        self.assertEqual(cust_ep_cfg3.cnat_enabled, "0")
        self.assertEqual(cust_ep_cfg3.cnat_min_count, 1)
        self.assertEqual(cust_ep_cfg3.cnat_max_count, 16)
        self.assertEqual(cust_ep_cfg3.ep_autosclale_cpu_target, 0.7)
        self.assertEqual(cust_ep_cfg3.cnat_mon_enabled, "1")

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg3)
    @patch('libs.cloud_providers.common.swgproxy_bringup.ExplicitProxyTenantInfoModel', return_value=ep_tenant_info)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx',
           return_value='gcp')
    def test_envoy_params(self, mock_cust_ep, mock_ep_tenant, mock_cp,
                          mock_cs, mock_region):
        self.maxDiff = None
        proxy = swgproxy_bringup(DB())
        cust_ep_cfg3.ep_min_count = 0
        cust_ep_cfg3.ep_max_count = 0
        cust_ep_cfg3.ep_autosclale_cpu_target = 0
        proxy.ep_set_envoy_parameters(121, 200, 153, -1)
        self.assertEqual(cust_ep_cfg3.ep_min_count, '1')
        self.assertEqual(cust_ep_cfg3.ep_max_count, '10')
        self.assertEqual(cust_ep_cfg3.ep_autosclale_cpu_target, '0.5')
        #

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg3)
    @patch('libs.cloud_providers.common.swgproxy_bringup.ExplicitProxyTenantInfoModel', return_value=ep_tenant_info)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.OrchCfgModel', return_value=OrchCfgModel())
    def test_get_service_timeout_0_in_cep_cfg(self, mock_cust_ep, mock_ep_tenant, mock_cp,
                          mock_cs, mock_cfg):
        self.maxDiff = None
        proxy = swgproxy_bringup(DB())
        timeoutSec = proxy.gpcs_envoy_service_timeout_for_lb(121, 200, 153, -1)
        self.assertEqual(timeoutSec, 86400)

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg4)
    @patch('libs.cloud_providers.common.swgproxy_bringup.ExplicitProxyTenantInfoModel', return_value=ep_tenant_info)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.OrchCfgModel', return_value=OrchCfgModel())
    def test_get_service_timeout_300_in_cep_cfg(self, mock_cust_ep, mock_ep_tenant, mock_cp,
                                 mock_cs, mock_cfg):
        self.maxDiff = None
        cust_ep_cfg3.set_param("service_timeout", 300)
        proxy = swgproxy_bringup(DB())
        timeoutSec = proxy.gpcs_envoy_service_timeout_for_lb(121, 200, 153, -1)
        self.assertEqual(timeoutSec, 300)


    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel', return_value=cust_ep_cfg)
    @patch('libs.cloud_providers.common.swgproxy_bringup.ExplicitProxyTenantInfoModel', return_value=ep_tenant_info)
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx', return_value=2)
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel', return_value=CustomerModel(24, DB()))
    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_compute_region_name_from_edge_region_idx',
           return_value='us-west2')
    def test_add_salt_profile_for_ep_region_aws(self, mock_cust_ep, mock_ep_tenant, mock_cp,
                                                                    mock_cs, mock_region):
        """
            for AWS, listener_proxy_protocol should be enabled in envoy userdata
        """
        self.maxDiff = None
        ep_tenant_info.enable_tls_term_on_ep = 1
        proxy = swgproxy_bringup(DB())
        proxy.add_salt_profile_for_ep_region(24, 220)
        sp = json.loads(cust_ep_cfg.salt_profile)
        userdata = json.loads(sp["user_data"])
        self.assertEqual(userdata.get("listener_proxy_protocol", None), 1)

    @pytest.fixture
    def swgproxy(self):
        return swgproxy_bringup(DB())

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel')
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel')
    def test_perform_ip_reservation_for_cnat_gcp_clean_ip_enabled(self, mock_customer_model, mock_cust_epaas_config_model, swgproxy, mock_db):
        mock_customer_model.return_value.gcp_is_clean_ip_project.return_value = True
        mock_cust_epaas_config_model.return_value.get_entry.return_value = True
        mock_cust_epaas_config_model.return_value.get_params.return_value = 1

        cnat_param_dict = {
            "cnat_min_count": 2,
            "cnat_public_ips": ["*******", "*******"]
        }

        with patch.object(swgproxy, 'get_cnat_ip_v2', return_value=(True, ["*******", "*******"])):
            result = swgproxy.perform_ip_reservation_for_cnat_gcp(mock_db, 123, 456, cnat_param_dict)

        assert result["ok"] == True
        assert result["ip_list"] == ["*******", "*******"]
        mock_cust_epaas_config_model.return_value.set_param.assert_called_once_with("cnat_public_ips", "*******, *******")
        mock_cust_epaas_config_model.return_value.save.assert_called_once()

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel')
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel')
    def test_perform_ip_reservation_for_cnat_gcp_clean_ip_disabled(self, mock_customer_model, mock_cust_epaas_config_model, swgproxy, mock_db):
        mock_customer_model.return_value.gcp_is_clean_ip_project.return_value = False

        result = swgproxy.perform_ip_reservation_for_cnat_gcp(mock_db, 123, 456, {})

        assert result["ok"] == False
        assert "Ips created dynamically for non clean ip tenants" in mock_db.logger.info.call_args[0][0]

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel')
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel')
    def test_perform_ip_reservation_for_cnat_gcp_feature_not_enabled(self, mock_customer_model, mock_cust_epaas_config_model, swgproxy, mock_db):
        mock_customer_model.return_value.gcp_is_clean_ip_project.return_value = True
        mock_cust_epaas_config_model.return_value.get_entry.return_value = False

        result = swgproxy.perform_ip_reservation_for_cnat_gcp(mock_db, 123, 456, {})

        assert result["ok"] == False
        assert result["err_msg"] == "Cloud nat feature not enabled"

    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel')
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel')
    def test_perform_ip_reservation_for_cnat_gcp_exception(self, mock_customer_model, mock_cust_epaas_config_model, swgproxy, mock_db):
        mock_customer_model.return_value.gcp_is_clean_ip_project.side_effect = Exception("Test exception")

        result = swgproxy.perform_ip_reservation_for_cnat_gcp(mock_db, 123, 456, {})

        assert result["ok"] == False
        assert "Test exception" in result["err_msg"]
        mock_db.logger.error.assert_called_once()


    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx')
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel')
    def test_gpcs_get_cnat_params_custom_values(self, mock_cust_epaas_config, mock_get_cloud_type, swgproxy, mock_db):
        mock_get_cloud_type.return_value = 'gcp'
        mock_cust_epaas_config_instance = MagicMock()
        mock_cust_epaas_config_instance.get_entry.return_value = True
        mock_cust_epaas_config_instance.get_param.side_effect = [
            '1', '2', '3', '1024', '65535', '10.0.0.0/24', '4', '*******, *******', '1', '1'
        ]
        mock_cust_epaas_config.return_value = mock_cust_epaas_config_instance

        orch_cfg = {}
        result = swgproxy.gpcs_get_cnat_params(orch_cfg, 123, 456, 'gcp', 153)

        expected = {
            'cnat_enabled': '1',
            'cnat_min_count': '2',
            'cnat_max_count': '3',
            'cnat_vm_min_port': '1024',
            'cnat_vm_max_port': '65535',
            'ip_range': '10.0.0.0/24',
            'cnat_ntuple_hash': '4',
            'cnat_public_ips': '*******, *******',
            'cnat_mon_enabled': '1',
            'cnat_exclsv_enabled': '1'
        }
        assert result == expected

    @patch('libs.cloud_providers.common.swgproxy_bringup.gpcs_get_cloud_type_from_region_idx')
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustEpaasConfigModel')
    def test_gpcs_get_cnat_params_default_values(self, mock_cust_epaas_config, mock_get_cloud_type, swgproxy, mock_db):
        mock_get_cloud_type.return_value = 'gcp'
        mock_cust_epaas_config_instance = MagicMock()
        mock_cust_epaas_config_instance.get_entry.return_value = False
        mock_cust_epaas_config.return_value = mock_cust_epaas_config_instance

        orch_cfg = {
            'cnat_enabled': '0',
            'cnat_min_count': 1,
            'cnat_max_count': 16,
            'cnat_vm_min_port': 512,
            'cnat_vm_max_port': 32768,
            'ipCidrRange': '/32',
            'cnat_ntuple_hash': 2,
            'cnat_mon_enabled': '1',
            'cnat_exclsv_enabled': '0'
        }
        result = swgproxy.gpcs_get_cnat_params(orch_cfg, 123, 456, 'gcp', 153)

        expected = {
            'cnat_enabled': '0',
            'cnat_min_count': 1,
            'cnat_max_count': 16,
            'cnat_vm_min_port': 512,
            'cnat_vm_max_port': 32768,
            'ip_range': '/32',
            'cnat_ntuple_hash': 2,
            'cnat_public_ips': None,
            'cnat_mon_enabled': '1',
            'cnat_exclsv_enabled': '0'
        }
        assert result == expected


class TestGetCnatIpV2(TestCase):

    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    def setUp(self):
        self.dbh = MagicMock()
        self.logger = MagicMock()
        self.dbh.logger = self.logger
        self.swgproxy = swgproxy_bringup(self.dbh)
        self.swgproxy.ipam_utils = MagicMock()

    @patch('libs.cloud_providers.common.swgproxy_bringup.is_ipam_service_enabled', return_value=IPAMService.ENABLED)
    @patch('libs.cloud_providers.common.swgproxy_bringup.RegionMasterModel')
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel')
    def test_get_cnat_ip_v2_ipam_enabled_allocate_ips(self, mock_customer, mock_region, mock_ipam_enabled):
        # Setup
        mock_region_instance = MagicMock()
        mock_region_instance.get_param.side_effect = ['us-west1', 'gcp', 'San Jose', 'US']
        mock_region.return_value = mock_region_instance

        mock_customer_instance = MagicMock()
        mock_customer_instance.get_param.return_value = "customer123"
        mock_customer.return_value = mock_customer_instance

        self.swgproxy.ipam_utils.ipam_fetch_address.return_value = ['*******']
        self.swgproxy.ipam_utils.ipam_allocate_address.return_value = ['*******', '*******']

        # Execute
        success, ip_list = self.swgproxy.get_cnat_ip_v2(123, 456, 3, [])

        # Assert
        self.assertTrue(success)
        self.assertEqual(ip_list, ['*******', '*******', '*******'])
        self.swgproxy.ipam_utils.ipam_fetch_address.assert_called_once()
        self.swgproxy.ipam_utils.ipam_allocate_address.assert_called_once_with(
            match_location='us-west1',
            match_city='San Jose',
            match_country='US',
            match_cloud='gcp',
            match_account_id='customer123',
            match_node_type=181,
            match_count=2,
            set_status='active',
            set_service_id='ep-cloud-nat-ip',
            call_context='orch:get_cnat_ip_v2'
        )

    @patch('libs.cloud_providers.common.swgproxy_bringup.is_ipam_service_enabled', return_value=IPAMService.ENABLED)
    @patch('libs.cloud_providers.common.swgproxy_bringup.RegionMasterModel')
    @patch('libs.cloud_providers.common.swgproxy_bringup.CustomerModel')
    def test_get_cnat_ip_v2_ipam_enabled_return_ips(self, mock_customer, mock_region, mock_ipam_enabled):
        # Setup
        mock_region_instance = MagicMock()
        mock_region_instance.get_param.side_effect = ['us-west1', 'gcp', 'San Jose', 'US']
        mock_region.return_value = mock_region_instance

        mock_customer_instance = MagicMock()
        mock_customer_instance.get_param.return_value = "customer123"
        mock_customer.return_value = mock_customer_instance

        self.swgproxy.ipam_utils.ipam_fetch_address.return_value = ['*******', '*******', '*******', '*******']

        # Execute
        success, ip_list = self.swgproxy.get_cnat_ip_v2(123, 456, 2, [])

        # Assert
        self.assertTrue(success)
        self.assertEqual(ip_list, ['*******', '*******'])
        self.swgproxy.ipam_utils.ipam_fetch_address.assert_called_once()
        self.swgproxy.ipam_utils.ipam_update_address.assert_called_once_with(
            match_address=['*******/32', '*******/32'],
            set_service_id='',
            set_status='reserved',
            call_context='orch:get_cnat_ip_v2'
        )


class TestGetCsRegion(TestCase):

    def setUp(self):
        self.dbh = MagicMock()
        self.logger = MagicMock()
        self.dbh.logger = self.logger
        self.swgproxy = swgproxy_bringup(self.dbh)
        self.swgproxy.logger = self.logger

    @patch('libs.cloud_providers.common.swgproxy_bringup.sys_get_cloud_provider_name')
    @patch('libs.cloud_providers.common.swgproxy_bringup.execute_orch_query')
    def test_get_cs_region_success(self, mock_execute_query, mock_get_cloud_provider_name):
        mock_get_cloud_provider_name.return_value = 'gcp'
        mock_execute_query.return_value = (True, [['us-west1-cs.cs-dev.sasedp.panclouddev.com']])
        cfg = {}

        result = self.swgproxy.get_cs_region('us-west1', 2, cfg)

        self.assertEqual(result, 'us-west1-cs.cs-dev.sasedp.panclouddev.com')
        mock_execute_query.assert_called_once_with(
            self.dbh,
            "select cs_endpoint from cs_region_mapping where pa_compute_region = %s and cloud_provider = %s",
            ('us-west1', 'gcp'),
            "fetchall"
        )

    @patch('libs.cloud_providers.common.swgproxy_bringup.sys_get_cloud_provider_name')
    @patch('libs.cloud_providers.common.swgproxy_bringup.execute_orch_query')
    def test_get_cs_region_query_failure(self, mock_execute_query, mock_get_cloud_provider_name):
        mock_get_cloud_provider_name.return_value = 'aws'
        mock_execute_query.return_value = (False, [])
        cfg = {'cs_endpoint_api': 'cs-dev.sasedp.panclouddev.com'}

        result = self.swgproxy.get_cs_region('us-east-1', 1, cfg)

        self.assertIsNone(result)
        self.logger.error.assert_called_once_with("failed to fetch cs_region endpoint for region us-east-1")

    @patch('libs.cloud_providers.common.swgproxy_bringup.sys_get_cloud_provider_name')
    @patch('libs.cloud_providers.common.swgproxy_bringup.execute_orch_query')
    def test_get_cs_region_empty_result(self, mock_execute_query, mock_get_cloud_provider_name):
        mock_get_cloud_provider_name.return_value = 'azure'
        mock_execute_query.return_value = (True, [[]])
        cfg = {'cs_endpoint_api': 'cs-dev.sasedp.panclouddev.com'}

        result = self.swgproxy.get_cs_region('eastus', 3, cfg)

        self.assertIsNone(result)
