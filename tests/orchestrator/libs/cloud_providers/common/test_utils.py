import pytest
import mock_imports_orchestrator

from unittest.mock import patch, MagicMock
from libs.common.shared.sys_utils import NODE_TYPE_REMOTE_NET, NODE_TYPE_SERVICE_CONN
from libs.common import utils
from libs.model.instancemodel import InstanceModel
import mock_models
from libs.cfg import *
import logging
from libs.cloud_providers.common.utils import send_sns_msg_for_spr_update

class TestSendSnsForSprUpdate:

    @patch('libs.cloud_providers.common.utils.dbconn')
    @patch('libs.cloud_providers.common.utils.send_sns_msg_for_db_change')
    def test_send_sns_msg_for_spr_update_success(self, mock_send_sns, mock_dbconn, caplog):
        # Setup
        logger = logging.getLogger('testlogger')
        caplog.set_level(logging.INFO)
        mock_dbconn.execute_lambda_query.return_value = {
            'ok': True,
            'result': [['12345']]
        }

        # Call the function
        send_sns_msg_for_spr_update(100, 200, logger)

        # Assertions
        assert "Tenant id = 12345 for custid 200" in caplog.text
        assert mock_dbconn.execute_lambda_query.called
        assert mock_send_sns.called

        # Verify SNS message structure
        call_args = mock_send_sns.call_args[0][0]
        assert call_args['table'] == "instance_master"
        assert call_args['action'] == "Update"
        assert call_args['row_id'] == 100
        assert call_args['tenant_id'] == "12345"
        assert 'timestamp' in call_args

    @patch('libs.cloud_providers.common.utils.dbconn')
    @patch('libs.cloud_providers.common.utils.send_sns_msg_for_db_change')
    def test_send_sns_msg_for_spr_update_db_failure(self, mock_send_sns, mock_dbconn, caplog):
        # Setup
        logger = logging.getLogger('testlogger')
        caplog.set_level(logging.ERROR)
        mock_dbconn.execute_lambda_query.return_value = {
            'ok': False,
            'result': None
        }

        # Call the function
        send_sns_msg_for_spr_update(100, 200, logger)

        # Assertions
        assert "Failed to find tenant id in RDS for custid: 200" in caplog.text
        assert mock_dbconn.execute_lambda_query.called
        assert not mock_send_sns.called

    @patch('libs.cloud_providers.common.utils.dbconn')
    @patch('libs.cloud_providers.common.utils.send_sns_msg_for_db_change')
    def test_send_sns_msg_for_spr_update_empty_result(self, mock_send_sns, mock_dbconn, caplog):
        # Setup
        logger = logging.getLogger('testlogger')
        caplog.set_level(logging.ERROR)
        mock_dbconn.execute_lambda_query.return_value = {
            'ok': True,
            'result': []
        }

        # Call the function - should raise IndexError but be caught
        send_sns_msg_for_spr_update(100, 200, logger)

        # Assertions
        assert "send_sns_msg_for_spr_update failed" in caplog.text
        assert mock_dbconn.execute_lambda_query.called
        assert not mock_send_sns.called

    @patch('libs.cloud_providers.common.utils.dbconn')
    @patch('libs.cloud_providers.common.utils.send_sns_msg_for_db_change', side_effect=Exception("SNS Error"))
    def test_send_sns_msg_for_spr_update_sns_failure(self, mock_send_sns, mock_dbconn, caplog):
        # Setup
        logger = logging.getLogger('testlogger')
        caplog.set_level(logging.ERROR)
        mock_dbconn.execute_lambda_query.return_value = {
            'ok': True,
            'result': [['12345']]
        }

        # Call the function
        send_sns_msg_for_spr_update(100, 200, logger)

        # Assertions
        assert "send_sns_msg_for_spr_update failed" in caplog.text
        assert "SNS Error" in caplog.text
        assert mock_dbconn.execute_lambda_query.called
        assert mock_send_sns.called

    @patch('libs.cloud_providers.common.utils.dbconn')
    @patch('libs.cloud_providers.common.utils.send_sns_msg_for_db_change')
    def test_send_sns_msg_for_spr_update_with_null_values(self, mock_send_sns, mock_dbconn, caplog):
        # Setup
        logger = logging.getLogger('testlogger')
        caplog.set_level(logging.ERROR)
        mock_dbconn.execute_lambda_query.return_value = {
            'ok': True,
            'result': [[None]]
        }

        # Call the function
        send_sns_msg_for_spr_update(None, None, logger)

        # Assertions
        assert "Failed to find tenant id in RDS for custid: None" in caplog.text
        assert mock_dbconn.execute_lambda_query.called

    def test_get_default_onramp_rn_capacity_type_expected_values(self):
        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger, sql_cpu_2std)

        instance_size, dpdk_qcount, capacity_type = utils.get_default_onramp_rn_capacity_typeget_onramp_rn_capacity_type(dbh)

        assert instance_size == "n2-standard-16"
        assert dpdk_qcount == 7
        assert capacity_type == "PA-CAP700"


