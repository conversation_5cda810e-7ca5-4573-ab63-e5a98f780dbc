import sys
import pytest
from unittest import TestCase
from unittest.mock import patch, MagicMock, ANY
import logging
logger = logging.getLogger()
import mock_imports_orchestrator
import mock_imports_pinned_instance
from mock_region_model import RegionMasterModel, DB, Service, mock_hc_response
from mock_customer_model import CustomerModelGlobal
from mock_models import MockInstanceModelStop, \
    MockPinnedInstanceUpgradeModel, \
    MockPinnedInstanceUpgradeModelNoBypass, MockInstanceModelCommitFails,\
    MockPinnedInstanceUpgradeModelFail, MockInstanceModel, \
    MockInstanceModelFail, MockInstanceModelNoBypass, \
    MockInstanceModelNoID, MockInstanceModelTwoPhaseUpgradePreUpgradeFailure, \
    MockInstanceModelTwoPhaseUpgradePreUpgradeSuccess, \
    MockPinnedInstanceUpgradeModelTwoPhaseUpgrade, \
    MockInstanceModelTwoPhaseUpgradeCompleted, \
    MockPinnedInstanceUpgradeModelTwoPhaseUpgradeStuckInR53, \
    MockPinnedInstanceUpgradeModelTwoPhaseUpgradeR53RetryLimitReached

from libs.cloud_providers.common.pinnedInstanceUpgradeBringup import pinned_instance_upgarde_bringup_processing

class Mock_conn():
    def __init__(self):
        pass

    def close(self):
        pass

class Mock_cursor():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params:
            query = self.sql%self.params
        else:
            query = self.sql
        pass

    def fetchone(self):
        pass

class Mock_DBHandler():
    def __init__(self):
        self.logger = logging.getLogger('testlogger')
        self.conn = Mock_conn()

    def conn(self):
        return self.conn

    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return

class logger():
    def info(self, *args):
        pass

    def error(self, *args):
        pass

    def warn(self, *args):
        pass

class TestPinnedInstanceUpgradeBringup(TestCase):
    
    def test_process_instance_transition(self):
        
        dbh = DB()
        process_obj = pinned_instance_upgarde_bringup_processing(dbh)
        ret = process_obj.process_instance_transition()

        assert ret == 0
    

    def test_process_instance_transition_finish(self):
        
        with patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', wraps=MockPinnedInstanceUpgradeModel) as mock_pinned_model:
            with patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', wraps=MockInstanceModel) as mock_instance_model:
                dbh = DB()
                process_obj = pinned_instance_upgarde_bringup_processing(dbh)
                ret = process_obj.process_instance_transition()

                assert ret == 0

    def test_process_instance_transition_vm_not_ready(self):
        
        with patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', wraps=MockPinnedInstanceUpgradeModel) as mock_pinned_model:
            with patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', wraps=MockInstanceModelStop) as mock_instance_model:
                dbh = DB()
                MockPinnedInstanceUpgradeModel.set_param = MagicMock()
                process_obj = pinned_instance_upgarde_bringup_processing(dbh)
                ret = process_obj.process_instance_transition()

                
                assert ret == 0
    
    def test_process_instance_transition_no_bypass(self):
        with patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', wraps=MockPinnedInstanceUpgradeModelNoBypass) as mock_pinned_model:
            with patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', wraps=MockInstanceModelNoBypass) as mock_instance_model:
                dbh = DB()
                MockPinnedInstanceUpgradeModelNoBypass.set_param = MagicMock()
                MockInstanceModelNoBypass.set_param = MagicMock()
                process_obj = pinned_instance_upgarde_bringup_processing(dbh)
                ret = process_obj.process_instance_transition()

                
                assert ret == 0
    
    def test_process_instance_transition_fail(self):
        with patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', wraps=MockPinnedInstanceUpgradeModelFail) as mock_pinned_model:
            with patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', wraps=MockInstanceModelFail) as mock_instance_model:
                dbh = DB()
                process_obj = pinned_instance_upgarde_bringup_processing(dbh)
                process_obj.pinned_gp_gateway_instance_upgrade_transition_error_log = MagicMock()
                ret = process_obj.process_instance_transition()

                assert ret == 1

    def test_process_instance_transition_fail_no_id(self):
        
        with patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', wraps=MockPinnedInstanceUpgradeModel) as mock_pinned_model:
            with patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', wraps=MockInstanceModelNoID) as mock_instance_model:
                dbh = DB()
                process_obj = pinned_instance_upgarde_bringup_processing(dbh)
                process_obj.pinned_gp_gateway_instance_upgrade_transition_error_log = MagicMock()
                ret = process_obj.process_instance_transition()

                assert ret == 1
                
        
    def test_process_instance_transition_commit_fails(self):
        
        with patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', wraps=MockPinnedInstanceUpgradeModelNoBypass) as mock_pinned_model:
            with patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', wraps=MockInstanceModelCommitFails) as mock_instance_model:
                dbh = DB()
                dbh.logger.error = MagicMock()
                process_obj = pinned_instance_upgarde_bringup_processing(dbh)
                process_obj.pinned_gp_gateway_instance_upgrade_transition_error_log = MagicMock()
                ret = process_obj.process_instance_transition()


                assert ret == 2

    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.send_ngpa_cust_alert', return_value = True)
    def test_pinned_gp_gateway_instance_upgrade_bringup_error_log(self, mock_send_alerts, caplog):
        caplog.set_level(logging.INFO)
        dbh=Mock_DBHandler()
        process_obj = pinned_instance_upgarde_bringup_processing(dbh)
        mocked_model = MockPinnedInstanceUpgradeModel()
        process_obj.pinned_gp_gateway_instance_upgrade_bringup_error_log(mocked_model)

    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.send_ngpa_cust_alert', return_value = True)
    def test_pinned_gp_gateway_instance_upgrade_transition_error_log(self, mock_send_alerts, caplog):
        caplog.set_level(logging.INFO)
        dbh=Mock_DBHandler()
        process_obj = pinned_instance_upgarde_bringup_processing(dbh)
        mocked_model = MockPinnedInstanceUpgradeModel()
        process_obj.pinned_gp_gateway_instance_upgrade_transition_error_log(mocked_model)

    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.send_ngpa_cust_alert', return_value = True)
    def test_pinned_gp_gateway_instance_upgrade_delete_old_error_log(self, mock_send_alerts, caplog):
        caplog.set_level(logging.INFO)
        dbh=Mock_DBHandler()
        process_obj = pinned_instance_upgarde_bringup_processing(dbh)
        mocked_model = MockPinnedInstanceUpgradeModel(dbh)
        process_obj.pinned_gp_gateway_instance_upgrade_delete_old_error_log(logger, mocked_model)
    
class TestPinnedInstanceUpgradeBringupProcessOldInstanceDelete:
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', return_value=MockInstanceModelTwoPhaseUpgradePreUpgradeSuccess(dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', return_value=MockPinnedInstanceUpgradeModelTwoPhaseUpgrade(dbh=Mock_DBHandler(), instance_id=9000, shard_id=100, replica_size=10))
    def test_process_old_instance_delete_new_instance_pre_upgrade_completed(self, patched_PinnedInstanceUpgradeModel, patched_InstanceModel, caplog):
        caplog.set_level(logging.INFO)
        dbh=Mock_DBHandler()
        process_obj = pinned_instance_upgarde_bringup_processing(dbh)
        ret = process_obj.process_old_instance_delete()
        assert 'Complete all needed actions and trigger the old instance deletion' in caplog.text
        assert 'Update upgrade_status to READY-TO-UPGRADE' in caplog.text
        assert 'All conditions met for deletion of old instance; go ahead with deletion/cleanup' not in caplog.text
        assert ret is True
    
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.publish_ngpa_notifications_to_cosmos', return_value="Success")
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', return_value=MockInstanceModelTwoPhaseUpgradePreUpgradeSuccess(dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', return_value=MockPinnedInstanceUpgradeModelTwoPhaseUpgradeStuckInR53(dbh=Mock_DBHandler(), instance_id=9000, shard_id=100, replica_size=10))
    def test_process_old_instance_delete_new_instance_stuck_in_r53_msg(self, patched_PinnedInstanceUpgradeModel, patched_InstanceModel, mock_publish, caplog):
        caplog.set_level(logging.INFO)
        dbh=Mock_DBHandler()
        process_obj = pinned_instance_upgarde_bringup_processing(dbh)
        ret = process_obj.process_old_instance_delete()
        assert 'Retrying r53 message: max_retries' in caplog.text
        assert 'Update upgrade_status to READY-TO-UPGRADE' in caplog.text
        assert ret is True

    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.check_nat_tunnel_status_for_vmid', return_value=False)
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.find_nr_nat_instances_by_custid_and_region_id', return_value=(True, 1, 2))
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.publish_ngpa_notifications_to_cosmos', return_value="Success")
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', return_value=MockInstanceModelTwoPhaseUpgradePreUpgradeSuccess(dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', return_value=MockPinnedInstanceUpgradeModelTwoPhaseUpgradeStuckInR53(dbh=Mock_DBHandler(), instance_id=9000, shard_id=100, replica_size=10))
    def test_process_old_instance_delete_new_instance_nat_tunnel_down(self, patched_PinnedInstanceUpgradeModel, patched_InstanceModel, mock_publish, mock_find_nr_nat, mock_check_nat_tunnel, caplog):
        caplog.set_level(logging.INFO)
        dbh=Mock_DBHandler()
        process_obj = pinned_instance_upgarde_bringup_processing(dbh)
        ret = process_obj.process_old_instance_delete()
        assert 'NAT tunnel status check failed for new instance' in caplog.text
        assert 'marking the upgrade as failed' in caplog.text
        assert ret is True

    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.publish_ngpa_notifications_to_cosmos', return_value="Success")
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', return_value=MockInstanceModelTwoPhaseUpgradePreUpgradeSuccess(dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', return_value=MockPinnedInstanceUpgradeModelTwoPhaseUpgradeR53RetryLimitReached(dbh=Mock_DBHandler(), instance_id=9000, shard_id=100, replica_size=10))
    def test_process_old_instance_delete_new_instance_stuck_in_r53_msg(self, patched_PinnedInstanceUpgradeModel, patched_InstanceModel, mock_publish, caplog):
        caplog.set_level(logging.INFO)
        dbh=Mock_DBHandler()
        process_obj = pinned_instance_upgarde_bringup_processing(dbh)
        ret = process_obj.process_old_instance_delete()
        assert 'Reached maximum allowed retries for' in caplog.text
        assert ret is True

    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', return_value=MockInstanceModelTwoPhaseUpgradePreUpgradeFailure(dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', return_value=MockPinnedInstanceUpgradeModelTwoPhaseUpgrade(dbh=Mock_DBHandler(), instance_id=9000, shard_id=100, replica_size=10))
    def test_process_old_instance_delete_new_instance_pre_upgrade_failed(self, patched_PinnedInstanceUpgradeModel, patched_InstanceModel, caplog):
        caplog.set_level(logging.INFO)
        dbh=Mock_DBHandler()
        process_obj = pinned_instance_upgarde_bringup_processing(dbh)
        ret = process_obj.process_old_instance_delete()
        assert 'May do no updates; since upgrade_status for the new instance: 118 may be invalid for the second phase of the upgrade: READY-TO-UPGRADE' in caplog.text
        assert 'New instance 118 is still not in COMPLETED-UPGRADE state; wait before deleting the old instance' in caplog.text
        assert ret is False

    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', return_value=MockInstanceModelTwoPhaseUpgradeCompleted(dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', return_value=MockPinnedInstanceUpgradeModelTwoPhaseUpgrade(dbh=Mock_DBHandler(), instance_id=9000, shard_id=100, replica_size=10))
    def test_process_old_instance_delete_new_instance_upgrade_completed(self, patched_PinnedInstanceUpgradeModel, patched_InstanceModel, caplog):
        caplog.set_level(logging.INFO)
        dbh=Mock_DBHandler()
        process_obj = pinned_instance_upgarde_bringup_processing(dbh)
        ret = process_obj.process_old_instance_delete()
        assert 'Completed all actions successfully for new instance 118; go ahead with DNS update and deletion of old instance' in caplog.text
        assert 'All conditions met for deletion of old instance; go ahead with deletion/cleanup' in caplog.text
        assert ret is True


    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', return_value=MockInstanceModelTwoPhaseUpgradeCompleted(dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', return_value=MockPinnedInstanceUpgradeModelTwoPhaseUpgrade(dbh=Mock_DBHandler(), instance_id=9000, shard_id=100, replica_size=10))
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.is_dhcp_pool_allocation_enabled', return_value=1)
    def test_process_old_instance_delete_dhcp_allocation_enabled(self, patched_dhcp_allocation_check, patched_PinnedInstanceUpgradeModel, patched_InstanceModel, caplog):
        caplog.set_level(logging.INFO)
        dbh=Mock_DBHandler()
        process_obj = pinned_instance_upgarde_bringup_processing(dbh)
        ret = process_obj.process_old_instance_delete()
        assert 'DHCP Pool allocation enabled for tenant; no reuse of IP for old instances will be done' in caplog.text
        assert 'Completed all actions successfully for new instance 118; go ahead with DNS update and deletion of old instance' in caplog.text
        assert 'All conditions met for deletion of old instance; go ahead with deletion/cleanup' in caplog.text
        assert ret is True


    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', return_value=MockInstanceModelTwoPhaseUpgradeCompleted(dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', return_value=MockPinnedInstanceUpgradeModelTwoPhaseUpgrade(dbh=Mock_DBHandler(), instance_id=9000, shard_id=100, replica_size=10))
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.is_dhcp_pool_allocation_enabled', return_value=0)
    def test_process_old_instance_delete_dhcp_allocation_disabled(self, patched_dhcp_allocation_check, patched_PinnedInstanceUpgradeModel, patched_InstanceModel, caplog):
        caplog.set_level(logging.INFO)
        dbh=Mock_DBHandler()
        process_obj = pinned_instance_upgarde_bringup_processing(dbh)
        ret = process_obj.process_old_instance_delete()
        assert 'DHCP Pool allocation enabled for tenant; no reuse of IP for old instances will be done' in caplog.text
        assert 'Completed all actions successfully for new instance 118; go ahead with DNS update and deletion of old instance' in caplog.text
        assert 'Successfully updated pool_allocation_table' in caplog.text
        assert ret is True

    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel.check_nat_tunnel_status_for_vmid', return_value=False)
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel.find_nat_configs_by_custid_and_region_id', return_value=(True,0,2,7200))
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.InstanceModel', return_value=MockInstanceModelTwoPhaseUpgradePreUpgradeSuccess(dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.pinnedInstanceUpgradeBringup.PinnedInstanceUpgradeModel', return_value=MockPinnedInstanceUpgradeModelTwoPhaseUpgrade(dbh=Mock_DBHandler(), instance_id=9000, shard_id=100, replica_size=10))
    def test_process_old_instance_delete_new_instance_tunnel_check_timeout(self, patched_PinnedInstanceUpgradeModel, patched_InstanceModel, mock_find_nat_config, mock_check_nat, caplog):
        caplog.set_level(logging.INFO)
        dbh=Mock_DBHandler()
        process_obj = pinned_instance_upgarde_bringup_processing(dbh)
        ret = process_obj.process_old_instance_delete()
        assert 'NAT tunnel status check failed for new instance' in caplog.text