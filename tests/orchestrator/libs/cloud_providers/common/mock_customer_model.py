#!/usr/bin/python

class CustomerModelGlobal():
    def __init__(self, custid=None, super_acct_id=None, acct_id=None, logger=None):
        self.id = 1
        self.name = None
        self.acct_id = 123456
        self.project_id = "testcurrentproject-01"
        self.eproxy_image_version = None
        if custid == 123:
            self.eproxy_image_version = "customer_ep_version"

        self.fields = ["id",
                       "name",
                       "acct_id",
                       "project_id",
                       "eproxy_image_version"
                       ]
        self.values = [None] * len(self.fields)
        for attr in self.fields:
            self.set_param(attr, self.__getattribute__(attr))

    def get_param(self, field):
        myidx = self.fields.index(field)
        return self.values[myidx]

    def set_param(self, field, value):
        if field in self.fields:
            myidx = self.fields.index(field)
            self.values[myidx] = value
        else:
            raise Exception(f"field {field} does not exist")
