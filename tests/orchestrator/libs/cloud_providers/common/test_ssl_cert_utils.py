import sys
from unittest import TestCase
from unittest.mock import patch
import mock_imports_upgrade_ep_os
from libs.cloud_providers.common.ssl_cert_utils import delete_redundant_ssl_certs
from mock_region_model import DB, Service
import logging
logger = logging.getLogger()



class TestSSlCertDeletion:

    @patch('libs.cloud_providers.common.ssl_cert_utils.get_compute', return_value=Service())
    @patch('libs.cloud_providers.common.ssl_cert_utils.get_primary_instance', return_value=1)
    def test_update_envoy_details_for_cust_epaas(self, mock_service, mock_gp, caplog):
        caplog.set_level(logging.INFO)
        response = delete_redundant_ssl_certs(DB(), {},  123, 201)
        assert "No redundant ssl certs for custid " in caplog.text
        assert response == True

