import sys
from mock_models import Boto3
from unittest.mock import MagicMock
sys.modules["boto3"] = Boto3()
sys.modules["libs.model.custEpaasConfigModel"] = MagicMock()
sys.modules["libs.model.custmodel"] = MagicMock()
sys.modules["libs.model.orchcfgmodel_v2"] = MagicMock()
sys.modules["libs.common.shared.sys_utils"] = MagicMock(PROVIDER_GCP='2')
sys.modules["libs.common.shared.gcp_utils"] = MagicMock()
sys.modules["libs.model.IP_management_model"] = MagicMock()
sys.modules["libs.common.shared.utils"] = MagicMock(get_aws_partition=MagicMock(return_value="aws"))
sys.modules["libs.common.shared.py3_utils"] = MagicMock(b64encode=MagicMock(return_value="testlog123"))
sys.modules["libs.common.shared"] = MagicMock()
