from unittest.mock import MagicMock

def get_client():
    return Client()

class Client:
    def get_alert_policy(self, request):
        return response

    def update_alert_policy(self, request):
        return response

class Request:
    def __init__(self):
        self.conditions = MagicMock()
        self.alert_policy = MagicMock()

class Monitoring_v3:

    def GetAlertPolicyRequest(self, name):
        return name

    def UpdateAlertPolicyRequest(self):
        return Request()

class resourcemanager_v3:

    def ProjectsClient(self, credentials):
        return Name

class Name():
    def get_project(self, name):
        return Project

class Project():
    def __init__(self):
        self.name = "project/1234567"

class pubsub_v1:

    def PublisherClient(self, credentials):
        return pub_client


class res_client():
    def get_project(self, name):
        return 123456

class pub_client():
    def get_project(self, name):
        return 123456

    def topic_path(self,project_id, topic_id):
        return "path123"

    def get_iam_policy(self, topic_path):
        return {
            "bindings": []
        }

    def set_iam_policy(self, topic_path, policy):
        return policy



response = {
  "name": "projects/cust-gpcs-mwernosr5hj1l6v29jv3/alertPolicies/18440726180537645888",
  "displayName": "CNAT_Port_Utilization-us-west1",
  "documentation": {},
  "userLabels": {},
  "conditions": [
    {
      "name": "projects/cust-gpcs-mwernosr5hj1l6v29jv3/alertPolicies/18440726180537645888/conditions/6832000537402227940",
      "displayName": "High Port Utilization",
      "conditionMonitoringQueryLanguage": {
        "duration": "120s",
        "query": "    fetch gce_instance    | metric 'compute.googleapis.com/nat/port_usage'    | filter (resource.zone =~ 'us-west1.*')    | group_by 1m, [value_port_usage_mean: max(value.port_usage)]    | every 1m    | condition val() > 38707    ",
        "trigger": {
          "count": 1
        }
      }
    }
  ],
  "alertStrategy": {
    "autoClose": "259200s"
  },
  "combiner": "OR",
  "enabled": True,
  "notificationChannels": [
    "projects/cust-gpcs-mwernosr5hj1l6v29jv3/notificationChannels/1773443744320344348",
    "projects/cust-gpcs-mwernosr5hj1l6v29jv3/notificationChannels/1773443744320347311"
  ],
  "creationRecord": {
    "mutateTime": "2024-06-14T18:30:48.326799960Z",
    "mutatedBy": "<EMAIL>"
  },
  "mutationRecord": {
    "mutateTime": "2024-06-14T18:47:19.758356368Z",
    "mutatedBy": "<EMAIL>"
  },
  "severity": "CRITICAL"
}



cnat_policy_params = {
        "CNAT_Port_Utilization": {"cloud_provider": "gcp", "alert_policy_name": "CNAT_Port_Utilization",
                                 "mql_query": "    fetch gce_instance    | metric 'compute.googleapis.com/nat/port_usage'"
                                              "    | filter (resource.zone =~ '{}.*')    "
                                              "| group_by 1m, [value_port_usage_mean: max(value.port_usage)]    "
                                              "| every 1m    | condition val() > {}    ",
                                 "pubsub_topic": "projects/host-gpcs-dev-01/topics/event_epcnatscale_topic-dev11.panclouddev.com",
                                 "emails": "<EMAIL>", "severity": "critical",
                                 "condition_name": "High Port Utilization",
                                 "retest_window": 2, "scaleout_factor": 0.6},
       "CNAT_Dropped_Sent_Packets": {"cloud_provider": "gcp", "alert_policy_name": "CNAT_Dropped_Sent_Packets",
                                     "pubsub_topic": "projects/host-gpcs-dev-01/topics/event_epcnatscale_topic-dev11.panclouddev.com",
                                     "emails": "<EMAIL>", "severity": "critical",
                                     "condition_name": "Dropped Sent Packets", "condition_type": "Threshold",
                                     "rolling_window_duration": 2, "rolling_window_function": "rate",
                                     "retest_window": 2, "threshold_value": 0, "scaleout_factor": 0.6}
       }


alert_policy_json ={'CNAT_Port_Utilization':'18440726180537645888'}