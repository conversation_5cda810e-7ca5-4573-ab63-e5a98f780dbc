from unittest.mock import patch, MagicMock

import pytest

import mock_imports_orchestrator
from libs.cloud_providers.common.instance_trigger_update import select_provision_engine
from mock_models import DB
from mock_customer_model import CustomerModelGlobal

select_provision_engine_test_cases = [
    # unsupported service type
    (153, 2, True, "terraform_import", True, False, "debug123", ""),
    # openstack
    (0, 4, True, "terraform_import", False, True, "debug123", "tUrl"),
    # greenfield: terraform_import
    (0, 2, False, "terraform_import", True, False, None, ""),
    # greenfield: terraform_provision
    (0, 2, False, "terraform_provision", False, True, None, "tUrl"),
    # brownfield: terraform_import
    (154, 2, True, "terraform_import", True, False, "debug123", ""),
    # brownfield: terraform_migration
    (154, 2, True, "terraform_migration", False, True, "debug123", "tUrl"),
    # brownfield: terraform_provision
    (154, 2, True, "terraform_provision", False, True, "debug123", "tUrl"),
]

class MockInstanceManagementModel():
    def __init__(self, has_entry, gcp_provision_type):
        self.has_entry = has_entry
        self.gcp_provision_type = gcp_provision_type
    def set_param(self, field, value):
        pass

    def get_param(self, field):
        if field == "gcp_provision_type":
            return self.gcp_provision_type
        elif field == "debug_id":
            return "debug123"
        else:
            return None

    def get_entry(self):
        return self.has_entry

class TestInstanceTriggerUpdate:
    @pytest.mark.parametrize("node_type, cloud_provider, has_entry, gcp_provision_type, "
                             "expected_use_dm, expected_use_prov_svc, expected_debug_id, expected_prov_url",
                             select_provision_engine_test_cases)
    @patch('libs.cloud_providers.common.instance_trigger_update.cfg')
    @patch('libs.cloud_providers.common.instance_trigger_update.is_aws_fedramp_env_govcloud', return_value=False)
    @patch('libs.cloud_providers.common.instance_trigger_update.default_gcp_provision_type')
    @patch('libs.cloud_providers.common.instance_trigger_update.InstanceManagementModel')
    @patch('libs.cloud_providers.common.instance_trigger_update.gpcs_get_edge_region_name_from_edge_region_idx', return_value='us-west1')
    @patch('libs.model.custmodel.CustomerModel', return_value=CustomerModelGlobal())
    def test_select_provision_engine(self, mock_cust_model, mock_get_region_name, mock_inst_mgmt_model,
                                     mock_default_gcp_provision_type, mock_is_fedramp, mock_cfg,
                                     node_type, cloud_provider, has_entry, gcp_provision_type,
                                     expected_use_dm, expected_use_prov_svc, expected_debug_id, expected_prov_url):
        custid = 1
        region_id = 200
        mock_inst_mgmt_model.return_value = MockInstanceManagementModel(has_entry, gcp_provision_type)
        mock_default_gcp_provision_type.return_value = gcp_provision_type
        cfg_dict = {"orchestration_service": {"state_import_url": "sUrl", "trigger_update_url": "tUrl"}}
        mock_cfg.__getitem__.side_effect = cfg_dict.__getitem__

        use_dm, use_prov_svc, debug_id, prov_url = select_provision_engine(DB(), custid, region_id, node_type, cloud_provider)
        assert use_dm == expected_use_dm
        assert use_prov_svc == expected_use_prov_svc
        assert debug_id == expected_debug_id
        assert prov_url == expected_prov_url

    @patch('libs.cloud_providers.common.instance_trigger_update.cfg')
    @patch('libs.cloud_providers.common.instance_trigger_update.is_aws_fedramp_env_govcloud', return_value=False)
    @patch('libs.cloud_providers.common.instance_trigger_update.default_gcp_provision_type')
    @patch('libs.cloud_providers.common.instance_trigger_update.InstanceManagementModel')
    @patch('libs.cloud_providers.common.instance_trigger_update.gpcs_get_edge_region_name_from_edge_region_idx',
           return_value='us-west1')
    @patch('libs.model.custmodel.CustomerModel', return_value=CustomerModelGlobal())
    def test_select_provision_engine_cfg_key_error(self, mock_cust_model, mock_get_region_name, mock_inst_mgmt_model,
                                                   mock_default_gcp_provision_type, mock_is_fedramp, mock_cfg):
        """Test that select_provision_engine raises exception when cfg access results in KeyError"""
        custid = 1
        region_id = 200
        node_type = 154
        cloud_provider = 2
        has_entry = True
        gcp_provision_type = "terraform_provision"

        mock_inst_mgmt_model.return_value = MockInstanceManagementModel(has_entry, gcp_provision_type)
        mock_default_gcp_provision_type.return_value = gcp_provision_type

        # Mock cfg to raise KeyError when accessing orchestration_service
        mock_cfg.__getitem__.side_effect = KeyError("orchestration_service")

        # Test that the function raises an exception when cfg access fails
        with pytest.raises(KeyError) as exc_info:
            select_provision_engine(DB(), custid, region_id, node_type, cloud_provider)

        assert str(exc_info.value) == "'orchestration_service'"

    @patch('libs.cloud_providers.common.instance_trigger_update.cfg')
    @patch('libs.cloud_providers.common.instance_trigger_update.is_aws_fedramp_env_govcloud', return_value=False)
    @patch('libs.cloud_providers.common.instance_trigger_update.default_gcp_provision_type')
    @patch('libs.cloud_providers.common.instance_trigger_update.InstanceManagementModel')
    @patch('libs.cloud_providers.common.instance_trigger_update.gpcs_get_edge_region_name_from_edge_region_idx',
           return_value='us-west1')
    @patch('libs.model.custmodel.CustomerModel', return_value=CustomerModelGlobal())
    def test_select_provision_engine_cfg_nested_key_error(self, mock_cust_model, mock_get_region_name,
                                                          mock_inst_mgmt_model,
                                                          mock_default_gcp_provision_type, mock_is_fedramp, mock_cfg):
        """Test that select_provision_engine raises exception when cfg nested key access results in KeyError"""
        custid = 1
        region_id = 200
        node_type = 154
        cloud_provider = 2
        has_entry = True
        gcp_provision_type = "terraform_provision"

        mock_inst_mgmt_model.return_value = MockInstanceManagementModel(has_entry, gcp_provision_type)
        mock_default_gcp_provision_type.return_value = gcp_provision_type

        # Mock cfg to return orchestration_service but missing required nested keys
        def mock_getitem(key):
            if key == "orchestration_service":
                return {}  # Empty dict, missing state_import_url and trigger_update_url
            raise KeyError(key)

        mock_cfg.__getitem__.side_effect = mock_getitem

        # Test that the function raises an exception when nested cfg access fails
        with pytest.raises(KeyError):
            select_provision_engine(DB(), custid, region_id, node_type, cloud_provider)