salt_profile = {
   "envoy_image_version":"pa-proxyvm-3-2-1-1-20221101203853",
   "project_id":"cust-gpcs-mwernosr5hj1l6v29jv3",
   "envoy_image_project":"image-gpcs-nonprod-01",
   "image_project":"image-gpcs-nonprod-01",
   "envoy_inital_cnt":"1",
   "envoy_min_cnt":"1",
   "envoy_max_cnt":"10",
   "envoy_traffic_port":"8080",
   "envoy_hc_port":"8887",
   "envoy_disc_size_gb":"40",
   "envoy_autosclale_cpu_target":"0.5",
   "user_data":"{\"ssh-keys\": \"admin:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r admin@SJCMACJ15HHTD8\", \"mtls_certpkey\": \"customer**********\", \"secret_mgr_project\": \"image-gpcs-nonprod-01\", \"cloud_provider\": 2, \"upstream-proxyprotocol\": \"v1\", \"mgmt-interface-swap\": \"enable\", \"custid_int\": 24, \"custid\": \"**********\", \"custsuperid\": \"**********\", \"svc_acct\": \"<EMAIL>\", \"pacfgds\": \"dev.cfgds.swg.panclouddev.com\", \"pagks\": \"dev.gks.swg.panclouddev.com\", \"pacdls\": \"dev.cdls.swg.panclouddev.com\", \"patgs\": \"dev.tgs.swg.panclouddev.com\", \"mtls_ca\": \"it_ca_cert\"}"
}

cfg = {
    "envoy_image_version":"pa-proxyvm-3-2-1-1-20221101203853",
   "project_id": "cust-gpcs-mwernosr5hj1l6v29jv3",
   "eproxy_image_project": "image-gpcs-nonprod-01",
   "gcp_img_host": "image-gpcs-nonprod-01",
   "envoy_inital_cnt": "1",
   "envoy_min_cnt": "1",
   "envoy_max_cnt": "10",
   "envoy_traffic_port": "8080",
   "envoy_hc_port": "8887",
   "envoy_disc_size_gb": "40",
   "envoy_autosclale_cpu_target": "0.5",
   "envoy_ssh_key": "admin:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r admin@SJCMACJ15HHTD8",
   "envoy_proxy_secret_id": "customer**********",
   "pa_proxy_config_discovery_fqdn": "dev.cfgds.swg.panclouddev.com",
   "pa_telegraph_fqdn": "dev.tgs.swg.panclouddev.com",
   "pa_proxy_services_ca_key_id": "it_ca_cert",
   "upstream-proxyprotocol-version": "v1",
   "pa_proxy_gatekeeper_fqdn" : "dev.gks.swg.panclouddev.com",
   "pa_cdl_proxy_fqdn": "dev.cdls.swg.panclouddev.com",
   "pa_proxy_sinkhole_fqdn": "all.sink.swg.panclouddev.com",
   "service_timeout": "86400",
   "cnat_enabled": '0',
   "cnat_min_count": 1,
   "cnat_max_count": 16,
   "cnat_vm_min_port": 512,
   "cnat_vm_max_port": 32768,
   "ipCidrRange": "/32",
   "cnat_ntuple_hash": 2,
   "cnat_exclsv_enabled": '0',
   "cs_endpoint_api": 'sasedp.panclouddev.com'


}
