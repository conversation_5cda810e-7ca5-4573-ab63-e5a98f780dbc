import unittest
from unittest.mock import patch, MagicMock
from libs.cloud_providers.common.ip_management.ipv6.aws.ipv6_mgmt_1 import IPV6_Handler
import mock_imports_orchestrator
import botocore
from botocore.exceptions import ClientError
import logging

from  libs.model.instancemodel import InstanceModel
class TestIPV6Handler(unittest.TestCase):

    #@patch('libs.cloud_providers.common.ip_management.ipv6.aws.ipv6_mgmt_1.dbh')
    @patch('libs.model.instancemodel.InstanceModel')
    @patch('libs.cloud_providers.common.ip_management.ipv6.aws.ipv6_mgmt_1.VpcModel')
    @patch('libs.cloud_providers.common.ip_management.ipv6.aws.ipv6_mgmt_1.RegionMasterModel')
    @patch('libs.cloud_providers.common.ip_management.ipv6.aws.ipv6_mgmt_1.boto3.resource')
    @patch('libs.cloud_providers.common.ip_management.ipv6.aws.ipv6_mgmt_1.boto3.client')
    def test_assign_ipv6_address_success(self, mock_client, mock_resource,
                                         mock_inst_model, mock_vpc_model, mock_rmm_model):
        logger = logging.getLogger()
        ec2_resource = MagicMock()
        ec2_client = MagicMock()
        mock_resource.return_value = ec2_resource
        mock_client.return_value = ec2_client
        mock_dbh = MagicMock()
        mock_dbh.logger = logger
        # Define return values
        mock_inst_model.return_value.get_param.return_value = 'i-1234'
        mock_vpc_model.return_value.get_dp_subnet1_for_zone.return_value = (True, ['subnet1'])
        mock_rmm_model.return_value.get_param.return_value = 'us-east-1'

        instance = MagicMock()
        instance.network_interfaces = [MagicMock(id="abc123", subnet=MagicMock(id='subnet1'))]
        ec2_resource.Instance.return_value = instance

        ec2_client.assign_ipv6_addresses.return_value = {'Response': 'Success'}

        handler = IPV6_Handler(mock_dbh, "12345")
        result = handler.assign_ipv6_address_to_interface('i-123', 'i-456', '2001:db8::1/80')

        self.assertTrue(result)

    def side_effect(self, *args, **kwargs):
        Exception("Error")

    @patch('libs.model.instancemodel.InstanceModel')
    @patch('libs.cloud_providers.common.ip_management.ipv6.aws.ipv6_mgmt_1.VpcModel')
    @patch('libs.cloud_providers.common.ip_management.ipv6.aws.ipv6_mgmt_1.RegionMasterModel')
    @patch('libs.cloud_providers.common.ip_management.ipv6.aws.ipv6_mgmt_1.boto3.resource')
    @patch('libs.cloud_providers.common.ip_management.ipv6.aws.ipv6_mgmt_1.boto3.client')
    def test_assign_ipv6_address_other_error(self, mock_client, mock_resource,
                                         mock_inst_model, mock_vpc_model, mock_rmm_model):
        class MyException(Exception):
            pass

        logger = logging.getLogger()
        ec2_resource = MagicMock()
        ec2_client = MagicMock()
        mock_resource.return_value = ec2_resource
        mock_client.return_value = ec2_client
        mock_dbh = MagicMock()
        mock_dbh.logger = logger
        # Define return values
        mock_inst_model.return_value.get_param.return_value = 'i-1234'
        mock_vpc_model.return_value.get_dp_subnet1_for_zone.return_value = (True, ['subnet1'])
        mock_rmm_model.return_value.get_param.return_value = 'us-east-1'

        instance = MagicMock()
        instance.network_interfaces = [MagicMock(id="abc123", subnet=MagicMock(id='subnet1'))]
        ec2_resource.Instance.return_value = instance

        ec2_client.assign_ipv6_addresses.side_effect = Exception("Invalid request")

        handler = IPV6_Handler(mock_dbh, "12345")
        result = handler.assign_ipv6_address_to_interface('i-123', 'i-456', '2001:db8::1/80')

        self.assertFalse(result)