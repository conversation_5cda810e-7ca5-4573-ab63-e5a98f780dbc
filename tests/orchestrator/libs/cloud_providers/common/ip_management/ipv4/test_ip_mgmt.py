import sys
import pytest
import json
import os
from unittest.mock import patch, MagicMock
import logging
from libs.cloud_providers.common.ip_management.ipv4.ip_mgmt import IPHandler

class Mock_cursor():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        return {"name": "test_ip_mgmt"}
    
class Mock_DBHandler():
    def __init__(self, logger):
        self.logger = logger

    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return

class Mock_ipam():
    def __init__(self, logger):
        self.logger = logger

    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return
    
class TestIPMgmt:

    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.create_ipam_utils", wraps=Mock_ipam)
    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.IPHandler._get_non_upgrade_inst_count")
    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.IPHandler._get_allocated_ip_count_in_city_country_in_location")
    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.IPHandler.find_nlb_settings_by_custid_and_region_id")
    def test_reserve_double_if_necessary_nat_and_nlb_enabled_single_instance(self, mock_find_nlb_settings, mock_get_allocated_ip_count, mock_get_non_upgrade_inst_count, mock_ipam, caplog):
        caplog.set_level(logging.INFO)
        mock_get_non_upgrade_inst_count.return_value = 1
        mock_get_allocated_ip_count.return_value = {('Dublin', 'Ireland'): 2}
        mock_find_nlb_settings.return_value = {
            'success': True,
            'is_nat_supported': True,
            'nr_instances': 1,
            'is_nlb_supported': True,
            'ingress_ip_reduction': 0,
            'cust_is_nlb_supported': True
        }

        ip_mgmt = IPHandler(Mock_DBHandler(logging.getLogger('testlogger')), 0, 0)
        result = ip_mgmt.reserve_double_if_necessary(**********, 1234, 200, 49, [('Portland', 'US')], 'gcp', 'mock_location', True, 9386)

        assert "NAT and NLB are supported, nr_instances = 1. 3 IP in total reserved for acct_id ********** in compute_region_idx 200." in caplog.text

    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.create_ipam_utils", wraps=Mock_ipam)
    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.IPHandler._get_non_upgrade_inst_count")
    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.IPHandler._get_allocated_ip_count_in_city_country_in_location")
    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.IPHandler.find_nlb_settings_by_custid_and_region_id")
    def test_reserve_double_if_necessary_nat_and_nlb_enabled_multiple_instances(self, mock_find_nlb_settings, mock_get_allocated_ip_count, mock_get_non_upgrade_inst_count, mock_ipam, caplog):
        caplog.set_level(logging.INFO)
        mock_get_non_upgrade_inst_count.return_value = 2
        mock_get_allocated_ip_count.return_value = {('Dublin', 'Ireland'): 2}
        mock_find_nlb_settings.return_value = {
            'success': True,
            'is_nat_supported': True,
            'nr_instances': 2,
            'is_nlb_supported': True,
            'ingress_ip_reduction': 0,
            'cust_is_nlb_supported': True
        }

        ip_mgmt = IPHandler(Mock_DBHandler(logging.getLogger('testlogger')), 1234, **********)
        result = ip_mgmt.reserve_double_if_necessary(**********, 1234, 200, 49, [('Dublin', 'Ireland')], 'gcp', 'mock_location', True, 9386)

        assert "Try reserve 2 IP(s)" in caplog.text
        # assert "NAT and NLB are supported, nr_instances = 2. 4 IP in total needs to reserve for acct_id 1226
        # assert (ret is True)

    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.create_ipam_utils", wraps=Mock_ipam)
    def test_reset_public_ip_pool_info(self, mock_find_nlb_settings, mock_get_allocated_ip_count, mock_get_non_upgrade_inst_count, mock_ipam, caplog):
        caplog.set_level(logging.INFO)
        ip_mgmt = IPHandler(Mock_DBHandler(logging.getLogger('testlogger')), 1234, **********)
        result = ip_mgmt.reset_public_ip_pool_info(preserve_nat_ip=True)

        assert "last_free_time=CURRENT_TIMESTAMP" in caplog.text and "transition" in caplog.text
        # assert "NAT and NLB are supported, nr_instances = 2. 4 IP in total needs to reserve for acct_id 1226
        # assert (ret is True)

    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.is_ipam_service_enabled")
    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.IPAMService")
    def test_get_subnet_name_from_ip_ipam_enabled(self, mock_ipam_service, mock_is_ipam_enabled, caplog):
        caplog.set_level(logging.INFO)
        mock_is_ipam_enabled.return_value = mock_ipam_service.ENABLED
        mock_ipam_utils = MagicMock()
        mock_ipam_utils.ipam_fetch_address.return_value = ['test_subnet']

        ip_mgmt = IPHandler(Mock_DBHandler(logging.getLogger('testlogger')), 0, 0)
        ip_mgmt.ipam_utils = mock_ipam_utils

        result = ip_mgmt.get_subnet_name_from_ip('***********')

        assert result == 'test_subnet'
        assert "subnet name for ip ***********: test_subnet" in caplog.text

    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.is_ipam_service_enabled")
    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.IPAMService")
    def test_get_subnet_name_from_ip_ipam_disabled(self, mock_ipam_service, mock_is_ipam_enabled, caplog):
        caplog.set_level(logging.INFO)
        mock_is_ipam_enabled.return_value = mock_ipam_service.DISABLED
        mock_db_handler = Mock_DBHandler(logging.getLogger('testlogger'))
        mock_cursor = mock_db_handler.get_cursor()
        mock_cursor.fetchone.return_value = ['test_subnet_db']

        ip_mgmt = IPHandler(mock_db_handler, 0, 0)

        result = ip_mgmt.get_subnet_name_from_ip('***********')

        assert result == 'test_subnet_db'
        assert "sql command to fetch subnet name: SELECT subnet_range_name FROM public_ip_pool where ip='***********'" in caplog.text
        assert "subnet name for ip ***********: test_subnet_db" in caplog.text

    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.is_ipam_service_enabled")
    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.IPAMService")
    def test_get_subnet_name_from_ip_ipam_enabled_no_subnet(self, mock_ipam_service, mock_is_ipam_enabled, caplog):
        caplog.set_level(logging.INFO)
        mock_is_ipam_enabled.return_value = mock_ipam_service.ENABLED
        mock_ipam_utils = MagicMock()
        mock_ipam_utils.ipam_fetch_address.return_value = []

        ip_mgmt = IPHandler(Mock_DBHandler(logging.getLogger('testlogger')), 0, 0)
        ip_mgmt.ipam_utils = mock_ipam_utils

        result = ip_mgmt.get_subnet_name_from_ip('***********')

        assert result is None
        assert "subnet name for ip ***********: None" in caplog.text

    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.is_ipam_service_enabled")
    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.IPAMService")
    def test_get_subnet_name_from_ip_ipam_disabled_no_subnet(self, mock_ipam_service, mock_is_ipam_enabled, caplog):
        caplog.set_level(logging.INFO)
        mock_is_ipam_enabled.return_value = mock_ipam_service.DISABLED
        mock_db_handler = Mock_DBHandler(logging.getLogger('testlogger'))
        mock_cursor = mock_db_handler.get_cursor()
        mock_cursor.fetchone.return_value = None

        ip_mgmt = IPHandler(mock_db_handler, 0, 0)

        result = ip_mgmt.get_subnet_name_from_ip('***********')

        assert result is None
        assert "subnet name for ip ***********: None" in caplog.text

    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.is_ipam_service_enabled")
    @patch("libs.cloud_providers.common.ip_management.ipv4.ip_mgmt.IPAMService")
    def test_get_subnet_name_from_ip_ipam_disabled_db_exception(self, mock_ipam_service, mock_is_ipam_enabled, caplog):
        caplog.set_level(logging.ERROR)
        mock_is_ipam_enabled.return_value = mock_ipam_service.DISABLED
        mock_db_handler = Mock_DBHandler(logging.getLogger('testlogger'))
        mock_cursor = mock_db_handler.get_cursor()
        mock_cursor.execute.side_effect = Exception("DB Error")

        ip_mgmt = IPHandler(mock_db_handler, 0, 0)

        result = ip_mgmt.get_subnet_name_from_ip('***********')

        assert result is None
        assert "Failed to get subnet name from public_ip_pool for ip ***********, exception: DB Error" in caplog.text
