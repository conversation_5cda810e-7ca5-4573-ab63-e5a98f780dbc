import logging
import os
from pathlib import Path

logger = logging.getLogger()


class swgproxy_bringup:
    def __init__(self, dbh):
        if dbh == None or dbh.logger == None:
            return
        self.logger = dbh.logger
        self._cust_id = None
        self._region_idx = None
        self._stack_name = None
        self._cloud_provider = None
        self.dbh = dbh

    def ep_cust_region_salt_profile(self, custid, region_idx, node_type=153, alt_node_type=-1):
        salt_profile = {"envoy_image_version": "pa-proxyvm-3-2-1-1-20221101203853",
                        "project_id": "cust-gpcs-mwernosr5hj1l6v29jv3", "envoy_image_project": "image-gpcs-nonprod-01",
                        "image_project": "image-gpcs-nonprod-01", "envoy_inital_cnt": "1", "envoy_min_cnt": "1",
                        "envoy_max_cnt": "10", "envoy_traffic_port": "8080", "envoy_hc_port": "8887",
                        "envoy_disc_size_gb": "40", "envoy_autosclale_cpu_target": "0.5",
                        "user_data": "{\"ssh-keys\": \"admin:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r admin@SJCMACJ15HHTD8\", \"mtls_certpkey\": \"customer**********\", \"secret_mgr_project\": \"image-gpcs-nonprod-01\", \"cloud_provider\": 2, \"upstream-proxyprotocol\": \"v1\", \"mgmt-interface-swap\": \"enable\", \"custid_int\": 24, \"custid\": \"**********\", \"custsuperid\": \"**********\", \"svc_acct\": \"<EMAIL>\", \"pacfgds\": \"dev.cfgds.swg.panclouddev.com\", \"pagks\": \"dev.gks.swg.panclouddev.com\", \"pacdls\": \"dev.cdls.swg.panclouddev.com\", \"patgs\": \"dev.tgs.swg.panclouddev.com\", \"mtls_ca\": \"it_ca_cert\"}"
                        }
        if region_idx == 123:
            salt_profile["migrate_ep_status"] = "DONE_MIGRATION"
        else:
            salt_profile["migrate_ep_status"] = None
        if region_idx == 1212:
            salt_profile["enable_global_nlb"] = 1

        return salt_profile

    def gpcs_envoy_service_timeout_for_lb(self, custid, region, node_type, alt_node_type):
        return 86400

    def gpcs_get_cnat_params(self, cfg, custid, region_idx, cloud_provider, node_type, alt_node_type=-1):
        cnat_dict = {}
        cnat_dict["cnat_enabled"] = "1"
        cnat_dict["cnat_min_count"] = 5
        cnat_dict["cnat_max_count"] = 4
        cnat_dict["cnat_vm_min_port"] = 32
        cnat_dict["cnat_vm_max_port"] = 32768
        cnat_dict["ip_range"] = '/32'
        cnat_dict["cnat_ntuple_hash"] = '0'
        cnat_dict["cnat_public_ips"] = "*******"
        if region_idx == 1278:
            cnat_dict["cnat_enabled"] = "1"
        return cnat_dict



class DB:
    def __init__(self):
        self.logger = logger

    def get_cursor(self):
        return Cursor()

    def cursorclose(self, cursor):
        pass

class Cursor:
    def execute(self, sql, params):
        pass

    def fetchone(self):
        return ["12345678912345678912345"]



param_dict = {2691: {'CustId': 16, 'MachineType': 'e2-standard-4', 'AcctId': '826841054',
                     'UserData': 'instance_name=SWGPROXY_2691_us-west-201_300is-826841054,saas_gpcs_api_endpoint=dev11.panclouddev.com/api,cert_fetch_otp=7d26fcae-7cdc-4e02-9afc-c7e06e2b7e10,cloud_provider=gcp,custid=826841054,lambdaprefix=300is-826841054,custid_int=16,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev11,panrepo_bucket_name=panrepo-us-west-2-dev11,aws-access-key-id=,aws-secret-access-key=,zone=us-west2-a,zone-ha=us-west2-b,region=us-west2,edge-compute-region=us-west-201,super_custid=826841054,ssh-keys=gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8,sase_fabric=False,commit_validate=0,is_nlb_supported=0,is_ngpa_protocol_enabled=0,is_central_cache_supported=0,is_instance_behind_nlb=0,central_cache_service_endpoint=,ciam_service_endpoint=,mgmt-interface-swap=enable',
                     'PrimaryInstanceName': 'swgproxy-2691-us-west-201-300is-826', 'PrimaryId': 2691,
                     'SerialNo': 'Dummy_serial_no',
                     'ImageProject': 'image-gpcs-nonprod-01', 'PrimaryImageName': 'pa-vm-saas-gcp-10-1-3-c99-saas',
                     'svc_acct': '<EMAIL>',
                     'clean_ip_tag': False, 'NodeType': 153,
                     'Name': 'SWGPROXY_2691_us-west-201_300is-826841054', 'PublicIp': '*************', 'IsBehindNlb': 0,
                     'enable_tls_term_on_ep': 0,
                     'enable_rn_to_ep_ctx_pass': 0, 'hasExternalIPv6': False,
                     'PriAllowedAsTarget': True, 'sase_fabric': 0, 'commit_validate': 0,
                     'nlb_ingress_ip': '************', 'egress_ip_list': '{}', 'l3fwdrules': 0,
                     'inbound_access_ip_list': None,
                     'allowed_fw_rules': None, 'static_ip': '*************', 'PrimaryZone': 'us-west2-a',
                     'PrimaryIntfSet': 0, 'RegionName': 'us-west2', 'MgmtSubnet': 'subnet-mgmt-us-west2-826841054',
                     'DPSubnet': 'subnet-dp-us-west2-826841054', 'HASubnet': 'subnet-ha-us-west2-826841054',
                     'ExtrnSubnet': 'subnet-extrn-us-west2-826841054',
                     'MgmtNetwork': 'gpcs-vpc-mgmt-826841054', 'DPNetwork': 'gpcs-vpc-dp-826841054',
                     'HANetwork': 'gpcs-vpc-ha-826841054', 'ExtrnNetwork': 'gpcs-vpc-extrn-826841054',
                     'MgmtInterfaceName': 'nic-mgmt', 'DPInterfaceName': 'CLIENT_IP', 'HAInterfaceName': 'nic-ha',
                     'ExtrnInterfaceName': 'nic-extrn', 'MgmtHasExternalIP': False,
                     'DPHasExternalIP': True, 'HAHasExternalIP': False, 'ExtrnHasExternalIP': True,
                     'egress_ipv6_list_subnet': 123,
                     'PrimaryCapacityType': 'PA-CAP421X'}}

gw_behind_nlb_param_dict_v6 = {2691: {'CustId': 16, 'MachineType': 'e2-standard-4', 'AcctId': '826841054',
                     'UserData': 'instance_name=gpgwDummy,saas_gpcs_api_endpoint=dev11.panclouddev.com/api,cert_fetch_otp=7d26fcae-7cdc-4e02-9afc-c7e06e2b7e10,cloud_provider=gcp,custid=826841054,lambdaprefix=300is-826841054,custid_int=16,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev11,panrepo_bucket_name=panrepo-us-west-2-dev11,aws-access-key-id=,aws-secret-access-key=,zone=us-west2-a,zone-ha=us-west2-b,region=us-west2,edge-compute-region=us-west-201,super_custid=826841054,ssh-keys=gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8,sase_fabric=False,commit_validate=0,is_nlb_supported=0,is_ngpa_protocol_enabled=0,is_central_cache_supported=0,is_instance_behind_nlb=0,central_cache_service_endpoint=,ciam_service_endpoint=,mgmt-interface-swap=enable',
                     'PrimaryInstanceName': 'gpgwDummy-2691', 'PrimaryId': 2691,
                     'SerialNo': 'Dummy_serial_no',
                     'ImageProject': 'image-gpcs-nonprod-01', 'PrimaryImageName': 'pa-vm-saas-gcp-10-1-3-c99-saas',
                     'svc_acct': '<EMAIL>',
                     'clean_ip_tag': False, 'NodeType': 49,
                     'Name': 'SWGPROXY_2691_us-west-201_300is-826841054', 'PublicIp': '*************', 'IsBehindNlb': 0,
                     'enable_tls_term_on_ep': 0,
                     'enable_rn_to_ep_ctx_pass': 0, 'hasExternalIPv6': False,
                     'PriAllowedAsTarget': True, 'sase_fabric': 0, 'commit_validate': 0,
                     'nlb_ingress_ip': '************', 'egress_ip_list': '{}', 'l3fwdrules': 0,
                     'inbound_access_ip_list': None,
                     'allowed_fw_rules': None, 'static_ip': '*************', 'PrimaryZone': 'us-west2-a',
                     'PrimaryIntfSet': 0, 'RegionName': 'us-west2', 'MgmtSubnet': 'subnet-mgmt-us-west2-826841054',
                     'DPSubnet': 'subnet-dp-us-west2-826841054', 'HASubnet': 'subnet-ha-us-west2-826841054',
                     'ExtrnSubnet': 'subnet-extrn-us-west2-826841054',
                     'MgmtNetwork': 'gpcs-vpc-mgmt-826841054', 'DPNetwork': 'gpcs-vpc-dp-826841054',
                     'HANetwork': 'gpcs-vpc-ha-826841054', 'ExtrnNetwork': 'gpcs-vpc-extrn-826841054',
                     'MgmtInterfaceName': 'nic-mgmt', 'DPInterfaceName': 'CLIENT_IP', 'HAInterfaceName': 'nic-ha',
                     'ExtrnInterfaceName': 'nic-extrn', 'MgmtHasExternalIP': False,
                     'hasExternalIPv6': True, 'IsBehindNlb': True,
                     'DPHasExternalIP': True, 'HAHasExternalIP': False, 'ExtrnHasExternalIP': True,
                     'egress_ipv6_list_subnet': {'subnet1': "subnet1Val"},
                     'PrimaryCapacityType': 'PA-CAP421X'}}

# For template generation for explicit proxy with agent support, we set "enable_tls_term_on_ep" to 1 for the instance.
param_dict_swg_agent = [{2691: {'CustId': 16, 'MachineType': 'e2-standard-4', 'AcctId': '826841054',
                     'UserData': 'instance_name=SWGPROXY_2691_us-west-201_300is-826841054,saas_gpcs_api_endpoint=dev11.panclouddev.com/api,cert_fetch_otp=7d26fcae-7cdc-4e02-9afc-c7e06e2b7e10,cloud_provider=gcp,custid=826841054,lambdaprefix=300is-826841054,custid_int=16,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev11,panrepo_bucket_name=panrepo-us-west-2-dev11,aws-access-key-id=,aws-secret-access-key=,zone=us-west2-a,zone-ha=us-west2-b,region=us-west2,edge-compute-region=us-west-201,super_custid=826841054,ssh-keys=gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8,sase_fabric=False,commit_validate=0,is_nlb_supported=0,is_ngpa_protocol_enabled=0,is_central_cache_supported=0,is_instance_behind_nlb=0,central_cache_service_endpoint=,ciam_service_endpoint=,mgmt-interface-swap=enable',
                     'PrimaryInstanceName': 'swgproxy-2691-us-west-201-300is-826', 'PrimaryId': 2691,
                     'SerialNo': 'Dummy_serial_no',
                     'ImageProject': 'image-gpcs-nonprod-01', 'PrimaryImageName': 'pa-vm-saas-gcp-10-1-3-c99-saas',
                     'svc_acct': '<EMAIL>',
                     'clean_ip_tag': False, 'NodeType': 153,
                     'Name': 'SWGPROXY_2691_us-west-201_300is-826841054', 'PublicIp': '*************', 'IsBehindNlb': 0,
                     'enable_tls_term_on_ep': 1,
                     'enable_rn_to_ep_ctx_pass': 0, 'hasExternalIPv6': False,
                     'PriAllowedAsTarget': True, 'sase_fabric': 0, 'commit_validate': 0,
                     'nlb_ingress_ip': '************', 'egress_ip_list': '{}', 'l3fwdrules': 0,
                     'inbound_access_ip_list': None,
                     'allowed_fw_rules': None, 'static_ip': '*************', 'PrimaryZone': 'us-west2-b',
                     'PrimaryIntfSet': 0, 'RegionName': 'us-west2', 'MgmtSubnet': 'subnet-mgmt-us-west2-826841054',
                     'DPSubnet': 'subnet-dp-us-west2-826841054', 'HASubnet': 'subnet-ha-us-west2-826841054',
                     'ExtrnSubnet': 'subnet-extrn-us-west2-826841054',
                     'MgmtNetwork': 'gpcs-vpc-mgmt-826841054', 'DPNetwork': 'gpcs-vpc-dp-826841054',
                     'HANetwork': 'gpcs-vpc-ha-826841054', 'ExtrnNetwork': 'gpcs-vpc-extrn-826841054',
                     'MgmtInterfaceName': 'nic-mgmt', 'DPInterfaceName': 'CLIENT_IP', 'HAInterfaceName': 'nic-ha',
                     'ExtrnInterfaceName': 'nic-extrn', 'MgmtHasExternalIP': False, 'egress_ipv6_list_subnet': 123,
                     'DPHasExternalIP': True, 'HAHasExternalIP': False, 'ExtrnHasExternalIP': True,
                     'PrimaryCapacityType': 'PA-CAP421X'}},
                     {2691: {'CustId': 287, 'MachineType': 'e2-standard-4', 'AcctId': '826841054',
                     'UserData': 'instance_name=SWGPROXY_2691_us-west-201_300is-826841054,saas_gpcs_api_endpoint=dev11.panclouddev.com/api,cert_fetch_otp=7d26fcae-7cdc-4e02-9afc-c7e06e2b7e10,cloud_provider=gcp,custid=826841054,lambdaprefix=300is-826841054,custid_int=16,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev11,panrepo_bucket_name=panrepo-us-west-2-dev11,aws-access-key-id=,aws-secret-access-key=,zone=us-west2-a,zone-ha=us-west2-b,region=us-west2,edge-compute-region=us-west-201,super_custid=826841054,ssh-keys=gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8,sase_fabric=False,commit_validate=0,is_nlb_supported=0,is_ngpa_protocol_enabled=0,is_central_cache_supported=0,is_instance_behind_nlb=0,central_cache_service_endpoint=,ciam_service_endpoint=,mgmt-interface-swap=enable',
                     'PrimaryInstanceName': 'swgproxy-2691-us-west-201-300is-826', 'PrimaryId': 2691,
                     'SerialNo': 'Dummy_serial_no',
                     'ImageProject': 'image-gpcs-nonprod-01', 'PrimaryImageName': 'pa-vm-saas-gcp-10-1-3-c99-saas',
                     'svc_acct': '<EMAIL>',
                     'clean_ip_tag': False, 'NodeType': 153,
                     'Name': 'SWGPROXY_2691_us-west-201_300is-826841054', 'PublicIp': '*************', 'IsBehindNlb': 0,
                     'enable_tls_term_on_ep': 1,
                     'enable_rn_to_ep_ctx_pass': 1, 'hasExternalIPv6': False,
                     'PriAllowedAsTarget': True, 'sase_fabric': 0, 'commit_validate': 0,
                     'nlb_ingress_ip': '************', 'egress_ip_list': '{}', 'l3fwdrules': 0,
                     'inbound_access_ip_list': None,
                     'allowed_fw_rules': None, 'static_ip': '*************', 'PrimaryZone': 'us-west2-a',
                     'PrimaryIntfSet': 0, 'RegionName': 'us-west2', 'MgmtSubnet': 'subnet-mgmt-us-west2-826841054',
                     'DPSubnet': 'subnet-dp-us-west2-826841054', 'HASubnet': 'subnet-ha-us-west2-826841054',
                     'ExtrnSubnet': 'subnet-extrn-us-west2-826841054',
                     'MgmtNetwork': 'gpcs-vpc-mgmt-826841054', 'DPNetwork': 'gpcs-vpc-dp-826841054',
                     'HANetwork': 'gpcs-vpc-ha-826841054', 'ExtrnNetwork': 'gpcs-vpc-extrn-826841054',
                     'MgmtInterfaceName': 'nic-mgmt', 'DPInterfaceName': 'CLIENT_IP', 'HAInterfaceName': 'nic-ha',
                     'ExtrnInterfaceName': 'nic-extrn', 'MgmtHasExternalIP': False,
                     'DPHasExternalIP': True, 'HAHasExternalIP': False, 'ExtrnHasExternalIP': True,
                     'PrimaryCapacityType': 'PA-CAP421X'}}]


# Here we set 'NodeType': 161 to make sure we generate template for NLB as per generate_template
# function
param_dict_nlb_node = {2692: {'CustId': 16, 'egress_ipv6_list_subnet': 123, 'MachineType': 'e2-standard-4', 'AcctId': '826841054', 'UserData': 'instance_name=NLB_2692_us-west-201_300is-826841054,saas_gpcs_api_endpoint=dev11.panclouddev.com/api,cert_fetch_otp=7d26fcae-7cdc-4e02-9afc-c7e06e2b7e10,cloud_provider=gcp,custid=826841054,lambdaprefix=300is-826841054,custid_int=16,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev11,panrepo_bucket_name=panrepo-us-west-2-dev11,aws-access-key-id=,aws-secret-access-key=,zone=us-west2-a,zone-ha=us-west2-b,region=us-west2,edge-compute-region=us-west-201,super_custid=826841054,ssh-keys=gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8,sase_fabric=False,commit_validate=0,is_nlb_supported=0,is_ngpa_protocol_enabled=0,is_central_cache_supported=0,is_instance_behind_nlb=0,central_cache_service_endpoint=,ciam_service_endpoint=,mgmt-interface-swap=enable', 'PrimaryInstanceName': 'nlb-2692-us-west-201-300is-826', 'PrimaryId': 2692, 'SerialNo': 'Dummy_serial_no','ImageProject': 'image-gpcs-nonprod-01', 'PrimaryImageName': 'pa-vm-saas-gcp-10-1-3-c99-saas', 'svc_acct': '<EMAIL>', 'clean_ip_tag': False, 'NodeType': 161, 'Name': 'NLB_2692_us-west-201_300is-826841054', 'PublicIp': '*************', 'IsBehindNlb': 0, 'hasExternalIPv6': False, 'PriAllowedAsTarget': True, 'sase_fabric': 0, 'commit_validate': 0, 'nlb_ingress_ip': '************', 'egress_ip_list': '{}', 'l3fwdrules': 0, 'inbound_access_ip_list': None, 'allowed_fw_rules': None, 'static_ip': '*************', 'PrimaryZone': 'us-west2-a', 'PrimaryIntfSet': 0, 'RegionName': 'us-west2', 'MgmtSubnet': 'subnet-mgmt-us-west2-826841054', 'DPSubnet': 'subnet-dp-us-west2-826841054', 'HASubnet': 'subnet-ha-us-west2-826841054', 'ExtrnSubnet': 'subnet-extrn-us-west2-826841054', 'MgmtNetwork': 'gpcs-vpc-mgmt-826841054', 'DPNetwork': 'gpcs-vpc-dp-826841054', 'HANetwork': 'gpcs-vpc-ha-826841054', 'ExtrnNetwork': 'gpcs-vpc-extrn-826841054', 'MgmtInterfaceName': 'nic-mgmt', 'DPInterfaceName': 'CLIENT_IP', 'HAInterfaceName': 'nic-ha', 'ExtrnInterfaceName': 'nic-extrn', 'MgmtHasExternalIP': False, 'DPHasExternalIP': True, 'HAHasExternalIP': False, 'ExtrnHasExternalIP': True, 'PrimaryCapacityType': 'PA-CAP421X'}}

nlb_param_dict = {'HealthCheckProtocol': 'http', 'HealthCheckPort': 8000, 'HealthCheckInterval': 2, 'HealthCheckTimeout': 2, 'HealthCheckUnhealthyThreshold': 2, 'HealthCheckHealthyThreshold': 2, 'ForwardingRuleProtocol': 'dummy', 'BackendServiceProtocol': 'dummy', 'SessionAffinity': 2, 'EnableStrongAffinity': 1, 'ConnectionPersistenceOnUnhealthyBackends': 1}

no_passive_instance_dict = {2183431: {'CustId': 3265, 'MachineType': 'e2-standard-4', 'AcctId': '**********', 'UserData': 'instance_name=SFW_2183431_ap-south-1_renault3402-**********,saas_gpcs_api_endpoint=qa3.panclouddev.com/api,cert_fetch_otp=02341a60-4113-46fd-b20b-16a70280d3e8,cloud_provider=gcp,custid=**********,lambdaprefix=renault3402-**********,custid_int=3265,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-us-west-2-qa3,panrepo_bucket_name=panrepo-us-west-2-qa3,aws-access-key-id=,aws-secret-access-key=,zone=asia-south1-a,zone-ha=asia-south1-b,region=asia-south1,edge-compute-region=ap-south-1,super_custid=**********,ssh-keys=gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8,sase_fabric=False,commit_validate=0,is_nlb_supported=0,is_ngpa_protocol_enabled=0,is_central_cache_supported=0,is_instance_behind_nlb=0,central_cache_service_endpoint=pa-service-api-us-qa01.tools.panclouddev.com,central_cache_service_backup_endpoint=pa-service-api-eu-qa03.tools.panclouddev.com,is_using_sp_interconnect=False,mgmt-interface-swap=enable', 'PrimaryInstanceName': 'sfw-2183431-ap-south-1-renault3402', 'PrimaryId': 2183431, 'SerialNo': 'Dummy_serial_no', 'ImageProject': 'image-gpcs-nonprod-01', 'SharedMgmtVPCProj': 'host-gpcs-test-01', 'PrimaryImageName': 'pa-vm-saas-gcp-10-2-4-ch58-saas', 'svc_acct': '<EMAIL>', 'clean_ip_tag': False, 'NodeType': 51, 'Name': 'SFW_2183432_ap-south-1_renault3402-**********', 'PublicIp': '**************', 'IsBehindNlb': 0, 'IsUpgradeCreation': 0, 'enable_tls_term_on_ep': 0, 'enable_rn_to_ep_ctx_pass': 0, 'hasExternalIPv6': False, 'PriAllowedAsTarget': True, 'sase_fabric': 0, 'commit_validate': False, 'is_using_sp_interconnect': False, 'min_cpu_platform': '', 'egress_ip_list': '{}', 'egress_ipv6_list_subnet': None, 'l3fwdrules': 0, 'inbound_access_ip_list': None, 'allowed_fw_rules': None, 'static_ip': '**************', 'PrimaryZone': 'asia-south1-a', 'RegionName': 'asia-south1', 'MgmtSubnet': 'shared-mgmt-asia-south1-01', 'DPSubnet': 'subnet-dp-asia-south1-**********', 'MgmtNetwork': 'shared-mgmt-vpc', 'DPNetwork': 'gpcs-vpc-dp-**********', 'MgmtInterfaceName': 'nic-mgmt', 'DPInterfaceName': 'CLIENT_IP', 'MgmtHasExternalIP': False, 'DPHasExternalIP': True, 'HAHasExternalIP': False, 'PrimaryCapacityType': 'PA-CAP530', 'interface_ip_list': None, 'SecAllowedAsTarget': True}}

with_passive_instance_dict = {2183431: {'CustId': 3265, 'MachineType': 'e2-standard-4', 'AcctId': '**********', 'UserData': 'instance_name=SFW_2183431_ap-south-1_renault3402-**********,saas_gpcs_api_endpoint=qa3.panclouddev.com/api,cert_fetch_otp=02341a60-4113-46fd-b20b-16a70280d3e8,cloud_provider=gcp,custid=**********,lambdaprefix=renault3402-**********,custid_int=3265,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-us-west-2-qa3,panrepo_bucket_name=panrepo-us-west-2-qa3,aws-access-key-id=,aws-secret-access-key=,zone=asia-south1-a,zone-ha=asia-south1-b,region=asia-south1,edge-compute-region=ap-south-1,super_custid=**********,ssh-keys=gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8,sase_fabric=False,commit_validate=0,is_nlb_supported=0,is_ngpa_protocol_enabled=0,is_central_cache_supported=0,is_instance_behind_nlb=0,central_cache_service_endpoint=pa-service-api-us-qa01.tools.panclouddev.com,central_cache_service_backup_endpoint=pa-service-api-eu-qa03.tools.panclouddev.com,is_using_sp_interconnect=False,mgmt-interface-swap=enable', 'PrimaryInstanceName': 'sfw-2183431-ap-south-1-renault3402', 'PrimaryId': 2183431, 'SerialNo': 'Dummy_serial_no', 'ImageProject': 'image-gpcs-nonprod-01', 'SharedMgmtVPCProj': 'host-gpcs-test-01', 'PrimaryImageName': 'pa-vm-saas-gcp-10-2-4-ch58-saas', 'svc_acct': '<EMAIL>', 'clean_ip_tag': False, 'NodeType': 51, 'Name': 'SFW_2183432_ap-south-1_renault3402-**********', 'PublicIp': '**************', 'IsBehindNlb': 0, 'IsUpgradeCreation': 0, 'enable_tls_term_on_ep': 0, 'enable_rn_to_ep_ctx_pass': 0, 'hasExternalIPv6': False, 'PriAllowedAsTarget': True, 'sase_fabric': 0, 'commit_validate': False, 'is_using_sp_interconnect': False, 'min_cpu_platform': '', 'egress_ip_list': '{}', 'egress_ipv6_list_subnet': None, 'l3fwdrules': 0, 'inbound_access_ip_list': None, 'allowed_fw_rules': None, 'static_ip': '**************', 'PrimaryZone': 'asia-south1-a', 'PrimaryIntfSet': 0, 'RegionName': 'asia-south1', 'MgmtSubnet': 'shared-mgmt-asia-south1-01', 'DPSubnet': 'subnet-dp-asia-south1-**********', 'HASubnet': 'subnet-ha-asia-south1-**********', 'MgmtNetwork': 'shared-mgmt-vpc', 'DPNetwork': 'gpcs-vpc-dp-**********', 'HANetwork': 'gpcs-vpc-ha-**********', 'MgmtInterfaceName': 'nic-mgmt', 'DPInterfaceName': 'CLIENT_IP', 'HAInterfaceName': 'nic-ha', 'MgmtHasExternalIP': False, 'DPHasExternalIP': True, 'HAHasExternalIP': False, 'PrimaryCapacityType': 'PA-CAP530', 'SecondaryInstanceName': 'sfw-2183432-ap-south-1-renault3402', 'SecondaryId': 2183432, 'SecondaryZone': 'asia-south1-b', 'SecondaryUserData': 'instance_name=SFW_2183432_ap-south-1_renault3402-**********,saas_gpcs_api_endpoint=qa3.panclouddev.com/api,cert_fetch_otp=93a4150d-c93d-4f81-92e8-2ff34e03ace4,cloud_provider=gcp,custid=**********,lambdaprefix=renault3402-**********,custid_int=3265,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-us-west-2-qa3,panrepo_bucket_name=panrepo-us-west-2-qa3,aws-access-key-id=,aws-secret-access-key=,zone=asia-south1-a,zone-ha=asia-south1-b,region=asia-south1,edge-compute-region=ap-south-1,super_custid=**********,ssh-keys=gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8,sase_fabric=False,commit_validate=False,is_nlb_supported=0,is_ngpa_protocol_enabled=0,is_central_cache_supported=0,is_instance_behind_nlb=0,central_cache_service_endpoint=pa-service-api-us-qa01.tools.panclouddev.com,central_cache_service_backup_endpoint=pa-service-api-eu-qa03.tools.panclouddev.com,is_using_sp_interconnect=False,mgmt-interface-swap=enable', 'SecondaryImageName': 'pa-vm-saas-gcp-10-2-4-ch58-saas', 'interface_ip_list': None, 'SecondaryIntfSet': 0, 'SecondaryMachineType': 'e2-standard-4', 'SecAllowedAsTarget': True, 'SecondaryCapacityType': 'PA-CAP530'}}

path = Path(__file__).resolve().parent
paths = [f"{path}/cust_unittest_region_123_proxy_gcp.tmpl",
         f"{path}/cust_unittest_region_456_proxy_gcp.tmpl",
         f"{path}/cust_unittest_region_789_gcp_nlb.tmpl",
         f"{path}/cust_unittest_region_123_proxy_agent_gcp.tmpl",
         f"{path}/cust_unittest_region_298_proxy_gcp.tmpl"]

class Service:
    def sslCertificates(self):
        return Operation()

    def access_secret_version(self, request):
        return SecretVersion("secretvalue")

class Operation:
    def get(self, project, sslCertificate):
        return Requestsget()
    def insert(self, project, body):
        return RequestsInsert()

class Requestsget():
    def execute(self):
        raise Exception("Not found")

class RequestsInsert():
    def execute(self):
        return {"selfLink":"responseurl",
                "targetLink": "responseurl"}

class Secret():
    def SecretManagerServiceClient(self, credentials):
        return Service()

class SecretVersion:
    def __init__(self, value):
        self.name = "sample-name"
        self.payload = Data(value)


class Data:
    def __init__(self, value):
        self.data = value.encode("UTF-8")
        self.dataCrc32c = "141866740"

class Open():
    def __init__(self):
        self.content = ""

    def write(self, template):
        self.content = template


class Customer:
    def __init__(self, id):
        self.id = id
        self.acct_id = 1234

class InstanceModel:
    def __init__(self, id, name):
        self.custid = id
        self.name = name

    def get_param(self, value):
        if value == "compute_region_idx":
            return 220
        if value == "node_type":
            return 153
        if value == "is_instance_behind_nlb":
            return None
        return "test_value "+ value
