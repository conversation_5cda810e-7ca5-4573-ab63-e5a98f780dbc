import logging
logger = logging.getLogger()

class MockIPManagementModel():
    def __init__(self, dbh):
        self.dbh=dbh
        self.logger = logger
    def get_egress_ip_dict_from_str(self, logger, input):
        return {"555":"11111"}

    def bind_non_allow_listed_ips_to_instance(self, instance_obj, edge_region_id, num_of_ips, target_node_type):
        return {"ok": True}

    def bind_reserved_ips_to_instance(self, instance_obj, edge_region_id, num_of_ips, target_node_type):
        return {"ok": True}

    def update_egress_ip_list_with_given_ipstr(self, instance_obj,
                                               edge_region_id, ipstr):
        return {'ok': True}

    def bind_non_allowed_public_ip_to_instance(self, instance_obj, compute_region_idx, target_node_type):
        return {"ok": True}

class MockInstanceModel():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0
    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def get_all_gp_gw_instance_for_region_and_custid(self,input1,input2):
        return ['123']
    def update_interface_ip_list_with_egress_ip(self, edge_location_idx):
        return True
    def save(self):
        return
    def save_stage4(self):
        return
    def get_param(self, input):
        if input=="delete_after":
            return 0
        elif input=="id":
            return 123
        elif input=="name":
            return "GPGW_31090435_us-southeast_reservebankofaustralia-**********"
        elif input=="custid":
            return 456
        elif input=="egress_ip_list":
            return """{"555":"11111"}"""
        elif input=="compute_region_idx":
            return 211
        elif input=="is_instance_behind_nlb":
            return True
        elif input=="node_type":
            return 49
        elif input == "has_nat_instance":
            return True
        else:
            return 1

    def get_existing_egress_ip_region_list_for_pinned_instance(self, custid, compute_region_idx, node_type):
        return ["222", "203"]

class MockInstanceModelSAGWIIPEnabled():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0
    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def get_all_gp_gw_instance_for_region_and_custid(self,input1,input2):
        return ['123']
    def update_interface_ip_list_with_egress_ip(self, edge_location_idx):
        return True
    def update_interface_ipv6_list_with_egress_ipv6(self, edge_location_idx):
        return True
    def save(self):
        return
    def save_stage4(self):
        return
    def get_param(self, input):
        if input=="delete_after":
            return 0
        elif input=="id":
            return 123
        elif input=="name":
            return "GPGW_31090435_us-southeast_reservebankofaustralia-**********"
        elif input=="custid":
            return 456
        elif input=="egress_ip_list":
            return """{"555":"11111"}"""
        elif input=="compute_region_idx":
            return 211
        elif input=="is_instance_behind_nlb":
            return False
        elif input=="node_type":
            return 49
        elif input == "has_nat_instance":
            return False
        else:
            return 1

    def get_existing_egress_ip_region_list_for_pinned_instance(self, custid, compute_region_idx, node_type):
        return ["222", "203"]

class MockInstanceModelSAGWIIPEnabledIpv6Failed():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0
    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def get_all_gp_gw_instance_for_region_and_custid(self,input1,input2):
        return ['123']
    def update_interface_ip_list_with_egress_ip(self, edge_location_idx):
        return True
    def update_interface_ipv6_list_with_egress_ipv6(self, edge_location_idx):
        return False
    def save(self):
        return
    def save_stage4(self):
        return
    def get_param(self, input):
        if input=="delete_after":
            return 0
        elif input=="id":
            return 123
        elif input=="name":
            return "GPGW_31090435_us-southeast_reservebankofaustralia-**********"
        elif input=="custid":
            return 456
        elif input=="egress_ip_list":
            return """{"555":"11111"}"""
        elif input=="compute_region_idx":
            return 211
        elif input=="is_instance_behind_nlb":
            return False
        elif input=="node_type":
            return 49
        elif input == "has_nat_instance":
            return False
        else:
            return 1

    def get_existing_egress_ip_region_list_for_pinned_instance(self, custid, compute_region_idx, node_type):
        return ["222", "203"]

class MockGCPIPHandler():
    def __init__(self, dbh=None, instance_id=None, acct_id=None, node_id=None):
        self.dbh = dbh

    def allocate_public_ip_for_customer(self, test, edge_region_idx, allocate=False):
        return [["220"]]

    def find_nlb_settings_by_custid_and_region_id(self, custid, compute_region_idx):
        return {'is_nlb_supported': False, 'cust_is_nlb_supported': True, 'ingress_ip_reduction': True}
