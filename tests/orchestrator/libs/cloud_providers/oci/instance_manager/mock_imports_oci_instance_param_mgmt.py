import sys
from unittest.mock import MagicMock
from mock_inputs_oci_instance_mgmt_utils import Customer
sys.modules["libs.cfg"] = MagicMock()
sys.modules["libs.common.utils"] = MagicMock()
sys.modules["libs.cloud_providers.oci.instance_manager.oci_instance_info"] = MagicMock()
sys.modules["libs.cloud_providers.common.swgproxy_bringup"] = MagicMock()
sys.modules["libs.cloud_providers.oci.instance_manager.oci_instance_info"] = MagicMock()
sys.modules["io"] = MagicMock()
sys.modules["gzip"] = MagicMock()
sys.modules["mysql"] = MagicMock()
sys.modules["mysql.connector"] = MagicMock()
sys.modules["libs.common.shared.py3_utils"] = MagicMock()
sys.modules["libs.common.utils"] = MagicMock()
sys.modules["libs.model.instancemodel"] = MagicMock()
sys.modules["orchestration_service.core.orchestrator_nat_mgmt"] = MagicMock()
sys.modules["orchestration_service.core.orchestrator_nlb_mgmt"] = MagicMock()
sys.modules["libs.model.instancemodel"] = MagicMock()
sys.modules["libs.model.IP_management_model"] = MagicMock()
sys.modules["libs.model.explicitProxyTenantInfoModel"] = MagicMock(ExplicitProxyTenantInfoModel=
                                                                   MagicMock(get_entry=True, get_param=0))
sys.modules["libs.model.custmodel"] = MagicMock(CustomerModel=Customer(1234))
sys.modules["libs.cloud_providers.oci.instance_manager.oci_instance_param_utils"] = MagicMock()
