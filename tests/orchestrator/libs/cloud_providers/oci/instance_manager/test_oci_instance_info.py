from unittest.mock import patch, MagicMock
import sys
import os
import pytest
import logging
from mock_inputs_oci_instance_mgmt_utils import DB
from libs.model.custmodel import *
from libs.model.custnodemodel import *
from libs.model.instancemodel import InstanceModel
from libs.cloud_providers.oci.instance_manager.oci_instance_info import oci_instance_info
logger = logging.getLogger()

class Mock_cursor():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.sql in mocked_db_return:
            answer = mocked_db_return.get(self.sql, None)
            return answer
        else:
            raise Exception("No matching dict found to lookup for this test suite.")

    def fetchone(self):
        answer = ''
        if 'inst_mgmt_id' in self.sql:
            json_str = r'{"template-url": "http://dummy.com"}'
            answer = [50, json_str, 1, 0, 1, "ERROR", 1, 1, 0, 1, 1]
        elif 'edge_location_region_name' in self.sql:
            answer = ["dummy"] * 24
            answer[8] = "oci"
        elif 'network_load_balancer_config' in self.sql:
            answer = [1]
        elif 'eidx, region, custid, stack_name, cloud_provider, debug, debug_id, node_type' in self.sql:
            answer = ["100"] * 8
        elif 'UPDATE orch_instance_management_gcp_table SET inst_mgmt_id=' in self.sql:
            answer = ["1"]
        return answer

    def lastrowid(self):
        pass

class Mock_DBHandler():
    def __init__(self, logger):
        self.logger = logger

    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return
class mocked_CustomerModel():
    def __init__(self, custid, dbh):
        self.id =  custid
        self.dbh = dbh
        self.fwdrulesall = ""
        self.name = "dummy"
        self.acct_id = 0
    
    def get_param(self, val):
        return 1
        

def mock_is_sase_fabric():
    return True

class TestOCIInstanceInfo:
    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_oci_instance_info(self, mock_json):
        with patch('libs.cloud_providers.oci.instance_manager.oci_instance_info.oci_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:

            dbh = Mock_DBHandler(logger)
            instModel = InstanceModel(iid=11111, dbh=dbh)
            instModel.get_param = MagicMock()
            instModel.get_param.return_value = 0
            libs.cloud_providers.oci.instance_manager.oci_instance_info.PROVIDER_OCI = "OCI"
            libs.cloud_providers.oci.instance_manager.oci_instance_info.INSTANCE_ROLE_LEGACY = 0
            event = {}
            event['dbh'] = MagicMock()
            event['inst_id'] = 0
            event['region'] = "us-west-1"
            event['isPassive'] = False
            event['cloudtype'] = "OCI"
            event['gpcs_instance_size'] = "small"
            event['version'] = "1.0"
            event['instance_name'] = "my-instance"
            event['saas_gpcs_api_endpoint'] = "https://api.example.com"
            event['cert_fetch_otp'] = "abcdef"
            event['cloud_provider'] = "oci"
            event['super_custid'] = "12345"
            event['custnode_id'] = "54321"
            event['parent_id'] = "98765"
            event['avail_domain'] = "example.com"
            event['sessionAffinity'] = "random"
            event['gp_gw_domain'] = "gw.example.com"
            event['commit_validate'] = True
            event['is_nlb_supported'] = True
            event['is_ngpa_protocol_enabled'] = True
            event['is_central_cache_supported'] = False
            event['is_instance_behind_nlb'] = False
            event['central_cache_service_endpoint'] = "https://cache.example.com"
            event['central_cache_service_backup_endpoint'] = "https://backup-cache.example.com"
            event['ciam_service_endpoint'] = "https://ciam.example.com"
            event['frr-enabled'] = 0
            event['cloud_machine_type'] = 'doesntmatter'
            event['cpu_platform'] = 'AMD'
            event['colo_interface_support'] = 0
            event['is_using_sp_interconnect'] = 1
            event['is_datapath_on_shared_vpc'] = 0
            event['proxy-protocol-enabled'] = 0
            event['inst_type'] = "some"
            
            ret = oci_instance_info(event)
            ret.customer = mocked_CustomerModel(0,dbh)
            ret.zones_list = [0,1]
            ret.region_name = "dummy"
            ret.node_type = 49
            ret.inst_type = 'blah'
            assert ret.central_cache_service_backup_endpoint == "https://backup-cache.example.com"
            assert ret.ciam_service_endpoint == "https://ciam.example.com"
            assert ret.get_user_data() == 'instance_name=my-instance,saas_gpcs_api_endpoint=https://api.example.com,cert_fetch_otp=abcdef,cloud_provider=oci,custid=1,lambdaprefix=1,custid_int=1,gp_domain=gw.example.com,route53acct=,bucket-name=,panrepo_bucket_name=,aws-access-key-id=,aws-secret-access-key=,zone=0,zone-ha=1,region=None,edge-compute-region=dummy,super_custid=12345,ssh-keys=,sase_fabric=True,commit_validate=True,is_nlb_supported=True,is_ngpa_protocol_enabled=True,is_central_cache_supported=False,is_instance_behind_nlb=0,central_cache_service_endpoint=https://cache.example.com,central_cache_service_backup_endpoint=https://backup-cache.example.com,ciam_service_endpoint=https://ciam.example.com,is_using_sp_interconnect=1,panrepo_oci_bucket_name=,capacity_type=default,instance-type=blah,frr-enabled=0'

    @patch("json.loads", return_value={"tenant_id": "123"})
    def test_oci_instance_info_swg(self, mock_json):
        with patch('libs.cloud_providers.oci.instance_manager.oci_instance_info.oci_instance_info.is_sase_fabric', wraps=mock_is_sase_fabric) as mock_sase:

            dbh = Mock_DBHandler(logger)
            instModel = InstanceModel(iid=11111, dbh=dbh)
            instModel.get_param = MagicMock()
            instModel.get_param.return_value = 0
            libs.cloud_providers.oci.instance_manager.oci_instance_info.PROVIDER_OCI = "OCI"
            libs.cloud_providers.oci.instance_manager.oci_instance_info.INSTANCE_ROLE_LEGACY = 0
            event = {}
            event['dbh'] = MagicMock()
            event['inst_id'] = 0
            event['region'] = "us-west-1"
            event['isPassive'] = False
            event['cloudtype'] = "OCI"
            event['gpcs_instance_size'] = "small"
            event['version'] = "1.0"
            event['instance_name'] = "my-instance"
            event['saas_gpcs_api_endpoint'] = "https://api.example.com"
            event['cert_fetch_otp'] = "abcdef"
            event['cloud_provider'] = "oci"
            event['super_custid'] = "12345"
            event['custnode_id'] = "54321"
            event['parent_id'] = "98765"
            event['avail_domain'] = "example.com"
            event['sessionAffinity'] = "random"
            event['gp_gw_domain'] = "gw.example.com"
            event['commit_validate'] = True
            event['is_nlb_supported'] = True
            event['is_ngpa_protocol_enabled'] = True
            event['is_central_cache_supported'] = False
            event['is_instance_behind_nlb'] = False
            event['central_cache_service_endpoint'] = "https://cache.example.com"
            event['central_cache_service_backup_endpoint'] = "https://backup-cache.example.com"
            event['ciam_service_endpoint'] = "https://ciam.example.com"
            event['frr-enabled'] = 0
            event['cloud_machine_type'] = 'doesntmatter'
            event['cpu_platform'] = 'AMD'
            event['colo_interface_support'] = 0
            event['is_using_sp_interconnect'] = 1
            event['is_datapath_on_shared_vpc'] = 0
            event['proxy-protocol-enabled'] = 0
            event['inst_type'] = "some"
            event['ep-geneve-enabled'] = "1"
            
            ret = oci_instance_info(event)
            ret.customer = mocked_CustomerModel(0,dbh)
            ret.zones_list = [0,1]
            ret.region_name = "dummy"
            ret.node_type = 153
            ret.inst_type = 'blah'
            assert ret.central_cache_service_backup_endpoint == "https://backup-cache.example.com"
            assert ret.ciam_service_endpoint == "https://ciam.example.com"
            assert ret.get_user_data() == 'instance_name=my-instance,saas_gpcs_api_endpoint=https://api.example.com,cert_fetch_otp=abcdef,cloud_provider=oci,custid=1,lambdaprefix=1,custid_int=1,gp_domain=gw.example.com,route53acct=,bucket-name=,panrepo_bucket_name=,aws-access-key-id=,aws-secret-access-key=,zone=0,zone-ha=1,region=None,edge-compute-region=dummy,super_custid=12345,ssh-keys=,sase_fabric=True,commit_validate=True,is_nlb_supported=True,is_ngpa_protocol_enabled=True,is_central_cache_supported=False,is_instance_behind_nlb=0,central_cache_service_endpoint=https://cache.example.com,central_cache_service_backup_endpoint=https://backup-cache.example.com,ciam_service_endpoint=https://ciam.example.com,is_using_sp_interconnect=1,panrepo_oci_bucket_name=,capacity_type=default,instance-type=blah,frr-enabled=0,proxy-protocol-enabled=1,ep-geneve-enabled=1'
    
