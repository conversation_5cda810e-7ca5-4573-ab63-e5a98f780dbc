import json
import sys
from unittest.mock import MagicMock, patch
import unittest
import pytest
import logging
import mock_imports_orchestrator
import libs.model.instancemodel as instancemodel
from libs.model.instancemodel import get_instance_ids_by_tenant_id_and_aggregate_region_name, \
                                     find_instances_by_custid_and_node_type, is_nat_dummy_upgrade_ongoing_in_region, \
                                     find_standalone_instances_by_custid_and_node_type_and_region_id
import utils.Exceptions as customException

class Logger:
    def __init__(self):
        self.trace_id = "123456"

    def info(self, text):
        print(text)

    def error(self, text):
        print(text)

    def debug(self, text):
        print(text)

class MockSql():
    def __init__(self, testSuite={}):
        self.testSuite = testSuite
        self._source = self.testSuite.get("testCaseQueryResult", self.testSuite)
        self.add_mock(mock_rds)

    def add_mock(self, k_v):
        self._source.update(k_v)

    def execute_query(self, sql, params=None, logger=None, component=None):
        return True
    
    def execute_orch_query(self, dbh, sql, params=None, operation=None, logger=None, component=None):
        return True, self._mock_query((sql%params), None, logger)
    
    def _mock_query(self, sql, params, logger):
        if params:
            sql = sql % params
        elif not isinstance(sql, str):
            params = sql[1]
            sql = sql[0]
            sql = sql % params
        answer = self._source.get(sql, [['mock_response']*50])
        if answer != 'NotDefinedInTestSuite':
            print(f'sql: {sql}: res: {answer}')
            return answer
        else:
            print(f"Not a defined unit test case sql: {sql}")
            return []

mock_rds = {
    'select id from cust_topology where node_type=49 and region in (select edge_location_region_id from region_master where compute_region_id=222                 and instance1_id != 0 and custid=9386)':
    [],
    'SELECT name from cust_topology where node_type=49 and instance1_id=0 and is_dynamic_node=1 and custid=9386 and region=222 and upgrade_creation=0 and is_deleted != 1':
    [],
    '(SELECT name FROM instance_master WHERE  node_type=49 AND gw_capabilities<>2 AND mark_delete=0  AND custid=9386 AND compute_region_idx=222  AND upgrade_creation=0) UNION (SELECT name FROM cust_topology WHERE  node_type=49 AND instance1_id=0  AND okyo_edge_site_id=0 AND is_deleted=0  AND (create_time > CURRENT_TIME - INTERVAL 900 SECOND)  AND custid=9386 AND region=222 AND upgrade_creation = 0)':
    ["inst1", "inst2"]
}

class Mock_cursor():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        return {"name" : "test_instance_model"}
    
    def callproc(self, *args, **kwargs):
        pass
    
    def stored_results(self):
        return []


class Mock_DBHandler():
    def __init__(self, logger):
        self.logger = logger
        self.r53_ctx = MagicMock()
        self.conn = MagicMock()
        self.avctx = MagicMock()

    def get_cursor(self, *args , **kwargs):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return

    def r53_ctx_prisma(self):
        return MagicMock()

    def r53_ctx(self):
        return MagicMock()

My_Global_Param = {}

def my_global_get_param(param_str):
    if param_str != None:
        return My_Global_Param[param_str]
    return None

class TestInstanceModel:
    def test_update_column_interface_ip_list(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=11111, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel.get_param = MagicMock()
        instModel.get_param.return_value = "11111"
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True

        actual = instModel.update_column_interface_ip_list("test_string_input")
        assert actual == False

        test_input = { "123": "********" }
        actual = instModel.update_column_interface_ip_list(test_input)
        assert actual == True

        # Adding test to cover a case where we have multiple entries in interface ip list as input
        test_input = { "277": "***********" , "222": "***********" }
        actual = instModel.update_column_interface_ip_list(test_input)
        assert actual == True

        # Adding test to cover a case where we have empty interface_ip_list
        test_input = {}
        actual = instModel.update_column_interface_ip_list(test_input)
        assert actual == True

        # Adding test to cover a case where we have invalid interface_ip_list format: list
        test_input = [{ "277": "***********" , "222": "***********" }]
        actual = instModel.update_column_interface_ip_list(test_input)
        assert actual == False

        # Adding test to cover a case where we have invalid interface_ip_list format: JSON string
        test_input = json.dumps({ "277": "***********" , "222": "***********" })
        actual = instModel.update_column_interface_ip_list(test_input)
        assert actual == False

        instModel2 = instancemodel.InstanceModel(iid=0, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel2.get_param = MagicMock()
        instModel2.get_param.return_value = 0
        instModel2.execute_query = MagicMock()
        instModel2.execute_query.return_value = True, None
        instModel2.execute_orch_query = MagicMock()
        instModel2.execute_orch_query.return_value = True, None
        instModel2.set_param = MagicMock()
        instModel2.set_param.return_value = True

        # Adding negative test to cover a case where get_param("id") is 0
        test_input = { "123": "********" }
        actual = instModel2.update_column_interface_ip_list(test_input)
        assert actual == False

        instModel3 = instancemodel.InstanceModel(iid=0, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel3.get_param = MagicMock()
        instModel3.get_param.return_value = "11111"
        instModel3.execute_query = MagicMock()
        instModel3.execute_query.return_value = False, None
        instModel3.execute_orch_query = MagicMock()
        instModel3.execute_orch_query.return_value = True, None
        instModel3.set_param = MagicMock()
        instModel3.set_param.return_value = True

        # Adding negative test to cover a case where execute_query returns False
        test_input = { "123": "********" }
        actual = instModel3.update_column_interface_ip_list(test_input)
        assert actual == False

    def test_update_column_interface_ipv6_list(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=11111, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel.get_param = MagicMock()
        instModel.get_param.return_value = "11111"
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True

        try:
            actual = instModel.update_column_interface_ipv6_list("test_string_input")
        except customException.InvalidInterfaceIpv6ListException as E:
            logger.error(f"Invalid IPv6 list exception {E.args}")
        assert f"Interface ip list test_string_input is invalid." in caplog.text

        test_input = { "123": "2ff0:1500:ab::2" }
        actual = instModel.update_column_interface_ipv6_list(test_input)
        assert actual == True

        # Adding test to cover a case where interface_ipvv6_list is None
        test_input = None
        actual = instModel.update_column_interface_ipv6_list(test_input)
        assert actual == False
        assert "Skip updating interface_ipv6_list in RDS since input interface_ipv6_list is None" in caplog.text

        # Adding test to cover a case where we have multiple entries in interface ip list as input
        test_input = { "277": "2ff0:1500:ab::2" , "222": "2ff0:1600:ab::2" }
        actual = instModel.update_column_interface_ipv6_list(test_input)
        assert actual == True

        # Adding test to cover a case where we have empty interface_ip_list
        test_input = {}
        actual = instModel.update_column_interface_ipv6_list(test_input)
        assert actual == True

        # Adding test to cover a case where we have invalid interface_ip_list format: list
        test_input = [{ "277": "2ff0:1500:ab::2" , "222": "2ff0:1600:ab::2" }]
        try:
            actual = instModel.update_column_interface_ipv6_list(test_input)
        except customException.InvalidInterfaceIpv6ListException as E:
            logger.error(f"Invalid IPv6 list exception {E.args}")
        assert f"Interface ip list test_string_input is invalid." in caplog.text

        # Adding test to cover a case where we have invalid interface_ip_list format: JSON string
        test_input = json.dumps({ "277": "2ff0:1500:ab::2" , "222": "2ff0:1600:ab::2" })
        try:
            actual = instModel.update_column_interface_ipv6_list(test_input)
        except customException.InvalidInterfaceIpv6ListException as E:
            logger.error(f"Invalid IPv6 list exception {E.args}")
        assert f"Interface ip list test_string_input is invalid." in caplog.text

        instModel2 = instancemodel.InstanceModel(iid=0, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel2.get_param = MagicMock()
        instModel2.get_param.return_value = 0
        instModel2.execute_query = MagicMock()
        instModel2.execute_query.return_value = True, None
        instModel2.execute_orch_query = MagicMock()
        instModel2.execute_orch_query.return_value = True, None
        instModel2.set_param = MagicMock()
        instModel2.set_param.return_value = True

        # Adding negative test to cover a case where get_param("id") is 0
        test_input = { "123": "2ff0:1500:ab::2" }
        try:
            actual = instModel2.update_column_interface_ipv6_list(test_input)
        except customException.InvalidInstanceIdException as E:
            logger.error(f"Invalid Instance Id exception {E.args}")
        assert f"Incorrect id in the instance master to update." in caplog.text

        instModel3 = instancemodel.InstanceModel(iid=0, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel3.get_param = MagicMock()
        instModel3.get_param.return_value = "11111"
        instModel3.execute_query = MagicMock()
        instModel3.execute_query.return_value = False, None
        instModel3.execute_orch_query = MagicMock()
        instModel3.execute_orch_query.return_value = True, None
        instModel3.set_param = MagicMock()
        instModel3.set_param.return_value = True

        # Adding negative test to cover a case where execute_query returns False
        test_input = { "123": "2ff0:1500:ab::2" }
        try:
            actual = instModel3.update_column_interface_ipv6_list(test_input)
        except customException.DbUpdateException as E:
            logger.error(f"Invalid IPv6 list exception {E.args}")
        assert f"Failed to execute sql" in caplog.text

    @patch('libs.model.instancemodel.json.dumps', side_effect = TypeError("test"))
    def test_update_column_interface_ipv6_list_json_dumps_exception_type_err(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=11111, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel.get_param = MagicMock()
        instModel.get_param.return_value = "11111"
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True

        test_input = { "123": "2ff0:1500:ab::2" }
        actual = False
        try:
            actual = instModel.update_column_interface_ipv6_list(test_input)
        except customException.InvalidInterfaceIpv6ListJsonException as E:
            logger.error(f"Invalid IPv6 list exception {E.args}")
            assert "to JSON string due to type exception:" in E.args[0]
        assert actual == False

    @patch('libs.model.instancemodel.json.dumps', side_effect = ValueError("test"))
    def test_update_column_interface_ipv6_list_json_dumps_exception_value_err(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=11111, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel.get_param = MagicMock()
        instModel.get_param.return_value = "11111"
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True

        test_input = { "123": "2ff0:1500:ab::2" }
        actual = False
        try:
            actual = instModel.update_column_interface_ipv6_list(test_input)
        except customException.InvalidInterfaceIpv6ListJsonException as E:
            logger.error(f"Invalid IPv6 list exception {E.args}")
            assert "to JSON string due to value exception:" in E.args[0]
        assert actual == False

    @patch('libs.model.instancemodel.json.dumps', side_effect = OverflowError("test"))
    def test_update_column_interface_ipv6_list_json_dumps_exception_overflow_err(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=11111, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel.get_param = MagicMock()
        instModel.get_param.return_value = "11111"
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True

        test_input = { "123": "2ff0:1500:ab::2" }
        actual = False
        try:
            actual = instModel.update_column_interface_ipv6_list(test_input)
        except customException.InvalidInterfaceIpv6ListJsonException as E:
            logger.error(f"Invalid IPv6 list exception {E.args}")
            assert "to JSON string due to overflow exception:" in E.args[0]
        assert actual == False

    def test_update_interface_ip_list_with_egress_ip_success(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel.get_param = MagicMock()
        instModel.get_param.return_value = json.dumps({"277": "**************"})
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None   
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        instModel.update_column_interface_ip_list = MagicMock()
        instModel.update_column_interface_ip_list.return_value = True
        actual = instModel.update_interface_ip_list_with_egress_ip(277)
        assert actual == True

    def test_update_interface_ipv6_list_with_egress_ipv6_success(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["id"] = "11111"
        My_Global_Param["egress_ipv6_list"] = json.dumps({"277": "2ff0:1500:ab::2"})
        My_Global_Param["interface_ipv6_list"] = "None"

        instModel.get_param = my_global_get_param
        instModel.get_param.return_value = json.dumps({"277": "2ff0:1500:ab::2"})
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        actual = instModel.update_interface_ipv6_list_with_egress_ipv6(277)
        assert actual == True

    @patch('libs.model.instancemodel.InstanceModel.update_column_interface_ipv6_list')
    def test_update_interface_ipv6_list_with_egress_ipv6_exception_instance(self, mock_update, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        mock_update.side_effect = customException.InvalidInstanceIdException("test")

        My_Global_Param["id"] = "11111"
        My_Global_Param["egress_ipv6_list"] = json.dumps({"277": "2ff0:1500:ab::2"})
        My_Global_Param["interface_ipv6_list"] = "None"

        instModel.get_param = my_global_get_param
        instModel.get_param.return_value = json.dumps({"277": "2ff0:1500:ab::2"})
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        actual = instModel.update_interface_ipv6_list_with_egress_ipv6(277)
        assert actual == False
        assert "Error updating interface_ipv6_list since instance cannot be found:" in caplog.text

    @patch('libs.model.instancemodel.InstanceModel.update_column_interface_ipv6_list')
    def test_update_interface_ipv6_list_with_egress_ipv6_exception_interface_ipv6_list(self, mock_update, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        mock_update.side_effect = customException.InvalidInterfaceIpv6ListException("test")

        My_Global_Param["id"] = "11111"
        My_Global_Param["egress_ipv6_list"] = json.dumps({"277": "2ff0:1500:ab::2"})
        My_Global_Param["interface_ipv6_list"] = "None"

        instModel.get_param = my_global_get_param
        instModel.get_param.return_value = json.dumps({"277": "2ff0:1500:ab::2"})
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        actual = instModel.update_interface_ipv6_list_with_egress_ipv6(277)
        assert actual == False
        assert "Error updating interface_ipv6_list since interface_ipv6_list is invalid:" in caplog.text

    @patch('libs.model.instancemodel.json.loads')
    def test_update_interface_ipv6_list_with_egress_ipv6_json_exception(self, mock_json_loads, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        mock_json_loads.side_effect = json.JSONDecodeError("Json Error","test",0)

        My_Global_Param["id"] = "11111"
        My_Global_Param["egress_ipv6_list"] = json.dumps({"277": "2ff0:1500:ab::2"})
        My_Global_Param["interface_ipv6_list"] = "None"

        instModel.get_param = my_global_get_param
        instModel.get_param.return_value = json.dumps({"277": "2ff0:1500:ab::2"})
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        actual = instModel.update_interface_ipv6_list_with_egress_ipv6(277)
        assert actual == False
        assert "Error decoding egress_ipv6_list due to JSON Decode Error:" in caplog.text

    @patch('libs.model.instancemodel.json.loads')
    def test_update_interface_ipv6_list_with_egress_ipv6_type_exception(self, mock_json_loads, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        mock_json_loads.side_effect = TypeError("Type Error")

        My_Global_Param["id"] = "11111"
        My_Global_Param["egress_ipv6_list"] = json.dumps({"277": "2ff0:1500:ab::2"})
        My_Global_Param["interface_ipv6_list"] = "None"

        instModel.get_param = my_global_get_param
        instModel.get_param.return_value = json.dumps({"277": "2ff0:1500:ab::2"})
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        actual = instModel.update_interface_ipv6_list_with_egress_ipv6(277)
        assert actual == False
        assert "Error decoding egress_ipv6_list due to Type Error:" in caplog.text

    @patch('libs.model.instancemodel.json.loads')
    def test_update_interface_ipv6_list_with_egress_ipv6_json_exception2(self, mock_json_loads, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["id"] = "11111"
        My_Global_Param["egress_ipv6_list"] = json.dumps({"277": "2ff0:1500:ab::2"})
        My_Global_Param["interface_ipv6_list"] = json.dumps({"123": "2ff0:1200:ab::2"})

        mock_json_loads.side_effect = [{"277": "2ff0:1500:ab::2"}, json.JSONDecodeError("Json Error","test",0)]

        instModel.get_param = my_global_get_param
        instModel.get_param.return_value = json.dumps({"277": "2ff0:1500:ab::2"})
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        actual = instModel.update_interface_ipv6_list_with_egress_ipv6(277)
        assert actual == False
        assert "Error decoding interface_ipv6_list due to JSON Decode Error:" in caplog.text

    @patch('libs.model.instancemodel.json.loads')
    def test_update_interface_ipv6_list_with_egress_ipv6_type_exception2(self, mock_json_loads, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["id"] = "11111"
        My_Global_Param["egress_ipv6_list"] = json.dumps({"277": "2ff0:1500:ab::2"})
        My_Global_Param["interface_ipv6_list"] = json.dumps({"123": "2ff0:1200:ab::2"})

        mock_json_loads.side_effect = [{"277": "2ff0:1500:ab::2"}, TypeError("Type Error")]

        instModel.get_param = my_global_get_param
        instModel.get_param.return_value = json.dumps({"277": "2ff0:1500:ab::2"})
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        actual = instModel.update_interface_ipv6_list_with_egress_ipv6(277)
        assert actual == False
        assert "Error decoding interface_ipv6_list due to Type Error:" in caplog.text

    def test_update_interface_ipv6_list_with_egress_ipv6_success_non_empty_interface_list(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["id"] = "11111"
        My_Global_Param["egress_ipv6_list"] = json.dumps({"277": "2ff0:1500:ab::2"})
        My_Global_Param["interface_ipv6_list"] = json.dumps({"123": "2ff0:1200:ab::2"})

        instModel.get_param = my_global_get_param
        instModel.get_param.return_value = json.dumps({"277": "2ff0:1500:ab::2"})
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        actual = instModel.update_interface_ipv6_list_with_egress_ipv6(277)
        assert actual == True

    def test_update_interface_ipv6_list_with_egress_ipv6_success_overwrite_existing(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["id"] = "11111"
        My_Global_Param["egress_ipv6_list"] = json.dumps({"277": "2ff0:1500:ab::2"})
        My_Global_Param["interface_ipv6_list"] = json.dumps({"277": "2ff0:1200:ab::2"})

        instModel.get_param = my_global_get_param
        instModel.get_param.return_value = json.dumps({"277": "2ff0:1500:ab::2"})
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        actual = instModel.update_interface_ipv6_list_with_egress_ipv6(277)
        assert actual == True

    def test_update_interface_ipv6_list_with_egress_ipv6_success_interface_ipv6_list_is_a_list(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["id"] = "11111"
        My_Global_Param["egress_ipv6_list"] = json.dumps({"277": "2ff0:1500:ab::2"})
        My_Global_Param["interface_ipv6_list"] = json.dumps([{"100": "2ff0:1000:ab::2"}])

        instModel.get_param = my_global_get_param
        instModel.get_param.return_value = json.dumps({"277": "2ff0:1500:ab::2"})
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        actual = instModel.update_interface_ipv6_list_with_egress_ipv6(277)
        assert actual == True

    def test_update_interface_ip_list_with_egress_ip_failure(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel.get_param = MagicMock()
        instModel.get_param.return_value = json.dumps({"277": "**************"})
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        instModel.update_column_interface_ip_list = MagicMock()
        instModel.update_column_interface_ip_list.return_value = False
        actual = instModel.update_interface_ip_list_with_egress_ip(277)
        assert actual == False

    def test_update_interface_ipv6_list_with_egress_ipv6_failure(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["egress_ipv6_list"] = json.dumps({"277": "2ff0:1500:ab::2"})
        My_Global_Param["interface_ipv6_list"] = "None"

        instModel.get_param = my_global_get_param
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        
    def test_update_column_lb_details(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel.get_param = MagicMock()
        instModel.get_param.return_value = "dummyid"
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        actual = instModel.update_column_lb_details("dummy_ip")
        assert actual == True

    def test_update_interface_ipv6_list_with_egress_ipv6_no_update(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel.get_param = MagicMock()
        instModel.get_param.return_value = "None"
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        instModel.update_column_interface_ipv6_list = MagicMock()
        instModel.update_column_interface_ipv6_list.return_value = True
        actual = instModel.update_interface_ipv6_list_with_egress_ipv6(277)
        assert actual == True

    def test_update_interface_ipv6_list_with_egress_ipv6_no_update_no_edge(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel.get_param = MagicMock()
        instModel.get_param.return_value = json.dumps({"100": "2ff0:1000:ab::2"})
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.execute_orch_query = MagicMock()
        instModel.execute_orch_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True
        instModel.update_column_interface_ipv6_list = MagicMock()
        instModel.update_column_interface_ipv6_list.return_value = True
        actual = instModel.update_interface_ipv6_list_with_egress_ipv6(277)
        assert actual == True

    def test_save_egress_ip_list(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["id"] = "11111"

        instModel.get_param = my_global_get_param
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True

        egress_ip_list = {"277": "********"}
        ret = instModel.save_egress_ip_list(egress_ip_list)
        assert ret == True

    def test_save_egress_ip_list_fail(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["id"] = 0

        instModel.get_param = my_global_get_param
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True

        egress_ip_list = {"277": "********"}
        ret = instModel.save_egress_ip_list(egress_ip_list)
        assert ret == False

    def test_save_egress_ip_list_query_fail(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["id"] = "11111"

        instModel.get_param = my_global_get_param
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = False, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True

        egress_ip_list = {"277": "********"}
        ret = instModel.save_egress_ip_list(egress_ip_list)
        assert ret == False

    def test_save_egress_ip_list_fail_input_None(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["id"] = "11111"

        instModel.get_param = my_global_get_param
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True

        egress_ip_list = None
        ret = instModel.save_egress_ip_list(egress_ip_list)
        assert ret == False

    def test_save_egress_ipv6_list(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["id"] = "11111"

        instModel.get_param = my_global_get_param
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True

        egress_ipv6_list = {"277": "2ff0:1500:ab::2"}
        ret = instModel.save_egress_ipv6_list(egress_ipv6_list)
        assert ret == True

    def test_save_egress_ipv6_list_fail(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["id"] = 0

        instModel.get_param = my_global_get_param
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True

        egress_ipv6_list = {"277": "2ff0:1500:ab::2"}
        ret = instModel.save_egress_ipv6_list(egress_ipv6_list)
        assert ret == False

    def test_save_egress_ipv6_list_query_fail(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["id"] = "11111"

        instModel.get_param = my_global_get_param
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = False, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True

        egress_ipv6_list = {"277": "2ff0:1500:ab::2"}
        ret = instModel.save_egress_ipv6_list(egress_ipv6_list)
        assert ret == False

    def test_save_egress_ipv6_list_fail_input_None(self, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        My_Global_Param["id"] = "11111"

        instModel.get_param = my_global_get_param
        instModel.execute_query = MagicMock()
        instModel.execute_query.return_value = True, None
        instModel.set_param = MagicMock()
        instModel.set_param.return_value = True

        egress_ipv6_list = None
        ret = instModel.save_egress_ipv6_list(egress_ipv6_list)
        assert ret == False

    @patch('libs.model.instancemodel.execute_orch_query', return_value=[True, (1, 95, 220, 1)])
    def test_is_ingress_ip_reduction_enabled(self, mock_execute_orch_query, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)
        custid = 95
        compute_region_idx = 220
        exception_triggered = False
        try:
            is_enabled = instancemodel.is_ingress_ip_reduction_enabled(dbh, custid, compute_region_idx)
        except Exception as E:
            logger.error(f"Exception was raised by is_ingress_ip_reduction_enabled: {str(E.args)}")
            exception_triggered = True

        assert exception_triggered == False
        assert is_enabled == 1
        assert (f"Ingress IP reduction setting is {is_enabled} for custid {custid} region {compute_region_idx}") in caplog.text

    @patch('libs.model.instancemodel.execute_orch_query', return_value=[False, None])
    def test_is_ingress_ip_reduction_enabled_failed(self, mock_execute_orch_query, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)
        custid = 95
        compute_region_idx = 220
        exception_triggered = False
        try:
            logger.info("123")
            is_enabled = instancemodel.is_ingress_ip_reduction_enabled(dbh, custid, compute_region_idx)
        except customException.DbReadException as E:
            logger.info("456")
            exception_triggered = True
            logger.error(f"Exception was raised by is_ingress_ip_reduction_enabled: {str(E.args)}")
        assert exception_triggered == True
        assert (f"!!! Fatal! Failed to execute query to check for ingress IP reduction. Return status") in caplog.text

    @patch('libs.model.instancemodel.execute_orch_query', return_value=[True, None])
    def test_is_ingress_ip_reduction_enabled_no_entry(self, mock_execute_orch_query, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)
        custid = 95
        compute_region_idx = 220
        exception_triggered = False
        try:
            is_enabled = instancemodel.is_ingress_ip_reduction_enabled(dbh, custid, compute_region_idx)
        except Exception as E:
            logger.error(f"Exception was raised by is_ingress_ip_reduction_enabled: {str(E.args)}")
            exception_triggered = True

        assert exception_triggered == False
        assert is_enabled == False
        assert (f"Ingress IP reduction check: Entry in cust_nat_mgmt_table for custid {custid} not found") in caplog.text

    @patch('libs.model.instancemodel.execute_orch_query', return_value=[True, (1, 95, 220, 0)])
    def test_is_ingress_ip_reduction_enabled_disabled(self, mock_execute_orch_query, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)
        custid = 95
        compute_region_idx = 220
        exception_triggered = False
        try:
            is_enabled = instancemodel.is_ingress_ip_reduction_enabled(dbh, custid, compute_region_idx)
        except Exception as E:
            logger.error(f"Exception was raised by is_ingress_ip_reduction_enabled: {str(E.args)}")
            exception_triggered = True

        assert exception_triggered == False
        assert is_enabled == 0
        assert (f"Ingress IP reduction setting is {is_enabled} for custid {custid} region {compute_region_idx}") in caplog.text

    @patch('libs.model.instancemodel.get_portal_base', return_value='prismaaccess.com')
    def test_cleanup_fqdn_success_selective_egress_ip_deletion_enabled(self, patched_get_portal_base, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        instModel.get_param = MagicMock()
        instModel.get_param.return_value = "dummy.com"
        actual = instModel.cleanup_fqdn(dbh, selective_egress_ip_deletion=True, egress_ip_dict={})
        assert 'delete node_fqdn dummy.com for this instance' in caplog.text
        assert 'Populating ingress_ip_dict with egress_ip_list for FQDN cleanup' not in caplog.text

    
    # TODO: Tejas P. Failing during unit test run.
    
    @patch('libs.db.dbhandle.DbHandle.get_cursor', wraps=Mock_cursor)
    @patch('libs.model.instancemodel.get_portal_base', return_value='prismaaccess.com')
    def test_cleanup_fqdn_nlb_ipv6_selective(self, patched_get_portal_base, mock_cursor, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)
        Mock_cursor.fetchone = MagicMock()
        Mock_cursor.fetchone.return_value = [["cust_name"]]
        instModel.get_param = MagicMock()
        param_dict = {
                "is_instance_behind_nlb": True,
                "node_fqdn": "dummy.com",
                "node_type": 49,
                "lb_details_v6": "dummyv6",
                "lb_details": "dummyv4",
                "vmid": "dummyvm",
                "compute_region_idx": "dummyCompute"
        }

        instModel.get_param = MagicMock(side_effect=lambda x: param_dict.get(x, 0))
        actual = instModel.cleanup_fqdn(dbh, selective_egress_ip_deletion=True, egress_ip_dict={"220": "dummy_ip_220"})
        assert "ingress_ip_dict {'220': 'dummy_ip_220'}" in caplog.text
    

    @patch('libs.model.instancemodel.get_portal_base', return_value='panclouddev.com')
    @patch('libs.model.instancemodel.InstanceModelGlobal')
    def test_cleanup_fqdn_RNSC(self, mock_instance_model_global, mock_portal_base, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        instModel.get_param = MagicMock()
        params = {
            "node_fqdn": "test_fqdn",
            "node_type": 48,
            "public_ip": "*******",
            "id": 100,
            "clusterid": 100,
        }
        instModel.get_param.side_effect = lambda k: params.get(k)
        instModel.cleanup_fqdn(dbh)
        dbh.r53_ctx.deleteRNSCFQDN.assert_called_with("test_fqdn")

    '''
    # TODO: Tejas P. Failing during unit test run

    @patch('libs.model.instancemodel.get_portal_base', return_value='panclouddev.com')
    @patch('libs.model.instancemodel.InstanceModelGlobal')
    @patch('libs.model.instancemodel.get_cloud_native_location_name_from_region_id')
    @patch('libs.model.instancemodel.aws_ipv6_mgmt.IPV6_Handler')
    def test_delete(self, mock_ipv6, mock_get_cloud_location, mock_instance, mock_get_portal, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        
        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        instModel.logger = logger
        instModel.get_param = MagicMock()
        params = {
            "node_fqdn": "test_fqdn",
            "node_type": 48,
            "public_ip": "*******",
            "id": 100,
            "clusterid": 100,
        }
        instModel.get_param.side_effect = lambda k: params.get(k)
        
        instModel.set_param = MagicMock()
        def _set(k, v):
            params[k] = v
        instModel.set_param.side_effect = _set
        instModel.cancel_spot_request_iid = MagicMock()
        instModel.recycle_public_ip = MagicMock()
        instModel.reclaim_ipv6_address_by_account_id = MagicMock()
        instModel.cleanup_fqdn = MagicMock()
        def _cleanup_fqdn(dbh):
            # Check id is not set to None when cleanup_fqdn is called
            assert instModel.get_param("id") == 100
        instModel.cleanup_fqdn.side_effect = _cleanup_fqdn
        
        instModel.delete(dbh, preserve_nat_ip=True)
        assert instModel.get_param("id") == None
        instModel.recycle_public_ip.assert_called_with(dbh, True)
    '''

    @patch('libs.model.instancemodel.get_portal_base', return_value='panclouddev.com')
    @patch('libs.model.instancemodel.InstanceModelGlobal')
    def test_cleanup_fqdn_IPv6(self, mock_instance_model_global, mock_portal_base, caplog):
        logger = logging.getLogger()

        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        instModel.get_param = MagicMock()
        params = {
            "node_fqdn": "test_fqdn",
            "node_type": 49,
            "public_ip": "*******",
            "public_ipv6" : "2001::200:1",
            "id": 100,
            "clusterid": 100,
        }
        instModel.get_param.side_effect = lambda k: params.get(k)
        instModel.logger = logger
        instModel.cleanup_fqdn(dbh, is_ipv6=True)
        dbh.r53_ctx.deleteRNSCFQDN.assert_not_called()


    @patch("libs.model.instancemodel._mapping_for_firewall_summary_table")
    def test_insert_in_firewall_summary_table_send_to_lambda(self, mock_test_mapping_for_firewall_summary_table, caplog):
        '''
            This UT will test the scenario where the lambda_params are successfully sent to async_call_firewall_summary_notify_lambda
        '''

        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger)

        caplog.set_level(logging.INFO)

        panorama_job_id = "265"
        push_type = "FW"
        panorama_id = "9"

        instancemodel.InstanceModel.insert_in_firewall_summary_table(MagicMock(), dbh, MagicMock(), panorama_job_id, panorama_id,
                                                       push_type, MagicMock())
        assert "Sending the params to async_call_firewall_summary_notify_lambda" in caplog.text

    @patch("libs.model.instancemodel.config_commit_al_notify")
    @patch("libs.model.instancemodel._mapping_for_firewall_summary_table")
    def test_insert_in_firewall_summary_table_exception(self, mock_test_mapping_for_firewall_summary_table,
                                                        mock_config_commit_al_notify, caplog):
        '''
            This UT will test the scenario where there is an exception while sending the params to async_call_firewall_summary_notify_lambda
        '''
        logger = logging.getLogger('testlogger')
        dbh = Mock_DBHandler(logger)

        caplog.set_level(logging.INFO)

        panorama_job_id = "265"
        push_type = "FW"
        panorama_id = "9"

        mock_config_commit_al_notify.return_value = MagicMock()
        mock_async = mock_config_commit_al_notify.async_call_firewall_summary_notify_lambda
        mock_async.side_effect = Exception("test error")

        instancemodel.InstanceModel.insert_in_firewall_summary_table(MagicMock(), dbh, MagicMock(), panorama_job_id, panorama_id,
                                                       push_type, MagicMock())
        assert "Exception: Failed to send new job message to alert engine. Err: test error" in caplog.text


    @patch('libs.model.instancemodel.execute_orch_query', return_value=[True, [('***********', json.dumps({123: '***********', 345: '***********'}))]])
    def test_get_rn_egress_ip_list_success_case1(self, mock_exec_orch_query, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)

        ret = instancemodel.get_rn_egress_ip_list(dbh, 123)
        assert set(ret) == set(['***********', '***********', '***********'])


    @patch('libs.model.instancemodel.execute_orch_query', return_value=[True, [('***********', json.dumps({}))]])
    def test_get_rn_egress_ip_list_success_case2(self, mock_exec_orch_query, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)

        ret = instancemodel.get_rn_egress_ip_list(dbh, 123)
        assert set(ret) == set(['***********'])


    @patch('libs.model.instancemodel.execute_orch_query', return_value=[True, [('***********', None)]])
    def test_get_rn_egress_ip_list_success_case3(self, mock_exec_orch_query, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)

        ret = instancemodel.get_rn_egress_ip_list(dbh, 123)
        assert set(ret) == set(['***********'])
        assert "No egress_ip to add" in caplog.text


    @patch('libs.model.instancemodel.execute_orch_query', return_value=[False, []])
    def test_get_rn_egress_ip_list_failure(self, mock_exec_orch_query, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)

        ret = instancemodel.get_rn_egress_ip_list(dbh, 123)
        assert ret == []
        assert "!!! Fatal! Failed to execute query or no entries found. Return status False, []" in caplog.text


    @patch('libs.model.instancemodel.execute_orch_query', return_value=[True, [(12, 'inst-1'), (34, 'inst-2')]])
    def test_get_ep_deployment_regions_for_cust_success(self, mock_find_instances, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)

        regions = instancemodel.get_ep_deployment_regions_for_cust(dbh, 345)
        assert regions == [12, 34]
        assert "Regions with EP Instances for 345 are [12, 34]" in caplog.text

    @patch('libs.model.instancemodel.batch_get_deployed_gw_edge_compute_location',
           return_value={'name_a': [[208, 200]], 'name_b': [[201, 200]], 'name_c': [[203, 203]]})
    def test_get_gp_deployed_regions_for_cust(self, mock_query, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)
        regions = instancemodel.get_gp_deployed_regions_for_cust(dbh, 200, 9386)
        assert regions == [208, 201]


    @patch('libs.model.instancemodel.find_instances_by_custid_and_region_id', return_value=[False, []])
    def test_get_ep_deployment_regions_for_cust_failure_case1(self, mock_find_instances, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)

        regions = instancemodel.get_ep_deployment_regions_for_cust(dbh, 345)
        assert regions == []
        assert "Regions with EP Instances for 345 are []" in caplog.text


    @patch('libs.model.instancemodel.find_instances_by_custid_and_region_id', return_value=[True, []])
    def test_get_ep_deployment_regions_for_cust_failure_case2(self, mock_find_instances, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)

        regions = instancemodel.get_ep_deployment_regions_for_cust(dbh, 345)
        assert regions == []
        assert "Regions with EP Instances for 345 are []" in caplog.text


    @patch('libs.model.instancemodel.get_ep_deployment_regions_for_cust', return_value=[12, 34])
    @patch('libs.model.instancemodel.is_enable_rn_to_ep_ctx_pass', return_value=True)
    def test_is_swg_proxy_deployment_needed_success(self, mock_has_ep_dep, mock_is_enable_rn_to_ep, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)

        ret, regions = instancemodel.is_swg_proxy_deployment_needed(dbh, 123, 48)
        assert ret == True
        assert regions == [12, 34]
        assert "is_swg_proxy_deployment_needed: All requirements for SWG Proxy redeployment are met; return True" in caplog.text


    @patch('libs.model.instancemodel.get_ep_deployment_regions_for_cust', return_value=[12, 34])
    @patch('libs.model.instancemodel.is_enable_rn_to_ep_ctx_pass', return_value=True)
    def test_is_swg_proxy_deployment_needed_failure_case1(self, mock_has_ep_dep, mock_is_enable_rn_to_ep, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)

        ret, regions = instancemodel.is_swg_proxy_deployment_needed(dbh, 123, 49)
        assert ret == False
        assert regions == []
        assert "is_swg_proxy_deployment_needed: SWG Proxy redeployment not needed; return False" in caplog.text


    @patch('libs.model.instancemodel.get_ep_deployment_regions_for_cust', return_value=[])
    @patch('libs.model.instancemodel.is_enable_rn_to_ep_ctx_pass', return_value=True)
    def test_is_swg_proxy_deployment_needed_failure_case2(self, mock_has_ep_dep, mock_is_enable_rn_to_ep, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)

        ret, regions = instancemodel.is_swg_proxy_deployment_needed(dbh, 123, 48)
        assert ret == False
        assert regions == []
        assert "is_swg_proxy_deployment_needed: SWG Proxy redeployment not needed; return False" in caplog.text


    @patch('libs.model.instancemodel.get_ep_deployment_regions_for_cust', return_value=[12, 34])
    @patch('libs.model.instancemodel.is_enable_rn_to_ep_ctx_pass', return_value=False)
    def test_is_swg_proxy_deployment_needed_failure_case3(self, mock_has_ep_dep, mock_is_enable_rn_to_ep, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)

        ret, regions = instancemodel.is_swg_proxy_deployment_needed(dbh, 123, 48)
        assert ret == False
        assert regions == []
        assert "is_swg_proxy_deployment_needed: SWG Proxy redeployment not needed; return False" in caplog.text

    @patch('libs.model.instancemodel.execute_orch_query', side_effect=[[True, ['inst-1']],[True, ['inst-1','inst-2']]])
    def test_find_non_transient_entries_by_custid_and_region(self, mock_query, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        res = instancemodel.find_non_transient_entries_by_custid_and_region(dbh, "169", 214, 49)
        assert res == (True, ['inst-1', 'inst-2'])

    @patch('libs.model.instancemodel.execute_orch_query', side_effect=[(True,['11110']),(True, ['inst-1']),(True, ['inst-1']),(True, ['inst-1','inst-2'])])
    def test_find_non_transient_entries_by_custid_and_region_with_new_edge(self, mock_query, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        res = instancemodel.find_non_transient_entries_by_custid_and_region(dbh, "169", 214, 49, edge_region_idx=255)
        assert res == (True, ['inst-1', 'inst-2'])

    @patch('libs.model.instancemodel.execute_orch_query', side_effect=[(True,['11110']),(True, []),(True, ['inst-1']),(True, ['inst-1','inst-2'])])
    def test_find_non_transient_entries_by_custid_and_region_with_edge_onboarded(self, mock_query, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        res = instancemodel.find_non_transient_entries_by_custid_and_region(dbh, "169", 214, 49)
        assert res == (True, ['inst-1', 'inst-2'])

    '''
    #TODO: Tejas P. Failing during unittest run
    @patch('libs.model.instancemodel.execute_orch_query')
    def test_find_non_transient_entries_by_custid_and_region_with_hard_sql_checks(self, mock_query, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)
        mock_responses = MockSql()
        mock_responses.add_mock(mock_rds)
        mock_query.side_effect = mock_responses.execute_orch_query
        res = instancemodel.find_non_transient_entries_by_custid_and_region(dbh, "9386", 222, 49, 222)
        assert res == (True, ["inst1", "inst2"])
    '''

    @patch('libs.model.instancemodel.execute_orch_query', side_effect=[(True,['11110']),(True, ['2222']),(True, ['inst-1']),(True, ['inst-1','inst-2'])])
    def test_find_non_transient_entries_by_custid_and_region_with_dynamic_node(self, mock_query, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        caplog.set_level(logging.INFO)

        res = instancemodel.find_non_transient_entries_by_custid_and_region(dbh, "169", 214, 49)
        assert res == (True, ['inst-1', 'inst-2'])


    @patch('libs.model.instancemodel.InstanceModel.create_fw_queue')
    @patch('libs.model.instancemodel.get_dr_region_list')
    def test_find_non_transient_entries_by_custid_and_region(self, mock_dr, mock_fw_queue):
        logger = logging.getLogger('testlogger')
        mock_dr.return_value = ['us-west-2', 'us-east-1']
        mock_fw_queue.return_value = True
        dbh = Mock_DBHandler(logger)
        instModel = instancemodel.InstanceModel(iid=100, dbh=dbh)
        ret = instModel.bind(dbh=dbh, custid=123, node_type=49, node_id=1, is_new=True, custnode=MagicMock())
        assert ret

    ###################### functions to test insert_last_successfull_job_info function ####################
    '''
    This function tests the scenario where a latest successfull job is present for the parent
    and the entry is also present in xxx_cfgserv_panorama_cfg_request table
    '''

    def test_insert_last_successfull_job_info_success_job_present_for_tenant_in_both_tables(self, caplog):
        logger = logging.getLogger('testlogger')

        class Mock_DBHandler_insert_last_successfull_job_info():
            def __init__(self, logger):
                self.logger = logger

            def get_cursor(self):
                return Mock_cursor_insert_last_successfull_job_info()

            def cursorclose(self, cursor):
                return

        class Mock_cursor_insert_last_successfull_job_info():
            def __init__(self):
                self.sql = None
                self.params = None

            def execute(self, sql, params):
                self.sql = sql
                self.params = params

            def fetchone(self):
                if "fw_job_status='Success' and panorama_job_id <> 0 order by id desc LIMIT 1" in self.sql:
                    return (66, 2964855483838833152, "GPGW", 826239852)
                elif "xxx_cfgserv_panorama_cfg_request where panorama_job_id =" in self.sql:
                    return (66,)
                else:
                    return None

        dbh = Mock_DBHandler_insert_last_successfull_job_info(logger)
        caplog.set_level(logging.INFO)

        # instantiate instance model
        instModel = instancemodel.InstanceModel(iid=111, dbh=dbh)
        instModel.set_param("slot_nr", 1)
        instModel.insert_in_firewall_summary_table = MagicMock()
        res = instModel.insert_last_successfull_job_info(dbh, "111")
        assert res == True
        assert "insert_last_successfull_job_info: latest success job present for the parent" in caplog.text
        assert "insert_last_successfull_job_info: panorama job id present in xxx_cfgserv_panorama_cfg_request" in caplog.text

    '''
       This function tests the scenario where a latest successfull job is not present for the parent
        so we fall back to getting the latest job for the parent and it is present in firewall summary table       
       and the entry is present in xxx_cfgserv_panorama_cfg_request table
       '''

    def test_insert_last_successfull_job_info_latest_job_present(self, caplog):
        logger = logging.getLogger('testlogger')

        class Mock_DBHandler_insert_last_successfull_job_info():
            def __init__(self, logger):
                self.logger = logger

            def get_cursor(self):
                return Mock_cursor_insert_last_successfull_job_info()

            def cursorclose(self, cursor):
                return

        class Mock_cursor_insert_last_successfull_job_info():
            def __init__(self):
                self.sql = None
                self.params = None

            def execute(self, sql, params):
                self.sql = sql
                self.params = params

            def fetchone(self):
                if "fw_job_status='Success' and panorama_job_id <> 0 order by id desc LIMIT 1" in self.sql:
                    return ()
                elif "fw_job_status='Success'" not in self.sql:
                    return (66, 2964855483838833152, "GPGW", 826239852)
                elif "xxx_cfgserv_panorama_cfg_request where panorama_job_id =" in self.sql:
                    return (66,)
                else:
                    return None

        dbh = Mock_DBHandler_insert_last_successfull_job_info(logger)
        caplog.set_level(logging.INFO)

        # instantiate instance model
        instModel = instancemodel.InstanceModel(iid=111, dbh=dbh)
        instModel.set_param("slot_nr", 1)
        instModel.insert_in_firewall_summary_table = MagicMock()
        res = instModel.insert_last_successfull_job_info(dbh, "111")
        assert res == True
        assert "insert_last_successfull_job_info: no successfull job present for the parent:" in caplog.text
        assert "insert_last_successfull_job_info: panorama job id present in xxx_cfgserv_panorama_cfg_request" in caplog.text

    '''
        This function tests the scenario where no job is present for the parent
        '''

    def test_insert_last_successfull_job_info_no_job_present_for_parent(self, caplog):
        logger = logging.getLogger('testlogger')

        class Mock_DBHandler_insert_last_successfull_job_info():
            def __init__(self, logger):
                self.logger = logger

            def get_cursor(self):
                return Mock_cursor_insert_last_successfull_job_info()

            def cursorclose(self, cursor):
                return

        class Mock_cursor_insert_last_successfull_job_info():
            def __init__(self):
                self.sql = None
                self.params = None

            def execute(self, sql, params):
                self.sql = sql
                self.params = params

            def fetchone(self):
                if "fw_job_status='Success' and panorama_job_id <> 0 order by id desc LIMIT 1" in self.sql:
                    return ()
                elif "fw_job_status='Success'" not in self.sql:
                    return ()
                elif "xxx_cfgserv_panorama_cfg_request where panorama_job_id =" in self.sql:
                    return (66,)
                else:
                    return None

        dbh = Mock_DBHandler_insert_last_successfull_job_info(logger)
        caplog.set_level(logging.INFO)

        # instantiate instance model
        instModel = instancemodel.InstanceModel(iid=111, dbh=dbh)
        instModel.set_param("slot_nr", 1)
        instModel.insert_in_firewall_summary_table = MagicMock()
        res = instModel.insert_last_successfull_job_info(dbh, "111")
        assert res == False
        assert "insert_last_successfull_job_info: no successfull job present for the parent:" in caplog.text
        assert "insert_last_successfull_job_info: did not find any job for the parent" in caplog.text

    '''
    Function to test the scenario where latest success job is present in xxx_cfgserv_firewall_summary table for the parent
    but the job is not present in xxx_cfgserv_panorama_cfg_request
    '''

    def test_insert_last_successfull_job_info_success_job_present_for_parent_but_emtpty_in_cfgserv_table(self,
                                                                                                         caplog):
        logger = logging.getLogger('testlogger')

        class Mock_DBHandler_insert_last_successfull_job_info():
            def __init__(self, logger):
                self.logger = logger

            def get_cursor(self):
                return Mock_cursor_insert_last_successfull_job_info()

            def cursorclose(self, cursor):
                return

        class Mock_cursor_insert_last_successfull_job_info():
            def __init__(self):
                self.sql = None
                self.params = None

            def execute(self, sql, params):
                self.sql = sql
                self.params = params

            def fetchone(self):
                if "fw_job_status='Success' and panorama_job_id <> 0 order by id desc LIMIT 1" in self.sql:
                    return (66, 2964855483838833152, "GPGW", 826239852)
                elif "xxx_cfgserv_panorama_cfg_request where panorama_job_id =" in self.sql:
                    return ()
                elif "(job_result = 'Init' or job_result = 'Success')  and tenant_id=" in self.sql:
                    return tuple([73, ])
                else:
                    return None

        dbh = Mock_DBHandler_insert_last_successfull_job_info(logger)
        caplog.set_level(logging.INFO)

        # instantiate instance model
        instModel = instancemodel.InstanceModel(iid=111, dbh=dbh)
        instModel.set_param("slot_nr", 1)
        instModel.insert_in_firewall_summary_table = MagicMock()
        res = instModel.insert_last_successfull_job_info(dbh, "111")
        assert res == True
        assert "insert_last_successfull_job_info: latest success job present for the parent" in caplog.text
        assert "insert_last_successfull_job_info: panorama job id not present in xxx_cfgserv_panorama_cfg_request" in caplog.text
        assert "insert_last_successfull_job_info: updating panorama jobs id : 73" in caplog.text
        assert "Inserted panorama_job_id 73" in caplog.text

    '''
        Function to test the scenario where latest success job is present in xxx_cfgserv_firewall_summary table for the parent
        but service type is not present in the map
    '''

    def test_insert_last_successfull_job_info_success_job_present_for_parent_but_invalid_service_type(self, caplog):
        logger = logging.getLogger('testlogger')

        class Mock_DBHandler_insert_last_successfull_job_info():
            def __init__(self, logger):
                self.logger = logger

            def get_cursor(self):
                return Mock_cursor_insert_last_successfull_job_info()

            def cursorclose(self, cursor):
                return

        class Mock_cursor_insert_last_successfull_job_info():
            def __init__(self):
                self.sql = None
                self.params = None

            def execute(self, sql, params):
                self.sql = sql
                self.params = params

            def fetchone(self):
                if "fw_job_status='Success' and panorama_job_id <> 0 order by id desc LIMIT 1" in self.sql:
                    return (66, 2964855483838833152, "test", 826239852)
                elif "xxx_cfgserv_panorama_cfg_request where panorama_job_id =" in self.sql:
                    return ()
                elif "(job_result = 'Init' or job_result = 'Success')  and tenant_id=" in self.sql:
                    return tuple([73, ])
                else:
                    return None

        dbh = Mock_DBHandler_insert_last_successfull_job_info(logger)
        caplog.set_level(logging.INFO)

        # instantiate instance model
        instModel = instancemodel.InstanceModel(iid=111, dbh=dbh)
        instModel.set_param("slot_nr", 1)
        instModel.insert_in_firewall_summary_table = MagicMock()
        res = instModel.insert_last_successfull_job_info(dbh, "111")
        assert res == False
        assert "insert_last_successfull_job_info: latest success job present for the parent" in caplog.text
        assert "insert_last_successfull_job_info: panorama job id not present in xxx_cfgserv_panorama_cfg_request" in caplog.text
        assert "not present in the map mapping_for_xxx_cfgserv_panorama_cfg_request_table" in caplog.text

    '''
           Function to test the scenario where latest success job is present in xxx_cfgserv_firewall_summary table for the parent
           but there is no init/success job in xxx_cfgserv_panorama_cfg_request_table
       '''

    def test_insert_last_successfull_job_info_success_job_present_for_parent_but_no_entry_in_cfgserv(self, caplog):
        logger = logging.getLogger('testlogger')

        class Mock_DBHandler_insert_last_successfull_job_info():
            def __init__(self, logger):
                self.logger = logger

            def get_cursor(self):
                return Mock_cursor_insert_last_successfull_job_info()

            def cursorclose(self, cursor):
                return

        class Mock_cursor_insert_last_successfull_job_info():
            def __init__(self):
                self.sql = None
                self.params = None

            def execute(self, sql, params):
                self.sql = sql
                self.params = params

            def fetchone(self):
                if "fw_job_status='Success' and panorama_job_id <> 0 order by id desc LIMIT 1" in self.sql:
                    return (66, 2964855483838833152, "GPGW", 826239852)
                elif "xxx_cfgserv_panorama_cfg_request where panorama_job_id =" in self.sql:
                    return ()
                elif "(job_result = 'Init' or job_result = 'Success')  and tenant_id=" in self.sql:
                    return ()
                else:
                    return None

        dbh = Mock_DBHandler_insert_last_successfull_job_info(logger)
        caplog.set_level(logging.INFO)

        # instantiate instance model
        instModel = instancemodel.InstanceModel(iid=111, dbh=dbh)
        instModel.set_param("slot_nr", 1)
        instModel.insert_in_firewall_summary_table = MagicMock()
        res = instModel.insert_last_successfull_job_info(dbh, "111")
        assert res == False
        assert "insert_last_successfull_job_info: latest success job present for the parent" in caplog.text
        assert "insert_last_successfull_job_info: panorama job id not present in xxx_cfgserv_panorama_cfg_request" in caplog.text
        assert "cannot find latest success/init entry in xxx_cfgserv_panorama_cfg_request table for" in caplog.text

class TestGetInstanceIds(unittest.TestCase):
    @patch('libs.model.instancemodel.execute_orch_query')
    def test_valid_result(self, mock_execute_query):
        # Set up mock to return empty result
        mock_execute_query.return_value = (True, [('123',), ('456',), ('789',)])
        dbh = MagicMock()
        dbh.logger = Logger()
        tenant_id = 'tenant1'
        region_name = 'region1'
        node_type = 'type1'

        # Call function
        success, instance_ids = get_instance_ids_by_tenant_id_and_aggregate_region_name(dbh, tenant_id, region_name, node_type)
        print(success)
        print(instance_ids)
        # Assert empty list returned
        self.assertEqual(instance_ids, ["123", "456", "789"])

    # existing test
    @patch('libs.model.instancemodel.execute_orch_query')
    def test_empty_result(self, mock_execute_query):
        # Set up mock to return empty result
        mock_execute_query.return_value = (True, [])
        dbh = MagicMock()
        tenant_id = 'tenant1'
        region_name = 'region1'
        node_type = 'type1'

        # Call function
        success, instance_ids = get_instance_ids_by_tenant_id_and_aggregate_region_name(dbh, tenant_id, region_name, node_type)

        # Assert empty list returned
        self.assertEqual(instance_ids, [])

    @patch('libs.model.instancemodel.execute_orch_query')
    def test_query_failed(self, mock_execute_query):
        # Set up mock to return query failed
        mock_execute_query.return_value = (False, [])
        dbh = MagicMock()
        tenant_id = 'tenant1'
        region_name = 'region1'
        node_type = 'type1'

        success, instance_ids = get_instance_ids_by_tenant_id_and_aggregate_region_name(dbh, tenant_id, region_name, node_type)
        self.assertEqual(success, False)
        self.assertEqual(instance_ids, None)


@patch('libs.model.instancemodel.execute_orch_query')
def test_find_instances_success(mock_execute_query):
    mock_execute_query.return_value = (True, [(1, 'instance1'), (2, 'instance2')])

    dbh = MagicMock()
    dbh.logger = Logger()
    custid = 'cust1'
    node_type = 'type1'

    success, instance_ids = find_instances_by_custid_and_node_type(dbh, custid, node_type)

    assert success
    assert instance_ids == [1, 2]

@patch('libs.model.instancemodel.execute_orch_query')
def test_find_instances_failure(mock_execute_query):
    mock_execute_query.return_value = (False, 'Error')

    dbh = MagicMock()
    dbh.logger = Logger()
    custid = 'cust1'
    node_type = 'type1'

    success, instance_ids = find_instances_by_custid_and_node_type(dbh, custid, node_type)

    assert not success
    assert instance_ids == []


@patch('libs.model.instancemodel.execute_orch_query')
def test_is_nat_dummy_upgrade_ongoing_in_region_failure(mock_execute_query):
    mock_execute_query.return_value = (False, 'Error')

    dbh = MagicMock()
    dbh.logger = Logger()
    custid = 'cust1'
    compute_region_idx = 214

    is_ongoing = is_nat_dummy_upgrade_ongoing_in_region(dbh, custid, compute_region_idx)

    assert not is_ongoing

@patch('libs.model.instancemodel.execute_orch_query')
def test_is_nat_dummy_upgrade_ongoing_in_region_success(mock_execute_query):
    mock_execute_query.return_value = (True, [("id",), ("id2",)])

    dbh = MagicMock()
    dbh.logger = Logger()
    custid = 'cust1'
    compute_region_idx = 214

    is_ongoing = is_nat_dummy_upgrade_ongoing_in_region(dbh, custid, compute_region_idx)

    assert is_ongoing

@patch('libs.model.instancemodel.execute_orch_query')
def test_find_standalone_instances_by_custid_and_node_type_and_region_id(mock_execute_query):
    mock_execute_query.return_value = (True, [(1, 'instance1'), (2, 'instance2')])

    dbh = MagicMock()
    dbh.logger = Logger()
    custid = 'cust1'
    node_type = 'type1'

    success, instance_ids = find_standalone_instances_by_custid_and_node_type_and_region_id(dbh, custid, node_type, 1234)

    assert success
    assert instance_ids == [1, 2]

class TestInstanceModelPrivRegionFQDNCleanup(unittest.TestCase):
    @patch('libs.model.instancemodel.get_portal_base')
    def test_cleanup_fqdn_private_region_prismaaccess(self, mock_get_portal_base):
        # Mock DBH and its attributes
        mock_dbh = MagicMock()
        mock_dbh.logger = Logger()
        mock_dbh.r53_ctx_prisma = MagicMock()
        mock_dbh.r53_ctx = MagicMock()
        instance = instancemodel.InstanceModel(iid=0, dbh=mock_dbh)
        instance.get_param = MagicMock(side_effect=lambda key: {
            "acct_id": "12345",
            "node_fqdn": "node.example.com",
            "public_ip": "*******",
            "healthcheck_id": "abc-123"
        }.get(key))
        instance.set_avisar_ctx = MagicMock()

        mock_get_portal_base.return_value = "something.prismaaccess.example.com"

        # Call method
        instance.cleanup_fqdn_private_region(mock_dbh, is_ipv6=True)

        # Asserts
        mock_get_portal_base.assert_called_once_with("12345", mock_dbh.logger)
        instance.set_avisar_ctx.assert_called_once_with(mock_dbh, mock_dbh.avctx)

        mock_dbh.r53_ctx_prisma.deleteNodeFQDNPrivateRegion.assert_called_once_with("node.example.com", True)
        mock_dbh.r53_ctx_prisma.deleteHealthCheck.assert_called_once_with("abc-123")

        # r53_ctx (non-prisma) should NOT be called
        mock_dbh.r53_ctx.deleteNodeFQDNPrivateRegion.assert_not_called()

    @patch('libs.model.instancemodel.get_portal_base')
    def test_cleanup_fqdn_private_region_no_node_fqdn(self, mock_get_portal_base):
        # Mock DBH and its attributes
        mock_dbh = MagicMock()
        mock_dbh.logger = Logger()
        mock_dbh.r53_ctx_prisma = MagicMock()
        mock_dbh.r53_ctx = MagicMock()
        instance = instancemodel.InstanceModel(iid=0, dbh=mock_dbh)
        instance.get_param = MagicMock(side_effect=lambda key: {
            "acct_id": "12345",
            "node_fqdn": "",
            "public_ip": "",
            "healthcheck_id": "abc-123"
        }.get(key))
        instance.set_avisar_ctx = MagicMock()

        mock_get_portal_base.return_value = "normal.example.com"

        # Call method
        instance.cleanup_fqdn_private_region(mock_dbh)

        # Nothing should be deleted
        mock_dbh.r53_ctx.deleteNodeFQDNPrivateRegion.assert_not_called()
        mock_dbh.r53_ctx.deleteHealthCheck.assert_not_called()
