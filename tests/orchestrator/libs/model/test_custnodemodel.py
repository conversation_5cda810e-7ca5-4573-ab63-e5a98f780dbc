import mock_imports_orchestrator
import json
from unittest.mock import MagicMock, patch
import pytest
import logging
from libs.model.custnodemodel import CustNodeModel
from libs.common.shared.sys_utils import PROVIDER_GCP

class Mock_cursor():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        return (11629,"SC-labreabakery168-491490908-uzbekistan-0-792", 51, -1 ,164, 1, 381, 381, 10, 2, 0, 0, 0, 0 , 0, "SERVICECONNECTION",1 ,0 ,"NULL" ,"NULL", "0848" , 10849 , "NULL", 0 , "NULL", "manual", "disabled", "NULL", "NULL", "NULL", 0, "NULL", "NULL", 0, 0, "NULL", "eyJ1dWlkIjogImFkMzliNDc1LWJjMzgtNGJkMS1iN2IzLTJiNjJiMDRiMWVlOCIsICJsZXZlbCI6IDEsICJjYWxsX3NlcSI6IDM3LCAicGlkIjogIjBjIiwgInBhdGgiOiAiMGMzN2MifQ==", "Successfully created required instances", 'ipsec' ,'{}',0 ,   23 , "PA-CAP550" , "gpcs-t3a-2xlarge-e2", "PA-VM-SaaS-10.2.5-c597.saas")


class Mock_DBHandler():
    def __init__(self, logger):
        self.logger = logger

    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return

    def r53_ctx_prisma(self):
        return MagicMock()

    def r53_ctx(self):
        return MagicMock()

class Logger:
    def __init__(self):
        pass

    def info(self, msg):
        print(msg)

    def error(self, msg):
        print(msg)

def Cursor():
    def __init__(self):
        return self
    def fetch_one(self):
        return "1345"
    def execute(sql, params):
        return


class TestCustNodeModel_GetCapType:
    @patch('libs.model.custnodemodel.CustomerModel')
    @patch('libs.model.custnodemodel.gpcs_get_cloud_type_from_region_idx')
    @patch('libs.model.custnodemodel.get_capacity_type')
    @patch('libs.model.custnodemodel.get_cloud_machine_type_with_region_idx')
    @patch('libs.model.custnodemodel.get_colo_sc_capacity_type')
    def test_init(self,
                  mock_get_colo_sc_capacity,
                  mock_get_cloud_machine,
                  mock_get_capacity,
                  mock_gpcs_get_cloud_type_from_region_idx,
                  mock_custmodel):

        mock_cursor1 = MagicMock()
        mock_cursor1.fetchone.return_value = \
            (
                1,  # id
                'Node 1',  # name
                'type',  # node_type
                'alt_type',  # alt_node_type
                123,  # custid
                0,  # is_hq
                'region',  # region
                'old_region',  # old_region
                'theater',  # theater
                0,  # num_allocs
                0,  # is_dynamic_node
                0,  # is_deleted
                'jobid',  # jobid
                'qos',  # curr_qos
                'status',  # status
                'license',  # license_type
                0,  # is_pa_connector_managed
                0,  # is_clean_pipe
                0,  # parent_id
                'domain',  # avail_domain
                1,  # instance1_id
                2,  # instance2_id
                'fqdn',  # service_fqdn
                1,  # okyo_edge_site_id
                '{}',  # okyo_data
                'ipsec',  # ipsec_connectivity
                'inbound',  # inbound_access
                'md5',  # inbound_access_md5
                '{}',  # inbound_access_ip_mapping
                '[]',  # inbound_access_ip_list
                0,  # inbound_access_max_ip
                None,  # spn_name
                None,  # old_spn_name
                0,  # sase_fabric
                0,  # is_commit_validate
                '{}',  # commit_validate_data
                'tag',  # rastro_log_tag
                'msg',  # status_msg
                'transport',  # transport_type
                '{}',  # colo_connection_info
                'qos',  # qos
                'machine',  # machine_type
                'capacity',  # capacity_type
                'size',  # gpcs_instance_size
                'version' # version
            )
        mock_cursor2 = MagicMock()
        mock_cursor2.fetchone.return_value = ('foo', '{}')
        # Mock DB handler
        dbh = MagicMock()
        dbh.logger = Logger()
        dbh.get_cursor.side_effect = [mock_cursor1, mock_cursor2]
        mock_custmodel = MagicMock()
        mock_gpcs_get_cloud_type_from_region_idx.return_value = PROVIDER_GCP
        # Mock capacity type lookup
        mock_get_capacity.return_value = ('market', 'gpcs_size', 'dp_gpcs_size',
                                          'capacity', 0, 'ha_mode')

        # Mock cloud machine type lookup
        mock_get_cloud_machine.return_value = 'cloud_machine'

        # Mock colo SC capacity lookup
        mock_get_colo_sc_capacity.return_value = ('gpcs_size', 0, 'capacity')

        # Call __init__
        model = CustNodeModel(iid=1, dbh=dbh, compute_region_id=1234)

        # The below assert will test that get_capacity was called with region as 1234, which is the provided
        # compute region
        mock_get_capacity.assert_called_with(dbh, 123, 1234, 'type',
                                             spn_name=None, version='version')

        # Assert model attributes set correctly
        assert model.market_type == 'market'
        assert model.gpcs_instance_size == 'gpcs_size'
        assert model.capacity_type == 'capacity'
