from unittest.mock import MagicMock, patch
import unittest

from libs.model.vpcmodel import VpcModel

class TestGetZoneNameAndVpcType(unittest.TestCase):

    @patch('libs.model.vpcmodel.execute_orch_query')
    def test_get_zone_name_and_vpc_type_success(self, mock_execute_query):
        # Arrange
        mock_dbh = MagicMock()
        mock_execute_query.return_value = (True, ('1', 'zone1', 'vpc1'))

        # Act
        vpcmodel = VpcModel()
        success, zone_name, vpctype = vpcmodel.get_zone_name_and_vpc_type_from_vpc_id(
            mock_dbh, 'vpc123', 'ha1')

        # Assert
        self.assertTrue(success)
        self.assertEqual(zone_name, 'zone1')
        self.assertEqual(vpctype, 'vpc1')

    @patch('libs.model.vpcmodel.execute_orch_query')
    def test_get_zone_name_and_vpc_type_failure(self, mock_execute_query):
        # Arrange
        mock_dbh = MagicMock()
        mock_execute_query.return_value = (False, None)

        # Act
        vpcmodel = VpcModel()
        success, zone_name, vpctype = vpcmodel.get_zone_name_and_vpc_type_from_vpc_id(
            mock_dbh, 'vpc123', 'ha1')

        # Assert
        self.assertFalse(success)
        self.assertIsNone(zone_name)
        self.assertIsNone(vpctype)
