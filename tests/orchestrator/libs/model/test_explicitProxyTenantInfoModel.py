import json
from unittest.mock import MagicMock, patch
import pytest
import logging
import mock_imports_orchestrator
from libs.model.explicitProxyTenantInfoModel import \
    ExplicitProxyTenantInfoModel, is_enable_rn_to_ep_ctx_pass


class Mock_DBHandler():
    def __init__(self, logger):
        self.logger = logger

    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return


class Mock_CustModel():
    def __init__(self, id = 0, acct_id = 0):
        self.fields = ["super_acct_id", "acct_id", "id", "gpname", "probe_enable", "name",
                       "psk_secret", "is_readonly", "url", "gpfqdn", "eproxy_image_version", "project_id",
                       "asymmetric_ha_mode", "binhp_eproxy_image_version", "uda_nhproxy_image_version"]
        self.values = [acct_id, acct_id, id, "test", 0, "test_customer", "dummy", 0, "dummyUR<PERSON>", "dummyFQD<PERSON>",
                       None, "dummy", "dummy-proj", 1, None, None]

    def get_param(self, field):
        print(self.fields)
        myidx = self.fields.index(field)
        return self.values[myidx]

    def set_param(self, field, value):
        if field in self.fields:
            myidx = self.fields.index(field)
            self.values[myidx] = value
        else:
            raise Exception(f"field {field} does not exist")

    def __str__(self):
        return "dummy"
    def save(self):
        return

class TestExplicitProxyTenantInfoModel:

    @patch('libs.model.explicitProxyTenantInfoModel.CustomerModel', return_value=Mock_CustModel(123, "123456"))
    @patch('libs.model.explicitProxyTenantInfoModel.ExplicitProxyTenantInfoModel.get_entry', return_value=True)
    @patch('libs.model.explicitProxyTenantInfoModel.ExplicitProxyTenantInfoModel.get_param', return_value=1)
    def test_is_enable_rn_to_ep_ctx_pass_success(self, mock_cm, mock_get_entry, mock_get_param, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)

        ret = is_enable_rn_to_ep_ctx_pass(dbh, 123)
        assert ret == 1


    @patch('libs.model.explicitProxyTenantInfoModel.CustomerModel', return_value=Mock_CustModel())
    @patch('libs.model.explicitProxyTenantInfoModel.ExplicitProxyTenantInfoModel.get_entry', return_value=True)
    @patch('libs.model.explicitProxyTenantInfoModel.ExplicitProxyTenantInfoModel.get_param', return_value=1)
    def test_is_enable_rn_to_ep_ctx_pass_failure_case1(self, mock_cm, mock_get_entry, mock_get_param, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)

        ret = is_enable_rn_to_ep_ctx_pass(dbh, 123)
        assert ret == 0
        assert "is_enable_rn_to_ep_ctx_pass: Failed to retrieve cust_master model for custid: 123" in caplog.text


    @patch('libs.model.explicitProxyTenantInfoModel.CustomerModel', return_value=Mock_CustModel(123, "123456"))
    @patch('libs.model.explicitProxyTenantInfoModel.ExplicitProxyTenantInfoModel.get_entry', return_value=False)
    @patch('libs.model.explicitProxyTenantInfoModel.ExplicitProxyTenantInfoModel.get_param', return_value=1)
    def test_is_enable_rn_to_ep_ctx_pass_failure_case1(self, mock_cm, mock_get_entry, mock_get_param, caplog):
        logger = logging.getLogger('testlogger')

        dbh = Mock_DBHandler(logger)
        caplog.set_level(logging.INFO)

        ret = is_enable_rn_to_ep_ctx_pass(dbh, 123)
        assert ret == 0
        assert "is_enable_rn_to_ep_ctx_pass: Failed to retrieve explicit proxy tenant info for tenant_id 123456" in caplog.text
