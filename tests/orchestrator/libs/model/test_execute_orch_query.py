import unittest
import mysql.connector
from unittest import mock
from unittest.mock import patch
from libs.model.execute_orch_query import execute_orch_query

class TestExecuteOrchQuery(unittest.TestCase):

    def setUp(self):
        self.dbh_mock = mock.MagicMock()
        self.sql = "UPDATE users SET name = %s where id=137"
        self.params = ("Tejas",)
        self.opertype = "update"

    def print_me(self, msg):
        print(msg)

    # Test where connection to the database fails.
    @patch("libs.model.execute_orch_query.MAX_RETRY", 3)
    def test_execute_orch_query_retry_on_read_only_error(self):
        err_no = 1290
        self.dbh_mock.logger.error.side_effect = self.print_me
        self.dbh_mock.conn.cursor.side_effect = [mock.MagicMock(), mock.MagicMock()]
        self.dbh_mock.conn.cursor.execute.side_effect = [mysql.connector.Error(errno=err_no, sqlstate="Read only"),
                                                         None]
        self.dbh_mock.conn.cursor.lastrowid = 137
        self.dbh_mock.connect.return_value = self.dbh_mock.conn
        self.dbh_mock.get_cursor.return_value = self.dbh_mock.conn.cursor
        ret, res = execute_orch_query(self.dbh_mock, self.sql, self.params, self.opertype)
        self.assertEqual(ret, True)
        self.assertEqual(res, 137)
        self.assertEqual(self.dbh_mock.connect.call_count, 1)


    @patch("libs.model.execute_orch_query.MAX_RETRY", 3)
    def test_execute_orch_query_exceed_max_retry(self):
        err_no = 1290
        self.dbh_mock.conn.cursor.side_effect = [mock.MagicMock(), mock.MagicMock(), mock.MagicMock(), mock.MagicMock()]
        self.dbh_mock.conn.cursor.execute.side_effect = [mysql.connector.Error(errno=err_no, sqlstate="Read only"),
                                                        mysql.connector.Error(errno=err_no, sqlstate="Read only"),
                                                        mysql.connector.Error(errno=err_no, sqlstate="Read only"),
                                                        mysql.connector.Error(errno=err_no, sqlstate="Read only")]
        self.dbh_mock.logger.error.side_effect = self.print_me
        self.dbh_mock.connect.return_value = self.dbh_mock.conn
        self.dbh_mock.get_cursor.return_value = self.dbh_mock.conn.cursor
        ret, res = execute_orch_query(self.dbh_mock, self.sql, self.params, self.opertype)
        self.assertEqual(ret, False)
        self.assertEqual(res, None)
        self.assertEqual(self.dbh_mock.connect.call_count, 3)

    @patch("libs.model.execute_orch_query.MAX_RETRY", 3)
    def test_execute_orch_query_other_mysql_exception(self):
        self.dbh_mock.logger.error.side_effect = self.print_me
        err_no = 555
        self.dbh_mock.conn.cursor.side_effect = [mock.MagicMock(), mock.MagicMock(), mock.MagicMock()]
        self.dbh_mock.conn.cursor.execute.side_effect = [mysql.connector.Error(errno=err_no, sqlstate="Unknown error"), None]
        self.dbh_mock.connect.return_value = self.dbh_mock.conn
        self.dbh_mock.get_cursor.return_value = self.dbh_mock.conn.cursor
        ret, res = execute_orch_query(self.dbh_mock, self.sql, self.params, self.opertype)
        self.assertEqual(ret, False)
        self.assertEqual(res, None)
        self.assertEqual(self.dbh_mock.connect.call_count, 0)

    @patch("libs.model.execute_orch_query.MAX_RETRY", 3)
    def test_execute_orch_query_other_exception(self):
        self.dbh_mock.logger.error.side_effect = self.print_me
        self.dbh_mock.conn.cursor.side_effect = [mock.MagicMock()]
        self.dbh_mock.conn.cursor.execute.side_effect = [OSError("Testing Unknown Exception")]
        self.dbh_mock.connect.return_value = self.dbh_mock.conn
        self.dbh_mock.get_cursor.return_value = self.dbh_mock.conn.cursor
        ret, res = execute_orch_query(self.dbh_mock, self.sql, self.params, self.opertype)
        self.assertEqual(ret, False)
        self.assertEqual(res, None)
        self.assertEqual(self.dbh_mock.connect.call_count, 0)
