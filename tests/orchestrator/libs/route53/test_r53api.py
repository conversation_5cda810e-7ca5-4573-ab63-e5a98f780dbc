import unittest
from unittest.mock import patch, MagicMock

from libs.route53.r53api import PanR53Context

class Logger():
    def info(self, msg):
        print(msg)

    def error(self, msg):
        print(msg)


class TestPanR53Context(unittest.TestCase):
    @patch('libs.route53.r53api.PanR53Context._PanR53Context__get_alias_rrs')
    @patch('libs.route53.r53api.PanR53Context._PanR53Context__deleteFQDN')
    @patch('libs.route53.r53api.PanR53Context._PanR53Context__get_gp_region_mapping')
    @patch('libs.route53.r53api.PanR53Context._PanR53Context__get_fqdn_record')
    @patch('libs.route53.r53api.PanR53Context.r53_get_zone_id')
    @patch('boto3.client')
    def test_deleteNodeFQDN_ipv6(self, mock_boto3_client,
                                 mock_r53_get_zone_id,
                                 mock_get_fqdn_record,
                                 mock_get_gp_region_mapping,
                                 mock_deleteFQDN,
                                 mock_get_alias_rrs) :

        mock_r53_get_zone_id = MagicMock()
        mock_get_gp_region_mapping = MagicMock()
        mock_init_r53_context = MagicMock()

        mock_gpcs_base = 'test_gpcs_base'
        mock_r53_account = 'test_account'
        mock_logger = Logger()
        mock_db_h = MagicMock()
        mock_boto3_client.return_value = MagicMock()
        mock_db_h.logger = Logger()
        mock_r53_context = MagicMock()
        mock_init_r53_context.return_value = mock_r53_context

        r53_context = PanR53Context(mock_gpcs_base, mock_r53_account,
                                    mock_logger, mock_db_h)

        # Rest of test code...
        mock_r53_context = MagicMock()
        mock_init_r53_context.return_value = mock_r53_context

        node_fqdn = 'test.example.com'
        vmid = 'vmid1'
        public_ip = '2001:db8::1'
        region = 'us-west-2'

        mock_fqdn_records = [
            {'SetIdentifier': 'vmid2'},
            {'SetIdentifier': public_ip + "-" + vmid},
        ]

        mock_get_fqdn_record.return_value = mock_fqdn_records

        mock_alias_records = [
            {'AliasTarget': {'DNSName': node_fqdn},
             'Type' : 'AAAA'
             },
        ]
        mock_get_alias_rrs.return_value = mock_alias_records

        r53_context.deleteNodeFQDN(node_fqdn, vmid, public_ip, region, False, True)

        mock_get_fqdn_record.assert_called_with(node_fqdn)
        mock_get_alias_rrs.assert_called_with(node_fqdn, region)

        # Only the AAAA record matching public_ip should be deleted
        mock_deleteFQDN.assert_called_with([mock_fqdn_records[1], mock_alias_records[0]])

class TestDeleteNodeFQDNPrivateRegion(unittest.TestCase):

    @patch('libs.route53.r53api.PanR53Context._PanR53Context__deleteFQDN')  # name-mangled private method
    @patch('libs.route53.r53api.PanR53Context._PanR53Context__get_fqdn_record')
    @patch('libs.route53.r53api.PanR53Context._PanR53Context__get_gp_region_mapping')
    @patch('libs.route53.r53api.PanR53Context.r53_get_zone_id')
    @patch('boto3.client')
    def test_delete_node_fqdn_private_region(self, mock_boto3_client, mock_r53_get_zone_id, mock_get_gp_region_mapping, mock_get_fqdn_record, mock_delete_fqdn):
        mock_gpcs_base = 'test_gpcs_base'
        mock_r53_account = 'test_account'
        mock_get_gp_region_mapping = MagicMock()
        mock_logger = Logger()
        mock_db_h = MagicMock()
        mock_db_h.logger = Logger()
        mock_r53_context = MagicMock()
        mock_r53_get_zone_id = MagicMock()

        r53_context = PanR53Context(mock_gpcs_base, mock_r53_account,
                                    mock_logger, mock_db_h)

        # Setup: mock return of __get_fqdn_record
        fqdn_records = [
            {'SetIdentifier': 'sase-private-region', 'Name': 'example1'},
            {'SetIdentifier': 'other-region', 'Name': 'example2'},
        ]
        mock_get_fqdn_record.return_value = fqdn_records
        r53_context.deleteNodeFQDNPrivateRegion('test-node.example.com')
        # __deleteFQDN should be called with the record that matched
        mock_delete_fqdn.assert_called_once_with([fqdn_records[0]])
