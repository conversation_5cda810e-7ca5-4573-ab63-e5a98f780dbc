import mock_imports_gcp_cust_prov
from utils.gcp_cust_provisioning.cust_provision_script import *


def test_cust_provision_global_settings():
    #assert 1==1
    assert CUSTOMER_PROVISIONING_SERVICE_PROVISIONING_LAMBDA_SETTING == 'customer_provisioning_service_provisioning_lambda'
    assert CUSTOMER_PROVISIONING_SERVICE_DEPROVISIONING_LAMBDA_SETTING == 'customer_provisioning_service_deprovisioning_lambda'
    assert CUSTOMER_PROVISIONING_SERVICE_PROVISIONING_LAMBDA_V1 == 'cust_provisioning'
    assert CUSTOMER_PROVISIONING_SERVICE_DEPROVISIONING_LAMBDA_V1 == 'cust_deprovisioning'