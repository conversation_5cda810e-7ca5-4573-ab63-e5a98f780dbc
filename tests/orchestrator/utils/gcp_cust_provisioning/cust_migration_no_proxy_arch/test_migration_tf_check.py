#!/usr/bin/env python

import unittest
from unittest.mock import Mock, patch, MagicMock, call
import sys
import os
import pytest
from enum import IntEnum

# Add the migration script directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', '..', 'src', 'apps', 'orchestrator', 'utils', 'gcp_cust_provisioning', 'cust_migration_no_proxy_arch'))

# Mock all the problematic imports before importing migration_project
sys.modules['jsonpickle'] = MagicMock()
sys.modules['google.oauth2'] = MagicMock()
sys.modules['rediscluster'] = MagicMock()
sys.modules['mysql'] = MagicMock()
sys.modules['mysql.connector'] = MagicMock()
sys.modules['common.shared.soft_upgrade.event_tracker'] = MagicMock()
sys.modules['os'] = MagicMock()
sys.modules['common.shared.soft_upgrade.update_validation_impl'] = MagicMock()
sys.modules['common.shared.soft_upgrade.soft_upgrade_util'] = MagicMock()
sys.modules['common.shared.soft_upgrade.custom_exception'] = MagicMock()
sys.modules['common.util'] = MagicMock()
sys.modules['common.shared.soft_upgrade.ami_plugin_mapping'] = MagicMock()
sys.modules['common.shared.gcp_utils'] = MagicMock()
sys.modules['logging_infra.logging_wrapper'] = MagicMock()
sys.modules['common.shared.utils'] = MagicMock()
sys.modules['apis.init_logger'] = MagicMock()
sys.modules['googleapiclient'] = MagicMock()
sys.modules['model.orchmodellambda'] = MagicMock()
sys.modules['upgrade_common.utils'] = MagicMock()
sys.modules['apis.region_master_lambda_api'] = MagicMock()
sys.modules['google.cloud.compute_v1'] = MagicMock()
sys.modules['google.cloud'] = MagicMock()
sys.modules['googleapiclient.discovery'] = MagicMock()
sys.modules['boto3'] = MagicMock()
sys.modules['migration_worker'] = MagicMock()
sys.modules['libs.cloud_providers.common.instance_trigger_update'] = MagicMock()
sys.modules['libs.db.dbhandle'] = MagicMock()
sys.modules['libs.common.shared.rastro.rastro_logging_wrapper'] = MagicMock()
sys.modules['libs.common.shared.gcp_utils'] = MagicMock()
sys.modules['libs.model.custmodel'] = MagicMock()
sys.modules['migrate_utils'] = MagicMock()
sys.modules['migrationhelper.trolle_gcp_utils'] = MagicMock()
sys.modules['libs.common.shared.dbconn'] = MagicMock()
sys.modules['libs.cfg.cfg'] = MagicMock()
sys.modules['libs.cfg'] = MagicMock()

class MockProxyStates(IntEnum):
    PROXY_UNINITIALIZED = -1
    NO_PROXY = 0
    PROXY_MIGRATION_IN_PROGRESS = 1

# Mock the dbconn module specifically
mock_dbconn = MagicMock()
sys.modules['libs.common.shared.dbconn'] = mock_dbconn
mock_gcp_utils = MagicMock()
mock_gcp_utils.ProxyStates = MockProxyStates

# Now import the functions we want to test
from migration_project import check_terraform_region_status

class TestTerraformSafetyCheck(unittest.TestCase):

    def setUp(self):
        self.mock_dbh = Mock()
        self.mock_logger = Mock()
        self.mock_dbh.logger = self.mock_logger

    @patch('migration_project.dbconn')
    def test_check_terraform_region_status_with_tf_regions(self, mock_dbconn):
        """Test detection of Terraform regions (BHP Group scenario)"""
        # Mock database response with TF regions
        mock_dbconn.execute_lambda_query.return_value = {
            'ok': True,
            'result': [
                (209, 'terraform_provision'),
                (222, 'terraform_provision'),  # ap-southeast-1
                (245, 'infrastructure_manager'),  # australia-south
                (425, 'terraform_migration'),   # chile
                (100, 'deployment_manager'),   # some DM region
            ]
        }

        has_tf, tf_regions, dm_regions = check_terraform_region_status(self.mock_dbh, 7411)

        self.assertTrue(has_tf)
        self.assertEqual(set(tf_regions), {209, 222, 245, 425})
        self.assertEqual(dm_regions, [100])

    @patch('migration_project.dbconn')
    def test_check_terraform_region_status_dm_only(self, mock_dbconn):
        """Test customer with only DM regions - should proceed normally"""
        mock_dbconn.execute_lambda_query.return_value = {
            'ok': True,
            'result': [
                (100, 'deployment_manager'),
                (101, 'deployment_manager'),
            ]
        }

        has_tf, tf_regions, dm_regions = check_terraform_region_status(self.mock_dbh, 7411)

        self.assertFalse(has_tf)
        self.assertEqual(tf_regions, [])
        self.assertEqual(dm_regions, [100, 101])

    @patch('migration_project.dbconn')
    def test_check_terraform_region_status_db_error(self, mock_dbconn):
        """Test handling of database errors"""
        mock_dbconn.execute_lambda_query.return_value = {
            'ok': False,
            'result': []
        }

        has_tf, tf_regions, dm_regions = check_terraform_region_status(self.mock_dbh, 7411)

        self.assertFalse(has_tf)
        self.assertEqual(tf_regions, [])
        self.assertEqual(dm_regions, [])

    @patch('migration_project.check_terraform_region_status')
    @patch('migration_project.get_project_id')
    @patch('migration_project.CustomerModel')
    @patch('migration_project.DbHandle')
    def test_terraform_safety_check_blocks_without_mtr_flag(self, mock_dbhandle_class, mock_customer_class, mock_get_project_id, mock_check):
        """Test that migration is blocked for TF regions without --migrate-tf-regions flag"""
        # Simulate BHP Group scenario - hybrid DM/TF regions
        mock_check.return_value = (True, [209, 222, 245, 425], [100])
        mock_get_project_id.return_value = 'test-project'

        mock_customer = Mock()
        mock_customer.get_param.return_value = 7411
        mock_customer_class.return_value = mock_customer

        mock_dbhandle = Mock()
        mock_dbhandle.logger = self.mock_logger
        mock_dbhandle_class.return_value = mock_dbhandle

        # Import here to avoid circular import issues
        from migration_project import MigrationProject

        # Mock boto3 to prevent AWS calls
        with patch('migration_project.boto3'):
            # This should raise RuntimeError
            with self.assertRaises(RuntimeError) as context:
                # Create a minimal instance that only runs the safety check
                mp = MigrationProject.__new__(MigrationProject)
                mp.logger = self.mock_logger
                mp.dbh = mock_dbhandle
                mp.migrate_tf_regions = False  # No -mtr flag
                mp.customer = mock_customer
                mp.region_id = None
                mp.node_types = ['48', '49']
                mp._perform_terraform_safety_check()

            error_msg = str(context.exception)
            self.assertIn("Migration aborted due to Terraform region safety check", error_msg)
            self.assertIn("Use --migrate-tf-regions", error_msg)

    @patch('migration_project.check_terraform_region_status')
    @patch('migration_project.get_project_id')
    @patch('migration_project.CustomerModel')
    @patch('migration_project.DbHandle')
    def test_terraform_safety_check_allows_with_mtr_flag(self, mock_dbhandle_class, mock_customer_class, mock_get_project_id, mock_check):
        """Test that migration proceeds for TF regions with --migrate-tf-regions flag"""
        # Simulate BHP Group scenario - hybrid DM/TF regions
        mock_check.return_value = (True, [209, 222, 245, 425], [100])
        mock_get_project_id.return_value = 'test-project'

        mock_customer = Mock()
        mock_customer.get_param.return_value = 7411
        mock_customer_class.return_value = mock_customer

        mock_dbhandle = Mock()
        mock_dbhandle.logger = self.mock_logger
        mock_dbhandle_class.return_value = mock_dbhandle

        # Import here to avoid circular import issues
        from migration_project import MigrationProject

        # Mock boto3 to prevent AWS calls
        with patch('migration_project.boto3'):
            # This should NOT raise an exception with -mtr flag
            try:
                # Create a minimal instance that only runs the safety check
                mp = MigrationProject.__new__(MigrationProject)
                mp.logger = self.mock_logger
                mp.dbh = mock_dbhandle
                mp.migrate_tf_regions = True  # -mtr flag provided
                mp.customer = mock_customer
                mp.region_id = None
                mp.node_types = ['48', '49']
                mp._perform_terraform_safety_check()

                # Should log warning but proceed
                self.mock_logger.warning.assert_called()

            except RuntimeError:
                self.fail("Safety check should not raise exception with --migrate-tf-regions flag")

    @patch('migration_project.check_terraform_region_status')
    @patch('migration_project.get_project_id')
    @patch('migration_project.CustomerModel')
    @patch('migration_project.DbHandle')
    def test_terraform_safety_check_passes_for_dm_only(self, mock_dbhandle_class, mock_customer_class, mock_get_project_id, mock_check):
        """Test that DM-only customers proceed without issues"""
        # Simulate DM-only customer
        mock_check.return_value = (False, [], [100, 101])
        mock_get_project_id.return_value = 'test-project'

        mock_customer = Mock()
        mock_customer.get_param.return_value = 1234
        mock_customer_class.return_value = mock_customer

        mock_dbhandle = Mock()
        mock_dbhandle.logger = self.mock_logger
        mock_dbhandle_class.return_value = mock_dbhandle

        # Import here to avoid circular import issues
        from migration_project import MigrationProject

        # Mock boto3 to prevent AWS calls
        with patch('migration_project.boto3'):
            # This should proceed normally
            try:
                # Create a minimal instance that only runs the safety check
                mp = MigrationProject.__new__(MigrationProject)
                mp.logger = self.mock_logger
                mp.dbh = mock_dbhandle
                mp.migrate_tf_regions = False  # No -mtr flag needed
                mp.customer = mock_customer
                mp.region_id = None
                mp.node_types = ['48', '49']
                mp._perform_terraform_safety_check()

                # Should log success message
                self.mock_logger.info.assert_called()

            except RuntimeError:
                self.fail("Safety check should pass for DM-only customers")

    @patch('migration_project.get_project_id')
    @patch('migration_project.CustomerModel')
    @patch('migration_project.DbHandle')
    def test_set_proxy_states_to_no_proxy(self, mock_dbhandle_class, mock_customer_class, mock_get_project_id):
        """Test setting proxy states to NO_PROXY based on node types"""
        mock_get_project_id.return_value = 'test-project'

        mock_customer = Mock()
        mock_customer.get_param.return_value = 7411
        mock_customer_class.return_value = mock_customer

        mock_dbhandle = Mock()
        mock_dbhandle.logger = self.mock_logger
        mock_dbhandle_class.return_value = mock_dbhandle

        # Import here to avoid circular import issues
        from migration_project import MigrationProject

        # Mock boto3 to prevent AWS calls
        with patch('migration_project.boto3'):
            with patch('migration_project.check_terraform_region_status') as mock_check:
                # Mock TF check to pass
                mock_check.return_value = (False, [], [100])

                # Patch ProxyStates in the migration_project module directly
                with patch('migration_project.ProxyStates', MockProxyStates):
                    # Create a minimal instance
                    mp = MigrationProject.__new__(MigrationProject)
                    mp.logger = self.mock_logger
                    mp.dbh = mock_dbhandle
                    mp.migrate_tf_regions = False
                    mp.customer = mock_customer
                    mp.region_id = None
                    mp.dry_run = False
                    mp.node_types = ['48', '49', '153']  # Mix of different proxy types

                    # Mock the safety check to pass
                    mp._perform_terraform_safety_check = Mock()

                    # Call the method
                    mp.set_proxy_states_to_no_proxy()

                    # Debug: Print actual calls
                    print("Actual calls:", mock_customer.set_param.call_args_list)

                    # Verify the correct proxy fields were set
                    expected_calls = [
                        call('rn_proxy_state', 0),      # Node type 48
                        call('global_proxy_state', 0),  # Node type 49
                        call('swgp_pvm_state', 0),      # Node type 153
                    ]

                    # Check that set_param was called for each expected field
                    actual_calls = mock_customer.set_param.call_args_list
                    for expected_call in expected_calls:
                        self.assertIn(expected_call, actual_calls)

                    # Verify save was called
                    mock_customer.save.assert_called_once()

    @patch('migration_project.get_project_id')
    @patch('migration_project.CustomerModel')
    @patch('migration_project.DbHandle')
    def test_set_proxy_states_dry_run(self, mock_dbhandle_class, mock_customer_class, mock_get_project_id):
        """Test dry run mode for setting proxy states"""
        mock_get_project_id.return_value = 'test-project'

        mock_customer = Mock()
        mock_customer.get_param.return_value = 7411
        mock_customer_class.return_value = mock_customer

        mock_dbhandle = Mock()
        mock_dbhandle.logger = self.mock_logger
        mock_dbhandle_class.return_value = mock_dbhandle

        from migration_project import MigrationProject

        with patch('migration_project.boto3'):
            with patch('migration_project.check_terraform_region_status') as mock_check:
                mock_check.return_value = (False, [], [100])

                mp = MigrationProject.__new__(MigrationProject)
                mp.logger = self.mock_logger
                mp.dbh = mock_dbhandle
                mp.migrate_tf_regions = False
                mp.customer = mock_customer
                mp.region_id = None
                mp.dry_run = True  # DRY RUN MODE
                mp.node_types = ['48']

                mp._perform_terraform_safety_check = Mock()

                mp.set_proxy_states_to_no_proxy()

                # In dry run, set_param and save should NOT be called
                mock_customer.set_param.assert_not_called()
                mock_customer.save.assert_not_called()

                # Should log dry run messages
                dry_run_logged = any(
                    "[DRY RUN]" in str(call)
                    for call in self.mock_logger.info.call_args_list
                )
                self.assertTrue(dry_run_logged, "Dry run logging not found")

    @patch('migration_project.get_project_id')
    @patch('migration_project.CustomerModel')
    @patch('migration_project.DbHandle')
    def test_set_proxy_states_unknown_node_type(self, mock_dbhandle_class, mock_customer_class, mock_get_project_id):
        """Test handling of unknown node types"""
        mock_get_project_id.return_value = 'test-project'

        mock_customer = Mock()
        mock_customer.get_param.return_value = 7411
        mock_customer_class.return_value = mock_customer

        mock_dbhandle = Mock()
        mock_dbhandle.logger = self.mock_logger
        mock_dbhandle_class.return_value = mock_dbhandle

        from migration_project import MigrationProject

        with patch('migration_project.boto3'):
            with patch('migration_project.check_terraform_region_status') as mock_check:
                mock_check.return_value = (False, [], [100])

                mp = MigrationProject.__new__(MigrationProject)
                mp.logger = self.mock_logger
                mp.dbh = mock_dbhandle
                mp.migrate_tf_regions = False
                mp.customer = mock_customer
                mp.region_id = None
                mp.dry_run = False
                mp.node_types = ['999']  # Unknown node type

                mp._perform_terraform_safety_check = Mock()

                mp.set_proxy_states_to_no_proxy()

                # Should log warning for unknown node type
                warning_logged = any(
                    "does not have a known proxy state mapping" in str(call)
                    for call in self.mock_logger.warning.call_args_list
                )
                self.assertTrue(warning_logged, "Warning for unknown node type not logged")

                # Should log that no proxy fields were identified
                no_fields_logged = any(
                    "No proxy state fields identified for update" in str(call)
                    for call in self.mock_logger.warning.call_args_list
                )
                self.assertTrue(no_fields_logged, "No proxy fields warning not logged")

                # No database operations should occur
                mock_customer.set_param.assert_not_called()
                mock_customer.save.assert_not_called()

    @patch('migration_project.get_project_id')
    @patch('migration_project.CustomerModel')
    @patch('migration_project.DbHandle')
    def test_set_proxy_states_no_node_types(self, mock_dbhandle_class, mock_customer_class, mock_get_project_id):
        """Test handling when no node types are specified"""
        mock_get_project_id.return_value = 'test-project'

        mock_customer = Mock()
        mock_customer.get_param.return_value = 7411
        mock_customer_class.return_value = mock_customer

        mock_dbhandle = Mock()
        mock_dbhandle.logger = self.mock_logger
        mock_dbhandle_class.return_value = mock_dbhandle

        from migration_project import MigrationProject

        with patch('migration_project.boto3'):
            with patch('migration_project.check_terraform_region_status') as mock_check:
                mock_check.return_value = (False, [], [100])

                mp = MigrationProject.__new__(MigrationProject)
                mp.logger = self.mock_logger
                mp.dbh = mock_dbhandle
                mp.migrate_tf_regions = False
                mp.customer = mock_customer
                mp.region_id = None
                mp.dry_run = False
                mp.node_types = None  # No node types

                mp._perform_terraform_safety_check = Mock()

                mp.set_proxy_states_to_no_proxy()

                # Should log that no node types are specified
                no_node_types_logged = any(
                    "No node types specified, skipping proxy state updates" in str(call)
                    for call in self.mock_logger.info.call_args_list
                )
                self.assertTrue(no_node_types_logged, "No node types message not logged")

                # No database operations should occur
                mock_customer.set_param.assert_not_called()
                mock_customer.save.assert_not_called()

if __name__ == '__main__':
    unittest.main()
