# __author__ = 'Ravi Bhavsar'
from unittest.mock import MagicMock
import sys
import os
import pytest

sys.modules['jsonpickle'] = MagicMock()
sys.modules['google.oauth2'] = MagicMock()
sys.modules['rediscluster'] = MagicMock()
sys.modules['mysql'] = MagicMock()
sys.modules['mysql.connector'] = MagicMock()
sys.modules['common.shared.soft_upgrade.event_tracker'] = MagicMock()
sys.modules['os'] = MagicMock()
sys.modules['common.shared.soft_upgrade.update_validation_impl'] = MagicMock()
sys.modules['common.shared.soft_upgrade.soft_upgrade_util'] = MagicMock()
sys.modules['common.shared.soft_upgrade.custom_exception'] = MagicMock()
sys.modules['common.util'] = MagicMock()
sys.modules['common.shared.soft_upgrade.ami_plugin_mapping'] = MagicMock()
# sys.modules['googleapiclient'] = MagicMock()
# sys.modules['model.orchmodellambda'] = MagicMock()
sys.modules['common.shared.gcp_utils'] = MagicMock()
sys.modules['logging_infra.logging_wrapper'] = MagicMock()
sys.modules['common.shared.utils'] = MagicMock()
sys.modules['apis.init_logger'] = MagicMock()
sys.modules['googleapiclient'] = MagicMock()
sys.modules['model.orchmodellambda'] = MagicMock()
sys.modules['upgrade_common.utils'] = MagicMock()
sys.modules['apis.region_master_lambda_api'] = MagicMock()