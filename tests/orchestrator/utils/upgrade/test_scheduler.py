import mock_imports_upgrade
import logging
import pytest
import unittest
from utils.upgrade.scheduler import Scheduler, check_if_env_use_swm_by_default
from utils.Exceptions import ValidationFailedException
from unittest.mock import patch, MagicMock


class TestScheduler(unittest.TestCase):

    @patch('utils.upgrade.scheduler.open')
    @patch('lib.lambda_func.db_access')
    def test_init(self,mock_db_access,mock_open):
        mock_db_access.return_value = {'next_start': 1, 'lastrowid': 0, 'ok': True, 'result': [["""{"cvs_support_account_id":"123"}""","1","2"]]}
        with pytest.raises(Exception) as ex:
            Scheduler(logger=logging.getLogger(),cust_id='1', region='us-west-2', vmid='i-12345', upgrading_slots=2, node_type='50')
        assert ex.type is ValidationFailedException

    @patch('utils.upgrade.scheduler.lambda_func')
    def test_check_if_env_use_swm_by_default(self, mock_lambda_func):
        logger = MagicMock()

        # Test case 1: Dev environment, swm_upgrade_by_default = 1
        mock_lambda_func.db_access = MagicMock(
            return_value={
            'ok': True,
            'result': [('gcp_env', 'dev'), ('swm_upgrade_by_default', '1')]
        })
        result = check_if_env_use_swm_by_default(logger)
        self.assertTrue(result)

        # Test case 2: Prod environment, swm_upgrade_by_default = 0
        mock_lambda_func.db_access = MagicMock(
            return_value={
            'ok': True,
            'result': [('gcp_env', 'prod'), ('swm_upgrade_by_default', '0')]
        })
        result = check_if_env_use_swm_by_default(logger)
        self.assertFalse(result)

        # Test case 3: Test environment, swm_upgrade_by_default not set
        mock_lambda_func.db_access = MagicMock(
            return_value={
            'ok': True,
            'result': [('gcp_env', 'test')]
        })
        result = check_if_env_use_swm_by_default(logger)
        self.assertTrue(result)

        # Test case 4: DB access failure
        mock_lambda_func.db_access = MagicMock(
            return_value = {'ok': False}
        )
        result = check_if_env_use_swm_by_default(logger)
        self.assertFalse(result)

        # Verify that db_access was called with the correct SQL query
        mock_lambda_func.db_access.assert_called_with(
            "SELECT name, value from orch_cfg where name in ('gcp_env', 'swm_upgrade_by_default')",
            None
        )