from unittest.mock import MagicMock
import sys
import os
import pytest

sys.modules['boto3'] = MagicMock()
sys.modules['boto3.dynamodb'] = MagicMock()
sys.modules['lib'] = MagicMock()
sys.modules['lib.lambda_func'] = MagicMock()
sys.modules['libs.common.shared.utils'] = MagicMock()
sys.modules['queue_generation'] = MagicMock()
sys.modules['worker'] = MagicMock()
sys.modules['libs.common.shared'] = MagicMock()
sys.modules['libs.common.shared.orch_utils'] = MagicMock()
sys.modules['libs.common.shared.dbconn'] = MagicMock()