import unittest
from unittest.mock import patch, MagicMock
import mock_imports_oci_sdk_api
from utils.oci_sdk.oci_sdk_api import OCISdkUtility

class TestOCISdkUtility(unittest.TestCase):

    @patch('oci.config.from_file')
    @patch('oci.core.VirtualNetworkClient')
    def setUp(self, mock_virtual_network_client, mock_from_file):
        self.mock_config = {'region': 'us-ashburn-1'}
        mock_from_file.return_value = self.mock_config
        self.oci_utility = OCISdkUtility()
        self.mock_virtual_network_client = mock_virtual_network_client.return_value

    def test_init_default(self):
        self.assertEqual(self.oci_utility.oci_config, self.mock_config)
        self.assertIsNotNone(self.oci_utility.oci_virtual_network_client)

    @patch('oci.config.from_file')
    @patch('oci.core.VirtualNetworkClient')
    def test_init_with_custom_params(self, mock_virtual_network_client, mock_from_file):
        custom_config = {'region': 'us-phoenix-1'}
        mock_from_file.return_value = custom_config
        oci_utility = OCISdkUtility(
            config_file='/custom/path/config.cfg',
            profile_name='CUSTOM',
            native_compute_region_name='us-phoenix-1'
        )
        mock_from_file.assert_called_with(file_location='/custom/path/config.cfg', profile_name='CUSTOM')
        self.assertEqual(oci_utility.oci_config, custom_config)
        self.assertEqual(oci_utility.oci_config['region'], 'us-phoenix-1')

    def test_update_public_ip(self):
        mock_response = MagicMock()
        self.mock_virtual_network_client.update_public_ip.return_value = mock_response

        reserved_public_ip_ocid = 'ocid1.publicip.oc1..example'
        private_ip_ocid = 'ocid1.privateip.oc1..example'
        response = self.oci_utility.update_public_ip(reserved_public_ip_ocid, private_ip_ocid)
        self.mock_virtual_network_client.update_public_ip.assert_called_once()
        self.assertEqual(response, mock_response)

    @patch('logging.Logger')
    def test_update_public_ip_with_logger(self, mock_logger):
        mock_response = MagicMock()
        self.mock_virtual_network_client.update_public_ip.return_value = mock_response
        reserved_public_ip_ocid = 'ocid1.publicip.oc1..example'
        private_ip_ocid = 'ocid1.privateip.oc1..example'
        response = self.oci_utility.update_public_ip(reserved_public_ip_ocid, private_ip_ocid, logger=mock_logger)
        
        mock_logger.info.assert_called()
        self.mock_virtual_network_client.update_public_ip.assert_called_once()
        self.assertEqual(response, mock_response)

if __name__ == '__main__':
    unittest.main()
