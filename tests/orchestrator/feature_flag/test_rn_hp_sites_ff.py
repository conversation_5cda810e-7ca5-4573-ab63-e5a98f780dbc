import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile



def test_rn_hp_sites_ff():

    with tempfile.TemporaryDirectory() as temp_dir:
        FF_NAME = 'rn_hp_sites'
        logger = logging.getLogger("rn_hp_sites_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/'rn_hp_sites_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        rn_hp_sites = FeatureFlag(name=FF_NAME,
                                  app='plugin',
                                  rel_version='6.0.0',
                                  feature=FF_NAME,
                                  flag_type='sre',
                                  logger=logger)

        assert rn_hp_sites.rel_version == '6.0.0'
        assert rn_hp_sites.flag_type == 'sre'
        assert rn_hp_sites.sre_dev_default_flag_value == 0


