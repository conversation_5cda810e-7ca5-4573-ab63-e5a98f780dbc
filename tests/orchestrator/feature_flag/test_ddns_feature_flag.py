import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'ddns_feature_flag'

def ddns_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_ami_version_dep("11.2.0")
        ff_obj.add_panorama_plugin_dep("panorama_plugin", "5.1.0")
        ff_obj.add_fw_app_version_dep("saas_agent", "5.1.0")
        ff_obj.add_ff_err_msg("PA DDNS requires at least 5.1 plugin, 5.1 saas agent, and 11.2.0 ami")
        ff_obj.save()
        
def test_ddns_ff_add_dep_510():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_ddns_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/ddns_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        ddns_ff = FeatureFlag(name=FF_NAME,
                                    app='plugin',
                                    rel_version='5.1.0',
                                    feature=FF_NAME,
                                    flag_type='compatibility',
                                    logger=logger)
        
        ddns_ff_add_dep(ddns_ff)
        assert ddns_ff.ami_dep_version == "11.2.0"
        assert ddns_ff.panorama_plugin_dep['panorama_plugin'] == "5.1.0"
        assert ddns_ff.fw_app_version["saas_agent"] == "5.1.0"
        assert ddns_ff.flag_type == 'compatibility'
        assert ddns_ff.err_message == "PA DDNS requires at least 5.1 plugin, 5.1 saas agent, and 11.2.0 ami"
