import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'ocsp_traffic_out_internet_ff'

def test_ocsp_traffic_out_internet_ff_sre_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_ocsp_traffic_out_internet_ff_sre_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/ocsp_traffic_out_internet_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        ocsp_ff = FeatureFlag(name=FF_NAME,
                                    app='saas_agent',
                                    rel_version='5.1.0',
                                    feature=FF_NAME,
                                    flag_type='sre',
                                    logger=logger)
        
        ocsp_ff.add_dev_sre_default_flag_value(0)
        ocsp_ff.add_ff_err_msg("OCSP traffic out internet feature flag disabled by default")
        ocsp_ff.save()

        assert ocsp_ff.sre_dev_default_flag_value == 0
        assert ocsp_ff.err_message == "OCSP traffic out internet feature flag disabled by default"

def test_ocsp_traffic_out_internet_compatibility_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_ocsp_traffic_out_internet_compatibility_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/ocsp_traffic_out_internet_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        ocsp_ff_compability = FeatureFlag(name=FF_NAME,
                                    app='plugin',
                                    rel_version='5.1.0',
                                    feature=FF_NAME,
                                    flag_type='compatibility',
                                    logger=logger)
        
        ocsp_ff_compability.add_panorama_plugin_dep("panorama_plugin", "5.1.0")
        ocsp_ff_compability.add_fw_app_version_dep("saas_agent", "5.1.0")
        ocsp_ff_compability.save()

        assert ocsp_ff_compability.panorama_plugin_dep['panorama_plugin'] == "5.1.0"
        assert ocsp_ff_compability.fw_app_version["saas_agent"] == "5.1.0"
        assert ocsp_ff_compability.flag_type == 'compatibility'
