import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'cloud_userid'
FF_SRE_NAME = 'cloud_userid_sre'
FEATURE_NAME = 'cloud_user_context'
REL_VERSION = '6.0.0'
AMI_REL_VERSION = "12.1.0"
ERR_MSG = "cloud_userid requires at least ami {AMI_REL_VERSION}, panorama plugin {REL_VERSION}, saas-agent {REL_VERSION}"

def cloud_userid_ff_add_dep(ff_obj):
    # Enabled only with the following added dependencies
    ff_obj.add_ami_version_dep(AMI_REL_VERSION)
    ff_obj.add_fw_app_version_dep("saas_agent", REL_VERSION)
    ff_obj.add_panorama_plugin_dep("panorama_plugin", REL_VERSION)
    ff_obj.add_ff_err_msg(ERR_MSG)
    ff_obj.save()

def test_cloud_userid_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_cloud_userid_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/'cloud_userid_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        
        apps = ['plugin']

        for app_name in apps:
            cloud_userid_ff_plugin = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version=REL_VERSION,
                                        feature=FEATURE_NAME,
                                        flag_type='compatibility',
                                        logger=logger)

            cloud_userid_ff_add_dep(cloud_userid_ff_plugin)

            assert cloud_userid_ff_plugin.flag_name == FF_NAME
            assert cloud_userid_ff_plugin.feature_name == FEATURE_NAME
            assert cloud_userid_ff_plugin.app == app_name
            assert cloud_userid_ff_plugin.rel_version == REL_VERSION
            assert cloud_userid_ff_plugin.flag_type == 'compatibility'
            assert cloud_userid_ff_plugin.err_message == ERR_MSG
            assert cloud_userid_ff_plugin.ami_dep_version == AMI_REL_VERSION
            assert cloud_userid_ff_plugin.panorama_plugin_dep['panorama_plugin'] == REL_VERSION

            cloud_userid_ff_plugin_sre = FeatureFlag(name=FF_SRE_NAME,
                                            app=app_name,
                                            rel_version=REL_VERSION,
                                            feature=FEATURE_NAME,
                                            flag_type='sre',
                                            logger=logger)

            # default value 1: enabled
            cloud_userid_ff_plugin_sre.add_dev_sre_default_flag_value(1)
            cloud_userid_ff_plugin_sre.add_ff_err_msg(
                "cloud_userid SRE is disabled for this tenant")
            cloud_userid_ff_plugin_sre.save()

            assert cloud_userid_ff_plugin_sre.flag_name == FF_SRE_NAME
            assert cloud_userid_ff_plugin_sre.app == app_name
            assert cloud_userid_ff_plugin_sre.rel_version == REL_VERSION
            assert cloud_userid_ff_plugin_sre.feature_name == FEATURE_NAME
            assert cloud_userid_ff_plugin_sre.flag_type == 'sre'
            assert cloud_userid_ff_plugin_sre.sre_dev_default_flag_value == 1
            assert cloud_userid_ff_plugin_sre.err_message == "cloud_userid SRE is disabled for this tenant"