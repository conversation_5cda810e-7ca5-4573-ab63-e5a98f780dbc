import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def enable_500_site_dep(obj):
    # Enabled only with the following added dependencies
    obj.add_ami_version_dep("10.2.5")
    obj.add_fw_app_version_dep("saas_agent", "4.0.0")
    obj.add_panorama_plugin_dep("panorama_plugin", "4.0.0")
    obj.add_ff_err_msg("500-site phase 2 support requires at least a 10.2.5 dataplane, "
                                            "4.0 Saas Agent")
    obj.save()

def check_enable_500_site_dep(obj):
    assert obj.ami_dep_version == "10.2.5"
    assert obj.fw_app_version["saas_agent"] == "4.0.0"
    assert obj.flag_type == 'compatibility'
    assert obj.panorama_plugin_dep["panorama_plugin"] == "4.0.0"
    assert obj.err_message == '500-site phase 2 support requires at least a 10.2.5 dataplane, 4.0 Saas Agent'

def test_enable_500_site_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_enable_500_site2_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/enable_500_site2_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        enable_500_site_ff_plugin = FeatureFlag(name='enable_500_site2',
                                            app='plugin',
                                            rel_version='4.2.0',
                                            feature='enable_500_site2',
                                            flag_type='compatibility',
                                            logger=logger)
        enable_500_site_ff_agent = FeatureFlag(name='enable_500_site2',
                                            app='saas_agent', 
                                            rel_version='4.2.0',
                                            feature='enable_500_site2',
                                            flag_type='compatibility', 
                                            logger=logger)

        enable_500_site_dep(enable_500_site_ff_plugin)
        enable_500_site_dep(enable_500_site_ff_agent)
        check_enable_500_site_dep(enable_500_site_ff_plugin)
        check_enable_500_site_dep(enable_500_site_ff_agent)
