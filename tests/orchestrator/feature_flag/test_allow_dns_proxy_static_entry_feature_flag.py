import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

supported_plugin_ver = ['4.1.0']
def test_allow_dns_proxy_static_entry_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("allow_dns_proxy_static_entry_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/allow_dns_proxy_static_entry_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        for ver in supported_plugin_ver:
            allow_dns_proxy_static_entry_ff = FeatureFlag(name='allow-dns-static-entries-ff',
                                        app='plugin',
                                        rel_version=ver,
                                        feature='allow-dns-static-entries-ff',
                                        flag_type='sre',
                                        logger=logger)

            allow_dns_proxy_static_entry_ff.add_dev_sre_default_flag_value(0)
            allow_dns_proxy_static_entry_ff.add_ff_err_msg(f"Static DNS entry feature is disabled " \
                "for this tenant in cloud service plugin {ver}")
            allow_dns_proxy_static_entry_ff.save()
            assert allow_dns_proxy_static_entry_ff.flag_type == 'sre'
            assert allow_dns_proxy_static_entry_ff.err_message == f"Static DNS entry feature is disabled " \
                "for this tenant in cloud service plugin {ver}"
