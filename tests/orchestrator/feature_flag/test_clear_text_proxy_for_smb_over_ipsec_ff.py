import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'clear_text_proxy_for_smb_over_ipsec_ff'
ERR_MSG = "Clear-text proxy for SMB over IPSec when SMB and IPSec share a dp requires at least 6.0.0 Saas Agent, 11.2.5 ami"
FF_SRE_NAME = 'clear_text_proxy_for_smb_over_ipsec_ff_sre'
ERR_MSG_SRE = "Clear-text proxy for SMB over IPSec feature flag disabled by default."

def test_clear_text_proxy_for_smb_over_ipsec_ff():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_clear_text_proxy_for_smb_over_ipsec_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/clear_text_proxy_for_smb_over_ipsec_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_infra', 'saas_agent']
        for app_name in apps:
            clear_text_proxy_for_smb_over_ipsec_ff = FeatureFlag(name=FF_NAME,
                                                                 app=app_name,
                                                                 rel_version='6.0.0',
                                                                 feature=FF_NAME,
                                                                 flag_type='compatibility',
                                                                 logger=logger)
            # Enabled only with the following added dependencies
            clear_text_proxy_for_smb_over_ipsec_ff.add_ff_err_msg(ERR_MSG)
            clear_text_proxy_for_smb_over_ipsec_ff.add_ami_version_dep("11.2.5")
            clear_text_proxy_for_smb_over_ipsec_ff.add_fw_app_version_dep("saas_agent", "6.0.0")
            clear_text_proxy_for_smb_over_ipsec_ff.save()

            clear_text_proxy_for_smb_over_ipsec_ff_sre = FeatureFlag(name=FF_SRE_NAME,
                                                                     app=app_name,
                                                                     rel_version='6.0.0',
                                                                     feature=FF_SRE_NAME,
                                                                     flag_type='sre',
                                                                     logger=logger)
            clear_text_proxy_for_smb_over_ipsec_ff_sre.add_dev_sre_default_flag_value(0)
            clear_text_proxy_for_smb_over_ipsec_ff_sre.add_ff_err_msg(ERR_MSG_SRE)
            clear_text_proxy_for_smb_over_ipsec_ff_sre.save()

            assert clear_text_proxy_for_smb_over_ipsec_ff.rel_version == "6.0.0"
            assert clear_text_proxy_for_smb_over_ipsec_ff.flag_type == 'compatibility'
            assert clear_text_proxy_for_smb_over_ipsec_ff.err_message == ERR_MSG
            assert clear_text_proxy_for_smb_over_ipsec_ff.ami_dep_version == '11.2.5'
            assert clear_text_proxy_for_smb_over_ipsec_ff.fw_app_version["saas_agent"] == "6.0.0"

            assert clear_text_proxy_for_smb_over_ipsec_ff_sre.sre_dev_default_flag_value == 0
            assert clear_text_proxy_for_smb_over_ipsec_ff_sre.rel_version == "6.0.0"
            assert clear_text_proxy_for_smb_over_ipsec_ff_sre.flag_type == 'sre'
            assert clear_text_proxy_for_smb_over_ipsec_ff_sre.err_message == ERR_MSG_SRE
