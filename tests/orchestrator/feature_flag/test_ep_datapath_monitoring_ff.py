import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'ep_datapath_monitoring_feature_flag'

def ep_datapath_monitoring_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_ami_version_dep("10.2.3")
        ff_obj.add_eproxy_outside_panos_dep(1)
        ff_obj.add_proxy_ami_version_dep("3.2.0")
        ff_obj.add_fw_app_version_dep("saas_agent", "3.2.1")
        ff_obj.add_ff_err_msg("Explicit Proxy Datapath monitoring feature requires at least 3.2.1 saas agent, 3.2.0 proxy ami, 10.2.3 ami and EProxy Outside Panos feature enabled")
        ff_obj.save()
        
def test_ep_datapath_monitoring_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_ep_datapath_monitoring_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/ep_datapath_monitoring_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['infra', 'saas_agent']
        for app_name in apps:
            ep_datapath_monitoring_ff = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='3.2.1',
                                        feature=FF_NAME,
                                        flag_type='compatibility',
                                        logger=logger)
            ep_datapath_monitoring_ff_add_dep(ep_datapath_monitoring_ff)
            assert ep_datapath_monitoring_ff.ami_dep_version == "10.2.3"
            assert ep_datapath_monitoring_ff.eproxy_outside_panos == 1
            assert ep_datapath_monitoring_ff.proxy_ami_version_dep == "3.2.0"
            assert ep_datapath_monitoring_ff.fw_app_version["saas_agent"] == "3.2.1"
            assert ep_datapath_monitoring_ff.flag_type == 'compatibility'
            assert ep_datapath_monitoring_ff.err_message == "Explicit Proxy Datapath monitoring feature requires at least 3.2.1 saas agent, 3.2.0 proxy ami, 10.2.3 ami and EProxy Outside Panos feature enabled"
