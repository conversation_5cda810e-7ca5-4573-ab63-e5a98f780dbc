import mock_imports_orchestrator
import logging
from unittest import TestCase
from unittest.mock import patch
from libs.feature_flags import feature_flags
from libs.feature_flags import feature_flags_exceptions as ffe
import pytest

logger = logging.getLogger()


class TestFeatureFlagsExceptions(TestCase):

    def test_missing_required_attr_exception(self):
        with pytest.raises(ffe.MissingRequiredAttributeException) as e:
            self.ff = feature_flags.FeatureFlag()
        assert "MissingRequiredAttributeException" in str(e.type)
        assert "Missing one of the required arguments" in str(e.value)

    def test_invalid_option_exception(self):
        with pytest.raises(ffe.InvalidOptionException) as e:
            self.ff = feature_flags.FeatureFlag(name='tenant_use_cfgservice',
                                                app='saas_agent', rel_version='4.0.0',
                                                feature='tenant_use_cfgservice',
                                                flag_type='dev', logger=logger)
            self.ff.add_ami_version_dep("10.2.2")
        assert "InvalidOptionException" in str(e.type)
        assert "Cannot set dependency flag" in str(e.value)

    @patch('libs.feature_flags.feature_flags.b64encode')
    def test_bad_value_exception(self, mock_b64encode):
        mock_b64encode.return_value = "a" * 60001
        with pytest.raises(ffe.BadValueException) as e:
            self.ff = feature_flags.FeatureFlag(name='tenant_use_cfgservice',
                                                app='saas_agent', rel_version='4.0.0',
                                                feature='tenant_use_cfgservice',
                                                flag_type='compatibility', logger=logger)
            self.ff.add_ami_version_dep("10.2.2" * 61000)
            self.ff.save()
        assert "BadValueException" in str(e.type)
        assert "dependencies length" in str(e.value)

    @patch("libs.feature_flags.feature_flags.dbconn.execute_query", return_value=False)
    def test_feature_flag_save_exception(self, mock_dbconn_exec_query):
        with pytest.raises(ffe.FeatureFlagSaveDBException) as e:
            self.ff = feature_flags.FeatureFlag(name='tenant_use_cfgservice',
                                                app='saas_agent', rel_version='4.0.0',
                                                feature='tenant_use_cfgservice',
                                                flag_type='dev', logger=logger)
            self.ff.add_dev_sre_default_flag_value(0)
            self.ff.save()
        assert "FeatureFlagSaveDBException" in str(e.type)
        assert "Failed to run execute query" in str(e.value)
