import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def inter_region_routing_dep(obj):
    # Enabled only with the following added dependencies
    obj.add_ami_version_dep("10.2.4")
    obj.add_fw_app_version_dep("saas_agent", "5.2.0")
    obj.add_ff_err_msg("inter_region_routing support requires at least a 10.2.4 dataplane, "
                                            "5.2 Saas Agent")
    obj.save()

def check_inter_region_routing_dep(obj):
    assert obj.ami_dep_version == "10.2.4"
    assert obj.fw_app_version["saas_agent"] == "5.2.0"
    assert obj.flag_type == 'compatibility'
    assert obj.err_message == 'inter_region_routing support requires at least a 10.2.4 dataplane, 5.2 Saas Agent'

def inter_region_routing_ff_dev(obj, feature_arguments):
    obj.add_dev_sre_default_flag_value(0)
    obj.add_feature_arguments(feature_arguments)
    obj.add_ff_err_msg("Inter Region Routing feature Greenfield for this tenant is not set. Use customer setting.")
    obj.save()

def check_inter_region_routing_ff_dev(obj, feature_arguments, version):
    assert obj.flag_name == "inter_region_routing_ff_dev"
    assert obj.rel_version == version
    assert obj.feature_arguments == json.dumps(feature_arguments)
    assert obj.err_message == 'Inter Region Routing feature Greenfield for this tenant is not set. Use customer setting.'

def test_inter_region_routing_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_inter_region_routing_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/inter_region_routing_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        inter_region_routing_ff_agent = FeatureFlag(name='inter_region_routing',
                                            app='saas_agent',
                                            rel_version='5.2.0',
                                            feature='inter_region_routing',
                                            flag_type='compatibility',
                                            logger=logger)
        inter_region_routing_dep(inter_region_routing_ff_agent)
        check_inter_region_routing_dep(inter_region_routing_ff_agent)
        
        versions = ["5.0.0", "5.0.1", "5.1.0", "5.2.0"]
        for version in versions:
            feature_arguments = {"green_field_only": True}
            inter_region_routing_ff_dev_plugin = FeatureFlag(name='inter_region_routing_ff_dev',
                                                app='plugin',
                                                rel_version=version,
                                                feature='inter_region_routing_ff_dev',
                                                flag_type='dev',
                                                logger=logger)
            inter_region_routing_ff_dev(inter_region_routing_ff_dev_plugin, feature_arguments)
            check_inter_region_routing_ff_dev(inter_region_routing_ff_dev_plugin, feature_arguments, version)
