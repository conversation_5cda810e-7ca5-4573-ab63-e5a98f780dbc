import logging
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile


def test_enable_ipam_service():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_enable_ipam_service.py")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/tenant_enable_ipam_service.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_infra']
        for app_name in apps:
            ipam_service_ff_infra = FeatureFlag(name="tenant_enable_ipam_service",
                                                        app=app_name,
                                                        rel_version='5.1.0',
                                                        feature="tenant_enable_ipam_service",
                                                        flag_type='dev',
                                                        logger=logger)
            ipam_service_ff_infra.add_dev_sre_default_flag_value(0)

            ipam_service_ff_infra.add_ff_err_msg(
                "tenant no passive instances feature is disabled for this tenant")

            assert ipam_service_ff_infra.rel_version == "5.1.0"
            assert ipam_service_ff_infra.flag_name == 'tenant_enable_ipam_service'
            assert ipam_service_ff_infra.sre_dev_default_flag_value == 0
            assert ipam_service_ff_infra.err_message == "tenant no passive instances feature is disabled for this tenant"

            ztt_ipam_service_ff_infra = FeatureFlag(name="ztt_enable_ipam_service",
                                                            app=app_name,
                                                            rel_version='5.1.0',
                                                            feature="ztt_enable_ipam_service",
                                                            flag_type='dev',
                                                            logger=logger)
            ztt_ipam_service_ff_infra.add_dev_sre_default_flag_value(0)

            ztt_ipam_service_ff_infra.add_ff_err_msg(
                "ztt no passive instances feature is disabled for this tenant")
            assert ztt_ipam_service_ff_infra.rel_version == "5.1.0"
            assert ztt_ipam_service_ff_infra.flag_name == 'ztt_enable_ipam_service'
            assert ztt_ipam_service_ff_infra.sre_dev_default_flag_value == 0
            assert ztt_ipam_service_ff_infra.err_message == "ztt no passive instances feature is disabled for this tenant"
