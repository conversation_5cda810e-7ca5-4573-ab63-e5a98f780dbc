import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'ep_pab_feature_flag'
ERR_MSG = "Prisma Access Browser feature requires at least 5.1.0 saas agent, 10.2.4 panos ami, 5.1.0 Envoy Proxy, and EProxy Outside Panos feature enabled"

def ep_pab_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_eproxy_outside_panos_dep(1)
        ff_obj.add_fw_app_version_dep("saas_agent", "5.1.0")
        ff_obj.add_swg_ami_dep_version("10.2.4")
        ff_obj.add_proxy_ami_version_dep("5.1.0")
        ff_obj.add_ff_err_msg("Prisma Access Browser feature requires at least 5.1.0 saas agent, 10.2.4 panos ami, 5.1.0 Envoy Proxy, and EProxy Outside Panos feature enabled")
        ff_obj.save()
        
def test_ep_pab_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_ep_pab_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/'ep_pab_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['plugin']
        for app_name in apps:
            ep_pab_ff = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='6.0.0',
                                        feature=FF_NAME,
                                        flag_type='compatibility',
                                        logger=logger)
            ep_pab_ff_add_dep(ep_pab_ff)
            assert ep_pab_ff.rel_version == "6.0.0"
            assert ep_pab_ff.flag_type == 'compatibility'
            assert ep_pab_ff.err_message == ERR_MSG
            assert ep_pab_ff.eproxy_outside_panos == 1
            assert ep_pab_ff.swg_ami_dep_version == "10.2.4"
            assert ep_pab_ff.proxy_ami_version_dep == "5.1.0"
            assert ep_pab_ff.fw_app_version["saas_agent"] == "5.1.0"