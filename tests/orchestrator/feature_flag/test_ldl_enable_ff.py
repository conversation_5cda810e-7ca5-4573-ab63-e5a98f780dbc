import mock_imports_orchestrator
import pytest
import logging
import json
import unittest
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def test_ldl_enable_feature_flag():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("ldl_feature_flag")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/ldl_feature_flag.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)


        ldl_ff = FeatureFlag(name='ldl_feature_flag',
                             app='plugin',
                             rel_version='5.1.0',
                             feature='ldl_feature_flag',
                             flag_type='sre',
                             logger=logger)

        ldl_ff.add_dev_sre_default_flag_value(0)
        ldl_ff.add_ff_err_msg(f"Local Deep Learning feature flag disabled by default")
        ldl_ff.save()

        assert ldl_ff.sre_dev_default_flag_value == 0
        assert ldl_ff.err_message == f"Local Deep Learning feature flag disabled by default"
