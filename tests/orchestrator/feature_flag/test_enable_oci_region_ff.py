import mock_imports_orchestrator
import pytest
import logging
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def test_enable_oci_regions_ff():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_oci_feature_flag")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/oci_regions_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        enable_oci_regions_ff = FeatureFlag(name='enable_oci_regions',
                                    app='saas_infra',
                                    rel_version='5.2.0',
                                    feature='enable_oci_regions_support',
                                    flag_type='dev',
                                    logger=logger
                        )
        enable_oci_regions_ff.add_ff_err_msg("test error message")
        assert enable_oci_regions_ff.rel_version == "5.2.0"
        assert enable_oci_regions_ff.flag_name == "enable_oci_regions"
        assert enable_oci_regions_ff.err_message == "test error message"
