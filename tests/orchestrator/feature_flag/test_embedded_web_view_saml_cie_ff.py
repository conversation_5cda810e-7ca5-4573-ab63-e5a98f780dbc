import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import libs.feature_flags.feature_flags_exceptions as ffe
import tempfile

SRE_FF_NAME = 'embedded_web_view_saml_cie_sre_ff'
FF_NAME = 'embedded_web_view_saml_cie_ff'
INFRA_REL_VER = '5.2.0'
RELEASE_MATRIX = {
    'sre_ff': {
        'default':  0,
        'apps': {
            'saas_agent': '5.1.0',
        },
    },
    'compat_ff': {
        'saas_agent': {
            'saas_agent': '5.1.0',
            'ami': '11.2.0',
        }
    },
}

def set_embedded_web_view_compat_ffs(logger):
    try:
        # setting compatibility feature flag
        for app, deps in RELEASE_MATRIX['compat_ff'].items():
            compat_ff = FeatureFlag(name=FF_NAME,
                                        app=app,
                                        rel_version=INFRA_REL_VER,
                                        feature=FF_NAME,
                                        flag_type='compatibility',
                                        logger=logger)
            err_msg = f"embedded_web_view_saml_cie_ff({app}) requires at least "
            if 'ami' in deps:
                compat_ff.add_ami_version_dep(deps['ami'])
                err_msg += f"ami({deps['ami']}) "
            if 'saas_agent' in deps:
                compat_ff.add_fw_app_version_dep('saas_agent', deps['saas_agent'])
                err_msg += f"saas_agent({deps['saas_agent']}) "
            compat_ff.add_ff_err_msg(err_msg)
            compat_ff.save()
            assert compat_ff.ami_dep_version == deps['ami']
            assert compat_ff.fw_app_version["saas_agent"] == deps['saas_agent']
            assert compat_ff.flag_type == 'compatibility'
            assert compat_ff.app == app
            assert compat_ff.rel_version == INFRA_REL_VER
            assert compat_ff.err_message == f"embedded_web_view_saml_cie_ff({app}) requires at least " \
                f"ami({deps['ami']}) " \
                f"saas_agent({deps['saas_agent']}) "

    except (ffe.MissingRequiredAttributeException, ffe.BadValueException, \
            ffe.InvalidOptionException, ffe.FeatureFlagSaveDBException) as e:
        logger.error(f"Exception: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Generic Exception: {str(e)}")
        raise

def test_embedded_web_view_saml_cie_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_embedded_web_view_saml_cie_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/embedded_web_view_saml_cie_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        for app, ver in RELEASE_MATRIX['sre_ff']['apps'].items():
            sre_ff = FeatureFlag(name=SRE_FF_NAME,
                                    app=app,
                                    rel_version=ver,
                                    feature=FF_NAME,
                                    flag_type='sre',
                                    logger=logger)

            sre_ff.add_dev_sre_default_flag_value(1)
            sre_ff.add_ff_err_msg("embedded_web_view_saml_cie_ff is enabled for this tenant.")

            assert sre_ff.rel_version == ver
            assert sre_ff.flag_name == SRE_FF_NAME
            assert sre_ff.flag_type == 'sre'
            assert sre_ff.err_message == f"{FF_NAME} is enabled for this tenant."
        set_embedded_web_view_compat_ffs(logger)