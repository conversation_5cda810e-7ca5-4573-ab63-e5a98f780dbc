import sys
import os
from unittest.mock import patch, MagicMock

sys.modules['pymongo'] = MagicMock()
sys.modules['redis'] = MagicMock()
sys.modules['rediscluster'] = MagicMock()
os.environ['AWS_REGION'] = "mock-aws-region"
sys.modules["boto3.dynamodb.conditions"] = MagicMock()
sys.modules["boto3"] = MagicMock()
sys.modules["io"] = MagicMock()
sys.modules["gzip"] = MagicMock()
sys.modules["pickle"] = MagicMock()
sys.modules["uuid"] = MagicMock()
sys.modules["common.shared.py3_utils"] = MagicMock()
sys.modules["common.shared.sys_utils"] = MagicMock()
sys.modules["common.shared.utils"] = MagicMock()
sys.modules["common.shared"] = MagicMock()
sys.modules["common.util"] = MagicMock()
sys.modules["common"] = MagicMock()
sys.modules["apis.region_master_lambda_api"] = MagicMock()
sys.modules["model.custmodellambda"] = MagicMock()
sys.modules['ngpa_extern'] = MagicMock()
sys.modules['ngpa_extern.external_utils'] = MagicMock()