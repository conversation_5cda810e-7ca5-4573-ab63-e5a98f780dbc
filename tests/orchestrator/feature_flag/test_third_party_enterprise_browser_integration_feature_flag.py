import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'third_party_enterprise_browser_integration_feature_flag'
ERR_MSG = "Explicit Proxy Third Party Enterprise integration requires at least 5.2.0 Saas Agent, EProxy Outside Panos feature, 5.2.0 proxy ami"

def third_party_enterprise_browser_integration_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_ff_err_msg(ERR_MSG)
        ff_obj.add_eproxy_outside_panos_dep(1)
        ff_obj.add_proxy_ami_version_dep("5.2.0")
        ff_obj.add_fw_app_version_dep("saas_agent", "5.2.0")
        ff_obj.save()
        
def test_third_party_enterprise_browser_integration_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_third_party_enterprise_browser_integration_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/third_party_enterprise_browser_integration_feature_flag.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_infra', 'saas_agent', 'plugin']
        for app_name in apps:
            third_party_enterprise_browser_integration_ff = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='5.2.0',
                                        feature=FF_NAME,
                                        flag_type='compatibility',
                                        logger=logger)
            third_party_enterprise_browser_integration_ff_add_dep(third_party_enterprise_browser_integration_ff)
            assert third_party_enterprise_browser_integration_ff.rel_version == "5.2.0"
            assert third_party_enterprise_browser_integration_ff.flag_type == 'compatibility'
            assert third_party_enterprise_browser_integration_ff.err_message == ERR_MSG
            assert third_party_enterprise_browser_integration_ff.eproxy_outside_panos == 1
            assert third_party_enterprise_browser_integration_ff.proxy_ami_version_dep == '5.2.0'
            assert third_party_enterprise_browser_integration_ff.fw_app_version["saas_agent"] == "5.2.0"