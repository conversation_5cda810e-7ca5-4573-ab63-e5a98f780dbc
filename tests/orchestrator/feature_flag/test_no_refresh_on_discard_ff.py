from mock_feature_flag import *
import traceback
import importlib.util
import sys
import os
import warnings

from utils import feature_flag_declarations
import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

ffd_dir = os.path.dirname(feature_flag_declarations.__file__)
path = os.path.join(ffd_dir,sorted([ name for name in os.listdir(ffd_dir)
                                     if os.path.isdir(os.path.join(ffd_dir, name))
                                     and name[0].isdigit()])[-1])
sys.path.append(path)

FF_NAME = 'no_refresh_on_discard_ff_sre'

class TestNoRefreshOnDiscard:
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_no_refresh_on_discard_ff_sre")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/no_refresh_on_discard_ff_sre.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_agent', 'saas_infra']
        for app_name in apps:
            no_refresh_on_discard_ff_sre = FeatureFlag(name=FF_NAME,
                                           app=app_name,
                                           rel_version='6.0.0',
                                           feature=FF_NAME,
                                           flag_type='sre',
                                           logger=logger)

            assert no_refresh_on_discard_ff_sre.rel_version == "6.0.0"
            assert no_refresh_on_discard_ff_sre.flag_name == FF_NAME
            assert no_refresh_on_discard_ff_sre.app == app_name
            assert no_refresh_on_discard_ff_sre.feature_name == FF_NAME
            assert no_refresh_on_discard_ff_sre.flag_type == 'sre'
            assert no_refresh_on_discard_ff_sre.sre_dev_default_flag_value == 0

