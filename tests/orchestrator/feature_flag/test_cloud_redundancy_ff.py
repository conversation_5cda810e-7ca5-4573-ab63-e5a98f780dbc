import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'cloud_redundancy'

def cloud_redundancy_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_dev_sre_default_flag_value(1)
        ff_obj.add_ami_version_dep("10.0.9")
        ff_obj.add_fw_app_version_dep("saas_agent", "3.1.0")
        ff_obj.add_panorama_plugin_dep("panorama_plugin", "3.1.0")

        ff_obj.add_ff_err_msg("Cloud Redundancy feature requires at least a 10.0.9 dataplane, 3.1 Saas Agent and 3.1 Cloud Services Plugin")
        ff_obj.save()

        
def test_cloud_redundancy_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_cloud_redundancy_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/cloud_redundancy_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        cloud_redundancy_ff = FeatureFlag(name=FF_NAME,
                                          app='plugin',
                                          rel_version='4.0.0',
                                          feature=FF_NAME,
                                          flag_type='compatibility',
                                          logger=logger)
        
        cloud_redundancy_ff_add_dep(cloud_redundancy_ff)
        assert cloud_redundancy_ff.ami_dep_version == "10.0.9"
        assert cloud_redundancy_ff.panorama_plugin_dep['panorama_plugin'] == "3.1.0"
        assert cloud_redundancy_ff.fw_app_version["saas_agent"] == "3.1.0"
        assert cloud_redundancy_ff.flag_type == 'compatibility'
        assert cloud_redundancy_ff.err_message == "Cloud Redundancy feature requires at least a 10.0.9 dataplane, 3.1 Saas Agent and 3.1 Cloud Services Plugin" 
