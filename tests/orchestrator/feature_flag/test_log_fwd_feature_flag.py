import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile


FF_NAME = 'sc_log_fwd'

def sc_log_fwd_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_ami_version_dep("10.0.8")
        ff_obj.add_panorama_plugin_dep("panorama_plugin", "3.2.0")
        ff_obj.add_fw_app_version_dep("saas_agent", "2.2.0")
        ff_obj.add_ff_err_msg("SC Log Forwarding feature requires at least a 10.0.8 dataplane, 2.2 Saas Agent")
        ff_obj.save()
        
def test_sc_log_fwd_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_sc_log_fwd_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/sc_log_fwd_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        sc_log_fwd_ff = FeatureFlag(name=FF_NAME,
                                    app='plugin',
                                    rel_version='4.0.0',
                                    feature=FF_NAME,
                                    flag_type='compatibility',
                                    logger=logger)
        
        sc_log_fwd_ff_add_dep(sc_log_fwd_ff)
        assert sc_log_fwd_ff.ami_dep_version == "10.0.8"
        assert sc_log_fwd_ff.panorama_plugin_dep['panorama_plugin'] == "3.2.0"
        assert sc_log_fwd_ff.fw_app_version["saas_agent"] == "2.2.0"
        assert sc_log_fwd_ff.flag_type == 'compatibility'
        assert sc_log_fwd_ff.err_message == "SC Log Forwarding feature requires at least a 10.0.8 dataplane, 2.2 Saas Agent"