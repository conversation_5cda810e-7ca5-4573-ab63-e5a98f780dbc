import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile



def test_mcw_ff():
    with tempfile.TemporaryDirectory() as temp_dir:
        FF_NAME = 'managed_cloud_wan'
        logger = logging.getLogger("mcw_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/'mcw_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['plugin', 'saas_infra', 'saas_agent']
        for app_name in apps:
            managed_cloud_wan_ff = FeatureFlag(name=FF_NAME,
                                                app=app_name,
                                                rel_version='5.0.0',
                                                feature=FF_NAME,
                                                flag_type='sre',
                                                logger=logger)
            assert managed_cloud_wan_ff.rel_version == "5.0.0"
            assert managed_cloud_wan_ff.flag_type == 'sre'
            assert managed_cloud_wan_ff.sre_dev_default_flag_value == 0

