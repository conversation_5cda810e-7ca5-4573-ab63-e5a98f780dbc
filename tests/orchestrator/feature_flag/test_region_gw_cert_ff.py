import logging
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'region_gw_cert_ff'

def test_region_gw_cert_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_region_gw_cert_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/region_gw_cert_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        supported_vers = ['6.1.0']
        for ver in supported_vers:
            region_gw_cert_ff = FeatureFlag(name=FF_NAME,
                                              app='saas_agent',
                                              rel_version=ver,
                                              feature=FF_NAME,
                                              flag_type='compatibility',
                                              logger=logger)

            region_gw_cert_ff.add_fw_app_version_dep("saas_agent", "6.1.0")
            region_gw_cert_ff.add_ff_err_msg("region_gw_cert_ff feature requires saas_agent version 6.1.0 or higher")

            assert region_gw_cert_ff.fw_app_version["saas_agent"] == "6.1.0"
            assert region_gw_cert_ff.rel_version == ver
            assert region_gw_cert_ff.flag_name == FF_NAME
            assert region_gw_cert_ff.err_message == "region_gw_cert_ff feature requires saas_agent version 6.1.0 or higher"
