import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'per_env_r53_ff'
def build_per_env_r53_ff_test(app_name, version, logger):
    feature_arguments = {"green_field_only": True}
    per_env_r53_ff = FeatureFlag(name=FF_NAME,
                                 app=app_name,
                                 rel_version=version,
                                 feature=FF_NAME,
                                 flag_type='sre',
                                 logger=logger)
    per_env_r53_ff.add_dev_sre_default_flag_value(0)
    per_env_r53_ff.add_feature_arguments(feature_arguments)
    per_env_r53_ff.add_ff_err_msg("Per env route 53 is not enabled for this tenant.")
    per_env_r53_ff.save()
    assert per_env_r53_ff.flag_name == "per_env_r53_ff"
    assert per_env_r53_ff.app == app_name
    assert per_env_r53_ff.rel_version == version
    assert per_env_r53_ff.sre_dev_default_flag_value == 0
    assert per_env_r53_ff.feature_arguments == json.dumps(feature_arguments)


def test_per_env_r53_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_per_env_r53_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/'per_env_r53_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        build_per_env_r53_ff_test('saas_agent', '6.0.0', logger)

