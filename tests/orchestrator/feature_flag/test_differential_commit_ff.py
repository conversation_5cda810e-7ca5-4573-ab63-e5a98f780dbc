import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def differential_commit_ff_dep(obj):
    # Enabled only with the following added dependencies
    obj.add_fw_app_version_dep("saas_agent", "6.0.1")
    obj.add_ff_err_msg("differential_commit_ff support requires at least 6.0.1 Saas Agent")
    obj.save()

def check_differential_commit_ff_dep(obj):
    assert obj.fw_app_version["saas_agent"] == "6.0.1"
    assert obj.flag_type == 'compatibility'
    assert obj.err_message == 'differential_commit_ff support requires at least 6.0.1 Saas Agent'

def test_differential_commit_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_differential_commit_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/differential_commit_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        differential_commit_ff_agent = FeatureFlag(name='differential_commit_ff',
                                            app='saas_infra',
                                            rel_version='6.0.0',
                                            feature='differential_commit_ff',
                                            flag_type='compatibility',
                                            logger=logger)

        differential_commit_ff_dep(differential_commit_ff_agent)
        differential_commit_ff_dep(differential_commit_ff_agent)