import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def enable_direct_io_dep(obj):
    # Enabled only with the following added dependencies
    obj.add_ami_version_dep("10.2.8")
    obj.add_fw_app_version_dep("saas_agent", "5.0.0")
    obj.add_ff_err_msg("direct-io support requires at least a 10.2.8 dataplane, "
                                            "5.0 Saas Agent")
    obj.save()

def check_enable_direct_io_dep(obj):
    assert obj.ami_dep_version == "10.2.8"
    assert obj.fw_app_version["saas_agent"] == "5.0.0"
    assert obj.flag_type == 'compatibility'
    assert obj.err_message == 'direct-io support requires at least a 10.2.8 dataplane, 5.0 Saas Agent'

def test_enable_direct_io_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_enable_direct_io_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/enable_direct_io_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        enable_direct_io_ff_agent = FeatureFlag(name='enable_direct_io',
                                            app='saas_agent',
                                            rel_version='5.0.0',
                                            feature='enable_direct_io',
                                            flag_type='compatibility',
                                            logger=logger)

        enable_direct_io_dep(enable_direct_io_ff_agent)
        check_enable_direct_io_dep(enable_direct_io_ff_agent)


FF_NAME_SRE = 'enable_direct_io_sre'

def test_enable_direct_io_sre_ff():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_enable_direct_io_sre_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/enable_direct_io_sre_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_agent']
        for app_name in apps:
            enable_direct_io_sre_ff = FeatureFlag(name=FF_NAME_SRE,
                                        app=app_name,
                                        rel_version='5.0.0',
                                        feature=FF_NAME_SRE,
                                        flag_type='sre',
                                        logger=logger)
            enable_direct_io_sre_ff.add_ff_err_msg(
                "enable_direct_io_sre feature enabled for this tenant")

            assert enable_direct_io_sre_ff.rel_version == "5.0.0"
            assert enable_direct_io_sre_ff.flag_name == 'enable_direct_io_sre'
            assert enable_direct_io_sre_ff.err_message == "enable_direct_io_sre feature enabled for this tenant"
