import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile



def colo_100g_no_gre_ff_comp(ff_obj):
    # Enabled only with the following added dependencies
    ff_obj.add_dev_sre_default_flag_value(0)
    ff_obj.add_ami_version_dep("11.2.7")
    ff_obj.add_fw_app_version_dep("saas_agent", "6.1.0")
    ff_obj.add_panorama_plugin_dep("panorama_plugin", "6.1.0")

    ff_obj.add_ff_err_msg(
        "Colo 100G No GRE feature requires atleast 11.2.7 dataplane, 6.1.0 Saas Agent and 6.1.0 Panorama Plugin")
    ff_obj.save()


def test_colo_100g_no_gre_ff_comp():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_colo_100g_no_gre_ff_comp")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/colo_100g_no_gre_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['plugin', 'saas_infra', 'saas_agent']
        for app_name in apps:
            colo_100g_no_gre_ff_dep = FeatureFlag(name='colo_100g_no_gre_dep',
                                            app=app_name,
                                            rel_version='6.1.0',
                                            feature='colo_100g_no_gre',
                                            flag_type='compatibility',
                                            logger=logger)

            colo_100g_no_gre_ff_comp(colo_100g_no_gre_ff_dep)
            assert colo_100g_no_gre_ff_dep.ami_dep_version == "11.2.7"
            assert colo_100g_no_gre_ff_dep.panorama_plugin_dep['panorama_plugin'] == "6.1.0"
            assert colo_100g_no_gre_ff_dep.fw_app_version["saas_agent"] == "6.1.0"
            assert colo_100g_no_gre_ff_dep.flag_type == 'compatibility'
            assert colo_100g_no_gre_ff_dep.err_message == "Colo 100G No GRE feature requires atleast 11.2.7 dataplane, 6.1.0 Saas Agent and 6.1.0 Panorama Plugin"


def test_colo_100g_no_gre_ff_sre():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_colo_100g_no_gre_ff_sre")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/colo_100g_no_gre_ff_sre.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['plugin', 'saas_infra', 'saas_agent']
        for app_name in apps:
            colo_100g_no_gre_ff_sre = FeatureFlag(name='colo_100g_no_gre_sre',
                                                    app=app_name,
                                                    rel_version='6.1.0',
                                                    feature='colo_100g_no_gre',
                                                    flag_type='sre',
                                                    logger=logger)

            # default value 1: enabled
            colo_100g_no_gre_ff_sre.add_dev_sre_default_flag_value(0)
            colo_100g_no_gre_ff_sre.add_ff_err_msg(
                "Colo 100G No GRE SRE disable by default")
            colo_100g_no_gre_ff_sre.save()

            assert colo_100g_no_gre_ff_sre.flag_name == 'colo_100g_no_gre_sre'
            assert colo_100g_no_gre_ff_sre.app == app_name
            assert colo_100g_no_gre_ff_sre.rel_version == '6.1.0'
            assert colo_100g_no_gre_ff_sre.feature_name == 'colo_100g_no_gre'
            assert colo_100g_no_gre_ff_sre.flag_type == 'sre'
            assert colo_100g_no_gre_ff_sre.sre_dev_default_flag_value == 0
            assert colo_100g_no_gre_ff_sre.err_message == "Colo 100G No GRE SRE disable by default"