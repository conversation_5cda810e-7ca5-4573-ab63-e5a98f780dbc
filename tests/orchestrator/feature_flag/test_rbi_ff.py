import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'remote_browser_isolation'
FF_NAME_SRE = 'remote_browser_isolation_sre'
ERR_MSG = "Remote Browser Isolation requires minimum versions as saas-agent: 5.0.0, saas-infra: 5.0.0, PANOS-AMI: 10.2.8, CSP: 5.0.0"

def test_rbi_feature_flag_sre():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_rbi_feature_flag")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/rbi_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['plugin', 'saas_infra', 'saas_agent']

        # Test flag_type='compatibility'
        for app_name in apps:
            rbi_feature_flag = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='5.0.0',
                                        feature=FF_NAME,
                                        flag_type='compatibility',
                                        logger=logger)
            rbi_feature_flag.add_ff_err_msg(ERR_MSG)
            assert rbi_feature_flag.rel_version == "5.0.0"
            assert rbi_feature_flag.flag_name == FF_NAME
            assert rbi_feature_flag.err_message == ERR_MSG

        # Test flag_type='sre'
        for app_name in apps:
            rbi_feature_flag = FeatureFlag(name=FF_NAME_SRE,
                                        app=app_name,
                                        rel_version='5.0.0',
                                        feature=FF_NAME,
                                        flag_type='sre',
                                        logger=logger)
            rbi_feature_flag.add_ff_err_msg(ERR_MSG)
            assert rbi_feature_flag.rel_version == "5.0.0"
            assert rbi_feature_flag.flag_name == FF_NAME_SRE
            assert rbi_feature_flag.err_message == ERR_MSG
