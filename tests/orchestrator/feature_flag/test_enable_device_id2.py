import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

REL_VERSION = '6.0.0'
MU_AMI_REL_VERSION = "11.2.4"
MU_SA_VERSION = "5.2.0"
RN_SA_VERSION = "5.0.0"

def iot_ff_add_mu_dep(ff_obj, ami_version, saas_agent_version):
    err_msg = f"device id2 for MU requires at least ami {ami_version}, panorama plugin {REL_VERSION}, saas-agent {saas_agent_version}"
    # Enabled only with the following added dependencies
    ff_obj.add_mu_ami_dep_version(ami_version)
    ff_obj.add_fw_app_version_dep("saas_agent", saas_agent_version)
    ff_obj.add_panorama_plugin_dep("panorama_plugin", REL_VERSION)
    ff_obj.add_ff_err_msg(err_msg)
    ff_obj.save()

def iot_ff_add_rn_dep(ff_obj, saas_agent_version):
    err_msg = f"device id2 for RN requires at least panorama plugin {REL_VERSION}, saas-agent {saas_agent_version}"
    # Enabled only with the following added dependencies
    ff_obj.add_fw_app_version_dep("saas_agent", saas_agent_version)
    ff_obj.add_panorama_plugin_dep("panorama_plugin", REL_VERSION)
    ff_obj.add_ff_err_msg(err_msg)
    ff_obj.save()

def iot_ff_check(ff_rn, ff_mu):
    assert ff_rn.rel_version == REL_VERSION
    assert ff_rn.flag_type == 'compatibility'
    assert ff_rn.err_message == f"device id2 for RN requires at least panorama plugin {REL_VERSION}, saas-agent {RN_SA_VERSION}"
    assert ff_rn.fw_app_version["saas_agent"] == RN_SA_VERSION
    assert ff_rn.panorama_plugin_dep['panorama_plugin'] == REL_VERSION

    assert ff_mu.rel_version == REL_VERSION
    assert ff_mu.flag_type == 'compatibility'
    assert ff_mu.err_message ==  f"device id2 for MU requires at least ami {MU_AMI_REL_VERSION}, panorama plugin {REL_VERSION}, saas-agent {MU_SA_VERSION}"
    assert ff_mu.mu_ami_dep_version == MU_AMI_REL_VERSION
    assert ff_mu.fw_app_version["saas_agent"] == MU_SA_VERSION
    assert ff_mu.panorama_plugin_dep['panorama_plugin'] == REL_VERSION

def test_device_id2():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_device_id2")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/test_device_id2.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        apps = ['plugin']
        for app_name in apps:
            # IOT RN compatibility Feature Flag
            device_id_rn_ff_plugin = FeatureFlag(name="device_id2_rn",
                                            app=app_name,
                                            rel_version='6.0.0',
                                            feature='enable_device_id2',
                                            flag_type='compatibility',
                                            logger=logger)
            iot_ff_add_rn_dep(device_id_rn_ff_plugin, RN_SA_VERSION)

            # IOT MU compatibility  Feature Flag
            device_id_mu_ff_plugin = FeatureFlag(name="device_id2_mu",
                                            app=app_name,
                                            rel_version='6.0.0',
                                            feature='enable_device_id2',
                                            flag_type='compatibility',
                                            logger=logger)
            iot_ff_add_mu_dep(device_id_mu_ff_plugin, MU_AMI_REL_VERSION, MU_SA_VERSION)

            iot_ff_check(device_id_rn_ff_plugin, device_id_mu_ff_plugin)