import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def tls_v13_support_dep(obj):
    # Enabled only with the following added dependencies
    obj.add_ami_version_dep("11.2.0")
    obj.add_fw_app_version_dep("saas_agent", "5.1.0")
    obj.add_ff_err_msg("tls version 1.3 support requires at least a 11.2.0 dataplane, "
                                            "5.1 Saas Agent")
    obj.save()

def check_tls_v13_support_dep(obj):
    assert obj.ami_dep_version == "11.2.0"
    assert obj.fw_app_version["saas_agent"] == "5.1.0"
    assert obj.flag_type == 'compatibility'
    assert obj.err_message == 'tls version 1.3 support requires at least a 11.2.0 dataplane, 5.1 Saas Agent'

def test_tls_v13_support_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_tls_v13_support_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/tls_v13_support_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        tls_v13_support_ff_agent = FeatureFlag(name='tls_v13_support',
                                            app='saas_agent',
                                            rel_version='5.1.0',
                                            feature='tls_v13_support',
                                            flag_type='compatibility',
                                            logger=logger)

        tls_v13_support_dep(tls_v13_support_ff_agent)
        check_tls_v13_support_dep(tls_v13_support_ff_agent)

