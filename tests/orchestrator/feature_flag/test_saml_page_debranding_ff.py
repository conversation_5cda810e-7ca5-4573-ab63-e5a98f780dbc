from mock_feature_flag import *
import logging
import sys
import os
import warnings
from utils import feature_flag_declarations
ffd_dir = os.path.dirname(feature_flag_declarations.__file__)
path = os.path.join(ffd_dir,sorted([ name for name in os.listdir(ffd_dir)
                                     if os.path.isdir(os.path.join(ffd_dir, name))
                                     and name[0].isdigit()])[-1])
sys.path.append(path)

class TestSamlPageDebrandingFF:
    @patch.object(logging.FileHandler, "emit")
    @patch.object(logging.FileHandler, "_open")
    @patch("libs.feature_flags.feature_flags.dbconn.execute_query")
    def test_build_saml_page_debranding_ff(self, patched_dbconn, fh_open, fh_emit):
            logger = logging.getLogger("test_build_saml_page_debranding_ff")
            logger.setLevel(logging.INFO)
            
            try:
                import saml_page_debranding_ff
            except ModuleNotFoundError:
                war = "saml_page_debranding feature flag not defined for latest infra"
                warnings.warn(war)
                logger.error(war)
                print(war)