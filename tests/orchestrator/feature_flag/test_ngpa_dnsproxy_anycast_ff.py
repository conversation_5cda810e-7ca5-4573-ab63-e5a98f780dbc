import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'ngpa_dnsproxy_anycast_ff'

def test_ngpa_dnsproxy_anycast_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_ngpa_dnsproxy_anycast_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/ngpa_dnsproxy_anycast_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        supported_vers = ['5.1.0', '5.2.0', '6.0.0', '6.1.0']
        for ver in supported_vers:
            ngpa_dnsproxy_anycast_ff = FeatureFlag(name=FF_NAME,
                                              app='saas_agent',
                                              rel_version=ver,
                                              feature=FF_NAME,
                                              flag_type='sre',
                                              logger=logger)

            ngpa_dnsproxy_anycast_ff.add_dev_sre_default_flag_value(0)
            ngpa_dnsproxy_anycast_ff.add_ff_err_msg("ngpa_dnsproxy_anycast feature is disabled by default for this tenant")

            assert ngpa_dnsproxy_anycast_ff.sre_dev_default_flag_value == 0
            assert ngpa_dnsproxy_anycast_ff.rel_version == ver
            assert ngpa_dnsproxy_anycast_ff.flag_name == FF_NAME
            assert ngpa_dnsproxy_anycast_ff.err_message == "ngpa_dnsproxy_anycast feature is disabled by default for this tenant"
