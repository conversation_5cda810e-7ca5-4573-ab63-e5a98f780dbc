import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'sc_nat_feature_flag'

def sc_nat_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_ami_version_dep("10.1.3")
        ff_obj.add_panorama_plugin_dep("panorama_plugin", "3.2.0")
        ff_obj.add_fw_app_version_dep("saas_agent", "3.1.0")
        ff_obj.add_ff_err_msg("SC-NAT requires at least 3.2 plugin, 3.1 saas agent, and 10.1.3 ami")
        ff_obj.save()
        
def test_sc_nat_ff_add_dep_400():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_sc_nat_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/sc_nat_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        sc_nat_ff = FeatureFlag(name=FF_NAME,
                                    app='plugin',
                                    rel_version='4.0.0',
                                    feature=FF_NAME,
                                    flag_type='compatibility',
                                    logger=logger)
        
        sc_nat_ff_add_dep(sc_nat_ff)
        assert sc_nat_ff.ami_dep_version == "10.1.3"
        assert sc_nat_ff.panorama_plugin_dep['panorama_plugin'] == "3.2.0"
        assert sc_nat_ff.fw_app_version["saas_agent"] == "3.1.0"
        assert sc_nat_ff.flag_type == 'compatibility'
        assert sc_nat_ff.err_message == "SC-NAT requires at least 3.2 plugin, 3.1 saas agent, and 10.1.3 ami"

def test_sc_nat_ff_add_dep_401():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_sc_nat_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/sc_nat_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        sc_nat_ff = FeatureFlag(name=FF_NAME,
                                    app='plugin',
                                    rel_version='4.1.0',
                                    feature=FF_NAME,
                                    flag_type='compatibility',
                                    logger=logger)

        sc_nat_ff_add_dep(sc_nat_ff)
        assert sc_nat_ff.ami_dep_version == "10.1.3"
        assert sc_nat_ff.panorama_plugin_dep['panorama_plugin'] == "3.2.0"
        assert sc_nat_ff.fw_app_version["saas_agent"] == "3.1.0"
        assert sc_nat_ff.flag_type == 'compatibility'
        assert sc_nat_ff.err_message == "SC-NAT requires at least 3.2 plugin, 3.1 saas agent, and 10.1.3 ami"

def test_sc_nat_ff_add_dep_410():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_sc_nat_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/sc_nat_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        sc_nat_ff = FeatureFlag(name=FF_NAME,
                                    app='plugin',
                                    rel_version='4.1.0',
                                    feature=FF_NAME,
                                    flag_type='compatibility',
                                    logger=logger)

        sc_nat_ff_add_dep(sc_nat_ff)
        assert sc_nat_ff.ami_dep_version == "10.1.3"
        assert sc_nat_ff.panorama_plugin_dep['panorama_plugin'] == "3.2.0"
        assert sc_nat_ff.fw_app_version["saas_agent"] == "3.1.0"
        assert sc_nat_ff.flag_type == 'compatibility'
        assert sc_nat_ff.err_message == "SC-NAT requires at least 3.2 plugin, 3.1 saas agent, and 10.1.3 ami"

def test_sc_nat_ff_add_dep_420():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_sc_nat_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/sc_nat_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        sc_nat_ff = FeatureFlag(name=FF_NAME,
                                    app='plugin',
                                    rel_version='4.2.0',
                                    feature=FF_NAME,
                                    flag_type='compatibility',
                                    logger=logger)

        sc_nat_ff_add_dep(sc_nat_ff)
        assert sc_nat_ff.ami_dep_version == "10.1.3"
        assert sc_nat_ff.panorama_plugin_dep['panorama_plugin'] == "3.2.0"
        assert sc_nat_ff.fw_app_version["saas_agent"] == "3.1.0"
        assert sc_nat_ff.flag_type == 'compatibility'
        assert sc_nat_ff.err_message == "SC-NAT requires at least 3.2 plugin, 3.1 saas agent, and 10.1.3 ami"
