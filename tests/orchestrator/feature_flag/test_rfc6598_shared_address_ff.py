import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def build_rfc6598_ff(app_name, version, logger):
    feature_arguments = {"green_field_only": True}
    rfc6598_shared_address_ff = FeatureFlag(name='rfc6598_shared_address',
                                          app=app_name,
                                          rel_version=version,
                                          feature='rfc6598_shared_address',
                                          flag_type='dev',
                                          logger=logger)
    rfc6598_shared_address_ff.add_dev_sre_default_flag_value(0)
    rfc6598_shared_address_ff.add_feature_arguments(feature_arguments)
    rfc6598_shared_address_ff.add_ff_err_msg("RFC 6598 shared address feature for this tenant is not set. Use customer setting.")
    rfc6598_shared_address_ff.save()
    assert rfc6598_shared_address_ff.flag_name == "rfc6598_shared_address"
    assert rfc6598_shared_address_ff.app == app_name
    assert rfc6598_shared_address_ff.rel_version == version
    assert rfc6598_shared_address_ff.feature_arguments == json.dumps(feature_arguments)


def test_rfc6598_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_cloud_userid_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/'cloud_userid_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        try:
           build_rfc6598_ff('saas_agent', '5.2.1', logger)
           build_rfc6598_ff('plugin', '5.2.1', logger)
        except (ffe.MissingRequiredAttributeException, ffe.BadValueException, ffe.InvalidOptionException, ffe.FeatureFlagSaveDBException) as e:
           logger.error(f"Exception: {str(e)}")
           raise
