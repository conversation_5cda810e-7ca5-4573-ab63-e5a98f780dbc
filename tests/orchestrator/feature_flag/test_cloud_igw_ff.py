import logging
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'cloud_igw'

def cloud_igw_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_ami_version_dep("10.2.4")
        ff_obj.add_panorama_plugin_dep("panorama_plugin", "5.1.0")
        ff_obj.add_fw_app_version_dep("saas_agent", "5.0.0")
        ff_obj.add_ff_err_msg("Cloud IGW feature requires at least 5.0.0 saas agent, 5.1.0 panorama plugin, 10.2.4 ami.")
        ff_obj.save()
        
def test_cloud_igw_compatibility_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_cloud_igw_compatibility_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/cloud_igw_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        cloud_igw_ff = FeatureFlag(name=FF_NAME,
                                    app='saas-agent',
                                    rel_version='5.0.0',
                                    feature=FF_NAME,
                                    flag_type='compatibility',
                                    logger=logger)
        
        cloud_igw_ff_add_dep(cloud_igw_ff)
        assert cloud_igw_ff.ami_dep_version == "10.2.4"
        assert cloud_igw_ff.panorama_plugin_dep['panorama_plugin'] == "5.1.0"
        assert cloud_igw_ff.fw_app_version["saas_agent"] == "5.0.0"
        assert cloud_igw_ff.flag_type == 'compatibility'
        assert cloud_igw_ff.err_message == "Cloud IGW feature requires at least 5.0.0 saas agent, 5.1.0 panorama plugin, 10.2.4 ami."

def test_cloud_igw_sre_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_cloud_igw_sre_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/cloud_igw_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        cloud_igw_ff = FeatureFlag(name=FF_NAME,
                                            app='plugin',
                                            rel_version='5.1.0',
                                            feature=FF_NAME,
                                            flag_type='sre',
                                            logger=logger)

        cloud_igw_ff.add_dev_sre_default_flag_value(0)
        cloud_igw_ff.add_ff_err_msg("Cloud IGW feature is disabled for this tenant")
        cloud_igw_ff.save()

        assert cloud_igw_ff.sre_dev_default_flag_value == 0
        assert cloud_igw_ff.err_message == "Cloud IGW feature is disabled for this tenant"
