import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'ep_enable_oci_regions'
ERR_MSG = "Explicit Proxy in Oracle Cloud requires minimum 6.0.0 Saas Agent and 6.0.0 Envoy Proxy versions"

def ep_oci_regions_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_eproxy_outside_panos_dep(1)
        ff_obj.add_fw_app_version_dep("saas_agent", "6.0.0")
        ff_obj.add_proxy_ami_version_dep("6.0.0")
        ff_obj.add_ff_err_msg("Explicit Proxy in Oracle Cloud requires minimum 6.0.0 Saas Agent and 6.0.0 Envoy Proxy versions")
        ff_obj.save()
        
def test_ep_oci_regions_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_ep_oci_regions_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/'ep_oci_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['plugin']
        for app_name in apps:
            ep_oci_regions_ff = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='6.0.0',
                                        feature=FF_NAME,
                                        flag_type='compatibility',
                                        logger=logger)
            ep_oci_regions_ff_add_dep(ep_oci_regions_ff)
            assert ep_oci_regions_ff.rel_version == "6.0.0"
            assert ep_oci_regions_ff.flag_type == 'compatibility'
            assert ep_oci_regions_ff.err_message == ERR_MSG
            assert ep_oci_regions_ff.eproxy_outside_panos == 1
            assert ep_oci_regions_ff.proxy_ami_version_dep == "6.0.0"
            assert ep_oci_regions_ff.fw_app_version["saas_agent"] == "6.0.0"