import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'mu_normalization_enable'

def test_mu_count_normalization_feature_flag():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_mu_count_normalization_feature_flag")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/mu_normalization_enable_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['infra', 'saas_agent']
        for app_name in apps:
            mu_count_normalization_feature_flag = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='4.0.0',
                                        feature=FF_NAME,
                                        flag_type='dev',
                                        logger=logger)
            mu_count_normalization_feature_flag.add_ff_err_msg(
                "mu_normalization_enable Feature enabled for this tenant")

            assert mu_count_normalization_feature_flag.rel_version == "4.0.0"
            assert mu_count_normalization_feature_flag.flag_name == 'mu_normalization_enable'
            assert mu_count_normalization_feature_flag.err_message == "mu_normalization_enable Feature enabled for this tenant"