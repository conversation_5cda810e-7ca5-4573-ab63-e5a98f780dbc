import mock_imports_orchestrator
import logging
from unittest import TestCase
from unittest.mock import patch
from libs.feature_flags.feature_flags import FeatureFlag
from libs.feature_flags import feature_flags_exceptions as ffe
import pytest

logger = logging.getLogger()

ff1 = FeatureFlag(name='append_end_token_to_domains_feature_flag',
                 app='plugin',
                 rel_version='4.1.0',
                 feature='append_end_token_to_domains_feature_flag',
                 flag_type='dev',
                 logger=logger)

ff2 = FeatureFlag(name='append_end_token_to_domains_feature_flag',
                 app='plugin',
                 rel_version='4.1.0',
                 feature='append_end_token_to_domains_feature_flag',
                 flag_type='compatibility',
                 logger=logger)

class TestFeatureFlags4point1:

    def test_can_add_dev_sre_default_flag_value(self):

        ff1.sre_dev_default_flag_value = 0
        ff1.add_dev_sre_default_flag_value(1)
        actual = ff1.sre_dev_default_flag_value
        assert actual == 1

    def test_add_ami_version_dep(self):
        self.flag_type = 'compatibility'
        ff2.add_ami_version_dep("10.0.8")
        actual = ff2.ami_dep_version
        assert actual == "10.0.8"

    def test_add_ami_version_dep_exception(self, caplog):
        caplog.set_level(logging.INFO)
        with pytest.raises(ffe.InvalidOptionException):
            ff1.add_ami_version_dep("10.0.8")
            assert " Cannot set dependency flag for non compatibility type of flag" in caplog.text

    def test_add_fw_app_version_dep(self):
        ff2.add_fw_app_version_dep("saas_agent", "2.2.0")
        actual = ff2.fw_app_version["saas_agent"]
        assert actual == "2.2.0"

    def test_add_fw_app_version_dep_exception(self, caplog):
        caplog.set_level(logging.INFO)
        with pytest.raises(ffe.InvalidOptionException):
            ff1.add_fw_app_version_dep("saas_agent", "2.2.0")
            assert "Cannot set dependency flag for non compatibility type of flag" in caplog.text

    def test_add_panorama_plugin_dep(self):
        ff2.add_panorama_plugin_dep("panorama_plugin", "3.0.0")
        actual = ff2.panorama_plugin_dep["panorama_plugin"]
        assert actual == "3.0.0"

    def test_add_panorama_plugin_dep_exception(self, caplog):
        caplog.set_level(logging.INFO)
        with pytest.raises(ffe.InvalidOptionException):
            ff1.add_fw_app_version_dep("panorama_plugin", "3.0.0")
            assert "Cannot set dependency flag for non compatibility type of flag" in caplog.text

    def test_add_ff_err_msg(self):
        ff2.add_ff_err_msg("Append End Token To Domains requires at least a 10.0.8 Dataplane, 2.2 Saas Agent and 3.0 Cloud Services Plugin")
        actual = ff2.err_message
        assert actual == "Append End Token To Domains requires at least a 10.0.8 Dataplane, 2.2 Saas Agent and 3.0 Cloud Services Plugin"


