import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'ciam_feature_flag_sre'

def test_ciam_ff_sre_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_ciam_ff_sre_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/ciam_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        ciam_ff = FeatureFlag(name=FF_NAME,
                                    app='saas_agent',
                                    rel_version='5.1.0',
                                    feature='ciam',
                                    flag_type='sre',
                                    logger=logger)
        
        ciam_ff.add_dev_sre_default_flag_value(0)
        ciam_ff.add_ff_err_msg("PA CIAM SRE disabled by default")
        ciam_ff.save()

        assert ciam_ff.sre_dev_default_flag_value == 0
        assert ciam_ff.err_message == "PA CIAM SRE disabled by default"
