import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'adns_resolver_ff'

def test_adns_resolver_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_adns_resolver_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/adns_resolver_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        cs_adns_resolver_ff = FeatureFlag(name=FF_NAME,
                                app='plugin',
                                rel_version="6.0.0",
                                feature=FF_NAME,
                                flag_type='sre',
                                logger=logger)

        cs_adns_resolver_ff.add_dev_sre_default_flag_value(0)
        cs_adns_resolver_ff.add_ff_err_msg("adns_resolver_ff is disabled by default.")

        assert cs_adns_resolver_ff.rel_version == "6.0.0"
        assert cs_adns_resolver_ff.flag_name == "adns_resolver_ff"
        assert cs_adns_resolver_ff.flag_type == 'sre'
        assert cs_adns_resolver_ff.err_message == "adns_resolver_ff is disabled by default."