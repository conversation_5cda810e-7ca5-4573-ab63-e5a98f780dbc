from unittest.mock import MagicMock
import sys
import os
import pytest

sys.modules['jwt'] = MagicMock()
sys.modules['pem'] = MagicMock()
sys.modules['urllib.request'] = MagicMock()
sys.modules['urllib.error'] = MagicMock()
sys.modules['urllib.parse']= MagicMock()
sys.modules['cryptography']= MagicMock()
sys.modules['cryptography.hazmat.primitives']= MagicMock()
sys.modules['cryptography.hazmat.backends']= MagicMock()
sys.modules['OpenSSL']= MagicMock()
sys.modules['OpenSSL.crypto.X509']= MagicMock()
sys.modules['pyauth.PanAPIGatewayAuthorizer'] = MagicMock()
os.environ['AWS_REGION'] = "mock-aws-region"
os.environ['r53_account'] = "mock-r53_account"
os.environ['acct_id'] = " test"

sys.modules['google'] = MagicMock()
sys.modules['google.oauth2'] = MagicMock()
sys.modules['googleapiclient'] = MagicMock()
sys.modules['googleapiclient.discovery'] = MagicMock()
sys.modules['boto3'] = MagicMock()
sys.modules['boto3.dynamodb'] = MagicMock()
sys.modules['boto3.dynamodb.conditions'] = MagicMock()
sys.modules['okyo_int'] = MagicMock()
sys.modules['okyo_int.okyo_util'] = MagicMock()
#sys.modules['common.shared.utils'] = MagicMock()
sys.modules['pymysql'] = MagicMock()
sys.modules['retry'] = MagicMock()
sys.modules['retry.api'] = MagicMock()
sys.modules['pycurl'] = MagicMock()
sys.modules['influxdb'] = MagicMock()
sys.modules['netaddr'] = MagicMock()
sys.modules['botocore'] = MagicMock()
sys.modules['botocore.client'] = MagicMock()
sys.modules['botocore.exceptions'] = MagicMock()
sys.modules['queryserv.pacparser'] = MagicMock()
sys.modules['yaml'] = MagicMock()
sys.modules['yaml.loader'] = MagicMock()
sys.modules['ns1'] = MagicMock()
sys.modules['ns1.rest'] = MagicMock()
sys.modules['ns1.rest.errors'] = MagicMock()
sys.modules['google.cloud'] = MagicMock()
sys.modules['google.api_core'] = MagicMock()
sys.modules['google.cloud.spanner_v1'] = MagicMock()
sys.modules['requests.utils'] = MagicMock()
sys.modules['libs.cfg'] = MagicMock()
sys.modules['dbconn'] = MagicMock()
sys.modules['cfg'] = MagicMock()
sys.modules['libs.common.shared.dbconn'] = MagicMock()
sys.modules['libs.common.shared.py3_utils'] = MagicMock()
sys.modules['google.protobuf'] = MagicMock()
sys.modules['google.protobuf.internal'] = MagicMock()
sys.modules["uvicorn"] = MagicMock()
sys.modules["jsonpickle"] = MagicMock()
sys.modules["mysql"] = MagicMock()
sys.modules["mysql.connector"] = MagicMock()
sys.modules['libs.common.shared.pba_utils'] = MagicMock()
sys.modules['ipam_plugin'] = MagicMock()
sys.modules['ipam_factory'] = MagicMock()
sys.modules['ngpa_extern'] = MagicMock()
sys.modules['ngpa_extern.external_utils'] = MagicMock()
sys.modules["libs.common.shared.model.privInstanceMgmtTableGlobal"] = MagicMock()
