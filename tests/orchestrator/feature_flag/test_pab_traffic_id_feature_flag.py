import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'pab_traffic_id_feature_flag'
ERR_MSG = "Prisma Access Browser Traffic Identification feature requires at least 6.0.0 saas agent, 12.2.5 panos ami, 5.2.0 Envoy Proxy, and EProxy Outside Panos feature enabled"

def pab_traffic_id_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_eproxy_outside_panos_dep(1)
        ff_obj.add_fw_app_version_dep("saas_agent", "6.0.0")
        ff_obj.add_swg_ami_dep_version("12.2.5")
        ff_obj.add_proxy_ami_version_dep("5.2.0")
        ff_obj.add_ff_err_msg("Prisma Access Browser Traffic Identification feature requires at least 6.0.0 saas agent, 12.2.5 panos ami, 5.2.0 Envoy Proxy, and EProxy Outside Panos feature enabled")
        ff_obj.save()
        
def test_pab_traffic_id_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_pab_traffic_id_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/'pab_traffic_id_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['plugin']
        for app_name in apps:
            pab_traffic_id_ff = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='6.0.0',
                                        feature=FF_NAME,
                                        flag_type='compatibility',
                                        logger=logger)
            pab_traffic_id_ff_add_dep(pab_traffic_id_ff)
            assert pab_traffic_id_ff.rel_version == "6.0.0"
            assert pab_traffic_id_ff.flag_type == 'compatibility'
            assert pab_traffic_id_ff.err_message == ERR_MSG
            assert pab_traffic_id_ff.eproxy_outside_panos == 1
            assert pab_traffic_id_ff.swg_ami_dep_version == "12.2.5"
            assert pab_traffic_id_ff.proxy_ami_version_dep == "5.2.0"
            assert pab_traffic_id_ff.fw_app_version["saas_agent"] == "6.0.0"