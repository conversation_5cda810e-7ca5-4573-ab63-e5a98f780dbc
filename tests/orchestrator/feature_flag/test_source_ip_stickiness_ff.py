import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'source_ip_stickiness_ff'

def test_source_ip_stickiness_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_source_ip_stickiness_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/source_ip_stickiness_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        supported_vers = ['5.1.0', '5.2.0', '6.0.0', '6.1.0']
        for ver in supported_vers:
            source_ip_stickiness_ff = FeatureFlag(name=FF_NAME,
                                              app='saas_agent',
                                              rel_version=ver,
                                              feature=FF_NAME,
                                              flag_type='sre',
                                              logger=logger)

            source_ip_stickiness_ff.add_dev_sre_default_flag_value(1)
            source_ip_stickiness_ff.add_ff_err_msg("source_ip_stickiness feature is enabled by default for this tenant")

            assert source_ip_stickiness_ff.sre_dev_default_flag_value == 1
            assert source_ip_stickiness_ff.rel_version == ver
            assert source_ip_stickiness_ff.flag_name == FF_NAME
            assert source_ip_stickiness_ff.err_message == "source_ip_stickiness feature is enabled by default for this tenant"
