import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'ep_multi_urlpage_ff'
ERR_MSG = "EP Multiple URL Response Page feature requires at least 6.1.0 Saas Agent, EProxy Outside Panos feature, 6.1.0 proxy ami, 11.2.7 ami"

def ep_multi_urlpage_ff_add_dep(ff_obj):
    # Enabled only with the following added dependencies
    ff_obj.add_eproxy_outside_panos_dep(1)
    ff_obj.add_fw_app_version_dep("saas_agent", "6.1.0")
    ff_obj.add_proxy_ami_version_dep("6.1.0") 
    ff_obj.add_swg_ami_dep_version("11.2.7")
    ff_obj.add_ff_err_msg(ERR_MSG)
    ff_obj.save()

def test_ep_multi_urlpage_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_ep_multi_urlpage_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/ep_multi_urlpage_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_infra', 'saas_agent', 'plugin']
        for app_name in apps:
            ep_multi_urlpage_ff = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='6.1.0',
                                        feature=FF_NAME,
                                        flag_type='compatibility',
                                        logger=logger)
            ep_multi_urlpage_ff_add_dep(ep_multi_urlpage_ff)
            assert ep_multi_urlpage_ff.rel_version == "6.1.0"
            assert ep_multi_urlpage_ff.flag_type == 'compatibility'
            assert ep_multi_urlpage_ff.err_message == ERR_MSG
            assert ep_multi_urlpage_ff.eproxy_outside_panos == 1
            assert ep_multi_urlpage_ff.proxy_ami_version_dep == "6.1.0"
            assert ep_multi_urlpage_ff.fw_app_version["saas_agent"] == "6.1.0"
            assert ep_multi_urlpage_ff.swg_ami_dep_version == "11.2.7"