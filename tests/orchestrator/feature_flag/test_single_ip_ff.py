import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile



def test_single_ip_ff():
    with tempfile.TemporaryDirectory() as temp_dir:
        FF_NAME = 'single-ip'
        logger = logging.getLogger("single_ip_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/'single_ip_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        single_ip = FeatureFlag(name=FF_NAME,
                                app='plugin',
                                rel_version='6.0.0',
                                feature=FF_NAME,
                                flag_type='sre',
                                logger=logger)

        assert single_ip.rel_version == '6.0.0'
        assert single_ip.flag_type == 'sre'
        assert single_ip.sre_dev_default_flag_value == 0





