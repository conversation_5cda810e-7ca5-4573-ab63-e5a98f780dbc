import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'ztna_feature_flag'

def ztna_ff_add_advanced_dep(ff_obj, ver):
        # Enabled only with the following added dependencies
        dependent_ff = ["test-ff-dependency"]
        deployment_type = {"fedramp-high": {"value": 5},"fedramp-il5": {"value": 6}}
        compute_region =  {"10": {"value": 2}, "20": {"value": 2},"all" :{"value": 3}}
        ff_obj.add_ami_version_dep("10.2.4")
        ff_obj.add_panorama_plugin_dep("panorama_plugin", ver)
        ff_obj.add_fw_app_version_dep("saas_agent", "5.0.0")
        ff_obj.add_featureflag_dep(dependent_ff)
        ff_obj.add_deployment_dep(deployment_type)
        ff_obj.add_compute_region_dep(compute_region)
        ff_obj.add_ff_err_msg(f"ZTNA feature requires at least 5.0.0 saas agent, {ver} panorama plugin, 10.2.4 ami.")
        ff_obj.save()

def test_ztna_ff_add_dep_advance():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_ztna_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/ztna_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        supported_vers = ["5.2.0", "5.1.0", "5.0.0", "4.2.0", "4.1.0", "4.0.0"]
        for ver in supported_vers:
            ztna_ff = FeatureFlag(name=FF_NAME,
                                    app='plugin',
                                    rel_version=ver,
                                    feature=FF_NAME,
                                    flag_type='compatibility',
                                    logger=logger)

            ztna_ff_add_advanced_dep(ztna_ff, ver)
            assert ztna_ff.ami_dep_version == "10.2.4"
            assert ztna_ff.panorama_plugin_dep['panorama_plugin'] == ver
            assert ztna_ff.fw_app_version["saas_agent"] == "5.0.0"
            assert ztna_ff.flag_type == 'compatibility'
            assert ztna_ff.err_message == f"ZTNA feature requires at least 5.0.0 saas agent, {ver} panorama plugin, 10.2.4 ami."
            assert len(ztna_ff.deployment_type.keys()) == 2
            assert len(ztna_ff.compute_region_id.keys()) == 3
            assert len(ztna_ff.feature_flag_dep.keys()) == 1

def ztna_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_ami_version_dep("10.2.4")
        ff_obj.add_panorama_plugin_dep("panorama_plugin", "4.0.0")
        ff_obj.add_fw_app_version_dep("saas_agent", "4.0.0")
        ff_obj.add_ff_err_msg("ZTNA feature requires at least 4.0.0 saas agent, 4.0.0 panorama plugin, 10.2.4 ami.")
        ff_obj.save()
        
def test_ztna_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_ztna_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/ztna_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        ztna_ff = FeatureFlag(name=FF_NAME,
                                    app='plugin',
                                    rel_version='4.0.0',
                                    feature=FF_NAME,
                                    flag_type='compatibility',
                                    logger=logger)
        
        ztna_ff_add_dep(ztna_ff)
        assert ztna_ff.ami_dep_version == "10.2.4"
        assert ztna_ff.panorama_plugin_dep['panorama_plugin'] == "4.0.0"
        assert ztna_ff.fw_app_version["saas_agent"] == "4.0.0"
        assert ztna_ff.flag_type == 'compatibility'
        assert ztna_ff.err_message == "ZTNA feature requires at least 4.0.0 saas agent, 4.0.0 panorama plugin, 10.2.4 ami."
