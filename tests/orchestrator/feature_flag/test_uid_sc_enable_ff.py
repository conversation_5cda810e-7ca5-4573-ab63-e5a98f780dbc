import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def test_uid_sc_enable_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_uid_sc_enable_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/test_uid_sc_enable_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        supported_vers = ['4.1.0', '4.2.0']

        for ver in supported_vers:
            uid_sc_enable_ff = FeatureFlag(name='uid-sc-enabled',
                                                app='plugin',
                                                rel_version=ver,
                                                feature='uid-sc-enabled',
                                                flag_type='sre',
                                                logger=logger)

            uid_sc_enable_ff.add_dev_sre_default_flag_value(0)
            uid_sc_enable_ff.add_ff_err_msg(f"User ID SC feature requires at least {ver} panorama plugin")
            uid_sc_enable_ff.save()

            assert uid_sc_enable_ff.sre_dev_default_flag_value == 0
            assert uid_sc_enable_ff.err_message == f"User ID SC feature requires at least {ver} panorama plugin"
