import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'sam_auth_response_customization'
FF_SRE_NAME = 'sam_auth_response_customization_sre'
FEATURE_NAME = 'sam_auth_response_customization'
REL_VERSION = '5.2.0'
AMI_REL_VERSION = "10.2.4"
ERR_MSG = "sam_auth_response_customization requires at least ami {AMI_REL_VERSION}, panorama plugin {REL_VERSION}, saas-agent {REL_VERSION}"


def saml_auth_response_customization_ff_add_dep(ff_obj):
    # Enabled only with the following added dependencies
    ff_obj.add_ami_version_dep(AMI_REL_VERSION)
    ff_obj.add_fw_app_version_dep("saas_agent", REL_VERSION)
    ff_obj.add_panorama_plugin_dep("panorama_plugin", REL_VERSION)
    ff_obj.add_ff_err_msg(ERR_MSG)
    ff_obj.save()

def test_saml_auth_response_customization_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_saml_auth_response_customization_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/'saml_auth_response_customization_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        
        apps = ['plugin', 'saas_agent']

        for app_name in apps:
            saml_auth_response_customization_ff_plugin = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version=REL_VERSION,
                                        feature=FEATURE_NAME,
                                        flag_type='compatibility',
                                        logger=logger)

            saml_auth_response_customization_ff_add_dep(saml_auth_response_customization_ff_plugin)

            assert saml_auth_response_customization_ff_plugin.flag_name == FF_NAME
            assert saml_auth_response_customization_ff_plugin.feature_name == FEATURE_NAME
            assert saml_auth_response_customization_ff_plugin.app == app_name
            assert saml_auth_response_customization_ff_plugin.rel_version == REL_VERSION
            assert saml_auth_response_customization_ff_plugin.flag_type == 'compatibility'
            assert saml_auth_response_customization_ff_plugin.err_message == ERR_MSG
            assert saml_auth_response_customization_ff_plugin.ami_dep_version == AMI_REL_VERSION
            assert saml_auth_response_customization_ff_plugin.panorama_plugin_dep['panorama_plugin'] == REL_VERSION

            saml_auth_response_customization_ff_plugin_sre = FeatureFlag(name=FF_SRE_NAME,
                                            app=app_name,
                                            rel_version=REL_VERSION,
                                            feature=FEATURE_NAME,
                                            flag_type='sre',
                                            logger=logger)

            # default value 1: enabled
            saml_auth_response_customization_ff_plugin_sre.add_dev_sre_default_flag_value(0)
            saml_auth_response_customization_ff_plugin_sre.add_ff_err_msg(
                "saml_auth_response_customization SRE is disabled for this tenant")
            saml_auth_response_customization_ff_plugin_sre.save()

            assert saml_auth_response_customization_ff_plugin_sre.flag_name == FF_SRE_NAME
            assert saml_auth_response_customization_ff_plugin_sre.app == app_name
            assert saml_auth_response_customization_ff_plugin_sre.rel_version == REL_VERSION
            assert saml_auth_response_customization_ff_plugin_sre.feature_name == FEATURE_NAME
            assert saml_auth_response_customization_ff_plugin_sre.flag_type == 'sre'
            assert saml_auth_response_customization_ff_plugin_sre.sre_dev_default_flag_value == 0
            assert saml_auth_response_customization_ff_plugin_sre.err_message == "saml_auth_response_customization SRE is disabled for this tenant"