import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'tenant_pra_pvt_web_apps_ff'
        
def test_tenant_pra_pvt_web_apps_ff():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_tenant_pra_pvt_web_apps_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/tenant_pra_pvt_web_apps_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_agent']
        for app_name in apps:
            tenant_pra_pvt_web_apps_ff = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='6.1.0',
                                        feature=FF_NAME,
                                        flag_type='sre',
                                        logger=logger)

            assert tenant_pra_pvt_web_apps_ff.rel_version == "6.1.0"
            assert tenant_pra_pvt_web_apps_ff.flag_name == FF_NAME
            assert tenant_pra_pvt_web_apps_ff.app == app_name
            assert tenant_pra_pvt_web_apps_ff.feature_name == FF_NAME
            assert tenant_pra_pvt_web_apps_ff.flag_type == 'sre'
            assert tenant_pra_pvt_web_apps_ff.sre_dev_default_flag_value == 0
