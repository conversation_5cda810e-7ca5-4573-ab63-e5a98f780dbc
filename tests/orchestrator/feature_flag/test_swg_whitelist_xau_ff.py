import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'swg_whitelist_xau_feature_flag'

def whitelist_xau_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_ami_version_dep("10.1.3")
        ff_obj.add_panorama_plugin_dep("panorama_plugin", "3.2.1")
        ff_obj.add_fw_app_version_dep("saas_agent", "3.1.0")
        ff_obj.add_ff_err_msg("Whitelist XAU Checkbox requires at least 3.2.1 plugin, 3.1.0 saas agent, and 10.1.3 ami")
        ff_obj.save()

def test_swg_whitelist_xau_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_swg_whitelist_xau_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/whitelist_xau_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['infra', 'saas_agent']
        for app_name in apps:
            whitelist_xau_ff = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='3.2.1',
                                        feature=FF_NAME,
                                        flag_type='compatibility',
                                        logger=logger)
            whitelist_xau_ff_add_dep(whitelist_xau_ff)
            assert whitelist_xau_ff.ami_dep_version == "10.1.3"
            assert whitelist_xau_ff.panorama_plugin_dep["panorama_plugin"] == "3.2.1"
            assert whitelist_xau_ff.fw_app_version["saas_agent"] == "3.1.0"
            assert whitelist_xau_ff.flag_type == 'compatibility'
            assert whitelist_xau_ff.err_message == "Whitelist XAU Checkbox requires at least 3.2.1 plugin, 3.1.0 saas agent, and 10.1.3 ami"