from mock_feature_flag import *
import traceback
import importlib.util
import sys
import os
import warnings

from utils import feature_flag_declarations
import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

ffd_dir = os.path.dirname(feature_flag_declarations.__file__)
path = os.path.join(ffd_dir,sorted([ name for name in os.listdir(ffd_dir)
                                     if os.path.isdir(os.path.join(ffd_dir, name))
                                     and name[0].isdigit()])[-1])
sys.path.append(path)

FF_NAME = 'disable-simplified-logging-ff'

class TestDisableSimplifiedLogging:
    with tempfile.TemporaryDirectory() as temp_dir:
        # Set up logger for testing
        logger = logging.getLogger("test_disable_simplified_logging_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/disable_simplified_logging_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        
        # Test feature flag creation for both saas_agent and saas_infra
        apps = ['saas_agent', 'saas_infra']
        for app_name in apps:
            disable_simplified_logging_ff = FeatureFlag(name=FF_NAME,
                                           app=app_name,
                                           rel_version='6.1.0',
                                           feature=FF_NAME,
                                           flag_type='sre',
                                           logger=logger)

            # Verify feature flag properties
            assert disable_simplified_logging_ff.rel_version == "6.1.0"
            assert disable_simplified_logging_ff.flag_name == FF_NAME
            assert disable_simplified_logging_ff.app == app_name
            assert disable_simplified_logging_ff.feature_name == FF_NAME
            assert disable_simplified_logging_ff.flag_type == 'sre'
            assert disable_simplified_logging_ff.sre_dev_default_flag_value == 0

