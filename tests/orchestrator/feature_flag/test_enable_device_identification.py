import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def test_enable_device_identification_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_enable_device_identification_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/enable_device_identification_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        device_id_ff = FeatureFlag(name='enable_device_identification',
                                            app='plugin',
                                            rel_version='4.0.0',
                                            feature='enable_device_identification',
                                            flag_type='sre',
                                            logger=logger)

        device_id_ff.add_dev_sre_default_flag_value(0)
        device_id_ff.add_ff_err_msg("enable_device_identification feature flag disabled for this tenant")
        device_id_ff.save()

        assert device_id_ff.sre_dev_default_flag_value == 0
        assert device_id_ff.err_message == "enable_device_identification feature flag disabled for this tenant"
