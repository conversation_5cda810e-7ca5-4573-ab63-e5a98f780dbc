import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile
        
def test_rn_to_ep_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_rn_to_ep_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/rn_to_ep_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        
        rn_to_ep_ff = FeatureFlag(name="rn_to_ep_feature_flag",
                                    app="plugin",
                                    rel_version='4.0.0',
                                    feature="rn_to_ep_feature_flag",
                                    flag_type='compatibility',
                                    logger=logger)

        rn_to_ep_ff.add_ami_version_dep("10.0.8")
        rn_to_ep_ff.add_panorama_plugin_dep("panorama_plugin", "3.0.0")
        rn_to_ep_ff.add_fw_app_version_dep("saas_agent", "2.2.0")
        rn_to_ep_ff.add_ff_err_msg("Remote Networks to Explicit Proxy requires at least 3.0 plugin, 2.2 saas agent, and 10.0.8 ami")

        assert rn_to_ep_ff.ami_dep_version == "10.0.8"
        assert rn_to_ep_ff.fw_app_version["saas_agent"] == "2.2.0"
        assert rn_to_ep_ff.flag_type == 'compatibility'
        assert rn_to_ep_ff.panorama_plugin_dep['panorama_plugin'] == "3.0.0"
        assert rn_to_ep_ff.err_message == "Remote Networks to Explicit Proxy requires at least 3.0 plugin, 2.2 saas agent, and 10.0.8 ami"