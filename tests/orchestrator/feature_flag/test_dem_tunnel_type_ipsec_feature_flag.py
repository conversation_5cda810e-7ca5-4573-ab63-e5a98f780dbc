from mock_feature_flag import *
from unittest.mock import call
import logging
import sys
import os
import warnings
from utils import feature_flag_declarations

ffd_dir = os.path.dirname(feature_flag_declarations.__file__)
path = os.path.join(ffd_dir,sorted([ name for name in os.listdir(ffd_dir)
                                     if os.path.isdir(os.path.join(ffd_dir, name))
                                     and name == "4.2.0"])[0])
sys.path.append(path)

class TestDemTunnelTypeIpsecFF:
    @patch.object(logging.FileHandler, "emit")
    @patch.object(logging.FileHandler, "_open")
    @patch("libs.feature_flags.feature_flags.dbconn.execute_query")
    def test_dem_tunnel_type_ipsec_ff(self, mock_dbconn, mock_fh_open, mock_fh_emit):
        logger = logging.getLogger("dem_tunnel_type_ipsec_feature_flag")
        logger.setLevel(logging.INFO)

        try:
            import dem_tunnel_type_ipsec_feature_flag
        except ModuleNotFoundError:
            war = "dem_tunnel_type_ipsec feature flag not defined for latest infra"
            warnings.warn(war)
            logger.error(war)
            print(war)

        expected_calls = []
        supported_versions = ["3.0.0", "3.1.0", "3.1.1", "3.1.2", "3.2.0", "3.2.1", "4.0.0",
                              "4.0.1", "4.1.0", "4.1.1", "4.2.0"]
        for ver in supported_versions:
            expected_calls.append(
                call("REPLACE INTO feature_flags (flag_name, flag_type, app, feature_name, "
                     "release_version, dependencies, default_value, err_msg, feature_arguments) "
                     "VALUES ('dem_tunnel_type_ipsec', 'sre', 'saas_agent', 'DEM_IPSec_tunnel', '%s', "
                     "'', '0', 'DEM_tunnel_type_ipsec feature flag disabled for this tenant', '')" % 
                     ver, logger=logger))
        mock_dbconn.assert_has_calls(expected_calls)
