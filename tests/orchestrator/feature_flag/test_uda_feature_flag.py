import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'tenant_uda_feature_flag'
ERR_MSG = "tenant_uda_feature_flag requires minimum versions as saas-agent: 5.0.0, saas-infra: 5.0.0, PANOS-AMI: 10.2.8"

def test_enable_query_service_20():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_bce_feature_flag.py")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/tenant_uda_feature_flag.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_infra', 'saas_agent']

        for app_name in apps:
            uda_ff = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='5.0.0',
                                        feature=FF_NAME,
                                        flag_type='compatibility',
                                        logger=logger)
            uda_ff.add_ami_version_dep("10.2.8")
            uda_ff.add_uda_nhproxy_ami_version_dep("5.0.0")
            uda_ff.add_fw_app_version_dep("saas_agent", "5.0.0")
            uda_ff.add_ff_err_msg(ERR_MSG)
            assert uda_ff.ami_dep_version == "10.2.8"
            assert uda_ff.uda_nhproxy_ami_version_dep == '5.0.0'
            assert uda_ff.fw_app_version["saas_agent"] == '5.0.0'
            assert uda_ff.err_message == ERR_MSG

        for app_name in apps:
            uda_sre_ff = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='5.0.0',
                                        feature=FF_NAME,
                                        flag_type='compatibility',
                                        logger=logger)
            uda_sre_ff.add_dev_sre_default_flag_value(0)

            uda_sre_ff.add_ff_err_msg(
                "uda feature is disabled for this tenant")

            assert uda_sre_ff.rel_version == "5.0.0"
            assert uda_sre_ff.flag_name == 'tenant_uda_feature_flag'
            assert uda_sre_ff.sre_dev_default_flag_value == 0
            assert uda_sre_ff.err_message == "uda feature is disabled for this tenant"