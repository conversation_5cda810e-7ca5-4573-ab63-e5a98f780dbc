import mock_imports_orchestrator
import logging
import pytest
from unittest.mock import patch, MagicMock, call
from libs.feature_flags.feature_flags import FeatureFlag
from libs.feature_flags import feature_flags_exceptions as ffe

class TestCiamStaticIpGatewayEdgeLocationFeatureFlag:

    def test_feature_flag_constructor_with_correct_values(self):
        """Test that we can create a feature flag with the expected values"""
        logger = logging.getLogger()
        
        # Create an actual FeatureFlag instance
        ff = FeatureFlag(
            name='ciam_static_ip_gateway_edge_location_ff',
            app='saas_infra',
            rel_version='6.1.0',
            feature='ciam_static_ip_gateway_edge_location',
            flag_type='sre',
            logger=logger
        )
        
        # Verify the properties are set correctly
        assert ff.flag_name == 'ciam_static_ip_gateway_edge_location_ff'
        assert ff.app == 'saas_infra'
        assert ff.rel_version == '6.1.0'
        assert ff.flag_type == 'sre'

    @patch.object(FeatureFlag, 'save')
    def test_ciam_static_ip_gateway_edge_location_flag_functionality(self, mock_save):
        """Test the main functionality of the ciam_static_ip_gateway_edge_location feature flag"""
        logger = logging.getLogger()
        
        # Create the feature flag as the function would
        ff = FeatureFlag(
            name='ciam_static_ip_gateway_edge_location_ff',
            app='saas_infra',
            rel_version='6.1.0',
            feature='ciam_static_ip_gateway_edge_location',
            flag_type='sre',
            logger=logger
        )
        
        # Set the default value and error message as the function would
        ff.add_dev_sre_default_flag_value(0)
        ff.add_ff_err_msg("CIAM Static IP Gateway Edge Location feature disabled by default")
        
        # Save the feature flag
        ff.save()
        
        # Verify save was called
        mock_save.assert_called_once()
        
        # Verify the flag has the expected value
        assert ff.sre_dev_default_flag_value == 0
        assert ff.err_message == "CIAM Static IP Gateway Edge Location feature disabled by default"

    def test_ciam_static_ip_gateway_edge_location_flag_missing_required_attribute(self):
        """Test that an exception is raised when required attributes are missing"""
        with pytest.raises(ffe.MissingRequiredAttributeException):
            # Missing the logger parameter
            FeatureFlag(
                name='ciam_static_ip_gateway_edge_location_ff',
                app='saas_infra',
                rel_version='6.1.0',
                feature='ciam_static_ip_gateway_edge_location',
                flag_type='sre'
            )

    @patch.object(FeatureFlag, 'save')
    def test_ciam_static_ip_gateway_edge_location_flag_db_save_exception(self, mock_save):
        """Test handling of DB save exceptions"""
        logger = MagicMock()
        
        # Make save raise an exception
        mock_save.side_effect = ffe.FeatureFlagSaveDBException("Failed to save to DB")
        
        # Create the feature flag
        ff = FeatureFlag(
            name='ciam_static_ip_gateway_edge_location_ff',
            app='saas_infra',
            rel_version='6.1.0',
            feature='ciam_static_ip_gateway_edge_location',
            flag_type='sre',
            logger=logger
        )
        
        # Try to save and expect an exception
        with pytest.raises(ffe.FeatureFlagSaveDBException):
            ff.save()

    @patch('logging.FileHandler')
    @patch('logging.getLogger')
    def test_log_handler_setup(self, mock_logger, mock_file_handler):
        """Test that logging is set up correctly"""
        # Setup mocks
        mock_logger_instance = MagicMock()
        mock_logger.return_value = mock_logger_instance
        
        mock_handler = MagicMock()
        mock_file_handler.return_value = mock_handler
        
        # Execute the function that sets up logging
        def setup_logging():
            logger = logging.getLogger(__name__)
            logger.setLevel(logging.INFO)
            f_handler = logging.FileHandler('/var/log/pan/ciam_static_ip_gateway_edge_location_ff.log')
            f_handler.setLevel(logging.INFO)
            f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            f_handler.setFormatter(f_format)
            logger.addHandler(f_handler)
            return logger
            
        logger = setup_logging()
        
        # Verify logging setup
        mock_logger.assert_called_once_with(__name__)
        mock_logger_instance.setLevel.assert_called_once_with(logging.INFO)
        mock_file_handler.assert_called_once_with('/var/log/pan/ciam_static_ip_gateway_edge_location_ff.log')
        mock_handler.setLevel.assert_called_once_with(logging.INFO)
        assert isinstance(mock_handler.setFormatter.call_args[0][0], logging.Formatter)
        mock_logger_instance.addHandler.assert_called_once()

    def test_feature_flag_constructor_with_correct_values_plugin(self):
        """Test that we can create a feature flag with the expected values"""
        logger = logging.getLogger()
        
        # Create an actual FeatureFlag instance
        ff = FeatureFlag(
            name='ciam_static_ip_gateway_edge_location_ff',
            app='plugin',
            rel_version='6.1.0',
            feature='ciam_static_ip_gateway_edge_location',
            flag_type='sre',
            logger=logger
        )
        
        # Verify the properties are set correctly
        assert ff.flag_name == 'ciam_static_ip_gateway_edge_location_ff'
        assert ff.app == 'plugin'
        assert ff.rel_version == '6.1.0'
        assert ff.flag_type == 'sre'

    @patch.object(FeatureFlag, 'save')
    def test_ciam_static_ip_gateway_edge_location_flag_functionality_plugin(self, mock_save):
        """Test the main functionality of the ciam_static_ip_gateway_edge_location feature flag"""
        logger = logging.getLogger()
        
        # Create the feature flag as the function would
        ff = FeatureFlag(
            name='ciam_static_ip_gateway_edge_location_ff',
            app='plugin',
            rel_version='6.1.0',
            feature='ciam_static_ip_gateway_edge_location',
            flag_type='sre',
            logger=logger
        )
        
        # Set the default value and error message as the function would
        ff.add_dev_sre_default_flag_value(0)
        ff.add_ff_err_msg("CIAM Static IP Gateway Edge Location feature disabled by default")
        
        # Save the feature flag
        ff.save()
        
        # Verify save was called
        mock_save.assert_called_once()
        
        # Verify the flag has the expected value
        assert ff.sre_dev_default_flag_value == 0
        assert ff.err_message == "CIAM Static IP Gateway Edge Location feature disabled by default"

    def test_ciam_static_ip_gateway_edge_location_flag_missing_required_attribute_plugin(self):
        """Test that an exception is raised when required attributes are missing"""
        with pytest.raises(ffe.MissingRequiredAttributeException):
            # Missing the logger parameter
            FeatureFlag(
                name='ciam_static_ip_gateway_edge_location_ff',
                app='plugin',
                rel_version='6.1.0',
                feature='ciam_static_ip_gateway_edge_location',
                flag_type='sre'
            )

    @patch.object(FeatureFlag, 'save')
    def test_ciam_static_ip_gateway_edge_location_flag_db_save_exception_plugin(self, mock_save):
        """Test handling of DB save exceptions"""
        logger = MagicMock()
        
        # Make save raise an exception
        mock_save.side_effect = ffe.FeatureFlagSaveDBException("Failed to save to DB")
        
        # Create the feature flag
        ff = FeatureFlag(
            name='ciam_static_ip_gateway_edge_location_ff',
            app='plugin',
            rel_version='6.1.0',
            feature='ciam_static_ip_gateway_edge_location',
            flag_type='sre',
            logger=logger
        )
        
        # Try to save and expect an exception
        with pytest.raises(ffe.FeatureFlagSaveDBException):
            ff.save()
