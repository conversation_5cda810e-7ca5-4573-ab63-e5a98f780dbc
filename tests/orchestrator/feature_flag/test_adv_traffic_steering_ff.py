import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME_COMPATIBILITY = 'adv_traffic_steering_ff'
FF_NAME_SRE = 'adv_traffic_steering_ff_sre'
ERR_MSG = 'Advanced traffic steering need ami 11.2.0, saas-agent 5.1.0 and plugin 5.1.0'

def test_adv_traffic_steering_ff():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_adv_traffic_steering_ff.py")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/adv_traffic_steering_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_agent', 'plugin']

        for app_name in apps:
            adv_traffic_steering_ff = FeatureFlag(name=FF_NAME_COMPATIBILITY,
                                        app=app_name,
                                        rel_version='5.1.0',
                                        feature=FF_NAME_COMPATIBILITY,
                                        flag_type='compatibility',
                                        logger=logger)
            adv_traffic_steering_ff.add_ami_version_dep("11.2.0")
            adv_traffic_steering_ff.add_fw_app_version_dep("saas_agent", "5.1.0")
            adv_traffic_steering_ff.add_panorama_plugin_dep("panorama_plugin", "5.1.0")
            adv_traffic_steering_ff.add_ff_err_msg(ERR_MSG)
            assert adv_traffic_steering_ff.ami_dep_version == "11.2.0"
            assert adv_traffic_steering_ff.fw_app_version["saas_agent"] == '5.1.0'
            assert adv_traffic_steering_ff.panorama_plugin_dep['panorama_plugin'] == '5.1.0'
            assert adv_traffic_steering_ff.err_message == ERR_MSG

        for app_name in apps:
            adv_traffic_steering_sre_ff = FeatureFlag(name=FF_NAME_SRE,
                                        app=app_name,
                                        rel_version='5.1.0',
                                        feature=FF_NAME_SRE,
                                        flag_type='sre',
                                        logger=logger)
            adv_traffic_steering_sre_ff.add_dev_sre_default_flag_value(0)

            adv_traffic_steering_sre_ff.add_ff_err_msg(
                "Advanced traffic steering SRE is disabled for this tenant")

            assert adv_traffic_steering_sre_ff.rel_version == "5.1.0"
            assert adv_traffic_steering_sre_ff.flag_name == 'adv_traffic_steering_ff_sre'
            assert adv_traffic_steering_sre_ff.sre_dev_default_flag_value == 0
            assert adv_traffic_steering_sre_ff.err_message == "Advanced traffic steering SRE is disabled for this tenant"
