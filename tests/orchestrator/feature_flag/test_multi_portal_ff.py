from mock_feature_flag import *
from unittest.mock import call
import logging
import sys
import os
import warnings
from utils import feature_flag_declarations
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

ffd_dir = os.path.dirname(feature_flag_declarations.__file__)
path = os.path.join(ffd_dir,sorted([ name for name in os.listdir(ffd_dir)
                                     if os.path.isdir(os.path.join(ffd_dir, name))
                                     and name == "5.0.0"])[0])
sys.path.append(path)


FF_NAME = 'multi_portal'

class TestMultiPortalFF:
    @patch.object(logging.FileHandler, "emit")
    @patch.object(logging.FileHandler, "_open")
    @patch("libs.feature_flags.feature_flags.dbconn.execute_query")
    def test_multi_portal_ff(self, mock_dbconn, mock_fh_open, mock_fh_emit):
        logger = logging.getLogger("multi_portal_ff")
        logger.setLevel(logging.INFO)
        
        try:
            import multi_portal_ff
        except ModuleNotFoundError:
            war = "multi_portal feature flag not defined for latest infra"
            warnings.warn(war)
            logger.error(war)
            print(war)

        expected_calls = []
        supported_versions = [
            "3.2.1",
            "4.0.0",
            "4.1.0",
            "5.0.0"
        ]
        for ver in supported_versions:
            expected_calls.append(
                call("REPLACE INTO feature_flags (flag_name, flag_type, app, feature_name, "
                     "release_version, dependencies, default_value, err_msg, feature_arguments) "
                     "VALUES ('%s', 'sre', 'plugin', '%s', '%s', "
                     "'', '0', 'Multi-Portal feature is disabled for this tenant', '')" % 
                     (FF_NAME, FF_NAME, ver), logger=logger))
        mock_dbconn.assert_has_calls(expected_calls)


    @patch("libs.feature_flags.feature_flags.dbconn.execute_query")
    def test_multi_portal_sre_ff_add_dep(self, mock_dbconn):
        with tempfile.TemporaryDirectory() as temp_dir:
            logger = logging.getLogger("test_multi_portal_sre_ff_add_dep")
            logger.setLevel(logging.INFO)
            tmp_log = temp_dir + "/multi_portal_ff.log"
            f_handler = logging.FileHandler(tmp_log)
            f_handler.setLevel(logging.INFO)
            f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            f_handler.setFormatter(f_format)
            logger.addHandler(f_handler)

            multi_portal_ff = FeatureFlag(name=FF_NAME,
                                                app='plugin',
                                                rel_version='5.1.0',
                                                feature=FF_NAME,
                                                flag_type='sre',
                                                logger=logger)

            multi_portal_ff.add_dev_sre_default_flag_value(0)
            multi_portal_ff.add_ff_err_msg("Multi-Portal feature is disabled for this tenant")
            multi_portal_ff.save()

            assert multi_portal_ff.sre_dev_default_flag_value == 0
            assert multi_portal_ff.err_message == "Multi-Portal feature is disabled for this tenant"
