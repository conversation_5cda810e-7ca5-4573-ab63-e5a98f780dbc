import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile
        
def test_swg_kerberos_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_swg_kerberos_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/swg_kerberos_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        
        swg_kerberos_ff = FeatureFlag(name="swg_kerberos_feature_flag",
                                    app="plugin",
                                    rel_version='4.0.0',
                                    feature="swg_kerberos_feature_flag",
                                    flag_type='compatibility',
                                    logger=logger)

        swg_kerberos_ff.add_ami_version_dep("10.1.3")
        swg_kerberos_ff.add_panorama_plugin_dep("panorama_plugin", "3.1.0")
        swg_kerberos_ff.add_eproxy_outside_panos_dep(1)
        swg_kerberos_ff.add_fw_app_version_dep("saas_agent", "3.1.0")
        swg_kerberos_ff.add_ff_err_msg("Explicit Proxy Kerberos feature requires at least 3.1.0 plugin, 3.1.0 saas agent, 10.1.3 ami and EProxy Outside Panos feature enabled")

        assert swg_kerberos_ff.ami_dep_version == "10.1.3"
        assert swg_kerberos_ff.eproxy_outside_panos == 1
        assert swg_kerberos_ff.fw_app_version["saas_agent"] == "3.1.0"
        assert swg_kerberos_ff.flag_type == 'compatibility'
        assert swg_kerberos_ff.panorama_plugin_dep['panorama_plugin'] == "3.1.0"
        assert swg_kerberos_ff.err_message == "Explicit Proxy Kerberos feature requires at least 3.1.0 plugin, 3.1.0 saas agent, 10.1.3 ami and EProxy Outside Panos feature enabled"