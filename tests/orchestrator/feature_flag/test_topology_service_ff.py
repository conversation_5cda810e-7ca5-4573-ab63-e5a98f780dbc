from mock_feature_flag import *
from unittest.mock import call
import logging
import sys
import os
import warnings
from utils import feature_flag_declarations

ffd_dir = os.path.dirname(feature_flag_declarations.__file__)
path = os.path.join(ffd_dir,sorted([ name for name in os.listdir(ffd_dir)
                                     if os.path.isdir(os.path.join(ffd_dir, name))
                                     and name == "5.1.0"])[0])
sys.path.append(path)

class TestTopologyServiceFF:
    @patch.object(logging.FileHandler, "emit")
    @patch.object(logging.FileHandler, "_open")
    @patch("libs.feature_flags.feature_flags.dbconn.execute_query")
    def test_topology_service_ff(self, mock_dbconn, mock_fh_open, mock_fh_emit):
        logger = logging.getLogger("topology_service_ff")
        logger.setLevel(logging.INFO)

        try:
            import topology_service_ff
        except ModuleNotFoundError:
            war = "Topology Service feature flag not defined for latest infra"
            warnings.warn(war)
            logger.error(war)
            print(war)

        expected_calls = []
        supported_versions = [
            "5.1.0"
        ]
        applications = [
            'saas_agent',
            'saas_infra'
        ]
        for app in applications:
            for ver in supported_versions:
                expected_calls.append(
                    call("REPLACE INTO feature_flags (flag_name, flag_type, app, feature_name, "
                         "release_version, dependencies, default_value, err_msg, feature_arguments) "
                         f"VALUES ('topology_service_feature_flag', 'sre', '{app}', 'topology_service_feature_flag', '{ver}', "
                         "'', '0', 'topology service is disabled for this tenant', '')", logger=logger))
        mock_dbconn.assert_has_calls(expected_calls)