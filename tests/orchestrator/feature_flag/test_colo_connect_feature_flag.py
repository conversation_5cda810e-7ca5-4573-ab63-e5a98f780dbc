import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile


FF_NAME = 'colo_connect_ff'

def colo_connect_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_ami_version_dep("10.2.4")
        ff_obj.add_panorama_plugin_dep("panorama_plugin", "4.1.0")
        ff_obj.add_fw_app_version_dep("saas_agent", "4.1.0")
        ff_obj.add_ff_err_msg("Colo-Connect requires at least 4.1.0 panorama plugin, 4.1.0 saas agent, and 10.2.4 ami")
        ff_obj.save()
        
def test_colo_connect_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_colo_connect_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/colo_connect_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        colo_connect_ff = FeatureFlag(name=FF_NAME,
                                    app='plugin',
                                    rel_version='4.1.0',
                                    feature=FF_NAME,
                                    flag_type='compatibility',
                                    logger=logger)
        
        colo_connect_ff_add_dep(colo_connect_ff)
        assert colo_connect_ff.ami_dep_version == "10.2.4"
        assert colo_connect_ff.panorama_plugin_dep['panorama_plugin'] == "4.1.0"
        assert colo_connect_ff.fw_app_version["saas_agent"] == "4.1.0"
        assert colo_connect_ff.flag_type == 'compatibility'
        assert colo_connect_ff.err_message == "Colo-Connect requires at least 4.1.0 panorama plugin, 4.1.0 saas agent, and 10.2.4 ami"