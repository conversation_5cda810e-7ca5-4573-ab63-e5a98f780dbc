# File: /Users/<USER>/code/orchestrator/tests/orchestrator/feature_flag/test_enable_saas_agent_no_passive_instances.py

import logging
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile


def test_enable_saas_agent_no_passive_instances():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_enable_saas_agent_no_passive_instances.py")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/saas_agent_enable_no_passive_instances.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        saas_agent_no_passive_ff = FeatureFlag(name="saas_agent_enable_no_passive_instances",
                                              app='saas_agent',
                                              rel_version='5.2.0',
                                              feature="saas_agent_enable_no_passive_instances",
                                              flag_type='compatibility',
                                              logger=logger)

        # Add dependencies for compatibility flag type
        saas_agent_no_passive_ff.add_fw_app_version_dep("saas_agent", "5.2.0")
        saas_agent_no_passive_ff.add_ff_err_msg("no passive instances feature for saas agent is disabled")

        # Validate flag properties
        assert saas_agent_no_passive_ff.rel_version == "5.2.0"
        assert saas_agent_no_passive_ff.flag_name == 'saas_agent_enable_no_passive_instances'
        assert saas_agent_no_passive_ff.flag_type == 'compatibility'
        assert saas_agent_no_passive_ff.fw_app_version["saas_agent"] == "5.2.0"
        assert saas_agent_no_passive_ff.err_message == "no passive instances feature for saas agent is disabled"
