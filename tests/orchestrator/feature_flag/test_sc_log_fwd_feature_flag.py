from mock_feature_flag import *
import logging
import sys
import os
import warnings
from utils import feature_flag_declarations
ffd_dir = os.path.dirname(feature_flag_declarations.__file__)
path = os.path.join(ffd_dir,sorted([ name for name in os.listdir(ffd_dir)
                                     if os.path.isdir(os.path.join(ffd_dir, name))
                                     and name[0].isdigit()])[-1])
sys.path.append(path)

class TestSCLogFwdFF:
    @patch.object(logging.FileHandler, "emit")
    @patch.object(logging.FileHandler, "_open")
    @patch("libs.feature_flags.feature_flags.dbconn.execute_query")
    def test_build_sc_log_fwd_ff(self, patched_dbconn, fh_open, fh_emit):
            logger = logging.getLogger("test_build_sc_log_fwd_ff")
            logger.setLevel(logging.INFO)
            
            try:
                import sc_log_fwd_feature_flag
            except ModuleNotFoundError:
                war = "sc_log_fwd feature flag not defined for latest infra"
                warnings.warn(war)
                logger.error(war)
                print(war)