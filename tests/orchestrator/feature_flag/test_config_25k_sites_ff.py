import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'config_25k_sites_ff'

def test_config_25k_sites_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_config_25k_sites_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/config_25k_sites_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        versions = ["5.0.0", "5.0.1", "5.1.0"]
        for version in versions:
            config_25k_sites_ff = FeatureFlag(name=FF_NAME,
                                  app='plugin',
                                  rel_version=version,
                                  feature=FF_NAME,
                                  flag_type='sre',
                                  logger=logger)

            config_25k_sites_ff.add_dev_sre_default_flag_value(0)
            config_25k_sites_ff.add_ff_err_msg("config_25k_sites_ff is disabled for this tenant.")

            assert config_25k_sites_ff.rel_version == version
            assert config_25k_sites_ff.flag_name == "config_25k_sites_ff"
            assert config_25k_sites_ff.flag_type == 'sre'
            assert config_25k_sites_ff.err_message == "config_25k_sites_ff is disabled for this tenant."