from mock_feature_flag import *
from unittest.mock import call
import logging

from test_ff_utils import ADD_TMP_FF_PATH


class TestBGPFilteringFF:
    @patch.object(logging.FileHandler, "emit")
    @patch.object(logging.FileHandler, "_open")
    @patch("libs.feature_flags.feature_flags.dbconn.execute_query")
    def test_bgp_filtering_ff(self, mock_dbconn, mock_fh_open, mock_fh_emit):
        logger = logging.getLogger("bgp_filtering_ff")
        logger.setLevel(logging.INFO)

        with ADD_TMP_FF_PATH("6.0.0", "bgp_filtering_ff"):
            supported_versions = [
                "6.0.0"
            ]
            err_msg = "bgp_filtering_feature_flag requires at least ami 10.2.4, panorama plugin 6.0.0, saas-agent 6.0.0"
            for ver in supported_versions:
                expected_call = ("REPLACE INTO feature_flags (flag_name, flag_type, app, feature_name, release_version, "
                                 "dependencies, default_value, err_msg, feature_arguments) "
                                 f"VALUES ('bgp_filtering_feature_flag', 'compatibility', 'plugin', 'bgp_filtering_feature_flag', '{ver}', ")
                assert expected_call in mock_dbconn.call_args_list[0][0][0]
                assert err_msg in mock_dbconn.call_args_list[0][0][0]
        