from mock_feature_flag import *
import traceback
import logging
import importlib.util
import sys
import os
import warnings
from utils import feature_flag_declarations
ffd_dir = os.path.dirname(feature_flag_declarations.__file__)
path = os.path.join(ffd_dir,sorted([ name for name in os.listdir(ffd_dir)
                                     if os.path.isdir(os.path.join(ffd_dir, name))
                                     and name[0].isdigit()])[-1])
sys.path.append(path)

def my_execute_query(query, logger):
    logger.info("Unit test execute_query: %s" % query)
    return True

class TestRegionalIPPool:
    @patch.object(logging.FileHandler, "emit")
    @patch.object(logging.FileHandler, "_open")
    @patch("libs.feature_flags.feature_flags.dbconn.execute_query")
    def test_ff_native(self,patched_dbconn, _, file_emit):
        logger = logging.getLogger()
        patched_dbconn.side_effect = my_execute_query
        try:
            import regional_ip_pool_feature_flag
        except ModuleNotFoundError:
            war = "regional_ip_pool_feature_flag not defined for latest infra"
            warnings.warn(war)
            logger.error(war)
            print(war)
            pass
