import mock_imports_orchestrator
import logging
from unittest import TestCase
from unittest.mock import patch
# import utils.feature_flag_declarations.initialize_flags
from libs.feature_flags import feature_flags_exceptions as ffe
import pytest
from unittest.mock import MagicMock
import sys
sys.modules['oci'] = MagicMock()

# from utils.feature_flag_declarations import initialize_flags
import utils.feature_flag_declarations.initialize_flags as initialize_flags


logger = logging.getLogger()

class TestInilializeFlags:
    @patch('utils.feature_flag_declarations.initialize_flags.logging.handlers')
    @patch('utils.feature_flag_declarations.initialize_flags.dbconn.find_one')
    @patch('utils.feature_flag_declarations.initialize_flags.logging.getLogger')
    @patch('utils.feature_flag_declarations.initialize_flags.os.path.exists')
    @patch('utils.feature_flag_declarations.initialize_flags.os.listdir')
    @patch('utils.feature_flag_declarations.initialize_flags.DbHandle')
    @patch('utils.feature_flag_declarations.initialize_flags.os.getcwd')
    def test_initialize_flags_valid_case(self, mock_getcwd, mock_DbHandle, mock_listdir, mock_path_exists, mock_logger, mock_dbconn_find_one , mock_logging_handlers):

        mock_logging_handlers.RotatingFileHandler.return_value = MagicMock()
        mock_logger.return_value = MagicMock()
        mock_dbconn_find_one.return_value = [['4.2.0.0-2391.pan_a82d15af95']]
        mock_path_exists = True
        mock_listdir = MagicMock()
        mock_DbHandle = MagicMock()
        mock_getcwd.return_value = "../../src/apps/orchestrator"
        initialize_flags.main()

    @patch('utils.feature_flag_declarations.initialize_flags.logging.handlers')
    @patch('utils.feature_flag_declarations.initialize_flags.dbconn.find_one')
    @patch('utils.feature_flag_declarations.initialize_flags.logging.getLogger')
    @patch('utils.feature_flag_declarations.initialize_flags.os.path.exists')
    @patch('utils.feature_flag_declarations.initialize_flags.os.listdir')
    @patch('utils.feature_flag_declarations.initialize_flags.DbHandle')
    def test_initialize_flags_exception_case_db_error(self, mock_DbHandle, mock_listdir, mock_path_exists, mock_logger, mock_dbconn_find_one , mock_logging_handlers):
        mock_logging_handlers.RotatingFileHandler.return_value = MagicMock()
        mock_logger.return_value = MagicMock()
        mock_dbconn_find_one.side_effect = Exception("db error")
        mock_path_exists = True
        mock_listdir = MagicMock()
        mock_DbHandle = MagicMock()

        with pytest.raises(Exception) as e:
            initialize_flags.main()
            assert e.value.message == "db error"

    @patch('utils.feature_flag_declarations.initialize_flags.logging.handlers')
    @patch('utils.feature_flag_declarations.initialize_flags.dbconn.find_one')
    @patch('utils.feature_flag_declarations.initialize_flags.logging.getLogger')
    @patch('utils.feature_flag_declarations.initialize_flags.os.path.exists')
    @patch('utils.feature_flag_declarations.initialize_flags.os.listdir')
    @patch('utils.feature_flag_declarations.initialize_flags.DbHandle')
    def test_initialize_flags_exception_case_format_error(self, mock_DbHandle, mock_listdir, mock_path_exists, mock_logger, mock_dbconn_find_one , mock_logging_handlers):
        mock_logging_handlers.RotatingFileHandler.return_value = MagicMock()
        mock_logger.return_value = MagicMock()
        mock_dbconn_find_one.return_value = [['some_wrong_format']]
        mock_path_exists = True
        mock_listdir = MagicMock()
        mock_DbHandle = MagicMock()

        with pytest.raises(Exception) as e:
            initialize_flags.main()
            assert e.value.message == "Version not in known format"

    @patch('utils.feature_flag_declarations.initialize_flags.logging.handlers')
    @patch('utils.feature_flag_declarations.initialize_flags.dbconn.find_one')
    @patch('utils.feature_flag_declarations.initialize_flags.logging.getLogger')
    @patch('utils.feature_flag_declarations.initialize_flags.os.path.exists')
    @patch('utils.feature_flag_declarations.initialize_flags.os.listdir')
    @patch('utils.feature_flag_declarations.initialize_flags.DbHandle')
    @patch('utils.feature_flag_declarations.initialize_flags.list')
    def test_initialize_flags_exception_case_fail_to_initialize_ff(self, mock_list, mock_DbHandle, mock_listdir, mock_path_exists, mock_logger, mock_dbconn_find_one , mock_logging_handlers):
        mock_logging_handlers.RotatingFileHandler.return_value = MagicMock()
        mock_logger.return_value = MagicMock()
        mock_dbconn_find_one.return_value = [['4.2.0.0-2391.pan_a82d15af95']]
        mock_path_exists = True
        mock_listdir = MagicMock()
        mock_DbHandle = MagicMock()
        mock_list.side_effect = [[1],[0]]

        with pytest.raises(Exception) as e:
            initialize_flags.main()
            assert e.value.message == "Failed to initialize feature flags"

    @patch('utils.feature_flag_declarations.initialize_flags.logging.handlers')
    @patch('utils.feature_flag_declarations.initialize_flags.dbconn.find_one')
    @patch('utils.feature_flag_declarations.initialize_flags.logging.getLogger')
    @patch('utils.feature_flag_declarations.initialize_flags.os.path.exists')
    @patch('utils.feature_flag_declarations.initialize_flags.os.listdir')
    @patch('utils.feature_flag_declarations.initialize_flags.DbHandle')
    @patch('utils.feature_flag_declarations.initialize_flags.boto3.client')
    def test_initialize_flags_exception_case_fail_boto3_error(self, mock_client, mock_DbHandle, mock_listdir, mock_path_exists, mock_logger, mock_dbconn_find_one , mock_logging_handlers):

        mock_logging_handlers.RotatingFileHandler.return_value = MagicMock()
        mock_logger.return_value = MagicMock()
        mock_dbconn_find_one.return_value = [['4.2.0.0-2391.pan_a82d15af95']]
        mock_path_exists = True
        mock_listdir = MagicMock()
        mock_DbHandle = MagicMock()
        mock_client.side_effect = Exception("boto3 error")

        with pytest.raises(Exception) as e:
            initialize_flags.main()
            assert e.value.message == " boto3 error"