import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'host_compliance'
FF_SRE_NAME = 'host_compliance_sre'
FEATURE_NAME = 'cloud_user_context'
REL_VERSION = '6.0.0'
AMI_REL_VERSION = "12.1.0"
ERR_MSG = "host_compliance requires at least ami {AMI_REL_VERSION}, panorama plugin {REL_VERSION}, saas-agent {REL_VERSION}"

def host_compliance_ff_add_dep(ff_obj):
    # Enabled only with the following added dependencies
    ff_obj.add_ami_version_dep(AMI_REL_VERSION)
    ff_obj.add_fw_app_version_dep("saas_agent", REL_VERSION)
    ff_obj.add_panorama_plugin_dep("panorama_plugin", REL_VERSION)
    ff_obj.add_ff_err_msg(ERR_MSG)
    ff_obj.save()

def test_host_compliance_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_host_compliance_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/'host_compliance_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        
        apps = ['plugin']

        for app_name in apps:
            host_compliance_ff_plugin = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version=REL_VERSION,
                                        feature=FEATURE_NAME,
                                        flag_type='compatibility',
                                        logger=logger)

            host_compliance_ff_add_dep(host_compliance_ff_plugin)

            assert host_compliance_ff_plugin.flag_name == FF_NAME
            assert host_compliance_ff_plugin.feature_name == FEATURE_NAME
            assert host_compliance_ff_plugin.app == app_name
            assert host_compliance_ff_plugin.rel_version == REL_VERSION
            assert host_compliance_ff_plugin.flag_type == 'compatibility'
            assert host_compliance_ff_plugin.err_message == ERR_MSG
            assert host_compliance_ff_plugin.ami_dep_version == AMI_REL_VERSION
            assert host_compliance_ff_plugin.panorama_plugin_dep['panorama_plugin'] == REL_VERSION

            host_compliance_ff_plugin_sre = FeatureFlag(name=FF_SRE_NAME,
                                            app=app_name,
                                            rel_version=REL_VERSION,
                                            feature=FEATURE_NAME,
                                            flag_type='sre',
                                            logger=logger)

            # default value 1: enabled
            host_compliance_ff_plugin_sre.add_dev_sre_default_flag_value(0)
            host_compliance_ff_plugin_sre.add_ff_err_msg(
                "host_compliance SRE is disabled for this tenant")
            host_compliance_ff_plugin_sre.save()

            assert host_compliance_ff_plugin_sre.flag_name == FF_SRE_NAME
            assert host_compliance_ff_plugin_sre.app == app_name
            assert host_compliance_ff_plugin_sre.rel_version == REL_VERSION
            assert host_compliance_ff_plugin_sre.feature_name == FEATURE_NAME
            assert host_compliance_ff_plugin_sre.flag_type == 'sre'
            assert host_compliance_ff_plugin_sre.sre_dev_default_flag_value == 0
            assert host_compliance_ff_plugin_sre.err_message == "host_compliance SRE is disabled for this tenant"