import mock_imports_orchestrator
import logging
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'sw_cutthrough_feature_flag_sre'
FEATURE_NAME = "sw_cutthrough"
        
def test_sw_cutthrough_feature_flag_sre():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_sw_cutthrough_feature_flag_sre_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/sw_cutthrough_feature_flag.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        sw_cutthrough_feature_flag_sre = FeatureFlag(name=FF_NAME,
                                    app='saas_agent',
                                    rel_version='6.0.0',
                                    feature=FEATURE_NAME,
                                    flag_type='sre',
                                    logger=logger)
        sw_cutthrough_feature_flag_sre.add_dev_sre_default_flag_value(1)
        sw_cutthrough_feature_flag_sre.add_ff_err_msg("SW cutthrough SRE flag enabled by default")
        sw_cutthrough_feature_flag_sre.save()

        assert sw_cutthrough_feature_flag_sre.rel_version == "6.0.0"
        assert sw_cutthrough_feature_flag_sre.flag_name == FF_NAME
        assert sw_cutthrough_feature_flag_sre.app == 'saas_agent'
        assert sw_cutthrough_feature_flag_sre.feature_name == FEATURE_NAME
        assert sw_cutthrough_feature_flag_sre.flag_type == 'sre'
        assert sw_cutthrough_feature_flag_sre.sre_dev_default_flag_value == 1