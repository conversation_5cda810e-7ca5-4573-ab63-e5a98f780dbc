import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def agent_to_ep_proxy_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_eproxy_outside_panos_dep(1)
        ff_obj.add_proxy_ami_version_dep("4.0.0")
        ff_obj.add_fw_app_version_dep("saas_agent", "4.0.0")
        ff_obj.add_swg_ami_dep_version("10.2.4")
        ff_obj.add_portal_ami_dep_version("10.2.4")
        ff_obj.add_ff_err_msg("GP Agent to Explicit Proxy Datapath feature requires at least 4.0.0 saas agent, 4.0.0 proxy ami, 10.2.4 ami and EProxy Outside Panos feature enabled")
        ff_obj.save()

def test_agent_to_ep_proxy_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_agent_to_ep_proxy_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/agent_to_ep_proxy_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['infra', 'saas_agent']
        for app_name in apps:
            agent_to_ep_proxy_ff = FeatureFlag(name="agent_to_ep_proxy",
                                        app=app_name,
                                        rel_version='4.0.0',
                                        feature="agent_to_ep_proxy",
                                        flag_type='compatibility',
                                        logger=logger)
            agent_to_ep_proxy_ff_add_dep(agent_to_ep_proxy_ff)
            assert agent_to_ep_proxy_ff.swg_ami_dep_version == "10.2.4"
            assert agent_to_ep_proxy_ff.portal_ami_dep_version == "10.2.4"
            assert agent_to_ep_proxy_ff.eproxy_outside_panos == 1
            assert agent_to_ep_proxy_ff.proxy_ami_version_dep == "4.0.0"
            assert agent_to_ep_proxy_ff.fw_app_version["saas_agent"] == "4.0.0"
            assert agent_to_ep_proxy_ff.flag_type == 'compatibility'
            assert agent_to_ep_proxy_ff.err_message == "GP Agent to Explicit Proxy Datapath feature requires at least 4.0.0 saas agent, 4.0.0 proxy ami, 10.2.4 ami and EProxy Outside Panos feature enabled"
