import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def application_split_tunnel_60_dep(obj):
    # Enabled only with the following added dependencies
    obj.add_ami_version_dep("11.2.0")
    obj.add_fw_app_version_dep("saas_agent", "6.0.0")
    obj.add_ff_err_msg("application_split_tunnel_60 support requires at least a 11.2.0 dataplane, "
                                            "6.0 Saas Agent")
    obj.save()

def check_application_split_tunnel_60_dep(obj):
    assert obj.ami_dep_version == "11.2.0"
    assert obj.fw_app_version["saas_agent"] == "6.0.0"
    assert obj.flag_type == 'compatibility'
    assert obj.err_message == 'application_split_tunnel_60 support requires at least a 11.2.0 dataplane, 6.0 Saas Agent'

def test_application_split_tunnel_60_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_application_split_tunnel_60_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/application_split_tunnel_60_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        application_split_tunnel_60_ff_agent = FeatureFlag(name='application_split_tunnel_60',
                                            app='saas_agent',
                                            rel_version='6.0.0',
                                            feature='application_split_tunnel_60',
                                            flag_type='compatibility',
                                            logger=logger)

        application_split_tunnel_60_dep(application_split_tunnel_60_ff_agent)
        check_application_split_tunnel_60_dep(application_split_tunnel_60_ff_agent)