import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

supported_saas_agent_ver = ['2.2.0', '3.0.0', '3.1.0', '3.2.0', '3.2.1', '4.0.0', '4.1.0']
def test_user_id_phase_1_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_user_id_phase_1_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/user_id_phase_1_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        for sa_ver in supported_saas_agent_ver:
            user_id_p1_ff_agent = FeatureFlag(name='uid-redist-on',
                                        app='agent',
                                        rel_version=sa_ver,
                                        feature='uid-redist-on',
                                        flag_type='dev',
                                        logger=logger)

            user_id_p1_ff_agent.add_dev_sre_default_flag_value(0)
            user_id_p1_ff_agent.add_ff_err_msg(f"user id redistribution phase 1 feature is disabled for this tenant in SaaS Agent {sa_ver}")
            user_id_p1_ff_agent.save()
            assert user_id_p1_ff_agent.flag_type == 'dev'
            assert user_id_p1_ff_agent.err_message == f"user id redistribution phase 1 feature is disabled for this tenant in SaaS Agent {sa_ver}"

