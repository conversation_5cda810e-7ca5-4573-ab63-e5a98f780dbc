from mock_feature_flag import *
from unittest.mock import call
import logging
import sys
import os
import warnings
from utils import feature_flag_declarations

ffd_dir = os.path.dirname(feature_flag_declarations.__file__)
path = os.path.join(ffd_dir,sorted([ name for name in os.listdir(ffd_dir)
                                     if os.path.isdir(os.path.join(ffd_dir, name))
                                     and name == "5.1.0"])[0])
sys.path.append(path)

class TestRNSCFQDNFF:
    @patch.object(logging.FileHandler, "emit")
    @patch.object(logging.FileHandler, "_open")
    @patch("libs.feature_flags.feature_flags.dbconn.execute_query")
    def test_rnsc_fqdn_ff(self, mock_dbconn, mock_fh_open, mock_fh_emit):
        logger = logging.getLogger("rnsc_fqdn_ff")
        logger.setLevel(logging.INFO)

        try:
            import rnsc_fqdn_ff
        except ModuleNotFoundError:
            war = "RN/SC FQDN feature flag not defined for latest infra"
            warnings.warn(war)
            logger.error(war)
            print(war)

        expected_calls = []
        supported_versions = [
            "5.1.0"
        ]
        for ver in supported_versions:
            expected_calls.append(
                call("REPLACE INTO feature_flags (flag_name, flag_type, app, feature_name, "
                     "release_version, dependencies, default_value, err_msg, feature_arguments) "
                     "VALUES ('rnsc_fqdn_feature_flag', 'sre', 'saas_infra', 'rnsc_fqdn_feature_flag', '%s', "
                     "'', '1', 'RN/SC FQDN feature is enabled by default', '')" % 
                     ver, logger=logger))
        mock_dbconn.assert_has_calls(expected_calls)