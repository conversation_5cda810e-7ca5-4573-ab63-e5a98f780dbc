import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile
        
def test_eproxy_outside_panos_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_eproxy_outside_panos_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/eproxy_outside_panos_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        eproxy_outside_panos_ff = FeatureFlag(name="eproxy_outside_panos_feature_flag",
                                    app="plugin",
                                    rel_version='4.0.0',
                                    feature="eproxy_outside_panos_feature_flag",
                                    flag_type='compatibility',
                                    logger=logger)
        eproxy_outside_panos_ff.add_ami_version_dep("10.0.9")
        eproxy_outside_panos_ff.add_panorama_plugin_dep("panorama_plugin", "3.0.0")
        eproxy_outside_panos_ff.add_eproxy_outside_panos_dep(1)
        eproxy_outside_panos_ff.add_fw_app_version_dep("saas_agent", "3.0.0")
        eproxy_outside_panos_ff.add_ff_err_msg("This explicit proxy feature requires at least 3.0 plugin, 3.0 saas agent, and 10.0.9 ami")

        assert eproxy_outside_panos_ff.ami_dep_version == "10.0.9"
        assert eproxy_outside_panos_ff.eproxy_outside_panos == 1
        assert eproxy_outside_panos_ff.fw_app_version["saas_agent"] == "3.0.0"
        assert eproxy_outside_panos_ff.flag_type == 'compatibility'
        assert eproxy_outside_panos_ff.panorama_plugin_dep['panorama_plugin'] == "3.0.0"
        assert eproxy_outside_panos_ff.err_message == "This explicit proxy feature requires at least 3.0 plugin, 3.0 saas agent, and 10.0.9 ami"