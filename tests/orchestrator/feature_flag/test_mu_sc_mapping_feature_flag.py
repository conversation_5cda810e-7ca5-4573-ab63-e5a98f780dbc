import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile
import os  # Import the 'os' module

FF_NAME = 'mu_sc_mapping_feature_flag'

def test_mu_sc_mapping_feature_flag():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)

        log_dir = os.path.join(temp_dir, "var", "log", "pan")  # Create the full path
        os.makedirs(log_dir, exist_ok=True)  # Create the directory if it doesn't exist

        tmp_log = os.path.join(log_dir, "mu_sc_mapping_ff.log")

        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        feature_arguments = {"green_field_only": True}

        ciam_ff = FeatureFlag(name='mu_sc_mapping_feature_flag',
                                    app='saas_infra',
                                    rel_version='6.0.0',
                                    feature='mu_sc_mapping',
                                    flag_type='sre',
                                    logger=logger)

        # default value 0: disabled for Brownfield but enabled for Greenfield
        ciam_ff.add_dev_sre_default_flag_value(0)
        ciam_ff.add_feature_arguments(feature_arguments)
        ciam_ff.add_ff_err_msg("MU to SC mapping feature flag enabled by default")

        ciam_ff.save()

        assert ciam_ff.rel_version == "6.0.0"
        assert ciam_ff.flag_name == "mu_sc_mapping_feature_flag"
        assert ciam_ff.flag_type == 'sre'
        assert ciam_ff.err_message == "MU to SC mapping feature flag enabled by default"
        