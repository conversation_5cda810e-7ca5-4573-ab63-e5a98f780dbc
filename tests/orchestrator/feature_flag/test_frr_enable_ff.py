import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'frr_feature_flag'

def test_frr_feature_flag_sre_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_frr_feature_flag_sre_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/frr_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        frr_ff = FeatureFlag(name=FF_NAME,
                                    app='plugin',
                                    rel_version='5.1.0',
                                    feature='frr_feature_flag',
                                    flag_type='sre',
                                    logger=logger)
        
        frr_ff.add_dev_sre_default_flag_value(0)
        frr_ff.add_ff_err_msg("FRR is disabled for this tenant")
        frr_ff.save()

        assert frr_ff.sre_dev_default_flag_value == 0
        assert frr_ff.err_message == "FRR is disabled for this tenant"
