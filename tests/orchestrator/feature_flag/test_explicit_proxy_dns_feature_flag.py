import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'explicit_proxy_dns_feature_flag'
ERR_MSG = "Explicit Proxy DNS Proxy feature requires at least 5.2.0 saas agent, 5.2.0 Envoy Proxy and EProxy Outside Panos feature enabled"

def explicit_proxy_dns_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_eproxy_outside_panos_dep(1)
        ff_obj.add_fw_app_version_dep("saas_agent", "5.2.0")
        ff_obj.add_proxy_ami_version_dep("5.2.0")
        ff_obj.add_ff_err_msg("Explicit Proxy DNS Proxy feature requires at least 5.2.0 saas agent, 5.2.0 Envoy Proxy and EProxy Outside Panos feature enabled")
        ff_obj.save()
        
def test_explicit_proxy_dns_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_explicit_proxy_dns_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/'explicit_proxy_dns_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['plugin']
        for app_name in apps:
            ep_priv_app_access_ff = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='5.2.0',
                                        feature=FF_NAME,
                                        flag_type='compatibility',
                                        logger=logger)
            explicit_proxy_dns_ff_add_dep(ep_priv_app_access_ff)
            assert ep_priv_app_access_ff.rel_version == "5.2.0"
            assert ep_priv_app_access_ff.flag_type == 'compatibility'
            assert ep_priv_app_access_ff.err_message == ERR_MSG
            assert ep_priv_app_access_ff.eproxy_outside_panos == 1
            assert ep_priv_app_access_ff.proxy_ami_version_dep == "5.2.0"
            assert ep_priv_app_access_ff.fw_app_version["saas_agent"] == "5.2.0"