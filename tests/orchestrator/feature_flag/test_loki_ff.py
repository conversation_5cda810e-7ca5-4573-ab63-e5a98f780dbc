import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'loki_ff'
apps = ['saas_infra', 'saas_agent', 'plugin']

def test_loki_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_loki_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/loki_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)

        supported_vers = ['6.1.0']
        for ver in supported_vers:
            for app in apps:
                loki_ff = FeatureFlag(name=FF_NAME,
                                                  app=app,
                                                  rel_version=ver,
                                                  feature=FF_NAME,
                                                  flag_type='sre',
                                                  logger=logger)

                loki_ff.add_dev_sre_default_flag_value(0)
                loki_ff.add_ff_err_msg("Loki feature is enabled for this tenant")

                assert loki_ff.sre_dev_default_flag_value == 0
                assert loki_ff.rel_version == ver
                assert loki_ff.flag_name == FF_NAME
                assert loki_ff.err_message == "Loki feature is enabled for this tenant"
