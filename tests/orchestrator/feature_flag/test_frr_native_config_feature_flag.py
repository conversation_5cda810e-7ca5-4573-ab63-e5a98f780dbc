from mock_feature_flag import *
import logging

from test_ff_utils import ADD_TMP_FF_PATH


class TestFrrNativeFF:
    @patch.object(logging.FileHandler, "emit")
    @patch.object(logging.FileHandler, "_open")
    @patch("libs.feature_flags.feature_flags.dbconn.execute_query")
    @patch("libs.feature_flags.feature_flags.b64encode")
    def test_frr_native_frr(self, mock_b64encode, mock_dbconn, mock_fh_open, mock_fh_emit):
        logger = logging.getLogger("frr_native_ff")
        logger.setLevel(logging.INFO)

        with ADD_TMP_FF_PATH("6.1.0", "frr_native_ff"):
            expected_call = ("REPLACE INTO feature_flags (flag_name, flag_type, app, feature_name, release_version, "
                                "dependencies, default_value, err_msg, feature_arguments) VALUES ('frr_native_config_ff', "
                                f"'compatibility', 'saas_agent', 'frr_native_config_ff', '6.1.0', '{mock_b64encode.return_value}', "
                                "'0', 'frr_native_config_ff requires at least ami 12.1.1, saas-agent 6.1.0', '')")
            mock_dbconn.assert_any_call(expected_call, logger=logger)
            
            expected_sre_call = ("REPLACE INTO feature_flags (flag_name, flag_type, app, feature_name, release_version, "
                                    "dependencies, default_value, err_msg, feature_arguments) VALUES ('frr_native_config_ff_sre', "
                                    "'sre', 'saas_agent', 'frr_native_config_ff', '6.1.0', '', '0', "
                                    "'FRR native config generation feature is disabled by default', '')")
            mock_dbconn.assert_any_call(expected_sre_call, logger=logger)
