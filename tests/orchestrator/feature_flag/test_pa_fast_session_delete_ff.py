from mock_feature_flag import *
import logging
import sys
import os
import warnings
from utils import feature_flag_declarations
ffd_dir = os.path.dirname(feature_flag_declarations.__file__)
path = os.path.join(ffd_dir,sorted([ name for name in os.listdir(ffd_dir)
                                     if os.path.isdir(os.path.join(ffd_dir, name))
                                     and name[0].isdigit()])[-1])
sys.path.append(path)

class TestPaFastSessionDeleteFF:
    @patch.object(logging.FileHandler, "emit")
    @patch.object(logging.FileHandler, "_open")
    @patch("libs.feature_flags.feature_flags.dbconn.execute_query")
    def test_pa_fast_session_delete_ff(self, patched_dbconn, fh_open, fh_emit):
            logger = logging.getLogger("test_pa_fast_session_delete_ff")
            logger.setLevel(logging.INFO)
            
            try:
                import pa_fast_session_delete_ff
            except ModuleNotFoundError:
                war = "test_pa_fast_session_delete feature flag not defined for latest infra"
                warnings.warn(war)
                logger.error(war)
                print(war)
