import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def test_ipv6_internet_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_ipv6_internet_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/ipv6_internet_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        ipv6_internet_ff = FeatureFlag(name='ipv6_internet',
                                       app='plugin',
                                       rel_version='4.2.0',
                                       feature='ipv6_internet',
                                       flag_type='compatibility',
                                       logger=logger)
        ipv6_internet_ff.add_ami_version_dep("10.2.5")
        ipv6_internet_ff.add_fw_app_version_dep("saas_agent", "4.2.0")
        ipv6_internet_ff.add_panorama_plugin_dep("panorama_plugin", "4.2.0")
        ipv6_internet_ff.save()
        assert ipv6_internet_ff.ami_dep_version == "10.2.5"
        assert ipv6_internet_ff.fw_app_version["saas_agent"] == "4.2.0"
        assert ipv6_internet_ff.flag_type == 'compatibility'
        assert ipv6_internet_ff.panorama_plugin_dep["panorama_plugin"] == "4.2.0"