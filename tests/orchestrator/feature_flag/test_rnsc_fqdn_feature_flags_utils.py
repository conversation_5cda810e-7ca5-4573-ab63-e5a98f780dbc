import mock_imports_orchestrator
import mock_feature_flag
import logging
from unittest.mock import patch
import pytest
from libs.feature_flags.rnsc_fqdn_feature_flags_utils import saas_agent_version_supports_fqdn, plugin_version_supports_fqdn

logger = logging.getLogger()


class TestFeatureFlagUtils:
    @pytest.mark.parametrize("saas_agent_version, expected_result",
                             [
                                 ("5.1.0", True),
                                 ("5.0.0", False),
                             ])
    def test_saas_agent_version_supports_fqdn(self, saas_agent_version, expected_result):
        result = saas_agent_version_supports_fqdn(logger, saas_agent_version)
        assert result == expected_result

    @pytest.mark.parametrize("plugin_version, expected_result",
                             [
                                 ("5.1.0", True),
                                 ("5.0.0", False),
                             ])
    def test_plugin_version_supports_fqdn(self, plugin_version, expected_result):
        result = saas_agent_version_supports_fqdn(logger, plugin_version)
        assert result == expected_result
