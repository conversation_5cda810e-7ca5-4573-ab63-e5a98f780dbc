import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'gp_ver_check_sre'

def test_mu_count_normalization_feature_flag():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_gp_ver_check_sre_feature_flag")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/mu_normalization_enable_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_agent']
        for app_name in apps:
            gp_ver_check_sre_ff = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='4.2.0',
                                        feature=FF_NAME,
                                        flag_type='dev',
                                        logger=logger)
            gp_ver_check_sre_ff.add_ff_err_msg(
                "gp_ver_check_sre feature enabled for this tenant")

            assert gp_ver_check_sre_ff.rel_version == "4.2.0"
            assert gp_ver_check_sre_ff.flag_name == 'gp_ver_check_sre'
            assert gp_ver_check_sre_ff.err_message == "gp_ver_check_sre feature enabled for this tenant"