import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'tenant_appsec_feature_flag_sre'
        
def test_tenant_appsec_ff_sre():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_tenant_appsec_ff_sre_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/tenant_appsec_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_infra']
        for app_name in apps:
            tenant_appsec_ff_sre = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='5.2.0',
                                        feature=FF_NAME,
                                        flag_type='sre',
                                        logger=logger)

            assert tenant_appsec_ff_sre.rel_version == "5.2.0"
            assert tenant_appsec_ff_sre.flag_name == FF_NAME
            assert tenant_appsec_ff_sre.app == app_name
            assert tenant_appsec_ff_sre.feature_name == FF_NAME
            assert tenant_appsec_ff_sre.flag_type == 'sre'
            assert tenant_appsec_ff_sre.sre_dev_default_flag_value == 0
