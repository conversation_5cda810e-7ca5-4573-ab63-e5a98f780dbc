import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def rn_to_ep_context_forwarding_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_swg_ami_dep_version("10.2.4")
        ff_obj.add_rn_ami_dep_version("10.2.4")
        ff_obj.add_eproxy_outside_panos_dep(1)
        ff_obj.add_fw_app_version_dep("saas_agent", "4.1.0")
        ff_obj.add_proxy_ami_version_dep("4.1.0")
        ff_obj.add_ff_err_msg("Remote Networks to Explicit Proxy context forwarding feature requires at least 4.1.0 saas agent, EProxy Outside Panos feature, 4.1.0 proxy ami and 10.2.4 proxy ami")
        ff_obj.save()

def test_rn_to_ep_context_forwarding_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_rn_to_ep_context_forwarding_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/rn_to_ep_context_forwarding_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['infra', 'saas_agent']
        for app_name in apps:
            rn_to_ep_context_forwarding_ff = FeatureFlag(name="rn_to_ep_context_forwarding",
                                        app=app_name,
                                        rel_version='4.1.0',
                                        feature="rn_to_ep_context_forwarding",
                                        flag_type='compatibility',
                                        logger=logger)
            rn_to_ep_context_forwarding_ff_add_dep(rn_to_ep_context_forwarding_ff)
            assert rn_to_ep_context_forwarding_ff.swg_ami_dep_version == "10.2.4"
            assert rn_to_ep_context_forwarding_ff.rn_ami_dep_version == "10.2.4"
            assert rn_to_ep_context_forwarding_ff.proxy_ami_version_dep == "4.1.0"
            assert rn_to_ep_context_forwarding_ff.eproxy_outside_panos == 1
            assert rn_to_ep_context_forwarding_ff.fw_app_version["saas_agent"] == "4.1.0"
            assert rn_to_ep_context_forwarding_ff.flag_type == 'compatibility'
            assert rn_to_ep_context_forwarding_ff.err_message == "Remote Networks to Explicit Proxy context forwarding feature requires at least 4.1.0 saas agent, EProxy Outside Panos feature, 4.1.0 proxy ami and 10.2.4 proxy ami"
