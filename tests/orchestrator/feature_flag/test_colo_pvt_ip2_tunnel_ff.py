import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'colo_pvt_ip2_tunnel_ff'
ERR_MSG = "Colo 6.0 private ip2 tunnel requires at least 6.0.0 Saas Agent, 11.2.6 ami"

def colo_pvt_ip2_tunnel_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_ff_err_msg(ERR_MSG)
        ff_obj.add_ami_version_dep("11.2.6")
        ff_obj.add_fw_app_version_dep("saas_agent", "6.0.0")
        ff_obj.save()
        
def test_colo_pvt_ip2_tunnel_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_colo_pvt_ip2_tunnel_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/colo_pvt_ip2_tunnel_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_infra', 'saas_agent']
        for app_name in apps:
            colo_pvt_ip2_tunnel_ff = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='6.0.0',
                                        feature=FF_NAME,
                                        flag_type='compatibility',
                                        logger=logger)
            colo_pvt_ip2_tunnel_ff_add_dep(colo_pvt_ip2_tunnel_ff)
            assert colo_pvt_ip2_tunnel_ff.rel_version == "6.0.0"
            assert colo_pvt_ip2_tunnel_ff.flag_type == 'compatibility'
            assert colo_pvt_ip2_tunnel_ff.err_message == ERR_MSG
            assert colo_pvt_ip2_tunnel_ff.ami_dep_version == '11.2.6'
            assert colo_pvt_ip2_tunnel_ff.fw_app_version["saas_agent"] == "6.0.0"


FF_NAME_SRE = 'colo_pvt_ip2_tunnel_ff_sre'
SRE_FF_ERR_MSG = "Colo 6.0 private ip2 tunnel is enabled by default for greenfield"

def test_colo_pvt_ip2_tunnel_sre_ff():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_colo_pvt_ip2_tunnel_sre_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/colo_pvt_ip2_tunnel_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_agent']
        for app_name in apps:
            colo_pvt_ip2_tunnel_sre_ff = FeatureFlag(name=FF_NAME_SRE,
                                        app=app_name,
                                        rel_version='6.0.0',
                                        feature=FF_NAME_SRE,
                                        flag_type='sre',
                                        logger=logger)
            colo_pvt_ip2_tunnel_sre_ff.add_ff_err_msg(SRE_FF_ERR_MSG)

            assert colo_pvt_ip2_tunnel_sre_ff.rel_version == "6.0.0"
            assert colo_pvt_ip2_tunnel_sre_ff.flag_name == 'colo_pvt_ip2_tunnel_ff_sre'
            assert colo_pvt_ip2_tunnel_sre_ff.err_message == SRE_FF_ERR_MSG