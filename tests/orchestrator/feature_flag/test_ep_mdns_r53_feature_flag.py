import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile


FF_NAME = 'ep_mdns_r53_feature_flag'
ERR_MSG = "Explicit Proxy MDNS Route53 requires at least 5.2.0 Saas Agent, EProxy Outside Panos feature, 6.0.0 proxy ami, 10.2.4 ami"

def ep_mdns_r53_ff_add_dep(ff_obj):
    # Enabled only with the following added dependencies
    ff_obj.add_ff_err_msg(ERR_MSG)
    ff_obj.add_eproxy_outside_panos_dep(1)
    ff_obj.add_proxy_ami_version_dep("6.0.0")
    ff_obj.add_swg_ami_dep_version("10.2.4")
    ff_obj.add_fw_app_version_dep("saas_agent", "5.2.0")
    ff_obj.save()


def test_ep_mdns_r53_feature_flag_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_ep_mdns_r53_feature_flag_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/ep_symmetric_key_rotation_new_arch_feature_flag.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_agent', 'orchestrator', 'infra', 'saas_infra']
        for app_name in apps:
            ep_mdns_r53_feature_flag = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='6.0.0',
                                        feature=FF_NAME,
                                        flag_type='compatibility',
                                        logger=logger)
            ep_mdns_r53_ff_add_dep(ep_mdns_r53_feature_flag)
            assert ep_mdns_r53_feature_flag.rel_version == "6.0.0"
            assert ep_mdns_r53_feature_flag.flag_type == 'compatibility'
            assert ep_mdns_r53_feature_flag.err_message == ERR_MSG
            assert ep_mdns_r53_feature_flag.eproxy_outside_panos == 1
            assert ep_mdns_r53_feature_flag.swg_ami_dep_version == '10.2.4'
            assert ep_mdns_r53_feature_flag.proxy_ami_version_dep == '6.0.0'
            assert ep_mdns_r53_feature_flag.fw_app_version["saas_agent"] == "5.2.0"
            

