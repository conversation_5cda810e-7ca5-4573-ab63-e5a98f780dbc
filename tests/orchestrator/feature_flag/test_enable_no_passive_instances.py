import logging
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile


def test_enable_no_passive_instances():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_enable_no_passive_instances.py")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/tenant_enable_no_passive_instances.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_infra']
        for app_name in apps:
            no_passive_instances_ff_infra = FeatureFlag(name="tenant_enable_no_passive_instances",
                                                        app=app_name,
                                                        rel_version='5.1.0',
                                                        feature="tenant_enable_no_passive_instances",
                                                        flag_type='dev',
                                                        logger=logger)
            no_passive_instances_ff_infra.add_dev_sre_default_flag_value(0)

            no_passive_instances_ff_infra.add_ff_err_msg(
                "tenant no passive instances feature is disabled for this tenant")

            assert no_passive_instances_ff_infra.rel_version == "5.1.0"
            assert no_passive_instances_ff_infra.flag_name == 'tenant_enable_no_passive_instances'
            assert no_passive_instances_ff_infra.sre_dev_default_flag_value == 0
            assert no_passive_instances_ff_infra.err_message == "tenant no passive instances feature is disabled for this tenant"

            ztt_no_passive_instances_ff_infra = FeatureFlag(name="ztt_enable_no_passive_instances",
                                                            app=app_name,
                                                            rel_version='5.1.0',
                                                            feature="ztt_enable_no_passive_instances",
                                                            flag_type='dev',
                                                            logger=logger)
            ztt_no_passive_instances_ff_infra.add_dev_sre_default_flag_value(0)

            ztt_no_passive_instances_ff_infra.add_ff_err_msg(
                "ztt no passive instances feature is disabled for this tenant")
            assert ztt_no_passive_instances_ff_infra.rel_version == "5.1.0"
            assert ztt_no_passive_instances_ff_infra.flag_name == 'ztt_enable_no_passive_instances'
            assert ztt_no_passive_instances_ff_infra.sre_dev_default_flag_value == 0
            assert ztt_no_passive_instances_ff_infra.err_message == "ztt no passive instances feature is disabled for this tenant"

        # Test saas_agent feature flag
        saas_agent_no_passive_ff = FeatureFlag(name="saas_agent_enable_no_passive_instances",
                                              app='saas_agent',
                                              rel_version='5.2.0',
                                              feature="saas_agent_enable_no_passive_instances",
                                              flag_type='sre',
                                              logger=logger)
        saas_agent_no_passive_ff.add_dev_sre_default_flag_value(0)

        saas_agent_no_passive_ff.add_ff_err_msg(
            "saas agent no passive instances feature is disabled")

        assert saas_agent_no_passive_ff.rel_version == "5.2.0"
        assert saas_agent_no_passive_ff.flag_name == 'saas_agent_enable_no_passive_instances'
        assert saas_agent_no_passive_ff.sre_dev_default_flag_value == 0
        assert saas_agent_no_passive_ff.err_message == "saas agent no passive instances feature is disabled"
