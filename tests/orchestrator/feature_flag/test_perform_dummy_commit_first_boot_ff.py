import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'perform_dummy_commit_first_boot'

def test_perform_dummy_commit_first_boot_ff():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_perform_dummy_commit_first_boot_ff")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/perform_dummy_commit_first_boot_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = {'saas_agent': ['5.0.0', '5.1.0']}
        for app_name, vers in apps.items():
            for ver in vers:
                dummy_commit_first_boot_ff = FeatureFlag(name=FF_NAME,
                                                         app=app_name,
                                                         rel_version=ver,
                                                         feature='perform_dummy_commit',
                                                         flag_type='dev',
                                                         logger=logger)

                dummy_commit_first_boot_ff.add_dev_sre_default_flag_value(1)
                dummy_commit_first_boot_ff.add_ff_err_msg("dummy_commit_first_boot_ff is enabled for this tenant.")

                assert dummy_commit_first_boot_ff.rel_version == ver
                assert dummy_commit_first_boot_ff.flag_name == FF_NAME
                assert dummy_commit_first_boot_ff.flag_type == 'dev'
                assert dummy_commit_first_boot_ff.err_message == ("dummy_commit_first_boot_ff is "
                                                                  "enabled for this tenant.")
