import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'mu_30_days_count_ff'

def test_mu_30_days_count_feature_flag():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_mu_30_days_count_feature_flag")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/mu_30_days_count_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_infra']
        for app_name in apps:
            mu_30_days_count_ff = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='5.0.0',
                                        feature=FF_NAME,
                                        flag_type='dev',
                                        logger=logger)
            mu_30_days_count_ff.add_ff_err_msg(
                "mu_30_days_count_ff Feature enabled for this tenant")

            assert mu_30_days_count_ff.rel_version == "5.0.0"
            assert mu_30_days_count_ff.flag_name == 'mu_30_days_count_ff'
            assert mu_30_days_count_ff.err_message == "mu_30_days_count_ff Feature enabled for this tenant"