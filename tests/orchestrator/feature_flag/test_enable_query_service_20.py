import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'tenant_use_query_service_20'

def test_enable_query_service_20():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_enable_query_service_20.py")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/tenant_use_query_service_20.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['saas_infra']
        for app_name in apps:
            query_service_ff_infra = FeatureFlag(name=FF_NAME,
                                        app=app_name,
                                        rel_version='5.1.0',
                                        feature=FF_NAME,
                                        flag_type='dev',
                                        logger=logger)
            query_service_ff_infra.add_dev_sre_default_flag_value(0)

            query_service_ff_infra.add_ff_err_msg(
                "query service 2.0 is disabled for this tenant")

            assert query_service_ff_infra.rel_version == "5.1.0"
            assert query_service_ff_infra.flag_name == 'tenant_use_query_service_20'
            assert query_service_ff_infra.sre_dev_default_flag_value == 0
            assert query_service_ff_infra.err_message == "query service 2.0 is disabled for this tenant"
