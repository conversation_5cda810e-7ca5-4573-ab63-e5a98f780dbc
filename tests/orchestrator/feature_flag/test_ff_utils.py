import os
import sys

from utils import feature_flag_declarations


class ADD_TMP_FF_PATH:
    def __init__(self, version, module):
        ffd_dir = os.path.dirname(feature_flag_declarations.__file__)
        path = os.path.join(ffd_dir,sorted([ name for name in os.listdir(ffd_dir)
                                     if os.path.isdir(os.path.join(ffd_dir, name))
                                     and name == version])[0])

        self.path = path
        self.module = module
    
    def __enter__(self):
        sys.path.insert(0, self.path)
        __import__(self.module) 
    
    def __exit__(self, exc_type, exc_value, traceback):
        try:
            sys.path.remove(self.path)
            del sys.modules[self.module]
        except ValueError:
            pass