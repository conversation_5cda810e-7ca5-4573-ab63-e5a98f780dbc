import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

FF_NAME = 'colo_macsec_ff'
ERR_MSG = "Colo MACsec feature requires at least 6.1.0 Plugin, 10.2.10 ami"
REL_VERSION = "6.1.0"

def colo_macsec_ff_add_dep(ff_obj):
    # Enabled only with the following added dependencies
    ff_obj.add_ff_err_msg(ERR_MSG)
    ff_obj.add_ami_version_dep("10.2.10")
    ff_obj.add_panorama_plugin_dep("panorama_plugin", REL_VERSION)
    ff_obj.save()

def test_colo_macsec_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_colo_macsec_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/colo_macsec_ff.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        colo_macsec_ff = FeatureFlag(name=FF_NAME,
                                    app='plugin',
                                    rel_version=REL_VERSION,
                                    feature=FF_NAME,
                                    flag_type='compatibility',
                                    logger=logger)
        colo_macsec_ff_add_dep(colo_macsec_ff)
        assert colo_macsec_ff.rel_version == "6.1.0"
        assert colo_macsec_ff.flag_type == 'compatibility'
        assert colo_macsec_ff.err_message == ERR_MSG
        assert colo_macsec_ff.ami_dep_version == '10.2.10'
        assert colo_macsec_ff.panorama_plugin_dep['panorama_plugin'] == REL_VERSION
