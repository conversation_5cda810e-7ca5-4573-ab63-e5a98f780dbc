import mock_imports_orchestrator
import pytest
import logging
import base64
import json
import unittest
from unittest.mock import patch, MagicMock
import sys
from libs.feature_flags.feature_flags import FeatureFlag
import tempfile

def swg_auth_bypass_ff_add_dep(ff_obj):
        # Enabled only with the following added dependencies
        ff_obj.add_swg_ami_dep_version("10.2.4.1")
        ff_obj.add_fw_app_version_dep("saas_agent", "4.1.0")
        ff_obj.add_ff_err_msg("Explicit Proxy Auth Bypass Feature requires at least 4.1.0 saas agent and 10.2.4 sase ami")
        ff_obj.save()

def test_swg_auth_bypass_ff_add_dep():
    with tempfile.TemporaryDirectory() as temp_dir:
        logger = logging.getLogger("test_swg_auth_bypass_ff_add_dep")
        logger.setLevel(logging.INFO)
        tmp_log = temp_dir + "/swg_auth_bypass_feature_flag.log"
        f_handler = logging.FileHandler(tmp_log)
        f_handler.setLevel(logging.INFO)
        f_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        f_handler.setFormatter(f_format)
        logger.addHandler(f_handler)
        apps = ['plugin']

        for app_name in apps:
            swg_auth_bypass_ff = FeatureFlag(name='swg_auth_bypass_feature_flag',
                                        app=app_name, rel_version='4.1.0',
                                        feature='swg_auth_bypass_feature_flag',
                                        flag_type='compatibility', logger=logger)
            swg_auth_bypass_ff_add_dep(swg_auth_bypass_ff)
            assert swg_auth_bypass_ff.swg_ami_dep_version == "10.2.4.1"
            assert swg_auth_bypass_ff.fw_app_version["saas_agent"] == "4.1.0"
            assert swg_auth_bypass_ff.flag_type == 'compatibility'
            assert swg_auth_bypass_ff.err_message == "Explicit Proxy Auth Bypass Feature requires at least 4.1.0 saas agent and 10.2.4 sase ami"