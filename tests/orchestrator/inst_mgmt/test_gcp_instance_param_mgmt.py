import pytest
import traceback
import sys
from unittest import TestCase
from unittest.mock import patch
from unittest.mock import MagicMock as Mock
import logging
import mock_imports_orchestrator
import libs.model.instancemodel

'''
PYTHONPATH=~/saas-infra/src/apps/orchestrator/ pytest -s test_gcp_instance_param_mgmt.py
'''

class logger():
    def info(self, *args):
        #print(args)
        pass

    def error(self, *args):
        pass

    def warn(self, *args):
        pass

class Mock_conn():
    def __init__(self):
        pass

    def close(self):
        pass

class Mock_cursor():
    def __init__(self, testSuiteIdx):
        self.sql = None
        self.params = None
        self.testSuiteIdx = testSuiteIdx

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params != None:
            answer = testCases[self.testSuiteIdx]["testCaseQueryResult"].get(self.sql%self.params)
            return answer
        raise Exception("No matching dict found to lookup for this test suite.")

    def fetchone(self):
        if self.params != None:
            ret = [1]
            return ret
        return {"name" : "Tejas"}

class Mock_DBHandler():
    def __init__(self, logger, testSuiteIdx):
        self.logger = logger
        self.conn = Mock_conn()
        self.testSuiteIdx = testSuiteIdx

    def conn(self):
        return self.conn

    def get_cursor(self):
        return  Mock_cursor(self.testSuiteIdx)

    def cursorclose(self, cursor):
        return

class mocked_CustomerModel():
    def __init__(self, custid, dbh):
        self.dbh = dbh
        self.fields = ["super_acct_id", "acct_id", "id", "gpname", "probe_enable", "name",
                       "psk_secret", "is_readonly", "url", "gpfqdn", "eproxy_image_version", "project_id",
                       "asymmetric_ha_mode", "fwdrulesall", "is_ngpa_protocol_enabled", "binhp_eproxy_image_version", "uda_nhproxy_image_version", "contact_label"]
        self.values = ['123456', '123456', custid, "test", 0, "test_customer", 
                       "dummy", 0, "dummyURL", "dummyFQDN", None, "dummy", 
                       "dummy-proj", 1, "", 1, None, "dummy-contact"]

    def get_param(self, field):
        print(self.fields)
        myidx = self.fields.index(field)
        return self.values[myidx]

    def set_param(self, field, value):
        if field in self.fields:
            myidx = self.fields.index(field)
            self.values[myidx] = value
        else:
            raise Exception(f"field {field} does not exist")

    def __str__(self):
        return "dummy"
    def save(self):
        return

class mocked_instance_model():
    def __init__(self, iid, dbh):
        self.dbh = dbh
        self.fields = ["acct_id", "id", "node_type", "compute_region_idx", "name"]
        self.values = ['123456', iid, 161, 214, "test_inst"]

    def get_param(self, field):
        print(self.fields)
        myidx = self.fields.index(field)
        return self.values[myidx]

    def set_param(self, field, value):
        if field in self.fields:
            myidx = self.fields.index(field)
            self.values[myidx] = value
        else:
            raise Exception(f"field {field} does not exist")

    def get_existing_egress_ipv6_region_list_for_pinned_instance(self, cust_id, region_id, node_type=None):
        return ['401', '443']
    
    def __str__(self):
        return "dummy"
    def save(self):
        return

class mocked_ipv6_handler():
    def __init__(self, dbh, acct_id):
        self.dbh = dbh
        self.acct_id = acct_id
    
    def upsert_instance_egress_ipv6_list_subnet_location(self, my_instance_id, compute_region_idx):
        return True
    
class mocked_ExplicitProxyTenantInfoModel():
    def __init__(self, dbh, tenant_id):
        self.dbh = dbh
        self.tenant_id =  tenant_id

    def get_entry(self):
        return True

    def get_param(self, field):
        return 0

testCases = [
        { "testCaseName": "test_get_gcp_cust_region_instance_params_success",
          "testCaseQueryResult" : {
    "SELECT DISTINCT instance_master.name, instance_master.alias, instance_master.clusterid, "
    "instance_master.salt_profile, instance_master.upgrade_creation, instance_master.ha_state, "
    "instance_master.egress_ip_list, instance_master.egress_ipv6_list_subnet, instance_master.interface_ip_list, "
    "instance_master.interface_ipv6_list, instance_master.inbound_access_ip_list, "
    "instance_master.node_type, instance_master.alt_node_type, instance_master.public_ip, instance_master.is_instance_behind_nlb, "
    "instance_master.has_nat_instance, instance_master.delete_deployment, instance_master.no_passive_instance FROM instance_master "
    "WHERE custid = 15 AND compute_region_idx = 201 AND ((instance_master.node_type in (48, 51) "
    "AND ((instance_master.ha_peer is NOT NULL AND instance_master.ha_peer != 0) or instance_master.no_passive_instance = 1)) "
    "or (instance_master.node_type in (49, 50, 156, 161)))" :
        (
            ("SFW_3754_us-west-201_mlala-245139", "sfw-3754-us-west-201-mlala-245139", 3754,
             '{"Zone": "us-west2-a", "is_clean_pipe": 0, "DPInterfaceName": "nic-dp", "AcctId": "245139", '
             '"InstType": "e2-standard-4", "InstanceId": 3754, "CustId": 15, "DPSubnet": "subnet-dp-us-west2-245139", '
             '"ImageProject": "image-gpcs-nonprod-01", "DPHasExternalIP": true, "PrimaryIntfSet": 0, '
             '"RegionName": "us-west2", "MgmtHasExternalIP": false, "DPNetwork": "gpcs-vpc-dp-245139", '
             '"HANetwork": "gpcs-vpc-ha-245139", "SerialNo": "Dummy_serial_no", "static_ip": null, '
             '"ImageName": "pa-vm-saas-gcp-9-1-5-c1-saas", "HAInterfaceName": "nic-ha", '
             '"PrimaryCapacityType": "PA-CAP530", "InstanceName": "sfw-3754-us-west-201-mlala-245139", '
             '"UserData": "instance_name=SFW_3754_us-west-201_mlala-245139,saas_gpcs_api_endpoint='
             'dev3.panclouddev.com/api, cert_fetch_otp=e2f448e8-8945-4f3e-924b-2ea29595f4d2, '
             'cloud_provider=gcp,custid=245139,lambdaprefix=mlala-245139,custid_int=15,gp_domain=panclouddev.com,'
             'route53acct=a447084568087,bucket-name=pan-content-dev3,panrepo_bucket_name=panrepo-us-west-2-dev3,'
             'aws-access-key-id=,aws-secret-access-key=, mgmt-interface-swap=enable, zone=us-west2-a,zone-ha=us-west2-b,'
             ' region=us-west2, edge-compute-region=us-west-201, super_custid=245139, '
             'ssh-keys=gce-user:ssh-rsa MYKEY gce-user@SJCMACJ15HHTD8", '
             '"svc_acct": "<EMAIL>", '
             '"MgmtNetwork": "gpcs-vpc-mgmt-245139", "MgmtSubnet": "subnet-mgmt-us-west2-245139", '
             '"clean_ip_tag": false, "MgmtInterfaceName": "nic-mgmt", "HAHasExternalIP": false, '
             '"HASubnet": "subnet-ha-us-west2-245139"}',
             0, 0, {"208": "*************"}, {"208" : "subnet-ipv6-208"}, {"208": "*************"}, {"208": "2ff0:1500:ab::2"}, None, 51, -1, "************", 0, 0, 0, 0),
            ("SFW_3755_us-west-201_mlala-245139", "sfw-3755-us-west-201-mlala-245139", 3754,
             '{"Zone": "us-west2-b", "is_clean_pipe": 0, "DPInterfaceName": "nic-dp", '
             '"AcctId": "245139", "SecondaryCapacityType": "PA-CAP530", "InstType": "e2-standard-4", '
             '"clean_ip_tag": false, "CustId": 15, "DPSubnet": "subnet-dp-us-west2-245139", '
             '"ImageProject": "image-gpcs-nonprod-01", "DPHasExternalIP": false, "PrimaryIntfSet": 0, '
             '"RegionName": "us-west2", "MgmtHasExternalIP": false, "DPNetwork": "gpcs-vpc-dp-245139", '
             '"HANetwork": "gpcs-vpc-ha-245139", "SerialNo": "Dummy_serial_no", "static_ip": null, '
             '"ImageName": "pa-vm-saas-gcp-9-1-5-c1-saas", "HAInterfaceName": "nic-ha", '
             '"PrimaryCapacityType": "PA-CAP530", "InstanceName": "sfw-3755-us-west-201-mlala-245139", '
             '"UserData": "instance_name=SFW_3755_us-west-201_mlala-245139,saas_gpcs_api_endpoint='
             'dev3.panclouddev.com/api, cert_fetch_otp=9cb2270c-113d-4734-8156-72a5f16996c7, '
             'cloud_provider=gcp,custid=245139,lambdaprefix=mlala-245139,custid_int=15,'
             'gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev3,'
             'panrepo_bucket_name=panrepo-us-west-2-dev3,aws-access-key-id=,aws-secret-access-key=,'
             ' mgmt-interface-swap=enable, zone=us-west2-a,zone-ha=us-west2-b, region=us-west2, '
             'edge-compute-region=us-west-201, super_custid=245139, ssh-keys=gce-user:ssh-rsa '
             'MYKEY gce-user@SJCMACJ15HHTD8", '
             '"svc_acct": "<EMAIL>",'
             ' "MgmtNetwork": "gpcs-vpc-mgmt-245139", "MgmtSubnet": "subnet-mgmt-us-west2-245139", '
             '"InstanceId": 3755, "MgmtInterfaceName": "nic-mgmt", "HAHasExternalIP": false, '
             '"HASubnet": "subnet-ha-us-west2-245139"}',
             0, 0, {"208": "*************"},{"208" : "subnet-ipv6-208"}, None, None, None, 51, -1, "************", 0, 0, 0, 0)
        ),

    "SELECT is_enabled, custid, compute_region_id, ingress_ip_reduction FROM cust_nat_mgmt_table "
    "WHERE node_type = 49 and custid in (0, 15) ORDER BY custid DESC LIMIT 1" : ()

},
          "testCaseExpectedOutput" :{3754: {'CustId': 15,
                                 'MachineType': 'e2-standard-4',
                                 'AcctId': '245139',
                                 'ContactLabel': 'dummy-contact',
                                 'UserData': 'instance_name=SFW_3754_us-west-201_mlala-245139,saas_gpcs_api_endpoint=dev3.panclouddev.com/api, cert_fetch_otp=e2f448e8-8945-4f3e-924b-2ea29595f4d2, cloud_provider=gcp,custid=245139,lambdaprefix=mlala-245139,custid_int=15,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev3,panrepo_bucket_name=panrepo-us-west-2-dev3,aws-access-key-id=,aws-secret-access-key=, mgmt-interface-swap=enable, zone=us-west2-a,zone-ha=us-west2-b, region=us-west2, edge-compute-region=us-west-201, super_custid=245139, ssh-keys=gce-user:ssh-rsa MYKEY gce-user@SJCMACJ15HHTD8', 'PrimaryInstanceName': 'sfw-3754-us-west-201-mlala-245139',
                                 'PrimaryId': 3754,
                                 'SerialNo': 'Dummy_serial_no',
                                 'ImageProject': 'image-gpcs-nonprod-01',
                                 'PrimaryImageName': 'pa-vm-saas-gcp-9-1-5-c1-saas',
                                 'svc_acct': '<EMAIL>',
                                 'clean_ip_tag': False,
                                 'NodeType': 51,
                                 'Name': 'SFW_3755_us-west-201_mlala-245139',
                                 'PublicIp': '************',
                                 'IsBehindNlb': 0,
                                 'IsUpgradeCreation': 0,
                                 'enable_rn_to_ep_ctx_pass': 0,
                                 'enable_tls_term_on_ep': 0,
                                 'hasExternalIPv6': False,
                                 'PriAllowedAsTarget': True,
                                 'sase_fabric': 0,
                                 'commit_validate': 0,
                                 'egress_ip_list': {"208": "*************"},
                                 'egress_ipv6_list_subnet': {"208" : "subnet-ipv6-208"},
                                 'interface_ip_list': None,
                                 'interface_ipv6_list': None,
                                 'l3fwdrules': 0,
                                 'inbound_access_ip_list': None,
                                 'allowed_fw_rules': None,
                                 'static_ip': None,
                                 'PrimaryZone': 'us-west2-a',
                                 'PrimaryIntfSet': 0,
                                 'RegionName': 'us-west2',
                                 'MgmtSubnet': 'subnet-mgmt-us-west2-245139',
                                 'DPSubnet': 'subnet-dp-us-west2-245139',
                                 'HASubnet': 'subnet-ha-us-west2-245139',
                                 'MgmtNetwork': 'gpcs-vpc-mgmt-245139',
                                 'DPNetwork': 'gpcs-vpc-dp-245139',
                                 'HANetwork': 'gpcs-vpc-ha-245139',
                                 'MgmtInterfaceName': 'nic-mgmt',
                                 'DPInterfaceName': 'CLIENT_IP',
                                 'HAInterfaceName': 'nic-ha',
                                 'MgmtHasExternalIP': False,
                                 'DPHasExternalIP': True,
                                 'HAHasExternalIP': False,
                                 'PrimaryCapacityType': 'PA-CAP530',
                                 'SecondaryInstanceName': 'sfw-3755-us-west-201-mlala-245139',
                                 'SecondaryId': 3755,
                                 'SecondaryZone': 'us-west2-b',
                                 'SecondaryUserData': 'instance_name=SFW_3755_us-west-201_mlala-245139,saas_gpcs_api_endpoint=dev3.panclouddev.com/api, cert_fetch_otp=9cb2270c-113d-4734-8156-72a5f16996c7, cloud_provider=gcp,custid=245139,lambdaprefix=mlala-245139,custid_int=15,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev3,panrepo_bucket_name=panrepo-us-west-2-dev3,aws-access-key-id=,aws-secret-access-key=, mgmt-interface-swap=enable, zone=us-west2-a,zone-ha=us-west2-b, region=us-west2, edge-compute-region=us-west-201, super_custid=245139, ssh-keys=gce-user:ssh-rsa MYKEY gce-user@SJCMACJ15HHTD8',
                                 'SecondaryImageName': 'pa-vm-saas-gcp-9-1-5-c1-saas',
                                 'interface_ip_list': None,
                                 'is_using_sp_interconnect': False,
                                 'min_cpu_platform': '',
                                 'perform_ingress_ip_reduction': 0,
                                 'SecondaryIntfSet': 0,
                                 'SecondaryMachineType': 'e2-standard-4',
                                 'SecAllowedAsTarget': True,
                                 'SecondaryCapacityType': 'PA-CAP530'}},
        },
        { "testCaseName": "test_get_gcp_cust_region_instance_params_delete_cluster_success",
          "testCaseQueryResult" : {
    "SELECT DISTINCT instance_master.name, instance_master.alias, instance_master.clusterid, "
    "instance_master.salt_profile, instance_master.upgrade_creation, instance_master.ha_state, "
    "instance_master.egress_ip_list, instance_master.egress_ipv6_list_subnet, instance_master.interface_ip_list, instance_master.inbound_access_ip_list, "
    "instance_master.node_type, instance_master.public_ip, instance_master.is_instance_behind_nlb, "
    "instance_master.has_nat_instance, instance_master.delete_deployment, instance_master.no_passive_instance FROM instance_master "
    "WHERE custid = 15 AND compute_region_idx = 201 AND ((instance_master.node_type in (48, 51) "
    "AND ((instance_master.ha_peer is NOT NULL AND instance_master.ha_peer != 0) or instance_master.no_passive_instance = 1)) "
    "or (instance_master.node_type in (49, 50, 156, 161)))" :
        (
            ("SFW_3754_us-west-201_mlala-245139", "sfw-3754-us-west-201-mlala-245139", 3754,
             '{"Zone": "us-west2-a", "is_clean_pipe": 0, "DPInterfaceName": "nic-dp", "AcctId": "245139", '
             '"InstType": "e2-standard-4", "InstanceId": 3754, "CustId": 15, "DPSubnet": "subnet-dp-us-west2-245139", '
             '"ImageProject": "image-gpcs-nonprod-01", "DPHasExternalIP": true, "PrimaryIntfSet": 0, '
             '"RegionName": "us-west2", "MgmtHasExternalIP": false, "DPNetwork": "gpcs-vpc-dp-245139", '
             '"HANetwork": "gpcs-vpc-ha-245139", "SerialNo": "Dummy_serial_no", "static_ip": null, '
             '"ImageName": "pa-vm-saas-gcp-9-1-5-c1-saas", "HAInterfaceName": "nic-ha", '
             '"PrimaryCapacityType": "PA-CAP530", "InstanceName": "sfw-3754-us-west-201-mlala-245139", '
             '"UserData": "instance_name=SFW_3754_us-west-201_mlala-245139,saas_gpcs_api_endpoint='
             'dev3.panclouddev.com/api, cert_fetch_otp=e2f448e8-8945-4f3e-924b-2ea29595f4d2, '
             'cloud_provider=gcp,custid=245139,lambdaprefix=mlala-245139,custid_int=15,gp_domain=panclouddev.com,'
             'route53acct=a447084568087,bucket-name=pan-content-dev3,panrepo_bucket_name=panrepo-us-west-2-dev3,'
             'aws-access-key-id=,aws-secret-access-key=, mgmt-interface-swap=enable, zone=us-west2-a,zone-ha=us-west2-b,'
             ' region=us-west2, edge-compute-region=us-west-201, super_custid=245139, '
             'ssh-keys=gce-user:ssh-rsa MYKEY gce-user@SJCMACJ15HHTD8", '
             '"svc_acct": "<EMAIL>", '
             '"MgmtNetwork": "gpcs-vpc-mgmt-245139", "MgmtSubnet": "subnet-mgmt-us-west2-245139", '
             '"clean_ip_tag": false, "MgmtInterfaceName": "nic-mgmt", "HAHasExternalIP": false, '
             '"HASubnet": "subnet-ha-us-west2-245139"}',
             0, 0, None, None, None, None, 51, "************", 0, 0, 3754, 0),
            ("SFW_3755_us-west-201_mlala-245139", "sfw-3755-us-west-201-mlala-245139", 3754,
             '{"Zone": "us-west2-b", "is_clean_pipe": 0, "DPInterfaceName": "nic-dp", '
             '"AcctId": "245139", "SecondaryCapacityType": "PA-CAP530", "InstType": "e2-standard-4", '
             '"clean_ip_tag": false, "CustId": 15, "DPSubnet": "subnet-dp-us-west2-245139", '
             '"ImageProject": "image-gpcs-nonprod-01", "DPHasExternalIP": false, "PrimaryIntfSet": 0, '
             '"RegionName": "us-west2", "MgmtHasExternalIP": false, "DPNetwork": "gpcs-vpc-dp-245139", '
             '"HANetwork": "gpcs-vpc-ha-245139", "SerialNo": "Dummy_serial_no", "static_ip": null, '
             '"ImageName": "pa-vm-saas-gcp-9-1-5-c1-saas", "HAInterfaceName": "nic-ha", '
             '"PrimaryCapacityType": "PA-CAP530", "InstanceName": "sfw-3755-us-west-201-mlala-245139", '
             '"UserData": "instance_name=SFW_3755_us-west-201_mlala-245139,saas_gpcs_api_endpoint='
             'dev3.panclouddev.com/api, cert_fetch_otp=9cb2270c-113d-4734-8156-72a5f16996c7, '
             'cloud_provider=gcp,custid=245139,lambdaprefix=mlala-245139,custid_int=15,'
             'gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev3,'
             'panrepo_bucket_name=panrepo-us-west-2-dev3,aws-access-key-id=,aws-secret-access-key=,'
             ' mgmt-interface-swap=enable, zone=us-west2-a,zone-ha=us-west2-b, region=us-west2, '
             'edge-compute-region=us-west-201, super_custid=245139, ssh-keys=gce-user:ssh-rsa '
             'MYKEY gce-user@SJCMACJ15HHTD8", '
             '"svc_acct": "<EMAIL>",'
             ' "MgmtNetwork": "gpcs-vpc-mgmt-245139", "MgmtSubnet": "subnet-mgmt-us-west2-245139", '
             '"InstanceId": 3755, "MgmtInterfaceName": "nic-mgmt", "HAHasExternalIP": false, '
             '"HASubnet": "subnet-ha-us-west2-245139"}',
             0, 0, None, None, None, None, 51, "************", 0, 0, 3754, 0)
        ),

    "SELECT is_enabled, custid, compute_region_id, ingress_ip_reduction FROM cust_nat_mgmt_table "
    "WHERE node_type = 49 and custid in (0, 15) ORDER BY custid DESC LIMIT 1" : ()

},
          "testCaseExpectedOutput" :{},
        },
        { "testCaseName": "test_get_gcp_cust_region_instance_params_success_nlb_node_egress_ip_list",
          "testCaseQueryResult" : {
    "SELECT DISTINCT instance_master.name, instance_master.alias, instance_master.clusterid, "
    "instance_master.salt_profile, instance_master.upgrade_creation, instance_master.ha_state, "
    "instance_master.egress_ip_list, instance_master.egress_ipv6_list_subnet, instance_master.interface_ip_list, "
    "instance_master.interface_ipv6_list, instance_master.inbound_access_ip_list, "
    "instance_master.node_type, instance_master.alt_node_type, instance_master.public_ip, instance_master.is_instance_behind_nlb, "
    "instance_master.has_nat_instance, instance_master.delete_deployment, instance_master.no_passive_instance FROM instance_master "
    "WHERE custid = 15 AND compute_region_idx = 201 AND ((instance_master.node_type in (48, 51) "
    "AND ((instance_master.ha_peer is NOT NULL AND instance_master.ha_peer != 0) or instance_master.no_passive_instance = 1)) "
    "or (instance_master.node_type in (49, 50, 156, 161)))" :
        (
            ("SFW_3754_us-west-201_mlala-245139", "sfw-3754-us-west-201-mlala-245139", 3754,
             '{"Zone": "us-west2-a", "is_clean_pipe": 0, "DPInterfaceName": "nic-dp", "AcctId": "245139", '
             '"InstType": "e2-standard-4", "InstanceId": 3754, "CustId": 15, "DPSubnet": "subnet-dp-us-west2-245139", '
             '"ImageProject": "image-gpcs-nonprod-01", "DPHasExternalIP": true, "PrimaryIntfSet": 0, '
             '"RegionName": "us-west2", "MgmtHasExternalIP": false, "DPNetwork": "gpcs-vpc-dp-245139", '
             '"HANetwork": "gpcs-vpc-ha-245139", "SerialNo": "Dummy_serial_no", "static_ip": null, '
             '"ImageName": "pa-vm-saas-gcp-9-1-5-c1-saas", "HAInterfaceName": "nic-ha", '
             '"PrimaryCapacityType": "PA-CAP530", "InstanceName": "sfw-3754-us-west-201-mlala-245139", '
             '"UserData": "instance_name=SFW_3754_us-west-201_mlala-245139,saas_gpcs_api_endpoint='
             'dev3.panclouddev.com/api, cert_fetch_otp=e2f448e8-8945-4f3e-924b-2ea29595f4d2, '
             'cloud_provider=gcp,custid=245139,lambdaprefix=mlala-245139,custid_int=15,gp_domain=panclouddev.com,'
             'route53acct=a447084568087,bucket-name=pan-content-dev3,panrepo_bucket_name=panrepo-us-west-2-dev3,'
             'aws-access-key-id=,aws-secret-access-key=, mgmt-interface-swap=enable, zone=us-west2-a,zone-ha=us-west2-b,'
             ' region=us-west2, edge-compute-region=us-west-201, super_custid=245139, '
             'ssh-keys=gce-user:ssh-rsa MYKEY gce-user@SJCMACJ15HHTD8", '
             '"svc_acct": "<EMAIL>", '
             '"MgmtNetwork": "gpcs-vpc-mgmt-245139", "MgmtSubnet": "subnet-mgmt-us-west2-245139", '
             '"clean_ip_tag": false, "MgmtInterfaceName": "nic-mgmt", "HAHasExternalIP": false, '
             '"HASubnet": "subnet-ha-us-west2-245139"}',
             0, 0, {"277": "*************", "222": "**************"}, None, {"208": "*************"}, None, None, 51, -1, "************", 0, 0, 0, 0),
            ("SFW_3755_us-west-201_mlala-245139", "sfw-3755-us-west-201-mlala-245139", 3754,
             '{"Zone": "us-west2-b", "is_clean_pipe": 0, "DPInterfaceName": "nic-dp", '
             '"AcctId": "245139", "SecondaryCapacityType": "PA-CAP530", "InstType": "e2-standard-4", '
             '"clean_ip_tag": false, "CustId": 15, "DPSubnet": "subnet-dp-us-west2-245139", '
             '"ImageProject": "image-gpcs-nonprod-01", "DPHasExternalIP": false, "PrimaryIntfSet": 0, '
             '"RegionName": "us-west2", "MgmtHasExternalIP": false, "DPNetwork": "gpcs-vpc-dp-245139", '
             '"HANetwork": "gpcs-vpc-ha-245139", "SerialNo": "Dummy_serial_no", "static_ip": null, '
             '"ImageName": "pa-vm-saas-gcp-9-1-5-c1-saas", "HAInterfaceName": "nic-ha", '
             '"PrimaryCapacityType": "PA-CAP530", "InstanceName": "sfw-3755-us-west-201-mlala-245139", '
             '"UserData": "instance_name=SFW_3755_us-west-201_mlala-245139,saas_gpcs_api_endpoint='
             'dev3.panclouddev.com/api, cert_fetch_otp=9cb2270c-113d-4734-8156-72a5f16996c7, '
             'cloud_provider=gcp,custid=245139,lambdaprefix=mlala-245139,custid_int=15,'
             'gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev3,'
             'panrepo_bucket_name=panrepo-us-west-2-dev3,aws-access-key-id=,aws-secret-access-key=,'
             ' mgmt-interface-swap=enable, zone=us-west2-a,zone-ha=us-west2-b, region=us-west2, '
             'edge-compute-region=us-west-201, super_custid=245139, ssh-keys=gce-user:ssh-rsa '
             'MYKEY gce-user@SJCMACJ15HHTD8", '
             '"svc_acct": "<EMAIL>",'
             ' "MgmtNetwork": "gpcs-vpc-mgmt-245139", "MgmtSubnet": "subnet-mgmt-us-west2-245139", '
             '"InstanceId": 3755, "MgmtInterfaceName": "nic-mgmt", "HAHasExternalIP": false, '
             '"HASubnet": "subnet-ha-us-west2-245139"}',
             0, 0, {"277": "*************", "222": "**************"}, None, None, None, None, 51, -1, "************", 0, 0, 0, 0)
        ),

    "SELECT is_enabled, custid, compute_region_id, ingress_ip_reduction FROM cust_nat_mgmt_table "
    "WHERE node_type = 49 and custid in (0, 15) ORDER BY custid DESC LIMIT 1" : ()

},
          "testCaseExpectedOutput" :{3754: {'CustId': 15,
                                 'MachineType': 'e2-standard-4',
                                 'AcctId': '245139',
                                 'ContactLabel': 'dummy-contact',
                                 'UserData': 'instance_name=SFW_3754_us-west-201_mlala-245139,saas_gpcs_api_endpoint=dev3.panclouddev.com/api, cert_fetch_otp=e2f448e8-8945-4f3e-924b-2ea29595f4d2, cloud_provider=gcp,custid=245139,lambdaprefix=mlala-245139,custid_int=15,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev3,panrepo_bucket_name=panrepo-us-west-2-dev3,aws-access-key-id=,aws-secret-access-key=, mgmt-interface-swap=enable, zone=us-west2-a,zone-ha=us-west2-b, region=us-west2, edge-compute-region=us-west-201, super_custid=245139, ssh-keys=gce-user:ssh-rsa MYKEY gce-user@SJCMACJ15HHTD8', 'PrimaryInstanceName': 'sfw-3754-us-west-201-mlala-245139',
                                 'PrimaryId': 3754,
                                 'SerialNo': 'Dummy_serial_no',
                                 'ImageProject': 'image-gpcs-nonprod-01',
                                 'PrimaryImageName': 'pa-vm-saas-gcp-9-1-5-c1-saas',
                                 'svc_acct': '<EMAIL>',
                                 'clean_ip_tag': False,
                                 'NodeType': 51,
                                 'Name': 'SFW_3755_us-west-201_mlala-245139',
                                 'PublicIp': '************',
                                 'IsBehindNlb': 0,
                                 'IsUpgradeCreation': 0,
                                 'enable_rn_to_ep_ctx_pass': 0,
                                 'enable_tls_term_on_ep': 0,
                                 'hasExternalIPv6': False,
                                 'PriAllowedAsTarget': True,
                                 'sase_fabric': 0,
                                 'commit_validate': 0,
                                 'egress_ip_list': {"277": "*************", "222": "**************"},
                                 'egress_ipv6_list_subnet' :None,
                                 'interface_ip_list': None,
                                 'interface_ipv6_list': None,
                                 'l3fwdrules': 0,
                                 'inbound_access_ip_list': None,
                                 'allowed_fw_rules': None,
                                 'static_ip': None,
                                 'PrimaryZone': 'us-west2-a',
                                 'PrimaryIntfSet': 0,
                                 'RegionName': 'us-west2',
                                 'MgmtSubnet': 'subnet-mgmt-us-west2-245139',
                                 'DPSubnet': 'subnet-dp-us-west2-245139',
                                 'HASubnet': 'subnet-ha-us-west2-245139',
                                 'MgmtNetwork': 'gpcs-vpc-mgmt-245139',
                                 'DPNetwork': 'gpcs-vpc-dp-245139',
                                 'HANetwork': 'gpcs-vpc-ha-245139',
                                 'MgmtInterfaceName': 'nic-mgmt',
                                 'DPInterfaceName': 'CLIENT_IP',
                                 'HAInterfaceName': 'nic-ha',
                                 'MgmtHasExternalIP': False,
                                 'DPHasExternalIP': True,
                                 'HAHasExternalIP': False,
                                 'PrimaryCapacityType': 'PA-CAP530',
                                 'SecondaryInstanceName': 'sfw-3755-us-west-201-mlala-245139',
                                 'SecondaryId': 3755,
                                 'SecondaryZone': 'us-west2-b',
                                 'SecondaryUserData': 'instance_name=SFW_3755_us-west-201_mlala-245139,saas_gpcs_api_endpoint=dev3.panclouddev.com/api, cert_fetch_otp=9cb2270c-113d-4734-8156-72a5f16996c7, cloud_provider=gcp,custid=245139,lambdaprefix=mlala-245139,custid_int=15,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev3,panrepo_bucket_name=panrepo-us-west-2-dev3,aws-access-key-id=,aws-secret-access-key=, mgmt-interface-swap=enable, zone=us-west2-a,zone-ha=us-west2-b, region=us-west2, edge-compute-region=us-west-201, super_custid=245139, ssh-keys=gce-user:ssh-rsa MYKEY gce-user@SJCMACJ15HHTD8',
                                 'SecondaryImageName': 'pa-vm-saas-gcp-9-1-5-c1-saas',
                                 'interface_ip_list': None,
                                 'is_using_sp_interconnect': False,
                                 'min_cpu_platform': '',
                                 'perform_ingress_ip_reduction': 0,
                                 'SecondaryIntfSet': 0,
                                 'SecondaryMachineType': 'e2-standard-4',
                                 'SecAllowedAsTarget': True,
                                 'SecondaryCapacityType': 'PA-CAP530'}},
        },
        { "testCaseName": "test_get_gcp_cust_region_instance_params_success_nlb_node_egress_ip_list_from_interface_list",
          "testCaseQueryResult" : {
    "SELECT DISTINCT instance_master.name, instance_master.alias, instance_master.clusterid, "
    "instance_master.salt_profile, instance_master.upgrade_creation, instance_master.ha_state, "
    "instance_master.egress_ip_list, instance_master.egress_ipv6_list_subnet, instance_master.interface_ip_list, "
    "instance_master.interface_ipv6_list, instance_master.inbound_access_ip_list, "
    "instance_master.node_type, instance_master.alt_node_type, instance_master.public_ip, instance_master.is_instance_behind_nlb, "
    "instance_master.has_nat_instance, instance_master.delete_deployment, instance_master.no_passive_instance FROM instance_master "
    "WHERE custid = 15 AND compute_region_idx = 201 AND ((instance_master.node_type in (48, 51) "
    "AND ((instance_master.ha_peer is NOT NULL AND instance_master.ha_peer != 0) or instance_master.no_passive_instance = 1)) "
    "or (instance_master.node_type in (49, 50, 156, 161)))" :
        (
            ("SFW_3754_us-west-201_mlala-245139", "sfw-3754-us-west-201-mlala-245139", 3754,
             '{"Zone": "us-west2-a", "is_clean_pipe": 0, "DPInterfaceName": "nic-dp", "AcctId": "245139", '
             '"InstType": "e2-standard-4", "InstanceId": 3754, "CustId": 15, "DPSubnet": "subnet-dp-us-west2-245139", '
             '"ImageProject": "image-gpcs-nonprod-01", "DPHasExternalIP": true, "PrimaryIntfSet": 0, '
             '"RegionName": "us-west2", "MgmtHasExternalIP": false, "DPNetwork": "gpcs-vpc-dp-245139", '
             '"HANetwork": "gpcs-vpc-ha-245139", "SerialNo": "Dummy_serial_no", "static_ip": null, '
             '"ImageName": "pa-vm-saas-gcp-9-1-5-c1-saas", "HAInterfaceName": "nic-ha", '
             '"PrimaryCapacityType": "PA-CAP530", "InstanceName": "sfw-3754-us-west-201-mlala-245139", '
             '"UserData": "instance_name=SFW_3754_us-west-201_mlala-245139,saas_gpcs_api_endpoint='
             'dev3.panclouddev.com/api, cert_fetch_otp=e2f448e8-8945-4f3e-924b-2ea29595f4d2, '
             'cloud_provider=gcp,custid=245139,lambdaprefix=mlala-245139,custid_int=15,gp_domain=panclouddev.com,'
             'route53acct=a447084568087,bucket-name=pan-content-dev3,panrepo_bucket_name=panrepo-us-west-2-dev3,'
             'aws-access-key-id=,aws-secret-access-key=, mgmt-interface-swap=enable, zone=us-west2-a,zone-ha=us-west2-b,'
             ' region=us-west2, edge-compute-region=us-west-201, super_custid=245139, '
             'ssh-keys=gce-user:ssh-rsa MYKEY gce-user@SJCMACJ15HHTD8", '
             '"svc_acct": "<EMAIL>", '
             '"MgmtNetwork": "gpcs-vpc-mgmt-245139", "MgmtSubnet": "subnet-mgmt-us-west2-245139", '
             '"clean_ip_tag": false, "MgmtInterfaceName": "nic-mgmt", "HAHasExternalIP": false, '
             '"HASubnet": "subnet-ha-us-west2-245139"}',
             0, 0, {"277": "*************", "222": "**************"}, None, None, None, None, 51, -1, "************", 0, 0, 0, 0),
            ("SFW_3755_us-west-201_mlala-245139", "sfw-3755-us-west-201-mlala-245139", 3754,
             '{"Zone": "us-west2-b", "is_clean_pipe": 0, "DPInterfaceName": "nic-dp", '
             '"AcctId": "245139", "SecondaryCapacityType": "PA-CAP530", "InstType": "e2-standard-4", '
             '"clean_ip_tag": false, "CustId": 15, "DPSubnet": "subnet-dp-us-west2-245139", '
             '"ImageProject": "image-gpcs-nonprod-01", "DPHasExternalIP": false, "PrimaryIntfSet": 0, '
             '"RegionName": "us-west2", "MgmtHasExternalIP": false, "DPNetwork": "gpcs-vpc-dp-245139", '
             '"HANetwork": "gpcs-vpc-ha-245139", "SerialNo": "Dummy_serial_no", "static_ip": null, '
             '"ImageName": "pa-vm-saas-gcp-9-1-5-c1-saas", "HAInterfaceName": "nic-ha", '
             '"PrimaryCapacityType": "PA-CAP530", "InstanceName": "sfw-3755-us-west-201-mlala-245139", '
             '"UserData": "instance_name=SFW_3755_us-west-201_mlala-245139,saas_gpcs_api_endpoint='
             'dev3.panclouddev.com/api, cert_fetch_otp=9cb2270c-113d-4734-8156-72a5f16996c7, '
             'cloud_provider=gcp,custid=245139,lambdaprefix=mlala-245139,custid_int=15,'
             'gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev3,'
             'panrepo_bucket_name=panrepo-us-west-2-dev3,aws-access-key-id=,aws-secret-access-key=,'
             ' mgmt-interface-swap=enable, zone=us-west2-a,zone-ha=us-west2-b, region=us-west2, '
             'edge-compute-region=us-west-201, super_custid=245139, ssh-keys=gce-user:ssh-rsa '
             'MYKEY gce-user@SJCMACJ15HHTD8", '
             '"svc_acct": "<EMAIL>",'
             ' "MgmtNetwork": "gpcs-vpc-mgmt-245139", "MgmtSubnet": "subnet-mgmt-us-west2-245139", '
             '"InstanceId": 3755, "MgmtInterfaceName": "nic-mgmt", "HAHasExternalIP": false, '
             '"HASubnet": "subnet-ha-us-west2-245139"}',
             0, 0, {"277": "*************", "222": "**************"}, None, None, None, None, 51, -1, "************", 0, 0, 0, 0)
        ),

    "SELECT is_enabled, custid, compute_region_id, ingress_ip_reduction FROM cust_nat_mgmt_table "
    "WHERE node_type = 49 and custid in (0, 15) ORDER BY custid DESC LIMIT 1" : ()

},
          "testCaseExpectedOutput" :{3754: {'CustId': 15,
                                 'MachineType': 'e2-standard-4',
                                 'AcctId': '245139',
                                 'ContactLabel': 'dummy-contact',
                                 'UserData': 'instance_name=SFW_3754_us-west-201_mlala-245139,saas_gpcs_api_endpoint=dev3.panclouddev.com/api, cert_fetch_otp=e2f448e8-8945-4f3e-924b-2ea29595f4d2, cloud_provider=gcp,custid=245139,lambdaprefix=mlala-245139,custid_int=15,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev3,panrepo_bucket_name=panrepo-us-west-2-dev3,aws-access-key-id=,aws-secret-access-key=, mgmt-interface-swap=enable, zone=us-west2-a,zone-ha=us-west2-b, region=us-west2, edge-compute-region=us-west-201, super_custid=245139, ssh-keys=gce-user:ssh-rsa MYKEY gce-user@SJCMACJ15HHTD8', 'PrimaryInstanceName': 'sfw-3754-us-west-201-mlala-245139',
                                 'PrimaryId': 3754,
                                 'SerialNo': 'Dummy_serial_no',
                                 'ImageProject': 'image-gpcs-nonprod-01',
                                 'PrimaryImageName': 'pa-vm-saas-gcp-9-1-5-c1-saas',
                                 'svc_acct': '<EMAIL>',
                                 'clean_ip_tag': False,
                                 'NodeType': 51,
                                 'Name': 'SFW_3755_us-west-201_mlala-245139',
                                 'PublicIp': '************',
                                 'IsBehindNlb': 0,
                                 'IsUpgradeCreation': 0,
                                 'enable_rn_to_ep_ctx_pass': 0,
                                 'enable_tls_term_on_ep': 0,
                                 'hasExternalIPv6': False,
                                 'PriAllowedAsTarget': True,
                                 'sase_fabric': 0,
                                 'commit_validate': 0,
                                 'egress_ip_list': {'222': '**************', '277': '*************'},
                                 'egress_ipv6_list_subnet' : None,
                                 'interface_ip_list': None,
                                 'interface_ipv6_list': None,
                                 'l3fwdrules': 0,
                                 'inbound_access_ip_list': None,
                                 'allowed_fw_rules': None,
                                 'static_ip': None,
                                 'PrimaryZone': 'us-west2-a',
                                 'PrimaryIntfSet': 0,
                                 'RegionName': 'us-west2',
                                 'MgmtSubnet': 'subnet-mgmt-us-west2-245139',
                                 'DPSubnet': 'subnet-dp-us-west2-245139',
                                 'HASubnet': 'subnet-ha-us-west2-245139',
                                 'MgmtNetwork': 'gpcs-vpc-mgmt-245139',
                                 'DPNetwork': 'gpcs-vpc-dp-245139',
                                 'HANetwork': 'gpcs-vpc-ha-245139',
                                 'MgmtInterfaceName': 'nic-mgmt',
                                 'DPInterfaceName': 'CLIENT_IP',
                                 'HAInterfaceName': 'nic-ha',
                                 'MgmtHasExternalIP': False,
                                 'DPHasExternalIP': True,
                                 'HAHasExternalIP': False,
                                 'PrimaryCapacityType': 'PA-CAP530',
                                 'SecondaryInstanceName': 'sfw-3755-us-west-201-mlala-245139',
                                 'SecondaryId': 3755,
                                 'SecondaryZone': 'us-west2-b',
                                 'SecondaryUserData': 'instance_name=SFW_3755_us-west-201_mlala-245139,saas_gpcs_api_endpoint=dev3.panclouddev.com/api, cert_fetch_otp=9cb2270c-113d-4734-8156-72a5f16996c7, cloud_provider=gcp,custid=245139,lambdaprefix=mlala-245139,custid_int=15,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev3,panrepo_bucket_name=panrepo-us-west-2-dev3,aws-access-key-id=,aws-secret-access-key=, mgmt-interface-swap=enable, zone=us-west2-a,zone-ha=us-west2-b, region=us-west2, edge-compute-region=us-west-201, super_custid=245139, ssh-keys=gce-user:ssh-rsa MYKEY gce-user@SJCMACJ15HHTD8',
                                 'SecondaryImageName': 'pa-vm-saas-gcp-9-1-5-c1-saas',
                                 'interface_ip_list': None,
                                 'is_using_sp_interconnect': False,
                                 'min_cpu_platform': '',
                                 'perform_ingress_ip_reduction': 0,
                                 'SecondaryIntfSet': 0,
                                 'SecondaryMachineType': 'e2-standard-4',
                                 'SecAllowedAsTarget': True,
                                 'SecondaryCapacityType': 'PA-CAP530'}},
          },
          { "testCaseName": "Test_get_gcp_cust_region_instance_params_no_passive_success",
          "testCaseQueryResult" : {
    "SELECT DISTINCT instance_master.name, instance_master.alias, instance_master.clusterid, "
    "instance_master.salt_profile, instance_master.upgrade_creation, instance_master.ha_state, "
    "instance_master.egress_ip_list, instance_master.egress_ipv6_list_subnet, instance_master.interface_ip_list, "
    "instance_master.interface_ipv6_list, instance_master.inbound_access_ip_list, instance_master.node_type, instance_master.alt_node_type, "
    "instance_master.public_ip, instance_master.is_instance_behind_nlb, instance_master.has_nat_instance, "
    "instance_master.delete_deployment, instance_master.no_passive_instance FROM instance_master WHERE custid = 3265 AND compute_region_idx = 210 "
    "AND ((instance_master.node_type in (48, 51) AND "
    "((instance_master.ha_peer is NOT NULL AND instance_master.ha_peer != 0) or instance_master.no_passive_instance = 1)) "
    "or (instance_master.node_type in (49, 50, 156, 161)))" :
        (
            ("SFW_2183431_ap-south-1_renault3402-**********", "sfw-2183431-ap-south-1-renault3402", "2183431", '{"CustId": 3265, "AcctId": "**********", "SerialNo": "Dummy_serial_no", "InstanceName": "sfw-2183431-ap-south-1-renault3402", "InstanceId": 2183431, "is_clean_pipe": 0, "sase_fabric": 0, "commit_validate": 0, "Zone": "asia-south1-a", "RegionName": "asia-south1", "UserData": "instance_name=SFW_2183431_ap-south-1_renault3402-**********,saas_gpcs_api_endpoint=qa3.panclouddev.com/api,cert_fetch_otp=02341a60-4113-46fd-b20b-16a70280d3e8,cloud_provider=gcp,custid=**********,lambdaprefix=renault3402-**********,custid_int=3265,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-us-west-2-qa3,panrepo_bucket_name=panrepo-us-west-2-qa3,aws-access-key-id=,aws-secret-access-key=,zone=asia-south1-a,zone-ha=asia-south1-b,region=asia-south1,edge-compute-region=ap-south-1,super_custid=**********,ssh-keys=gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8,sase_fabric=False,commit_validate=0,is_nlb_supported=0,is_ngpa_protocol_enabled=0,is_central_cache_supported=0,is_instance_behind_nlb=0,central_cache_service_endpoint=pa-service-api-us-qa01.tools.panclouddev.com,central_cache_service_backup_endpoint=pa-service-api-eu-qa03.tools.panclouddev.com,is_using_sp_interconnect=False,mgmt-interface-swap=enable", "ImageProject": "image-gpcs-nonprod-01", "SharedMgmtVPCProj": "host-gpcs-test-01", "svc_acct": "<EMAIL>", "PrimaryCapacityType": "PA-CAP530", "InstType": "e2-standard-4", "ImageName": "pa-vm-saas-gcp-10-2-4-ch58-saas", "clean_ip_tag": false, "static_ip": "**************", "MgmtSubnet": "shared-mgmt-asia-south1-01", "DPSubnet": "subnet-dp-asia-south1-**********", "HASubnet": "", "MgmtNetwork": "shared-mgmt-vpc", "DPNetwork": "gpcs-vpc-dp-**********", "HANetwork": "", "MgmtInterfaceName": "nic-mgmt", "DPInterfaceName": "nic-dp", "HAInterfaceName": "", "MgmtHasExternalIP": false, "DPHasExternalIP": true, "HAHasExternalIP": false, "is_using_sp_interconnect": false, "PrimaryIntfSet": 0}', "0", "0", {}, None, None, None, None, "51", "-1", "**************", "0", "0", "0", 0),
        ),
    "SELECT is_enabled, custid, compute_region_id, ingress_ip_reduction FROM cust_nat_mgmt_table "
    "WHERE node_type = 49 and custid in (0, 15) ORDER BY custid DESC LIMIT 1" : ()
},
          "testCaseExpectedOutput" : {'2183431': {'CustId': 3265, 'ContactLabel': 'dummy-contact', 'MachineType': 'e2-standard-4', 'AcctId': '**********', 'UserData': 'instance_name=SFW_2183431_ap-south-1_renault3402-**********,saas_gpcs_api_endpoint=qa3.panclouddev.com/api,cert_fetch_otp=02341a60-4113-46fd-b20b-16a70280d3e8,cloud_provider=gcp,custid=**********,lambdaprefix=renault3402-**********,custid_int=3265,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-us-west-2-qa3,panrepo_bucket_name=panrepo-us-west-2-qa3,aws-access-key-id=,aws-secret-access-key=,zone=asia-south1-a,zone-ha=asia-south1-b,region=asia-south1,edge-compute-region=ap-south-1,super_custid=**********,ssh-keys=gce-user:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCZKSJw3BaW9cWhn3bkGLX863DXLx7VeAY/GbGqSmgnvrgDpT0E8FZVaBM0jniQt/pHK0p5c2HQwKV9E0sfOjnwL08QOhZZ8SnJKEaOPYgcFFXzffTGJuWZwWPw2ZAo+Yw8ufacEx72WaqVYugaGGDuhKnc/tFKA+nnG1dLNPh8wMaHkmakgilBMY9DMOSfHv0EeG2Ji1UvuRdlogFj5TtGR97uCBcDMmqcCLPuOLsp6jvL5ksFgukJLuOkKzXT2wMzvUIyNWOpdkUAFhZ/JDG0sbOIWVBJsFGuYpdp64W5CkY9meS5mJnVnCcVYvz7/rgzHb5AyLPwlzT2SX+6Xl9r gce-user@SJCMACJ15HHTD8,sase_fabric=False,commit_validate=0,is_nlb_supported=0,is_ngpa_protocol_enabled=0,is_central_cache_supported=0,is_instance_behind_nlb=0,central_cache_service_endpoint=pa-service-api-us-qa01.tools.panclouddev.com,central_cache_service_backup_endpoint=pa-service-api-eu-qa03.tools.panclouddev.com,is_using_sp_interconnect=False,mgmt-interface-swap=enable', 'PrimaryInstanceName': 'sfw-2183431-ap-south-1-renault3402', 'PrimaryId': 2183431, 'SerialNo': 'Dummy_serial_no', 'ImageProject': 'image-gpcs-nonprod-01', 'SharedMgmtVPCProj': 'host-gpcs-test-01', 'PrimaryImageName': 'pa-vm-saas-gcp-10-2-4-ch58-saas', 'svc_acct': '<EMAIL>', 'clean_ip_tag': False, 'NodeType': '51', 'Name': 'SFW_2183431_ap-south-1_renault3402-**********', 'PublicIp': '**************', 'IsBehindNlb': '0', 'IsUpgradeCreation': '0', 'enable_tls_term_on_ep': 0, 'enable_rn_to_ep_ctx_pass': 0, 'hasExternalIPv6': False, 'PriAllowedAsTarget': True, 'sase_fabric': 0, 'commit_validate': 0, 'is_using_sp_interconnect': False, 'min_cpu_platform': '', 'perform_ingress_ip_reduction': 0, 'egress_ip_list': {}, 'egress_ipv6_list_subnet': None, 'l3fwdrules': 0, 'inbound_access_ip_list': None, 'allowed_fw_rules': None, 'static_ip': '**************', 'PrimaryZone': 'asia-south1-a', 'PrimaryIntfSet': 0, 'RegionName': 'asia-south1', 'MgmtSubnet': 'shared-mgmt-asia-south1-01', 'DPSubnet': 'subnet-dp-asia-south1-**********', 'HASubnet': '', 'MgmtNetwork': 'shared-mgmt-vpc', 'DPNetwork': 'gpcs-vpc-dp-**********', 'HANetwork': '', 'MgmtInterfaceName': 'nic-mgmt', 'DPInterfaceName': 'CLIENT_IP', 'HAInterfaceName': '', 'MgmtHasExternalIP': False, 'DPHasExternalIP': True, 'HAHasExternalIP': False, 'PrimaryCapacityType': 'PA-CAP530'}},
          },
          { "testCaseName": "Test_get_zone_by_balancing_usage",
           "testCaseQueryResult": {
               "SELECT salt_profile FROM instance_master WHERE "
               "custid=1 AND compute_region_idx=200 AND node_type=49 AND cloud_provider='gcp' AND id!=1":
               (
                   ('{"Zone":"zone-a"}',),
                )
               }
           },
          { "testCaseName": "Test_get_zone_by_balancing_usage",
           "testCaseQueryResult": {
               "SELECT salt_profile FROM instance_master WHERE "
               "custid=1 AND compute_region_idx=200 AND node_type=49 AND cloud_provider='gcp' AND id!=1":
               (
                   ('{"Zone":"zone-a"}',),
                   ('{"Zone":"zone-b"}',),
                )
               }
           }
     ]

from libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt import get_gcp_cust_region_instance_params, get_gcp_cust_region_nlb_params

# TestCaseName: test_get_gcp_cust_region_instance_params_success, idx 0 from testCases
class Test_get_gcp_cust_region_instance_params_success(TestCase):
    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_inbound_access_list_none', return_value=True)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_ingress_ip_reduction_enabled', return_value=False)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel', side_effect=mocked_CustomerModel)
    @patch('libs.model.explicitProxyTenantInfoModel.ExplicitProxyTenantInfoModel', side_effect=mocked_ExplicitProxyTenantInfoModel)
    def test_get_gcp_cust_region_instance_params_success(self,
                                                         patched_is_inbound_access_list_none,
                                                         patched_is_ingress_ip_reduction_enabled,
                                                         patched_CustomerModel,
                                                         patched_ExplicitProxyTenantInfoModel
                                                         ):
        self._caplog.set_level(logging.INFO)
        suiteIdx=0
        myname = testCases[suiteIdx]
        dbh=Mock_DBHandler(logger=logging.getLogger('testlogger'), testSuiteIdx=suiteIdx)
        custid=15
        region_idx=201
        is_clean_pipe=0


        answer = get_gcp_cust_region_instance_params(dbh, custid, region_idx, is_clean_pipe, node_type=None, upgrade_ep_os=False)
        assert answer == testCases[0]["testCaseExpectedOutput"]

# TestCaseName: Test_get_gcp_cust_region_instance_params_delete_cluster_success, idx 1 from testCases
class Test_get_gcp_cust_region_instance_params_delete_cluster_success(TestCase):
    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_inbound_access_list_none', return_value=True)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_ingress_ip_reduction_enabled', return_value=False)
    @patch('libs.model.custmodel.CustomerModel', side_effect=mocked_CustomerModel)
    @patch('libs.model.explicitProxyTenantInfoModel.ExplicitProxyTenantInfoModel', side_effect=mocked_ExplicitProxyTenantInfoModel)
    def test_get_gcp_cust_region_instance_params_success(self,
                                                         patched_is_inbound_access_list_none,
                                                         patched_is_ingress_ip_reduction_enabled,
                                                         patched_CustomerModel,
                                                         patched_ExplicitProxyTenantInfoModel
                                                         ):
        self._caplog.set_level(logging.INFO)
        suiteIdx=1
        myname = testCases[suiteIdx]
        dbh=Mock_DBHandler(logger=logging.getLogger('testlogger'), testSuiteIdx=suiteIdx)
        custid=15
        region_idx=201
        is_clean_pipe=0

        answer = get_gcp_cust_region_instance_params(dbh, custid, region_idx, is_clean_pipe, node_type=None, upgrade_ep_os=False)
        assert answer == testCases[1]["testCaseExpectedOutput"]

class Test_get_gcp_cust_region_instance_params_success_nlb_node_egress_ip_list(TestCase):
    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    '''
      UT to check that egress IP list gets populated correctly when node is NLB type
    '''
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_inbound_access_list_none', return_value=True)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_ingress_ip_reduction_enabled', return_value=False)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel', side_effect=mocked_CustomerModel)
    @patch('libs.model.explicitProxyTenantInfoModel.ExplicitProxyTenantInfoModel', side_effect=mocked_ExplicitProxyTenantInfoModel)
    def test_get_gcp_cust_region_instance_params_success_nlb_node_egress_ip_list(self,
                                                         patched_is_inbound_access_list_none,
                                                         patched_is_ingress_ip_reduction_enabled,
                                                         patched_CustomerModel,
                                                         patched_ExplicitProxyTenantInfoModel
                                                         ):
        self._caplog.set_level(logging.INFO)
        suiteIdx=2
        myname = testCases[suiteIdx]
        dbh=Mock_DBHandler(logger=logging.getLogger('testlogger'), testSuiteIdx=suiteIdx)
        custid=15
        region_idx=201
        is_clean_pipe=0

        answer = get_gcp_cust_region_instance_params(dbh, custid, region_idx, is_clean_pipe, node_type=None, upgrade_ep_os=False)
        assert answer == testCases[2]["testCaseExpectedOutput"]

class Test_get_gcp_cust_region_instance_params_success_nlb_node_egress_ip_list_from_interface_list(TestCase):
    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    '''
      UT to check that egress IP list gets populated correctly
    '''
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_inbound_access_list_none', return_value=True)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_ingress_ip_reduction_enabled', return_value=True)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel', side_effect=mocked_CustomerModel)
    @patch('libs.model.explicitProxyTenantInfoModel.ExplicitProxyTenantInfoModel', side_effect=mocked_ExplicitProxyTenantInfoModel)
    def test_get_gcp_cust_region_instance_params_success_nlb_node_egress_ip_list_from_interface_list(self,
                                                         patched_is_inbound_access_list_none,
                                                         patched_is_ingress_ip_reduction_enabled,
                                                         patched_CustomerModel,
                                                         patched_ExplicitProxyTenantInfoModel
                                                         ):
        self._caplog.set_level(logging.INFO)
        suiteIdx=3
        myname = testCases[suiteIdx]
        dbh=Mock_DBHandler(logger=logging.getLogger('testlogger'), testSuiteIdx=suiteIdx)
        custid=15
        region_idx=201
        is_clean_pipe=0

        answer = get_gcp_cust_region_instance_params(dbh, custid, region_idx, is_clean_pipe, node_type=None, upgrade_ep_os=False)
        assert answer == testCases[3]["testCaseExpectedOutput"]

class Test_get_gcp_cust_region_instance_params_no_passive_success(TestCase):
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_inbound_access_list_none', return_value=True)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_ingress_ip_reduction_enabled', return_value=False)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel', side_effect=mocked_CustomerModel)
    @patch('libs.model.explicitProxyTenantInfoModel.ExplicitProxyTenantInfoModel', side_effect=mocked_ExplicitProxyTenantInfoModel)
    def test_get_gcp_cust_region_instance_params_no_passive_success(self,
                                                                    patched_is_inbound_access_list_none,
                                                                    patched_is_ingress_ip_reduction_enabled,
                                                                    patched_CustomerModel,
                                                                    patched_ExplicitProxyTenantInfoModel
                                                                    ):
        suiteIdx=4
        myname = testCases[suiteIdx]
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        custid=3265
        region_idx=210
        is_clean_pipe=0


        answer = get_gcp_cust_region_instance_params(dbh, custid, region_idx, is_clean_pipe, node_type=None, upgrade_ep_os=False)
        assert answer == testCases[suiteIdx]["testCaseExpectedOutput"]

# TestCaseName: test_get_gcp_cust_region_nlb_params_configure_parameters_ngpa_protocol_enabled
# Verify if session affinity is set to 3 when ngpa protocol is enabled
class Test_get_gcp_cust_region_nlb_params_configure_parameters_ngpa_protocol_enabled(TestCase):
    def test_get_gcp_cust_region_nlb_params_configure_parameters_ngpa_protocol_enabled(self):
        suiteIdx=1
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        custid=15
        region_idx=201
        #CYR-38285 temporarily setting this to regular session affinity instead of CLIENT_IP_PORT_PROTO
        #testCaseExpectedOutput = { 'ForwardingRuleProtocol': 'L3_DEFAULT', 'BackendServiceProtocol': 'UNSPECIFIED', 'SessionAffinity': 'CLIENT_IP_PORT_PROTO', 'ConnectionPersistenceOnUnhealthyBackends': 'ALWAYS_PERSIST', 'HealthCheckProtocol': 'HTTP', 'HealthCheckPort': 8000, 'HealthCheckInterval': 2, 'HealthCheckTimeout': 2, 'HealthCheckUnhealthyThreshold': 2, 'HealthCheckHealthyThreshold': 2, 'EnableStrongAffinity': 1, 'idleTimeoutSec': 600 }
        testCaseExpectedOutput = { 'ForwardingRuleProtocol': 'L3_DEFAULT', 'BackendServiceProtocol': 'UNSPECIFIED', 'SessionAffinity': 'CLIENT_IP_PORT_PROTO', 'ConnectionPersistenceOnUnhealthyBackends': 'ALWAYS_PERSIST', 'HealthCheckProtocol': 'HTTP', 'HealthCheckPort': 8000, 'HealthCheckInterval': 2, 'HealthCheckTimeout': 2, 'HealthCheckUnhealthyThreshold': 2, 'HealthCheckHealthyThreshold': 2, 'EnableStrongAffinity': 1, 'idleTimeoutSec': 1800 }
        answer = get_gcp_cust_region_nlb_params(dbh, custid, region_idx)
        assert answer == testCaseExpectedOutput

# TestCaseName: Test_get_gcp_cust_region_nlb_params_configure_parameters_enable_ssa
# Verify if EnableStrongAffinity is always set as 1 when configuring NLB parameter dict by default
class Test_get_gcp_cust_region_nlb_params_configure_parameters_enable_ssa(TestCase):
    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    def test_get_gcp_cust_region_nlb_params_configure_parameters_enable_ssa(self):
        self._caplog.set_level(logging.INFO)
        suiteIdx=1
        dbh=Mock_DBHandler(logger=logging.getLogger('testlogger'), testSuiteIdx=suiteIdx)
        custid=15
        region_idx=201
        #CYR-38285 temporarily setting this to regular session affinity instead of CLIENT_IP_PORT_PROTO
        testCaseExpectedOutput = { 'ForwardingRuleProtocol': 'L3_DEFAULT', 'BackendServiceProtocol': 'UNSPECIFIED', 'SessionAffinity': 'CLIENT_IP_PORT_PROTO', 'ConnectionPersistenceOnUnhealthyBackends': 'ALWAYS_PERSIST', 'HealthCheckProtocol': 'HTTP', 'HealthCheckPort': 8000, 'HealthCheckInterval': 2, 'HealthCheckTimeout': 2, 'HealthCheckUnhealthyThreshold': 2, 'HealthCheckHealthyThreshold': 2, 'EnableStrongAffinity': 1, 'idleTimeoutSec': 1800 }
        answer = get_gcp_cust_region_nlb_params(dbh, custid, region_idx)
        assert answer == testCaseExpectedOutput

# TestCaseName: Test_get_gcp_cust_region_nlb_params_configure_parameters_disable_ssa
# Verify if EnableStrongAffinity is always set as 0 when DB returns SSA needs to be disabled
class Test_get_gcp_cust_region_nlb_params_configure_parameters_disable_ssa(TestCase):
    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    def test_get_gcp_cust_region_nlb_params_configure_parameters_disable_ssa(self):
        self._caplog.set_level(logging.INFO)
        suiteIdx=1
        dbh=Mock_DBHandler(logger=logging.getLogger('testlogger'), testSuiteIdx=suiteIdx)
        custid=15
        region_idx=201
        answer = get_gcp_cust_region_nlb_params(dbh, custid, region_idx)
        #CYR-38285 temporarily setting this to regular session affinity instead of CLIENT_IP_PORT_PROTO
        testCaseExpectedOutput = { 'ForwardingRuleProtocol': 'L3_DEFAULT', 'BackendServiceProtocol': 'UNSPECIFIED', 'SessionAffinity': 'CLIENT_IP_PORT_PROTO', 'ConnectionPersistenceOnUnhealthyBackends': 'ALWAYS_PERSIST', 'HealthCheckProtocol': 'HTTP', 'HealthCheckPort': 8000, 'HealthCheckInterval': 2, 'HealthCheckTimeout': 2, 'HealthCheckUnhealthyThreshold': 2, 'HealthCheckHealthyThreshold': 2, 'EnableStrongAffinity': 0, 'idleTimeoutSec': 1800 }
        answer['EnableStrongAffinity'] = 0
        assert answer == testCaseExpectedOutput

from libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt import allocate_nat_nlb_egress_ip_for_new_instance_impl
class Test_gcp_egress_IP_mgmt(TestCase):
    @patch('libs.common.shared.model.IP_managememt_model_global.create_ipam_utils')
    @patch('libs.model.custmodel.CustomerModel')
    @patch('libs.model.instancemodel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.update_instances_behind_nlb')
    def test_allocate_nat_nlb_egress_ip_for_new_instance_impl(self, mock_update_instances_behind_nlb, mock_inst, mock_cust, mock_create_ipam_utils):
        suiteIdx = 1
        dbh = Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        instance_entry_dict = {'acct_id': 123, 'instance_id': 123, 'perform_ingress_ip_reduction': True}
        ret = allocate_nat_nlb_egress_ip_for_new_instance_impl(dbh, instance_entry_dict)
        assert ret is True

from libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt import allocate_egress_ipv6_for_new_instance_impl
class Test_allocate_egress_ipv6_for_new_instance_impl(TestCase):
    # TestCaseName: Test_allocate_egress_ipv6_for_new_instance_impl
    # Verify allocate_egress_ipv6_for_new_instance_impl works with NLB instances
    @patch('libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1.IPV6_Handler', wraps=mocked_ipv6_handler)
    @patch('libs.model.custmodel.CustomerModel')
    @patch('libs.model.instancemodel', wraps=mocked_instance_model)
    def test_allocate_egress_ipv6_for_new_instance_impl(self, mock_instance_model, mock_customer_model, mock_ipv6_handler):
        suiteIdx = 1
        dbh = Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        ret = allocate_egress_ipv6_for_new_instance_impl(dbh, 1234, 214, False, False)
        assert ret is True

from libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt import get_zone_by_balancing_usage
class Test_get_zone_by_balancing_usage(TestCase):
    def test_get_zone_by_balancing_usage(self):
        suiteIdx = 5
        dbh = Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        inst_prof = dict()
        inst_prof["zone"] = "zone-a"
        inst_prof["zonesList"] = ["zone-a", "zone-b"]
        my_inst = dict()
        my_inst["cust_id"] = 1
        my_inst["region"] = 200
        my_inst["node_type_id"] = 49
        my_inst["instance_id"] = 1
        ret = get_zone_by_balancing_usage(dbh, my_inst, inst_prof)
        assert ret == "zone-b"
        suiteIdx = 6
        dbh = Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        ret = get_zone_by_balancing_usage(dbh, my_inst, inst_prof)
        assert ret == "zone-a"
        pass


def get_ep_val():
    return True

from libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt import is_global_ip_needed
class Test_swg_nlb_egress_ip(TestCase):
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.swgproxy_bringup',
           return_value=Mock(gpcs_envoy_outside_panos_val=get_ep_val()))
    def test_is_global_ip_needed(self):
        suiteIdx = 1
        dbh = Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        answer = is_global_ip_needed(dbh, 1, 1, 24, 210, 153, -1)


class TestAllocateNatEdgeLocationEgressIpDp2ForNewInstanceImpl(TestCase):
    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_num_of_ips_by_instance_type')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_nat_dummy_upgrade_ongoing_in_region')
    def test_allocate_nat_edge_location_egress_ip_dp2_for_new_instance_impl_nat_instance(self, mock_is_nat_dummy_upgrade, mock_get_num_of_ips, mock_IPManagementModel, mock_CustomerModel, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger=logger())
        my_inst = MockInstanceModel(dbh=dbh)
        egress_ip_list_loc = [220]
        instance_entry_dict = {"perform_ingress_ip_reduction": False}
        is_clean_ip_project = False

        mock_CustomerModel.return_value.get_auto_scale_options.return_value = (None, None, None, True)
        mock_get_num_of_ips.return_value = 2
        mock_IPManagementModel.return_value.reserve_ips_for_cust_region.return_value = {'ok': True, 'ip_list': [['***********', 'reserved']]}
        mock_IPManagementModel.return_value.bind_allow_listed_dp2_ips_to_instance.return_value = {"ok": True}
        mock_is_nat_dummy_upgrade.return_value = False

        result = allocate_nat_edge_location_egress_ip_dp2_for_new_instance_impl(dbh, my_inst, egress_ip_list_loc, instance_entry_dict, is_clean_ip_project)

        assert result == True
        assert "Successfully bound edge IP address for instance" in caplog.text

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_num_of_ips_by_instance_type')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_nat_dummy_upgrade_ongoing_in_region')
    def test_allocate_nat_edge_location_egress_ip_dp2_for_new_instance_impl_ip_reservation_failure(self, mock_is_nat_dummy_upgrade, mock_get_num_of_ips, mock_IPManagementModel, mock_CustomerModel, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger=logger())
        my_inst = MockInstanceModel(dbh=dbh)
        egress_ip_list_loc = [220]
        instance_entry_dict = {"perform_ingress_ip_reduction": False}
        is_clean_ip_project = False

        mock_CustomerModel.return_value.get_auto_scale_options.return_value = (None, None, None, False)
        mock_get_num_of_ips.return_value = 2
        mock_IPManagementModel.return_value.reserve_ips_for_cust_region.return_value = {'ok': False}
        mock_is_nat_dummy_upgrade.return_value = False

        result = allocate_nat_edge_location_egress_ip_dp2_for_new_instance_impl(dbh, my_inst, egress_ip_list_loc, instance_entry_dict, is_clean_ip_project)

        assert result == False
        assert "Failed to reserver IP address for custid" in caplog.text

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_num_of_ips_by_instance_type')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_nat_dummy_upgrade_ongoing_in_region')
    def test_allocate_nat_edge_location_egress_ip_dp2_for_new_instance_impl_bind_ip_failure(self, mock_is_nat_dummy_upgrade, mock_get_num_of_ips, mock_IPManagementModel, mock_CustomerModel, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger=logger())
        my_inst = MockInstanceModel(dbh=dbh)
        egress_ip_list_loc = [220]
        instance_entry_dict = {"perform_ingress_ip_reduction": False}
        is_clean_ip_project = False

        mock_CustomerModel.return_value.get_auto_scale_options.return_value = (None, None, None, False)
        mock_get_num_of_ips.return_value = 2
        mock_IPManagementModel.return_value.reserve_ips_for_cust_region.return_value = {'ok': True, 'ip_list': [['***********', 'reserved']]}
        mock_IPManagementModel.return_value.bind_reserved_dp2_ips_to_instance.return_value = {"ok": False}
        mock_is_nat_dummy_upgrade.return_value = False

        result = allocate_nat_edge_location_egress_ip_dp2_for_new_instance_impl(dbh, my_inst, egress_ip_list_loc, instance_entry_dict, is_clean_ip_project)

        assert result == False
        assert "Failed to bind reserved IP addresses to instance" in caplog.text

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_num_of_ips_by_instance_type')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_nat_dummy_upgrade_ongoing_in_region')
    def test_allocate_nat_edge_location_egress_ip_dp2_for_new_instance_impl_clean_ip_project(self, mock_is_nat_dummy_upgrade, mock_get_num_of_ips, mock_IPManagementModel, mock_CustomerModel, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger=logger())
        my_inst = MockInstanceModel(dbh=dbh)
        egress_ip_list_loc = [220]
        instance_entry_dict = {"perform_ingress_ip_reduction": False}
        is_clean_ip_project = True

        mock_CustomerModel.return_value.get_auto_scale_options.return_value = (None, None, None, False)
        mock_get_num_of_ips.return_value = 2
        mock_IPManagementModel.return_value.reserve_ips_for_cust_region.return_value = {'ok': False}
        mock_is_nat_dummy_upgrade.return_value = False

        with pytest.raises(Exception) as excinfo:
            allocate_nat_edge_location_egress_ip_dp2_for_new_instance_impl(dbh, my_inst, egress_ip_list_loc, instance_entry_dict, is_clean_ip_project)

        assert "Failed to reserver IP address for custid" in str(excinfo.value)

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_num_of_ips_by_instance_type')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.is_nat_dummy_upgrade_ongoing_in_region')
    def test_allocate_nat_edge_location_egress_ip_dp2_for_new_instance_impl_nat_dummy_upgrade(self, mock_is_nat_dummy_upgrade, mock_get_num_of_ips, mock_IPManagementModel, mock_CustomerModel, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger=logger())
        my_inst = MockInstanceModel(dbh=dbh)
        egress_ip_list_loc = [220]
        instance_entry_dict = {"perform_ingress_ip_reduction": False}
        is_clean_ip_project = False

        mock_CustomerModel.return_value.get_auto_scale_options.return_value = (None, None, None, True)
        mock_get_num_of_ips.return_value = 2
        mock_IPManagementModel.return_value.reserve_ips_for_cust_region.return_value = {'ok': True, 'ip_list': [['***********', 'reserved']]}
        mock_IPManagementModel.return_value.bind_reserved_dp2_ips_to_instance.return_value = {"ok": True}
        mock_is_nat_dummy_upgrade.return_value = True

        result = allocate_nat_edge_location_egress_ip_dp2_for_new_instance_impl(dbh, my_inst, egress_ip_list_loc, instance_entry_dict, is_clean_ip_project)

        assert result == True
        assert mock_IPManagementModel.return_value.bind_reserved_dp2_ips_to_instance.called
        assert not mock_IPManagementModel.return_value.bind_allow_listed_dp2_ips_to_instance.called
class TestAllocateNatEgressIpDp2ForNewInstanceImpl(TestCase):
    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.gcp_is_clean_ip_project')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.InstanceModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_num_of_ips_by_instance_type')
    @patch('libs.db.dbhandle.DbHandle.get_gp_deployed_regions_for_cust_dbh')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.allocate_nat_edge_location_egress_ip_dp2_for_new_instance_impl')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.update_instances_behind_nlb')
    def test_allocate_nat_egress_ip_dp2_for_new_instance_impl_success(self, mock_update_nlb, mock_allocate_edge, mock_get_regions, mock_get_num_ips, mock_ip_model, mock_cust_model, mock_inst_model, mock_is_clean_ip, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger=logger())
        instance_entry_dict = {'acct_id': '123', 'instance_id': '456', 'perform_ingress_ip_reduction': False}

        mock_is_clean_ip.return_value = False
        mock_inst_model.return_value.get_param.side_effect = lambda x: {'id': '456', 'custid': '789', 'node_type': '51', 'compute_region_idx': '210', 'name': 'test_instance'}[x]
        mock_cust_model.return_value.get_auto_scale_options.return_value = (None, None, None, False)
        mock_ip_model.return_value.reserve_ips_for_cust_region.return_value = {'ok': True}
        mock_ip_model.return_value.bind_reserved_public_ip_dp2_to_instance.return_value = {'ok': True}
        mock_get_num_ips.return_value = 2
        mock_inst_model.return_value.get_existing_egress_ip_region_list_for_pinned_instance.return_value = ['220']
        mock_allocate_edge.return_value = True
        mock_update_nlb.return_value = True

        result = allocate_nat_egress_ip_dp2_for_new_instance_impl(dbh, instance_entry_dict)

        assert result == True
        assert "Allocating DP2 IP and egress IP using new API" in caplog.text
        mock_inst_model.return_value.update_column_use_PBF.assert_called_once_with(dbh, False)

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.gcp_is_clean_ip_project')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.InstanceModel')
    def test_allocate_nat_egress_ip_dp2_for_new_instance_impl_instance_not_found(self, mock_inst_model, mock_is_clean_ip, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger=logger())
        instance_entry_dict = {'acct_id': '123', 'instance_id': '456'}

        mock_is_clean_ip.return_value = False
        mock_inst_model.return_value.get_param.return_value = None

        result = allocate_nat_egress_ip_dp2_for_new_instance_impl(dbh, instance_entry_dict)

        assert result == False
        assert "Failed to get the instance ref for instance id 456" in caplog.text

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.gcp_is_clean_ip_project')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.InstanceModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_num_of_ips_by_instance_type')
    def test_allocate_nat_egress_ip_dp2_for_new_instance_impl_ip_reservation_failure(self, mock_get_num_ips, mock_ip_model, mock_cust_model, mock_inst_model, mock_is_clean_ip, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger=logger())
        instance_entry_dict = {'acct_id': '123', 'instance_id': '456'}

        mock_is_clean_ip.return_value = False
        mock_inst_model.return_value.get_param.side_effect = lambda x: {'id': '456', 'custid': '789', 'node_type': '51', 'compute_region_idx': '210', 'name': 'test_instance'}[x]
        mock_cust_model.return_value.get_auto_scale_options.return_value = (None, None, None, False)
        mock_ip_model.return_value.reserve_ips_for_cust_region.return_value = {'ok': False}
        mock_get_num_ips.return_value = 2

        result = allocate_nat_egress_ip_dp2_for_new_instance_impl(dbh, instance_entry_dict)

        assert result == False
        assert "Failed to pre-allocate IPs for instance 456 in region 210" in caplog.text

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.gcp_is_clean_ip_project')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.InstanceModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.CustomerModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.get_num_of_ips_by_instance_type')
    @patch('libs.db.dbhandle.DbHandle.get_gp_deployed_regions_for_cust_dbh')
    def test_allocate_nat_egress_ip_dp2_for_new_instance_impl_allow_list_enabled(self, mock_get_regions, mock_get_num_ips, mock_ip_model, mock_cust_model, mock_inst_model, mock_is_clean_ip, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger=logger())
        instance_entry_dict = {'acct_id': '123', 'instance_id': '456'}

        mock_is_clean_ip.return_value = False
        mock_inst_model.return_value.get_param.side_effect = lambda x: {'id': '456', 'custid': '789', 'node_type': '51', 'compute_region_idx': '210', 'name': 'test_instance'}[x]
        mock_cust_model.return_value.get_auto_scale_options.return_value = (None, None, None, True)
        mock_ip_model.return_value.reserve_ips_for_cust_region.return_value = {'ok': True}
        mock_get_num_ips.return_value = 2
        mock_get_regions.return_value = ['210']
        mock_ip_model.return_value.bind_allowed_public_ip_dp2_to_instance.return_value = {'ok': True}

        result = allocate_nat_egress_ip_dp2_for_new_instance_impl(dbh, instance_entry_dict)

        assert result == True
        mock_ip_model.return_value.bind_allowed_public_ip_dp2_to_instance.assert_called_once()


class TestAllocateGpGatewaysEgressIpDp2ForNewInstanceBehindNlbNatImpl(TestCase):
    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.gcp_is_clean_ip_project')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.InstanceModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    def test_allocate_gp_gateways_egress_ip_dp2_for_new_instance_behind_nlb_nat_impl_success(self, mock_ip_model, mock_inst_model, mock_is_clean_ip, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger=logger())
        instance_entry_dict = {'acct_id': '123', 'instance_id': '456', 'perform_ingress_ip_reduction': False}

        mock_is_clean_ip.return_value = False
        mock_inst_model.return_value.get_param.side_effect = lambda x: {'id': '456', 'compute_region_idx': '210', 'node_type': '51', 'name': 'test_instance'}[x]
        mock_ip_model.return_value.bind_non_allowed_public_ip_dp2_to_instance.return_value = {'ok': True}

        result = allocate_gp_gateways_egress_ip_dp2_for_new_instance_behind_nlb_nat_impl(dbh, instance_entry_dict)

        assert result == True
        assert "Allocating public IP and egress IP using new API" in caplog.text
        mock_ip_model.return_value.bind_non_allowed_public_ip_dp2_to_instance.assert_called_once()

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.gcp_is_clean_ip_project')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.InstanceModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    def test_allocate_gp_gateways_egress_ip_dp2_for_new_instance_behind_nlb_nat_impl_clean_ip_project(self, mock_ip_model, mock_inst_model, mock_is_clean_ip, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger=logger())
        instance_entry_dict = {'acct_id': '123', 'instance_id': '456', 'perform_ingress_ip_reduction': False}

        mock_is_clean_ip.return_value = True
        mock_inst_model.return_value.get_param.side_effect = lambda x: {'id': '456', 'compute_region_idx': '210', 'node_type': '51', 'name': 'test_instance'}[x]
        mock_ip_model.return_value.bind_non_allowed_public_ip_dp2_to_instance.return_value = {'ok': True}

        result = allocate_gp_gateways_egress_ip_dp2_for_new_instance_behind_nlb_nat_impl(dbh, instance_entry_dict)

        assert result == True
        assert "This is clean IP project" in caplog.text

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.gcp_is_clean_ip_project')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.InstanceModel')
    def test_allocate_gp_gateways_egress_ip_dp2_for_new_instance_behind_nlb_nat_impl_instance_not_found(self, mock_inst_model, mock_is_clean_ip, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger=logger())
        instance_entry_dict = {'acct_id': '123', 'instance_id': '456', 'perform_ingress_ip_reduction': False}

        mock_is_clean_ip.return_value = False
        mock_inst_model.return_value.get_param.return_value = None

        result = allocate_gp_gateways_egress_ip_dp2_for_new_instance_behind_nlb_nat_impl(dbh, instance_entry_dict)

        assert result == False
        assert "Failed to get the instance ref for instance id 456" in caplog.text

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.gcp_is_clean_ip_project')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.InstanceModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    def test_allocate_gp_gateways_egress_ip_dp2_for_new_instance_behind_nlb_nat_impl_bind_ip_failure(self, mock_ip_model, mock_inst_model, mock_is_clean_ip, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger=logger())
        instance_entry_dict = {'acct_id': '123', 'instance_id': '456', 'perform_ingress_ip_reduction': False}

        mock_is_clean_ip.return_value = False
        mock_inst_model.return_value.get_param.side_effect = lambda x: {'id': '456', 'compute_region_idx': '210', 'node_type': '51', 'name': 'test_instance'}[x]
        mock_ip_model.return_value.bind_non_allowed_public_ip_dp2_to_instance.return_value = {'ok': False}

        result = allocate_gp_gateways_egress_ip_dp2_for_new_instance_behind_nlb_nat_impl(dbh, instance_entry_dict)

        assert result == True
        assert "Failed to bind non allowed IP_DP2 to instance test_instance" in caplog.text

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.gcp_is_clean_ip_project')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.InstanceModel')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_instance_param_mgmt.IPManagementModel')
    def test_allocate_gp_gateways_egress_ip_dp2_for_new_instance_behind_nlb_nat_impl_bind_ip_failure_clean_ip(self, mock_ip_model, mock_inst_model, mock_is_clean_ip, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger=logger())
        instance_entry_dict = {'acct_id': '123', 'instance_id': '456', 'perform_ingress_ip_reduction': False}

        mock_is_clean_ip.return_value = True
        mock_inst_model.return_value.get_param.side_effect = lambda x: {'id': '456', 'compute_region_idx': '210', 'node_type': '51', 'name': 'test_instance'}[x]
        mock_ip_model.return_value.bind_non_allowed_public_ip_dp2_to_instance.return_value = {'ok': False}

        result = allocate_gp_gateways_egress_ip_dp2_for_new_instance_behind_nlb_nat_impl(dbh, instance_entry_dict)

        assert result == False
        assert "Failed to bind non allowed IP_DP2 to instance test_instance" in caplog.text
        mock_update_nlb.assert_called_once_with(dbh, '789', '210')

class TestIsRnIlbBringUp:
    def test_is_rn_ilb_bring_up_valid_inputs(self, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        custid = 12345
        region_id = 220

        result = gcp_param_mgmt.is_rn_ilb_bring_up(dbh, custid, region_id)

        assert f"Checking if RN ILB bring up is needed for tenant: {custid} and region: {region_id}" in caplog.text

    def test_is_rn_ilb_bring_up_string_custid(self, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        custid = "test_customer"
        region_id = 201

        result = gcp_param_mgmt.is_rn_ilb_bring_up(dbh, custid, region_id)

        assert f"Checking if RN ILB bring up is needed for tenant: {custid} and region: {region_id}" in caplog.text

    def test_is_rn_ilb_bring_up_zero_custid(self, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        custid = 0
        region_id = 100

        result = gcp_param_mgmt.is_rn_ilb_bring_up(dbh, custid, region_id)

        assert f"Checking if RN ILB bring up is needed for tenant: {custid} and region: {region_id}" in caplog.text

    def test_is_rn_ilb_bring_up_negative_region_id(self, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        custid = 54321
        region_id = -1

        result = gcp_param_mgmt.is_rn_ilb_bring_up(dbh, custid, region_id)

        assert f"Checking if RN ILB bring up is needed for tenant: {custid} and region: {region_id}" in caplog.text

    def test_is_rn_ilb_bring_up_large_values(self, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        custid = 999999999
        region_id = 999999

        result = gcp_param_mgmt.is_rn_ilb_bring_up(dbh, custid, region_id)

        assert f"Checking if RN ILB bring up is needed for tenant: {custid} and region: {region_id}" in caplog.text

    def test_is_rn_ilb_bring_up_none_custid(self, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        custid = None
        region_id = 210

        result = gcp_param_mgmt.is_rn_ilb_bring_up(dbh, custid, region_id)

        assert f"Checking if RN ILB bring up is needed for tenant: {custid} and region: {region_id}" in caplog.text

    def test_is_rn_ilb_bring_up_none_region_id(self, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        custid = 98765
        region_id = None

        result = gcp_param_mgmt.is_rn_ilb_bring_up(dbh, custid, region_id)

        assert f"Checking if RN ILB bring up is needed for tenant: {custid} and region: {region_id}" in caplog.text

    def test_is_rn_ilb_bring_up_string_region_id(self, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        custid = 11111
        region_id = "us-west-2"

        result = gcp_param_mgmt.is_rn_ilb_bring_up(dbh, custid, region_id)

        assert f"Checking if RN ILB bring up is needed for tenant: {custid} and region: {region_id}" in caplog.text

    def test_is_rn_ilb_bring_up_empty_string_custid(self, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        custid = ""
        region_id = 250

        result = gcp_param_mgmt.is_rn_ilb_bring_up(dbh, custid, region_id)

        assert f"Checking if RN ILB bring up is needed for tenant: {custid} and region: {region_id}" in caplog.text

    def test_is_rn_ilb_bring_up_float_values(self, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler(logger)
        custid = 123.45
        region_id = 200.5

        result = gcp_param_mgmt.is_rn_ilb_bring_up(dbh, custid, region_id)

        assert f"Checking if RN ILB bring up is needed for tenant: {custid} and region: {region_id}" in caplog.text

