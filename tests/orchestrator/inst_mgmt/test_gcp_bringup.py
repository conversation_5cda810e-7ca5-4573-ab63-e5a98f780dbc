import logging
import pytest
import traceback
import sys
from unittest import TestCase
from unittest.mock import patch
from unittest.mock import MagicMock as Mock
import mock_imports_orchestrator
import libs.model.instancemodel
import utils.Exceptions as customException
from libs.model.orchcfgmodel_v2 import OrchCfgModel_v2 as OrchCfgModel
from libs.cloud_providers.gcp.instance_manager.gcp_bringup import BringupGCPHandler, process_completed_nlb, reset_global_ip_in_public_ip_pool, calc_ipv6_interface_list
import libs.cloud_providers.gcp.instance_manager.gcp_bringup as gcp_bringup
import libs.model.instancemodel as instancemodel
from libs.common.shared.gcp_utils import gcp_authenticate
'''
PYTHONPATH=~/saas-infra/src/apps/orchestrator/ pytest -s test_gcp_bringup.py
'''

mocked_db_return = { "SELECT distinct cm.id, cm.acct_id, cm.super_acct_id, cm.name, cm.trace_id, cm.project_id, cm.subscription_id FROM `cust_master` cm  INNER JOIN orch_instance_management_table oimt ON cm.id = oimt.custid" : [["115", "100", "200", "test-res", "300", "400", "500"]],
"SELECT oimt.eidx, oimt.region, oimt.custid, oimt.stack_name, oimt.cloud_provider, oimt.debug,  oimt.debug_id, oimt.node_type from orch_instance_management_table oimt INNER JOIN   orch_instance_management_gcp_table oimat ON oimt.eidx = oimat.inst_mgmt_id   and (oimt.cloud_provider='gcp' and oimt.gcp_provision_type IN ('deployment_manager', 'terraform_import') and oimat.finished_update > oimat.finished_processing) and (not (oimat.curr_stack_state = 'ERROR' and UNIX_TIMESTAMP() < next_retry_at)  or  (oimat.finished_update >  oimat.started_processing)) AND MOD(oimat.inst_mgmt_id, 10) = 100  AND oimt.custid in (SELECT id from cust_master WHERE active_service_region = 'mock-aws-region')" : [["50", "100", "115", "700", "300", "400", "500", "600"]] }

test_instance_stack_return_ngpa_v6 = [{'name': 'GP16813', 'PrimaryId': 16813, 'PrimaryVmId': '3469518719945072071', 'PrimaryMachineType': 'e2-custom-2-10240',\
                                'PrimaryMgtIp': '***************', 'PrimaryPvtIp': '*************',\
                                'EIP': '**************', 'EIP_V6': '2606:f4c0:26cc:11b:0:33:0:0', 'EIP_V6_PREFIX_LEN': 96,\
                                'EGRESS_IPV6_LIST': {'214': '2606:f4c0:26cc:11b:8000:32:0:0/96'}},\
                                {'name': 'GP16814', 'PrimaryId': 16814, 'PrimaryVmId': '7231033631624715719', 'PrimaryMachineType': 'e2-custom-2-10240', 'PrimaryMgtIp': '***************', 'PrimaryPvtIp': '*************', 'EIP': '************', 'EIP_V6': '2606:f4c0:26cc:11b:0:32:0:0', 'EIP_V6_PREFIX_LEN': 96, 'EGRESS_IPV6_LIST': {'214': '2606:f4c0:26cc:11b:8000:33:0:0/96', '401': '2606:f4c0:26dc:11b:8000:b:0:0/96', '443': '2606:f4c0:284c:117:8000:2b:0:0/96'}},\
                                {'name': 'GP16816', 'PrimaryId': 16816, 'PrimaryVmId': '3844684319409101362', 'PrimaryMachineType': 'e2-custom-2-10240', 'PrimaryMgtIp': '***************', 'PrimaryPvtIp': '*************', 'EIP': '*************', 'EIP_V6': '2606:f4c0:26cc:11b:0:34:0:0', 'EIP_V6_PREFIX_LEN': 96, 'EGRESS_IPV6_LIST': {'214': '2606:f4c0:26cc:11b:8000:37:0:0/96', '401': '2606:f4c0:26dc:11b:8000:e:0:0/96', '443': '2606:f4c0:284c:117:8000:2a:0:0/96'}},\
                                {'name': 'NAT16817', 'PrimaryId': 16817, 'PrimaryVmId': '9124257251516013106', 'PrimaryMachineType': 'e2-standard-8', 'PrimaryMgtIp': '***************', 'PrimaryPvtIp': '*************', 'EIP': '*************'},\
                                {'name': 'NAT16819', 'PrimaryId': 16819, 'PrimaryVmId': '4072592403874503218', 'PrimaryMachineType': 'e2-standard-8', 'PrimaryMgtIp': '***************', 'PrimaryPvtIp': '*************', 'EIP': '**************'},\
                                {'name': 'GP16821', 'PrimaryId': 16821, 'PrimaryVmId': '1293129847553710480', 'PrimaryMachineType': 'e2-custom-2-10240', 'PrimaryMgtIp': '***************', 'PrimaryPvtIp': '*************', 'EIP': '**************', 'EIP_V6': '2606:f4c0:26cc:11b:0:35:0:0', 'EIP_V6_PREFIX_LEN': 96, 'EGRESS_IPV6_LIST': {'214': '2606:f4c0:26cc:11b:8000:35:0:0/96', '401': '2606:f4c0:26dc:11b:8000:f:0:0/96', '443': '2606:f4c0:284c:117:8000:29:0:0/96'}},\
                                {'name': 'GP16822', 'PrimaryId': 16822, 'PrimaryVmId': '2220852550245340261', 'PrimaryMachineType': 'e2-custom-2-10240', 'PrimaryMgtIp': '***************', 'PrimaryPvtIp': '*************', 'EIP': '**************', 'EIP_V6': '2606:f4c0:26cc:11b:0:36:0:0', 'EIP_V6_PREFIX_LEN': 96, 'EGRESS_IPV6_LIST': {'214': '2606:f4c0:26cc:11b:8000:36:0:0/96', '401': '2606:f4c0:26dc:11b:8000:c:0:0/96', '443': '2606:f4c0:284c:117:8000:2c:0:0/96'}}, {'name': 'nlb-16818-us-east-1-labreabakery-**********-umig', 'selfLink': 'https://www.googleapis.com/compute/v1/projects/cust-gpcs-mw70m27mt72csmd4zy5s/zones/us-east4-a/instanceGroups/nlb-16818-us-east-1-labreabakery-**********-umig'},\
                                {'name': 'nlb-16818-us-east-1-labreabakery-**********-hc', 'selfLink': 'https://www.googleapis.com/compute/v1/projects/cust-gpcs-mw70m27mt72csmd4zy5s/regions/us-east4/healthChecks/nlb-16818-us-east-1-labreabakery-**********-hc', 'creationTimestamp': '2023-11-06T14:12:45.934-08:00', 'region': 'us-east4'},\
                                {'name': 'nlb-16818-us-east-1-labreabakery-**********', 'backendServiceName': 'nlb-16818-us-east-1-labreabakery-10830895-bs', 'backendServiceSelfLink': 'https://www.googleapis.com/compute/v1/projects/cust-gpcs-mw70m27mt72csmd4zy5s/regions/us-east4/backendServices/nlb-16818-us-east-1-labreabakery-10830895-bs', 'forwardingRuleName': 'nlb-16818-us-east-1-labreabakery-10830895-fw-v6-443', 'forwardingRuleSelfLink': 'https://www.googleapis.com/compute/v1/projects/cust-gpcs-mw70m27mt72csmd4zy5s/regions/us-east4/forwardingRules/nlb-16818-us-east-1-labreabakery-10830895-fw-v6-443', 'IPAddressV6-214': '2606:f4c0:26cc:11b:8000:34:0:0/96', 'region': 'us-east4', 'IPAddressV6-401': '2606:f4c0:26dc:11b:8000:d:0:0/96', 'IPAddressV6-443': '2606:f4c0:284c:117:8000:28:0:0/96',\
                                 'EGRESS_IPV6_LIST': {'214': '2606:f4c0:26cc:11b:8000:34:0:0/96', '401': '2606:f4c0:26dc:11b:8000:d:0:0/96', '443': '2606:f4c0:284c:117:8000:28:0:0/96'}}]
test_instance_stack_return_ngpa_v6_compute_only = [{'name': 'GP16813', 'PrimaryId': 16813, 'PrimaryVmId': '3469518719945072071', 'PrimaryMachineType': 'e2-custom-2-10240',\
                                'PrimaryMgtIp': '***************', 'PrimaryPvtIp': '*************',\
                                'EIP': '**************', 'EIP_V6': '2606:f4c0:26cc:11b:0:33:0:0', 'EIP_V6_PREFIX_LEN': 96,\
                                'EGRESS_IPV6_LIST': {'214': '2606:f4c0:26cc:11b:8000:32:0:0/96'}},\
                                {'name': 'GP16814', 'PrimaryId': 16814, 'PrimaryVmId': '7231033631624715719', 'PrimaryMachineType': 'e2-custom-2-10240', 'PrimaryMgtIp': '***************', 'PrimaryPvtIp': '*************', 'EIP': '************', 'EIP_V6': '2606:f4c0:26cc:11b:0:32:0:0', 'EIP_V6_PREFIX_LEN': 96, 'EGRESS_IPV6_LIST': {'214': '2606:f4c0:26cc:11b:8000:33:0:0/96', '401': '2606:f4c0:26dc:11b:8000:b:0:0/96', '443': '2606:f4c0:284c:117:8000:2b:0:0/96'}},\
                                {'name': 'GP16816', 'PrimaryId': 16816, 'PrimaryVmId': '3844684319409101362', 'PrimaryMachineType': 'e2-custom-2-10240', 'PrimaryMgtIp': '***************', 'PrimaryPvtIp': '*************', 'EIP': '*************', 'EIP_V6': '2606:f4c0:26cc:11b:0:34:0:0', 'EIP_V6_PREFIX_LEN': 96, 'EGRESS_IPV6_LIST': {'214': '2606:f4c0:26cc:11b:8000:37:0:0/96', '401': '2606:f4c0:26dc:11b:8000:e:0:0/96', '443': '2606:f4c0:284c:117:8000:2a:0:0/96'}},\
                                {'name': 'NAT16817', 'PrimaryId': 16817, 'PrimaryVmId': '9124257251516013106', 'PrimaryMachineType': 'e2-standard-8', 'PrimaryMgtIp': '***************', 'PrimaryPvtIp': '*************', 'EIP': '*************'},\
                                {'name': 'NAT16819', 'PrimaryId': 16819, 'PrimaryVmId': '4072592403874503218', 'PrimaryMachineType': 'e2-standard-8', 'PrimaryMgtIp': '***************', 'PrimaryPvtIp': '*************', 'EIP': '**************'},\
                                {'name': 'GP16821', 'PrimaryId': 16821, 'PrimaryVmId': '1293129847553710480', 'PrimaryMachineType': 'e2-custom-2-10240', 'PrimaryMgtIp': '***************', 'PrimaryPvtIp': '*************', 'EIP': '**************', 'EIP_V6': '2606:f4c0:26cc:11b:0:35:0:0', 'EIP_V6_PREFIX_LEN': 96, 'EGRESS_IPV6_LIST': {'214': '2606:f4c0:26cc:11b:8000:35:0:0/96', '401': '2606:f4c0:26dc:11b:8000:f:0:0/96', '443': '2606:f4c0:284c:117:8000:29:0:0/96'}},\
                                {'name': 'GP16822', 'PrimaryId': 16822, 'PrimaryVmId': '2220852550245340261', 'PrimaryMachineType': 'e2-custom-2-10240', 'PrimaryMgtIp': '***************', 'PrimaryPvtIp': '*************', 'EIP': '**************', 'EIP_V6': '2606:f4c0:26cc:11b:0:36:0:0', 'EIP_V6_PREFIX_LEN': 96, 'EGRESS_IPV6_LIST': {'214': '2606:f4c0:26cc:11b:8000:36:0:0/96', '401': '2606:f4c0:26dc:11b:8000:c:0:0/96', '443': '2606:f4c0:284c:117:8000:2c:0:0/96'}}, {'name': 'nlb-16818-us-east-1-labreabakery-**********-umig', 'selfLink': 'https://www.googleapis.com/compute/v1/projects/cust-gpcs-mw70m27mt72csmd4zy5s/zones/us-east4-a/instanceGroups/nlb-16818-us-east-1-labreabakery-**********-umig'},\
                                {'name': 'nlb-16818-us-east-1-labreabakery-**********-hc', 'selfLink': 'https://www.googleapis.com/compute/v1/projects/cust-gpcs-mw70m27mt72csmd4zy5s/regions/us-east4/healthChecks/nlb-16818-us-east-1-labreabakery-**********-hc', 'creationTimestamp': '2023-11-06T14:12:45.934-08:00', 'region': 'us-east4'},\
                                {'name': 'nlb-16818-us-east-1-labreabakery-**********', 'backendServiceName': 'nlb-16818-us-east-1-labreabakery-10830895-bs', 'backendServiceSelfLink': 'https://www.googleapis.com/compute/v1/projects/cust-gpcs-mw70m27mt72csmd4zy5s/regions/us-east4/backendServices/nlb-16818-us-east-1-labreabakery-10830895-bs', 'forwardingRuleName': 'nlb-16818-us-east-1-labreabakery-10830895-fw-v6-443', 'forwardingRuleSelfLink': 'https://www.googleapis.com/compute/v1/projects/cust-gpcs-mw70m27mt72csmd4zy5s/regions/us-east4/forwardingRules/nlb-16818-us-east-1-labreabakery-10830895-fw-v6-443', 'IPAddressV6-214': '2606:f4c0:26cc:11b:8000:34:0:0/96', 'region': 'us-east4', 'IPAddressV6-401': '2606:f4c0:26dc:11b:8000:d:0:0/96', 'IPAddressV6-443': '2606:f4c0:284c:117:8000:28:0:0/96',\
                                 'EGRESS_IPV6_LIST': {'214': '2606:f4c0:26cc:11b:8000:34:0:0/96'}}]
test_elb_info_from_tf = [{'name': 'nlb-16953-us-west-201-labreabakery-**********', 'egress_ip_v6_list': {'nlb-16953-us-west-201-labreabakery-**********-v6-201': '2606:f4c0:270c:11b:8000:1f:0:0/96'}}]
class logger():
    def info(self, *args):
        print(*args)

    def error(self, *args):
        print(*args)

    def warn(self, *args):
        pass

    def debug(self, *args):
        pass

    def set_sub_uuid(self, *args):
        self.sub_uuid = 10

    def reset_sub_uuid(self):
        self.sub_uuid = 0

class Mock_conn():
    def __init__(self):
        pass

    def close(self):
        pass

    def commit(self):
        pass

class Mock_cursor():
    def __init__(self, testSuiteIdx):
        self.sql = None
        self.params = None
        self.testSuiteIdx = testSuiteIdx

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.sql in mocked_db_return:
            answer = mocked_db_return.get(self.sql, None)
            return answer
        else:
            raise Exception("No matching dict found to lookup for this test suite.")

    def fetchone(self):
        answer = ''
        if 'inst_mgmt_id' in self.sql:
            json_str = r'{"template-url": "http://dummy.com"}'
            answer = [50, json_str, 1, 0, 1, "ERROR", 1, 1, 0, 1, 1]
        elif 'edge_location_region_name' in self.sql:
            answer = ["dummy"] * 25
            answer[9] = "gcp"
        elif 'network_load_balancer_config' in self.sql:
            answer = [1]
        elif 'eidx, region, custid, stack_name, cloud_provider, debug, debug_id, node_type' in self.sql:
            answer = ["100"] * 8
        elif 'UPDATE orch_instance_management_gcp_table SET inst_mgmt_id=' in self.sql:
            answer = ["1"]
        return answer

    def lastrowid(self):
        pass

class Mock_DBHandler():
    def __init__(self, logger, testSuiteIdx):
        self.logger = logger
        self.conn = Mock_conn()
        self.testSuiteIdx = testSuiteIdx

    def conn(self):
        return self.conn

    def get_cursor(self):
        return  Mock_cursor(self.testSuiteIdx)

    def cursorclose(self, cursor):
        return

class mocked_CustomerModel():
    def __init__(self, custid=115, dbh=None):
        self.dbh = dbh
        self.fields = ["super_acct_id", "acct_id", "id", "gpname", "probe_enable", "name",
                       "psk_secret", "is_readonly", "url", "gpfqdn", "eproxy_image_version", "project_id",
                       "asymmetric_ha_mode", "fwdrulesall", "eproxy_image_version"]
        self.values = [1011453056, 1011453056, custid, "test", 0, "test_customer", "dummy", 0, "dummyURL", "dummyFQDN",
                       None, "dummy", "dummy-proj", 1, "", None]
    def get_all_cust_dict(self):
        return (True, {'115': {'trace_id': '300', 'acct_id': '100', 'super_acct_id': '200', 'name': 'test-res', 'project_id': '400', 'subscription_id': '500'}})

    def get_param(self, field):
        print(self.fields)
        myidx = self.fields.index(field)
        return self.values[myidx]

    def set_param(self, field, value):
        if field in self.fields:
            myidx = self.fields.index(field)
            self.values[myidx] = value
        else:
            raise Exception(f"field {field} does not exist")

    def __str__(self):
        return "dummy"
    def save(self):
        return

class mocked_InstanceManagement_GCP_Model():
    def __init__(self, dbh):
        self.dbh = dbh

class mocked_InstanceModel():
    def __init__(self, dbh=None, iid=0, custid=0,compute_region_idx=0,is_instance_behind_nlb=0,has_nat_instance=0):
        self.dbh = dbh
        self.fields = ["id", "custid", "compute_region_idx", "is_instance_behind_nlb", "has_nat_instance"]
        self.values = [iid, custid, compute_region_idx, is_instance_behind_nlb, has_nat_instance]

    def get_param(self, field):
        print(self.fields)
        myidx = self.fields.index(field)

        val = self.values[myidx]
        if val == 'None':
            return None

        return val

    def set_param(self, field, value):
        if field in self.fields:
            myidx = self.fields.index(field)
            self.values[myidx] = value
        else:
            raise Exception(f"field {field} does not exist")

    def requested_native_machine_type(self):
        return 0

    def __str__(self):
        return "dummy"

    def save(self):
        return

class TestGCPBringup():
    @patch('libs.common.shared.gcp_utils.gcp_authenticate')
    @patch('libs.common.shared.gcp_utils.__get_orch_key')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.custmgmt', side_effect=mocked_CustomerModel)
    # TestCaseName: test_process_gcp_stack_handle_ssa_not_supported_failure to UT changes added for handling SSA not supported failure from GCP
    def test_process_gcp_stack_handle_ssa_not_supported_failure(self, patched_CustomerModel, patched_get_orch_key, patched_gcp_authenticate):
        suiteIdx=0
        custid=15
        region_idx=201
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        self.avctx = Mock()
        OrchCfgModel.execute_query = Mock()
        json_str = r'{"name": "test-key", "key": 123}'
        patched_get_orch_key.return_value = json_str
        OrchCfgModel.execute_query.return_value = (True, [["1", "dummy", "dummy",  "regional", "region1","100", "200"]])
        buph = BringupGCPHandler(dbh=dbh, avctx=self.avctx, shard_id=100, replica_size=10)
        buph.template_created = Mock()
        buph.template_created.return_value = True
        buph.get_stack_status = Mock()
        buph.get_stack_status.return_value = ('ERROR', 'EnableStrongAffinity is not supported')
        buph.change_my_state = Mock()
        buph.change_my_state.return_value = True
        buph.trigger_gcp_template_update_on_ssa_config_failure = Mock()
        buph.react_to_dm_error = Mock()
        buph.process_gcp_stack()
        buph.react_to_dm_error.assert_called()

    @patch('libs.model.custmodel.CustomerModel', side_effect=mocked_CustomerModel)
    @patch('libs.common.shared.gcp_utils.__get_orch_key')
    @patch('libs.common.shared.gcp_utils.gcp_authenticate')
    # TestCaseName: test_process_gcp_stack_handle_ssa_supported where we will not re-update GCP stack as SSA is already supported
    def test_process_gcp_stack_handle_ssa_supported(self, patched_CustomerModel, patched_get_orch_key, patched_gcp_authenticate):
        suiteIdx=0
        custid=15
        region_idx=201
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        self.avctx = Mock()
        OrchCfgModel.execute_query = Mock()
        json_str = r'{"name": "test-key", "key": 123}'
        patched_get_orch_key.return_value = json_str
        OrchCfgModel.execute_query.return_value = (True, [["1", "dummy", "dummy",  "regional", "region1", "100", "200"]])
        buph = BringupGCPHandler(dbh=dbh, avctx=self.avctx, shard_id=100, replica_size=10)
        buph.template_created = Mock()
        buph.template_created.return_value = True
        buph.get_stack_status = Mock()
        buph.get_stack_status.return_value = ('ERROR', '')
        buph.change_my_state = Mock()
        buph.change_my_state.return_value = True
        buph.trigger_gcp_template_update_on_ssa_config_failure = Mock()
        buph.process_gcp_stack()
        buph.trigger_gcp_template_update_on_ssa_config_failure.assert_not_called()

    @patch('libs.model.custmodel.CustomerModel', side_effect=mocked_CustomerModel)
    @patch('libs.common.shared.gcp_utils.__get_orch_key')
    @patch('libs.common.shared.gcp_utils.gcp_authenticate')
    @patch('orchestration_service.core.orchestrator_nlb_mgmt.get_is_ngpa_protocol_enabled', return_value = "1")
    # TestCaseName: test_trigger_gcp_template_update_on_ssa_config_failure_update_gcp_stack where we will UT newly added function
    # trigger_gcp_template_update_on_ssa_config_failure
    def test_trigger_gcp_template_update_on_ssa_config_failure_update_gcp_stack(self, mock_ngpa_protocol, patched_CustomerModel, patched_get_orch_key, patched_gcp_authenticate):
        suiteIdx=0
        custid=15
        region_idx=201
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        self.avctx = Mock()
        OrchCfgModel.execute_query = Mock()
        json_str = r'{"name": "test-key", "key": 123}'
        patched_get_orch_key.return_value = json_str
        OrchCfgModel.execute_query.return_value = (True, [["1", "dummy", "dummy", "regional", "region1", "100", "200"]])
        buph = BringupGCPHandler(dbh=dbh, avctx=self.avctx, shard_id=100, replica_size=10)
        answer = buph.trigger_gcp_template_update_on_ssa_config_failure(dbh=dbh, custid=115, region_id=211, avctx=self.avctx)
        assert answer is True

    @patch('libs.model.custmodel.CustomerModel', side_effect=mocked_CustomerModel)
    @patch('libs.common.shared.gcp_utils.__get_orch_key')
    @patch('libs.common.shared.gcp_utils.gcp_authenticate')
    @patch('orchestration_service.core.orchestrator_nlb_mgmt.disable_nlb_strong_session_affinity_configuration', return_value=Mock())
    @patch('orchestration_service.core.orchestrator_nlb_mgmt.get_is_ngpa_protocol_enabled', return_value = "1")
    def test_react_to_dm_error(self, patched_ngpa_p, patchd_sa_cfg, patched_CustomerModel, patched_get_orch_key, patched_gcp_authenticate):
        suiteIdx=0
        custid=15
        region_idx=201
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        self.avctx = Mock()
        OrchCfgModel.execute_query = Mock()
        json_str = r'{"name": "test-key", "key": 123}'
        patched_get_orch_key.return_value = json_str
        OrchCfgModel.execute_query.return_value = (True, [["1", "dummy", "dummy",  "regional", "region1", "100", "200"]])
        patched_sa_cfg = Mock()
        buph = BringupGCPHandler(dbh=dbh, avctx=self.avctx, shard_id=1, replica_size=10)
        buph.error = {"errors": ["EnableStrongAffinity is not supported"]}
        fall_through, triggered = buph.react_to_dm_error(dbh, custid, region_idx)
        assert fall_through is False and triggered is True

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_nlb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_nlb_bring_up', return_value = True)
    def test_process_completed_nlb_success_with_v6(self, mock_is_nlb_bring_up, mock_get_nlb_public_ip_address, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        all_inst_output = test_instance_stack_return_ngpa_v6
        res = process_completed_nlb(dbh, my_custid, my_region_idx, all_inst_output=all_inst_output)
        assert res == {"nlb_ip_address": "**********", "nlb_ipv6_address": "2606:f4c0:26cc:11b:8000:34:0:0/96", "nlb_egress_ipv6_list_with_compute": {'214': '2606:f4c0:26cc:11b:8000:34:0:0/96', '401': '2606:f4c0:26dc:11b:8000:d:0:0/96', '443': '2606:f4c0:284c:117:8000:28:0:0/96'}, "nlb_egress_ipv6_list": {'401': '2606:f4c0:26dc:11b:8000:d:0:0/96', '443': '2606:f4c0:284c:117:8000:28:0:0/96'}}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_nlb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_nlb_bring_up', return_value = True)
    def test_process_completed_nlb_success_with_v6_empty_list(self, mock_is_nlb_bring_up, mock_get_nlb_public_ip_address, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        all_inst_output = test_instance_stack_return_ngpa_v6_compute_only
        res = process_completed_nlb(dbh, my_custid, my_region_idx, all_inst_output=all_inst_output)     
        assert res == {"nlb_ip_address": "**********", "nlb_ipv6_address": "2606:f4c0:26cc:11b:8000:34:0:0/96", "nlb_egress_ipv6_list_with_compute": {'214': '2606:f4c0:26cc:11b:8000:34:0:0/96'}, "nlb_egress_ipv6_list": {}}
        
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_nlb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_nlb_bring_up', return_value = True)
    def test_process_completed_nlb_success_with_elb_info_from_tf(self, mock_is_nlb_bring_up, mock_get_nlb_public_ip_address, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 201
        all_inst_output = test_elb_info_from_tf
        res = process_completed_nlb(dbh, my_custid, my_region_idx, all_inst_output=all_inst_output)
        assert res == {"nlb_ip_address": "**********", "nlb_ipv6_address": "2606:f4c0:270c:11b:8000:1f:0:0/96", "nlb_egress_ipv6_list_with_compute": {'201': '2606:f4c0:270c:11b:8000:1f:0:0/96'}, "nlb_egress_ipv6_list": {}}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_nlb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_nlb_bring_up', return_value = True)
    def test_process_completed_nlb_success_v4_only(self, mock_is_nlb_bring_up, mock_get_nlb_public_ip_address, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        res = process_completed_nlb(dbh, my_custid, my_region_idx, all_inst_output=None)
        assert res == {"nlb_ip_address": "**********", "nlb_ipv6_address": None, "nlb_egress_ipv6_list_with_compute": {}, "nlb_egress_ipv6_list": {}}

    @patch('libs.model.instancemodel.is_ingress_ip_reduction_enabled', return_value = False)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.InstanceModel')
    def test_process_calc_ipv6_interface_list_cannot_retrieve_instance(self, mock_instance_model, mock_ip_reduction, caplog):
        caplog.set_level(logging.INFO)
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger,testSuiteIdx=0)
        instance_id = 0
        is_instance_behind_nlb = 1
        has_nat_instance = 1
        cust_id = 95
        compute_region_id = 2001
        exception_detected = False
        exception_message = None
        mock_instance_model.return_value = mocked_InstanceModel(dbh,
                                                                instance_id,
                                                                cust_id,
                                                                compute_region_id,
                                                                is_instance_behind_nlb,
                                                                has_nat_instance)

        public_ipv6_address = '2606:f4c0:26cc:11b:9000:55:0:0/96'
        nlb_ipv6_address = '2606:f4c0:26cc:11b:9000:34:0:0/96'
        nlb_egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96', '2001': '2606:f4c0:26cc:11b:9000:34:0:0/96'}
        egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96'}
        try:
            ipv6_interface_list = calc_ipv6_interface_list(dbh, instance_id, public_ipv6_address, egress_ipv6_list, nlb_egress_ipv6_list, nlb_ipv6_address, test_logger)
        except customException.InstanceNotExistsException as e:
            exception_detected = True
            exception_message = e.args[0]
        assert exception_detected == True
        assert exception_message ==  "Instance with ID 0 not found in database."

    @patch('libs.model.instancemodel.is_ingress_ip_reduction_enabled', side_effect = customException.DbReadException("test"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.InstanceModel')
    def test_process_calc_ipv6_interface_list_exception(self, mock_instance_model, mock_ip_reduction, caplog):
        caplog.set_level(logging.INFO)
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger,testSuiteIdx=0)
        instance_id = 12345
        is_instance_behind_nlb = 1
        has_nat_instance = 1
        cust_id = 95
        compute_region_id = 2001
        mock_instance_model.return_value = mocked_InstanceModel(dbh,
                                                                instance_id,
                                                                cust_id,
                                                                compute_region_id,
                                                                is_instance_behind_nlb,
                                                                has_nat_instance)
        public_ipv6_address = '2606:f4c0:26cc:11b:9000:55:0:0/96'
        nlb_ipv6_address = '2606:f4c0:26cc:11b:9000:34:0:0/96'
        nlb_egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96', '2001': '2606:f4c0:26cc:11b:9000:34:0:0/96'}
        egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96'}
        ipv6_interface_list = calc_ipv6_interface_list(dbh, instance_id, public_ipv6_address, egress_ipv6_list, nlb_egress_ipv6_list, nlb_ipv6_address, test_logger)
        assert ipv6_interface_list == nlb_egress_ipv6_list
        assert (f"Error getting ingress IP reduction for custid") in caplog.text
        assert (f"Behind NLB: Set interface_ipv6_list for custid {cust_id} to {nlb_egress_ipv6_list}") in caplog.text

    @patch('libs.model.instancemodel.is_ingress_ip_reduction_enabled', return_value = False)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.InstanceModel')
    def test_process_calc_ipv6_interface_list_public_ipv6_none(self, mock_instance_model, mock_ip_reduction, caplog):
        caplog.set_level(logging.INFO)
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger,testSuiteIdx=0)
        instance_id = 12345
        is_instance_behind_nlb = 1
        has_nat_instance = 1
        cust_id = 95
        compute_region_id = 2001
        mock_instance_model.return_value = mocked_InstanceModel(dbh,
                                                                instance_id,
                                                                cust_id,
                                                                compute_region_id,
                                                                is_instance_behind_nlb,
                                                                has_nat_instance)
        public_ipv6_address = None
        nlb_ipv6_address = None
        nlb_egress_ipv6_list = None
        egress_ipv6_list = None
        ipv6_interface_list = calc_ipv6_interface_list(dbh, instance_id, public_ipv6_address, egress_ipv6_list, nlb_egress_ipv6_list, nlb_ipv6_address, test_logger)
        assert ipv6_interface_list == None
        assert (f"Instance {instance_id} has no public IPv6 address so interface_ipv6_list is None") in caplog.text


    @patch('libs.model.instancemodel.is_ingress_ip_reduction_enabled', return_value = False)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.InstanceModel')
    def test_process_calc_ipv6_interface_list_behind_nlb(self, mock_instance_model, mock_ip_reduction, caplog):
        caplog.set_level(logging.INFO)
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger,testSuiteIdx=0)
        instance_id = 12345
        is_instance_behind_nlb = 1
        has_nat_instance = 1
        cust_id = 95
        compute_region_id = 2001
        mock_instance_model.return_value = mocked_InstanceModel(dbh,
                                                                instance_id,
                                                                cust_id,
                                                                compute_region_id,
                                                                is_instance_behind_nlb,
                                                                has_nat_instance)
        public_ipv6_address = '2606:f4c0:26cc:11b:9000:55:0:0/96'
        nlb_ipv6_address = '2606:f4c0:26cc:11b:9000:34:0:0/96'
        nlb_egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96', '2001': '2606:f4c0:26cc:11b:9000:34:0:0/96'}
        egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96'}
        ipv6_interface_list = calc_ipv6_interface_list(dbh, instance_id, public_ipv6_address, egress_ipv6_list, nlb_egress_ipv6_list, nlb_ipv6_address, test_logger)
        assert ipv6_interface_list == nlb_egress_ipv6_list
        assert (f"Behind NLB: Set interface_ipv6_list for custid {cust_id} to {nlb_egress_ipv6_list}") in caplog.text

    @patch('libs.model.instancemodel.is_ingress_ip_reduction_enabled', return_value = True)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.InstanceModel')
    def test_process_calc_ipv6_interface_list_behind_nlb_ip_reduction(self, mock_instance_model, mock_ip_reduction, caplog):
        caplog.set_level(logging.INFO)
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger,testSuiteIdx=0)
        instance_id = 12345
        is_instance_behind_nlb = 1
        has_nat_instance = 1
        cust_id = 95
        compute_region_id = 2001
        mock_instance_model.return_value = mocked_InstanceModel(dbh,
                                                                instance_id,
                                                                cust_id,
                                                                compute_region_id,
                                                                is_instance_behind_nlb,
                                                                has_nat_instance)
        public_ipv6_address = '2606:f4c0:26cc:11b:9000:55:0:0/96'
        nlb_ipv6_address = '2606:f4c0:26cc:11b:9000:34:0:0/96'
        nlb_egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96', '2001': '2606:f4c0:26cc:11b:9000:34:0:0/96'}
        egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96'}
        nlb_compute_region = {'2001': '2606:f4c0:26cc:11b:9000:34:0:0/96'}
        ipv6_interface_list = calc_ipv6_interface_list(dbh, instance_id, public_ipv6_address, egress_ipv6_list, nlb_egress_ipv6_list, nlb_ipv6_address, test_logger)
        assert ipv6_interface_list == nlb_compute_region
        assert (f"Behind NLB with ingress IP reduction: Set interface_ipv6_list for custid {cust_id} to {nlb_compute_region}") in caplog.text

    @patch('libs.model.instancemodel.is_ingress_ip_reduction_enabled', return_value = False)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.InstanceModel')
    def test_process_calc_ipv6_interface_list_has_nat(self, mock_instance_model, mock_ip_reduction, caplog):
        caplog.set_level(logging.INFO)
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger,testSuiteIdx=0)
        instance_id = 12345
        is_instance_behind_nlb = 0
        has_nat_instance = 1
        cust_id = 95
        compute_region_id = 2001
        mock_instance_model.return_value = mocked_InstanceModel(dbh,
                                                                instance_id,
                                                                cust_id,
                                                                compute_region_id,
                                                                is_instance_behind_nlb,
                                                                has_nat_instance)
        public_ipv6_address = '2606:f4c0:26cc:11b:9000:55:0:0/96'
        nlb_ipv6_address = '2606:f4c0:26cc:11b:9000:34:0:0/96'
        nlb_egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96', '2001': '2606:f4c0:26cc:11b:9000:34:0:0/96'}
        egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96'}
        ipv6_interface_list = calc_ipv6_interface_list(dbh, instance_id, public_ipv6_address, egress_ipv6_list, nlb_egress_ipv6_list, nlb_ipv6_address, test_logger)
        assert ipv6_interface_list == nlb_egress_ipv6_list
        assert (f"Behind NLB: Set interface_ipv6_list for custid {cust_id} to {nlb_egress_ipv6_list}") in caplog.text

    @patch('libs.model.instancemodel.is_ingress_ip_reduction_enabled', return_value = False)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.InstanceModel')
    def test_process_calc_ipv6_interface_list_no_nlb_no_nat(self, mock_instance_model, mock_ip_reduction, caplog):
        caplog.set_level(logging.INFO)
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger,testSuiteIdx=0)
        instance_id = 12345
        is_instance_behind_nlb = 0
        has_nat_instance = 0
        cust_id = 95
        compute_region_id = 2001
        mock_instance_model.return_value = mocked_InstanceModel(dbh,
                                                                instance_id,
                                                                cust_id,
                                                                compute_region_id,
                                                                is_instance_behind_nlb,
                                                                has_nat_instance)
        public_ipv6_address = '2606:f4c0:26cc:11b:9000:55:0:0/96'
        nlb_ipv6_address = '2606:f4c0:26cc:11b:9000:34:0:0/96'
        nlb_egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96', '2001': '2606:f4c0:26cc:11b:9000:34:0:0/96'}
        egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96'}
        ipv6_interface_list = calc_ipv6_interface_list(dbh, instance_id, public_ipv6_address, egress_ipv6_list, nlb_egress_ipv6_list, nlb_ipv6_address, test_logger)
        assert ipv6_interface_list == egress_ipv6_list
        assert (f"Standalone: Set interface_ipv6_list for custid {cust_id} to {egress_ipv6_list}") in caplog.text

    @patch('libs.model.instancemodel.is_ingress_ip_reduction_enabled', return_value = True)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.InstanceModel')
    def test_process_calc_ipv6_interface_list_no_nlb_no_nat_ip_reduction(self, mock_instance_model, mock_ip_reduction, caplog):
        caplog.set_level(logging.INFO)
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger,testSuiteIdx=0)
        instance_id = 12345
        is_instance_behind_nlb = 0
        has_nat_instance = 0
        cust_id = 95
        compute_region_id = 2001
        mock_instance_model.return_value = mocked_InstanceModel(dbh,
                                                                instance_id,
                                                                cust_id,
                                                                compute_region_id,
                                                                is_instance_behind_nlb,
                                                                has_nat_instance)
        public_ipv6_address = '2606:f4c0:26cc:11b:9000:55:0:0/96'
        nlb_ipv6_address = '2606:f4c0:26cc:11b:9000:34:0:0/96'
        nlb_egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96', '2001': '2606:f4c0:26cc:11b:9000:34:0:0/96'}
        egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96'}
        none_list = None
        ipv6_interface_list = calc_ipv6_interface_list(dbh, instance_id, public_ipv6_address, egress_ipv6_list, nlb_egress_ipv6_list, nlb_ipv6_address, test_logger)
        assert ipv6_interface_list == none_list
        assert (f"Standalone with ingress IP reduction: Set interface_ipv6_list for custid {cust_id} to {none_list}") in caplog.text

    @patch('libs.model.instancemodel.is_ingress_ip_reduction_enabled', return_value = False)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.InstanceModel')
    def test_process_calc_ipv6_interface_list_empty_nlb_egress_ipv6_list(self, mock_instance_model, mock_ip_reduction, caplog):
        caplog.set_level(logging.INFO)
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger,testSuiteIdx=0)
        instance_id = 12345
        is_instance_behind_nlb = 0
        has_nat_instance = 0
        cust_id = 95
        compute_region_id = 2001
        mock_instance_model.return_value = mocked_InstanceModel(dbh,
                                                                instance_id,
                                                                cust_id,
                                                                compute_region_id,
                                                                is_instance_behind_nlb,
                                                                has_nat_instance)
        public_ipv6_address = '2606:f4c0:26cc:11b:9000:55:0:0/96'
        nlb_ipv6_address = None
        nlb_egress_ipv6_list = {}
        egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96'}
        ipv6_interface_list = calc_ipv6_interface_list(dbh, instance_id, public_ipv6_address, egress_ipv6_list, nlb_egress_ipv6_list, nlb_ipv6_address, test_logger)
        assert ipv6_interface_list == egress_ipv6_list
        assert (f"Standalone: Set interface_ipv6_list for custid {cust_id} to {egress_ipv6_list}") in caplog.text

    @patch('libs.model.instancemodel.is_ingress_ip_reduction_enabled', return_value = True)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.InstanceModel')
    def test_process_calc_ipv6_interface_list_empty_nlb_egress_ipv6_list_ip_reduction(self, mock_instance_model, mock_ip_reduction, caplog):
        caplog.set_level(logging.INFO)
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger,testSuiteIdx=0)
        instance_id = 12345
        is_instance_behind_nlb = 0
        has_nat_instance = 0
        cust_id = 95
        compute_region_id = 2001
        mock_instance_model.return_value = mocked_InstanceModel(dbh,
                                                                instance_id,
                                                                cust_id,
                                                                compute_region_id,
                                                                is_instance_behind_nlb,
                                                                has_nat_instance)
        public_ipv6_address = '2606:f4c0:26cc:11b:9000:55:0:0/96'
        nlb_ipv6_address = None
        nlb_egress_ipv6_list = {}
        egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96'}
        none_list = None
        ipv6_interface_list = calc_ipv6_interface_list(dbh, instance_id, public_ipv6_address, egress_ipv6_list, nlb_egress_ipv6_list, nlb_ipv6_address, test_logger)
        assert ipv6_interface_list == none_list
        assert (f"Standalone with ingress IP reduction: Set interface_ipv6_list for custid {cust_id} to {none_list}") in caplog.text

    @patch('libs.model.instancemodel.is_ingress_ip_reduction_enabled', return_value = False)
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.InstanceModel')
    def test_process_calc_ipv6_interface_list_nlb_egress_ipv6_list_none(self, mock_instance_model, mock_ip_reduction, caplog):
        caplog.set_level(logging.INFO)
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger,testSuiteIdx=0)
        instance_id = 12345
        is_instance_behind_nlb = 0
        has_nat_instance = 0
        cust_id = 95
        compute_region_id = 2001
        mock_instance_model.return_value = mocked_InstanceModel(dbh,
                                                                instance_id,
                                                                cust_id,
                                                                compute_region_id,
                                                                is_instance_behind_nlb,
                                                                has_nat_instance)
        public_ipv6_address = '2606:f4c0:26cc:11b:9000:55:0:0/96'
        nlb_ipv6_address = None
        nlb_egress_ipv6_list = None
        egress_ipv6_list = {'2000': '2606:f4c0:26cc:11b:8000:34:0:0/96'}
        ipv6_interface_list = calc_ipv6_interface_list(dbh, instance_id, public_ipv6_address, egress_ipv6_list, nlb_egress_ipv6_list, nlb_ipv6_address, test_logger)
        assert ipv6_interface_list == egress_ipv6_list
        assert (f"Standalone: Set interface_ipv6_list for custid {cust_id} to {egress_ipv6_list}") in caplog.text

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.update_nlb_state')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_nlb_public_ip_address', return_value = (None))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_nlb_bring_up', return_value = True)
    def test_process_completed_nlb_fail(self, mock_is_nlb_bring_up, mock_get_nlb_public_ip_address, mock_update_nlb_state, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        res = process_completed_nlb(dbh, my_custid, my_region_idx)
        mock_update_nlb_state.assert_not_called()
        assert res == {'nlb_egress_ipv6_list': {}, 'nlb_egress_ipv6_list_with_compute': {}, 'nlb_ip_address': None, 'nlb_ipv6_address': None}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_ipam_service_enabled', return_value=0)
    def test_reset_global_ip_in_public_ip_pool(self, patchis_ipam_service_enabled, patch_execute_query):

        #test function to reeset global ip by setting service_id as null

        suiteIdx = 3
        custid = 15
        service_id = "gpcs-proxy-nlb-us-west1-11223344"
        dbh = Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        success = reset_global_ip_in_public_ip_pool(dbh, custid, service_id)
        assert success is True

    @patch('libs.common.shared.gcp_utils.__get_orch_key')
    @patch('libs.common.shared.gcp_utils.gcp_authenticate')
    @patch('orchestration_service.core.orchestrator_nlb_mgmt.disable_nlb_strong_session_affinity_configuration',
           return_value=Mock())
    @patch('orchestration_service.core.orchestrator_nlb_mgmt.get_is_ngpa_protocol_enabled', return_value="1")
    def test_react_to_dm_error_for_inst_tmpl(self, patched_ngpa_p, patchd_sa_cfg, patched_CustomerModel, patched_get_orch_key,
                               patched_gcp_authenticate):
        suiteIdx = 0
        custid = 15
        region_idx = 201
        dbh = Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        self.avctx = Mock()
        OrchCfgModel.execute_query = Mock()
        json_str = r'{"name": "test-key", "key": 123}'
        patched_get_orch_key.return_value = json_str
        OrchCfgModel.execute_query.return_value = (True, [["1", "dummy", "dummy", "regional", "region1", "100", "200"]])
        patched_sa_cfg = Mock()
        buph = BringupGCPHandler(dbh=dbh, avctx=self.avctx, shard_id=1, replica_size=10)
        buph.error = { "errors": [
        {
          "code": "NO_METHOD_TO_UPDATE_FIELD",
          "message": "No method found to update field 'machineType' on resource 'gpcs-proxy-it-agp-us-west1-826841054' of type 'compute.v1.instanceTemplate'. The resource may need to be recreated with the new field."
        }
      ]}
        fall_through, triggered = buph.react_to_dm_error(dbh, custid, region_idx)
        assert fall_through is True and triggered is True


    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_onramp_ilb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_onramp_ilb_bring_up', return_value = True)
    def test_process_completed_onramp_ilb_success_with_v4_only(self, mock_is_onramp_ilb_bring_up, mock_get_onramp_ilb_public_ip_address, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        res = process_completed_onramp_ilb(dbh, my_custid, my_region_idx, all_inst_output=None)
        assert res == {"onramp_ilb_ip_address": "**********", "onramp_ilb_ipv6_address": None}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_onramp_ilb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_onramp_ilb_bring_up', return_value = True)
    def test_process_completed_onramp_ilb_success_with_v6(self, mock_is_onramp_ilb_bring_up, mock_get_onramp_ilb_public_ip_address, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        test_onramp_ilb_output = [{'name': 'onramp-ilb-16818-us-east-1-test', 'ipv6_address': '2606:f4c0:26cc:11b:0:40:0:0/96'}]
        res = process_completed_onramp_ilb(dbh, my_custid, my_region_idx, all_inst_output=test_onramp_ilb_output)
        assert res == {"onramp_ilb_ip_address": "**********", "onramp_ilb_ipv6_address": "2606:f4c0:26cc:11b:0:40:0:0/96"}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_onramp_ilb_public_ip_address', return_value = (None))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_onramp_ilb_bring_up', return_value = True)
    def test_process_completed_onramp_ilb_fail_no_ip(self, mock_is_onramp_ilb_bring_up, mock_get_onramp_ilb_public_ip_address, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        res = process_completed_onramp_ilb(dbh, my_custid, my_region_idx)
        assert res == {"onramp_ilb_ip_address": None, "onramp_ilb_ipv6_address": None}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_onramp_ilb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_onramp_ilb_bring_up', return_value = False)
    def test_process_completed_onramp_ilb_not_bring_up(self, mock_is_onramp_ilb_bring_up, mock_get_onramp_ilb_public_ip_address, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        res = process_completed_onramp_ilb(dbh, my_custid, my_region_idx)
        assert res == {"onramp_ilb_ip_address": None, "onramp_ilb_ipv6_address": None}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_onramp_ilb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_onramp_ilb_bring_up', return_value = True)
    def test_process_completed_onramp_ilb_with_empty_inst_output(self, mock_is_onramp_ilb_bring_up, mock_get_onramp_ilb_public_ip_address, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        test_empty_output = []
        res = process_completed_onramp_ilb(dbh, my_custid, my_region_idx, all_inst_output=test_empty_output)
        assert res == {"onramp_ilb_ip_address": "**********", "onramp_ilb_ipv6_address": None}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_onramp_ilb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_onramp_ilb_bring_up', return_value = True)
    def test_process_completed_onramp_ilb_with_multiple_entries(self, mock_is_onramp_ilb_bring_up, mock_get_onramp_ilb_public_ip_address, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        test_multiple_output = [
            {'name': 'onramp-ilb-16818-us-east-1-test', 'ipv6_address': '2606:f4c0:26cc:11b:0:40:0:0/96'},
            {'name': 'other-resource', 'ipv4_address': '**********'},
            {'name': 'onramp-ilb-16819-us-west-1-test', 'ipv6_address': '2606:f4c0:26cc:11b:0:41:0:0/96'}
        ]
        res = process_completed_onramp_ilb(dbh, my_custid, my_region_idx, all_inst_output=test_multiple_output)
        assert res == {"onramp_ilb_ip_address": "**********", "onramp_ilb_ipv6_address": "2606:f4c0:26cc:11b:0:40:0:0/96"}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_onramp_ilb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_onramp_ilb_bring_up', return_value = True)
    def test_process_completed_onramp_ilb_with_missing_ipv6_field(self, mock_is_onramp_ilb_bring_up, mock_get_onramp_ilb_public_ip_address, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        test_missing_ipv6_output = [{'name': 'onramp-ilb-16818-us-east-1-test', 'ipv4_address': '**********'}]
        res = process_completed_onramp_ilb(dbh, my_custid, my_region_idx, all_inst_output=test_missing_ipv6_output)
        assert res == {"onramp_ilb_ip_address": "**********", "onramp_ilb_ipv6_address": None}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_ilb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_ilb_bring_up', return_value = True)
    def test_process_completed_ilb_success_with_v6(self, mock_is_ilb_bring_up, mock_get_ilb_public_ip_address, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        all_inst_output = test_instance_stack_return_ngpa_v6
        res = process_completed_ilb(dbh, my_custid, my_region_idx, all_inst_output=all_inst_output)
        assert res == {"ilb_ip_address": "**********", "ilb_ipv6_address": "2606:f4c0:26cc:11b:8000:34:0:0/96", "ilb_egress_ipv6_list_with_compute": {'214': '2606:f4c0:26cc:11b:8000:34:0:0/96', '401': '2606:f4c0:26dc:11b:8000:d:0:0/96', '443': '2606:f4c0:284c:117:8000:28:0:0/96'}, "ilb_egress_ipv6_list": {'401': '2606:f4c0:26dc:11b:8000:d:0:0/96', '443': '2606:f4c0:284c:117:8000:28:0:0/96'}}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_ilb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_ilb_bring_up', return_value = True)
    def test_process_completed_ilb_success_with_v6_empty_list(self, mock_is_ilb_bring_up, mock_get_ilb_public_ip_address, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        all_inst_output = test_instance_stack_return_ngpa_v6_compute_only
        res = process_completed_ilb(dbh, my_custid, my_region_idx, all_inst_output=all_inst_output)
        assert res == {"ilb_ip_address": "**********", "ilb_ipv6_address": "2606:f4c0:26cc:11b:8000:34:0:0/96", "ilb_egress_ipv6_list_with_compute": {'214': '2606:f4c0:26cc:11b:8000:34:0:0/96'}, "ilb_egress_ipv6_list": {}}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_ilb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_ilb_bring_up', return_value = True)
    def test_process_completed_ilb_success_with_elb_info_from_tf(self, mock_is_ilb_bring_up, mock_get_ilb_public_ip_address, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 201
        all_inst_output = test_elb_info_from_tf
        res = process_completed_ilb(dbh, my_custid, my_region_idx, all_inst_output=all_inst_output)
        assert res == {"ilb_ip_address": "**********", "ilb_ipv6_address": "2606:f4c0:270c:11b:8000:1f:0:0/96", "ilb_egress_ipv6_list_with_compute": {'201': '2606:f4c0:270c:11b:8000:1f:0:0/96'}, "ilb_egress_ipv6_list": {}}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_ilb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_ilb_bring_up', return_value = True)
    def test_process_completed_ilb_success_v4_only(self, mock_is_ilb_bring_up, mock_get_ilb_public_ip_address, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        res = process_completed_ilb(dbh, my_custid, my_region_idx, all_inst_output=None)
        assert res == {"ilb_ip_address": "**********", "ilb_ipv6_address": None, "ilb_egress_ipv6_list_with_compute": {}, "ilb_egress_ipv6_list": {}}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.update_ilb_state')
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_ilb_public_ip_address', return_value = (None))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_ilb_bring_up', return_value = True)
    def test_process_completed_ilb_fail(self, mock_is_ilb_bring_up, mock_get_ilb_public_ip_address, mock_update_ilb_state, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        res = process_completed_ilb(dbh, my_custid, my_region_idx)
        mock_update_ilb_state.assert_not_called()
        assert res == {'ilb_egress_ipv6_list': {}, 'ilb_egress_ipv6_list_with_compute': {}, 'ilb_ip_address': None, 'ilb_ipv6_address': None}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_ilb_bring_up', return_value = False)
    def test_process_completed_ilb_not_bring_up(self, mock_is_ilb_bring_up, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        res = process_completed_ilb(dbh, my_custid, my_region_idx)
        assert res == {'ilb_egress_ipv6_list': {}, 'ilb_egress_ipv6_list_with_compute': {}, 'ilb_ip_address': None, 'ilb_ipv6_address': None}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_ilb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_ilb_bring_up', return_value = True)
    def test_process_completed_ilb_no_egress_ipv6_list(self, mock_is_ilb_bring_up, mock_get_ilb_public_ip_address, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        all_inst_output = [{'name': 'GP16813', 'PrimaryId': 16813, 'EIP': '**************'}]
        res = process_completed_ilb(dbh, my_custid, my_region_idx, all_inst_output=all_inst_output)
        assert res == {"ilb_ip_address": "**********", "ilb_ipv6_address": None, "ilb_egress_ipv6_list_with_compute": {}, "ilb_egress_ipv6_list": {}}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', return_value=Mock())
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_ilb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_ilb_bring_up', return_value = True)
    def test_process_completed_ilb_empty_all_inst_output(self, mock_is_ilb_bring_up, mock_get_ilb_public_ip_address, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        all_inst_output = []
        res = process_completed_ilb(dbh, my_custid, my_region_idx, all_inst_output=all_inst_output)
        assert res == {"ilb_ip_address": "**********", "ilb_ipv6_address": None, "ilb_egress_ipv6_list_with_compute": {}, "ilb_egress_ipv6_list": {}}

    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.execute_query', side_effect=Exception("Database error"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.get_ilb_public_ip_address', return_value = ("**********"))
    @patch('libs.cloud_providers.gcp.instance_manager.gcp_bringup.is_ilb_bring_up', return_value = True)
    def test_process_completed_ilb_database_exception(self, mock_is_ilb_bring_up, mock_get_ilb_public_ip_address, mock_execute_query, caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        my_custid = 229
        my_region_idx = 214
        all_inst_output = test_instance_stack_return_ngpa_v6
        exception_detected = False
        try:
            res = process_completed_ilb(dbh, my_custid, my_region_idx, all_inst_output=all_inst_output)
        except Exception as e:
            exception_detected = True
        assert exception_detected == True

