# import pytest
# import traceback
# import sys
# from unittest import TestCase
# from unittest.mock import patch
# from unittest.mock import MagicMock as Mock
# import mock_imports_orchestrator
# import libs.model.instancemodel
#
# '''
# PYTHONPATH=~/saas-infra/src/apps/orchestrator/ pytest -s test_aws_instance_param_mgmt.py
# '''
#
# class logger():
#     def info(self, *args):
#         print(args)
#
#     def error(self, *args):
#         print(args)
#
#     def warn(self, *args):
#         print(args)
#
# class Mock_conn():
#     def __init__(self):
#         pass
#
#     def close(self):
#         pass
#
# class Mock_cursor():
#     def __init__(self, testSuiteIdx):
#         self.sql = None
#         self.params = None
#         self.testSuiteIdx = testSuiteIdx
#
#     def execute(self, sql, params):
#         self.sql = sql
#         self.params = params
#
#     def fetchall(self):
#         if self.params != None:
#             answer = testCases[self.testSuiteIdx]["testCaseQueryResult"].values()
#             return answer
#         raise Exception("No matching dict found to lookup for this test suite.")
#
#     def fetchone(self):
#         return {"name" : "test"}
#
# class Mock_DBHandler():
#     def __init__(self, logger, testSuiteIdx):
#         self.logger = logger
#         self.conn = Mock_conn()
#         self.testSuiteIdx = testSuiteIdx
#
#     def conn(self):
#         return self.conn
#
#     def get_cursor(self):
#         return  Mock_cursor(self.testSuiteIdx)
#
#     def cursorclose(self, cursor):
#         return
#
# class mocked_CustomerModel():
#     def __init__(self, custid, dbh):
#         self.id =  custid
#         self.acct_id = '123456'
#         self.dbh = dbh
#         self.fwdrulesall = ""
#
# class mocked_ProbeVMTenantInfoModel():
#     def __init__(self, dbh, tenant_id):
#         self.dbh = dbh
#         self.tenant_id =  tenant_id
#
#     def get_entry(self):
#         return True
#
#     def get_param(self, field):
#         return 0
#
# testCases = [
#     { "testCaseName": "test_get_aws_cust_region_instance_params_success",
#       "testCaseQueryResult" : {"select distinct instance_master.name, instance_master.delete_deployment, instance_master.clusterid,"
#                                " instance_master.salt_profile, instance_master.node_type,"
#                                " instance_master.no_passive_instance from instance_master left join vpc_master on"
#                                " instance_master.vpc_id = vpc_master.vpc_id and "
#                                " vpc_master.ha_flag = 0 and "
#                                "vpc_master.zone=instance_master.compute_region_idx where "
#                                "custid = 45 and vpc_master.zone=10 and instance_master.node_type = 49" :
#                                (
#                                    ('GPGW_7743_eu-west-1_renault-**********', 0, '7743', '{\"CustId\": 45, \"AcctId\": \"**********\", \"SerialNo\": \"Dummy_serial_no\", \"_SlotNr\": 0, \"sase_fabric\": 0, \"commit_validate\": 0, \"KeyName\": \"orchestrator\", \"UserData\": \"instance_name=GPGW_7743_eu-west-1_renault-**********,saas_gpcs_api_endpoint=dev10.panclouddev.com/api, cert_fetch_otp=1cc7b4c0-6e6a-44dd-b21b-3514635a9ebf,cloud_provider=aws,custid=**********,lambdaprefix=renault-**********,custid_int=45,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev10,panrepo_bucket_name=panrepo-us-west-2-dev10,sase_fabric=0,super_custid=**********,commit_validate=0\", \"PrimaryInstanceName\": \"GPGW_7743_eu-west-1_renault-**********\", \"PrimaryId\": 7743, \"PrimaryImage\": \"ami-0abda9c236f271feb\", \"MgmtActiveSubnet\": \"subnet-0117829da5fd75234\", \"Eth0SecGrp\": \"sg-013899eb7773b1b66\", \"DPActiveSubnet\": \"subnet-0a4439d075542aefb\", \"Eth1SecGrp\": \"sg-02046100628eb8c50\", \"Eth2SecGrp\": \"sg-02046100628eb8c50\", \"HAActiveSubnet\": \"subnet-01647ad9a72d284b6\", \"IamRole\": \"renault-**********_role\", \"PrimaryIntfSet\": 0, \"PrimaryCapacityType\": \"PA-CAP530\", \"InstType\": \"t3a.xlarge\", \"is_central_cache_supported\": 1, \"PrimaryMarketType\": \"\"}', 0, 0)
#
#         )
#                                },
#       "testCaseExpectedOutput" :{'7743': {'CustId': 45, 'InstType': 't3a.xlarge', 'AcctId': '**********', 'UserData': 'instance_name=GPGW_7743_eu-west-1_renault-**********,saas_gpcs_api_endpoint=dev10.panclouddev.com/api, cert_fetch_otp=1cc7b4c0-6e6a-44dd-b21b-3514635a9ebf,cloud_provider=aws,custid=**********,lambdaprefix=renault-**********,custid_int=45,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev10,panrepo_bucket_name=panrepo-us-west-2-dev10,sase_fabric=0,super_custid=**********,commit_validate=0', 'PrimaryInstanceName': 'GPGW_7743_eu-west-1_renault-**********', 'PrimaryId': 7743, 'SerialNo': 'Dummy_serial_no', 'KeyName': 'orchestrator', 'PrimaryImage': 'ami-0abda9c236f271feb', 'Eth0SecGrp': 'sg-013899eb7773b1b66', 'IamRole': 'renault-**********_role', 'PrimaryIntfSet': 0, 'MgmtActiveSubnet': 'subnet-0117829da5fd75234', 'Eth1SecGrp': 'sg-02046100628eb8c50', 'Eth2SecGrp': 'sg-02046100628eb8c50', 'DPActiveSubnet': 'subnet-0a4439d075542aefb', 'HAActiveSubnet': 'subnet-01647ad9a72d284b6', 'PrimaryCapacityType': 'PA-CAP530', 'AvRPMBuckets': '', 'ContentRPMBuckets': '', '_InstNodeType': 0}}
#      },
#     { "testCaseName": "test_get_aws_cust_region_instance_params_success",
#       "testCaseQueryResult" : {"select distinct instance_master.name, instance_master.delete_deployment, instance_master.clusterid,"
#                                " instance_master.salt_profile, instance_master.node_type,"
#                                " instance_master.no_passive_instance from instance_master left join vpc_master on"
#                                " instance_master.vpc_id = vpc_master.vpc_id and "
#                                " vpc_master.ha_flag = 0 and "
#                                "vpc_master.zone=instance_master.compute_region_idx where "
#                                "custid = 512 and vpc_master.zone=153 and instance_master.node_type = 154" :
#                                (
#                                    ('PROBEVM_2133021_us-east-1-miami_transcontinental-467402688', 0, '2133021', '{\"CustId\": 512, \"AcctId\": \"467402688\", \"SerialNo\": \"Dummy_serial_no\", \"_SlotNr\": 0, \"sase_fabric\": 0, \"commit_validate\": 0, \"KeyName\": \"orchestrator\", \"UserData\": \"instance_name=PROBEVM_2133021_us-east-1-miami_transcontinental-467402688,saas_gpcs_api_endpoint=qa3.panclouddev.com/api, cert_fetch_otp=745c1d4f-44c9-49cf-a5a3-3ebd6ecc190f,cloud_provider=aws,custid=467402688,lambdaprefix=transcontinental-467402688,custid_int=512,gp_domain=test,route53acct=a447084568087,bucket-name=pan-content-qa3,panrepo_bucket_name=panrepo-us-west-2-qa3,sase_fabric=0,super_custid=467402688,commit_validate=0,edge-compute-region=us-east-1-miami,timezone=US/Eastern\", \"PrimaryInstanceName\": \"PROBEVM_2133021_us-east-1-miami_transcontinental-467402688\", \"PrimaryId\": 2133021, \"PrimaryImage\": \"ami-06d62dcb0351ef077\", \"MgmtActiveSubnet\": \"subnet-0117829da5fd75234\", \"Eth0SecGrp\": \"sg-013899eb7773b1b66\", \"DPActiveSubnet\": \"subnet-0a4439d075542aefb\", \"Eth1SecGrp\": \"sg-02046100628eb8c50\", \"Eth2SecGrp\": \"sg-02046100628eb8c50\", \"HAActiveSubnet\": \"subnet-01647ad9a72d284b6\", \"IamRole\": \"renault-**********_role\", \"PrimaryIntfSet\": 0, \"PrimaryCapacityType\": \"PA-CAP530\", \"InstType\": \"t3.xlarge\", \"PrimaryMarketType\": \"\", \"LocalZoneRegion\": \"us-east-1-mia-1\"}'
#                                     , 48, 0)
#                                )
#                                },
#       "testCaseExpectedOutput" :{'2133021': {'CustId': 512, 'InstType': 't3.xlarge', 'AcctId': '467402688', 'UserData': 'instance_name=PROBEVM_2133021_us-east-1-miami_transcontinental-467402688,saas_gpcs_api_endpoint=qa3.panclouddev.com/api, cert_fetch_otp=745c1d4f-44c9-49cf-a5a3-3ebd6ecc190f,cloud_provider=aws,custid=467402688,lambdaprefix=transcontinental-467402688,custid_int=512,gp_domain=test,route53acct=a447084568087,bucket-name=pan-content-qa3,panrepo_bucket_name=panrepo-us-west-2-qa3,sase_fabric=0,super_custid=467402688,commit_validate=0,edge-compute-region=us-east-1-miami,timezone=US/Eastern', 'PrimaryInstanceName': 'PROBEVM_2133021_us-east-1-miami_transcontinental-467402688', 'PrimaryId': 2133021, 'SerialNo': 'Dummy_serial_no', 'KeyName': 'orchestrator', 'PrimaryImage': 'ami-06d62dcb0351ef077', 'Eth0SecGrp': 'sg-013899eb7773b1b66', 'IamRole': 'renault-**********_role', 'PrimaryIntfSet': 0, 'MgmtActiveSubnet': 'subnet-0117829da5fd75234', 'LocalZoneRegion': 'us-east-1-mia-1', 'MountPoint': '/dev/sda1', 'Eth1SecGrp': 'sg-02046100628eb8c50', 'Eth2SecGrp': 'sg-02046100628eb8c50', 'DPActiveSubnet': 'subnet-0a4439d075542aefb', 'HAActiveSubnet': 'subnet-01647ad9a72d284b6', 'PrimaryCapacityType': 'PA-CAP530', 'AvRPMBuckets': '', 'ContentRPMBuckets': '', 'SAASAgentAPIEndpoint': '', '_InstNodeType': 48}
#                                  }
#      },
#          { "testCaseName": "test_get_aws_cust_region_instance_params_success",
#       "testCaseQueryResult" : {"select distinct instance_master.name, instance_master.delete_deployment, instance_master.clusterid,"
#                                " instance_master.salt_profile, instance_master.node_type,"
#                                " instance_master.no_passive_instance from instance_master left join vpc_master on"
#                                " instance_master.vpc_id = vpc_master.vpc_id and "
#                                " vpc_master.ha_flag = 0 and "
#                                "vpc_master.zone=instance_master.compute_region_idx where "
#                                "custid = 45 and vpc_master.zone=10 and instance_master.node_type = 49" :
#                                (
#                                    ('GPGW_7743_eu-west-1_renault-**********', 0, '7743', '{\"CustId\": 45, \"AcctId\": \"**********\", \"SerialNo\": \"Dummy_serial_no\", \"_SlotNr\": 0, \"sase_fabric\": 0, \"commit_validate\": 0, \"KeyName\": \"orchestrator\", \"UserData\": \"instance_name=GPGW_7743_eu-west-1_renault-**********,saas_gpcs_api_endpoint=dev10.panclouddev.com/api, cert_fetch_otp=1cc7b4c0-6e6a-44dd-b21b-3514635a9ebf,cloud_provider=aws,custid=**********,lambdaprefix=renault-**********,custid_int=45,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev10,panrepo_bucket_name=panrepo-us-west-2-dev10,sase_fabric=0,super_custid=**********,commit_validate=0\", \"PrimaryInstanceName\": \"GPGW_7743_eu-west-1_renault-**********\", \"PrimaryId\": 7743, \"PrimaryImage\": \"ami-0abda9c236f271feb\", \"MgmtActiveSubnet\": \"subnet-0117829da5fd75234\", \"Eth0SecGrp\": \"sg-013899eb7773b1b66\", \"DPActiveSubnet\": \"subnet-0a4439d075542aefb\", \"Eth1SecGrp\": \"sg-02046100628eb8c50\", \"Eth2SecGrp\": \"sg-02046100628eb8c50\", \"HAActiveSubnet\": \"subnet-01647ad9a72d284b6\", \"IamRole\": \"renault-**********_role\", \"PrimaryIntfSet\": 0, \"PrimaryCapacityType\": \"PA-CAP530\", \"InstType\": \"t3a.xlarge\", \"is_central_cache_supported\": 1, \"PrimaryMarketType\": \"\"}', 49, 0)
#
#         )
#                                },
#       "testCaseExpectedOutput" :{'7743': {'CustId': 45, 'InstType': 't3a.xlarge', 'IsCentralCacheSupported': 1, 'AcctId': '**********', 'UserData': 'instance_name=GPGW_7743_eu-west-1_renault-**********,saas_gpcs_api_endpoint=dev10.panclouddev.com/api, cert_fetch_otp=1cc7b4c0-6e6a-44dd-b21b-3514635a9ebf,cloud_provider=aws,custid=**********,lambdaprefix=renault-**********,custid_int=45,gp_domain=panclouddev.com,route53acct=a447084568087,bucket-name=pan-content-dev10,panrepo_bucket_name=panrepo-us-west-2-dev10,sase_fabric=0,super_custid=**********,commit_validate=0', 'PrimaryInstanceName': 'GPGW_7743_eu-west-1_renault-**********', 'PrimaryId': 7743, 'SerialNo': 'Dummy_serial_no', 'KeyName': 'orchestrator', 'PrimaryImage': 'ami-0abda9c236f271feb', 'Eth0SecGrp': 'sg-013899eb7773b1b66', 'IamRole': 'renault-**********_role', 'PrimaryIntfSet': 0, 'MgmtActiveSubnet': 'subnet-0117829da5fd75234', 'Eth1SecGrp': 'sg-02046100628eb8c50', 'Eth2SecGrp': 'sg-02046100628eb8c50', 'DPActiveSubnet': 'subnet-0a4439d075542aefb', 'HAActiveSubnet': 'subnet-01647ad9a72d284b6', 'PrimaryCapacityType': 'PA-CAP530', 'AvRPMBuckets': '', 'ContentRPMBuckets': '', '_InstNodeType': 49}}
#      }
# ]
#
# from libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt import get_aws_cust_region_instance_params
#
# # TestCaseName: Test_get_aws_cust_region_instance_fw_params_success, idx 0 from testCases
# class Test_get_aws_cust_region_instance_fw_params_success(TestCase):
#     @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_saas_agent_api_endpoint', return_value="")
#     @patch('libs.model.custmodel.CustomerModel', side_effect=mocked_CustomerModel)
#     def test_get_aws_cust_region_instance_params_success(self,
#                                                          patched_CustomerModel,
#                                                          patched_get_saas_agent_endpoint
#                                                          ):
#         suiteIdx=0
#         myname = testCases[suiteIdx]
#         dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
#         custid=45
#         region_idx=10
#         node_type = 49
#
#         answer = get_aws_cust_region_instance_params(dbh, custid, region_idx, node_type)
#         assert answer == (True, testCases[suiteIdx]["testCaseExpectedOutput"])
#
# class Test_get_aws_cust_region_instance_probe_vm_params_success(TestCase):
#     @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_saas_agent_api_endpoint',
#            return_value="")
#     @patch('libs.model.custmodel.CustomerModel', side_effect=mocked_CustomerModel)
#     def test_get_aws_cust_region_instance_params_success(self,
#                                                          patched_CustomerModel,
#                                                          patched_get_saas_agent_endpoint):
#         suiteIdx=1
#         myname = testCases[suiteIdx]
#         dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
#         custid=512
#         region_idx=153
#         node_type = 154
#
#         answer = get_aws_cust_region_instance_params(dbh, custid, region_idx, node_type)
#         assert answer == (True, testCases[suiteIdx]["testCaseExpectedOutput"])
#
# class Test_get_aws_cust_region_instance_gpgw_params_success(TestCase):
#     @patch('libs.cloud_providers.aws.instance_manager.aws_instance_param_mgmt.get_saas_agent_api_endpoint', return_value="")
#     @patch('libs.model.custmodel.CustomerModel', side_effect=mocked_CustomerModel)
#     def test_get_aws_cust_region_instance_params_success(self,
#                                                          patched_CustomerModel,
#                                                          patched_get_saas_agent_endpoint
#                                                          ):
#         suiteIdx=2
#         myname = testCases[suiteIdx]
#         dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
#         custid=45
#         region_idx=10
#         node_type = 49
#
#         answer = get_aws_cust_region_instance_params(dbh, custid, region_idx, node_type)
#         assert answer == (True, testCases[suiteIdx]["testCaseExpectedOutput"])
