import mock_imports_orchestrator
import logging
import pytest
import traceback
import copy
from unittest import TestCase
from unittest.mock import patch
from unittest.mock import MagicMock as Mock
from fastapi import HTTPException, Request, Response
from instance_mgmt_service.core.api_defs.post_process import (
    DeploymentPostProcessModel, 
    deployment_post_process
)

'''
PYTHONPATH=~/saas-infra/src/apps/orchestrator/
pytest -s test_osp_postprocess.py
'''

# Sample message body
TEST_MSG_BODY = {
            "uuid": "7a84300d-2cb4-4195-8244-912be5159a69",
            "status": "ok",
            "output": {
                "instances": [
                    {
                        "name":"GPGW_9488_us-west-700_renault3402-767988308",
                        "vm_id":"86311d1e-79c6-4148-bf28-54c966c6df1e",
                        "interfaces":[
                            {
                                "name":"nic-mgmt",
                                "private_ip":"************"
                            },
                            {
                                "name":"nic-dp",
                                "public_ip":"**************",
                                "private_ip":"************"
                            }
                        ]
                    }
                ]
            }
        }


class logger():
    def info(self, *args):
        print(*args)

    def error(self, *args):
        print(*args)

    def warn(self, *args):
        pass

    def debug(self, *args):
        pass

    def set_sub_uuid(self, *args):
        self.sub_uuid = 10

    def reset_sub_uuid(self):
        self.sub_uuid = 0


class Mock_conn():
    def __init__(self):
        pass

    def close(self):
        pass

    def commit(self):
        pass


class Mock_cursor():
    def __init__(self, testSuiteIdx):
        self.sql = None
        self.params = None
        self.testSuiteIdx = testSuiteIdx


class Mock_DBHandler():
    def __init__(self, logger, testSuiteIdx):
        self.logger = logger
        self.conn = Mock_conn()
        self.testSuiteIdx = testSuiteIdx

    def conn(self):
        return self.conn

    def get_cursor(self):
        return  Mock_cursor(self.testSuiteIdx)

    def cursorclose(self, cursor):
        return


class mocked_InstanceModel():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.fields = ["id", "name", "vmid", "mgt_ip", "pvt_ip", "public_ip", "node_type",
                       "state", "gw_capabilities", "sase_fabric_ip", "native_machine_type"]
        self.values = [9488, "GPGW_9488_us-west-700_renault3402-767988308", "", "", "", "", 49,
                       0, 'None', 'None', '']

    def get_param(self, field):
        print(self.fields)
        myidx = self.fields.index(field)

        val = self.values[myidx]
        if val == 'None':
            return None

        return val

    def set_param(self, field, value):
        if field in self.fields:
            myidx = self.fields.index(field)
            self.values[myidx] = value
        else:
            raise Exception(f"field {field} does not exist")

    def requested_native_machine_type(self):
        return 0

    def __str__(self):
        return "dummy"

    def save(self):
        return


class TestOspPostprocess():

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', side_effect = mocked_InstanceModel)
    def test_deployment_post_process(self, patched_InstanceModel):
        # test normal case, expect status: ok

        dbh = Mock_DBHandler(logger=logger(), testSuiteIdx=0)
        inst_mgmt_id = 0
        request = None # Request()
        response = None # Response()
        msg_body_dict = TEST_MSG_BODY
        body = DeploymentPostProcessModel(**msg_body_dict)
        res = deployment_post_process(dbh, request, inst_mgmt_id, body, response)

        assert res == {'status': 'ok'}

    @patch('libs.cloud_providers.common.instance_bringup.InstanceModel', side_effect = mocked_InstanceModel)
    def test_deployment_post_process_wrong_msg_body(self, patched_InstanceModel):
        # negative test, with wrong msg_body

        dbh = Mock_DBHandler(logger=logger(), testSuiteIdx=0)
        inst_mgmt_id = 0
        request = None # Request()
        response = None # Response()
        msg_body_dict = copy.deepcopy(TEST_MSG_BODY)
        msg_body_dict['status'] = 'dummy'
        # purposedly set status to not ok, expect exception

        body = DeploymentPostProcessModel(**msg_body_dict)
        try:
            res = deployment_post_process(dbh, request, inst_mgmt_id, body, response)
        except Exception as ex:
            print(f"wrong status ex: {ex}")
        else:
            assert False

        msg_body_dict = copy.deepcopy(TEST_MSG_BODY)
        msg_body_dict['output']['instances'] = None
        # purposedly set instance to None, expect exception

        try:
            body = DeploymentPostProcessModel(**msg_body_dict)
            res = deployment_post_process(dbh, request, inst_mgmt_id, body, response)
        except Exception as ex:
            print(f"None instances ex: {ex}")
        else:
            assert False

        msg_body_dict = copy.deepcopy(TEST_MSG_BODY)
        msg_body_dict['output']['instances'][0]['vm_id'] = None
        # purposedly set vmid to None, expect exception

        try:
            body = DeploymentPostProcessModel(**msg_body_dict)
            res = deployment_post_process(dbh, request, inst_mgmt_id, body, response)
        except Exception as ex:
            print(f"wrong vmid ex: {ex}")
        else:
            assert False

        msg_body_dict = copy.deepcopy(TEST_MSG_BODY)
        msg_body_dict['output']['instances'][0]['interfaces'] = None
        # purposedly set interfaces to None, expect exception

        try:
            body = DeploymentPostProcessModel(**msg_body_dict)
            res = deployment_post_process(dbh, request, inst_mgmt_id, body, response)
        except Exception as ex:
            print(f"wrong interfaces ex: {ex}")
        else:
            assert False
