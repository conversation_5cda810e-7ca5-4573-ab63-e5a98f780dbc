export PYTHONPATH := $(PAN_BUILD_DIR)/src/apps/orchestrator:$(PAN_BUILD_DIR)/tests/orchestrator
UNIT_TEST_DIR ?= orchestrator

#TODO need to mock for third party lib and sync SUBDIRS with LOCAL_SUBDIRS
SUBDIRS := feature_flag \
	       $(NULL)

LOCAL_SUBDIRS := instance_mgmt_service \
	       inst_mgmt \
           orchestration_service \
           notification_service \
           libs/cloud_providers/aws \
           feature_flag \
           libs/common \
           libs/model \
	       libs/route53 \
           $(NULL)

include $(PAN_BUILD_DIR)/src/mk/unit_test.mk

define unittest-prep
	pip3 install -Ur $(PAN_BUILD_DIR)/tests/orchestrator/requirements.txt
	rm -rf $(PAN_BUILD_DIR)/tests/orchestrator/.coverage
	rm -rf $(PAN_BUILD_DIR)/tests/orchestrator/htmlcov
endef

.PHONY: unittest
unittest: unit_test_prep unit_test_all

.PHONY: unit_test_prep
unit_test_prep:
	@echo "pip3 install..."
	#$(call unittest-prep)

.PHONY: unit_test_all
unit_test_all: $(SUBDIRS)
	$(MAKE) unit_test_report

.PHONY: $(SUBDIRS)
$(SUBDIRS):
	$(MAKE) unit_test UNIT_TEST_DIR="$(UNIT_TEST_DIR)" UNIT_TEST_MODULE="$@"

.PHONY: local_ut
local_ut:
	@echo "copy shared directory"
	cp -RL $(SAAS_INFRA_BUILD_DIR)/src/apps/shared $(PAN_BUILD_DIR)/src/apps
	@echo "pip3 install..."
	$(call unittest-prep)
	@echo "iterate LOCAL_SUBDIRS and run ut"
	@for dir in $(LOCAL_SUBDIRS); do \
		$(MAKE) unit_test UNIT_TEST_DIR="$(UNIT_TEST_DIR)" UNIT_TEST_MODULE="$$dir"; \
	done
	$(MAKE) unit_test_report

include $(PAN_BUILD_DIR)/src/mk/unit_test.mk
