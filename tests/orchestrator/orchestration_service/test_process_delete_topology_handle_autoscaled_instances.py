import traceback
from unittest import TestCase
from unittest.mock import patch
import mock_imports_orchestrator
import orchestration_service.core.orchestrator
from orchestration_service.core.orchestrator import OrchstrationHandler
'''
PYTHONPATH=~/saas-infra/src/apps/orchestrator/ pytest -s orchestrator.py
'''

def set_cfg(cfg):
    cfg['log-root-dir']= '/tmp/',
    cfg['region'] = 'us-west-2',
    cfg['queues'] =  []
    cfg['dbpassword'] = "mankind"
    cfg['unittest'] = True

class logger():
    def info(self, *args):
        print(args)

    def error(self, *args):
        print(args)

    def warn(self, *args):
        print(args)

class Mock_conn():
    def __init__(self):
        print("Making conn")
        pass

    def close(self):
        pass

class Mock_cursor():
    def __init__(self, testSuiteIdx):
        self.sql = None
        self.params = None
        self.testSuiteIdx = testSuiteIdx

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params:
            print("Fetching all for %s" % str(self.sql % self.params))
            query = self.sql % self.params
        else:
            print("Fetching all for %s" % str(self.sql))
            query = self.sql
        answer = testCases[self.testSuiteIdx]["testCaseQueryResult"].get(query)
        return answer

    def fetchone(self):
        return {"name" : "Tejas"}

class Mock_DBHandler():
    def __init__(self, logger, testSuiteIdx):
        self.logger = logger
        self.conn = Mock_conn()
        self.testSuiteIdx = testSuiteIdx

    def conn(self):
        return self.conn

    def get_cursor(self):
        return  Mock_cursor(self.testSuiteIdx)

    def cursorclose(self, cursor):
        return

testCases = [
        { "testCaseName": "test_delete_topology_handle_auto_scaled_instances_success",
          "testCaseQueryResult" : {
              "SELECT instance1_transient AS inst_id FROM cust_topology WHERE id=9000 UNION SELECT instance2_transient AS inst_id FROM cust_topology WHERE id=9000" :
                  ([4001], [4002], [4003])

          },
          "testCaseExpectedOutput" : True
        },
        { "testCaseName": "test_delete_topology_handle_auto_scaled_instances_failure_pinned_instances_not_deleted",
          "testCaseQueryResult" : {
              "SELECT instance1_transient AS inst_id FROM cust_topology WHERE id=9000 UNION SELECT instance2_transient AS inst_id FROM cust_topology WHERE id=9000" :
                  ([4001], [4002], [4003])

          },
          "testCaseExpectedOutput": (False, [4000, 4001, 4002, 4003])
        },
]

class Test_success(TestCase):
    @patch('orchestration_service.core.orchestrator.CSN.CustNodeModel')
    @patch('orchestration_service.core.orchestrator.CM.CustomerModel')
    @patch('orchestration_service.core.orchestrator.INST.InstanceModel')
    def test_success(self, patched_Instancemodel, patched_Customermodel, patched_Custnodemodel):
        suiteIdx=0
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        # Create a mock handle for dbh
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        # Create a basic instanceModel object.
        class mocked_InstanceModel():
            def __init__(self, iid=None, dbh=None):
                print("XXX Came to instance model")
                self.fields = {}
                self.fields["id"] = iid
                self.fields["compute_region_idx"] = 200
                self.fields["compute_region_name"] = "us-west-2"
                self.fields["is_pinned_instance"] = False
                if self.fields["id"] in [4001, 4002, 4003]:
                    self.fields["is_dynamic_instance"] = True
                else:
                    self.fields["is_dynamic_instance"] = False
                self.fields["mpdp_clusterid"] = None
                self.fields["node_type"] = 49
                self.fields["alt_node_type"] = -1

            def get_param(self, keyword):
                return self.fields[keyword]

            def unbind(self, custnode, db_h, job):
                # Since we are simulating a success condition of deletion of instances, set the id to None to
                # simulate successful deletion
                self.fields["id"] = None
                return True

            def get_all_instances_for_region_and_custid(self,
                                                        custid,
                                                        compute_region_idx,
                                                        node_type_list):
                # We will return a static list of instances and assume that they are auto-scaled instances.
                return [[4001], [4002], [4003]]

            def get_sites(self, dbh):
                if self.fields["id"] == 4001:
                    return [9001]
                if self.fields["id"] == 4002:
                    return [9002]
                if self.fields["id"] == 4003:
                    return [9003, 9004, 9005]

        # Create a basic cust node model
        class mocked_CustNodeModel():
            def __init__(self, iid=0, dbh=None):
                print("XXXXX Came to Cust Node model for iid %s!" % str(iid))
                self.id = iid
                self.custid = 15
                self.dbh = dbh
                self.okyo_edge_site_id = 0
                self.is_deleted = 0
                self.node_type = 49
                self.alt_node_type = -1

            def save(self, dbh):
                return True

        # Create a mocked job class
        class mocked_Job():
            def __init__(self):
                self.status_msg = ""
                print("Mocked Job called")

            def save_job(self, dbh):
                return True


        # Create a basic cust model
        class mocked_CustomerModel():
            def __init__(self, custid, dbh):
                self.id = custid
                self.dbh = dbh
                self.fwdrulesall = ""

        patched_Custnodemodel.side_effect = mocked_CustNodeModel
        patched_Instancemodel.side_effect = mocked_InstanceModel
        patched_Customermodel.side_effect = mocked_CustomerModel

        assert patched_Instancemodel is orchestration_service.core.orchestrator.INST.InstanceModel
        assert patched_Custnodemodel is orchestration_service.core.orchestrator.CSN.CustNodeModel
        assert patched_Customermodel is orchestration_service.core.orchestrator.CM.CustomerModel

        orchHandler = None
        print(mocked_InstanceModel)
        print(mocked_CustNodeModel)

        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = logger()
            orchHandler.db_h = dbh
            orchHandler.tenant_id = 245139
        except Exception as E:
            print("Exception %s, Traceback %s" % (str(E.args), str(traceback.format_exc())))

        # Create a mocked custnode model object.
        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.compute_region_idx = 200
        deleteInstanceDetailsObj.job = mocked_Job()

        answer, instance_ids = orchHandler.process_delete_topology_handle_autoscaled_instances(custnode,
                                                                                 deleteInstanceDetailsObj)
        print(vars(deleteInstanceDetailsObj))
        print("Got output : %s" % str(answer))
        assert answer == testCases[suiteIdx]["testCaseExpectedOutput"]

class Test_failure_pinned_instance_not_deleted(TestCase):
    @patch('orchestration_service.core.orchestrator.is_current_upgrade_instance', return_value=False)
    @patch('orchestration_service.core.orchestrator.CSN.CustNodeModel')
    @patch('orchestration_service.core.orchestrator.CM.CustomerModel')
    @patch('orchestration_service.core.orchestrator.INST.InstanceModel')
    def test_success(self, patched_Instancemodel,
                           patched_Customermodel,
                           patched_Custnodemodel,
                           patched_is_current_upgrade_instance):
        suiteIdx=1
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        # Create a mock handle for dbh
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        # Create a basic instanceModel object.
        class mocked_InstanceModel():
            def __init__(self, iid=None, dbh=None):
                print("XXX Came to instance model")
                self.fields = {}
                self.fields["id"] = iid
                self.fields["compute_region_idx"] = 200
                self.fields["compute_region_name"] = "us-west-2"
                self.fields["is_pinned_instance"] = False
                if self.fields["id"] in [4001, 4002, 4003]:
                    self.fields["is_dynamic_instance"] = True
                else:
                    self.fields["is_dynamic_instance"] = False
                if self.fields["id"] == 4000:
                    self.fields["is_pinned_instance"] = True
                self.fields["mpdp_clusterid"] = None
                self.fields["node_type"] = 49
                self.fields["alt_node_type"] = -1

            def get_param(self, keyword):
                return self.fields[keyword]

            def unbind(self, custnode, db_h, job):
                # Since we are simulating a success condition of deletion of instances, set the id to None to
                # simulate successful deletion
                self.fields["id"] = None
                return True

            def get_all_instances_for_region_and_custid(self,
                                                        custid,
                                                        compute_region_idx,
                                                        node_type_list):
                # We will return a static list of instances and assume that they are auto-scaled instances.
                return [[4000], [4001], [4002], [4003]]

            def get_sites(self, dbh):
                if self.fields["id"] == 4000:
                    return [9000]
                if self.fields["id"] == 4001:
                    return [9001]
                if self.fields["id"] == 4002:
                    return [9002]
                if self.fields["id"] == 4003:
                    return [9003, 9004, 9005]

        # Create a basic cust node model
        class mocked_CustNodeModel():
            def __init__(self, iid=0, dbh=None):
                print("XXXXX Came to Cust Node model for iid %s!" % str(iid))
                self.id = iid
                self.custid = 15
                self.dbh = dbh
                self.okyo_edge_site_id = 0
                self.is_deleted = 0
                self.node_type = 49
                self.alt_node_type = -1

            def save(self, dbh):
                return True

        # Create a mocked job class
        class mocked_Job():
            def __init__(self):
                self.status_msg = ""
                print("Mocked Job called")

            def save_job(self, dbh):
                return True


        # Create a basic cust model
        class mocked_CustomerModel():
            def __init__(self, custid, dbh):
                self.id = custid
                self.dbh = dbh
                self.fwdrulesall = ""

        patched_Custnodemodel.side_effect = mocked_CustNodeModel
        patched_Instancemodel.side_effect = mocked_InstanceModel
        patched_Customermodel.side_effect = mocked_CustomerModel

        assert patched_Instancemodel is orchestration_service.core.orchestrator.INST.InstanceModel
        assert patched_Custnodemodel is orchestration_service.core.orchestrator.CSN.CustNodeModel
        assert patched_Customermodel is orchestration_service.core.orchestrator.CM.CustomerModel

        orchHandler = None
        print(mocked_InstanceModel)
        print(mocked_CustNodeModel)

        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = logger()
            orchHandler.db_h = dbh
            orchHandler.tenant_id = 245139
        except Exception as E:
            print("Exception %s, Traceback %s" % (str(E.args), str(traceback.format_exc())))

        # Create a mocked custnode model object.
        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.compute_region_idx = 200
        deleteInstanceDetailsObj.job = mocked_Job()

        answer = orchHandler.process_delete_topology_handle_autoscaled_instances(custnode,
                                                                                 deleteInstanceDetailsObj)
        print(vars(deleteInstanceDetailsObj))
        print("Got output : %s" % str(answer))
        assert answer == testCases[suiteIdx]["testCaseExpectedOutput"]
