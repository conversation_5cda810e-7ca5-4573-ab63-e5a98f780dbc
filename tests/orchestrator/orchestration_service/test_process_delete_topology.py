import traceback
import pytest
import logging
from unittest import TestCase
from unittest.mock import patch
import orchestration_service.core.orchestrator
from orchestration_service.core.orchestrator import OrchstrationHandler
'''
PYTHONPATH=~/saas-infra/src/apps/orchestrator/ pytest -s orchestrator.py
'''

def set_cfg(cfg):
    cfg['log-root-dir']= '/tmp/',
    cfg['region'] = 'us-west-2',
    cfg['queues'] =  []
    cfg['dbpassword'] = "mankind"
    cfg['unittest'] = True

class logger():
    def info(self, *args):
        print(args)

    def error(self, *args):
        print(args)

    def warn(self, *args):
        print(args)

class Mock_conn():
    def __init__(self):
        print("Making conn")
        pass

    def close(self):
        pass

class Mock_cursor():
    def __init__(self, testSuiteIdx):
        self.sql = None
        self.params = None
        self.testSuiteIdx = testSuiteIdx

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params:
            print("Fetching all for %s" % str(self.sql % self.params))
            query = self.sql % self.params
        else:
            print("Fetching all for %s" % str(self.sql))
            query = self.sql
        answer = testCases[self.testSuiteIdx]["testCaseQueryResult"].get(query)
        return answer

    def fetchone(self):
        return {"name" : "Tejas"}

class Mock_DBHandler():
    def __init__(self, logger, testSuiteIdx):
        self.logger = logger
        self.conn = Mock_conn()
        self.testSuiteIdx = testSuiteIdx

    def conn(self):
        return self.conn

    def get_cursor(self):
        return  Mock_cursor(self.testSuiteIdx)

    def cursorclose(self, cursor):
        return

testCases = [
        { "testCaseName": "test_process_delete_topology_success",
          "testCaseQueryResult" : {},
          "testCaseExpectedOutput" : True
        },
        {  "testCaseName": "test_process_delete_topology_success_instance_not_deleted",
           "testCaseQueryResult": {},
           "testCaseExpectedOutput": True
        },
        {  "testCaseName": "test_process_delete_topology_failure_error_returned_by_process_delete_topology_handle_instances",
           "testCaseQueryResult": {},
           "testCaseExpectedOutput": False
        },
        {"testCaseName": "test_delete_cust_epaas_entry",
         "testCaseQueryResult":   {"select instance1_id AS instance_id from cust_topology where custid=20002445 and"
                                   " region=200 and is_deleted = 0 and node_type = 153 and alt_node_type = -1 UNION select instance2_id AS"
                                   " instance_id from cust_topology where custid=20002445 and region=200 and is_deleted"
                                   " = 0 and node_type = 153 and alt_node_type = -1": [],
                                   "select instance1_id AS instance_id from cust_topology where custid=20002445 and"
                                   " region=220 and is_deleted = 0 and node_type = 153 and alt_node_type = -1 UNION select instance2_id AS"
                                   " instance_id from cust_topology where custid=20002445 and region=220 and is_deleted"
                                   " = 0 and node_type = 153 and alt_node_type = -1": [[12345]]
                                   },
         "testCaseExpectedOutput": True
         },
]


class Test_success():
    @pytest.mark.parametrize("test_input, test_log_capture", 
                             [((True, 0), "Successfully deleted record from gpcs_tenant_region_mapping"), 
                              ((False, 0), "Failed to get count of topology instances that "), 
                              ((True, 10), "Skip deletion of tenant region mapping since ")])
    @patch('orchestration_service.core.orchestrator.trigger_update_for_instance_changes', return_value=(True, 3718128))
    @patch('orchestration_service.core.orchestrator.get_count_of_topology_instances_not_marked_for_deletion_in_region')
    @patch('orchestration_service.core.orchestrator_central_cache.execute_orch_query', return_value=(True, [1]))
    @patch('orchestration_service.core.orchestrator.OrchstrationHandler.process_delete_topology_handle_transient_instances')
    @patch('orchestration_service.core.orchestrator.OrchstrationHandler.process_delete_topology_handle_instances')
    @patch('orchestration_service.core.orchestrator.CSN.CustNodeModel')
    @patch('orchestration_service.core.orchestrator.CM.CustomerModel')
    @patch('orchestration_service.core.orchestrator.INST.InstanceModel')
    def test_success(self,
                     patched_Instancemodel,
                     patched_Customermodel,
                     patched_Custnodemodel,
                     patched_process_delete_topology_handle_instances,
                     patched_process_delete_topology_handle_transient_instances,
                     mock_execute_orch_query,
                     mock_get_count_of_topology_instances,
                     patched_trigger_update_for_instance_changes,
                     test_input,
                     test_log_capture,
                     caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        mock_get_count_of_topology_instances.return_value = test_input

        # Create a mock handle for dbh
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger, testSuiteIdx=suiteIdx)

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        # Patch process_delete_topology_handle_instances
        def process_delete_topology_handle_instances(custnode, deleteInstanceDetailsObj):
            deleteInstanceDetailsObj.compute_region_idx=200
            deleteInstanceDetailsObj.node_type=48
            deleteInstanceDetailsObj.job_instances.append(4000)
            deleteInstanceDetailsObj.job_instances.append(4001)
            print(vars(deleteInstanceDetailsObj))
            return True, True

        # Patch process_delete_topology_handle_transient_instances
        def process_delete_topology_handle_transient_instances(custnode, deleteInstanceDetailsObj):
            deleteInstanceDetailsObj.compute_region_idx=200
            deleteInstanceDetailsObj.node_type=48
            deleteInstanceDetailsObj.job_transient_instances.append(4002)
            deleteInstanceDetailsObj.job_transient_instances.append(4003)
            print(vars(deleteInstanceDetailsObj))
            return True, True


        # Create a basic instanceModel object.
        class mocked_InstanceModel():
            def __init__(self, iid=None, dbh=None):
                print("XXX Came to instance model")
                self.fields = {}
                self.fields["id"] = iid
                self.fields["compute_region_idx"] = 200
                self.fields["compute_region_name"] = "us-west-2"
                self.fields["is_pinned_instance"] = False
                if self.fields["id"] in [4001, 4002, 4003]:
                    self.fields["is_dynamic_instance"] = True
                else:
                    self.fields["is_dynamic_instance"] = False
                self.fields["mpdp_clusterid"] = None
                self.fields["node_type"] = 49

            def get_param(self, keyword):
                return self.fields[keyword]

            def unbind(self, custnode, db_h, job):
                # Since we are simulating a success condition of deletion of instances, set the id to None to
                # simulate successful deletion
                self.fields["id"] = None
                return True

            def get_all_instances_for_region_and_custid(self,
                                                        custid,
                                                        compute_region_idx,
                                                        node_type_list):
                # We will return a static list of instances and assume that they are auto-scaled instances.
                return [[4001], [4002], [4003]]

            def get_sites(self, dbh):
                if self.fields["id"] == 4001:
                    return [9001]
                if self.fields["id"] == 4002:
                    return [9002]
                if self.fields["id"] == 4003:
                    return [9003, 9004, 9005]

        # Create a basic cust node model
        class mocked_CustNodeModel():
            def __init__(self, iid=0, dbh=None):
                print("XXXXX Came to Cust Node model for iid %s!" % str(iid))
                self.id = iid
                self.custid = 15
                self.dbh = dbh
                self.okyo_edge_site_id = 0
                self.is_deleted = 0
                self.node_type = 49
                self.alt_node_type = -1
                # Assume that the cust node model is deleted.
                self.is_deleted = 1
                self.is_clean_pipe = 0
                self.region = 200
                self.name = "Aster"
                self.num_allocs = 2

            def save(self, dbh):
                return True

        # Create a mocked job class
        class mocked_Job():
            def __init__(self):
                self.status_msg = ""
                print("Mocked Job called")

            def save_job(self, dbh):
                return True


        # Create a basic cust model
        class mocked_CustomerModel():
            def __init__(self, custid, dbh):
                self.id = custid
                self.dbh = dbh
                self.fwdrulesall = ""

        #Create an avisar context.
        class mocked_AvisarCtx():
            def __init__(self):
                self.cust_id = 15
                self.cust_topology_id = 9000
                self.panorama_job_id = 12345
                self.region_id = 200

        patched_Custnodemodel.side_effect = mocked_CustNodeModel
        patched_Instancemodel.side_effect = mocked_InstanceModel
        patched_Customermodel.side_effect = mocked_CustomerModel
        patched_process_delete_topology_handle_instances.side_effect = process_delete_topology_handle_instances
        patched_process_delete_topology_handle_transient_instances.side_effect = process_delete_topology_handle_transient_instances

        assert patched_Instancemodel is orchestration_service.core.orchestrator.INST.InstanceModel
        assert patched_Custnodemodel is orchestration_service.core.orchestrator.CSN.CustNodeModel
        assert patched_Customermodel is orchestration_service.core.orchestrator.CM.CustomerModel
        assert patched_process_delete_topology_handle_instances is orchestration_service.core.orchestrator.OrchstrationHandler.process_delete_topology_handle_instances
        assert patched_process_delete_topology_handle_transient_instances is orchestration_service.core.orchestrator.OrchstrationHandler.process_delete_topology_handle_transient_instances

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = test_logger
            orchHandler.db_h = dbh
            orchHandler.tenant_id = 245139
            orchHandler.avctx = mocked_AvisarCtx()

        except Exception as E:
            print("Exception %s, Traceback %s" % (str(E.args), str(traceback.format_exc())))

        job_instances =[]
        job_transient_instances = []
        answer = orchHandler.process_delete_topology(mocked_Job(),
                                                     job_instances,
                                                     job_transient_instances)

        print("Got output : %s" % str(answer))
        assert answer == testCases[suiteIdx]["testCaseExpectedOutput"]
        assert job_instances == [4000, 4001]
        assert job_transient_instances == [4002, 4003]
        assert test_log_capture in caplog.text

class Test_success_instance_not_deleted(TestCase):
    @patch('orchestration_service.core.orchestrator.OrchstrationHandler.process_delete_topology_handle_edge_location_deletion', return_value=True)
    @patch('orchestration_service.core.orchestrator.OrchstrationHandler.process_delete_topology_handle_transient_instances')
    @patch('orchestration_service.core.orchestrator.OrchstrationHandler.process_delete_topology_handle_instances')
    @patch('orchestration_service.core.orchestrator.CSN.CustNodeModel')
    @patch('orchestration_service.core.orchestrator.CM.CustomerModel')
    @patch('orchestration_service.core.orchestrator.INST.InstanceModel')
    def test_success_instance_not_deleted(self,
                     patched_Instancemodel,
                     patched_Customermodel,
                     patched_Custnodemodel,
                     patched_process_delete_topology_handle_instances,
                     patched_process_delete_topology_handle_transient_instances,
                     pathed_process_delete_topology_handle_edge_location_deletion):
        suiteIdx=1
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        # Create a mock handle for dbh
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        # Patch process_delete_topology_handle_instances
        def process_delete_topology_handle_instances(custnode, deleteInstanceDetailsObj):
            deleteInstanceDetailsObj.compute_region_idx=200
            deleteInstanceDetailsObj.node_type=48
            deleteInstanceDetailsObj.job_instances.append(4000)
            deleteInstanceDetailsObj.job_instances.append(4001)
            print(vars(deleteInstanceDetailsObj))
            return True, False

        # Patch process_delete_topology_handle_transient_instances
        def process_delete_topology_handle_transient_instances(custnode, deleteInstanceDetailsObj):
            deleteInstanceDetailsObj.compute_region_idx=200
            deleteInstanceDetailsObj.node_type=48
            print(vars(deleteInstanceDetailsObj))
            return True, False


        # Create a basic instanceModel object.
        class mocked_InstanceModel():
            def __init__(self, iid=None, dbh=None):
                print("XXX Came to instance model")
                self.fields = {}
                self.fields["id"] = iid
                self.fields["compute_region_idx"] = 200
                self.fields["compute_region_name"] = "us-west-2"
                self.fields["is_pinned_instance"] = False
                if self.fields["id"] in [4001, 4002, 4003]:
                    self.fields["is_dynamic_instance"] = True
                else:
                    self.fields["is_dynamic_instance"] = False
                self.fields["mpdp_clusterid"] = None
                self.fields["node_type"] = 49

            def get_param(self, keyword):
                return self.fields[keyword]

            def unbind(self, custnode, db_h, job):
                # Since we are simulating a success condition of deletion of instances, set the id to None to
                # simulate successful deletion
                self.fields["id"] = None
                return True

            def get_all_instances_for_region_and_custid(self,
                                                        custid,
                                                        compute_region_idx,
                                                        node_type_list):
                # We will return a static list of instances and assume that they are auto-scaled instances.
                return [[4001], [4002], [4003]]

            def get_sites(self, dbh):
                if self.fields["id"] == 4001:
                    return [9001]
                if self.fields["id"] == 4002:
                    return [9002]
                if self.fields["id"] == 4003:
                    return [9003, 9004, 9005]

        # Create a basic cust node model
        class mocked_CustNodeModel():
            def __init__(self, iid=0, dbh=None):
                print("XXXXX Came to Cust Node model for iid %s!" % str(iid))
                self.id = iid
                self.custid = 15
                self.dbh = dbh
                self.okyo_edge_site_id = 0
                self.is_deleted = 0
                self.node_type = 49
                self.alt_node_type = -1
                # Assume that the cust node model is deleted.
                self.is_deleted = 1
                self.is_clean_pipe = 0
                self.region = 200
                self.name = "Aster"
                self.num_allocs = 2

            def save(self, dbh):
                return True

        # Create a mocked job class
        class mocked_Job():
            def __init__(self):
                self.status_msg = ""
                print("Mocked Job called")

            def save_job(self, dbh):
                return True


        # Create a basic cust model
        class mocked_CustomerModel():
            def __init__(self, custid, dbh):
                self.id = custid
                self.dbh = dbh
                self.fwdrulesall = ""

        #Create an avisar context.
        class mocked_AvisarCtx():
            def __init__(self):
                self.cust_id = 15
                self.cust_topology_id = 9000
                self.panorama_job_id = 12345
                self.region_id = 200

        patched_Custnodemodel.side_effect = mocked_CustNodeModel
        patched_Instancemodel.side_effect = mocked_InstanceModel
        patched_Customermodel.side_effect = mocked_CustomerModel
        patched_process_delete_topology_handle_instances.side_effect = process_delete_topology_handle_instances
        patched_process_delete_topology_handle_transient_instances.side_effect = process_delete_topology_handle_transient_instances

        assert patched_Instancemodel is orchestration_service.core.orchestrator.INST.InstanceModel
        assert patched_Custnodemodel is orchestration_service.core.orchestrator.CSN.CustNodeModel
        assert patched_Customermodel is orchestration_service.core.orchestrator.CM.CustomerModel
        assert patched_process_delete_topology_handle_instances is orchestration_service.core.orchestrator.OrchstrationHandler.process_delete_topology_handle_instances
        assert patched_process_delete_topology_handle_transient_instances is orchestration_service.core.orchestrator.OrchstrationHandler.process_delete_topology_handle_transient_instances

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = logger()
            orchHandler.db_h = dbh
            orchHandler.tenant_id = 245139
            orchHandler.avctx = mocked_AvisarCtx()

        except Exception as E:
            print("Exception %s, Traceback %s" % (str(E.args), str(traceback.format_exc())))

        job_instances =[]
        job_transient_instances = []
        answer = orchHandler.process_delete_topology(mocked_Job(),
                                                     job_instances,
                                                     job_transient_instances)

        print("Got output : %s" % str(answer))
        assert answer == testCases[suiteIdx]["testCaseExpectedOutput"]
        assert job_instances == [4000, 4001]
        assert job_transient_instances == []

class Test_failure_error_returned_by_process_delete_topology_handle_instances(TestCase):
    @patch('orchestration_service.core.orchestrator.OrchstrationHandler.process_delete_topology_handle_instances', return_value = (False, False))
    @patch('orchestration_service.core.orchestrator.CSN.CustNodeModel')
    @patch('orchestration_service.core.orchestrator.CM.CustomerModel')
    @patch('orchestration_service.core.orchestrator.INST.InstanceModel')
    def test_failure_error_returned_by_process_delete_topology_handle_instances(self,
                     patched_Instancemodel,
                     patched_Customermodel,
                     patched_Custnodemodel,
                     patched_process_delete_topology_handle_instances):
        suiteIdx=2
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        # Create a mock handle for dbh
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        # Create a basic instanceModel object.
        class mocked_InstanceModel():
            def __init__(self, iid=None, dbh=None):
                print("XXX Came to instance model")
                self.fields = {}
                self.fields["id"] = iid
                self.fields["compute_region_idx"] = 200
                self.fields["compute_region_name"] = "us-west-2"
                self.fields["is_pinned_instance"] = False
                if self.fields["id"] in [4001, 4002, 4003]:
                    self.fields["is_dynamic_instance"] = True
                else:
                    self.fields["is_dynamic_instance"] = False
                self.fields["mpdp_clusterid"] = None
                self.fields["node_type"] = 49

            def get_param(self, keyword):
                return self.fields[keyword]

            def unbind(self, custnode, db_h, job):
                # Since we are simulating a success condition of deletion of instances, set the id to None to
                # simulate successful deletion
                self.fields["id"] = None
                return True

            def get_all_instances_for_region_and_custid(self,
                                                        custid,
                                                        compute_region_idx,
                                                        node_type_list):
                # We will return a static list of instances and assume that they are auto-scaled instances.
                return [[4001], [4002], [4003]]

            def get_sites(self, dbh):
                if self.fields["id"] == 4001:
                    return [9001]
                if self.fields["id"] == 4002:
                    return [9002]
                if self.fields["id"] == 4003:
                    return [9003, 9004, 9005]

        # Create a basic cust node model
        class mocked_CustNodeModel():
            def __init__(self, iid=0, dbh=None):
                print("XXXXX Came to Cust Node model for iid %s!" % str(iid))
                self.id = iid
                self.custid = 15
                self.dbh = dbh
                self.okyo_edge_site_id = 0
                self.is_deleted = 0
                self.node_type = 49
                self.alt_node_type = -1
                # Assume that the cust node model is deleted.
                self.is_deleted = 1
                self.is_clean_pipe = 0
                self.region = 200
                self.name = "Aster"

            def save(self, dbh):
                return True

        # Create a mocked job class
        class mocked_Job():
            def __init__(self):
                self.status_msg = ""
                print("Mocked Job called")

            def save_job(self, dbh):
                return True


        # Create a basic cust model
        class mocked_CustomerModel():
            def __init__(self, custid, dbh):
                self.id = custid
                self.dbh = dbh
                self.fwdrulesall = ""

        #Create an avisar context.
        class mocked_AvisarCtx():
            def __init__(self):
                self.cust_id = 15
                self.cust_topology_id = 9000
                self.panorama_job_id = 12345
                self.region_id = 200

        patched_Custnodemodel.side_effect = mocked_CustNodeModel
        patched_Instancemodel.side_effect = mocked_InstanceModel
        patched_Customermodel.side_effect = mocked_CustomerModel

        assert patched_Instancemodel is orchestration_service.core.orchestrator.INST.InstanceModel
        assert patched_Custnodemodel is orchestration_service.core.orchestrator.CSN.CustNodeModel
        assert patched_Customermodel is orchestration_service.core.orchestrator.CM.CustomerModel

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = logger()
            orchHandler.db_h = dbh
            orchHandler.tenant_id = 245139
            orchHandler.avctx = mocked_AvisarCtx()

        except Exception as E:
            print("Exception %s, Traceback %s" % (str(E.args), str(traceback.format_exc())))

        job_instances =[]
        job_transient_instances = []
        answer = orchHandler.process_delete_topology(mocked_Job(),
                                                     job_instances,
                                                     job_transient_instances)

        print("Got output : %s" % str(answer))
        assert answer == testCases[suiteIdx]["testCaseExpectedOutput"]
        assert job_instances == []
        assert job_transient_instances == []