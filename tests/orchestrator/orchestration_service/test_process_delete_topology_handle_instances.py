import traceback
import mock_imports_orchestrator
from unittest import TestCase
from unittest.mock import patch
import libs.model.instancemodel
from orchestration_service.core.orchestrator import OrchstrationHandler
'''
PYTHONPATH=~/saas-infra/src/apps/orchestrator/ pytest -s orchestrator.py
'''

def set_cfg(cfg):
    cfg['log-root-dir']= '/tmp/',
    cfg['region'] = 'us-west-2',
    cfg['queues'] =  []
    cfg['dbpassword'] = "mankind"
    cfg['unittest'] = True

class logger():
    def info(self, *args):
        print(args)

    def error(self, *args):
        print(args)

    def warn(self, *args):
        print(args)

class Mock_conn():
    def __init__(self):
        print("Making conn")
        pass

    def close(self):
        pass

class Mock_cursor():
    def __init__(self, testSuiteIdx):
        self.sql = None
        self.params = None
        self.testSuiteIdx = testSuiteIdx

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params:
            print("Fetching all for %s" % str(self.sql%self.params))
            query = self.sql%self.params
        else:
            print("Fetching all for %s" % str(self.sql))
            query = self.sql
        answer = testCases[self.testSuiteIdx]["testCaseQueryResult"].get(query)
        return answer

    def fetchone(self):
        return {"name" : "Tejas"}

class Mock_DBHandler():
    def __init__(self, logger, testSuiteIdx):
        self.logger = logger
        self.conn = Mock_conn()
        self.testSuiteIdx = testSuiteIdx

    def conn(self):
        return self.conn

    def get_cursor(self):
        return  Mock_cursor(self.testSuiteIdx)

    def cursorclose(self, cursor):
        return

testCases = [
        { "testCaseName": "test_delete_topology_handle_instances_success",
          "testCaseQueryResult" : {
    "SELECT instance1_id AS inst_id FROM cust_topology WHERE id=33705 UNION SELECT instance2_id AS inst_id FROM cust_topology WHERE id=33705" : (([4152], [4153]))

},
          "testCaseExpectedOutput" : (True, True)
        },
        { "testCaseName": "test_delete_topology_handle_instances_success_instance_not_deleted",
          "testCaseQueryResult" : {
    "SELECT instance1_id AS inst_id FROM cust_topology WHERE id=33705 UNION SELECT instance2_id AS inst_id FROM cust_topology WHERE id=33705" : (([4152], [4153]))
},
          "testCaseExpectedOutput" : (True, False),
        },
        { "testCaseName": "test_delete_topology_handle_instances_failure",
                  "testCaseQueryResult" : {
            "SELECT instance1_id AS inst_id FROM cust_topology WHERE id=33705 UNION SELECT instance2_id AS inst_id FROM cust_topology WHERE id=33705" : (([4152], [4153]))
        },
          "testCaseExpectedOutput" : (False, False)
    },
        { "testCaseName": "test_delete_topology_handle_instances_instance1_id_as_0",
                  "testCaseQueryResult" : {
            "SELECT instance1_id AS inst_id FROM cust_topology WHERE id=33705 UNION SELECT instance2_id AS inst_id FROM cust_topology WHERE id=33705" : (([0], [4153]))
        },
          "testCaseExpectedOutput" : (True, True)
    }
]
'''
def process_delete_topology_handle_instances(self,
                                             custnode,
                                             deleteInstanceDetailsObj):
'''

# TestCaseName: test_delete_topology_handle_instances_success, idx 0 from testCases
class Test_success(TestCase):
    @patch('libs.model.custmodel.CustomerModel')
    @patch('libs.model.instancemodel.InstanceModel')
    @patch('libs.model.custnodemodel.CustNodeModel')
    def test_success(self,
                                                         patched_Custnodemodel,
                                                         patched_Instancemodel,
                                                         patched_CustomerModel
                                                         ):
        suiteIdx=0
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        # Create a mock handle for dbh
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        # Create a basic cust node model
        class mocked_CustNodeModel():
            def __init__(self, custid, dbh):
                self.id = 33705
                self.custid = custid
                self.dbh = dbh
                self.okyo_edge_site_id = 0

        # Create a basic cut model
        class mocked_CustomerModel():
            def __init__(self, custid, dbh):
                self.id = custid
                self.dbh = dbh
                self.fwdrulesall = ""

        # Create a basic instanceModel object.
        class mocked_InstanceModel():
            def __init__(self, instanceid, db_h):
                self.fields = {}
                self.fields["id"] = instanceid
                self.fields["compute_region_idx"] = 200
                self.fields["compute_region_name"] = "us-west-2"
                self.fields["is_pinned_instance"] = False
                self.fields["mpdp_clusterid"] = None
                self.fields["node_type"] = 48
                self.fields["is_dedicated_inbound_instance"] = False
                self.fields["alias"] = "alias_name"
                self.fields["is_using_sp_interconnect"] = False
                self.fields["vmid"] = "123456"

            def get_param(self, keyword):
                return self.fields[keyword]

            def unbind(self, custnode, db_h, job):
                # Since we are simulating a success condition of deletion of instances, set the id to None to
                # simulate successful deletion
                self.fields["id"] = None
                return True

        patched_Custnodemodel.side_effect = mocked_CustNodeModel
        patched_Instancemodel.side_effect = mocked_InstanceModel
        patched_CustomerModel.side_effect = mocked_CustomerModel

        assert patched_Custnodemodel is libs.model.custnodemodel.CustNodeModel
        assert patched_Instancemodel is libs.model.instancemodel.InstanceModel
        assert patched_CustomerModel is libs.model.custmodel.CustomerModel

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = logger()
            orchHandler.db_h = dbh
        except Exception as E:
            print("Exception %s, Traceback %s" % (str(E.args), str(traceback.format_exc())))

        # Create a mocked custnode model object.
        custnode = mocked_CustNodeModel(custid=15, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.set_job_instances([])
        deleteInstanceDetailsObj.set_job_transient_instances([])
        answer = orchHandler.process_delete_topology_handle_instances(custnode,
                                                                      deleteInstanceDetailsObj)
        print(vars(deleteInstanceDetailsObj))
        print("Got output : %s" % str(answer))
        assert answer == testCases[suiteIdx]["testCaseExpectedOutput"]
        assert deleteInstanceDetailsObj.job_instances == [4152, 4153]
        assert deleteInstanceDetailsObj.job_transient_instances == []


# TestCaseName: test_delete_topology_handle_instances_success_instance_not_deleted, idx 1 from testCases
class Test_success_instance_not_cleanedup(TestCase):
    @patch('libs.model.custmodel.CustomerModel')
    @patch('libs.model.instancemodel.InstanceModel')
    @patch('libs.model.custnodemodel.CustNodeModel')
    def test_get_gcp_cust_region_instance_params_success(self,
                                                         patched_Custnodemodel,
                                                         patched_Instancemodel,
                                                         patched_CustomerModel
                                                         ):
        suiteIdx=1
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        # Create a mock handle for dbh
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        # Create a basic cust node model
        class mocked_CustNodeModel():
            def __init__(self, custid, dbh):
                self.id = 33705
                self.custid = custid
                self.dbh = dbh
                self.okyo_edge_site_id = 0

        # Create a basic cut model
        class mocked_CustomerModel():
            def __init__(self, custid, dbh):
                self.id = custid
                self.dbh = dbh
                self.fwdrulesall = ""

        # Create a basic instanceModel object.
        class mocked_InstanceModel():
            def __init__(self, instanceid, db_h):
                self.fields = {}
                self.fields["id"] = instanceid
                self.fields["compute_region_idx"] = 200
                self.fields["compute_region_name"] = "us-west-2"
                self.fields["is_pinned_instance"] = False
                self.fields["mpdp_clusterid"] = None
                self.fields["node_type"] = 48
                self.fields["is_dedicated_inbound_instance"] = False
                self.fields["alias"] = "alias_name"
                self.fields["is_using_sp_interconnect"] = False
                self.fields["vmid"] = "123456"

            def get_param(self, keyword):
                return self.fields[keyword]

            def unbind(self, custnode, db_h, job):
                # Since we are simulating a success condition of deletion of instances, set the id to None to
                # simulate successful deletion
                return True

        patched_Custnodemodel.side_effect = mocked_CustNodeModel
        patched_Instancemodel.side_effect = mocked_InstanceModel
        patched_CustomerModel.side_effect = mocked_CustomerModel

        assert patched_Custnodemodel is libs.model.custnodemodel.CustNodeModel
        assert patched_Instancemodel is libs.model.instancemodel.InstanceModel
        assert patched_CustomerModel is libs.model.custmodel.CustomerModel

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = logger()
            orchHandler.db_h = dbh
        except Exception as E:
            print("Exception %s, Traceback %s" % (str(E.args), str(traceback.format_exc())))

        # Create a mocked custnode model object.
        custnode = mocked_CustNodeModel(custid=15, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.set_job_instances([])
        deleteInstanceDetailsObj.set_job_transient_instances([])

        answer = orchHandler.process_delete_topology_handle_instances(custnode,
                                                                      deleteInstanceDetailsObj)
        print(vars(deleteInstanceDetailsObj))
        print("Got output : %s" % str(answer))
        assert answer == testCases[suiteIdx]["testCaseExpectedOutput"]
        assert deleteInstanceDetailsObj.job_instances == [4152, 4153]
        assert deleteInstanceDetailsObj.job_transient_instances == []


# TestCaseName: test_delete_topology_handle_instances_failure, idx 2 from testCases
class Test_failure(TestCase):
    @patch('libs.model.custmodel.CustomerModel')
    @patch('libs.model.instancemodel.InstanceModel')
    @patch('libs.model.custnodemodel.CustNodeModel')
    def test_failure(self,
                     patched_Custnodemodel,
                     patched_Instancemodel,
                     patched_CustomerModel):
        suiteIdx=2
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        # Create a mock handle for dbh
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        # Create a basic cust node model
        class mocked_CustNodeModel():
            def __init__(self, custid, dbh):
                self.id = 33705
                self.custid = custid
                self.dbh = dbh
                self.okyo_edge_site_id = 0
                self.name = "jupiter-rn"

        # Create a basic cut model
        class mocked_CustomerModel():
            def __init__(self, custid, dbh):
                self.id = custid
                self.dbh = dbh
                self.fwdrulesall = ""

        # Create a basic instanceModel object.
        class mocked_InstanceModel():
            def __init__(self, instanceid, db_h):
                self.fields = {}
                self.fields["name"] = str("FW-") + str(instanceid)
                self.fields["id"] = instanceid
                self.fields["compute_region_idx"] = 200
                self.fields["compute_region_name"] = "us-west-2"
                self.fields["is_pinned_instance"] = False
                self.fields["mpdp_clusterid"] = None
                self.fields["node_type"] = 48
                if instanceid == 4152:
                    self.fields["name"] = "FW_4152_us-west-2_mlala-245139"
                elif instanceid == 4153:
                    self.fields["name"] = "FW_4153_us-west-2_mlala-245139"
                self.fields["is_dedicated_inbound_instance"] = False
                self.fields["alias"] = "alias_name"
                self.fields["is_using_sp_interconnect"] = False
                self.fields["vmid"] = "123456"

            def get_param(self, keyword):
                return self.fields[keyword]

            def unbind(self, custnode, db_h, job):
                # Since we are simulating a success condition of deletion of instances, set the id to None to
                # simulate un-successful deletion
                return False

        # Create a mocked job class
        class mocked_Job():
            def __init__(self):
                print("Mocked Job called")
            def save_job(self, dbh):
                return True

        patched_Custnodemodel.side_effect = mocked_CustNodeModel
        patched_Instancemodel.side_effect = mocked_InstanceModel
        patched_CustomerModel.side_effect = mocked_CustomerModel

        assert patched_Custnodemodel is libs.model.custnodemodel.CustNodeModel
        assert patched_Instancemodel is libs.model.instancemodel.InstanceModel
        assert patched_CustomerModel is libs.model.custmodel.CustomerModel

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = logger()
            orchHandler.db_h = dbh
        except Exception as E:
            print("Exception %s, Traceback %s" % (str(E.args), str(traceback.format_exc())))

        # Create a mocked custnode model object.
        custnode = mocked_CustNodeModel(custid=15, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.job = mocked_Job()
        deleteInstanceDetailsObj.set_job_instances([])
        deleteInstanceDetailsObj.set_job_transient_instances([])
        answer = orchHandler.process_delete_topology_handle_instances(custnode,
                                                                      deleteInstanceDetailsObj)
        print(vars(deleteInstanceDetailsObj))
        print("Got output : %s" % str(answer))
        assert answer == testCases[suiteIdx]["testCaseExpectedOutput"]
        assert deleteInstanceDetailsObj.job_instances == [4152, 4153]
        assert deleteInstanceDetailsObj.job_transient_instances == []


# TestCaseName: test_delete_topology_handle_instances_success_incomplete_instance_deletion, idx 3 from testCases
# Here the instance1_id is 0 but instance2_id is populated. This can happen due to incomplete cleanup.

class Test_success_instance1_id_as_0(TestCase):
    @patch('libs.model.custmodel.CustomerModel')
    @patch('libs.model.instancemodel.InstanceModel')
    @patch('libs.model.custnodemodel.CustNodeModel')
    def test_success_instance1_id_as_0(self,
                                       patched_Custnodemodel,
                                       patched_Instancemodel,
                                       patched_CustomerModel
                                       ):
        suiteIdx=3
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        # Create a mock handle for dbh
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        # Create a basic cust node model
        class mocked_CustNodeModel():
            def __init__(self, custid, dbh):
                self.id = 33705
                self.custid = custid
                self.dbh = dbh
                self.okyo_edge_site_id = 0
                self.name = "jupiter-rn"

        # Create a basic cut model
        class mocked_CustomerModel():
            def __init__(self, custid, dbh):
                self.id = custid
                self.dbh = dbh
                self.fwdrulesall = ""

        # Create a basic instanceModel object.
        class mocked_InstanceModel():
            def __init__(self, instanceid, db_h):
                self.fields = {}
                self.fields["name"] = str("FW-") + str(instanceid)
                self.fields["id"] = instanceid
                self.fields["compute_region_idx"] = 200
                self.fields["compute_region_name"] = "us-west-2"
                self.fields["is_pinned_instance"] = False
                self.fields["mpdp_clusterid"] = None
                self.fields["node_type"] = 48
                if instanceid == 4152:
                    self.fields["name"] = "FW_4152_us-west-2_mlala-245139"
                elif instanceid == 4153:
                    self.fields["name"] = "FW_4153_us-west-2_mlala-245139"
                self.fields["is_dedicated_inbound_instance"] = False
                self.fields["alias"] = "alias_name"
                self.fields["is_using_sp_interconnect"] = False
                self.fields["vmid"] = "123456"
            def get_param(self, keyword):
                return self.fields[keyword]

            def unbind(self, custnode, db_h, job):
                # Since we are simulating a success condition of deletion of instances, set the id to None to
                # simulate un-successful deletion
                self.fields["id"] = None
                return True


        # Create a mocked job class
        class mocked_Job():
            def __init__(self):
                print("Mocked Job called")
            def save_job(self, dbh):
                return True

        patched_Custnodemodel.side_effect = mocked_CustNodeModel
        patched_Instancemodel.side_effect = mocked_InstanceModel
        patched_CustomerModel.side_effect = mocked_CustomerModel

        assert patched_Custnodemodel is libs.model.custnodemodel.CustNodeModel
        assert patched_Instancemodel is libs.model.instancemodel.InstanceModel
        assert patched_CustomerModel is libs.model.custmodel.CustomerModel

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = logger()
            orchHandler.db_h = dbh
        except Exception as E:
            print("Exception %s, Traceback %s" % (str(E.args), str(traceback.format_exc())))

        # Create a mocked custnode model object.
        custnode = mocked_CustNodeModel(custid=15, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.job = mocked_Job()
        deleteInstanceDetailsObj.set_job_instances([])
        deleteInstanceDetailsObj.set_job_transient_instances([])
        answer = orchHandler.process_delete_topology_handle_instances(custnode,
                                                                      deleteInstanceDetailsObj)
        print(vars(deleteInstanceDetailsObj))
        print("Got output : %s" % str(answer))
        assert answer == testCases[suiteIdx]["testCaseExpectedOutput"]
        assert deleteInstanceDetailsObj.job_instances == [4153, -1]
        assert deleteInstanceDetailsObj.job_transient_instances == []
