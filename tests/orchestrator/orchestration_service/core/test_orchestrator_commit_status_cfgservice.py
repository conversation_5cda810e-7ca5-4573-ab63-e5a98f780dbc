from unittest.mock import patch, MagicMock
import pytest
import logging
import mock_imports_orchestrator
import json
from orchestration_service.core import orchestrator_commit_status_cfgservice


class mockDbh:
    def __init__(self, logger):
        self.logger = logger


class TestOrchCommitStatusCfgService:
    @patch('orchestration_service.core.orchestrator_commit_status_cfgservice.b64encode')
    @pytest.mark.parametrize("test_input_payload, convert_str_to_bytes_ret_val, expected_output",
                             [
                                 ({"tenant_id": "123", "log_id": "abc"},
                                  "eyJwYW5vcmFtYV9qb2JfaWQiOiAiMzExMiIsICJjdXN0X3RvcG9sb2d5X2lkIjogNTEyLCAiaW5zdGFuY2UxX2lkIjogODAxLCAiaW5zdGFuY2UyX2lkIjogODAyLCAid29ya2Zsb3dfdHlwZSI6ICJUT1BPTE9HWV9BREQiLCAibG9nX2lkIjogIjcyZjBkM2VhLWFiOGMtNDE3MS05ZGFiLWFmZWIyZjhiYzBmNiIsICJ3b3JrZmxvd19zdGF0dXMiOiAiIiwgIndvcmtmbG93X3N0YXR1c19kZXRhaWxzIjogIiIsICJvcmNoZXN0cmF0b3Jfc3RhdHVzIjogMywgIm9yY2hlc3RyYXRvcl9zdGF0dXNfZGV0YWlscyI6ICIiLCAibm9kZV90eXBlIjogNDgsICJjbGllbnRfaWQiOiAib3JjaGVzdHJhdG9yIiwgInJlcXVlc3RfdHlwZSI6ICJJbnN0YW5jZU9uYm9hcmRpbmdVcGRhdGVSZXF1ZXN0IiwgInN1Yl90ZW5hbnRfaWQiOiAiODk1ODY3ODY2In0=",
                                  {
                                      "data": "eyJwYW5vcmFtYV9qb2JfaWQiOiAiMzExMiIsICJjdXN0X3RvcG9sb2d5X2lkIjogNTEyLCAiaW5zdGFuY2UxX2lkIjogODAxLCAiaW5zdGFuY2UyX2lkIjogODAyLCAid29ya2Zsb3dfdHlwZSI6ICJUT1BPTE9HWV9BREQiLCAibG9nX2lkIjogIjcyZjBkM2VhLWFiOGMtNDE3MS05ZGFiLWFmZWIyZjhiYzBmNiIsICJ3b3JrZmxvd19zdGF0dXMiOiAiIiwgIndvcmtmbG93X3N0YXR1c19kZXRhaWxzIjogIiIsICJvcmNoZXN0cmF0b3Jfc3RhdHVzIjogMywgIm9yY2hlc3RyYXRvcl9zdGF0dXNfZGV0YWlscyI6ICIiLCAibm9kZV90eXBlIjogNDgsICJjbGllbnRfaWQiOiAib3JjaGVzdHJhdG9yIiwgInJlcXVlc3RfdHlwZSI6ICJJbnN0YW5jZU9uYm9hcmRpbmdVcGRhdGVSZXF1ZXN0IiwgInN1Yl90ZW5hbnRfaWQiOiAiODk1ODY3ODY2In0=",
                                      "data_type": 1, "data_encoding": 0, "log_tag": "abc"})
                             ])
    def test_get_publish_payload(self, mock_convert_str_to_bytes, test_input_payload,
                                 convert_str_to_bytes_ret_val, expected_output):
        mock_convert_str_to_bytes.return_value = convert_str_to_bytes_ret_val
        actual_out, err_code, err_msg = orchestrator_commit_status_cfgservice._get_publish_payload(test_input_payload)
        assert actual_out == expected_output
        assert err_code == 0

    @patch("orchestration_service.core.orchestrator_commit_status_cfgservice.OrchCfgModel")
    @patch("orchestration_service.core.orchestrator_commit_status_cfgservice.gcp_authenticate")
    @patch("orchestration_service.core.orchestrator_commit_status_cfgservice.gcpPubSub")
    @patch("orchestration_service.core.orchestrator_commit_status_cfgservice.cfg", {"aws_env": "dev",
                                                                                    "aws_env_type": "commercial"})
    @patch("orchestration_service.core.orchestrator_commit_status_cfgservice.get_pac_project_name")
    @patch("orchestration_service.core.orchestrator_commit_status_cfgservice.is_china_env", return_value=False)
    @pytest.mark.parametrize("test_input_payload, expected_out",
                             [
                                 ({"workflow_type": "TOPOLOGY_UPDATE", "node_type": 48}, "successfully published")
                             ])
    def test_send_configservice_commit_status(self, mock_china, mock_getProj, mock_gcpPubSub,
                                              mock_gcp_authenticate, mock_orchCfgModel, test_input_payload,
                                              expected_out, caplog):
        mock_gcpPubSub.return_value.PubSubClient = MagicMock()
        mock_gcpPubSub.PubSubClient.return_value.set_topic_path.return_value = None
        mock_gcpPubSub.PubSubClient.return_value.is_topic_valid.return_value = True
        mock_gcpPubSub.PubSubClient.return_value.publish.return_value = True
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        dbh = mockDbh(logger)
        input_payload = dict()
        input_payload["tenant_id"] = "123"
        input_payload.update(test_input_payload)
        input_payload["panorama_job_id"] = "1233"
        input_payload["workflow_status"] = ""
        input_payload["workflow_status_details"] = ""
        input_payload["instance1_id"] = "1"
        input_payload["instance2_id"] = "2"
        input_payload["cust_topology_id"] = "23"
        input_payload["orchestrator_status"] = ""
        input_payload["orchestrator_status_details"] = ""
        input_payload["log_id"] = "abc"
        orchestrator_commit_status_cfgservice.send_configservice_commit_status(input_payload, dbh)
        assert expected_out in caplog.text

    @patch("orchestration_service.core.orchestrator_commit_status_cfgservice.is_china_env", return_value=True)
    @patch("orchestration_service.core.orchestrator_commit_status_cfgservice.cfg", {"aws_env_type": "commercial"})
    def test_send_configservice_commit_status_china(self, mock_env, caplog):
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        dbh = mockDbh(logger)
        input_payload = dict()
        orchestrator_commit_status_cfgservice.send_configservice_commit_status(input_payload, dbh)
        assert "Avoid sending message to config service since env" in caplog.text

    @patch("orchestration_service.core.orchestrator_commit_status_cfgservice.is_china_env", return_value=False)
    @patch("orchestration_service.core.orchestrator_commit_status_cfgservice.cfg", {"aws_env_type": "fedramp-il5"})
    def test_send_configservice_commit_status_fedramp(self, mock_env, caplog):
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        dbh = mockDbh(logger)
        input_payload = dict()
        orchestrator_commit_status_cfgservice.send_configservice_commit_status(input_payload, dbh)
        assert "Avoid sending message to config service since env" in caplog.text
