import pytest
from unittest import TestCase
from unittest.mock import patch
from unittest.mock import <PERSON><PERSON><PERSON> as <PERSON><PERSON>
from orchestration_service.core.orchestrator_onramp_mgmt import allocate_onramp_rn_nodes

def print_me_one_arg(info_msg):
    print("MSG: " + info_msg)

def mock_create_onramp_rn_child_node_success(*args):
    return True

def mock_create_onramp_rn_child_node_failure(*args):
    return False

def mock_create_onramp_rn_child_node_exception(*args):
    raise Exception("Failed to create RN node")

class TestAllocateOnrampNodes(TestCase):

    def test_allocate_onramp_rn_nodes_success_single_node(self):
        db_h_mock = Mock()
        db_h_mock.logger.info.side_effect = print_me_one_arg
        db_h_mock.logger.error.side_effect = print_me_one_arg

        custnode_mock = Mock()
        custnode_mock.custid = 12345
        custnode_mock.region = "us-west-2"

        with patch('orchestration_service.core.orchestrator_onramp_mgmt.create_onramp_rn_child_node', side_effect=mock_create_onramp_rn_child_node_success):
            result = allocate_onramp_rn_nodes(db_h_mock, custnode_mock, 1)

        assert result == 1

    def test_allocate_onramp_rn_nodes_success_multiple_nodes(self):
        db_h_mock = Mock()
        db_h_mock.logger.info.side_effect = print_me_one_arg
        db_h_mock.logger.error.side_effect = print_me_one_arg

        custnode_mock = Mock()
        custnode_mock.custid = 12345
        custnode_mock.region = "us-west-2"

        with patch('orchestration_service.core.orchestrator_onramp_mgmt.create_onramp_rn_child_node', side_effect=mock_create_onramp_rn_child_node_success):
            result = allocate_onramp_rn_nodes(db_h_mock, custnode_mock, 3)

        assert result == 3

    def test_allocate_onramp_rn_nodes_zero_nodes(self):
        db_h_mock = Mock()
        db_h_mock.logger.info.side_effect = print_me_one_arg
        db_h_mock.logger.error.side_effect = print_me_one_arg

        custnode_mock = Mock()
        custnode_mock.custid = 12345
        custnode_mock.region = "us-west-2"

        result = allocate_onramp_rn_nodes(db_h_mock, custnode_mock, 0)
        assert result == 0

    def test_allocate_onramp_rn_nodes_partial_success(self):
        db_h_mock = Mock()
        db_h_mock.logger.info.side_effect = print_me_one_arg
        db_h_mock.logger.error.side_effect = print_me_one_arg

        custnode_mock = Mock()
        custnode_mock.custid = 12345
        custnode_mock.region = "us-west-2"

        def mock_create_partial_success(*args):
            if args[2] == 0:
                return True
            else:
                return False

        with patch('orchestration_service.core.orchestrator_onramp_mgmt.create_onramp_rn_child_node', side_effect=mock_create_partial_success):
            result = allocate_onramp_rn_nodes(db_h_mock, custnode_mock, 3)

        assert result == 1

    def test_allocate_onramp_rn_nodes_creation_exception(self):
        db_h_mock = Mock()
        db_h_mock.logger.info.side_effect = print_me_one_arg
        db_h_mock.logger.error.side_effect = print_me_one_arg

        custnode_mock = Mock()
        custnode_mock.custid = 12345
        custnode_mock.region = "us-west-2"

        with patch('orchestration_service.core.orchestrator_onramp_mgmt.create_onramp_rn_child_node', side_effect=mock_create_onramp_rn_child_node_exception):
            result = allocate_onramp_rn_nodes(db_h_mock, custnode_mock, 2)

        assert result == -1

    def test_allocate_onramp_rn_nodes_custnode_attribute_error(self):
        db_h_mock = Mock()
        db_h_mock.logger.info.side_effect = print_me_one_arg
        db_h_mock.logger.error.side_effect = print_me_one_arg

        custnode_mock = Mock()
        del custnode_mock.custid

        result = allocate_onramp_rn_nodes(db_h_mock, custnode_mock, 1)
        assert result == 0

    def test_allocate_onramp_rn_nodes_all_creation_failures(self):
        db_h_mock = Mock()
        db_h_mock.logger.info.side_effect = print_me_one_arg
        db_h_mock.logger.error.side_effect = print_me_one_arg

        custnode_mock = Mock()
        custnode_mock.custid = 12345
        custnode_mock.region = "us-west-2"

        with patch('orchestration_service.core.orchestrator_onramp_mgmt.create_onramp_rn_child_node', side_effect=mock_create_onramp_rn_child_node_failure):
            result = allocate_onramp_rn_nodes(db_h_mock, custnode_mock, 2)

        assert result == 0
