import pytest
import logging
from unittest import TestCase
from unittest.mock import patch, MagicMock
from mock_models import *
from orchestration_service.core.orchestrator_nlb_mgmt import disable_nlb_strong_session_affinity_configuration, \
    allocate_nlb, get_nlb_ip_address_map, \
    update_instance_behind_nlb, update_instances_behind_nlb

class logger():
    def info(self, *args):
        print(args)

    def error(self, *args):
        print(args)

    def warn(self, *args):
        print(args)

class Mock_conn():
    def __init__(self):
        print("Making conn")
        pass

    def close(self):
        pass

class Mock_cursor():
    def __init__(self, testSuiteIdx):
        self.sql = None
        self.params = None
        self.testSuiteIdx = testSuiteIdx

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params:
            print("Fetching all for %s" % str(self.sql%self.params))
            query = self.sql%self.params
        else:
            print("Fetching all for %s" % str(self.sql))
            query = self.sql
        answer = testCases[self.testSuiteIdx]["testCaseQueryResult"].get(query)
        return answer

    def fetchone(self):
        return {"name" : "Tejas"}

class Mock_DBHandler():
    def __init__(self, logger, testSuiteIdx):
        self.logger = logger
        self.conn = Mock_conn()
        self.testSuiteIdx = testSuiteIdx

    def conn(self):
        return self.conn

    def get_cursor(self):
        return  Mock_cursor(self.testSuiteIdx)

    def cursorclose(self, cursor):
        return

class Mock_cursorIpv4():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        return ["**************", '{"277": "**************"}']

class Mock_DBHandlerIpv4():
    def __init__(self, logger):
        self.logger = logger

    def get_cursor(self):
        return Mock_cursorIpv4()

    def cursorclose(self, cursor):
        return

class Mock_cursorIpv6():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        return ["2ff0:1000:ab::2", '{"277": "2ff0:1500:ab::2"}']

    def fetchall(self):
        return [("1001", "GPGW_1001", 277)]

class Mock_DBHandlerIpv6():
    def __init__(self, logger):
        self.logger = logger

    def get_cursor(self):
        return Mock_cursorIpv6()

    def cursorclose(self, cursor):
        return
 
class MockInstanceIpv6Model():
    def __init__(self, dbh, iid=None, instance_id=None):
        self.dbh = dbh
        self.instance_id = 1
    def set_param(self,input1, input2):
        return
    def save(self):
        return
    def save_stage4(self):
        return
    def get_param(self, input):
        if input=="delete_after":
            return 0
        if input=="is_instance_behind_nlb":
            return 1
        if input=="name":
            return "GPGW_1001"
        else:
            return 1

    def update_column_interface_ip_list(self, interface_ip_list):
        return

    def update_column_interface_ipv6_list(self, interface_ipv6_list):
        return

    def get_all_gp_gw_instance_for_region_and_custid(self, custid, compute_region_idx):
        return [('1001', 277)]

class TestOrchestratorNlbMgmt(TestCase):
    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    @patch('orchestration_service.core.orchestrator_nlb_mgmt.get_is_ngpa_protocol_enabled', return_value = "1")
    def test_disable_nlb_strong_session_affinity_configuration(self, mock_ngpa_enabled):
        suiteIdx=1
        self.db_h = Mock_DBHandler(logger(), testSuiteIdx=suiteIdx)
        answer = disable_nlb_strong_session_affinity_configuration(self.db_h, custid=115, region_id=211)
        print("Got output : %s" % str(answer))
        self.assertEqual(answer, None)
    
    @patch('orchestration_service.core.orchestrator_nlb_mgmt.has_nlb_in_region', return_value = 0) 
    @patch('orchestration_service.core.orchestrator_nlb_mgmt.generate_sample_log_tag', return_value = b"sample_tag")
    def test_allocate_nlb(self, mock_tag, mock_has_nlb):
        dbh = DB()
        with patch('orchestration_service.core.orchestrator_nlb_mgmt.CustomerModel', wraps=MockCustomerModel) as mock_cust:
            with patch('orchestration_service.core.orchestrator_nlb_mgmt.CustNodeModel', wraps=MockCustNodeModel) as mock_cust_node:
                with patch('orchestration_service.core.orchestrator_nlb_mgmt.RegionMasterModel', wraps=MockRegionMasterModel) as mock_region_master:
                    custid = 123
                    compute_region_idx = 214
                    ret = allocate_nlb(dbh, custid, compute_region_idx)

                    assert ret == 1

    def test_get_nlb_ip_address_map(self):
        self._caplog.set_level(logging.INFO)
        dbh = Mock_DBHandlerIpv4(logger=logging.getLogger('testlogger'))
        custid = 123
        compute_region_idx = 214
        ipv4_map = get_nlb_ip_address_map(dbh, custid, compute_region_idx)

        assert ipv4_map["214"] == "**************"
        assert ipv4_map["277"] == "**************"

    def test_update_instance_behind_nlb(self):
        self._caplog.set_level(logging.INFO)

        dbh = Mock_DBHandlerIpv6(logger=logging.getLogger('testlogger'))
        instanceModel = MockInstanceIpv6Model(dbh)

        ret = update_instance_behind_nlb(dbh, instanceModel)

        assert ret == True

    @patch('orchestration_service.core.orchestrator_nlb_mgmt.InstanceModel', wraps = MockInstanceIpv6Model)
    def test_update_instances_behind_nlb(self, mockInstanceModel):
        self._caplog.set_level(logging.INFO)

        dbh = Mock_DBHandlerIpv6(logger=logging.getLogger('testlogger'))

        custid = 123456
        compute_region_idx = 277

        ret = update_instances_behind_nlb(dbh, custid, compute_region_idx)

class Mock_cursor_interconnect():
    def __init__(self, return_empty=False, should_fail=False):
        self.sql = None
        self.params = None
        self.return_empty = return_empty
        self.should_fail = should_fail

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.should_fail:
            return None
        if self.return_empty:
            return []
        return [("topology_123",), ("topology_456",)]

class Mock_DBHandler_interconnect():
    def __init__(self, logger, return_empty=False, should_fail=False):
        self.logger = logger
        self.return_empty = return_empty
        self.should_fail = should_fail

    def get_cursor(self):
        return Mock_cursor_interconnect(self.return_empty, self.should_fail)

    def cursorclose(self, cursor):
        return

class TestHasInterconnectOnrampIlbInRegion(TestCase):
    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    @patch('orchestration_service.core.orchestrator_nlb_mgmt.find_topology_entries_by_custid_and_region_id')
    def test_has_interconnect_onramp_ilb_in_region_with_topology_entries(self, mock_find_topology):
        from orchestration_service.core.orchestrator_nlb_mgmt import has_interconnect_onramp_ilb_in_region

        dbh = Mock_DBHandler_interconnect(logger())
        mock_find_topology.return_value = (True, ["topology_123", "topology_456"])

        result = has_interconnect_onramp_ilb_in_region(dbh, custid=115, compute_region_idx=211)

        self.assertEqual(result, True)
        mock_find_topology.assert_called_once()

    @patch('orchestration_service.core.orchestrator_nlb_mgmt.find_topology_entries_by_custid_and_region_id')
    def test_has_interconnect_onramp_ilb_in_region_no_topology_entries(self, mock_find_topology):
        from orchestration_service.core.orchestrator_nlb_mgmt import has_interconnect_onramp_ilb_in_region

        dbh = Mock_DBHandler_interconnect(logger(), return_empty=True)
        mock_find_topology.return_value = (True, [])

        result = has_interconnect_onramp_ilb_in_region(dbh, custid=115, compute_region_idx=211)

        self.assertEqual(result, False)

    @patch('orchestration_service.core.orchestrator_nlb_mgmt.find_topology_entries_by_custid_and_region_id')
    def test_has_interconnect_onramp_ilb_in_region_lookup_failure(self, mock_find_topology):
        from orchestration_service.core.orchestrator_nlb_mgmt import has_interconnect_onramp_ilb_in_region

        dbh = Mock_DBHandler_interconnect(logger(), should_fail=True)
        mock_find_topology.return_value = (False, [])

        with self.assertRaises(Exception) as context:
            has_interconnect_onramp_ilb_in_region(dbh, custid=115, compute_region_idx=211)

        self.assertIn("Failed to lookup OnRamp ILB instance", str(context.exception))
        self.assertIn("custid: 115", str(context.exception))
        self.assertIn("compute_region: 211", str(context.exception))

    @patch('orchestration_service.core.orchestrator_nlb_mgmt.find_topology_entries_by_custid_and_region_id')
    def test_has_interconnect_onramp_ilb_in_region_single_topology_entry(self, mock_find_topology):
        from orchestration_service.core.orchestrator_nlb_mgmt import has_interconnect_onramp_ilb_in_region

        dbh = Mock_DBHandler_interconnect(logger())
        mock_find_topology.return_value = (True, ["topology_789"])

        result = has_interconnect_onramp_ilb_in_region(dbh, custid=999, compute_region_idx=555)

        self.assertEqual(result, True)

    @patch('orchestration_service.core.orchestrator_nlb_mgmt.find_topology_entries_by_custid_and_region_id')
    def test_has_interconnect_onramp_ilb_in_region_with_none_topology_list(self, mock_find_topology):
        from orchestration_service.core.orchestrator_nlb_mgmt import has_interconnect_onramp_ilb_in_region

        dbh = Mock_DBHandler_interconnect(logger())
        mock_find_topology.return_value = (True, None)

        with self.assertRaises(TypeError):
            has_interconnect_onramp_ilb_in_region(dbh, custid=115, compute_region_idx=211)


class MockCustNode():
    def __init__(self):
        self.node_id = "test_node_123"
        self.region = "us-west-1"

class TestAllocateInterconnectOnrampIlb(TestCase):
    @pytest.fixture(autouse=True)
    def inject_fixtures(self, caplog):
        self._caplog = caplog

    @patch('orchestration_service.core.orchestrator_nlb_mgmt.has_interconnect_onramp_ilb_in_region', return_value=True)
    def test_allocate_interconnect_onramp_ilb_already_exists(self, mock_has_ilb):
        from orchestration_service.core.orchestrator_nlb_mgmt import allocate_interconnect_onramp_ilb

        dbh = Mock_DBHandler_interconnect(logger(), has_ilb=True)
        custnode = MockCustNode()
        custid = 456
        compute_region_idx = 789

        result = allocate_interconnect_onramp_ilb(dbh, custid, compute_region_idx, custnode)

        assert result == 1

    @patch('orchestration_service.core.orchestrator_nlb_mgmt.create_int_onramp_ilb_instance_by_custid_and_region_id', return_value=1)
    @patch('orchestration_service.core.orchestrator_nlb_mgmt.has_interconnect_onramp_ilb_in_region', return_value=False)
    def test_allocate_interconnect_onramp_ilb_create_success(self, mock_has_ilb, mock_create):
        from orchestration_service.core.orchestrator_nlb_mgmt import allocate_interconnect_onramp_ilb

        dbh = Mock_DBHandler_interconnect(logger(), has_ilb=False)
        custnode = MockCustNode()
        custid = 456
        compute_region_idx = 789

        result = allocate_interconnect_onramp_ilb(dbh, custid, compute_region_idx, custnode)

        assert result == 1

    @patch('orchestration_service.core.orchestrator_nlb_mgmt.create_int_onramp_ilb_instance_by_custid_and_region_id', return_value=-1)
    @patch('orchestration_service.core.orchestrator_nlb_mgmt.has_interconnect_onramp_ilb_in_region', return_value=False)
    def test_allocate_interconnect_onramp_ilb_create_failure(self, mock_has_ilb, mock_create):
        from orchestration_service.core.orchestrator_nlb_mgmt import allocate_interconnect_onramp_ilb

        dbh = Mock_DBHandler_interconnect(logger(), has_ilb=False)
        custnode = MockCustNode()
        custid = 456
        compute_region_idx = 789

        result = allocate_interconnect_onramp_ilb(dbh, custid, compute_region_idx, custnode)

        assert result == -1

    @patch('orchestration_service.core.orchestrator_nlb_mgmt.has_interconnect_onramp_ilb_in_region', side_effect=Exception("Database connection error"))
    def test_allocate_interconnect_onramp_ilb_exception_handling(self, mock_has_ilb):
        from orchestration_service.core.orchestrator_nlb_mgmt import allocate_interconnect_onramp_ilb

        dbh = Mock_DBHandler_interconnect(logger())
        custnode = MockCustNode()
        custid = 456
        compute_region_idx = 789

        result = allocate_interconnect_onramp_ilb(dbh, custid, compute_region_idx, custnode)

        assert result == -1

    @patch('orchestration_service.core.orchestrator_nlb_mgmt.create_int_onramp_ilb_instance_by_custid_and_region_id', return_value=0)
    @patch('orchestration_service.core.orchestrator_nlb_mgmt.has_interconnect_onramp_ilb_in_region', return_value=False)
    def test_allocate_interconnect_onramp_ilb_create_zero_return(self, mock_has_ilb, mock_create):
        from orchestration_service.core.orchestrator_nlb_mgmt import allocate_interconnect_onramp_ilb

        dbh = Mock_DBHandler_interconnect(logger(), has_ilb=False)
        custnode = MockCustNode()
        custid = 456
        compute_region_idx = 789

        result = allocate_interconnect_onramp_ilb(dbh, custid, compute_region_idx, custnode)

        assert result == 1

        assert ret == True
