import sys
import pytest
import json
from unittest.mock import patch, MagicMock
import logging
import mock_imports_orchestrator
import mock_instance_model as mock_inst_model
from libs.common.shared.sys_utils import PROVIDER_GCP
from libs.common.shared.sys_utils import NODE_TYPE_GP_GATEWAY
from orchestration_service.core.orchestrator_ip_mgmt import delete_gp_gateway_edge_location_references_impl
import json
from mock_models import *
from unittest import TestCase
from unittest.mock import patch, MagicMock
from orchestration_service.core.orchestrator_ip_mgmt import allocate_new_egress_ip_gateways
from libs.model.instancemodel import InstanceModel
import libs.model.instancemodel as instancemodel
from libs.common.shared.sys_utils import NODE_TYPE_GP_GATEWAY, NODE_TYPE_NLB_INSTANCE
from orchestration_service.core.orchestrator_ip_mgmt import delete_gp_gateway_edge_location_references_impl, unbind_egress_ips_from_node_impl, \
        allocate_new_egress_ip_standalone_gateways, allocate_new_egress_ip_nat_gateways, allocate_new_egress_ip_for_nlb_instances, release_cnat_public_ips, update_egress_ips_gateways_behind_nlb_nat
import utils.Exceptions as customExceptions

class logger():
    def info(self, *args):
        pass

    def error(self, *args):
        pass

    def warn(self, *args):
        pass

class Mock_conn():
    def __init__(self):
        pass

    def close(self):
        pass

class Mock_cursor():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params:
            query = self.sql%self.params
        else:
            query = self.sql
        pass

    def fetchone(self):
        pass

class Mock_DBHandler():
    def __init__(self):
        self.logger = logging.getLogger('testlogger')
        self.conn = Mock_conn()

    def conn(self):
        return self.conn

    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return

class TestOrchIpMgmt():
    @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value = (True,[]))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', return_value = False)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.IPManagementModel', wraps=MockIPManagementModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', wraps=MockInstanceModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.CM.CustomerModel', wraps=MockCustomerModel)
    def test_allocate_new_egress_ip_gateways_no_nat_or_nlb_instance(self, mock_cust, mock_inst, mock_ip, mock_reduction_enabled, mock_find_instances):
        dbh = DB()
        inst = mock_inst()
        edge_region_idx = 555
        node_type = 49

        ret = allocate_new_egress_ip_gateways(dbh, inst, edge_region_idx, node_type)

        assert ret == True

    @patch('orchestration_service.core.orchestrator_ip_mgmt.ipv4_mgmt.GCPIPHandler', wraps=MockGCPIPHandler)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value = (True,[]))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', return_value = True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.IPManagementModel', wraps=MockIPManagementModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', wraps=MockInstanceModelNoNat)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.CM.CustomerModel', wraps=MockCustomerModel)
    def test_allocate_new_egress_ip_gateways_no_nat(self, mock_cust, mock_inst, mock_ip, mock_reduction_enabled, mock_find_instances, mock_gcp):
        dbh = DB()
        inst = mock_inst()
        edge_region_idx = 214
        node_type = 49

        ret = allocate_new_egress_ip_gateways(dbh, inst, edge_region_idx, node_type)

        assert ret == True

    @patch('orchestration_service.core.orchestrator_ip_mgmt.allocate_new_egress_ip_nat_gateways', return_value=True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.update_instances_behind_nlb', return_value={'ok': True})
    @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value = (True,[2222]))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', return_value = False)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.IPManagementModel', wraps=MockIPManagementModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', wraps=MockInstanceModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.CM.CustomerModel', wraps=MockCustomerModel)
    def test_allocate_new_egress_ip_gateways(self, mock_cust, mock_inst, mock_ip, mock_reduction_enabled, mock_find_instances, mock_update, mock_alloc, caplog):
        caplog.set_level(logging.INFO)
        dbh = DB()
        inst = mock_inst(123, dbh)
        edge_region_idx = 555
        node_type = 49

        ret = allocate_new_egress_ip_gateways(dbh, inst, edge_region_idx, node_type)

        assert ret == True


    @patch('orchestration_service.core.orchestrator_ip_mgmt.allocate_new_egress_ip_nat_gateways', return_value=True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.update_instances_behind_nlb', return_value={'ok': True})
    @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value = (True,[2222]))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', side_effect = customExceptions.DbReadException("test exception"))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.IPManagementModel', wraps=MockIPManagementModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', wraps=MockInstanceModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.CM.CustomerModel', wraps=MockCustomerModel)
    def test_allocate_new_egress_ip_gateways_exception(self, mock_cust, mock_inst, mock_ip, mock_reduction_enabled, mock_find_instances, mock_update, mock_alloc, caplog):
        caplog.set_level(logging.INFO)
        dbh = Mock_DBHandler()
        inst = mock_inst(123, dbh)
        edge_region_idx = 555
        node_type = 49

        ret = allocate_new_egress_ip_gateways(dbh, inst, edge_region_idx, node_type)

        assert ret == False
        assert "Failed to check for ingress IP reduction with exception:" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.allocate_new_egress_ip_nat_gateways', return_value=True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.update_instances_behind_nlb', return_value={'ok': False})
    @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value = (True,[2222]))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', return_value = False)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.IPManagementModel', wraps=MockIPManagementModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', wraps=MockInstanceModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.CM.CustomerModel', wraps=MockCustomerModel)
    def test_allocate_new_egress_ip_gateways_update_nlb_fail(self, mock_cust, mock_inst, mock_ip, mock_reduction_enabled, mock_find_instances, mock_update, mock_alloc):
        mockLogger.info = MagicMock()
        dbh = DB()
        inst = mock_inst()
        edge_region_idx = 555
        node_type = 49

        ret = allocate_new_egress_ip_gateways(dbh, inst, edge_region_idx, node_type)

        assert ret == True
        assert mockLogger.info.called_with("Failed to update all GW's behind NLB [dummy]" )


    @patch('orchestration_service.core.orchestrator_ip_mgmt.allocate_new_egress_ip_nat_gateways', return_value=True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.update_instances_behind_nlb', return_value={'ok': True})
    @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value = (True,[2222]))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', return_value = False)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.IPManagementModel', wraps=MockIPManagementModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', wraps=MockInstanceModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.CM.CustomerModel', wraps=MockCustomerModel)
    def test_allocate_new_egress_ip_gateways_bind_failed(self, mock_cust, mock_inst, mock_ip, mock_reduction_enabled, mock_find_instances, mock_update, mock_alloc):
        dbh = DB()
        mockLogger.info = MagicMock()
        MockIPManagementModel.bind_non_allow_listed_ips_to_instance.return_value = {'ok': False}
        inst = mock_inst()
        edge_region_idx = 555
        node_type = 49

        ret = allocate_new_egress_ip_gateways(dbh, inst, edge_region_idx, node_type)

        assert ret == True
        assert mockLogger.info.called_with("Failed to bind non allow listed IP's to instance: dummy")

    @patch('orchestration_service.core.orchestrator_ip_mgmt.allocate_new_egress_ip_nat_gateways', return_value=True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.update_instances_behind_nlb', return_value={'ok': True})
    @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value = (True,[2222]))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', return_value = False)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.IPManagementModel', wraps=MockIPManagementModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', wraps=MockInstanceModelFailUpdate)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.CM.CustomerModel', wraps=MockCustomerModel)
    def test_allocate_new_egress_ip_gateways_update_interface_fail(self, mock_cust, mock_inst, mock_ip, mock_reduction_enabled, mock_find_instances, mock_update, mock_alloc):
        dbh = DB()
        mockLogger.error = MagicMock()
        inst = mock_inst()
        edge_region_idx = 555
        node_type = 49

        ret = allocate_new_egress_ip_gateways(dbh, inst, edge_region_idx, node_type)
        err_msg = ("Failed to update interface ip list with "
            "egress ip for %s" % edge_region_idx)

        assert ret == False
        assert mockLogger.error.called_with("Error: %s" % err_msg)

    @patch('orchestration_service.core.orchestrator_ip_mgmt.allocate_new_egress_ip_nat_gateways', return_value=True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.update_instances_behind_nlb', return_value={'ok': True})
    @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value = (False,[]))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', return_value = False)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.IPManagementModel', wraps=MockIPManagementModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', wraps=MockInstanceModelFailUpdate)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.CM.CustomerModel', wraps=MockCustomerModel)
    def test_allocate_new_egress_ip_gateways_find_instance_fail(self, mock_cust, mock_inst, mock_ip, mock_reduction_enabled, mock_find_instances, mock_update, mock_alloc):
        dbh = DB()
        mockLogger.error = MagicMock()
        inst = mock_inst()
        edge_region_idx = 555
        node_type = 49

        ret = allocate_new_egress_ip_gateways(dbh, inst, edge_region_idx, node_type)
        err_msg = ("Failed to get the NLB for custid %s and region %s"
            % (str(inst.get_param("custid")),
                str(inst.get_param("compute_region_idx"))))

        assert ret == False
        assert mockLogger.error.called_with(err_msg)

    @patch('orchestration_service.core.orchestrator_ip_mgmt.allocate_new_egress_ip_nat_gateways', return_value=False)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.update_instances_behind_nlb', return_value={'ok': True})
    @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value = (True,[2222]))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', return_value = False)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.IPManagementModel', wraps=MockIPManagementModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', wraps=MockInstanceModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.CM.CustomerModel', wraps=MockCustomerModel)
    def test_allocate_new_egress_ip_gateways_alloc_ip_nat_gw_fail(self, mock_cust, mock_inst, mock_ip, mock_reduction_enabled, mock_find_instances, mock_update, mock_alloc):
        dbh = DB()
        mockLogger.error = MagicMock()
        inst = mock_inst()
        edge_region_idx = 555
        node_type = 49

        err_msg = "Failed to allocate egress IP for NAT gateway for edge_region_idx 555"
        ret = allocate_new_egress_ip_gateways(dbh, inst, edge_region_idx, node_type)

        assert ret == False
        assert mockLogger.error.called_with(err_msg)

    @patch('orchestration_service.core.orchestrator_ip_mgmt.update_instances_behind_nlb', return_value=True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value=(True, [1234]))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.IPManagementModel', wraps=MockIPManagementModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', wraps=MockInstanceModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.CM.CustomerModel', wraps=MockCustomerModel)
    def test_allocate_new_egress_ip_for_nlb_instances_success(self, mock_cust, mock_inst, mock_ip, mock_find_instances, mock_update, caplog):
        dbh = DB()
        dbh.logger = logging.getLogger()
        inst = mock_inst()

        perform_ingress_ip_reduction = False
        is_clean_ip_tag = False
        edge_region_idx = 789

        ret = allocate_new_egress_ip_for_nlb_instances(dbh, inst, perform_ingress_ip_reduction, is_clean_ip_tag, edge_region_idx)

        assert ret == True
        mock_update.assert_called_once_with(dbh, 456, 211)

    @patch('orchestration_service.core.orchestrator_ip_mgmt.update_instances_behind_nlb', return_value=True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value=(True, [1234]))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.IPManagementModel', wraps=MockIPManagementModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', wraps=MockInstanceModel)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.CM.CustomerModel', wraps=MockCustomerModel)
    def test_allocate_new_egress_ip_for_nlb_instances_success_iir(self, mock_cust, mock_inst, mock_ip, mock_find_instances, mock_update, caplog):
        dbh = DB()
        dbh.logger = logging.getLogger()
        inst = mock_inst()

        caplog.set_level(logging.INFO)
        perform_ingress_ip_reduction = True
        is_clean_ip_tag = False
        edge_region_idx = 789

        ret = allocate_new_egress_ip_for_nlb_instances(dbh, inst, perform_ingress_ip_reduction, is_clean_ip_tag, edge_region_idx)

        assert ret == True
        assert "No egress IPs are allocated" in caplog.text
        
class TestOrchestratorIPMgmt:
    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest1(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    def test_delete_gp_gateway_edge_location_references_impl(self, patched_is_ingress_ip_reduction_enabled, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '277', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {'277': '**************'}" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest4(id=115, dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest4(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    def test_delete_gp_gateway_edge_location_references_impl_ipv6(self, patched_is_ingress_ip_reduction_enabled, patched_InstancemodelV6, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '277', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "interface_ipv6_list for instance 115 is {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {'277': '**************'}" in caplog.text
        assert "Removing region 277 from egress ipv6 list {'277': '2ff0:1600:ab::2'}" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest4(id=115, dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest4(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.json.dumps')
    def test_delete_gp_gateway_edge_location_references_impl_ipv6_json_dumps_exception_type(self, mock_json_dumps, patched_is_ingress_ip_reduction_enabled, patched_InstancemodelV6, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        mock_json_dumps.side_effect = ['{"277": "**************"}', '{"277": "2ff0:1600:ab::2"}', '{"277": "**************"}', '{"277": "2ff0:1600:ab::2"}', {"277": "**************"}, TypeError("test")]
        delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '277', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "interface_ipv6_list for instance 115 is {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {'277': '**************'}" in caplog.text
        assert "Removing region 277 from egress ipv6 list {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "to JSON string due to type exception:" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest4(id=115, dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest4(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.json.dumps')
    def test_delete_gp_gateway_edge_location_references_impl_ipv6_json_dumps_exception_value(self, mock_json_dumps, patched_is_ingress_ip_reduction_enabled, patched_InstancemodelV6, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        mock_json_dumps.side_effect = ['{"277": "**************"}', '{"277": "2ff0:1600:ab::2"}', '{"277": "**************"}', '{"277": "2ff0:1600:ab::2"}', {"277": "**************"}, ValueError("test")]
        delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '277', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "interface_ipv6_list for instance 115 is {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {'277': '**************'}" in caplog.text
        assert "Removing region 277 from egress ipv6 list {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "to JSON string due to value exception:" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest4(id=115, dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest4(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.json.dumps')
    def test_delete_gp_gateway_edge_location_references_impl_ipv6_json_dumps_exception_overflow(self, mock_json_dumps, patched_is_ingress_ip_reduction_enabled, patched_InstancemodelV6, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        mock_json_dumps.side_effect = ['{"277": "**************"}', '{"277": "2ff0:1600:ab::2"}', '{"277": "**************"}', '{"277": "2ff0:1600:ab::2"}', {"277": "**************"}, OverflowError("test")]
        delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '277', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "interface_ipv6_list for instance 115 is {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {'277': '**************'}" in caplog.text
        assert "Removing region 277 from egress ipv6 list {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "to JSON string due to overflow exception:" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest5(id=115, dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest5(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    def test_delete_gp_gateway_edge_location_references_impl_ipv6_assigned_to_nat(self, patched_is_ingress_ip_reduction_enabled, patched_InstancemodelV6, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '277', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "interface_ipv6_list for instance 115 is {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {'277': '**************'}" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest5(id=115, dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest5(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=True)
    def test_delete_gp_gateway_edge_location_references_impl_ipv6_assigned_to_nat_ingress_ip_reduction(self, patched_is_ingress_ip_reduction_enabled, patched_InstancemodelV6, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '277', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "interface_ipv6_list for instance 115 is {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {}" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest6(id=115, dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest6(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    def test_delete_gp_gateway_edge_location_references_impl_ipv6_assigned_to_nat_no_entry(self, patched_is_ingress_ip_reduction_enabled, patched_InstancemodelV6, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '290', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "interface_ipv6_list for instance 115 is {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "No entry for edge location [290] in interface_ip_list" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest7(id=115, dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest4(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    def test_delete_gp_gateway_edge_location_references_impl_exception_interface_ipv6_list_invalid(self, patched_is_ingress_ip_reduction_enabled, patched_InstancemodelV6, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '277', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "interface_ipv6_list for instance 115 is {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {'277': '**************'}" in caplog.text
        assert "Removing region 277 from egress ipv6 list {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "Failed to update interface_ipv6_list since interface_ipv6_list is invalid" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest8(id=115, dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest4(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    def test_delete_gp_gateway_edge_location_references_impl_exception_instance_not_found(self, patched_is_ingress_ip_reduction_enabled, patched_InstancemodelV6, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '277', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "interface_ipv6_list for instance 115 is {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {'277': '**************'}" in caplog.text
        assert "Removing region 277 from egress ipv6 list {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "Failed to update interface_ipv6_list since instance is not found" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest9(id=115, dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest4(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    def test_delete_gp_gateway_edge_location_references_impl_exception_interface_ipv6_list_invalid_json(self, patched_is_ingress_ip_reduction_enabled, patched_InstancemodelV6, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '277', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "interface_ipv6_list for instance 115 is {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {'277': '**************'}" in caplog.text
        assert "Removing region 277 from egress ipv6 list {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "Failed to update interface_ipv6_list since interface_ipv6_list cannot be converted to json" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest10(id=115, dbh=Mock_DBHandler()))
    @patch('libs.cloud_providers.common.ip_management.ipv6.gcp.ipv6_mgmt_1.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest4(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    def test_delete_gp_gateway_edge_location_references_impl_exception_interface_ipv6_list_db_update_exception(self, patched_is_ingress_ip_reduction_enabled, patched_InstancemodelV6, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '277', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "interface_ipv6_list for instance 115 is {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {'277': '**************'}" in caplog.text
        assert "Removing region 277 from egress ipv6 list {'277': '2ff0:1600:ab::2'}" in caplog.text
        assert "Failed to update interface_ipv6_list in RDS" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest2(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    def test_delete_gp_gateway_edge_location_references_impl_nat_nlb_instances(self, patched_is_ingress_ip_reduction_enabled, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '277', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "Instance has ingress_ip_reduction disabled; get the edge IP from the interface_ip_list" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {'277': '**************'}" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest2(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    def test_delete_gp_gateway_edge_location_references_impl_interface_ip_list_json_string(self, patched_is_ingress_ip_reduction_enabled, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        ret = delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '277', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "Instance has ingress_ip_reduction disabled; get the edge IP from the interface_ip_list" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {'277': '**************'}" in caplog.text
        assert ret is True

    @patch('orchestration_service.core.orchestrator_ip_mgmt.trigger_update_for_instance_changes', return_value=(True, None))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.get_instance_ids_having_egress_ip_by_edge_loc', return_value=[115])
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest3(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    def test_delete_gp_gateway_edge_location_references_impl_interface_ip_list_dict(self, patched_is_ingress_ip_reduction_enabled, patched_Instancemodel, patched_get_instance_ids_having_egress_ip_by_edge_loc, patched_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)
        ret = delete_gp_gateway_edge_location_references_impl(Mock_DBHandler(), 115, '277', 100, MagicMock())
        assert "interface_ip_list for instance 115 is {'277': '**************'}" in caplog.text
        assert "Instance has ingress_ip_reduction disabled; get the edge IP from the interface_ip_list" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {'277': '**************'}" in caplog.text
        assert ret is True

    # TODO need to fix
    # @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value=(True, [115]))
    # @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest2(id=115, dbh=Mock_DBHandler()))
    # @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    # def test_unbind_egress_ips_from_node_impl_delete_all_true(self, patched_is_ingress_ip_reduction_enabled, patched_Instancemodel, patched_find_instances_by_custid_and_region_id, caplog):
    #     caplog.set_level(logging.INFO)
    #     unbind_egress_ips_from_node_impl(Mock_DBHandler(), 115, 277, None, NODE_TYPE_NLB_INSTANCE, delete_all=True)
    #     assert 'Skip updating interface IP list for instances behind the NLB as we are deleting all instances' in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.update_instances_behind_nlb', return_value=True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value=(True, [115]))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest2(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    def test_unbind_egress_ips_from_node_impl_delete_all_false(self, patched_is_ingress_ip_reduction_enabled, patched_Instancemodel, patched_find_instances_by_custid_and_region_id, patched_update_instances_behind_nlb, caplog):
        caplog.set_level(logging.INFO)
        unbind_egress_ips_from_node_impl(Mock_DBHandler(), 115, 277, None, NODE_TYPE_NLB_INSTANCE, delete_all=False)
        assert "Updated all GW's behind NLB" in caplog.text
        assert 'Skip updating interface IP list for instances behind the NLB as we are deleting all instances' not in caplog.text
        assert "Instance has ingress_ip_reduction is off get the edge IP from the interface_ip_list" in caplog.text
        assert "Trying to cleanup FQDN entries for egress_ip_dict {'277': '**************'}" in caplog.text

    # TODO need to fix
    # @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value=(True, [115]))
    # @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest2(id=115, dbh=Mock_DBHandler()))
    # @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    # def test_unbind_egress_ips_from_node_impl_delete_all_true(self, patched_is_ingress_ip_reduction_enabled, patched_Instancemodel, patched_find_instances_by_custid_and_region_id, caplog):
    #     caplog.set_level(logging.INFO)
    #     unbind_egress_ips_from_node_impl(Mock_DBHandler(), 115, 277, None, NODE_TYPE_NLB_INSTANCE, delete_all=True)
    #     assert 'Skip updating interface IP list for instances behind the NLB as we are deleting all instances' in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.update_instances_behind_nlb', return_value=True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.find_instances_by_custid_and_region_id', return_value=(True, [115]))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', return_value=mock_inst_model.InstanceModelOrchestratorIPMgmtTest2(id=115, dbh=Mock_DBHandler()))
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.is_ingress_ip_reduction_enabled', return_value=False)
    def test_unbind_egress_ips_from_node_impl_delete_all_false(self, patched_is_ingress_ip_reduction_enabled, patched_Instancemodel, patched_find_instances_by_custid_and_region_id, patched_update_instances_behind_nlb, caplog):
        caplog.set_level(logging.INFO)
        unbind_egress_ips_from_node_impl(Mock_DBHandler(), 115, 277, None, NODE_TYPE_NLB_INSTANCE, delete_all=False)
        assert "Updated all GW's behind NLB" in caplog.text
        assert 'Skip updating interface IP list for instances behind the NLB as we are deleting all instances' not in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.get_egress_ip_dict_from_str')
    @patch('orchestration_service.core.orchestrator_ip_mgmt.ipv4_mgmt.GCPIPHandler')
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel')
    def test_allocate_new_egress_ip_standalone_gateways_IIR_enabled(self, mock_InstanceModel, mock_GCPIPHandler, mock_get_egress_ip_dict_from_str, caplog):
        caplog.set_level(logging.INFO)
        edge_region_idx = 555
        mock_InstanceModel.return_value = MockInstanceModelSAGWIIREnabledSATest(dbh=Mock_DBHandler())
        mock_GCPIPHandler.return_value = MockGCPIPHandler(dbh=Mock_DBHandler(), instance_id="1000", acct_id="20000")
        mock_get_egress_ip_dict_from_str.return_value = {}
        ret = allocate_new_egress_ip_standalone_gateways(Mock_DBHandler(), mock_InstanceModel, True, edge_region_idx)
        assert ret == True
        assert "IIR is enabled. Skip Updating IIL for instance GPGW_31090435_us-southeast_reservebankofaustralia-**********" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.get_egress_ip_dict_from_str')
    @patch('orchestration_service.core.orchestrator_ip_mgmt.ipv4_mgmt.GCPIPHandler')
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel')
    def test_allocate_new_egress_ip_standalone_gateways_IIR_disabled(self, mock_InstanceModel, mock_GCPIPHandler, mock_get_egress_ip_dict_from_str, caplog):
        caplog.set_level(logging.INFO)
        edge_region_idx = 555
        mock_InstanceModel.return_value = MockInstanceModelSAGWIIREnabledSATest(dbh=Mock_DBHandler())
        mock_GCPIPHandler.return_value = MockGCPIPHandler(dbh=Mock_DBHandler(), instance_id="1000", acct_id="20000")
        mock_get_egress_ip_dict_from_str.return_value = {}
        ret = allocate_new_egress_ip_standalone_gateways(Mock_DBHandler(), mock_InstanceModel, False, edge_region_idx)
        assert ret == True
        assert '''Updating IIL for instance GPGW_31090435_us-southeast_reservebankofaustralia-********** with EIL {"555":"11111"}''' in caplog.text

    # TODO need to fix
    # @patch('orchestration_service.core.orchestrator_ip_mgmt.IPManagementModel', wraps=MockIPManagementModel)
    # @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel', wraps=MockInstanceModel)
    # @patch('orchestration_service.core.orchestrator_ip_mgmt.CM.CustomerModel', wraps=MockCustomerModel)
    # def test_allocate_new_egress_ip_nat_gateways(self, mock_cust, mock_inst, mock_IPMGMT, caplog):
    #     caplog.set_level(logging.INFO)
    #     dbh = DB()
    #     inst = mock_inst()
    #     edge_region_idx = 555
    #     node_type = 156
    #     ret = allocate_new_egress_ip_nat_gateways(dbh, inst, edge_region_idx, False,
    #                                               num_of_ips_to_reserve=1,
    #                                               num_of_ips_to_bind=1,
    #                                               target_node_type=node_type)
    #     assert ret == True


class TestReleaseCnatPublicIps:
    @pytest.fixture
    def mock_dbh(self):
        return MagicMock()

    @pytest.fixture
    def mock_region_master_model(self, mocker):
        return mocker.patch('libs.model.regionmastermodel.RegionMasterModel')

    @pytest.fixture
    def mock_ip_management_model(self, mocker):
        return mocker.patch('libs.model.ipmanagementmodel.IPManagementModel')

    def test_release_cnat_public_ips_gcp(self, mock_dbh, mock_region_master_model, mock_ip_management_model):
        mock_dbh.logger = MagicMock()
        mock_region_master_model.return_value.get_cloud_type.return_value = PROVIDER_GCP
        mock_region_master_model.return_value.get_param.return_value = PROVIDER_GCP
        mock_ip_management_model.return_value.release_cnat_ip_to_customer_ip_pool.return_value = 5

        result = release_cnat_public_ips(mock_dbh, 1, 'test_customer')

        assert result == 5
        mock_ip_management_model.return_value.release_cnat_ip_to_customer_ip_pool.assert_called_once()


    def test_release_cnat_public_ips_failure(self, mock_dbh, mock_region_master_model, mock_ip_management_model):
        mock_dbh.logger = MagicMock()
        mock_region_master_model.return_value.get_cloud_type.return_value = PROVIDER_GCP
        mock_region_master_model.return_value.get_param.return_value = PROVIDER_GCP
        mock_ip_management_model.return_value.release_cnat_ip_to_customer_ip_pool.return_value = -1

        result = release_cnat_public_ips(mock_dbh, 4, 'test_customer')

        assert result == -1
        mock_dbh.logger.error.assert_called_with('Failed to release_cnat_public_ips')

    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', return_value=True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel')
    @patch('orchestration_service.core.orchestrator_ip_mgmt.allocate_new_egress_ip_gateways_behind_nat', return_value=True)
    def test_update_egress_ips_gateways_behind_nlb_nat_success(self, mock_allocate, mock_instance_model, mock_iir_enabled, caplog):
        caplog.set_level(logging.INFO)
        mock_dbh = Mock_DBHandler()
        mock_inst = MagicMock()
        mock_inst.get_param.side_effect = lambda x: {"custid": 123, "compute_region_idx": 456}[x]
        mock_instance_model.return_value.get_all_gp_gw_instance_for_region_and_custid.return_value = [(789,)]
        mock_instance_model.return_value.get_param.return_value = 789

        result = update_egress_ips_gateways_behind_nlb_nat(mock_dbh, mock_inst, 555)

        assert result == True
        assert "Processing GPGW instance (789,)" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', side_effect=customExceptions.DbReadException("Test exception"))
    def test_update_egress_ips_gateways_behind_nlb_nat_db_read_exception(self, mock_iir_enabled, caplog):
        caplog.set_level(logging.ERROR)
        mock_dbh = Mock_DBHandler()
        mock_inst = MagicMock()

        result = update_egress_ips_gateways_behind_nlb_nat(mock_dbh, mock_inst, 555)

        assert result == False
        assert "Failed to check for ingress IP reduction with exception: ('Test exception',)" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', return_value=False)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel')
    @patch('orchestration_service.core.orchestrator_ip_mgmt.allocate_new_egress_ip_gateways_behind_nat', return_value=False)
    def test_update_egress_ips_gateways_behind_nlb_nat_allocation_failure(self, mock_allocate, mock_instance_model, mock_iir_enabled, caplog):
        caplog.set_level(logging.ERROR)
        mock_dbh = Mock_DBHandler()
        mock_inst = MagicMock()
        mock_inst.get_param.side_effect = lambda x: {"custid": 123, "compute_region_idx": 456}[x]
        mock_instance_model.return_value.get_all_gp_gw_instance_for_region_and_custid.return_value = [(789,)]
        mock_instance_model.return_value.get_param.return_value = 789

        result = update_egress_ips_gateways_behind_nlb_nat(mock_dbh, mock_inst, 555)

        assert result == False
        assert "Failed with Exception ('Failed to allocate egress IP behind NAT gateways',)" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', return_value=True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel')
    def test_update_egress_ips_gateways_behind_nlb_nat_invalid_instance(self, mock_instance_model, mock_iir_enabled, caplog):
        caplog.set_level(logging.INFO)
        mock_dbh = Mock_DBHandler()
        mock_inst = MagicMock()
        mock_inst.get_param.side_effect = lambda x: {"custid": 123, "compute_region_idx": 456}[x]
        mock_instance_model.return_value.get_all_gp_gw_instance_for_region_and_custid.return_value = [(789,)]
        mock_instance_model.return_value.get_param.return_value = None

        result = update_egress_ips_gateways_behind_nlb_nat(mock_dbh, mock_inst, 555)

        assert result == False
        assert "Instance 789 is invalid in instance_master" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', return_value=True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel')
    @patch('orchestration_service.core.orchestrator_ip_mgmt.allocate_new_egress_ip_gateways_behind_nat', side_effect=Exception("Unexpected error"))
    def test_update_egress_ips_gateways_behind_nlb_nat_unexpected_exception(self, mock_allocate, mock_instance_model, mock_iir_enabled, caplog):
        caplog.set_level(logging.ERROR)
        mock_dbh = Mock_DBHandler()
        mock_inst = MagicMock()
        mock_inst.get_param.side_effect = lambda x: {"custid": 123, "compute_region_idx": 456}[x]
        mock_instance_model.return_value.get_all_gp_gw_instance_for_region_and_custid.return_value = [(789,)]
        mock_instance_model.return_value.get_param.return_value = 789

        result = update_egress_ips_gateways_behind_nlb_nat(mock_dbh, mock_inst, 555)

        assert result == False
        assert "Failed with Exception ('Unexpected error',)" in caplog.text

    @patch('orchestration_service.core.orchestrator_ip_mgmt.is_ingress_ip_reduction_enabled', return_value=True)
    @patch('orchestration_service.core.orchestrator_ip_mgmt.INST.InstanceModel')
    @patch('orchestration_service.core.orchestrator_ip_mgmt.allocate_new_egress_ip_gateways_behind_nat', return_value=True)
    def test_update_egress_ips_gateways_behind_nlb_nat_custom_cloud_provider(self, mock_allocate, mock_instance_model, mock_iir_enabled, caplog):
        caplog.set_level(logging.INFO)
        mock_dbh = Mock_DBHandler()
        mock_inst = MagicMock()
        mock_inst.get_param.side_effect = lambda x: {"custid": 123, "compute_region_idx": 456}[x]
        mock_instance_model.return_value.get_all_gp_gw_instance_for_region_and_custid.return_value = [(789,)]
        mock_instance_model.return_value.get_param.return_value = 789

        result = update_egress_ips_gateways_behind_nlb_nat(mock_dbh, mock_inst, 555, cloud_provider='aws')

        assert result == True
        assert "Processing gateways eip_list for edge region 555 in aws cloud in compute 456" in caplog.text
