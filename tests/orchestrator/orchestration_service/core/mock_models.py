import logging
logger = logging.getLogger()

class mockLogger():
    def __init__(self):
        self.logger = logging.getLogger('testlogger')
    def error(self, input=None):
        self.logger.error(input)
        return
    def debug(self, input=None):
        self.logger.debug(input)
        return
    def info(self, input=None):
        self.logger.info(input)
        return
    def get_uuid(self):
        return 1
    def set_sub_uuid(self, sub_uuid):
        return
    def reset_sub_uuid(self):
        return

class DB:
    def __init__(self):
        self.logger = mockLogger()

class MockPinnedInstanceUpgradeModel():
    def __init__(self, dbh, instance_id=None, shard_id=None, replica_size=None):
        self.dbh = dbh
        self.instance_id = 1
        self.shard_id = 1
        self.replica_size = 1
        self.isvalid = True
    def query_all_stage1_entries(self):
        current_instance_id = 123
        new_instance_id = 0
        target_cloud_machine_type = 49
        target_capacity_type = None
        return 1, [[current_instance_id,new_instance_id,target_cloud_machine_type,target_capacity_type]]
    def query_all_stage4_entries(self):
        return 1, [[0]]
    def set_param(self,input1, input2):
        return
    def save(self):
        return
    def save_stage4(self):
        return
    def get_param(self, input):
        if input=="delete_after":
            return 0
        else:
            return 1
class MockPinnedInstanceUpgradeModelNoEntries():
    def __init__(self, dbh, instance_id=None, shard_id=None, replica_size=None):
        self.dbh = dbh
        self.instance_id = 1
        self.shard_id = 1
        self.replica_size = 1
        self.isvalid = True
    def query_all_stage1_entries(self):
        current_instance_id = 123
        new_instance_id = 0
        target_cloud_machine_type = 49
        target_capacity_type = None
        return 0, [[]]
    def query_all_stage4_entries(self):
        return 1, [[0]]
    def set_param(self,input1, input2):
        return
class DB():
    def __init__(self):
        self.logger = mockLogger()
    
    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return

class Mock_cursor():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchone(self):
        return {"name" : "test_instance_model"}

class MockInstanceModel():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0
    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def get_all_gp_gw_instance_for_region_and_custid(self,input1,input2):
        return ['123']
    def update_interface_ip_list_with_egress_ip(self, edge_location_idx):
        return True
    def update_interface_ipv6_list_with_egress_ipv6(self, edge_location_idx):
        return True
    def save(self):
        return
    def save_stage4(self):
        return
    def get_param(self, input):
        if input=="delete_after":
            return 0
        elif input=="id":
            return 123
        elif input=="name":
            return "dummy"
        elif input=="custid":
            return 456
        elif input=="egress_ip_list":
            return """{"555":"11111"}"""
        elif input=="compute_region_idx":
            return 211
        elif input=="is_instance_behind_nlb":
            return 0
        elif input=="node_type":
            return 49
        else:
            return 1
class MockPinnedInstanceUpgradeModelFail():
    def __init__(self, dbh, instance_id=None, shard_id=None, replica_size=None):
        self.dbh = dbh
        self.instance_id = 1
        self.shard_id = 1
        self.replica_size = 1
        self.isvalid = True
    def query_all_stage1_entries(self):
        return 1, [[0]]
    def query_all_stage4_entries(self):
        return 1, [[0]]
    def set_param(self,input1, input2):
        return
    def save(self):
        return
    def save_stage4(self):
        return 0
    def get_param(self, input):
        if input=="delete_after":
            return 0
        else:
            return 1
            
class MockInstanceModelFailUpdate():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0
    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def get_all_gp_gw_instance_for_region_and_custid(self,input1,input2):
        return ['123']
    def update_interface_ip_list_with_egress_ip(self, edge_location_idx):
        return
    def save(self):
        return
    def save_stage4(self):
        return
    def get_param(self, input):
        if input=="id":
            return 123
        elif input=="name":
            return "dummy"
        elif input=="custid":
            return 456
        elif input=="egress_ip_list":
            return """{"555":"11111"}"""
        elif input=="compute_region_idx":
            return 211
        elif input=="is_instance_behind_nlb":
            return 0
        elif input=="node_type":
            return 49
        else:
            return 1

class MockInstanceModelNoNat():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0
    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def get_all_gp_gw_instance_for_region_and_custid(self,input1,input2):
        return [['123']]
    def update_interface_ip_list_with_egress_ip(self, edge_location_idx):
        return True
    def update_interface_ipv6_list_with_egress_ipv6(self, edge_location_idx):
        return True
    def save(self):
        return
    def save_stage4(self):
        return
    def get_param(self, input):
        if input=="id":
            return 123
        elif input=="name":
            return "dummy"
        elif input=="custid":
            return 456
        elif input=="egress_ip_list":
            return """{"555":"11111"}"""
        elif input=="compute_region_idx":
            return 211
        elif input=="is_instance_behind_nlb":
            return 0
        elif input=="has_nat_instance":
            return 0
        elif input=="node_type":
            return 49
        else:
            return 1
class MockInstanceModelFail():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = 0
    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def save(self):
        return
    def save_stage4(self):
        return 0
    def get_param(self, input):
        return 1
    def update_column_upgrade_status(self,dbh,input):
        return 1

class MockCustomerModel():
    def __init__(self, custid, dbh):
        self.id =  custid
        self.acct_id = 4009
        self.name = "testCust"
        self.dbh = dbh
        self.fwdrulesall = ""

    def gcp_is_clean_ip_project(self):
        return False

    def get_auto_scale_options(self, node_type):
        return True, True, True, True

class MockIPManagementModel():
    def __init__(self, dbh):
        self.dbh=dbh
        self.logger = mockLogger()
    def get_egress_ip_dict_from_str(self, logger, input):
        return {"555":"11111"}

    def bind_non_allow_listed_ips_to_instance(self, instance_obj, edge_region_id, num_of_ips, target_node_type):
        return {"ok": True}

    def bind_reserved_ips_to_instance(self, instance_obj, edge_region_id, num_of_ips, target_node_type):
        return {"ok": True}

    def update_egress_ip_list_with_given_ipstr(self, instance_obj,
                                               edge_region_id, ipstr):
        return {'ok': True}

    def reserve_ips_for_cust_region(self, custid, node_type, edge_region_id, compute_region_idx,
                                    total_ip_addresses_to_reserve, target_node_type, status='reserved'):
        return {'ok': True, 'ip_list': '*******'}

    def bind_allow_listed_ips_to_instance(self, inst, edge_region_idx, num_of_ips_to_bind, target_node_type):
        return {'ok': True}

class MockGCPIPHandler():
    def __init__(self, dbh, input1, input2):
        self.dbh=dbh
    def allocate_public_ip_for_customer(self, old_public_ip, edge_region_id=None, include_compute_region=True):
        return True

class MockRegionMasterModel():
    def __init__(self, dbh, edge_location_region_id):
        self.dbh = dbh
        self.edge_location_region_id = edge_location_region_id
        self.valid_tuple = True

    def get_param(self, input):
        if input == "edge_location_region_name":
            return "us-west2"
        elif input == "cloud_provider":
            return "gcp"
        elif input == "primary_theater_id":
            return 0
        return 1

class MockCustNodeModel():
    def __init__(self, dbh):
        self.dbh = dbh
        self.name = "TestCustNode"
        self.custid = 321
        self.region = 214
        self.theater = 0
        self.old_region = 214
        self.is_hq = 1
        self.is_deleted = 0
        self.node_type = 49
    
    def save(self, dhb):
        return 1

class MockCustEpaasModel:
    def __init__(self, dbh, custid, compute_region_id, cloud_provider, node_type):
        self.custid = custid
        self.dbh = dbh
        self.compute_region_id = compute_region_id
        self.cloud_provider = cloud_provider
        self.node_type = node_type
        self.migrate_ep_status = "STARTED"

        self.value = {
            "custid": custid,
            "dbh": dbh,
            "compute_region_id": compute_region_id,
            "cloud_provider": cloud_provider,
            "node_type": node_type,
            "migrate_ep_status": "STARTED"
        }

    def get_entry(self):
        return True

    def set_param(self, key, val):
        self.value[key] = val

    def get_param(self, key):
        return self.value[key]

class MockGCPIPHandler():
    def __init__(self, dbh=None, instance_id=None, acct_id=None, node_id=None):
        self.dbh = dbh

    def allocate_public_ip_for_customer(self, test, edge_region_idx, allocate=False):
        return [["220"]]

    def find_nlb_settings_by_custid_and_region_id(self, custid, compute_region_idx):
        return {'is_nlb_supported': False, 'cust_is_nlb_supported': True, 'ingress_ip_reduction': True}

class MockInstanceModelSAGWIIREnabledSATest():
    def __init__(self, iid=0, dbh=None):
        self.dbh = dbh
        self.iid = iid

    def query_all_stage4_entries(self):
        return True, [[0]]
    def set_param(self,input1, input2):
        return
    def get_all_gp_gw_instance_for_region_and_custid(self,input1,input2):
        return ['123']
    def update_interface_ip_list_with_egress_ip(self, edge_location_idx):
        return True
    def update_interface_ipv6_list_with_egress_ipv6(self, edge_location_idx):
        return True
    def save(self):
        return
    def save_stage4(self):
        return
    def get_param(self, input):
        if input=="delete_after":
            return 0
        elif input=="id":
            return 123
        elif input=="name":
            return "GPGW_31090435_us-southeast_reservebankofaustralia-**********"
        elif input=="custid":
            return 456
        elif input=="egress_ip_list":
            return """{"555":"11111"}"""
        elif input=="compute_region_idx":
            return 211
        elif input=="is_instance_behind_nlb":
            return False
        elif input=="node_type":
            return 49
        elif input == "has_nat_instance":
            return False
        else:
            return 1

    def get_existing_egress_ip_region_list_for_pinned_instance(self, custid, compute_region_idx, node_type):
        return ["222", "203"]
