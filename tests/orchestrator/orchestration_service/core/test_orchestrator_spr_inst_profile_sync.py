import json
import unittest
from unittest import mock

import pytest
import mock_imports_orchestrator
from libs.model.instancemodel import InstanceModel
import libs.model.instancemodel as instancemodel

from unittest.mock import MagicMock, patch, call

from src.apps.orchestrator.orchestration_service.core.orchestrator_spr_inst_profile_sync import (
    ProfileSyncManager, ChangeType, NODE_TYPE_GP_GATEWAY, NODE_TYPE_SASE_PRIV_REGION_LB
)


class TestProfileSyncManager(unittest.TestCase):
    def setUp(self):
        """Set up common test fixtures."""
        self.mock_dbh = MagicMock()
        self.mock_dbh.logger = MagicMock()
        self.mock_dbh.get_cursor = MagicMock()
        self.mock_dbh.cursorclose = MagicMock()

        # Common test parameters
        self.node_type = NODE_TYPE_GP_GATEWAY
        self.service_node_type = NODE_TYPE_GP_GATEWAY
        self.sase_private_region_id = 123
        self.custid = 456

        # Create the manager instance for testing
        self.manager = ProfileSyncManager(
            self.mock_dbh,
            self.node_type,
            self.service_node_type,
            self.sase_private_region_id,
            self.custid
        )

    def test_init_with_valid_node_type(self):
        """Test initialization with valid node types."""
        # GP Gateway
        manager = ProfileSyncManager(
            self.mock_dbh, NODE_TYPE_GP_GATEWAY, self.service_node_type,
            self.sase_private_region_id, self.custid
        )
        self.assertEqual(manager.node_type, NODE_TYPE_GP_GATEWAY)

        # SASE Private Region LB
        manager = ProfileSyncManager(
            self.mock_dbh, NODE_TYPE_SASE_PRIV_REGION_LB, self.service_node_type,
            self.sase_private_region_id, self.custid
        )
        self.assertEqual(manager.node_type, NODE_TYPE_SASE_PRIV_REGION_LB)

    def test_init_with_invalid_node_type(self):
        """Test initialization with invalid node type raises ValueError."""
        with self.assertRaises(ValueError):
            ProfileSyncManager(
                self.mock_dbh, 999, self.service_node_type,
                self.sase_private_region_id, self.custid
            )

    def test_create_empty_result(self):
        """Test the _create_empty_result method returns correctly structured dict."""
        result = self.manager._create_empty_result()

        self.assertEqual(result["success"], False)
        self.assertEqual(result["changes"]["vip_changes"], 0)
        self.assertEqual(result["changes"]["ip_changes"], 0)
        self.assertEqual(result["changes"]["gateway_changes"], 0)
        self.assertEqual(result["changes"]["total_instances_updated"], 0)
        self.assertEqual(result["changes"]["instances_with_changes"], [])
        self.assertEqual(result["changes"]["failed_instances"], [])
        self.assertEqual(result["change_types"], set())
        self.assertIsNone(result["error"])
        self.assertEqual(result["partial_success"], False)
        self.assertEqual(result["node_type"], self.node_type)

    @patch(
        'src.apps.orchestrator.orchestration_service.core.orchestrator_spr_inst_profile_sync.privateRegionInstanceProfileModelOrch')
    def test_get_instance_profile_success(self, mock_profile_model_class):
        """Test _get_instance_profile with successful profile retrieval."""
        # Setup mock profile model
        mock_profile_model = MagicMock()
        mock_profile_model.valid = True
        mock_profile_model.get_param.side_effect = lambda param: {
            "id": 789,
            "instance_details": json.dumps({
                "global": {
                    "public.vip_address": "*************",
                    "private.vip_address": "************"
                },
                "instances": [
                    {
                        "instance_name": "orange",
                        "interfaces": [
                            {"type": "dataplane_outside_network", "ip-address": "************3/24",
                             "default-gateway": "************"}
                        ]
                    }
                ]
            })
        }.get(param)

        mock_profile_model_class.return_value = mock_profile_model

        # Call the method
        profile = self.manager._get_instance_profile()

        # Verify the result
        self.assertIsNotNone(profile)
        self.assertEqual(profile["id"], 789)
        self.assertIn("global", profile["instance_details"])
        self.assertIn("instances", profile["instance_details"])

        # Verify the model was initialized correctly
        mock_profile_model_class.assert_called_once_with(
            self.mock_dbh,
            custid=self.custid,
            host_profile_id=None,
            node_type=self.node_type,
            service_node_type=self.service_node_type,
            sase_private_region_id=self.sase_private_region_id
        )

    @patch(
        'src.apps.orchestrator.orchestration_service.core.orchestrator_spr_inst_profile_sync.privateRegionInstanceProfileModelOrch')
    def test_get_instance_profile_not_valid(self, mock_profile_model_class):
        """Test _get_instance_profile when profile is not valid."""
        # Setup mock profile model that's not valid
        mock_profile_model = MagicMock()
        mock_profile_model.valid = False
        mock_profile_model_class.return_value = mock_profile_model

        # Call the method
        profile = self.manager._get_instance_profile()

        # Verify the result
        self.assertIsNone(profile)
        self.mock_dbh.logger.error.assert_called()

    @patch(
        'src.apps.orchestrator.orchestration_service.core.orchestrator_spr_inst_profile_sync.privateRegionInstanceProfileModelOrch')
    def test_get_instance_profile_exception(self, mock_profile_model_class):
        """Test _get_instance_profile handling exceptions."""
        # Setup mock to raise exception
        mock_profile_model_class.side_effect = Exception("Test exception")

        # Call the method
        profile = self.manager._get_instance_profile()

        # Verify the result
        self.assertIsNone(profile)
        self.mock_dbh.logger.error.assert_called()

    @patch('src.apps.orchestrator.orchestration_service.core.orchestrator_spr_inst_profile_sync.InstanceModel')
    def test_get_instances_success(self, mock_instance_model_class):
        """Test _get_instances with successful instance retrieval."""
        # Setup mock cursor
        mock_cursor = MagicMock()
        mock_cursor.fetchall.return_value = [(1001,), (1002,)]
        self.mock_dbh.get_cursor.return_value = mock_cursor

        # Setup mock instance models
        mock_instance1 = MagicMock()
        mock_instance1.get_param.side_effect = lambda param: {
            "alias": "orange",
            "lb_details": "************:*************",
            "pvt_ip": "************3",
            "salt_profile": json.dumps({
                "NetworkConfig": {
                    "dataplane_outside.ip_address": "************3/24",
                    "dataplane_outside.gateway": "************"
                }
            })
        }.get(param)

        mock_instance2 = MagicMock()
        mock_instance2.get_param.side_effect = lambda param: {
            "alias": "banana",
            "lb_details": "************:*************",
            "pvt_ip": "************2",
            "salt_profile": json.dumps({
                "NetworkConfig": {
                    "dataplane_outside.ip_address": "************2/24",
                    "dataplane_outside.gateway": "************"
                }
            })
        }.get(param)

        # Setup the mock instance model class to return our mock instances
        mock_instance_model_class.side_effect = [mock_instance1, mock_instance2]

        # Call the method
        instances = self.manager._get_instances()

        # Verify the result
        self.assertEqual(len(instances), 2)
        self.assertEqual(instances[0]["id"], 1001)
        self.assertEqual(instances[0]["alias"], "orange")
        self.assertEqual(instances[1]["id"], 1002)
        self.assertEqual(instances[1]["alias"], "banana")

        # Verify the SQL query was executed correctly
        mock_cursor.execute.assert_called_once()
        self.mock_dbh.cursorclose.assert_called_once_with(mock_cursor)

    def test_get_instances_no_results(self):
        """Test _get_instances when no instances are found."""
        # Setup mock cursor with no results
        mock_cursor = MagicMock()
        mock_cursor.fetchall.return_value = []
        self.mock_dbh.get_cursor.return_value = mock_cursor

        # Call the method
        instances = self.manager._get_instances()

        # Verify the result
        self.assertEqual(instances, [])

    def test_get_instances_sql_exception(self):
        """Test _get_instances handling SQL exceptions."""
        # Setup mock cursor to raise exception
        self.mock_dbh.get_cursor.side_effect = Exception("SQL Error")

        # Call the method
        instances = self.manager._get_instances()

        # Verify the result
        self.assertEqual(instances, [])
        self.mock_dbh.logger.error.assert_called()

    @patch('src.apps.orchestrator.orchestration_service.core.orchestrator_spr_inst_profile_sync.InstanceModel')
    def test_sync_instance_vip_change(self, mock_instance_model_class):
        """Test _sync_instance detecting and applying VIP changes."""
        # Setup instance data and config
        instance_id = 1001
        instance_data = {
            'id': instance_id,
            'alias': 'orange',
            'lb_details': '************:*************',  # Old VIPs
            'pvt_ip': '************3',
            'salt_profile': {
                'NetworkConfig': {
                    'dataplane_outside.public.vip_address': '*************',
                    'dataplane_outside.private.vip_address': '************',
                    'dataplane_outside.ip_address': '************3/24',
                    'dataplane_outside.gateway': '************'
                }
            }
        }

        instance_config = {
            'instance_name': 'orange',
            'interfaces': [
                {
                    'type': 'dataplane_outside_network',
                    'ip-address': '************3/24',
                    'default-gateway': '************'
                }
            ]
        }

        # New VIP values
        vip_string = '************:*************'
        public_vip = '*************'
        private_vip = '************'

        # Setup mock instance model
        mock_instance = MagicMock()
        mock_instance_model_class.return_value = mock_instance

        # Call the method
        changes = self.manager._sync_instance(
            instance_id, instance_data, instance_config,
            vip_string, public_vip, private_vip,
            'dataplane_outside', self.node_type
        )

        # Verify the changes
        self.assertTrue(changes["change_detected"])
        self.assertTrue(changes["vip_change"])
        self.assertFalse(changes["ip_change"])
        self.assertFalse(changes["gateway_change"])

        # Verify instance model updates
        mock_instance.set_param.assert_any_call("lb_details", vip_string)
        mock_instance.save.assert_called_once()

    @patch('src.apps.orchestrator.orchestration_service.core.orchestrator_spr_inst_profile_sync.InstanceModel')
    def test_sync_instance_ip_change(self, mock_instance_model_class):
        """Test _sync_instance detecting and applying IP changes."""
        # Setup instance data and config
        instance_id = 1001
        instance_data = {
            'id': instance_id,
            'alias': 'orange',
            'lb_details': '************:*************',
            'pvt_ip': '************3',
            'salt_profile': {
                'NetworkConfig': {
                    'dataplane_outside.public.vip_address': '*************',
                    'dataplane_outside.private.vip_address': '************',
                    'dataplane_outside.ip_address': '************3/24',  # Old IP
                    'dataplane_outside.gateway': '************'
                }
            }
        }

        # Config with new IP address
        instance_config = {
            'instance_name': 'orange',
            'interfaces': [
                {
                    'type': 'dataplane_outside_network',
                    'ip-address': '************5/24',  # New IP
                    'default-gateway': '************'
                }
            ]
        }

        vip_string = '************:*************'
        public_vip = '*************'
        private_vip = '************'

        # Setup mock instance model
        mock_instance = MagicMock()
        mock_instance_model_class.return_value = mock_instance

        # Call the method
        changes = self.manager._sync_instance(
            instance_id, instance_data, instance_config,
            vip_string, public_vip, private_vip,
            'dataplane_outside', self.node_type
        )

        # Verify the changes
        self.assertTrue(changes["change_detected"])
        self.assertFalse(changes["vip_change"])
        self.assertTrue(changes["ip_change"])
        self.assertFalse(changes["gateway_change"])

        # Verify instance model updates
        mock_instance.set_param.assert_any_call("pvt_ip", "************5")
        mock_instance.save.assert_called_once()

    @patch('src.apps.orchestrator.orchestration_service.core.orchestrator_spr_inst_profile_sync.InstanceModel')
    def test_sync_instance_gateway_change(self, mock_instance_model_class):
        """Test _sync_instance detecting and applying gateway changes."""
        # Setup instance data and config
        instance_id = 1001
        instance_data = {
            'id': instance_id,
            'alias': 'orange',
            'lb_details': '************:*************',
            'pvt_ip': '************3',
            'salt_profile': {
                'NetworkConfig': {
                    'dataplane_outside.public.vip_address': '*************',
                    'dataplane_outside.private.vip_address': '************',
                    'dataplane_outside.ip_address': '************3/24',
                    'dataplane_outside.gateway': '************'  # Old gateway
                }
            }
        }

        # Config with new gateway
        instance_config = {
            'instance_name': 'orange',
            'interfaces': [
                {
                    'type': 'dataplane_outside_network',
                    'ip-address': '************3/24',
                    'default-gateway': '************'  # New gateway
                }
            ]
        }

        vip_string = '************:*************'
        public_vip = '*************'
        private_vip = '************'

        # Setup mock instance model
        mock_instance = MagicMock()
        mock_instance_model_class.return_value = mock_instance

        # Call the method
        changes = self.manager._sync_instance(
            instance_id, instance_data, instance_config,
            vip_string, public_vip, private_vip,
            'dataplane_outside', self.node_type
        )

        # Verify the changes
        self.assertTrue(changes["change_detected"])
        self.assertFalse(changes["vip_change"])
        self.assertFalse(changes["ip_change"])
        self.assertTrue(changes["gateway_change"])

        # Verify salt_profile was updated with new gateway
        expected_call = call("salt_profile", json.dumps({
            'NetworkConfig': {
                'dataplane_outside.public.vip_address': '*************',
                'dataplane_outside.private.vip_address': '************',
                'dataplane_outside.ip_address': '************3/24',
                'dataplane_outside.gateway': '************'  # Updated gateway
            }
        }))
        mock_instance.set_param.assert_has_calls([expected_call], any_order=True)
        mock_instance.save.assert_called_once()

    @patch('src.apps.orchestrator.orchestration_service.core.orchestrator_spr_inst_profile_sync.InstanceModel')
    def test_sync_instance_no_change(self, mock_instance_model_class):
        """Test _sync_instance when no changes are needed."""
        # Setup instance data and config with matching values
        instance_id = 1001
        instance_data = {
            'id': instance_id,
            'alias': 'orange',
            'lb_details': '************:*************',
            'pvt_ip': '************3',
            'salt_profile': {
                'NetworkConfig': {
                    'dataplane_outside.public.vip_address': '*************',
                    'dataplane_outside.private.vip_address': '************',
                    'dataplane_outside.ip_address': '************3/24',
                    'dataplane_outside.gateway': '************'
                }
            }
        }

        instance_config = {
            'instance_name': 'orange',
            'interfaces': [
                {
                    'type': 'dataplane_outside_network',
                    'ip-address': '************3/24',
                    'default-gateway': '************'
                }
            ]
        }

        vip_string = '************:*************'
        public_vip = '*************'
        private_vip = '************'

        # Setup mock instance model
        mock_instance = MagicMock()
        mock_instance_model_class.return_value = mock_instance

        # Call the method
        changes = self.manager._sync_instance(
            instance_id, instance_data, instance_config,
            vip_string, public_vip, private_vip,
            'dataplane_outside', self.node_type
        )

        # Verify no changes were detected
        self.assertFalse(changes["change_detected"])
        self.assertFalse(changes["vip_change"])
        self.assertFalse(changes["ip_change"])
        self.assertFalse(changes["gateway_change"])

        # Verify instance model was not updated
        mock_instance.set_param.assert_not_called()
        mock_instance.save.assert_not_called()

    def test_sync_profile_to_instances_success(self):
        """Test sync_profile_to_instances with successful synchronization."""
        # Mock the required methods
        self.manager._get_instance_profile = MagicMock(return_value={
            'id': 789,
            'instance_details': {
                'global': {
                    'public.vip_address': '*************',
                    'private.vip_address': '************'
                },
                'instances': [
                    {
                        'instance_name': 'orange',
                        'interfaces': [
                            {
                                'type': 'dataplane_outside_network',
                                'ip-address': '************3/24',
                                'default-gateway': '************'
                            }
                        ]
                    },
                    {
                        'instance_name': 'banana',
                        'interfaces': [
                            {
                                'type': 'dataplane_outside_network',
                                'ip-address': '************2/24',
                                'default-gateway': '************'
                            }
                        ]
                    }
                ]
            }
        })

        self.manager._get_instances = MagicMock(return_value=[
            {
                'id': 1001,
                'alias': 'orange',
                'lb_details': '************:*************',  # VIP needs update
                'pvt_ip': '************3',
                'salt_profile': {
                    'NetworkConfig': {
                        'dataplane_outside.public.vip_address': '*************',
                        'dataplane_outside.private.vip_address': '************',
                        'dataplane_outside.ip_address': '************3/24',
                        'dataplane_outside.gateway': '************'
                    }
                }
            },
            {
                'id': 1002,
                'alias': 'banana',
                'lb_details': '************:*************',  # VIP already updated
                'pvt_ip': '************2',
                'salt_profile': {
                    'NetworkConfig': {
                        'dataplane_outside.public.vip_address': '*************',
                        'dataplane_outside.private.vip_address': '************',
                        'dataplane_outside.ip_address': '************4/24',  # IP needs update
                        'dataplane_outside.gateway': '************'
                    }
                }
            }
        ])

        self.manager._process_sync = MagicMock(return_value=(
            {
                'vip_changes': 1,
                'ip_changes': 1,
                'gateway_changes': 0,
                'total_instances_updated': 2,
                'instances_with_changes': [
                    {'id': 1001, 'alias': 'orange', 'changes': {'vip_change': True}},
                    {'id': 1002, 'alias': 'banana', 'changes': {'ip_change': True}}
                ],
                'failed_instances': []
            },
            {ChangeType.VIP_CHANGE.value, ChangeType.IP_CHANGE.value}
        ))

        # Call the method
        result = self.manager.sync_profile_to_instances()

        # Verify the result
        self.assertTrue(result['success'])
        self.assertFalse(result['partial_success'])
        self.assertIsNone(result['error'])
        self.assertEqual(result['changes']['vip_changes'], 1)
        self.assertEqual(result['changes']['ip_changes'], 1)
        self.assertEqual(result['changes']['gateway_changes'], 0)
        self.assertEqual(result['changes']['total_instances_updated'], 2)

        # Verify method calls
        self.manager._get_instance_profile.assert_called_once()
        self.manager._get_instances.assert_called_once()
        self.manager._process_sync.assert_called_once()


    def test_sync_profile_to_instances_no_instances(self):
        """Test sync_profile_to_instances when no instances are found."""
        # Mock the required methods
        self.manager._get_instance_profile = MagicMock(return_value={
            'id': 789,
            'instance_details': {
                'global': {
                    'public.vip_address': '*************',
                    'private.vip_address': '************'
                },
                'instances': []
            }
        })

        self.manager._get_instances = MagicMock(return_value=[])

        # Call the method
        result = self.manager.sync_profile_to_instances()

        # Verify the result
        self.assertFalse(result['success'])
        self.assertFalse(result['partial_success'])
        self.assertTrue('No instances found' in result['error'])

        # Verify method calls
        self.manager._get_instance_profile.assert_called_once()
        self.manager._get_instances.assert_called_once()

    def test_sync_profile_to_instances_partial_success(self):
        """Test sync_profile_to_instances with partial success (some instances failed)."""
        # Mock the required methods
        self.manager._get_instance_profile = MagicMock(return_value={
            'id': 789,
            'instance_details': {
                'global': {
                    'public.vip_address': '*************',
                    'private.vip_address': '************'
                },
                'instances': [
                    {'instance_name': 'orange'},
                    {'instance_name': 'banana'},
                    {'instance_name': 'apple'}
                ]
            }
        })

        self.manager._get_instances = MagicMock(return_value=[
            {'id': 1001, 'alias': 'orange'},
            {'id': 1002, 'alias': 'banana'},
            {'id': 1003, 'alias': 'apple'}
        ])

        # Return changes with some failures
        self.manager._process_sync = MagicMock(return_value=(
            {
                'vip_changes': 2,
                'ip_changes': 0,
                'gateway_changes': 0,
                'total_instances_updated': 2,
                'instances_with_changes': [
                    {'id': 1001, 'alias': 'orange', 'changes': {'vip_change': True}},
                    {'id': 1002, 'alias': 'banana', 'changes': {'vip_change': True}}
                ],
                'failed_instances': [
                    {'id': 1003, 'alias': 'apple', 'error': 'Test error'}
                ]
            },
            {ChangeType.VIP_CHANGE.value}
        ))

        # Call the method
        result = self.manager.sync_profile_to_instances()

        # Verify the result
        self.assertTrue(result['success'])  # Still success if some instances updated
        self.assertTrue(result['partial_success'])
        self.assertEqual(result['changes']['vip_changes'], 2)
        self.assertEqual(result['changes']['total_instances_updated'], 2)
        self.assertEqual(len(result['changes']['failed_instances']), 1)
        self.assertTrue('instances failed' in result['error'])

        # Verify method calls
        self.manager._get_instance_profile.assert_called_once()
        self.manager._get_instances.assert_called_once()
        self.manager._process_sync.assert_called_once()

    def test_sync_profile_to_instances_all_instances_failed(self):
        """Test sync_profile_to_instances when all instances fail to update."""
        # Mock the required methods
        self.manager._get_instance_profile = MagicMock(return_value={
            'id': 789,
            'instance_details': {
                'global': {
                    'public.vip_address': '*************',
                    'private.vip_address': '************'
                },
                'instances': [
                    {'instance_name': 'orange'},
                    {'instance_name': 'banana'}
                ]
            }
        })

        self.manager._get_instances = MagicMock(return_value=[
            {'id': 1001, 'alias': 'orange'},
            {'id': 1002, 'alias': 'banana'}
        ])

        # Return changes with all failures
        self.manager._process_sync = MagicMock(return_value=(
            {
                'vip_changes': 0,
                'ip_changes': 0,
                'gateway_changes': 0,
                'total_instances_updated': 0,
                'instances_with_changes': [],
                'failed_instances': [
                    {'id': 1001, 'alias': 'orange', 'error': 'Test error 1'},
                    {'id': 1002, 'alias': 'banana', 'error': 'Test error 2'}
                ]
            },
            set()
        ))

        # Call the method
        result = self.manager.sync_profile_to_instances()

        # Verify the result
        self.assertFalse(result['success'])
        self.assertFalse(result['partial_success'])
        self.assertEqual(result['changes']['total_instances_updated'], 0)
        self.assertEqual(len(result['changes']['failed_instances']), 2)
        self.assertTrue('All 2 instances failed' in result['error'])

        # Verify method calls
        self.manager._get_instance_profile.assert_called_once()
        self.manager._get_instances.assert_called_once()
        self.manager._process_sync.assert_called_once()

    def test_sync_profile_to_instances_global_error(self):
        """Test sync_profile_to_instances with a global error during sync process."""
        # Mock the required methods
        self.manager._get_instance_profile = MagicMock(return_value={
            'id': 789,
            'instance_details': {
                'global': {
                    'public.vip_address': '*************',
                    'private.vip_address': '************'
                },
                'instances': [
                    {'instance_name': 'orange'},
                    {'instance_name': 'banana'}
                ]
            }
        })

        self.manager._get_instances = MagicMock(return_value=[
            {'id': 1001, 'alias': 'orange'},
            {'id': 1002, 'alias': 'banana'}
        ])

        # Return changes with global error but some updates
        self.manager._process_sync = MagicMock(return_value=(
            {
                'vip_changes': 1,
                'ip_changes': 0,
                'gateway_changes': 0,
                'total_instances_updated': 1,
                'instances_with_changes': [
                    {'id': 1001, 'alias': 'orange', 'changes': {'vip_change': True}}
                ],
                'failed_instances': [
                    {'id': 1002, 'alias': 'banana', 'error': 'Test error'}
                ],
                'global_error': 'Critical process error'
            },
            {ChangeType.VIP_CHANGE.value}
        ))

        # Call the method
        result = self.manager.sync_profile_to_instances()

        # Verify the result
        self.assertFalse(result['success'])
        self.assertTrue(result['partial_success'])
        self.assertEqual(result['error'], 'Critical process error')
        self.assertEqual(result['changes']['total_instances_updated'], 1)

        # Verify method calls
        self.manager._get_instance_profile.assert_called_once()
        self.manager._get_instances.assert_called_once()
        self.manager._process_sync.assert_called_once()

    def test_sync_profile_to_instances_exception(self):
        """Test sync_profile_to_instances handling unexpected exceptions."""
        # Mock to raise an exception
        self.manager._get_instance_profile = MagicMock(side_effect=Exception("Unexpected test error"))

        # Call the method
        result = self.manager.sync_profile_to_instances()

        # Verify the result
        self.assertFalse(result['success'])
        self.assertFalse(result['partial_success'])
        self.assertTrue('Error in sync_profile_to_instances' in result['error'])

        # Verify logger was called with error
        self.mock_dbh.logger.error.assert_called()



class TestProcessSync(unittest.TestCase):
    def setUp(self):
        """Set up common test fixtures."""
        self.mock_dbh = MagicMock()
        self.mock_dbh.logger = MagicMock()
        self.mock_dbh.get_cursor = MagicMock()
        self.mock_dbh.cursorclose = MagicMock()

        # Common test parameters
        self.node_type = NODE_TYPE_GP_GATEWAY
        self.service_node_type = NODE_TYPE_GP_GATEWAY
        self.sase_private_region_id = 123
        self.custid = 456

        # Create the manager instance for testing
        self.manager = ProfileSyncManager(
            self.mock_dbh,
            self.node_type,
            self.service_node_type,
            self.sase_private_region_id,
            self.custid
        )

        # Add logger property for testing
        self.manager.logger = self.mock_dbh.logger

        # Patch the SNS function
        self.sns_patch = patch('src.apps.orchestrator.orchestration_service.core.orchestrator_spr_inst_profile_sync.send_sns_msg_for_spr_update')
        self.mock_sns = self.sns_patch.start()

    def tearDown(self):
        self.sns_patch.stop()

    def test_process_sync_empty_inputs(self):
        """Test _process_sync with empty profile and instances."""
        profile = {}
        instances = []

        changes, change_types = self.manager._process_sync(profile, instances, self.node_type)

        # Verify the result
        self.assertEqual(changes["vip_changes"], 0)
        self.assertEqual(changes["ip_changes"], 0)
        self.assertEqual(changes["gateway_changes"], 0)
        self.assertEqual(changes["total_instances_updated"], 0)
        self.assertEqual(changes["instances_with_changes"], [])
        self.assertEqual(changes["failed_instances"], [])
        self.assertEqual(change_types, set())

    def test_process_sync_missing_global_config(self):
        """Test _process_sync with missing global configuration."""
        profile = {
            'id': 789,
            'instance_details': {
                # No global config
                'instances': [
                    {'instance_name': 'test-instance'}
                ]
            }
        }
        instances = [
            {'id': 1001, 'alias': 'test-instance'}
        ]

        self.manager._sync_instance = MagicMock(return_value={
            "change_detected": False,
            "vip_change": False,
            "ip_change": False,
            "gateway_change": False
        })

        changes, change_types = self.manager._process_sync(profile, instances, self.node_type)

        # Verify the result
        self.assertEqual(changes["total_instances_updated"], 0)
        self.assertEqual(changes["failed_instances"], [])
        self.assertEqual(change_types, set())

        # Verify _sync_instance was called with None values for VIPs
        self.manager._sync_instance.assert_called_once_with(
            1001, {'id': 1001, 'alias': 'test-instance'},
            {'instance_name': 'test-instance'},
            None, None, None, 'dataplane_outside', self.node_type
        )

    def test_process_sync_instance_not_in_profile(self):
        """Test _process_sync when an instance is not found in the profile."""
        profile = {
            'id': 789,
            'instance_details': {
                'global': {
                    'public.vip_address': '*************',
                    'private.vip_address': '************'
                },
                'instances': [
                    {'instance_name': 'known-instance'}
                ]
            }
        }
        instances = [
            {'id': 1001, 'alias': 'unknown-instance'}
        ]

        changes, change_types = self.manager._process_sync(profile, instances, self.node_type)

        # Verify the result
        self.assertEqual(changes["total_instances_updated"], 0)
        self.assertEqual(changes["failed_instances"], [])
        self.assertEqual(change_types, set())

        # Verify logger was called for the missing instance
        self.manager.logger.error.assert_called_with(
            "Instance unknown-instance (ID: 1001) not found in profile"
        )

    def test_process_sync_multiple_change_types(self):
        """Test _process_sync with multiple types of changes."""
        profile = {
            'id': 789,
            'instance_details': {
                'global': {
                    'public.vip_address': '*************',
                    'private.vip_address': '************'
                },
                'instances': [
                    {'instance_name': 'instance1instance1'},
                    {'instance_name': 'instance2'},
                    {'instance_name': 'instance3'}
                ]
            }
        }
        instances = [
            {'id': 1001, 'alias': 'instance1instance1'},
            {'id': 1002, 'alias': 'instance2'},
            {'id': 1003, 'alias': 'instance3'}
        ]

        # Mock _sync_instance to return different changes for each instance
        def mock_sync_instance(*args, **kwargs):
            instance_id = args[0]
            if instance_id == 1001:
                return {
                    "change_detected": True,
                    "vip_change": True,
                    "ip_change": False,
                    "gateway_change": False
                }
            elif instance_id == 1002:
                return {
                    "change_detected": True,
                    "vip_change": False,
                    "ip_change": True,
                    "gateway_change": False
                }
            elif instance_id == 1003:
                return {
                    "change_detected": True,
                    "vip_change": False,
                    "ip_change": False,
                    "gateway_change": True
                }

        self.manager._sync_instance = MagicMock(side_effect=mock_sync_instance)

        changes, change_types = self.manager._process_sync(profile, instances, self.node_type)

        # Verify the result
        self.assertEqual(changes["vip_changes"], 1)
        self.assertEqual(changes["ip_changes"], 1)
        self.assertEqual(changes["gateway_changes"], 1)
        self.assertEqual(changes["total_instances_updated"], 3)
        self.assertEqual(len(changes["instances_with_changes"]), 3)
        self.assertEqual(change_types, {
            ChangeType.VIP_CHANGE.value,
            ChangeType.IP_CHANGE.value,
            ChangeType.GATEWAY_CHANGE.value
        })

        # Verify SNS was called for each changed instance
        self.assertEqual(self.mock_sns.call_count, 3)
        self.mock_sns.assert_has_calls([
            call(1001, self.custid, self.manager.logger),
            call(1002, self.custid, self.manager.logger),
            call(1003, self.custid, self.manager.logger)
        ])

    def test_process_sync_with_instance_error(self):
        """Test _process_sync handling instances with errors."""
        profile = {
            'id': 789,
            'instance_details': {
                'global': {
                    'public.vip_address': '*************',
                    'private.vip_address': '************'
                },
                'instances': [
                    {'instance_name': 'success-instance'},
                    {'instance_name': 'error-instance'}
                ]
            }
        }
        instances = [
            {'id': 1001, 'alias': 'success-instance'},
            {'id': 1002, 'alias': 'error-instance'}
        ]

        # Mock _sync_instance to return success for one instance and error for another
        def mock_sync_instance(*args, **kwargs):
            instance_id = args[0]
            if instance_id == 1001:
                return {
                    "change_detected": True,
                    "vip_change": True,
                    "ip_change": False,
                    "gateway_change": False
                }
            elif instance_id == 1002:
                return {
                    "change_detected": False,
                    "vip_change": False,
                    "ip_change": False,
                    "gateway_change": False,
                    "error": "Test error message"
                }

        self.manager._sync_instance = MagicMock(side_effect=mock_sync_instance)

        changes, change_types = self.manager._process_sync(profile, instances, self.node_type)

        # Verify the result
        self.assertEqual(changes["vip_changes"], 1)
        self.assertEqual(changes["total_instances_updated"], 1)
        self.assertEqual(len(changes["instances_with_changes"]), 1)
        self.assertEqual(len(changes["failed_instances"]), 1)
        self.assertEqual(changes["failed_instances"][0]["id"], 1002)
        self.assertEqual(changes["failed_instances"][0]["error"], "Test error message")
        self.assertEqual(change_types, {ChangeType.VIP_CHANGE.value})

        # Verify SNS was only called for the successful instance
        self.mock_sns.assert_called_once_with(1001, self.custid, self.manager.logger)

    def test_process_sync_with_exception_in_instance_processing(self):
        """Test _process_sync handling exceptions during instance processing."""
        profile = {
            'id': 789,
            'instance_details': {
                'global': {
                    'public.vip_address': '*************',
                    'private.vip_address': '************'
                },
                'instances': [
                    {'instance_name': 'instance1'},
                    {'instance_name': 'instance2'}
                ]
            }
        }
        instances = [
            {'id': 1001, 'alias': 'instance1'},
            {'id': 1002, 'alias': 'instance2'}
        ]

        # Mock _sync_instance to raise an exception for the second instance
        def mock_sync_instance(*args, **kwargs):
            instance_id = args[0]
            if instance_id == 1001:
                return {
                    "change_detected": True,
                    "vip_change": True,
                    "ip_change": False,
                    "gateway_change": False
                }
            elif instance_id == 1002:
                raise Exception("Test exception during instance processing")

        self.manager._sync_instance = MagicMock(side_effect=mock_sync_instance)

        changes, change_types = self.manager._process_sync(profile, instances, self.node_type)

        # Verify the result
        self.assertEqual(changes["vip_changes"], 1)
        self.assertEqual(changes["total_instances_updated"], 1)
        self.assertEqual(len(changes["instances_with_changes"]), 1)
        self.assertEqual(len(changes["failed_instances"]), 1)
        self.assertEqual(changes["failed_instances"][0]["id"], 1002)
        self.assertTrue("Test exception during instance processing" in changes["failed_instances"][0]["error"])

        # Verify logger was called for the exception
        self.manager.logger.error.assert_any_call(mock.ANY)  # Exception message
        self.manager.logger.error.assert_any_call(mock.ANY)  # Traceback

    def test_process_sync_with_global_exception(self):
        """Test _process_sync handling global exceptions."""
        profile = {
            'id': 789,
            'instance_details': {
                'global': {
                    'public.vip_address': '*************',
                    'private.vip_address': '************'
                },
                'instances': [
                    {'instance_name': 'instance1'}
                ]
            }
        }
        instances = [
            {'id': 1001, 'alias': 'instance1'}
        ]

        # Simulate a global exception by making instance_configs[instance_alias] fail
        with patch.object(self.manager, '_sync_instance', side_effect=Exception("Global test exception")):
            changes, change_types = self.manager._process_sync(profile, instances, self.node_type)

        # Verify the result includes the global error
        self.assertTrue("Global test exception" in changes["failed_instances"][0]['error'])

        # Verify logger was called for the exception
        self.manager.logger.error.assert_any_call(mock.ANY)  # Exception message
        self.manager.logger.error.assert_any_call(mock.ANY)  # Traceback

