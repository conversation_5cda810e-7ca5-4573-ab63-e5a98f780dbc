from unittest.mock import patch, MagicMock
import sys
import os
import pytest
import logging
from mock_models import *
from orchestration_service.core.orchestrator_nat_mgmt import allocate_nat_gateways, is_nat_gateway_nlb_needed, has_nat_gateways
from libs.common.shared.sys_utils import NODE_TYPE_NAT_INSTANCE

class TestOrchestatorNatMgmt:

    @patch('orchestration_service.core.orchestrator_nat_mgmt.has_nat_gateways', return_value = 0) 
    @patch('orchestration_service.core.orchestrator_nat_mgmt.get_capacity_type', return_value = [[0],[0],[0],[500]])
    @patch('orchestration_service.core.orchestrator_nat_mgmt.find_nr_nat_instances_by_custid_and_region_id', return_value = (1, 1, 2))
    @patch('orchestration_service.core.orchestrator_nat_mgmt.generate_sample_log_tag', return_value = b"sample_tag")
    def test_allocate_nat_gateways(self, mock_tag, mock_find_nr_nat_instances, mock_capacity, mock_has_gw):
        dbh = DB()
        with patch('orchestration_service.core.orchestrator_nat_mgmt.CustomerModel', wraps=MockCustomerModel) as mock_cust:
            with patch('orchestration_service.core.orchestrator_nat_mgmt.CustNodeModel', wraps=MockCustNodeModel) as mock_cust_node:
               with patch('orchestration_service.core.orchestrator_nat_mgmt.RegionMasterModel', wraps=MockRegionMasterModel) as mock_region_master:

                   custid = 123
                   compute_region_idx = 214
                   ret = allocate_nat_gateways(dbh, custid, compute_region_idx)

                   assert ret == 1

    @patch('orchestration_service.core.orchestrator_nat_mgmt.has_nat_gateways', return_value = 0)
    @patch('orchestration_service.core.orchestrator_nat_mgmt.get_capacity_type', return_value = [[0],[0],[0],[500]])
    @patch('orchestration_service.core.orchestrator_nat_mgmt.find_nr_nat_instances_by_custid_and_region_id', return_value = (1, 1, 2))
    @patch('orchestration_service.core.orchestrator_nat_mgmt.generate_sample_log_tag', return_value = b"sample_tag")
    def test_allocate_nat_gateways_oci_cloud(self, mock_tag, mock_find_nr_nat_instances, mock_capacity, mock_has_gw):
        dbh = DB()
        with patch('orchestration_service.core.orchestrator_nat_mgmt.CustomerModel', wraps=MockCustomerModel) as mock_cust:
            with patch('orchestration_service.core.orchestrator_nat_mgmt.CustNodeModel', wraps=MockCustNodeModel) as mock_cust_node:
               with patch('orchestration_service.core.orchestrator_nat_mgmt.RegionMasterModel', wraps=MockRegionMasterModel) as mock_region_master:

                   custid = 123
                   compute_region_idx = 214
                   edge_region_idx = 302
                   cloud_provider = 'oci'
                   ret = allocate_nat_gateways(dbh, custid, compute_region_idx, edge_region_idx=edge_region_idx, cloud_provider=cloud_provider)

                   assert ret == 1
                   

    @patch('orchestration_service.core.orchestrator_nlb_mgmt.has_nlb_in_region', return_value = 0) 
    @patch('orchestration_service.core.orchestrator_nat_mgmt.has_nat_gateways', return_value = 0) 
    @patch('orchestration_service.core.orchestrator_nat_mgmt.execute_orch_query', side_effect = [(True, [(1,1,2,2,0,0)]), (True,(1,0))])
    @patch('orchestration_service.core.orchestrator_nat_mgmt.gpcs_get_cloud_type_from_region_idx', return_value = 2) 
    @patch('orchestration_service.core.orchestrator_nat_mgmt.find_non_transient_entries_by_custid_and_region', return_value = (True, [1,2,3]))
    def test_is_nat_gateway_nlb_needed(self, mock_find, mock_get_cloud, mock_orch_query, mock_has_nat, mock_has_nlb):
        dbh = DB()
        custid = 123
        compute_region_idx = 214
        nat_gw_needed, nlb_needed = is_nat_gateway_nlb_needed(dbh, custid, compute_region_idx)

        assert nat_gw_needed == 1 and nlb_needed == 1

    @patch('orchestration_service.core.orchestrator_nlb_mgmt.has_nlb_in_region', return_value = 0) 
    @patch('orchestration_service.core.orchestrator_nat_mgmt.has_nat_gateways', return_value = 0) 
    @patch('orchestration_service.core.orchestrator_nat_mgmt.execute_orch_query', side_effect = [(True, [(1,1,2,2,0,0)]), (True,(1,0))])
    @patch('orchestration_service.core.orchestrator_nat_mgmt.gpcs_get_cloud_type_from_region_idx', return_value = 2) 
    @patch('orchestration_service.core.orchestrator_nat_mgmt.find_non_transient_entries_by_custid_and_region', return_value = (True, [1,2,3]))
    def test_is_nat_gateway_nlb_needed_with_edge(self, mock_find, mock_get_cloud, mock_orch_query, mock_has_nat, mock_has_nlb):
        dbh = DB()
        custid = 123
        compute_region_idx = 214
        nat_gw_needed, nlb_needed = is_nat_gateway_nlb_needed(dbh, custid, compute_region_idx, edge_region_idx=255)

        assert nat_gw_needed == 1 and nlb_needed == 1

    @patch('orchestration_service.core.orchestrator_nat_mgmt.find_topology_entries_by_custid_and_region_id')
    def test_has_nat_gateways_gcp(self, mock_find_topology):
        mock_find_topology.return_value = (True, [1, 2, 3])
        dbh = MagicMock()
        custid = 123
        compute_region_idx = 214

        result = has_nat_gateways(dbh, custid, compute_region_idx)

        assert result == True
        mock_find_topology.assert_called_once_with(dbh=dbh, custid=custid, compute_region_idx=compute_region_idx, node_type=NODE_TYPE_NAT_INSTANCE)

    @patch('orchestration_service.core.orchestrator_nat_mgmt.find_topology_entries_by_custid_and_region_id')
    def test_has_nat_gateways_gcp_no_gateways(self, mock_find_topology):
        mock_find_topology.return_value = (True, [])
        dbh = MagicMock()
        custid = 123
        compute_region_idx = 214

        result = has_nat_gateways(dbh, custid, compute_region_idx)

        assert result == False

    @patch('orchestration_service.core.orchestrator_nat_mgmt.find_topology_entries_by_custid_and_region_id')
    def test_has_nat_gateways_gcp_exception(self, mock_find_topology):
        mock_find_topology.return_value = (False, None)
        dbh = MagicMock()
        custid = 123
        compute_region_idx = 214

        with pytest.raises(Exception):
            has_nat_gateways(dbh, custid, compute_region_idx)

    @patch('orchestration_service.core.orchestrator_nat_mgmt.find_topology_entries_by_custid_and_region_id')
    def test_has_nat_gateways_oci(self, mock_find_topology):
        mock_find_topology.return_value = (True, [1])
        dbh = MagicMock()
        custid = 123
        compute_region_idx = 214
        edge_region_idx = 215
        cloud_provider = 'oci'

        result = has_nat_gateways(dbh, custid, compute_region_idx, edge_region_idx, cloud_provider)

        assert result == True
        mock_find_topology.assert_called_once_with(dbh=dbh, custid=custid, compute_region_idx=edge_region_idx, node_type=NODE_TYPE_NAT_INSTANCE)

    @patch('orchestration_service.core.orchestrator_nat_mgmt.find_topology_entries_by_custid_and_region_id')
    def test_has_nat_gateways_oci_no_gateways(self, mock_find_topology):
        mock_find_topology.return_value = (True, [])
        dbh = MagicMock()
        custid = 123
        compute_region_idx = 214
        edge_region_idx = 215
        cloud_provider = 'oci'

        result = has_nat_gateways(dbh, custid, compute_region_idx, edge_region_idx, cloud_provider)

        assert result == False

    @patch('orchestration_service.core.orchestrator_nat_mgmt.find_topology_entries_by_custid_and_region_id')
    def test_has_nat_gateways_oci_same_region(self, mock_find_topology):
        mock_find_topology.return_value = (True, [1])
        dbh = MagicMock()
        custid = 123
        compute_region_idx = 214
        edge_region_idx = 214
        cloud_provider = 'oci'

        result = has_nat_gateways(dbh, custid, compute_region_idx, edge_region_idx, cloud_provider)

        assert result == True
        mock_find_topology.assert_called_once_with(dbh=dbh, custid=custid, compute_region_idx=compute_region_idx, node_type=NODE_TYPE_NAT_INSTANCE)

    @patch('orchestration_service.core.orchestrator_nat_mgmt.find_topology_entries_by_custid_and_region_id')
    def test_has_nat_gateways_oci_lookup_failure(self, mock_find_topology):
        mock_find_topology.return_value = (False, None)
        dbh = MagicMock()
        dbh.logger = MagicMock()
        custid = 123
        compute_region_idx = 214
        edge_region_idx = 215
        cloud_provider = 'oci'

        result = has_nat_gateways(dbh, custid, compute_region_idx, edge_region_idx, cloud_provider)

        assert result == False
        dbh.logger.error.assert_called_once()
