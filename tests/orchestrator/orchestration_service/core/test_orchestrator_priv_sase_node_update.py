import unittest
from unittest.mock import MagicMock, patch
import traceback
import mock_imports_orchestrator

from orchestration_service.core.orchestrator_priv_sase_node_update import handle_priv_sase_node_update_event
from libs.common.shared.sys_utils import NODE_TYPE_GP_GATEWAY, NODE_TYPE_SASE_PRIV_REGION_LB


class TestHandlePrivSaseNodeUpdateEvent(unittest.TestCase):
    def setUp(self):
        # Create mock database handler
        self.db_h = MagicMock()
        self.db_h.logger = MagicMock()

        # Set up valid test parameters
        self.valid_sase_private_region_id = 123
        self.valid_service_node_type = 456
        self.valid_custid = 789
        self.valid_node_type_gateway = NODE_TYPE_GP_GATEWAY
        self.valid_node_type_lb = NODE_TYPE_SASE_PRIV_REGION_LB

        # Create mock successful synchronization result
        self.successful_sync_result = {
            "success": True,
            "changes": {
                "vip_changes": 2,
                "ip_changes": 3,
                "gateway_changes": 1,
                "total_instances_updated": 5
            },
            "change_types": ["VIP", "IP", "Gateway"]
        }

        # Create mock failed synchronization result
        self.failed_sync_result = {
            "success": False,
            "error": "Failed to synchronize due to database error"
        }

    @patch('orchestration_service.core.orchestrator_priv_sase_node_update.ProfileSyncManager')
    def test_successful_sync_with_gateway_node_type(self, MockProfileSyncManager):
        # Arrange
        mock_sync_manager = MockProfileSyncManager.return_value
        mock_sync_manager.sync_profile_to_instances.return_value = self.successful_sync_result

        # Act
        result = handle_priv_sase_node_update_event(
            self.db_h,
            self.valid_sase_private_region_id,
            self.valid_service_node_type,
            self.valid_custid,
            self.valid_node_type_gateway
        )

        # Assert
        self.assertTrue(result["success"])
        MockProfileSyncManager.assert_called_once_with(
            dbh=self.db_h,
            node_type=self.valid_node_type_gateway,
            service_node_type=self.valid_service_node_type,
            sase_private_region_id=self.valid_sase_private_region_id,
            custid=self.valid_custid
        )
        self.db_h.logger.info.assert_called()  # Verify logging happened
        self.assertEqual(result, self.successful_sync_result)

    @patch('orchestration_service.core.orchestrator_priv_sase_node_update.ProfileSyncManager')
    def test_successful_sync_with_lb_node_type(self, MockProfileSyncManager):
        # Arrange
        mock_sync_manager = MockProfileSyncManager.return_value
        mock_sync_manager.sync_profile_to_instances.return_value = self.successful_sync_result

        # Act
        result = handle_priv_sase_node_update_event(
            self.db_h,
            self.valid_sase_private_region_id,
            self.valid_service_node_type,
            self.valid_custid,
            self.valid_node_type_lb
        )

        # Assert
        self.assertTrue(result["success"])
        MockProfileSyncManager.assert_called_once_with(
            dbh=self.db_h,
            node_type=self.valid_node_type_lb,
            service_node_type=self.valid_service_node_type,
            sase_private_region_id=self.valid_sase_private_region_id,
            custid=self.valid_custid
        )
        self.assertEqual(result, self.successful_sync_result)

    @patch('orchestration_service.core.orchestrator_priv_sase_node_update.ProfileSyncManager')
    def test_failed_sync(self, MockProfileSyncManager):
        # Arrange
        mock_sync_manager = MockProfileSyncManager.return_value
        mock_sync_manager.sync_profile_to_instances.return_value = self.failed_sync_result

        # Act
        result = handle_priv_sase_node_update_event(
            self.db_h,
            self.valid_sase_private_region_id,
            self.valid_service_node_type,
            self.valid_custid,
            self.valid_node_type_gateway
        )

        # Assert
        self.assertFalse(result["success"])
        self.db_h.logger.error.assert_called()  # Verify error logging happened
        self.assertEqual(result, self.failed_sync_result)

    def test_invalid_parameter_types(self):
        # Test with non-integer sase_private_region_id
        result = handle_priv_sase_node_update_event(
            self.db_h,
            "123",  # String instead of int
            self.valid_service_node_type,
            self.valid_custid,
            self.valid_node_type_gateway
        )
        self.assertFalse(result["success"])
        self.assertIn("Invalid parameter types", result["error"])

        # Test with non-integer service_node_type
        result = handle_priv_sase_node_update_event(
            self.db_h,
            self.valid_sase_private_region_id,
            None,  # None instead of int
            self.valid_custid,
            self.valid_node_type_gateway
        )
        self.assertFalse(result["success"])
        self.assertIn("Invalid parameter types", result["error"])

        # Test with non-integer custid
        result = handle_priv_sase_node_update_event(
            self.db_h,
            self.valid_sase_private_region_id,
            self.valid_service_node_type,
            3.14,  # Float instead of int
            self.valid_node_type_gateway
        )
        self.assertFalse(result["success"])
        self.assertIn("Invalid parameter types", result["error"])

        # Test with non-integer node_type
        result = handle_priv_sase_node_update_event(
            self.db_h,
            self.valid_sase_private_region_id,
            self.valid_service_node_type,
            self.valid_custid,
            [49]  # List instead of int
        )
        self.assertFalse(result["success"])
        self.assertIn("Invalid parameter types", result["error"])

    def test_unsupported_node_type(self):
        # Test with unsupported node type (not 49 or 179)
        result = handle_priv_sase_node_update_event(
            self.db_h,
            self.valid_sase_private_region_id,
            self.valid_service_node_type,
            self.valid_custid,
            999  # Unsupported node type
        )
        self.assertFalse(result["success"])
        self.assertIn("Unsupported node_type", result["error"])

    @patch('orchestration_service.core.orchestrator_priv_sase_node_update.ProfileSyncManager')
    def test_exception_handling(self, MockProfileSyncManager):
        # Arrange
        mock_sync_manager = MockProfileSyncManager.return_value
        mock_sync_manager.sync_profile_to_instances.side_effect = Exception("Unexpected error")

        # Act
        result = handle_priv_sase_node_update_event(
            self.db_h,
            self.valid_sase_private_region_id,
            self.valid_service_node_type,
            self.valid_custid,
            self.valid_node_type_gateway
        )

        # Assert
        self.assertFalse(result["success"])
        self.assertIn("Exception in handle_priv_sase_node_update_event", result["error"])
        self.assertIn("Unexpected error", result["error"])
        self.db_h.logger.error.assert_called()  # Verify error logging happened


