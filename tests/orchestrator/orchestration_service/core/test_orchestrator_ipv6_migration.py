import sys
import unittest
import sys

from unittest.mock import patch, Mock
from unittest.mock import Magic<PERSON>ock
import mock_imports_orchestrator
from orchestration_service.core.orchestrator_ipv6_migration import add_aws_ipv6_address_to_salt_profile, \
    get_zone_name_from_vpc_id, set_salt_profile_external_ipv6, handle_aws_ipv6_instance_change


class TestAddAWSIPv6(unittest.TestCase):
    @patch('orchestration_service.core.orchestrator_ipv6_migration.AWS_IPV6_Handler')
    def test_add_aws_ipv6_address_success(self, MockAWSHandler):
        # Arrange
        mock_dbh = Mock()
        mock_salt_profile = {}
        mock_instance = Mock()
        mock_instance.get_param.return_value = 'test_id'
        mock_ipv6_handler = MockAWSHandler.return_value
        mock_ipv6_handler.get_ipv6_address_by_account_id.return_value = (True, 'test_ipv6')

        # Act
        success = add_aws_ipv6_address_to_salt_profile(
            mock_dbh, mock_salt_profile, mock_instance, 'test_zone')

        # Assert
        self.assertTrue(success)
        self.assertEqual(mock_salt_profile['public_ipv6_address'], 'test_ipv6')

    @patch('orchestration_service.core.orchestrator_ipv6_migration.AWS_IPV6_Handler')
    def test_add_aws_ipv6_address_failure(self, MockAWSHandler):
        # Arrange
        mock_dbh = Mock()
        mock_salt_profile = {}
        mock_instance = Mock()
        mock_instance.get_param.return_value = 'test_id'
        mock_ipv6_handler = MockAWSHandler.return_value
        mock_ipv6_handler.get_ipv6_address_by_account_id.return_value = (False, None)

        # Act
        success = add_aws_ipv6_address_to_salt_profile(
            mock_dbh, mock_salt_profile, mock_instance, 'test_zone')

        # Assert
        self.assertFalse(success)
        self.assertNotIn('public_ipv6_address', mock_salt_profile)


class TestGetZoneName(unittest.TestCase):

    @patch('orchestration_service.core.orchestrator_ipv6_migration.VpcModel')
    def test_get_zone_name_success(self, mock_vpc_model):
        # Arrange
        dbh = Mock()
        vpc_id = 'vpc123'
        ha_flag = True
        mock_vpc_model.return_value.get_zone_name_and_vpc_type_from_vpc_id.return_value = (
            True, 'zone1', 'dp')

        # Act
        success, zone_name = get_zone_name_from_vpc_id(dbh, vpc_id, ha_flag)

        # Assert
        self.assertTrue(success)
        self.assertEqual(zone_name, 'zone1')

    @patch('orchestration_service.core.orchestrator_ipv6_migration.VpcModel')
    def test_get_zone_name_failure(self, mock_vpc_model):
        # Arrange
        dbh = Mock()
        vpc_id = 'vpc123'
        ha_flag = True
        mock_vpc_model.return_value.get_zone_name_and_vpc_type_from_vpc_id.return_value = (
            False, None, None)

        # Act
        success, zone_name = get_zone_name_from_vpc_id(dbh, vpc_id, ha_flag)

        # Assert
        self.assertFalse(success)
        self.assertIsNone(zone_name)

class TestSetSaltProfileExternalIpv6(unittest.TestCase):

    def test_set_salt_profile_external_ipv6_success(self):
        # Arrange
        mock_dbh = MagicMock()
        mock_instance = MagicMock()
        mock_dbh.logger = MagicMock()
        mock_instance.get_param.return_value = '{"key": "value"}'
        mock_instance.save = MagicMock()

        # Act
        result = set_salt_profile_external_ipv6(mock_dbh, mock_instance)
        print(result)
        # Assert
        self.assertTrue(result)

    def test_set_salt_profile_external_ipv6_exception(self):
        # Arrange
        mock_dbh = MagicMock()
        mock_dbh.logger = MagicMock()
        mock_instance = MagicMock()
        mock_instance.get_param.return_value = '{"key": "value"}'
        mock_instance.save = MagicMock(side_effect=Exception("Test exception"))

        # Act
        result = set_salt_profile_external_ipv6(mock_dbh, mock_instance)

        # Assert
        self.assertFalse(result)
        mock_dbh.logger.error.assert_called()


class TestHandleAwsIpv6InstanceChange(unittest.TestCase):

    @patch('orchestration_service.core.orchestrator_ipv6_migration.get_zone_name_from_vpc_id')
    @patch('orchestration_service.core.orchestrator_ipv6_migration.add_aws_ipv6_address_to_salt_profile')
    def test_handle_aws_ipv6_success(self, mock_add_ipv6, mock_get_zone):
        # Arrange
        mock_dbh = Mock()
        mock_salt_profile = {}
        mock_instance = Mock()
        mock_instance.get_param.return_value = 'test_id'
        mock_get_zone.return_value = (True, 'zone1')
        mock_add_ipv6.return_value = True

        # Act
        success = handle_aws_ipv6_instance_change(
            mock_dbh, mock_salt_profile, mock_instance)

        # Assert
        self.assertTrue(success)
        self.assertTrue(mock_salt_profile['has_external_ipv6'])

    @patch('orchestration_service.core.orchestrator_ipv6_migration.get_zone_name_from_vpc_id')
    @patch('orchestration_service.core.orchestrator_ipv6_migration.add_aws_ipv6_address_to_salt_profile')
    def test_handle_aws_ipv6_failure(self, mock_add_ipv6, mock_get_zone):
        # Arrange
        mock_dbh = Mock()
        mock_salt_profile = {}
        mock_instance = Mock()
        mock_instance.get_param.return_value = 'test_id'
        mock_get_zone.return_value = (True, 'zone1')
        mock_add_ipv6.return_value = False

        # Act
        success = handle_aws_ipv6_instance_change(
            mock_dbh, mock_salt_profile, mock_instance)

        # Assert
        self.assertFalse(success)
        self.assertNotIn('has_external_ipv6', mock_salt_profile)
