from unittest.mock import patch, MagicMock
import sys
import os
import pytest
import logging
from mock_models import *
from orchestration_service.core.orchestrator_central_cache import get_is_central_cache_supported, \
    add_central_cache_tenant_region_mapping, remove_central_cache_tenant_region_mapping

class mockDbh:
    def __init__(self, logger):
        self.logger = logger

class TestOrchestatorCentralCache:

    @patch('orchestration_service.core.orchestrator_central_cache.execute_orch_query')
    def test_get_is_central_cache_supported(self, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        dbh = mockDbh(logger)
        mock_query.return_value = (True, [1])
        is_central_cache_supported = get_is_central_cache_supported(dbh, cust_id)
        assert is_central_cache_supported == "1"

    @patch('orchestration_service.core.orchestrator_central_cache.execute_orch_query')
    def test_get_is_central_cache_supported_not_supported(self, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        dbh = mockDbh(logger)
        mock_query.return_value = (True, [0])
        is_central_cache_supported = get_is_central_cache_supported(dbh, cust_id)
        assert is_central_cache_supported == "0"

    @patch('orchestration_service.core.orchestrator_central_cache.execute_orch_query')
    def test_get_is_central_cache_supported_failure(self, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        dbh = mockDbh(logger)
        mock_query.return_value = (False, [])
        is_central_cache_supported = get_is_central_cache_supported(dbh, cust_id)
        assert is_central_cache_supported == None

    @patch('orchestration_service.core.orchestrator_central_cache.execute_orch_query')
    def test_get_is_central_cache_supported_empty(self, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        dbh = mockDbh(logger)
        mock_query.return_value = (True, [])
        is_central_cache_supported = get_is_central_cache_supported(dbh, cust_id)
        assert is_central_cache_supported == None

    @patch('orchestration_service.core.orchestrator_central_cache.execute_orch_query')
    def test_get_is_central_cache_supported_result_none(self, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        dbh = mockDbh(logger)
        mock_query.return_value = (True, None)
        is_central_cache_supported = get_is_central_cache_supported(dbh, cust_id)
        assert is_central_cache_supported == None
 
    @patch('orchestration_service.core.orchestrator_central_cache.execute_orch_query')
    @patch('orchestration_service.core.orchestrator_central_cache.get_is_central_cache_supported')
    def test_add_central_cache_tenant_region_mapping(self, mock_get_is_central_cache_supported, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        dbh = mockDbh(logger)
        tenant_id = 12345678
        region_id = 215
        mock_get_is_central_cache_supported.return_value = "1"
        mock_query.return_value = (True, [])
        add_central_cache_tenant_region_mapping(dbh, cust_id, tenant_id, region_id)
        assert (f"Successfully inserted record into gpcs_tenant_region_mapping for tenant {tenant_id}, region {region_id}:") in caplog.text

    @patch('orchestration_service.core.orchestrator_central_cache.execute_orch_query')
    @patch('orchestration_service.core.orchestrator_central_cache.get_is_central_cache_supported')
    def test_add_central_cache_tenant_region_mapping_skip(self, mock_get_is_central_cache_supported, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        dbh = mockDbh(logger)
        tenant_id = 12345678
        region_id = 215
        mock_get_is_central_cache_supported.return_value = "0"
        mock_query.return_value = (True, [])
        add_central_cache_tenant_region_mapping(dbh, cust_id, tenant_id, region_id)
        assert (f"Skip adding central cache service tenant region mapping for customer {cust_id}") in caplog.text

    @patch('orchestration_service.core.orchestrator_central_cache.execute_orch_query')
    @patch('orchestration_service.core.orchestrator_central_cache.get_is_central_cache_supported')
    def test_add_central_cache_tenant_region_mapping_failed(self, mock_get_is_central_cache_supported, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        dbh = mockDbh(logger)
        tenant_id = 12345678
        region_id = 215
        mock_get_is_central_cache_supported.return_value = "1"
        mock_query.return_value = (False, [])
        add_central_cache_tenant_region_mapping(dbh, cust_id, tenant_id, region_id)
        assert (f"Failed to insert record in gpcs_tenant_region_mapping for tenant {tenant_id}, region {region_id}:") in caplog.text

    @patch('orchestration_service.core.orchestrator_central_cache.execute_orch_query')
    @patch('orchestration_service.core.orchestrator_central_cache.get_is_central_cache_supported')
    def test_remove_central_cache_tenant_region_mapping(self, mock_get_is_central_cache_supported, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        dbh = mockDbh(logger)
        tenant_id = 12345678
        region_id = 215
        mock_get_is_central_cache_supported.return_value = "1"
        mock_query.return_value = (True, [])
        remove_central_cache_tenant_region_mapping(dbh, cust_id, tenant_id, region_id)
        assert (f"Successfully deleted record from gpcs_tenant_region_mapping for tenant {tenant_id}, region {region_id}:") in caplog.text

    @patch('orchestration_service.core.orchestrator_central_cache.execute_orch_query')
    @patch('orchestration_service.core.orchestrator_central_cache.get_is_central_cache_supported')
    def test_remove_central_cache_tenant_region_mapping_skip(self, mock_get_is_central_cache_supported, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        dbh = mockDbh(logger)
        tenant_id = 12345678
        region_id = 215
        mock_get_is_central_cache_supported.return_value = "0"
        mock_query.return_value = (True, [])
        remove_central_cache_tenant_region_mapping(dbh, cust_id, tenant_id, region_id)
        assert (f"Skip deleting central cache service tenant region mapping for customer {cust_id}") in caplog.text

    @patch('orchestration_service.core.orchestrator_central_cache.execute_orch_query')
    @patch('orchestration_service.core.orchestrator_central_cache.get_is_central_cache_supported')
    def test_remove_central_cache_tenant_region_mapping_failed(self, mock_get_is_central_cache_supported, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        dbh = mockDbh(logger)
        tenant_id = 12345678
        region_id = 215
        mock_get_is_central_cache_supported.return_value = "1"
        mock_query.return_value = (False, [])
        remove_central_cache_tenant_region_mapping(dbh, cust_id, tenant_id, region_id)
        assert (f"Failed to delete record from gpcs_tenant_region_mapping for tenant {tenant_id}, region {region_id}:") in caplog.text
