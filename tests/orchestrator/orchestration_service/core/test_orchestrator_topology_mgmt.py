from unittest.mock import patch, MagicMock
import sys
import os
import pytest
import logging
from mock_models import *
from orchestration_service.core.orchestrator_topology_mgmt import get_count_of_topology_instances_not_marked_for_deletion_in_region

class mockDbh:
    def __init__(self, logger):
        self.logger = logger

class TestOrchestatorTopologyMgmt:
    @patch('orchestration_service.core.orchestrator_topology_mgmt.execute_orch_query')
    def test_get_count_of_topology_instances_not_marked_for_deletion_in_region(self, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        region_id = 215
        node_type = 49
        dbh = mockDbh(logger)
        mock_query.return_value = (True, [10])
        status, count = get_count_of_topology_instances_not_marked_for_deletion_in_region(dbh, cust_id, region_id, node_type)
        assert status == True
        assert count == 10

    @patch('orchestration_service.core.orchestrator_topology_mgmt.execute_orch_query')
    def test_get_count_of_topology_instances_not_marked_for_deletion_in_region_failed(self, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        region_id = 215
        node_type = 49
        dbh = mockDbh(logger)
        mock_query.return_value = (False, [])
        status, count = get_count_of_topology_instances_not_marked_for_deletion_in_region(dbh, cust_id, region_id, node_type)
        assert status == False
        assert count == 0

    @patch('orchestration_service.core.orchestrator_topology_mgmt.execute_orch_query')
    def test_get_count_of_topology_instances_not_marked_for_deletion_in_region_failed_empty_result(self, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        region_id = 215
        node_type = 49
        dbh = mockDbh(logger)
        mock_query.return_value = (True, [])
        status, count = get_count_of_topology_instances_not_marked_for_deletion_in_region(dbh, cust_id, region_id, node_type)
        assert status == False
        assert count == 0

    @patch('orchestration_service.core.orchestrator_topology_mgmt.execute_orch_query')
    def test_get_count_of_topology_instances_not_marked_for_deletion_in_region_failed_none_result(self, mock_query, caplog):
        logger = logging.getLogger()
        caplog.set_level(logging.INFO)
        cust_id = 95
        region_id = 215
        node_type = 49
        dbh = mockDbh(logger)
        mock_query.return_value = (True, None)
        status, count = get_count_of_topology_instances_not_marked_for_deletion_in_region(dbh, cust_id, region_id, node_type)
        assert status == False
        assert count == 0
    