from unittest.mock import patch, MagicMock
import mock_imports_orchestrator
import sys
import os
import pytest
import logging
from orchestration_service.core.orchestrator import <PERSON><PERSON><PERSON>Handler
from mock_models import DB, MockPinnedInstanceUpgradeModel, MockInstanceModel, mockLogger, MockPinnedInstanceUpgradeModelNoEntries
from orchestration_service.core.orchestrator_inst_upgrade_mgmt import pinned_gp_gateway_instance_upgrade_orchestration, pinned_gp_gateway_instance_upgrade_cloud_formation_error_log,\
                                                                pinned_gp_gateway_instance_upgrade_orchestration_error_log
logger = logging.getLogger()

def mock_is_nat_gateway_nlb_needed(dbh, custid, compute_region_idx):
    return 1,1

def mock_allocate_nat_gateways(db_h, custid, compute_region_idx):
    return 1

def mock_allocate_nlb(db_h, custid, compute_region_idx):
    return 1


def mock_impl(db_h,current_pinned_instance_ref,current_instance_id,target_cloud_machine_type,target_capacity_type):
    return 1

def mock_get_replica_sharding_info(db_h):
    return None, None


class TestOrchestatorInstUpgradeMgmt:
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.allocate_nlb', wraps=mock_allocate_nlb)
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.allocate_nat_gateways', wraps=mock_allocate_nat_gateways)
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.is_nat_gateway_nlb_needed', wraps=mock_is_nat_gateway_nlb_needed)
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.get_replica_sharding_info', wraps=mock_get_replica_sharding_info)
    def test_pinned_gp_gateway_instance_upgrade_orchestration(self, 
                                                              mock_nlb_needed, 
                                                              mock_gateways, 
                                                              mock_nlb, 
                                                              mock_replica_info):
        dbh = DB()
        with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.PinnedInstanceUpgradeModel', wraps=MockPinnedInstanceUpgradeModel) as mock_pinn:
            with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.INST.InstanceModel', wraps=MockInstanceModel) as mock_inst:
                with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.pinned_gp_gateway_instance_upgrade_orchestration_impl', wraps=mock_impl) as mock_pinn_impl:
                    orch_hndlr = OrchstrationHandler(cfg={"unittest": True})
                    orch_hndlr.running = True
                    ret = pinned_gp_gateway_instance_upgrade_orchestration(orch_hndlr, dbh)
                    assert ret == False
                    assert mock_pinn_impl.called == True
    
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.allocate_nlb', wraps=mock_allocate_nlb)
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.allocate_nat_gateways', wraps=mock_allocate_nat_gateways)
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.is_nat_gateway_nlb_needed', wraps=mock_is_nat_gateway_nlb_needed)
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.get_replica_sharding_info', wraps=mock_get_replica_sharding_info)
    def test_pinned_gp_gateway_instance_upgrade_orchestration_allocate_nlb_failed(self, 
                                                                                  mock_nlb_needed, 
                                                                                  mock_gateways, 
                                                                                  mock_nlb, 
                                                                                  mock_replica_info):
        dbh = DB()
        with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.PinnedInstanceUpgradeModel', wraps=MockPinnedInstanceUpgradeModel) as mock_pinn:
            with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.INST.InstanceModel', wraps=MockInstanceModel) as mock_inst:
                with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.pinned_gp_gateway_instance_upgrade_orchestration_impl', wraps=mock_impl) as mock_pinn_impl:
                    mock_nlb.return_value = 0
                    mockLogger.error = MagicMock()
                    orch_hndlr = OrchstrationHandler(cfg={"unittest": True})
                    orch_hndlr.running = True
                    ret = pinned_gp_gateway_instance_upgrade_orchestration(orch_hndlr, dbh)

                    assert ret == False

    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.allocate_nlb', wraps=MagicMock())
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.allocate_nat_gateways', wraps=MagicMock())
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.is_nat_gateway_nlb_needed', wraps=mock_is_nat_gateway_nlb_needed)
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.get_replica_sharding_info', wraps=mock_get_replica_sharding_info)
    def test_pinned_gp_gateway_instance_upgrade_orchestration_allocate_nlb_exception(self, 
                                                                                     mock_nlb_needed, 
                                                                                     mock_gateways, 
                                                                                     mock_nlb, 
                                                                                     mock_replica_info):
        dbh = DB()
        with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.PinnedInstanceUpgradeModel', wraps=MockPinnedInstanceUpgradeModel) as mock_pinn:
            with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.INST.InstanceModel', wraps=MockInstanceModel) as mock_inst:
                with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.pinned_gp_gateway_instance_upgrade_orchestration_impl', wraps=mock_impl) as mock_pinn_impl:
                    mockLogger.error = MagicMock()
                    mock_nlb.return_value = None
                    mock_gateways.return_value = 1
                    orch_hndlr = OrchstrationHandler(cfg={"unittest": True})
                    orch_hndlr.running = True
                    ret = pinned_gp_gateway_instance_upgrade_orchestration(orch_hndlr, dbh)

                    assert ret == False
                    assert mock_gateways.called == True
    
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.allocate_nlb', wraps=mock_allocate_nlb)
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.allocate_nat_gateways', wraps=mock_allocate_nat_gateways)
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.is_nat_gateway_nlb_needed', wraps=mock_is_nat_gateway_nlb_needed)
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.get_replica_sharding_info', wraps=mock_get_replica_sharding_info)
    def test_pinned_gp_gateway_instance_upgrade_orchestration_allocate_nat_fails(self, 
                                                                                 mock_nlb_needed, 
                                                                                 mock_gateways, 
                                                                                 mock_nlb, 
                                                                                 mock_replica_info):
        dbh = DB()
        with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.PinnedInstanceUpgradeModel', wraps=MockPinnedInstanceUpgradeModel) as mock_pinn:
            with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.INST.InstanceModel', wraps=MockInstanceModel) as mock_inst:
                with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.pinned_gp_gateway_instance_upgrade_orchestration_impl', wraps=mock_impl) as mock_pinn_impl:
                    mock_gateways.return_value = 0
                    mockLogger.error = MagicMock()
                    orch_hndlr = OrchstrationHandler(cfg={"unittest": True})
                    orch_hndlr.running = True
                    ret = pinned_gp_gateway_instance_upgrade_orchestration(orch_hndlr, dbh)

                    assert ret == False

    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.allocate_nlb', wraps=MagicMock())
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.allocate_nat_gateways', wraps=MagicMock())
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.is_nat_gateway_nlb_needed', wraps=mock_is_nat_gateway_nlb_needed)
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.get_replica_sharding_info', wraps=mock_get_replica_sharding_info)
    def test_pinned_gp_gateway_instance_upgrade_orchestration_allocate_nat_exception(self, 
                                                                                     mock_nlb_needed, 
                                                                                     mock_gateways, 
                                                                                     mock_nlb, 
                                                                                     mock_replica_info):
        dbh = DB()
        with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.PinnedInstanceUpgradeModel', wraps=MockPinnedInstanceUpgradeModel) as mock_pinn:
            with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.INST.InstanceModel', wraps=MockInstanceModel) as mock_inst:
                    mockLogger.error = MagicMock()
                    mock_gateways.return_value = None
                    orch_hndlr = OrchstrationHandler(cfg={"unittest": True})
                    orch_hndlr.running = True
                    ret = pinned_gp_gateway_instance_upgrade_orchestration(orch_hndlr, dbh)

                    assert ret == False
                    assert mock_nlb.called == False
                    assert mock_gateways.called == True
    
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.allocate_nlb', wraps=mock_allocate_nlb)
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.allocate_nat_gateways', wraps=mock_allocate_nat_gateways)
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.is_nat_gateway_nlb_needed', wraps=mock_is_nat_gateway_nlb_needed)
    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.get_replica_sharding_info', wraps=mock_get_replica_sharding_info)
    def test_pinned_gp_gateway_instance_upgrade_orchestration_no_nr_entries(self, 
                                                                            mock_nlb_needed, 
                                                                            mock_gateways, 
                                                                            mock_nlb, 
                                                                            mock_replica_info):
        dbh = DB()
        with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.PinnedInstanceUpgradeModel', wraps=MockPinnedInstanceUpgradeModelNoEntries) as mock_pinn:
            with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.INST.InstanceModel', wraps=MockInstanceModel) as mock_inst:
                with patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.pinned_gp_gateway_instance_upgrade_orchestration_impl', wraps=mock_impl) as mock_pinn_impl:
                    orch_hndlr = OrchstrationHandler(cfg={"unittest": True})
                    orch_hndlr.running = True
                    ret = pinned_gp_gateway_instance_upgrade_orchestration(orch_hndlr, dbh)

                    assert ret == True

    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.send_ngpa_cust_alert')
    def test_pinned_gp_gateway_instance_upgrade_cloud_formation_error_log(self, mock_send_alerts, caplog):
        caplog.set_level(logging.INFO)
        dbh = DB()
        mocked_model = MockPinnedInstanceUpgradeModel(dbh)
        pinned_gp_gateway_instance_upgrade_orchestration_error_log(logger, "test_err", mocked_model)

    @patch('orchestration_service.core.orchestrator_inst_upgrade_mgmt.send_ngpa_cust_alert')
    def test_pinned_gp_gateway_instance_upgrade_cloud_formation_error_log(self, mock_send_alerts, caplog):
        caplog.set_level(logging.INFO)
        dbh = DB()
        mocked_model = MockPinnedInstanceUpgradeModel(dbh)
        pinned_gp_gateway_instance_upgrade_cloud_formation_error_log(logger, "test_err", mocked_model)