import logging
import json

logger = logging.getLogger()
from libs.common.shared.sys_utils import NODE_TYPE_GP_GATEWAY
import utils.Exceptions as customExceptions

class InstanceModel:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        pass

    def get_param(self, key):
        if key == "salt_profile":
            return {
                "Zone": "us-west1-a",
                "InstanceName": "swgproxy-456-fw",
                "AcctId": 1011453056
            }

class InstanceModelOrchestratorIPMgmtTest1:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        pass

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "egress_ip_list":
            return json.dumps({"277": "**************"})
        elif key == "interface_ip_list":
            return json.dumps({"277": "**************"})
        return None

    def cleanup_fqdn(self, db_h, selective_egress_ip_deletion, egress_ip_dict):
        return

    def save_egress_ip_list(self, egress_list):
        return

    def update_column_interface_ip_list(self, ip_list_dict):
        return

    def return_ip_to_public_ip_pool(self, dbh, ip):
        return

class InstanceModelOrchestratorIPMgmtTest2:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        pass

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "egress_ip_list":
            return json.dumps({"277": "ASSIGNED_TO_NAT"})
        elif key == "interface_ip_list":
            return json.dumps({"277": "**************"})
        return None

    def cleanup_fqdn(self, db_h, selective_egress_ip_deletion, egress_ip_dict):
        return

    def save_egress_ip_list(self, egress_list):
        return

    def update_column_interface_ip_list(self, ip_list_dict):
        return

    def return_ip_to_public_ip_pool(self, dbh, ip):
        return

class InstanceModelOrchestratorIPMgmtTest3:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        pass

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "egress_ip_list":
            return json.dumps({"277": "ASSIGNED_TO_NAT"})
        elif key == "interface_ip_list":
            return {"277": "**************"}
        return None

    def cleanup_fqdn(self, db_h, selective_egress_ip_deletion, egress_ip_dict):
        return

    def save_egress_ip_list(self, egress_list):
        return

    def update_column_interface_ip_list(self, ip_list_dict):
        return

    def return_ip_to_public_ip_pool(self, dbh, ip):
        return

class InstanceModelOrchestratorIPMgmtTest4:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        return True

    def query(self, update=False):
        return True
    
    def save(self):
        return True

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "egress_ip_list":
            return json.dumps({"277": "**************"})
        elif key == "interface_ip_list":
            return json.dumps({"277": "**************"})
        elif key == "egress_ipv6_list":
            return json.dumps({"277": "2ff0:1600:ab::2"})
        elif key == "interface_ipv6_list":
            return json.dumps({"277": "2ff0:1600:ab::2"})
        elif key == "egress_ipv6_list_subnet":
            return json.dumps({"277": "subnet-ipv6-277"})
        return None
    
    def set_param(self, field, value):
        pass

    def cleanup_fqdn(self, db_h, selective_egress_ip_deletion, egress_ip_dict):
        return

    def save_egress_ip_list(self, egress_list):
        return

    def save_egress_ipv6_list(self, egress_list):
        return

    def update_column_interface_ip_list(self, ip_list_dict):
        return

    def update_column_interface_ipv6_list(self, ip_list_dict):
        return

    def return_ip_to_public_ip_pool(self, dbh, ip):
        return
    
class InstanceModelOrchestratorIPMgmtTest5:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        return True

    def query(self, update=False):
        return True
    
    def save(self):
        return True

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "egress_ip_list":
            return json.dumps({"277": "ASSIGNED_TO_NAT"})
        elif key == "interface_ip_list":
            return json.dumps({"277": "**************"})
        elif key == "egress_ipv6_list":
            return json.dumps({"277": "2ff0:1600:ab::2"})
        elif key == "interface_ipv6_list":
            return json.dumps({"277": "2ff0:1600:ab::2"})
        elif key == "egress_ipv6_list_subnet":
            return json.dumps({"277": "subnet-ipv6-277"})
        return None
    
    def set_param(self, field, value):
        pass

    def cleanup_fqdn(self, db_h, selective_egress_ip_deletion, egress_ip_dict):
        return

    def save_egress_ip_list(self, egress_list):
        return

    def save_egress_ipv6_list(self, egress_list):
        return

    def update_column_interface_ip_list(self, ip_list_dict):
        return

    def update_column_interface_ipv6_list(self, ip_list_dict):
        return

    def return_ip_to_public_ip_pool(self, dbh, ip):
        return
    
class InstanceModelOrchestratorIPMgmtTest6:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        return True

    def query(self, update=False):
        return True
    
    def save(self):
        return True

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "egress_ip_list":
            return json.dumps({"277": "ASSIGNED_TO_NAT", "290": "ASSIGNED_TO_NAT"})
        elif key == "interface_ip_list":
            return json.dumps({"277": "**************"})
        elif key == "egress_ipv6_list":
            return json.dumps({"277": "2ff0:1600:ab::2", "290": "2ff0:2000:ab::2"})
        elif key == "interface_ipv6_list":
            return json.dumps({"277": "2ff0:1600:ab::2"})
        elif key == "egress_ipv6_list_subnet":
            return json.dumps({"277": "subnet-ipv6-277"})
        return None
    
    def set_param(self, field, value):
        pass

    def cleanup_fqdn(self, db_h, selective_egress_ip_deletion, egress_ip_dict):
        return

    def save_egress_ip_list(self, egress_list):
        return

    def save_egress_ipv6_list(self, egress_list):
        return

    def update_column_interface_ip_list(self, ip_list_dict):
        return

    def update_column_interface_ipv6_list(self, ip_list_dict):
        return

    def return_ip_to_public_ip_pool(self, dbh, ip):
        return

class InstanceModelOrchestratorIPMgmtTest7:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        return True

    def query(self, update=False):
        return True
    
    def save(self):
        return True

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "egress_ip_list":
            return json.dumps({"277": "**************"})
        elif key == "interface_ip_list":
            return json.dumps({"277": "**************"})
        elif key == "egress_ipv6_list":
            return json.dumps({"277": "2ff0:1600:ab::2"})
        elif key == "interface_ipv6_list":
            return json.dumps({"277": "2ff0:1600:ab::2"})
        elif key == "egress_ipv6_list_subnet":
            return json.dumps({"277": "subnet-ipv6-277"})
        return None
    
    def set_param(self, field, value):
        pass

    def cleanup_fqdn(self, db_h, selective_egress_ip_deletion, egress_ip_dict):
        return

    def save_egress_ip_list(self, egress_list):
        return

    def save_egress_ipv6_list(self, egress_list):
        return

    def update_column_interface_ip_list(self, ip_list_dict):
        return

    def update_column_interface_ipv6_list(self, ip_list_dict):
        raise customExceptions.InvalidInterfaceIpv6ListException("test")
        return

    def return_ip_to_public_ip_pool(self, dbh, ip):
        return

class InstanceModelOrchestratorIPMgmtTest8:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        return True

    def query(self, update=False):
        return True
    
    def save(self):
        return True

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "egress_ip_list":
            return json.dumps({"277": "**************"})
        elif key == "interface_ip_list":
            return json.dumps({"277": "**************"})
        elif key == "egress_ipv6_list":
            return json.dumps({"277": "2ff0:1600:ab::2"})
        elif key == "interface_ipv6_list":
            return json.dumps({"277": "2ff0:1600:ab::2"})
        elif key == "egress_ipv6_list_subnet":
            return json.dumps({"277": "subnet-ipv6-277"})
        return None
    
    def set_param(self, field, value):
        pass

    def cleanup_fqdn(self, db_h, selective_egress_ip_deletion, egress_ip_dict):
        return

    def save_egress_ip_list(self, egress_list):
        return

    def save_egress_ipv6_list(self, egress_list):
        return

    def update_column_interface_ip_list(self, ip_list_dict):
        return

    def update_column_interface_ipv6_list(self, ip_list_dict):
        raise customExceptions.InvalidInstanceIdException("test")
        return

    def return_ip_to_public_ip_pool(self, dbh, ip):
        return
    
class InstanceModelOrchestratorIPMgmtTest9:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        return True

    def query(self, update=False):
        return True
    
    def save(self):
        return True

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "egress_ip_list":
            return json.dumps({"277": "**************"})
        elif key == "interface_ip_list":
            return json.dumps({"277": "**************"})
        elif key == "egress_ipv6_list":
            return json.dumps({"277": "2ff0:1600:ab::2"})
        elif key == "interface_ipv6_list":
            return json.dumps({"277": "2ff0:1600:ab::2"})
        elif key == "egress_ipv6_list_subnet":
            return json.dumps({"277": "subnet-ipv6-277"})
        return None
    
    def set_param(self, field, value):
        pass

    def cleanup_fqdn(self, db_h, selective_egress_ip_deletion, egress_ip_dict):
        return

    def save_egress_ip_list(self, egress_list):
        return

    def save_egress_ipv6_list(self, egress_list):
        return

    def update_column_interface_ip_list(self, ip_list_dict):
        return

    def update_column_interface_ipv6_list(self, ip_list_dict):
        raise customExceptions.InvalidInterfaceIpv6ListJsonException("test")
        return

    def return_ip_to_public_ip_pool(self, dbh, ip):
        return
    
class InstanceModelOrchestratorIPMgmtTest10:
    def __init__(self, id, dbh):
        self.dbh = dbh
        self.id = id

    def get_entry(self):
        return True

    def query(self, update=False):
        return True
    
    def save(self):
        return True

    def get_param(self, key):
        if key == "id":
            return 115
        elif key == "node_type":
            return NODE_TYPE_GP_GATEWAY
        elif key == "lb_details":
            return "dummy"
        elif key == "egress_ip_list":
            return json.dumps({"277": "**************"})
        elif key == "interface_ip_list":
            return json.dumps({"277": "**************"})
        elif key == "egress_ipv6_list":
            return json.dumps({"277": "2ff0:1600:ab::2"})
        elif key == "interface_ipv6_list":
            return json.dumps({"277": "2ff0:1600:ab::2"})
        elif key == "egress_ipv6_list_subnet":
            return json.dumps({"277": "subnet-ipv6-277"})
        return None
    
    def set_param(self, field, value):
        pass

    def cleanup_fqdn(self, db_h, selective_egress_ip_deletion, egress_ip_dict):
        return

    def save_egress_ip_list(self, egress_list):
        return

    def save_egress_ipv6_list(self, egress_list):
        return

    def update_column_interface_ip_list(self, ip_list_dict):
        return

    def update_column_interface_ipv6_list(self, ip_list_dict):
        raise customExceptions.DbUpdateException("test")
        return

    def return_ip_to_public_ip_pool(self, dbh, ip):
        return