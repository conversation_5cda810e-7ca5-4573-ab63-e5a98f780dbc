import pytest
from unittest.mock import patch, MagicMock
import logging
import mock_imports_orchestrator
from mock_models import MockCustEpaasModel, DB
from orchestration_service.core.orchestrator import OrchstrationHandler


class MockInstanceModelForSpInterconnect:
    def __init__(self, iid, dbh):
        self.iid = iid
        self.id = iid
        self.dbh = dbh
        self.dictval = {"id": iid, "node_type": 48, "is_using_sp_interconnect": True,
                        "is_dedicated_inbound_instance": True}
        self.dns_insert = 0

    def set_param(self, key, value):
        self.dictval[key] = value

    def get_param(self, key):
        return self.dictval.get(key, None)

    def save(self):
        pass


# Create a basic cust node model
class mocked_CustNodeModel():
    def __init__(self, iid=0, dbh=None):
        self.id = iid
        self.custid = 15
        self.dbh = dbh
        self.okyo_edge_site_id = 0
        self.is_deleted = 0
        self.node_type = 49
        self.region = 200

    def save(self, dbh):
        return True

# Create a mocked job class
class mocked_Job():
    def __init__(self):
        self.status_msg = ""
        self.status = ""
        self.jobid = 2114

    def save_job(self, dbh):
        return True


class logger():
    def __init__(self):
        self.uuid = "abc"

    def info(self, *args):
        print(*args)

    def error(self, *args):
        print(*args)

    def warn(self, *args):
        print(*args)

    def get_enhanced_traceid(self):
        return "traceid1"

    def reset_enhanced_log_tag(self):
        pass

class Mock_conn():
    def __init__(self):
        pass

    def close(self):
        pass

class Mock_cursor():
    def __init__(self):
        self.sql = None
        self.params = None

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params:
            query = self.sql%self.params
        else:
            query = self.sql
        pass

    def fetchone(self):
        pass

class Mock_DBHandler():
    def __init__(self):
        self.logger = logging.getLogger('testlogger')
        self.conn = Mock_conn()

    def conn(self):
        return self.conn

    def get_cursor(self):
        return Mock_cursor()

    def cursorclose(self, cursor):
        return

cust_ep_cfg = MockCustEpaasModel(Mock_DBHandler(), 123, 220, 2, 153)
cust_ep_cfg.set_param("migrate_ep_status", "IN_PROGRESS")

def set_cfg(cfg):
    cfg['log-root-dir']= '/tmp/',
    cfg['region'] = 'us-west-2',
    cfg['queues'] =  []
    cfg['dbpassword'] = "mankind"
    cfg['unittest'] = True

class Avisar():
    def __init__(self):
        self.panorama_job_id = None
        self.tenant_id = None
        self.cust_topology_id = None
        self.trace_id = None

    def set_ctx(self, trace_id=None, metric_type=None, metric_state=None, metric_severity=None):
        pass

    def publish_event(self):
        pass


class TestOrchestrator:

    @patch('orchestration_service.core.orchestrator.trigger_update_for_instance_changes', return_value=(True, "100"))
    @patch('orchestration_service.core.orchestrator.unbind_egress_ips_from_node_impl', return_value=True)
    @patch('orchestration_service.core.orchestrator.delete_gp_gateway_edge_location_references_impl', return_value=True)
    def test_process_delete_topology_handle_edge_location_deletion_trigger_instance_update_success(self, mocked_delete_gp_gateway_edge_location_references_impl, mocked_unbind_egress_ips_from_node_impl, mocked_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)

        # Create a mock handle for dbh
        dbh=Mock_DBHandler()

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = logging.getLogger('testlogger')
            orchHandler.db_h = dbh
            orchHandler.avctx = None
            orchHandler.tenant_id = 245139
        except Exception as E:
            pass

        # Create a mocked custnode model object.
        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.compute_region_idx = 200
        deleteInstanceDetailsObj.job = mocked_Job()
        success = orchHandler.process_delete_topology_handle_edge_location_deletion(custnode, deleteInstanceDetailsObj)
        assert success is True

    @patch('orchestration_service.core.orchestrator.trigger_update_for_instance_changes', return_value=(False, ""))
    @patch('orchestration_service.core.orchestrator.unbind_egress_ips_from_node_impl', return_value=True)
    @patch('orchestration_service.core.orchestrator.delete_gp_gateway_edge_location_references_impl', return_value=True)
    def test_process_delete_topology_handle_edge_location_deletion_trigger_instance_update_failure(self, mocked_delete_gp_gateway_edge_location_references_impl, mocked_unbind_egress_ips_from_node_impl, mocked_trigger_update_for_instance_changes, caplog):
        caplog.set_level(logging.INFO)

        # Create a mock handle for dbh
        dbh=Mock_DBHandler()

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = logging.getLogger('testlogger')
            orchHandler.db_h = dbh
            orchHandler.avctx = None
            orchHandler.tenant_id = 245139
        except Exception as E:
            pass

        # Create a mocked custnode model object.
        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.compute_region_idx = 200
        deleteInstanceDetailsObj.job = mocked_Job()
        success = orchHandler.process_delete_topology_handle_edge_location_deletion(custnode, deleteInstanceDetailsObj)
        assert "Failed to trigger instance changes for tenant 15 changes for compute idx 200\n" in caplog.text
        assert success is False

    @pytest.mark.parametrize("mock_inbound_access, mock_is_using_sp_interconnect , "
                             "mock_node_type, exp_result",
                             [('shared', True, 48, True),
                              ('dedicated', True, 48, True),
                              ('disabled', False, 48, False),
                              ('dedicated', False, 48, False),
                              ('dedicated', True, 49, False)])
    def test_should_trigger_sp_firewall_rule_operation(self, mock_inbound_access,
                                                       mock_is_using_sp_interconnect, mock_node_type, exp_result):
        mock_inst_obj = MockInstanceModelForSpInterconnect(111, DB)
        mock_inst_obj.set_param("is_using_sp_interconnect", mock_is_using_sp_interconnect)
        mock_inst_obj.set_param("node_type", mock_node_type)

        # Create a mock handle for dbh
        dbh = Mock_DBHandler()
        # Create a mocked custnode model object.
        mock_custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        mock_custnode.inbound_access = mock_inbound_access

        # Create a mock handle for dbh
        dbh = Mock_DBHandler()

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = logging.getLogger('testlogger')
            orchHandler.db_h = dbh
            orchHandler.avctx = None
            orchHandler.tenant_id = 245139
        except Exception as E:
            pass

        ret_val = orchHandler.should_trigger_sp_firewall_rule_operation(mock_inst_obj, mock_custnode)
        assert ret_val == exp_result

    # TODO need to fix - tejas
    # @patch('orchestration_service.core.orchestrator.get_instance_ids_by_tenant_id_and_region_name')
    # @patch('orchestration_service.core.orchestrator.INST.InstanceModel')
    # def test_handle_tenant_ipv6_settings_event_success(self,
    #                                                    mock_InstanceModel,
    #                                                    mock_get_instance_ids,
    #                                                    caplog):
    #     caplog.set_level(logging.INFO)
    #
    #     # Create a mock handle for dbh
    #     dbh = Mock_DBHandler()
    #
    #     # Set the basic cfg for object creation
    #     cfg = {}
    #     set_cfg(cfg)
    #
    #     db_h = MagicMock()
    #     db_h.logger = logger()
    #     job_ref = 'job1'
    #     tenant_id = 'tenant1'
    #     region_name = 'region1'
    #     mode = 'disable'
    #     node_type = 'type1'
    #
    #     orchHandler = None
    #     try:
    #         orchHandler = OrchstrationHandler(cfg)
    #         orchHandler.logger = logging.getLogger('testlogger')
    #         orchHandler.db_h = dbh
    #         orchHandler.avctx = None
    #         orchHandler.tenant_id = 245139
    #     except Exception as E:
    #         pass
    #
    #     # Set up mock return values
    #     mock_get_instance_ids.return_value = (True, [1, 2, 3])
    #     instance1 = MagicMock()
    #     instance1.get_param.return_value = 'id1'
    #     instance2 = MagicMock()
    #     instance2.get_param.return_value = 'id2'
    #     instance3 = MagicMock()
    #     instance3.get_param.return_value = 'id3'
    #     mock_InstanceModel.side_effect = [instance1, instance2, instance3]
    #
    #     # Call function
    #     success = orchHandler.handle_tenant_ipv6_settings_event(job_ref, tenant_id, region_name, mode, node_type)
    #
    #     # Assert results
    #     assert success == True

'''
Invalid test, commenting it

    @patch('orchestration_service.core.orchestrator.JOB.OrchJobs', return_value=mocked_Job())
    @patch('orchestration_service.core.orchestrator.CustEpaasConfigModel', return_value=cust_ep_cfg)
    def test_upgrade_ep_outside_panos_duplicate_request(self, mock_job, mock_cfg, caplog):
        """
        test exit migration process for duplicate requests
        """
        caplog.set_level(logging.INFO)

        # Create a mock handle for dbh
        dbh = Mock_DBHandler()

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.db_h = dbh
            orchHandler.avctx = Avisar()
            orchHandler.tenant_id = 245139
            orchHandler.logger = logger()
        except Exception as E:
            print(E)
            pass

        # Create a mocked custnode model object.
        msg = {"custid":123, "compute_region_id": 220, "cloud_provider": "gcp", "node_type":153}
        res = orchHandler.process_msg(msg, 'UPGRADE_EP_OUTSIDE_PANOS', 1234, [])
        assert res is False
'''

class TestOnrampRNNodeProcessing:

    @patch('orchestration_service.core.orchestrator.allocate_interconnect_onramp_ilb', return_value=1)
    @patch('orchestration_service.core.orchestrator.allocate_onramp_rn_nodes', return_value=2)
    @patch('orchestration_service.core.orchestrator.get_onramp_rn_count', return_value=2)
    def test_onramp_rn_parent_node_success(self, mock_get_count, mock_allocate_nodes, mock_allocate_ilb, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.parent_id = None
        custnode.name = "test-onramp"
        custnode.node_type = 47
        custnode.custid = 123

        compute_region_idx = 200
        custid = 123

        result = orchHandler.process_onramp_rn_node_allocation(custnode, custid, compute_region_idx)

        assert result is True
        mock_get_count.assert_called_once_with(dbh, custnode)
        mock_allocate_nodes.assert_called_once_with(dbh, custnode, 2)
        mock_allocate_ilb.assert_called_once_with(dbh, custid, compute_region_idx, custnode)

    @patch('orchestration_service.core.orchestrator.allocate_interconnect_onramp_ilb', return_value=1)
    @patch('orchestration_service.core.orchestrator.allocate_onramp_rn_nodes', return_value=0)
    @patch('orchestration_service.core.orchestrator.get_onramp_rn_count', return_value=3)
    def test_onramp_rn_parent_node_zero_nodes_created_failure(self, mock_get_count, mock_allocate_nodes, mock_allocate_ilb, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.parent_id = 0
        custnode.name = "test-onramp"
        custnode.node_type = 47
        custnode.custid = 123

        compute_region_idx = 200
        custid = 123

        with pytest.raises(Exception, match="Failed to create child onramp RN nodes"):
            orchHandler.process_onramp_rn_node_allocation(custnode, custid, compute_region_idx)

    @patch('orchestration_service.core.orchestrator.allocate_interconnect_onramp_ilb', return_value=-1)
    @patch('orchestration_service.core.orchestrator.allocate_onramp_rn_nodes', return_value=2)
    @patch('orchestration_service.core.orchestrator.get_onramp_rn_count', return_value=2)
    def test_onramp_rn_parent_node_ilb_creation_failure(self, mock_get_count, mock_allocate_nodes, mock_allocate_ilb, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.parent_id = 9000
        custnode.id = 9000
        custnode.name = "test-onramp"
        custnode.node_type = 47
        custnode.custid = 123

        compute_region_idx = 200
        custid = 123

        with pytest.raises(Exception, match="Failed to create ILB for onramp RN nodes"):
            orchHandler.process_onramp_rn_node_allocation(custnode, custid, compute_region_idx)

    def test_onramp_rn_child_node_skipped(self, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.parent_id = 8000
        custnode.id = 9000
        custnode.name = "test-onramp-child"
        custnode.node_type = 47
        custnode.custid = 123

        compute_region_idx = 200
        custid = 123

        result = orchHandler.process_onramp_rn_node_allocation(custnode, custid, compute_region_idx)

        assert result is None

    def test_gp_gateway_node_type_continuation(self, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.parent_id = None
        custnode.name = "test-gp-gateway"
        custnode.node_type = 49
        custnode.custid = 123

        compute_region_idx = 200
        custid = 123

        result = orchHandler.process_gp_gateway_node_logic(custnode, custid, compute_region_idx)

        assert result is None

    @patch('orchestration_service.core.orchestrator.allocate_interconnect_onramp_ilb', return_value=0)
    @patch('orchestration_service.core.orchestrator.allocate_onramp_rn_nodes', return_value=1)
    @patch('orchestration_service.core.orchestrator.get_onramp_rn_count', return_value=1)
    def test_onramp_rn_parent_node_ilb_zero_return_failure(self, mock_get_count, mock_allocate_nodes, mock_allocate_ilb, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.parent_id = None
        custnode.name = "test-onramp"
        custnode.node_type = 47
        custnode.custid = 123

        compute_region_idx = 200
        custid = 123

        with pytest.raises(Exception, match="Failed to create ILB for onramp RN nodes"):
            orchHandler.process_onramp_rn_node_allocation(custnode, custid, compute_region_idx)

class TestDeleteChildOnrampNodes:

    @patch('orchestration_service.core.orchestrator.delete_onramp_ilb_instance_by_custid_and_region_id')
    @patch('orchestration_service.core.orchestrator.delete_child_onramp_rn')
    def test_delete_child_onramp_nodes_parent_id_none(self, mock_delete_child_onramp_rn, mock_delete_onramp_ilb, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.parent_id = None
        custnode.custid = 123
        custnode.region = 200

        orchHandler.process_delete_child_onramp_cleanup(custnode)

        mock_delete_child_onramp_rn.assert_called_once_with(dbh, custnode.id, custnode.custid, custnode.region)
        mock_delete_onramp_ilb.assert_called_once_with(dbh, custnode.id, custnode.custid, custnode.region)
        assert "Delete child onramp nodes and load balancer" in caplog.text

    @patch('orchestration_service.core.orchestrator.delete_onramp_ilb_instance_by_custid_and_region_id')
    @patch('orchestration_service.core.orchestrator.delete_child_onramp_rn')
    def test_delete_child_onramp_nodes_parent_id_zero(self, mock_delete_child_onramp_rn, mock_delete_onramp_ilb, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.parent_id = 0
        custnode.custid = 123
        custnode.region = 200

        orchHandler.process_delete_child_onramp_cleanup(custnode)

        mock_delete_child_onramp_rn.assert_called_once_with(dbh, custnode.id, custnode.custid, custnode.region)
        mock_delete_onramp_ilb.assert_called_once_with(dbh, custnode.id, custnode.custid, custnode.region)
        assert "Delete child onramp nodes and load balancer" in caplog.text

    @patch('orchestration_service.core.orchestrator.delete_onramp_ilb_instance_by_custid_and_region_id')
    @patch('orchestration_service.core.orchestrator.delete_child_onramp_rn')
    def test_delete_child_onramp_nodes_parent_id_equals_node_id(self, mock_delete_child_onramp_rn, mock_delete_onramp_ilb, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.parent_id = 9000
        custnode.custid = 123
        custnode.region = 200

        orchHandler.process_delete_child_onramp_cleanup(custnode)

        mock_delete_child_onramp_rn.assert_called_once_with(dbh, custnode.id, custnode.custid, custnode.region)
        mock_delete_onramp_ilb.assert_called_once_with(dbh, custnode.id, custnode.custid, custnode.region)
        assert "Delete child onramp nodes and load balancer" in caplog.text

    @patch('orchestration_service.core.orchestrator.delete_onramp_ilb_instance_by_custid_and_region_id')
    @patch('orchestration_service.core.orchestrator.delete_child_onramp_rn')
    def test_delete_child_onramp_nodes_parent_id_different_from_node_id(self, mock_delete_child_onramp_rn, mock_delete_onramp_ilb, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.parent_id = 8000
        custnode.custid = 123
        custnode.region = 200

        orchHandler.process_delete_child_onramp_cleanup(custnode)

        mock_delete_child_onramp_rn.assert_not_called()
        mock_delete_onramp_ilb.assert_not_called()
        assert "Delete child onramp nodes and load balancer" not in caplog.text

    @patch('orchestration_service.core.orchestrator.delete_onramp_ilb_instance_by_custid_and_region_id', side_effect=Exception("ILB deletion failed"))
    @patch('orchestration_service.core.orchestrator.delete_child_onramp_rn')
    def test_delete_child_onramp_nodes_ilb_deletion_exception(self, mock_delete_child_onramp_rn, mock_delete_onramp_ilb, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.parent_id = None
        custnode.custid = 123
        custnode.region = 200

        with pytest.raises(Exception, match="ILB deletion failed"):
            orchHandler.process_delete_child_onramp_cleanup(custnode)

        mock_delete_child_onramp_rn.assert_called_once_with(dbh, custnode.id, custnode.custid, custnode.region)
        mock_delete_onramp_ilb.assert_called_once_with(dbh, custnode.id, custnode.custid, custnode.region)

    @patch('orchestration_service.core.orchestrator.delete_onramp_ilb_instance_by_custid_and_region_id')
    @patch('orchestration_service.core.orchestrator.delete_child_onramp_rn', side_effect=Exception("Child deletion failed"))
    def test_delete_child_onramp_nodes_child_deletion_exception(self, mock_delete_child_onramp_rn, mock_delete_onramp_ilb, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.parent_id = 0
        custnode.custid = 123
        custnode.region = 200

        with pytest.raises(Exception, match="Child deletion failed"):
            orchHandler.process_delete_child_onramp_cleanup(custnode)

        mock_delete_child_onramp_rn.assert_called_once_with(dbh, custnode.id, custnode.custid, custnode.region)
        mock_delete_onramp_ilb.assert_not_called()

    @pytest.mark.parametrize("parent_id, custid, region, node_id", [
        (None, 456, 300, 8500),
        (0, 789, 400, 7500),
        (6500, 321, 500, 6500)
    ])
    @patch('orchestration_service.core.orchestrator.delete_onramp_ilb_instance_by_custid_and_region_id')
    @patch('orchestration_service.core.orchestrator.delete_child_onramp_rn')
    def test_delete_child_onramp_nodes_with_different_parameters(self, mock_delete_child_onramp_rn, mock_delete_onramp_ilb, parent_id, custid, region, node_id, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=node_id, dbh=dbh)
        custnode.parent_id = parent_id
        custnode.custid = custid
        custnode.region = region

        orchHandler.process_delete_child_onramp_cleanup(custnode)

        if parent_id is None or parent_id == 0 or parent_id == node_id:
            mock_delete_child_onramp_rn.assert_called_once_with(dbh, node_id, custid, region)
            mock_delete_onramp_ilb.assert_called_once_with(dbh, node_id, custid, region)
            assert "Delete child onramp nodes and load balancer" in caplog.text
        else:
            mock_delete_child_onramp_rn.assert_not_called()
            mock_delete_onramp_ilb.assert_not_called()

class TestProcessDeleteTopologyHandleNatNlbDeletion:

    @patch('orchestration_service.core.orchestrator.delete_nat_nlb_instance_impl', return_value=True)
    def test_process_delete_topology_handle_nat_nlb_deletion_success(self, mock_delete_nat_nlb, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.compute_region_idx = 200
        deleteInstanceDetailsObj.job = mocked_Job()

        success = orchHandler.process_delete_topology_handle_nat_nlb_deletion(custnode, deleteInstanceDetailsObj)

        assert success is True
        assert "process_delete_topology_handle_nat_nlb_deletion: NAT/NLB deleted True" in caplog.text
        mock_delete_nat_nlb.assert_called_once()

    @patch('orchestration_service.core.orchestrator.delete_nat_nlb_instance_impl', return_value=False)
    def test_process_delete_topology_handle_nat_nlb_deletion_failure(self, mock_delete_nat_nlb, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.compute_region_idx = 200
        deleteInstanceDetailsObj.job = mocked_Job()

        success = orchHandler.process_delete_topology_handle_nat_nlb_deletion(custnode, deleteInstanceDetailsObj)

        assert success is False
        assert "process_delete_topology_handle_nat_nlb_deletion: NAT/NLB deleted False" in caplog.text
        mock_delete_nat_nlb.assert_called_once()

    @patch('orchestration_service.core.orchestrator.delete_nat_nlb_instance_impl', side_effect=Exception("NAT/NLB deletion error"))
    def test_process_delete_topology_handle_nat_nlb_deletion_exception(self, mock_delete_nat_nlb, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.compute_region_idx = 200
        deleteInstanceDetailsObj.job = mocked_Job()

        with pytest.raises(Exception, match="NAT/NLB deletion error"):
            orchHandler.process_delete_topology_handle_nat_nlb_deletion(custnode, deleteInstanceDetailsObj)

        assert "process_delete_topology_handle_nat_nlb_deletion: NAT/NLB deleted False" in caplog.text
        mock_delete_nat_nlb.assert_called_once()

    @patch('orchestration_service.core.orchestrator.delete_nat_nlb_instance_impl', return_value=None)
    def test_process_delete_topology_handle_nat_nlb_deletion_none_return(self, mock_delete_nat_nlb, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.compute_region_idx = 200
        deleteInstanceDetailsObj.job = mocked_Job()

        success = orchHandler.process_delete_topology_handle_nat_nlb_deletion(custnode, deleteInstanceDetailsObj)

        assert success is None
        assert "process_delete_topology_handle_nat_nlb_deletion: NAT/NLB deleted None" in caplog.text
        mock_delete_nat_nlb.assert_called_once()

    @pytest.mark.parametrize("custnode_id, region_idx, expected_log_suffix", [
        (8000, 150, "True"),
        (9500, 250, "True"),
        (7500, 300, "True")
    ])
    @patch('orchestration_service.core.orchestrator.delete_nat_nlb_instance_impl', return_value=True)
    def test_process_delete_topology_handle_nat_nlb_deletion_different_parameters(self, mock_delete_nat_nlb, custnode_id, region_idx, expected_log_suffix, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=custnode_id, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.compute_region_idx = region_idx
        deleteInstanceDetailsObj.job = mocked_Job()

        success = orchHandler.process_delete_topology_handle_nat_nlb_deletion(custnode, deleteInstanceDetailsObj)

        assert success is True
        assert f"process_delete_topology_handle_nat_nlb_deletion: NAT/NLB deleted {expected_log_suffix}" in caplog.text
        mock_delete_nat_nlb.assert_called_once()

class TestProcessDeleteTopology:

    @patch('orchestration_service.core.orchestrator.some_delete_function', return_value=True)
    def test_process_delete_topology_basic_call(self, mock_delete_function, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.compute_region_idx = 200
        deleteInstanceDetailsObj.job = mocked_Job()

        result = orchHandler.process_delete_topology(custnode, deleteInstanceDetailsObj)

        assert result is not None

    def test_process_delete_topology_with_none_custnode(self, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.compute_region_idx = 200
        deleteInstanceDetailsObj.job = mocked_Job()

        result = orchHandler.process_delete_topology(None, deleteInstanceDetailsObj)

        assert result is not None

    def test_process_delete_topology_with_none_delete_details(self, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)

        result = orchHandler.process_delete_topology(custnode, None)

        assert result is not None

    @pytest.mark.parametrize("custnode_id, region_idx", [
        (5000, 100),
        (6000, 150),
        (7000, 200),
        (8000, 250)
    ])
    def test_process_delete_topology_with_different_parameters(self, custnode_id, region_idx, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh

        custnode = mocked_CustNodeModel(iid=custnode_id, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.compute_region_idx = region_idx
        deleteInstanceDetailsObj.job = mocked_Job()

        result = orchHandler.process_delete_topology(custnode, deleteInstanceDetailsObj)
        assert result is not None

class TestProcessAddTopology:

    @patch('orchestration_service.core.orchestrator.process_onramp_rn_node_allocation')
    @patch('orchestration_service.core.orchestrator.allocate_egress_ips_to_node_impl')
    @patch('orchestration_service.core.orchestrator.create_gp_gateway_edge_location_references_impl')
    def test_process_add_topology_onramp_node_success(self, mock_create_gp_gateway, mock_allocate_egress_ips, mock_process_onramp, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh
        orchHandler.tenant_id = 245139

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.node_type = 47
        custnode.region = 200
        job = mocked_Job()
        job_instances = []

        mock_process_onramp.return_value = True
        mock_allocate_egress_ips.return_value = True
        mock_create_gp_gateway.return_value = True

        result = orchHandler.process_add_topology(custnode, job, job_instances)

        assert result is True
        mock_process_onramp.assert_called_once()

    @patch('orchestration_service.core.orchestrator.process_onramp_rn_node_allocation')
    @patch('orchestration_service.core.orchestrator.allocate_egress_ips_to_node_impl')
    @patch('orchestration_service.core.orchestrator.create_gp_gateway_edge_location_references_impl')
    def test_process_add_topology_gp_gateway_node_success(self, mock_create_gp_gateway, mock_allocate_egress_ips, mock_process_onramp, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh
        orchHandler.tenant_id = 245139

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.node_type = 49
        custnode.region = 200
        job = mocked_Job()
        job_instances = []

        mock_process_onramp.return_value = None
        mock_allocate_egress_ips.return_value = True
        mock_create_gp_gateway.return_value = True

        result = orchHandler.process_add_topology(custnode, job, job_instances)

        assert result is True
        mock_create_gp_gateway.assert_called_once_with(orchHandler.db_h, custnode, orchHandler.tenant_id, custnode.region)

    @patch('orchestration_service.core.orchestrator.process_onramp_rn_node_allocation')
    @patch('orchestration_service.core.orchestrator.allocate_egress_ips_to_node_impl')
    @patch('orchestration_service.core.orchestrator.create_gp_gateway_edge_location_references_impl')
    def test_process_add_topology_regular_node_success(self, mock_create_gp_gateway, mock_allocate_egress_ips, mock_process_onramp, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh
        orchHandler.tenant_id = 245139

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.node_type = 48
        custnode.region = 200
        job = mocked_Job()
        job_instances = []

        mock_process_onramp.return_value = None
        mock_allocate_egress_ips.return_value = True
        mock_create_gp_gateway.return_value = True

        result = orchHandler.process_add_topology(custnode, job, job_instances)

        assert result is True
        mock_allocate_egress_ips.assert_called_once_with(orchHandler.db_h, custnode)

    @patch('orchestration_service.core.orchestrator.process_onramp_rn_node_allocation')
    @patch('orchestration_service.core.orchestrator.allocate_egress_ips_to_node_impl')
    @patch('orchestration_service.core.orchestrator.create_gp_gateway_edge_location_references_impl')
    def test_process_add_topology_onramp_allocation_failure(self, mock_create_gp_gateway, mock_allocate_egress_ips, mock_process_onramp, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh
        orchHandler.tenant_id = 245139

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.node_type = 47
        custnode.region = 200
        job = mocked_Job()
        job_instances = []

        mock_process_onramp.side_effect = Exception("Onramp allocation failed")

        with pytest.raises(Exception, match="Onramp allocation failed"):
            orchHandler.process_add_topology(custnode, job, job_instances)

    @patch('orchestration_service.core.orchestrator.process_onramp_rn_node_allocation')
    @patch('orchestration_service.core.orchestrator.allocate_egress_ips_to_node_impl')
    @patch('orchestration_service.core.orchestrator.create_gp_gateway_edge_location_references_impl')
    def test_process_add_topology_egress_ip_allocation_failure(self, mock_create_gp_gateway, mock_allocate_egress_ips, mock_process_onramp, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh
        orchHandler.tenant_id = 245139

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.node_type = 48
        custnode.region = 200
        job = mocked_Job()
        job_instances = []

        mock_process_onramp.return_value = None
        mock_allocate_egress_ips.return_value = False

        result = orchHandler.process_add_topology(custnode, job, job_instances)

        assert result is False

    @patch('orchestration_service.core.orchestrator.process_onramp_rn_node_allocation')
    @patch('orchestration_service.core.orchestrator.allocate_egress_ips_to_node_impl')
    @patch('orchestration_service.core.orchestrator.create_gp_gateway_edge_location_references_impl')
    def test_process_add_topology_gp_gateway_edge_location_failure(self, mock_create_gp_gateway, mock_allocate_egress_ips, mock_process_onramp, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh
        orchHandler.tenant_id = 245139

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.node_type = 49
        custnode.region = 200
        job = mocked_Job()
        job_instances = []

        mock_process_onramp.return_value = None
        mock_allocate_egress_ips.return_value = True
        mock_create_gp_gateway.return_value = False

        result = orchHandler.process_add_topology(custnode, job, job_instances)

        assert result is False

    @patch('orchestration_service.core.orchestrator.process_onramp_rn_node_allocation')
    @patch('orchestration_service.core.orchestrator.allocate_egress_ips_to_node_impl')
    @patch('orchestration_service.core.orchestrator.create_gp_gateway_edge_location_references_impl')
    def test_process_add_topology_exception_handling(self, mock_create_gp_gateway, mock_allocate_egress_ips, mock_process_onramp, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh
        orchHandler.tenant_id = 245139

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.node_type = 48
        custnode.region = 200
        job = mocked_Job()
        job_instances = []

        mock_process_onramp.return_value = None
        mock_allocate_egress_ips.side_effect = Exception("Unexpected error")

        with pytest.raises(Exception, match="Unexpected error"):
            orchHandler.process_add_topology(custnode, job, job_instances)

    @pytest.mark.parametrize("node_type, expected_onramp_call, expected_gp_gateway_call", [
        (47, True, False),
        (48, False, False),
        (49, False, True),
        (50, False, False)
    ])
    @patch('orchestration_service.core.orchestrator.process_onramp_rn_node_allocation')
    @patch('orchestration_service.core.orchestrator.allocate_egress_ips_to_node_impl')
    @patch('orchestration_service.core.orchestrator.create_gp_gateway_edge_location_references_impl')
    def test_process_add_topology_node_type_behavior(self, mock_create_gp_gateway, mock_allocate_egress_ips, mock_process_onramp, node_type, expected_onramp_call, expected_gp_gateway_call, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh
        orchHandler.tenant_id = 245139

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.node_type = node_type
        custnode.region = 200
        job = mocked_Job()
        job_instances = []

        mock_process_onramp.return_value = None
        mock_allocate_egress_ips.return_value = True
        mock_create_gp_gateway.return_value = True

        result = orchHandler.process_add_topology(custnode, job, job_instances)

        assert result is True

        if expected_onramp_call:
            mock_process_onramp.assert_called_once()

        if expected_gp_gateway_call:
            mock_create_gp_gateway.assert_called_once()
        else:
            mock_create_gp_gateway.assert_not_called()

    @patch('orchestration_service.core.orchestrator.process_onramp_rn_node_allocation')
    @patch('orchestration_service.core.orchestrator.allocate_egress_ips_to_node_impl')
    @patch('orchestration_service.core.orchestrator.create_gp_gateway_edge_location_references_impl')
    def test_process_add_topology_with_empty_job_instances(self, mock_create_gp_gateway, mock_allocate_egress_ips, mock_process_onramp, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh
        orchHandler.tenant_id = 245139

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.node_type = 48
        custnode.region = 200
        job = mocked_Job()
        job_instances = []

        mock_process_onramp.return_value = None
        mock_allocate_egress_ips.return_value = True
        mock_create_gp_gateway.return_value = True

        result = orchHandler.process_add_topology(custnode, job, job_instances)

        assert result is True

    @patch('orchestration_service.core.orchestrator.process_onramp_rn_node_allocation')
    @patch('orchestration_service.core.orchestrator.allocate_egress_ips_to_node_impl')
    @patch('orchestration_service.core.orchestrator.create_gp_gateway_edge_location_references_impl')
    def test_process_add_topology_with_populated_job_instances(self, mock_create_gp_gateway, mock_allocate_egress_ips, mock_process_onramp, caplog):
        caplog.set_level(logging.INFO)

        dbh = Mock_DBHandler()
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logging.getLogger('testlogger')
        orchHandler.db_h = dbh
        orchHandler.tenant_id = 245139

        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        custnode.node_type = 48
        custnode.region = 200
        job = mocked_Job()
        job_instances = [MockInstanceModelForSpInterconnect(111, dbh), MockInstanceModelForSpInterconnect(112, dbh)]

        mock_process_onramp.return_value = None
        mock_allocate_egress_ips.return_value = True
        mock_create_gp_gateway.return_value = True

        result = orchHandler.process_add_topology(custnode, job, job_instances)

        assert result is True


