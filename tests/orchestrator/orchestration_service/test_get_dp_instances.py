from unittest import TestCase
from unittest.mock import patch
import mock_imports_orchestrator
from orchestration_service.core.orchestrator import OrchstrationHandler

def set_cfg(cfg):
    cfg['log-root-dir']= '/tmp/',
    cfg['region'] = 'us-west-2',
    cfg['queues'] =  []
    cfg['dbpassword'] = "mankind"
    cfg['unittest'] = True

class logger():
    def info(self, *args):
        print(args)

    def error(self, *args):
        print(args)

    def warn(self, *args):
        print(args)

class Mock_conn():
    def __init__(self):
        print("Making conn")
        pass

    def close(self):
        pass

class Mock_cursor():
    def __init__(self, testSuiteIdx):
        self.sql = None
        self.params = None
        self.testSuiteIdx = testSuiteIdx

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params:
            print("Fetching all for %s" % str(self.sql%self.params))
            query = self.sql%self.params
        else:
            print("Fetching all for %s" % str(self.sql))
            query = self.sql
        answer = testCases[self.testSuiteIdx]["testCaseQueryResult"].get(query)
        return answer

    def fetchone(self):
        return {"name" : "Tejas"}

class Mock_DBHandler():
    def __init__(self, logger, testSuiteIdx):
        self.logger = logger
        self.conn = Mock_conn()
        self.testSuiteIdx = testSuiteIdx

    def conn(self):
        return self.conn

    def get_cursor(self):
        return  Mock_cursor(self.testSuiteIdx)

    def cursorclose(self, cursor):
        return

testCases = [
        { "testCaseName": "test_get_dp_instances_success",
          "testCaseQueryResult" : {
              "select id from instance_master where ((mp1_id = 100 or mp1_id = 101) and slot_nr > 1)" : [[200], [201]]
          },
          "testCaseExpectedOutput" : 2
        },
        {  "testCaseName": "test_get_dp_instances_success_no_dp_instances",
           "testCaseQueryResult": {
                "select id from instance_master where ((mp1_id = 100 or mp1_id = 101) and slot_nr > 1)" : []
           },
           "testCaseExpectedOutput": []
        }
]


class Test_success(TestCase):
    @patch('orchestration_service.core.orchestrator.INST.InstanceModel')
    def test_success(self, patched_InstanceModel):
        suiteIdx=0
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        class mocked_InstanceModel():
            def __init__(self, iid=None, dbh=None):
                print("XXX Came to instance model")
                self.fields = {}
                self.fields["id"] = iid
                self.fields["compute_region_idx"] = 200
                self.fields["compute_region_name"] = "us-west-2"
                self.fields["is_pinned_instance"] = False
                if self.fields["id"] in [4001, 4002, 4003]:
                    self.fields["is_dynamic_instance"] = True
                else:
                    self.fields["is_dynamic_instance"] = False
                self.fields["mpdp_clusterid"] = None
                self.fields["node_type"] = 49

            def get_param(self, keyword):
                return self.fields[keyword]

        self.db_h = Mock_DBHandler(logger(), testSuiteIdx=0)
        self.mp_instances = [mocked_InstanceModel(iid=100, dbh=self.db_h),
                             mocked_InstanceModel(iid=101, dbh=self.db_h)]
        patched_InstanceModel = mocked_InstanceModel

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.db_h = self.db_h
        answer = orchHandler.get_dp_instances(self.mp_instances)
        print("Got output : %s" % str(answer))
        self.assertEqual(len(answer), 2)

class Test_success_no_dp_instances(TestCase):
    @patch('orchestration_service.core.orchestrator.INST.InstanceModel')
    def test_success_no_dp_instances(self, patched_InstanceModel):
        suiteIdx=1
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        class mocked_InstanceModel():
            def __init__(self, iid=None, dbh=None):
                print("XXX Came to instance model")
                self.fields = {}
                self.fields["id"] = iid
                self.fields["compute_region_idx"] = 200
                self.fields["compute_region_name"] = "us-west-2"
                self.fields["is_pinned_instance"] = False
                if self.fields["id"] in [4001, 4002, 4003]:
                    self.fields["is_dynamic_instance"] = True
                else:
                    self.fields["is_dynamic_instance"] = False
                self.fields["mpdp_clusterid"] = None
                self.fields["node_type"] = 49

            def get_param(self, keyword):
                return self.fields[keyword]

        self.db_h = Mock_DBHandler(logger(), testSuiteIdx=suiteIdx)
        self.mp_instances = [mocked_InstanceModel(iid=100, dbh=self.db_h),
                             mocked_InstanceModel(iid=101, dbh=self.db_h)]
        patched_InstanceModel = mocked_InstanceModel

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        orchHandler = OrchstrationHandler(cfg)
        orchHandler.db_h = self.db_h
        answer = orchHandler.get_dp_instances(self.mp_instances)
        print("Got output : %s" % str(answer))
        self.assertEqual(answer, testCases[suiteIdx]["testCaseExpectedOutput"])
