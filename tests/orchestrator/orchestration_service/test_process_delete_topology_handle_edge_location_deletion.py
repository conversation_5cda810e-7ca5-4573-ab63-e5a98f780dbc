import traceback
import mock_imports_orchestrator
from unittest import TestCase
from unittest.mock import patch
from orchestration_service.core.orchestrator import OrchstrationHandler
'''
PYTHONPATH=~/saas-infra/src/apps/orchestrator/ pytest -s orchestrator.py
'''

def set_cfg(cfg):
    cfg['log-root-dir']= '/tmp/',
    cfg['region'] = 'us-west-2',
    cfg['queues'] =  []
    cfg['dbpassword'] = "mankind"
    cfg['unittest'] = True

class logger():
    def info(self, *args):
        print(args)

    def error(self, *args):
        print(args)

    def warn(self, *args):
        print(args)

class Mock_conn():
    def __init__(self):
        print("Making conn")
        pass

    def close(self):
        pass

class Mock_cursor():
    def __init__(self, testSuiteIdx):
        self.sql = None
        self.params = None
        self.testSuiteIdx = testSuiteIdx

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params:
            print("Fetching all for %s" % str(self.sql%self.params))
            query = self.sql%self.params
        else:
            print("Fetching all for %s" % str(self.sql))
            query = self.sql
        answer = testCases[self.testSuiteIdx]["testCaseQueryResult"].get(query)
        return answer

    def fetchone(self):
        return {"name" : "Tejas"}

class Mock_DBHandler():
    def __init__(self, logger, testSuiteIdx):
        self.logger = logger
        self.conn = Mock_conn()
        self.testSuiteIdx = testSuiteIdx

    def conn(self):
        return self.conn

    def get_cursor(self):
        return  Mock_cursor(self.testSuiteIdx)

    def cursorclose(self, cursor):
        return

testCases = [
        { "testCaseName": "handle_edge_location_deletion_success",
          "testCaseQueryResult" : {},
          "testCaseExpectedOutput" : True
        },
        { "testCaseName": "handle_edge_location_deletion_failure",
          "testCaseQueryResult": {},
          "testCaseExpectedOutput": False
        },
]

class Test_success(TestCase):
    @patch('orchestration_service.core.orchestrator.trigger_update_for_instance_changes', return_value=(True, "100"))
    @patch('orchestration_service.core.orchestrator.unbind_egress_ips_from_node_impl', return_value=True)
    @patch('orchestration_service.core.orchestrator.delete_gp_gateway_edge_location_references_impl', return_value=True)
    def test_success(self,
                     patched_delete_gp_gateway_edge_location_references_impl,
                     patched_unbind_egress_ips_from_node_impl, patched_trigger_update_for_instance_changes):
        suiteIdx=0
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        # Create a mock handle for dbh
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        # Create a basic cust node model
        class mocked_CustNodeModel():
            def __init__(self, iid=0, dbh=None):
                print("XXXXX Came to Cust Node model for iid %s!" % str(iid))
                self.id = iid
                self.custid = 15
                self.dbh = dbh
                self.okyo_edge_site_id = 0
                self.is_deleted = 0
                self.node_type = 49
                self.region = 200

            def save(self, dbh):
                return True

        # Create a mocked job class
        class mocked_Job():
            def __init__(self):
                self.status_msg = ""
                print("Mocked Job called")

            def save_job(self, dbh):
                return True

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = logger()
            orchHandler.db_h = dbh
            orchHandler.avctx = None
            orchHandler.tenant_id = 245139
        except Exception as E:
            print("Exception %s, Traceback %s" % (str(E.args), str(traceback.format_exc())))

        # Create a mocked custnode model object.
        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.compute_region_idx = 200
        deleteInstanceDetailsObj.job = mocked_Job()

        answer = orchHandler.process_delete_topology_handle_edge_location_deletion(custnode,
                                                                                   deleteInstanceDetailsObj)
        print(vars(deleteInstanceDetailsObj))
        print("Got output : %s" % str(answer))
        assert answer == testCases[suiteIdx]["testCaseExpectedOutput"]

class Test_failure(TestCase):
    @patch('orchestration_service.core.orchestrator.unbind_egress_ips_from_node_impl', return_value=False)
    @patch('orchestration_service.core.orchestrator.delete_gp_gateway_edge_location_references_impl', return_value=True)
    def test_failure(self,
                     patched_delete_gp_gateway_edge_location_references_impl,
                     patched_unbind_egress_ips_from_node_impl):
        suiteIdx=1
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        # Create a mock handle for dbh
        dbh=Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        # Create a basic cust node model
        class mocked_CustNodeModel():
            def __init__(self, iid=0, dbh=None):
                print("XXXXX Came to Cust Node model for iid %s!" % str(iid))
                self.id = iid
                self.custid = 15
                self.dbh = dbh
                self.okyo_edge_site_id = 0
                self.is_deleted = 0
                self.node_type = 49
                self.region = 200

            def save(self, dbh):
                return True

        # Create a mocked job class
        class mocked_Job():
            def __init__(self):
                self.status_msg = ""
                print("Mocked Job called")

            def save_job(self, dbh):
                return True

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = logger()
            orchHandler.db_h = dbh
            orchHandler.avctx = None
            orchHandler.tenant_id = 245139
        except Exception as E:
            print("Exception %s, Traceback %s" % (str(E.args), str(traceback.format_exc())))

        # Create a mocked custnode model object.
        custnode = mocked_CustNodeModel(iid=9000, dbh=dbh)
        deleteInstanceDetailsObj = orchHandler.deleteInstanceDetails()
        deleteInstanceDetailsObj.compute_region_idx = 200
        deleteInstanceDetailsObj.job = mocked_Job()

        answer = orchHandler.process_delete_topology_handle_edge_location_deletion(custnode,
                                                                                   deleteInstanceDetailsObj)
        print(vars(deleteInstanceDetailsObj))
        print("Got output : %s" % str(answer))
        assert answer == testCases[suiteIdx]["testCaseExpectedOutput"]
