import traceback
from unittest import TestCase
from unittest.mock import patch, MagicMock
import mock_imports_orchestrator
from test_process_delete_topology import testCases, <PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>, set_cfg, logger
from orchestration_service.core.orchestrator import OrchstrationHandler


class InstanceModel:
    def __init__(self, id):
        self.id = id

    def get_param(self, val):
        if val == "id":
            return self.id
        if val == "mark_delete":
            return 0


class TestDeleteCustEpaasEntry(TestCase):

    @patch('orchestration_service.core.orchestrator.INST.InstanceModel', return_value=InstanceModel(id=12131))
    def test_delete_cust_epaas_entry_positive_tesctcase(self, mockim):
        '''
         test deleting cust_epaas_entry function when delete topology iss triggered
        '''
        cfg = {}
        set_cfg(cfg)
        suiteIdx = 3
        dbh = Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logger()
        orchHandler.db_h = dbh
        orchHandler.tenant_id = 245139
        success = orchHandler.delete_cust_epaas_entries(custid=20002445, cust_ep_region_to_delete=200,
                                                        instanceids_to_delete=[1212])
        self.assertTrue(success)

    @patch('orchestration_service.core.orchestrator.INST.InstanceModel', return_value=InstanceModel(id=12131))
    def test_delete_cust_epaas_entry_negative_testcase(self, mockim):
        '''
         test deleting cust_epaas_entry function when delete topology iss triggered
        '''
        cfg = {}
        set_cfg(cfg)
        suiteIdx = 3
        dbh = Mock_DBHandler(logger=logger(), testSuiteIdx=suiteIdx)
        orchHandler = OrchstrationHandler(cfg)
        orchHandler.logger = logger()
        orchHandler.db_h = dbh
        orchHandler.tenant_id = 245139
        success = orchHandler.delete_cust_epaas_entries(custid=20002445, cust_ep_region_to_delete=220,
                                                        instanceids_to_delete=[1212])
        self.assertFalse(success)


