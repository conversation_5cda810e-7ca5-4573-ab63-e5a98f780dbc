import unittest
import re


testcases = ['PA-CAP530', 'PA-CAP550', 'PA-CAP410', 'PA-CAP310', 'PA-CAP530N', 'PA-CAP550N',"abcd","abc11abc"]
groundTruth = ['NAAS-SMALL', 'NAAS', 'NAAS-SMALL', 'NAAS-SMALL', 'NAAS-SMALL', 'NAAS', 'NAAS','NAAS-SMALL']
class MyTestCase(unittest.TestCase):
    def test_get_pa_capacity_type(self):
        license_type = []
        for pa_capacity_type in testcases:
            try:
                pa_cap = re.findall("\d+", pa_capacity_type)[0]
                if int(pa_cap) < 550:
                    license_type.append('NAAS-SMALL')
                else:
                    license_type.append("NAAS")
            except:
                license_type.append("NAAS")
        self.assertEqual(groundTruth, license_type)  # add assertion here

if __name__ == '__main__':
    unittest.main()
