import unittest
from orchestration_service.main import thread_handler

class TestThreadHandler(unittest.TestCase):

    class threadHandler:
        def __init__(self, exception=False):
            self.qname = "orchestration.Q"
            self.exception = exception
        def init_db(self):
            return
        def handle(self):
            if self.exception:
                raise OSError("Fatal! No disk space left")
            return

    def test_thread_handler(self):
        thread_h = self.threadHandler()
        thread_handler(thread_h)

    def test_thread_handler_exception(self):
        thread_h = self.threadHandler(exception=True)
        try:
            thread_handler(thread_h)
        except SystemExit as E:
            assert E.args == (-1,)

if __name__ == '__main__':
    unittest.main()
