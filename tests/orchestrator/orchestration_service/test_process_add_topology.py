import traceback
import pytest
import logging
from unittest import TestCase
from unittest.mock import patch
import orchestration_service.core.orchestrator
from orchestration_service.core.orchestrator import OrchstrationHandler
'''
PYTHONPATH=~/saas-infra/src/apps/orchestrator/ pytest -s orchestrator.py
'''

def set_cfg(cfg):
    cfg['log-root-dir']= '/tmp/',
    cfg['region'] = 'us-west-2',
    cfg['queues'] =  []
    cfg['dbpassword'] = "mankind"
    cfg['unittest'] = True

class logger():
    def info(self, *args):
        print(args)

    def error(self, *args):
        print(args)

    def warn(self, *args):
        print(args)

class Mock_conn():
    def __init__(self):
        print("Making conn")
        pass

    def close(self):
        pass

class Mock_cursor():
    def __init__(self, testSuiteIdx):
        self.sql = None
        self.params = None
        self.testSuiteIdx = testSuiteIdx

    def execute(self, sql, params):
        self.sql = sql
        self.params = params

    def fetchall(self):
        if self.params:
            print("Fetching all for %s" % str(self.sql % self.params))
            query = self.sql % self.params
        else:
            print("Fetching all for %s" % str(self.sql))
            query = self.sql
        answer = testCases[self.testSuiteIdx]["testCaseQueryResult"].get(query)
        return answer

    def fetchone(self):
        return {"name" : "Tejas"}

class Mock_DBHandler():
    def __init__(self, logger, testSuiteIdx):
        self.logger = logger
        self.conn = Mock_conn()
        self.testSuiteIdx = testSuiteIdx

    def conn(self):
        return self.conn

    def get_cursor(self):
        return  Mock_cursor(self.testSuiteIdx)

    def cursorclose(self, cursor):
        return
    
    def get_new_instance(self, version, node_type, region, custnode, transient, custid=0,
                         is_clean_pipe=0, panorama_job_id = None,
                         instance_role = 0, slot_nr = 0,
                         inter_instance_psk = None, has_nat_gateway = False,
                         has_nlb = False):
        return [4001, 4002, 4003]

testCases = [
        { "testCaseName": "test_process_add_topology_success",
          "testCaseQueryResult" : {},
          "testCaseExpectedOutput" : True
        },
        { "testCaseName": "test_process_add_topology_retry_success",
          "testCaseQueryResult" : {},
          "testCaseExpectedOutput" : True
        }
]

class Test_success():
    @patch('orchestration_service.core.orchestrator.OrchstrationHandler.should_trigger_sp_firewall_rule_operation')
    @patch('orchestration_service.core.orchestrator.trigger_update_for_instance_changes', return_value=(True, 3718128))
    @patch('orchestration_service.core.orchestrator.allocate_nlb')
    @patch('orchestration_service.core.orchestrator.allocate_nat_gateways')
    @patch('orchestration_service.core.orchestrator.is_nat_gateway_nlb_needed')
    @patch('orchestration_service.core.orchestrator.gpcs_get_compute_region_idx_from_edge_region_idx')
    @patch('orchestration_service.core.orchestrator_central_cache.execute_orch_query', return_value=(True, [1]))
    @patch('orchestration_service.core.orchestrator.CSN.CustNodeModel')
    @patch('orchestration_service.core.orchestrator.CM.CustomerModel')
    @patch('orchestration_service.core.orchestrator.INST.InstanceModel')
    def test_success(self,
                     patched_Instancemodel,
                     patched_Customermodel,
                     patched_Custnodemodel,
                     mock_execute_orch_query,
                     mock_gpcs_get_compute_region_idx_from_edge_region_idx,
                     mock_is_nat_gateway_nlb_needed,
                     mock_allocate_nat_gateways,
                     mock_allocate_nlb,
                     patched_trigger_update_for_instance_changes,
                     mock_should_trigger_sp_firewall_rule_operation,
                     caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=0
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        mock_gpcs_get_compute_region_idx_from_edge_region_idx.return_value = 200
        mock_is_nat_gateway_nlb_needed.return_value = (True, True)
        mock_allocate_nat_gateways.return_value = 1
        mock_allocate_nlb.return_value = 1
        mock_should_trigger_sp_firewall_rule_operation.return_value = False

        # Create a mock handle for dbh
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger, testSuiteIdx=suiteIdx)

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        # Create a basic instanceModel object.
        class mocked_InstanceModel():
            def __init__(self, iid=None, dbh=None):
                print("XXX Came to instance model")
                self.fields = {}
                self.fields["id"] = iid
                self.fields["name"] = f"GP_GATEWAY_{iid}"
                self.fields["compute_region_idx"] = 200
                self.fields["compute_region_name"] = "us-west-2"
                self.fields["is_pinned_instance"] = True
                if self.fields["id"] in [4001, 4002, 4003]:
                    self.fields["is_dynamic_instance"] = True
                else:
                    self.fields["is_dynamic_instance"] = False
                self.fields["mpdp_clusterid"] = None
                self.fields["node_type"] = 49

            def get_param(self, keyword):
                return self.fields[keyword]

            def unbind(self, custnode, db_h, job):
                # Since we are simulating a success condition of deletion of instances, set the id to None to
                # simulate successful deletion
                self.fields["id"] = None
                return True
            
            def bind(self, dbh, custid, node_type, node_id, is_new, custnode, transient=0):
                return True

            def get_all_instances_for_region_and_custid(self,
                                                        custid,
                                                        compute_region_idx,
                                                        node_type_list):
                # We will return a static list of instances and assume that they are auto-scaled instances.
                return [[4001], [4002], [4003]]

            def get_sites(self, dbh):
                if self.fields["id"] == 4001:
                    return [9001]
                if self.fields["id"] == 4002:
                    return [9002]
                if self.fields["id"] == 4003:
                    return [9003, 9004, 9005]

        # Create a basic cust node model
        class mocked_CustNodeModel():
            def __init__(self, iid=0, dbh=None):
                print("XXXXX Came to Cust Node model for iid %s!" % str(iid))
                self.id = iid
                self.custid = 15
                self.dbh = dbh
                self.okyo_edge_site_id = 0
                self.is_deleted = 0
                self.node_type = 49
                self.alt_node_type = -1
                self.is_clean_pipe = 0
                self.region = 200
                self.name = "Aster"
                self.num_allocs = 0
                self.is_dynamic_node = 0
                self.inbound_access = 'dedicated'

            def save(self, dbh):
                return True
            
            def get_retry_instance_list(self, dbh, ids):
                return (True, [mocked_InstanceModel(iid=4001), mocked_InstanceModel(iid=4002), mocked_InstanceModel(iid=4003)])
            
        class mocked_Node():
            def __init__(self):
                self.nodeid = 101
                self.region = 200
                self.vmtype = "GPGATEWAY"
                self.version = "version1.0"

        # Create a mocked job class
        class mocked_Job():
            def __init__(self):
                self.status_msg = ""
                print("Mocked Job called")

            def save_job(self, dbh):
                return True


        # Create a basic cust model
        class mocked_CustomerModel():
            def __init__(self, custid, dbh):
                self.id = custid
                self.dbh = dbh
                self.fwdrulesall = ""

        #Create an avisar context.
        class mocked_AvisarCtx():
            def __init__(self):
                self.cust_id = 15
                self.cust_topology_id = 9000
                self.panorama_job_id = 12345
                self.region_id = 200

        patched_Custnodemodel.side_effect = mocked_CustNodeModel
        patched_Instancemodel.side_effect = mocked_InstanceModel
        patched_Customermodel.side_effect = mocked_CustomerModel

        assert patched_Instancemodel is orchestration_service.core.orchestrator.INST.InstanceModel
        assert patched_Custnodemodel is orchestration_service.core.orchestrator.CSN.CustNodeModel
        assert patched_Customermodel is orchestration_service.core.orchestrator.CM.CustomerModel

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = test_logger
            orchHandler.db_h = dbh
            orchHandler.tenant_id = 245139
            orchHandler.avctx = mocked_AvisarCtx()
            orchHandler.ids = [4001, 4002, 4003]

        except Exception as E:
            print("Exception %s, Traceback %s" % (str(E.args), str(traceback.format_exc())))

        job_instances =[]
        answer = orchHandler.process_add_topology(mocked_Node(),
                                                    mocked_Job(),
                                                    job_instances)

        print("Got output : %s" % str(answer))
        assert answer == testCases[suiteIdx]["testCaseExpectedOutput"]
        assert job_instances == [4001, 4002, 4003]
        assert "Found pinned instance" in caplog.text
        assert "Successfully inserted record into gpcs_tenant_region_mapping for tenant" in caplog.text

class Test_success_retry():
    @patch('orchestration_service.core.orchestrator.trigger_update_for_instance_changes', return_value=(True, 3718128))
    @patch('orchestration_service.core.orchestrator.allocate_nlb')
    @patch('orchestration_service.core.orchestrator.allocate_nat_gateways')
    @patch('orchestration_service.core.orchestrator.is_nat_gateway_nlb_needed')
    @patch('orchestration_service.core.orchestrator.gpcs_get_compute_region_idx_from_edge_region_idx')
    @patch('orchestration_service.core.orchestrator_central_cache.execute_orch_query', return_value=(True, [1]))
    @patch('orchestration_service.core.orchestrator.CSN.CustNodeModel')
    @patch('orchestration_service.core.orchestrator.CM.CustomerModel')
    @patch('orchestration_service.core.orchestrator.INST.InstanceModel')
    def test_success_retry(self,
                     patched_Instancemodel,
                     patched_Customermodel,
                     patched_Custnodemodel,
                     mock_execute_orch_query,
                     mock_gpcs_get_compute_region_idx_from_edge_region_idx,
                     mock_is_nat_gateway_nlb_needed,
                     mock_allocate_nat_gateways,
                     mock_allocate_nlb,
                     patched_trigger_update_for_instance_changes,
                     caplog):
        caplog.set_level(logging.INFO)
        suiteIdx=1
        myname = testCases[suiteIdx]
        print("Running test %s ..." % str(myname))

        mock_gpcs_get_compute_region_idx_from_edge_region_idx.return_value = 200
        mock_is_nat_gateway_nlb_needed.return_value = (True, True)
        mock_allocate_nat_gateways.return_value = 1
        mock_allocate_nlb.return_value = 1

        # Create a mock handle for dbh
        test_logger=logging.getLogger('testlogger')
        dbh=Mock_DBHandler(logger=test_logger, testSuiteIdx=suiteIdx)

        # Set the basic cfg for object creation
        cfg = {}
        set_cfg(cfg)

        # Create a basic instanceModel object.
        class mocked_InstanceModel():
            def __init__(self, iid=None, dbh=None):
                print("XXX Came to instance model")
                self.fields = {}
                self.fields["id"] = iid
                self.fields["compute_region_idx"] = 200
                self.fields["compute_region_name"] = "us-west-2"
                self.fields["is_pinned_instance"] = True
                if self.fields["id"] in [4001, 4002, 4003]:
                    self.fields["is_dynamic_instance"] = True
                else:
                    self.fields["is_dynamic_instance"] = False
                self.fields["mpdp_clusterid"] = None
                self.fields["node_type"] = 49

            def get_param(self, keyword):
                return self.fields[keyword]

            def unbind(self, custnode, db_h, job):
                # Since we are simulating a success condition of deletion of instances, set the id to None to
                # simulate successful deletion
                self.fields["id"] = None
                return True

            def get_all_instances_for_region_and_custid(self,
                                                        custid,
                                                        compute_region_idx,
                                                        node_type_list):
                # We will return a static list of instances and assume that they are auto-scaled instances.
                return [[4001], [4002], [4003]]

            def get_sites(self, dbh):
                if self.fields["id"] == 4001:
                    return [9001]
                if self.fields["id"] == 4002:
                    return [9002]
                if self.fields["id"] == 4003:
                    return [9003, 9004, 9005]

        # Create a basic cust node model
        class mocked_CustNodeModel():
            def __init__(self, iid=0, dbh=None):
                print("XXXXX Came to Cust Node model for iid %s!" % str(iid))
                self.id = iid
                self.custid = 15
                self.dbh = dbh
                self.okyo_edge_site_id = 0
                self.is_deleted = 0
                self.node_type = 49
                self.alt_node_type = -1
                self.is_clean_pipe = 0
                self.region = 200
                self.name = "Aster"
                self.num_allocs = 2
                self.is_dynamic_node = 0

            def save(self, dbh):
                return True
            
            def get_retry_instance_list(self, dbh, ids):
                return (True, [mocked_InstanceModel(iid=4001), mocked_InstanceModel(iid=4002), mocked_InstanceModel(iid=4003)])
            
        class mocked_Node():
            def __init__(self):
                self.nodeid = 101
                self.region = 200
                self.vmtype = "GPGATEWAY"
                self.version = "version1.0"

        # Create a mocked job class
        class mocked_Job():
            def __init__(self):
                self.status_msg = ""
                print("Mocked Job called")

            def save_job(self, dbh):
                return True


        # Create a basic cust model
        class mocked_CustomerModel():
            def __init__(self, custid, dbh):
                self.id = custid
                self.dbh = dbh
                self.fwdrulesall = ""

        #Create an avisar context.
        class mocked_AvisarCtx():
            def __init__(self):
                self.cust_id = 15
                self.cust_topology_id = 9000
                self.panorama_job_id = 12345
                self.region_id = 200

        patched_Custnodemodel.side_effect = mocked_CustNodeModel
        patched_Instancemodel.side_effect = mocked_InstanceModel
        patched_Customermodel.side_effect = mocked_CustomerModel

        assert patched_Instancemodel is orchestration_service.core.orchestrator.INST.InstanceModel
        assert patched_Custnodemodel is orchestration_service.core.orchestrator.CSN.CustNodeModel
        assert patched_Customermodel is orchestration_service.core.orchestrator.CM.CustomerModel

        orchHandler = None
        try:
            orchHandler = OrchstrationHandler(cfg)
            orchHandler.logger = test_logger
            orchHandler.db_h = dbh
            orchHandler.tenant_id = 245139
            orchHandler.avctx = mocked_AvisarCtx()
            orchHandler.ids = [4001, 4002, 4003]

        except Exception as E:
            print("Exception %s, Traceback %s" % (str(E.args), str(traceback.format_exc())))

        job_instances =[]
        answer = orchHandler.process_add_topology(mocked_Node(),
                                                    mocked_Job(),
                                                    job_instances)

        print("Got output : %s" % str(answer))
        assert answer == testCases[suiteIdx]["testCaseExpectedOutput"]
        assert job_instances == []